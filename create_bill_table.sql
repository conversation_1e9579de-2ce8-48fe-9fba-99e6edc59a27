-- ========================================
-- 账单导入记录表 - MySQL建表语句
-- 创建时间：2025-01-06
-- 说明：用于存储通过API成功导入的账单数据
-- ========================================

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `bill_test` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `bill_test`;

-- 创建账单导入记录表
CREATE TABLE IF NOT EXISTS `bill_import_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `exact_bill_type` varchar(20) NOT NULL COMMENT '细分账单类型：1-普通结账单，10102-美团外卖单，10103-饿了么外卖单',
  `bill_serial_number` varchar(100) NOT NULL COMMENT '票据流水号：设备编号前6位+销售时间+随机数',
  `terminal_number` varchar(50) NOT NULL COMMENT '设备编号：12位设备标识',
  `sale_time` datetime NOT NULL COMMENT '销售时间：YYYY-MM-DD HH:MM:SS格式',
  `third_party_order_no` varchar(100) NOT NULL COMMENT '第三方订单号：平台要求唯一性',
  `receivable_amount` decimal(10,2) NOT NULL COMMENT '实收金额：保留两位小数',
  `total_num` decimal(10,2) NOT NULL COMMENT '商品数量：支持小数',
  `total_fee` decimal(10,2) NOT NULL COMMENT '应收金额：保留两位小数',
  `paid_amount` decimal(10,2) NOT NULL COMMENT '实付金额：保留两位小数',
  `bill_type` varchar(10) NOT NULL COMMENT '账单类型：1-结账单，6-退款单，3-日结单',
  `api_response` text COMMENT 'API响应结果：完整的JSON响应',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_serial` (`bill_serial_number`) COMMENT '票据流水号唯一索引',
  UNIQUE KEY `uk_third_party_order` (`third_party_order_no`) COMMENT '第三方订单号唯一索引',
  KEY `idx_terminal_number` (`terminal_number`) COMMENT '设备编号索引',
  KEY `idx_sale_time` (`sale_time`) COMMENT '销售时间索引',
  KEY `idx_exact_bill_type` (`exact_bill_type`) COMMENT '账单类型索引',
  KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账单导入记录表';

-- ========================================
-- 常用查询语句示例
-- ========================================

-- 1. 查询所有记录
-- SELECT * FROM bill_import_records ORDER BY created_at DESC;

-- 2. 按日期查询
-- SELECT * FROM bill_import_records 
-- WHERE DATE(sale_time) = '2025-07-06' 
-- ORDER BY sale_time DESC;

-- 3. 按设备编号查询
-- SELECT * FROM bill_import_records 
-- WHERE terminal_number = 'BBBB00000271' 
-- ORDER BY sale_time DESC;

-- 4. 按账单类型统计
-- SELECT 
--     exact_bill_type,
--     COUNT(*) as record_count,
--     SUM(total_fee) as total_amount,
--     SUM(receivable_amount) as total_received
-- FROM bill_import_records 
-- GROUP BY exact_bill_type;

-- 5. 按日期统计
-- SELECT 
--     DATE(sale_time) as sale_date,
--     COUNT(*) as daily_count,
--     SUM(total_fee) as daily_amount
-- FROM bill_import_records 
-- GROUP BY DATE(sale_time)
-- ORDER BY sale_date DESC;

-- 6. 查询最近导入的记录
-- SELECT * FROM bill_import_records 
-- ORDER BY created_at DESC 
-- LIMIT 10;

-- ========================================
-- 表结构说明
-- ========================================

/*
字段说明：
1. id: 自增主键，唯一标识每条记录
2. exact_bill_type: 细分账单类型，对应API参数exactBillType
3. bill_serial_number: 票据流水号，对应API参数billSerialNumber，具有唯一性约束
4. terminal_number: 设备编号，对应API参数terminalNumber
5. sale_time: 销售时间，对应API参数saleTime
6. third_party_order_no: 第三方订单号，对应API参数thirdPartyOrderNo，具有唯一性约束
7. receivable_amount: 实收金额，对应API参数receivableAmount
8. total_num: 商品数量，对应API参数totalNum
9. total_fee: 应收金额，对应API参数totalFee
10. paid_amount: 实付金额，对应API参数paidAmount
11. bill_type: 账单类型，对应API参数billType
12. api_response: 完整的API响应JSON，用于问题排查
13. created_at: 记录创建时间，自动生成
14. updated_at: 记录更新时间，自动更新

索引说明：
1. 主键索引：id字段
2. 唯一索引：bill_serial_number（票据流水号）
3. 唯一索引：third_party_order_no（第三方订单号）
4. 普通索引：terminal_number（设备编号）
5. 普通索引：sale_time（销售时间）
6. 普通索引：exact_bill_type（账单类型）
7. 普通索引：created_at（创建时间）

数据类型说明：
1. varchar：可变长度字符串，适用于文本字段
2. decimal(10,2)：精确小数，10位总长度，2位小数，适用于金额字段
3. datetime：日期时间类型，适用于时间字段
4. text：长文本类型，适用于JSON响应存储
5. timestamp：时间戳类型，支持自动更新
6. bigint：长整型，适用于大数值主键

约束说明：
1. NOT NULL：非空约束，确保重要字段不为空
2. UNIQUE：唯一约束，确保关键字段不重复
3. DEFAULT：默认值约束，自动设置时间字段
4. AUTO_INCREMENT：自增约束，主键自动递增
*/
