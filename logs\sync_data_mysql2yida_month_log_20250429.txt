2025-04-29 00:00:03,410 - INFO - =================使用默认全量同步=============
2025-04-29 00:00:04,536 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-29 00:00:04,552 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-29 00:00:04,567 - INFO - 开始处理日期: 2025-01
2025-04-29 00:00:04,567 - INFO - Request Parameters - Page 1:
2025-04-29 00:00:04,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:04,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:05,521 - INFO - Response - Page 1:
2025-04-29 00:00:05,740 - INFO - 第 1 页获取到 100 条记录
2025-04-29 00:00:05,740 - INFO - Request Parameters - Page 2:
2025-04-29 00:00:05,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:05,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:06,428 - INFO - Response - Page 2:
2025-04-29 00:00:06,631 - INFO - 第 2 页获取到 100 条记录
2025-04-29 00:00:06,631 - INFO - Request Parameters - Page 3:
2025-04-29 00:00:06,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:06,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:07,194 - INFO - Response - Page 3:
2025-04-29 00:00:07,397 - INFO - 第 3 页获取到 100 条记录
2025-04-29 00:00:07,397 - INFO - Request Parameters - Page 4:
2025-04-29 00:00:07,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:07,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:07,851 - INFO - Response - Page 4:
2025-04-29 00:00:08,054 - INFO - 第 4 页获取到 100 条记录
2025-04-29 00:00:08,054 - INFO - Request Parameters - Page 5:
2025-04-29 00:00:08,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:08,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:08,586 - INFO - Response - Page 5:
2025-04-29 00:00:08,789 - INFO - 第 5 页获取到 100 条记录
2025-04-29 00:00:08,789 - INFO - Request Parameters - Page 6:
2025-04-29 00:00:08,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:08,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:09,321 - INFO - Response - Page 6:
2025-04-29 00:00:09,524 - INFO - 第 6 页获取到 100 条记录
2025-04-29 00:00:09,524 - INFO - Request Parameters - Page 7:
2025-04-29 00:00:09,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:09,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:09,978 - INFO - Response - Page 7:
2025-04-29 00:00:10,181 - INFO - 第 7 页获取到 82 条记录
2025-04-29 00:00:10,181 - INFO - 查询完成，共获取到 682 条记录
2025-04-29 00:00:10,181 - INFO - 获取到 682 条表单数据
2025-04-29 00:00:10,181 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-29 00:00:10,196 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 00:00:10,196 - INFO - 开始处理日期: 2025-02
2025-04-29 00:00:10,196 - INFO - Request Parameters - Page 1:
2025-04-29 00:00:10,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:10,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:10,712 - INFO - Response - Page 1:
2025-04-29 00:00:10,916 - INFO - 第 1 页获取到 100 条记录
2025-04-29 00:00:10,916 - INFO - Request Parameters - Page 2:
2025-04-29 00:00:10,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:10,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:11,385 - INFO - Response - Page 2:
2025-04-29 00:00:11,588 - INFO - 第 2 页获取到 100 条记录
2025-04-29 00:00:11,588 - INFO - Request Parameters - Page 3:
2025-04-29 00:00:11,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:11,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:12,026 - INFO - Response - Page 3:
2025-04-29 00:00:12,229 - INFO - 第 3 页获取到 100 条记录
2025-04-29 00:00:12,229 - INFO - Request Parameters - Page 4:
2025-04-29 00:00:12,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:12,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:12,683 - INFO - Response - Page 4:
2025-04-29 00:00:12,886 - INFO - 第 4 页获取到 100 条记录
2025-04-29 00:00:12,886 - INFO - Request Parameters - Page 5:
2025-04-29 00:00:12,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:12,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:13,371 - INFO - Response - Page 5:
2025-04-29 00:00:13,574 - INFO - 第 5 页获取到 100 条记录
2025-04-29 00:00:13,574 - INFO - Request Parameters - Page 6:
2025-04-29 00:00:13,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:13,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:14,059 - INFO - Response - Page 6:
2025-04-29 00:00:14,262 - INFO - 第 6 页获取到 100 条记录
2025-04-29 00:00:14,262 - INFO - Request Parameters - Page 7:
2025-04-29 00:00:14,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:14,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:14,684 - INFO - Response - Page 7:
2025-04-29 00:00:14,887 - INFO - 第 7 页获取到 70 条记录
2025-04-29 00:00:14,887 - INFO - 查询完成，共获取到 670 条记录
2025-04-29 00:00:14,887 - INFO - 获取到 670 条表单数据
2025-04-29 00:00:14,887 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-29 00:00:14,903 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 00:00:14,903 - INFO - 开始处理日期: 2025-03
2025-04-29 00:00:14,903 - INFO - Request Parameters - Page 1:
2025-04-29 00:00:14,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:14,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:15,388 - INFO - Response - Page 1:
2025-04-29 00:00:15,591 - INFO - 第 1 页获取到 100 条记录
2025-04-29 00:00:15,591 - INFO - Request Parameters - Page 2:
2025-04-29 00:00:15,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:15,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:16,107 - INFO - Response - Page 2:
2025-04-29 00:00:16,310 - INFO - 第 2 页获取到 100 条记录
2025-04-29 00:00:16,310 - INFO - Request Parameters - Page 3:
2025-04-29 00:00:16,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:16,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:16,779 - INFO - Response - Page 3:
2025-04-29 00:00:16,983 - INFO - 第 3 页获取到 100 条记录
2025-04-29 00:00:16,983 - INFO - Request Parameters - Page 4:
2025-04-29 00:00:16,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:16,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:17,499 - INFO - Response - Page 4:
2025-04-29 00:00:17,702 - INFO - 第 4 页获取到 100 条记录
2025-04-29 00:00:17,702 - INFO - Request Parameters - Page 5:
2025-04-29 00:00:17,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:17,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:18,234 - INFO - Response - Page 5:
2025-04-29 00:00:18,437 - INFO - 第 5 页获取到 100 条记录
2025-04-29 00:00:18,437 - INFO - Request Parameters - Page 6:
2025-04-29 00:00:18,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:18,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:18,969 - INFO - Response - Page 6:
2025-04-29 00:00:19,172 - INFO - 第 6 页获取到 100 条记录
2025-04-29 00:00:19,172 - INFO - Request Parameters - Page 7:
2025-04-29 00:00:19,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:19,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:19,594 - INFO - Response - Page 7:
2025-04-29 00:00:19,797 - INFO - 第 7 页获取到 61 条记录
2025-04-29 00:00:19,797 - INFO - 查询完成，共获取到 661 条记录
2025-04-29 00:00:19,797 - INFO - 获取到 661 条表单数据
2025-04-29 00:00:19,797 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-29 00:00:19,813 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 00:00:19,813 - INFO - 开始处理日期: 2025-04
2025-04-29 00:00:19,813 - INFO - Request Parameters - Page 1:
2025-04-29 00:00:19,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:19,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:20,235 - INFO - Response - Page 1:
2025-04-29 00:00:20,438 - INFO - 第 1 页获取到 100 条记录
2025-04-29 00:00:20,438 - INFO - Request Parameters - Page 2:
2025-04-29 00:00:20,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:20,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:20,908 - INFO - Response - Page 2:
2025-04-29 00:00:21,111 - INFO - 第 2 页获取到 100 条记录
2025-04-29 00:00:21,111 - INFO - Request Parameters - Page 3:
2025-04-29 00:00:21,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:21,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:21,533 - INFO - Response - Page 3:
2025-04-29 00:00:21,736 - INFO - 第 3 页获取到 100 条记录
2025-04-29 00:00:21,736 - INFO - Request Parameters - Page 4:
2025-04-29 00:00:21,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:21,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:22,190 - INFO - Response - Page 4:
2025-04-29 00:00:22,393 - INFO - 第 4 页获取到 100 条记录
2025-04-29 00:00:22,393 - INFO - Request Parameters - Page 5:
2025-04-29 00:00:22,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:22,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:22,956 - INFO - Response - Page 5:
2025-04-29 00:00:23,159 - INFO - 第 5 页获取到 100 条记录
2025-04-29 00:00:23,159 - INFO - Request Parameters - Page 6:
2025-04-29 00:00:23,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:23,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:23,644 - INFO - Response - Page 6:
2025-04-29 00:00:23,847 - INFO - 第 6 页获取到 100 条记录
2025-04-29 00:00:23,847 - INFO - Request Parameters - Page 7:
2025-04-29 00:00:23,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 00:00:23,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 00:00:24,285 - INFO - Response - Page 7:
2025-04-29 00:00:24,488 - INFO - 第 7 页获取到 27 条记录
2025-04-29 00:00:24,488 - INFO - 查询完成，共获取到 627 条记录
2025-04-29 00:00:24,488 - INFO - 获取到 627 条表单数据
2025-04-29 00:00:24,488 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-29 00:00:24,488 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MK3
2025-04-29 00:00:24,926 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MK3
2025-04-29 00:00:24,926 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71140.0, 'new_value': 76865.0}, {'field': 'total_amount', 'old_value': 71140.0, 'new_value': 76865.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-04-29 00:00:24,926 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG4
2025-04-29 00:00:25,364 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG4
2025-04-29 00:00:25,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 974426.0, 'new_value': 1014019.0}, {'field': 'total_amount', 'old_value': 974426.0, 'new_value': 1014019.0}, {'field': 'order_count', 'old_value': 160, 'new_value': 165}]
2025-04-29 00:00:25,364 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH4
2025-04-29 00:00:25,833 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH4
2025-04-29 00:00:25,833 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9181.44, 'new_value': 9762.92}, {'field': 'offline_amount', 'old_value': 219009.15, 'new_value': 228882.99}, {'field': 'total_amount', 'old_value': 228190.59, 'new_value': 238645.91}, {'field': 'order_count', 'old_value': 1252, 'new_value': 1299}]
2025-04-29 00:00:25,833 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ4
2025-04-29 00:00:26,240 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ4
2025-04-29 00:00:26,240 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 100.0}, {'field': 'offline_amount', 'old_value': 41181.0, 'new_value': 42555.0}, {'field': 'total_amount', 'old_value': 41181.0, 'new_value': 42655.0}, {'field': 'order_count', 'old_value': 126, 'new_value': 131}]
2025-04-29 00:00:26,240 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK4
2025-04-29 00:00:26,709 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK4
2025-04-29 00:00:26,709 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73408.7, 'new_value': 75493.21}, {'field': 'offline_amount', 'old_value': 104709.32, 'new_value': 108151.53}, {'field': 'total_amount', 'old_value': 178118.02, 'new_value': 183644.74}, {'field': 'order_count', 'old_value': 6179, 'new_value': 6380}]
2025-04-29 00:00:26,709 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ4
2025-04-29 00:00:27,131 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ4
2025-04-29 00:00:27,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89573.0, 'new_value': 92274.0}, {'field': 'total_amount', 'old_value': 89573.0, 'new_value': 92274.0}, {'field': 'order_count', 'old_value': 785, 'new_value': 812}]
2025-04-29 00:00:27,147 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M65
2025-04-29 00:00:27,553 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M65
2025-04-29 00:00:27,553 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1862.28, 'new_value': 1899.79}, {'field': 'offline_amount', 'old_value': 104483.55, 'new_value': 105810.34}, {'field': 'total_amount', 'old_value': 106345.83, 'new_value': 107710.13}, {'field': 'order_count', 'old_value': 2660, 'new_value': 2700}]
2025-04-29 00:00:27,553 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M95
2025-04-29 00:00:28,022 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M95
2025-04-29 00:00:28,022 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27522.84, 'new_value': 28333.74}, {'field': 'offline_amount', 'old_value': 21031.7, 'new_value': 21768.6}, {'field': 'total_amount', 'old_value': 48554.54, 'new_value': 50102.34}, {'field': 'order_count', 'old_value': 242, 'new_value': 251}]
2025-04-29 00:00:28,022 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN5
2025-04-29 00:00:28,429 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN5
2025-04-29 00:00:28,429 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22244.78, 'new_value': 23094.71}, {'field': 'offline_amount', 'old_value': 245119.88, 'new_value': 247294.2}, {'field': 'total_amount', 'old_value': 267364.66, 'new_value': 270388.91}, {'field': 'order_count', 'old_value': 1287, 'new_value': 1307}]
2025-04-29 00:00:28,429 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M99
2025-04-29 00:00:28,898 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M99
2025-04-29 00:00:28,898 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40141.38, 'new_value': 41429.63}, {'field': 'offline_amount', 'old_value': 299070.28, 'new_value': 303427.98}, {'field': 'total_amount', 'old_value': 339211.66, 'new_value': 344857.61}, {'field': 'order_count', 'old_value': 2351, 'new_value': 2396}]
2025-04-29 00:00:28,898 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJ9
2025-04-29 00:00:29,336 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJ9
2025-04-29 00:00:29,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 292328.04, 'new_value': 301977.04}, {'field': 'total_amount', 'old_value': 292328.04, 'new_value': 301977.04}, {'field': 'order_count', 'old_value': 197, 'new_value': 211}]
2025-04-29 00:00:29,336 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MT9
2025-04-29 00:00:29,852 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MT9
2025-04-29 00:00:29,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38973.95, 'new_value': 40342.07}, {'field': 'offline_amount', 'old_value': 525519.92, 'new_value': 536582.89}, {'field': 'total_amount', 'old_value': 564493.87, 'new_value': 576924.96}, {'field': 'order_count', 'old_value': 4987, 'new_value': 5139}]
2025-04-29 00:00:29,852 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZ9
2025-04-29 00:00:30,305 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZ9
2025-04-29 00:00:30,305 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44057.48, 'new_value': 46398.99}, {'field': 'offline_amount', 'old_value': 37652.0, 'new_value': 39622.0}, {'field': 'total_amount', 'old_value': 81709.48, 'new_value': 86020.99}, {'field': 'order_count', 'old_value': 1086, 'new_value': 1144}]
2025-04-29 00:00:30,305 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGA
2025-04-29 00:00:30,774 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGA
2025-04-29 00:00:30,774 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54252.71, 'new_value': 55817.11}, {'field': 'offline_amount', 'old_value': 336314.8, 'new_value': 344182.27}, {'field': 'total_amount', 'old_value': 390567.51, 'new_value': 399999.38}, {'field': 'order_count', 'old_value': 2660, 'new_value': 2724}]
2025-04-29 00:00:30,774 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJA
2025-04-29 00:00:31,212 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJA
2025-04-29 00:00:31,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 398276.16, 'new_value': 411874.16}, {'field': 'total_amount', 'old_value': 398276.16, 'new_value': 411874.16}, {'field': 'order_count', 'old_value': 74, 'new_value': 76}]
2025-04-29 00:00:31,212 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPA
2025-04-29 00:00:31,619 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPA
2025-04-29 00:00:31,619 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 321429.85, 'new_value': 330492.27}, {'field': 'total_amount', 'old_value': 321429.85, 'new_value': 330492.27}, {'field': 'order_count', 'old_value': 1640, 'new_value': 1684}]
2025-04-29 00:00:31,619 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXA
2025-04-29 00:00:32,150 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXA
2025-04-29 00:00:32,150 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6427.9, 'new_value': 6437.8}, {'field': 'offline_amount', 'old_value': 46869.0, 'new_value': 48267.0}, {'field': 'total_amount', 'old_value': 53296.9, 'new_value': 54704.8}, {'field': 'order_count', 'old_value': 62, 'new_value': 64}]
2025-04-29 00:00:32,150 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0B
2025-04-29 00:00:32,572 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0B
2025-04-29 00:00:32,572 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101875.2, 'new_value': 105065.0}, {'field': 'total_amount', 'old_value': 101875.2, 'new_value': 105065.0}, {'field': 'order_count', 'old_value': 525, 'new_value': 541}]
2025-04-29 00:00:32,572 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1B
2025-04-29 00:00:32,979 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1B
2025-04-29 00:00:32,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73628.3, 'new_value': 77019.3}, {'field': 'total_amount', 'old_value': 73628.3, 'new_value': 77019.3}, {'field': 'order_count', 'old_value': 206, 'new_value': 213}]
2025-04-29 00:00:32,979 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNB
2025-04-29 00:00:33,432 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNB
2025-04-29 00:00:33,432 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 247452.72, 'new_value': 247712.17}, {'field': 'offline_amount', 'old_value': 157748.0, 'new_value': 158004.0}, {'field': 'total_amount', 'old_value': 405200.72, 'new_value': 405716.17}, {'field': 'order_count', 'old_value': 166, 'new_value': 169}]
2025-04-29 00:00:33,432 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN5
2025-04-29 00:00:33,855 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN5
2025-04-29 00:00:33,855 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88740.68, 'new_value': 91402.49}, {'field': 'offline_amount', 'old_value': 174309.91, 'new_value': 176992.98}, {'field': 'total_amount', 'old_value': 263050.59, 'new_value': 268395.47}, {'field': 'order_count', 'old_value': 3726, 'new_value': 3767}]
2025-04-29 00:00:33,855 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS5
2025-04-29 00:00:34,277 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS5
2025-04-29 00:00:34,277 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33203.0, 'new_value': 36468.0}, {'field': 'total_amount', 'old_value': 34355.0, 'new_value': 37620.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 80}]
2025-04-29 00:00:34,277 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV5
2025-04-29 00:00:34,730 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV5
2025-04-29 00:00:34,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49901.32, 'new_value': 51656.82}, {'field': 'total_amount', 'old_value': 49901.32, 'new_value': 51656.82}, {'field': 'order_count', 'old_value': 215, 'new_value': 220}]
2025-04-29 00:00:34,730 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY5
2025-04-29 00:00:35,215 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY5
2025-04-29 00:00:35,215 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230733.7, 'new_value': 237716.0}, {'field': 'total_amount', 'old_value': 235913.9, 'new_value': 242896.2}, {'field': 'order_count', 'old_value': 2702, 'new_value': 2764}]
2025-04-29 00:00:35,215 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-04-29 00:00:35,669 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-04-29 00:00:35,669 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 231146.0, 'new_value': 232300.0}, {'field': 'total_amount', 'old_value': 231146.0, 'new_value': 232300.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 62}]
2025-04-29 00:00:35,669 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF6
2025-04-29 00:00:36,122 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF6
2025-04-29 00:00:36,122 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5539.0, 'new_value': 6003.0}, {'field': 'offline_amount', 'old_value': 391234.0, 'new_value': 414541.0}, {'field': 'total_amount', 'old_value': 396773.0, 'new_value': 420544.0}, {'field': 'order_count', 'old_value': 218, 'new_value': 225}]
2025-04-29 00:00:36,122 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG6
2025-04-29 00:00:36,560 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG6
2025-04-29 00:00:36,560 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65168.0, 'new_value': 69136.0}, {'field': 'total_amount', 'old_value': 65168.0, 'new_value': 69136.0}, {'field': 'order_count', 'old_value': 165, 'new_value': 173}]
2025-04-29 00:00:36,560 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI6
2025-04-29 00:00:36,966 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI6
2025-04-29 00:00:36,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235829.0, 'new_value': 243109.0}, {'field': 'total_amount', 'old_value': 260704.0, 'new_value': 267984.0}, {'field': 'order_count', 'old_value': 5429, 'new_value': 5440}]
2025-04-29 00:00:36,966 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML6
2025-04-29 00:00:37,467 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML6
2025-04-29 00:00:37,467 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98896.0, 'new_value': 104075.0}, {'field': 'total_amount', 'old_value': 98896.0, 'new_value': 104075.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-04-29 00:00:37,467 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM6
2025-04-29 00:00:37,842 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM6
2025-04-29 00:00:37,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27828.0, 'new_value': 28906.0}, {'field': 'total_amount', 'old_value': 27828.0, 'new_value': 28906.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 48}]
2025-04-29 00:00:37,842 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR6
2025-04-29 00:00:38,295 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR6
2025-04-29 00:00:38,295 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62144.0, 'new_value': 64244.0}, {'field': 'total_amount', 'old_value': 62144.0, 'new_value': 64244.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 101}]
2025-04-29 00:00:38,295 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT6
2025-04-29 00:00:38,718 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT6
2025-04-29 00:00:38,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199904.0, 'new_value': 204740.0}, {'field': 'total_amount', 'old_value': 199904.0, 'new_value': 204740.0}, {'field': 'order_count', 'old_value': 276, 'new_value': 283}]
2025-04-29 00:00:38,718 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV6
2025-04-29 00:00:39,109 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV6
2025-04-29 00:00:39,109 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 180161.14, 'new_value': 185688.89}, {'field': 'offline_amount', 'old_value': 405186.67, 'new_value': 419186.67}, {'field': 'total_amount', 'old_value': 585347.81, 'new_value': 604875.56}, {'field': 'order_count', 'old_value': 2620, 'new_value': 2773}]
2025-04-29 00:00:39,109 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW6
2025-04-29 00:00:39,562 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW6
2025-04-29 00:00:39,562 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 229366.26, 'new_value': 236696.52}, {'field': 'offline_amount', 'old_value': 561802.35, 'new_value': 578445.01}, {'field': 'total_amount', 'old_value': 791168.61, 'new_value': 815141.53}, {'field': 'order_count', 'old_value': 4782, 'new_value': 4916}]
2025-04-29 00:00:39,562 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ6
2025-04-29 00:00:40,015 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ6
2025-04-29 00:00:40,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23912.1, 'new_value': 24232.1}, {'field': 'total_amount', 'old_value': 27413.2, 'new_value': 27733.2}, {'field': 'order_count', 'old_value': 315, 'new_value': 319}]
2025-04-29 00:00:40,015 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME7
2025-04-29 00:00:40,422 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME7
2025-04-29 00:00:40,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 722029.0, 'new_value': 734027.0}, {'field': 'total_amount', 'old_value': 722029.0, 'new_value': 734027.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 79}]
2025-04-29 00:00:40,422 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI7
2025-04-29 00:00:40,891 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI7
2025-04-29 00:00:40,891 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1161391.0, 'new_value': 1184282.0}, {'field': 'total_amount', 'old_value': 1161391.0, 'new_value': 1184282.0}, {'field': 'order_count', 'old_value': 1211, 'new_value': 1249}]
2025-04-29 00:00:40,891 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS7
2025-04-29 00:00:41,329 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS7
2025-04-29 00:00:41,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 604872.0, 'new_value': 608153.0}, {'field': 'total_amount', 'old_value': 604872.0, 'new_value': 608153.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 99}]
2025-04-29 00:00:41,329 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW7
2025-04-29 00:00:41,767 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW7
2025-04-29 00:00:41,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74994.5, 'new_value': 80993.5}, {'field': 'total_amount', 'old_value': 99237.24, 'new_value': 105236.24}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-04-29 00:00:41,767 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX7
2025-04-29 00:00:42,220 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX7
2025-04-29 00:00:42,220 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94931.8, 'new_value': 97064.8}, {'field': 'offline_amount', 'old_value': 54208.24, 'new_value': 55141.84}, {'field': 'total_amount', 'old_value': 149140.04, 'new_value': 152206.64}, {'field': 'order_count', 'old_value': 987, 'new_value': 1008}]
2025-04-29 00:00:42,220 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHH
2025-04-29 00:00:42,596 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHH
2025-04-29 00:00:42,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 245659.19, 'new_value': 250759.19}, {'field': 'total_amount', 'old_value': 245659.19, 'new_value': 250759.19}, {'field': 'order_count', 'old_value': 1494, 'new_value': 1528}]
2025-04-29 00:00:42,596 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIH
2025-04-29 00:00:42,986 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIH
2025-04-29 00:00:42,986 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 234764.25, 'new_value': 242245.05}, {'field': 'offline_amount', 'old_value': 55394.85, 'new_value': 57439.67}, {'field': 'total_amount', 'old_value': 290159.1, 'new_value': 299684.72}, {'field': 'order_count', 'old_value': 1158, 'new_value': 1202}]
2025-04-29 00:00:42,986 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJH
2025-04-29 00:00:43,424 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJH
2025-04-29 00:00:43,424 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199202.71, 'new_value': 205037.11}, {'field': 'total_amount', 'old_value': 206715.01, 'new_value': 212549.41}, {'field': 'order_count', 'old_value': 387, 'new_value': 398}]
2025-04-29 00:00:43,424 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOH
2025-04-29 00:00:43,831 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOH
2025-04-29 00:00:43,831 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58766.0, 'new_value': 59426.0}, {'field': 'total_amount', 'old_value': 58766.0, 'new_value': 59426.0}, {'field': 'order_count', 'old_value': 3891, 'new_value': 3910}]
2025-04-29 00:00:43,831 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXH
2025-04-29 00:00:44,253 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXH
2025-04-29 00:00:44,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 537906.28, 'new_value': 551851.28}, {'field': 'total_amount', 'old_value': 543603.28, 'new_value': 557548.28}, {'field': 'order_count', 'old_value': 373, 'new_value': 417}]
2025-04-29 00:00:44,253 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2I
2025-04-29 00:00:44,660 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2I
2025-04-29 00:00:44,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248720.11, 'new_value': 252821.11}, {'field': 'total_amount', 'old_value': 253486.11, 'new_value': 257587.11}, {'field': 'order_count', 'old_value': 3606, 'new_value': 3665}]
2025-04-29 00:00:44,660 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7I
2025-04-29 00:00:45,129 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7I
2025-04-29 00:00:45,129 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75632.32, 'new_value': 77649.53}, {'field': 'offline_amount', 'old_value': 1337633.71, 'new_value': 1382556.67}, {'field': 'total_amount', 'old_value': 1413266.03, 'new_value': 1460206.2}, {'field': 'order_count', 'old_value': 9883, 'new_value': 10285}]
2025-04-29 00:00:45,129 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXI
2025-04-29 00:00:45,551 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXI
2025-04-29 00:00:45,551 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43273.08, 'new_value': 44582.38}, {'field': 'offline_amount', 'old_value': 1080869.88, 'new_value': 1106491.51}, {'field': 'total_amount', 'old_value': 1124142.96, 'new_value': 1151073.89}, {'field': 'order_count', 'old_value': 5502, 'new_value': 5659}]
2025-04-29 00:00:45,551 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHJ
2025-04-29 00:00:45,957 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHJ
2025-04-29 00:00:45,957 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13040000.0, 'new_value': 13440000.0}, {'field': 'total_amount', 'old_value': 13040001.0, 'new_value': 13440001.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 68}]
2025-04-29 00:00:45,957 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIJ
2025-04-29 00:00:46,411 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIJ
2025-04-29 00:00:46,411 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 949668.9, 'new_value': 988316.4}, {'field': 'total_amount', 'old_value': 949668.9, 'new_value': 988316.4}, {'field': 'order_count', 'old_value': 1215, 'new_value': 1257}]
2025-04-29 00:00:46,411 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB
2025-04-29 00:00:46,864 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB
2025-04-29 00:00:46,864 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16972.87, 'new_value': 17359.77}, {'field': 'offline_amount', 'old_value': 321328.25, 'new_value': 332585.75}, {'field': 'total_amount', 'old_value': 338301.12, 'new_value': 349945.52}, {'field': 'order_count', 'old_value': 17138, 'new_value': 17769}]
2025-04-29 00:00:46,864 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME7
2025-04-29 00:00:47,302 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME7
2025-04-29 00:00:47,302 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 143773.71, 'new_value': 147991.71}, {'field': 'total_amount', 'old_value': 143773.71, 'new_value': 147991.71}, {'field': 'order_count', 'old_value': 11551, 'new_value': 12127}]
2025-04-29 00:00:47,302 - INFO - 日期 2025-04 处理完成 - 更新: 52 条，插入: 0 条，错误: 0 条
2025-04-29 00:00:47,302 - INFO - 数据同步完成！更新: 52 条，插入: 0 条，错误: 0 条
2025-04-29 00:00:47,302 - INFO - =================同步完成====================
2025-04-29 03:00:03,431 - INFO - =================使用默认全量同步=============
2025-04-29 03:00:04,572 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-29 03:00:04,572 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-29 03:00:04,588 - INFO - 开始处理日期: 2025-01
2025-04-29 03:00:04,588 - INFO - Request Parameters - Page 1:
2025-04-29 03:00:04,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:04,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:05,604 - INFO - Response - Page 1:
2025-04-29 03:00:05,807 - INFO - 第 1 页获取到 100 条记录
2025-04-29 03:00:05,807 - INFO - Request Parameters - Page 2:
2025-04-29 03:00:05,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:05,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:06,480 - INFO - Response - Page 2:
2025-04-29 03:00:06,683 - INFO - 第 2 页获取到 100 条记录
2025-04-29 03:00:06,683 - INFO - Request Parameters - Page 3:
2025-04-29 03:00:06,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:06,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:07,168 - INFO - Response - Page 3:
2025-04-29 03:00:07,371 - INFO - 第 3 页获取到 100 条记录
2025-04-29 03:00:07,371 - INFO - Request Parameters - Page 4:
2025-04-29 03:00:07,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:07,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:07,840 - INFO - Response - Page 4:
2025-04-29 03:00:08,044 - INFO - 第 4 页获取到 100 条记录
2025-04-29 03:00:08,044 - INFO - Request Parameters - Page 5:
2025-04-29 03:00:08,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:08,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:08,528 - INFO - Response - Page 5:
2025-04-29 03:00:08,732 - INFO - 第 5 页获取到 100 条记录
2025-04-29 03:00:08,732 - INFO - Request Parameters - Page 6:
2025-04-29 03:00:08,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:08,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:09,201 - INFO - Response - Page 6:
2025-04-29 03:00:09,404 - INFO - 第 6 页获取到 100 条记录
2025-04-29 03:00:09,404 - INFO - Request Parameters - Page 7:
2025-04-29 03:00:09,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:09,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:09,826 - INFO - Response - Page 7:
2025-04-29 03:00:10,029 - INFO - 第 7 页获取到 82 条记录
2025-04-29 03:00:10,029 - INFO - 查询完成，共获取到 682 条记录
2025-04-29 03:00:10,029 - INFO - 获取到 682 条表单数据
2025-04-29 03:00:10,029 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-29 03:00:10,045 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 03:00:10,045 - INFO - 开始处理日期: 2025-02
2025-04-29 03:00:10,045 - INFO - Request Parameters - Page 1:
2025-04-29 03:00:10,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:10,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:10,483 - INFO - Response - Page 1:
2025-04-29 03:00:10,686 - INFO - 第 1 页获取到 100 条记录
2025-04-29 03:00:10,686 - INFO - Request Parameters - Page 2:
2025-04-29 03:00:10,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:10,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:11,187 - INFO - Response - Page 2:
2025-04-29 03:00:11,390 - INFO - 第 2 页获取到 100 条记录
2025-04-29 03:00:11,390 - INFO - Request Parameters - Page 3:
2025-04-29 03:00:11,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:11,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:11,906 - INFO - Response - Page 3:
2025-04-29 03:00:12,109 - INFO - 第 3 页获取到 100 条记录
2025-04-29 03:00:12,109 - INFO - Request Parameters - Page 4:
2025-04-29 03:00:12,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:12,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:12,656 - INFO - Response - Page 4:
2025-04-29 03:00:12,860 - INFO - 第 4 页获取到 100 条记录
2025-04-29 03:00:12,860 - INFO - Request Parameters - Page 5:
2025-04-29 03:00:12,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:12,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:13,391 - INFO - Response - Page 5:
2025-04-29 03:00:13,595 - INFO - 第 5 页获取到 100 条记录
2025-04-29 03:00:13,595 - INFO - Request Parameters - Page 6:
2025-04-29 03:00:13,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:13,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:14,032 - INFO - Response - Page 6:
2025-04-29 03:00:14,236 - INFO - 第 6 页获取到 100 条记录
2025-04-29 03:00:14,236 - INFO - Request Parameters - Page 7:
2025-04-29 03:00:14,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:14,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:14,673 - INFO - Response - Page 7:
2025-04-29 03:00:14,877 - INFO - 第 7 页获取到 70 条记录
2025-04-29 03:00:14,877 - INFO - 查询完成，共获取到 670 条记录
2025-04-29 03:00:14,877 - INFO - 获取到 670 条表单数据
2025-04-29 03:00:14,877 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-29 03:00:14,892 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 03:00:14,892 - INFO - 开始处理日期: 2025-03
2025-04-29 03:00:14,892 - INFO - Request Parameters - Page 1:
2025-04-29 03:00:14,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:14,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:15,377 - INFO - Response - Page 1:
2025-04-29 03:00:15,580 - INFO - 第 1 页获取到 100 条记录
2025-04-29 03:00:15,580 - INFO - Request Parameters - Page 2:
2025-04-29 03:00:15,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:15,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:16,050 - INFO - Response - Page 2:
2025-04-29 03:00:16,253 - INFO - 第 2 页获取到 100 条记录
2025-04-29 03:00:16,253 - INFO - Request Parameters - Page 3:
2025-04-29 03:00:16,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:16,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:16,863 - INFO - Response - Page 3:
2025-04-29 03:00:17,066 - INFO - 第 3 页获取到 100 条记录
2025-04-29 03:00:17,066 - INFO - Request Parameters - Page 4:
2025-04-29 03:00:17,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:17,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:17,551 - INFO - Response - Page 4:
2025-04-29 03:00:17,754 - INFO - 第 4 页获取到 100 条记录
2025-04-29 03:00:17,754 - INFO - Request Parameters - Page 5:
2025-04-29 03:00:17,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:17,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:18,207 - INFO - Response - Page 5:
2025-04-29 03:00:18,411 - INFO - 第 5 页获取到 100 条记录
2025-04-29 03:00:18,411 - INFO - Request Parameters - Page 6:
2025-04-29 03:00:18,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:18,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:18,880 - INFO - Response - Page 6:
2025-04-29 03:00:19,083 - INFO - 第 6 页获取到 100 条记录
2025-04-29 03:00:19,083 - INFO - Request Parameters - Page 7:
2025-04-29 03:00:19,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:19,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:19,521 - INFO - Response - Page 7:
2025-04-29 03:00:19,724 - INFO - 第 7 页获取到 61 条记录
2025-04-29 03:00:19,724 - INFO - 查询完成，共获取到 661 条记录
2025-04-29 03:00:19,724 - INFO - 获取到 661 条表单数据
2025-04-29 03:00:19,724 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-29 03:00:19,740 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 03:00:19,740 - INFO - 开始处理日期: 2025-04
2025-04-29 03:00:19,740 - INFO - Request Parameters - Page 1:
2025-04-29 03:00:19,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:19,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:20,256 - INFO - Response - Page 1:
2025-04-29 03:00:20,459 - INFO - 第 1 页获取到 100 条记录
2025-04-29 03:00:20,459 - INFO - Request Parameters - Page 2:
2025-04-29 03:00:20,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:20,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:20,944 - INFO - Response - Page 2:
2025-04-29 03:00:21,147 - INFO - 第 2 页获取到 100 条记录
2025-04-29 03:00:21,147 - INFO - Request Parameters - Page 3:
2025-04-29 03:00:21,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:21,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:21,569 - INFO - Response - Page 3:
2025-04-29 03:00:21,773 - INFO - 第 3 页获取到 100 条记录
2025-04-29 03:00:21,773 - INFO - Request Parameters - Page 4:
2025-04-29 03:00:21,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:21,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:22,242 - INFO - Response - Page 4:
2025-04-29 03:00:22,445 - INFO - 第 4 页获取到 100 条记录
2025-04-29 03:00:22,445 - INFO - Request Parameters - Page 5:
2025-04-29 03:00:22,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:22,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:22,898 - INFO - Response - Page 5:
2025-04-29 03:00:23,102 - INFO - 第 5 页获取到 100 条记录
2025-04-29 03:00:23,102 - INFO - Request Parameters - Page 6:
2025-04-29 03:00:23,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:23,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:23,555 - INFO - Response - Page 6:
2025-04-29 03:00:23,758 - INFO - 第 6 页获取到 100 条记录
2025-04-29 03:00:23,758 - INFO - Request Parameters - Page 7:
2025-04-29 03:00:23,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 03:00:23,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 03:00:24,087 - INFO - Response - Page 7:
2025-04-29 03:00:24,290 - INFO - 第 7 页获取到 27 条记录
2025-04-29 03:00:24,290 - INFO - 查询完成，共获取到 627 条记录
2025-04-29 03:00:24,290 - INFO - 获取到 627 条表单数据
2025-04-29 03:00:24,290 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-29 03:00:24,306 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5I
2025-04-29 03:00:24,837 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5I
2025-04-29 03:00:24,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31303.0, 'new_value': 32211.0}, {'field': 'total_amount', 'old_value': 31303.0, 'new_value': 32211.0}, {'field': 'order_count', 'old_value': 195, 'new_value': 201}]
2025-04-29 03:00:24,853 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-04-29 03:00:24,853 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-04-29 03:00:24,853 - INFO - =================同步完成====================
2025-04-29 06:00:03,622 - INFO - =================使用默认全量同步=============
2025-04-29 06:00:04,747 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-29 06:00:04,747 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-29 06:00:04,779 - INFO - 开始处理日期: 2025-01
2025-04-29 06:00:04,779 - INFO - Request Parameters - Page 1:
2025-04-29 06:00:04,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:04,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:05,717 - INFO - Response - Page 1:
2025-04-29 06:00:05,920 - INFO - 第 1 页获取到 100 条记录
2025-04-29 06:00:05,920 - INFO - Request Parameters - Page 2:
2025-04-29 06:00:05,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:05,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:06,561 - INFO - Response - Page 2:
2025-04-29 06:00:06,765 - INFO - 第 2 页获取到 100 条记录
2025-04-29 06:00:06,765 - INFO - Request Parameters - Page 3:
2025-04-29 06:00:06,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:06,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:07,296 - INFO - Response - Page 3:
2025-04-29 06:00:07,500 - INFO - 第 3 页获取到 100 条记录
2025-04-29 06:00:07,500 - INFO - Request Parameters - Page 4:
2025-04-29 06:00:07,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:07,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:07,953 - INFO - Response - Page 4:
2025-04-29 06:00:08,156 - INFO - 第 4 页获取到 100 条记录
2025-04-29 06:00:08,156 - INFO - Request Parameters - Page 5:
2025-04-29 06:00:08,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:08,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:08,610 - INFO - Response - Page 5:
2025-04-29 06:00:08,813 - INFO - 第 5 页获取到 100 条记录
2025-04-29 06:00:08,813 - INFO - Request Parameters - Page 6:
2025-04-29 06:00:08,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:08,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:09,267 - INFO - Response - Page 6:
2025-04-29 06:00:09,470 - INFO - 第 6 页获取到 100 条记录
2025-04-29 06:00:09,470 - INFO - Request Parameters - Page 7:
2025-04-29 06:00:09,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:09,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:09,877 - INFO - Response - Page 7:
2025-04-29 06:00:10,080 - INFO - 第 7 页获取到 82 条记录
2025-04-29 06:00:10,080 - INFO - 查询完成，共获取到 682 条记录
2025-04-29 06:00:10,080 - INFO - 获取到 682 条表单数据
2025-04-29 06:00:10,080 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-29 06:00:10,096 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 06:00:10,096 - INFO - 开始处理日期: 2025-02
2025-04-29 06:00:10,096 - INFO - Request Parameters - Page 1:
2025-04-29 06:00:10,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:10,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:10,518 - INFO - Response - Page 1:
2025-04-29 06:00:10,721 - INFO - 第 1 页获取到 100 条记录
2025-04-29 06:00:10,721 - INFO - Request Parameters - Page 2:
2025-04-29 06:00:10,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:10,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:11,221 - INFO - Response - Page 2:
2025-04-29 06:00:11,425 - INFO - 第 2 页获取到 100 条记录
2025-04-29 06:00:11,425 - INFO - Request Parameters - Page 3:
2025-04-29 06:00:11,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:11,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:11,941 - INFO - Response - Page 3:
2025-04-29 06:00:12,144 - INFO - 第 3 页获取到 100 条记录
2025-04-29 06:00:12,144 - INFO - Request Parameters - Page 4:
2025-04-29 06:00:12,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:12,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:12,598 - INFO - Response - Page 4:
2025-04-29 06:00:12,801 - INFO - 第 4 页获取到 100 条记录
2025-04-29 06:00:12,801 - INFO - Request Parameters - Page 5:
2025-04-29 06:00:12,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:12,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:13,270 - INFO - Response - Page 5:
2025-04-29 06:00:13,473 - INFO - 第 5 页获取到 100 条记录
2025-04-29 06:00:13,473 - INFO - Request Parameters - Page 6:
2025-04-29 06:00:13,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:13,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:13,927 - INFO - Response - Page 6:
2025-04-29 06:00:14,130 - INFO - 第 6 页获取到 100 条记录
2025-04-29 06:00:14,130 - INFO - Request Parameters - Page 7:
2025-04-29 06:00:14,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:14,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:14,599 - INFO - Response - Page 7:
2025-04-29 06:00:14,803 - INFO - 第 7 页获取到 70 条记录
2025-04-29 06:00:14,803 - INFO - 查询完成，共获取到 670 条记录
2025-04-29 06:00:14,803 - INFO - 获取到 670 条表单数据
2025-04-29 06:00:14,803 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-29 06:00:14,818 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 06:00:14,818 - INFO - 开始处理日期: 2025-03
2025-04-29 06:00:14,818 - INFO - Request Parameters - Page 1:
2025-04-29 06:00:14,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:14,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:15,319 - INFO - Response - Page 1:
2025-04-29 06:00:15,522 - INFO - 第 1 页获取到 100 条记录
2025-04-29 06:00:15,522 - INFO - Request Parameters - Page 2:
2025-04-29 06:00:15,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:15,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:16,022 - INFO - Response - Page 2:
2025-04-29 06:00:16,226 - INFO - 第 2 页获取到 100 条记录
2025-04-29 06:00:16,226 - INFO - Request Parameters - Page 3:
2025-04-29 06:00:16,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:16,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:16,726 - INFO - Response - Page 3:
2025-04-29 06:00:16,929 - INFO - 第 3 页获取到 100 条记录
2025-04-29 06:00:16,929 - INFO - Request Parameters - Page 4:
2025-04-29 06:00:16,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:16,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:17,508 - INFO - Response - Page 4:
2025-04-29 06:00:17,711 - INFO - 第 4 页获取到 100 条记录
2025-04-29 06:00:17,711 - INFO - Request Parameters - Page 5:
2025-04-29 06:00:17,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:17,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:18,196 - INFO - Response - Page 5:
2025-04-29 06:00:18,399 - INFO - 第 5 页获取到 100 条记录
2025-04-29 06:00:18,399 - INFO - Request Parameters - Page 6:
2025-04-29 06:00:18,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:18,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:18,900 - INFO - Response - Page 6:
2025-04-29 06:00:19,103 - INFO - 第 6 页获取到 100 条记录
2025-04-29 06:00:19,103 - INFO - Request Parameters - Page 7:
2025-04-29 06:00:19,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:19,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:19,541 - INFO - Response - Page 7:
2025-04-29 06:00:19,744 - INFO - 第 7 页获取到 61 条记录
2025-04-29 06:00:19,744 - INFO - 查询完成，共获取到 661 条记录
2025-04-29 06:00:19,744 - INFO - 获取到 661 条表单数据
2025-04-29 06:00:19,744 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-29 06:00:19,760 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 06:00:19,760 - INFO - 开始处理日期: 2025-04
2025-04-29 06:00:19,760 - INFO - Request Parameters - Page 1:
2025-04-29 06:00:19,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:19,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:20,260 - INFO - Response - Page 1:
2025-04-29 06:00:20,463 - INFO - 第 1 页获取到 100 条记录
2025-04-29 06:00:20,463 - INFO - Request Parameters - Page 2:
2025-04-29 06:00:20,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:20,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:20,948 - INFO - Response - Page 2:
2025-04-29 06:00:21,152 - INFO - 第 2 页获取到 100 条记录
2025-04-29 06:00:21,152 - INFO - Request Parameters - Page 3:
2025-04-29 06:00:21,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:21,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:21,699 - INFO - Response - Page 3:
2025-04-29 06:00:21,902 - INFO - 第 3 页获取到 100 条记录
2025-04-29 06:00:21,902 - INFO - Request Parameters - Page 4:
2025-04-29 06:00:21,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:21,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:22,387 - INFO - Response - Page 4:
2025-04-29 06:00:22,590 - INFO - 第 4 页获取到 100 条记录
2025-04-29 06:00:22,590 - INFO - Request Parameters - Page 5:
2025-04-29 06:00:22,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:22,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:23,059 - INFO - Response - Page 5:
2025-04-29 06:00:23,263 - INFO - 第 5 页获取到 100 条记录
2025-04-29 06:00:23,263 - INFO - Request Parameters - Page 6:
2025-04-29 06:00:23,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:23,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:23,857 - INFO - Response - Page 6:
2025-04-29 06:00:24,060 - INFO - 第 6 页获取到 100 条记录
2025-04-29 06:00:24,060 - INFO - Request Parameters - Page 7:
2025-04-29 06:00:24,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 06:00:24,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 06:00:24,451 - INFO - Response - Page 7:
2025-04-29 06:00:24,654 - INFO - 第 7 页获取到 27 条记录
2025-04-29 06:00:24,654 - INFO - 查询完成，共获取到 627 条记录
2025-04-29 06:00:24,654 - INFO - 获取到 627 条表单数据
2025-04-29 06:00:24,654 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-29 06:00:24,670 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 06:00:24,670 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 06:00:24,670 - INFO - =================同步完成====================
2025-04-29 09:00:01,864 - INFO - =================使用默认全量同步=============
2025-04-29 09:00:03,021 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-29 09:00:03,021 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-29 09:00:03,036 - INFO - 开始处理日期: 2025-01
2025-04-29 09:00:03,052 - INFO - Request Parameters - Page 1:
2025-04-29 09:00:03,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:03,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:04,146 - INFO - Response - Page 1:
2025-04-29 09:00:04,349 - INFO - 第 1 页获取到 100 条记录
2025-04-29 09:00:04,349 - INFO - Request Parameters - Page 2:
2025-04-29 09:00:04,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:04,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:04,802 - INFO - Response - Page 2:
2025-04-29 09:00:05,005 - INFO - 第 2 页获取到 100 条记录
2025-04-29 09:00:05,005 - INFO - Request Parameters - Page 3:
2025-04-29 09:00:05,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:05,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:05,552 - INFO - Response - Page 3:
2025-04-29 09:00:05,755 - INFO - 第 3 页获取到 100 条记录
2025-04-29 09:00:05,755 - INFO - Request Parameters - Page 4:
2025-04-29 09:00:05,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:05,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:06,271 - INFO - Response - Page 4:
2025-04-29 09:00:06,474 - INFO - 第 4 页获取到 100 条记录
2025-04-29 09:00:06,474 - INFO - Request Parameters - Page 5:
2025-04-29 09:00:06,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:06,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:06,974 - INFO - Response - Page 5:
2025-04-29 09:00:07,177 - INFO - 第 5 页获取到 100 条记录
2025-04-29 09:00:07,177 - INFO - Request Parameters - Page 6:
2025-04-29 09:00:07,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:07,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:07,724 - INFO - Response - Page 6:
2025-04-29 09:00:07,927 - INFO - 第 6 页获取到 100 条记录
2025-04-29 09:00:07,927 - INFO - Request Parameters - Page 7:
2025-04-29 09:00:07,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:07,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:08,318 - INFO - Response - Page 7:
2025-04-29 09:00:08,521 - INFO - 第 7 页获取到 82 条记录
2025-04-29 09:00:08,521 - INFO - 查询完成，共获取到 682 条记录
2025-04-29 09:00:08,521 - INFO - 获取到 682 条表单数据
2025-04-29 09:00:08,521 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-29 09:00:08,536 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 09:00:08,536 - INFO - 开始处理日期: 2025-02
2025-04-29 09:00:08,536 - INFO - Request Parameters - Page 1:
2025-04-29 09:00:08,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:08,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:09,021 - INFO - Response - Page 1:
2025-04-29 09:00:09,224 - INFO - 第 1 页获取到 100 条记录
2025-04-29 09:00:09,224 - INFO - Request Parameters - Page 2:
2025-04-29 09:00:09,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:09,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:09,739 - INFO - Response - Page 2:
2025-04-29 09:00:09,943 - INFO - 第 2 页获取到 100 条记录
2025-04-29 09:00:09,943 - INFO - Request Parameters - Page 3:
2025-04-29 09:00:09,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:09,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:10,396 - INFO - Response - Page 3:
2025-04-29 09:00:10,599 - INFO - 第 3 页获取到 100 条记录
2025-04-29 09:00:10,599 - INFO - Request Parameters - Page 4:
2025-04-29 09:00:10,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:10,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:11,146 - INFO - Response - Page 4:
2025-04-29 09:00:11,349 - INFO - 第 4 页获取到 100 条记录
2025-04-29 09:00:11,349 - INFO - Request Parameters - Page 5:
2025-04-29 09:00:11,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:11,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:11,864 - INFO - Response - Page 5:
2025-04-29 09:00:12,068 - INFO - 第 5 页获取到 100 条记录
2025-04-29 09:00:12,068 - INFO - Request Parameters - Page 6:
2025-04-29 09:00:12,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:12,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:12,646 - INFO - Response - Page 6:
2025-04-29 09:00:12,849 - INFO - 第 6 页获取到 100 条记录
2025-04-29 09:00:12,849 - INFO - Request Parameters - Page 7:
2025-04-29 09:00:12,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:12,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:13,239 - INFO - Response - Page 7:
2025-04-29 09:00:13,443 - INFO - 第 7 页获取到 70 条记录
2025-04-29 09:00:13,443 - INFO - 查询完成，共获取到 670 条记录
2025-04-29 09:00:13,443 - INFO - 获取到 670 条表单数据
2025-04-29 09:00:13,443 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-29 09:00:13,458 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 09:00:13,458 - INFO - 开始处理日期: 2025-03
2025-04-29 09:00:13,458 - INFO - Request Parameters - Page 1:
2025-04-29 09:00:13,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:13,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:13,958 - INFO - Response - Page 1:
2025-04-29 09:00:14,161 - INFO - 第 1 页获取到 100 条记录
2025-04-29 09:00:14,161 - INFO - Request Parameters - Page 2:
2025-04-29 09:00:14,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:14,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:14,599 - INFO - Response - Page 2:
2025-04-29 09:00:14,802 - INFO - 第 2 页获取到 100 条记录
2025-04-29 09:00:14,802 - INFO - Request Parameters - Page 3:
2025-04-29 09:00:14,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:14,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:15,239 - INFO - Response - Page 3:
2025-04-29 09:00:15,443 - INFO - 第 3 页获取到 100 条记录
2025-04-29 09:00:15,443 - INFO - Request Parameters - Page 4:
2025-04-29 09:00:15,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:15,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:15,958 - INFO - Response - Page 4:
2025-04-29 09:00:16,161 - INFO - 第 4 页获取到 100 条记录
2025-04-29 09:00:16,161 - INFO - Request Parameters - Page 5:
2025-04-29 09:00:16,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:16,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:16,755 - INFO - Response - Page 5:
2025-04-29 09:00:16,958 - INFO - 第 5 页获取到 100 条记录
2025-04-29 09:00:16,958 - INFO - Request Parameters - Page 6:
2025-04-29 09:00:16,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:16,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:17,411 - INFO - Response - Page 6:
2025-04-29 09:00:17,614 - INFO - 第 6 页获取到 100 条记录
2025-04-29 09:00:17,614 - INFO - Request Parameters - Page 7:
2025-04-29 09:00:17,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:17,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:18,021 - INFO - Response - Page 7:
2025-04-29 09:00:18,224 - INFO - 第 7 页获取到 61 条记录
2025-04-29 09:00:18,224 - INFO - 查询完成，共获取到 661 条记录
2025-04-29 09:00:18,224 - INFO - 获取到 661 条表单数据
2025-04-29 09:00:18,224 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-29 09:00:18,239 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 09:00:18,239 - INFO - 开始处理日期: 2025-04
2025-04-29 09:00:18,239 - INFO - Request Parameters - Page 1:
2025-04-29 09:00:18,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:18,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:18,755 - INFO - Response - Page 1:
2025-04-29 09:00:18,958 - INFO - 第 1 页获取到 100 条记录
2025-04-29 09:00:18,958 - INFO - Request Parameters - Page 2:
2025-04-29 09:00:18,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:18,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:19,411 - INFO - Response - Page 2:
2025-04-29 09:00:19,614 - INFO - 第 2 页获取到 100 条记录
2025-04-29 09:00:19,614 - INFO - Request Parameters - Page 3:
2025-04-29 09:00:19,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:19,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:20,083 - INFO - Response - Page 3:
2025-04-29 09:00:20,286 - INFO - 第 3 页获取到 100 条记录
2025-04-29 09:00:20,286 - INFO - Request Parameters - Page 4:
2025-04-29 09:00:20,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:20,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:20,771 - INFO - Response - Page 4:
2025-04-29 09:00:20,974 - INFO - 第 4 页获取到 100 条记录
2025-04-29 09:00:20,974 - INFO - Request Parameters - Page 5:
2025-04-29 09:00:20,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:20,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:21,474 - INFO - Response - Page 5:
2025-04-29 09:00:21,677 - INFO - 第 5 页获取到 100 条记录
2025-04-29 09:00:21,677 - INFO - Request Parameters - Page 6:
2025-04-29 09:00:21,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:21,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:22,146 - INFO - Response - Page 6:
2025-04-29 09:00:22,349 - INFO - 第 6 页获取到 100 条记录
2025-04-29 09:00:22,349 - INFO - Request Parameters - Page 7:
2025-04-29 09:00:22,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:00:22,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:00:22,724 - INFO - Response - Page 7:
2025-04-29 09:00:22,927 - INFO - 第 7 页获取到 27 条记录
2025-04-29 09:00:22,927 - INFO - 查询完成，共获取到 627 条记录
2025-04-29 09:00:22,927 - INFO - 获取到 627 条表单数据
2025-04-29 09:00:22,927 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-29 09:00:22,927 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M03
2025-04-29 09:00:23,411 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M03
2025-04-29 09:00:23,411 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 145507.0, 'new_value': 155577.0}, {'field': 'total_amount', 'old_value': 410460.9, 'new_value': 420530.9}]
2025-04-29 09:00:23,411 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M13
2025-04-29 09:00:23,927 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M13
2025-04-29 09:00:23,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16769.91, 'new_value': 17217.54}, {'field': 'offline_amount', 'old_value': 44299.47, 'new_value': 46109.55}, {'field': 'total_amount', 'old_value': 61069.38, 'new_value': 63327.09}, {'field': 'order_count', 'old_value': 3248, 'new_value': 3367}]
2025-04-29 09:00:23,927 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M43
2025-04-29 09:00:24,396 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M43
2025-04-29 09:00:24,396 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141683.05, 'new_value': 148478.84}, {'field': 'total_amount', 'old_value': 141683.05, 'new_value': 148478.84}, {'field': 'order_count', 'old_value': 750, 'new_value': 775}]
2025-04-29 09:00:24,396 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M53
2025-04-29 09:00:24,896 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M53
2025-04-29 09:00:24,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14917.0, 'new_value': 16417.0}, {'field': 'total_amount', 'old_value': 14917.0, 'new_value': 16417.0}, {'field': 'order_count', 'old_value': 2912, 'new_value': 3145}]
2025-04-29 09:00:24,896 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M63
2025-04-29 09:00:25,286 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M63
2025-04-29 09:00:25,286 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175000.0, 'new_value': 180000.0}, {'field': 'total_amount', 'old_value': 175000.0, 'new_value': 180000.0}, {'field': 'order_count', 'old_value': 247, 'new_value': 248}]
2025-04-29 09:00:25,286 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M73
2025-04-29 09:00:25,817 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M73
2025-04-29 09:00:25,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175000.0, 'new_value': 180000.0}, {'field': 'total_amount', 'old_value': 175000.0, 'new_value': 180000.0}, {'field': 'order_count', 'old_value': 151, 'new_value': 152}]
2025-04-29 09:00:25,817 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M83
2025-04-29 09:00:26,427 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M83
2025-04-29 09:00:26,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1750000.0, 'new_value': 1800000.0}, {'field': 'total_amount', 'old_value': 1750000.0, 'new_value': 1800000.0}, {'field': 'order_count', 'old_value': 352, 'new_value': 353}]
2025-04-29 09:00:26,427 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-04-29 09:00:26,911 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-04-29 09:00:26,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1850000.0, 'new_value': 1900000.0}, {'field': 'total_amount', 'old_value': 1850000.0, 'new_value': 1900000.0}, {'field': 'order_count', 'old_value': 495, 'new_value': 496}]
2025-04-29 09:00:26,911 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MC3
2025-04-29 09:00:27,380 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MC3
2025-04-29 09:00:27,380 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29565.71, 'new_value': 31783.71}, {'field': 'offline_amount', 'old_value': 15928.98, 'new_value': 16365.98}, {'field': 'total_amount', 'old_value': 45494.69, 'new_value': 48149.69}, {'field': 'order_count', 'old_value': 2034, 'new_value': 2143}]
2025-04-29 09:00:27,396 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ME3
2025-04-29 09:00:27,896 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ME3
2025-04-29 09:00:27,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 286063.0, 'new_value': 289275.0}, {'field': 'total_amount', 'old_value': 286063.0, 'new_value': 289275.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 105}]
2025-04-29 09:00:27,896 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ML3
2025-04-29 09:00:28,317 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ML3
2025-04-29 09:00:28,317 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20293.9, 'new_value': 20906.9}, {'field': 'offline_amount', 'old_value': 29774.35, 'new_value': 30969.35}, {'field': 'total_amount', 'old_value': 50068.25, 'new_value': 51876.25}, {'field': 'order_count', 'old_value': 2398, 'new_value': 2485}]
2025-04-29 09:00:28,317 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MO3
2025-04-29 09:00:28,724 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MO3
2025-04-29 09:00:28,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103782.0, 'new_value': 105172.0}, {'field': 'total_amount', 'old_value': 103782.0, 'new_value': 105172.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 125}]
2025-04-29 09:00:28,724 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR3
2025-04-29 09:00:29,224 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR3
2025-04-29 09:00:29,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9977.0, 'new_value': 10534.0}, {'field': 'total_amount', 'old_value': 10567.0, 'new_value': 11124.0}, {'field': 'order_count', 'old_value': 208, 'new_value': 217}]
2025-04-29 09:00:29,224 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS3
2025-04-29 09:00:29,646 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS3
2025-04-29 09:00:29,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110573.0, 'new_value': 115425.0}, {'field': 'total_amount', 'old_value': 110573.0, 'new_value': 115425.0}, {'field': 'order_count', 'old_value': 6106, 'new_value': 6353}]
2025-04-29 09:00:29,646 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT3
2025-04-29 09:00:30,083 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT3
2025-04-29 09:00:30,083 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65186.67, 'new_value': 67258.43}, {'field': 'offline_amount', 'old_value': 130785.35, 'new_value': 136513.35}, {'field': 'total_amount', 'old_value': 195972.02, 'new_value': 203771.78}, {'field': 'order_count', 'old_value': 2151, 'new_value': 2237}]
2025-04-29 09:00:30,083 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX3
2025-04-29 09:00:30,489 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX3
2025-04-29 09:00:30,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39159.8, 'new_value': 42639.8}, {'field': 'total_amount', 'old_value': 41639.8, 'new_value': 45119.8}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-04-29 09:00:30,489 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ3
2025-04-29 09:00:30,927 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ3
2025-04-29 09:00:30,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63442.0, 'new_value': 64612.0}, {'field': 'offline_amount', 'old_value': 118157.85, 'new_value': 121805.85}, {'field': 'total_amount', 'old_value': 181599.85, 'new_value': 186417.85}, {'field': 'order_count', 'old_value': 246, 'new_value': 252}]
2025-04-29 09:00:30,927 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M34
2025-04-29 09:00:31,427 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M34
2025-04-29 09:00:31,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22898.82, 'new_value': 23247.82}, {'field': 'total_amount', 'old_value': 22954.62, 'new_value': 23303.62}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-04-29 09:00:31,427 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M44
2025-04-29 09:00:31,880 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M44
2025-04-29 09:00:31,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16798.0, 'new_value': 19243.6}, {'field': 'total_amount', 'old_value': 16899.0, 'new_value': 19344.6}, {'field': 'order_count', 'old_value': 1364, 'new_value': 1546}]
2025-04-29 09:00:31,880 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M64
2025-04-29 09:00:32,349 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M64
2025-04-29 09:00:32,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67787.0, 'new_value': 71145.0}, {'field': 'total_amount', 'old_value': 67787.0, 'new_value': 71145.0}, {'field': 'order_count', 'old_value': 1249, 'new_value': 1303}]
2025-04-29 09:00:32,349 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M94
2025-04-29 09:00:32,942 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M94
2025-04-29 09:00:32,942 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33808.45, 'new_value': 37037.39}, {'field': 'offline_amount', 'old_value': 113404.41, 'new_value': 126230.92}, {'field': 'total_amount', 'old_value': 147212.86, 'new_value': 163268.31}, {'field': 'order_count', 'old_value': 2005, 'new_value': 2228}]
2025-04-29 09:00:32,942 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB4
2025-04-29 09:00:33,396 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB4
2025-04-29 09:00:33,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90390.0, 'new_value': 91520.0}, {'field': 'total_amount', 'old_value': 90390.0, 'new_value': 91520.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 45}]
2025-04-29 09:00:33,396 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC4
2025-04-29 09:00:33,864 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC4
2025-04-29 09:00:33,864 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80551.23, 'new_value': 90397.29}, {'field': 'total_amount', 'old_value': 80551.23, 'new_value': 90397.29}, {'field': 'order_count', 'old_value': 4216, 'new_value': 4717}]
2025-04-29 09:00:33,864 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME4
2025-04-29 09:00:34,349 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME4
2025-04-29 09:00:34,349 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 155156.0, 'new_value': 159562.0}, {'field': 'offline_amount', 'old_value': 54700.11, 'new_value': 56777.67}, {'field': 'total_amount', 'old_value': 209856.11, 'new_value': 216339.67}, {'field': 'order_count', 'old_value': 1428, 'new_value': 1465}]
2025-04-29 09:00:34,349 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF4
2025-04-29 09:00:34,802 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF4
2025-04-29 09:00:34,802 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6618.7, 'new_value': 6786.7}, {'field': 'offline_amount', 'old_value': 45681.5, 'new_value': 47437.2}, {'field': 'total_amount', 'old_value': 52300.2, 'new_value': 54223.9}, {'field': 'order_count', 'old_value': 538, 'new_value': 559}]
2025-04-29 09:00:34,802 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI4
2025-04-29 09:00:35,208 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI4
2025-04-29 09:00:35,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103215.36, 'new_value': 106998.16}, {'field': 'total_amount', 'old_value': 103215.36, 'new_value': 106998.16}, {'field': 'order_count', 'old_value': 2747, 'new_value': 2838}]
2025-04-29 09:00:35,208 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML4
2025-04-29 09:00:35,677 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML4
2025-04-29 09:00:35,677 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38578.9, 'new_value': 40193.31}, {'field': 'total_amount', 'old_value': 38578.9, 'new_value': 40193.31}, {'field': 'order_count', 'old_value': 1428, 'new_value': 1486}]
2025-04-29 09:00:35,677 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM4
2025-04-29 09:00:36,161 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM4
2025-04-29 09:00:36,161 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12300.32, 'new_value': 12729.85}, {'field': 'offline_amount', 'old_value': 190145.51, 'new_value': 197204.65}, {'field': 'total_amount', 'old_value': 202445.83, 'new_value': 209934.5}, {'field': 'order_count', 'old_value': 2171, 'new_value': 2254}]
2025-04-29 09:00:36,161 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR4
2025-04-29 09:00:36,646 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR4
2025-04-29 09:00:36,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88067.75, 'new_value': 90590.7}, {'field': 'offline_amount', 'old_value': 832752.27, 'new_value': 862142.77}, {'field': 'total_amount', 'old_value': 920820.02, 'new_value': 952733.47}, {'field': 'order_count', 'old_value': 4101, 'new_value': 4211}]
2025-04-29 09:00:36,646 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS4
2025-04-29 09:00:37,177 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS4
2025-04-29 09:00:37,177 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 191271.1, 'new_value': 193489.1}, {'field': 'offline_amount', 'old_value': 70395.36, 'new_value': 71819.36}, {'field': 'total_amount', 'old_value': 261666.46, 'new_value': 265308.46}, {'field': 'order_count', 'old_value': 1875, 'new_value': 1918}]
2025-04-29 09:00:37,177 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M05
2025-04-29 09:00:37,646 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M05
2025-04-29 09:00:37,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56948.0, 'new_value': 57828.0}, {'field': 'total_amount', 'old_value': 56948.0, 'new_value': 57828.0}, {'field': 'order_count', 'old_value': 851, 'new_value': 915}]
2025-04-29 09:00:37,646 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M45
2025-04-29 09:00:38,099 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M45
2025-04-29 09:00:38,099 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3342.0, 'new_value': 3897.0}, {'field': 'offline_amount', 'old_value': 43709.0, 'new_value': 49988.0}, {'field': 'total_amount', 'old_value': 47051.0, 'new_value': 53885.0}, {'field': 'order_count', 'old_value': 532, 'new_value': 610}]
2025-04-29 09:00:38,099 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB5
2025-04-29 09:00:38,552 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB5
2025-04-29 09:00:38,552 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22145.4, 'new_value': 22531.4}, {'field': 'offline_amount', 'old_value': 50201.87, 'new_value': 51075.87}, {'field': 'total_amount', 'old_value': 72347.27, 'new_value': 73607.27}, {'field': 'order_count', 'old_value': 852, 'new_value': 864}]
2025-04-29 09:00:38,552 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC5
2025-04-29 09:00:38,989 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC5
2025-04-29 09:00:38,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144523.94, 'new_value': 150822.02}, {'field': 'total_amount', 'old_value': 144523.94, 'new_value': 150822.02}, {'field': 'order_count', 'old_value': 1694, 'new_value': 1762}]
2025-04-29 09:00:38,989 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME5
2025-04-29 09:00:39,474 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME5
2025-04-29 09:00:39,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216267.7, 'new_value': 221481.5}, {'field': 'total_amount', 'old_value': 216267.7, 'new_value': 221481.5}, {'field': 'order_count', 'old_value': 2687, 'new_value': 2743}]
2025-04-29 09:00:39,474 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI5
2025-04-29 09:00:39,942 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI5
2025-04-29 09:00:39,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142761.4, 'new_value': 146442.65}, {'field': 'total_amount', 'old_value': 142761.4, 'new_value': 146442.65}, {'field': 'order_count', 'old_value': 257, 'new_value': 263}]
2025-04-29 09:00:39,942 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM5
2025-04-29 09:00:40,395 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM5
2025-04-29 09:00:40,395 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40294.8, 'new_value': 47080.7}, {'field': 'total_amount', 'old_value': 40294.8, 'new_value': 47080.7}, {'field': 'order_count', 'old_value': 215, 'new_value': 242}]
2025-04-29 09:00:40,395 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO5
2025-04-29 09:00:40,864 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO5
2025-04-29 09:00:40,864 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3888.0, 'new_value': 5992.0}, {'field': 'offline_amount', 'old_value': 20528.0, 'new_value': 25321.0}, {'field': 'total_amount', 'old_value': 24416.0, 'new_value': 31313.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 62}]
2025-04-29 09:00:40,880 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ5
2025-04-29 09:00:41,333 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ5
2025-04-29 09:00:41,333 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 220670.0, 'new_value': 229927.0}, {'field': 'total_amount', 'old_value': 233171.0, 'new_value': 242428.0}, {'field': 'order_count', 'old_value': 1246, 'new_value': 1307}]
2025-04-29 09:00:41,333 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M79
2025-04-29 09:00:41,817 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M79
2025-04-29 09:00:41,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 488115.99, 'new_value': 507074.95}, {'field': 'total_amount', 'old_value': 488115.99, 'new_value': 507074.95}, {'field': 'order_count', 'old_value': 2208, 'new_value': 2265}]
2025-04-29 09:00:41,833 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M89
2025-04-29 09:00:42,270 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M89
2025-04-29 09:00:42,270 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 157, 'new_value': 163}]
2025-04-29 09:00:42,270 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MA9
2025-04-29 09:00:42,708 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MA9
2025-04-29 09:00:42,708 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58158.0, 'new_value': 64965.0}, {'field': 'offline_amount', 'old_value': 185286.45, 'new_value': 206017.45}, {'field': 'total_amount', 'old_value': 243444.45, 'new_value': 270982.45}, {'field': 'order_count', 'old_value': 1684, 'new_value': 1891}]
2025-04-29 09:00:42,708 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MF9
2025-04-29 09:00:43,145 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MF9
2025-04-29 09:00:43,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124833.4, 'new_value': 130901.4}, {'field': 'total_amount', 'old_value': 151146.21, 'new_value': 157214.21}, {'field': 'order_count', 'old_value': 3698, 'new_value': 3833}]
2025-04-29 09:00:43,145 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MG9
2025-04-29 09:00:43,630 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MG9
2025-04-29 09:00:43,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25262.02, 'new_value': 25792.58}, {'field': 'total_amount', 'old_value': 25262.02, 'new_value': 25792.58}, {'field': 'order_count', 'old_value': 150, 'new_value': 155}]
2025-04-29 09:00:43,630 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MH9
2025-04-29 09:00:44,161 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MH9
2025-04-29 09:00:44,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24167.02, 'new_value': 27443.56}, {'field': 'total_amount', 'old_value': 24167.02, 'new_value': 27443.56}, {'field': 'order_count', 'old_value': 40, 'new_value': 43}]
2025-04-29 09:00:44,161 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MK9
2025-04-29 09:00:44,661 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MK9
2025-04-29 09:00:44,661 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 183471.0, 'new_value': 194747.0}, {'field': 'total_amount', 'old_value': 183471.0, 'new_value': 194747.0}, {'field': 'order_count', 'old_value': 366, 'new_value': 379}]
2025-04-29 09:00:44,661 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MM9
2025-04-29 09:00:45,145 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MM9
2025-04-29 09:00:45,145 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 146652.97, 'new_value': 149669.97}, {'field': 'offline_amount', 'old_value': 136651.14, 'new_value': 140995.31}, {'field': 'total_amount', 'old_value': 283304.11, 'new_value': 290665.28}, {'field': 'order_count', 'old_value': 895, 'new_value': 917}]
2025-04-29 09:00:45,145 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MN9
2025-04-29 09:00:45,708 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MN9
2025-04-29 09:00:45,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37575.0, 'new_value': 41642.0}, {'field': 'total_amount', 'old_value': 37575.0, 'new_value': 41642.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 145}]
2025-04-29 09:00:45,708 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MP9
2025-04-29 09:00:46,239 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MP9
2025-04-29 09:00:46,239 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88197.0, 'new_value': 94168.0}, {'field': 'offline_amount', 'old_value': 212598.0, 'new_value': 217287.0}, {'field': 'total_amount', 'old_value': 300795.0, 'new_value': 311455.0}, {'field': 'order_count', 'old_value': 284, 'new_value': 296}]
2025-04-29 09:00:46,239 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MR9
2025-04-29 09:00:46,724 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MR9
2025-04-29 09:00:46,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 478552.0, 'new_value': 481752.0}, {'field': 'total_amount', 'old_value': 523790.8, 'new_value': 526990.8}, {'field': 'order_count', 'old_value': 68, 'new_value': 69}]
2025-04-29 09:00:46,724 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MV9
2025-04-29 09:00:47,177 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MV9
2025-04-29 09:00:47,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 273089.95, 'new_value': 284452.3}, {'field': 'total_amount', 'old_value': 273089.95, 'new_value': 284452.3}, {'field': 'order_count', 'old_value': 1055, 'new_value': 1111}]
2025-04-29 09:00:47,192 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0A
2025-04-29 09:00:47,583 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0A
2025-04-29 09:00:47,583 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32778.19, 'new_value': 32903.63}, {'field': 'offline_amount', 'old_value': 84453.58, 'new_value': 87061.64}, {'field': 'total_amount', 'old_value': 117231.77, 'new_value': 119965.27}, {'field': 'order_count', 'old_value': 478, 'new_value': 493}]
2025-04-29 09:00:47,599 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2A
2025-04-29 09:00:48,161 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2A
2025-04-29 09:00:48,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203532.0, 'new_value': 223429.0}, {'field': 'total_amount', 'old_value': 220629.0, 'new_value': 240526.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 47}]
2025-04-29 09:00:48,161 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3A
2025-04-29 09:00:48,614 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3A
2025-04-29 09:00:48,614 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24108.42, 'new_value': 25303.76}, {'field': 'total_amount', 'old_value': 24108.42, 'new_value': 25303.76}, {'field': 'order_count', 'old_value': 250, 'new_value': 261}]
2025-04-29 09:00:48,614 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5A
2025-04-29 09:00:49,099 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5A
2025-04-29 09:00:49,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19799.0, 'new_value': 20007.0}, {'field': 'total_amount', 'old_value': 19799.0, 'new_value': 20007.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 74}]
2025-04-29 09:00:49,099 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6A
2025-04-29 09:00:49,505 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6A
2025-04-29 09:00:49,520 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 187629.63, 'new_value': 194219.19}, {'field': 'offline_amount', 'old_value': 153689.08, 'new_value': 157994.08}, {'field': 'total_amount', 'old_value': 341318.71, 'new_value': 352213.27}, {'field': 'order_count', 'old_value': 2955, 'new_value': 3084}]
2025-04-29 09:00:49,520 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCA
2025-04-29 09:00:49,958 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCA
2025-04-29 09:00:49,958 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 144550.14, 'new_value': 148400.23}, {'field': 'offline_amount', 'old_value': 31377.17, 'new_value': 32023.7}, {'field': 'total_amount', 'old_value': 175927.31, 'new_value': 180423.93}, {'field': 'order_count', 'old_value': 725, 'new_value': 740}]
2025-04-29 09:00:49,958 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDA
2025-04-29 09:00:50,489 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDA
2025-04-29 09:00:50,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90158.21, 'new_value': 94004.21}, {'field': 'total_amount', 'old_value': 90158.21, 'new_value': 94004.21}, {'field': 'order_count', 'old_value': 411, 'new_value': 421}]
2025-04-29 09:00:50,489 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEA
2025-04-29 09:00:50,958 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEA
2025-04-29 09:00:50,958 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 188163.79, 'new_value': 195817.04}, {'field': 'offline_amount', 'old_value': 367107.52, 'new_value': 376519.66}, {'field': 'total_amount', 'old_value': 555271.31, 'new_value': 572336.7}, {'field': 'order_count', 'old_value': 4195, 'new_value': 4351}]
2025-04-29 09:00:50,974 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHA
2025-04-29 09:00:51,411 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHA
2025-04-29 09:00:51,411 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10997.5, 'new_value': 11454.5}, {'field': 'offline_amount', 'old_value': 14862.7, 'new_value': 15010.7}, {'field': 'total_amount', 'old_value': 25860.2, 'new_value': 26465.2}, {'field': 'order_count', 'old_value': 75, 'new_value': 80}]
2025-04-29 09:00:51,411 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIA
2025-04-29 09:00:51,880 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIA
2025-04-29 09:00:51,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22403.0, 'new_value': 22960.0}, {'field': 'total_amount', 'old_value': 22403.0, 'new_value': 22960.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 67}]
2025-04-29 09:00:51,895 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMA
2025-04-29 09:00:52,333 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMA
2025-04-29 09:00:52,333 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111042.0, 'new_value': 115596.0}, {'field': 'total_amount', 'old_value': 173295.0, 'new_value': 177849.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 74}]
2025-04-29 09:00:52,333 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQA
2025-04-29 09:00:52,786 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQA
2025-04-29 09:00:52,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41938.78, 'new_value': 43780.36}, {'field': 'total_amount', 'old_value': 41938.78, 'new_value': 43780.36}, {'field': 'order_count', 'old_value': 182, 'new_value': 190}]
2025-04-29 09:00:52,786 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRA
2025-04-29 09:00:53,270 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRA
2025-04-29 09:00:53,270 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70131.54, 'new_value': 72062.25}, {'field': 'total_amount', 'old_value': 70131.54, 'new_value': 72062.25}, {'field': 'order_count', 'old_value': 1953, 'new_value': 2007}]
2025-04-29 09:00:53,270 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSA
2025-04-29 09:00:53,708 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSA
2025-04-29 09:00:53,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260390.94, 'new_value': 267036.93}, {'field': 'total_amount', 'old_value': 260423.94, 'new_value': 267069.93}, {'field': 'order_count', 'old_value': 1958, 'new_value': 2015}]
2025-04-29 09:00:53,708 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTA
2025-04-29 09:00:54,208 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTA
2025-04-29 09:00:54,208 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 365833.0, 'new_value': 384320.0}, {'field': 'offline_amount', 'old_value': 20098.0, 'new_value': 20305.0}, {'field': 'total_amount', 'old_value': 385931.0, 'new_value': 404625.0}, {'field': 'order_count', 'old_value': 3663, 'new_value': 3767}]
2025-04-29 09:00:54,208 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVA
2025-04-29 09:00:54,661 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVA
2025-04-29 09:00:54,661 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126289.19, 'new_value': 126306.19}, {'field': 'offline_amount', 'old_value': 281122.35, 'new_value': 291617.35}, {'field': 'total_amount', 'old_value': 407411.54, 'new_value': 417923.54}, {'field': 'order_count', 'old_value': 3817, 'new_value': 3855}]
2025-04-29 09:00:54,661 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWA
2025-04-29 09:00:55,224 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWA
2025-04-29 09:00:55,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174442.32, 'new_value': 179384.34}, {'field': 'total_amount', 'old_value': 174442.32, 'new_value': 179384.34}, {'field': 'order_count', 'old_value': 805, 'new_value': 831}]
2025-04-29 09:00:55,224 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYA
2025-04-29 09:00:55,723 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYA
2025-04-29 09:00:55,723 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21963.0, 'new_value': 23567.0}, {'field': 'total_amount', 'old_value': 21963.0, 'new_value': 23567.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 22}]
2025-04-29 09:00:55,723 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZA
2025-04-29 09:00:56,161 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZA
2025-04-29 09:00:56,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55803.0, 'new_value': 60242.0}, {'field': 'total_amount', 'old_value': 56200.0, 'new_value': 60639.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 99}]
2025-04-29 09:00:56,161 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3B
2025-04-29 09:00:56,645 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3B
2025-04-29 09:00:56,645 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 238272.0, 'new_value': 248832.0}, {'field': 'total_amount', 'old_value': 238272.0, 'new_value': 248832.0}, {'field': 'order_count', 'old_value': 19856, 'new_value': 20736}]
2025-04-29 09:00:56,645 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5B
2025-04-29 09:00:57,130 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5B
2025-04-29 09:00:57,130 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1627853.48, 'new_value': 1686387.68}, {'field': 'total_amount', 'old_value': 1627853.48, 'new_value': 1686387.68}, {'field': 'order_count', 'old_value': 3423, 'new_value': 3545}]
2025-04-29 09:00:57,130 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8B
2025-04-29 09:00:57,692 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8B
2025-04-29 09:00:57,692 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7603.2, 'new_value': 7719.8}, {'field': 'offline_amount', 'old_value': 19770.53, 'new_value': 20242.13}, {'field': 'total_amount', 'old_value': 27373.73, 'new_value': 27961.93}, {'field': 'order_count', 'old_value': 905, 'new_value': 929}]
2025-04-29 09:00:57,692 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MBB
2025-04-29 09:00:58,161 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MBB
2025-04-29 09:00:58,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43460.36, 'new_value': 45626.56}, {'field': 'total_amount', 'old_value': 43460.36, 'new_value': 45626.56}, {'field': 'order_count', 'old_value': 39, 'new_value': 41}]
2025-04-29 09:00:58,161 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCB
2025-04-29 09:00:58,661 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCB
2025-04-29 09:00:58,661 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 480090.1, 'new_value': 491170.1}, {'field': 'offline_amount', 'old_value': 109776.0, 'new_value': 110968.0}, {'field': 'total_amount', 'old_value': 589866.1, 'new_value': 602138.1}, {'field': 'order_count', 'old_value': 776, 'new_value': 795}]
2025-04-29 09:00:58,661 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEB
2025-04-29 09:00:59,052 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEB
2025-04-29 09:00:59,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 114080.6, 'new_value': 116080.6}, {'field': 'offline_amount', 'old_value': 16442.1, 'new_value': 17157.1}, {'field': 'total_amount', 'old_value': 130522.7, 'new_value': 133237.7}, {'field': 'order_count', 'old_value': 372, 'new_value': 390}]
2025-04-29 09:00:59,052 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGB
2025-04-29 09:00:59,473 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGB
2025-04-29 09:00:59,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83964.0, 'new_value': 86338.0}, {'field': 'total_amount', 'old_value': 86545.0, 'new_value': 88919.0}, {'field': 'order_count', 'old_value': 347, 'new_value': 360}]
2025-04-29 09:00:59,473 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MKB
2025-04-29 09:00:59,942 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MKB
2025-04-29 09:00:59,942 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23909.05, 'new_value': 24731.25}, {'field': 'offline_amount', 'old_value': 39235.14, 'new_value': 40744.14}, {'field': 'total_amount', 'old_value': 63144.19, 'new_value': 65475.39}, {'field': 'order_count', 'old_value': 2638, 'new_value': 2741}]
2025-04-29 09:00:59,942 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOB
2025-04-29 09:01:00,567 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOB
2025-04-29 09:01:00,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138717.28, 'new_value': 138907.28}, {'field': 'total_amount', 'old_value': 138717.28, 'new_value': 138907.28}, {'field': 'order_count', 'old_value': 123, 'new_value': 126}]
2025-04-29 09:01:00,567 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSB
2025-04-29 09:01:01,036 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSB
2025-04-29 09:01:01,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7310.0, 'new_value': 7548.0}, {'field': 'offline_amount', 'old_value': 28979.0, 'new_value': 29646.2}, {'field': 'total_amount', 'old_value': 36289.0, 'new_value': 37194.2}, {'field': 'order_count', 'old_value': 1382, 'new_value': 1418}]
2025-04-29 09:01:01,036 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUB
2025-04-29 09:01:01,442 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUB
2025-04-29 09:01:01,442 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 381799.59, 'new_value': 398904.49}, {'field': 'total_amount', 'old_value': 381800.59, 'new_value': 398905.49}, {'field': 'order_count', 'old_value': 631, 'new_value': 654}]
2025-04-29 09:01:01,442 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYB
2025-04-29 09:01:01,927 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYB
2025-04-29 09:01:01,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 255234.16, 'new_value': 256077.99}, {'field': 'offline_amount', 'old_value': 69780.67, 'new_value': 74602.27}, {'field': 'total_amount', 'old_value': 325014.83, 'new_value': 330680.26}, {'field': 'order_count', 'old_value': 2242, 'new_value': 2293}]
2025-04-29 09:01:01,927 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP5
2025-04-29 09:01:02,380 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP5
2025-04-29 09:01:02,380 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153130.41, 'new_value': 160290.41}, {'field': 'total_amount', 'old_value': 153130.41, 'new_value': 160290.41}, {'field': 'order_count', 'old_value': 5401, 'new_value': 5659}]
2025-04-29 09:01:02,380 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR5
2025-04-29 09:01:02,848 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR5
2025-04-29 09:01:02,848 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13604.77, 'new_value': 14000.52}, {'field': 'offline_amount', 'old_value': 497706.54, 'new_value': 513503.14}, {'field': 'total_amount', 'old_value': 511311.31, 'new_value': 527503.66}, {'field': 'order_count', 'old_value': 2070, 'new_value': 2130}]
2025-04-29 09:01:02,864 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M06
2025-04-29 09:01:03,302 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M06
2025-04-29 09:01:03,302 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28683.79, 'new_value': 29668.59}, {'field': 'offline_amount', 'old_value': 52131.54, 'new_value': 53662.66}, {'field': 'total_amount', 'old_value': 80815.33, 'new_value': 83331.25}, {'field': 'order_count', 'old_value': 2882, 'new_value': 2970}]
2025-04-29 09:01:03,302 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M26
2025-04-29 09:01:03,739 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M26
2025-04-29 09:01:03,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60244.75, 'new_value': 60596.75}, {'field': 'total_amount', 'old_value': 60244.75, 'new_value': 60596.75}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-04-29 09:01:03,739 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M56
2025-04-29 09:01:04,145 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M56
2025-04-29 09:01:04,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83742.48, 'new_value': 84358.48}, {'field': 'total_amount', 'old_value': 92941.98, 'new_value': 93557.98}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-04-29 09:01:04,145 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-04-29 09:01:04,661 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-04-29 09:01:04,661 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 643576.02, 'new_value': 675731.02}, {'field': 'total_amount', 'old_value': 643576.02, 'new_value': 675731.02}, {'field': 'order_count', 'old_value': 852, 'new_value': 878}]
2025-04-29 09:01:04,661 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA6
2025-04-29 09:01:05,098 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA6
2025-04-29 09:01:05,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45649.5, 'new_value': 57889.5}, {'field': 'total_amount', 'old_value': 45649.5, 'new_value': 57889.5}, {'field': 'order_count', 'old_value': 9, 'new_value': 12}]
2025-04-29 09:01:05,098 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC6
2025-04-29 09:01:05,583 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC6
2025-04-29 09:01:05,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126876.44, 'new_value': 131182.09}, {'field': 'total_amount', 'old_value': 126876.44, 'new_value': 131182.09}, {'field': 'order_count', 'old_value': 3689, 'new_value': 3811}]
2025-04-29 09:01:05,583 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH6
2025-04-29 09:01:06,020 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH6
2025-04-29 09:01:06,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17273.0, 'new_value': 26163.0}, {'field': 'total_amount', 'old_value': 17273.0, 'new_value': 26163.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 15}]
2025-04-29 09:01:06,020 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK6
2025-04-29 09:01:06,442 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK6
2025-04-29 09:01:06,442 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 339274.81, 'new_value': 350588.61}, {'field': 'total_amount', 'old_value': 339274.81, 'new_value': 350588.61}, {'field': 'order_count', 'old_value': 8798, 'new_value': 9139}]
2025-04-29 09:01:06,442 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO6
2025-04-29 09:01:06,927 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO6
2025-04-29 09:01:06,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7595.2, 'new_value': 8288.2}, {'field': 'offline_amount', 'old_value': 63286.1, 'new_value': 66486.1}, {'field': 'total_amount', 'old_value': 70881.3, 'new_value': 74774.3}, {'field': 'order_count', 'old_value': 62, 'new_value': 67}]
2025-04-29 09:01:06,927 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ6
2025-04-29 09:01:07,333 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ6
2025-04-29 09:01:07,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155565.78, 'new_value': 161498.39}, {'field': 'total_amount', 'old_value': 155565.78, 'new_value': 161498.39}, {'field': 'order_count', 'old_value': 5507, 'new_value': 5713}]
2025-04-29 09:01:07,333 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M77
2025-04-29 09:01:07,786 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M77
2025-04-29 09:01:07,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8498.0, 'new_value': 8578.0}, {'field': 'total_amount', 'old_value': 8499.0, 'new_value': 8579.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 62}]
2025-04-29 09:01:07,786 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M97
2025-04-29 09:01:08,333 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M97
2025-04-29 09:01:08,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53682.15, 'new_value': 55727.45}, {'field': 'total_amount', 'old_value': 53682.15, 'new_value': 55727.45}, {'field': 'order_count', 'old_value': 2475, 'new_value': 2566}]
2025-04-29 09:01:08,333 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD7
2025-04-29 09:01:08,864 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD7
2025-04-29 09:01:08,864 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26339.8, 'new_value': 26629.8}, {'field': 'offline_amount', 'old_value': 48991.6, 'new_value': 59391.6}, {'field': 'total_amount', 'old_value': 75331.4, 'new_value': 86021.4}, {'field': 'order_count', 'old_value': 96, 'new_value': 103}]
2025-04-29 09:01:08,864 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG7
2025-04-29 09:01:09,302 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG7
2025-04-29 09:01:09,302 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42230.3, 'new_value': 44303.5}, {'field': 'total_amount', 'old_value': 42420.3, 'new_value': 44493.5}, {'field': 'order_count', 'old_value': 415, 'new_value': 431}]
2025-04-29 09:01:09,302 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH7
2025-04-29 09:01:09,755 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH7
2025-04-29 09:01:09,755 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 128753.0, 'new_value': 134196.0}, {'field': 'offline_amount', 'old_value': 111060.0, 'new_value': 113641.0}, {'field': 'total_amount', 'old_value': 239813.0, 'new_value': 247837.0}, {'field': 'order_count', 'old_value': 9305, 'new_value': 9408}]
2025-04-29 09:01:09,755 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP7
2025-04-29 09:01:10,208 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP7
2025-04-29 09:01:10,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136657.4, 'new_value': 138974.6}, {'field': 'total_amount', 'old_value': 136657.4, 'new_value': 138974.6}, {'field': 'order_count', 'old_value': 253, 'new_value': 258}]
2025-04-29 09:01:10,208 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ7
2025-04-29 09:01:10,630 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ7
2025-04-29 09:01:10,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 297988.2, 'new_value': 308695.0}, {'field': 'total_amount', 'old_value': 297988.2, 'new_value': 308695.0}, {'field': 'order_count', 'old_value': 8606, 'new_value': 8920}]
2025-04-29 09:01:10,630 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU7
2025-04-29 09:01:11,083 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU7
2025-04-29 09:01:11,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110472.17, 'new_value': 116723.37}, {'field': 'total_amount', 'old_value': 236573.37, 'new_value': 242824.57}, {'field': 'order_count', 'old_value': 6513, 'new_value': 6703}]
2025-04-29 09:01:11,083 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY7
2025-04-29 09:01:11,505 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY7
2025-04-29 09:01:11,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49088.8, 'new_value': 49447.8}, {'field': 'total_amount', 'old_value': 49088.8, 'new_value': 49447.8}, {'field': 'order_count', 'old_value': 66, 'new_value': 67}]
2025-04-29 09:01:11,505 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ7
2025-04-29 09:01:12,020 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ7
2025-04-29 09:01:12,020 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46935.87, 'new_value': 48249.87}, {'field': 'offline_amount', 'old_value': 56252.19, 'new_value': 56907.44}, {'field': 'total_amount', 'old_value': 103188.06, 'new_value': 105157.31}, {'field': 'order_count', 'old_value': 3726, 'new_value': 3774}]
2025-04-29 09:01:12,020 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M18
2025-04-29 09:01:12,458 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M18
2025-04-29 09:01:12,458 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82916.06, 'new_value': 86004.95}, {'field': 'offline_amount', 'old_value': 120917.48, 'new_value': 125482.77}, {'field': 'total_amount', 'old_value': 203833.54, 'new_value': 211487.72}, {'field': 'order_count', 'old_value': 8194, 'new_value': 8505}]
2025-04-29 09:01:12,458 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M28
2025-04-29 09:01:12,958 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M28
2025-04-29 09:01:12,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109692.0, 'new_value': 112336.0}, {'field': 'total_amount', 'old_value': 110439.0, 'new_value': 113083.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 53}]
2025-04-29 09:01:12,958 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M38
2025-04-29 09:01:13,395 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M38
2025-04-29 09:01:13,395 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51693.2, 'new_value': 53998.1}, {'field': 'total_amount', 'old_value': 55441.2, 'new_value': 57746.1}, {'field': 'order_count', 'old_value': 58, 'new_value': 65}]
2025-04-29 09:01:13,395 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M58
2025-04-29 09:01:13,786 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M58
2025-04-29 09:01:13,786 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 150239.63, 'new_value': 152286.73}, {'field': 'offline_amount', 'old_value': 117358.92, 'new_value': 120145.92}, {'field': 'total_amount', 'old_value': 267598.55, 'new_value': 272432.65}, {'field': 'order_count', 'old_value': 2691, 'new_value': 2755}]
2025-04-29 09:01:13,786 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M88
2025-04-29 09:01:14,239 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M88
2025-04-29 09:01:14,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 395222.79, 'new_value': 405039.02}, {'field': 'total_amount', 'old_value': 433548.57, 'new_value': 443364.8}, {'field': 'order_count', 'old_value': 1785, 'new_value': 1823}]
2025-04-29 09:01:14,239 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8H
2025-04-29 09:01:14,692 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8H
2025-04-29 09:01:14,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 499242.93, 'new_value': 513212.75}, {'field': 'total_amount', 'old_value': 518306.43, 'new_value': 532276.25}, {'field': 'order_count', 'old_value': 2020, 'new_value': 2070}]
2025-04-29 09:01:14,692 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9H
2025-04-29 09:01:15,161 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9H
2025-04-29 09:01:15,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72968.0, 'new_value': 75791.0}, {'field': 'total_amount', 'old_value': 79387.0, 'new_value': 82210.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 52}]
2025-04-29 09:01:15,161 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCH
2025-04-29 09:01:15,630 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCH
2025-04-29 09:01:15,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109263.5, 'new_value': 128013.5}, {'field': 'total_amount', 'old_value': 111663.5, 'new_value': 130413.5}, {'field': 'order_count', 'old_value': 24, 'new_value': 29}]
2025-04-29 09:01:15,630 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFH
2025-04-29 09:01:16,052 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFH
2025-04-29 09:01:16,052 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 690965.97, 'new_value': 710208.47}, {'field': 'total_amount', 'old_value': 691496.18, 'new_value': 710738.68}, {'field': 'order_count', 'old_value': 1636, 'new_value': 1699}]
2025-04-29 09:01:16,052 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPH
2025-04-29 09:01:16,536 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPH
2025-04-29 09:01:16,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107908.0, 'new_value': 112185.0}, {'field': 'total_amount', 'old_value': 111266.0, 'new_value': 115543.0}, {'field': 'order_count', 'old_value': 201, 'new_value': 197}]
2025-04-29 09:01:16,536 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRH
2025-04-29 09:01:16,973 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRH
2025-04-29 09:01:16,973 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 302.0, 'new_value': 322.0}, {'field': 'total_amount', 'old_value': 16728.6, 'new_value': 16748.6}, {'field': 'order_count', 'old_value': 167, 'new_value': 169}]
2025-04-29 09:01:16,973 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTH
2025-04-29 09:01:17,473 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTH
2025-04-29 09:01:17,473 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50743.73, 'new_value': 52600.14}, {'field': 'offline_amount', 'old_value': 442354.91, 'new_value': 452819.39}, {'field': 'total_amount', 'old_value': 493098.64, 'new_value': 505419.53}, {'field': 'order_count', 'old_value': 2220, 'new_value': 2274}]
2025-04-29 09:01:17,473 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVH
2025-04-29 09:01:18,036 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVH
2025-04-29 09:01:18,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69393.84, 'new_value': 77966.84}, {'field': 'total_amount', 'old_value': 69393.84, 'new_value': 77966.84}, {'field': 'order_count', 'old_value': 3782, 'new_value': 4286}]
2025-04-29 09:01:18,036 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWH
2025-04-29 09:01:18,473 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWH
2025-04-29 09:01:18,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248003.23, 'new_value': 260242.56}, {'field': 'total_amount', 'old_value': 248003.23, 'new_value': 260242.56}, {'field': 'order_count', 'old_value': 1374, 'new_value': 1443}]
2025-04-29 09:01:18,489 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZH
2025-04-29 09:01:18,942 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZH
2025-04-29 09:01:18,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26409.0, 'new_value': 26717.0}, {'field': 'total_amount', 'old_value': 27612.8, 'new_value': 27920.8}, {'field': 'order_count', 'old_value': 282, 'new_value': 285}]
2025-04-29 09:01:18,942 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0I
2025-04-29 09:01:19,364 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0I
2025-04-29 09:01:19,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94001.98, 'new_value': 100054.98}, {'field': 'total_amount', 'old_value': 96299.98, 'new_value': 102352.98}, {'field': 'order_count', 'old_value': 45, 'new_value': 48}]
2025-04-29 09:01:19,364 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1I
2025-04-29 09:01:19,801 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1I
2025-04-29 09:01:19,801 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50036.36, 'new_value': 51616.93}, {'field': 'offline_amount', 'old_value': 40780.03, 'new_value': 41553.93}, {'field': 'total_amount', 'old_value': 90816.39, 'new_value': 93170.86}, {'field': 'order_count', 'old_value': 6893, 'new_value': 7050}]
2025-04-29 09:01:19,801 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3I
2025-04-29 09:01:20,255 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3I
2025-04-29 09:01:20,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 644722.0, 'new_value': 682559.0}, {'field': 'total_amount', 'old_value': 644722.0, 'new_value': 682559.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 149}]
2025-04-29 09:01:20,255 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4I
2025-04-29 09:01:20,676 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4I
2025-04-29 09:01:20,676 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 595364.45, 'new_value': 611890.45}, {'field': 'total_amount', 'old_value': 595364.45, 'new_value': 611890.45}, {'field': 'order_count', 'old_value': 2004, 'new_value': 2065}]
2025-04-29 09:01:20,676 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDI
2025-04-29 09:01:21,114 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDI
2025-04-29 09:01:21,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124022.0, 'new_value': 126746.0}, {'field': 'total_amount', 'old_value': 124022.0, 'new_value': 126746.0}, {'field': 'order_count', 'old_value': 556, 'new_value': 568}]
2025-04-29 09:01:21,114 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFI
2025-04-29 09:01:21,567 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFI
2025-04-29 09:01:21,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 580260.0, 'new_value': 607416.0}, {'field': 'total_amount', 'old_value': 591298.0, 'new_value': 618454.0}, {'field': 'order_count', 'old_value': 509, 'new_value': 530}]
2025-04-29 09:01:21,567 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MII
2025-04-29 09:01:22,020 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MII
2025-04-29 09:01:22,020 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12593.04, 'new_value': 14314.17}, {'field': 'offline_amount', 'old_value': 69886.9, 'new_value': 78024.36}, {'field': 'total_amount', 'old_value': 82479.94, 'new_value': 92338.53}, {'field': 'order_count', 'old_value': 2123, 'new_value': 2364}]
2025-04-29 09:01:22,020 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLI
2025-04-29 09:01:22,458 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLI
2025-04-29 09:01:22,458 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 118616.56, 'new_value': 125033.66}, {'field': 'offline_amount', 'old_value': 116465.45, 'new_value': 121224.05}, {'field': 'total_amount', 'old_value': 235082.01, 'new_value': 246257.71}, {'field': 'order_count', 'old_value': 5382, 'new_value': 5593}]
2025-04-29 09:01:22,458 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOI
2025-04-29 09:01:22,958 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOI
2025-04-29 09:01:22,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257025.5, 'new_value': 259215.3}, {'field': 'total_amount', 'old_value': 257025.5, 'new_value': 259215.3}, {'field': 'order_count', 'old_value': 5491, 'new_value': 5541}]
2025-04-29 09:01:22,958 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSI
2025-04-29 09:01:23,411 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSI
2025-04-29 09:01:23,411 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1789078.0, 'new_value': 1844796.0}, {'field': 'total_amount', 'old_value': 1789078.0, 'new_value': 1844796.0}, {'field': 'order_count', 'old_value': 7292, 'new_value': 7469}]
2025-04-29 09:01:23,411 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVI
2025-04-29 09:01:23,864 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVI
2025-04-29 09:01:23,864 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65720.0, 'new_value': 69165.0}, {'field': 'total_amount', 'old_value': 65730.0, 'new_value': 69175.0}, {'field': 'order_count', 'old_value': 248, 'new_value': 261}]
2025-04-29 09:01:23,864 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZI
2025-04-29 09:01:24,380 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZI
2025-04-29 09:01:24,380 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240011.6, 'new_value': 244410.0}, {'field': 'total_amount', 'old_value': 240011.6, 'new_value': 244410.0}, {'field': 'order_count', 'old_value': 996, 'new_value': 1021}]
2025-04-29 09:01:24,380 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0J
2025-04-29 09:01:24,770 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0J
2025-04-29 09:01:24,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134641.22, 'new_value': 140193.59}, {'field': 'total_amount', 'old_value': 157908.6, 'new_value': 163460.97}, {'field': 'order_count', 'old_value': 12327, 'new_value': 12627}]
2025-04-29 09:01:24,770 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2J
2025-04-29 09:01:25,192 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2J
2025-04-29 09:01:25,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127308.81, 'new_value': 132638.81}, {'field': 'total_amount', 'old_value': 127408.81, 'new_value': 132738.81}, {'field': 'order_count', 'old_value': 77, 'new_value': 80}]
2025-04-29 09:01:25,192 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-04-29 09:01:25,614 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-04-29 09:01:25,614 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29032.0, 'new_value': 30132.0}, {'field': 'total_amount', 'old_value': 29032.0, 'new_value': 30132.0}, {'field': 'order_count', 'old_value': 148, 'new_value': 154}]
2025-04-29 09:01:25,614 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8J
2025-04-29 09:01:26,083 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8J
2025-04-29 09:01:26,083 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4987.0, 'new_value': 5235.0}, {'field': 'offline_amount', 'old_value': 23969.0, 'new_value': 24789.0}, {'field': 'total_amount', 'old_value': 28956.0, 'new_value': 30024.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 149}]
2025-04-29 09:01:26,083 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCJ
2025-04-29 09:01:26,536 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCJ
2025-04-29 09:01:26,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82297.0, 'new_value': 84247.0}, {'field': 'total_amount', 'old_value': 113994.0, 'new_value': 115944.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 152}]
2025-04-29 09:01:26,536 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEJ
2025-04-29 09:01:27,098 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEJ
2025-04-29 09:01:27,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1035423.0, 'new_value': 1064403.0}, {'field': 'total_amount', 'old_value': 1035423.0, 'new_value': 1064403.0}, {'field': 'order_count', 'old_value': 4400, 'new_value': 4524}]
2025-04-29 09:01:27,098 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMJ
2025-04-29 09:01:27,723 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMJ
2025-04-29 09:01:27,723 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243062.38, 'new_value': 248286.38}, {'field': 'total_amount', 'old_value': 243062.38, 'new_value': 248286.38}, {'field': 'order_count', 'old_value': 51, 'new_value': 53}]
2025-04-29 09:01:27,723 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPJ
2025-04-29 09:01:28,223 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPJ
2025-04-29 09:01:28,223 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16716.0, 'new_value': 22516.0}, {'field': 'total_amount', 'old_value': 32987.0, 'new_value': 38787.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-04-29 09:01:28,223 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSJ
2025-04-29 09:01:28,801 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSJ
2025-04-29 09:01:28,801 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1686143.62, 'new_value': 1764704.12}, {'field': 'offline_amount', 'old_value': 283352.0, 'new_value': 284675.0}, {'field': 'total_amount', 'old_value': 1969495.62, 'new_value': 2049379.12}, {'field': 'order_count', 'old_value': 9602, 'new_value': 9902}]
2025-04-29 09:01:28,801 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWJ
2025-04-29 09:01:29,270 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWJ
2025-04-29 09:01:29,270 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75215.4, 'new_value': 76038.0}, {'field': 'total_amount', 'old_value': 75215.4, 'new_value': 76038.0}, {'field': 'order_count', 'old_value': 2190, 'new_value': 2215}]
2025-04-29 09:01:29,270 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M6
2025-04-29 09:01:29,723 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M6
2025-04-29 09:01:29,723 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109942.1, 'new_value': 114261.0}, {'field': 'total_amount', 'old_value': 109942.1, 'new_value': 114261.0}, {'field': 'order_count', 'old_value': 538, 'new_value': 556}]
2025-04-29 09:01:29,723 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M7
2025-04-29 09:01:30,208 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M7
2025-04-29 09:01:30,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184582.06, 'new_value': 189992.36}, {'field': 'total_amount', 'old_value': 184582.06, 'new_value': 189992.36}, {'field': 'order_count', 'old_value': 758, 'new_value': 781}]
2025-04-29 09:01:30,208 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M8
2025-04-29 09:01:30,661 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M8
2025-04-29 09:01:30,661 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39612.92, 'new_value': 41780.92}, {'field': 'total_amount', 'old_value': 72249.3, 'new_value': 74417.3}, {'field': 'order_count', 'old_value': 2651, 'new_value': 2745}]
2025-04-29 09:01:30,661 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M9
2025-04-29 09:01:31,145 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M9
2025-04-29 09:01:31,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 183514.1, 'new_value': 189605.3}, {'field': 'total_amount', 'old_value': 183514.1, 'new_value': 189605.3}, {'field': 'order_count', 'old_value': 878, 'new_value': 905}]
2025-04-29 09:01:31,145 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC
2025-04-29 09:01:31,567 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC
2025-04-29 09:01:31,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170191.0, 'new_value': 176621.0}, {'field': 'total_amount', 'old_value': 170191.0, 'new_value': 176621.0}, {'field': 'order_count', 'old_value': 12168, 'new_value': 12682}]
2025-04-29 09:01:31,567 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME
2025-04-29 09:01:31,989 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME
2025-04-29 09:01:31,989 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48502.83, 'new_value': 49617.73}, {'field': 'offline_amount', 'old_value': 46993.61, 'new_value': 48581.32}, {'field': 'total_amount', 'old_value': 95496.44, 'new_value': 98199.05}, {'field': 'order_count', 'old_value': 5043, 'new_value': 5195}]
2025-04-29 09:01:31,989 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG
2025-04-29 09:01:32,473 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG
2025-04-29 09:01:32,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1053112.0, 'new_value': 1083491.0}, {'field': 'total_amount', 'old_value': 1053277.0, 'new_value': 1083656.0}, {'field': 'order_count', 'old_value': 1224, 'new_value': 1269}]
2025-04-29 09:01:32,473 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH
2025-04-29 09:01:33,020 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH
2025-04-29 09:01:33,020 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11643.24, 'new_value': 12318.46}, {'field': 'offline_amount', 'old_value': 146803.0, 'new_value': 157888.0}, {'field': 'total_amount', 'old_value': 158446.24, 'new_value': 170206.46}, {'field': 'order_count', 'old_value': 84, 'new_value': 88}]
2025-04-29 09:01:33,020 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI
2025-04-29 09:01:33,567 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI
2025-04-29 09:01:33,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 472726.14, 'new_value': 484315.42}, {'field': 'total_amount', 'old_value': 472726.14, 'new_value': 484315.42}, {'field': 'order_count', 'old_value': 3614, 'new_value': 3766}]
2025-04-29 09:01:33,567 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ
2025-04-29 09:01:34,051 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ
2025-04-29 09:01:34,051 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78226.97, 'new_value': 81587.7}, {'field': 'offline_amount', 'old_value': 944374.97, 'new_value': 993150.75}, {'field': 'total_amount', 'old_value': 994934.05, 'new_value': 1043709.83}, {'field': 'order_count', 'old_value': 4095, 'new_value': 4279}]
2025-04-29 09:01:34,051 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML
2025-04-29 09:01:34,520 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML
2025-04-29 09:01:34,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137724.0, 'new_value': 141625.0}, {'field': 'total_amount', 'old_value': 162895.0, 'new_value': 166796.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 62}]
2025-04-29 09:01:34,520 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN
2025-04-29 09:01:34,958 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN
2025-04-29 09:01:34,958 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30955.02, 'new_value': 31269.92}, {'field': 'total_amount', 'old_value': 30955.02, 'new_value': 31269.92}, {'field': 'order_count', 'old_value': 984, 'new_value': 1015}]
2025-04-29 09:01:34,958 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO
2025-04-29 09:01:35,364 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO
2025-04-29 09:01:35,380 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50676.0, 'new_value': 51475.0}, {'field': 'offline_amount', 'old_value': 45523.98, 'new_value': 47332.12}, {'field': 'total_amount', 'old_value': 96199.98, 'new_value': 98807.12}, {'field': 'order_count', 'old_value': 337, 'new_value': 350}]
2025-04-29 09:01:35,380 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP
2025-04-29 09:01:35,848 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP
2025-04-29 09:01:35,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87255.64, 'new_value': 91534.89}, {'field': 'total_amount', 'old_value': 89487.05, 'new_value': 93766.3}, {'field': 'order_count', 'old_value': 471, 'new_value': 491}]
2025-04-29 09:01:35,864 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ
2025-04-29 09:01:36,270 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ
2025-04-29 09:01:36,270 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 295990.26, 'new_value': 302709.36}, {'field': 'offline_amount', 'old_value': 1474393.98, 'new_value': 1517846.8}, {'field': 'total_amount', 'old_value': 1770384.24, 'new_value': 1820556.16}, {'field': 'order_count', 'old_value': 9078, 'new_value': 9357}]
2025-04-29 09:01:36,270 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MR
2025-04-29 09:01:36,708 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MR
2025-04-29 09:01:36,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51908.0, 'new_value': 51962.0}, {'field': 'total_amount', 'old_value': 51908.0, 'new_value': 51962.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 56}]
2025-04-29 09:01:36,708 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS
2025-04-29 09:01:37,145 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS
2025-04-29 09:01:37,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113767.0, 'new_value': 116897.0}, {'field': 'total_amount', 'old_value': 115659.0, 'new_value': 118789.0}, {'field': 'order_count', 'old_value': 556, 'new_value': 574}]
2025-04-29 09:01:37,145 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT
2025-04-29 09:01:37,629 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT
2025-04-29 09:01:37,629 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 115715.86, 'new_value': 119827.95}, {'field': 'offline_amount', 'old_value': 131530.85, 'new_value': 136391.52}, {'field': 'total_amount', 'old_value': 247246.71, 'new_value': 256219.47}, {'field': 'order_count', 'old_value': 6364, 'new_value': 6582}]
2025-04-29 09:01:37,629 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ
2025-04-29 09:01:38,051 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ
2025-04-29 09:01:38,051 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40114.21, 'new_value': 41942.31}, {'field': 'offline_amount', 'old_value': 98436.0, 'new_value': 100446.0}, {'field': 'total_amount', 'old_value': 138550.21, 'new_value': 142388.31}, {'field': 'order_count', 'old_value': 1713, 'new_value': 1777}]
2025-04-29 09:01:38,051 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M01
2025-04-29 09:01:38,536 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M01
2025-04-29 09:01:38,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69352.33, 'new_value': 70665.83}, {'field': 'total_amount', 'old_value': 71183.71, 'new_value': 72497.21}, {'field': 'order_count', 'old_value': 634, 'new_value': 642}]
2025-04-29 09:01:38,536 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M11
2025-04-29 09:01:38,989 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M11
2025-04-29 09:01:38,989 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11888.0, 'new_value': 11951.0}, {'field': 'offline_amount', 'old_value': 81404.0, 'new_value': 85439.0}, {'field': 'total_amount', 'old_value': 93292.0, 'new_value': 97390.0}, {'field': 'order_count', 'old_value': 696, 'new_value': 719}]
2025-04-29 09:01:38,989 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M21
2025-04-29 09:01:39,551 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M21
2025-04-29 09:01:39,551 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75827.0, 'new_value': 76827.0}, {'field': 'offline_amount', 'old_value': 329542.0, 'new_value': 339604.0}, {'field': 'total_amount', 'old_value': 405369.0, 'new_value': 416431.0}, {'field': 'order_count', 'old_value': 1617, 'new_value': 1667}]
2025-04-29 09:01:39,551 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M31
2025-04-29 09:01:40,020 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M31
2025-04-29 09:01:40,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 563227.4, 'new_value': 563233.4}, {'field': 'total_amount', 'old_value': 563227.4, 'new_value': 563233.4}, {'field': 'order_count', 'old_value': 181, 'new_value': 19775}]
2025-04-29 09:01:40,020 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M41
2025-04-29 09:01:40,473 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M41
2025-04-29 09:01:40,473 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 10, 'new_value': 13}]
2025-04-29 09:01:40,473 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M51
2025-04-29 09:01:40,911 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M51
2025-04-29 09:01:40,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 247940.2, 'new_value': 257323.3}, {'field': 'total_amount', 'old_value': 247940.2, 'new_value': 257323.3}, {'field': 'order_count', 'old_value': 1121, 'new_value': 1162}]
2025-04-29 09:01:40,911 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M61
2025-04-29 09:01:41,333 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M61
2025-04-29 09:01:41,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75528.0, 'new_value': 84799.0}, {'field': 'total_amount', 'old_value': 222674.0, 'new_value': 231945.0}, {'field': 'order_count', 'old_value': 512, 'new_value': 527}]
2025-04-29 09:01:41,333 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M91
2025-04-29 09:01:41,723 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M91
2025-04-29 09:01:41,723 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 163824.0, 'new_value': 169026.0}, {'field': 'offline_amount', 'old_value': 234769.0, 'new_value': 241332.0}, {'field': 'total_amount', 'old_value': 398593.0, 'new_value': 410358.0}, {'field': 'order_count', 'old_value': 925, 'new_value': 953}]
2025-04-29 09:01:41,723 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC1
2025-04-29 09:01:42,176 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC1
2025-04-29 09:01:42,176 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12171.0, 'new_value': 12240.0}, {'field': 'offline_amount', 'old_value': 156660.0, 'new_value': 174740.0}, {'field': 'total_amount', 'old_value': 168831.0, 'new_value': 186980.0}, {'field': 'order_count', 'old_value': 288, 'new_value': 312}]
2025-04-29 09:01:42,176 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD1
2025-04-29 09:01:42,942 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD1
2025-04-29 09:01:42,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16310.0, 'new_value': 16643.0}, {'field': 'total_amount', 'old_value': 16310.0, 'new_value': 16643.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 37}]
2025-04-29 09:01:42,942 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF1
2025-04-29 09:01:43,442 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF1
2025-04-29 09:01:43,442 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56150.42, 'new_value': 57923.38}, {'field': 'offline_amount', 'old_value': 68099.4, 'new_value': 71118.51}, {'field': 'total_amount', 'old_value': 124249.82, 'new_value': 129041.89}, {'field': 'order_count', 'old_value': 6280, 'new_value': 6528}]
2025-04-29 09:01:43,442 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG1
2025-04-29 09:01:43,973 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG1
2025-04-29 09:01:43,973 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3500.0, 'new_value': 3610.0}, {'field': 'offline_amount', 'old_value': 43300.0, 'new_value': 44420.0}, {'field': 'total_amount', 'old_value': 46800.0, 'new_value': 48030.0}, {'field': 'order_count', 'old_value': 637, 'new_value': 650}]
2025-04-29 09:01:43,973 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH1
2025-04-29 09:01:44,504 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH1
2025-04-29 09:01:44,504 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60563.0, 'new_value': 68058.0}, {'field': 'total_amount', 'old_value': 60563.0, 'new_value': 68058.0}, {'field': 'order_count', 'old_value': 13071, 'new_value': 14460}]
2025-04-29 09:01:44,504 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI1
2025-04-29 09:01:44,958 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI1
2025-04-29 09:01:44,958 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 3244.0}, {'field': 'offline_amount', 'old_value': 90850.0, 'new_value': 98848.0}, {'field': 'total_amount', 'old_value': 90850.0, 'new_value': 102092.0}, {'field': 'order_count', 'old_value': 13071, 'new_value': 14460}]
2025-04-29 09:01:44,958 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML1
2025-04-29 09:01:45,583 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML1
2025-04-29 09:01:45,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24888.9, 'new_value': 25996.9}, {'field': 'total_amount', 'old_value': 24888.9, 'new_value': 25996.9}, {'field': 'order_count', 'old_value': 130, 'new_value': 135}]
2025-04-29 09:01:45,583 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN1
2025-04-29 09:01:46,051 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN1
2025-04-29 09:01:46,051 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6005.0, 'new_value': 6666.48}, {'field': 'offline_amount', 'old_value': 48393.8, 'new_value': 51983.8}, {'field': 'total_amount', 'old_value': 54398.8, 'new_value': 58650.28}, {'field': 'order_count', 'old_value': 100, 'new_value': 106}]
2025-04-29 09:01:46,051 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO1
2025-04-29 09:01:46,458 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO1
2025-04-29 09:01:46,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235441.0, 'new_value': 246322.0}, {'field': 'total_amount', 'old_value': 235441.0, 'new_value': 246322.0}, {'field': 'order_count', 'old_value': 25358, 'new_value': 26568}]
2025-04-29 09:01:46,458 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP1
2025-04-29 09:01:46,926 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP1
2025-04-29 09:01:46,926 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 354463.0, 'new_value': 366861.0}, {'field': 'total_amount', 'old_value': 365253.0, 'new_value': 377651.0}, {'field': 'order_count', 'old_value': 292, 'new_value': 300}]
2025-04-29 09:01:46,926 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU1
2025-04-29 09:01:47,333 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU1
2025-04-29 09:01:47,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 726131.41, 'new_value': 734626.41}, {'field': 'total_amount', 'old_value': 726131.41, 'new_value': 734626.41}, {'field': 'order_count', 'old_value': 647, 'new_value': 652}]
2025-04-29 09:01:47,333 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV1
2025-04-29 09:01:47,801 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV1
2025-04-29 09:01:47,801 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13163.3, 'new_value': 13373.57}, {'field': 'offline_amount', 'old_value': 31711.0, 'new_value': 31870.0}, {'field': 'total_amount', 'old_value': 44874.3, 'new_value': 45243.57}, {'field': 'order_count', 'old_value': 274, 'new_value': 279}]
2025-04-29 09:01:47,801 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX1
2025-04-29 09:01:48,254 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX1
2025-04-29 09:01:48,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3605940.0, 'new_value': 4183940.0}, {'field': 'total_amount', 'old_value': 5187391.0, 'new_value': 5765391.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-04-29 09:01:48,254 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ1
2025-04-29 09:01:48,754 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ1
2025-04-29 09:01:48,754 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98130.0, 'new_value': 100422.0}, {'field': 'total_amount', 'old_value': 98130.0, 'new_value': 100422.0}, {'field': 'order_count', 'old_value': 6709, 'new_value': 6890}]
2025-04-29 09:01:48,754 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M02
2025-04-29 09:01:49,176 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M02
2025-04-29 09:01:49,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124992.78, 'new_value': 128885.43}, {'field': 'total_amount', 'old_value': 124992.78, 'new_value': 128885.43}, {'field': 'order_count', 'old_value': 8886, 'new_value': 9166}]
2025-04-29 09:01:49,176 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M12
2025-04-29 09:01:49,676 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M12
2025-04-29 09:01:49,676 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 659196.13, 'new_value': 679036.73}, {'field': 'total_amount', 'old_value': 659196.13, 'new_value': 679036.73}, {'field': 'order_count', 'old_value': 3704, 'new_value': 3802}]
2025-04-29 09:01:49,676 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M72
2025-04-29 09:01:50,145 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M72
2025-04-29 09:01:50,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44247.0, 'new_value': 44436.0}, {'field': 'total_amount', 'old_value': 44247.0, 'new_value': 44436.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-04-29 09:01:50,145 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MD2
2025-04-29 09:01:50,598 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MD2
2025-04-29 09:01:50,598 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26977.6, 'new_value': 28195.3}, {'field': 'offline_amount', 'old_value': 75059.5, 'new_value': 77607.9}, {'field': 'total_amount', 'old_value': 102037.1, 'new_value': 105803.2}, {'field': 'order_count', 'old_value': 4277, 'new_value': 4434}]
2025-04-29 09:01:50,598 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MF2
2025-04-29 09:01:51,083 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MF2
2025-04-29 09:01:51,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107769.0, 'new_value': 120289.0}, {'field': 'total_amount', 'old_value': 107769.0, 'new_value': 120289.0}, {'field': 'order_count', 'old_value': 287, 'new_value': 320}]
2025-04-29 09:01:51,083 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MJ2
2025-04-29 09:01:51,520 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MJ2
2025-04-29 09:01:51,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216098.0, 'new_value': 225270.0}, {'field': 'total_amount', 'old_value': 216098.0, 'new_value': 225270.0}, {'field': 'order_count', 'old_value': 7674, 'new_value': 7979}]
2025-04-29 09:01:51,520 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MO2
2025-04-29 09:01:52,004 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MO2
2025-04-29 09:01:52,004 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11329.9, 'new_value': 11507.8}, {'field': 'offline_amount', 'old_value': 32886.42, 'new_value': 34160.54}, {'field': 'total_amount', 'old_value': 44216.32, 'new_value': 45668.34}, {'field': 'order_count', 'old_value': 456, 'new_value': 469}]
2025-04-29 09:01:52,004 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MQ2
2025-04-29 09:01:52,489 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MQ2
2025-04-29 09:01:52,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26758.32, 'new_value': 29985.49}, {'field': 'offline_amount', 'old_value': 25877.12, 'new_value': 29070.77}, {'field': 'total_amount', 'old_value': 52635.44, 'new_value': 59056.26}, {'field': 'order_count', 'old_value': 2883, 'new_value': 3248}]
2025-04-29 09:01:52,489 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MS2
2025-04-29 09:01:52,911 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MS2
2025-04-29 09:01:52,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 386190.0, 'new_value': 398870.0}, {'field': 'total_amount', 'old_value': 386190.0, 'new_value': 398870.0}, {'field': 'order_count', 'old_value': 148, 'new_value': 154}]
2025-04-29 09:01:52,911 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M46
2025-04-29 09:01:53,489 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M46
2025-04-29 09:01:53,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13595.11, 'new_value': 13967.48}, {'field': 'offline_amount', 'old_value': 119038.35, 'new_value': 134145.78}, {'field': 'total_amount', 'old_value': 132633.46, 'new_value': 148113.26}, {'field': 'order_count', 'old_value': 2015, 'new_value': 2278}]
2025-04-29 09:01:53,489 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M56
2025-04-29 09:01:53,942 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M56
2025-04-29 09:01:53,942 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24502.69, 'new_value': 25560.24}, {'field': 'offline_amount', 'old_value': 18871.55, 'new_value': 19494.55}, {'field': 'total_amount', 'old_value': 43374.24, 'new_value': 45054.79}, {'field': 'order_count', 'old_value': 2250, 'new_value': 2335}]
2025-04-29 09:01:53,942 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB6
2025-04-29 09:01:54,395 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB6
2025-04-29 09:01:54,395 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-04-29 09:01:54,395 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME6
2025-04-29 09:01:54,832 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME6
2025-04-29 09:01:54,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4722361.57, 'new_value': 4871110.57}, {'field': 'total_amount', 'old_value': 4722361.57, 'new_value': 4871110.57}, {'field': 'order_count', 'old_value': 140787, 'new_value': 144336}]
2025-04-29 09:01:54,832 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH6
2025-04-29 09:01:55,301 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH6
2025-04-29 09:01:55,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260707.0, 'new_value': 261387.0}, {'field': 'total_amount', 'old_value': 260710.0, 'new_value': 261390.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 68}]
2025-04-29 09:01:55,301 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK6
2025-04-29 09:01:55,801 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK6
2025-04-29 09:01:55,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 302146.3, 'new_value': 306784.5}, {'field': 'total_amount', 'old_value': 302146.3, 'new_value': 306784.5}, {'field': 'order_count', 'old_value': 99, 'new_value': 101}]
2025-04-29 09:01:55,801 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN6
2025-04-29 09:01:56,223 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN6
2025-04-29 09:01:56,223 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103316.0, 'new_value': 106457.0}, {'field': 'offline_amount', 'old_value': 1227758.0, 'new_value': 1270917.0}, {'field': 'total_amount', 'old_value': 1331074.0, 'new_value': 1377374.0}, {'field': 'order_count', 'old_value': 30886, 'new_value': 32084}]
2025-04-29 09:01:56,223 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP6
2025-04-29 09:01:56,661 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP6
2025-04-29 09:01:56,661 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110313.4, 'new_value': 120625.8}, {'field': 'total_amount', 'old_value': 110313.4, 'new_value': 120625.8}, {'field': 'order_count', 'old_value': 533, 'new_value': 578}]
2025-04-29 09:01:56,661 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW6
2025-04-29 09:01:57,114 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW6
2025-04-29 09:01:57,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 607664.76, 'new_value': 625260.16}, {'field': 'total_amount', 'old_value': 607664.76, 'new_value': 625260.16}, {'field': 'order_count', 'old_value': 5016, 'new_value': 5182}]
2025-04-29 09:01:57,114 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M37
2025-04-29 09:01:57,536 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M37
2025-04-29 09:01:57,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 367343.0, 'new_value': 370924.0}, {'field': 'total_amount', 'old_value': 378505.0, 'new_value': 382086.0}, {'field': 'order_count', 'old_value': 8500, 'new_value': 8580}]
2025-04-29 09:01:57,536 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M87
2025-04-29 09:01:57,973 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M87
2025-04-29 09:01:57,973 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73323.85, 'new_value': 76305.62}, {'field': 'total_amount', 'old_value': 73323.85, 'new_value': 76305.62}, {'field': 'order_count', 'old_value': 178, 'new_value': 194}]
2025-04-29 09:01:57,973 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC7
2025-04-29 09:01:58,489 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC7
2025-04-29 09:01:58,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51064.0, 'new_value': 51959.0}, {'field': 'total_amount', 'old_value': 51064.0, 'new_value': 51959.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 63}]
2025-04-29 09:01:58,489 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF7
2025-04-29 09:01:58,911 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF7
2025-04-29 09:01:58,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56777.9, 'new_value': 62501.9}, {'field': 'total_amount', 'old_value': 57573.9, 'new_value': 63297.9}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-04-29 09:01:58,911 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MX1
2025-04-29 09:01:59,489 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MX1
2025-04-29 09:01:59,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160464.0, 'new_value': 169327.0}, {'field': 'total_amount', 'old_value': 169871.0, 'new_value': 178734.0}, {'field': 'order_count', 'old_value': 679, 'new_value': 712}]
2025-04-29 09:01:59,489 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M12
2025-04-29 09:02:00,145 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M12
2025-04-29 09:02:00,145 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30118.86, 'new_value': 35583.06}, {'field': 'offline_amount', 'old_value': 346895.18, 'new_value': 353427.71}, {'field': 'total_amount', 'old_value': 377014.04, 'new_value': 389010.77}, {'field': 'order_count', 'old_value': 4370, 'new_value': 4514}]
2025-04-29 09:02:00,145 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MA2
2025-04-29 09:02:00,551 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MA2
2025-04-29 09:02:00,551 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 198430.71, 'new_value': 208803.25}, {'field': 'offline_amount', 'old_value': 169948.09, 'new_value': 176550.93}, {'field': 'total_amount', 'old_value': 368378.8, 'new_value': 385354.18}, {'field': 'order_count', 'old_value': 1231, 'new_value': 1292}]
2025-04-29 09:02:00,551 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MD2
2025-04-29 09:02:00,989 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MD2
2025-04-29 09:02:00,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129071.0, 'new_value': 173121.0}, {'field': 'total_amount', 'old_value': 129071.0, 'new_value': 173121.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 15}]
2025-04-29 09:02:00,989 - INFO - 日期 2025-04 处理完成 - 更新: 208 条，插入: 0 条，错误: 0 条
2025-04-29 09:02:00,989 - INFO - 数据同步完成！更新: 208 条，插入: 0 条，错误: 0 条
2025-04-29 09:02:01,004 - INFO - =================同步完成====================
2025-04-29 12:00:01,859 - INFO - =================使用默认全量同步=============
2025-04-29 12:00:03,000 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-29 12:00:03,000 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-29 12:00:03,015 - INFO - 开始处理日期: 2025-01
2025-04-29 12:00:03,015 - INFO - Request Parameters - Page 1:
2025-04-29 12:00:03,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:03,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:03,953 - INFO - Response - Page 1:
2025-04-29 12:00:04,156 - INFO - 第 1 页获取到 100 条记录
2025-04-29 12:00:04,156 - INFO - Request Parameters - Page 2:
2025-04-29 12:00:04,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:04,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:04,812 - INFO - Response - Page 2:
2025-04-29 12:00:05,015 - INFO - 第 2 页获取到 100 条记录
2025-04-29 12:00:05,015 - INFO - Request Parameters - Page 3:
2025-04-29 12:00:05,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:05,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:05,594 - INFO - Response - Page 3:
2025-04-29 12:00:05,797 - INFO - 第 3 页获取到 100 条记录
2025-04-29 12:00:05,797 - INFO - Request Parameters - Page 4:
2025-04-29 12:00:05,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:05,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:06,265 - INFO - Response - Page 4:
2025-04-29 12:00:06,469 - INFO - 第 4 页获取到 100 条记录
2025-04-29 12:00:06,469 - INFO - Request Parameters - Page 5:
2025-04-29 12:00:06,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:06,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:06,906 - INFO - Response - Page 5:
2025-04-29 12:00:07,109 - INFO - 第 5 页获取到 100 条记录
2025-04-29 12:00:07,109 - INFO - Request Parameters - Page 6:
2025-04-29 12:00:07,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:07,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:07,578 - INFO - Response - Page 6:
2025-04-29 12:00:07,781 - INFO - 第 6 页获取到 100 条记录
2025-04-29 12:00:07,781 - INFO - Request Parameters - Page 7:
2025-04-29 12:00:07,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:07,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:08,265 - INFO - Response - Page 7:
2025-04-29 12:00:08,469 - INFO - 第 7 页获取到 82 条记录
2025-04-29 12:00:08,469 - INFO - 查询完成，共获取到 682 条记录
2025-04-29 12:00:08,469 - INFO - 获取到 682 条表单数据
2025-04-29 12:00:08,469 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-29 12:00:08,484 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 12:00:08,484 - INFO - 开始处理日期: 2025-02
2025-04-29 12:00:08,484 - INFO - Request Parameters - Page 1:
2025-04-29 12:00:08,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:08,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:09,031 - INFO - Response - Page 1:
2025-04-29 12:00:09,234 - INFO - 第 1 页获取到 100 条记录
2025-04-29 12:00:09,234 - INFO - Request Parameters - Page 2:
2025-04-29 12:00:09,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:09,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:09,734 - INFO - Response - Page 2:
2025-04-29 12:00:09,937 - INFO - 第 2 页获取到 100 条记录
2025-04-29 12:00:09,937 - INFO - Request Parameters - Page 3:
2025-04-29 12:00:09,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:09,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:10,437 - INFO - Response - Page 3:
2025-04-29 12:00:10,640 - INFO - 第 3 页获取到 100 条记录
2025-04-29 12:00:10,640 - INFO - Request Parameters - Page 4:
2025-04-29 12:00:10,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:10,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:11,094 - INFO - Response - Page 4:
2025-04-29 12:00:11,297 - INFO - 第 4 页获取到 100 条记录
2025-04-29 12:00:11,297 - INFO - Request Parameters - Page 5:
2025-04-29 12:00:11,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:11,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:11,812 - INFO - Response - Page 5:
2025-04-29 12:00:12,015 - INFO - 第 5 页获取到 100 条记录
2025-04-29 12:00:12,015 - INFO - Request Parameters - Page 6:
2025-04-29 12:00:12,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:12,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:12,468 - INFO - Response - Page 6:
2025-04-29 12:00:12,672 - INFO - 第 6 页获取到 100 条记录
2025-04-29 12:00:12,672 - INFO - Request Parameters - Page 7:
2025-04-29 12:00:12,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:12,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:13,172 - INFO - Response - Page 7:
2025-04-29 12:00:13,375 - INFO - 第 7 页获取到 70 条记录
2025-04-29 12:00:13,375 - INFO - 查询完成，共获取到 670 条记录
2025-04-29 12:00:13,375 - INFO - 获取到 670 条表单数据
2025-04-29 12:00:13,375 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-29 12:00:13,390 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 12:00:13,390 - INFO - 开始处理日期: 2025-03
2025-04-29 12:00:13,390 - INFO - Request Parameters - Page 1:
2025-04-29 12:00:13,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:13,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:13,890 - INFO - Response - Page 1:
2025-04-29 12:00:14,093 - INFO - 第 1 页获取到 100 条记录
2025-04-29 12:00:14,093 - INFO - Request Parameters - Page 2:
2025-04-29 12:00:14,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:14,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:14,609 - INFO - Response - Page 2:
2025-04-29 12:00:14,812 - INFO - 第 2 页获取到 100 条记录
2025-04-29 12:00:14,812 - INFO - Request Parameters - Page 3:
2025-04-29 12:00:14,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:14,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:15,359 - INFO - Response - Page 3:
2025-04-29 12:00:15,562 - INFO - 第 3 页获取到 100 条记录
2025-04-29 12:00:15,562 - INFO - Request Parameters - Page 4:
2025-04-29 12:00:15,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:15,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:16,031 - INFO - Response - Page 4:
2025-04-29 12:00:16,234 - INFO - 第 4 页获取到 100 条记录
2025-04-29 12:00:16,234 - INFO - Request Parameters - Page 5:
2025-04-29 12:00:16,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:16,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:16,734 - INFO - Response - Page 5:
2025-04-29 12:00:16,937 - INFO - 第 5 页获取到 100 条记录
2025-04-29 12:00:16,937 - INFO - Request Parameters - Page 6:
2025-04-29 12:00:16,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:16,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:17,500 - INFO - Response - Page 6:
2025-04-29 12:00:17,703 - INFO - 第 6 页获取到 100 条记录
2025-04-29 12:00:17,703 - INFO - Request Parameters - Page 7:
2025-04-29 12:00:17,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:17,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:18,140 - INFO - Response - Page 7:
2025-04-29 12:00:18,343 - INFO - 第 7 页获取到 61 条记录
2025-04-29 12:00:18,343 - INFO - 查询完成，共获取到 661 条记录
2025-04-29 12:00:18,343 - INFO - 获取到 661 条表单数据
2025-04-29 12:00:18,343 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-29 12:00:18,359 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 12:00:18,359 - INFO - 开始处理日期: 2025-04
2025-04-29 12:00:18,359 - INFO - Request Parameters - Page 1:
2025-04-29 12:00:18,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:18,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:18,937 - INFO - Response - Page 1:
2025-04-29 12:00:19,140 - INFO - 第 1 页获取到 100 条记录
2025-04-29 12:00:19,140 - INFO - Request Parameters - Page 2:
2025-04-29 12:00:19,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:19,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:19,609 - INFO - Response - Page 2:
2025-04-29 12:00:19,812 - INFO - 第 2 页获取到 100 条记录
2025-04-29 12:00:19,812 - INFO - Request Parameters - Page 3:
2025-04-29 12:00:19,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:19,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:20,281 - INFO - Response - Page 3:
2025-04-29 12:00:20,484 - INFO - 第 3 页获取到 100 条记录
2025-04-29 12:00:20,484 - INFO - Request Parameters - Page 4:
2025-04-29 12:00:20,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:20,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:20,922 - INFO - Response - Page 4:
2025-04-29 12:00:21,125 - INFO - 第 4 页获取到 100 条记录
2025-04-29 12:00:21,125 - INFO - Request Parameters - Page 5:
2025-04-29 12:00:21,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:21,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:21,578 - INFO - Response - Page 5:
2025-04-29 12:00:21,781 - INFO - 第 5 页获取到 100 条记录
2025-04-29 12:00:21,781 - INFO - Request Parameters - Page 6:
2025-04-29 12:00:21,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:21,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:22,281 - INFO - Response - Page 6:
2025-04-29 12:00:22,484 - INFO - 第 6 页获取到 100 条记录
2025-04-29 12:00:22,484 - INFO - Request Parameters - Page 7:
2025-04-29 12:00:22,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:00:22,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:00:22,875 - INFO - Response - Page 7:
2025-04-29 12:00:23,078 - INFO - 第 7 页获取到 27 条记录
2025-04-29 12:00:23,078 - INFO - 查询完成，共获取到 627 条记录
2025-04-29 12:00:23,078 - INFO - 获取到 627 条表单数据
2025-04-29 12:00:23,078 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-29 12:00:23,078 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MZ2
2025-04-29 12:00:23,578 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MZ2
2025-04-29 12:00:23,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40902.75, 'new_value': 42516.28}, {'field': 'total_amount', 'old_value': 41109.75, 'new_value': 42723.28}, {'field': 'order_count', 'old_value': 5224, 'new_value': 5427}]
2025-04-29 12:00:23,578 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M33
2025-04-29 12:00:24,078 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M33
2025-04-29 12:00:24,078 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85615.81, 'new_value': 89287.17}, {'field': 'offline_amount', 'old_value': 227990.35, 'new_value': 233303.0}, {'field': 'total_amount', 'old_value': 313606.16, 'new_value': 322590.17}, {'field': 'order_count', 'old_value': 11135, 'new_value': 11498}]
2025-04-29 12:00:24,078 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MB3
2025-04-29 12:00:24,500 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MB3
2025-04-29 12:00:24,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3992.0, 'new_value': 4391.0}, {'field': 'total_amount', 'old_value': 12690.0, 'new_value': 13089.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-04-29 12:00:24,500 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MD3
2025-04-29 12:00:24,968 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MD3
2025-04-29 12:00:24,968 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 375849.0, 'new_value': 392278.0}, {'field': 'offline_amount', 'old_value': 682812.0, 'new_value': 690323.0}, {'field': 'total_amount', 'old_value': 1058661.0, 'new_value': 1082601.0}, {'field': 'order_count', 'old_value': 1006, 'new_value': 1036}]
2025-04-29 12:00:24,968 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MF3
2025-04-29 12:00:25,531 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MF3
2025-04-29 12:00:25,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23670.26, 'new_value': 24246.95}, {'field': 'total_amount', 'old_value': 23670.26, 'new_value': 24246.95}, {'field': 'order_count', 'old_value': 96, 'new_value': 102}]
2025-04-29 12:00:25,531 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MH3
2025-04-29 12:00:26,000 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MH3
2025-04-29 12:00:26,000 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68247.1, 'new_value': 76203.1}, {'field': 'total_amount', 'old_value': 68247.1, 'new_value': 76203.1}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-04-29 12:00:26,000 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MJ3
2025-04-29 12:00:26,515 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MJ3
2025-04-29 12:00:26,515 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191894.0, 'new_value': 198154.0}, {'field': 'total_amount', 'old_value': 191894.0, 'new_value': 198154.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-04-29 12:00:26,515 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MN3
2025-04-29 12:00:27,047 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MN3
2025-04-29 12:00:27,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62960.0, 'new_value': 65400.0}, {'field': 'total_amount', 'old_value': 71020.0, 'new_value': 73460.0}, {'field': 'order_count', 'old_value': 724, 'new_value': 746}]
2025-04-29 12:00:27,047 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ3
2025-04-29 12:00:27,515 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ3
2025-04-29 12:00:27,515 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 562239.0, 'new_value': 570995.0}, {'field': 'total_amount', 'old_value': 648432.0, 'new_value': 657188.0}, {'field': 'order_count', 'old_value': 107, 'new_value': 108}]
2025-04-29 12:00:27,515 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU3
2025-04-29 12:00:27,922 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU3
2025-04-29 12:00:27,922 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12131.39, 'new_value': 14145.03}, {'field': 'offline_amount', 'old_value': 52350.64, 'new_value': 52837.35}, {'field': 'total_amount', 'old_value': 64482.03, 'new_value': 66982.38}, {'field': 'order_count', 'old_value': 1227, 'new_value': 1280}]
2025-04-29 12:00:27,922 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV3
2025-04-29 12:00:28,328 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV3
2025-04-29 12:00:28,328 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4513.35, 'new_value': 4687.35}, {'field': 'offline_amount', 'old_value': 57334.7, 'new_value': 57350.7}, {'field': 'total_amount', 'old_value': 61848.05, 'new_value': 62038.05}, {'field': 'order_count', 'old_value': 3925, 'new_value': 3939}]
2025-04-29 12:00:28,328 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY3
2025-04-29 12:00:28,765 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY3
2025-04-29 12:00:28,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46793.53, 'new_value': 48797.84}, {'field': 'total_amount', 'old_value': 46793.53, 'new_value': 48797.84}, {'field': 'order_count', 'old_value': 9361, 'new_value': 9779}]
2025-04-29 12:00:28,765 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M04
2025-04-29 12:00:29,281 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M04
2025-04-29 12:00:29,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63475.0, 'new_value': 66175.0}, {'field': 'total_amount', 'old_value': 143108.0, 'new_value': 145808.0}, {'field': 'order_count', 'old_value': 2029, 'new_value': 2087}]
2025-04-29 12:00:29,281 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M14
2025-04-29 12:00:29,672 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M14
2025-04-29 12:00:29,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140171.0, 'new_value': 149345.0}, {'field': 'total_amount', 'old_value': 195343.0, 'new_value': 204517.0}, {'field': 'order_count', 'old_value': 3869, 'new_value': 4002}]
2025-04-29 12:00:29,672 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M74
2025-04-29 12:00:30,203 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M74
2025-04-29 12:00:30,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55446.24, 'new_value': 58469.24}, {'field': 'total_amount', 'old_value': 55446.24, 'new_value': 58469.24}, {'field': 'order_count', 'old_value': 3048, 'new_value': 3252}]
2025-04-29 12:00:30,203 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M84
2025-04-29 12:00:30,640 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M84
2025-04-29 12:00:30,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6866.55, 'new_value': 6872.55}, {'field': 'total_amount', 'old_value': 6866.55, 'new_value': 6872.55}, {'field': 'order_count', 'old_value': 942, 'new_value': 943}]
2025-04-29 12:00:30,640 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA4
2025-04-29 12:00:31,187 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA4
2025-04-29 12:00:31,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95364.3, 'new_value': 97512.3}, {'field': 'total_amount', 'old_value': 95364.3, 'new_value': 97512.3}, {'field': 'order_count', 'old_value': 615, 'new_value': 633}]
2025-04-29 12:00:31,187 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN4
2025-04-29 12:00:31,718 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN4
2025-04-29 12:00:31,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 689601.0, 'new_value': 689674.9}, {'field': 'total_amount', 'old_value': 689601.0, 'new_value': 689674.9}, {'field': 'order_count', 'old_value': 87, 'new_value': 88}]
2025-04-29 12:00:31,718 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO4
2025-04-29 12:00:32,218 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO4
2025-04-29 12:00:32,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9251.0, 'new_value': 10197.0}, {'field': 'total_amount', 'old_value': 9251.0, 'new_value': 10197.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 27}]
2025-04-29 12:00:32,218 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP4
2025-04-29 12:00:32,812 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP4
2025-04-29 12:00:32,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132689.0, 'new_value': 133138.0}, {'field': 'total_amount', 'old_value': 253625.0, 'new_value': 254074.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 69}]
2025-04-29 12:00:32,812 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ4
2025-04-29 12:00:33,234 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ4
2025-04-29 12:00:33,234 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108875.9, 'new_value': 113644.9}, {'field': 'offline_amount', 'old_value': 173638.25, 'new_value': 182245.25}, {'field': 'total_amount', 'old_value': 282514.15, 'new_value': 295890.15}, {'field': 'order_count', 'old_value': 6156, 'new_value': 6448}]
2025-04-29 12:00:33,250 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT4
2025-04-29 12:00:33,796 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT4
2025-04-29 12:00:33,796 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66204.55, 'new_value': 68711.51}, {'field': 'offline_amount', 'old_value': 687458.35, 'new_value': 718716.97}, {'field': 'total_amount', 'old_value': 753662.9, 'new_value': 787428.48}, {'field': 'order_count', 'old_value': 3081, 'new_value': 3210}]
2025-04-29 12:00:33,796 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU4
2025-04-29 12:00:34,312 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU4
2025-04-29 12:00:34,312 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 79.8}, {'field': 'offline_amount', 'old_value': 42780.8, 'new_value': 44049.4}, {'field': 'total_amount', 'old_value': 42780.8, 'new_value': 44129.2}, {'field': 'order_count', 'old_value': 259, 'new_value': 272}]
2025-04-29 12:00:34,312 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MW4
2025-04-29 12:00:34,750 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MW4
2025-04-29 12:00:34,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82704.28, 'new_value': 84313.71}, {'field': 'total_amount', 'old_value': 85773.61, 'new_value': 87383.04}, {'field': 'order_count', 'old_value': 5079, 'new_value': 5188}]
2025-04-29 12:00:34,750 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX4
2025-04-29 12:00:35,218 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX4
2025-04-29 12:00:35,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 658674.25, 'new_value': 683870.24}, {'field': 'total_amount', 'old_value': 658674.25, 'new_value': 683870.24}, {'field': 'order_count', 'old_value': 4700, 'new_value': 4901}]
2025-04-29 12:00:35,218 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M25
2025-04-29 12:00:35,687 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M25
2025-04-29 12:00:35,687 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17542.0, 'new_value': 18542.0}, {'field': 'offline_amount', 'old_value': 246260.0, 'new_value': 252699.0}, {'field': 'total_amount', 'old_value': 263802.0, 'new_value': 271241.0}, {'field': 'order_count', 'old_value': 233, 'new_value': 241}]
2025-04-29 12:00:35,687 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M35
2025-04-29 12:00:36,109 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M35
2025-04-29 12:00:36,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90055.0, 'new_value': 92745.0}, {'field': 'total_amount', 'old_value': 90055.0, 'new_value': 92745.0}, {'field': 'order_count', 'old_value': 5190, 'new_value': 5310}]
2025-04-29 12:00:36,109 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M75
2025-04-29 12:00:36,656 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M75
2025-04-29 12:00:36,656 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59874.51, 'new_value': 61645.86}, {'field': 'offline_amount', 'old_value': 615583.3, 'new_value': 638805.0}, {'field': 'total_amount', 'old_value': 675457.81, 'new_value': 700450.86}, {'field': 'order_count', 'old_value': 2191, 'new_value': 2275}]
2025-04-29 12:00:36,656 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA5
2025-04-29 12:00:37,046 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA5
2025-04-29 12:00:37,046 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 217547.13, 'new_value': 222655.14}, {'field': 'total_amount', 'old_value': 217547.13, 'new_value': 222655.14}, {'field': 'order_count', 'old_value': 1113, 'new_value': 1145}]
2025-04-29 12:00:37,046 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MD5
2025-04-29 12:00:37,468 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MD5
2025-04-29 12:00:37,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142700.0, 'new_value': 152200.0}, {'field': 'total_amount', 'old_value': 142700.0, 'new_value': 152200.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-04-29 12:00:37,468 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF5
2025-04-29 12:00:37,921 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF5
2025-04-29 12:00:37,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39270.0, 'new_value': 40846.0}, {'field': 'total_amount', 'old_value': 43277.0, 'new_value': 44853.0}, {'field': 'order_count', 'old_value': 207, 'new_value': 215}]
2025-04-29 12:00:37,921 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH5
2025-04-29 12:00:38,390 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH5
2025-04-29 12:00:38,390 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63918.71, 'new_value': 66796.28}, {'field': 'offline_amount', 'old_value': 47372.9, 'new_value': 48812.6}, {'field': 'total_amount', 'old_value': 111291.61, 'new_value': 115608.88}, {'field': 'order_count', 'old_value': 3809, 'new_value': 3963}]
2025-04-29 12:00:38,390 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK5
2025-04-29 12:00:38,875 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK5
2025-04-29 12:00:38,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5147.0, 'new_value': 5322.0}, {'field': 'offline_amount', 'old_value': 21889.0, 'new_value': 22408.0}, {'field': 'total_amount', 'old_value': 27036.0, 'new_value': 27730.0}, {'field': 'order_count', 'old_value': 166, 'new_value': 174}]
2025-04-29 12:00:38,875 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML5
2025-04-29 12:00:39,281 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML5
2025-04-29 12:00:39,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132301.12, 'new_value': 139779.99}, {'field': 'total_amount', 'old_value': 134477.85, 'new_value': 141956.72}, {'field': 'order_count', 'old_value': 610, 'new_value': 650}]
2025-04-29 12:00:39,281 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MB9
2025-04-29 12:00:39,671 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MB9
2025-04-29 12:00:39,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104266.1, 'new_value': 105044.2}, {'field': 'total_amount', 'old_value': 104266.1, 'new_value': 105044.2}, {'field': 'order_count', 'old_value': 204, 'new_value': 206}]
2025-04-29 12:00:39,671 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MC9
2025-04-29 12:00:40,093 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MC9
2025-04-29 12:00:40,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1694809.47, 'new_value': 1746393.89}, {'field': 'total_amount', 'old_value': 1694809.47, 'new_value': 1746393.89}, {'field': 'order_count', 'old_value': 13353, 'new_value': 13855}]
2025-04-29 12:00:40,093 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MD9
2025-04-29 12:00:40,593 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MD9
2025-04-29 12:00:40,593 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23945.07, 'new_value': 25224.42}, {'field': 'offline_amount', 'old_value': 41342.94, 'new_value': 42536.14}, {'field': 'total_amount', 'old_value': 65288.01, 'new_value': 67760.56}, {'field': 'order_count', 'old_value': 2661, 'new_value': 2779}]
2025-04-29 12:00:40,593 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9ME9
2025-04-29 12:00:41,031 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9ME9
2025-04-29 12:00:41,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50878.0, 'new_value': 51768.0}, {'field': 'total_amount', 'old_value': 50878.0, 'new_value': 51768.0}, {'field': 'order_count', 'old_value': 915, 'new_value': 920}]
2025-04-29 12:00:41,031 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MI9
2025-04-29 12:00:41,484 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MI9
2025-04-29 12:00:41,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210354.56, 'new_value': 219651.16}, {'field': 'total_amount', 'old_value': 210354.56, 'new_value': 219651.16}, {'field': 'order_count', 'old_value': 330, 'new_value': 340}]
2025-04-29 12:00:41,484 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MO9
2025-04-29 12:00:41,921 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MO9
2025-04-29 12:00:41,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97784.42, 'new_value': 100329.42}, {'field': 'total_amount', 'old_value': 97784.42, 'new_value': 100329.42}, {'field': 'order_count', 'old_value': 180, 'new_value': 184}]
2025-04-29 12:00:41,921 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-04-29 12:00:42,343 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-04-29 12:00:42,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 395625.73, 'new_value': 409021.87}, {'field': 'offline_amount', 'old_value': 1443.0, 'new_value': 1463.0}, {'field': 'total_amount', 'old_value': 397068.73, 'new_value': 410484.87}, {'field': 'order_count', 'old_value': 4757, 'new_value': 4944}]
2025-04-29 12:00:42,343 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MS9
2025-04-29 12:00:42,734 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MS9
2025-04-29 12:00:42,734 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33902.5, 'new_value': 34016.1}, {'field': 'offline_amount', 'old_value': 49.0, 'new_value': 51.0}, {'field': 'total_amount', 'old_value': 33951.5, 'new_value': 34067.1}, {'field': 'order_count', 'old_value': 69, 'new_value': 71}]
2025-04-29 12:00:42,734 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-04-29 12:00:43,265 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-04-29 12:00:43,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112265.99, 'new_value': 134124.98}, {'field': 'total_amount', 'old_value': 723770.06, 'new_value': 745629.05}, {'field': 'order_count', 'old_value': 2385, 'new_value': 2449}]
2025-04-29 12:00:43,265 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1A
2025-04-29 12:00:43,750 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1A
2025-04-29 12:00:43,750 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8480.6, 'new_value': 9736.5}, {'field': 'offline_amount', 'old_value': 101205.9, 'new_value': 101343.9}, {'field': 'total_amount', 'old_value': 109686.5, 'new_value': 111080.4}, {'field': 'order_count', 'old_value': 145, 'new_value': 149}]
2025-04-29 12:00:43,750 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4A
2025-04-29 12:00:44,265 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4A
2025-04-29 12:00:44,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40805.0, 'new_value': 41305.0}, {'field': 'total_amount', 'old_value': 48096.3, 'new_value': 48596.3}, {'field': 'order_count', 'old_value': 509, 'new_value': 510}]
2025-04-29 12:00:44,265 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-04-29 12:00:44,781 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-04-29 12:00:44,781 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20145.76, 'new_value': 20957.07}, {'field': 'offline_amount', 'old_value': 377865.9, 'new_value': 388954.3}, {'field': 'total_amount', 'old_value': 398011.66, 'new_value': 409911.37}, {'field': 'order_count', 'old_value': 2857, 'new_value': 2944}]
2025-04-29 12:00:44,781 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAA
2025-04-29 12:00:45,218 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAA
2025-04-29 12:00:45,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76169.35, 'new_value': 78346.95}, {'field': 'total_amount', 'old_value': 77750.69, 'new_value': 79928.29}, {'field': 'order_count', 'old_value': 3066, 'new_value': 3157}]
2025-04-29 12:00:45,218 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLA
2025-04-29 12:00:45,640 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLA
2025-04-29 12:00:45,640 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107153.42, 'new_value': 110649.41}, {'field': 'offline_amount', 'old_value': 312173.32, 'new_value': 319889.23}, {'field': 'total_amount', 'old_value': 419326.74, 'new_value': 430538.64}, {'field': 'order_count', 'old_value': 10780, 'new_value': 11109}]
2025-04-29 12:00:45,640 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUA
2025-04-29 12:00:46,203 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUA
2025-04-29 12:00:46,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53155.8, 'new_value': 55717.1}, {'field': 'total_amount', 'old_value': 53155.8, 'new_value': 55717.1}, {'field': 'order_count', 'old_value': 436, 'new_value': 452}]
2025-04-29 12:00:46,203 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4B
2025-04-29 12:00:46,656 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4B
2025-04-29 12:00:46,656 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7960.87, 'new_value': 8184.07}, {'field': 'total_amount', 'old_value': 23427.73, 'new_value': 23650.93}, {'field': 'order_count', 'old_value': 88, 'new_value': 89}]
2025-04-29 12:00:46,656 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6B
2025-04-29 12:00:47,203 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6B
2025-04-29 12:00:47,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42550.58, 'new_value': 43633.18}, {'field': 'offline_amount', 'old_value': 347279.61, 'new_value': 353230.28}, {'field': 'total_amount', 'old_value': 389830.19, 'new_value': 396863.46}, {'field': 'order_count', 'old_value': 3447, 'new_value': 3525}]
2025-04-29 12:00:47,203 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7B
2025-04-29 12:00:47,718 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7B
2025-04-29 12:00:47,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35249.0, 'new_value': 36993.0}, {'field': 'total_amount', 'old_value': 35249.0, 'new_value': 36993.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 121}]
2025-04-29 12:00:47,718 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAB
2025-04-29 12:00:48,203 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAB
2025-04-29 12:00:48,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62587.55, 'new_value': 64286.45}, {'field': 'offline_amount', 'old_value': 83716.82, 'new_value': 84397.82}, {'field': 'total_amount', 'old_value': 146304.37, 'new_value': 148684.27}, {'field': 'order_count', 'old_value': 6780, 'new_value': 6809}]
2025-04-29 12:00:48,203 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDB
2025-04-29 12:00:48,671 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDB
2025-04-29 12:00:48,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46751.0, 'new_value': 48410.0}, {'field': 'total_amount', 'old_value': 47381.0, 'new_value': 49040.0}, {'field': 'order_count', 'old_value': 182, 'new_value': 188}]
2025-04-29 12:00:48,671 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MFB
2025-04-29 12:00:49,140 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MFB
2025-04-29 12:00:49,140 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39762.92, 'new_value': 40656.92}, {'field': 'offline_amount', 'old_value': 325398.75, 'new_value': 332099.85}, {'field': 'total_amount', 'old_value': 365161.67, 'new_value': 372756.77}, {'field': 'order_count', 'old_value': 678, 'new_value': 691}]
2025-04-29 12:00:49,140 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHB
2025-04-29 12:00:49,562 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHB
2025-04-29 12:00:49,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32675.2, 'new_value': 33675.2}, {'field': 'total_amount', 'old_value': 32675.2, 'new_value': 33675.2}, {'field': 'order_count', 'old_value': 140, 'new_value': 141}]
2025-04-29 12:00:49,562 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIB
2025-04-29 12:00:50,046 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIB
2025-04-29 12:00:50,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25369.7, 'new_value': 25502.6}, {'field': 'offline_amount', 'old_value': 55952.3, 'new_value': 60014.3}, {'field': 'total_amount', 'old_value': 81322.0, 'new_value': 85516.9}, {'field': 'order_count', 'old_value': 230, 'new_value': 235}]
2025-04-29 12:00:50,046 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJB
2025-04-29 12:00:50,437 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJB
2025-04-29 12:00:50,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51939.0, 'new_value': 51688.0}, {'field': 'total_amount', 'old_value': 51939.0, 'new_value': 51688.0}]
2025-04-29 12:00:50,437 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLB
2025-04-29 12:00:50,953 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLB
2025-04-29 12:00:50,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 199.7, 'new_value': 397.1}, {'field': 'offline_amount', 'old_value': 38901.4, 'new_value': 40179.0}, {'field': 'total_amount', 'old_value': 39101.1, 'new_value': 40576.1}, {'field': 'order_count', 'old_value': 122, 'new_value': 127}]
2025-04-29 12:00:50,953 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMB
2025-04-29 12:00:51,375 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMB
2025-04-29 12:00:51,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27951.5, 'new_value': 29047.5}, {'field': 'total_amount', 'old_value': 27951.5, 'new_value': 29047.5}, {'field': 'order_count', 'old_value': 130, 'new_value': 136}]
2025-04-29 12:00:51,375 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPB
2025-04-29 12:00:51,828 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPB
2025-04-29 12:00:51,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280731.0, 'new_value': 312731.0}, {'field': 'total_amount', 'old_value': 409351.0, 'new_value': 441351.0}, {'field': 'order_count', 'old_value': 86, 'new_value': 91}]
2025-04-29 12:00:51,828 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQB
2025-04-29 12:00:52,453 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQB
2025-04-29 12:00:52,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53715.8, 'new_value': 54215.8}, {'field': 'total_amount', 'old_value': 53715.8, 'new_value': 54215.8}, {'field': 'order_count', 'old_value': 292, 'new_value': 295}]
2025-04-29 12:00:52,453 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRB
2025-04-29 12:00:52,984 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRB
2025-04-29 12:00:52,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 256471.0, 'new_value': 291271.0}, {'field': 'total_amount', 'old_value': 485621.0, 'new_value': 520421.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 116}]
2025-04-29 12:00:52,984 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTB
2025-04-29 12:00:53,421 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTB
2025-04-29 12:00:53,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209221.0, 'new_value': 243759.0}, {'field': 'total_amount', 'old_value': 408121.0, 'new_value': 442659.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 103}]
2025-04-29 12:00:53,421 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVB
2025-04-29 12:00:53,968 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVB
2025-04-29 12:00:53,968 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56565.53, 'new_value': 58316.28}, {'field': 'offline_amount', 'old_value': 73626.45, 'new_value': 75142.68}, {'field': 'total_amount', 'old_value': 130191.98, 'new_value': 133458.96}, {'field': 'order_count', 'old_value': 5151, 'new_value': 5286}]
2025-04-29 12:00:53,968 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWB
2025-04-29 12:00:54,437 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWB
2025-04-29 12:00:54,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81245.0, 'new_value': 90745.0}, {'field': 'total_amount', 'old_value': 81245.0, 'new_value': 90745.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-04-29 12:00:54,437 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML5
2025-04-29 12:00:54,843 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML5
2025-04-29 12:00:54,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74484.0, 'new_value': 87971.0}, {'field': 'total_amount', 'old_value': 74484.0, 'new_value': 87971.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 47}]
2025-04-29 12:00:54,843 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT5
2025-04-29 12:00:55,312 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT5
2025-04-29 12:00:55,312 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15065.05, 'new_value': 15507.95}, {'field': 'offline_amount', 'old_value': 157989.03, 'new_value': 162371.06}, {'field': 'total_amount', 'old_value': 173054.08, 'new_value': 177879.01}, {'field': 'order_count', 'old_value': 4260, 'new_value': 4391}]
2025-04-29 12:00:55,312 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU5
2025-04-29 12:00:55,796 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU5
2025-04-29 12:00:55,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66788.0, 'new_value': 74487.0}, {'field': 'total_amount', 'old_value': 66788.0, 'new_value': 74487.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-04-29 12:00:55,796 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW5
2025-04-29 12:00:56,203 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW5
2025-04-29 12:00:56,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27178.0, 'new_value': 29293.0}, {'field': 'total_amount', 'old_value': 27178.0, 'new_value': 29293.0}, {'field': 'order_count', 'old_value': 253, 'new_value': 279}]
2025-04-29 12:00:56,203 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX5
2025-04-29 12:00:56,687 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX5
2025-04-29 12:00:56,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21530.54, 'new_value': 22542.51}, {'field': 'total_amount', 'old_value': 23530.54, 'new_value': 24542.51}, {'field': 'order_count', 'old_value': 452, 'new_value': 470}]
2025-04-29 12:00:56,687 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M46
2025-04-29 12:00:57,109 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M46
2025-04-29 12:00:57,109 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 238572.61, 'new_value': 241138.61}, {'field': 'offline_amount', 'old_value': 87520.42, 'new_value': 88253.62}, {'field': 'total_amount', 'old_value': 326093.03, 'new_value': 329392.23}, {'field': 'order_count', 'old_value': 621, 'new_value': 629}]
2025-04-29 12:00:57,109 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M66
2025-04-29 12:00:57,531 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M66
2025-04-29 12:00:57,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3262.3, 'new_value': 3641.3}, {'field': 'offline_amount', 'old_value': 119780.8, 'new_value': 122630.6}, {'field': 'total_amount', 'old_value': 123043.1, 'new_value': 126271.9}, {'field': 'order_count', 'old_value': 554, 'new_value': 574}]
2025-04-29 12:00:57,531 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M86
2025-04-29 12:00:57,968 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M86
2025-04-29 12:00:57,968 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62826.43, 'new_value': 64907.67}, {'field': 'offline_amount', 'old_value': 70215.32, 'new_value': 72621.17}, {'field': 'total_amount', 'old_value': 133041.75, 'new_value': 137528.84}, {'field': 'order_count', 'old_value': 5813, 'new_value': 6031}]
2025-04-29 12:00:57,968 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD6
2025-04-29 12:00:58,453 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD6
2025-04-29 12:00:58,453 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55306.95, 'new_value': 57087.89}, {'field': 'offline_amount', 'old_value': 24777.23, 'new_value': 25239.33}, {'field': 'total_amount', 'old_value': 80084.18, 'new_value': 82327.22}, {'field': 'order_count', 'old_value': 3260, 'new_value': 3366}]
2025-04-29 12:00:58,453 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ6
2025-04-29 12:00:58,921 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ6
2025-04-29 12:00:58,921 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9490.52, 'new_value': 10633.06}, {'field': 'offline_amount', 'old_value': 81656.1, 'new_value': 83987.81}, {'field': 'total_amount', 'old_value': 91146.62, 'new_value': 94620.87}, {'field': 'order_count', 'old_value': 2530, 'new_value': 2592}]
2025-04-29 12:00:58,921 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP6
2025-04-29 12:00:59,374 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP6
2025-04-29 12:00:59,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40179.0, 'new_value': 43629.0}, {'field': 'total_amount', 'old_value': 40179.0, 'new_value': 43629.0}, {'field': 'order_count', 'old_value': 107, 'new_value': 110}]
2025-04-29 12:00:59,374 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX6
2025-04-29 12:00:59,843 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX6
2025-04-29 12:00:59,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 422536.0, 'new_value': 431999.0}, {'field': 'total_amount', 'old_value': 422536.0, 'new_value': 431999.0}, {'field': 'order_count', 'old_value': 12169, 'new_value': 12526}]
2025-04-29 12:00:59,843 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M17
2025-04-29 12:01:00,328 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M17
2025-04-29 12:01:00,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32301.6, 'new_value': 33002.1}, {'field': 'total_amount', 'old_value': 32301.6, 'new_value': 33002.1}, {'field': 'order_count', 'old_value': 324, 'new_value': 329}]
2025-04-29 12:01:00,328 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M27
2025-04-29 12:01:00,859 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M27
2025-04-29 12:01:00,859 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93077.24, 'new_value': 97089.4}, {'field': 'offline_amount', 'old_value': 64596.8, 'new_value': 66196.14}, {'field': 'total_amount', 'old_value': 157674.04, 'new_value': 163285.54}, {'field': 'order_count', 'old_value': 8526, 'new_value': 8830}]
2025-04-29 12:01:00,859 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M37
2025-04-29 12:01:01,296 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M37
2025-04-29 12:01:01,296 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41190.69, 'new_value': 41377.16}, {'field': 'offline_amount', 'old_value': 247473.42, 'new_value': 255560.64}, {'field': 'total_amount', 'old_value': 288664.11, 'new_value': 296937.8}, {'field': 'order_count', 'old_value': 7068, 'new_value': 7234}]
2025-04-29 12:01:01,296 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M47
2025-04-29 12:01:01,703 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M47
2025-04-29 12:01:01,703 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9416.85, 'new_value': 9634.82}, {'field': 'offline_amount', 'old_value': 14534.67, 'new_value': 14815.57}, {'field': 'total_amount', 'old_value': 23951.52, 'new_value': 24450.39}, {'field': 'order_count', 'old_value': 1598, 'new_value': 1634}]
2025-04-29 12:01:01,703 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M57
2025-04-29 12:01:02,171 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M57
2025-04-29 12:01:02,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 286291.22, 'new_value': 296804.26}, {'field': 'total_amount', 'old_value': 302050.02, 'new_value': 312563.06}, {'field': 'order_count', 'old_value': 12674, 'new_value': 13082}]
2025-04-29 12:01:02,171 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M87
2025-04-29 12:01:02,703 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M87
2025-04-29 12:01:02,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41402.0, 'new_value': 47352.0}, {'field': 'total_amount', 'old_value': 45078.0, 'new_value': 51028.0}, {'field': 'order_count', 'old_value': 83, 'new_value': 91}]
2025-04-29 12:01:02,703 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB7
2025-04-29 12:01:03,109 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB7
2025-04-29 12:01:03,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120270.0, 'new_value': 121471.0}, {'field': 'total_amount', 'old_value': 120270.0, 'new_value': 121471.0}, {'field': 'order_count', 'old_value': 3832, 'new_value': 3870}]
2025-04-29 12:01:03,109 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC7
2025-04-29 12:01:03,562 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC7
2025-04-29 12:01:03,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 580061.09, 'new_value': 601076.34}, {'field': 'total_amount', 'old_value': 662929.6, 'new_value': 683944.85}, {'field': 'order_count', 'old_value': 4869, 'new_value': 5042}]
2025-04-29 12:01:03,562 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ7
2025-04-29 12:01:04,046 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ7
2025-04-29 12:01:04,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 290119.71, 'new_value': 297703.74}, {'field': 'offline_amount', 'old_value': 16016.06, 'new_value': 16577.86}, {'field': 'total_amount', 'old_value': 306135.77, 'new_value': 314281.6}, {'field': 'order_count', 'old_value': 10934, 'new_value': 11204}]
2025-04-29 12:01:04,046 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK7
2025-04-29 12:01:04,593 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK7
2025-04-29 12:01:04,593 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9492.6, 'new_value': 9825.8}, {'field': 'total_amount', 'old_value': 18350.2, 'new_value': 18683.4}, {'field': 'order_count', 'old_value': 160, 'new_value': 163}]
2025-04-29 12:01:04,593 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML7
2025-04-29 12:01:05,203 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML7
2025-04-29 12:01:05,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199995.0, 'new_value': 207268.0}, {'field': 'total_amount', 'old_value': 199995.0, 'new_value': 207268.0}, {'field': 'order_count', 'old_value': 230, 'new_value': 245}]
2025-04-29 12:01:05,203 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM7
2025-04-29 12:01:05,656 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM7
2025-04-29 12:01:05,656 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204492.71, 'new_value': 208296.96}, {'field': 'total_amount', 'old_value': 204492.71, 'new_value': 208296.96}, {'field': 'order_count', 'old_value': 664, 'new_value': 678}]
2025-04-29 12:01:05,656 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO7
2025-04-29 12:01:06,109 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO7
2025-04-29 12:01:06,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 574857.36, 'new_value': 588715.09}, {'field': 'total_amount', 'old_value': 574857.36, 'new_value': 588715.09}, {'field': 'order_count', 'old_value': 11344, 'new_value': 11557}]
2025-04-29 12:01:06,109 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT7
2025-04-29 12:01:06,546 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT7
2025-04-29 12:01:06,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40775.0, 'new_value': 42222.0}, {'field': 'total_amount', 'old_value': 40775.0, 'new_value': 42222.0}, {'field': 'order_count', 'old_value': 131, 'new_value': 136}]
2025-04-29 12:01:06,546 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV7
2025-04-29 12:01:07,015 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV7
2025-04-29 12:01:07,015 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83370.69, 'new_value': 86198.27}, {'field': 'offline_amount', 'old_value': 269367.63, 'new_value': 281804.95}, {'field': 'total_amount', 'old_value': 352738.32, 'new_value': 368003.22}, {'field': 'order_count', 'old_value': 16963, 'new_value': 17729}]
2025-04-29 12:01:07,015 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M08
2025-04-29 12:01:07,484 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M08
2025-04-29 12:01:07,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79892.73, 'new_value': 81798.17}, {'field': 'total_amount', 'old_value': 79892.73, 'new_value': 81798.17}, {'field': 'order_count', 'old_value': 2342, 'new_value': 2397}]
2025-04-29 12:01:07,484 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M48
2025-04-29 12:01:07,968 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M48
2025-04-29 12:01:07,968 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 157514.51, 'new_value': 162070.67}, {'field': 'offline_amount', 'old_value': 439179.17, 'new_value': 451940.2}, {'field': 'total_amount', 'old_value': 596693.68, 'new_value': 614010.87}, {'field': 'order_count', 'old_value': 3085, 'new_value': 3135}]
2025-04-29 12:01:07,968 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M68
2025-04-29 12:01:08,390 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M68
2025-04-29 12:01:08,390 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 542260.6, 'new_value': 550785.7}, {'field': 'total_amount', 'old_value': 604245.9, 'new_value': 612771.0}, {'field': 'order_count', 'old_value': 1333, 'new_value': 1341}]
2025-04-29 12:01:08,390 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M78
2025-04-29 12:01:08,843 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M78
2025-04-29 12:01:08,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211258.1, 'new_value': 216992.26}, {'field': 'total_amount', 'old_value': 211258.1, 'new_value': 216992.26}, {'field': 'order_count', 'old_value': 4425, 'new_value': 4517}]
2025-04-29 12:01:08,843 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M98
2025-04-29 12:01:09,296 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M98
2025-04-29 12:01:09,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141273.25, 'new_value': 151979.05}, {'field': 'total_amount', 'old_value': 182987.15, 'new_value': 193692.95}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-04-29 12:01:09,296 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA8
2025-04-29 12:01:09,874 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA8
2025-04-29 12:01:09,874 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76632.44, 'new_value': 80139.72}, {'field': 'offline_amount', 'old_value': 43289.0, 'new_value': 44109.4}, {'field': 'total_amount', 'old_value': 119921.44, 'new_value': 124249.12}, {'field': 'order_count', 'old_value': 6117, 'new_value': 6379}]
2025-04-29 12:01:09,874 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAH
2025-04-29 12:01:10,374 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAH
2025-04-29 12:01:10,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 359172.13, 'new_value': 368417.17}, {'field': 'total_amount', 'old_value': 359172.13, 'new_value': 368417.17}, {'field': 'order_count', 'old_value': 15591, 'new_value': 16049}]
2025-04-29 12:01:10,374 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBH
2025-04-29 12:01:10,765 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBH
2025-04-29 12:01:10,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 613748.98, 'new_value': 628107.82}, {'field': 'total_amount', 'old_value': 613748.98, 'new_value': 628107.82}, {'field': 'order_count', 'old_value': 6529, 'new_value': 6759}]
2025-04-29 12:01:10,765 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDH
2025-04-29 12:01:11,187 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDH
2025-04-29 12:01:11,187 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8588.74, 'new_value': 9003.27}, {'field': 'offline_amount', 'old_value': 35417.08, 'new_value': 36074.98}, {'field': 'total_amount', 'old_value': 44005.82, 'new_value': 45078.25}, {'field': 'order_count', 'old_value': 1970, 'new_value': 2029}]
2025-04-29 12:01:11,187 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEH
2025-04-29 12:01:11,687 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEH
2025-04-29 12:01:11,687 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9547.05, 'new_value': 9819.01}, {'field': 'offline_amount', 'old_value': 64126.37, 'new_value': 65784.13}, {'field': 'total_amount', 'old_value': 73673.42, 'new_value': 75603.14}, {'field': 'order_count', 'old_value': 2981, 'new_value': 3062}]
2025-04-29 12:01:11,687 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMH
2025-04-29 12:01:12,109 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMH
2025-04-29 12:01:12,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196997.0, 'new_value': 203581.0}, {'field': 'total_amount', 'old_value': 196997.0, 'new_value': 203581.0}, {'field': 'order_count', 'old_value': 609, 'new_value': 631}]
2025-04-29 12:01:12,109 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNH
2025-04-29 12:01:12,687 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNH
2025-04-29 12:01:12,687 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31298.83, 'new_value': 32054.9}, {'field': 'offline_amount', 'old_value': 197860.97, 'new_value': 202991.77}, {'field': 'total_amount', 'old_value': 229159.8, 'new_value': 235046.67}, {'field': 'order_count', 'old_value': 7679, 'new_value': 7870}]
2025-04-29 12:01:12,687 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQH
2025-04-29 12:01:13,140 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQH
2025-04-29 12:01:13,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 338207.0, 'new_value': 359883.0}, {'field': 'total_amount', 'old_value': 338207.0, 'new_value': 359883.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 140}]
2025-04-29 12:01:13,140 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAI
2025-04-29 12:01:13,609 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAI
2025-04-29 12:01:13,609 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96450.36, 'new_value': 99150.02}, {'field': 'offline_amount', 'old_value': 197922.51, 'new_value': 204063.37}, {'field': 'total_amount', 'old_value': 294372.87, 'new_value': 303213.39}, {'field': 'order_count', 'old_value': 9054, 'new_value': 9383}]
2025-04-29 12:01:13,609 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBI
2025-04-29 12:01:14,077 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBI
2025-04-29 12:01:14,077 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1626.6, 'new_value': 2084.6}, {'field': 'offline_amount', 'old_value': 27930.71, 'new_value': 28345.91}, {'field': 'total_amount', 'old_value': 29557.31, 'new_value': 30430.51}, {'field': 'order_count', 'old_value': 1214, 'new_value': 1237}]
2025-04-29 12:01:14,077 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCI
2025-04-29 12:01:14,593 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCI
2025-04-29 12:01:14,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 842643.88, 'new_value': 868439.09}, {'field': 'total_amount', 'old_value': 842643.88, 'new_value': 868439.09}, {'field': 'order_count', 'old_value': 5792, 'new_value': 5998}]
2025-04-29 12:01:14,593 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEI
2025-04-29 12:01:15,015 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEI
2025-04-29 12:01:15,015 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 162247.0, 'new_value': 168448.0}, {'field': 'offline_amount', 'old_value': 120538.0, 'new_value': 125276.0}, {'field': 'total_amount', 'old_value': 282785.0, 'new_value': 293724.0}, {'field': 'order_count', 'old_value': 10890, 'new_value': 11407}]
2025-04-29 12:01:15,015 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGI
2025-04-29 12:01:15,452 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGI
2025-04-29 12:01:15,452 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49058.08, 'new_value': 50397.52}, {'field': 'offline_amount', 'old_value': 183916.17, 'new_value': 188758.28}, {'field': 'total_amount', 'old_value': 232974.25, 'new_value': 239155.8}, {'field': 'order_count', 'old_value': 4560, 'new_value': 4704}]
2025-04-29 12:01:15,452 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJI
2025-04-29 12:01:15,859 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJI
2025-04-29 12:01:15,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234933.92, 'new_value': 241558.92}, {'field': 'total_amount', 'old_value': 234933.92, 'new_value': 241558.92}, {'field': 'order_count', 'old_value': 1809, 'new_value': 1861}]
2025-04-29 12:01:15,859 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNI
2025-04-29 12:01:16,312 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNI
2025-04-29 12:01:16,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28579.0, 'new_value': 28808.0}, {'field': 'total_amount', 'old_value': 28579.0, 'new_value': 28808.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 144}]
2025-04-29 12:01:16,312 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRI
2025-04-29 12:01:16,765 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRI
2025-04-29 12:01:16,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111250.81, 'new_value': 114347.81}, {'field': 'total_amount', 'old_value': 122767.81, 'new_value': 125864.81}, {'field': 'order_count', 'old_value': 970, 'new_value': 990}]
2025-04-29 12:01:16,765 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYI
2025-04-29 12:01:17,249 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYI
2025-04-29 12:01:17,249 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36596.24, 'new_value': 38053.24}, {'field': 'total_amount', 'old_value': 36696.44, 'new_value': 38153.44}, {'field': 'order_count', 'old_value': 298, 'new_value': 305}]
2025-04-29 12:01:17,249 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1J
2025-04-29 12:01:17,656 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1J
2025-04-29 12:01:17,656 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1035202.0, 'new_value': 1074974.0}, {'field': 'offline_amount', 'old_value': 511528.0, 'new_value': 531390.0}, {'field': 'total_amount', 'old_value': 1546730.0, 'new_value': 1606364.0}, {'field': 'order_count', 'old_value': 1282, 'new_value': 1338}]
2025-04-29 12:01:17,656 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3J
2025-04-29 12:01:18,140 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3J
2025-04-29 12:01:18,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12699.0, 'new_value': 13327.0}, {'field': 'total_amount', 'old_value': 12699.0, 'new_value': 13327.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-04-29 12:01:18,140 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6J
2025-04-29 12:01:18,624 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6J
2025-04-29 12:01:18,624 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88187.2, 'new_value': 89528.9}, {'field': 'offline_amount', 'old_value': 162616.1, 'new_value': 166584.3}, {'field': 'total_amount', 'old_value': 250803.3, 'new_value': 256113.2}, {'field': 'order_count', 'old_value': 4510, 'new_value': 4616}]
2025-04-29 12:01:18,624 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9J
2025-04-29 12:01:19,077 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9J
2025-04-29 12:01:19,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47519.02, 'new_value': 48659.56}, {'field': 'total_amount', 'old_value': 47519.02, 'new_value': 48659.56}]
2025-04-29 12:01:19,077 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAJ
2025-04-29 12:01:19,577 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAJ
2025-04-29 12:01:19,577 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170352.48, 'new_value': 173844.75}, {'field': 'total_amount', 'old_value': 170352.48, 'new_value': 173844.75}, {'field': 'order_count', 'old_value': 7469, 'new_value': 7638}]
2025-04-29 12:01:19,577 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBJ
2025-04-29 12:01:20,062 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBJ
2025-04-29 12:01:20,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80216.45, 'new_value': 84899.75}, {'field': 'total_amount', 'old_value': 80216.45, 'new_value': 84899.75}, {'field': 'order_count', 'old_value': 286, 'new_value': 295}]
2025-04-29 12:01:20,062 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJJ
2025-04-29 12:01:20,531 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJJ
2025-04-29 12:01:20,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89900.6, 'new_value': 91489.3}, {'field': 'total_amount', 'old_value': 89900.6, 'new_value': 91489.3}, {'field': 'order_count', 'old_value': 295, 'new_value': 302}]
2025-04-29 12:01:20,531 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKJ
2025-04-29 12:01:20,999 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKJ
2025-04-29 12:01:20,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 406654.13, 'new_value': 419220.43}, {'field': 'total_amount', 'old_value': 417417.35, 'new_value': 429983.65}, {'field': 'order_count', 'old_value': 17554, 'new_value': 18105}]
2025-04-29 12:01:20,999 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLJ
2025-04-29 12:01:21,421 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLJ
2025-04-29 12:01:21,421 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 216974.91, 'new_value': 224078.15}, {'field': 'offline_amount', 'old_value': 43000.97, 'new_value': 43622.17}, {'field': 'total_amount', 'old_value': 259975.88, 'new_value': 267700.32}, {'field': 'order_count', 'old_value': 12475, 'new_value': 12731}]
2025-04-29 12:01:21,421 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNJ
2025-04-29 12:01:21,906 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNJ
2025-04-29 12:01:21,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106508.0, 'new_value': 110158.0}, {'field': 'total_amount', 'old_value': 106508.0, 'new_value': 110158.0}, {'field': 'order_count', 'old_value': 287, 'new_value': 298}]
2025-04-29 12:01:21,906 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-04-29 12:01:22,343 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-04-29 12:01:22,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56613.36, 'new_value': 58916.03}, {'field': 'offline_amount', 'old_value': 54302.13, 'new_value': 56157.63}, {'field': 'total_amount', 'old_value': 110915.49, 'new_value': 115073.66}, {'field': 'order_count', 'old_value': 2779, 'new_value': 2880}]
2025-04-29 12:01:22,343 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQJ
2025-04-29 12:01:22,874 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQJ
2025-04-29 12:01:22,874 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143552.0, 'new_value': 145931.0}, {'field': 'total_amount', 'old_value': 143552.0, 'new_value': 145931.0}, {'field': 'order_count', 'old_value': 2507, 'new_value': 2552}]
2025-04-29 12:01:22,874 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVJ
2025-04-29 12:01:23,359 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVJ
2025-04-29 12:01:23,359 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2958.41, 'new_value': 3064.64}, {'field': 'offline_amount', 'old_value': 15099.56, 'new_value': 15350.76}, {'field': 'total_amount', 'old_value': 18057.97, 'new_value': 18415.4}, {'field': 'order_count', 'old_value': 650, 'new_value': 664}]
2025-04-29 12:01:23,359 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXJ
2025-04-29 12:01:23,796 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXJ
2025-04-29 12:01:23,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47050.0, 'new_value': 47926.0}, {'field': 'total_amount', 'old_value': 47050.0, 'new_value': 47926.0}, {'field': 'order_count', 'old_value': 113, 'new_value': 116}]
2025-04-29 12:01:23,796 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M2
2025-04-29 12:01:24,234 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M2
2025-04-29 12:01:24,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 273560.38, 'new_value': 277632.18}, {'field': 'total_amount', 'old_value': 273560.38, 'new_value': 277632.18}, {'field': 'order_count', 'old_value': 1957, 'new_value': 1994}]
2025-04-29 12:01:24,234 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M4
2025-04-29 12:01:24,609 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M4
2025-04-29 12:01:24,609 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39021.84, 'new_value': 42140.54}, {'field': 'total_amount', 'old_value': 140480.55, 'new_value': 143599.25}, {'field': 'order_count', 'old_value': 4036, 'new_value': 4128}]
2025-04-29 12:01:24,609 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD
2025-04-29 12:01:25,093 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD
2025-04-29 12:01:25,093 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 20000.0}, {'field': 'total_amount', 'old_value': 78859.0, 'new_value': 98859.0}]
2025-04-29 12:01:25,093 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF
2025-04-29 12:01:25,515 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF
2025-04-29 12:01:25,515 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18339.1, 'new_value': 19023.0}, {'field': 'offline_amount', 'old_value': 200115.3, 'new_value': 205243.4}, {'field': 'total_amount', 'old_value': 218454.4, 'new_value': 224266.4}, {'field': 'order_count', 'old_value': 6751, 'new_value': 6962}]
2025-04-29 12:01:25,515 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM
2025-04-29 12:01:25,890 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM
2025-04-29 12:01:25,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52358.4, 'new_value': 54998.0}, {'field': 'total_amount', 'old_value': 52358.4, 'new_value': 54998.0}, {'field': 'order_count', 'old_value': 287, 'new_value': 299}]
2025-04-29 12:01:25,890 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU
2025-04-29 12:01:26,374 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU
2025-04-29 12:01:26,374 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3748.0, 'new_value': 3848.0}, {'field': 'offline_amount', 'old_value': 20827.8, 'new_value': 21356.8}, {'field': 'total_amount', 'old_value': 24575.8, 'new_value': 25204.8}, {'field': 'order_count', 'old_value': 899, 'new_value': 919}]
2025-04-29 12:01:26,374 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX
2025-04-29 12:01:26,859 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX
2025-04-29 12:01:26,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19148.9, 'new_value': 19297.9}, {'field': 'total_amount', 'old_value': 19148.9, 'new_value': 19297.9}, {'field': 'order_count', 'old_value': 125, 'new_value': 128}]
2025-04-29 12:01:26,859 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY
2025-04-29 12:01:27,234 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY
2025-04-29 12:01:27,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 253941.0, 'new_value': 280713.0}, {'field': 'total_amount', 'old_value': 253942.0, 'new_value': 280714.0}, {'field': 'order_count', 'old_value': 408, 'new_value': 439}]
2025-04-29 12:01:27,234 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M71
2025-04-29 12:01:27,671 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M71
2025-04-29 12:01:27,671 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85914.85, 'new_value': 90263.35}, {'field': 'offline_amount', 'old_value': 257083.59, 'new_value': 265402.43}, {'field': 'total_amount', 'old_value': 342998.44, 'new_value': 355665.78}, {'field': 'order_count', 'old_value': 2337, 'new_value': 2437}]
2025-04-29 12:01:27,671 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA1
2025-04-29 12:01:28,140 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA1
2025-04-29 12:01:28,140 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 215437.9, 'new_value': 220556.24}, {'field': 'offline_amount', 'old_value': 44631.68, 'new_value': 45415.99}, {'field': 'total_amount', 'old_value': 260069.58, 'new_value': 265972.23}, {'field': 'order_count', 'old_value': 13883, 'new_value': 14243}]
2025-04-29 12:01:28,140 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB1
2025-04-29 12:01:28,640 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB1
2025-04-29 12:01:28,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 208678.0, 'new_value': 216487.0}, {'field': 'total_amount', 'old_value': 224552.0, 'new_value': 232361.0}, {'field': 'order_count', 'old_value': 314, 'new_value': 327}]
2025-04-29 12:01:28,640 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ1
2025-04-29 12:01:29,093 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ1
2025-04-29 12:01:29,093 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5437.0, 'new_value': 5625.0}, {'field': 'offline_amount', 'old_value': 27580.0, 'new_value': 28567.0}, {'field': 'total_amount', 'old_value': 33017.0, 'new_value': 34192.0}, {'field': 'order_count', 'old_value': 817, 'new_value': 852}]
2025-04-29 12:01:29,093 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MK1
2025-04-29 12:01:29,577 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MK1
2025-04-29 12:01:29,577 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91657.56, 'new_value': 94103.22}, {'field': 'total_amount', 'old_value': 91657.56, 'new_value': 94103.22}, {'field': 'order_count', 'old_value': 8245, 'new_value': 8456}]
2025-04-29 12:01:29,577 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM1
2025-04-29 12:01:30,062 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM1
2025-04-29 12:01:30,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203064.49, 'new_value': 210941.21}, {'field': 'total_amount', 'old_value': 203064.49, 'new_value': 210941.21}, {'field': 'order_count', 'old_value': 14850, 'new_value': 15428}]
2025-04-29 12:01:30,062 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ1
2025-04-29 12:01:30,499 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ1
2025-04-29 12:01:30,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220294.81, 'new_value': 229024.81}, {'field': 'total_amount', 'old_value': 225295.82, 'new_value': 234025.82}, {'field': 'order_count', 'old_value': 3913, 'new_value': 4070}]
2025-04-29 12:01:30,499 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS1
2025-04-29 12:01:30,999 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS1
2025-04-29 12:01:30,999 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21220.9, 'new_value': 22105.45}, {'field': 'offline_amount', 'old_value': 31398.26, 'new_value': 32201.46}, {'field': 'total_amount', 'old_value': 52619.16, 'new_value': 54306.91}, {'field': 'order_count', 'old_value': 2605, 'new_value': 2696}]
2025-04-29 12:01:30,999 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT1
2025-04-29 12:01:31,374 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT1
2025-04-29 12:01:31,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200015.0, 'new_value': 207257.0}, {'field': 'total_amount', 'old_value': 203533.0, 'new_value': 210775.0}, {'field': 'order_count', 'old_value': 29381, 'new_value': 29540}]
2025-04-29 12:01:31,374 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M62
2025-04-29 12:01:31,890 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M62
2025-04-29 12:01:31,890 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111110.5, 'new_value': 117872.37}, {'field': 'total_amount', 'old_value': 182690.01, 'new_value': 189451.88}, {'field': 'order_count', 'old_value': 11967, 'new_value': 12420}]
2025-04-29 12:01:31,890 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M82
2025-04-29 12:01:32,374 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M82
2025-04-29 12:01:32,374 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56748.6, 'new_value': 60440.32}, {'field': 'total_amount', 'old_value': 92717.62, 'new_value': 96409.34}, {'field': 'order_count', 'old_value': 6349, 'new_value': 6591}]
2025-04-29 12:01:32,374 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MB2
2025-04-29 12:01:32,859 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MB2
2025-04-29 12:01:32,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 348467.0, 'new_value': 362603.0}, {'field': 'total_amount', 'old_value': 348467.0, 'new_value': 362603.0}, {'field': 'order_count', 'old_value': 7749, 'new_value': 8042}]
2025-04-29 12:01:32,859 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MC2
2025-04-29 12:01:33,265 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MC2
2025-04-29 12:01:33,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 304207.62, 'new_value': 310036.31}, {'field': 'total_amount', 'old_value': 304207.62, 'new_value': 310036.31}, {'field': 'order_count', 'old_value': 881, 'new_value': 901}]
2025-04-29 12:01:33,265 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MH2
2025-04-29 12:01:33,718 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MH2
2025-04-29 12:01:33,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 443046.54, 'new_value': 455596.71}, {'field': 'total_amount', 'old_value': 445530.12, 'new_value': 458080.29}, {'field': 'order_count', 'old_value': 6949, 'new_value': 7150}]
2025-04-29 12:01:33,718 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-04-29 12:01:34,171 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-04-29 12:01:34,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15039947.0, 'new_value': 15501053.0}, {'field': 'total_amount', 'old_value': 15039947.0, 'new_value': 15501053.0}, {'field': 'order_count', 'old_value': 44553, 'new_value': 45945}]
2025-04-29 12:01:34,171 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ML2
2025-04-29 12:01:34,624 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ML2
2025-04-29 12:01:34,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1145775.33, 'new_value': 1189537.64}, {'field': 'total_amount', 'old_value': 1145775.33, 'new_value': 1189537.64}, {'field': 'order_count', 'old_value': 3516, 'new_value': 3637}]
2025-04-29 12:01:34,624 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MN2
2025-04-29 12:01:35,093 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MN2
2025-04-29 12:01:35,093 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1090191.0, 'new_value': 1125466.32}, {'field': 'total_amount', 'old_value': 1090191.0, 'new_value': 1125466.32}, {'field': 'order_count', 'old_value': 3780, 'new_value': 3892}]
2025-04-29 12:01:35,093 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MP2
2025-04-29 12:01:35,562 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MP2
2025-04-29 12:01:35,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 740093.53, 'new_value': 764703.9}, {'field': 'total_amount', 'old_value': 740093.53, 'new_value': 764703.9}, {'field': 'order_count', 'old_value': 3589, 'new_value': 3743}]
2025-04-29 12:01:35,562 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MT2
2025-04-29 12:01:36,015 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MT2
2025-04-29 12:01:36,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 338206.0, 'new_value': 353788.0}, {'field': 'total_amount', 'old_value': 338206.0, 'new_value': 353788.0}, {'field': 'order_count', 'old_value': 6847, 'new_value': 7193}]
2025-04-29 12:01:36,015 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M16
2025-04-29 12:01:36,437 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M16
2025-04-29 12:01:36,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74693.88, 'new_value': 76505.31}, {'field': 'total_amount', 'old_value': 74693.88, 'new_value': 76505.31}, {'field': 'order_count', 'old_value': 1241, 'new_value': 1275}]
2025-04-29 12:01:36,437 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M26
2025-04-29 12:01:36,937 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M26
2025-04-29 12:01:36,937 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17684.9, 'new_value': 18653.57}, {'field': 'offline_amount', 'old_value': 34400.71, 'new_value': 35863.06}, {'field': 'total_amount', 'old_value': 52085.61, 'new_value': 54516.63}, {'field': 'order_count', 'old_value': 2440, 'new_value': 2573}]
2025-04-29 12:01:36,937 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M36
2025-04-29 12:01:37,343 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M36
2025-04-29 12:01:37,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215421.65, 'new_value': 224448.75}, {'field': 'total_amount', 'old_value': 215421.65, 'new_value': 224448.75}, {'field': 'order_count', 'old_value': 15234, 'new_value': 15936}]
2025-04-29 12:01:37,343 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M66
2025-04-29 12:01:37,843 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M66
2025-04-29 12:01:37,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 305808.0, 'new_value': 314433.0}, {'field': 'total_amount', 'old_value': 305808.0, 'new_value': 314433.0}, {'field': 'order_count', 'old_value': 12433, 'new_value': 12525}]
2025-04-29 12:01:37,843 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M76
2025-04-29 12:01:38,374 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M76
2025-04-29 12:01:38,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 751451.69, 'new_value': 769280.15}, {'field': 'total_amount', 'old_value': 751451.69, 'new_value': 769280.15}, {'field': 'order_count', 'old_value': 6177, 'new_value': 6368}]
2025-04-29 12:01:38,374 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M86
2025-04-29 12:01:38,812 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M86
2025-04-29 12:01:38,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131376.83, 'new_value': 134484.41}, {'field': 'total_amount', 'old_value': 131376.83, 'new_value': 134484.41}, {'field': 'order_count', 'old_value': 3415, 'new_value': 3499}]
2025-04-29 12:01:38,812 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA6
2025-04-29 12:01:39,468 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA6
2025-04-29 12:01:39,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19080.0, 'new_value': 31960.0}, {'field': 'total_amount', 'old_value': 19080.0, 'new_value': 31960.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-04-29 12:01:39,468 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC6
2025-04-29 12:01:39,905 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC6
2025-04-29 12:01:39,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185026.77, 'new_value': 190676.14}, {'field': 'total_amount', 'old_value': 185026.77, 'new_value': 190676.14}, {'field': 'order_count', 'old_value': 20429, 'new_value': 21099}]
2025-04-29 12:01:39,905 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD6
2025-04-29 12:01:40,359 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD6
2025-04-29 12:01:40,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15289.0, 'new_value': 15489.0}, {'field': 'total_amount', 'old_value': 15289.0, 'new_value': 15489.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-04-29 12:01:40,359 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF6
2025-04-29 12:01:40,859 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF6
2025-04-29 12:01:40,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1307836.0, 'new_value': 1349812.0}, {'field': 'total_amount', 'old_value': 1307836.0, 'new_value': 1349812.0}, {'field': 'order_count', 'old_value': 5505, 'new_value': 5694}]
2025-04-29 12:01:40,859 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG6
2025-04-29 12:01:41,296 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG6
2025-04-29 12:01:41,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48708.0, 'new_value': 50633.0}, {'field': 'total_amount', 'old_value': 48708.0, 'new_value': 50633.0}, {'field': 'order_count', 'old_value': 307, 'new_value': 318}]
2025-04-29 12:01:41,296 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI6
2025-04-29 12:01:41,718 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI6
2025-04-29 12:01:41,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 771896.17, 'new_value': 795917.97}, {'field': 'total_amount', 'old_value': 807965.15, 'new_value': 831986.95}, {'field': 'order_count', 'old_value': 2053, 'new_value': 2120}]
2025-04-29 12:01:41,718 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML6
2025-04-29 12:01:42,124 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML6
2025-04-29 12:01:42,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 377882.0, 'new_value': 385282.0}, {'field': 'total_amount', 'old_value': 377882.0, 'new_value': 385282.0}, {'field': 'order_count', 'old_value': 684, 'new_value': 701}]
2025-04-29 12:01:42,124 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM6
2025-04-29 12:01:42,562 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM6
2025-04-29 12:01:42,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64022.02, 'new_value': 63164.39}, {'field': 'total_amount', 'old_value': 64022.02, 'new_value': 63164.39}, {'field': 'order_count', 'old_value': 1077, 'new_value': 1071}]
2025-04-29 12:01:42,562 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO6
2025-04-29 12:01:43,015 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO6
2025-04-29 12:01:43,015 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18329.08, 'new_value': 18524.08}, {'field': 'offline_amount', 'old_value': 273080.0, 'new_value': 283289.0}, {'field': 'total_amount', 'old_value': 291409.08, 'new_value': 301813.08}, {'field': 'order_count', 'old_value': 1435, 'new_value': 1485}]
2025-04-29 12:01:43,015 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT6
2025-04-29 12:01:43,562 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT6
2025-04-29 12:01:43,562 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94824.54, 'new_value': 100706.51}, {'field': 'offline_amount', 'old_value': 350386.04, 'new_value': 356420.74}, {'field': 'total_amount', 'old_value': 445210.58, 'new_value': 457127.25}, {'field': 'order_count', 'old_value': 3282, 'new_value': 3406}]
2025-04-29 12:01:43,562 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY6
2025-04-29 12:01:43,984 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY6
2025-04-29 12:01:43,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 288982.91, 'new_value': 301221.91}, {'field': 'total_amount', 'old_value': 288982.91, 'new_value': 301221.91}, {'field': 'order_count', 'old_value': 6125, 'new_value': 6406}]
2025-04-29 12:01:43,984 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M17
2025-04-29 12:01:44,421 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M17
2025-04-29 12:01:44,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223004.43, 'new_value': 233140.63}, {'field': 'total_amount', 'old_value': 223004.43, 'new_value': 233140.63}, {'field': 'order_count', 'old_value': 8327, 'new_value': 8663}]
2025-04-29 12:01:44,421 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M47
2025-04-29 12:01:44,859 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M47
2025-04-29 12:01:44,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138404.96, 'new_value': 142824.2}, {'field': 'total_amount', 'old_value': 138404.96, 'new_value': 142824.2}, {'field': 'order_count', 'old_value': 3248, 'new_value': 3370}]
2025-04-29 12:01:44,859 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M57
2025-04-29 12:01:45,327 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M57
2025-04-29 12:01:45,327 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65722.0, 'new_value': 67322.0}, {'field': 'total_amount', 'old_value': 65722.0, 'new_value': 67322.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 42}]
2025-04-29 12:01:45,327 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M67
2025-04-29 12:01:45,780 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M67
2025-04-29 12:01:45,780 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106899.6, 'new_value': 110452.4}, {'field': 'offline_amount', 'old_value': 28612.9, 'new_value': 29450.9}, {'field': 'total_amount', 'old_value': 135512.5, 'new_value': 139903.3}, {'field': 'order_count', 'old_value': 11713, 'new_value': 12121}]
2025-04-29 12:01:45,780 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M97
2025-04-29 12:01:46,437 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M97
2025-04-29 12:01:46,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 373149.4, 'new_value': 387315.6}, {'field': 'total_amount', 'old_value': 373149.4, 'new_value': 387315.6}, {'field': 'order_count', 'old_value': 1511, 'new_value': 1568}]
2025-04-29 12:01:46,437 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA7
2025-04-29 12:01:46,937 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA7
2025-04-29 12:01:46,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3617930.66, 'new_value': 3726692.53}, {'field': 'total_amount', 'old_value': 3617930.66, 'new_value': 3726692.53}, {'field': 'order_count', 'old_value': 6274, 'new_value': 6474}]
2025-04-29 12:01:46,937 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD7
2025-04-29 12:01:47,359 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD7
2025-04-29 12:01:47,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23887.0, 'new_value': 24087.0}, {'field': 'total_amount', 'old_value': 23887.0, 'new_value': 24087.0}, {'field': 'order_count', 'old_value': 227, 'new_value': 230}]
2025-04-29 12:01:47,359 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG7
2025-04-29 12:01:47,796 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG7
2025-04-29 12:01:47,796 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81551.0, 'new_value': 87192.0}, {'field': 'offline_amount', 'old_value': 148358.0, 'new_value': 150635.0}, {'field': 'total_amount', 'old_value': 229909.0, 'new_value': 237827.0}, {'field': 'order_count', 'old_value': 5528, 'new_value': 5720}]
2025-04-29 12:01:47,796 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI7
2025-04-29 12:01:48,234 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI7
2025-04-29 12:01:48,234 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125387.12, 'new_value': 128571.78}, {'field': 'offline_amount', 'old_value': 237687.09, 'new_value': 241629.99}, {'field': 'total_amount', 'old_value': 363074.21, 'new_value': 370201.77}, {'field': 'order_count', 'old_value': 4284, 'new_value': 4370}]
2025-04-29 12:01:48,234 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO7
2025-04-29 12:01:48,624 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO7
2025-04-29 12:01:48,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 369040.0, 'new_value': 382330.0}, {'field': 'total_amount', 'old_value': 369040.0, 'new_value': 382330.0}, {'field': 'order_count', 'old_value': 9172, 'new_value': 9502}]
2025-04-29 12:01:48,624 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ7
2025-04-29 12:01:49,077 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ7
2025-04-29 12:01:49,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81559.0, 'new_value': 82530.0}, {'field': 'total_amount', 'old_value': 81559.0, 'new_value': 82530.0}, {'field': 'order_count', 'old_value': 1461, 'new_value': 1492}]
2025-04-29 12:01:49,077 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR7
2025-04-29 12:01:49,546 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR7
2025-04-29 12:01:49,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37366.0, 'new_value': 37666.0}, {'field': 'total_amount', 'old_value': 37366.0, 'new_value': 37666.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-04-29 12:01:49,546 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY7
2025-04-29 12:01:49,968 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY7
2025-04-29 12:01:49,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17341.7, 'new_value': 17440.7}, {'field': 'total_amount', 'old_value': 17341.7, 'new_value': 17440.7}, {'field': 'order_count', 'old_value': 67, 'new_value': 68}]
2025-04-29 12:01:49,968 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF8
2025-04-29 12:01:50,405 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF8
2025-04-29 12:01:50,405 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1442000.0, 'new_value': 1482000.0}, {'field': 'total_amount', 'old_value': 1442000.0, 'new_value': 1482000.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-04-29 12:01:50,405 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM8
2025-04-29 12:01:50,843 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM8
2025-04-29 12:01:50,843 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-04-29 12:01:50,843 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MV1
2025-04-29 12:01:51,327 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MV1
2025-04-29 12:01:51,327 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128704.0, 'new_value': 132049.0}, {'field': 'total_amount', 'old_value': 128704.0, 'new_value': 132049.0}, {'field': 'order_count', 'old_value': 1262, 'new_value': 1300}]
2025-04-29 12:01:51,327 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MW1
2025-04-29 12:01:51,905 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MW1
2025-04-29 12:01:51,905 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 307054.34, 'new_value': 318493.58}, {'field': 'offline_amount', 'old_value': 263713.85, 'new_value': 270869.22}, {'field': 'total_amount', 'old_value': 570768.19, 'new_value': 589362.8}, {'field': 'order_count', 'old_value': 15781, 'new_value': 16327}]
2025-04-29 12:01:51,905 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MY1
2025-04-29 12:01:52,468 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MY1
2025-04-29 12:01:52,468 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75354.77, 'new_value': 77878.35}, {'field': 'offline_amount', 'old_value': 79914.49, 'new_value': 82787.3}, {'field': 'total_amount', 'old_value': 155269.26, 'new_value': 160665.65}, {'field': 'order_count', 'old_value': 6425, 'new_value': 6650}]
2025-04-29 12:01:52,468 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MZ1
2025-04-29 12:01:52,905 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MZ1
2025-04-29 12:01:52,905 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 233858.48, 'new_value': 240282.39}, {'field': 'offline_amount', 'old_value': 513741.05, 'new_value': 523741.05}, {'field': 'total_amount', 'old_value': 747599.53, 'new_value': 764023.44}, {'field': 'order_count', 'old_value': 1686, 'new_value': 1734}]
2025-04-29 12:01:52,905 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-04-29 12:01:53,327 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-04-29 12:01:53,327 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154839.0, 'new_value': 159621.0}, {'field': 'total_amount', 'old_value': 172653.0, 'new_value': 177435.0}, {'field': 'order_count', 'old_value': 736, 'new_value': 766}]
2025-04-29 12:01:53,327 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-04-29 12:01:53,780 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-04-29 12:01:53,780 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121864.0, 'new_value': 125406.0}, {'field': 'total_amount', 'old_value': 127466.0, 'new_value': 131008.0}, {'field': 'order_count', 'old_value': 439, 'new_value': 455}]
2025-04-29 12:01:53,780 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M32
2025-04-29 12:01:54,296 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M32
2025-04-29 12:01:54,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40395.0, 'new_value': 41895.0}, {'field': 'total_amount', 'old_value': 114727.0, 'new_value': 116227.0}, {'field': 'order_count', 'old_value': 194, 'new_value': 195}]
2025-04-29 12:01:54,296 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M52
2025-04-29 12:01:54,749 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M52
2025-04-29 12:01:54,749 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8060.7, 'new_value': 8140.6}, {'field': 'offline_amount', 'old_value': 43795.5, 'new_value': 44412.5}, {'field': 'total_amount', 'old_value': 51856.2, 'new_value': 52553.1}, {'field': 'order_count', 'old_value': 494, 'new_value': 499}]
2025-04-29 12:01:54,749 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M82
2025-04-29 12:01:55,265 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M82
2025-04-29 12:01:55,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14890295.61, 'new_value': 15441788.04}, {'field': 'total_amount', 'old_value': 14890295.61, 'new_value': 15441788.04}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-04-29 12:01:55,265 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MC2
2025-04-29 12:01:55,702 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MC2
2025-04-29 12:01:55,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89844.0, 'new_value': 103654.0}, {'field': 'total_amount', 'old_value': 130285.0, 'new_value': 144095.0}, {'field': 'order_count', 'old_value': 890, 'new_value': 972}]
2025-04-29 12:01:55,702 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-04-29 12:01:56,202 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-04-29 12:01:56,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29338.25, 'new_value': 36838.25}, {'field': 'total_amount', 'old_value': 29338.25, 'new_value': 36838.25}, {'field': 'order_count', 'old_value': 24, 'new_value': 44}]
2025-04-29 12:01:56,202 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I718UXUFQYW74TY65GTGPXF3UKTQW0AML7
2025-04-29 12:01:56,765 - INFO - 更新表单数据成功: FINST-2FD66I718UXUFQYW74TY65GTGPXF3UKTQW0AML7
2025-04-29 12:01:56,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11382.0, 'new_value': 13337.0}, {'field': 'offline_amount', 'old_value': 7371.0, 'new_value': 8750.0}, {'field': 'total_amount', 'old_value': 18753.0, 'new_value': 22087.0}, {'field': 'order_count', 'old_value': 116, 'new_value': 129}]
2025-04-29 12:01:56,765 - INFO - 日期 2025-04 处理完成 - 更新: 200 条，插入: 0 条，错误: 0 条
2025-04-29 12:01:56,765 - INFO - 数据同步完成！更新: 200 条，插入: 0 条，错误: 0 条
2025-04-29 12:01:56,765 - INFO - =================同步完成====================
2025-04-29 15:00:01,870 - INFO - =================使用默认全量同步=============
2025-04-29 15:00:03,010 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-29 15:00:03,010 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-29 15:00:03,026 - INFO - 开始处理日期: 2025-01
2025-04-29 15:00:03,026 - INFO - Request Parameters - Page 1:
2025-04-29 15:00:03,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:03,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:03,901 - INFO - Response - Page 1:
2025-04-29 15:00:04,104 - INFO - 第 1 页获取到 100 条记录
2025-04-29 15:00:04,104 - INFO - Request Parameters - Page 2:
2025-04-29 15:00:04,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:04,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:04,620 - INFO - Response - Page 2:
2025-04-29 15:00:04,823 - INFO - 第 2 页获取到 100 条记录
2025-04-29 15:00:04,823 - INFO - Request Parameters - Page 3:
2025-04-29 15:00:04,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:04,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:05,620 - INFO - Response - Page 3:
2025-04-29 15:00:05,823 - INFO - 第 3 页获取到 100 条记录
2025-04-29 15:00:05,823 - INFO - Request Parameters - Page 4:
2025-04-29 15:00:05,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:05,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:06,339 - INFO - Response - Page 4:
2025-04-29 15:00:06,542 - INFO - 第 4 页获取到 100 条记录
2025-04-29 15:00:06,542 - INFO - Request Parameters - Page 5:
2025-04-29 15:00:06,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:06,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:07,057 - INFO - Response - Page 5:
2025-04-29 15:00:07,260 - INFO - 第 5 页获取到 100 条记录
2025-04-29 15:00:07,260 - INFO - Request Parameters - Page 6:
2025-04-29 15:00:07,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:07,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:07,760 - INFO - Response - Page 6:
2025-04-29 15:00:07,964 - INFO - 第 6 页获取到 100 条记录
2025-04-29 15:00:07,964 - INFO - Request Parameters - Page 7:
2025-04-29 15:00:07,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:07,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:08,667 - INFO - Response - Page 7:
2025-04-29 15:00:08,870 - INFO - 第 7 页获取到 82 条记录
2025-04-29 15:00:08,870 - INFO - 查询完成，共获取到 682 条记录
2025-04-29 15:00:08,870 - INFO - 获取到 682 条表单数据
2025-04-29 15:00:08,870 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-29 15:00:08,885 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 15:00:08,885 - INFO - 开始处理日期: 2025-02
2025-04-29 15:00:08,885 - INFO - Request Parameters - Page 1:
2025-04-29 15:00:08,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:08,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:09,354 - INFO - Response - Page 1:
2025-04-29 15:00:09,557 - INFO - 第 1 页获取到 100 条记录
2025-04-29 15:00:09,557 - INFO - Request Parameters - Page 2:
2025-04-29 15:00:09,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:09,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:10,057 - INFO - Response - Page 2:
2025-04-29 15:00:10,260 - INFO - 第 2 页获取到 100 条记录
2025-04-29 15:00:10,260 - INFO - Request Parameters - Page 3:
2025-04-29 15:00:10,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:10,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:10,760 - INFO - Response - Page 3:
2025-04-29 15:00:10,964 - INFO - 第 3 页获取到 100 条记录
2025-04-29 15:00:10,964 - INFO - Request Parameters - Page 4:
2025-04-29 15:00:10,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:10,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:11,417 - INFO - Response - Page 4:
2025-04-29 15:00:11,620 - INFO - 第 4 页获取到 100 条记录
2025-04-29 15:00:11,620 - INFO - Request Parameters - Page 5:
2025-04-29 15:00:11,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:11,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:12,089 - INFO - Response - Page 5:
2025-04-29 15:00:12,292 - INFO - 第 5 页获取到 100 条记录
2025-04-29 15:00:12,292 - INFO - Request Parameters - Page 6:
2025-04-29 15:00:12,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:12,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:12,760 - INFO - Response - Page 6:
2025-04-29 15:00:12,963 - INFO - 第 6 页获取到 100 条记录
2025-04-29 15:00:12,963 - INFO - Request Parameters - Page 7:
2025-04-29 15:00:12,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:12,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:13,464 - INFO - Response - Page 7:
2025-04-29 15:00:13,667 - INFO - 第 7 页获取到 70 条记录
2025-04-29 15:00:13,667 - INFO - 查询完成，共获取到 670 条记录
2025-04-29 15:00:13,667 - INFO - 获取到 670 条表单数据
2025-04-29 15:00:13,667 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-29 15:00:13,682 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 15:00:13,682 - INFO - 开始处理日期: 2025-03
2025-04-29 15:00:13,682 - INFO - Request Parameters - Page 1:
2025-04-29 15:00:13,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:13,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:14,120 - INFO - Response - Page 1:
2025-04-29 15:00:14,323 - INFO - 第 1 页获取到 100 条记录
2025-04-29 15:00:14,323 - INFO - Request Parameters - Page 2:
2025-04-29 15:00:14,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:14,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:14,870 - INFO - Response - Page 2:
2025-04-29 15:00:15,073 - INFO - 第 2 页获取到 100 条记录
2025-04-29 15:00:15,073 - INFO - Request Parameters - Page 3:
2025-04-29 15:00:15,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:15,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:15,557 - INFO - Response - Page 3:
2025-04-29 15:00:15,760 - INFO - 第 3 页获取到 100 条记录
2025-04-29 15:00:15,760 - INFO - Request Parameters - Page 4:
2025-04-29 15:00:15,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:15,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:16,229 - INFO - Response - Page 4:
2025-04-29 15:00:16,432 - INFO - 第 4 页获取到 100 条记录
2025-04-29 15:00:16,432 - INFO - Request Parameters - Page 5:
2025-04-29 15:00:16,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:16,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:16,917 - INFO - Response - Page 5:
2025-04-29 15:00:17,120 - INFO - 第 5 页获取到 100 条记录
2025-04-29 15:00:17,120 - INFO - Request Parameters - Page 6:
2025-04-29 15:00:17,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:17,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:17,604 - INFO - Response - Page 6:
2025-04-29 15:00:17,807 - INFO - 第 6 页获取到 100 条记录
2025-04-29 15:00:17,807 - INFO - Request Parameters - Page 7:
2025-04-29 15:00:17,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:17,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:18,260 - INFO - Response - Page 7:
2025-04-29 15:00:18,463 - INFO - 第 7 页获取到 61 条记录
2025-04-29 15:00:18,463 - INFO - 查询完成，共获取到 661 条记录
2025-04-29 15:00:18,463 - INFO - 获取到 661 条表单数据
2025-04-29 15:00:18,463 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-29 15:00:18,479 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 15:00:18,479 - INFO - 开始处理日期: 2025-04
2025-04-29 15:00:18,479 - INFO - Request Parameters - Page 1:
2025-04-29 15:00:18,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:18,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:18,963 - INFO - Response - Page 1:
2025-04-29 15:00:19,167 - INFO - 第 1 页获取到 100 条记录
2025-04-29 15:00:19,167 - INFO - Request Parameters - Page 2:
2025-04-29 15:00:19,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:19,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:19,760 - INFO - Response - Page 2:
2025-04-29 15:00:19,963 - INFO - 第 2 页获取到 100 条记录
2025-04-29 15:00:19,963 - INFO - Request Parameters - Page 3:
2025-04-29 15:00:19,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:19,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:20,463 - INFO - Response - Page 3:
2025-04-29 15:00:20,667 - INFO - 第 3 页获取到 100 条记录
2025-04-29 15:00:20,667 - INFO - Request Parameters - Page 4:
2025-04-29 15:00:20,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:20,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:21,104 - INFO - Response - Page 4:
2025-04-29 15:00:21,307 - INFO - 第 4 页获取到 100 条记录
2025-04-29 15:00:21,307 - INFO - Request Parameters - Page 5:
2025-04-29 15:00:21,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:21,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:21,760 - INFO - Response - Page 5:
2025-04-29 15:00:21,963 - INFO - 第 5 页获取到 100 条记录
2025-04-29 15:00:21,963 - INFO - Request Parameters - Page 6:
2025-04-29 15:00:21,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:21,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:22,448 - INFO - Response - Page 6:
2025-04-29 15:00:22,651 - INFO - 第 6 页获取到 100 条记录
2025-04-29 15:00:22,651 - INFO - Request Parameters - Page 7:
2025-04-29 15:00:22,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:00:22,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:00:22,995 - INFO - Response - Page 7:
2025-04-29 15:00:23,198 - INFO - 第 7 页获取到 27 条记录
2025-04-29 15:00:23,198 - INFO - 查询完成，共获取到 627 条记录
2025-04-29 15:00:23,198 - INFO - 获取到 627 条表单数据
2025-04-29 15:00:23,198 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-29 15:00:23,198 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8A
2025-04-29 15:00:23,620 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8A
2025-04-29 15:00:23,620 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 167627.68, 'new_value': 177086.3}, {'field': 'offline_amount', 'old_value': 683813.48, 'new_value': 705455.94}, {'field': 'total_amount', 'old_value': 851441.16, 'new_value': 882542.24}, {'field': 'order_count', 'old_value': 1926, 'new_value': 2000}]
2025-04-29 15:00:23,620 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-04-29 15:00:23,620 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-04-29 15:00:23,635 - INFO - =================同步完成====================
2025-04-29 18:00:01,943 - INFO - =================使用默认全量同步=============
2025-04-29 18:00:03,084 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-29 18:00:03,084 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-29 18:00:03,115 - INFO - 开始处理日期: 2025-01
2025-04-29 18:00:03,115 - INFO - Request Parameters - Page 1:
2025-04-29 18:00:03,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:03,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:03,959 - INFO - Response - Page 1:
2025-04-29 18:00:04,162 - INFO - 第 1 页获取到 100 条记录
2025-04-29 18:00:04,162 - INFO - Request Parameters - Page 2:
2025-04-29 18:00:04,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:04,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:04,896 - INFO - Response - Page 2:
2025-04-29 18:00:05,100 - INFO - 第 2 页获取到 100 条记录
2025-04-29 18:00:05,100 - INFO - Request Parameters - Page 3:
2025-04-29 18:00:05,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:05,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:05,631 - INFO - Response - Page 3:
2025-04-29 18:00:05,834 - INFO - 第 3 页获取到 100 条记录
2025-04-29 18:00:05,834 - INFO - Request Parameters - Page 4:
2025-04-29 18:00:05,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:05,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:06,334 - INFO - Response - Page 4:
2025-04-29 18:00:06,537 - INFO - 第 4 页获取到 100 条记录
2025-04-29 18:00:06,537 - INFO - Request Parameters - Page 5:
2025-04-29 18:00:06,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:06,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:07,021 - INFO - Response - Page 5:
2025-04-29 18:00:07,225 - INFO - 第 5 页获取到 100 条记录
2025-04-29 18:00:07,225 - INFO - Request Parameters - Page 6:
2025-04-29 18:00:07,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:07,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:07,959 - INFO - Response - Page 6:
2025-04-29 18:00:08,162 - INFO - 第 6 页获取到 100 条记录
2025-04-29 18:00:08,162 - INFO - Request Parameters - Page 7:
2025-04-29 18:00:08,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:08,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:08,631 - INFO - Response - Page 7:
2025-04-29 18:00:08,834 - INFO - 第 7 页获取到 82 条记录
2025-04-29 18:00:08,834 - INFO - 查询完成，共获取到 682 条记录
2025-04-29 18:00:08,834 - INFO - 获取到 682 条表单数据
2025-04-29 18:00:08,834 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-29 18:00:08,850 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 18:00:08,850 - INFO - 开始处理日期: 2025-02
2025-04-29 18:00:08,850 - INFO - Request Parameters - Page 1:
2025-04-29 18:00:08,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:08,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:09,365 - INFO - Response - Page 1:
2025-04-29 18:00:09,568 - INFO - 第 1 页获取到 100 条记录
2025-04-29 18:00:09,568 - INFO - Request Parameters - Page 2:
2025-04-29 18:00:09,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:09,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:10,068 - INFO - Response - Page 2:
2025-04-29 18:00:10,271 - INFO - 第 2 页获取到 100 条记录
2025-04-29 18:00:10,271 - INFO - Request Parameters - Page 3:
2025-04-29 18:00:10,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:10,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:10,771 - INFO - Response - Page 3:
2025-04-29 18:00:10,975 - INFO - 第 3 页获取到 100 条记录
2025-04-29 18:00:10,975 - INFO - Request Parameters - Page 4:
2025-04-29 18:00:10,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:10,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:11,443 - INFO - Response - Page 4:
2025-04-29 18:00:11,646 - INFO - 第 4 页获取到 100 条记录
2025-04-29 18:00:11,646 - INFO - Request Parameters - Page 5:
2025-04-29 18:00:11,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:11,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:12,115 - INFO - Response - Page 5:
2025-04-29 18:00:12,318 - INFO - 第 5 页获取到 100 条记录
2025-04-29 18:00:12,318 - INFO - Request Parameters - Page 6:
2025-04-29 18:00:12,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:12,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:12,787 - INFO - Response - Page 6:
2025-04-29 18:00:12,990 - INFO - 第 6 页获取到 100 条记录
2025-04-29 18:00:12,990 - INFO - Request Parameters - Page 7:
2025-04-29 18:00:12,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:12,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:13,443 - INFO - Response - Page 7:
2025-04-29 18:00:13,646 - INFO - 第 7 页获取到 70 条记录
2025-04-29 18:00:13,646 - INFO - 查询完成，共获取到 670 条记录
2025-04-29 18:00:13,646 - INFO - 获取到 670 条表单数据
2025-04-29 18:00:13,646 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-29 18:00:13,662 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 18:00:13,662 - INFO - 开始处理日期: 2025-03
2025-04-29 18:00:13,662 - INFO - Request Parameters - Page 1:
2025-04-29 18:00:13,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:13,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:14,115 - INFO - Response - Page 1:
2025-04-29 18:00:14,318 - INFO - 第 1 页获取到 100 条记录
2025-04-29 18:00:14,318 - INFO - Request Parameters - Page 2:
2025-04-29 18:00:14,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:14,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:14,787 - INFO - Response - Page 2:
2025-04-29 18:00:14,990 - INFO - 第 2 页获取到 100 条记录
2025-04-29 18:00:14,990 - INFO - Request Parameters - Page 3:
2025-04-29 18:00:14,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:14,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:15,490 - INFO - Response - Page 3:
2025-04-29 18:00:15,693 - INFO - 第 3 页获取到 100 条记录
2025-04-29 18:00:15,693 - INFO - Request Parameters - Page 4:
2025-04-29 18:00:15,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:15,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:16,162 - INFO - Response - Page 4:
2025-04-29 18:00:16,365 - INFO - 第 4 页获取到 100 条记录
2025-04-29 18:00:16,365 - INFO - Request Parameters - Page 5:
2025-04-29 18:00:16,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:16,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:16,881 - INFO - Response - Page 5:
2025-04-29 18:00:17,084 - INFO - 第 5 页获取到 100 条记录
2025-04-29 18:00:17,084 - INFO - Request Parameters - Page 6:
2025-04-29 18:00:17,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:17,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:17,553 - INFO - Response - Page 6:
2025-04-29 18:00:17,756 - INFO - 第 6 页获取到 100 条记录
2025-04-29 18:00:17,756 - INFO - Request Parameters - Page 7:
2025-04-29 18:00:17,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:17,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:18,178 - INFO - Response - Page 7:
2025-04-29 18:00:18,381 - INFO - 第 7 页获取到 61 条记录
2025-04-29 18:00:18,381 - INFO - 查询完成，共获取到 661 条记录
2025-04-29 18:00:18,381 - INFO - 获取到 661 条表单数据
2025-04-29 18:00:18,381 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-29 18:00:18,396 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 18:00:18,396 - INFO - 开始处理日期: 2025-04
2025-04-29 18:00:18,396 - INFO - Request Parameters - Page 1:
2025-04-29 18:00:18,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:18,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:18,912 - INFO - Response - Page 1:
2025-04-29 18:00:19,115 - INFO - 第 1 页获取到 100 条记录
2025-04-29 18:00:19,115 - INFO - Request Parameters - Page 2:
2025-04-29 18:00:19,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:19,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:19,662 - INFO - Response - Page 2:
2025-04-29 18:00:19,865 - INFO - 第 2 页获取到 100 条记录
2025-04-29 18:00:19,865 - INFO - Request Parameters - Page 3:
2025-04-29 18:00:19,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:19,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:20,396 - INFO - Response - Page 3:
2025-04-29 18:00:20,600 - INFO - 第 3 页获取到 100 条记录
2025-04-29 18:00:20,600 - INFO - Request Parameters - Page 4:
2025-04-29 18:00:20,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:20,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:21,084 - INFO - Response - Page 4:
2025-04-29 18:00:21,287 - INFO - 第 4 页获取到 100 条记录
2025-04-29 18:00:21,287 - INFO - Request Parameters - Page 5:
2025-04-29 18:00:21,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:21,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:21,771 - INFO - Response - Page 5:
2025-04-29 18:00:21,974 - INFO - 第 5 页获取到 100 条记录
2025-04-29 18:00:21,974 - INFO - Request Parameters - Page 6:
2025-04-29 18:00:21,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:21,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:22,428 - INFO - Response - Page 6:
2025-04-29 18:00:22,631 - INFO - 第 6 页获取到 100 条记录
2025-04-29 18:00:22,631 - INFO - Request Parameters - Page 7:
2025-04-29 18:00:22,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:00:22,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:00:22,974 - INFO - Response - Page 7:
2025-04-29 18:00:23,178 - INFO - 第 7 页获取到 27 条记录
2025-04-29 18:00:23,178 - INFO - 查询完成，共获取到 627 条记录
2025-04-29 18:00:23,178 - INFO - 获取到 627 条表单数据
2025-04-29 18:00:23,178 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-29 18:00:23,178 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MA3
2025-04-29 18:00:23,646 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MA3
2025-04-29 18:00:23,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29612.7, 'new_value': 30244.5}, {'field': 'total_amount', 'old_value': 29612.7, 'new_value': 30244.5}, {'field': 'order_count', 'old_value': 234, 'new_value': 239}]
2025-04-29 18:00:23,646 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MM3
2025-04-29 18:00:24,146 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MM3
2025-04-29 18:00:24,146 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6473.73, 'new_value': 7210.73}, {'field': 'offline_amount', 'old_value': 25527.41, 'new_value': 26264.41}, {'field': 'total_amount', 'old_value': 32001.14, 'new_value': 33475.14}, {'field': 'order_count', 'old_value': 4513, 'new_value': 5250}]
2025-04-29 18:00:24,146 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNA
2025-04-29 18:00:24,678 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNA
2025-04-29 18:00:24,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 213883.0, 'new_value': 216883.0}, {'field': 'total_amount', 'old_value': 217198.0, 'new_value': 220198.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-04-29 18:00:24,693 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB7
2025-04-29 18:00:25,162 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB7
2025-04-29 18:00:25,162 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42987.29, 'new_value': 44393.7}, {'field': 'total_amount', 'old_value': 42987.29, 'new_value': 44393.7}, {'field': 'order_count', 'old_value': 1992, 'new_value': 2060}]
2025-04-29 18:00:25,178 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-04-29 18:00:25,646 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-04-29 18:00:25,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36838.25, 'new_value': 37161.4}, {'field': 'total_amount', 'old_value': 36838.25, 'new_value': 37161.4}, {'field': 'order_count', 'old_value': 44, 'new_value': 26}]
2025-04-29 18:00:25,646 - INFO - 日期 2025-04 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-04-29 18:00:25,662 - INFO - 数据同步完成！更新: 5 条，插入: 0 条，错误: 0 条
2025-04-29 18:00:25,662 - INFO - =================同步完成====================
2025-04-29 21:00:02,013 - INFO - =================使用默认全量同步=============
2025-04-29 21:00:03,154 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-29 21:00:03,154 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-29 21:00:03,170 - INFO - 开始处理日期: 2025-01
2025-04-29 21:00:03,170 - INFO - Request Parameters - Page 1:
2025-04-29 21:00:03,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:03,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:03,998 - INFO - Response - Page 1:
2025-04-29 21:00:04,201 - INFO - 第 1 页获取到 100 条记录
2025-04-29 21:00:04,201 - INFO - Request Parameters - Page 2:
2025-04-29 21:00:04,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:04,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:05,185 - INFO - Response - Page 2:
2025-04-29 21:00:05,388 - INFO - 第 2 页获取到 100 条记录
2025-04-29 21:00:05,388 - INFO - Request Parameters - Page 3:
2025-04-29 21:00:05,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:05,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:05,857 - INFO - Response - Page 3:
2025-04-29 21:00:06,060 - INFO - 第 3 页获取到 100 条记录
2025-04-29 21:00:06,060 - INFO - Request Parameters - Page 4:
2025-04-29 21:00:06,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:06,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:06,592 - INFO - Response - Page 4:
2025-04-29 21:00:06,795 - INFO - 第 4 页获取到 100 条记录
2025-04-29 21:00:06,795 - INFO - Request Parameters - Page 5:
2025-04-29 21:00:06,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:06,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:07,295 - INFO - Response - Page 5:
2025-04-29 21:00:07,498 - INFO - 第 5 页获取到 100 条记录
2025-04-29 21:00:07,498 - INFO - Request Parameters - Page 6:
2025-04-29 21:00:07,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:07,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:08,029 - INFO - Response - Page 6:
2025-04-29 21:00:08,232 - INFO - 第 6 页获取到 100 条记录
2025-04-29 21:00:08,232 - INFO - Request Parameters - Page 7:
2025-04-29 21:00:08,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:08,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:08,826 - INFO - Response - Page 7:
2025-04-29 21:00:09,029 - INFO - 第 7 页获取到 82 条记录
2025-04-29 21:00:09,029 - INFO - 查询完成，共获取到 682 条记录
2025-04-29 21:00:09,029 - INFO - 获取到 682 条表单数据
2025-04-29 21:00:09,029 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-29 21:00:09,045 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 21:00:09,045 - INFO - 开始处理日期: 2025-02
2025-04-29 21:00:09,045 - INFO - Request Parameters - Page 1:
2025-04-29 21:00:09,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:09,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:09,560 - INFO - Response - Page 1:
2025-04-29 21:00:09,763 - INFO - 第 1 页获取到 100 条记录
2025-04-29 21:00:09,763 - INFO - Request Parameters - Page 2:
2025-04-29 21:00:09,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:09,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:10,295 - INFO - Response - Page 2:
2025-04-29 21:00:10,498 - INFO - 第 2 页获取到 100 条记录
2025-04-29 21:00:10,498 - INFO - Request Parameters - Page 3:
2025-04-29 21:00:10,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:10,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:10,967 - INFO - Response - Page 3:
2025-04-29 21:00:11,170 - INFO - 第 3 页获取到 100 条记录
2025-04-29 21:00:11,170 - INFO - Request Parameters - Page 4:
2025-04-29 21:00:11,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:11,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:11,654 - INFO - Response - Page 4:
2025-04-29 21:00:11,857 - INFO - 第 4 页获取到 100 条记录
2025-04-29 21:00:11,857 - INFO - Request Parameters - Page 5:
2025-04-29 21:00:11,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:11,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:12,388 - INFO - Response - Page 5:
2025-04-29 21:00:12,591 - INFO - 第 5 页获取到 100 条记录
2025-04-29 21:00:12,591 - INFO - Request Parameters - Page 6:
2025-04-29 21:00:12,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:12,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:13,045 - INFO - Response - Page 6:
2025-04-29 21:00:13,248 - INFO - 第 6 页获取到 100 条记录
2025-04-29 21:00:13,248 - INFO - Request Parameters - Page 7:
2025-04-29 21:00:13,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:13,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:13,701 - INFO - Response - Page 7:
2025-04-29 21:00:13,904 - INFO - 第 7 页获取到 70 条记录
2025-04-29 21:00:13,904 - INFO - 查询完成，共获取到 670 条记录
2025-04-29 21:00:13,904 - INFO - 获取到 670 条表单数据
2025-04-29 21:00:13,904 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-29 21:00:13,920 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 21:00:13,920 - INFO - 开始处理日期: 2025-03
2025-04-29 21:00:13,920 - INFO - Request Parameters - Page 1:
2025-04-29 21:00:13,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:13,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:14,420 - INFO - Response - Page 1:
2025-04-29 21:00:14,623 - INFO - 第 1 页获取到 100 条记录
2025-04-29 21:00:14,623 - INFO - Request Parameters - Page 2:
2025-04-29 21:00:14,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:14,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:15,170 - INFO - Response - Page 2:
2025-04-29 21:00:15,373 - INFO - 第 2 页获取到 100 条记录
2025-04-29 21:00:15,373 - INFO - Request Parameters - Page 3:
2025-04-29 21:00:15,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:15,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:15,810 - INFO - Response - Page 3:
2025-04-29 21:00:16,013 - INFO - 第 3 页获取到 100 条记录
2025-04-29 21:00:16,013 - INFO - Request Parameters - Page 4:
2025-04-29 21:00:16,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:16,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:16,482 - INFO - Response - Page 4:
2025-04-29 21:00:16,685 - INFO - 第 4 页获取到 100 条记录
2025-04-29 21:00:16,685 - INFO - Request Parameters - Page 5:
2025-04-29 21:00:16,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:16,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:17,123 - INFO - Response - Page 5:
2025-04-29 21:00:17,326 - INFO - 第 5 页获取到 100 条记录
2025-04-29 21:00:17,326 - INFO - Request Parameters - Page 6:
2025-04-29 21:00:17,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:17,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:17,763 - INFO - Response - Page 6:
2025-04-29 21:00:17,966 - INFO - 第 6 页获取到 100 条记录
2025-04-29 21:00:17,966 - INFO - Request Parameters - Page 7:
2025-04-29 21:00:17,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:17,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:18,326 - INFO - Response - Page 7:
2025-04-29 21:00:18,529 - INFO - 第 7 页获取到 61 条记录
2025-04-29 21:00:18,529 - INFO - 查询完成，共获取到 661 条记录
2025-04-29 21:00:18,529 - INFO - 获取到 661 条表单数据
2025-04-29 21:00:18,529 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-29 21:00:18,545 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-29 21:00:18,545 - INFO - 开始处理日期: 2025-04
2025-04-29 21:00:18,545 - INFO - Request Parameters - Page 1:
2025-04-29 21:00:18,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:18,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:19,091 - INFO - Response - Page 1:
2025-04-29 21:00:19,295 - INFO - 第 1 页获取到 100 条记录
2025-04-29 21:00:19,295 - INFO - Request Parameters - Page 2:
2025-04-29 21:00:19,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:19,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:19,795 - INFO - Response - Page 2:
2025-04-29 21:00:19,998 - INFO - 第 2 页获取到 100 条记录
2025-04-29 21:00:19,998 - INFO - Request Parameters - Page 3:
2025-04-29 21:00:19,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:19,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:20,451 - INFO - Response - Page 3:
2025-04-29 21:00:20,654 - INFO - 第 3 页获取到 100 条记录
2025-04-29 21:00:20,654 - INFO - Request Parameters - Page 4:
2025-04-29 21:00:20,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:20,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:21,138 - INFO - Response - Page 4:
2025-04-29 21:00:21,341 - INFO - 第 4 页获取到 100 条记录
2025-04-29 21:00:21,341 - INFO - Request Parameters - Page 5:
2025-04-29 21:00:21,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:21,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:21,826 - INFO - Response - Page 5:
2025-04-29 21:00:22,029 - INFO - 第 5 页获取到 100 条记录
2025-04-29 21:00:22,029 - INFO - Request Parameters - Page 6:
2025-04-29 21:00:22,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:22,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:22,482 - INFO - Response - Page 6:
2025-04-29 21:00:22,685 - INFO - 第 6 页获取到 100 条记录
2025-04-29 21:00:22,685 - INFO - Request Parameters - Page 7:
2025-04-29 21:00:22,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:00:22,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:00:23,045 - INFO - Response - Page 7:
2025-04-29 21:00:23,248 - INFO - 第 7 页获取到 27 条记录
2025-04-29 21:00:23,248 - INFO - 查询完成，共获取到 627 条记录
2025-04-29 21:00:23,248 - INFO - 获取到 627 条表单数据
2025-04-29 21:00:23,248 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-29 21:00:23,248 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MF3
2025-04-29 21:00:23,685 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MF3
2025-04-29 21:00:23,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24246.95, 'new_value': 26876.95}, {'field': 'total_amount', 'old_value': 24246.95, 'new_value': 26876.95}, {'field': 'order_count', 'old_value': 102, 'new_value': 105}]
2025-04-29 21:00:23,685 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M15
2025-04-29 21:00:24,060 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M15
2025-04-29 21:00:24,060 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30282.41, 'new_value': 36532.11}, {'field': 'total_amount', 'old_value': 114204.11, 'new_value': 120453.81}, {'field': 'order_count', 'old_value': 6021, 'new_value': 6357}]
2025-04-29 21:00:24,060 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ5
2025-04-29 21:00:24,513 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ5
2025-04-29 21:00:24,513 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35797.0, 'new_value': 37063.0}, {'field': 'total_amount', 'old_value': 35797.0, 'new_value': 37063.0}, {'field': 'order_count', 'old_value': 197, 'new_value': 203}]
2025-04-29 21:00:24,513 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME6
2025-04-29 21:00:25,013 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME6
2025-04-29 21:00:25,013 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158895.9, 'new_value': 161934.9}, {'field': 'total_amount', 'old_value': 158895.9, 'new_value': 161934.9}, {'field': 'order_count', 'old_value': 54, 'new_value': 55}]
2025-04-29 21:00:25,013 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8I
2025-04-29 21:00:25,420 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8I
2025-04-29 21:00:25,420 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146543.03, 'new_value': 151171.49}, {'field': 'total_amount', 'old_value': 146543.03, 'new_value': 151171.49}, {'field': 'order_count', 'old_value': 5679, 'new_value': 5865}]
2025-04-29 21:00:25,420 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV6
2025-04-29 21:00:25,795 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV6
2025-04-29 21:00:25,795 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89866.0, 'new_value': 92948.94}, {'field': 'total_amount', 'old_value': 89866.0, 'new_value': 92948.94}, {'field': 'order_count', 'old_value': 3674, 'new_value': 3816}]
2025-04-29 21:00:25,795 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M77
2025-04-29 21:00:26,232 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M77
2025-04-29 21:00:26,232 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31049.0, 'new_value': 32607.0}, {'field': 'total_amount', 'old_value': 31049.0, 'new_value': 32607.0}, {'field': 'order_count', 'old_value': 2907, 'new_value': 3059}]
2025-04-29 21:00:26,232 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP7
2025-04-29 21:00:26,716 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP7
2025-04-29 21:00:26,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102015.0, 'new_value': 103253.0}, {'field': 'total_amount', 'old_value': 102015.0, 'new_value': 103253.0}, {'field': 'order_count', 'old_value': 683, 'new_value': 703}]
2025-04-29 21:00:26,716 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M08
2025-04-29 21:00:27,154 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M08
2025-04-29 21:00:27,154 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 433299.32, 'new_value': 446925.32}, {'field': 'total_amount', 'old_value': 433299.32, 'new_value': 446925.32}, {'field': 'order_count', 'old_value': 79, 'new_value': 81}]
2025-04-29 21:00:27,154 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M28
2025-04-29 21:00:27,545 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M28
2025-04-29 21:00:27,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32187.68, 'new_value': 33562.68}, {'field': 'total_amount', 'old_value': 32187.68, 'new_value': 33562.68}, {'field': 'order_count', 'old_value': 2819, 'new_value': 2925}]
2025-04-29 21:00:27,545 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M38
2025-04-29 21:00:28,013 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M38
2025-04-29 21:00:28,013 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280954.15, 'new_value': 294547.96}, {'field': 'total_amount', 'old_value': 283978.15, 'new_value': 297571.96}, {'field': 'order_count', 'old_value': 593, 'new_value': 616}]
2025-04-29 21:00:28,013 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M58
2025-04-29 21:00:28,451 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M58
2025-04-29 21:00:28,451 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24678.8, 'new_value': 24943.8}, {'field': 'total_amount', 'old_value': 24678.8, 'new_value': 24943.8}, {'field': 'order_count', 'old_value': 715, 'new_value': 721}]
2025-04-29 21:00:28,451 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M78
2025-04-29 21:00:28,904 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M78
2025-04-29 21:00:28,904 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47913.4, 'new_value': 49543.0}, {'field': 'total_amount', 'old_value': 47913.4, 'new_value': 49543.0}, {'field': 'order_count', 'old_value': 527, 'new_value': 555}]
2025-04-29 21:00:28,904 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M88
2025-04-29 21:00:29,388 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M88
2025-04-29 21:00:29,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124767.0, 'new_value': 130542.0}, {'field': 'total_amount', 'old_value': 124767.0, 'new_value': 130542.0}, {'field': 'order_count', 'old_value': 4497, 'new_value': 4612}]
2025-04-29 21:00:29,388 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-04-29 21:00:29,857 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-04-29 21:00:29,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173677.69, 'new_value': 177091.25}, {'field': 'total_amount', 'old_value': 180072.69, 'new_value': 183486.25}, {'field': 'order_count', 'old_value': 1046, 'new_value': 1073}]
2025-04-29 21:00:29,857 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI8
2025-04-29 21:00:30,263 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI8
2025-04-29 21:00:30,263 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6304815.0, 'new_value': 6527974.0}, {'field': 'total_amount', 'old_value': 6304815.0, 'new_value': 6527974.0}, {'field': 'order_count', 'old_value': 111739, 'new_value': 115621}]
2025-04-29 21:00:30,263 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML8
2025-04-29 21:00:30,795 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML8
2025-04-29 21:00:30,795 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160182.77, 'new_value': 165237.91}, {'field': 'total_amount', 'old_value': 160182.77, 'new_value': 165237.91}, {'field': 'order_count', 'old_value': 14046, 'new_value': 14474}]
2025-04-29 21:00:30,795 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M42
2025-04-29 21:00:31,201 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M42
2025-04-29 21:00:31,201 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12636.33, 'new_value': 12843.33}, {'field': 'total_amount', 'old_value': 12636.33, 'new_value': 12843.33}, {'field': 'order_count', 'old_value': 340, 'new_value': 347}]
2025-04-29 21:00:31,201 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9ME2
2025-04-29 21:00:31,779 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9ME2
2025-04-29 21:00:31,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42585.7, 'new_value': 44934.7}, {'field': 'total_amount', 'old_value': 42585.7, 'new_value': 44934.7}, {'field': 'order_count', 'old_value': 483, 'new_value': 499}]
2025-04-29 21:00:31,779 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381ZKVUYUYGD0EVH8PCR91L20Q91PX9MY3
2025-04-29 21:00:32,216 - INFO - 更新表单数据成功: FINST-OJ966381ZKVUYUYGD0EVH8PCR91L20Q91PX9MY3
2025-04-29 21:00:32,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1089000.0, 'new_value': 1269700.0}, {'field': 'total_amount', 'old_value': 1089000.0, 'new_value': 1269700.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-04-29 21:00:32,216 - INFO - 日期 2025-04 处理完成 - 更新: 20 条，插入: 0 条，错误: 0 条
2025-04-29 21:00:32,216 - INFO - 数据同步完成！更新: 20 条，插入: 0 条，错误: 0 条
2025-04-29 21:00:32,216 - INFO - =================同步完成====================
