2025-06-09 08:00:03,322 - INFO - ==================================================
2025-06-09 08:00:03,322 - INFO - 程序启动 - 版本 v1.0.0
2025-06-09 08:00:03,322 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250609.log
2025-06-09 08:00:03,322 - INFO - ==================================================
2025-06-09 08:00:03,322 - INFO - 程序入口点: __main__
2025-06-09 08:00:03,322 - INFO - ==================================================
2025-06-09 08:00:03,322 - INFO - 程序启动 - 版本 v1.0.1
2025-06-09 08:00:03,322 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250609.log
2025-06-09 08:00:03,322 - INFO - ==================================================
2025-06-09 08:00:03,338 - INFO - MySQL数据库连接成功
2025-06-09 08:00:03,619 - INFO - MySQL数据库连接成功
2025-06-09 08:00:03,619 - INFO - sales_data表已存在，无需创建
2025-06-09 08:00:03,619 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-06-09 08:00:03,619 - INFO - DataSyncManager初始化完成
2025-06-09 08:00:03,619 - INFO - 开始更新店铺映射表...
2025-06-09 08:00:03,619 - INFO - 开始获取最近7天的店铺信息，时间范围: 2025-06-02 至 2025-06-08
2025-06-09 08:00:03,619 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:03,619 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:03,619 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '85CDD3E71D6F6DD0049B404167763402'}
2025-06-09 08:00:04,838 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:04,838 - INFO - 日期 ******** 获取到 328 条店铺记录
2025-06-09 08:00:05,353 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:05,353 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:05,353 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2FEAC9CE98EBB38F81CC7D30F853A065'}
2025-06-09 08:00:06,135 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:06,135 - INFO - 日期 ******** 获取到 328 条店铺记录
2025-06-09 08:00:06,650 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:06,650 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:06,650 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '508D7EC1772033EA02C5B2D989CB057D'}
2025-06-09 08:00:07,400 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:07,400 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-09 08:00:07,900 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:07,900 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:07,900 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '46D3787A9398D661272A710560FF1310'}
2025-06-09 08:00:08,619 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:08,619 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-09 08:00:09,135 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:09,135 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:09,135 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DEF97B584C61763391A16A941DCEC912'}
2025-06-09 08:00:09,822 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:09,822 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-09 08:00:10,338 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:10,338 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:10,338 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F59D662E6E46EB26F62A6F27403F1A0C'}
2025-06-09 08:00:11,072 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:11,072 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-09 08:00:11,588 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:11,588 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:11,588 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5C34A83BDE4BFB771558ADFED12CA221'}
2025-06-09 08:00:12,244 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:12,244 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-09 08:00:12,947 - INFO - 店铺映射表更新完成，总计: 328条，成功: 328条 (更新: 328条, 插入: 0条)
2025-06-09 08:00:12,947 - INFO - 店铺映射表更新完成
2025-06-09 08:00:12,947 - INFO - 未提供日期参数，使用默认值
2025-06-09 08:00:12,947 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-06-09 08:00:12,947 - INFO - 开始综合数据同步流程...
2025-06-09 08:00:12,947 - INFO - 当前错误日期列表为空
2025-06-09 08:00:12,947 - INFO - 正在获取数衍平台日销售数据...
2025-06-09 08:00:12,947 - INFO - 开始获取最近7天的店铺信息，时间范围: 2025-06-02 至 2025-06-08
2025-06-09 08:00:12,947 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:12,947 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:12,947 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '876EE496FD273FDDA479B98917D3B92B'}
2025-06-09 08:00:13,791 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:13,791 - INFO - 日期 ******** 获取到 328 条店铺记录
2025-06-09 08:00:14,291 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:14,291 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:14,291 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7684424841D7DECAF02EDC82E500AEF5'}
2025-06-09 08:00:14,963 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:14,963 - INFO - 日期 ******** 获取到 328 条店铺记录
2025-06-09 08:00:15,463 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:15,463 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:15,463 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F8E1B5ED173E2A3BB3BDCF2CFC48E763'}
2025-06-09 08:00:16,635 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:16,635 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-09 08:00:17,135 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:17,135 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:17,135 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '98035BB71072169BB654F1A972C42724'}
2025-06-09 08:00:17,681 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:17,681 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-09 08:00:18,181 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:18,181 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:18,181 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A00AD3538B4F6A2F7B28D5D958BE60E0'}
2025-06-09 08:00:18,806 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:18,806 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-09 08:00:19,322 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:19,322 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:19,322 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DBB78D3BBAAD267194B06228F8964F3C'}
2025-06-09 08:00:20,010 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:20,010 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-09 08:00:20,525 - INFO - 查询日期 ******** 的店铺信息
2025-06-09 08:00:20,525 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:20,525 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-06-09 08:00:21,275 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:21,275 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-09 08:00:21,994 - INFO - 店铺映射表更新完成，总计: 328条，成功: 328条 (更新: 328条, 插入: 0条)
2025-06-09 08:00:21,994 - INFO - 查询数衍平台数据，时间段为: 2025-04-09, 2025-06-08
2025-06-09 08:00:21,994 - INFO - 正在获取********至********的数据
2025-06-09 08:00:21,994 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:21,994 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '632D92746FB4F5C4213892945E0510E4'}
2025-06-09 08:00:23,369 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:23,385 - INFO - 过滤后保留 423 条记录
2025-06-09 08:00:25,400 - INFO - 正在获取********至********的数据
2025-06-09 08:00:25,400 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:25,400 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9499EB24E82D6A51CE5A3F6A4DE5F4A9'}
2025-06-09 08:00:26,650 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:26,650 - INFO - 过滤后保留 432 条记录
2025-06-09 08:00:28,666 - INFO - 正在获取********至********的数据
2025-06-09 08:00:28,666 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:28,666 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '28E569ADCD96CDC1B77AABDF92B45DB8'}
2025-06-09 08:00:29,525 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:29,525 - INFO - 过滤后保留 434 条记录
2025-06-09 08:00:31,541 - INFO - 正在获取********至********的数据
2025-06-09 08:00:31,541 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:31,541 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A8AF937B1BBF2B20F5AAFA64A04E130A'}
2025-06-09 08:00:32,353 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:32,369 - INFO - 过滤后保留 424 条记录
2025-06-09 08:00:34,384 - INFO - 正在获取********至********的数据
2025-06-09 08:00:34,384 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:34,384 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4D00C283D4C723500FE2CA7401279037'}
2025-06-09 08:00:35,306 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:35,306 - INFO - 过滤后保留 436 条记录
2025-06-09 08:00:37,338 - INFO - 正在获取********至********的数据
2025-06-09 08:00:37,338 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:37,338 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '84E66BEED3C14D8522D4EF4E0E7BE318'}
2025-06-09 08:00:38,259 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:38,275 - INFO - 过滤后保留 431 条记录
2025-06-09 08:00:40,291 - INFO - 正在获取********至********的数据
2025-06-09 08:00:40,291 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:40,291 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7AE86028C17E3BCF6645ED74FD5D2130'}
2025-06-09 08:00:41,134 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:41,134 - INFO - 过滤后保留 425 条记录
2025-06-09 08:00:43,134 - INFO - 正在获取********至********的数据
2025-06-09 08:00:43,134 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:43,134 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '410D0A21BA24D481FA45FD0371A853A3'}
2025-06-09 08:00:43,947 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:43,963 - INFO - 过滤后保留 414 条记录
2025-06-09 08:00:45,978 - INFO - 正在获取********至********的数据
2025-06-09 08:00:45,978 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:45,978 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1B761AA75C9D609B6201425BF81F0767'}
2025-06-09 08:00:46,853 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:46,853 - INFO - 过滤后保留 427 条记录
2025-06-09 08:00:48,869 - INFO - 正在获取********至********的数据
2025-06-09 08:00:48,869 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:48,869 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0A95CD55CC01D87304EF8C24F539C119'}
2025-06-09 08:00:49,697 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:49,697 - INFO - 过滤后保留 428 条记录
2025-06-09 08:00:51,712 - INFO - 正在获取********至********的数据
2025-06-09 08:00:51,712 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:51,712 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5A081AEB864846816C07B4E302074831'}
2025-06-09 08:00:52,509 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:52,509 - INFO - 过滤后保留 429 条记录
2025-06-09 08:00:54,525 - INFO - 正在获取********至********的数据
2025-06-09 08:00:54,525 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:54,525 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DC9C82E8C207301A5B15EFD25E044712'}
2025-06-09 08:00:55,384 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:55,384 - INFO - 过滤后保留 427 条记录
2025-06-09 08:00:57,384 - INFO - 正在获取********至********的数据
2025-06-09 08:00:57,384 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:00:57,384 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '70DB8B6E0C8A84CE3F2E9052C8A03DB5'}
2025-06-09 08:00:58,228 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:00:58,244 - INFO - 过滤后保留 428 条记录
2025-06-09 08:01:00,259 - INFO - 正在获取********至********的数据
2025-06-09 08:01:00,259 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:00,259 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2F836313D93B58E278595A1B4549EC91'}
2025-06-09 08:01:01,041 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:01,041 - INFO - 过滤后保留 417 条记录
2025-06-09 08:01:03,056 - INFO - 正在获取********至********的数据
2025-06-09 08:01:03,056 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:03,056 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5CD8364F6B3A69F6AA378F15103C99B2'}
2025-06-09 08:01:03,806 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:03,806 - INFO - 过滤后保留 415 条记录
2025-06-09 08:01:05,822 - INFO - 正在获取********至********的数据
2025-06-09 08:01:05,822 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:05,822 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1FAA60DAE425EDF0D1EBB5CFCE72C647'}
2025-06-09 08:01:06,587 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:06,587 - INFO - 过滤后保留 433 条记录
2025-06-09 08:01:08,603 - INFO - 正在获取********至********的数据
2025-06-09 08:01:08,603 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:08,603 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7D4C9916062245BCF01C422D1A1292B7'}
2025-06-09 08:01:09,400 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:09,400 - INFO - 过滤后保留 433 条记录
2025-06-09 08:01:11,415 - INFO - 正在获取********至********的数据
2025-06-09 08:01:11,415 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:11,415 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8EA91F9172166EA94527DF2BF4662F78'}
2025-06-09 08:01:12,165 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:12,165 - INFO - 过滤后保留 417 条记录
2025-06-09 08:01:14,181 - INFO - 正在获取********至********的数据
2025-06-09 08:01:14,181 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:14,181 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '12671581275D702F15265B1BBFFAF0B4'}
2025-06-09 08:01:14,962 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:14,978 - INFO - 过滤后保留 420 条记录
2025-06-09 08:01:16,994 - INFO - 正在获取********至********的数据
2025-06-09 08:01:16,994 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:16,994 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '84E30E103FF5160BD3F96798F827A8CC'}
2025-06-09 08:01:17,712 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:17,712 - INFO - 过滤后保留 431 条记录
2025-06-09 08:01:19,728 - INFO - 正在获取********至********的数据
2025-06-09 08:01:19,728 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:19,728 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2BC59E3A88B34FCA14658688C17C300C'}
2025-06-09 08:01:20,509 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:20,525 - INFO - 过滤后保留 423 条记录
2025-06-09 08:01:22,540 - INFO - 正在获取********至********的数据
2025-06-09 08:01:22,540 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:22,540 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'ABECEA12052991D6DB2D64DF9CAC086C'}
2025-06-09 08:01:23,259 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:23,259 - INFO - 过滤后保留 416 条记录
2025-06-09 08:01:25,275 - INFO - 正在获取********至********的数据
2025-06-09 08:01:25,275 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:25,275 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5B98DF3BBBA4D4CB1C4B84EED8CF7C6C'}
2025-06-09 08:01:26,072 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:26,087 - INFO - 过滤后保留 423 条记录
2025-06-09 08:01:28,103 - INFO - 正在获取********至********的数据
2025-06-09 08:01:28,103 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:28,103 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '610738A1D1C878ECF2FDB7AEC7A11EC1'}
2025-06-09 08:01:28,822 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:28,822 - INFO - 过滤后保留 414 条记录
2025-06-09 08:01:30,837 - INFO - 正在获取********至********的数据
2025-06-09 08:01:30,837 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:30,837 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6F6ACA6B488ED280BEDA670DAB250C5E'}
2025-06-09 08:01:31,556 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:31,556 - INFO - 过滤后保留 413 条记录
2025-06-09 08:01:33,572 - INFO - 正在获取********至********的数据
2025-06-09 08:01:33,572 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:33,572 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '927B0E1B1B87E58CF6162F9655906B0F'}
2025-06-09 08:01:34,275 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:34,290 - INFO - 过滤后保留 414 条记录
2025-06-09 08:01:36,306 - INFO - 正在获取********至********的数据
2025-06-09 08:01:36,306 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:36,306 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9D0EAD8D702DB248C98FCD4680113A7E'}
2025-06-09 08:01:36,993 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:36,993 - INFO - 过滤后保留 415 条记录
2025-06-09 08:01:38,993 - INFO - 正在获取********至********的数据
2025-06-09 08:01:38,993 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:38,993 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '04FBA8382C2756C73D5D0E2F27FCD16E'}
2025-06-09 08:01:39,697 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:39,697 - INFO - 过滤后保留 400 条记录
2025-06-09 08:01:41,712 - INFO - 正在获取********至********的数据
2025-06-09 08:01:41,712 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:41,712 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '10C3E3D32BFB3BC099923731A434AA91'}
2025-06-09 08:01:42,431 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:42,447 - INFO - 过滤后保留 398 条记录
2025-06-09 08:01:44,447 - INFO - 正在获取********至********的数据
2025-06-09 08:01:44,447 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:44,447 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '63C29714C40057EC286759987DE81F56'}
2025-06-09 08:01:45,072 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:45,087 - INFO - 过滤后保留 406 条记录
2025-06-09 08:01:47,103 - INFO - 正在获取********至********的数据
2025-06-09 08:01:47,103 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-09 08:01:47,103 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C15ED650D3301378AC1A027BD16E6636'}
2025-06-09 08:01:47,650 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-09 08:01:47,650 - INFO - 过滤后保留 198 条记录
2025-06-09 08:01:49,665 - INFO - 开始保存数据到MySQL数据库，共 12844 条记录待处理
2025-06-09 08:01:51,931 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ08R852VK83IKSIOCDI7KE001FRF, sale_time=2025-04-20
2025-06-09 08:01:51,931 - INFO - 变更字段: amount: 8098 -> 8097
2025-06-09 08:01:53,368 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q, sale_time=2025-04-27
2025-06-09 08:01:53,368 - INFO - 变更字段: amount: 857 -> 856
2025-06-09 08:01:53,587 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-04-29
2025-06-09 08:01:53,587 - INFO - 变更字段: amount: 234 -> 233
2025-06-09 08:01:59,603 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-31
2025-06-09 08:01:59,603 - INFO - 变更字段: amount: 3958 -> 3957
2025-06-09 08:02:00,571 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-06-07
2025-06-09 08:02:00,571 - INFO - 变更字段: recommend_amount: 0.0 -> 8236.5, daily_bill_amount: 0.0 -> 8236.5
2025-06-09 08:02:00,665 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-06-07
2025-06-09 08:02:00,665 - INFO - 变更字段: recommend_amount: 0.0 -> 5916.82, daily_bill_amount: 0.0 -> 5916.82
2025-06-09 08:02:00,696 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEO8LGKTTH42F6DB81RH8V001P57, sale_time=2025-06-07
2025-06-09 08:02:00,696 - INFO - 变更字段: recommend_amount: 1789.55 -> 1799.97, amount: 1789 -> 1799, instore_amount: 1822.88 -> 1833.3
2025-06-09 08:02:00,743 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-06-07
2025-06-09 08:02:00,743 - INFO - 变更字段: recommend_amount: 0.0 -> 34386.75, daily_bill_amount: 0.0 -> 34386.75
2025-06-09 08:02:00,790 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-06-07
2025-06-09 08:02:00,790 - INFO - 变更字段: amount: 3404 -> 3487, count: 37 -> 38, instore_amount: 3058.69 -> 3142.29, instore_count: 31 -> 32
2025-06-09 08:02:01,181 - INFO - MySQL数据保存完成，统计信息：
2025-06-09 08:02:01,181 - INFO - - 总记录数: 12844
2025-06-09 08:02:01,181 - INFO - - 成功插入: 199
2025-06-09 08:02:01,181 - INFO - - 成功更新: 9
2025-06-09 08:02:01,181 - INFO - - 无需更新: 12636
2025-06-09 08:02:01,181 - INFO - - 处理失败: 0
2025-06-09 08:02:01,181 - INFO - 成功获取数衍平台数据，共 12844 条记录
2025-06-09 08:02:01,181 - INFO - 正在更新MySQL月度汇总数据...
2025-06-09 08:02:01,228 - INFO - 月度数据表清空完成
2025-06-09 08:02:01,571 - INFO - 月度汇总数据更新完成，处理了 1403 条汇总记录
2025-06-09 08:02:01,571 - INFO - 成功更新月度汇总数据，共 1403 条记录
2025-06-09 08:02:01,571 - INFO - 正在获取宜搭日销售表单数据...
2025-06-09 08:02:01,571 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-04-09 00:00:00 至 2025-06-08 23:59:59
2025-06-09 08:02:01,571 - INFO - 查询分段 1: 2025-04-09 至 2025-04-10
2025-06-09 08:02:01,571 - INFO - 查询日期范围: 2025-04-09 至 2025-04-10，使用分页查询，每页 100 条记录
2025-06-09 08:02:01,571 - INFO - Request Parameters - Page 1:
2025-06-09 08:02:01,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:01,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000571, 1744214400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:09,712 - ERROR - API请求失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5E2A1825-0200-7B54-8627-E7513C61B7DE Response: {'code': 'ServiceUnavailable', 'requestid': '5E2A1825-0200-7B54-8627-E7513C61B7DE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-09 08:02:09,712 - WARNING - 已将错误日期范围添加到排除列表: 2025-04-09 至 2025-04-10
2025-06-09 08:02:09,712 - ERROR - 服务不可用，将等待后重试
2025-06-09 08:02:09,712 - ERROR - 获取第 1 页数据时出错: 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5E2A1825-0200-7B54-8627-E7513C61B7DE Response: {'code': 'ServiceUnavailable', 'requestid': '5E2A1825-0200-7B54-8627-E7513C61B7DE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-09 08:02:09,712 - WARNING - 服务暂时不可用，等待 6 秒后重试...
2025-06-09 08:02:15,728 - WARNING - 服务暂时不可用，将等待更长时间: 10秒
2025-06-09 08:02:15,728 - WARNING - 获取表单数据失败 (尝试 1/3): 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5E2A1825-0200-7B54-8627-E7513C61B7DE Response: {'code': 'ServiceUnavailable', 'requestid': '5E2A1825-0200-7B54-8627-E7513C61B7DE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}，将在 10 秒后重试...
2025-06-09 08:02:25,743 - INFO - Request Parameters - Page 1:
2025-06-09 08:02:25,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:25,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000571, 1744214400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:26,602 - INFO - API请求耗时: 859ms
2025-06-09 08:02:26,602 - INFO - Response - Page 1
2025-06-09 08:02:26,602 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:02:27,118 - INFO - Request Parameters - Page 2:
2025-06-09 08:02:27,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:27,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000571, 1744214400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:27,852 - INFO - API请求耗时: 734ms
2025-06-09 08:02:27,852 - INFO - Response - Page 2
2025-06-09 08:02:27,852 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:02:28,352 - INFO - Request Parameters - Page 3:
2025-06-09 08:02:28,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:28,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000571, 1744214400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:28,806 - INFO - API请求耗时: 453ms
2025-06-09 08:02:28,806 - INFO - Response - Page 3
2025-06-09 08:02:28,806 - INFO - 第 3 页获取到 11 条记录
2025-06-09 08:02:28,806 - INFO - 查询完成，共获取到 211 条记录
2025-06-09 08:02:28,806 - INFO - 分段 1 查询成功，获取到 211 条记录
2025-06-09 08:02:29,821 - INFO - 查询分段 2: 2025-04-11 至 2025-04-12
2025-06-09 08:02:29,821 - INFO - 查询日期范围: 2025-04-11 至 2025-04-12，使用分页查询，每页 100 条记录
2025-06-09 08:02:29,821 - INFO - Request Parameters - Page 1:
2025-06-09 08:02:29,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:29,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800571, 1744387200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:30,587 - INFO - API请求耗时: 766ms
2025-06-09 08:02:30,587 - INFO - Response - Page 1
2025-06-09 08:02:30,587 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:02:31,087 - INFO - Request Parameters - Page 2:
2025-06-09 08:02:31,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:31,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800571, 1744387200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:31,727 - INFO - API请求耗时: 641ms
2025-06-09 08:02:31,727 - INFO - Response - Page 2
2025-06-09 08:02:31,727 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:02:32,227 - INFO - Request Parameters - Page 3:
2025-06-09 08:02:32,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:32,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800571, 1744387200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:32,821 - INFO - API请求耗时: 594ms
2025-06-09 08:02:32,821 - INFO - Response - Page 3
2025-06-09 08:02:32,821 - INFO - 第 3 页获取到 21 条记录
2025-06-09 08:02:32,821 - INFO - 查询完成，共获取到 221 条记录
2025-06-09 08:02:32,821 - INFO - 分段 2 查询成功，获取到 221 条记录
2025-06-09 08:02:33,837 - INFO - 查询分段 3: 2025-04-13 至 2025-04-14
2025-06-09 08:02:33,837 - INFO - 查询日期范围: 2025-04-13 至 2025-04-14，使用分页查询，每页 100 条记录
2025-06-09 08:02:33,837 - INFO - Request Parameters - Page 1:
2025-06-09 08:02:33,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:33,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600571, 1744560000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:34,759 - INFO - API请求耗时: 922ms
2025-06-09 08:02:34,774 - INFO - Response - Page 1
2025-06-09 08:02:34,774 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:02:35,274 - INFO - Request Parameters - Page 2:
2025-06-09 08:02:35,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:35,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600571, 1744560000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:36,009 - INFO - API请求耗时: 734ms
2025-06-09 08:02:36,009 - INFO - Response - Page 2
2025-06-09 08:02:36,009 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:02:36,524 - INFO - Request Parameters - Page 3:
2025-06-09 08:02:36,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:36,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600571, 1744560000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:36,977 - INFO - API请求耗时: 453ms
2025-06-09 08:02:36,977 - INFO - Response - Page 3
2025-06-09 08:02:36,977 - INFO - 第 3 页获取到 15 条记录
2025-06-09 08:02:36,977 - INFO - 查询完成，共获取到 215 条记录
2025-06-09 08:02:36,977 - INFO - 分段 3 查询成功，获取到 215 条记录
2025-06-09 08:02:37,993 - INFO - 查询分段 4: 2025-04-15 至 2025-04-16
2025-06-09 08:02:37,993 - INFO - 查询日期范围: 2025-04-15 至 2025-04-16，使用分页查询，每页 100 条记录
2025-06-09 08:02:37,993 - INFO - Request Parameters - Page 1:
2025-06-09 08:02:37,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:37,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400571, 1744732800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:38,634 - INFO - API请求耗时: 641ms
2025-06-09 08:02:38,634 - INFO - Response - Page 1
2025-06-09 08:02:38,634 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:02:39,134 - INFO - Request Parameters - Page 2:
2025-06-09 08:02:39,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:39,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400571, 1744732800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:39,712 - INFO - API请求耗时: 578ms
2025-06-09 08:02:39,712 - INFO - Response - Page 2
2025-06-09 08:02:39,712 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:02:40,212 - INFO - Request Parameters - Page 3:
2025-06-09 08:02:40,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:40,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400571, 1744732800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:40,681 - INFO - API请求耗时: 469ms
2025-06-09 08:02:40,681 - INFO - Response - Page 3
2025-06-09 08:02:40,681 - INFO - 第 3 页获取到 10 条记录
2025-06-09 08:02:40,681 - INFO - 查询完成，共获取到 210 条记录
2025-06-09 08:02:40,681 - INFO - 分段 4 查询成功，获取到 210 条记录
2025-06-09 08:02:41,681 - INFO - 查询分段 5: 2025-04-17 至 2025-04-18
2025-06-09 08:02:41,681 - INFO - 查询日期范围: 2025-04-17 至 2025-04-18，使用分页查询，每页 100 条记录
2025-06-09 08:02:41,681 - INFO - Request Parameters - Page 1:
2025-06-09 08:02:41,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:41,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200571, 1744905600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:42,634 - INFO - API请求耗时: 953ms
2025-06-09 08:02:42,634 - INFO - Response - Page 1
2025-06-09 08:02:42,634 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:02:43,149 - INFO - Request Parameters - Page 2:
2025-06-09 08:02:43,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:43,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200571, 1744905600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:43,837 - INFO - API请求耗时: 687ms
2025-06-09 08:02:43,837 - INFO - Response - Page 2
2025-06-09 08:02:43,837 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:02:44,337 - INFO - Request Parameters - Page 3:
2025-06-09 08:02:44,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:44,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200571, 1744905600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:44,805 - INFO - API请求耗时: 469ms
2025-06-09 08:02:44,805 - INFO - Response - Page 3
2025-06-09 08:02:44,805 - INFO - 第 3 页获取到 17 条记录
2025-06-09 08:02:44,805 - INFO - 查询完成，共获取到 217 条记录
2025-06-09 08:02:44,805 - INFO - 分段 5 查询成功，获取到 217 条记录
2025-06-09 08:02:45,821 - INFO - 查询分段 6: 2025-04-19 至 2025-04-20
2025-06-09 08:02:45,821 - INFO - 查询日期范围: 2025-04-19 至 2025-04-20，使用分页查询，每页 100 条记录
2025-06-09 08:02:45,821 - INFO - Request Parameters - Page 1:
2025-06-09 08:02:45,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:45,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000571, 1745078400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:46,477 - INFO - API请求耗时: 656ms
2025-06-09 08:02:46,477 - INFO - Response - Page 1
2025-06-09 08:02:46,477 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:02:46,977 - INFO - Request Parameters - Page 2:
2025-06-09 08:02:46,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:46,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000571, 1745078400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:47,712 - INFO - API请求耗时: 734ms
2025-06-09 08:02:47,712 - INFO - Response - Page 2
2025-06-09 08:02:47,712 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:02:48,227 - INFO - Request Parameters - Page 3:
2025-06-09 08:02:48,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:48,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000571, 1745078400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:48,649 - INFO - API请求耗时: 422ms
2025-06-09 08:02:48,649 - INFO - Response - Page 3
2025-06-09 08:02:48,649 - INFO - 第 3 页获取到 16 条记录
2025-06-09 08:02:48,649 - INFO - 查询完成，共获取到 216 条记录
2025-06-09 08:02:48,649 - INFO - 分段 6 查询成功，获取到 216 条记录
2025-06-09 08:02:49,649 - INFO - 查询分段 7: 2025-04-21 至 2025-04-22
2025-06-09 08:02:49,649 - INFO - 查询日期范围: 2025-04-21 至 2025-04-22，使用分页查询，每页 100 条记录
2025-06-09 08:02:49,649 - INFO - Request Parameters - Page 1:
2025-06-09 08:02:49,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:49,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800571, 1745251200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:50,290 - INFO - API请求耗时: 641ms
2025-06-09 08:02:50,290 - INFO - Response - Page 1
2025-06-09 08:02:50,290 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:02:50,790 - INFO - Request Parameters - Page 2:
2025-06-09 08:02:50,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:50,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800571, 1745251200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:51,462 - INFO - API请求耗时: 672ms
2025-06-09 08:02:51,462 - INFO - Response - Page 2
2025-06-09 08:02:51,462 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:02:51,977 - INFO - Request Parameters - Page 3:
2025-06-09 08:02:51,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:51,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800571, 1745251200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:52,477 - INFO - API请求耗时: 500ms
2025-06-09 08:02:52,477 - INFO - Response - Page 3
2025-06-09 08:02:52,477 - INFO - 第 3 页获取到 12 条记录
2025-06-09 08:02:52,477 - INFO - 查询完成，共获取到 212 条记录
2025-06-09 08:02:52,477 - INFO - 分段 7 查询成功，获取到 212 条记录
2025-06-09 08:02:53,493 - INFO - 查询分段 8: 2025-04-23 至 2025-04-24
2025-06-09 08:02:53,493 - INFO - 查询日期范围: 2025-04-23 至 2025-04-24，使用分页查询，每页 100 条记录
2025-06-09 08:02:53,493 - INFO - Request Parameters - Page 1:
2025-06-09 08:02:53,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:53,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600571, 1745424000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:54,149 - INFO - API请求耗时: 656ms
2025-06-09 08:02:54,149 - INFO - Response - Page 1
2025-06-09 08:02:54,149 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:02:54,665 - INFO - Request Parameters - Page 2:
2025-06-09 08:02:54,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:54,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600571, 1745424000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:55,352 - INFO - API请求耗时: 687ms
2025-06-09 08:02:55,352 - INFO - Response - Page 2
2025-06-09 08:02:55,352 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:02:55,868 - INFO - Request Parameters - Page 3:
2025-06-09 08:02:55,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:55,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600571, 1745424000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:56,290 - INFO - API请求耗时: 422ms
2025-06-09 08:02:56,290 - INFO - Response - Page 3
2025-06-09 08:02:56,290 - INFO - 第 3 页获取到 7 条记录
2025-06-09 08:02:56,290 - INFO - 查询完成，共获取到 207 条记录
2025-06-09 08:02:56,290 - INFO - 分段 8 查询成功，获取到 207 条记录
2025-06-09 08:02:57,305 - INFO - 查询分段 9: 2025-04-25 至 2025-04-26
2025-06-09 08:02:57,305 - INFO - 查询日期范围: 2025-04-25 至 2025-04-26，使用分页查询，每页 100 条记录
2025-06-09 08:02:57,305 - INFO - Request Parameters - Page 1:
2025-06-09 08:02:57,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:57,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400571, 1745596800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:57,930 - INFO - API请求耗时: 625ms
2025-06-09 08:02:57,930 - INFO - Response - Page 1
2025-06-09 08:02:57,930 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:02:58,430 - INFO - Request Parameters - Page 2:
2025-06-09 08:02:58,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:58,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400571, 1745596800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:58,993 - INFO - API请求耗时: 562ms
2025-06-09 08:02:59,009 - INFO - Response - Page 2
2025-06-09 08:02:59,009 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:02:59,509 - INFO - Request Parameters - Page 3:
2025-06-09 08:02:59,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:02:59,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400571, 1745596800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:02:59,977 - INFO - API请求耗时: 469ms
2025-06-09 08:02:59,977 - INFO - Response - Page 3
2025-06-09 08:02:59,977 - INFO - 第 3 页获取到 18 条记录
2025-06-09 08:02:59,977 - INFO - 查询完成，共获取到 218 条记录
2025-06-09 08:02:59,977 - INFO - 分段 9 查询成功，获取到 218 条记录
2025-06-09 08:03:00,993 - INFO - 查询分段 10: 2025-04-27 至 2025-04-28
2025-06-09 08:03:00,993 - INFO - 查询日期范围: 2025-04-27 至 2025-04-28，使用分页查询，每页 100 条记录
2025-06-09 08:03:00,993 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:00,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:00,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200571, 1745769600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:01,540 - INFO - API请求耗时: 547ms
2025-06-09 08:03:01,540 - INFO - Response - Page 1
2025-06-09 08:03:01,540 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:02,040 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:02,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:02,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200571, 1745769600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:02,930 - INFO - API请求耗时: 891ms
2025-06-09 08:03:02,930 - INFO - Response - Page 2
2025-06-09 08:03:02,930 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:03,430 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:03,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:03,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200571, 1745769600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:03,852 - INFO - API请求耗时: 422ms
2025-06-09 08:03:03,852 - INFO - Response - Page 3
2025-06-09 08:03:03,852 - INFO - 第 3 页获取到 12 条记录
2025-06-09 08:03:03,852 - INFO - 查询完成，共获取到 212 条记录
2025-06-09 08:03:03,852 - INFO - 分段 10 查询成功，获取到 212 条记录
2025-06-09 08:03:04,868 - INFO - 查询分段 11: 2025-04-29 至 2025-04-30
2025-06-09 08:03:04,868 - INFO - 查询日期范围: 2025-04-29 至 2025-04-30，使用分页查询，每页 100 条记录
2025-06-09 08:03:04,868 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:04,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:04,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000571, 1745942400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:05,524 - INFO - API请求耗时: 656ms
2025-06-09 08:03:05,524 - INFO - Response - Page 1
2025-06-09 08:03:05,524 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:06,024 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:06,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:06,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000571, 1745942400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:06,665 - INFO - API请求耗时: 641ms
2025-06-09 08:03:06,665 - INFO - Response - Page 2
2025-06-09 08:03:06,665 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:07,165 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:07,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:07,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000571, 1745942400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:07,696 - INFO - API请求耗时: 531ms
2025-06-09 08:03:07,696 - INFO - Response - Page 3
2025-06-09 08:03:07,696 - INFO - 第 3 页获取到 17 条记录
2025-06-09 08:03:07,696 - INFO - 查询完成，共获取到 217 条记录
2025-06-09 08:03:07,696 - INFO - 分段 11 查询成功，获取到 217 条记录
2025-06-09 08:03:08,712 - INFO - 查询分段 12: 2025-05-01 至 2025-05-02
2025-06-09 08:03:08,712 - INFO - 查询日期范围: 2025-05-01 至 2025-05-02，使用分页查询，每页 100 条记录
2025-06-09 08:03:08,712 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:08,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:08,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800571, 1746115200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:09,383 - INFO - API请求耗时: 672ms
2025-06-09 08:03:09,383 - INFO - Response - Page 1
2025-06-09 08:03:09,383 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:09,899 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:09,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:09,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800571, 1746115200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:10,602 - INFO - API请求耗时: 703ms
2025-06-09 08:03:10,602 - INFO - Response - Page 2
2025-06-09 08:03:10,602 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:11,102 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:11,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:11,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800571, 1746115200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:11,571 - INFO - API请求耗时: 469ms
2025-06-09 08:03:11,571 - INFO - Response - Page 3
2025-06-09 08:03:11,571 - INFO - 第 3 页获取到 11 条记录
2025-06-09 08:03:11,571 - INFO - 查询完成，共获取到 211 条记录
2025-06-09 08:03:11,571 - INFO - 分段 12 查询成功，获取到 211 条记录
2025-06-09 08:03:12,587 - INFO - 查询分段 13: 2025-05-03 至 2025-05-04
2025-06-09 08:03:12,587 - INFO - 查询日期范围: 2025-05-03 至 2025-05-04，使用分页查询，每页 100 条记录
2025-06-09 08:03:12,587 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:12,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:12,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600571, 1746288000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:13,227 - INFO - API请求耗时: 641ms
2025-06-09 08:03:13,227 - INFO - Response - Page 1
2025-06-09 08:03:13,227 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:13,727 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:13,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:13,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600571, 1746288000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:14,477 - INFO - API请求耗时: 750ms
2025-06-09 08:03:14,477 - INFO - Response - Page 2
2025-06-09 08:03:14,477 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:14,977 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:14,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:14,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600571, 1746288000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:15,477 - INFO - API请求耗时: 500ms
2025-06-09 08:03:15,477 - INFO - Response - Page 3
2025-06-09 08:03:15,477 - INFO - 第 3 页获取到 15 条记录
2025-06-09 08:03:15,477 - INFO - 查询完成，共获取到 215 条记录
2025-06-09 08:03:15,477 - INFO - 分段 13 查询成功，获取到 215 条记录
2025-06-09 08:03:16,477 - INFO - 查询分段 14: 2025-05-05 至 2025-05-06
2025-06-09 08:03:16,477 - INFO - 查询日期范围: 2025-05-05 至 2025-05-06，使用分页查询，每页 100 条记录
2025-06-09 08:03:16,477 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:16,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:16,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400571, 1746460800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:17,430 - INFO - API请求耗时: 953ms
2025-06-09 08:03:17,430 - INFO - Response - Page 1
2025-06-09 08:03:17,430 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:17,946 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:17,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:17,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400571, 1746460800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:18,712 - INFO - API请求耗时: 766ms
2025-06-09 08:03:18,712 - INFO - Response - Page 2
2025-06-09 08:03:18,712 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:19,227 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:19,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:19,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400571, 1746460800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:19,618 - INFO - API请求耗时: 391ms
2025-06-09 08:03:19,618 - INFO - Response - Page 3
2025-06-09 08:03:19,633 - INFO - 第 3 页获取到 4 条记录
2025-06-09 08:03:19,633 - INFO - 查询完成，共获取到 204 条记录
2025-06-09 08:03:19,633 - INFO - 分段 14 查询成功，获取到 204 条记录
2025-06-09 08:03:20,633 - INFO - 查询分段 15: 2025-05-07 至 2025-05-08
2025-06-09 08:03:20,633 - INFO - 查询日期范围: 2025-05-07 至 2025-05-08，使用分页查询，每页 100 条记录
2025-06-09 08:03:20,633 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:20,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:20,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200571, 1746633600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:21,290 - INFO - API请求耗时: 656ms
2025-06-09 08:03:21,290 - INFO - Response - Page 1
2025-06-09 08:03:21,290 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:21,790 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:21,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:21,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200571, 1746633600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:22,399 - INFO - API请求耗时: 609ms
2025-06-09 08:03:22,399 - INFO - Response - Page 2
2025-06-09 08:03:22,399 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:22,915 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:22,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:22,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200571, 1746633600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:23,290 - INFO - API请求耗时: 375ms
2025-06-09 08:03:23,290 - INFO - Response - Page 3
2025-06-09 08:03:23,290 - INFO - 第 3 页获取到 8 条记录
2025-06-09 08:03:23,290 - INFO - 查询完成，共获取到 208 条记录
2025-06-09 08:03:23,305 - INFO - 分段 15 查询成功，获取到 208 条记录
2025-06-09 08:03:24,305 - INFO - 查询分段 16: 2025-05-09 至 2025-05-10
2025-06-09 08:03:24,305 - INFO - 查询日期范围: 2025-05-09 至 2025-05-10，使用分页查询，每页 100 条记录
2025-06-09 08:03:24,305 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:24,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:24,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000571, 1746806400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:24,977 - INFO - API请求耗时: 672ms
2025-06-09 08:03:24,977 - INFO - Response - Page 1
2025-06-09 08:03:24,977 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:25,477 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:25,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:25,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000571, 1746806400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:26,086 - INFO - API请求耗时: 609ms
2025-06-09 08:03:26,086 - INFO - Response - Page 2
2025-06-09 08:03:26,086 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:26,602 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:26,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:26,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000571, 1746806400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:27,071 - INFO - API请求耗时: 469ms
2025-06-09 08:03:27,071 - INFO - Response - Page 3
2025-06-09 08:03:27,071 - INFO - 第 3 页获取到 18 条记录
2025-06-09 08:03:27,071 - INFO - 查询完成，共获取到 218 条记录
2025-06-09 08:03:27,071 - INFO - 分段 16 查询成功，获取到 218 条记录
2025-06-09 08:03:28,086 - INFO - 查询分段 17: 2025-05-11 至 2025-05-12
2025-06-09 08:03:28,086 - INFO - 查询日期范围: 2025-05-11 至 2025-05-12，使用分页查询，每页 100 条记录
2025-06-09 08:03:28,086 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:28,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:28,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800571, 1746979200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:28,680 - INFO - API请求耗时: 594ms
2025-06-09 08:03:28,680 - INFO - Response - Page 1
2025-06-09 08:03:28,680 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:29,196 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:29,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:29,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800571, 1746979200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:29,790 - INFO - API请求耗时: 594ms
2025-06-09 08:03:29,790 - INFO - Response - Page 2
2025-06-09 08:03:29,790 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:30,305 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:30,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:30,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800571, 1746979200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:30,883 - INFO - API请求耗时: 578ms
2025-06-09 08:03:30,883 - INFO - Response - Page 3
2025-06-09 08:03:30,883 - INFO - 第 3 页获取到 15 条记录
2025-06-09 08:03:30,883 - INFO - 查询完成，共获取到 215 条记录
2025-06-09 08:03:30,883 - INFO - 分段 17 查询成功，获取到 215 条记录
2025-06-09 08:03:31,883 - INFO - 查询分段 18: 2025-05-13 至 2025-05-14
2025-06-09 08:03:31,883 - INFO - 查询日期范围: 2025-05-13 至 2025-05-14，使用分页查询，每页 100 条记录
2025-06-09 08:03:31,883 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:31,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:31,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600571, 1747152000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:32,477 - INFO - API请求耗时: 594ms
2025-06-09 08:03:32,477 - INFO - Response - Page 1
2025-06-09 08:03:32,477 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:32,977 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:32,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:32,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600571, 1747152000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:33,586 - INFO - API请求耗时: 609ms
2025-06-09 08:03:33,586 - INFO - Response - Page 2
2025-06-09 08:03:33,586 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:34,086 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:34,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:34,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600571, 1747152000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:34,493 - INFO - API请求耗时: 406ms
2025-06-09 08:03:34,493 - INFO - Response - Page 3
2025-06-09 08:03:34,493 - INFO - 第 3 页获取到 7 条记录
2025-06-09 08:03:34,493 - INFO - 查询完成，共获取到 207 条记录
2025-06-09 08:03:34,493 - INFO - 分段 18 查询成功，获取到 207 条记录
2025-06-09 08:03:35,508 - INFO - 查询分段 19: 2025-05-15 至 2025-05-16
2025-06-09 08:03:35,508 - INFO - 查询日期范围: 2025-05-15 至 2025-05-16，使用分页查询，每页 100 条记录
2025-06-09 08:03:35,508 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:35,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:35,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400571, 1747324800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:36,321 - INFO - API请求耗时: 813ms
2025-06-09 08:03:36,321 - INFO - Response - Page 1
2025-06-09 08:03:36,321 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:36,821 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:36,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:36,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400571, 1747324800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:37,586 - INFO - API请求耗时: 766ms
2025-06-09 08:03:37,586 - INFO - Response - Page 2
2025-06-09 08:03:37,586 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:38,102 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:38,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:38,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400571, 1747324800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:38,602 - INFO - API请求耗时: 500ms
2025-06-09 08:03:38,618 - INFO - Response - Page 3
2025-06-09 08:03:38,618 - INFO - 第 3 页获取到 10 条记录
2025-06-09 08:03:38,618 - INFO - 查询完成，共获取到 210 条记录
2025-06-09 08:03:38,618 - INFO - 分段 19 查询成功，获取到 210 条记录
2025-06-09 08:03:39,633 - INFO - 查询分段 20: 2025-05-17 至 2025-05-18
2025-06-09 08:03:39,633 - INFO - 查询日期范围: 2025-05-17 至 2025-05-18，使用分页查询，每页 100 条记录
2025-06-09 08:03:39,633 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:39,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:39,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200571, 1747497600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:40,321 - INFO - API请求耗时: 688ms
2025-06-09 08:03:40,321 - INFO - Response - Page 1
2025-06-09 08:03:40,321 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:40,836 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:40,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:40,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200571, 1747497600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:41,446 - INFO - API请求耗时: 609ms
2025-06-09 08:03:41,446 - INFO - Response - Page 2
2025-06-09 08:03:41,446 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:41,961 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:41,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:41,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200571, 1747497600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:42,414 - INFO - API请求耗时: 453ms
2025-06-09 08:03:42,414 - INFO - Response - Page 3
2025-06-09 08:03:42,414 - INFO - 第 3 页获取到 16 条记录
2025-06-09 08:03:42,414 - INFO - 查询完成，共获取到 216 条记录
2025-06-09 08:03:42,414 - INFO - 分段 20 查询成功，获取到 216 条记录
2025-06-09 08:03:43,430 - INFO - 查询分段 21: 2025-05-19 至 2025-05-20
2025-06-09 08:03:43,430 - INFO - 查询日期范围: 2025-05-19 至 2025-05-20，使用分页查询，每页 100 条记录
2025-06-09 08:03:43,430 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:43,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:43,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000571, 1747670400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:44,508 - INFO - API请求耗时: 1078ms
2025-06-09 08:03:44,508 - INFO - Response - Page 1
2025-06-09 08:03:44,508 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:45,008 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:45,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:45,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000571, 1747670400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:45,743 - INFO - API请求耗时: 734ms
2025-06-09 08:03:45,743 - INFO - Response - Page 2
2025-06-09 08:03:45,743 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:46,258 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:46,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:46,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000571, 1747670400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:46,758 - INFO - API请求耗时: 500ms
2025-06-09 08:03:46,758 - INFO - Response - Page 3
2025-06-09 08:03:46,758 - INFO - 第 3 页获取到 13 条记录
2025-06-09 08:03:46,758 - INFO - 查询完成，共获取到 213 条记录
2025-06-09 08:03:46,758 - INFO - 分段 21 查询成功，获取到 213 条记录
2025-06-09 08:03:47,774 - INFO - 查询分段 22: 2025-05-21 至 2025-05-22
2025-06-09 08:03:47,774 - INFO - 查询日期范围: 2025-05-21 至 2025-05-22，使用分页查询，每页 100 条记录
2025-06-09 08:03:47,774 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:47,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:47,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800571, 1747843200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:48,461 - INFO - API请求耗时: 687ms
2025-06-09 08:03:48,461 - INFO - Response - Page 1
2025-06-09 08:03:48,461 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:48,961 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:48,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:48,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800571, 1747843200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:49,571 - INFO - API请求耗时: 609ms
2025-06-09 08:03:49,571 - INFO - Response - Page 2
2025-06-09 08:03:49,571 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:50,071 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:50,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:50,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800571, 1747843200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:50,446 - INFO - API请求耗时: 375ms
2025-06-09 08:03:50,446 - INFO - Response - Page 3
2025-06-09 08:03:50,446 - INFO - 第 3 页获取到 8 条记录
2025-06-09 08:03:50,446 - INFO - 查询完成，共获取到 208 条记录
2025-06-09 08:03:50,446 - INFO - 分段 22 查询成功，获取到 208 条记录
2025-06-09 08:03:51,461 - INFO - 查询分段 23: 2025-05-23 至 2025-05-24
2025-06-09 08:03:51,461 - INFO - 查询日期范围: 2025-05-23 至 2025-05-24，使用分页查询，每页 100 条记录
2025-06-09 08:03:51,461 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:51,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:51,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600571, 1748016000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:52,086 - INFO - API请求耗时: 625ms
2025-06-09 08:03:52,086 - INFO - Response - Page 1
2025-06-09 08:03:52,086 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:52,602 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:52,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:52,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600571, 1748016000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:53,414 - INFO - API请求耗时: 813ms
2025-06-09 08:03:53,430 - INFO - Response - Page 2
2025-06-09 08:03:53,430 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:53,930 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:53,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:53,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600571, 1748016000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:54,446 - INFO - API请求耗时: 516ms
2025-06-09 08:03:54,446 - INFO - Response - Page 3
2025-06-09 08:03:54,446 - INFO - 第 3 页获取到 15 条记录
2025-06-09 08:03:54,446 - INFO - 查询完成，共获取到 215 条记录
2025-06-09 08:03:54,446 - INFO - 分段 23 查询成功，获取到 215 条记录
2025-06-09 08:03:55,446 - INFO - 查询分段 24: 2025-05-25 至 2025-05-26
2025-06-09 08:03:55,446 - INFO - 查询日期范围: 2025-05-25 至 2025-05-26，使用分页查询，每页 100 条记录
2025-06-09 08:03:55,446 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:55,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:55,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400571, 1748188800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:56,243 - INFO - API请求耗时: 797ms
2025-06-09 08:03:56,243 - INFO - Response - Page 1
2025-06-09 08:03:56,243 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:03:56,743 - INFO - Request Parameters - Page 2:
2025-06-09 08:03:56,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:56,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400571, 1748188800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:57,649 - INFO - API请求耗时: 906ms
2025-06-09 08:03:57,649 - INFO - Response - Page 2
2025-06-09 08:03:57,649 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:03:58,149 - INFO - Request Parameters - Page 3:
2025-06-09 08:03:58,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:58,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400571, 1748188800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:03:58,618 - INFO - API请求耗时: 469ms
2025-06-09 08:03:58,618 - INFO - Response - Page 3
2025-06-09 08:03:58,618 - INFO - 第 3 页获取到 5 条记录
2025-06-09 08:03:58,618 - INFO - 查询完成，共获取到 205 条记录
2025-06-09 08:03:58,618 - INFO - 分段 24 查询成功，获取到 205 条记录
2025-06-09 08:03:59,633 - INFO - 查询分段 25: 2025-05-27 至 2025-05-28
2025-06-09 08:03:59,633 - INFO - 查询日期范围: 2025-05-27 至 2025-05-28，使用分页查询，每页 100 条记录
2025-06-09 08:03:59,633 - INFO - Request Parameters - Page 1:
2025-06-09 08:03:59,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:03:59,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200571, 1748361600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:00,414 - INFO - API请求耗时: 781ms
2025-06-09 08:04:00,414 - INFO - Response - Page 1
2025-06-09 08:04:00,414 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:04:00,914 - INFO - Request Parameters - Page 2:
2025-06-09 08:04:00,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:00,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200571, 1748361600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:01,633 - INFO - API请求耗时: 719ms
2025-06-09 08:04:01,633 - INFO - Response - Page 2
2025-06-09 08:04:01,633 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:04:02,133 - INFO - Request Parameters - Page 3:
2025-06-09 08:04:02,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:02,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200571, 1748361600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:02,555 - INFO - API请求耗时: 422ms
2025-06-09 08:04:02,555 - INFO - Response - Page 3
2025-06-09 08:04:02,555 - INFO - 第 3 页获取到 8 条记录
2025-06-09 08:04:02,555 - INFO - 查询完成，共获取到 208 条记录
2025-06-09 08:04:02,555 - INFO - 分段 25 查询成功，获取到 208 条记录
2025-06-09 08:04:03,571 - INFO - 查询分段 26: 2025-05-29 至 2025-05-30
2025-06-09 08:04:03,571 - INFO - 查询日期范围: 2025-05-29 至 2025-05-30，使用分页查询，每页 100 条记录
2025-06-09 08:04:03,571 - INFO - Request Parameters - Page 1:
2025-06-09 08:04:03,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:03,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000571, 1748534400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:04,258 - INFO - API请求耗时: 688ms
2025-06-09 08:04:04,258 - INFO - Response - Page 1
2025-06-09 08:04:04,258 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:04:04,774 - INFO - Request Parameters - Page 2:
2025-06-09 08:04:04,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:04,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000571, 1748534400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:05,508 - INFO - API请求耗时: 734ms
2025-06-09 08:04:05,508 - INFO - Response - Page 2
2025-06-09 08:04:05,508 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:04:06,024 - INFO - Request Parameters - Page 3:
2025-06-09 08:04:06,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:06,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000571, 1748534400571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:06,477 - INFO - API请求耗时: 453ms
2025-06-09 08:04:06,477 - INFO - Response - Page 3
2025-06-09 08:04:06,477 - INFO - 第 3 页获取到 8 条记录
2025-06-09 08:04:06,477 - INFO - 查询完成，共获取到 208 条记录
2025-06-09 08:04:06,477 - INFO - 分段 26 查询成功，获取到 208 条记录
2025-06-09 08:04:07,492 - INFO - 查询分段 27: 2025-05-31 至 2025-06-01
2025-06-09 08:04:07,492 - INFO - 查询日期范围: 2025-05-31 至 2025-06-01，使用分页查询，每页 100 条记录
2025-06-09 08:04:07,492 - INFO - Request Parameters - Page 1:
2025-06-09 08:04:07,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:07,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800571, 1748707200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:08,180 - INFO - API请求耗时: 687ms
2025-06-09 08:04:08,180 - INFO - Response - Page 1
2025-06-09 08:04:08,180 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:04:08,680 - INFO - Request Parameters - Page 2:
2025-06-09 08:04:08,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:08,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800571, 1748707200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:09,321 - INFO - API请求耗时: 641ms
2025-06-09 08:04:09,321 - INFO - Response - Page 2
2025-06-09 08:04:09,321 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:04:09,821 - INFO - Request Parameters - Page 3:
2025-06-09 08:04:09,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:09,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800571, 1748707200571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:10,274 - INFO - API请求耗时: 453ms
2025-06-09 08:04:10,274 - INFO - Response - Page 3
2025-06-09 08:04:10,274 - INFO - 第 3 页获取到 6 条记录
2025-06-09 08:04:10,274 - INFO - 查询完成，共获取到 206 条记录
2025-06-09 08:04:10,274 - INFO - 分段 27 查询成功，获取到 206 条记录
2025-06-09 08:04:11,289 - INFO - 查询分段 28: 2025-06-02 至 2025-06-03
2025-06-09 08:04:11,289 - INFO - 查询日期范围: 2025-06-02 至 2025-06-03，使用分页查询，每页 100 条记录
2025-06-09 08:04:11,289 - INFO - Request Parameters - Page 1:
2025-06-09 08:04:11,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:11,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600571, 1748880000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:12,071 - INFO - API请求耗时: 781ms
2025-06-09 08:04:12,071 - INFO - Response - Page 1
2025-06-09 08:04:12,071 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:04:12,586 - INFO - Request Parameters - Page 2:
2025-06-09 08:04:12,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:12,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600571, 1748880000571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:13,289 - INFO - API请求耗时: 703ms
2025-06-09 08:04:13,289 - INFO - Response - Page 2
2025-06-09 08:04:13,289 - INFO - 第 2 页获取到 99 条记录
2025-06-09 08:04:13,289 - INFO - 查询完成，共获取到 199 条记录
2025-06-09 08:04:13,289 - INFO - 分段 28 查询成功，获取到 199 条记录
2025-06-09 08:04:14,289 - INFO - 查询分段 29: 2025-06-04 至 2025-06-05
2025-06-09 08:04:14,289 - INFO - 查询日期范围: 2025-06-04 至 2025-06-05，使用分页查询，每页 100 条记录
2025-06-09 08:04:14,289 - INFO - Request Parameters - Page 1:
2025-06-09 08:04:14,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:14,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748966400571, 1749052800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:14,930 - INFO - API请求耗时: 641ms
2025-06-09 08:04:14,930 - INFO - Response - Page 1
2025-06-09 08:04:14,930 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:04:15,446 - INFO - Request Parameters - Page 2:
2025-06-09 08:04:15,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:15,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748966400571, 1749052800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:16,149 - INFO - API请求耗时: 703ms
2025-06-09 08:04:16,149 - INFO - Response - Page 2
2025-06-09 08:04:16,149 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:04:16,664 - INFO - Request Parameters - Page 3:
2025-06-09 08:04:16,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:16,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748966400571, 1749052800571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:16,961 - INFO - API请求耗时: 297ms
2025-06-09 08:04:16,961 - INFO - Response - Page 3
2025-06-09 08:04:16,961 - INFO - 第 3 页没有数据，已到达最后一页
2025-06-09 08:04:16,961 - INFO - 查询完成，共获取到 200 条记录
2025-06-09 08:04:16,961 - INFO - 分段 29 查询成功，获取到 200 条记录
2025-06-09 08:04:17,977 - INFO - 查询分段 30: 2025-06-06 至 2025-06-07
2025-06-09 08:04:17,977 - INFO - 查询日期范围: 2025-06-06 至 2025-06-07，使用分页查询，每页 100 条记录
2025-06-09 08:04:17,977 - INFO - Request Parameters - Page 1:
2025-06-09 08:04:17,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:17,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749139200571, 1749225600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:18,617 - INFO - API请求耗时: 641ms
2025-06-09 08:04:18,617 - INFO - Response - Page 1
2025-06-09 08:04:18,617 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:04:19,117 - INFO - Request Parameters - Page 2:
2025-06-09 08:04:19,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:19,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749139200571, 1749225600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:19,742 - INFO - API请求耗时: 625ms
2025-06-09 08:04:19,742 - INFO - Response - Page 2
2025-06-09 08:04:19,742 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:04:20,242 - INFO - Request Parameters - Page 3:
2025-06-09 08:04:20,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:20,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749139200571, 1749225600571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:20,633 - INFO - API请求耗时: 391ms
2025-06-09 08:04:20,633 - INFO - Response - Page 3
2025-06-09 08:04:20,633 - INFO - 第 3 页获取到 1 条记录
2025-06-09 08:04:20,633 - INFO - 查询完成，共获取到 201 条记录
2025-06-09 08:04:20,633 - INFO - 分段 30 查询成功，获取到 201 条记录
2025-06-09 08:04:21,633 - INFO - 查询分段 31: 2025-06-08 至 2025-06-08
2025-06-09 08:04:21,633 - INFO - 查询日期范围: 2025-06-08 至 2025-06-08，使用分页查询，每页 100 条记录
2025-06-09 08:04:21,633 - INFO - Request Parameters - Page 1:
2025-06-09 08:04:21,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:04:21,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749312000571, 1749398399571], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:04:22,024 - INFO - API请求耗时: 391ms
2025-06-09 08:04:22,024 - INFO - Response - Page 1
2025-06-09 08:04:22,024 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-09 08:04:22,024 - INFO - 查询完成，共获取到 0 条记录
2025-06-09 08:04:22,024 - WARNING - 分段 31 查询返回空数据
2025-06-09 08:04:23,039 - INFO - 宜搭每日表单数据查询完成，共 31 个分段，成功获取 6323 条记录，失败 0 次
2025-06-09 08:04:23,039 - INFO - 成功获取宜搭日销售表单数据，共 6323 条记录
2025-06-09 08:04:23,039 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-09 08:04:23,039 - INFO - 开始对比和同步日销售数据...
2025-06-09 08:04:23,211 - INFO - 成功创建宜搭日销售数据索引，共 6323 条记录
2025-06-09 08:04:23,211 - INFO - 开始处理数衍数据，共 12844 条记录
2025-06-09 08:04:23,211 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: F56E8E23D2584556A30D1378611DF4AE
2025-06-09 08:04:23,211 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: F56E8E23D2584556A30D1378611DF4AE
2025-06-09 08:04:23,211 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: D384CB5088914FB296DE32297895B8D6
2025-06-09 08:04:23,211 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: BF554F536BF14762AEB7110E7BD583B7
2025-06-09 08:04:23,211 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: BF554F536BF14762AEB7110E7BD583B7
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: AC07B70DB49845A8A52846E099EBC515
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: AC07B70DB49845A8A52846E099EBC515
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: AA76628FACEC4C13BD44C8280B45416D
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: AA76628FACEC4C13BD44C8280B45416D
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: A93C60005A8F41B092F6C5A8C21577CB
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: A93C60005A8F41B092F6C5A8C21577CB
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 78655ECA4A32471AB7842F8DE2018120
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 78655ECA4A32471AB7842F8DE2018120
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 6B7571A27AF84C73B4FC04CCBDB83D9B
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 6B7571A27AF84C73B4FC04CCBDB83D9B
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 6692283A183D432BAE322E1032539CE8
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 6692283A183D432BAE322E1032539CE8
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 3BB9A16AE8544997965802FAA3B83381
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 3BB9A16AE8544997965802FAA3B83381
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1IBBJ3RNCSAVNO7A70STAEF09Q001IPV
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1IBBJ3RNCSAVNO7A70STAEF09Q001IPV
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1I6E0VAU3IFJEQ22MH147FMU0M0013E8
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1I6E0VAU3IFJEQ22MH147FMU0M0013E8
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HVORJ88U7D2IL1AIB692RTFU8001185
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HVORJ88U7D2IL1AIB692RTFU8001185
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HVOQTVVFR41OA22BBK6R0G53G001SV2
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HVOQTVVFR41OA22BBK6R0G53G001SV2
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HV6P9SGUVGG9S36QDA69ST70J0015SA
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HV6P9SGUVGG9S36QDA69ST70J0015SA
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HOE1A3UTAESD606LODAUCEHAF001M2A
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HOE1A3UTAESD606LODAUCEHAF001M2A
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HINIR4GO8E5NM5U25UDHUFEGO001L3K
2025-06-09 08:04:23,227 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HINIR4GO8E5NM5U25UDHUFEGO001L3K
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HI85875BG16NA6U1G19QP117C001UB5
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HI85875BG16NA6U1G19QP117C001UB5
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HHDS9O4DUBH2L6U1G19QP11V40018CI
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HHDS9O4DUBH2L6U1G19QP11V40018CI
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HHDRIR0P38R666U1G19QP11V00018CE
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HB8D7KAC5K1G3723F7K257LIN001TSQ
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HB8D7KAC5K1G3723F7K257LIN001TSQ
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9UCRQKEOIF52ASKKUBQUNH0018FA
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9UCRQKEOIF52ASKKUBQUNH0018FA
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9S64J8E8R652ASKKUBQUMU0018EN
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9S64J8E8R652ASKKUBQUMU0018EN
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9PGR3703D752ASKKUBQUM50018DU
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9PGR3703D752ASKKUBQUM50018DU
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE2TORA7KI7Q2OV4FVC7FC0014BT
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE2TORA7KI7Q2OV4FVC7FC0014BT
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE29HIJ7QK7Q2OV4FVC7F40014BL
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE29HIJ7QK7Q2OV4FVC7F40014BL
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE1H27HPQN7Q2OV4FVC7EO0014B9
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE1H27HPQN7Q2OV4FVC7EO0014B9
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE0PKSKM647Q2OV4FVC7EC0014AT
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE0PKSKM647Q2OV4FVC7EC0014AT
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDVG061A0H7Q2OV4FVC7DO0014A9
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDVG061A0H7Q2OV4FVC7DO0014A9
2025-06-09 08:04:23,242 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDV84M589R7Q2OV4FVC7DK0014A5
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDV84M589R7Q2OV4FVC7DK0014A5
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDV0D9P6J27Q2OV4FVC7DG0014A1
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDV0D9P6J27Q2OV4FVC7DG0014A1
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDUNDNMH3D7Q2OV4FVC7DC00149T
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDUNDNMH3D7Q2OV4FVC7DC00149T
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDTG15Q4SH7Q2OV4FVC7CO001499
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDTG15Q4SH7Q2OV4FVC7CO001499
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDT8J32K6E7Q2OV4FVC7CK001495
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDT8J32K6E7Q2OV4FVC7CK001495
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSTUTUPTG7Q2OV4FVC7CG001491
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSTUTUPTG7Q2OV4FVC7CG001491
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSM3EAQJB7Q2OV4FVC7CC00148T
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSM3EAQJB7Q2OV4FVC7CC00148T
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSD911K3E7Q2OV4FVC7C800148P
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSD911K3E7Q2OV4FVC7C800148P
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDS537TI7U7Q2OV4FVC7C400148L
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDS537TI7U7Q2OV4FVC7C400148L
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDRLHCKFK97Q2OV4FVC7BS00148D
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDRLHCKFK97Q2OV4FVC7BS00148D
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDQDVL1EG67Q2OV4FVC7B800147P
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDQDVL1EG67Q2OV4FVC7B800147P
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDOE3O1J9R7Q2OV4FVC7A800146P
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDOE3O1J9R7Q2OV4FVC7A800146P
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDN6L4LMS87Q2OV4FVC79K001465
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDN6L4LMS87Q2OV4FVC79K001465
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDMMR23CHP7Q2OV4FVC79C00145T
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDMMR23CHP7Q2OV4FVC79C00145T
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDKDOANT3H7Q2OV4FVC78800144P
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDKDOANT3H7Q2OV4FVC78800144P
2025-06-09 08:04:23,258 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDJ5HVP5F47Q2OV4FVC77O001449
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDJ5HVP5F47Q2OV4FVC77O001449
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTE3QTM66G7Q2OVBN4IS8M001D4O
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTE3QTM66G7Q2OVBN4IS8M001D4O
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTD2B070E67Q2OVBN4IS86001D48
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTD2B070E67Q2OVBN4IS86001D48
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTCIR7D5JD7Q2OVBN4IS7U001D40
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTCIR7D5JD7Q2OVBN4IS7U001D40
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTAKS3N4EI7Q2OVBN4IS76001D38
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTAKS3N4EI7Q2OVBN4IS76001D38
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTA575PTKJ7Q2OVBN4IS72001D34
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTA575PTKJ7Q2OVBN4IS72001D34
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT72H2L6227Q2OVBN4IS62001D24
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT72H2L6227Q2OVBN4IS62001D24
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6QJG5GQO7Q2OVBN4IS5U001D20
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6QJG5GQO7Q2OVBN4IS5U001D20
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O
2025-06-09 08:04:23,274 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINSCOR6MVCC7Q2OV392411100148T
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINSCOR6MVCC7Q2OV392411100148T
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINSC23H1J3M7Q2OV392410L00148H
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINSC23H1J3M7Q2OV392410L00148H
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINPMIJNMLUJ7Q2OV392410G00148C
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINPMIJNMLUJ7Q2OV392410G00148C
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: A48FB8F8F66644F59454F3E73DFCEB92
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: A48FB8F8F66644F59454F3E73DFCEB92
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 7987833E6DE549FCBAC0AAF7A1D27E61
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 7987833E6DE549FCBAC0AAF7A1D27E61
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 6FEB527E4B354363BD1420A3FF0FB3E3
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 6FEB527E4B354363BD1420A3FF0FB3E3
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 6D1F9FC749FA44C6B70CA818C3E7FB77
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 6D1F9FC749FA44C6B70CA818C3E7FB77
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 48AFA71F437742278E0CD956382F1110
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 48AFA71F437742278E0CD956382F1110
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1I16HSQFKC90AM6QNN0HOT12RK0013M7
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1I16HSQFKC90AM6QNN0HOT12RK0013M7
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1I16GTCIOI81KU0UR9LEHSI4JM001PFU
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1I16GTCIOI81KU0UR9LEHSI4JM001PFU
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGETHMN6B8P42F6DB81RHC2001P8A
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGETHMN6B8P42F6DB81RHC2001P8A
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERRPAJGP042F6DB81RHB6001P7E
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERRPAJGP042F6DB81RHB6001P7E
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERL0RJ5BM42F6DB81RHB2001P7A
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERL0RJ5BM42F6DB81RHB2001P7A
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERF6AAOTL42F6DB81RHAU001P76
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERF6AAOTL42F6DB81RHAU001P76
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEQIR94JOQ42F6DB81RHAE001P6M
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEQIR94JOQ42F6DB81RHAE001P6M
2025-06-09 08:04:23,289 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEQ5682UCT42F6DB81RHA6001P6E
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEQ5682UCT42F6DB81RHA6001P6E
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEPVAL5TUK42F6DB81RHA2001P6A
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEPVAL5TUK42F6DB81RHA2001P6A
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEOTOKMGDD42F6DB81RH9E001P5M
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEOTOKMGDD42F6DB81RH9E001P5M
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEO8LGKTTH42F6DB81RH8V001P57
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEO8LGKTTH42F6DB81RH8V001P57
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEO2283NPC42F6DB81RH8Q001P52
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEMMCI75GD42F6DB81RH81001P49
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEMMCI75GD42F6DB81RH81001P49
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEMFCSIPU442F6DB81RH7T001P45
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEMFCSIPU442F6DB81RH7T001P45
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEM2RU37BD42F6DB81RH7L001P3T
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEM2RU37BD42F6DB81RH7L001P3T
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEKSIE6DGB42F6DB81RH77001P3F
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEKSIE6DGB42F6DB81RH77001P3F
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEKLLRRBCQ42F6DB81RH73001P3B
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEKLLRRBCQ42F6DB81RH73001P3B
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEJQAOTB2542F6DB81RH6O001P30
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEJQAOTB2542F6DB81RH6O001P30
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEJ92AGQAT42F6DB81RH6G001P2O
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEJ92AGQAT42F6DB81RH6G001P2O
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 17590B062D954DB088AC6EE572EFECE9
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 101F34500A0D43DF833463DEFB95F423
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 101F34500A0D43DF833463DEFB95F423
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: EC9758A692DF47FBA8F7C97344079C9E
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: EC9758A692DF47FBA8F7C97344079C9E
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: E214E9E9A4534AE1943BBACB09056E2E
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: E214E9E9A4534AE1943BBACB09056E2E
2025-06-09 08:04:23,305 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: B5A5FB25D4B04323BCABB528AF5E427E
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: B5A5FB25D4B04323BCABB528AF5E427E
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: B358872E0DBE420182AF77D4C47644F6
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: B358872E0DBE420182AF77D4C47644F6
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 6245DA9460784DDC85246900484DAA79
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 6245DA9460784DDC85246900484DAA79
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1IFRR231GUB4QN7QBECDAL3H28001J69
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1IFRR231GUB4QN7QBECDAL3H28001J69
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1IFRPJDJBMAPL77QBECDAL3H1H001J5I
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1IFRPJDJBMAPL77QBECDAL3H1H001J5I
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HO90F90E71NK12I1UUTD5AE7C001O7G
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HO90F90E71NK12I1UUTD5AE7C001O7G
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HLFG4AKMT96P82UAQ9ONTBKHO001HHS
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HLFG4AKMT96P82UAQ9ONTBKHO001HHS
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEC0U8246I0I86N3H2U10A001F9G
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEC0U8246I0I86N3H2U10A001F9G
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEBP5Q03GB0I86N3H2U106001F9C
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEBP5Q03GB0I86N3H2U106001F9C
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEBHLLHVNF0I86N3H2U102001F98
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEBHLLHVNF0I86N3H2U102001F98
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEB9O4F53S0I86N3H2U1VT001F93
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEB9O4F53S0I86N3H2U1VT001F93
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEAIN5DMKK0I86N3H2U1VH001F8N
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEAIN5DMKK0I86N3H2U1VH001F8N
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEAB2RFI0N0I86N3H2U1VD001F8J
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEAB2RFI0N0I86N3H2U1VD001F8J
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEA4041D6F0I86N3H2U1V9001F8F
2025-06-09 08:04:23,320 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEA4041D6F0I86N3H2U1V9001F8F
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE85EF5FB90I86N3H2U1U9001F7F
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE85EF5FB90I86N3H2U1U9001F7F
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE7UKVCV6D0I86N3H2U1U5001F7B
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE7UKVCV6D0I86N3H2U1U5001F7B
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE4GES1U770I86N3H2U1SF001F5L
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE4GES1U770I86N3H2U1SF001F5L
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE48KDQ6AS0I86N3H2U1SB001F5H
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE48KDQ6AS0I86N3H2U1SB001F5H
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE3PJ2TE9A0I86N3H2U1S3001F59
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE3PJ2TE9A0I86N3H2U1S3001F59
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE39GOJVP40I86N3H2U1RR001F51
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE39GOJVP40I86N3H2U1RR001F51
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE2CVHLBFV0I86N3H2U1RH001F4N
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE2CVHLBFV0I86N3H2U1RH001F4N
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE25DAIM3B0I86N3H2U1RD001F4J
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE25DAIM3B0I86N3H2U1RD001F4J
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE1T4E94FK0I86N3H2U1R9001F4F
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE1T4E94FK0I86N3H2U1R9001F4F
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE1EFF1UHG0I86N3H2U1R1001F47
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE1EFF1UHG0I86N3H2U1R1001F47
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE0A2N9KG60I86N3H2U1QB001F3H
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE0A2N9KG60I86N3H2U1QB001F3H
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVQHT7NSH0I86N3H2U1Q2001F38
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVQHT7NSH0I86N3H2U1Q2001F38
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVIE6UN1G0I86N3H2U1PU001F34
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVIE6UN1G0I86N3H2U1PU001F34
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVBILA3OJ0I86N3H2U1PQ001F30
2025-06-09 08:04:23,336 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVBILA3OJ0I86N3H2U1PQ001F30
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDUSSKMEM60I86N3H2U1PI001F2O
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDUSSKMEM60I86N3H2U1PI001F2O
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDPJQDDVRC0I86N3H2U1MO001EVU
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDPJQDDVRC0I86N3H2U1MO001EVU
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDP34FLR400I86N3H2U1MG001EVM
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDP34FLR400I86N3H2U1MG001EVM
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDOR4RUGJG0I86N3H2U1MC001EVI
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDOR4RUGJG0I86N3H2U1MC001EVI
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJP082LC00I86N3H2U1JM001ESS
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJGUIA2QR0I86N3H2U1JH001ESN
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJGUIA2QR0I86N3H2U1JH001ESN
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDG7SC9VNO0I86N3H2U1HP001EQV
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDG7SC9VNO0I86N3H2U1HP001EQV
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDF8HFHI690I86N3H2U1H9001EQF
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDF8HFHI690I86N3H2U1H9001EQF
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDF11AKIN30I86N3H2U1H5001EQB
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDF11AKIN30I86N3H2U1H5001EQB
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDEQ2M9E710I86N3H2U1H1001EQ7
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDEQ2M9E710I86N3H2U1H1001EQ7
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDEISQT8KE0I86N3H2U1GT001EQ3
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDEISQT8KE0I86N3H2U1GT001EQ3
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDCE3748SO0I86N3H2U1FP001EOV
2025-06-09 08:04:23,352 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDCE3748SO0I86N3H2U1FP001EOV
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDAAQ1NK8J0I86N3H2U1EL001ENR
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9H11376450I86N3H2U19G001EIM
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9GHTM38HU0I86N3H2U198001EIE
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9G31FV3GL0I86N3H2U190001EI6
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9G31FV3GL0I86N3H2U190001EI6
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9EN1GSFF80I86N3H2U18C001EHI
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9EG92S1SB0I86N3H2U188001EHE
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9EG92S1SB0I86N3H2U188001EHE
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9D9OBM41R0I86N3H2U17K001EGQ
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCE13J2R9CI0I86N3H2U13D001ECJ
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCE13J2R9CI0I86N3H2U13D001ECJ
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCE0JS3BF7R0I86N3H2U135001ECB
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCE0JS3BF7R0I86N3H2U135001ECB
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDVSS4V3460I86N3H2U12P001EBV
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDVSS4V3460I86N3H2U12P001EBV
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDVBEGSM760I86N3H2U12H001EBN
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDUDM4PLNS0I86N3H2U121001EB7
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDUDM4PLNS0I86N3H2U121001EB7
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDU5QKVU3D0I86N3H2U11T001EB3
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDU5QKVU3D0I86N3H2U11T001EB3
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDTTV5HD0Q0I86N3H2U11P001EAV
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDTTV5HD0Q0I86N3H2U11P001EAV
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDSK489TE20I86N3H2U114001EAA
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDSK489TE20I86N3H2U114001EAA
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDSC7N3PHM0I86N3H2U10T001EA3
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDSC7N3PHM0I86N3H2U10T001EA3
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDS341MMSU0I86N3H2U10P001E9V
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDS341MMSU0I86N3H2U10P001E9V
2025-06-09 08:04:23,367 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDRR6FJ7A60I86N3H2U10L001E9R
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDRR6FJ7A60I86N3H2U10L001E9R
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQQG9THS10I86N3H2U108001E9E
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQQG9THS10I86N3H2U108001E9E
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQJKO8O0D0I86N3H2U104001E9A
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQJKO8O0D0I86N3H2U104001E9A
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQ4GA7M630I86N3H2U1VS001E92
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQ4GA7M630I86N3H2U1VS001E92
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDOM98AG2E0I86N3H2U1V8001E8E
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDOM98AG2E0I86N3H2U1V8001E8E
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDNMK1P3900I86N3H2U1UO001E7U
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDNMK1P3900I86N3H2U1UO001E7U
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDN4LTS5B80I86N3H2U1UG001E7M
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDN4LTS5B80I86N3H2U1UG001E7M
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDMFCJQF4F0I86N3H2U1UC001E7I
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDMFCJQF4F0I86N3H2U1UC001E7I
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 3F059827C9E04DEAA6B50797867EC52B
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 3F059827C9E04DEAA6B50797867EC52B
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1HRQIR9VACRH722I1UUTD5AEGF001EGK
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1HRQIR9VACRH722I1UUTD5AEGF001EGK
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1GPFAB4PUHT7OL7Q2OV4FVC7US001R67
2025-06-09 08:04:23,383 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1GPFAB4PUHT7OL7Q2OV4FVC7US001R67
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MRVUM0P77G7Q2OV78BKOG4001PUK
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MRVUM0P77G7Q2OV78BKOG4001PUK
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MR50JEM3SR7Q2OVAE57DM4001Q85
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MR50JEM3SR7Q2OVAE57DM4001Q85
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MO2IE70S367Q2OVAE57DLH001Q7I
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MO2IE70S367Q2OVAE57DLH001Q7I
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0TQNGGEI53IKSIOCDI7U6001G57
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0TQNGGEI53IKSIOCDI7U6001G57
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0NLURMV9D3IKSIOCDI7R2001G23
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0NLURMV9D3IKSIOCDI7R2001G23
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0LAK18ROU3IKSIOCDI7Q6001G17
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0LAK18ROU3IKSIOCDI7Q6001G17
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0J21PQQ9M3IKSIOCDI7P2001G03
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI
2025-06-09 08:04:23,399 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0A4NM87423IKSIOCDI7KU001FRV
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0A4NM87423IKSIOCDI7KU001FRV
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ08R852VK83IKSIOCDI7KE001FRF
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ08R852VK83IKSIOCDI7KE001FRF
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE3GMAAFL56AJB6QM8HA860011R4
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE3GMAAFL56AJB6QM8HA860011R4
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE38FF3LOL6AJB6QM8HA820011R0
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE38FF3LOL6AJB6QM8HA820011R0
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE2PLB45826AJB6QM8HA7Q0011QO
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE2PLB45826AJB6QM8HA7Q0011QO
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE29O5UH0D6AJB6QM8HA7I0011QG
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE29O5UH0D6AJB6QM8HA7I0011QG
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRD2F2MCTBD6AJB6QM8HA650011P3
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRD2F2MCTBD6AJB6QM8HA650011P3
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRD0GVFB5C86AJB6QM8HA590011O7
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRD0GVFB5C86AJB6QM8HA590011O7
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: E79F261889C1492982227C207062C267
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: E79F261889C1492982227C207062C267
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: C207460918D74AAAB2E154B47B74F863
2025-06-09 08:04:23,414 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: C207460918D74AAAB2E154B47B74F863
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 41D3E4ED4CEA49C09C36DE504B997534
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 41D3E4ED4CEA49C09C36DE504B997534
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUISOVAPU1P7AV8LHQQGIDU001EK7
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUISOVAPU1P7AV8LHQQGIDU001EK7
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUISLI3BD9P7AV8LHQQGIDR001EK4
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUISLI3BD9P7AV8LHQQGIDR001EK4
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIS7OTCALF7AV8LHQQGIDO001EK1
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIS7OTCALF7AV8LHQQGIDO001EK1
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIS34G4B697AV8LHQQGIDL001EJU
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIS34G4B697AV8LHQQGIDL001EJU
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRL782OM57AV8LHQQGIDF001EJO
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRL782OM57AV8LHQQGIDF001EJO
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRGKS2JA27AV8LHQQGIDC001EJL
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRGKS2JA27AV8LHQQGIDC001EJL
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRBVE2CQT7AV8LHQQGID9001EJI
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRBVE2CQT7AV8LHQQGID9001EJI
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIR7B0BL957AV8LHQQGID6001EJF
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIR7B0BL957AV8LHQQGID6001EJF
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIR2ETOHPO6E7AERKQ83K0001UO2
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIR2ETOHPO6E7AERKQ83K0001UO2
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQTRHD5AN6E7AERKQ83JT001UNV
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQTRHD5AN6E7AERKQ83JT001UNV
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQP8527T06E7AERKQ83JQ001UNS
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQP8527T06E7AERKQ83JQ001UNS
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQG1BD1C36E7AERKQ83JN001UNP
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQG1BD1C36E7AERKQ83JN001UNP
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQ716B84L7AV8LHQQGID0001EJ9
2025-06-09 08:04:23,430 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQ716B84L7AV8LHQQGID0001EJ9
2025-06-09 08:04:23,445 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPOL2K5B16E7AERKQ83JE001UNG
2025-06-09 08:04:23,445 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPOL2K5B16E7AERKQ83JE001UNG
2025-06-09 08:04:23,445 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPK0KE3MN6E7AERKQ83JB001UND
2025-06-09 08:04:23,445 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPK0KE3MN6E7AERKQ83JB001UND
2025-06-09 08:04:23,445 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPGJC2SUF6E7AERKQ83J8001UNA
2025-06-09 08:04:23,445 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPGJC2SUF6E7AERKQ83J8001UNA
2025-06-09 08:04:23,445 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPC0VTE5M6E7AERKQ83J5001UN7
2025-06-09 08:04:23,445 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPC0VTE5M6E7AERKQ83J5001UN7
2025-06-09 08:04:23,445 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIP3GSKTFR6E7AERKQ83J2001UN4
2025-06-09 08:04:23,445 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIP3GSKTFR6E7AERKQ83J2001UN4
2025-06-09 08:04:23,445 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 14EB204A0BDE44888B43308269C1626A
2025-06-09 08:04:23,445 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 14EB204A0BDE44888B43308269C1626A
2025-06-09 08:04:28,086 - INFO - 更新表单数据成功: FINST-XMC66R919C3WDD4OC4X5L8NF6SG92YR1PONBMGM
2025-06-09 08:04:28,086 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8236.5}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8236.5}]
2025-06-09 08:04:28,477 - INFO - 更新表单数据成功: FINST-2FD66I715C2W6DAT9TWWM5SF8E9M3PE4PONBMCD
2025-06-09 08:04:28,477 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5916.82}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5916.82}]
2025-06-09 08:04:28,945 - INFO - 更新表单数据成功: FINST-2FD66I715C2W6DAT9TWWM5SF8E9M3QE4PONBMEE
2025-06-09 08:04:28,945 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1789.55, 'new_value': 1799.97}, {'field': 'amount', 'old_value': 1789.5500000000002, 'new_value': 1799.97}, {'field': 'instoreAmount', 'old_value': 1822.88, 'new_value': 1833.3}]
2025-06-09 08:04:29,383 - INFO - 更新表单数据成功: FINST-2FD66I715C2W6DAT9TWWM5SF8E9M3QE4PONBMIF
2025-06-09 08:04:29,383 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 34386.75}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 34386.75}]
2025-06-09 08:04:29,899 - INFO - 更新表单数据成功: FINST-3PF66O71LS2W4I4JC725OCYBXUW32W17PONBMCX
2025-06-09 08:04:29,899 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_********, 变更字段: [{'field': 'amount', 'old_value': 3404.19, 'new_value': 3487.79}, {'field': 'count', 'old_value': 37, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 3058.69, 'new_value': 3142.29}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 32}]
2025-06-09 08:04:30,149 - INFO - 正在批量插入每日数据，批次 1/64，共 100 条记录
2025-06-09 08:04:30,680 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-06-09 08:04:33,695 - INFO - 正在批量插入每日数据，批次 2/64，共 100 条记录
2025-06-09 08:04:34,070 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-06-09 08:04:37,086 - INFO - 正在批量插入每日数据，批次 3/64，共 100 条记录
2025-06-09 08:04:37,617 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-06-09 08:04:40,633 - INFO - 正在批量插入每日数据，批次 4/64，共 100 条记录
2025-06-09 08:04:41,070 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-06-09 08:04:44,086 - INFO - 正在批量插入每日数据，批次 5/64，共 100 条记录
2025-06-09 08:04:44,508 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-06-09 08:04:47,523 - INFO - 正在批量插入每日数据，批次 6/64，共 100 条记录
2025-06-09 08:04:48,023 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-06-09 08:04:51,039 - INFO - 正在批量插入每日数据，批次 7/64，共 100 条记录
2025-06-09 08:04:51,523 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-06-09 08:04:54,539 - INFO - 正在批量插入每日数据，批次 8/64，共 100 条记录
2025-06-09 08:04:55,055 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-06-09 08:04:58,070 - INFO - 正在批量插入每日数据，批次 9/64，共 100 条记录
2025-06-09 08:04:58,539 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-06-09 08:05:01,555 - INFO - 正在批量插入每日数据，批次 10/64，共 100 条记录
2025-06-09 08:05:02,023 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-06-09 08:05:05,039 - INFO - 正在批量插入每日数据，批次 11/64，共 100 条记录
2025-06-09 08:05:05,508 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-06-09 08:05:08,523 - INFO - 正在批量插入每日数据，批次 12/64，共 100 条记录
2025-06-09 08:05:08,930 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-06-09 08:05:11,945 - INFO - 正在批量插入每日数据，批次 13/64，共 100 条记录
2025-06-09 08:05:12,508 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-06-09 08:05:15,523 - INFO - 正在批量插入每日数据，批次 14/64，共 100 条记录
2025-06-09 08:05:15,945 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-06-09 08:05:18,961 - INFO - 正在批量插入每日数据，批次 15/64，共 100 条记录
2025-06-09 08:05:19,476 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-06-09 08:05:22,492 - INFO - 正在批量插入每日数据，批次 16/64，共 100 条记录
2025-06-09 08:05:22,929 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-06-09 08:05:25,929 - INFO - 正在批量插入每日数据，批次 17/64，共 100 条记录
2025-06-09 08:05:26,367 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-06-09 08:05:29,383 - INFO - 正在批量插入每日数据，批次 18/64，共 100 条记录
2025-06-09 08:05:29,883 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-06-09 08:05:32,898 - INFO - 正在批量插入每日数据，批次 19/64，共 100 条记录
2025-06-09 08:05:33,304 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-06-09 08:05:36,320 - INFO - 正在批量插入每日数据，批次 20/64，共 100 条记录
2025-06-09 08:05:36,726 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-06-09 08:05:39,742 - INFO - 正在批量插入每日数据，批次 21/64，共 100 条记录
2025-06-09 08:05:40,148 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-06-09 08:05:43,164 - INFO - 正在批量插入每日数据，批次 22/64，共 100 条记录
2025-06-09 08:05:43,648 - INFO - 批量插入每日数据成功，批次 22，100 条记录
2025-06-09 08:05:46,664 - INFO - 正在批量插入每日数据，批次 23/64，共 100 条记录
2025-06-09 08:05:47,070 - INFO - 批量插入每日数据成功，批次 23，100 条记录
2025-06-09 08:05:50,101 - INFO - 正在批量插入每日数据，批次 24/64，共 100 条记录
2025-06-09 08:05:50,570 - INFO - 批量插入每日数据成功，批次 24，100 条记录
2025-06-09 08:05:53,585 - INFO - 正在批量插入每日数据，批次 25/64，共 100 条记录
2025-06-09 08:05:54,054 - INFO - 批量插入每日数据成功，批次 25，100 条记录
2025-06-09 08:05:57,070 - INFO - 正在批量插入每日数据，批次 26/64，共 100 条记录
2025-06-09 08:05:57,648 - INFO - 批量插入每日数据成功，批次 26，100 条记录
2025-06-09 08:06:00,664 - INFO - 正在批量插入每日数据，批次 27/64，共 100 条记录
2025-06-09 08:06:01,101 - INFO - 批量插入每日数据成功，批次 27，100 条记录
2025-06-09 08:06:04,117 - INFO - 正在批量插入每日数据，批次 28/64，共 100 条记录
2025-06-09 08:06:04,585 - INFO - 批量插入每日数据成功，批次 28，100 条记录
2025-06-09 08:06:07,601 - INFO - 正在批量插入每日数据，批次 29/64，共 100 条记录
2025-06-09 08:06:08,054 - INFO - 批量插入每日数据成功，批次 29，100 条记录
2025-06-09 08:06:11,070 - INFO - 正在批量插入每日数据，批次 30/64，共 100 条记录
2025-06-09 08:06:11,538 - INFO - 批量插入每日数据成功，批次 30，100 条记录
2025-06-09 08:06:14,554 - INFO - 正在批量插入每日数据，批次 31/64，共 100 条记录
2025-06-09 08:06:14,976 - INFO - 批量插入每日数据成功，批次 31，100 条记录
2025-06-09 08:06:17,992 - INFO - 正在批量插入每日数据，批次 32/64，共 100 条记录
2025-06-09 08:06:18,476 - INFO - 批量插入每日数据成功，批次 32，100 条记录
2025-06-09 08:06:21,492 - INFO - 正在批量插入每日数据，批次 33/64，共 100 条记录
2025-06-09 08:06:21,960 - INFO - 批量插入每日数据成功，批次 33，100 条记录
2025-06-09 08:06:24,976 - INFO - 正在批量插入每日数据，批次 34/64，共 100 条记录
2025-06-09 08:06:25,460 - INFO - 批量插入每日数据成功，批次 34，100 条记录
2025-06-09 08:06:28,476 - INFO - 正在批量插入每日数据，批次 35/64，共 100 条记录
2025-06-09 08:06:28,976 - INFO - 批量插入每日数据成功，批次 35，100 条记录
2025-06-09 08:06:31,992 - INFO - 正在批量插入每日数据，批次 36/64，共 100 条记录
2025-06-09 08:06:32,507 - INFO - 批量插入每日数据成功，批次 36，100 条记录
2025-06-09 08:06:35,523 - INFO - 正在批量插入每日数据，批次 37/64，共 100 条记录
2025-06-09 08:06:35,945 - INFO - 批量插入每日数据成功，批次 37，100 条记录
2025-06-09 08:06:38,960 - INFO - 正在批量插入每日数据，批次 38/64，共 100 条记录
2025-06-09 08:06:39,382 - INFO - 批量插入每日数据成功，批次 38，100 条记录
2025-06-09 08:06:42,398 - INFO - 正在批量插入每日数据，批次 39/64，共 100 条记录
2025-06-09 08:06:42,804 - INFO - 批量插入每日数据成功，批次 39，100 条记录
2025-06-09 08:06:45,820 - INFO - 正在批量插入每日数据，批次 40/64，共 100 条记录
2025-06-09 08:06:46,241 - INFO - 批量插入每日数据成功，批次 40，100 条记录
2025-06-09 08:06:49,257 - INFO - 正在批量插入每日数据，批次 41/64，共 100 条记录
2025-06-09 08:06:49,773 - INFO - 批量插入每日数据成功，批次 41，100 条记录
2025-06-09 08:06:52,788 - INFO - 正在批量插入每日数据，批次 42/64，共 100 条记录
2025-06-09 08:06:53,273 - INFO - 批量插入每日数据成功，批次 42，100 条记录
2025-06-09 08:06:56,288 - INFO - 正在批量插入每日数据，批次 43/64，共 100 条记录
2025-06-09 08:06:56,788 - INFO - 批量插入每日数据成功，批次 43，100 条记录
2025-06-09 08:06:59,804 - INFO - 正在批量插入每日数据，批次 44/64，共 100 条记录
2025-06-09 08:07:00,241 - INFO - 批量插入每日数据成功，批次 44，100 条记录
2025-06-09 08:07:03,257 - INFO - 正在批量插入每日数据，批次 45/64，共 100 条记录
2025-06-09 08:07:03,616 - INFO - 批量插入每日数据成功，批次 45，100 条记录
2025-06-09 08:07:06,632 - INFO - 正在批量插入每日数据，批次 46/64，共 100 条记录
2025-06-09 08:07:07,116 - INFO - 批量插入每日数据成功，批次 46，100 条记录
2025-06-09 08:07:10,132 - INFO - 正在批量插入每日数据，批次 47/64，共 100 条记录
2025-06-09 08:07:10,554 - INFO - 批量插入每日数据成功，批次 47，100 条记录
2025-06-09 08:07:13,569 - INFO - 正在批量插入每日数据，批次 48/64，共 100 条记录
2025-06-09 08:07:14,038 - INFO - 批量插入每日数据成功，批次 48，100 条记录
2025-06-09 08:07:17,054 - INFO - 正在批量插入每日数据，批次 49/64，共 100 条记录
2025-06-09 08:07:17,507 - INFO - 批量插入每日数据成功，批次 49，100 条记录
2025-06-09 08:07:20,507 - INFO - 正在批量插入每日数据，批次 50/64，共 100 条记录
2025-06-09 08:07:21,007 - INFO - 批量插入每日数据成功，批次 50，100 条记录
2025-06-09 08:07:24,022 - INFO - 正在批量插入每日数据，批次 51/64，共 100 条记录
2025-06-09 08:07:24,382 - INFO - 批量插入每日数据成功，批次 51，100 条记录
2025-06-09 08:07:27,397 - INFO - 正在批量插入每日数据，批次 52/64，共 100 条记录
2025-06-09 08:07:27,772 - INFO - 批量插入每日数据成功，批次 52，100 条记录
2025-06-09 08:07:30,788 - INFO - 正在批量插入每日数据，批次 53/64，共 100 条记录
2025-06-09 08:07:31,241 - INFO - 批量插入每日数据成功，批次 53，100 条记录
2025-06-09 08:07:34,257 - INFO - 正在批量插入每日数据，批次 54/64，共 100 条记录
2025-06-09 08:07:34,741 - INFO - 批量插入每日数据成功，批次 54，100 条记录
2025-06-09 08:07:37,757 - INFO - 正在批量插入每日数据，批次 55/64，共 100 条记录
2025-06-09 08:07:38,225 - INFO - 批量插入每日数据成功，批次 55，100 条记录
2025-06-09 08:07:41,241 - INFO - 正在批量插入每日数据，批次 56/64，共 100 条记录
2025-06-09 08:07:41,741 - INFO - 批量插入每日数据成功，批次 56，100 条记录
2025-06-09 08:07:44,757 - INFO - 正在批量插入每日数据，批次 57/64，共 100 条记录
2025-06-09 08:07:45,116 - INFO - 批量插入每日数据成功，批次 57，100 条记录
2025-06-09 08:07:48,132 - INFO - 正在批量插入每日数据，批次 58/64，共 100 条记录
2025-06-09 08:07:48,600 - INFO - 批量插入每日数据成功，批次 58，100 条记录
2025-06-09 08:07:51,616 - INFO - 正在批量插入每日数据，批次 59/64，共 100 条记录
2025-06-09 08:07:52,007 - INFO - 批量插入每日数据成功，批次 59，100 条记录
2025-06-09 08:07:55,022 - INFO - 正在批量插入每日数据，批次 60/64，共 100 条记录
2025-06-09 08:07:55,507 - INFO - 批量插入每日数据成功，批次 60，100 条记录
2025-06-09 08:07:58,522 - INFO - 正在批量插入每日数据，批次 61/64，共 100 条记录
2025-06-09 08:07:59,085 - INFO - 批量插入每日数据成功，批次 61，100 条记录
2025-06-09 08:08:02,100 - INFO - 正在批量插入每日数据，批次 62/64，共 100 条记录
2025-06-09 08:08:02,522 - INFO - 批量插入每日数据成功，批次 62，100 条记录
2025-06-09 08:08:05,538 - INFO - 正在批量插入每日数据，批次 63/64，共 100 条记录
2025-06-09 08:08:06,007 - INFO - 批量插入每日数据成功，批次 63，100 条记录
2025-06-09 08:08:09,038 - INFO - 正在批量插入每日数据，批次 64/64，共 9 条记录
2025-06-09 08:08:09,210 - INFO - 批量插入每日数据成功，批次 64，9 条记录
2025-06-09 08:08:12,225 - INFO - 批量插入每日数据完成: 总计 6309 条，成功 6309 条，失败 0 条
2025-06-09 08:08:12,225 - INFO - 批量插入日销售数据完成，共 6309 条记录
2025-06-09 08:08:12,225 - INFO - 日销售数据同步完成！更新: 5 条，插入: 6309 条，错误: 0 条，跳过: 6530 条
2025-06-09 08:08:12,225 - INFO - 正在获取宜搭月销售表单数据...
2025-06-09 08:08:12,225 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-06-01 至 2025-06-30
2025-06-09 08:08:12,225 - INFO - 查询月度分段 1: 2024-06-01 至 2024-08-31
2025-06-09 08:08:12,225 - INFO - 查询日期范围: 2024-06-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-09 08:08:12,225 - INFO - Request Parameters - Page 1:
2025-06-09 08:08:12,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:12,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:12,819 - INFO - API请求耗时: 594ms
2025-06-09 08:08:12,819 - INFO - Response - Page 1
2025-06-09 08:08:12,819 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-09 08:08:12,819 - INFO - 查询完成，共获取到 0 条记录
2025-06-09 08:08:12,819 - WARNING - 月度分段 1 查询返回空数据
2025-06-09 08:08:12,819 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-06-09 08:08:12,819 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-06-09 08:08:12,819 - INFO - Request Parameters - Page 1:
2025-06-09 08:08:12,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:12,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:13,319 - INFO - API请求耗时: 500ms
2025-06-09 08:08:13,319 - INFO - Response - Page 1
2025-06-09 08:08:13,319 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-09 08:08:13,319 - INFO - 查询完成，共获取到 0 条记录
2025-06-09 08:08:13,319 - WARNING - 单月查询返回空数据: 2024-06
2025-06-09 08:08:13,819 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-06-09 08:08:13,819 - INFO - Request Parameters - Page 1:
2025-06-09 08:08:13,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:13,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:14,022 - INFO - API请求耗时: 203ms
2025-06-09 08:08:14,022 - INFO - Response - Page 1
2025-06-09 08:08:14,022 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-09 08:08:14,022 - INFO - 查询完成，共获取到 0 条记录
2025-06-09 08:08:14,022 - WARNING - 单月查询返回空数据: 2024-07
2025-06-09 08:08:14,538 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-09 08:08:14,538 - INFO - Request Parameters - Page 1:
2025-06-09 08:08:14,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:14,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:14,756 - INFO - API请求耗时: 219ms
2025-06-09 08:08:14,756 - INFO - Response - Page 1
2025-06-09 08:08:14,756 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-09 08:08:14,756 - INFO - 查询完成，共获取到 0 条记录
2025-06-09 08:08:14,756 - WARNING - 单月查询返回空数据: 2024-08
2025-06-09 08:08:16,256 - INFO - 查询月度分段 2: 2024-09-01 至 2024-11-30
2025-06-09 08:08:16,256 - INFO - 查询日期范围: 2024-09-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-09 08:08:16,256 - INFO - Request Parameters - Page 1:
2025-06-09 08:08:16,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:16,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:16,460 - INFO - API请求耗时: 203ms
2025-06-09 08:08:16,460 - INFO - Response - Page 1
2025-06-09 08:08:16,460 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-09 08:08:16,460 - INFO - 查询完成，共获取到 0 条记录
2025-06-09 08:08:16,460 - WARNING - 月度分段 2 查询返回空数据
2025-06-09 08:08:16,460 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-06-09 08:08:16,460 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-06-09 08:08:16,460 - INFO - Request Parameters - Page 1:
2025-06-09 08:08:16,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:16,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:16,678 - INFO - API请求耗时: 219ms
2025-06-09 08:08:16,678 - INFO - Response - Page 1
2025-06-09 08:08:16,678 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-09 08:08:16,678 - INFO - 查询完成，共获取到 0 条记录
2025-06-09 08:08:16,678 - WARNING - 单月查询返回空数据: 2024-09
2025-06-09 08:08:17,178 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-06-09 08:08:17,178 - INFO - Request Parameters - Page 1:
2025-06-09 08:08:17,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:17,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:17,397 - INFO - API请求耗时: 219ms
2025-06-09 08:08:17,397 - INFO - Response - Page 1
2025-06-09 08:08:17,397 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-09 08:08:17,397 - INFO - 查询完成，共获取到 0 条记录
2025-06-09 08:08:17,397 - WARNING - 单月查询返回空数据: 2024-10
2025-06-09 08:08:17,913 - INFO - 查询日期范围: 2024-11-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-09 08:08:17,913 - INFO - Request Parameters - Page 1:
2025-06-09 08:08:17,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:17,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:18,147 - INFO - API请求耗时: 234ms
2025-06-09 08:08:18,147 - INFO - Response - Page 1
2025-06-09 08:08:18,147 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-09 08:08:18,147 - INFO - 查询完成，共获取到 0 条记录
2025-06-09 08:08:18,147 - WARNING - 单月查询返回空数据: 2024-11
2025-06-09 08:08:19,663 - INFO - 查询月度分段 3: 2024-12-01 至 2025-02-28
2025-06-09 08:08:19,663 - INFO - 查询日期范围: 2024-12-01 至 2025-02-28，使用分页查询，每页 100 条记录
2025-06-09 08:08:19,663 - INFO - Request Parameters - Page 1:
2025-06-09 08:08:19,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:19,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:20,241 - INFO - API请求耗时: 578ms
2025-06-09 08:08:20,241 - INFO - Response - Page 1
2025-06-09 08:08:20,241 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:08:20,741 - INFO - Request Parameters - Page 2:
2025-06-09 08:08:20,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:20,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:21,225 - INFO - API请求耗时: 484ms
2025-06-09 08:08:21,225 - INFO - Response - Page 2
2025-06-09 08:08:21,225 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:08:21,741 - INFO - Request Parameters - Page 3:
2025-06-09 08:08:21,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:21,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:22,319 - INFO - API请求耗时: 578ms
2025-06-09 08:08:22,319 - INFO - Response - Page 3
2025-06-09 08:08:22,319 - INFO - 第 3 页获取到 100 条记录
2025-06-09 08:08:22,835 - INFO - Request Parameters - Page 4:
2025-06-09 08:08:22,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:22,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:23,475 - INFO - API请求耗时: 641ms
2025-06-09 08:08:23,475 - INFO - Response - Page 4
2025-06-09 08:08:23,475 - INFO - 第 4 页获取到 100 条记录
2025-06-09 08:08:23,991 - INFO - Request Parameters - Page 5:
2025-06-09 08:08:23,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:23,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:24,553 - INFO - API请求耗时: 562ms
2025-06-09 08:08:24,569 - INFO - Response - Page 5
2025-06-09 08:08:24,569 - INFO - 第 5 页获取到 94 条记录
2025-06-09 08:08:24,569 - INFO - 查询完成，共获取到 494 条记录
2025-06-09 08:08:24,569 - INFO - 月度分段 3 查询成功，获取到 494 条记录
2025-06-09 08:08:25,569 - INFO - 查询月度分段 4: 2025-03-01 至 2025-05-31
2025-06-09 08:08:25,569 - INFO - 查询日期范围: 2025-03-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-09 08:08:25,569 - INFO - Request Parameters - Page 1:
2025-06-09 08:08:25,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:25,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:26,459 - INFO - API请求耗时: 891ms
2025-06-09 08:08:26,459 - INFO - Response - Page 1
2025-06-09 08:08:26,459 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:08:26,959 - INFO - Request Parameters - Page 2:
2025-06-09 08:08:26,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:26,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:27,553 - INFO - API请求耗时: 594ms
2025-06-09 08:08:27,553 - INFO - Response - Page 2
2025-06-09 08:08:27,553 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:08:28,053 - INFO - Request Parameters - Page 3:
2025-06-09 08:08:28,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:28,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:28,569 - INFO - API请求耗时: 516ms
2025-06-09 08:08:28,569 - INFO - Response - Page 3
2025-06-09 08:08:28,569 - INFO - 第 3 页获取到 100 条记录
2025-06-09 08:08:29,069 - INFO - Request Parameters - Page 4:
2025-06-09 08:08:29,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:29,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:29,600 - INFO - API请求耗时: 531ms
2025-06-09 08:08:29,600 - INFO - Response - Page 4
2025-06-09 08:08:29,600 - INFO - 第 4 页获取到 100 条记录
2025-06-09 08:08:30,100 - INFO - Request Parameters - Page 5:
2025-06-09 08:08:30,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:30,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:30,709 - INFO - API请求耗时: 609ms
2025-06-09 08:08:30,709 - INFO - Response - Page 5
2025-06-09 08:08:30,709 - INFO - 第 5 页获取到 100 条记录
2025-06-09 08:08:31,209 - INFO - Request Parameters - Page 6:
2025-06-09 08:08:31,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:31,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:31,709 - INFO - API请求耗时: 500ms
2025-06-09 08:08:31,709 - INFO - Response - Page 6
2025-06-09 08:08:31,709 - INFO - 第 6 页获取到 100 条记录
2025-06-09 08:08:32,209 - INFO - Request Parameters - Page 7:
2025-06-09 08:08:32,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:32,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:32,756 - INFO - API请求耗时: 547ms
2025-06-09 08:08:32,756 - INFO - Response - Page 7
2025-06-09 08:08:32,756 - INFO - 第 7 页获取到 98 条记录
2025-06-09 08:08:32,756 - INFO - 查询完成，共获取到 698 条记录
2025-06-09 08:08:32,756 - INFO - 月度分段 4 查询成功，获取到 698 条记录
2025-06-09 08:08:33,772 - INFO - 查询月度分段 5: 2025-06-01 至 2025-06-30
2025-06-09 08:08:33,772 - INFO - 查询日期范围: 2025-06-01 至 2025-06-30，使用分页查询，每页 100 条记录
2025-06-09 08:08:33,772 - INFO - Request Parameters - Page 1:
2025-06-09 08:08:33,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:33,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:34,288 - INFO - API请求耗时: 516ms
2025-06-09 08:08:34,288 - INFO - Response - Page 1
2025-06-09 08:08:34,288 - INFO - 第 1 页获取到 100 条记录
2025-06-09 08:08:34,788 - INFO - Request Parameters - Page 2:
2025-06-09 08:08:34,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:34,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:35,334 - INFO - API请求耗时: 547ms
2025-06-09 08:08:35,334 - INFO - Response - Page 2
2025-06-09 08:08:35,334 - INFO - 第 2 页获取到 100 条记录
2025-06-09 08:08:35,834 - INFO - Request Parameters - Page 3:
2025-06-09 08:08:35,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f564514ef7c032e7b0cbaebe0f8a85f9'}
2025-06-09 08:08:35,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-09 08:08:36,116 - INFO - API请求耗时: 281ms
2025-06-09 08:08:36,131 - INFO - Response - Page 3
2025-06-09 08:08:36,131 - INFO - 第 3 页获取到 11 条记录
2025-06-09 08:08:36,131 - INFO - 查询完成，共获取到 211 条记录
2025-06-09 08:08:36,131 - INFO - 月度分段 5 查询成功，获取到 211 条记录
2025-06-09 08:08:37,147 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1403 条记录，失败 0 次
2025-06-09 08:08:37,147 - INFO - 成功获取宜搭月销售表单数据，共 1403 条记录
2025-06-09 08:08:37,147 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-09 08:08:37,147 - INFO - 正在从MySQL获取月度汇总数据...
2025-06-09 08:08:37,194 - INFO - 成功获取MySQL月度汇总数据，共 1403 条记录
2025-06-09 08:08:37,819 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250609.xlsx
2025-06-09 08:08:37,897 - INFO - 成功创建宜搭月销售数据索引，共 1403 条记录
2025-06-09 08:08:37,913 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:38,413 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4I
2025-06-09 08:08:38,413 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 195644.57, 'new_value': 218725.95}, {'field': 'dailyBillAmount', 'old_value': 195644.57, 'new_value': 218725.95}, {'field': 'amount', 'old_value': 147798.7, 'new_value': 163700.75}, {'field': 'count', 'old_value': 712, 'new_value': 810}, {'field': 'instoreAmount', 'old_value': 147798.7, 'new_value': 163700.75}, {'field': 'instoreCount', 'old_value': 712, 'new_value': 810}]
2025-06-09 08:08:38,413 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:38,866 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5I
2025-06-09 08:08:38,866 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 138940.95, 'new_value': 165258.24}, {'field': 'dailyBillAmount', 'old_value': 138940.95, 'new_value': 165258.24}, {'field': 'amount', 'old_value': 216160.0, 'new_value': 250766.0}, {'field': 'count', 'old_value': 776, 'new_value': 902}, {'field': 'instoreAmount', 'old_value': 214965.0, 'new_value': 249571.0}, {'field': 'instoreCount', 'old_value': 770, 'new_value': 896}]
2025-06-09 08:08:38,866 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:39,381 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6I
2025-06-09 08:08:39,381 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14214.1, 'new_value': 15877.8}, {'field': 'dailyBillAmount', 'old_value': 14214.1, 'new_value': 15877.8}, {'field': 'amount', 'old_value': 16695.6, 'new_value': 19957.1}, {'field': 'count', 'old_value': 86, 'new_value': 93}, {'field': 'instoreAmount', 'old_value': 7906.9, 'new_value': 9396.9}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 9}, {'field': 'onlineAmount', 'old_value': 9176.7, 'new_value': 12531.6}, {'field': 'onlineCount', 'old_value': 78, 'new_value': 84}]
2025-06-09 08:08:39,381 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:39,788 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7I
2025-06-09 08:08:39,788 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28570.1, 'new_value': 38450.1}, {'field': 'amount', 'old_value': 28570.1, 'new_value': 38450.1}, {'field': 'count', 'old_value': 33, 'new_value': 40}, {'field': 'instoreAmount', 'old_value': 28570.1, 'new_value': 38450.1}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 40}]
2025-06-09 08:08:39,788 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:40,194 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9I
2025-06-09 08:08:40,194 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 179120.12, 'new_value': 209798.07}, {'field': 'dailyBillAmount', 'old_value': 179120.12, 'new_value': 209798.07}, {'field': 'amount', 'old_value': 35070.37, 'new_value': 40184.37}, {'field': 'count', 'old_value': 178, 'new_value': 208}, {'field': 'instoreAmount', 'old_value': 35070.37, 'new_value': 40184.37}, {'field': 'instoreCount', 'old_value': 178, 'new_value': 208}]
2025-06-09 08:08:40,194 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:40,709 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8I
2025-06-09 08:08:40,709 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 200585.47, 'new_value': 232370.27}, {'field': 'dailyBillAmount', 'old_value': 200585.47, 'new_value': 232370.27}, {'field': 'amount', 'old_value': 122203.53, 'new_value': 140377.57}, {'field': 'count', 'old_value': 873, 'new_value': 989}, {'field': 'instoreAmount', 'old_value': 110509.19, 'new_value': 126837.23}, {'field': 'instoreCount', 'old_value': 475, 'new_value': 542}, {'field': 'onlineAmount', 'old_value': 13703.95, 'new_value': 15769.45}, {'field': 'onlineCount', 'old_value': 398, 'new_value': 447}]
2025-06-09 08:08:40,709 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:41,241 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1I
2025-06-09 08:08:41,241 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 59234.69, 'new_value': 67744.59}, {'field': 'dailyBillAmount', 'old_value': 59234.69, 'new_value': 67744.59}]
2025-06-09 08:08:41,241 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:41,756 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2I
2025-06-09 08:08:41,756 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 137971.71, 'new_value': 164590.5}, {'field': 'dailyBillAmount', 'old_value': 137971.71, 'new_value': 164590.5}, {'field': 'amount', 'old_value': 79316.6, 'new_value': 90424.9}, {'field': 'count', 'old_value': 721, 'new_value': 817}, {'field': 'instoreAmount', 'old_value': 37069.0, 'new_value': 42765.6}, {'field': 'instoreCount', 'old_value': 286, 'new_value': 332}, {'field': 'onlineAmount', 'old_value': 42308.5, 'new_value': 47720.2}, {'field': 'onlineCount', 'old_value': 435, 'new_value': 485}]
2025-06-09 08:08:41,756 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:42,194 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3I
2025-06-09 08:08:42,194 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 85862.52, 'new_value': 101049.17}, {'field': 'dailyBillAmount', 'old_value': 85862.52, 'new_value': 101049.17}, {'field': 'amount', 'old_value': 86892.64, 'new_value': 102396.24}, {'field': 'count', 'old_value': 561, 'new_value': 653}, {'field': 'instoreAmount', 'old_value': 80649.63, 'new_value': 94462.13}, {'field': 'instoreCount', 'old_value': 472, 'new_value': 546}, {'field': 'onlineAmount', 'old_value': 6305.91, 'new_value': 8063.01}, {'field': 'onlineCount', 'old_value': 89, 'new_value': 107}]
2025-06-09 08:08:42,288 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-09 08:08:42,772 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS22U0NXX9MRH
2025-06-09 08:08:42,772 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-04, 变更字段: [{'field': 'amount', 'old_value': 17849.86, 'new_value': 17848.86}]
2025-06-09 08:08:42,803 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:43,241 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUJ
2025-06-09 08:08:43,241 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 145510.69, 'new_value': 179943.69}, {'field': 'dailyBillAmount', 'old_value': 145510.69, 'new_value': 179943.69}, {'field': 'amount', 'old_value': 247944.33000000002, 'new_value': 277285.47}, {'field': 'count', 'old_value': 328, 'new_value': 369}, {'field': 'instoreAmount', 'old_value': 247944.33000000002, 'new_value': 277285.47}, {'field': 'instoreCount', 'old_value': 328, 'new_value': 369}]
2025-06-09 08:08:43,241 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:43,694 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTJ
2025-06-09 08:08:43,694 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 97198.64, 'new_value': 117533.64}, {'field': 'dailyBillAmount', 'old_value': 97198.64, 'new_value': 117533.64}, {'field': 'amount', 'old_value': 109174.64, 'new_value': 129509.64}, {'field': 'count', 'old_value': 345, 'new_value': 411}, {'field': 'instoreAmount', 'old_value': 109174.64, 'new_value': 129509.64}, {'field': 'instoreCount', 'old_value': 345, 'new_value': 411}]
2025-06-09 08:08:43,694 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:44,147 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSJ
2025-06-09 08:08:44,147 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6086.33, 'new_value': 7021.93}, {'field': 'dailyBillAmount', 'old_value': 6086.33, 'new_value': 7021.93}, {'field': 'amount', 'old_value': 6086.33, 'new_value': 7021.93}, {'field': 'count', 'old_value': 10, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 6086.33, 'new_value': 7021.93}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 11}]
2025-06-09 08:08:44,147 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:44,569 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRJ
2025-06-09 08:08:44,569 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-06, 变更字段: [{'field': 'amount', 'old_value': 24503.71, 'new_value': 26501.91}, {'field': 'count', 'old_value': 148, 'new_value': 168}, {'field': 'instoreAmount', 'old_value': 24503.71, 'new_value': 26501.91}, {'field': 'instoreCount', 'old_value': 148, 'new_value': 168}]
2025-06-09 08:08:44,569 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:45,100 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMI1
2025-06-09 08:08:45,100 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_8711865ACD3B4C2AADD3843CA2A204D9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16200.0, 'new_value': 25100.0}, {'field': 'amount', 'old_value': 16200.0, 'new_value': 25100.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 16200.0, 'new_value': 25100.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-06-09 08:08:45,100 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:45,522 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQJ
2025-06-09 08:08:45,522 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78C3CF1DDB8443E8A4A4D425F7CA9756_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 52494.04, 'new_value': 60848.04}, {'field': 'dailyBillAmount', 'old_value': 46292.04, 'new_value': 54646.04}, {'field': 'amount', 'old_value': 52494.04, 'new_value': 60848.04}, {'field': 'count', 'old_value': 265, 'new_value': 304}, {'field': 'instoreAmount', 'old_value': 52494.04, 'new_value': 60848.04}, {'field': 'instoreCount', 'old_value': 265, 'new_value': 304}]
2025-06-09 08:08:45,522 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:45,975 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPJ
2025-06-09 08:08:45,975 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 86967.0, 'new_value': 87816.0}, {'field': 'amount', 'old_value': 86967.0, 'new_value': 87816.0}, {'field': 'count', 'old_value': 18, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 86967.0, 'new_value': 87816.0}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 19}]
2025-06-09 08:08:45,975 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:46,444 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOJ
2025-06-09 08:08:46,444 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10544.99, 'new_value': 12424.54}, {'field': 'amount', 'old_value': 10544.99, 'new_value': 12424.54}, {'field': 'count', 'old_value': 98, 'new_value': 116}, {'field': 'instoreAmount', 'old_value': 10544.99, 'new_value': 12424.54}, {'field': 'instoreCount', 'old_value': 98, 'new_value': 116}]
2025-06-09 08:08:46,444 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:46,944 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNJ
2025-06-09 08:08:46,944 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 83849.0, 'new_value': 96086.0}, {'field': 'amount', 'old_value': 83849.0, 'new_value': 96086.0}, {'field': 'count', 'old_value': 136, 'new_value': 158}, {'field': 'instoreAmount', 'old_value': 83849.0, 'new_value': 96086.0}, {'field': 'instoreCount', 'old_value': 136, 'new_value': 158}]
2025-06-09 08:08:46,944 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:47,459 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMJ
2025-06-09 08:08:47,459 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11063.01, 'new_value': 12641.19}, {'field': 'dailyBillAmount', 'old_value': 11063.01, 'new_value': 12641.19}, {'field': 'amount', 'old_value': 12375.3, 'new_value': 14240.98}, {'field': 'count', 'old_value': 340, 'new_value': 396}, {'field': 'instoreAmount', 'old_value': 12375.3, 'new_value': 14240.98}, {'field': 'instoreCount', 'old_value': 340, 'new_value': 396}]
2025-06-09 08:08:47,459 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:47,928 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLJ
2025-06-09 08:08:47,928 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 57763.05, 'new_value': 65728.48}, {'field': 'dailyBillAmount', 'old_value': 48215.05, 'new_value': 56180.48}, {'field': 'amount', 'old_value': 57763.05, 'new_value': 65728.48}, {'field': 'count', 'old_value': 240, 'new_value': 273}, {'field': 'instoreAmount', 'old_value': 57763.05, 'new_value': 65728.48}, {'field': 'instoreCount', 'old_value': 240, 'new_value': 273}]
2025-06-09 08:08:47,928 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:48,397 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKJ
2025-06-09 08:08:48,397 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-06, 变更字段: [{'field': 'amount', 'old_value': 878.1, 'new_value': 1065.1}, {'field': 'count', 'old_value': 15, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 878.1, 'new_value': 1065.1}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 16}]
2025-06-09 08:08:48,397 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:48,866 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJJ
2025-06-09 08:08:48,866 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37317.78, 'new_value': 41935.28}, {'field': 'dailyBillAmount', 'old_value': 37317.78, 'new_value': 41935.28}, {'field': 'amount', 'old_value': 18125.75, 'new_value': 19696.9}, {'field': 'count', 'old_value': 206, 'new_value': 228}, {'field': 'instoreAmount', 'old_value': 17350.35, 'new_value': 18876.61}, {'field': 'instoreCount', 'old_value': 173, 'new_value': 188}, {'field': 'onlineAmount', 'old_value': 1114.1, 'new_value': 1315.5}, {'field': 'onlineCount', 'old_value': 33, 'new_value': 40}]
2025-06-09 08:08:48,866 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:49,319 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHJ
2025-06-09 08:08:49,319 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56282.16, 'new_value': 65616.16}, {'field': 'dailyBillAmount', 'old_value': 56282.16, 'new_value': 65616.16}, {'field': 'amount', 'old_value': 69312.6, 'new_value': 80013.6}, {'field': 'count', 'old_value': 371, 'new_value': 439}, {'field': 'instoreAmount', 'old_value': 69312.6, 'new_value': 80013.6}, {'field': 'instoreCount', 'old_value': 371, 'new_value': 439}]
2025-06-09 08:08:49,319 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:49,787 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWI
2025-06-09 08:08:49,787 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 94132.89, 'new_value': 105919.91}, {'field': 'dailyBillAmount', 'old_value': 94132.89, 'new_value': 105919.91}, {'field': 'amount', 'old_value': 55491.56, 'new_value': 60315.72}, {'field': 'count', 'old_value': 224, 'new_value': 242}, {'field': 'instoreAmount', 'old_value': 56388.52, 'new_value': 61318.2}, {'field': 'instoreCount', 'old_value': 224, 'new_value': 242}]
2025-06-09 08:08:49,787 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:50,287 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGJ
2025-06-09 08:08:50,287 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 103251.29, 'new_value': 115940.07}, {'field': 'dailyBillAmount', 'old_value': 103251.29, 'new_value': 115940.07}, {'field': 'amount', 'old_value': 44931.09, 'new_value': 52500.9}, {'field': 'count', 'old_value': 444, 'new_value': 535}, {'field': 'instoreAmount', 'old_value': 23001.7, 'new_value': 26005.69}, {'field': 'instoreCount', 'old_value': 163, 'new_value': 195}, {'field': 'onlineAmount', 'old_value': 21929.39, 'new_value': 26495.21}, {'field': 'onlineCount', 'old_value': 281, 'new_value': 340}]
2025-06-09 08:08:50,287 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:50,756 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFJ
2025-06-09 08:08:50,756 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 27895.41, 'new_value': 35369.81}, {'field': 'dailyBillAmount', 'old_value': 27895.41, 'new_value': 35369.81}, {'field': 'amount', 'old_value': 25767.18, 'new_value': 31265.98}, {'field': 'count', 'old_value': 105, 'new_value': 128}, {'field': 'instoreAmount', 'old_value': 25612.5, 'new_value': 31111.3}, {'field': 'instoreCount', 'old_value': 102, 'new_value': 125}]
2025-06-09 08:08:50,756 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:51,303 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEJ
2025-06-09 08:08:51,303 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18101.82, 'new_value': 20374.67}, {'field': 'amount', 'old_value': 18101.82, 'new_value': 20374.67}, {'field': 'count', 'old_value': 821, 'new_value': 933}, {'field': 'instoreAmount', 'old_value': 19109.59, 'new_value': 21563.87}, {'field': 'instoreCount', 'old_value': 821, 'new_value': 933}]
2025-06-09 08:08:51,303 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:51,756 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBI
2025-06-09 08:08:51,756 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-06, 变更字段: [{'field': 'amount', 'old_value': 32445.0, 'new_value': 39230.0}, {'field': 'count', 'old_value': 51, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 32445.0, 'new_value': 39230.0}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 61}]
2025-06-09 08:08:51,756 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:52,241 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCI
2025-06-09 08:08:52,241 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 322542.33, 'new_value': 372206.69}, {'field': 'dailyBillAmount', 'old_value': 322542.33, 'new_value': 372206.69}, {'field': 'amount', 'old_value': -149638.69, 'new_value': -170561.15}, {'field': 'count', 'old_value': 283, 'new_value': 337}, {'field': 'instoreAmount', 'old_value': 168898.88, 'new_value': 198235.33}, {'field': 'instoreCount', 'old_value': 283, 'new_value': 337}]
2025-06-09 08:08:52,241 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:52,678 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDI
2025-06-09 08:08:52,678 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 101965.0, 'new_value': 118510.0}, {'field': 'amount', 'old_value': 101965.0, 'new_value': 118510.0}, {'field': 'count', 'old_value': 385, 'new_value': 447}, {'field': 'instoreAmount', 'old_value': 101965.0, 'new_value': 118510.0}, {'field': 'instoreCount', 'old_value': 385, 'new_value': 447}]
2025-06-09 08:08:52,678 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:53,162 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEI
2025-06-09 08:08:53,162 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 110444.92, 'new_value': 127432.8}, {'field': 'dailyBillAmount', 'old_value': 100368.32, 'new_value': 116004.4}, {'field': 'amount', 'old_value': 110444.92, 'new_value': 127432.8}, {'field': 'count', 'old_value': 353, 'new_value': 406}, {'field': 'instoreAmount', 'old_value': 110444.92, 'new_value': 127432.8}, {'field': 'instoreCount', 'old_value': 353, 'new_value': 406}]
2025-06-09 08:08:53,162 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:53,600 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMG1
2025-06-09 08:08:53,600 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 42828.9, 'new_value': 48745.72}, {'field': 'dailyBillAmount', 'old_value': 42828.9, 'new_value': 48745.72}]
2025-06-09 08:08:53,600 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:54,053 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVJ
2025-06-09 08:08:54,053 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29092.9, 'new_value': 37329.4}, {'field': 'dailyBillAmount', 'old_value': 29092.9, 'new_value': 37329.4}, {'field': 'amount', 'old_value': 9082.7, 'new_value': 11266.5}, {'field': 'count', 'old_value': 40, 'new_value': 49}, {'field': 'instoreAmount', 'old_value': 9082.7, 'new_value': 11266.5}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 49}]
2025-06-09 08:08:54,053 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:54,553 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGI
2025-06-09 08:08:54,553 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41557.33, 'new_value': 45352.68}, {'field': 'dailyBillAmount', 'old_value': 21950.0, 'new_value': 25457.76}, {'field': 'amount', 'old_value': 41557.33, 'new_value': 45352.68}, {'field': 'count', 'old_value': 1375, 'new_value': 1501}, {'field': 'instoreAmount', 'old_value': 37602.88, 'new_value': 40982.72}, {'field': 'instoreCount', 'old_value': 1266, 'new_value': 1379}, {'field': 'onlineAmount', 'old_value': 3954.45, 'new_value': 4369.96}, {'field': 'onlineCount', 'old_value': 109, 'new_value': 122}]
2025-06-09 08:08:54,553 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:54,991 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHI
2025-06-09 08:08:54,991 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 75243.83, 'new_value': 82419.83}, {'field': 'dailyBillAmount', 'old_value': 75202.0, 'new_value': 82378.0}, {'field': 'amount', 'old_value': 62475.2, 'new_value': 66066.19}, {'field': 'count', 'old_value': 63, 'new_value': 72}, {'field': 'instoreAmount', 'old_value': 61903.0, 'new_value': 65250.0}, {'field': 'instoreCount', 'old_value': 59, 'new_value': 67}, {'field': 'onlineAmount', 'old_value': 572.2, 'new_value': 816.19}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 5}]
2025-06-09 08:08:54,991 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:55,459 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMII
2025-06-09 08:08:55,475 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 122444.55, 'new_value': 132207.73}, {'field': 'dailyBillAmount', 'old_value': 122444.55, 'new_value': 132207.73}, {'field': 'amount', 'old_value': 122444.55, 'new_value': 132207.73}, {'field': 'count', 'old_value': 118, 'new_value': 132}, {'field': 'instoreAmount', 'old_value': 122444.55, 'new_value': 132207.73}, {'field': 'instoreCount', 'old_value': 118, 'new_value': 132}]
2025-06-09 08:08:55,475 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:55,912 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKI
2025-06-09 08:08:55,912 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22890.2, 'new_value': 26904.2}, {'field': 'dailyBillAmount', 'old_value': 22890.2, 'new_value': 26904.2}, {'field': 'amount', 'old_value': 26611.9, 'new_value': 30932.9}, {'field': 'count', 'old_value': 80, 'new_value': 91}, {'field': 'instoreAmount', 'old_value': 26611.9, 'new_value': 30932.9}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 91}]
2025-06-09 08:08:55,912 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:56,381 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDJ
2025-06-09 08:08:56,381 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8061.0, 'new_value': 9664.0}, {'field': 'dailyBillAmount', 'old_value': 8061.0, 'new_value': 9664.0}, {'field': 'amount', 'old_value': 8061.0, 'new_value': 9664.0}, {'field': 'count', 'old_value': 23, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 8409.0, 'new_value': 10051.0}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 27}]
2025-06-09 08:08:56,381 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:56,834 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNI
2025-06-09 08:08:56,834 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 46729.85, 'new_value': 50954.86}, {'field': 'dailyBillAmount', 'old_value': 46729.85, 'new_value': 50954.86}, {'field': 'amount', 'old_value': 4885.99, 'new_value': 5453.63}, {'field': 'count', 'old_value': 380, 'new_value': 416}, {'field': 'instoreAmount', 'old_value': 5865.19, 'new_value': 6442.33}, {'field': 'instoreCount', 'old_value': 380, 'new_value': 416}]
2025-06-09 08:08:56,850 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:57,287 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPI
2025-06-09 08:08:57,287 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 69948.14, 'new_value': 84793.7}, {'field': 'dailyBillAmount', 'old_value': 69948.14, 'new_value': 84793.7}, {'field': 'amount', 'old_value': 70216.41, 'new_value': 85061.97}, {'field': 'count', 'old_value': 183, 'new_value': 227}, {'field': 'instoreAmount', 'old_value': 70216.41, 'new_value': 85061.97}, {'field': 'instoreCount', 'old_value': 183, 'new_value': 227}]
2025-06-09 08:08:57,287 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:57,741 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQI
2025-06-09 08:08:57,741 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 64922.15, 'new_value': 69321.15}, {'field': 'amount', 'old_value': 21717.7, 'new_value': 26116.7}, {'field': 'count', 'old_value': 53, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 21717.7, 'new_value': 26116.7}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 64}]
2025-06-09 08:08:57,741 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:58,225 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRI
2025-06-09 08:08:58,225 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 99756.65, 'new_value': 114788.96}, {'field': 'dailyBillAmount', 'old_value': 99756.65, 'new_value': 114788.96}, {'field': 'amount', 'old_value': 39052.4, 'new_value': 44928.3}, {'field': 'count', 'old_value': 155, 'new_value': 174}, {'field': 'instoreAmount', 'old_value': 39052.4, 'new_value': 44928.3}, {'field': 'instoreCount', 'old_value': 155, 'new_value': 174}]
2025-06-09 08:08:58,225 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:58,678 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSI
2025-06-09 08:08:58,678 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26198.85, 'new_value': 30591.03}, {'field': 'dailyBillAmount', 'old_value': 26198.85, 'new_value': 30591.03}, {'field': 'amount', 'old_value': 5718.22, 'new_value': 6483.85}, {'field': 'count', 'old_value': 219, 'new_value': 245}, {'field': 'instoreAmount', 'old_value': 1551.3, 'new_value': 1718.9}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 43}, {'field': 'onlineAmount', 'old_value': 4244.04, 'new_value': 4842.07}, {'field': 'onlineCount', 'old_value': 178, 'new_value': 202}]
2025-06-09 08:08:58,678 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:59,053 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTI
2025-06-09 08:08:59,053 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 43787.66, 'new_value': 50369.63}, {'field': 'dailyBillAmount', 'old_value': 43787.66, 'new_value': 50369.63}, {'field': 'amount', 'old_value': 7667.27, 'new_value': 8753.27}, {'field': 'count', 'old_value': 191, 'new_value': 224}, {'field': 'instoreAmount', 'old_value': 6333.2, 'new_value': 7021.32}, {'field': 'instoreCount', 'old_value': 157, 'new_value': 180}, {'field': 'onlineAmount', 'old_value': 1358.22, 'new_value': 1756.1}, {'field': 'onlineCount', 'old_value': 34, 'new_value': 44}]
2025-06-09 08:08:59,053 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:59,444 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUI
2025-06-09 08:08:59,444 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9943.6, 'new_value': 12564.69}, {'field': 'dailyBillAmount', 'old_value': 9943.6, 'new_value': 12564.69}, {'field': 'amount', 'old_value': 8462.6, 'new_value': 9472.6}, {'field': 'count', 'old_value': 265, 'new_value': 333}, {'field': 'instoreAmount', 'old_value': 8562.6, 'new_value': 9572.6}, {'field': 'instoreCount', 'old_value': 265, 'new_value': 333}]
2025-06-09 08:08:59,459 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:08:59,928 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVI
2025-06-09 08:08:59,928 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-06, 变更字段: [{'field': 'amount', 'old_value': 7744.51, 'new_value': 9049.21}, {'field': 'count', 'old_value': 428, 'new_value': 497}, {'field': 'instoreAmount', 'old_value': 2383.77, 'new_value': 2758.77}, {'field': 'instoreCount', 'old_value': 103, 'new_value': 120}, {'field': 'onlineAmount', 'old_value': 5619.14, 'new_value': 6564.84}, {'field': 'onlineCount', 'old_value': 325, 'new_value': 377}]
2025-06-09 08:08:59,928 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:00,381 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCJ
2025-06-09 08:09:00,381 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 51535.7, 'new_value': 58449.7}, {'field': 'dailyBillAmount', 'old_value': 51535.7, 'new_value': 58449.7}, {'field': 'amount', 'old_value': 57720.7, 'new_value': 68637.7}, {'field': 'count', 'old_value': 205, 'new_value': 251}, {'field': 'instoreAmount', 'old_value': 58628.7, 'new_value': 69545.7}, {'field': 'instoreCount', 'old_value': 205, 'new_value': 251}]
2025-06-09 08:09:00,381 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:00,866 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBJ
2025-06-09 08:09:00,866 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26778.6, 'new_value': 32596.42}, {'field': 'dailyBillAmount', 'old_value': 26778.6, 'new_value': 32596.42}, {'field': 'amount', 'old_value': 27689.78, 'new_value': 33633.34}, {'field': 'count', 'old_value': 137, 'new_value': 169}, {'field': 'instoreAmount', 'old_value': 25807.61, 'new_value': 31625.66}, {'field': 'instoreCount', 'old_value': 116, 'new_value': 147}, {'field': 'onlineAmount', 'old_value': 1882.17, 'new_value': 2007.68}, {'field': 'onlineCount', 'old_value': 21, 'new_value': 22}]
2025-06-09 08:09:00,866 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:01,334 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAJ
2025-06-09 08:09:01,334 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 43310.69, 'new_value': 47710.09}, {'field': 'dailyBillAmount', 'old_value': 42406.12, 'new_value': 46751.12}, {'field': 'amount', 'old_value': 43310.69, 'new_value': 47710.09}, {'field': 'count', 'old_value': 559, 'new_value': 618}, {'field': 'instoreAmount', 'old_value': 41882.22, 'new_value': 46052.62}, {'field': 'instoreCount', 'old_value': 539, 'new_value': 594}, {'field': 'onlineAmount', 'old_value': 1482.47, 'new_value': 1711.47}, {'field': 'onlineCount', 'old_value': 20, 'new_value': 24}]
2025-06-09 08:09:01,334 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:01,787 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLI
2025-06-09 08:09:01,787 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20852.94, 'new_value': 25335.94}, {'field': 'amount', 'old_value': 20852.94, 'new_value': 25335.94}, {'field': 'count', 'old_value': 36, 'new_value': 39}, {'field': 'instoreAmount', 'old_value': 20852.94, 'new_value': 25335.94}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 39}]
2025-06-09 08:09:01,787 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:02,256 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9J
2025-06-09 08:09:02,256 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 42963.55, 'new_value': 49524.55}, {'field': 'dailyBillAmount', 'old_value': 42963.55, 'new_value': 49524.55}, {'field': 'amount', 'old_value': 25571.6, 'new_value': 29820.36}, {'field': 'count', 'old_value': 631, 'new_value': 742}, {'field': 'instoreAmount', 'old_value': 22779.08, 'new_value': 26420.0}, {'field': 'instoreCount', 'old_value': 541, 'new_value': 636}, {'field': 'onlineAmount', 'old_value': 3651.22, 'new_value': 4274.01}, {'field': 'onlineCount', 'old_value': 90, 'new_value': 106}]
2025-06-09 08:09:02,256 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:02,709 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8J
2025-06-09 08:09:02,709 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23987.6, 'new_value': 27569.5}, {'field': 'dailyBillAmount', 'old_value': 23987.6, 'new_value': 27569.5}, {'field': 'amount', 'old_value': 23987.6, 'new_value': 27569.5}, {'field': 'count', 'old_value': 63, 'new_value': 80}, {'field': 'instoreAmount', 'old_value': 24917.2, 'new_value': 28616.1}, {'field': 'instoreCount', 'old_value': 63, 'new_value': 80}]
2025-06-09 08:09:02,709 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:03,147 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7J
2025-06-09 08:09:03,147 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16797.0, 'new_value': 18132.0}, {'field': 'dailyBillAmount', 'old_value': 16797.0, 'new_value': 18132.0}, {'field': 'amount', 'old_value': 18889.0, 'new_value': 20224.0}, {'field': 'count', 'old_value': 28, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 19457.0, 'new_value': 20792.0}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 32}]
2025-06-09 08:09:03,147 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:03,584 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6J
2025-06-09 08:09:03,584 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22808.74, 'new_value': 25208.64}, {'field': 'dailyBillAmount', 'old_value': 22808.74, 'new_value': 25208.64}, {'field': 'amount', 'old_value': 21940.74, 'new_value': 24016.64}, {'field': 'count', 'old_value': 93, 'new_value': 101}, {'field': 'instoreAmount', 'old_value': 22179.74, 'new_value': 24255.64}, {'field': 'instoreCount', 'old_value': 93, 'new_value': 101}]
2025-06-09 08:09:03,600 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:03,990 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5J
2025-06-09 08:09:03,990 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 84520.0, 'new_value': 95700.0}, {'field': 'dailyBillAmount', 'old_value': 84520.0, 'new_value': 95700.0}, {'field': 'amount', 'old_value': 34982.5, 'new_value': 39043.5}, {'field': 'count', 'old_value': 100, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 34982.5, 'new_value': 39043.5}, {'field': 'instoreCount', 'old_value': 100, 'new_value': 110}]
2025-06-09 08:09:03,990 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:04,459 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4J
2025-06-09 08:09:04,459 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22909.9, 'new_value': 29006.0}, {'field': 'amount', 'old_value': 22909.9, 'new_value': 29006.0}, {'field': 'count', 'old_value': 550, 'new_value': 661}, {'field': 'instoreAmount', 'old_value': 23187.0, 'new_value': 29386.3}, {'field': 'instoreCount', 'old_value': 550, 'new_value': 661}]
2025-06-09 08:09:04,459 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:04,897 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3J
2025-06-09 08:09:04,897 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-06, 变更字段: [{'field': 'amount', 'old_value': 15361.41, 'new_value': 16737.01}, {'field': 'count', 'old_value': 196, 'new_value': 211}, {'field': 'instoreAmount', 'old_value': 15361.41, 'new_value': 16737.01}, {'field': 'instoreCount', 'old_value': 196, 'new_value': 211}]
2025-06-09 08:09:04,897 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:05,381 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2J
2025-06-09 08:09:05,381 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-06, 变更字段: [{'field': 'amount', 'old_value': 11232.03, 'new_value': 12325.68}, {'field': 'count', 'old_value': 443, 'new_value': 490}, {'field': 'onlineAmount', 'old_value': 5692.33, 'new_value': 6810.3}, {'field': 'onlineCount', 'old_value': 260, 'new_value': 307}]
2025-06-09 08:09:05,381 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:05,850 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1J
2025-06-09 08:09:05,850 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24465.71, 'new_value': 27968.26}, {'field': 'dailyBillAmount', 'old_value': 24465.71, 'new_value': 27968.26}, {'field': 'amount', 'old_value': 25191.55, 'new_value': 28759.21}, {'field': 'count', 'old_value': 1400, 'new_value': 1618}, {'field': 'instoreAmount', 'old_value': 13715.91, 'new_value': 15526.51}, {'field': 'instoreCount', 'old_value': 721, 'new_value': 824}, {'field': 'onlineAmount', 'old_value': 11945.6, 'new_value': 13819.11}, {'field': 'onlineCount', 'old_value': 679, 'new_value': 794}]
2025-06-09 08:09:05,850 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:06,303 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0J
2025-06-09 08:09:06,303 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15564.0, 'new_value': 17228.0}, {'field': 'dailyBillAmount', 'old_value': 15564.0, 'new_value': 17228.0}, {'field': 'amount', 'old_value': 15529.0, 'new_value': 17193.0}, {'field': 'count', 'old_value': 303, 'new_value': 336}, {'field': 'instoreAmount', 'old_value': 15529.0, 'new_value': 17193.0}, {'field': 'instoreCount', 'old_value': 303, 'new_value': 336}]
2025-06-09 08:09:06,303 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:06,772 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZI
2025-06-09 08:09:06,772 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 81429.1, 'new_value': 97092.8}, {'field': 'dailyBillAmount', 'old_value': 81429.1, 'new_value': 97092.8}, {'field': 'amount', 'old_value': 57714.7, 'new_value': 69113.05}, {'field': 'count', 'old_value': 147, 'new_value': 181}, {'field': 'instoreAmount', 'old_value': 57714.7, 'new_value': 69342.0}, {'field': 'instoreCount', 'old_value': 147, 'new_value': 181}]
2025-06-09 08:09:06,772 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:07,178 - INFO - 更新表单数据成功: FINST-L8D665C1C82WOKI6BGFCGBTDWGJP22VFUWMBM9U
2025-06-09 08:09:07,178 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSCOR6MVCC7Q2OV392411100148T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1000.0, 'new_value': 1493.0}, {'field': 'amount', 'old_value': 1000.0, 'new_value': 1493.0}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 1000.0, 'new_value': 1493.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-09 08:09:07,194 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:07,709 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXI
2025-06-09 08:09:07,709 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 51749.49, 'new_value': 58147.7}, {'field': 'dailyBillAmount', 'old_value': 51749.49, 'new_value': 58147.7}, {'field': 'amount', 'old_value': 25378.81, 'new_value': 28634.0}, {'field': 'count', 'old_value': 1180, 'new_value': 1339}, {'field': 'instoreAmount', 'old_value': 26244.23, 'new_value': 29621.12}, {'field': 'instoreCount', 'old_value': 1180, 'new_value': 1339}]
2025-06-09 08:09:07,709 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:08,303 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYI
2025-06-09 08:09:08,303 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 156106.1, 'new_value': 180630.8}, {'field': 'dailyBillAmount', 'old_value': 156106.1, 'new_value': 180630.8}, {'field': 'amount', 'old_value': 156106.1, 'new_value': 180630.8}, {'field': 'count', 'old_value': 190, 'new_value': 220}, {'field': 'instoreAmount', 'old_value': 156106.1, 'new_value': 180630.8}, {'field': 'instoreCount', 'old_value': 190, 'new_value': 220}]
2025-06-09 08:09:08,428 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-09 08:09:08,881 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9MPN
2025-06-09 08:09:08,881 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-04, 变更字段: [{'field': 'amount', 'old_value': 42442.35, 'new_value': 42441.35}]
2025-06-09 08:09:08,912 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:09,365 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMOK
2025-06-09 08:09:09,365 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21959.0, 'new_value': 24867.0}, {'field': 'amount', 'old_value': 21959.0, 'new_value': 24867.0}, {'field': 'count', 'old_value': 19, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 21959.0, 'new_value': 24867.0}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 22}]
2025-06-09 08:09:09,365 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:09,772 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMNK
2025-06-09 08:09:09,772 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8166.0, 'new_value': 8818.0}, {'field': 'amount', 'old_value': 8166.0, 'new_value': 8818.0}, {'field': 'count', 'old_value': 17, 'new_value': 18}, {'field': 'instoreAmount', 'old_value': 8166.0, 'new_value': 8818.0}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 18}]
2025-06-09 08:09:09,772 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:10,194 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMMK
2025-06-09 08:09:10,194 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38349.0, 'new_value': 39885.0}, {'field': 'amount', 'old_value': 38349.0, 'new_value': 39885.0}, {'field': 'count', 'old_value': 13, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 38349.0, 'new_value': 39885.0}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 14}]
2025-06-09 08:09:10,194 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:10,647 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMLK
2025-06-09 08:09:10,647 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 132700.0, 'new_value': 165294.0}, {'field': 'dailyBillAmount', 'old_value': 132700.0, 'new_value': 165294.0}, {'field': 'amount', 'old_value': 149447.0, 'new_value': 182041.0}, {'field': 'count', 'old_value': 22, 'new_value': 26}, {'field': 'instoreAmount', 'old_value': 149447.0, 'new_value': 182041.0}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 26}]
2025-06-09 08:09:10,647 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:11,178 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMKK
2025-06-09 08:09:11,178 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18228.5, 'new_value': 22297.0}, {'field': 'amount', 'old_value': 18228.5, 'new_value': 22297.0}, {'field': 'count', 'old_value': 47, 'new_value': 55}, {'field': 'instoreAmount', 'old_value': 18228.5, 'new_value': 22297.0}, {'field': 'instoreCount', 'old_value': 47, 'new_value': 55}]
2025-06-09 08:09:11,178 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:11,725 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMFK
2025-06-09 08:09:11,725 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 66028.0, 'new_value': 72564.0}, {'field': 'dailyBillAmount', 'old_value': 66028.0, 'new_value': 72564.0}, {'field': 'amount', 'old_value': 72651.0, 'new_value': 78301.0}, {'field': 'count', 'old_value': 66, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 79472.0, 'new_value': 85122.0}, {'field': 'instoreCount', 'old_value': 66, 'new_value': 74}]
2025-06-09 08:09:11,725 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:12,225 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMJK
2025-06-09 08:09:12,225 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14293.0, 'new_value': 17735.0}, {'field': 'dailyBillAmount', 'old_value': 6290.0, 'new_value': 9732.0}, {'field': 'amount', 'old_value': 13355.0, 'new_value': 16302.0}, {'field': 'count', 'old_value': 18, 'new_value': 23}, {'field': 'instoreAmount', 'old_value': 13355.0, 'new_value': 16302.0}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 23}]
2025-06-09 08:09:12,225 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:12,647 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMIK
2025-06-09 08:09:12,647 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17032.0, 'new_value': 19327.0}, {'field': 'dailyBillAmount', 'old_value': 17032.0, 'new_value': 19327.0}, {'field': 'amount', 'old_value': 6514.0, 'new_value': 6753.0}, {'field': 'count', 'old_value': 18, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 6514.0, 'new_value': 6753.0}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 19}]
2025-06-09 08:09:12,647 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:13,225 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMHK
2025-06-09 08:09:13,225 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6992.0, 'new_value': 10052.0}, {'field': 'amount', 'old_value': 6992.0, 'new_value': 10052.0}, {'field': 'count', 'old_value': 23, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 6992.0, 'new_value': 10052.0}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 28}]
2025-06-09 08:09:13,225 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:13,725 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMGK
2025-06-09 08:09:13,725 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 44957.6, 'new_value': 56711.4}, {'field': 'dailyBillAmount', 'old_value': 44957.6, 'new_value': 56711.4}, {'field': 'amount', 'old_value': 44830.61, 'new_value': 57341.61}, {'field': 'count', 'old_value': 107, 'new_value': 132}, {'field': 'instoreAmount', 'old_value': 45813.21, 'new_value': 58787.51}, {'field': 'instoreCount', 'old_value': 107, 'new_value': 132}]
2025-06-09 08:09:13,725 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:14,178 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMEK
2025-06-09 08:09:14,178 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32513.55, 'new_value': 38455.57}, {'field': 'dailyBillAmount', 'old_value': 32513.55, 'new_value': 38455.57}, {'field': 'amount', 'old_value': 34096.61, 'new_value': 40386.04}, {'field': 'count', 'old_value': 1161, 'new_value': 1368}, {'field': 'instoreAmount', 'old_value': 34050.31, 'new_value': 40339.74}, {'field': 'instoreCount', 'old_value': 1158, 'new_value': 1365}]
2025-06-09 08:09:14,178 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:14,647 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLS
2025-06-09 08:09:14,647 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 115031.68, 'new_value': 149418.43}, {'field': 'dailyBillAmount', 'old_value': 115031.68, 'new_value': 149418.43}, {'field': 'amount', 'old_value': 115786.84, 'new_value': 143217.28}, {'field': 'count', 'old_value': 516, 'new_value': 642}, {'field': 'instoreAmount', 'old_value': 115786.84, 'new_value': 143217.28}, {'field': 'instoreCount', 'old_value': 516, 'new_value': 642}]
2025-06-09 08:09:14,647 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:15,115 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMDK
2025-06-09 08:09:15,115 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19529.3, 'new_value': 22520.86}, {'field': 'dailyBillAmount', 'old_value': 19529.3, 'new_value': 22520.86}, {'field': 'amount', 'old_value': 22675.75, 'new_value': 26154.25}, {'field': 'count', 'old_value': 781, 'new_value': 910}, {'field': 'instoreAmount', 'old_value': 8564.49, 'new_value': 9878.49}, {'field': 'instoreCount', 'old_value': 282, 'new_value': 332}, {'field': 'onlineAmount', 'old_value': 14293.06, 'new_value': 16478.16}, {'field': 'onlineCount', 'old_value': 499, 'new_value': 578}]
2025-06-09 08:09:15,115 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:15,506 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMCK
2025-06-09 08:09:15,506 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10259.8, 'new_value': 14911.8}, {'field': 'amount', 'old_value': 10259.8, 'new_value': 14911.8}, {'field': 'count', 'old_value': 10, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 10259.8, 'new_value': 14911.8}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 11}]
2025-06-09 08:09:15,506 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:15,912 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMBK
2025-06-09 08:09:15,912 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 55929.54, 'new_value': 66017.02}, {'field': 'dailyBillAmount', 'old_value': 55929.54, 'new_value': 66017.02}, {'field': 'amount', 'old_value': 46075.71, 'new_value': 54610.37}, {'field': 'count', 'old_value': 1426, 'new_value': 1708}, {'field': 'instoreAmount', 'old_value': 25762.33, 'new_value': 30207.89}, {'field': 'instoreCount', 'old_value': 1057, 'new_value': 1259}, {'field': 'onlineAmount', 'old_value': 22945.2, 'new_value': 27874.1}, {'field': 'onlineCount', 'old_value': 369, 'new_value': 449}]
2025-06-09 08:09:15,912 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:16,475 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMAK
2025-06-09 08:09:16,475 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 49759.15, 'new_value': 57451.92}, {'field': 'dailyBillAmount', 'old_value': 49759.15, 'new_value': 57451.92}, {'field': 'amount', 'old_value': 29952.18, 'new_value': 34334.19}, {'field': 'count', 'old_value': 2192, 'new_value': 2491}, {'field': 'instoreAmount', 'old_value': 1943.5, 'new_value': 2175.5}, {'field': 'instoreCount', 'old_value': 127, 'new_value': 151}, {'field': 'onlineAmount', 'old_value': 29260.66, 'new_value': 33559.57}, {'field': 'onlineCount', 'old_value': 2065, 'new_value': 2340}]
2025-06-09 08:09:16,490 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:16,912 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM9K
2025-06-09 08:09:16,912 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-06, 变更字段: [{'field': 'amount', 'old_value': 48049.72, 'new_value': 53302.85}, {'field': 'count', 'old_value': 2459, 'new_value': 2803}, {'field': 'instoreAmount', 'old_value': 50602.3, 'new_value': 55987.93}, {'field': 'instoreCount', 'old_value': 2446, 'new_value': 2790}]
2025-06-09 08:09:16,912 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:17,490 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM8K
2025-06-09 08:09:17,490 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34580.08, 'new_value': 39090.87}, {'field': 'amount', 'old_value': 34580.08, 'new_value': 39090.87}, {'field': 'count', 'old_value': 964, 'new_value': 1125}, {'field': 'instoreAmount', 'old_value': 32561.71, 'new_value': 36984.0}, {'field': 'instoreCount', 'old_value': 941, 'new_value': 1100}, {'field': 'onlineAmount', 'old_value': 2025.67, 'new_value': 2114.17}, {'field': 'onlineCount', 'old_value': 23, 'new_value': 25}]
2025-06-09 08:09:17,490 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:17,959 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7K
2025-06-09 08:09:17,959 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41826.09, 'new_value': 49807.32}, {'field': 'dailyBillAmount', 'old_value': 41826.09, 'new_value': 49807.32}, {'field': 'amount', 'old_value': 28393.16, 'new_value': 33407.25}, {'field': 'count', 'old_value': 1362, 'new_value': 1601}, {'field': 'instoreAmount', 'old_value': 5549.0, 'new_value': 6288.2}, {'field': 'instoreCount', 'old_value': 351, 'new_value': 397}, {'field': 'onlineAmount', 'old_value': 23422.81, 'new_value': 27789.51}, {'field': 'onlineCount', 'old_value': 1011, 'new_value': 1204}]
2025-06-09 08:09:17,975 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:18,381 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6K
2025-06-09 08:09:18,381 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 42023.62, 'new_value': 50313.79}, {'field': 'dailyBillAmount', 'old_value': 42023.62, 'new_value': 50313.79}, {'field': 'amount', 'old_value': 8720.34, 'new_value': 10028.94}, {'field': 'count', 'old_value': 281, 'new_value': 322}, {'field': 'instoreAmount', 'old_value': 8886.88, 'new_value': 10216.88}, {'field': 'instoreCount', 'old_value': 281, 'new_value': 322}]
2025-06-09 08:09:18,381 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:18,990 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5K
2025-06-09 08:09:18,990 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32088.57, 'new_value': 37960.35}, {'field': 'amount', 'old_value': 32088.57, 'new_value': 37960.35}, {'field': 'count', 'old_value': 1833, 'new_value': 2184}, {'field': 'instoreAmount', 'old_value': 15877.43, 'new_value': 18711.71}, {'field': 'instoreCount', 'old_value': 1059, 'new_value': 1266}, {'field': 'onlineAmount', 'old_value': 16211.14, 'new_value': 19248.64}, {'field': 'onlineCount', 'old_value': 774, 'new_value': 918}]
2025-06-09 08:09:18,990 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:19,475 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4K
2025-06-09 08:09:19,475 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 75040.71, 'new_value': 88590.03}, {'field': 'dailyBillAmount', 'old_value': 75040.71, 'new_value': 88590.03}, {'field': 'amount', 'old_value': 69972.85, 'new_value': 83011.29}, {'field': 'count', 'old_value': 2083, 'new_value': 2504}, {'field': 'instoreAmount', 'old_value': 70249.85, 'new_value': 83288.29}, {'field': 'instoreCount', 'old_value': 2083, 'new_value': 2504}]
2025-06-09 08:09:19,475 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:19,928 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3K
2025-06-09 08:09:19,928 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30434.22, 'new_value': 38637.4}, {'field': 'dailyBillAmount', 'old_value': 30434.22, 'new_value': 38637.4}, {'field': 'amount', 'old_value': 43728.94, 'new_value': 49843.14}, {'field': 'count', 'old_value': 2830, 'new_value': 3256}, {'field': 'instoreAmount', 'old_value': 32646.99, 'new_value': 36641.69}, {'field': 'instoreCount', 'old_value': 1907, 'new_value': 2171}, {'field': 'onlineAmount', 'old_value': 11982.55, 'new_value': 14154.55}, {'field': 'onlineCount', 'old_value': 923, 'new_value': 1085}]
2025-06-09 08:09:19,928 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:20,381 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2K
2025-06-09 08:09:20,381 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-06, 变更字段: [{'field': 'amount', 'old_value': -3586.83, 'new_value': -4031.83}, {'field': 'count', 'old_value': 30, 'new_value': 33}, {'field': 'instoreAmount', 'old_value': 899.0, 'new_value': 938.0}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 8}, {'field': 'onlineAmount', 'old_value': 536.0, 'new_value': 575.0}, {'field': 'onlineCount', 'old_value': 23, 'new_value': 25}]
2025-06-09 08:09:20,381 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:20,819 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1K
2025-06-09 08:09:20,819 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 145718.36, 'new_value': 166242.41}, {'field': 'amount', 'old_value': 145718.36, 'new_value': 166242.41}, {'field': 'count', 'old_value': 3298, 'new_value': 3730}, {'field': 'instoreAmount', 'old_value': 107779.76, 'new_value': 121662.26}, {'field': 'instoreCount', 'old_value': 2250, 'new_value': 2507}, {'field': 'onlineAmount', 'old_value': 37938.6, 'new_value': 44580.15}, {'field': 'onlineCount', 'old_value': 1048, 'new_value': 1223}]
2025-06-09 08:09:20,834 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:21,350 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0K
2025-06-09 08:09:21,350 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 169687.35, 'new_value': 203886.33}, {'field': 'dailyBillAmount', 'old_value': 169687.35, 'new_value': 203886.33}, {'field': 'amount', 'old_value': 168113.56, 'new_value': 182898.08}, {'field': 'count', 'old_value': 2967, 'new_value': 3294}, {'field': 'instoreAmount', 'old_value': 156570.46, 'new_value': 169508.18}, {'field': 'instoreCount', 'old_value': 2733, 'new_value': 3019}, {'field': 'onlineAmount', 'old_value': 12138.3, 'new_value': 14026.9}, {'field': 'onlineCount', 'old_value': 234, 'new_value': 275}]
2025-06-09 08:09:21,350 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:21,772 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3S
2025-06-09 08:09:21,772 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 164714.65, 'new_value': 192057.82}, {'field': 'dailyBillAmount', 'old_value': 164714.65, 'new_value': 192057.82}, {'field': 'amount', 'old_value': 164384.05, 'new_value': 192739.23}, {'field': 'count', 'old_value': 1733, 'new_value': 1983}, {'field': 'instoreAmount', 'old_value': 133442.12, 'new_value': 155090.39}, {'field': 'instoreCount', 'old_value': 675, 'new_value': 783}, {'field': 'onlineAmount', 'old_value': 31909.7, 'new_value': 38689.99}, {'field': 'onlineCount', 'old_value': 1058, 'new_value': 1200}]
2025-06-09 08:09:21,772 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:22,225 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYJ
2025-06-09 08:09:22,225 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 97440.81, 'new_value': 112002.11}, {'field': 'dailyBillAmount', 'old_value': 97440.81, 'new_value': 112002.11}, {'field': 'amount', 'old_value': 47770.1, 'new_value': 55260.84}, {'field': 'count', 'old_value': 992, 'new_value': 1190}, {'field': 'instoreAmount', 'old_value': 42358.9, 'new_value': 48019.13}, {'field': 'instoreCount', 'old_value': 876, 'new_value': 1039}, {'field': 'onlineAmount', 'old_value': 6144.3, 'new_value': 7979.71}, {'field': 'onlineCount', 'old_value': 116, 'new_value': 151}]
2025-06-09 08:09:22,225 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:22,756 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWJ
2025-06-09 08:09:22,756 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 59850.29, 'new_value': 66685.01}, {'field': 'amount', 'old_value': 59850.29, 'new_value': 66685.01}, {'field': 'count', 'old_value': 648, 'new_value': 722}, {'field': 'instoreAmount', 'old_value': 42006.57, 'new_value': 46677.44}, {'field': 'instoreCount', 'old_value': 399, 'new_value': 441}, {'field': 'onlineAmount', 'old_value': 19496.4, 'new_value': 21660.25}, {'field': 'onlineCount', 'old_value': 249, 'new_value': 281}]
2025-06-09 08:09:22,756 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:23,209 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZJ
2025-06-09 08:09:23,209 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 77562.5, 'new_value': 91497.8}, {'field': 'dailyBillAmount', 'old_value': 111208.6, 'new_value': 135300.1}, {'field': 'amount', 'old_value': 77562.5, 'new_value': 91497.8}, {'field': 'count', 'old_value': 291, 'new_value': 347}, {'field': 'instoreAmount', 'old_value': 79468.2, 'new_value': 93403.5}, {'field': 'instoreCount', 'old_value': 291, 'new_value': 347}]
2025-06-09 08:09:23,209 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:23,725 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVR
2025-06-09 08:09:23,725 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5608.0, 'new_value': 7860.0}, {'field': 'amount', 'old_value': 5608.0, 'new_value': 7860.0}, {'field': 'count', 'old_value': 12, 'new_value': 13}, {'field': 'instoreAmount', 'old_value': 5608.0, 'new_value': 7860.0}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 13}]
2025-06-09 08:09:23,725 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:24,131 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUR
2025-06-09 08:09:24,131 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19262.0, 'new_value': 21299.0}, {'field': 'amount', 'old_value': 19262.0, 'new_value': 21299.0}, {'field': 'count', 'old_value': 25, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 20060.0, 'new_value': 22097.0}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 32}]
2025-06-09 08:09:24,131 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:24,631 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTR
2025-06-09 08:09:24,631 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 322101.63, 'new_value': 368084.67}, {'field': 'dailyBillAmount', 'old_value': 322101.63, 'new_value': 368084.67}, {'field': 'amount', 'old_value': 20154.65, 'new_value': 22856.44}, {'field': 'count', 'old_value': 210, 'new_value': 237}, {'field': 'instoreAmount', 'old_value': 16213.5, 'new_value': 18323.73}, {'field': 'instoreCount', 'old_value': 154, 'new_value': 173}, {'field': 'onlineAmount', 'old_value': 4058.95, 'new_value': 4651.3}, {'field': 'onlineCount', 'old_value': 56, 'new_value': 64}]
2025-06-09 08:09:24,631 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:25,084 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSR
2025-06-09 08:09:25,084 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2176.0, 'new_value': 2779.0}, {'field': 'dailyBillAmount', 'old_value': 2176.0, 'new_value': 2779.0}, {'field': 'amount', 'old_value': 13275.0, 'new_value': 16290.0}, {'field': 'count', 'old_value': 37, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 13473.0, 'new_value': 16488.0}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 42}]
2025-06-09 08:09:25,084 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:25,537 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRR
2025-06-09 08:09:25,537 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9458.7, 'new_value': 10875.7}, {'field': 'amount', 'old_value': 9458.7, 'new_value': 10875.7}, {'field': 'count', 'old_value': 56, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 9566.7, 'new_value': 10983.7}, {'field': 'instoreCount', 'old_value': 56, 'new_value': 64}]
2025-06-09 08:09:25,537 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:26,037 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMSK
2025-06-09 08:09:26,037 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5205.0, 'new_value': 7060.0}, {'field': 'dailyBillAmount', 'old_value': 5205.0, 'new_value': 7060.0}, {'field': 'amount', 'old_value': 5200.0, 'new_value': 6587.0}, {'field': 'count', 'old_value': 24, 'new_value': 30}, {'field': 'instoreAmount', 'old_value': 5730.0, 'new_value': 7117.0}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 30}]
2025-06-09 08:09:26,037 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:26,459 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMRK
2025-06-09 08:09:26,459 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3527.0, 'new_value': 3776.0}, {'field': 'amount', 'old_value': 3527.0, 'new_value': 3776.0}, {'field': 'count', 'old_value': 9, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 3527.0, 'new_value': 3776.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 11}]
2025-06-09 08:09:26,459 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:26,912 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMQK
2025-06-09 08:09:26,912 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13147.37, 'new_value': 17880.07}, {'field': 'dailyBillAmount', 'old_value': 13147.37, 'new_value': 17880.07}, {'field': 'amount', 'old_value': 12636.93, 'new_value': 16557.13}, {'field': 'count', 'old_value': 131, 'new_value': 172}, {'field': 'instoreAmount', 'old_value': 10888.1, 'new_value': 14711.7}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 149}, {'field': 'onlineAmount', 'old_value': 1750.33, 'new_value': 1846.93}, {'field': 'onlineCount', 'old_value': 21, 'new_value': 23}]
2025-06-09 08:09:26,912 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:27,397 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXJ
2025-06-09 08:09:27,397 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 97157.72, 'new_value': 113943.46}, {'field': 'dailyBillAmount', 'old_value': 97157.72, 'new_value': 113943.46}, {'field': 'amount', 'old_value': 7950.52, 'new_value': 9679.45}, {'field': 'count', 'old_value': 222, 'new_value': 274}, {'field': 'instoreAmount', 'old_value': 8607.09, 'new_value': 10441.82}, {'field': 'instoreCount', 'old_value': 222, 'new_value': 274}]
2025-06-09 08:09:27,397 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:27,850 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMPK
2025-06-09 08:09:27,850 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 72090.0, 'new_value': 85769.1}, {'field': 'dailyBillAmount', 'old_value': 72090.0, 'new_value': 85769.1}, {'field': 'amount', 'old_value': 71256.8, 'new_value': 84935.4}, {'field': 'count', 'old_value': 91, 'new_value': 107}, {'field': 'instoreAmount', 'old_value': 72233.8, 'new_value': 85912.4}, {'field': 'instoreCount', 'old_value': 91, 'new_value': 107}]
2025-06-09 08:09:27,850 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:28,303 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXR
2025-06-09 08:09:28,303 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9280.0, 'new_value': 11202.0}, {'field': 'dailyBillAmount', 'old_value': 9280.0, 'new_value': 11202.0}, {'field': 'amount', 'old_value': 9333.0, 'new_value': 11273.0}, {'field': 'count', 'old_value': 22, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 10968.0, 'new_value': 12908.0}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 27}]
2025-06-09 08:09:28,303 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:28,740 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYR
2025-06-09 08:09:28,740 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 73874.82, 'new_value': 87267.65}, {'field': 'dailyBillAmount', 'old_value': 65594.01, 'new_value': 77500.05}, {'field': 'amount', 'old_value': 71476.81, 'new_value': 84869.64}, {'field': 'count', 'old_value': 383, 'new_value': 464}, {'field': 'instoreAmount', 'old_value': 72419.81, 'new_value': 85867.64}, {'field': 'instoreCount', 'old_value': 383, 'new_value': 464}]
2025-06-09 08:09:28,756 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:29,178 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0S
2025-06-09 08:09:29,178 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDQR0OPQVI0I86N3H2U1NE001F0K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30676.0, 'new_value': 33516.0}, {'field': 'amount', 'old_value': 30676.0, 'new_value': 33516.0}, {'field': 'count', 'old_value': 7, 'new_value': 8}, {'field': 'instoreAmount', 'old_value': 30676.0, 'new_value': 33516.0}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 8}]
2025-06-09 08:09:29,178 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:29,662 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1S
2025-06-09 08:09:29,662 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 74140.4, 'new_value': 90296.69}, {'field': 'dailyBillAmount', 'old_value': 74140.4, 'new_value': 90296.69}, {'field': 'amount', 'old_value': 73686.4, 'new_value': 89779.69}, {'field': 'count', 'old_value': 422, 'new_value': 512}, {'field': 'instoreAmount', 'old_value': 73686.4, 'new_value': 89779.69}, {'field': 'instoreCount', 'old_value': 422, 'new_value': 512}]
2025-06-09 08:09:29,662 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:30,147 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2S
2025-06-09 08:09:30,147 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24337.88, 'new_value': 29440.22}, {'field': 'dailyBillAmount', 'old_value': 24337.88, 'new_value': 29440.22}, {'field': 'amount', 'old_value': 13125.38, 'new_value': 15662.54}, {'field': 'count', 'old_value': 1175, 'new_value': 1417}, {'field': 'instoreAmount', 'old_value': 13470.85, 'new_value': 16047.51}, {'field': 'instoreCount', 'old_value': 1175, 'new_value': 1417}]
2025-06-09 08:09:30,147 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:30,693 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4S
2025-06-09 08:09:30,693 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 59029.3, 'new_value': 70964.0}, {'field': 'amount', 'old_value': 59029.3, 'new_value': 70964.0}, {'field': 'count', 'old_value': 365, 'new_value': 446}, {'field': 'instoreAmount', 'old_value': 59029.3, 'new_value': 70964.0}, {'field': 'instoreCount', 'old_value': 365, 'new_value': 446}]
2025-06-09 08:09:30,693 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:31,225 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5S
2025-06-09 08:09:31,225 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-06, 变更字段: [{'field': 'amount', 'old_value': 40041.76, 'new_value': 48900.57}, {'field': 'count', 'old_value': 1620, 'new_value': 1940}, {'field': 'instoreAmount', 'old_value': 12000.45, 'new_value': 16026.65}, {'field': 'instoreCount', 'old_value': 494, 'new_value': 622}, {'field': 'onlineAmount', 'old_value': 28690.71, 'new_value': 33687.82}, {'field': 'onlineCount', 'old_value': 1126, 'new_value': 1318}]
2025-06-09 08:09:31,225 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:31,725 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6S
2025-06-09 08:09:31,725 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21980.22, 'new_value': 25743.6}, {'field': 'dailyBillAmount', 'old_value': 21980.22, 'new_value': 25743.6}, {'field': 'amount', 'old_value': 32790.1, 'new_value': 38615.63}, {'field': 'count', 'old_value': 1662, 'new_value': 1979}, {'field': 'instoreAmount', 'old_value': 17578.08, 'new_value': 21201.76}, {'field': 'instoreCount', 'old_value': 987, 'new_value': 1184}, {'field': 'onlineAmount', 'old_value': 15761.42, 'new_value': 18097.57}, {'field': 'onlineCount', 'old_value': 675, 'new_value': 795}]
2025-06-09 08:09:31,725 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:32,193 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7S
2025-06-09 08:09:32,193 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_2025-06, 变更字段: [{'field': 'count', 'old_value': 43, 'new_value': 47}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 47}]
2025-06-09 08:09:32,193 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:32,662 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8S
2025-06-09 08:09:32,662 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14382.0, 'new_value': 21016.0}, {'field': 'amount', 'old_value': 14382.0, 'new_value': 21016.0}, {'field': 'count', 'old_value': 10, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 14382.0, 'new_value': 21016.0}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 16}]
2025-06-09 08:09:32,662 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:33,193 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9S
2025-06-09 08:09:33,193 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32785.03, 'new_value': 38928.82}, {'field': 'dailyBillAmount', 'old_value': 32785.03, 'new_value': 38928.82}, {'field': 'amount', 'old_value': 16760.06, 'new_value': 20024.26}, {'field': 'count', 'old_value': 1182, 'new_value': 1409}, {'field': 'instoreAmount', 'old_value': 3281.1, 'new_value': 3734.8}, {'field': 'instoreCount', 'old_value': 131, 'new_value': 154}, {'field': 'onlineAmount', 'old_value': 13478.96, 'new_value': 16289.46}, {'field': 'onlineCount', 'old_value': 1051, 'new_value': 1255}]
2025-06-09 08:09:33,193 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:33,631 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAS
2025-06-09 08:09:33,631 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 83711.2, 'new_value': 102410.66}, {'field': 'dailyBillAmount', 'old_value': 83711.2, 'new_value': 102410.66}, {'field': 'amount', 'old_value': 79362.93, 'new_value': 98614.17}, {'field': 'count', 'old_value': 699, 'new_value': 861}, {'field': 'instoreAmount', 'old_value': 64897.43, 'new_value': 80981.87}, {'field': 'instoreCount', 'old_value': 417, 'new_value': 520}, {'field': 'onlineAmount', 'old_value': 14465.5, 'new_value': 17632.3}, {'field': 'onlineCount', 'old_value': 282, 'new_value': 341}]
2025-06-09 08:09:33,631 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:34,162 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBS
2025-06-09 08:09:34,178 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 115186.94, 'new_value': 136307.78}, {'field': 'dailyBillAmount', 'old_value': 115186.94, 'new_value': 136307.78}, {'field': 'amount', 'old_value': 104873.8, 'new_value': 122370.76}, {'field': 'count', 'old_value': 579, 'new_value': 689}, {'field': 'instoreAmount', 'old_value': 105625.8, 'new_value': 124734.76}, {'field': 'instoreCount', 'old_value': 579, 'new_value': 689}]
2025-06-09 08:09:34,178 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:34,631 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCS
2025-06-09 08:09:34,631 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 272090.51, 'new_value': 322930.86}, {'field': 'dailyBillAmount', 'old_value': 272090.51, 'new_value': 322930.86}, {'field': 'amount', 'old_value': 297701.55, 'new_value': 353614.46}, {'field': 'count', 'old_value': 1718, 'new_value': 2020}, {'field': 'instoreAmount', 'old_value': 226001.11, 'new_value': 268457.36}, {'field': 'instoreCount', 'old_value': 906, 'new_value': 1076}, {'field': 'onlineAmount', 'old_value': 73038.16, 'new_value': 86830.66}, {'field': 'onlineCount', 'old_value': 812, 'new_value': 944}]
2025-06-09 08:09:34,631 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:35,068 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDS
2025-06-09 08:09:35,084 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 91083.33, 'new_value': 111189.44}, {'field': 'dailyBillAmount', 'old_value': 91083.33, 'new_value': 111189.44}, {'field': 'amount', 'old_value': 128059.8, 'new_value': 157502.45}, {'field': 'count', 'old_value': 622, 'new_value': 764}, {'field': 'instoreAmount', 'old_value': 120276.2, 'new_value': 148224.76}, {'field': 'instoreCount', 'old_value': 486, 'new_value': 599}, {'field': 'onlineAmount', 'old_value': 8052.15, 'new_value': 9546.24}, {'field': 'onlineCount', 'old_value': 136, 'new_value': 165}]
2025-06-09 08:09:35,084 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:35,600 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMES
2025-06-09 08:09:35,600 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 110451.24, 'new_value': 134923.28}, {'field': 'dailyBillAmount', 'old_value': 110451.24, 'new_value': 134923.28}, {'field': 'amount', 'old_value': 105572.6, 'new_value': 128421.6}, {'field': 'count', 'old_value': 458, 'new_value': 562}, {'field': 'instoreAmount', 'old_value': 107086.5, 'new_value': 130291.4}, {'field': 'instoreCount', 'old_value': 458, 'new_value': 562}]
2025-06-09 08:09:35,600 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:36,115 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFS
2025-06-09 08:09:36,115 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 230612.26, 'new_value': 279436.34}, {'field': 'amount', 'old_value': 230612.26, 'new_value': 279436.34}, {'field': 'count', 'old_value': 1747, 'new_value': 2115}, {'field': 'instoreAmount', 'old_value': 230612.26, 'new_value': 279436.34}, {'field': 'instoreCount', 'old_value': 1747, 'new_value': 2115}]
2025-06-09 08:09:36,115 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:36,568 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGS
2025-06-09 08:09:36,568 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 219518.25, 'new_value': 262405.26}, {'field': 'dailyBillAmount', 'old_value': 219518.25, 'new_value': 262405.26}, {'field': 'amount', 'old_value': 253281.93, 'new_value': 300918.85}, {'field': 'count', 'old_value': 1744, 'new_value': 2074}, {'field': 'instoreAmount', 'old_value': 145545.0, 'new_value': 173458.4}, {'field': 'instoreCount', 'old_value': 767, 'new_value': 934}, {'field': 'onlineAmount', 'old_value': 111336.8, 'new_value': 131733.3}, {'field': 'onlineCount', 'old_value': 977, 'new_value': 1140}]
2025-06-09 08:09:36,568 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:37,037 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHS
2025-06-09 08:09:37,037 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 138065.61, 'new_value': 167158.67}, {'field': 'dailyBillAmount', 'old_value': 138065.61, 'new_value': 167158.67}, {'field': 'amount', 'old_value': 136777.66, 'new_value': 166566.41}, {'field': 'count', 'old_value': 1357, 'new_value': 1635}, {'field': 'instoreAmount', 'old_value': 101818.46, 'new_value': 125108.96}, {'field': 'instoreCount', 'old_value': 673, 'new_value': 833}, {'field': 'onlineAmount', 'old_value': 35323.77, 'new_value': 41872.92}, {'field': 'onlineCount', 'old_value': 684, 'new_value': 802}]
2025-06-09 08:09:37,037 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:37,615 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIS
2025-06-09 08:09:37,615 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 155949.22, 'new_value': 184180.23}, {'field': 'dailyBillAmount', 'old_value': 155949.22, 'new_value': 184180.23}, {'field': 'amount', 'old_value': 156991.18, 'new_value': 185175.28}, {'field': 'count', 'old_value': 1292, 'new_value': 1479}, {'field': 'instoreAmount', 'old_value': 141772.16, 'new_value': 168294.9}, {'field': 'instoreCount', 'old_value': 758, 'new_value': 885}, {'field': 'onlineAmount', 'old_value': 15294.1, 'new_value': 17006.46}, {'field': 'onlineCount', 'old_value': 534, 'new_value': 594}]
2025-06-09 08:09:37,631 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:38,053 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJS
2025-06-09 08:09:38,053 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40813.4, 'new_value': 46324.4}, {'field': 'amount', 'old_value': 40813.4, 'new_value': 46324.4}, {'field': 'count', 'old_value': 166, 'new_value': 201}, {'field': 'instoreAmount', 'old_value': 40813.4, 'new_value': 46324.4}, {'field': 'instoreCount', 'old_value': 166, 'new_value': 201}]
2025-06-09 08:09:38,053 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:38,490 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKS
2025-06-09 08:09:38,490 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 113722.82, 'new_value': 130115.24}, {'field': 'dailyBillAmount', 'old_value': 113722.82, 'new_value': 130115.24}, {'field': 'amount', 'old_value': -84769.58, 'new_value': -100454.16}, {'field': 'count', 'old_value': 243, 'new_value': 297}, {'field': 'instoreAmount', 'old_value': 2075.3, 'new_value': 2478.0}, {'field': 'instoreCount', 'old_value': 89, 'new_value': 109}, {'field': 'onlineAmount', 'old_value': 3757.92, 'new_value': 4478.64}, {'field': 'onlineCount', 'old_value': 154, 'new_value': 188}]
2025-06-09 08:09:38,490 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:38,928 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMS
2025-06-09 08:09:38,928 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 148328.17, 'new_value': 171854.15}, {'field': 'dailyBillAmount', 'old_value': 148328.17, 'new_value': 171854.15}, {'field': 'amount', 'old_value': 44938.9, 'new_value': 50206.9}, {'field': 'count', 'old_value': 185, 'new_value': 216}, {'field': 'instoreAmount', 'old_value': 45463.1, 'new_value': 50375.6}, {'field': 'instoreCount', 'old_value': 177, 'new_value': 206}, {'field': 'onlineAmount', 'old_value': 610.8, 'new_value': 1113.3}, {'field': 'onlineCount', 'old_value': 8, 'new_value': 10}]
2025-06-09 08:09:38,928 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:39,365 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNS
2025-06-09 08:09:39,365 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 100201.18, 'new_value': 119796.73}, {'field': 'dailyBillAmount', 'old_value': 100201.18, 'new_value': 119796.73}, {'field': 'amount', 'old_value': 98398.12, 'new_value': 117594.33}, {'field': 'count', 'old_value': 591, 'new_value': 702}, {'field': 'instoreAmount', 'old_value': 94096.83, 'new_value': 112804.53}, {'field': 'instoreCount', 'old_value': 473, 'new_value': 570}, {'field': 'onlineAmount', 'old_value': 4359.35, 'new_value': 4847.86}, {'field': 'onlineCount', 'old_value': 118, 'new_value': 132}]
2025-06-09 08:09:39,365 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:39,818 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOS
2025-06-09 08:09:39,818 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 98571.48, 'new_value': 118240.79}, {'field': 'dailyBillAmount', 'old_value': 98571.48, 'new_value': 118240.79}, {'field': 'amount', 'old_value': 43831.4, 'new_value': 50810.84}, {'field': 'count', 'old_value': 592, 'new_value': 678}, {'field': 'instoreAmount', 'old_value': 30262.63, 'new_value': 35087.24}, {'field': 'instoreCount', 'old_value': 179, 'new_value': 199}, {'field': 'onlineAmount', 'old_value': 13568.77, 'new_value': 15723.6}, {'field': 'onlineCount', 'old_value': 413, 'new_value': 479}]
2025-06-09 08:09:39,818 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:40,303 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPS
2025-06-09 08:09:40,303 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41729.88, 'new_value': 47276.86}, {'field': 'amount', 'old_value': 41729.47, 'new_value': 47276.45}, {'field': 'count', 'old_value': 1935, 'new_value': 2215}, {'field': 'instoreAmount', 'old_value': 10890.65, 'new_value': 12613.42}, {'field': 'instoreCount', 'old_value': 451, 'new_value': 522}, {'field': 'onlineAmount', 'old_value': 31580.31, 'new_value': 35613.51}, {'field': 'onlineCount', 'old_value': 1484, 'new_value': 1693}]
2025-06-09 08:09:40,303 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:40,772 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQS
2025-06-09 08:09:40,772 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13766.0, 'new_value': 16222.0}, {'field': 'amount', 'old_value': 13766.0, 'new_value': 16222.0}, {'field': 'count', 'old_value': 57, 'new_value': 66}, {'field': 'instoreAmount', 'old_value': 13766.0, 'new_value': 16222.0}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 66}]
2025-06-09 08:09:40,772 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:41,178 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRS
2025-06-09 08:09:41,178 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 116135.03, 'new_value': 137099.43}, {'field': 'dailyBillAmount', 'old_value': 116135.03, 'new_value': 137099.43}, {'field': 'amount', 'old_value': 45771.29, 'new_value': 54727.99}, {'field': 'count', 'old_value': 881, 'new_value': 1052}, {'field': 'instoreAmount', 'old_value': 46136.6, 'new_value': 55125.8}, {'field': 'instoreCount', 'old_value': 881, 'new_value': 1052}]
2025-06-09 08:09:41,178 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:41,600 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSS
2025-06-09 08:09:41,600 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 53287.06, 'new_value': 63995.98}, {'field': 'amount', 'old_value': 53287.06, 'new_value': 63995.98}, {'field': 'count', 'old_value': 1235, 'new_value': 1472}, {'field': 'instoreAmount', 'old_value': 53287.06, 'new_value': 63995.98}, {'field': 'instoreCount', 'old_value': 1235, 'new_value': 1472}]
2025-06-09 08:09:41,600 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:42,068 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTS
2025-06-09 08:09:42,068 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12145.79, 'new_value': 14321.39}, {'field': 'amount', 'old_value': 12145.79, 'new_value': 14321.39}, {'field': 'count', 'old_value': 716, 'new_value': 861}, {'field': 'instoreAmount', 'old_value': 4824.5, 'new_value': 5939.88}, {'field': 'instoreCount', 'old_value': 242, 'new_value': 304}, {'field': 'onlineAmount', 'old_value': 7565.17, 'new_value': 8628.66}, {'field': 'onlineCount', 'old_value': 474, 'new_value': 557}]
2025-06-09 08:09:42,068 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:42,490 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUS
2025-06-09 08:09:42,490 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16656.1, 'new_value': 18318.7}, {'field': 'amount', 'old_value': 16656.1, 'new_value': 18318.7}, {'field': 'count', 'old_value': 46, 'new_value': 53}, {'field': 'instoreAmount', 'old_value': 16656.1, 'new_value': 18318.7}, {'field': 'instoreCount', 'old_value': 46, 'new_value': 53}]
2025-06-09 08:09:42,490 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:42,943 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZR
2025-06-09 08:09:42,943 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 42123.0, 'new_value': 49043.0}, {'field': 'amount', 'old_value': 42123.0, 'new_value': 49043.0}, {'field': 'count', 'old_value': 225, 'new_value': 279}, {'field': 'instoreAmount', 'old_value': 44501.0, 'new_value': 53103.0}, {'field': 'instoreCount', 'old_value': 225, 'new_value': 279}]
2025-06-09 08:09:42,959 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-09 08:09:43,381 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MX1
2025-06-09 08:09:43,381 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ08R852VK83IKSIOCDI7KE001FRF_2025-04, 变更字段: [{'field': 'amount', 'old_value': 160341.49, 'new_value': 160340.49}]
2025-06-09 08:09:43,381 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:43,850 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZS
2025-06-09 08:09:43,850 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36220.62, 'new_value': 43141.83}, {'field': 'dailyBillAmount', 'old_value': 36342.33, 'new_value': 43139.54}, {'field': 'amount', 'old_value': 36220.62, 'new_value': 43141.83}, {'field': 'count', 'old_value': 2015, 'new_value': 2388}, {'field': 'instoreAmount', 'old_value': 17879.08, 'new_value': 21613.21}, {'field': 'instoreCount', 'old_value': 936, 'new_value': 1138}, {'field': 'onlineAmount', 'old_value': 18611.19, 'new_value': 21798.27}, {'field': 'onlineCount', 'old_value': 1079, 'new_value': 1250}]
2025-06-09 08:09:43,850 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:44,303 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0T
2025-06-09 08:09:44,303 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20120.11, 'new_value': 23727.32}, {'field': 'amount', 'old_value': 20120.11, 'new_value': 23727.32}, {'field': 'count', 'old_value': 1266, 'new_value': 1486}, {'field': 'instoreAmount', 'old_value': 10958.92, 'new_value': 13135.97}, {'field': 'instoreCount', 'old_value': 608, 'new_value': 729}, {'field': 'onlineAmount', 'old_value': 10259.24, 'new_value': 11890.59}, {'field': 'onlineCount', 'old_value': 658, 'new_value': 757}]
2025-06-09 08:09:44,303 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:44,725 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1T
2025-06-09 08:09:44,725 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7686.54}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7686.54}, {'field': 'amount', 'old_value': 53532.44, 'new_value': 64230.86}, {'field': 'count', 'old_value': 539, 'new_value': 631}, {'field': 'instoreAmount', 'old_value': 53545.44, 'new_value': 64256.06}, {'field': 'instoreCount', 'old_value': 539, 'new_value': 631}]
2025-06-09 08:09:44,725 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:45,209 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2T
2025-06-09 08:09:45,209 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29509.0, 'new_value': 35008.92}, {'field': 'dailyBillAmount', 'old_value': 30755.36, 'new_value': 36400.47}, {'field': 'amount', 'old_value': 29509.0, 'new_value': 35008.92}, {'field': 'count', 'old_value': 778, 'new_value': 914}, {'field': 'instoreAmount', 'old_value': 26941.92, 'new_value': 32022.39}, {'field': 'instoreCount', 'old_value': 564, 'new_value': 669}, {'field': 'onlineAmount', 'old_value': 2597.38, 'new_value': 3016.83}, {'field': 'onlineCount', 'old_value': 214, 'new_value': 245}]
2025-06-09 08:09:45,209 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:45,631 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVS
2025-06-09 08:09:45,631 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 52130.99, 'new_value': 58941.72}, {'field': 'dailyBillAmount', 'old_value': 42875.0, 'new_value': 48053.1}, {'field': 'amount', 'old_value': 52130.99, 'new_value': 58941.72}, {'field': 'count', 'old_value': 742, 'new_value': 865}, {'field': 'instoreAmount', 'old_value': 49379.5, 'new_value': 55931.1}, {'field': 'instoreCount', 'old_value': 619, 'new_value': 726}, {'field': 'onlineAmount', 'old_value': 2780.49, 'new_value': 3134.62}, {'field': 'onlineCount', 'old_value': 123, 'new_value': 139}]
2025-06-09 08:09:45,631 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:46,084 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWS
2025-06-09 08:09:46,084 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9050.4, 'new_value': 10804.28}, {'field': 'amount', 'old_value': 9050.4, 'new_value': 10804.28}, {'field': 'count', 'old_value': 400, 'new_value': 477}, {'field': 'instoreAmount', 'old_value': 7852.3, 'new_value': 9532.48}, {'field': 'instoreCount', 'old_value': 361, 'new_value': 436}, {'field': 'onlineAmount', 'old_value': 1232.5, 'new_value': 1306.2}, {'field': 'onlineCount', 'old_value': 39, 'new_value': 41}]
2025-06-09 08:09:46,084 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:46,521 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXS
2025-06-09 08:09:46,521 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 117101.29, 'new_value': 136111.7}, {'field': 'dailyBillAmount', 'old_value': 117101.29, 'new_value': 136111.7}, {'field': 'amount', 'old_value': 151798.34, 'new_value': 176544.51}, {'field': 'count', 'old_value': 1337, 'new_value': 1549}, {'field': 'instoreAmount', 'old_value': 144835.0, 'new_value': 167817.84}, {'field': 'instoreCount', 'old_value': 929, 'new_value': 1066}, {'field': 'onlineAmount', 'old_value': 10087.76, 'new_value': 12216.09}, {'field': 'onlineCount', 'old_value': 408, 'new_value': 483}]
2025-06-09 08:09:46,521 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:46,975 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYS
2025-06-09 08:09:46,975 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-06, 变更字段: [{'field': 'amount', 'old_value': 11834.75, 'new_value': 14165.56}, {'field': 'count', 'old_value': 184, 'new_value': 223}, {'field': 'instoreAmount', 'old_value': 8359.87, 'new_value': 10016.78}, {'field': 'instoreCount', 'old_value': 116, 'new_value': 140}, {'field': 'onlineAmount', 'old_value': 3575.86, 'new_value': 4278.16}, {'field': 'onlineCount', 'old_value': 68, 'new_value': 83}]
2025-06-09 08:09:47,021 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:47,553 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGT
2025-06-09 08:09:47,553 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 236020.73, 'new_value': 272818.43}, {'field': 'dailyBillAmount', 'old_value': 236020.73, 'new_value': 272818.43}, {'field': 'amount', 'old_value': 170310.0, 'new_value': 197368.3}, {'field': 'count', 'old_value': 1158, 'new_value': 1343}, {'field': 'instoreAmount', 'old_value': 123665.6, 'new_value': 142582.4}, {'field': 'instoreCount', 'old_value': 938, 'new_value': 1084}, {'field': 'onlineAmount', 'old_value': 46644.4, 'new_value': 54785.9}, {'field': 'onlineCount', 'old_value': 220, 'new_value': 259}]
2025-06-09 08:09:47,568 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:48,021 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHT
2025-06-09 08:09:48,021 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 299710.15, 'new_value': 339327.66}, {'field': 'amount', 'old_value': 299710.15, 'new_value': 339327.66}, {'field': 'count', 'old_value': 1029, 'new_value': 1176}, {'field': 'instoreAmount', 'old_value': 299567.15, 'new_value': 339184.66}, {'field': 'instoreCount', 'old_value': 1028, 'new_value': 1175}]
2025-06-09 08:09:48,021 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:48,490 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIT
2025-06-09 08:09:48,490 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 149680.4, 'new_value': 166751.79}, {'field': 'dailyBillAmount', 'old_value': 134212.95, 'new_value': 149594.64}, {'field': 'amount', 'old_value': 149680.4, 'new_value': 166751.79}, {'field': 'count', 'old_value': 960, 'new_value': 1060}, {'field': 'instoreAmount', 'old_value': 136236.75, 'new_value': 152210.48}, {'field': 'instoreCount', 'old_value': 584, 'new_value': 658}, {'field': 'onlineAmount', 'old_value': 13537.54, 'new_value': 14665.9}, {'field': 'onlineCount', 'old_value': 376, 'new_value': 402}]
2025-06-09 08:09:48,490 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:48,896 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJT
2025-06-09 08:09:48,896 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 104911.28, 'new_value': 124467.82}, {'field': 'dailyBillAmount', 'old_value': 90992.89, 'new_value': 110549.43}, {'field': 'amount', 'old_value': 104911.28, 'new_value': 124467.82}, {'field': 'count', 'old_value': 333, 'new_value': 388}, {'field': 'instoreAmount', 'old_value': 94701.8, 'new_value': 113029.3}, {'field': 'instoreCount', 'old_value': 253, 'new_value': 294}, {'field': 'onlineAmount', 'old_value': 10376.31, 'new_value': 11605.35}, {'field': 'onlineCount', 'old_value': 80, 'new_value': 94}]
2025-06-09 08:09:48,896 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:49,365 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3T
2025-06-09 08:09:49,365 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 57479.08, 'new_value': 62025.76}, {'field': 'dailyBillAmount', 'old_value': 57479.08, 'new_value': 62025.76}, {'field': 'amount', 'old_value': 7832.67, 'new_value': 8728.67}, {'field': 'count', 'old_value': 268, 'new_value': 294}, {'field': 'instoreAmount', 'old_value': 8563.0, 'new_value': 9487.8}, {'field': 'instoreCount', 'old_value': 268, 'new_value': 294}]
2025-06-09 08:09:49,365 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:49,912 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAT
2025-06-09 08:09:49,912 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29704.05, 'new_value': 34212.98}, {'field': 'dailyBillAmount', 'old_value': 14667.72, 'new_value': 17380.37}, {'field': 'amount', 'old_value': 29704.05, 'new_value': 34212.98}, {'field': 'count', 'old_value': 756, 'new_value': 869}, {'field': 'instoreAmount', 'old_value': 15972.39, 'new_value': 18960.8}, {'field': 'instoreCount', 'old_value': 405, 'new_value': 477}, {'field': 'onlineAmount', 'old_value': 14143.1, 'new_value': 15921.18}, {'field': 'onlineCount', 'old_value': 351, 'new_value': 392}]
2025-06-09 08:09:49,912 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:50,412 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9T
2025-06-09 08:09:50,412 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-06, 变更字段: [{'field': 'amount', 'old_value': 1804.5, 'new_value': 1935.98}, {'field': 'count', 'old_value': 87, 'new_value': 93}, {'field': 'onlineAmount', 'old_value': 1858.5700000000002, 'new_value': 1990.05}, {'field': 'onlineCount', 'old_value': 87, 'new_value': 93}]
2025-06-09 08:09:50,412 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:50,881 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8T
2025-06-09 08:09:50,881 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-06, 变更字段: [{'field': 'amount', 'old_value': 1720.67, 'new_value': 1808.15}, {'field': 'count', 'old_value': 65, 'new_value': 69}, {'field': 'instoreAmount', 'old_value': 1720.67, 'new_value': 1808.15}, {'field': 'instoreCount', 'old_value': 65, 'new_value': 69}]
2025-06-09 08:09:50,881 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:51,318 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMT
2025-06-09 08:09:51,318 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 43104.8, 'new_value': 47285.8}, {'field': 'dailyBillAmount', 'old_value': 43104.8, 'new_value': 47285.8}, {'field': 'amount', 'old_value': 42110.0, 'new_value': 46213.0}, {'field': 'count', 'old_value': 74, 'new_value': 83}, {'field': 'instoreAmount', 'old_value': 42110.0, 'new_value': 46213.0}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 83}]
2025-06-09 08:09:51,334 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:51,834 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7T
2025-06-09 08:09:51,834 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50679.04, 'new_value': 56565.96}, {'field': 'dailyBillAmount', 'old_value': 47061.84, 'new_value': 52948.76}, {'field': 'amount', 'old_value': 35646.42, 'new_value': 39642.69}, {'field': 'count', 'old_value': 1088, 'new_value': 1226}, {'field': 'instoreAmount', 'old_value': 7239.83, 'new_value': 8292.34}, {'field': 'instoreCount', 'old_value': 161, 'new_value': 188}, {'field': 'onlineAmount', 'old_value': 28542.55, 'new_value': 31525.48}, {'field': 'onlineCount', 'old_value': 927, 'new_value': 1038}]
2025-06-09 08:09:51,834 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:52,303 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6T
2025-06-09 08:09:52,318 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 99753.76, 'new_value': 112307.34}, {'field': 'amount', 'old_value': 99753.76, 'new_value': 112307.34}, {'field': 'count', 'old_value': 1036, 'new_value': 1145}, {'field': 'instoreAmount', 'old_value': 93556.6, 'new_value': 105436.2}, {'field': 'instoreCount', 'old_value': 823, 'new_value': 914}, {'field': 'onlineAmount', 'old_value': 7146.27, 'new_value': 7938.65}, {'field': 'onlineCount', 'old_value': 213, 'new_value': 231}]
2025-06-09 08:09:52,318 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:52,709 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5T
2025-06-09 08:09:52,709 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 5278.93, 'new_value': 5795.84}, {'field': 'count', 'old_value': 270, 'new_value': 292}, {'field': 'onlineAmount', 'old_value': 5321.01, 'new_value': 5854.47}, {'field': 'onlineCount', 'old_value': 270, 'new_value': 292}]
2025-06-09 08:09:52,709 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:53,178 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQT
2025-06-09 08:09:53,178 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 101120.69, 'new_value': 115482.86}, {'field': 'dailyBillAmount', 'old_value': 101120.69, 'new_value': 115482.86}, {'field': 'amount', 'old_value': 103258.98, 'new_value': 118963.32}, {'field': 'count', 'old_value': 2897, 'new_value': 3314}, {'field': 'instoreAmount', 'old_value': 96204.32, 'new_value': 110850.91}, {'field': 'instoreCount', 'old_value': 2500, 'new_value': 2854}, {'field': 'onlineAmount', 'old_value': 8648.32, 'new_value': 9958.68}, {'field': 'onlineCount', 'old_value': 397, 'new_value': 460}]
2025-06-09 08:09:53,178 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:53,615 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPT
2025-06-09 08:09:53,615 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20643.58, 'new_value': 24458.83}, {'field': 'amount', 'old_value': 20643.58, 'new_value': 24458.83}, {'field': 'count', 'old_value': 192, 'new_value': 230}, {'field': 'instoreAmount', 'old_value': 20959.74, 'new_value': 24774.99}, {'field': 'instoreCount', 'old_value': 192, 'new_value': 230}]
2025-06-09 08:09:53,615 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:54,068 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOT
2025-06-09 08:09:54,068 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6118.53, 'new_value': 6980.24}, {'field': 'amount', 'old_value': 6118.53, 'new_value': 6980.24}, {'field': 'count', 'old_value': 130, 'new_value': 146}, {'field': 'instoreAmount', 'old_value': 6128.31, 'new_value': 6990.02}, {'field': 'instoreCount', 'old_value': 130, 'new_value': 146}]
2025-06-09 08:09:54,068 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:54,568 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNT
2025-06-09 08:09:54,568 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32103.36, 'new_value': 36121.78}, {'field': 'dailyBillAmount', 'old_value': 32103.36, 'new_value': 36121.78}, {'field': 'amount', 'old_value': 27962.26, 'new_value': 30214.17}, {'field': 'count', 'old_value': 34, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 30702.65, 'new_value': 32829.25}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 32}, {'field': 'onlineAmount', 'old_value': 644.41, 'new_value': 769.72}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 6}]
2025-06-09 08:09:54,568 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:55,068 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKT
2025-06-09 08:09:55,068 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 208169.75, 'new_value': 240833.31}, {'field': 'amount', 'old_value': 208169.75, 'new_value': 240833.31}, {'field': 'count', 'old_value': 1267, 'new_value': 1445}, {'field': 'instoreAmount', 'old_value': 190060.43, 'new_value': 218915.01}, {'field': 'instoreCount', 'old_value': 690, 'new_value': 785}, {'field': 'onlineAmount', 'old_value': 18109.53, 'new_value': 21918.51}, {'field': 'onlineCount', 'old_value': 577, 'new_value': 660}]
2025-06-09 08:09:55,084 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:55,521 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLT
2025-06-09 08:09:55,521 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 202029.73, 'new_value': 229536.41}, {'field': 'dailyBillAmount', 'old_value': 202029.73, 'new_value': 229536.41}, {'field': 'amount', 'old_value': 190162.02, 'new_value': 216064.37}, {'field': 'count', 'old_value': 970, 'new_value': 1114}, {'field': 'instoreAmount', 'old_value': 173132.64, 'new_value': 196172.83}, {'field': 'instoreCount', 'old_value': 789, 'new_value': 902}, {'field': 'onlineAmount', 'old_value': 17370.64, 'new_value': 20273.1}, {'field': 'onlineCount', 'old_value': 181, 'new_value': 212}]
2025-06-09 08:09:55,521 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:55,959 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4T
2025-06-09 08:09:55,975 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 158351.86, 'new_value': 180117.36}, {'field': 'dailyBillAmount', 'old_value': 158351.86, 'new_value': 180117.36}, {'field': 'amount', 'old_value': 15491.2, 'new_value': 17088.2}, {'field': 'count', 'old_value': 84, 'new_value': 91}, {'field': 'instoreAmount', 'old_value': 15491.2, 'new_value': 17088.2}, {'field': 'instoreCount', 'old_value': 84, 'new_value': 91}]
2025-06-09 08:09:55,975 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:56,459 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFT
2025-06-09 08:09:56,459 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 64398.01, 'new_value': 71264.58}, {'field': 'dailyBillAmount', 'old_value': 64398.01, 'new_value': 71264.58}, {'field': 'amount', 'old_value': 42675.37, 'new_value': 46834.22}, {'field': 'count', 'old_value': 1067, 'new_value': 1183}, {'field': 'instoreAmount', 'old_value': 27677.12, 'new_value': 29892.02}, {'field': 'instoreCount', 'old_value': 557, 'new_value': 599}, {'field': 'onlineAmount', 'old_value': 19649.46, 'new_value': 22209.6}, {'field': 'onlineCount', 'old_value': 510, 'new_value': 584}]
2025-06-09 08:09:56,459 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:56,850 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBT
2025-06-09 08:09:56,850 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40546.81, 'new_value': 45693.79}, {'field': 'amount', 'old_value': 40546.81, 'new_value': 45693.79}, {'field': 'count', 'old_value': 1923, 'new_value': 2215}, {'field': 'instoreAmount', 'old_value': 41191.74, 'new_value': 46425.39}, {'field': 'instoreCount', 'old_value': 1923, 'new_value': 2215}]
2025-06-09 08:09:56,850 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:57,271 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCT
2025-06-09 08:09:57,271 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16543.87, 'new_value': 18464.27}, {'field': 'dailyBillAmount', 'old_value': 16543.87, 'new_value': 18464.27}, {'field': 'amount', 'old_value': 10390.46, 'new_value': 11679.39}, {'field': 'count', 'old_value': 497, 'new_value': 553}, {'field': 'instoreAmount', 'old_value': 4146.09, 'new_value': 4672.19}, {'field': 'instoreCount', 'old_value': 143, 'new_value': 159}, {'field': 'onlineAmount', 'old_value': 6254.63, 'new_value': 7017.46}, {'field': 'onlineCount', 'old_value': 354, 'new_value': 394}]
2025-06-09 08:09:57,271 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:57,740 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDT
2025-06-09 08:09:57,740 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18299.9, 'new_value': 20592.1}, {'field': 'amount', 'old_value': 18299.9, 'new_value': 20592.1}, {'field': 'count', 'old_value': 609, 'new_value': 671}, {'field': 'instoreAmount', 'old_value': 8989.39, 'new_value': 9662.2}, {'field': 'instoreCount', 'old_value': 384, 'new_value': 417}, {'field': 'onlineAmount', 'old_value': 9310.51, 'new_value': 10962.52}, {'field': 'onlineCount', 'old_value': 225, 'new_value': 254}]
2025-06-09 08:09:57,740 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:58,193 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMET
2025-06-09 08:09:58,193 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11715.05, 'new_value': 13216.21}, {'field': 'amount', 'old_value': 11715.05, 'new_value': 13216.21}, {'field': 'count', 'old_value': 297, 'new_value': 335}, {'field': 'instoreAmount', 'old_value': 9302.7, 'new_value': 10580.1}, {'field': 'instoreCount', 'old_value': 249, 'new_value': 280}, {'field': 'onlineAmount', 'old_value': 2672.35, 'new_value': 2896.11}, {'field': 'onlineCount', 'old_value': 48, 'new_value': 55}]
2025-06-09 08:09:58,209 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:58,615 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMYT
2025-06-09 08:09:58,615 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 78615.65, 'new_value': 91602.0}, {'field': 'dailyBillAmount', 'old_value': 78615.65, 'new_value': 91602.0}, {'field': 'amount', 'old_value': 84141.6, 'new_value': 96443.6}, {'field': 'count', 'old_value': 579, 'new_value': 682}, {'field': 'instoreAmount', 'old_value': 84815.6, 'new_value': 97117.6}, {'field': 'instoreCount', 'old_value': 579, 'new_value': 682}]
2025-06-09 08:09:58,615 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:59,053 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMXT
2025-06-09 08:09:59,053 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 100481.88, 'new_value': 116296.25}, {'field': 'dailyBillAmount', 'old_value': 100481.88, 'new_value': 116296.25}, {'field': 'amount', 'old_value': 163344.44, 'new_value': 191242.39}, {'field': 'count', 'old_value': 276, 'new_value': 318}, {'field': 'instoreAmount', 'old_value': 161445.84, 'new_value': 188912.79}, {'field': 'instoreCount', 'old_value': 265, 'new_value': 304}, {'field': 'onlineAmount', 'old_value': 1898.6, 'new_value': 2329.6}, {'field': 'onlineCount', 'old_value': 11, 'new_value': 14}]
2025-06-09 08:09:59,053 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:59,459 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMWT
2025-06-09 08:09:59,459 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 247226.95, 'new_value': 286370.92}, {'field': 'dailyBillAmount', 'old_value': 247226.95, 'new_value': 286370.92}, {'field': 'amount', 'old_value': 283826.77, 'new_value': 322970.74}, {'field': 'count', 'old_value': 1223, 'new_value': 1416}, {'field': 'instoreAmount', 'old_value': 283826.77, 'new_value': 322970.74}, {'field': 'instoreCount', 'old_value': 1223, 'new_value': 1416}]
2025-06-09 08:09:59,459 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:09:59,881 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMVT
2025-06-09 08:09:59,881 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 81436.53, 'new_value': 94948.69}, {'field': 'dailyBillAmount', 'old_value': 81436.53, 'new_value': 94948.69}, {'field': 'amount', 'old_value': 80963.98, 'new_value': 93571.14}, {'field': 'count', 'old_value': 415, 'new_value': 477}, {'field': 'instoreAmount', 'old_value': 77046.5, 'new_value': 89024.8}, {'field': 'instoreCount', 'old_value': 350, 'new_value': 403}, {'field': 'onlineAmount', 'old_value': 4540.76, 'new_value': 5541.62}, {'field': 'onlineCount', 'old_value': 65, 'new_value': 74}]
2025-06-09 08:09:59,881 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:00,365 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMUT
2025-06-09 08:10:00,365 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 494371.29, 'new_value': 561170.3}, {'field': 'dailyBillAmount', 'old_value': 494371.29, 'new_value': 561170.3}, {'field': 'amount', 'old_value': 425017.0, 'new_value': 486840.0}, {'field': 'count', 'old_value': 991, 'new_value': 1154}, {'field': 'instoreAmount', 'old_value': 444775.0, 'new_value': 509306.0}, {'field': 'instoreCount', 'old_value': 991, 'new_value': 1154}]
2025-06-09 08:10:00,365 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:00,834 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMST
2025-06-09 08:10:00,850 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 314142.81, 'new_value': 362014.9}, {'field': 'amount', 'old_value': 314142.81, 'new_value': 362014.9}, {'field': 'count', 'old_value': 1016, 'new_value': 1168}, {'field': 'instoreAmount', 'old_value': 316087.81, 'new_value': 363959.9}, {'field': 'instoreCount', 'old_value': 1016, 'new_value': 1168}]
2025-06-09 08:10:00,850 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:01,381 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTT
2025-06-09 08:10:01,381 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 263372.08, 'new_value': 289218.48}, {'field': 'dailyBillAmount', 'old_value': 263372.08, 'new_value': 289218.48}, {'field': 'amount', 'old_value': 192613.82, 'new_value': 205084.11}, {'field': 'count', 'old_value': 720, 'new_value': 787}, {'field': 'instoreAmount', 'old_value': 190218.24, 'new_value': 201799.28}, {'field': 'instoreCount', 'old_value': 430, 'new_value': 464}, {'field': 'onlineAmount', 'old_value': 8793.45, 'new_value': 9682.7}, {'field': 'onlineCount', 'old_value': 290, 'new_value': 323}]
2025-06-09 08:10:01,381 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:01,896 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRT
2025-06-09 08:10:01,896 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 194687.11, 'new_value': 215804.21}, {'field': 'dailyBillAmount', 'old_value': 194687.11, 'new_value': 215804.21}, {'field': 'amount', 'old_value': 181811.92, 'new_value': 203069.88}, {'field': 'count', 'old_value': 483, 'new_value': 544}, {'field': 'instoreAmount', 'old_value': 186479.19, 'new_value': 207596.29}, {'field': 'instoreCount', 'old_value': 398, 'new_value': 454}, {'field': 'onlineAmount', 'old_value': 2399.09, 'new_value': 2539.95}, {'field': 'onlineCount', 'old_value': 85, 'new_value': 90}]
2025-06-09 08:10:01,943 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-09 08:10:02,412 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-06-09 08:10:02,412 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'amount', 'old_value': 100046.8, 'new_value': 100045.8}]
2025-06-09 08:10:02,428 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:02,896 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMPW
2025-06-09 08:10:02,896 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 51393.62, 'new_value': 55834.96}, {'field': 'dailyBillAmount', 'old_value': 51393.62, 'new_value': 55834.96}, {'field': 'amount', 'old_value': 54217.35, 'new_value': 59135.55}, {'field': 'count', 'old_value': 365, 'new_value': 408}, {'field': 'instoreAmount', 'old_value': 51593.0, 'new_value': 55595.0}, {'field': 'instoreCount', 'old_value': 315, 'new_value': 350}, {'field': 'onlineAmount', 'old_value': 2656.35, 'new_value': 3572.55}, {'field': 'onlineCount', 'old_value': 50, 'new_value': 58}]
2025-06-09 08:10:02,896 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:03,381 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMFU
2025-06-09 08:10:03,381 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35215.8, 'new_value': 43478.8}, {'field': 'dailyBillAmount', 'old_value': 35215.8, 'new_value': 43478.8}, {'field': 'amount', 'old_value': 42356.0, 'new_value': 52388.0}, {'field': 'count', 'old_value': 171, 'new_value': 219}, {'field': 'instoreAmount', 'old_value': 42356.0, 'new_value': 52388.0}, {'field': 'instoreCount', 'old_value': 171, 'new_value': 219}]
2025-06-09 08:10:03,381 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:03,849 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMGU
2025-06-09 08:10:03,849 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-06, 变更字段: [{'field': 'amount', 'old_value': 15436.0, 'new_value': 17823.0}, {'field': 'count', 'old_value': 81, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 15436.0, 'new_value': 17823.0}, {'field': 'instoreCount', 'old_value': 81, 'new_value': 97}]
2025-06-09 08:10:03,849 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:04,303 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMHU
2025-06-09 08:10:04,303 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28484.0, 'new_value': 32644.0}, {'field': 'amount', 'old_value': 28484.0, 'new_value': 32644.0}, {'field': 'count', 'old_value': 339, 'new_value': 359}, {'field': 'instoreAmount', 'old_value': 28484.0, 'new_value': 32644.0}, {'field': 'instoreCount', 'old_value': 339, 'new_value': 359}]
2025-06-09 08:10:04,303 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:04,834 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMIU
2025-06-09 08:10:04,834 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7013.55, 'new_value': 7565.87}, {'field': 'dailyBillAmount', 'old_value': 7013.55, 'new_value': 7565.87}, {'field': 'amount', 'old_value': 793.49, 'new_value': 857.19}, {'field': 'count', 'old_value': 26, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 1026.49, 'new_value': 1090.19}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 27}]
2025-06-09 08:10:04,834 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:05,287 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMMW
2025-06-09 08:10:05,287 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8569.0, 'new_value': 10473.0}, {'field': 'dailyBillAmount', 'old_value': 8569.0, 'new_value': 10473.0}, {'field': 'amount', 'old_value': 12665.0, 'new_value': 14569.0}, {'field': 'count', 'old_value': 62, 'new_value': 69}, {'field': 'instoreAmount', 'old_value': 12665.0, 'new_value': 14569.0}, {'field': 'instoreCount', 'old_value': 62, 'new_value': 69}]
2025-06-09 08:10:05,287 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:05,740 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMNW
2025-06-09 08:10:05,740 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21354.63, 'new_value': 23829.59}, {'field': 'dailyBillAmount', 'old_value': 21354.63, 'new_value': 23829.59}, {'field': 'amount', 'old_value': 16445.21, 'new_value': 17986.65}, {'field': 'count', 'old_value': 521, 'new_value': 564}, {'field': 'instoreAmount', 'old_value': 16115.11, 'new_value': 17635.65}, {'field': 'instoreCount', 'old_value': 505, 'new_value': 547}, {'field': 'onlineAmount', 'old_value': 330.1, 'new_value': 351.0}, {'field': 'onlineCount', 'old_value': 16, 'new_value': 17}]
2025-06-09 08:10:05,740 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:06,178 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMOW
2025-06-09 08:10:06,178 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 27571.42, 'new_value': 30897.89}, {'field': 'dailyBillAmount', 'old_value': 27571.42, 'new_value': 30897.89}, {'field': 'amount', 'old_value': 27034.3, 'new_value': 30258.3}, {'field': 'count', 'old_value': 125, 'new_value': 145}, {'field': 'instoreAmount', 'old_value': 26805.3, 'new_value': 29955.6}, {'field': 'instoreCount', 'old_value': 115, 'new_value': 133}, {'field': 'onlineAmount', 'old_value': 309.0, 'new_value': 382.7}, {'field': 'onlineCount', 'old_value': 10, 'new_value': 12}]
2025-06-09 08:10:06,178 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:06,646 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMZT
2025-06-09 08:10:06,646 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-06, 变更字段: [{'field': 'amount', 'old_value': 55844.6, 'new_value': 62341.8}, {'field': 'count', 'old_value': 359, 'new_value': 415}, {'field': 'instoreAmount', 'old_value': 57207.0, 'new_value': 63508.0}, {'field': 'instoreCount', 'old_value': 348, 'new_value': 401}, {'field': 'onlineAmount', 'old_value': 495.6, 'new_value': 691.8}, {'field': 'onlineCount', 'old_value': 11, 'new_value': 14}]
2025-06-09 08:10:06,646 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:07,084 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM1U
2025-06-09 08:10:07,084 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-06, 变更字段: [{'field': 'amount', 'old_value': 25327.97, 'new_value': 29483.57}, {'field': 'count', 'old_value': 1398, 'new_value': 1623}, {'field': 'instoreAmount', 'old_value': 3474.23, 'new_value': 4089.73}, {'field': 'instoreCount', 'old_value': 308, 'new_value': 357}, {'field': 'onlineAmount', 'old_value': 23041.0, 'new_value': 26729.0}, {'field': 'onlineCount', 'old_value': 1090, 'new_value': 1266}]
2025-06-09 08:10:07,084 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:07,584 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM2U
2025-06-09 08:10:07,584 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 61188.42, 'new_value': 70075.56}, {'field': 'amount', 'old_value': 61188.42, 'new_value': 70075.56}, {'field': 'count', 'old_value': 1105, 'new_value': 1260}, {'field': 'instoreAmount', 'old_value': 51508.99, 'new_value': 57777.73}, {'field': 'instoreCount', 'old_value': 921, 'new_value': 1036}, {'field': 'onlineAmount', 'old_value': 9679.43, 'new_value': 12297.83}, {'field': 'onlineCount', 'old_value': 184, 'new_value': 224}]
2025-06-09 08:10:07,584 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:08,037 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM3U
2025-06-09 08:10:08,037 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8850.8, 'new_value': 11310.0}, {'field': 'dailyBillAmount', 'old_value': 3233.8, 'new_value': 4872.8}, {'field': 'amount', 'old_value': 8850.8, 'new_value': 11310.0}, {'field': 'count', 'old_value': 57, 'new_value': 70}, {'field': 'instoreAmount', 'old_value': 8850.8, 'new_value': 11310.0}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 70}]
2025-06-09 08:10:08,037 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:08,490 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM4U
2025-06-09 08:10:08,490 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3544.0, 'new_value': 4050.0}, {'field': 'dailyBillAmount', 'old_value': 3544.0, 'new_value': 4050.0}, {'field': 'amount', 'old_value': 7716.7, 'new_value': 9065.7}, {'field': 'count', 'old_value': 73, 'new_value': 82}, {'field': 'instoreAmount', 'old_value': 7884.7, 'new_value': 9233.7}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 82}]
2025-06-09 08:10:08,490 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:08,943 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMEU
2025-06-09 08:10:08,943 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 33430.77, 'new_value': 37859.38}, {'field': 'dailyBillAmount', 'old_value': 33430.77, 'new_value': 37859.38}, {'field': 'amount', 'old_value': 25337.21, 'new_value': 27323.71}, {'field': 'count', 'old_value': 227, 'new_value': 248}, {'field': 'instoreAmount', 'old_value': 25337.21, 'new_value': 27323.71}, {'field': 'instoreCount', 'old_value': 227, 'new_value': 248}]
2025-06-09 08:10:08,943 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:09,537 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM6U
2025-06-09 08:10:09,537 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 66081.6, 'new_value': 77649.6}, {'field': 'dailyBillAmount', 'old_value': 66081.6, 'new_value': 77649.6}, {'field': 'amount', 'old_value': 47089.09, 'new_value': 57062.33}, {'field': 'count', 'old_value': 1565, 'new_value': 1864}, {'field': 'instoreAmount', 'old_value': 44176.29, 'new_value': 53824.79}, {'field': 'instoreCount', 'old_value': 1483, 'new_value': 1770}, {'field': 'onlineAmount', 'old_value': 3222.9, 'new_value': 3592.54}, {'field': 'onlineCount', 'old_value': 82, 'new_value': 94}]
2025-06-09 08:10:09,537 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:09,959 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM7U
2025-06-09 08:10:09,959 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14114.7, 'new_value': 18887.3}, {'field': 'dailyBillAmount', 'old_value': 14114.7, 'new_value': 18887.3}, {'field': 'amount', 'old_value': 13019.2, 'new_value': 17565.4}, {'field': 'count', 'old_value': 82, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 13546.7, 'new_value': 18092.9}, {'field': 'instoreCount', 'old_value': 82, 'new_value': 110}]
2025-06-09 08:10:09,959 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:10,412 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM8U
2025-06-09 08:10:10,412 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15915.83, 'new_value': 19029.94}, {'field': 'dailyBillAmount', 'old_value': 15915.83, 'new_value': 19029.94}]
2025-06-09 08:10:10,412 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:10,834 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM9U
2025-06-09 08:10:10,834 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14126.88, 'new_value': 16001.63}, {'field': 'amount', 'old_value': 14126.88, 'new_value': 16000.66}, {'field': 'count', 'old_value': 857, 'new_value': 980}, {'field': 'instoreAmount', 'old_value': 14237.42, 'new_value': 16171.56}, {'field': 'instoreCount', 'old_value': 857, 'new_value': 980}]
2025-06-09 08:10:10,834 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:11,334 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMAU
2025-06-09 08:10:11,334 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21369.34, 'new_value': 24406.59}, {'field': 'dailyBillAmount', 'old_value': 21369.34, 'new_value': 24406.59}, {'field': 'amount', 'old_value': 21762.73, 'new_value': 24859.7}, {'field': 'count', 'old_value': 1104, 'new_value': 1250}, {'field': 'instoreAmount', 'old_value': 19913.6, 'new_value': 22481.4}, {'field': 'instoreCount', 'old_value': 995, 'new_value': 1119}, {'field': 'onlineAmount', 'old_value': 2047.92, 'new_value': 2577.09}, {'field': 'onlineCount', 'old_value': 109, 'new_value': 131}]
2025-06-09 08:10:11,349 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:11,771 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMBU
2025-06-09 08:10:11,771 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15845.55, 'new_value': 19297.46}, {'field': 'amount', 'old_value': 15845.55, 'new_value': 19297.46}, {'field': 'count', 'old_value': 756, 'new_value': 903}, {'field': 'instoreAmount', 'old_value': 9018.46, 'new_value': 10922.57}, {'field': 'instoreCount', 'old_value': 458, 'new_value': 540}, {'field': 'onlineAmount', 'old_value': 6864.91, 'new_value': 8412.71}, {'field': 'onlineCount', 'old_value': 298, 'new_value': 363}]
2025-06-09 08:10:11,771 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:12,178 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMCU
2025-06-09 08:10:12,178 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12774.72, 'new_value': 14698.2}, {'field': 'dailyBillAmount', 'old_value': 12774.72, 'new_value': 14698.2}, {'field': 'amount', 'old_value': 8721.22, 'new_value': 10054.47}, {'field': 'count', 'old_value': 336, 'new_value': 379}, {'field': 'instoreAmount', 'old_value': 8834.97, 'new_value': 10200.87}, {'field': 'instoreCount', 'old_value': 336, 'new_value': 379}]
2025-06-09 08:10:12,178 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:12,615 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMDU
2025-06-09 08:10:12,615 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18584.61, 'new_value': 21745.99}, {'field': 'amount', 'old_value': 18584.61, 'new_value': 21745.99}, {'field': 'count', 'old_value': 1123, 'new_value': 1295}, {'field': 'instoreAmount', 'old_value': 4255.51, 'new_value': 4760.08}, {'field': 'instoreCount', 'old_value': 211, 'new_value': 233}, {'field': 'onlineAmount', 'old_value': 14769.67, 'new_value': 17551.48}, {'field': 'onlineCount', 'old_value': 912, 'new_value': 1062}]
2025-06-09 08:10:12,615 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-09 08:10:13,053 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM5U
2025-06-09 08:10:13,053 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13582.0, 'new_value': 18210.0}, {'field': 'dailyBillAmount', 'old_value': 13582.0, 'new_value': 18210.0}]
2025-06-09 08:10:13,053 - INFO - 月销售数据同步完成！更新: 203 条，插入: 0 条，错误: 0 条，跳过: 1200 条
2025-06-09 08:10:13,053 - INFO - 综合数据同步流程完成！
2025-06-09 08:10:13,099 - INFO - 综合数据同步完成
2025-06-09 08:10:13,099 - INFO - MySQL数据库连接已关闭
2025-06-09 08:10:13,099 - INFO - ==================================================
2025-06-09 08:10:13,099 - INFO - 程序退出
2025-06-09 08:10:13,099 - INFO - ==================================================
