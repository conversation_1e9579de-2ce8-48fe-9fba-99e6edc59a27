import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import time
import sqlite3
from pathlib import Path
import os
import sys
import pandas as pd
from get_shuyandata import call_sale_query_api
from update_formdata import Sample as UpdateFormSample
from insert_save_formdata import Sample as InsertFormSample
from get_token import token
from alibabacloud_dingtalk.yida_2_0 import models as dingtalkyida__2__0_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_dingtalk.yida_1_0 import models as dingtalkyida__1__0_models
from alibabacloud_dingtalk.yida_1_0.client import Client as dingtalkyida_1_0Client
from alibabacloud_tea_openapi import models as open_api_models

# SQLite数据库配置
DB_PATH = Path('data/sales_data.db')
DB_PATH.parent.mkdir(exist_ok=True)

# 配置日志
log_dir = Path('logs')
log_dir.mkdir(exist_ok=True)
today = datetime.now().strftime('%Y%m%d')
log_file = log_dir / f'sync_data_{today}.log'

# 配置根日志记录器
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8', mode='a'),
        logging.StreamHandler()
    ]
)

logging.info(f"日志文件: {log_file}")

class YidaFormDataClient:
    def __init__(self, yida_config: Dict):
        self.yida_config = yida_config
        self.access_token = token.get_token()
        self.client = self._create_client()

    def _create_client(self) -> dingtalkyida_1_0Client:
        """
        初始化宜搭客户端
        
        Returns:
            dingtalkyida_1_0Client: 宜搭客户端实例
        """
        config = open_api_models.Config(
            protocol='https',
            region_id='central'
        )
        return dingtalkyida_1_0Client(config)

    def get_form_data(self, page_size: int = 100, search_condition: Optional[List[Dict]] = None) -> List[Dict[str, Any]]:
        """
        获取宜搭表单数据
        
        Args:
            page_size: 每页数据条数，默认100
            search_condition: 查询条件，可选
            
        Returns:
            List[Dict[str, Any]]: 表单数据列表
        """
        try:
            all_data = []
            current_page = 1
            
            while True:
                headers = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldHeaders()
                headers.x_acs_dingtalk_access_token = self.access_token
                
                request = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldRequest(
                    page_number=current_page,
                    form_uuid=self.yida_config['FORM_UUID'],
                    search_condition=json.dumps(search_condition) if search_condition else None,
                    system_token=self.yida_config['SYSTEM_TOKEN'],
                    page_size=page_size,
                    user_id=self.yida_config['USER_ID'],
                    app_type=self.yida_config['APP_TYPE']
                )
                
                # 记录请求参数
                logging.info(f"Request Parameters - Page {current_page}:")
                logging.info(f"Headers: {headers}")
                logging.info(f"Request: {request}")
                
                result = self.client.search_form_data_second_generation_no_table_field_with_options(
                    request, 
                    headers, 
                    util_models.RuntimeOptions()
                )
                
                # 记录响应结果
                logging.info(f"Response - Page {current_page}:")
                
                if not result or not result.body or not result.body.data:
                    break
                
                # 提取指定字段
                for item in result.body.data:
                    filtered_data = {
                        'formInstanceId': item.form_instance_id,
                        'formData': item.form_data
                    }
                    all_data.append(filtered_data)
                
                logging.info(f"第 {current_page} 页获取到 {len(result.body.data)} 条记录")
                
                # 如果获取的数据少于页大小，说明已经是最后一页
                if len(result.body.data) < page_size:
                    break
                    
                current_page += 1
            
            logging.info(f"查询完成，共获取到 {len(all_data)} 条记录")
            return all_data
            
        except Exception as e:
            error_msg = f"获取表单数据失败: {str(e)}"
            if hasattr(e, 'code') and hasattr(e, 'message'):
                error_msg += f" (错误码: {e.code}, 错误信息: {e.message})"
            raise Exception(error_msg)

class DataSyncManager:
    def __init__(self):
        self.shuyan_config = {
            'appId': 'a5274b7e5d9a41939346c33c2c3443db',
            'appKey': '2c9a5a628e7dab16018f5b055f3d0002',
            'apiSecret': '07F77244AD915AC2BB3EECE8EF7AE4DB',
            'method': 'gogo.open.auto.routing',
            'lowerMethod': 'com.gooagoo.open.api.salequery',
            'url': 'http://api.gooagoo.com/oapi/rest'
        }
        self.yida_config = {
            'APP_TYPE': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
            'SYSTEM_TOKEN': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
            'USER_ID': 'hexuepeng',
            'LANGUAGE': 'zh_CN',
            'FORM_UUID': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE'
        }
        self.shop_mapping = {
            "1ETDLFB9DIMQME7Q2OVD93ISAI00189O": "广州VT101维多利广场",
            "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE": "武汉国金天地",
            "1HFLOR99TBR11L6UBHOUTGCK1C001A3F": "广州悦汇城",
            "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV": "悦汇广场·南海",
            "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU": "广州IFC国金天地",
            "1HRIS7255PESAA7AV8LHQQGIH8001KNH": "广州ICC环贸天地",
            "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D": "武汉星汇维港"
        }
        self.access_token = token.get_token()
        
        # 初始化SQLite数据库
        self._init_db()
        self._init_monthly_table()

    def _init_db(self):
        """初始化SQLite数据库"""
        try:
            # 检查数据库文件是否存在
            if DB_PATH.exists():
                logging.info(f"数据库文件已存在: {DB_PATH}")
                # 检查表是否存在
                conn = sqlite3.connect(DB_PATH)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sales_data'")
                if cursor.fetchone():
                    logging.info("sales_data表已存在，无需创建")
                    conn.close()
                    return
                conn.close()
            
            # 创建数据库和表
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            # 创建销售数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sales_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    shop_id TEXT,
                    shop_entity_id TEXT,
                    shop_entity_name TEXT,
                    sale_time DATE,
                    recommend_amount REAL,
                    daily_bill_amount REAL,
                    amount REAL,
                    count INTEGER,
                    instore_amount REAL,
                    instore_count INTEGER,
                    online_amount REAL,
                    online_count INTEGER,
                    project_name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(shop_id, shop_entity_id, sale_time)
                )
            ''')
            
            conn.commit()
            conn.close()
            logging.info("SQLite数据库初始化成功")
        except Exception as e:
            logging.error(f"SQLite数据库初始化失败: {str(e)}")
            raise

    def _init_monthly_table(self):
        """初始化月度汇总表"""
        try:
            # 创建数据库连接
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            # 检查月度汇总表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sales_data_month'")
            if cursor.fetchone():
                logging.info("sales_data_month表已存在，无需创建")
                conn.close()
                return
            
            # 创建月度销售数据汇总表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sales_data_month (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    shop_id TEXT,
                    shop_entity_id TEXT,
                    shop_entity_name TEXT,
                    year INTEGER,
                    month INTEGER,
                    recommend_amount REAL,
                    daily_bill_amount REAL,
                    amount REAL,
                    count INTEGER,
                    instore_amount REAL,
                    instore_count INTEGER,
                    online_amount REAL,
                    online_count INTEGER,
                    project_name TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(shop_id, shop_entity_id, year, month)
                )
            ''')
            
            conn.commit()
            conn.close()
            logging.info("月度汇总表初始化成功")
        except Exception as e:
            logging.error(f"月度汇总表初始化失败: {str(e)}")
            raise

    def _save_to_sqlite(self, data_list: List[Dict]):
        """将数据保存到SQLite数据库"""
        try:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            # 初始化统计计数器
            total_records = len(data_list)
            insert_count = 0
            update_count = 0
            skip_count = 0
            error_count = 0
            
            logging.info(f"开始保存数据到SQLite数据库，共 {total_records} 条记录待处理")
            
            # 定义需要比较的金额和笔数字段及其映射关系
            field_mapping = {
                'recommendAmount': 'recommend_amount',
                'dailyBillAmount': 'daily_bill_amount',
                'amount': 'amount',
                'count': 'count',
                'instoreAmount': 'instore_amount',
                'instoreCount': 'instore_count',
                'onlineAmount': 'online_amount',
                'onlineCount': 'online_count'
            }
            
            for item in data_list:
                try:
                    # 将日期字符串转换为SQLite的DATE格式 (YYYY-MM-DD)
                    sale_time = item.get('saleTime', '')
                    if sale_time:
                        try:
                            dt = datetime.strptime(sale_time, '%Y%m%d')
                            sale_time = dt.strftime('%Y-%m-%d')
                        except ValueError as e:
                            logging.error(f"日期格式转换错误: {sale_time}, {str(e)}")
                            error_count += 1
                            continue
                    
                    # 检查记录是否存在
                    cursor.execute('''
                        SELECT id, recommend_amount, daily_bill_amount, amount, count,
                               instore_amount, instore_count, online_amount, online_count
                        FROM sales_data
                        WHERE shop_id = ? AND shop_entity_id = ? AND sale_time = ?
                    ''', (
                        item.get('shopId', ''),
                        item.get('shopEntityId', ''),
                        sale_time
                    ))
                    
                    existing_record = cursor.fetchone()
                    
                    if existing_record:
                        # 记录存在，检查是否需要更新
                        record_id = existing_record[0]
                        need_update = False
                        update_fields = []
                        update_values = []
                        changed_fields = []
                        
                        # 检查所有字段
                        for i, (source_field, db_field) in enumerate(field_mapping.items(), 1):
                            new_value = item.get(source_field, 0)
                            old_value = existing_record[i]
                            
                            # 根据字段类型进行转换和比较
                            if source_field.endswith('Amount'):
                                new_value = float(new_value)
                                old_value = float(old_value)
                                if abs(new_value - old_value) > 0.01:  # 考虑浮点数精度
                                    need_update = True
                                    update_fields.append(f"{db_field} = ?")
                                    update_values.append(new_value)
                                    changed_fields.append(f"{db_field}: {old_value} -> {new_value}")
                            else:  # 笔数字段
                                new_value = int(float(new_value))
                                old_value = int(old_value)
                                if new_value != old_value:
                                    need_update = True
                                    update_fields.append(f"{db_field} = ?")
                                    update_values.append(new_value)
                                    changed_fields.append(f"{db_field}: {old_value} -> {new_value}")
                        
                        if need_update:
                            # 更新记录
                            update_sql = f'''
                                UPDATE sales_data
                                SET {', '.join(update_fields)}
                                WHERE id = ?
                            '''
                            update_values.append(record_id)
                            cursor.execute(update_sql, update_values)
                            update_count += 1
                            logging.info(f"更新记录成功: shop_id={item.get('shopId')}, shop_entity_id={item.get('shopEntityId')}, sale_time={sale_time}")
                            logging.info(f"变更字段: {', '.join(changed_fields)}")
                        else:
                            skip_count += 1
                            logging.debug(f"记录无需更新: shop_id={item.get('shopId')}, shop_entity_id={item.get('shopEntityId')}, sale_time={sale_time}")
                    else:
                        # 记录不存在，插入新记录
                        cursor.execute('''
                            INSERT INTO sales_data (
                                shop_id, shop_entity_id, shop_entity_name, sale_time,
                                recommend_amount, daily_bill_amount, amount, count,
                                instore_amount, instore_count, online_amount, online_count,
                                project_name
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            item.get('shopId', ''),
                            item.get('shopEntityId', ''),
                            item.get('shopEntityName', ''),
                            sale_time,
                            float(item.get('recommendAmount', 0)),
                            float(item.get('dailyBillAmount', 0)),
                            float(item.get('amount', 0)),
                            int(float(item.get('count', 0))),
                            float(item.get('instoreAmount', 0)),
                            int(float(item.get('instoreCount', 0))),
                            float(item.get('onlineAmount', 0)),
                            int(float(item.get('onlineCount', 0))),
                            self.shop_mapping.get(item.get('shopId', ''), '')
                        ))
                        insert_count += 1
                        logging.info(f"插入新记录成功: shop_id={item.get('shopId')}, shop_entity_id={item.get('shopEntityId')}, sale_time={sale_time}")
                
                except Exception as e:
                    error_count += 1
                    logging.error(f"处理数据项失败: {item}, {str(e)}")
                    continue
            
            conn.commit()
            conn.close()
            
            # 输出统计信息
            logging.info(f"SQLite数据保存完成，统计信息：")
            logging.info(f"- 总记录数: {total_records}")
            logging.info(f"- 成功插入: {insert_count}")
            logging.info(f"- 成功更新: {update_count}")
            logging.info(f"- 无需更新: {skip_count}")
            logging.info(f"- 处理失败: {error_count}")
            
        except Exception as e:
            logging.error(f"保存数据到SQLite数据库失败: {str(e)}")
            raise

    def get_shuyan_data(self, start_date=None, end_date=None) -> List[Dict]:
        """
        获取数衍平台数据并保存到Excel和SQLite数据库
        :param start_date: 开始日期，格式：YYYYMMDD，默认为30天前
        :param end_date: 结束日期，格式：YYYYMMDD，默认为昨天
        """
        try:
            if end_date is None:
                end_date = datetime.now() - timedelta(days=1)
            else:
                try:
                    end_date = datetime.strptime(end_date, '%Y%m%d')
                except ValueError as e:
                    logging.error(f"结束日期格式错误: {end_date}, {str(e)}")
                    return []
            
            if start_date is None:
                start_date = end_date - timedelta(days=60)  # 默认获取31天数据
            else:
                try:
                    start_date = datetime.strptime(start_date, '%Y%m%d')
                except ValueError as e:
                    logging.error(f"开始日期格式错误: {start_date}, {str(e)}")
                    return []
            
            all_data = []
            current_date = start_date
            logging.info(f"查询数衍平台数据，时间段为: {start_date}, {end_date}")
            # 将时间段分成多个7天的批次
            while current_date <= end_date:  # 修改循环条件，使用 <= 而不是 <
                batch_end_date = min(current_date + timedelta(days=6), end_date)
                
                business_data = {
                    "fromDate": current_date.strftime("%Y%m%d"),
                    "toDate": batch_end_date.strftime("%Y%m%d"),
                    "shopIds": list(self.shop_mapping.keys())
                }
                
                logging.info(f"正在获取{business_data['fromDate']}至{business_data['toDate']}的数据")
                
                try:
                    result = call_sale_query_api(
                        self.shuyan_config['appId'],
                        self.shuyan_config['appKey'],
                        self.shuyan_config['apiSecret'],
                        self.shuyan_config['method'],
                        self.shuyan_config['lowerMethod'],
                        self.shuyan_config['url'],
                        business_data
                    )
                    
                    # 优化日志输出，避免显示过大的数据内容
                    if 'rescode' in result and 'resmsg' in result:
                        logging.info(f"Response: {{'rescode': '{result['rescode']}', 'resmsg': '{result['resmsg']}'}}")
                    else:
                        logging.error(f"获取{business_data['fromDate']}至{business_data['toDate']}的数据失败: 响应格式错误")
                        continue
                    
                    if 'data' in result and isinstance(result['data'], list):
                        # 过滤掉所有金额和笔数都为0的记录
                        filtered_data = []
                        for item in result['data']:
                            # 检查所有金额和笔数字段
                            amount_fields = ['recommendAmount', 'dailyBillAmount', 'amount', 'instoreAmount', 'onlineAmount']
                            count_fields = ['count', 'instoreCount', 'onlineCount']
                            
                            # 检查是否有任何金额或笔数不为0
                            has_non_zero_amount = any(float(item.get(field, 0)) > 0 for field in amount_fields)
                            has_non_zero_count = any(float(item.get(field, 0)) > 0 for field in count_fields)
                            
                            if has_non_zero_amount or has_non_zero_count:
                                filtered_data.append(item)
                            else:
                                logging.debug(f"过滤掉全零记录: {item}")
                        
                        all_data.extend(filtered_data)
                        logging.info(f"过滤后保留 {len(filtered_data)} 条记录")
                    else:
                        logging.error(f"获取{business_data['fromDate']}至{business_data['toDate']}的数据失败: {result}")
                    
                    # 添加延时避免请求过于频繁
                    time.sleep(2)
                    current_date = batch_end_date + timedelta(days=1)  # 修改为使用 batch_end_date 而不是 end_date
                except Exception as e:
                    logging.error(f"调用数衍平台API失败: {str(e)}")
                    continue
            
            # 保存数据到SQLite数据库
            if all_data:
                try:
                    self._save_to_sqlite(all_data)
                except Exception as e:
                    logging.error(f"保存数据到SQLite数据库失败: {str(e)}")
            
            # 生成Excel文件
            if all_data:
                try:
                    # 准备数据
                    excel_data = []
                    for item in all_data:
                        try:
                            shop_id = item.get('shopId', '')
                            shop_entity_id = item.get('shopEntityId', '')
                            project_name = self.shop_mapping.get(shop_id, '')
                            
                            if not project_name:
                                logging.warning(f"警告：机构ID {shop_id} 未找到对应的项目名称")
                            
                            excel_data.append({
                                '项目名称': project_name,
                                '数衍平台机构ID': shop_id,
                                '数衍平台店铺ID': shop_entity_id,
                                '店铺名称': item.get('shopEntityName', ''),
                                '销售日期': item.get('saleTime', ''),
                                '推荐金额': round(float(item.get('recommendAmount', 0)), 2),
                                '日结金额': round(float(item.get('dailyBillAmount', 0)), 2),
                                '净销售额': round(float(item.get('amount', 0)), 2),
                                '总销售笔数': int(float(item.get('count', 0))),
                                '店内净销售额': round(float(item.get('instoreAmount', 0)), 2),
                                '店内销售笔数': int(float(item.get('instoreCount', 0))),
                                '线上净销售额': round(float(item.get('onlineAmount', 0)), 2),
                                '线上销售笔数': int(float(item.get('onlineCount', 0)))
                            })
                        except Exception as e:
                            logging.error(f"处理数据项失败: {item}, {str(e)}")
                            continue
                    
                    # 创建DataFrame
                    df = pd.DataFrame(excel_data)
                    
                    # 生成文件名
                    today = datetime.now().strftime('%Y%m%d')
                    file_path = Path(f'数衍平台数据导出_{today}.xlsx')
                    
                    # 保存Excel文件
                    df.to_excel(file_path, index=False, engine='openpyxl')
                    logging.info(f"数据已保存到Excel文件: {file_path}")
                except Exception as e:
                    logging.error(f"生成Excel文件失败: {str(e)}")
            
            return all_data
        except Exception as e:
            logging.error(f"获取数衍平台数据失败: {str(e)}")
            return []

    def get_form_data(self, start_date=None, end_date=None) -> List[Dict]:
        """
        从宜搭表单获取数据
        :param start_date: 开始日期，格式：YYYYMMDD
        :param end_date: 结束日期，格式：YYYYMMDD
        :return: 表单数据列表
        """
        try:
            client = YidaFormDataClient(self.yida_config)
            
            # 构建日期筛选条件
            if start_date is None:
                start_date = (datetime.now() - timedelta(days=61)).strftime('%Y%m%d')
            if end_date is None:
                end_date = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
            
            logging.info(f"开始从宜搭表单获取数据，时间段: {start_date} 至 {end_date}")
            
            # 将日期转换为时间戳（毫秒）
            try:
                start_timestamp = int(datetime.strptime(start_date, '%Y%m%d').timestamp() * 1000)
                end_timestamp = int(datetime.strptime(end_date, '%Y%m%d').timestamp() * 1000) + 86399000  # 加上23:59:59的毫秒数
            except ValueError as e:
                logging.error(f"日期格式转换错误: {start_date}, {end_date}, {str(e)}")
                return []
            
            # 构建筛选条件
            search_condition = [{
                "key": "dateField_m9dkdkoz",  # 销售日期字段
                "value": [start_timestamp, end_timestamp],
                "type": "DOUBLE",
                "operator": "between",
                "componentName": "DateField"
            }]
            
            # 获取表单数据
            form_data = client.get_form_data(search_condition=search_condition)
            
            if not form_data:
                logging.warning("未获取到宜搭表单数据")
                return []
            
            logging.info(f"成功获取宜搭表单数据，共 {len(form_data)} 条记录")
            
            # 处理表单数据
            processed_data = []
            for item in form_data:
                try:
                    # 转换数据格式
                    standard_data = convert_yida_to_standard(item['formData'])
                    processed_data.append({
                        'form_instance_id': item['formInstanceId'],
                        'form_data': standard_data
                    })
                except Exception as e:
                    logging.error(f"处理表单数据失败: {item}, {str(e)}")
                    continue
            
            logging.info(f"表单数据处理完成，成功处理 {len(processed_data)} 条记录")
            return processed_data
            
        except Exception as e:
            logging.error(f"获取宜搭表单数据失败: {str(e)}")
            return []

    def sync_data(self, start_date=None, end_date=None):
        """
        同步数据主流程
        :param start_date: 开始日期，格式：YYYYMMDD，默认为7天前
        :param end_date: 结束日期，格式：YYYYMMDD，默认为昨天
        """
        try:
            logging.info("开始数据同步流程...")
            
            # 1. 获取数衍平台数据
            logging.info("正在获取数衍平台数据...")
            shuyan_data = self.get_shuyan_data(start_date, end_date)
            if not shuyan_data:
                logging.error("未获取到数衍平台数据，同步流程终止")
                return
            logging.info(f"成功获取数衍平台数据，共 {len(shuyan_data)} 条记录")
            
            # 2. 获取宜搭表单数据
            logging.info("正在获取宜搭表单数据...")
            try:
                yida_data = self.get_form_data(start_date, end_date)
                logging.info(f"成功获取宜搭表单数据，共 {len(yida_data)} 条记录")
            except Exception as e:
                logging.error(f"获取宜搭表单数据失败: {str(e)}")
                return
            
            # 创建宜搭数据索引
            logging.info("正在处理宜搭数据索引...")
            yida_data_dict = {}
            for item in yida_data:
                try:
                    # 直接使用 form_data，避免重复转换
                    key = self.create_key(item['form_data'])
                    yida_data_dict[key] = {
                        'form_instance_id': item['form_instance_id'],
                        'form_data': item['form_data']
                    }
                except Exception as e:
                    logging.error(f"处理宜搭数据项失败: {item}, {str(e)}")
                    continue
            logging.info(f"数据索引处理完成，共 {len(yida_data_dict)} 条记录")
            
            # 对比并同步数据
            logging.info("开始数据对比和同步...")
            update_count = 0
            insert_count = 0
            error_count = 0
            insert_data_list = []
            
            # 定义需要比较的字段
            compare_fields = [
                'recommendAmount',    # 推荐金额
                'dailyBillAmount',    # 日结金额
                'amount',             # 净销售额
                'count',              # 总销售笔数
                'instoreAmount',      # 店内净销售额
                'instoreCount',       # 店内销售笔数
                'onlineAmount',       # 线上净销售额
                'onlineCount',        # 线上销售笔数
                'shopEntityName'      # 数衍平台店铺名称
            ]
            
            for shuyan_item in shuyan_data:
                try:
                    key = self.create_key(shuyan_item)
                    
                    if key in yida_data_dict:
                        yida_item = yida_data_dict[key]['form_data']
                        # 逐个字段比较
                        need_update = False
                        changed_fields = []
                        
                        for field in compare_fields:
                            shuyan_value = shuyan_item.get(field, 0)
                            yida_value = yida_item.get(field, 0)
                            
                            # 对于笔数字段，确保比较整数
                            if field in ['count', 'instoreCount', 'onlineCount']:
                                shuyan_value = int(shuyan_value) if shuyan_value else 0
                                yida_value = int(yida_value) if yida_value else 0
                            
                            if shuyan_value != yida_value:
                                need_update = True
                                changed_fields.append({
                                    'field': field,
                                    'old_value': yida_value,
                                    'new_value': shuyan_value
                                })
                        
                        if need_update:
                            try:
                                yida_format_data = convert_standard_to_yida(shuyan_item, self.shop_mapping)
                                form_instance_id = yida_data_dict[key]['form_instance_id']
                                
                                if not form_instance_id or not form_instance_id.startswith('FINST-'):
                                    logging.error(f"无效的表单实例ID: {form_instance_id}")
                                    error_count += 1
                                    continue
                                
                                if not all(key in yida_format_data for key in ['textField_m9dkdkpg', 'textField_m9dkdkph', 'dateField_m9dkdkoz']):
                                    logging.error(f"转换后的数据缺少必要字段: {yida_format_data}")
                                    error_count += 1
                                    continue
                                
                                self.update_form_data(form_instance_id, yida_format_data)
                                update_count += 1
                                logging.info(f"更新记录成功，变更字段: {changed_fields}")
                            except Exception as e:
                                logging.error(f"更新记录时发生错误: {str(e)}")
                                error_count += 1
                    else:
                        try:
                            yida_format_data = convert_standard_to_yida(shuyan_item, self.shop_mapping)
                            insert_data_list.append(yida_format_data)
                            insert_count += 1
                        except Exception as e:
                            logging.error(f"转换数据格式失败: {str(e)}")
                            error_count += 1
                except Exception as e:
                    logging.error(f"处理数据项失败: {shuyan_item}, {str(e)}")
                    error_count += 1
                    continue
            
            # 批量插入新数据
            if insert_data_list:
                try:
                    self.batch_insert_form_data(insert_data_list)
                    logging.info(f"批量插入完成，共 {len(insert_data_list)} 条记录")
                except Exception as e:
                    logging.error(f"批量插入数据失败: {str(e)}")
                    error_count += len(insert_data_list)
            
            logging.info(f"数据同步完成！更新: {update_count} 条，插入: {insert_count} 条，错误: {error_count} 条")
        except Exception as e:
            logging.error(f"数据同步流程失败: {str(e)}")
            raise

    def create_key(self, data: Dict) -> str:
        """创建数据唯一标识"""
        return f"{data.get('shopId')}_{data.get('shopEntityId')}_{data.get('saleTime')}"

    def update_form_data(self, form_instance_id: str, new_data: Dict):
        """更新宜搭表单数据"""
        try:
            # 使用 UpdateFormSample 创建客户端
            client = UpdateFormSample.create_client()
            headers = dingtalkyida__2__0_models.UpdateFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            request = dingtalkyida__2__0_models.UpdateFormDataRequest()
            request.app_type = self.yida_config['APP_TYPE']
            request.system_token = self.yida_config['SYSTEM_TOKEN']
            request.user_id = self.yida_config['USER_ID']
            request.language = self.yida_config['LANGUAGE']
            request.form_instance_id = form_instance_id
            request.form_uuid = self.yida_config['FORM_UUID']
            request.update_form_data_json = json.dumps(new_data, ensure_ascii=False)
            request.use_alias = False
            request.use_latest_version = False
            
            response = client.update_form_data_with_options(request, headers, util_models.RuntimeOptions())
            logging.info(f"更新表单数据成功: {form_instance_id}")
            return response
            
        except Exception as e:
            logging.error(f"更新表单数据失败: {str(e)}")
            raise

    # def insert_form_data(self, new_data: Dict):
    #     """插入宜搭表单数据"""
    #     try:
    #         client = InsertFormSample.create_client()
    #         headers = dingtalkyida__2__0_models.SaveFormDataHeaders()
    #         headers.x_acs_dingtalk_access_token = self.access_token
            
    #         request = dingtalkyida__2__0_models.SaveFormDataRequest()
    #         request.app_type = self.yida_config['APP_TYPE']
    #         request.system_token = self.yida_config['SYSTEM_TOKEN']
    #         request.user_id = self.yida_config['USER_ID']
    #         request.language = self.yida_config['LANGUAGE']
    #         request.form_uuid = self.yida_config['FORM_UUID']
    #         request.form_data_json = json.dumps(new_data, ensure_ascii=False)
    #         request.use_alias = False
            
    #         response = client.save_form_data_with_options(request, headers, util_models.RuntimeOptions())
    #         logging.info(f"插入表单数据成功")
    #         return response
            
    #     except Exception as e:
    #         logging.error(f"插入表单数据失败: {str(e)}")
    #         raise

    def batch_insert_form_data(self, data_list: List[Dict], batch_size: int = 100):
        """批量插入宜搭表单数据"""
        try:
            client = dingtalkyida_1_0Client(open_api_models.Config(
                protocol='https',
                region_id='central'
            ))
            
            headers = dingtalkyida__1__0_models.BatchSaveFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            # 将数据列表分成多个批次
            for i in range(0, len(data_list), batch_size):
                batch_data = data_list[i:i + batch_size]
                form_data_json_list = [json.dumps(item, ensure_ascii=False) for item in batch_data]
                
                request = dingtalkyida__1__0_models.BatchSaveFormDataRequest(
                    no_execute_expression=True,
                    form_uuid=self.yida_config['FORM_UUID'],
                    app_type=self.yida_config['APP_TYPE'],
                    asynchronous_execution=True,
                    system_token=self.yida_config['SYSTEM_TOKEN'],
                    keep_running_after_exception=True,
                    user_id=self.yida_config['USER_ID'],
                    form_data_json_list=form_data_json_list
                )
                
                try:
                    response = client.batch_save_form_data_with_options(request, headers, util_models.RuntimeOptions())
                    if response.status_code == 200:
                        logging.info(f"批量插入表单数据成功，批次 {i//batch_size + 1}，共 {len(batch_data)} 条记录")
                    else:
                        logging.error(f"批量插入表单数据失败，批次 {i//batch_size + 1}: {response.status_code}, {response.body}")
                except Exception as e:
                    logging.error(f"批量插入表单数据失败，批次 {i//batch_size + 1}: {str(e)}")
                    continue
                
                # 添加延时避免请求过于频繁
                time.sleep(5)
            
        except Exception as e:
            logging.error(f"批量插入表单数据失败: {str(e)}")
            raise

    def update_monthly_summary(self, start_date=None, end_date=None):
        """更新月度汇总数据
        
        Args:
            start_date: 开始日期，格式：YYYYMMDD
            end_date: 结束日期，格式：YYYYMMDD
        """
        try:
            # 解析日期范围
            if start_date:
                start_dt = datetime.strptime(start_date, '%Y%m%d')
            else:
                # 默认处理过去12个月的数据
                end_dt = datetime.now()
                start_dt = end_dt - timedelta(days=365)
            
            if end_date:
                end_dt = datetime.strptime(end_date, '%Y%m%d')
            else:
                end_dt = datetime.now()

            # 格式化为SQLite可识别的日期格式
            start_date_db = start_dt.strftime('%Y-%m-%d')
            end_date_db = end_dt.strftime('%Y-%m-%d')
            
            logging.info(f"开始更新月度汇总数据，时间范围: {start_date_db} 至 {end_date_db}")
            
            # 连接数据库
            conn = sqlite3.connect(DB_PATH)
            conn.row_factory = sqlite3.Row  # 设置行工厂以便通过列名访问
            cursor = conn.cursor()
            
            # 清空指定日期范围内的汇总数据（可选，也可以改为追加模式）
            cursor.execute('''
                DELETE FROM sales_data_month 
                WHERE (year > ? OR (year = ? AND month >= ?)) 
                  AND (year < ? OR (year = ? AND month <= ?))
            ''', (
                start_dt.year, start_dt.year, start_dt.month,
                end_dt.year, end_dt.year, end_dt.month
            ))
            
            # 执行月度汇总查询
            cursor.execute('''
                INSERT INTO sales_data_month 
                    (shop_id, shop_entity_id, shop_entity_name, year, month, 
                     recommend_amount, daily_bill_amount, amount, count, 
                     instore_amount, instore_count, online_amount, online_count, 
                     project_name, updated_at)
                SELECT 
                    shop_id, 
                    shop_entity_id, 
                    shop_entity_name, 
                    strftime('%Y', sale_time) AS year, 
                    strftime('%m', sale_time) AS month, 
                    SUM(recommend_amount) AS recommend_amount, 
                    SUM(daily_bill_amount) AS daily_bill_amount, 
                    SUM(amount) AS amount, 
                    SUM(count) AS count, 
                    SUM(instore_amount) AS instore_amount, 
                    SUM(instore_count) AS instore_count, 
                    SUM(online_amount) AS online_amount, 
                    SUM(online_count) AS online_count, 
                    project_name, 
                    CURRENT_TIMESTAMP
                FROM sales_data
                WHERE sale_time BETWEEN ? AND ?
                GROUP BY shop_id, shop_entity_id, project_name, year, month
                ORDER BY shop_id, year, month
            ''', (start_date_db, end_date_db))
            
            # 获取处理的记录数
            rows_affected = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            logging.info(f"月度汇总数据更新完成，处理了 {rows_affected} 条汇总记录")
            return rows_affected
        
        except Exception as e:
            logging.error(f"更新月度汇总数据失败: {str(e)}")
            raise

    def export_monthly_data_to_excel(self, start_date=None, end_date=None):
        """导出月度汇总数据到Excel
        
        Args:
            start_date: 开始日期，格式：YYYYMMDD
            end_date: 结束日期，格式：YYYYMMDD
            
        Returns:
            str: 导出的Excel文件路径
        """
        try:
            # 解析日期范围
            if start_date:
                start_dt = datetime.strptime(start_date, '%Y%m%d')
            else:
                # 默认导出过去12个月的数据
                end_dt = datetime.now()
                start_dt = end_dt - timedelta(days=365)
            
            if end_date:
                end_dt = datetime.strptime(end_date, '%Y%m%d')
            else:
                end_dt = datetime.now()
            
            start_year = start_dt.year
            start_month = start_dt.month
            end_year = end_dt.year
            end_month = end_dt.month
            
            logging.info(f"开始导出月度汇总数据到Excel，时间范围: {start_year}-{start_month} 至 {end_year}-{end_month}")
            
            # 连接数据库
            conn = sqlite3.connect(DB_PATH)
            
            # 查询月度汇总数据
            query = '''
                SELECT 
                    project_name AS '项目名称',
                    shop_id AS '数衍平台机构ID',
                    shop_entity_id AS '数衍平台店铺ID',
                    shop_entity_name AS '店铺名称',
                    year || '-' || month AS '销售月份',
                    recommend_amount AS '推荐金额',
                    daily_bill_amount AS '日结金额',
                    amount AS '净销售额',
                    count AS '总销售笔数',
                    instore_amount AS '店内净销售额',
                    instore_count AS '店内销售笔数',
                    online_amount AS '线上净销售额',
                    online_count AS '线上销售笔数'
                FROM sales_data_month
                WHERE (year > ? OR (year = ? AND month >= ?)) 
                  AND (year < ? OR (year = ? AND month <= ?))
                ORDER BY project_name, shop_id, year, month
            '''
            
            df = pd.read_sql_query(query, conn, params=(
                start_year, start_year, start_month,
                end_year, end_year, end_month
            ))
            
            conn.close()
            
            if df.empty:
                logging.warning("未找到符合条件的月度汇总数据")
                return None
            
            # 生成Excel文件
            today = datetime.now().strftime('%Y%m%d')
            file_path = f'数衍平台月度数据_{today}.xlsx'
            
            # 导出到Excel
            df.to_excel(file_path, index=False, engine='openpyxl')
            
            logging.info(f"月度汇总数据已导出到Excel文件: {file_path}")
            return file_path
            
        except Exception as e:
            logging.error(f"导出月度汇总数据失败: {str(e)}")
            raise

def convert_yida_to_standard(yida_data: Dict) -> Dict:
    """将宜搭数据格式转换为标准格式"""
    logging.debug(f"开始转换宜搭数据到标准格式: {yida_data}")
    mapping = {
        'numberField_m9dkdkp8': 'recommendAmount',    # 推荐金额
        'numberField_m9dkdkp9': 'dailyBillAmount',    # 日结金额
        'numberField_m9dkdkpa': 'amount',             # 净销售额
        'numberField_m9dkdkpb': 'count',              # 总销售笔数
        'numberField_m9dkdkpc': 'instoreAmount',      # 店内净销售额
        'numberField_m9dkdkpd': 'instoreCount',       # 店内销售笔数
        'numberField_m9dkdkpe': 'onlineAmount',       # 线上净销售额
        'numberField_m9dkdkpf': 'onlineCount',        # 线上销售笔数
        'textField_m9dkdkpg': 'shopId',              # 数衍平台机构ID
        'textField_m9dkdkph': 'shopEntityId',        # 数衍平台店铺ID
        'textField_m9dkdkpi': 'shopEntityName',      # 数衍平台店铺名称
        'textField_m9dkdkox': 'projectName',         # 项目名称
        'dateField_m9dkdkoz': 'saleTime'             # 销售日期
    }
    
    result = {}
    for yida_key, standard_key in mapping.items():
        if yida_key in yida_data:
            value = yida_data[yida_key]
            # 处理日期字段
            if yida_key == 'dateField_m9dkdkoz':
                timestamp = int(value) / 1000  # 毫秒转秒
                value = datetime.fromtimestamp(timestamp).strftime('%Y%m%d')
            # 处理数值字段（使用不带_value后缀的数值版本）
            elif yida_key.startswith('numberField_'):
                value = float(value) if value else 0.0
            result[standard_key] = value
            
    return result

def convert_standard_to_yida(standard_data: Dict, shop_mapping: Dict) -> Dict:
    """将标准格式转换为宜搭数据格式"""
    logging.debug(f"开始转换标准数据到宜搭格式: {standard_data}")
    # 修正映射关系的获取方式
    mapping = {
        'recommendAmount': 'numberField_m9dkdkp8',    # 推荐金额 - 商户推荐的销售金额
        'dailyBillAmount': 'numberField_m9dkdkp9',    # 日结金额 - 每日结算的销售总额
        'amount': 'numberField_m9dkdkpa',             # 净销售额 - 扣除退款后的实际销售金额
        'count': 'numberField_m9dkdkpb',              # 总销售笔数 - 包含所有销售交易的笔数
        'instoreAmount': 'numberField_m9dkdkpc',      # 店内净销售额 - 实体店铺内的销售金额
        'instoreCount': 'numberField_m9dkdkpd',       # 店内销售笔数 - 实体店铺内的销售交易笔数
        'onlineAmount': 'numberField_m9dkdkpe',       # 线上净销售额 - 线上渠道的销售金额
        'onlineCount': 'numberField_m9dkdkpf',        # 线上销售笔数 - 线上渠道的销售交易笔数
        'shopId': 'textField_m9dkdkpg',              # 数衍平台机构ID - 商户在数衍平台的唯一标识
        'shopEntityId': 'textField_m9dkdkph',        # 数衍平台店铺ID - 店铺在数衍平台的唯一标识
        'shopEntityName': 'textField_m9dkdkpi',      # 数衍平台店铺名称 - 店铺的显示名称
        'projectName': 'textField_m9dkdkox',         # 项目名称 - 所属项目的名称
        'saleTime': 'dateField_m9dkdkoz'             # 销售日期 - 销售数据统计的日期
    }
    result = {}
    
    # 获取项目名称
    shop_id = standard_data.get('shopId', '')
    project_name = shop_mapping.get(shop_id, '')
    if not project_name:
        logging.warning(f"警告：机构ID {shop_id} 未找到对应的项目名称")
    standard_data['projectName'] = project_name
    
    for standard_key, value in standard_data.items():
        if standard_key in mapping:
            yida_key = mapping[standard_key]
            # 处理日期字段
            if yida_key == 'dateField_m9dkdkoz':
                try:
                    dt = datetime.strptime(str(value), '%Y%m%d')
                    value = int(dt.timestamp() * 1000)  # 秒转毫秒
                except ValueError as e:
                    logging.error(f"日期格式转换错误: {value}, {str(e)}")
                    continue
            # 处理数值字段
            elif yida_key.startswith('numberField_'):
                try:
                    # 保持原始值，不进行类型转换
                    if value is None or value == '':
                        value = 0
                    # 对于笔数字段，确保是整数
                    if standard_key in ['count', 'instoreCount', 'onlineCount']:
                        value = int(value) if value else 0
                    # 对于金额字段，保持原始类型
                    elif standard_key in ['recommendAmount', 'dailyBillAmount', 'amount', 'instoreAmount', 'onlineAmount']:
                        if value == 0:
                            value = 0  # 保持为整数0
                        elif isinstance(value, (int, float)):
                            value = value  # 保持原始数值类型
                    result[f"{yida_key}_value"] = str(value)  # 添加字符串版本
                except ValueError as e:
                    logging.error(f"数值转换错误: {value}, {str(e)}")
                    continue
            result[yida_key] = value
            
    return result

def main():
    try:
        sync_manager = DataSyncManager()
        
        # 处理命令行参数
        if len(sys.argv) > 1:
            # 处理参数逻辑
            if sys.argv[1] == 'monthly':
                # 生成月度汇总数据
                if len(sys.argv) > 2:
                    start_date = sys.argv[2]
                    end_date = sys.argv[3] if len(sys.argv) > 3 else None
                else:
                    start_date = None
                    end_date = None
                
                sync_manager.update_monthly_summary(start_date, end_date)
                sync_manager.export_monthly_data_to_excel(start_date, end_date)
                return
            
            # 如果只有一个参数，则作为开始日期
            if len(sys.argv) == 2:
                start_date = sys.argv[1]
                end_date = None
            # 如果有两个参数，则分别作为开始日期和结束日期
            elif len(sys.argv) == 3:
                start_date = sys.argv[1]
                end_date = sys.argv[2]
            else:
                logging.error("参数格式错误，请使用以下格式：python sync_data.py [开始日期] [结束日期]")
                return
        else:
            start_date = None
            end_date = None
        
        sync_manager.sync_data(start_date, end_date)
        
        # 同步完成后，自动更新月度汇总数据
        sync_manager.update_monthly_summary(start_date, end_date)
        
    except Exception as e:
        logging.error(f"数据同步失败: {str(e)}")

if __name__ == '__main__':
    main()