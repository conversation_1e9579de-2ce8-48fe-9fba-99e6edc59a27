2025-06-30 01:30:34,217 - INFO - 使用默认增量同步（当天更新数据）
2025-06-30 01:30:34,217 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-30 01:30:34,217 - INFO - 查询参数: ('2025-06-30',)
2025-06-30 01:30:34,295 - INFO - MySQL查询成功，增量数据（日期: 2025-06-30），共获取 0 条记录
2025-06-30 01:30:34,295 - ERROR - 未获取到MySQL数据
2025-06-30 01:31:34,310 - INFO - 开始同步昨天与今天的销售数据: 2025-06-29 至 2025-06-30
2025-06-30 01:31:34,310 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-30 01:31:34,310 - INFO - 查询参数: ('2025-06-29', '2025-06-30')
2025-06-30 01:31:34,435 - INFO - MySQL查询成功，时间段: 2025-06-29 至 2025-06-30，共获取 59 条记录
2025-06-30 01:31:34,435 - INFO - 获取到 1 个日期需要处理: ['2025-06-29']
2025-06-30 01:31:34,435 - INFO - 开始处理日期: 2025-06-29
2025-06-30 01:31:34,435 - INFO - Request Parameters - Page 1:
2025-06-30 01:31:34,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 01:31:34,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 01:31:42,560 - ERROR - 处理日期 2025-06-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F5FBA1F3-1372-767F-A408-099AC0AA6FD0 Response: {'code': 'ServiceUnavailable', 'requestid': 'F5FBA1F3-1372-767F-A408-099AC0AA6FD0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F5FBA1F3-1372-767F-A408-099AC0AA6FD0)
2025-06-30 01:31:42,560 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-30 01:31:42,560 - INFO - 同步完成
2025-06-30 04:30:34,082 - INFO - 使用默认增量同步（当天更新数据）
2025-06-30 04:30:34,082 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-30 04:30:34,082 - INFO - 查询参数: ('2025-06-30',)
2025-06-30 04:30:34,207 - INFO - MySQL查询成功，增量数据（日期: 2025-06-30），共获取 5 条记录
2025-06-30 04:30:34,207 - INFO - 获取到 1 个日期需要处理: ['2025-06-29']
2025-06-30 04:30:34,207 - INFO - 开始处理日期: 2025-06-29
2025-06-30 04:30:34,223 - INFO - Request Parameters - Page 1:
2025-06-30 04:30:34,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 04:30:34,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 04:30:42,348 - ERROR - 处理日期 2025-06-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A6EC05F5-D7D4-7785-A82A-A7FB45C4789D Response: {'code': 'ServiceUnavailable', 'requestid': 'A6EC05F5-D7D4-7785-A82A-A7FB45C4789D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A6EC05F5-D7D4-7785-A82A-A7FB45C4789D)
2025-06-30 04:30:42,348 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-30 04:31:42,363 - INFO - 开始同步昨天与今天的销售数据: 2025-06-29 至 2025-06-30
2025-06-30 04:31:42,363 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-30 04:31:42,363 - INFO - 查询参数: ('2025-06-29', '2025-06-30')
2025-06-30 04:31:42,488 - INFO - MySQL查询成功，时间段: 2025-06-29 至 2025-06-30，共获取 118 条记录
2025-06-30 04:31:42,488 - INFO - 获取到 1 个日期需要处理: ['2025-06-29']
2025-06-30 04:31:42,488 - INFO - 开始处理日期: 2025-06-29
2025-06-30 04:31:42,488 - INFO - Request Parameters - Page 1:
2025-06-30 04:31:42,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 04:31:42,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 04:31:43,050 - INFO - Response - Page 1:
2025-06-30 04:31:43,050 - INFO - 第 1 页获取到 5 条记录
2025-06-30 04:31:43,550 - INFO - 查询完成，共获取到 5 条记录
2025-06-30 04:31:43,550 - INFO - 获取到 5 条表单数据
2025-06-30 04:31:43,550 - INFO - 当前日期 2025-06-29 有 114 条MySQL数据需要处理
2025-06-30 04:31:43,550 - INFO - 开始批量插入 109 条新记录
2025-06-30 04:31:43,769 - INFO - 批量插入响应状态码: 200
2025-06-30 04:31:43,769 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 20:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '42054B54-7C30-7349-A00A-EE01560F0241', 'x-acs-trace-id': '2bb13df77775deb31589447cb3b8f9a6', 'etag': '2Nvr6Ns/RDu+GWtb6MKVdfQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 04:31:43,769 - INFO - 批量插入响应体: {'result': ['FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMC9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMD9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICME9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMF9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMG9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMH9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMI9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMJ9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMK9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICML9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMM9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMN9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMO9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMP9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMQ9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMR9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMS9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMT9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMU9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMV9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMW9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMX9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMY9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMZ9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM0A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM1A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM2A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM3A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM4A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM5A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM6A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM7A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM8A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM9A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMAA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMBA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMCA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMDA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMEA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMFA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMGA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMHA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMIA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMJA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMKA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMLA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMMA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMNA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMOA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMPA']}
2025-06-30 04:31:43,769 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-30 04:31:43,769 - INFO - 成功插入的数据ID: ['FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMC9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMD9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICME9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMF9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMG9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMH9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMI9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMJ9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMK9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICML9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMM9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMN9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMO9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMP9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMQ9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMR9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMS9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMT9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMU9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMV9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMW9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMX9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMY9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMZ9', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM0A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM1A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM2A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM3A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM4A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM5A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM6A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM7A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM8A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICM9A', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMAA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMBA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMCA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMDA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMEA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMFA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMGA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMHA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMIA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMJA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMKA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMLA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMMA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMNA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMOA', 'FINST-CK766D71HZPWB4UWA9HAABU9TMBS3MACL4ICMPA']
2025-06-30 04:31:49,035 - INFO - 批量插入响应状态码: 200
2025-06-30 04:31:49,035 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 20:31:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '953F5E71-7655-7308-8C87-3C67A0022E77', 'x-acs-trace-id': '18ce733854d7e3199482dc1a0741d1f8', 'etag': '2ZdAx1MMw6X2P0STtm+BwIQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 04:31:49,035 - INFO - 批量插入响应体: {'result': ['FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICML7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMM7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMN7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMO7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMP7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMQ7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMR7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMS7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMT7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMU7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMV7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMW7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMX7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMY7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMZ7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM08', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM18', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM28', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM38', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM48', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM58', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM68', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM78', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM88', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM98', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMA8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMB8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMC8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMD8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICME8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMF8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMG8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMH8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMI8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMJ8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMK8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICML8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMM8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMN8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMO8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMP8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMQ8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMR8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMS8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMT8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMU8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMV8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMW8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMX8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMY8']}
2025-06-30 04:31:49,035 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-30 04:31:49,035 - INFO - 成功插入的数据ID: ['FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICML7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMM7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMN7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMO7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMP7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMQ7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMR7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMS7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMT7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMU7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMV7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMW7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMX7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMY7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMZ7', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM08', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM18', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM28', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM38', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM48', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM58', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM68', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM78', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM88', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICM98', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMA8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMB8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMC8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMD8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICME8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMF8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMG8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMH8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMI8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMJ8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMK8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICML8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMM8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMN8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMO8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMP8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMQ8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMR8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMS8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMT8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMU8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMV8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMW8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMX8', 'FINST-Q86667B1MXPWU6UHAHP67A1L8TIP2DCGL4ICMY8']
2025-06-30 04:31:54,206 - INFO - 批量插入响应状态码: 200
2025-06-30 04:31:54,206 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 20:31:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '444', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0C6A383F-3C49-7809-8455-088A6C1BCEB8', 'x-acs-trace-id': '6b780f865b778fb648b6fba56adb78ce', 'etag': '4bnbHWk2k6zHTxFd+73qSdw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 04:31:54,206 - INFO - 批量插入响应体: {'result': ['FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICMX8', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICMY8', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICMZ8', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICM09', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICM19', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICM29', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICM39', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICM49', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICM59']}
2025-06-30 04:31:54,206 - INFO - 批量插入表单数据成功，批次 3，共 9 条记录
2025-06-30 04:31:54,206 - INFO - 成功插入的数据ID: ['FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICMX8', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICMY8', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICMZ8', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICM09', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICM19', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICM29', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICM39', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICM49', 'FINST-SI766181I1QWN5CGF1BZE9OV9Y5P3CCKL4ICM59']
2025-06-30 04:31:59,222 - INFO - 批量插入完成，共 109 条记录
2025-06-30 04:31:59,222 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 109 条，错误: 0 条
2025-06-30 04:31:59,222 - INFO - 数据同步完成！更新: 0 条，插入: 109 条，错误: 0 条
2025-06-30 04:31:59,222 - INFO - 同步完成
2025-06-30 07:30:33,759 - INFO - 使用默认增量同步（当天更新数据）
2025-06-30 07:30:33,759 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-30 07:30:33,759 - INFO - 查询参数: ('2025-06-30',)
2025-06-30 07:30:33,900 - INFO - MySQL查询成功，增量数据（日期: 2025-06-30），共获取 5 条记录
2025-06-30 07:30:33,900 - INFO - 获取到 1 个日期需要处理: ['2025-06-29']
2025-06-30 07:30:33,900 - INFO - 开始处理日期: 2025-06-29
2025-06-30 07:30:33,900 - INFO - Request Parameters - Page 1:
2025-06-30 07:30:33,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 07:30:33,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 07:30:40,243 - INFO - Response - Page 1:
2025-06-30 07:30:40,243 - INFO - 第 1 页获取到 50 条记录
2025-06-30 07:30:40,759 - INFO - Request Parameters - Page 2:
2025-06-30 07:30:40,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 07:30:40,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 07:30:41,478 - INFO - Response - Page 2:
2025-06-30 07:30:41,478 - INFO - 第 2 页获取到 50 条记录
2025-06-30 07:30:41,978 - INFO - Request Parameters - Page 3:
2025-06-30 07:30:41,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 07:30:41,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 07:30:50,103 - ERROR - 处理日期 2025-06-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E18C05B7-5AF7-7BB5-B41E-C4F5F6F731F0 Response: {'code': 'ServiceUnavailable', 'requestid': 'E18C05B7-5AF7-7BB5-B41E-C4F5F6F731F0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E18C05B7-5AF7-7BB5-B41E-C4F5F6F731F0)
2025-06-30 07:30:50,103 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-30 07:31:50,118 - INFO - 开始同步昨天与今天的销售数据: 2025-06-29 至 2025-06-30
2025-06-30 07:31:50,118 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-30 07:31:50,118 - INFO - 查询参数: ('2025-06-29', '2025-06-30')
2025-06-30 07:31:50,243 - INFO - MySQL查询成功，时间段: 2025-06-29 至 2025-06-30，共获取 118 条记录
2025-06-30 07:31:50,243 - INFO - 获取到 1 个日期需要处理: ['2025-06-29']
2025-06-30 07:31:50,243 - INFO - 开始处理日期: 2025-06-29
2025-06-30 07:31:50,243 - INFO - Request Parameters - Page 1:
2025-06-30 07:31:50,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 07:31:50,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 07:31:50,930 - INFO - Response - Page 1:
2025-06-30 07:31:50,930 - INFO - 第 1 页获取到 50 条记录
2025-06-30 07:31:51,446 - INFO - Request Parameters - Page 2:
2025-06-30 07:31:51,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 07:31:51,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 07:31:52,134 - INFO - Response - Page 2:
2025-06-30 07:31:52,134 - INFO - 第 2 页获取到 50 条记录
2025-06-30 07:31:52,634 - INFO - Request Parameters - Page 3:
2025-06-30 07:31:52,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 07:31:52,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 07:31:53,180 - INFO - Response - Page 3:
2025-06-30 07:31:53,180 - INFO - 第 3 页获取到 14 条记录
2025-06-30 07:31:53,696 - INFO - 查询完成，共获取到 114 条记录
2025-06-30 07:31:53,696 - INFO - 获取到 114 条表单数据
2025-06-30 07:31:53,696 - INFO - 当前日期 2025-06-29 有 114 条MySQL数据需要处理
2025-06-30 07:31:53,696 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 07:31:53,696 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 07:31:53,696 - INFO - 同步完成
2025-06-30 10:30:34,049 - INFO - 使用默认增量同步（当天更新数据）
2025-06-30 10:30:34,049 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-30 10:30:34,049 - INFO - 查询参数: ('2025-06-30',)
2025-06-30 10:30:34,206 - INFO - MySQL查询成功，增量数据（日期: 2025-06-30），共获取 131 条记录
2025-06-30 10:30:34,206 - INFO - 获取到 4 个日期需要处理: ['2025-06-27', '2025-06-28', '2025-06-29', '2025-06-30']
2025-06-30 10:30:34,206 - INFO - 开始处理日期: 2025-06-27
2025-06-30 10:30:34,206 - INFO - Request Parameters - Page 1:
2025-06-30 10:30:34,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:30:34,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:30:42,331 - ERROR - 处理日期 2025-06-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F58FDAC2-1FC9-7976-A51C-D24608AC714E Response: {'code': 'ServiceUnavailable', 'requestid': 'F58FDAC2-1FC9-7976-A51C-D24608AC714E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F58FDAC2-1FC9-7976-A51C-D24608AC714E)
2025-06-30 10:30:42,331 - INFO - 开始处理日期: 2025-06-28
2025-06-30 10:30:42,331 - INFO - Request Parameters - Page 1:
2025-06-30 10:30:42,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:30:42,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:30:50,455 - INFO - Response - Page 1:
2025-06-30 10:30:50,455 - INFO - 第 1 页获取到 50 条记录
2025-06-30 10:30:50,955 - INFO - Request Parameters - Page 2:
2025-06-30 10:30:50,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:30:50,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:30:51,674 - INFO - Response - Page 2:
2025-06-30 10:30:51,674 - INFO - 第 2 页获取到 50 条记录
2025-06-30 10:30:52,174 - INFO - Request Parameters - Page 3:
2025-06-30 10:30:52,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:30:52,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:30:52,799 - INFO - Response - Page 3:
2025-06-30 10:30:52,799 - INFO - 第 3 页获取到 50 条记录
2025-06-30 10:30:53,299 - INFO - Request Parameters - Page 4:
2025-06-30 10:30:53,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:30:53,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:30:53,924 - INFO - Response - Page 4:
2025-06-30 10:30:53,924 - INFO - 第 4 页获取到 50 条记录
2025-06-30 10:30:54,424 - INFO - Request Parameters - Page 5:
2025-06-30 10:30:54,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:30:54,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:30:55,112 - INFO - Response - Page 5:
2025-06-30 10:30:55,127 - INFO - 第 5 页获取到 50 条记录
2025-06-30 10:30:55,643 - INFO - Request Parameters - Page 6:
2025-06-30 10:30:55,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:30:55,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:30:56,330 - INFO - Response - Page 6:
2025-06-30 10:30:56,330 - INFO - 第 6 页获取到 50 条记录
2025-06-30 10:30:56,846 - INFO - Request Parameters - Page 7:
2025-06-30 10:30:56,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:30:56,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:30:57,502 - INFO - Response - Page 7:
2025-06-30 10:30:57,502 - INFO - 第 7 页获取到 50 条记录
2025-06-30 10:30:58,018 - INFO - Request Parameters - Page 8:
2025-06-30 10:30:58,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:30:58,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:30:58,643 - INFO - Response - Page 8:
2025-06-30 10:30:58,643 - INFO - 第 8 页获取到 50 条记录
2025-06-30 10:30:59,159 - INFO - Request Parameters - Page 9:
2025-06-30 10:30:59,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:30:59,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:30:59,862 - INFO - Response - Page 9:
2025-06-30 10:30:59,862 - INFO - 第 9 页获取到 50 条记录
2025-06-30 10:31:00,362 - INFO - Request Parameters - Page 10:
2025-06-30 10:31:00,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:31:00,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:31:00,971 - INFO - Response - Page 10:
2025-06-30 10:31:00,971 - INFO - 第 10 页获取到 27 条记录
2025-06-30 10:31:01,487 - INFO - 查询完成，共获取到 477 条记录
2025-06-30 10:31:01,487 - INFO - 获取到 477 条表单数据
2025-06-30 10:31:01,487 - INFO - 当前日期 2025-06-28 有 1 条MySQL数据需要处理
2025-06-30 10:31:01,487 - INFO - 开始批量插入 1 条新记录
2025-06-30 10:31:01,643 - INFO - 批量插入响应状态码: 200
2025-06-30 10:31:01,643 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 02:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4A814C70-4323-7B1A-8B09-90F1567E2224', 'x-acs-trace-id': '6514384adb4f50b300511767766cc541', 'etag': '5+lo/6euHq9/Y6hdzaF92Yg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 10:31:01,643 - INFO - 批量插入响应体: {'result': ['FINST-X3766I91WQQWFZ92DBLFWBEH67B42JKEFHICM2']}
2025-06-30 10:31:01,643 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-30 10:31:01,643 - INFO - 成功插入的数据ID: ['FINST-X3766I91WQQWFZ92DBLFWBEH67B42JKEFHICM2']
2025-06-30 10:31:06,658 - INFO - 批量插入完成，共 1 条记录
2025-06-30 10:31:06,658 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-30 10:31:06,658 - INFO - 开始处理日期: 2025-06-29
2025-06-30 10:31:06,658 - INFO - Request Parameters - Page 1:
2025-06-30 10:31:06,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:31:06,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:31:07,362 - INFO - Response - Page 1:
2025-06-30 10:31:07,362 - INFO - 第 1 页获取到 50 条记录
2025-06-30 10:31:07,877 - INFO - Request Parameters - Page 2:
2025-06-30 10:31:07,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:31:07,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:31:08,580 - INFO - Response - Page 2:
2025-06-30 10:31:08,580 - INFO - 第 2 页获取到 50 条记录
2025-06-30 10:31:09,096 - INFO - Request Parameters - Page 3:
2025-06-30 10:31:09,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:31:09,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:31:09,596 - INFO - Response - Page 3:
2025-06-30 10:31:09,596 - INFO - 第 3 页获取到 14 条记录
2025-06-30 10:31:10,112 - INFO - 查询完成，共获取到 114 条记录
2025-06-30 10:31:10,112 - INFO - 获取到 114 条表单数据
2025-06-30 10:31:10,112 - INFO - 当前日期 2025-06-29 有 125 条MySQL数据需要处理
2025-06-30 10:31:10,112 - INFO - 开始更新记录 - 表单实例ID: FINST-RI766091U8OWGTMQBXRAV9M2V5ZC2VALZ1HCM6Y
2025-06-30 10:31:10,658 - INFO - 更新表单数据成功: FINST-RI766091U8OWGTMQBXRAV9M2V5ZC2VALZ1HCM6Y
2025-06-30 10:31:10,658 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26540.0, 'new_value': 23574.06}, {'field': 'total_amount', 'old_value': 26540.0, 'new_value': 23574.06}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/1b947ee80f274f8b8926e3c50ae28116.jpg?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=lSFLidJo3g82iOzIkRZnpoSUIkA%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/3fd45db8a12a4954a5e755cc4d79410b.jpg?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=YdsrXF5aIHbJCEbMWjRsiU8eP4E%3D'}]
2025-06-30 10:31:10,658 - INFO - 开始批量插入 119 条新记录
2025-06-30 10:31:10,955 - INFO - 批量插入响应状态码: 200
2025-06-30 10:31:10,955 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 02:31:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2386', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1BF2F686-C4B8-7031-A747-68291395E6CD', 'x-acs-trace-id': '3d69c0e88ac14317d9d7c6755484da9f', 'etag': '2UwA2JLVnMEOsY77ekHPrlw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 10:31:10,955 - INFO - 批量插入响应体: {'result': ['FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMA', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMB', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMC', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMD', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICME', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMF', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMG', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMH', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMI', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMJ', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMK', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICML', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMM', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMN', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMO', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMP', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMQ', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMR', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMS', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMT', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMU', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMV', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMW', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMX', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMY', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMZ', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM01', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM11', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM21', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM31', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM41', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM51', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM61', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM71', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM81', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM91', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMA1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMB1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMC1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMD1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICME1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMF1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMG1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMH1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMI1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMJ1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3SQLFHICMK1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3SQLFHICML1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3SQLFHICMM1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3SQLFHICMN1']}
2025-06-30 10:31:10,955 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-30 10:31:10,955 - INFO - 成功插入的数据ID: ['FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMA', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMB', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMC', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMD', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICME', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMF', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMG', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMH', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMI', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMJ', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMK', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICML', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMM', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMN', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMO', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMP', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMQ', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMR', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMS', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMT', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMU', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMV', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMW', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMX', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMY', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMZ', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM01', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM11', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM21', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM31', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM41', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM51', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM61', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM71', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM81', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICM91', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMA1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMB1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMC1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMD1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICME1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMF1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMG1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMH1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMI1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3RQLFHICMJ1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3SQLFHICMK1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3SQLFHICML1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3SQLFHICMM1', 'FINST-TKF669819RQW1BNFDWB0NB86ZDGH3SQLFHICMN1']
2025-06-30 10:31:16,221 - INFO - 批量插入响应状态码: 200
2025-06-30 10:31:16,221 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 02:31:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '25994C1C-2D50-750C-BED8-F21BFD5D0C49', 'x-acs-trace-id': '2a72eba57ae88dc7a032761e6bbbee17', 'etag': '2Sy1GrlBxVPxh0yNdOFC+tg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 10:31:16,221 - INFO - 批量插入响应体: {'result': ['FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMD8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICME8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMF8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMG8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMH8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMI8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMJ8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMK8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICML8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMM8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMN8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMO8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMP8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMQ8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMR8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMS8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMT8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMU8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMV8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMW8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMX8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMY8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMZ8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM09', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM19', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM29', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM39', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM49', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM59', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM69', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM79', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM89', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM99', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMA9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMB9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMC9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMD9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICME9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMF9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMG9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMH9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMI9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMJ9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICMK9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICML9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICMM9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICMN9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICMO9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICMP9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICMQ9']}
2025-06-30 10:31:16,221 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-30 10:31:16,221 - INFO - 成功插入的数据ID: ['FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMD8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICME8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMF8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMG8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMH8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMI8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMJ8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMK8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICML8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMM8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMN8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMO8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMP8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMQ8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMR8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMS8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMT8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMU8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMV8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMW8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMX8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMY8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMZ8', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM09', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM19', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM29', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM39', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM49', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM59', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM69', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM79', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM89', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICM99', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMA9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMB9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMC9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMD9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICME9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMF9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMG9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMH9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMI9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMJ9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICMK9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICML9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICMM9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICMN9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICMO9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICMP9', 'FINST-7PF66CC150QW227G8EPV7D0FC8Z83XSPFHICMQ9']
2025-06-30 10:31:21,440 - INFO - 批量插入响应状态码: 200
2025-06-30 10:31:21,440 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 02:31:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '924', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3D421A32-0DA7-78AD-8F39-0B002FD571AA', 'x-acs-trace-id': '8f07dccb5496ff823ce126fdfeff8a55', 'etag': '9HBcRWlyNByWG6pShreUcEA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 10:31:21,440 - INFO - 批量插入响应体: {'result': ['FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMAC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMBC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMCC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMDC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMEC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMFC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMGC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMHC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMIC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMJC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMKC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMLC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMMC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMNC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMOC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMPC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMQC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMRC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMSC']}
2025-06-30 10:31:21,440 - INFO - 批量插入表单数据成功，批次 3，共 19 条记录
2025-06-30 10:31:21,440 - INFO - 成功插入的数据ID: ['FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMAC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMBC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMCC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMDC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMEC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMFC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMGC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMHC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMIC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMJC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMKC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMLC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMMC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMNC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMOC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMPC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMQC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMRC', 'FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMSC']
2025-06-30 10:31:26,455 - INFO - 批量插入完成，共 119 条记录
2025-06-30 10:31:26,455 - INFO - 日期 2025-06-29 处理完成 - 更新: 1 条，插入: 119 条，错误: 0 条
2025-06-30 10:31:26,455 - INFO - 开始处理日期: 2025-06-30
2025-06-30 10:31:26,455 - INFO - Request Parameters - Page 1:
2025-06-30 10:31:26,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:31:26,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:31:26,877 - INFO - Response - Page 1:
2025-06-30 10:31:26,877 - INFO - 查询完成，共获取到 0 条记录
2025-06-30 10:31:26,877 - INFO - 获取到 0 条表单数据
2025-06-30 10:31:26,877 - INFO - 当前日期 2025-06-30 有 1 条MySQL数据需要处理
2025-06-30 10:31:26,877 - INFO - 开始批量插入 1 条新记录
2025-06-30 10:31:27,127 - INFO - 批量插入响应状态码: 200
2025-06-30 10:31:27,127 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 02:31:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DE94D1DC-29D4-7D52-99F6-44A2492D4509', 'x-acs-trace-id': '44aae1ba5d0b9b70e5be2068add825d7', 'etag': '6kukPygplb5jHNKhKlSZfWw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 10:31:27,127 - INFO - 批量插入响应体: {'result': ['FINST-KLF66WC1N8OW3LTWA16JR79RM7252F8YFHICMQV']}
2025-06-30 10:31:27,127 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-30 10:31:27,127 - INFO - 成功插入的数据ID: ['FINST-KLF66WC1N8OW3LTWA16JR79RM7252F8YFHICMQV']
2025-06-30 10:31:32,158 - INFO - 批量插入完成，共 1 条记录
2025-06-30 10:31:32,158 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-30 10:31:32,158 - INFO - 数据同步完成！更新: 1 条，插入: 121 条，错误: 1 条
2025-06-30 10:32:32,174 - INFO - 开始同步昨天与今天的销售数据: 2025-06-29 至 2025-06-30
2025-06-30 10:32:32,174 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-30 10:32:32,174 - INFO - 查询参数: ('2025-06-29', '2025-06-30')
2025-06-30 10:32:32,314 - INFO - MySQL查询成功，时间段: 2025-06-29 至 2025-06-30，共获取 463 条记录
2025-06-30 10:32:32,314 - INFO - 获取到 2 个日期需要处理: ['2025-06-29', '2025-06-30']
2025-06-30 10:32:32,330 - INFO - 开始处理日期: 2025-06-29
2025-06-30 10:32:32,330 - INFO - Request Parameters - Page 1:
2025-06-30 10:32:32,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:32:32,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:32:33,017 - INFO - Response - Page 1:
2025-06-30 10:32:33,017 - INFO - 第 1 页获取到 50 条记录
2025-06-30 10:32:33,533 - INFO - Request Parameters - Page 2:
2025-06-30 10:32:33,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:32:33,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:32:34,158 - INFO - Response - Page 2:
2025-06-30 10:32:34,158 - INFO - 第 2 页获取到 50 条记录
2025-06-30 10:32:34,658 - INFO - Request Parameters - Page 3:
2025-06-30 10:32:34,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:32:34,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:32:35,314 - INFO - Response - Page 3:
2025-06-30 10:32:35,314 - INFO - 第 3 页获取到 50 条记录
2025-06-30 10:32:35,814 - INFO - Request Parameters - Page 4:
2025-06-30 10:32:35,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:32:35,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:32:36,470 - INFO - Response - Page 4:
2025-06-30 10:32:36,470 - INFO - 第 4 页获取到 50 条记录
2025-06-30 10:32:36,986 - INFO - Request Parameters - Page 5:
2025-06-30 10:32:36,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:32:36,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:32:37,642 - INFO - Response - Page 5:
2025-06-30 10:32:37,642 - INFO - 第 5 页获取到 33 条记录
2025-06-30 10:32:38,142 - INFO - 查询完成，共获取到 233 条记录
2025-06-30 10:32:38,142 - INFO - 获取到 233 条表单数据
2025-06-30 10:32:38,142 - INFO - 当前日期 2025-06-29 有 449 条MySQL数据需要处理
2025-06-30 10:32:38,142 - INFO - 开始批量插入 216 条新记录
2025-06-30 10:32:38,377 - INFO - 批量插入响应状态码: 200
2025-06-30 10:32:38,377 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 02:32:38 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E62ACCC8-FA42-72D1-8BAD-18A9621C1A14', 'x-acs-trace-id': '374d8ce1ca0f5671978294b46547c79c', 'etag': '2a0pyJ8wxTsusYpJtD4OF5w2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 10:32:38,377 - INFO - 批量插入响应体: {'result': ['FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMV4', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMW4', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMX4', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMY4', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMZ4', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM05', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM15', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM25', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM35', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM45', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM55', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM65', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM75', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM85', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM95', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMA5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMB5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMC5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMD5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICME5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMF5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMG5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMH5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMI5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMJ5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMK5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICML5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMM5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMN5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMO5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMP5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMQ5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMR5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMS5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMT5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMU5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMV5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMW5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMX5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMY5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMZ5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM06', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM16', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM26', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM36', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM46', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM56', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM66', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM76', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM86']}
2025-06-30 10:32:38,377 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-30 10:32:38,377 - INFO - 成功插入的数据ID: ['FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMV4', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMW4', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMX4', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMY4', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMZ4', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM05', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM15', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM25', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM35', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM45', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM55', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM65', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM75', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM85', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM95', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMA5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMB5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMC5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMD5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICME5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMF5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMG5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMH5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMI5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMJ5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMK5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICML5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMM5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMN5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMO5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMP5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMQ5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMR5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMS5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMT5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMU5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMV5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMW5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMX5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMY5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICMZ5', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM06', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM16', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM26', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM36', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM46', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM56', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM66', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM76', 'FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM86']
2025-06-30 10:32:43,673 - INFO - 批量插入响应状态码: 200
2025-06-30 10:32:43,673 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 02:32:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2381', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '35CBC3E1-94DB-7F76-8225-62D1D3BB9328', 'x-acs-trace-id': '999b53db89b4ebf550e935c017addb06', 'etag': '2hy5B+1NvOsbbtl0wJErUeA1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 10:32:43,673 - INFO - 批量插入响应体: {'result': ['FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM5', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM6', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM7', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM8', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM9', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMA', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMB', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMC', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMD', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICME', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMF', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMG', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMH', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMI', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMJ', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMK', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICML', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMM', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMN', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMO', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMP', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMQ', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMR', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMS', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMT', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMU', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMV', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMW', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMX', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMY', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMZ', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM01', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM11', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM21', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM31', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM41', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM51', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM61', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM71', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM81', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM91', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMA1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMB1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMC1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMD1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICME1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMF1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMG1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMH1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMI1']}
2025-06-30 10:32:43,673 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-30 10:32:43,673 - INFO - 成功插入的数据ID: ['FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM5', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM6', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM7', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM8', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM9', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMA', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMB', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMC', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMD', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICME', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMF', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMG', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMH', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMI', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMJ', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMK', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICML', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMM', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMN', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMO', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMP', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMQ', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMR', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMS', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMT', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMU', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMV', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMW', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMX', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMY', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMZ', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM01', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM11', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM21', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM31', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM41', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM51', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM61', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM71', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM81', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICM91', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMA1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMB1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMC1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMD1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICME1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMF1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMG1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMH1', 'FINST-EWE66Z91LRQWU1E3E0DVK7BG0UFD29ALHHICMI1']
2025-06-30 10:32:48,923 - INFO - 批量插入响应状态码: 200
2025-06-30 10:32:48,923 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 02:32:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '61748194-3BD2-7AC5-9569-1C27FDD30C91', 'x-acs-trace-id': 'c51ab227a172772a216ac2f3acfad6f4', 'etag': '2XQElq8E5Emij5u6Mb0VG4w2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 10:32:48,923 - INFO - 批量插入响应体: {'result': ['FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMPU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMQU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMRU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMSU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMTU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMUU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMVU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMWU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMXU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMYU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMZU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM0V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM1V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM2V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM3V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM4V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM5V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM6V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM7V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM8V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM9V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMAV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMBV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMCV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMDV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMEV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMFV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMGV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMHV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMIV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMJV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMKV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMLV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMMV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMNV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMOV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMPV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMQV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMRV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMSV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMTV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMUV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMVV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMWV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMXV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMYV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMZV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM0W', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM1W', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM2W']}
2025-06-30 10:32:48,923 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-30 10:32:48,923 - INFO - 成功插入的数据ID: ['FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMPU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMQU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMRU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMSU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMTU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMUU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMVU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMWU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMXU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMYU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMZU', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM0V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM1V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM2V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM3V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM4V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM5V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM6V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM7V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM8V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM9V', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMAV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMBV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMCV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMDV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMEV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMFV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMGV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMHV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMIV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMJV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMKV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMLV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMMV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMNV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMOV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMPV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMQV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMRV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMSV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMTV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMUV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMVV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMWV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMXV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMYV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICMZV', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM0W', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM1W', 'FINST-1V966BA1T6OW4NED8OMAA762J2GW2BCPHHICM2W']
2025-06-30 10:32:54,189 - INFO - 批量插入响应状态码: 200
2025-06-30 10:32:54,189 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 02:32:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2F576534-2BCB-7B95-A244-479E5709D681', 'x-acs-trace-id': '715a5649b354482fb4733e37982e00e6', 'etag': '2ZEhfTawyij8FtuCA4mj9dQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 10:32:54,189 - INFO - 批量插入响应体: {'result': ['FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM13', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM23', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM33', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM43', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM53', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM63', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM73', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM83', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM93', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMA3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMB3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMC3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMD3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICME3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMF3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMG3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMH3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMI3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMJ3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMK3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICML3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMM3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMN3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMO3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMP3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMQ3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMR3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMS3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMT3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMU3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMV3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMW3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMX3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMY3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMZ3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM04', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM14', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM24', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM34', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM44', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM54', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM64', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM74', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM84', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM94', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMA4', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMB4', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMC4', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMD4', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICME4']}
2025-06-30 10:32:54,189 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-06-30 10:32:54,189 - INFO - 成功插入的数据ID: ['FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM13', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM23', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM33', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM43', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM53', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM63', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM73', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM83', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM93', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMA3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMB3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMC3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMD3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICME3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMF3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMG3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMH3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMI3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMJ3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMK3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICML3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMM3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMN3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMO3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMP3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMQ3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMR3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMS3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMT3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMU3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMV3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMW3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMX3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMY3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMZ3', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM04', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM14', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM24', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM34', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM44', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM54', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM64', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM74', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM84', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICM94', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMA4', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMB4', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMC4', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICMD4', 'FINST-XL866HB1YPQWNNJW5YYHV8ACR57J3DETHHICME4']
2025-06-30 10:32:59,392 - INFO - 批量插入响应状态码: 200
2025-06-30 10:32:59,392 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 02:32:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '764', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E52720FC-DF5E-7824-B5C3-E7DC245251E5', 'x-acs-trace-id': '982de334c36e1b85d6e51ef2ce565691', 'etag': '71LWWeqywcruYUz8XPglvWg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 10:32:59,392 - INFO - 批量插入响应体: {'result': ['FINST-********************************HHICM5', 'FINST-********************************HHICM6', 'FINST-********************************HHICM7', 'FINST-********************************HHICM8', 'FINST-********************************HHICM9', 'FINST-********************************HHICMA', 'FINST-********************************HHICMB', 'FINST-********************************HHICMC', 'FINST-********************************HHICMD', 'FINST-********************************HHICME', 'FINST-********************************HHICMF', 'FINST-********************************HHICMG', 'FINST-********************************HHICMH', 'FINST-********************************HHICMI', 'FINST-********************************HHICMJ', 'FINST-********************************HHICMK']}
2025-06-30 10:32:59,392 - INFO - 批量插入表单数据成功，批次 5，共 16 条记录
2025-06-30 10:32:59,392 - INFO - 成功插入的数据ID: ['FINST-********************************HHICM5', 'FINST-********************************HHICM6', 'FINST-********************************HHICM7', 'FINST-********************************HHICM8', 'FINST-********************************HHICM9', 'FINST-********************************HHICMA', 'FINST-********************************HHICMB', 'FINST-********************************HHICMC', 'FINST-********************************HHICMD', 'FINST-********************************HHICME', 'FINST-********************************HHICMF', 'FINST-********************************HHICMG', 'FINST-********************************HHICMH', 'FINST-********************************HHICMI', 'FINST-********************************HHICMJ', 'FINST-********************************HHICMK']
2025-06-30 10:33:04,408 - INFO - 批量插入完成，共 216 条记录
2025-06-30 10:33:04,408 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 216 条，错误: 0 条
2025-06-30 10:33:04,408 - INFO - 开始处理日期: 2025-06-30
2025-06-30 10:33:04,408 - INFO - Request Parameters - Page 1:
2025-06-30 10:33:04,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 10:33:04,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 10:33:04,830 - INFO - Response - Page 1:
2025-06-30 10:33:04,830 - INFO - 第 1 页获取到 1 条记录
2025-06-30 10:33:05,330 - INFO - 查询完成，共获取到 1 条记录
2025-06-30 10:33:05,330 - INFO - 获取到 1 条表单数据
2025-06-30 10:33:05,330 - INFO - 当前日期 2025-06-30 有 1 条MySQL数据需要处理
2025-06-30 10:33:05,330 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 10:33:05,330 - INFO - 数据同步完成！更新: 0 条，插入: 216 条，错误: 0 条
2025-06-30 10:33:05,330 - INFO - 同步完成
2025-06-30 13:30:34,055 - INFO - 使用默认增量同步（当天更新数据）
2025-06-30 13:30:34,055 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-30 13:30:34,055 - INFO - 查询参数: ('2025-06-30',)
2025-06-30 13:30:34,211 - INFO - MySQL查询成功，增量数据（日期: 2025-06-30），共获取 145 条记录
2025-06-30 13:30:34,211 - INFO - 获取到 7 个日期需要处理: ['2025-06-18', '2025-06-21', '2025-06-25', '2025-06-27', '2025-06-28', '2025-06-29', '2025-06-30']
2025-06-30 13:30:34,211 - INFO - 开始处理日期: 2025-06-18
2025-06-30 13:30:34,211 - INFO - Request Parameters - Page 1:
2025-06-30 13:30:34,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:30:34,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:30:42,320 - ERROR - 处理日期 2025-06-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FFB13C87-ED05-7889-9D11-952FE73026A3 Response: {'code': 'ServiceUnavailable', 'requestid': 'FFB13C87-ED05-7889-9D11-952FE73026A3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FFB13C87-ED05-7889-9D11-952FE73026A3)
2025-06-30 13:30:42,320 - INFO - 开始处理日期: 2025-06-21
2025-06-30 13:30:42,320 - INFO - Request Parameters - Page 1:
2025-06-30 13:30:42,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:30:42,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:30:45,149 - INFO - Response - Page 1:
2025-06-30 13:30:45,149 - INFO - 第 1 页获取到 50 条记录
2025-06-30 13:30:45,664 - INFO - Request Parameters - Page 2:
2025-06-30 13:30:45,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:30:45,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:30:51,867 - INFO - Response - Page 2:
2025-06-30 13:30:51,867 - INFO - 第 2 页获取到 50 条记录
2025-06-30 13:30:52,383 - INFO - Request Parameters - Page 3:
2025-06-30 13:30:52,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:30:52,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:30:53,102 - INFO - Response - Page 3:
2025-06-30 13:30:53,102 - INFO - 第 3 页获取到 50 条记录
2025-06-30 13:30:53,617 - INFO - Request Parameters - Page 4:
2025-06-30 13:30:53,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:30:53,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:30:54,320 - INFO - Response - Page 4:
2025-06-30 13:30:54,320 - INFO - 第 4 页获取到 50 条记录
2025-06-30 13:30:54,836 - INFO - Request Parameters - Page 5:
2025-06-30 13:30:54,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:30:54,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:30:55,445 - INFO - Response - Page 5:
2025-06-30 13:30:55,445 - INFO - 第 5 页获取到 50 条记录
2025-06-30 13:30:55,961 - INFO - Request Parameters - Page 6:
2025-06-30 13:30:55,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:30:55,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:30:56,633 - INFO - Response - Page 6:
2025-06-30 13:30:56,633 - INFO - 第 6 页获取到 50 条记录
2025-06-30 13:30:57,148 - INFO - Request Parameters - Page 7:
2025-06-30 13:30:57,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:30:57,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:30:57,805 - INFO - Response - Page 7:
2025-06-30 13:30:57,805 - INFO - 第 7 页获取到 50 条记录
2025-06-30 13:30:58,320 - INFO - Request Parameters - Page 8:
2025-06-30 13:30:58,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:30:58,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:30:59,023 - INFO - Response - Page 8:
2025-06-30 13:30:59,023 - INFO - 第 8 页获取到 50 条记录
2025-06-30 13:30:59,523 - INFO - Request Parameters - Page 9:
2025-06-30 13:30:59,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:30:59,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:00,227 - INFO - Response - Page 9:
2025-06-30 13:31:00,227 - INFO - 第 9 页获取到 50 条记录
2025-06-30 13:31:00,742 - INFO - Request Parameters - Page 10:
2025-06-30 13:31:00,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:00,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:01,445 - INFO - Response - Page 10:
2025-06-30 13:31:01,445 - INFO - 第 10 页获取到 50 条记录
2025-06-30 13:31:01,961 - INFO - Request Parameters - Page 11:
2025-06-30 13:31:01,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:01,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:02,570 - INFO - Response - Page 11:
2025-06-30 13:31:02,570 - INFO - 第 11 页获取到 20 条记录
2025-06-30 13:31:03,086 - INFO - 查询完成，共获取到 520 条记录
2025-06-30 13:31:03,086 - INFO - 获取到 520 条表单数据
2025-06-30 13:31:03,086 - INFO - 当前日期 2025-06-21 有 1 条MySQL数据需要处理
2025-06-30 13:31:03,086 - INFO - 开始更新记录 - 表单实例ID: FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMA2
2025-06-30 13:31:03,664 - INFO - 更新表单数据成功: FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMA2
2025-06-30 13:31:03,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-06-30 13:31:03,664 - INFO - 日期 2025-06-21 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-30 13:31:03,664 - INFO - 开始处理日期: 2025-06-25
2025-06-30 13:31:03,664 - INFO - Request Parameters - Page 1:
2025-06-30 13:31:03,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:03,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:04,383 - INFO - Response - Page 1:
2025-06-30 13:31:04,383 - INFO - 第 1 页获取到 50 条记录
2025-06-30 13:31:04,898 - INFO - Request Parameters - Page 2:
2025-06-30 13:31:04,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:04,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:05,617 - INFO - Response - Page 2:
2025-06-30 13:31:05,617 - INFO - 第 2 页获取到 50 条记录
2025-06-30 13:31:06,133 - INFO - Request Parameters - Page 3:
2025-06-30 13:31:06,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:06,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:06,742 - INFO - Response - Page 3:
2025-06-30 13:31:06,742 - INFO - 第 3 页获取到 50 条记录
2025-06-30 13:31:07,258 - INFO - Request Parameters - Page 4:
2025-06-30 13:31:07,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:07,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:07,976 - INFO - Response - Page 4:
2025-06-30 13:31:07,976 - INFO - 第 4 页获取到 50 条记录
2025-06-30 13:31:08,477 - INFO - Request Parameters - Page 5:
2025-06-30 13:31:08,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:08,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:09,164 - INFO - Response - Page 5:
2025-06-30 13:31:09,164 - INFO - 第 5 页获取到 50 条记录
2025-06-30 13:31:09,664 - INFO - Request Parameters - Page 6:
2025-06-30 13:31:09,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:09,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:10,351 - INFO - Response - Page 6:
2025-06-30 13:31:10,351 - INFO - 第 6 页获取到 50 条记录
2025-06-30 13:31:10,867 - INFO - Request Parameters - Page 7:
2025-06-30 13:31:10,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:10,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:11,523 - INFO - Response - Page 7:
2025-06-30 13:31:11,523 - INFO - 第 7 页获取到 50 条记录
2025-06-30 13:31:12,023 - INFO - Request Parameters - Page 8:
2025-06-30 13:31:12,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:12,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:12,758 - INFO - Response - Page 8:
2025-06-30 13:31:12,758 - INFO - 第 8 页获取到 50 条记录
2025-06-30 13:31:13,273 - INFO - Request Parameters - Page 9:
2025-06-30 13:31:13,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:13,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:13,945 - INFO - Response - Page 9:
2025-06-30 13:31:13,945 - INFO - 第 9 页获取到 50 条记录
2025-06-30 13:31:14,445 - INFO - Request Parameters - Page 10:
2025-06-30 13:31:14,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:14,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:15,117 - INFO - Response - Page 10:
2025-06-30 13:31:15,117 - INFO - 第 10 页获取到 49 条记录
2025-06-30 13:31:15,633 - INFO - 查询完成，共获取到 499 条记录
2025-06-30 13:31:15,633 - INFO - 获取到 499 条表单数据
2025-06-30 13:31:15,633 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-30 13:31:15,633 - INFO - 开始批量插入 1 条新记录
2025-06-30 13:31:15,805 - INFO - 批量插入响应状态码: 200
2025-06-30 13:31:15,805 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 05:31:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9830B59E-D9A1-7E07-BBEC-91D68A3ECD4D', 'x-acs-trace-id': '21e1ca26c06cd40703f88de3ee3e8b84', 'etag': '6lgVnq1agD3qTGHwzYh+KyQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 13:31:15,805 - INFO - 批量插入响应体: {'result': ['FINST-HJ966H81O6OWBYIADX8P96UQETKA3GV6VNICMSS']}
2025-06-30 13:31:15,805 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-30 13:31:15,805 - INFO - 成功插入的数据ID: ['FINST-HJ966H81O6OWBYIADX8P96UQETKA3GV6VNICMSS']
2025-06-30 13:31:20,820 - INFO - 批量插入完成，共 1 条记录
2025-06-30 13:31:20,820 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-30 13:31:20,820 - INFO - 开始处理日期: 2025-06-27
2025-06-30 13:31:20,820 - INFO - Request Parameters - Page 1:
2025-06-30 13:31:20,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:20,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:21,539 - INFO - Response - Page 1:
2025-06-30 13:31:21,539 - INFO - 第 1 页获取到 50 条记录
2025-06-30 13:31:22,055 - INFO - Request Parameters - Page 2:
2025-06-30 13:31:22,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:22,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:22,695 - INFO - Response - Page 2:
2025-06-30 13:31:22,695 - INFO - 第 2 页获取到 50 条记录
2025-06-30 13:31:23,211 - INFO - Request Parameters - Page 3:
2025-06-30 13:31:23,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:23,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:23,867 - INFO - Response - Page 3:
2025-06-30 13:31:23,867 - INFO - 第 3 页获取到 50 条记录
2025-06-30 13:31:24,367 - INFO - Request Parameters - Page 4:
2025-06-30 13:31:24,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:24,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:25,055 - INFO - Response - Page 4:
2025-06-30 13:31:25,055 - INFO - 第 4 页获取到 50 条记录
2025-06-30 13:31:25,570 - INFO - Request Parameters - Page 5:
2025-06-30 13:31:25,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:25,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:26,242 - INFO - Response - Page 5:
2025-06-30 13:31:26,242 - INFO - 第 5 页获取到 50 条记录
2025-06-30 13:31:26,758 - INFO - Request Parameters - Page 6:
2025-06-30 13:31:26,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:26,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:27,367 - INFO - Response - Page 6:
2025-06-30 13:31:27,367 - INFO - 第 6 页获取到 50 条记录
2025-06-30 13:31:27,883 - INFO - Request Parameters - Page 7:
2025-06-30 13:31:27,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:27,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:28,476 - INFO - Response - Page 7:
2025-06-30 13:31:28,476 - INFO - 第 7 页获取到 50 条记录
2025-06-30 13:31:28,976 - INFO - Request Parameters - Page 8:
2025-06-30 13:31:28,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:28,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:29,617 - INFO - Response - Page 8:
2025-06-30 13:31:29,617 - INFO - 第 8 页获取到 50 条记录
2025-06-30 13:31:30,117 - INFO - Request Parameters - Page 9:
2025-06-30 13:31:30,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:30,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:30,804 - INFO - Response - Page 9:
2025-06-30 13:31:30,804 - INFO - 第 9 页获取到 50 条记录
2025-06-30 13:31:31,320 - INFO - Request Parameters - Page 10:
2025-06-30 13:31:31,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:31,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:31,945 - INFO - Response - Page 10:
2025-06-30 13:31:31,945 - INFO - 第 10 页获取到 26 条记录
2025-06-30 13:31:32,445 - INFO - 查询完成，共获取到 476 条记录
2025-06-30 13:31:32,445 - INFO - 获取到 476 条表单数据
2025-06-30 13:31:32,445 - INFO - 当前日期 2025-06-27 有 2 条MySQL数据需要处理
2025-06-30 13:31:32,445 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMWB
2025-06-30 13:31:32,976 - INFO - 更新表单数据成功: FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMWB
2025-06-30 13:31:32,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41.0, 'new_value': 2475.0}, {'field': 'total_amount', 'old_value': 41.0, 'new_value': 2475.0}, {'field': 'order_count', 'old_value': 2475, 'new_value': 41}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-30 13:31:32,976 - INFO - 开始批量插入 1 条新记录
2025-06-30 13:31:33,133 - INFO - 批量插入响应状态码: 200
2025-06-30 13:31:33,133 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 05:31:33 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7A854916-E607-7061-8A65-0C8E49050A92', 'x-acs-trace-id': '22e27da3e3fdd593fecc9a8f84ebcbcc', 'etag': '6hyM9uvUjGX/25N0U4zXzbQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 13:31:33,133 - INFO - 批量插入响应体: {'result': ['FINST-UW966371XOOW4W5RFW59297R7O20209KVNICMEM']}
2025-06-30 13:31:33,133 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-30 13:31:33,133 - INFO - 成功插入的数据ID: ['FINST-UW966371XOOW4W5RFW59297R7O20209KVNICMEM']
2025-06-30 13:31:38,148 - INFO - 批量插入完成，共 1 条记录
2025-06-30 13:31:38,148 - INFO - 日期 2025-06-27 处理完成 - 更新: 1 条，插入: 1 条，错误: 0 条
2025-06-30 13:31:38,148 - INFO - 开始处理日期: 2025-06-28
2025-06-30 13:31:38,148 - INFO - Request Parameters - Page 1:
2025-06-30 13:31:38,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:38,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:38,820 - INFO - Response - Page 1:
2025-06-30 13:31:38,820 - INFO - 第 1 页获取到 50 条记录
2025-06-30 13:31:39,320 - INFO - Request Parameters - Page 2:
2025-06-30 13:31:39,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:39,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:40,023 - INFO - Response - Page 2:
2025-06-30 13:31:40,023 - INFO - 第 2 页获取到 50 条记录
2025-06-30 13:31:40,539 - INFO - Request Parameters - Page 3:
2025-06-30 13:31:40,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:40,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:41,289 - INFO - Response - Page 3:
2025-06-30 13:31:41,289 - INFO - 第 3 页获取到 50 条记录
2025-06-30 13:31:41,804 - INFO - Request Parameters - Page 4:
2025-06-30 13:31:41,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:41,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:42,523 - INFO - Response - Page 4:
2025-06-30 13:31:42,539 - INFO - 第 4 页获取到 50 条记录
2025-06-30 13:31:43,054 - INFO - Request Parameters - Page 5:
2025-06-30 13:31:43,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:43,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:43,726 - INFO - Response - Page 5:
2025-06-30 13:31:43,726 - INFO - 第 5 页获取到 50 条记录
2025-06-30 13:31:44,242 - INFO - Request Parameters - Page 6:
2025-06-30 13:31:44,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:44,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:44,945 - INFO - Response - Page 6:
2025-06-30 13:31:44,945 - INFO - 第 6 页获取到 50 条记录
2025-06-30 13:31:45,445 - INFO - Request Parameters - Page 7:
2025-06-30 13:31:45,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:45,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:46,086 - INFO - Response - Page 7:
2025-06-30 13:31:46,086 - INFO - 第 7 页获取到 50 条记录
2025-06-30 13:31:46,601 - INFO - Request Parameters - Page 8:
2025-06-30 13:31:46,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:46,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:47,304 - INFO - Response - Page 8:
2025-06-30 13:31:47,304 - INFO - 第 8 页获取到 50 条记录
2025-06-30 13:31:47,820 - INFO - Request Parameters - Page 9:
2025-06-30 13:31:47,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:47,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:48,507 - INFO - Response - Page 9:
2025-06-30 13:31:48,507 - INFO - 第 9 页获取到 50 条记录
2025-06-30 13:31:49,023 - INFO - Request Parameters - Page 10:
2025-06-30 13:31:49,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:49,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:49,632 - INFO - Response - Page 10:
2025-06-30 13:31:49,632 - INFO - 第 10 页获取到 28 条记录
2025-06-30 13:31:50,148 - INFO - 查询完成，共获取到 478 条记录
2025-06-30 13:31:50,148 - INFO - 获取到 478 条表单数据
2025-06-30 13:31:50,148 - INFO - 当前日期 2025-06-28 有 2 条MySQL数据需要处理
2025-06-30 13:31:50,148 - INFO - 开始批量插入 1 条新记录
2025-06-30 13:31:50,320 - INFO - 批量插入响应状态码: 200
2025-06-30 13:31:50,320 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 05:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B73F0931-0211-7D2E-81C8-DF6CF93D353C', 'x-acs-trace-id': '3a0b9861b68d2432938325381dc85f89', 'etag': '6x204usefJjkjWPNIfPaBNA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 13:31:50,320 - INFO - 批量插入响应体: {'result': ['FINST-QUA66S713ROWHWAQ7LI3SBGXH2M12CIXVNICM1T']}
2025-06-30 13:31:50,320 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-30 13:31:50,320 - INFO - 成功插入的数据ID: ['FINST-QUA66S713ROWHWAQ7LI3SBGXH2M12CIXVNICM1T']
2025-06-30 13:31:55,336 - INFO - 批量插入完成，共 1 条记录
2025-06-30 13:31:55,336 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-30 13:31:55,336 - INFO - 开始处理日期: 2025-06-29
2025-06-30 13:31:55,336 - INFO - Request Parameters - Page 1:
2025-06-30 13:31:55,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:55,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:55,992 - INFO - Response - Page 1:
2025-06-30 13:31:55,992 - INFO - 第 1 页获取到 50 条记录
2025-06-30 13:31:56,507 - INFO - Request Parameters - Page 2:
2025-06-30 13:31:56,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:56,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:57,211 - INFO - Response - Page 2:
2025-06-30 13:31:57,211 - INFO - 第 2 页获取到 50 条记录
2025-06-30 13:31:57,711 - INFO - Request Parameters - Page 3:
2025-06-30 13:31:57,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:57,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:58,304 - INFO - Response - Page 3:
2025-06-30 13:31:58,304 - INFO - 第 3 页获取到 50 条记录
2025-06-30 13:31:58,804 - INFO - Request Parameters - Page 4:
2025-06-30 13:31:58,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:58,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:31:59,445 - INFO - Response - Page 4:
2025-06-30 13:31:59,445 - INFO - 第 4 页获取到 50 条记录
2025-06-30 13:31:59,961 - INFO - Request Parameters - Page 5:
2025-06-30 13:31:59,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:31:59,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:32:00,601 - INFO - Response - Page 5:
2025-06-30 13:32:00,601 - INFO - 第 5 页获取到 50 条记录
2025-06-30 13:32:01,101 - INFO - Request Parameters - Page 6:
2025-06-30 13:32:01,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:32:01,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:32:01,773 - INFO - Response - Page 6:
2025-06-30 13:32:01,773 - INFO - 第 6 页获取到 50 条记录
2025-06-30 13:32:02,273 - INFO - Request Parameters - Page 7:
2025-06-30 13:32:02,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:32:02,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:32:02,898 - INFO - Response - Page 7:
2025-06-30 13:32:02,898 - INFO - 第 7 页获取到 50 条记录
2025-06-30 13:32:03,398 - INFO - Request Parameters - Page 8:
2025-06-30 13:32:03,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:32:03,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:32:04,054 - INFO - Response - Page 8:
2025-06-30 13:32:04,054 - INFO - 第 8 页获取到 50 条记录
2025-06-30 13:32:04,570 - INFO - Request Parameters - Page 9:
2025-06-30 13:32:04,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:32:04,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:32:05,242 - INFO - Response - Page 9:
2025-06-30 13:32:05,242 - INFO - 第 9 页获取到 49 条记录
2025-06-30 13:32:05,742 - INFO - 查询完成，共获取到 449 条记录
2025-06-30 13:32:05,742 - INFO - 获取到 449 条表单数据
2025-06-30 13:32:05,742 - INFO - 当前日期 2025-06-29 有 133 条MySQL数据需要处理
2025-06-30 13:32:05,742 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMJ8
2025-06-30 13:32:06,289 - INFO - 更新表单数据成功: FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMJ8
2025-06-30 13:32:06,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1917.0, 'new_value': 21383.02}, {'field': 'total_amount', 'old_value': 2383.02, 'new_value': 21849.04}]
2025-06-30 13:32:06,289 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMF8
2025-06-30 13:32:06,851 - INFO - 更新表单数据成功: FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMF8
2025-06-30 13:32:06,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6900.0, 'new_value': 16900.0}, {'field': 'total_amount', 'old_value': 6900.0, 'new_value': 16900.0}]
2025-06-30 13:32:06,851 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMI8
2025-06-30 13:32:07,414 - INFO - 更新表单数据成功: FINST-7PF66CC150QW227G8EPV7D0FC8Z83WSPFHICMI8
2025-06-30 13:32:07,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5000.0, 'new_value': 35000.0}, {'field': 'total_amount', 'old_value': 5000.0, 'new_value': 35000.0}]
2025-06-30 13:32:07,414 - INFO - 开始批量插入 8 条新记录
2025-06-30 13:32:07,585 - INFO - 批量插入响应状态码: 200
2025-06-30 13:32:07,585 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 05:32:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '396', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1D154020-7433-7094-A821-7F397340B56D', 'x-acs-trace-id': '30da8e2ededae4ed6b5de59ae5be983b', 'etag': '3Jf4V+KpzwJVMSbZnLykBRg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 13:32:07,585 - INFO - 批量插入响应体: {'result': ['FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMTK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMUK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMVK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMWK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMXK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMYK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMZK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICM0L']}
2025-06-30 13:32:07,585 - INFO - 批量插入表单数据成功，批次 1，共 8 条记录
2025-06-30 13:32:07,585 - INFO - 成功插入的数据ID: ['FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMTK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMUK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMVK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMWK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMXK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMYK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICMZK', 'FINST-TQB666715WPWF8947EB16A25TACF32UAWNICM0L']
2025-06-30 13:32:12,601 - INFO - 批量插入完成，共 8 条记录
2025-06-30 13:32:12,601 - INFO - 日期 2025-06-29 处理完成 - 更新: 3 条，插入: 8 条，错误: 0 条
2025-06-30 13:32:12,601 - INFO - 开始处理日期: 2025-06-30
2025-06-30 13:32:12,601 - INFO - Request Parameters - Page 1:
2025-06-30 13:32:12,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:32:12,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:32:13,039 - INFO - Response - Page 1:
2025-06-30 13:32:13,039 - INFO - 第 1 页获取到 1 条记录
2025-06-30 13:32:13,539 - INFO - 查询完成，共获取到 1 条记录
2025-06-30 13:32:13,539 - INFO - 获取到 1 条表单数据
2025-06-30 13:32:13,539 - INFO - 当前日期 2025-06-30 有 1 条MySQL数据需要处理
2025-06-30 13:32:13,539 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 13:32:13,539 - INFO - 数据同步完成！更新: 5 条，插入: 11 条，错误: 1 条
2025-06-30 13:33:13,554 - INFO - 开始同步昨天与今天的销售数据: 2025-06-29 至 2025-06-30
2025-06-30 13:33:13,554 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-30 13:33:13,554 - INFO - 查询参数: ('2025-06-29', '2025-06-30')
2025-06-30 13:33:13,694 - INFO - MySQL查询成功，时间段: 2025-06-29 至 2025-06-30，共获取 494 条记录
2025-06-30 13:33:13,694 - INFO - 获取到 2 个日期需要处理: ['2025-06-29', '2025-06-30']
2025-06-30 13:33:13,694 - INFO - 开始处理日期: 2025-06-29
2025-06-30 13:33:13,694 - INFO - Request Parameters - Page 1:
2025-06-30 13:33:13,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:33:13,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:33:14,429 - INFO - Response - Page 1:
2025-06-30 13:33:14,429 - INFO - 第 1 页获取到 50 条记录
2025-06-30 13:33:14,944 - INFO - Request Parameters - Page 2:
2025-06-30 13:33:14,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:33:14,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:33:15,616 - INFO - Response - Page 2:
2025-06-30 13:33:15,616 - INFO - 第 2 页获取到 50 条记录
2025-06-30 13:33:16,132 - INFO - Request Parameters - Page 3:
2025-06-30 13:33:16,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:33:16,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:33:16,788 - INFO - Response - Page 3:
2025-06-30 13:33:16,788 - INFO - 第 3 页获取到 50 条记录
2025-06-30 13:33:17,304 - INFO - Request Parameters - Page 4:
2025-06-30 13:33:17,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:33:17,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:33:18,069 - INFO - Response - Page 4:
2025-06-30 13:33:18,069 - INFO - 第 4 页获取到 50 条记录
2025-06-30 13:33:18,585 - INFO - Request Parameters - Page 5:
2025-06-30 13:33:18,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:33:18,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:33:19,194 - INFO - Response - Page 5:
2025-06-30 13:33:19,194 - INFO - 第 5 页获取到 50 条记录
2025-06-30 13:33:19,694 - INFO - Request Parameters - Page 6:
2025-06-30 13:33:19,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:33:19,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:33:20,429 - INFO - Response - Page 6:
2025-06-30 13:33:20,429 - INFO - 第 6 页获取到 50 条记录
2025-06-30 13:33:20,929 - INFO - Request Parameters - Page 7:
2025-06-30 13:33:20,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:33:20,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:33:21,616 - INFO - Response - Page 7:
2025-06-30 13:33:21,616 - INFO - 第 7 页获取到 50 条记录
2025-06-30 13:33:22,116 - INFO - Request Parameters - Page 8:
2025-06-30 13:33:22,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:33:22,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:33:22,788 - INFO - Response - Page 8:
2025-06-30 13:33:22,788 - INFO - 第 8 页获取到 50 条记录
2025-06-30 13:33:23,304 - INFO - Request Parameters - Page 9:
2025-06-30 13:33:23,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:33:23,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:33:23,976 - INFO - Response - Page 9:
2025-06-30 13:33:23,976 - INFO - 第 9 页获取到 50 条记录
2025-06-30 13:33:24,476 - INFO - Request Parameters - Page 10:
2025-06-30 13:33:24,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:33:24,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:33:24,976 - INFO - Response - Page 10:
2025-06-30 13:33:24,976 - INFO - 第 10 页获取到 7 条记录
2025-06-30 13:33:25,476 - INFO - 查询完成，共获取到 457 条记录
2025-06-30 13:33:25,476 - INFO - 获取到 457 条表单数据
2025-06-30 13:33:25,476 - INFO - 当前日期 2025-06-29 有 478 条MySQL数据需要处理
2025-06-30 13:33:25,491 - INFO - 开始批量插入 21 条新记录
2025-06-30 13:33:25,679 - INFO - 批量插入响应状态码: 200
2025-06-30 13:33:25,679 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 05:33:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1020', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '67D8A0BB-FF55-7499-87DD-6D2EB1AE05F3', 'x-acs-trace-id': '903fd1e5fd7c84a5e014c44fda65ff15', 'etag': '1U922oKG3Rttmf+9b7ukHfQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 13:33:25,679 - INFO - 批量插入响应体: {'result': ['FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM1U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM2U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM3U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM4U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM5U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM6U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM7U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICM8U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICM9U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMAU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMBU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMCU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMDU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMEU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMFU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMGU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMHU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMIU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMJU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMKU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMLU']}
2025-06-30 13:33:25,679 - INFO - 批量插入表单数据成功，批次 1，共 21 条记录
2025-06-30 13:33:25,679 - INFO - 成功插入的数据ID: ['FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM1U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM2U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM3U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM4U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM5U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM6U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3H3ZXNICM7U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICM8U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICM9U', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMAU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMBU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMCU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMDU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMEU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMFU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMGU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMHU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMIU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMJU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMKU', 'FINST-DO566BD1O6OW61577KL1PAXCF48C3I3ZXNICMLU']
2025-06-30 13:33:30,694 - INFO - 批量插入完成，共 21 条记录
2025-06-30 13:33:30,694 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 21 条，错误: 0 条
2025-06-30 13:33:30,694 - INFO - 开始处理日期: 2025-06-30
2025-06-30 13:33:30,694 - INFO - Request Parameters - Page 1:
2025-06-30 13:33:30,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 13:33:30,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 13:33:31,147 - INFO - Response - Page 1:
2025-06-30 13:33:31,147 - INFO - 第 1 页获取到 1 条记录
2025-06-30 13:33:31,663 - INFO - 查询完成，共获取到 1 条记录
2025-06-30 13:33:31,663 - INFO - 获取到 1 条表单数据
2025-06-30 13:33:31,663 - INFO - 当前日期 2025-06-30 有 1 条MySQL数据需要处理
2025-06-30 13:33:31,663 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 13:33:31,663 - INFO - 数据同步完成！更新: 0 条，插入: 21 条，错误: 0 条
2025-06-30 13:33:31,663 - INFO - 同步完成
2025-06-30 16:30:34,071 - INFO - 使用默认增量同步（当天更新数据）
2025-06-30 16:30:34,071 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-30 16:30:34,071 - INFO - 查询参数: ('2025-06-30',)
2025-06-30 16:30:34,227 - INFO - MySQL查询成功，增量数据（日期: 2025-06-30），共获取 164 条记录
2025-06-30 16:30:34,227 - INFO - 获取到 7 个日期需要处理: ['2025-06-18', '2025-06-21', '2025-06-25', '2025-06-27', '2025-06-28', '2025-06-29', '2025-06-30']
2025-06-30 16:30:34,227 - INFO - 开始处理日期: 2025-06-18
2025-06-30 16:30:34,227 - INFO - Request Parameters - Page 1:
2025-06-30 16:30:34,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:30:34,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:30:40,617 - INFO - Response - Page 1:
2025-06-30 16:30:40,617 - INFO - 第 1 页获取到 50 条记录
2025-06-30 16:30:41,117 - INFO - Request Parameters - Page 2:
2025-06-30 16:30:41,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:30:41,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:30:49,242 - ERROR - 处理日期 2025-06-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AA785034-99A6-7671-AF9D-A11B605EFCDA Response: {'code': 'ServiceUnavailable', 'requestid': 'AA785034-99A6-7671-AF9D-A11B605EFCDA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AA785034-99A6-7671-AF9D-A11B605EFCDA)
2025-06-30 16:30:49,242 - INFO - 开始处理日期: 2025-06-21
2025-06-30 16:30:49,242 - INFO - Request Parameters - Page 1:
2025-06-30 16:30:49,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:30:49,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:30:51,289 - INFO - Response - Page 1:
2025-06-30 16:30:51,289 - INFO - 第 1 页获取到 50 条记录
2025-06-30 16:30:51,805 - INFO - Request Parameters - Page 2:
2025-06-30 16:30:51,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:30:51,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:30:52,508 - INFO - Response - Page 2:
2025-06-30 16:30:52,508 - INFO - 第 2 页获取到 50 条记录
2025-06-30 16:30:53,024 - INFO - Request Parameters - Page 3:
2025-06-30 16:30:53,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:30:53,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:30:53,680 - INFO - Response - Page 3:
2025-06-30 16:30:53,680 - INFO - 第 3 页获取到 50 条记录
2025-06-30 16:30:54,195 - INFO - Request Parameters - Page 4:
2025-06-30 16:30:54,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:30:54,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:30:54,852 - INFO - Response - Page 4:
2025-06-30 16:30:54,852 - INFO - 第 4 页获取到 50 条记录
2025-06-30 16:30:55,352 - INFO - Request Parameters - Page 5:
2025-06-30 16:30:55,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:30:55,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:30:56,055 - INFO - Response - Page 5:
2025-06-30 16:30:56,055 - INFO - 第 5 页获取到 50 条记录
2025-06-30 16:30:56,570 - INFO - Request Parameters - Page 6:
2025-06-30 16:30:56,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:30:56,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:30:57,258 - INFO - Response - Page 6:
2025-06-30 16:30:57,258 - INFO - 第 6 页获取到 50 条记录
2025-06-30 16:30:57,774 - INFO - Request Parameters - Page 7:
2025-06-30 16:30:57,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:30:57,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:30:58,445 - INFO - Response - Page 7:
2025-06-30 16:30:58,445 - INFO - 第 7 页获取到 50 条记录
2025-06-30 16:30:58,945 - INFO - Request Parameters - Page 8:
2025-06-30 16:30:58,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:30:58,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:30:59,602 - INFO - Response - Page 8:
2025-06-30 16:30:59,602 - INFO - 第 8 页获取到 50 条记录
2025-06-30 16:31:00,102 - INFO - Request Parameters - Page 9:
2025-06-30 16:31:00,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:00,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:00,852 - INFO - Response - Page 9:
2025-06-30 16:31:00,852 - INFO - 第 9 页获取到 50 条记录
2025-06-30 16:31:01,367 - INFO - Request Parameters - Page 10:
2025-06-30 16:31:01,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:01,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:02,055 - INFO - Response - Page 10:
2025-06-30 16:31:02,055 - INFO - 第 10 页获取到 50 条记录
2025-06-30 16:31:02,555 - INFO - Request Parameters - Page 11:
2025-06-30 16:31:02,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:02,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:03,195 - INFO - Response - Page 11:
2025-06-30 16:31:03,195 - INFO - 第 11 页获取到 20 条记录
2025-06-30 16:31:03,695 - INFO - 查询完成，共获取到 520 条记录
2025-06-30 16:31:03,695 - INFO - 获取到 520 条表单数据
2025-06-30 16:31:03,695 - INFO - 当前日期 2025-06-21 有 1 条MySQL数据需要处理
2025-06-30 16:31:03,695 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 16:31:03,695 - INFO - 开始处理日期: 2025-06-25
2025-06-30 16:31:03,695 - INFO - Request Parameters - Page 1:
2025-06-30 16:31:03,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:03,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:04,477 - INFO - Response - Page 1:
2025-06-30 16:31:04,477 - INFO - 第 1 页获取到 50 条记录
2025-06-30 16:31:04,977 - INFO - Request Parameters - Page 2:
2025-06-30 16:31:04,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:04,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:05,664 - INFO - Response - Page 2:
2025-06-30 16:31:05,664 - INFO - 第 2 页获取到 50 条记录
2025-06-30 16:31:06,180 - INFO - Request Parameters - Page 3:
2025-06-30 16:31:06,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:06,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:06,852 - INFO - Response - Page 3:
2025-06-30 16:31:06,852 - INFO - 第 3 页获取到 50 条记录
2025-06-30 16:31:07,352 - INFO - Request Parameters - Page 4:
2025-06-30 16:31:07,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:07,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:08,008 - INFO - Response - Page 4:
2025-06-30 16:31:08,008 - INFO - 第 4 页获取到 50 条记录
2025-06-30 16:31:08,523 - INFO - Request Parameters - Page 5:
2025-06-30 16:31:08,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:08,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:09,148 - INFO - Response - Page 5:
2025-06-30 16:31:09,148 - INFO - 第 5 页获取到 50 条记录
2025-06-30 16:31:09,648 - INFO - Request Parameters - Page 6:
2025-06-30 16:31:09,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:09,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:10,430 - INFO - Response - Page 6:
2025-06-30 16:31:10,430 - INFO - 第 6 页获取到 50 条记录
2025-06-30 16:31:10,930 - INFO - Request Parameters - Page 7:
2025-06-30 16:31:10,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:10,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:11,570 - INFO - Response - Page 7:
2025-06-30 16:31:11,570 - INFO - 第 7 页获取到 50 条记录
2025-06-30 16:31:12,086 - INFO - Request Parameters - Page 8:
2025-06-30 16:31:12,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:12,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:12,773 - INFO - Response - Page 8:
2025-06-30 16:31:12,773 - INFO - 第 8 页获取到 50 条记录
2025-06-30 16:31:13,273 - INFO - Request Parameters - Page 9:
2025-06-30 16:31:13,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:13,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:13,930 - INFO - Response - Page 9:
2025-06-30 16:31:13,930 - INFO - 第 9 页获取到 50 条记录
2025-06-30 16:31:14,445 - INFO - Request Parameters - Page 10:
2025-06-30 16:31:14,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:14,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:15,102 - INFO - Response - Page 10:
2025-06-30 16:31:15,102 - INFO - 第 10 页获取到 50 条记录
2025-06-30 16:31:15,602 - INFO - Request Parameters - Page 11:
2025-06-30 16:31:15,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:15,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:16,008 - INFO - Response - Page 11:
2025-06-30 16:31:16,008 - INFO - 查询完成，共获取到 500 条记录
2025-06-30 16:31:16,008 - INFO - 获取到 500 条表单数据
2025-06-30 16:31:16,023 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-30 16:31:16,023 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 16:31:16,023 - INFO - 开始处理日期: 2025-06-27
2025-06-30 16:31:16,023 - INFO - Request Parameters - Page 1:
2025-06-30 16:31:16,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:16,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:16,680 - INFO - Response - Page 1:
2025-06-30 16:31:16,680 - INFO - 第 1 页获取到 50 条记录
2025-06-30 16:31:17,195 - INFO - Request Parameters - Page 2:
2025-06-30 16:31:17,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:17,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:17,867 - INFO - Response - Page 2:
2025-06-30 16:31:17,867 - INFO - 第 2 页获取到 50 条记录
2025-06-30 16:31:18,367 - INFO - Request Parameters - Page 3:
2025-06-30 16:31:18,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:18,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:19,023 - INFO - Response - Page 3:
2025-06-30 16:31:19,023 - INFO - 第 3 页获取到 50 条记录
2025-06-30 16:31:19,523 - INFO - Request Parameters - Page 4:
2025-06-30 16:31:19,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:19,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:20,195 - INFO - Response - Page 4:
2025-06-30 16:31:20,195 - INFO - 第 4 页获取到 50 条记录
2025-06-30 16:31:20,711 - INFO - Request Parameters - Page 5:
2025-06-30 16:31:20,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:20,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:21,367 - INFO - Response - Page 5:
2025-06-30 16:31:21,367 - INFO - 第 5 页获取到 50 条记录
2025-06-30 16:31:21,883 - INFO - Request Parameters - Page 6:
2025-06-30 16:31:21,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:21,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:22,523 - INFO - Response - Page 6:
2025-06-30 16:31:22,523 - INFO - 第 6 页获取到 50 条记录
2025-06-30 16:31:23,039 - INFO - Request Parameters - Page 7:
2025-06-30 16:31:23,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:23,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:23,851 - INFO - Response - Page 7:
2025-06-30 16:31:23,851 - INFO - 第 7 页获取到 50 条记录
2025-06-30 16:31:24,351 - INFO - Request Parameters - Page 8:
2025-06-30 16:31:24,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:24,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:25,023 - INFO - Response - Page 8:
2025-06-30 16:31:25,023 - INFO - 第 8 页获取到 50 条记录
2025-06-30 16:31:25,539 - INFO - Request Parameters - Page 9:
2025-06-30 16:31:25,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:25,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:26,195 - INFO - Response - Page 9:
2025-06-30 16:31:26,195 - INFO - 第 9 页获取到 50 条记录
2025-06-30 16:31:26,711 - INFO - Request Parameters - Page 10:
2025-06-30 16:31:26,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:26,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:27,258 - INFO - Response - Page 10:
2025-06-30 16:31:27,258 - INFO - 第 10 页获取到 27 条记录
2025-06-30 16:31:27,773 - INFO - 查询完成，共获取到 477 条记录
2025-06-30 16:31:27,773 - INFO - 获取到 477 条表单数据
2025-06-30 16:31:27,773 - INFO - 当前日期 2025-06-27 有 2 条MySQL数据需要处理
2025-06-30 16:31:27,773 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 16:31:27,773 - INFO - 开始处理日期: 2025-06-28
2025-06-30 16:31:27,773 - INFO - Request Parameters - Page 1:
2025-06-30 16:31:27,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:27,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:28,445 - INFO - Response - Page 1:
2025-06-30 16:31:28,445 - INFO - 第 1 页获取到 50 条记录
2025-06-30 16:31:28,945 - INFO - Request Parameters - Page 2:
2025-06-30 16:31:28,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:28,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:29,586 - INFO - Response - Page 2:
2025-06-30 16:31:29,586 - INFO - 第 2 页获取到 50 条记录
2025-06-30 16:31:30,086 - INFO - Request Parameters - Page 3:
2025-06-30 16:31:30,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:30,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:30,726 - INFO - Response - Page 3:
2025-06-30 16:31:30,726 - INFO - 第 3 页获取到 50 条记录
2025-06-30 16:31:31,242 - INFO - Request Parameters - Page 4:
2025-06-30 16:31:31,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:31,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:31,992 - INFO - Response - Page 4:
2025-06-30 16:31:31,992 - INFO - 第 4 页获取到 50 条记录
2025-06-30 16:31:32,492 - INFO - Request Parameters - Page 5:
2025-06-30 16:31:32,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:32,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:33,148 - INFO - Response - Page 5:
2025-06-30 16:31:33,148 - INFO - 第 5 页获取到 50 条记录
2025-06-30 16:31:33,664 - INFO - Request Parameters - Page 6:
2025-06-30 16:31:33,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:33,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:34,383 - INFO - Response - Page 6:
2025-06-30 16:31:34,383 - INFO - 第 6 页获取到 50 条记录
2025-06-30 16:31:34,898 - INFO - Request Parameters - Page 7:
2025-06-30 16:31:34,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:34,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:35,554 - INFO - Response - Page 7:
2025-06-30 16:31:35,554 - INFO - 第 7 页获取到 50 条记录
2025-06-30 16:31:36,070 - INFO - Request Parameters - Page 8:
2025-06-30 16:31:36,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:36,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:36,726 - INFO - Response - Page 8:
2025-06-30 16:31:36,726 - INFO - 第 8 页获取到 50 条记录
2025-06-30 16:31:37,226 - INFO - Request Parameters - Page 9:
2025-06-30 16:31:37,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:37,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:37,867 - INFO - Response - Page 9:
2025-06-30 16:31:37,867 - INFO - 第 9 页获取到 50 条记录
2025-06-30 16:31:38,367 - INFO - Request Parameters - Page 10:
2025-06-30 16:31:38,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:38,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:38,930 - INFO - Response - Page 10:
2025-06-30 16:31:38,930 - INFO - 第 10 页获取到 29 条记录
2025-06-30 16:31:39,430 - INFO - 查询完成，共获取到 479 条记录
2025-06-30 16:31:39,430 - INFO - 获取到 479 条表单数据
2025-06-30 16:31:39,430 - INFO - 当前日期 2025-06-28 有 2 条MySQL数据需要处理
2025-06-30 16:31:39,430 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 16:31:39,430 - INFO - 开始处理日期: 2025-06-29
2025-06-30 16:31:39,430 - INFO - Request Parameters - Page 1:
2025-06-30 16:31:39,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:39,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:40,195 - INFO - Response - Page 1:
2025-06-30 16:31:40,195 - INFO - 第 1 页获取到 50 条记录
2025-06-30 16:31:40,711 - INFO - Request Parameters - Page 2:
2025-06-30 16:31:40,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:40,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:41,383 - INFO - Response - Page 2:
2025-06-30 16:31:41,383 - INFO - 第 2 页获取到 50 条记录
2025-06-30 16:31:41,883 - INFO - Request Parameters - Page 3:
2025-06-30 16:31:41,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:41,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:42,570 - INFO - Response - Page 3:
2025-06-30 16:31:42,570 - INFO - 第 3 页获取到 50 条记录
2025-06-30 16:31:43,086 - INFO - Request Parameters - Page 4:
2025-06-30 16:31:43,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:43,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:43,726 - INFO - Response - Page 4:
2025-06-30 16:31:43,726 - INFO - 第 4 页获取到 50 条记录
2025-06-30 16:31:44,226 - INFO - Request Parameters - Page 5:
2025-06-30 16:31:44,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:44,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:44,898 - INFO - Response - Page 5:
2025-06-30 16:31:44,898 - INFO - 第 5 页获取到 50 条记录
2025-06-30 16:31:45,414 - INFO - Request Parameters - Page 6:
2025-06-30 16:31:45,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:45,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:46,101 - INFO - Response - Page 6:
2025-06-30 16:31:46,101 - INFO - 第 6 页获取到 50 条记录
2025-06-30 16:31:46,601 - INFO - Request Parameters - Page 7:
2025-06-30 16:31:46,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:46,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:47,273 - INFO - Response - Page 7:
2025-06-30 16:31:47,273 - INFO - 第 7 页获取到 50 条记录
2025-06-30 16:31:47,789 - INFO - Request Parameters - Page 8:
2025-06-30 16:31:47,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:47,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:48,508 - INFO - Response - Page 8:
2025-06-30 16:31:48,508 - INFO - 第 8 页获取到 50 条记录
2025-06-30 16:31:49,008 - INFO - Request Parameters - Page 9:
2025-06-30 16:31:49,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:49,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:49,633 - INFO - Response - Page 9:
2025-06-30 16:31:49,633 - INFO - 第 9 页获取到 50 条记录
2025-06-30 16:31:50,148 - INFO - Request Parameters - Page 10:
2025-06-30 16:31:50,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:50,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:50,726 - INFO - Response - Page 10:
2025-06-30 16:31:50,726 - INFO - 第 10 页获取到 28 条记录
2025-06-30 16:31:51,226 - INFO - 查询完成，共获取到 478 条记录
2025-06-30 16:31:51,226 - INFO - 获取到 478 条表单数据
2025-06-30 16:31:51,226 - INFO - 当前日期 2025-06-29 有 152 条MySQL数据需要处理
2025-06-30 16:31:51,226 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMPC
2025-06-30 16:31:51,758 - INFO - 更新表单数据成功: FINST-T9D66B81K1QWPXBKCBOV19LSRAYI29UTFHICMPC
2025-06-30 16:31:51,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4107.66}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4107.66}, {'field': 'order_count', 'old_value': 0, 'new_value': 5}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/e34669586c2b4f1ea1a853b0c9e5f83e.jpg?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=hp%2B%2FgdPAs1iJsCKuhPAQfbbYV2E%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/66faea89684b495097eff504b23047ef.png?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=vr0v2CFi1OMIRvp19Z%2BpglUGCKE%3D'}]
2025-06-30 16:31:51,758 - INFO - 开始批量插入 19 条新记录
2025-06-30 16:31:51,961 - INFO - 批量插入响应状态码: 200
2025-06-30 16:31:51,961 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 08:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '924', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A4F9B50E-354D-7F55-AF28-8DEDC2C58AA3', 'x-acs-trace-id': 'b065505cc51614ec10fa23ff43e10ceb', 'etag': '90+ZhYDn5KIA+FFRDh45XIA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 16:31:51,961 - INFO - 批量插入响应体: {'result': ['FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMBW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMCW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMDW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMEW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMFW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMGW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMHW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMIW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMJW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMKW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMLW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMMW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMNW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMOW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMPW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMQW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMRW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMSW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMTW']}
2025-06-30 16:31:51,961 - INFO - 批量插入表单数据成功，批次 1，共 19 条记录
2025-06-30 16:31:51,961 - INFO - 成功插入的数据ID: ['FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMBW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMCW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMDW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMEW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMFW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMGW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMHW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMIW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMJW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMKW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMLW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMMW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMNW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMOW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMPW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMQW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMRW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMSW', 'FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMTW']
2025-06-30 16:31:56,976 - INFO - 批量插入完成，共 19 条记录
2025-06-30 16:31:56,976 - INFO - 日期 2025-06-29 处理完成 - 更新: 1 条，插入: 19 条，错误: 0 条
2025-06-30 16:31:56,976 - INFO - 开始处理日期: 2025-06-30
2025-06-30 16:31:56,976 - INFO - Request Parameters - Page 1:
2025-06-30 16:31:56,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:31:56,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:31:57,398 - INFO - Response - Page 1:
2025-06-30 16:31:57,398 - INFO - 第 1 页获取到 1 条记录
2025-06-30 16:31:57,898 - INFO - 查询完成，共获取到 1 条记录
2025-06-30 16:31:57,898 - INFO - 获取到 1 条表单数据
2025-06-30 16:31:57,898 - INFO - 当前日期 2025-06-30 有 1 条MySQL数据需要处理
2025-06-30 16:31:57,898 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 16:31:57,898 - INFO - 数据同步完成！更新: 1 条，插入: 19 条，错误: 1 条
2025-06-30 16:32:57,913 - INFO - 开始同步昨天与今天的销售数据: 2025-06-29 至 2025-06-30
2025-06-30 16:32:57,913 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-30 16:32:57,913 - INFO - 查询参数: ('2025-06-29', '2025-06-30')
2025-06-30 16:32:58,054 - INFO - MySQL查询成功，时间段: 2025-06-29 至 2025-06-30，共获取 561 条记录
2025-06-30 16:32:58,054 - INFO - 获取到 2 个日期需要处理: ['2025-06-29', '2025-06-30']
2025-06-30 16:32:58,070 - INFO - 开始处理日期: 2025-06-29
2025-06-30 16:32:58,070 - INFO - Request Parameters - Page 1:
2025-06-30 16:32:58,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:32:58,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:32:58,710 - INFO - Response - Page 1:
2025-06-30 16:32:58,710 - INFO - 第 1 页获取到 50 条记录
2025-06-30 16:32:59,226 - INFO - Request Parameters - Page 2:
2025-06-30 16:32:59,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:32:59,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:32:59,866 - INFO - Response - Page 2:
2025-06-30 16:32:59,866 - INFO - 第 2 页获取到 50 条记录
2025-06-30 16:33:00,366 - INFO - Request Parameters - Page 3:
2025-06-30 16:33:00,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:33:00,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:33:01,023 - INFO - Response - Page 3:
2025-06-30 16:33:01,023 - INFO - 第 3 页获取到 50 条记录
2025-06-30 16:33:01,523 - INFO - Request Parameters - Page 4:
2025-06-30 16:33:01,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:33:01,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:33:02,257 - INFO - Response - Page 4:
2025-06-30 16:33:02,257 - INFO - 第 4 页获取到 50 条记录
2025-06-30 16:33:02,773 - INFO - Request Parameters - Page 5:
2025-06-30 16:33:02,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:33:02,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:33:03,491 - INFO - Response - Page 5:
2025-06-30 16:33:03,491 - INFO - 第 5 页获取到 50 条记录
2025-06-30 16:33:04,007 - INFO - Request Parameters - Page 6:
2025-06-30 16:33:04,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:33:04,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:33:04,648 - INFO - Response - Page 6:
2025-06-30 16:33:04,648 - INFO - 第 6 页获取到 50 条记录
2025-06-30 16:33:05,163 - INFO - Request Parameters - Page 7:
2025-06-30 16:33:05,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:33:05,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:33:05,820 - INFO - Response - Page 7:
2025-06-30 16:33:05,820 - INFO - 第 7 页获取到 50 条记录
2025-06-30 16:33:06,335 - INFO - Request Parameters - Page 8:
2025-06-30 16:33:06,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:33:06,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:33:06,976 - INFO - Response - Page 8:
2025-06-30 16:33:06,976 - INFO - 第 8 页获取到 50 条记录
2025-06-30 16:33:07,491 - INFO - Request Parameters - Page 9:
2025-06-30 16:33:07,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:33:07,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:33:08,101 - INFO - Response - Page 9:
2025-06-30 16:33:08,101 - INFO - 第 9 页获取到 50 条记录
2025-06-30 16:33:08,601 - INFO - Request Parameters - Page 10:
2025-06-30 16:33:08,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:33:08,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:33:09,194 - INFO - Response - Page 10:
2025-06-30 16:33:09,194 - INFO - 第 10 页获取到 47 条记录
2025-06-30 16:33:09,695 - INFO - 查询完成，共获取到 497 条记录
2025-06-30 16:33:09,695 - INFO - 获取到 497 条表单数据
2025-06-30 16:33:09,695 - INFO - 当前日期 2025-06-29 有 539 条MySQL数据需要处理
2025-06-30 16:33:09,710 - INFO - 开始批量插入 42 条新记录
2025-06-30 16:33:09,960 - INFO - 批量插入响应状态码: 200
2025-06-30 16:33:09,960 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 08:33:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2028', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5E99E7BD-F975-7732-85AA-1D70AC990BDF', 'x-acs-trace-id': 'dc78f4ac4f0d0d6a8a6d85087f1909ef', 'etag': '2G2HlsjjZOI7ROYqriqZUuQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 16:33:09,960 - INFO - 批量插入响应体: {'result': ['FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM5B', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM6B', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM7B', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM8B', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM9B', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMAB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMBB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMCB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMDB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMEB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMFB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMGB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMHB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMIB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMJB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMKB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMLB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMMB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMNB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMOB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMPB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMQB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMRB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMSB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMTB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMUB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMVB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMWB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMXB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMYB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMZB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM0C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM1C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM2C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM3C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM4C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM5C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM6C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM7C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM8C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM9C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMAC']}
2025-06-30 16:33:09,960 - INFO - 批量插入表单数据成功，批次 1，共 42 条记录
2025-06-30 16:33:09,960 - INFO - 成功插入的数据ID: ['FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM5B', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM6B', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM7B', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM8B', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM9B', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMAB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMBB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMCB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMDB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMEB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMFB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMGB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMHB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMIB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMJB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMKB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMLB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMMB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMNB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMOB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMPB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMQB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMRB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMSB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMTB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMUB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMVB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMWB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMXB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMYB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMZB', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM0C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM1C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM2C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM3C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM4C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM5C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM6C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM7C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM8C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICM9C', 'FINST-DUF66091D3QWXPT99U7OH48PQCZX3OC4DUICMAC']
2025-06-30 16:33:14,976 - INFO - 批量插入完成，共 42 条记录
2025-06-30 16:33:14,976 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 42 条，错误: 0 条
2025-06-30 16:33:14,976 - INFO - 开始处理日期: 2025-06-30
2025-06-30 16:33:14,976 - INFO - Request Parameters - Page 1:
2025-06-30 16:33:14,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 16:33:14,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 16:33:15,460 - INFO - Response - Page 1:
2025-06-30 16:33:15,460 - INFO - 第 1 页获取到 1 条记录
2025-06-30 16:33:15,960 - INFO - 查询完成，共获取到 1 条记录
2025-06-30 16:33:15,960 - INFO - 获取到 1 条表单数据
2025-06-30 16:33:15,960 - INFO - 当前日期 2025-06-30 有 1 条MySQL数据需要处理
2025-06-30 16:33:15,960 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 16:33:15,960 - INFO - 数据同步完成！更新: 0 条，插入: 42 条，错误: 0 条
2025-06-30 16:33:15,960 - INFO - 同步完成
2025-06-30 19:30:34,640 - INFO - 使用默认增量同步（当天更新数据）
2025-06-30 19:30:34,640 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-30 19:30:34,655 - INFO - 查询参数: ('2025-06-30',)
2025-06-30 19:30:34,796 - INFO - MySQL查询成功，增量数据（日期: 2025-06-30），共获取 165 条记录
2025-06-30 19:30:34,796 - INFO - 获取到 7 个日期需要处理: ['2025-06-18', '2025-06-21', '2025-06-25', '2025-06-27', '2025-06-28', '2025-06-29', '2025-06-30']
2025-06-30 19:30:34,796 - INFO - 开始处理日期: 2025-06-18
2025-06-30 19:30:34,796 - INFO - Request Parameters - Page 1:
2025-06-30 19:30:34,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:30:34,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:30:41,064 - INFO - Response - Page 1:
2025-06-30 19:30:41,064 - INFO - 第 1 页获取到 50 条记录
2025-06-30 19:30:41,580 - INFO - Request Parameters - Page 2:
2025-06-30 19:30:41,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:30:41,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:30:49,693 - ERROR - 处理日期 2025-06-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D7774ABE-3981-704E-8F30-443E70759A37 Response: {'code': 'ServiceUnavailable', 'requestid': 'D7774ABE-3981-704E-8F30-443E70759A37', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D7774ABE-3981-704E-8F30-443E70759A37)
2025-06-30 19:30:49,693 - INFO - 开始处理日期: 2025-06-21
2025-06-30 19:30:49,693 - INFO - Request Parameters - Page 1:
2025-06-30 19:30:49,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:30:49,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:30:50,349 - INFO - Response - Page 1:
2025-06-30 19:30:50,349 - INFO - 第 1 页获取到 50 条记录
2025-06-30 19:30:50,865 - INFO - Request Parameters - Page 2:
2025-06-30 19:30:50,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:30:50,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:30:51,553 - INFO - Response - Page 2:
2025-06-30 19:30:51,553 - INFO - 第 2 页获取到 50 条记录
2025-06-30 19:30:52,053 - INFO - Request Parameters - Page 3:
2025-06-30 19:30:52,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:30:52,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:30:52,710 - INFO - Response - Page 3:
2025-06-30 19:30:52,710 - INFO - 第 3 页获取到 50 条记录
2025-06-30 19:30:53,225 - INFO - Request Parameters - Page 4:
2025-06-30 19:30:53,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:30:53,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:30:53,913 - INFO - Response - Page 4:
2025-06-30 19:30:53,913 - INFO - 第 4 页获取到 50 条记录
2025-06-30 19:30:54,429 - INFO - Request Parameters - Page 5:
2025-06-30 19:30:54,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:30:54,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:30:55,132 - INFO - Response - Page 5:
2025-06-30 19:30:55,132 - INFO - 第 5 页获取到 50 条记录
2025-06-30 19:30:55,648 - INFO - Request Parameters - Page 6:
2025-06-30 19:30:55,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:30:55,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:30:56,414 - INFO - Response - Page 6:
2025-06-30 19:30:56,414 - INFO - 第 6 页获取到 50 条记录
2025-06-30 19:30:56,930 - INFO - Request Parameters - Page 7:
2025-06-30 19:30:56,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:30:56,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:30:57,618 - INFO - Response - Page 7:
2025-06-30 19:30:57,618 - INFO - 第 7 页获取到 50 条记录
2025-06-30 19:30:58,134 - INFO - Request Parameters - Page 8:
2025-06-30 19:30:58,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:30:58,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:30:58,884 - INFO - Response - Page 8:
2025-06-30 19:30:58,884 - INFO - 第 8 页获取到 50 条记录
2025-06-30 19:30:59,384 - INFO - Request Parameters - Page 9:
2025-06-30 19:30:59,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:30:59,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:00,119 - INFO - Response - Page 9:
2025-06-30 19:31:00,119 - INFO - 第 9 页获取到 50 条记录
2025-06-30 19:31:00,619 - INFO - Request Parameters - Page 10:
2025-06-30 19:31:00,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:00,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:01,244 - INFO - Response - Page 10:
2025-06-30 19:31:01,244 - INFO - 第 10 页获取到 50 条记录
2025-06-30 19:31:01,744 - INFO - Request Parameters - Page 11:
2025-06-30 19:31:01,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:01,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:02,323 - INFO - Response - Page 11:
2025-06-30 19:31:02,323 - INFO - 第 11 页获取到 20 条记录
2025-06-30 19:31:02,823 - INFO - 查询完成，共获取到 520 条记录
2025-06-30 19:31:02,823 - INFO - 获取到 520 条表单数据
2025-06-30 19:31:02,823 - INFO - 当前日期 2025-06-21 有 1 条MySQL数据需要处理
2025-06-30 19:31:02,823 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 19:31:02,823 - INFO - 开始处理日期: 2025-06-25
2025-06-30 19:31:02,823 - INFO - Request Parameters - Page 1:
2025-06-30 19:31:02,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:02,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:03,573 - INFO - Response - Page 1:
2025-06-30 19:31:03,573 - INFO - 第 1 页获取到 50 条记录
2025-06-30 19:31:04,089 - INFO - Request Parameters - Page 2:
2025-06-30 19:31:04,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:04,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:04,761 - INFO - Response - Page 2:
2025-06-30 19:31:04,761 - INFO - 第 2 页获取到 50 条记录
2025-06-30 19:31:05,277 - INFO - Request Parameters - Page 3:
2025-06-30 19:31:05,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:05,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:05,980 - INFO - Response - Page 3:
2025-06-30 19:31:05,980 - INFO - 第 3 页获取到 50 条记录
2025-06-30 19:31:06,496 - INFO - Request Parameters - Page 4:
2025-06-30 19:31:06,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:06,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:07,200 - INFO - Response - Page 4:
2025-06-30 19:31:07,200 - INFO - 第 4 页获取到 50 条记录
2025-06-30 19:31:07,715 - INFO - Request Parameters - Page 5:
2025-06-30 19:31:07,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:07,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:08,372 - INFO - Response - Page 5:
2025-06-30 19:31:08,372 - INFO - 第 5 页获取到 50 条记录
2025-06-30 19:31:08,872 - INFO - Request Parameters - Page 6:
2025-06-30 19:31:08,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:08,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:09,513 - INFO - Response - Page 6:
2025-06-30 19:31:09,513 - INFO - 第 6 页获取到 50 条记录
2025-06-30 19:31:10,029 - INFO - Request Parameters - Page 7:
2025-06-30 19:31:10,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:10,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:10,701 - INFO - Response - Page 7:
2025-06-30 19:31:10,701 - INFO - 第 7 页获取到 50 条记录
2025-06-30 19:31:11,217 - INFO - Request Parameters - Page 8:
2025-06-30 19:31:11,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:11,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:11,920 - INFO - Response - Page 8:
2025-06-30 19:31:11,920 - INFO - 第 8 页获取到 50 条记录
2025-06-30 19:31:12,420 - INFO - Request Parameters - Page 9:
2025-06-30 19:31:12,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:12,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:13,093 - INFO - Response - Page 9:
2025-06-30 19:31:13,093 - INFO - 第 9 页获取到 50 条记录
2025-06-30 19:31:13,593 - INFO - Request Parameters - Page 10:
2025-06-30 19:31:13,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:13,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:14,234 - INFO - Response - Page 10:
2025-06-30 19:31:14,234 - INFO - 第 10 页获取到 50 条记录
2025-06-30 19:31:14,749 - INFO - Request Parameters - Page 11:
2025-06-30 19:31:14,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:14,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:15,156 - INFO - Response - Page 11:
2025-06-30 19:31:15,156 - INFO - 查询完成，共获取到 500 条记录
2025-06-30 19:31:15,156 - INFO - 获取到 500 条表单数据
2025-06-30 19:31:15,172 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-30 19:31:15,172 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 19:31:15,172 - INFO - 开始处理日期: 2025-06-27
2025-06-30 19:31:15,172 - INFO - Request Parameters - Page 1:
2025-06-30 19:31:15,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:15,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:15,844 - INFO - Response - Page 1:
2025-06-30 19:31:15,844 - INFO - 第 1 页获取到 50 条记录
2025-06-30 19:31:16,360 - INFO - Request Parameters - Page 2:
2025-06-30 19:31:16,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:16,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:17,016 - INFO - Response - Page 2:
2025-06-30 19:31:17,016 - INFO - 第 2 页获取到 50 条记录
2025-06-30 19:31:17,516 - INFO - Request Parameters - Page 3:
2025-06-30 19:31:17,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:17,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:18,173 - INFO - Response - Page 3:
2025-06-30 19:31:18,173 - INFO - 第 3 页获取到 50 条记录
2025-06-30 19:31:18,673 - INFO - Request Parameters - Page 4:
2025-06-30 19:31:18,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:18,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:19,345 - INFO - Response - Page 4:
2025-06-30 19:31:19,345 - INFO - 第 4 页获取到 50 条记录
2025-06-30 19:31:19,845 - INFO - Request Parameters - Page 5:
2025-06-30 19:31:19,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:19,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:20,486 - INFO - Response - Page 5:
2025-06-30 19:31:20,486 - INFO - 第 5 页获取到 50 条记录
2025-06-30 19:31:21,002 - INFO - Request Parameters - Page 6:
2025-06-30 19:31:21,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:21,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:21,612 - INFO - Response - Page 6:
2025-06-30 19:31:21,612 - INFO - 第 6 页获取到 50 条记录
2025-06-30 19:31:22,127 - INFO - Request Parameters - Page 7:
2025-06-30 19:31:22,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:22,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:22,846 - INFO - Response - Page 7:
2025-06-30 19:31:22,846 - INFO - 第 7 页获取到 50 条记录
2025-06-30 19:31:23,362 - INFO - Request Parameters - Page 8:
2025-06-30 19:31:23,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:23,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:24,034 - INFO - Response - Page 8:
2025-06-30 19:31:24,034 - INFO - 第 8 页获取到 50 条记录
2025-06-30 19:31:24,535 - INFO - Request Parameters - Page 9:
2025-06-30 19:31:24,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:24,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:25,191 - INFO - Response - Page 9:
2025-06-30 19:31:25,191 - INFO - 第 9 页获取到 50 条记录
2025-06-30 19:31:25,691 - INFO - Request Parameters - Page 10:
2025-06-30 19:31:25,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:25,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:26,254 - INFO - Response - Page 10:
2025-06-30 19:31:26,254 - INFO - 第 10 页获取到 27 条记录
2025-06-30 19:31:26,770 - INFO - 查询完成，共获取到 477 条记录
2025-06-30 19:31:26,770 - INFO - 获取到 477 条表单数据
2025-06-30 19:31:26,770 - INFO - 当前日期 2025-06-27 有 2 条MySQL数据需要处理
2025-06-30 19:31:26,770 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 19:31:26,770 - INFO - 开始处理日期: 2025-06-28
2025-06-30 19:31:26,770 - INFO - Request Parameters - Page 1:
2025-06-30 19:31:26,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:26,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:27,411 - INFO - Response - Page 1:
2025-06-30 19:31:27,411 - INFO - 第 1 页获取到 50 条记录
2025-06-30 19:31:27,927 - INFO - Request Parameters - Page 2:
2025-06-30 19:31:27,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:27,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:28,583 - INFO - Response - Page 2:
2025-06-30 19:31:28,583 - INFO - 第 2 页获取到 50 条记录
2025-06-30 19:31:29,083 - INFO - Request Parameters - Page 3:
2025-06-30 19:31:29,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:29,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:29,724 - INFO - Response - Page 3:
2025-06-30 19:31:29,724 - INFO - 第 3 页获取到 50 条记录
2025-06-30 19:31:30,240 - INFO - Request Parameters - Page 4:
2025-06-30 19:31:30,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:30,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:30,928 - INFO - Response - Page 4:
2025-06-30 19:31:30,928 - INFO - 第 4 页获取到 50 条记录
2025-06-30 19:31:31,428 - INFO - Request Parameters - Page 5:
2025-06-30 19:31:31,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:31,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:32,084 - INFO - Response - Page 5:
2025-06-30 19:31:32,084 - INFO - 第 5 页获取到 50 条记录
2025-06-30 19:31:32,585 - INFO - Request Parameters - Page 6:
2025-06-30 19:31:32,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:32,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:33,194 - INFO - Response - Page 6:
2025-06-30 19:31:33,194 - INFO - 第 6 页获取到 50 条记录
2025-06-30 19:31:33,695 - INFO - Request Parameters - Page 7:
2025-06-30 19:31:33,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:33,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:34,351 - INFO - Response - Page 7:
2025-06-30 19:31:34,351 - INFO - 第 7 页获取到 50 条记录
2025-06-30 19:31:34,851 - INFO - Request Parameters - Page 8:
2025-06-30 19:31:34,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:34,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:35,492 - INFO - Response - Page 8:
2025-06-30 19:31:35,492 - INFO - 第 8 页获取到 50 条记录
2025-06-30 19:31:36,008 - INFO - Request Parameters - Page 9:
2025-06-30 19:31:36,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:36,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:36,633 - INFO - Response - Page 9:
2025-06-30 19:31:36,633 - INFO - 第 9 页获取到 50 条记录
2025-06-30 19:31:37,149 - INFO - Request Parameters - Page 10:
2025-06-30 19:31:37,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:37,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:37,759 - INFO - Response - Page 10:
2025-06-30 19:31:37,759 - INFO - 第 10 页获取到 29 条记录
2025-06-30 19:31:38,259 - INFO - 查询完成，共获取到 479 条记录
2025-06-30 19:31:38,259 - INFO - 获取到 479 条表单数据
2025-06-30 19:31:38,259 - INFO - 当前日期 2025-06-28 有 2 条MySQL数据需要处理
2025-06-30 19:31:38,259 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 19:31:38,259 - INFO - 开始处理日期: 2025-06-29
2025-06-30 19:31:38,259 - INFO - Request Parameters - Page 1:
2025-06-30 19:31:38,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:38,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:38,947 - INFO - Response - Page 1:
2025-06-30 19:31:38,947 - INFO - 第 1 页获取到 50 条记录
2025-06-30 19:31:39,462 - INFO - Request Parameters - Page 2:
2025-06-30 19:31:39,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:39,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:40,135 - INFO - Response - Page 2:
2025-06-30 19:31:40,135 - INFO - 第 2 页获取到 50 条记录
2025-06-30 19:31:40,635 - INFO - Request Parameters - Page 3:
2025-06-30 19:31:40,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:40,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:41,291 - INFO - Response - Page 3:
2025-06-30 19:31:41,291 - INFO - 第 3 页获取到 50 条记录
2025-06-30 19:31:41,791 - INFO - Request Parameters - Page 4:
2025-06-30 19:31:41,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:41,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:42,464 - INFO - Response - Page 4:
2025-06-30 19:31:42,464 - INFO - 第 4 页获取到 50 条记录
2025-06-30 19:31:42,964 - INFO - Request Parameters - Page 5:
2025-06-30 19:31:42,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:42,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:43,620 - INFO - Response - Page 5:
2025-06-30 19:31:43,620 - INFO - 第 5 页获取到 50 条记录
2025-06-30 19:31:44,121 - INFO - Request Parameters - Page 6:
2025-06-30 19:31:44,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:44,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:44,777 - INFO - Response - Page 6:
2025-06-30 19:31:44,777 - INFO - 第 6 页获取到 50 条记录
2025-06-30 19:31:45,277 - INFO - Request Parameters - Page 7:
2025-06-30 19:31:45,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:45,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:45,949 - INFO - Response - Page 7:
2025-06-30 19:31:45,949 - INFO - 第 7 页获取到 50 条记录
2025-06-30 19:31:46,465 - INFO - Request Parameters - Page 8:
2025-06-30 19:31:46,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:46,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:47,106 - INFO - Response - Page 8:
2025-06-30 19:31:47,106 - INFO - 第 8 页获取到 50 条记录
2025-06-30 19:31:47,606 - INFO - Request Parameters - Page 9:
2025-06-30 19:31:47,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:47,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:48,357 - INFO - Response - Page 9:
2025-06-30 19:31:48,357 - INFO - 第 9 页获取到 50 条记录
2025-06-30 19:31:48,857 - INFO - Request Parameters - Page 10:
2025-06-30 19:31:48,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:48,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:49,529 - INFO - Response - Page 10:
2025-06-30 19:31:49,529 - INFO - 第 10 页获取到 50 条记录
2025-06-30 19:31:50,045 - INFO - Request Parameters - Page 11:
2025-06-30 19:31:50,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:50,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:50,686 - INFO - Response - Page 11:
2025-06-30 19:31:50,686 - INFO - 第 11 页获取到 39 条记录
2025-06-30 19:31:51,186 - INFO - 查询完成，共获取到 539 条记录
2025-06-30 19:31:51,186 - INFO - 获取到 539 条表单数据
2025-06-30 19:31:51,186 - INFO - 当前日期 2025-06-29 有 152 条MySQL数据需要处理
2025-06-30 19:31:51,186 - INFO - 开始更新记录 - 表单实例ID: FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMRW
2025-06-30 19:31:51,733 - INFO - 更新表单数据成功: FINST-W4G66DA1H5OWUCVYBUA3C6IKZ4KC3Y5GBUICMRW
2025-06-30 19:31:51,733 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6604.0, 'new_value': 17546.0}, {'field': 'total_amount', 'old_value': 6604.0, 'new_value': 17546.0}, {'field': 'order_count', 'old_value': 610, 'new_value': 15}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-30 19:31:51,733 - INFO - 日期 2025-06-29 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-30 19:31:51,733 - INFO - 开始处理日期: 2025-06-30
2025-06-30 19:31:51,733 - INFO - Request Parameters - Page 1:
2025-06-30 19:31:51,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:31:51,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:31:52,171 - INFO - Response - Page 1:
2025-06-30 19:31:52,171 - INFO - 第 1 页获取到 1 条记录
2025-06-30 19:31:52,686 - INFO - 查询完成，共获取到 1 条记录
2025-06-30 19:31:52,686 - INFO - 获取到 1 条表单数据
2025-06-30 19:31:52,686 - INFO - 当前日期 2025-06-30 有 2 条MySQL数据需要处理
2025-06-30 19:31:52,686 - INFO - 开始批量插入 1 条新记录
2025-06-30 19:31:52,843 - INFO - 批量插入响应状态码: 200
2025-06-30 19:31:52,843 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 11:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6D7660DE-C34B-7709-986A-7176C2C2C38F', 'x-acs-trace-id': 'a88a4971a652b8a48194bf03ebb43772', 'etag': '5cOCs0nCTKS/0qRhrRh4H+A9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 19:31:52,843 - INFO - 批量插入响应体: {'result': ['FINST-6I766IB1HTQWEBEMAIC4OA9VOMI23IYXQ0JCME']}
2025-06-30 19:31:52,843 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-30 19:31:52,843 - INFO - 成功插入的数据ID: ['FINST-6I766IB1HTQWEBEMAIC4OA9VOMI23IYXQ0JCME']
2025-06-30 19:31:57,860 - INFO - 批量插入完成，共 1 条记录
2025-06-30 19:31:57,860 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-30 19:31:57,860 - INFO - 数据同步完成！更新: 1 条，插入: 1 条，错误: 1 条
2025-06-30 19:32:57,900 - INFO - 开始同步昨天与今天的销售数据: 2025-06-29 至 2025-06-30
2025-06-30 19:32:57,900 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-30 19:32:57,900 - INFO - 查询参数: ('2025-06-29', '2025-06-30')
2025-06-30 19:32:58,040 - INFO - MySQL查询成功，时间段: 2025-06-29 至 2025-06-30，共获取 562 条记录
2025-06-30 19:32:58,040 - INFO - 获取到 2 个日期需要处理: ['2025-06-29', '2025-06-30']
2025-06-30 19:32:58,056 - INFO - 开始处理日期: 2025-06-29
2025-06-30 19:32:58,056 - INFO - Request Parameters - Page 1:
2025-06-30 19:32:58,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:32:58,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:32:58,775 - INFO - Response - Page 1:
2025-06-30 19:32:58,775 - INFO - 第 1 页获取到 50 条记录
2025-06-30 19:32:59,275 - INFO - Request Parameters - Page 2:
2025-06-30 19:32:59,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:32:59,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:32:59,947 - INFO - Response - Page 2:
2025-06-30 19:32:59,947 - INFO - 第 2 页获取到 50 条记录
2025-06-30 19:33:00,448 - INFO - Request Parameters - Page 3:
2025-06-30 19:33:00,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:33:00,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:33:01,135 - INFO - Response - Page 3:
2025-06-30 19:33:01,135 - INFO - 第 3 页获取到 50 条记录
2025-06-30 19:33:01,651 - INFO - Request Parameters - Page 4:
2025-06-30 19:33:01,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:33:01,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:33:02,433 - INFO - Response - Page 4:
2025-06-30 19:33:02,433 - INFO - 第 4 页获取到 50 条记录
2025-06-30 19:33:02,949 - INFO - Request Parameters - Page 5:
2025-06-30 19:33:02,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:33:02,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:33:03,605 - INFO - Response - Page 5:
2025-06-30 19:33:03,605 - INFO - 第 5 页获取到 50 条记录
2025-06-30 19:33:04,105 - INFO - Request Parameters - Page 6:
2025-06-30 19:33:04,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:33:04,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:33:04,809 - INFO - Response - Page 6:
2025-06-30 19:33:04,809 - INFO - 第 6 页获取到 50 条记录
2025-06-30 19:33:05,325 - INFO - Request Parameters - Page 7:
2025-06-30 19:33:05,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:33:05,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:33:05,997 - INFO - Response - Page 7:
2025-06-30 19:33:05,997 - INFO - 第 7 页获取到 50 条记录
2025-06-30 19:33:06,497 - INFO - Request Parameters - Page 8:
2025-06-30 19:33:06,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:33:06,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:33:07,169 - INFO - Response - Page 8:
2025-06-30 19:33:07,169 - INFO - 第 8 页获取到 50 条记录
2025-06-30 19:33:07,669 - INFO - Request Parameters - Page 9:
2025-06-30 19:33:07,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:33:07,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:33:08,357 - INFO - Response - Page 9:
2025-06-30 19:33:08,357 - INFO - 第 9 页获取到 50 条记录
2025-06-30 19:33:08,857 - INFO - Request Parameters - Page 10:
2025-06-30 19:33:08,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:33:08,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:33:09,483 - INFO - Response - Page 10:
2025-06-30 19:33:09,483 - INFO - 第 10 页获取到 50 条记录
2025-06-30 19:33:09,998 - INFO - Request Parameters - Page 11:
2025-06-30 19:33:09,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:33:09,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:33:10,624 - INFO - Response - Page 11:
2025-06-30 19:33:10,624 - INFO - 第 11 页获取到 39 条记录
2025-06-30 19:33:11,139 - INFO - 查询完成，共获取到 539 条记录
2025-06-30 19:33:11,139 - INFO - 获取到 539 条表单数据
2025-06-30 19:33:11,139 - INFO - 当前日期 2025-06-29 有 539 条MySQL数据需要处理
2025-06-30 19:33:11,155 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 19:33:11,155 - INFO - 开始处理日期: 2025-06-30
2025-06-30 19:33:11,155 - INFO - Request Parameters - Page 1:
2025-06-30 19:33:11,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 19:33:11,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 19:33:11,608 - INFO - Response - Page 1:
2025-06-30 19:33:11,608 - INFO - 第 1 页获取到 2 条记录
2025-06-30 19:33:12,124 - INFO - 查询完成，共获取到 2 条记录
2025-06-30 19:33:12,124 - INFO - 获取到 2 条表单数据
2025-06-30 19:33:12,124 - INFO - 当前日期 2025-06-30 有 2 条MySQL数据需要处理
2025-06-30 19:33:12,124 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 19:33:12,124 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 19:33:12,124 - INFO - 同步完成
2025-06-30 22:30:34,483 - INFO - 使用默认增量同步（当天更新数据）
2025-06-30 22:30:34,483 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-30 22:30:34,483 - INFO - 查询参数: ('2025-06-30',)
2025-06-30 22:30:34,624 - INFO - MySQL查询成功，增量数据（日期: 2025-06-30），共获取 170 条记录
2025-06-30 22:30:34,624 - INFO - 获取到 7 个日期需要处理: ['2025-06-18', '2025-06-21', '2025-06-25', '2025-06-27', '2025-06-28', '2025-06-29', '2025-06-30']
2025-06-30 22:30:34,624 - INFO - 开始处理日期: 2025-06-18
2025-06-30 22:30:34,624 - INFO - Request Parameters - Page 1:
2025-06-30 22:30:34,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:30:34,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:30:40,907 - INFO - Response - Page 1:
2025-06-30 22:30:40,907 - INFO - 第 1 页获取到 50 条记录
2025-06-30 22:30:41,408 - INFO - Request Parameters - Page 2:
2025-06-30 22:30:41,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:30:41,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:30:42,033 - INFO - Response - Page 2:
2025-06-30 22:30:42,033 - INFO - 第 2 页获取到 50 条记录
2025-06-30 22:30:42,533 - INFO - Request Parameters - Page 3:
2025-06-30 22:30:42,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:30:42,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:30:43,205 - INFO - Response - Page 3:
2025-06-30 22:30:43,205 - INFO - 第 3 页获取到 50 条记录
2025-06-30 22:30:43,721 - INFO - Request Parameters - Page 4:
2025-06-30 22:30:43,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:30:43,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:30:44,346 - INFO - Response - Page 4:
2025-06-30 22:30:44,346 - INFO - 第 4 页获取到 50 条记录
2025-06-30 22:30:44,847 - INFO - Request Parameters - Page 5:
2025-06-30 22:30:44,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:30:44,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:30:45,519 - INFO - Response - Page 5:
2025-06-30 22:30:45,519 - INFO - 第 5 页获取到 50 条记录
2025-06-30 22:30:46,035 - INFO - Request Parameters - Page 6:
2025-06-30 22:30:46,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:30:46,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:30:54,163 - ERROR - 处理日期 2025-06-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6EB523D8-6219-7101-AAEE-C56C107E8711 Response: {'code': 'ServiceUnavailable', 'requestid': '6EB523D8-6219-7101-AAEE-C56C107E8711', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6EB523D8-6219-7101-AAEE-C56C107E8711)
2025-06-30 22:30:54,163 - INFO - 开始处理日期: 2025-06-21
2025-06-30 22:30:54,163 - INFO - Request Parameters - Page 1:
2025-06-30 22:30:54,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:30:54,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:30:55,460 - INFO - Response - Page 1:
2025-06-30 22:30:55,460 - INFO - 第 1 页获取到 50 条记录
2025-06-30 22:30:55,976 - INFO - Request Parameters - Page 2:
2025-06-30 22:30:55,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:30:55,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:30:56,648 - INFO - Response - Page 2:
2025-06-30 22:30:56,648 - INFO - 第 2 页获取到 50 条记录
2025-06-30 22:30:57,164 - INFO - Request Parameters - Page 3:
2025-06-30 22:30:57,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:30:57,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:30:57,899 - INFO - Response - Page 3:
2025-06-30 22:30:57,899 - INFO - 第 3 页获取到 50 条记录
2025-06-30 22:30:58,414 - INFO - Request Parameters - Page 4:
2025-06-30 22:30:58,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:30:58,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:30:59,087 - INFO - Response - Page 4:
2025-06-30 22:30:59,087 - INFO - 第 4 页获取到 50 条记录
2025-06-30 22:30:59,602 - INFO - Request Parameters - Page 5:
2025-06-30 22:30:59,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:30:59,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:00,243 - INFO - Response - Page 5:
2025-06-30 22:31:00,243 - INFO - 第 5 页获取到 50 条记录
2025-06-30 22:31:00,743 - INFO - Request Parameters - Page 6:
2025-06-30 22:31:00,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:00,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:01,400 - INFO - Response - Page 6:
2025-06-30 22:31:01,400 - INFO - 第 6 页获取到 50 条记录
2025-06-30 22:31:01,900 - INFO - Request Parameters - Page 7:
2025-06-30 22:31:01,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:01,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:02,588 - INFO - Response - Page 7:
2025-06-30 22:31:02,588 - INFO - 第 7 页获取到 50 条记录
2025-06-30 22:31:03,104 - INFO - Request Parameters - Page 8:
2025-06-30 22:31:03,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:03,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:03,885 - INFO - Response - Page 8:
2025-06-30 22:31:03,885 - INFO - 第 8 页获取到 50 条记录
2025-06-30 22:31:04,401 - INFO - Request Parameters - Page 9:
2025-06-30 22:31:04,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:04,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:05,089 - INFO - Response - Page 9:
2025-06-30 22:31:05,089 - INFO - 第 9 页获取到 50 条记录
2025-06-30 22:31:05,589 - INFO - Request Parameters - Page 10:
2025-06-30 22:31:05,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:05,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:06,230 - INFO - Response - Page 10:
2025-06-30 22:31:06,230 - INFO - 第 10 页获取到 50 条记录
2025-06-30 22:31:06,746 - INFO - Request Parameters - Page 11:
2025-06-30 22:31:06,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:06,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:07,324 - INFO - Response - Page 11:
2025-06-30 22:31:07,324 - INFO - 第 11 页获取到 20 条记录
2025-06-30 22:31:07,824 - INFO - 查询完成，共获取到 520 条记录
2025-06-30 22:31:07,824 - INFO - 获取到 520 条表单数据
2025-06-30 22:31:07,824 - INFO - 当前日期 2025-06-21 有 1 条MySQL数据需要处理
2025-06-30 22:31:07,824 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 22:31:07,824 - INFO - 开始处理日期: 2025-06-25
2025-06-30 22:31:07,824 - INFO - Request Parameters - Page 1:
2025-06-30 22:31:07,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:07,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:08,481 - INFO - Response - Page 1:
2025-06-30 22:31:08,481 - INFO - 第 1 页获取到 50 条记录
2025-06-30 22:31:08,997 - INFO - Request Parameters - Page 2:
2025-06-30 22:31:08,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:08,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:09,685 - INFO - Response - Page 2:
2025-06-30 22:31:09,685 - INFO - 第 2 页获取到 50 条记录
2025-06-30 22:31:10,200 - INFO - Request Parameters - Page 3:
2025-06-30 22:31:10,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:10,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:10,841 - INFO - Response - Page 3:
2025-06-30 22:31:10,841 - INFO - 第 3 页获取到 50 条记录
2025-06-30 22:31:11,357 - INFO - Request Parameters - Page 4:
2025-06-30 22:31:11,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:11,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:11,998 - INFO - Response - Page 4:
2025-06-30 22:31:11,998 - INFO - 第 4 页获取到 50 条记录
2025-06-30 22:31:12,514 - INFO - Request Parameters - Page 5:
2025-06-30 22:31:12,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:12,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:13,139 - INFO - Response - Page 5:
2025-06-30 22:31:13,155 - INFO - 第 5 页获取到 50 条记录
2025-06-30 22:31:13,670 - INFO - Request Parameters - Page 6:
2025-06-30 22:31:13,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:13,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:14,358 - INFO - Response - Page 6:
2025-06-30 22:31:14,358 - INFO - 第 6 页获取到 50 条记录
2025-06-30 22:31:14,858 - INFO - Request Parameters - Page 7:
2025-06-30 22:31:14,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:14,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:15,484 - INFO - Response - Page 7:
2025-06-30 22:31:15,484 - INFO - 第 7 页获取到 50 条记录
2025-06-30 22:31:15,984 - INFO - Request Parameters - Page 8:
2025-06-30 22:31:15,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:15,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:16,625 - INFO - Response - Page 8:
2025-06-30 22:31:16,625 - INFO - 第 8 页获取到 50 条记录
2025-06-30 22:31:17,141 - INFO - Request Parameters - Page 9:
2025-06-30 22:31:17,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:17,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:17,860 - INFO - Response - Page 9:
2025-06-30 22:31:17,860 - INFO - 第 9 页获取到 50 条记录
2025-06-30 22:31:18,375 - INFO - Request Parameters - Page 10:
2025-06-30 22:31:18,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:18,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:19,016 - INFO - Response - Page 10:
2025-06-30 22:31:19,016 - INFO - 第 10 页获取到 50 条记录
2025-06-30 22:31:19,532 - INFO - Request Parameters - Page 11:
2025-06-30 22:31:19,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:19,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:19,939 - INFO - Response - Page 11:
2025-06-30 22:31:19,939 - INFO - 查询完成，共获取到 500 条记录
2025-06-30 22:31:19,939 - INFO - 获取到 500 条表单数据
2025-06-30 22:31:19,954 - INFO - 当前日期 2025-06-25 有 1 条MySQL数据需要处理
2025-06-30 22:31:19,954 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 22:31:19,954 - INFO - 开始处理日期: 2025-06-27
2025-06-30 22:31:19,954 - INFO - Request Parameters - Page 1:
2025-06-30 22:31:19,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:19,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:20,626 - INFO - Response - Page 1:
2025-06-30 22:31:20,626 - INFO - 第 1 页获取到 50 条记录
2025-06-30 22:31:21,127 - INFO - Request Parameters - Page 2:
2025-06-30 22:31:21,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:21,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:21,736 - INFO - Response - Page 2:
2025-06-30 22:31:21,736 - INFO - 第 2 页获取到 50 条记录
2025-06-30 22:31:22,252 - INFO - Request Parameters - Page 3:
2025-06-30 22:31:22,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:22,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:22,940 - INFO - Response - Page 3:
2025-06-30 22:31:22,940 - INFO - 第 3 页获取到 50 条记录
2025-06-30 22:31:23,456 - INFO - Request Parameters - Page 4:
2025-06-30 22:31:23,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:23,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:24,128 - INFO - Response - Page 4:
2025-06-30 22:31:24,128 - INFO - 第 4 页获取到 50 条记录
2025-06-30 22:31:24,644 - INFO - Request Parameters - Page 5:
2025-06-30 22:31:24,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:24,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:25,316 - INFO - Response - Page 5:
2025-06-30 22:31:25,316 - INFO - 第 5 页获取到 50 条记录
2025-06-30 22:31:25,816 - INFO - Request Parameters - Page 6:
2025-06-30 22:31:25,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:25,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:26,551 - INFO - Response - Page 6:
2025-06-30 22:31:26,551 - INFO - 第 6 页获取到 50 条记录
2025-06-30 22:31:27,051 - INFO - Request Parameters - Page 7:
2025-06-30 22:31:27,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:27,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:27,707 - INFO - Response - Page 7:
2025-06-30 22:31:27,707 - INFO - 第 7 页获取到 50 条记录
2025-06-30 22:31:28,207 - INFO - Request Parameters - Page 8:
2025-06-30 22:31:28,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:28,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:28,958 - INFO - Response - Page 8:
2025-06-30 22:31:28,958 - INFO - 第 8 页获取到 50 条记录
2025-06-30 22:31:29,474 - INFO - Request Parameters - Page 9:
2025-06-30 22:31:29,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:29,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:30,099 - INFO - Response - Page 9:
2025-06-30 22:31:30,099 - INFO - 第 9 页获取到 50 条记录
2025-06-30 22:31:30,615 - INFO - Request Parameters - Page 10:
2025-06-30 22:31:30,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:30,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:31,209 - INFO - Response - Page 10:
2025-06-30 22:31:31,209 - INFO - 第 10 页获取到 27 条记录
2025-06-30 22:31:31,724 - INFO - 查询完成，共获取到 477 条记录
2025-06-30 22:31:31,724 - INFO - 获取到 477 条表单数据
2025-06-30 22:31:31,724 - INFO - 当前日期 2025-06-27 有 2 条MySQL数据需要处理
2025-06-30 22:31:31,724 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 22:31:31,724 - INFO - 开始处理日期: 2025-06-28
2025-06-30 22:31:31,724 - INFO - Request Parameters - Page 1:
2025-06-30 22:31:31,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:31,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:32,428 - INFO - Response - Page 1:
2025-06-30 22:31:32,428 - INFO - 第 1 页获取到 50 条记录
2025-06-30 22:31:32,944 - INFO - Request Parameters - Page 2:
2025-06-30 22:31:32,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:32,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:33,585 - INFO - Response - Page 2:
2025-06-30 22:31:33,585 - INFO - 第 2 页获取到 50 条记录
2025-06-30 22:31:34,100 - INFO - Request Parameters - Page 3:
2025-06-30 22:31:34,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:34,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:34,726 - INFO - Response - Page 3:
2025-06-30 22:31:34,726 - INFO - 第 3 页获取到 50 条记录
2025-06-30 22:31:35,242 - INFO - Request Parameters - Page 4:
2025-06-30 22:31:35,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:35,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:35,882 - INFO - Response - Page 4:
2025-06-30 22:31:35,882 - INFO - 第 4 页获取到 50 条记录
2025-06-30 22:31:36,398 - INFO - Request Parameters - Page 5:
2025-06-30 22:31:36,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:36,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:37,102 - INFO - Response - Page 5:
2025-06-30 22:31:37,102 - INFO - 第 5 页获取到 50 条记录
2025-06-30 22:31:37,617 - INFO - Request Parameters - Page 6:
2025-06-30 22:31:37,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:37,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:38,305 - INFO - Response - Page 6:
2025-06-30 22:31:38,305 - INFO - 第 6 页获取到 50 条记录
2025-06-30 22:31:38,805 - INFO - Request Parameters - Page 7:
2025-06-30 22:31:38,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:38,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:39,431 - INFO - Response - Page 7:
2025-06-30 22:31:39,431 - INFO - 第 7 页获取到 50 条记录
2025-06-30 22:31:39,931 - INFO - Request Parameters - Page 8:
2025-06-30 22:31:39,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:39,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:40,650 - INFO - Response - Page 8:
2025-06-30 22:31:40,650 - INFO - 第 8 页获取到 50 条记录
2025-06-30 22:31:41,150 - INFO - Request Parameters - Page 9:
2025-06-30 22:31:41,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:41,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:41,807 - INFO - Response - Page 9:
2025-06-30 22:31:41,807 - INFO - 第 9 页获取到 50 条记录
2025-06-30 22:31:42,322 - INFO - Request Parameters - Page 10:
2025-06-30 22:31:42,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:42,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:42,885 - INFO - Response - Page 10:
2025-06-30 22:31:42,885 - INFO - 第 10 页获取到 29 条记录
2025-06-30 22:31:43,401 - INFO - 查询完成，共获取到 479 条记录
2025-06-30 22:31:43,401 - INFO - 获取到 479 条表单数据
2025-06-30 22:31:43,401 - INFO - 当前日期 2025-06-28 有 2 条MySQL数据需要处理
2025-06-30 22:31:43,401 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 22:31:43,401 - INFO - 开始处理日期: 2025-06-29
2025-06-30 22:31:43,401 - INFO - Request Parameters - Page 1:
2025-06-30 22:31:43,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:43,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:44,058 - INFO - Response - Page 1:
2025-06-30 22:31:44,058 - INFO - 第 1 页获取到 50 条记录
2025-06-30 22:31:44,573 - INFO - Request Parameters - Page 2:
2025-06-30 22:31:44,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:44,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:45,245 - INFO - Response - Page 2:
2025-06-30 22:31:45,245 - INFO - 第 2 页获取到 50 条记录
2025-06-30 22:31:45,746 - INFO - Request Parameters - Page 3:
2025-06-30 22:31:45,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:45,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:46,402 - INFO - Response - Page 3:
2025-06-30 22:31:46,402 - INFO - 第 3 页获取到 50 条记录
2025-06-30 22:31:46,918 - INFO - Request Parameters - Page 4:
2025-06-30 22:31:46,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:46,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:47,528 - INFO - Response - Page 4:
2025-06-30 22:31:47,528 - INFO - 第 4 页获取到 50 条记录
2025-06-30 22:31:48,028 - INFO - Request Parameters - Page 5:
2025-06-30 22:31:48,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:48,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:48,762 - INFO - Response - Page 5:
2025-06-30 22:31:48,762 - INFO - 第 5 页获取到 50 条记录
2025-06-30 22:31:49,278 - INFO - Request Parameters - Page 6:
2025-06-30 22:31:49,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:49,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:49,919 - INFO - Response - Page 6:
2025-06-30 22:31:49,919 - INFO - 第 6 页获取到 50 条记录
2025-06-30 22:31:50,435 - INFO - Request Parameters - Page 7:
2025-06-30 22:31:50,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:50,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:51,092 - INFO - Response - Page 7:
2025-06-30 22:31:51,092 - INFO - 第 7 页获取到 50 条记录
2025-06-30 22:31:51,592 - INFO - Request Parameters - Page 8:
2025-06-30 22:31:51,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:51,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:52,248 - INFO - Response - Page 8:
2025-06-30 22:31:52,248 - INFO - 第 8 页获取到 50 条记录
2025-06-30 22:31:52,764 - INFO - Request Parameters - Page 9:
2025-06-30 22:31:52,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:52,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:53,483 - INFO - Response - Page 9:
2025-06-30 22:31:53,483 - INFO - 第 9 页获取到 50 条记录
2025-06-30 22:31:53,999 - INFO - Request Parameters - Page 10:
2025-06-30 22:31:53,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:53,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:54,624 - INFO - Response - Page 10:
2025-06-30 22:31:54,624 - INFO - 第 10 页获取到 50 条记录
2025-06-30 22:31:55,140 - INFO - Request Parameters - Page 11:
2025-06-30 22:31:55,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:55,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:55,781 - INFO - Response - Page 11:
2025-06-30 22:31:55,781 - INFO - 第 11 页获取到 39 条记录
2025-06-30 22:31:56,297 - INFO - 查询完成，共获取到 539 条记录
2025-06-30 22:31:56,297 - INFO - 获取到 539 条表单数据
2025-06-30 22:31:56,297 - INFO - 当前日期 2025-06-29 有 152 条MySQL数据需要处理
2025-06-30 22:31:56,312 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 22:31:56,312 - INFO - 开始处理日期: 2025-06-30
2025-06-30 22:31:56,312 - INFO - Request Parameters - Page 1:
2025-06-30 22:31:56,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:31:56,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:31:56,750 - INFO - Response - Page 1:
2025-06-30 22:31:56,750 - INFO - 第 1 页获取到 2 条记录
2025-06-30 22:31:57,266 - INFO - 查询完成，共获取到 2 条记录
2025-06-30 22:31:57,266 - INFO - 获取到 2 条表单数据
2025-06-30 22:31:57,266 - INFO - 当前日期 2025-06-30 有 7 条MySQL数据需要处理
2025-06-30 22:31:57,266 - INFO - 开始批量插入 5 条新记录
2025-06-30 22:31:57,438 - INFO - 批量插入响应状态码: 200
2025-06-30 22:31:57,438 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 14:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '247', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3ED9787D-75BC-7D75-AB6E-9BAC2FEB1B16', 'x-acs-trace-id': '62df8317cb88c4ae26d4e1edcd95b823', 'etag': '246BOw42tAGdTWRvTAfqSnA7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-30 22:31:57,438 - INFO - 批量插入响应体: {'result': ['FINST-FPB66VB1PARWKBDU6UPIZ7JFU2I931JF67JCM2', 'FINST-FPB66VB1PARWKBDU6UPIZ7JFU2I932JF67JCM3', 'FINST-FPB66VB1PARWKBDU6UPIZ7JFU2I932JF67JCM4', 'FINST-FPB66VB1PARWKBDU6UPIZ7JFU2I932JF67JCM5', 'FINST-FPB66VB1PARWKBDU6UPIZ7JFU2I932JF67JCM6']}
2025-06-30 22:31:57,438 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-06-30 22:31:57,438 - INFO - 成功插入的数据ID: ['FINST-FPB66VB1PARWKBDU6UPIZ7JFU2I931JF67JCM2', 'FINST-FPB66VB1PARWKBDU6UPIZ7JFU2I932JF67JCM3', 'FINST-FPB66VB1PARWKBDU6UPIZ7JFU2I932JF67JCM4', 'FINST-FPB66VB1PARWKBDU6UPIZ7JFU2I932JF67JCM5', 'FINST-FPB66VB1PARWKBDU6UPIZ7JFU2I932JF67JCM6']
2025-06-30 22:32:02,455 - INFO - 批量插入完成，共 5 条记录
2025-06-30 22:32:02,455 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 5 条，错误: 0 条
2025-06-30 22:32:02,455 - INFO - 数据同步完成！更新: 0 条，插入: 5 条，错误: 1 条
2025-06-30 22:33:02,495 - INFO - 开始同步昨天与今天的销售数据: 2025-06-29 至 2025-06-30
2025-06-30 22:33:02,495 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-30 22:33:02,495 - INFO - 查询参数: ('2025-06-29', '2025-06-30')
2025-06-30 22:33:02,636 - INFO - MySQL查询成功，时间段: 2025-06-29 至 2025-06-30，共获取 567 条记录
2025-06-30 22:33:02,636 - INFO - 获取到 2 个日期需要处理: ['2025-06-29', '2025-06-30']
2025-06-30 22:33:02,651 - INFO - 开始处理日期: 2025-06-29
2025-06-30 22:33:02,651 - INFO - Request Parameters - Page 1:
2025-06-30 22:33:02,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:33:02,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:33:03,292 - INFO - Response - Page 1:
2025-06-30 22:33:03,292 - INFO - 第 1 页获取到 50 条记录
2025-06-30 22:33:03,808 - INFO - Request Parameters - Page 2:
2025-06-30 22:33:03,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:33:03,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:33:04,464 - INFO - Response - Page 2:
2025-06-30 22:33:04,464 - INFO - 第 2 页获取到 50 条记录
2025-06-30 22:33:04,965 - INFO - Request Parameters - Page 3:
2025-06-30 22:33:04,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:33:04,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:33:05,652 - INFO - Response - Page 3:
2025-06-30 22:33:05,652 - INFO - 第 3 页获取到 50 条记录
2025-06-30 22:33:06,168 - INFO - Request Parameters - Page 4:
2025-06-30 22:33:06,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:33:06,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:33:06,825 - INFO - Response - Page 4:
2025-06-30 22:33:06,825 - INFO - 第 4 页获取到 50 条记录
2025-06-30 22:33:07,341 - INFO - Request Parameters - Page 5:
2025-06-30 22:33:07,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:33:07,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:33:08,013 - INFO - Response - Page 5:
2025-06-30 22:33:08,013 - INFO - 第 5 页获取到 50 条记录
2025-06-30 22:33:08,528 - INFO - Request Parameters - Page 6:
2025-06-30 22:33:08,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:33:08,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:33:09,201 - INFO - Response - Page 6:
2025-06-30 22:33:09,201 - INFO - 第 6 页获取到 50 条记录
2025-06-30 22:33:09,701 - INFO - Request Parameters - Page 7:
2025-06-30 22:33:09,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:33:09,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:33:10,404 - INFO - Response - Page 7:
2025-06-30 22:33:10,404 - INFO - 第 7 页获取到 50 条记录
2025-06-30 22:33:10,920 - INFO - Request Parameters - Page 8:
2025-06-30 22:33:10,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:33:10,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:33:11,592 - INFO - Response - Page 8:
2025-06-30 22:33:11,592 - INFO - 第 8 页获取到 50 条记录
2025-06-30 22:33:12,092 - INFO - Request Parameters - Page 9:
2025-06-30 22:33:12,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:33:12,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:33:12,796 - INFO - Response - Page 9:
2025-06-30 22:33:12,796 - INFO - 第 9 页获取到 50 条记录
2025-06-30 22:33:13,312 - INFO - Request Parameters - Page 10:
2025-06-30 22:33:13,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:33:13,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:33:14,015 - INFO - Response - Page 10:
2025-06-30 22:33:14,015 - INFO - 第 10 页获取到 50 条记录
2025-06-30 22:33:14,531 - INFO - Request Parameters - Page 11:
2025-06-30 22:33:14,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:33:14,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751126400000, 1751212799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:33:15,109 - INFO - Response - Page 11:
2025-06-30 22:33:15,109 - INFO - 第 11 页获取到 39 条记录
2025-06-30 22:33:15,609 - INFO - 查询完成，共获取到 539 条记录
2025-06-30 22:33:15,609 - INFO - 获取到 539 条表单数据
2025-06-30 22:33:15,609 - INFO - 当前日期 2025-06-29 有 539 条MySQL数据需要处理
2025-06-30 22:33:15,625 - INFO - 日期 2025-06-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 22:33:15,625 - INFO - 开始处理日期: 2025-06-30
2025-06-30 22:33:15,625 - INFO - Request Parameters - Page 1:
2025-06-30 22:33:15,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-30 22:33:15,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-30 22:33:16,141 - INFO - Response - Page 1:
2025-06-30 22:33:16,141 - INFO - 第 1 页获取到 7 条记录
2025-06-30 22:33:16,641 - INFO - 查询完成，共获取到 7 条记录
2025-06-30 22:33:16,641 - INFO - 获取到 7 条表单数据
2025-06-30 22:33:16,641 - INFO - 当前日期 2025-06-30 有 7 条MySQL数据需要处理
2025-06-30 22:33:16,641 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 22:33:16,641 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-30 22:33:16,641 - INFO - 同步完成
