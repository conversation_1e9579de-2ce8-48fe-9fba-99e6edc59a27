import configparser

def load_config(config_file, section):
    """
    读取配置文件指定节点下的所有参数值
    
    Args:
        config_file: 配置文件路径
        section: 节点名称
        
    Returns:
        dict: 包含节点下所有参数的字典
    """
    # 读取配置文件
    config = configparser.ConfigParser()
    config.read(config_file, encoding='utf-8')
    
    # 检查节点是否存在
    if not config.has_section(section):
        raise ValueError(f"配置文件中不存在节点 '{section}'")
        
    # 获取节点下所有参数
    config_dict = {}
    for key, value in config.items(section):
        # 如果值包含逗号,则分割为列表
        if ',' in value:
            config_dict[key] = [item.strip() for item in value.split(',')]
        else:
            config_dict[key] = value
            
    return config_dict

if __name__ == "__main__":
    # 测试读取不同节点的配置
    db_config = load_config('config.ini', 'DATABASE')
    email_config = load_config('config.ini', 'EmailConfig')
    print("数据库配置:", db_config)
    print("邮件配置:", email_config)
