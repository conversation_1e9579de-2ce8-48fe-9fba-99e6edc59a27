2025-05-30 00:00:02,049 - INFO - =================使用默认全量同步=============
2025-05-30 00:00:03,587 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-30 00:00:03,588 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-30 00:00:03,617 - INFO - 开始处理日期: 2025-01
2025-05-30 00:00:03,621 - INFO - Request Parameters - Page 1:
2025-05-30 00:00:03,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:03,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:05,104 - INFO - Response - Page 1:
2025-05-30 00:00:05,306 - INFO - 第 1 页获取到 100 条记录
2025-05-30 00:00:05,306 - INFO - Request Parameters - Page 2:
2025-05-30 00:00:05,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:05,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:05,946 - INFO - Response - Page 2:
2025-05-30 00:00:06,147 - INFO - 第 2 页获取到 100 条记录
2025-05-30 00:00:06,147 - INFO - Request Parameters - Page 3:
2025-05-30 00:00:06,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:06,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:06,897 - INFO - Response - Page 3:
2025-05-30 00:00:07,098 - INFO - 第 3 页获取到 100 条记录
2025-05-30 00:00:07,098 - INFO - Request Parameters - Page 4:
2025-05-30 00:00:07,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:07,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:07,730 - INFO - Response - Page 4:
2025-05-30 00:00:07,931 - INFO - 第 4 页获取到 100 条记录
2025-05-30 00:00:07,931 - INFO - Request Parameters - Page 5:
2025-05-30 00:00:07,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:07,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:08,514 - INFO - Response - Page 5:
2025-05-30 00:00:08,714 - INFO - 第 5 页获取到 100 条记录
2025-05-30 00:00:08,714 - INFO - Request Parameters - Page 6:
2025-05-30 00:00:08,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:08,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:09,348 - INFO - Response - Page 6:
2025-05-30 00:00:09,549 - INFO - 第 6 页获取到 100 条记录
2025-05-30 00:00:09,549 - INFO - Request Parameters - Page 7:
2025-05-30 00:00:09,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:09,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:10,206 - INFO - Response - Page 7:
2025-05-30 00:00:10,406 - INFO - 第 7 页获取到 82 条记录
2025-05-30 00:00:10,406 - INFO - 查询完成，共获取到 682 条记录
2025-05-30 00:00:10,406 - INFO - 获取到 682 条表单数据
2025-05-30 00:00:10,422 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-30 00:00:10,434 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 00:00:10,434 - INFO - 开始处理日期: 2025-02
2025-05-30 00:00:10,434 - INFO - Request Parameters - Page 1:
2025-05-30 00:00:10,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:10,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:10,902 - INFO - Response - Page 1:
2025-05-30 00:00:11,102 - INFO - 第 1 页获取到 100 条记录
2025-05-30 00:00:11,102 - INFO - Request Parameters - Page 2:
2025-05-30 00:00:11,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:11,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:11,699 - INFO - Response - Page 2:
2025-05-30 00:00:11,900 - INFO - 第 2 页获取到 100 条记录
2025-05-30 00:00:11,900 - INFO - Request Parameters - Page 3:
2025-05-30 00:00:11,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:11,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:12,597 - INFO - Response - Page 3:
2025-05-30 00:00:12,797 - INFO - 第 3 页获取到 100 条记录
2025-05-30 00:00:12,797 - INFO - Request Parameters - Page 4:
2025-05-30 00:00:12,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:12,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:13,663 - INFO - Response - Page 4:
2025-05-30 00:00:13,863 - INFO - 第 4 页获取到 100 条记录
2025-05-30 00:00:13,863 - INFO - Request Parameters - Page 5:
2025-05-30 00:00:13,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:13,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:14,574 - INFO - Response - Page 5:
2025-05-30 00:00:14,774 - INFO - 第 5 页获取到 100 条记录
2025-05-30 00:00:14,774 - INFO - Request Parameters - Page 6:
2025-05-30 00:00:14,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:14,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:15,353 - INFO - Response - Page 6:
2025-05-30 00:00:15,554 - INFO - 第 6 页获取到 100 条记录
2025-05-30 00:00:15,554 - INFO - Request Parameters - Page 7:
2025-05-30 00:00:15,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:15,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:16,126 - INFO - Response - Page 7:
2025-05-30 00:00:16,327 - INFO - 第 7 页获取到 70 条记录
2025-05-30 00:00:16,327 - INFO - 查询完成，共获取到 670 条记录
2025-05-30 00:00:16,327 - INFO - 获取到 670 条表单数据
2025-05-30 00:00:16,341 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-30 00:00:16,353 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 00:00:16,353 - INFO - 开始处理日期: 2025-03
2025-05-30 00:00:16,353 - INFO - Request Parameters - Page 1:
2025-05-30 00:00:16,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:16,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:17,184 - INFO - Response - Page 1:
2025-05-30 00:00:17,385 - INFO - 第 1 页获取到 100 条记录
2025-05-30 00:00:17,385 - INFO - Request Parameters - Page 2:
2025-05-30 00:00:17,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:17,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:18,015 - INFO - Response - Page 2:
2025-05-30 00:00:18,216 - INFO - 第 2 页获取到 100 条记录
2025-05-30 00:00:18,216 - INFO - Request Parameters - Page 3:
2025-05-30 00:00:18,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:18,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:18,878 - INFO - Response - Page 3:
2025-05-30 00:00:19,078 - INFO - 第 3 页获取到 100 条记录
2025-05-30 00:00:19,078 - INFO - Request Parameters - Page 4:
2025-05-30 00:00:19,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:19,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:19,664 - INFO - Response - Page 4:
2025-05-30 00:00:19,864 - INFO - 第 4 页获取到 100 条记录
2025-05-30 00:00:19,864 - INFO - Request Parameters - Page 5:
2025-05-30 00:00:19,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:19,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:20,480 - INFO - Response - Page 5:
2025-05-30 00:00:20,680 - INFO - 第 5 页获取到 100 条记录
2025-05-30 00:00:20,680 - INFO - Request Parameters - Page 6:
2025-05-30 00:00:20,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:20,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:21,250 - INFO - Response - Page 6:
2025-05-30 00:00:21,450 - INFO - 第 6 页获取到 100 条记录
2025-05-30 00:00:21,450 - INFO - Request Parameters - Page 7:
2025-05-30 00:00:21,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:21,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:21,974 - INFO - Response - Page 7:
2025-05-30 00:00:22,176 - INFO - 第 7 页获取到 61 条记录
2025-05-30 00:00:22,176 - INFO - 查询完成，共获取到 661 条记录
2025-05-30 00:00:22,176 - INFO - 获取到 661 条表单数据
2025-05-30 00:00:22,192 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-30 00:00:22,205 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 00:00:22,205 - INFO - 开始处理日期: 2025-04
2025-05-30 00:00:22,205 - INFO - Request Parameters - Page 1:
2025-05-30 00:00:22,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:22,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:22,736 - INFO - Response - Page 1:
2025-05-30 00:00:22,936 - INFO - 第 1 页获取到 100 条记录
2025-05-30 00:00:22,936 - INFO - Request Parameters - Page 2:
2025-05-30 00:00:22,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:22,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:23,500 - INFO - Response - Page 2:
2025-05-30 00:00:23,700 - INFO - 第 2 页获取到 100 条记录
2025-05-30 00:00:23,700 - INFO - Request Parameters - Page 3:
2025-05-30 00:00:23,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:23,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:24,241 - INFO - Response - Page 3:
2025-05-30 00:00:24,442 - INFO - 第 3 页获取到 100 条记录
2025-05-30 00:00:24,442 - INFO - Request Parameters - Page 4:
2025-05-30 00:00:24,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:24,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:25,003 - INFO - Response - Page 4:
2025-05-30 00:00:25,203 - INFO - 第 4 页获取到 100 条记录
2025-05-30 00:00:25,203 - INFO - Request Parameters - Page 5:
2025-05-30 00:00:25,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:25,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:25,680 - INFO - Response - Page 5:
2025-05-30 00:00:25,881 - INFO - 第 5 页获取到 100 条记录
2025-05-30 00:00:25,881 - INFO - Request Parameters - Page 6:
2025-05-30 00:00:25,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:25,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:26,446 - INFO - Response - Page 6:
2025-05-30 00:00:26,646 - INFO - 第 6 页获取到 100 条记录
2025-05-30 00:00:26,646 - INFO - Request Parameters - Page 7:
2025-05-30 00:00:26,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:26,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:27,036 - INFO - Response - Page 7:
2025-05-30 00:00:27,236 - INFO - 第 7 页获取到 56 条记录
2025-05-30 00:00:27,236 - INFO - 查询完成，共获取到 656 条记录
2025-05-30 00:00:27,237 - INFO - 获取到 656 条表单数据
2025-05-30 00:00:27,251 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-30 00:00:27,262 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 00:00:27,262 - INFO - 开始处理日期: 2025-05
2025-05-30 00:00:27,263 - INFO - Request Parameters - Page 1:
2025-05-30 00:00:27,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:27,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:27,808 - INFO - Response - Page 1:
2025-05-30 00:00:28,009 - INFO - 第 1 页获取到 100 条记录
2025-05-30 00:00:28,009 - INFO - Request Parameters - Page 2:
2025-05-30 00:00:28,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:28,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:28,568 - INFO - Response - Page 2:
2025-05-30 00:00:28,768 - INFO - 第 2 页获取到 100 条记录
2025-05-30 00:00:28,768 - INFO - Request Parameters - Page 3:
2025-05-30 00:00:28,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:28,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:29,361 - INFO - Response - Page 3:
2025-05-30 00:00:29,561 - INFO - 第 3 页获取到 100 条记录
2025-05-30 00:00:29,561 - INFO - Request Parameters - Page 4:
2025-05-30 00:00:29,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:29,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:30,201 - INFO - Response - Page 4:
2025-05-30 00:00:30,401 - INFO - 第 4 页获取到 100 条记录
2025-05-30 00:00:30,401 - INFO - Request Parameters - Page 5:
2025-05-30 00:00:30,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:30,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:30,898 - INFO - Response - Page 5:
2025-05-30 00:00:31,098 - INFO - 第 5 页获取到 100 条记录
2025-05-30 00:00:31,098 - INFO - Request Parameters - Page 6:
2025-05-30 00:00:31,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:31,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:31,594 - INFO - Response - Page 6:
2025-05-30 00:00:31,795 - INFO - 第 6 页获取到 100 条记录
2025-05-30 00:00:31,795 - INFO - Request Parameters - Page 7:
2025-05-30 00:00:31,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 00:00:31,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 00:00:32,196 - INFO - Response - Page 7:
2025-05-30 00:00:32,396 - INFO - 第 7 页获取到 35 条记录
2025-05-30 00:00:32,396 - INFO - 查询完成，共获取到 635 条记录
2025-05-30 00:00:32,396 - INFO - 获取到 635 条表单数据
2025-05-30 00:00:32,408 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-30 00:00:32,409 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-30 00:00:32,903 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-30 00:00:32,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86854.0, 'new_value': 112010.84}, {'field': 'total_amount', 'old_value': 86854.0, 'new_value': 112010.84}, {'field': 'order_count', 'old_value': 14, 'new_value': 18}]
2025-05-30 00:00:32,903 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-30 00:00:33,330 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-30 00:00:33,330 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 127204.0, 'new_value': 132865.0}, {'field': 'offline_amount', 'old_value': 153740.28, 'new_value': 159119.28}, {'field': 'total_amount', 'old_value': 280944.28, 'new_value': 291984.28}, {'field': 'order_count', 'old_value': 6029, 'new_value': 6223}]
2025-05-30 00:00:33,330 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-30 00:00:33,793 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-30 00:00:33,793 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2580.6, 'new_value': 6294.6}, {'field': 'total_amount', 'old_value': 34262.4, 'new_value': 37976.4}, {'field': 'order_count', 'old_value': 41, 'new_value': 49}]
2025-05-30 00:00:33,793 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-30 00:00:34,307 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-30 00:00:34,307 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1202046.0, 'new_value': 1353062.0}, {'field': 'offline_amount', 'old_value': 344657.0, 'new_value': 397427.0}, {'field': 'total_amount', 'old_value': 1546703.0, 'new_value': 1750489.0}, {'field': 'order_count', 'old_value': 1799, 'new_value': 2000}]
2025-05-30 00:00:34,307 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-30 00:00:34,760 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-30 00:00:34,760 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72335.0, 'new_value': 73934.0}, {'field': 'total_amount', 'old_value': 109935.0, 'new_value': 111534.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-30 00:00:34,760 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-30 00:00:35,192 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-30 00:00:35,192 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91726.0, 'new_value': 95702.0}, {'field': 'offline_amount', 'old_value': 118258.0, 'new_value': 122335.0}, {'field': 'total_amount', 'old_value': 209984.0, 'new_value': 218037.0}, {'field': 'order_count', 'old_value': 4748, 'new_value': 4883}]
2025-05-30 00:00:35,192 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-30 00:00:35,635 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-30 00:00:35,635 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 277005.0, 'new_value': 288605.0}, {'field': 'total_amount', 'old_value': 307005.0, 'new_value': 318605.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 51}]
2025-05-30 00:00:35,636 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-30 00:00:36,103 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-30 00:00:36,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 334621.89, 'new_value': 346521.89}, {'field': 'total_amount', 'old_value': 374621.89, 'new_value': 386521.89}, {'field': 'order_count', 'old_value': 63, 'new_value': 66}]
2025-05-30 00:00:36,103 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-30 00:00:36,582 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-30 00:00:36,582 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 298425.13, 'new_value': 317395.13}, {'field': 'total_amount', 'old_value': 367785.13, 'new_value': 386755.13}, {'field': 'order_count', 'old_value': 54, 'new_value': 57}]
2025-05-30 00:00:36,582 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-30 00:00:37,029 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-30 00:00:37,029 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33026.2, 'new_value': 33902.2}, {'field': 'offline_amount', 'old_value': 189909.46, 'new_value': 193697.2}, {'field': 'total_amount', 'old_value': 222935.66, 'new_value': 227599.4}, {'field': 'order_count', 'old_value': 292, 'new_value': 301}]
2025-05-30 00:00:37,029 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-30 00:00:37,475 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-30 00:00:37,475 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44427.53, 'new_value': 50243.98}, {'field': 'total_amount', 'old_value': 44427.53, 'new_value': 50243.98}, {'field': 'order_count', 'old_value': 3241, 'new_value': 3673}]
2025-05-30 00:00:37,475 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-30 00:00:37,977 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-30 00:00:37,977 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5376.0, 'new_value': 6376.0}, {'field': 'offline_amount', 'old_value': 43104.0, 'new_value': 46530.0}, {'field': 'total_amount', 'old_value': 48480.0, 'new_value': 52906.0}, {'field': 'order_count', 'old_value': 223, 'new_value': 244}]
2025-05-30 00:00:37,977 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-30 00:00:38,403 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-30 00:00:38,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73381.56, 'new_value': 78490.1}, {'field': 'total_amount', 'old_value': 73381.56, 'new_value': 78490.1}, {'field': 'order_count', 'old_value': 132, 'new_value': 145}]
2025-05-30 00:00:38,404 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-30 00:00:38,841 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-30 00:00:38,841 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10433.0, 'new_value': 12861.0}, {'field': 'total_amount', 'old_value': 10433.0, 'new_value': 12861.0}, {'field': 'order_count', 'old_value': 306, 'new_value': 309}]
2025-05-30 00:00:38,841 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-30 00:00:39,295 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-30 00:00:39,296 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70.9, 'new_value': 80.8}, {'field': 'offline_amount', 'old_value': 53783.0, 'new_value': 58156.6}, {'field': 'total_amount', 'old_value': 53853.9, 'new_value': 58237.4}, {'field': 'order_count', 'old_value': 321, 'new_value': 349}]
2025-05-30 00:00:39,296 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-30 00:00:39,767 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-30 00:00:39,768 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57826.86, 'new_value': 59156.26}, {'field': 'offline_amount', 'old_value': 771524.53, 'new_value': 797156.03}, {'field': 'total_amount', 'old_value': 829351.39, 'new_value': 856312.29}, {'field': 'order_count', 'old_value': 3444, 'new_value': 3544}]
2025-05-30 00:00:39,768 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-30 00:00:40,237 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-30 00:00:40,238 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91047.21, 'new_value': 101875.51}, {'field': 'offline_amount', 'old_value': 44328.96, 'new_value': 50312.81}, {'field': 'total_amount', 'old_value': 135376.17, 'new_value': 152188.32}, {'field': 'order_count', 'old_value': 4701, 'new_value': 5313}]
2025-05-30 00:00:40,238 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-30 00:00:40,654 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-30 00:00:40,654 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57054.0, 'new_value': 64854.0}, {'field': 'total_amount', 'old_value': 57054.0, 'new_value': 64854.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 78}]
2025-05-30 00:00:40,654 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-30 00:00:41,076 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-30 00:00:41,076 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 145854.0, 'new_value': 166296.0}, {'field': 'offline_amount', 'old_value': 76970.0, 'new_value': 80905.0}, {'field': 'total_amount', 'old_value': 222824.0, 'new_value': 247201.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 91}]
2025-05-30 00:00:41,077 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-30 00:00:41,545 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-30 00:00:41,546 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4518.7, 'new_value': 4902.11}, {'field': 'offline_amount', 'old_value': 141588.59, 'new_value': 157548.07}, {'field': 'total_amount', 'old_value': 146107.29, 'new_value': 162450.18}, {'field': 'order_count', 'old_value': 675, 'new_value': 753}]
2025-05-30 00:00:41,548 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-30 00:00:41,979 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-30 00:00:41,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1913885.08, 'new_value': 1966310.73}, {'field': 'total_amount', 'old_value': 1913885.08, 'new_value': 1966310.73}, {'field': 'order_count', 'old_value': 17032, 'new_value': 17698}]
2025-05-30 00:00:41,980 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-30 00:00:42,416 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-30 00:00:42,416 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102356.84, 'new_value': 112255.26}, {'field': 'offline_amount', 'old_value': 74633.98, 'new_value': 82553.24}, {'field': 'total_amount', 'old_value': 176990.82, 'new_value': 194808.5}, {'field': 'order_count', 'old_value': 7539, 'new_value': 8338}]
2025-05-30 00:00:42,416 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-30 00:00:42,890 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-30 00:00:42,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116210.0, 'new_value': 128170.0}, {'field': 'total_amount', 'old_value': 116210.0, 'new_value': 128170.0}, {'field': 'order_count', 'old_value': 5633, 'new_value': 6498}]
2025-05-30 00:00:42,891 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-30 00:00:43,380 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-30 00:00:43,380 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23709.29, 'new_value': 24341.5}, {'field': 'total_amount', 'old_value': 26009.29, 'new_value': 26641.5}, {'field': 'order_count', 'old_value': 502, 'new_value': 513}]
2025-05-30 00:00:43,380 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-30 00:00:43,802 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-30 00:00:43,802 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12216.0, 'new_value': 13150.0}, {'field': 'total_amount', 'old_value': 12216.0, 'new_value': 13150.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 689}]
2025-05-30 00:00:43,804 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-30 00:00:44,236 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-30 00:00:44,236 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95082.07, 'new_value': 106728.1}, {'field': 'offline_amount', 'old_value': 238515.5, 'new_value': 262179.75}, {'field': 'total_amount', 'old_value': 333597.57, 'new_value': 368907.85}, {'field': 'order_count', 'old_value': 16481, 'new_value': 18258}]
2025-05-30 00:00:44,237 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-30 00:00:44,684 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-30 00:00:44,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 802683.23, 'new_value': 821115.97}, {'field': 'total_amount', 'old_value': 802683.23, 'new_value': 821115.97}, {'field': 'order_count', 'old_value': 5645, 'new_value': 5773}]
2025-05-30 00:00:44,686 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-30 00:00:45,156 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-30 00:00:45,156 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4300.0, 'new_value': 4800.0}, {'field': 'offline_amount', 'old_value': 312836.0, 'new_value': 347706.0}, {'field': 'total_amount', 'old_value': 317136.0, 'new_value': 352506.0}, {'field': 'order_count', 'old_value': 226, 'new_value': 258}]
2025-05-30 00:00:45,157 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-30 00:00:45,547 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-30 00:00:45,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27478.0, 'new_value': 37878.0}, {'field': 'total_amount', 'old_value': 27478.0, 'new_value': 37878.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-05-30 00:00:45,548 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-30 00:00:45,961 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-30 00:00:45,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37053.5, 'new_value': 37686.5}, {'field': 'total_amount', 'old_value': 37053.5, 'new_value': 37686.5}, {'field': 'order_count', 'old_value': 175, 'new_value': 187}]
2025-05-30 00:00:45,961 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F
2025-05-30 00:00:46,436 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F
2025-05-30 00:00:46,436 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23090.0, 'new_value': 26514.0}, {'field': 'total_amount', 'old_value': 23090.0, 'new_value': 26514.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-30 00:00:46,437 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-30 00:00:46,890 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-30 00:00:46,890 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24537.2, 'new_value': 26628.8}, {'field': 'offline_amount', 'old_value': 195257.7, 'new_value': 209845.7}, {'field': 'total_amount', 'old_value': 219794.9, 'new_value': 236474.5}, {'field': 'order_count', 'old_value': 6970, 'new_value': 7577}]
2025-05-30 00:00:46,893 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-30 00:00:47,326 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-30 00:00:47,326 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 488051.0, 'new_value': 497527.0}, {'field': 'total_amount', 'old_value': 494008.0, 'new_value': 503484.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 106}]
2025-05-30 00:00:47,328 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-30 00:00:47,782 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-30 00:00:47,782 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9043.17, 'new_value': 10367.63}, {'field': 'offline_amount', 'old_value': 57987.1, 'new_value': 61256.86}, {'field': 'total_amount', 'old_value': 67030.27, 'new_value': 71624.49}, {'field': 'order_count', 'old_value': 2820, 'new_value': 3065}]
2025-05-30 00:00:47,782 - INFO - 日期 2025-05 处理完成 - 更新: 34 条，插入: 0 条，错误: 0 条
2025-05-30 00:00:47,782 - INFO - 数据同步完成！更新: 34 条，插入: 0 条，错误: 0 条
2025-05-30 00:00:47,784 - INFO - =================同步完成====================
2025-05-30 03:00:01,982 - INFO - =================使用默认全量同步=============
2025-05-30 03:00:03,494 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-30 03:00:03,494 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-30 03:00:03,521 - INFO - 开始处理日期: 2025-01
2025-05-30 03:00:03,525 - INFO - Request Parameters - Page 1:
2025-05-30 03:00:03,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:03,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:04,620 - INFO - Response - Page 1:
2025-05-30 03:00:04,820 - INFO - 第 1 页获取到 100 条记录
2025-05-30 03:00:04,820 - INFO - Request Parameters - Page 2:
2025-05-30 03:00:04,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:04,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:05,700 - INFO - Response - Page 2:
2025-05-30 03:00:05,900 - INFO - 第 2 页获取到 100 条记录
2025-05-30 03:00:05,900 - INFO - Request Parameters - Page 3:
2025-05-30 03:00:05,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:05,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:06,392 - INFO - Response - Page 3:
2025-05-30 03:00:06,592 - INFO - 第 3 页获取到 100 条记录
2025-05-30 03:00:06,592 - INFO - Request Parameters - Page 4:
2025-05-30 03:00:06,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:06,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:07,125 - INFO - Response - Page 4:
2025-05-30 03:00:07,326 - INFO - 第 4 页获取到 100 条记录
2025-05-30 03:00:07,326 - INFO - Request Parameters - Page 5:
2025-05-30 03:00:07,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:07,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:07,811 - INFO - Response - Page 5:
2025-05-30 03:00:08,012 - INFO - 第 5 页获取到 100 条记录
2025-05-30 03:00:08,012 - INFO - Request Parameters - Page 6:
2025-05-30 03:00:08,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:08,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:08,578 - INFO - Response - Page 6:
2025-05-30 03:00:08,778 - INFO - 第 6 页获取到 100 条记录
2025-05-30 03:00:08,778 - INFO - Request Parameters - Page 7:
2025-05-30 03:00:08,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:08,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:09,279 - INFO - Response - Page 7:
2025-05-30 03:00:09,479 - INFO - 第 7 页获取到 82 条记录
2025-05-30 03:00:09,479 - INFO - 查询完成，共获取到 682 条记录
2025-05-30 03:00:09,479 - INFO - 获取到 682 条表单数据
2025-05-30 03:00:09,491 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-30 03:00:09,503 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 03:00:09,504 - INFO - 开始处理日期: 2025-02
2025-05-30 03:00:09,504 - INFO - Request Parameters - Page 1:
2025-05-30 03:00:09,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:09,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:10,106 - INFO - Response - Page 1:
2025-05-30 03:00:10,307 - INFO - 第 1 页获取到 100 条记录
2025-05-30 03:00:10,307 - INFO - Request Parameters - Page 2:
2025-05-30 03:00:10,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:10,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:10,813 - INFO - Response - Page 2:
2025-05-30 03:00:11,014 - INFO - 第 2 页获取到 100 条记录
2025-05-30 03:00:11,014 - INFO - Request Parameters - Page 3:
2025-05-30 03:00:11,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:11,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:11,533 - INFO - Response - Page 3:
2025-05-30 03:00:11,733 - INFO - 第 3 页获取到 100 条记录
2025-05-30 03:00:11,733 - INFO - Request Parameters - Page 4:
2025-05-30 03:00:11,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:11,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:12,191 - INFO - Response - Page 4:
2025-05-30 03:00:12,391 - INFO - 第 4 页获取到 100 条记录
2025-05-30 03:00:12,391 - INFO - Request Parameters - Page 5:
2025-05-30 03:00:12,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:12,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:12,859 - INFO - Response - Page 5:
2025-05-30 03:00:13,060 - INFO - 第 5 页获取到 100 条记录
2025-05-30 03:00:13,060 - INFO - Request Parameters - Page 6:
2025-05-30 03:00:13,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:13,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:13,729 - INFO - Response - Page 6:
2025-05-30 03:00:13,930 - INFO - 第 6 页获取到 100 条记录
2025-05-30 03:00:13,930 - INFO - Request Parameters - Page 7:
2025-05-30 03:00:13,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:13,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:14,526 - INFO - Response - Page 7:
2025-05-30 03:00:14,726 - INFO - 第 7 页获取到 70 条记录
2025-05-30 03:00:14,726 - INFO - 查询完成，共获取到 670 条记录
2025-05-30 03:00:14,726 - INFO - 获取到 670 条表单数据
2025-05-30 03:00:14,740 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-30 03:00:14,752 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 03:00:14,752 - INFO - 开始处理日期: 2025-03
2025-05-30 03:00:14,752 - INFO - Request Parameters - Page 1:
2025-05-30 03:00:14,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:14,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:15,267 - INFO - Response - Page 1:
2025-05-30 03:00:15,468 - INFO - 第 1 页获取到 100 条记录
2025-05-30 03:00:15,468 - INFO - Request Parameters - Page 2:
2025-05-30 03:00:15,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:15,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:15,977 - INFO - Response - Page 2:
2025-05-30 03:00:16,179 - INFO - 第 2 页获取到 100 条记录
2025-05-30 03:00:16,179 - INFO - Request Parameters - Page 3:
2025-05-30 03:00:16,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:16,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:16,670 - INFO - Response - Page 3:
2025-05-30 03:00:16,871 - INFO - 第 3 页获取到 100 条记录
2025-05-30 03:00:16,871 - INFO - Request Parameters - Page 4:
2025-05-30 03:00:16,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:16,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:17,465 - INFO - Response - Page 4:
2025-05-30 03:00:17,665 - INFO - 第 4 页获取到 100 条记录
2025-05-30 03:00:17,665 - INFO - Request Parameters - Page 5:
2025-05-30 03:00:17,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:17,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:18,231 - INFO - Response - Page 5:
2025-05-30 03:00:18,431 - INFO - 第 5 页获取到 100 条记录
2025-05-30 03:00:18,431 - INFO - Request Parameters - Page 6:
2025-05-30 03:00:18,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:18,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:18,896 - INFO - Response - Page 6:
2025-05-30 03:00:19,097 - INFO - 第 6 页获取到 100 条记录
2025-05-30 03:00:19,097 - INFO - Request Parameters - Page 7:
2025-05-30 03:00:19,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:19,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:19,561 - INFO - Response - Page 7:
2025-05-30 03:00:19,762 - INFO - 第 7 页获取到 61 条记录
2025-05-30 03:00:19,762 - INFO - 查询完成，共获取到 661 条记录
2025-05-30 03:00:19,762 - INFO - 获取到 661 条表单数据
2025-05-30 03:00:19,774 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-30 03:00:19,786 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 03:00:19,786 - INFO - 开始处理日期: 2025-04
2025-05-30 03:00:19,786 - INFO - Request Parameters - Page 1:
2025-05-30 03:00:19,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:19,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:20,342 - INFO - Response - Page 1:
2025-05-30 03:00:20,543 - INFO - 第 1 页获取到 100 条记录
2025-05-30 03:00:20,543 - INFO - Request Parameters - Page 2:
2025-05-30 03:00:20,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:20,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:21,066 - INFO - Response - Page 2:
2025-05-30 03:00:21,266 - INFO - 第 2 页获取到 100 条记录
2025-05-30 03:00:21,266 - INFO - Request Parameters - Page 3:
2025-05-30 03:00:21,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:21,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:21,783 - INFO - Response - Page 3:
2025-05-30 03:00:21,984 - INFO - 第 3 页获取到 100 条记录
2025-05-30 03:00:21,984 - INFO - Request Parameters - Page 4:
2025-05-30 03:00:21,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:21,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:22,485 - INFO - Response - Page 4:
2025-05-30 03:00:22,685 - INFO - 第 4 页获取到 100 条记录
2025-05-30 03:00:22,685 - INFO - Request Parameters - Page 5:
2025-05-30 03:00:22,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:22,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:23,210 - INFO - Response - Page 5:
2025-05-30 03:00:23,410 - INFO - 第 5 页获取到 100 条记录
2025-05-30 03:00:23,410 - INFO - Request Parameters - Page 6:
2025-05-30 03:00:23,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:23,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:23,877 - INFO - Response - Page 6:
2025-05-30 03:00:24,077 - INFO - 第 6 页获取到 100 条记录
2025-05-30 03:00:24,077 - INFO - Request Parameters - Page 7:
2025-05-30 03:00:24,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:24,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:24,552 - INFO - Response - Page 7:
2025-05-30 03:00:24,753 - INFO - 第 7 页获取到 56 条记录
2025-05-30 03:00:24,753 - INFO - 查询完成，共获取到 656 条记录
2025-05-30 03:00:24,753 - INFO - 获取到 656 条表单数据
2025-05-30 03:00:24,764 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-30 03:00:24,776 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 03:00:24,777 - INFO - 开始处理日期: 2025-05
2025-05-30 03:00:24,777 - INFO - Request Parameters - Page 1:
2025-05-30 03:00:24,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:24,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:25,262 - INFO - Response - Page 1:
2025-05-30 03:00:25,463 - INFO - 第 1 页获取到 100 条记录
2025-05-30 03:00:25,463 - INFO - Request Parameters - Page 2:
2025-05-30 03:00:25,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:25,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:25,928 - INFO - Response - Page 2:
2025-05-30 03:00:26,128 - INFO - 第 2 页获取到 100 条记录
2025-05-30 03:00:26,128 - INFO - Request Parameters - Page 3:
2025-05-30 03:00:26,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:26,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:26,630 - INFO - Response - Page 3:
2025-05-30 03:00:26,830 - INFO - 第 3 页获取到 100 条记录
2025-05-30 03:00:26,830 - INFO - Request Parameters - Page 4:
2025-05-30 03:00:26,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:26,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:27,425 - INFO - Response - Page 4:
2025-05-30 03:00:27,625 - INFO - 第 4 页获取到 100 条记录
2025-05-30 03:00:27,625 - INFO - Request Parameters - Page 5:
2025-05-30 03:00:27,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:27,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:28,117 - INFO - Response - Page 5:
2025-05-30 03:00:28,317 - INFO - 第 5 页获取到 100 条记录
2025-05-30 03:00:28,317 - INFO - Request Parameters - Page 6:
2025-05-30 03:00:28,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:28,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:28,983 - INFO - Response - Page 6:
2025-05-30 03:00:29,183 - INFO - 第 6 页获取到 100 条记录
2025-05-30 03:00:29,183 - INFO - Request Parameters - Page 7:
2025-05-30 03:00:29,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 03:00:29,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 03:00:29,640 - INFO - Response - Page 7:
2025-05-30 03:00:29,841 - INFO - 第 7 页获取到 35 条记录
2025-05-30 03:00:29,841 - INFO - 查询完成，共获取到 635 条记录
2025-05-30 03:00:29,841 - INFO - 获取到 635 条表单数据
2025-05-30 03:00:29,853 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-30 03:00:29,854 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-30 03:00:30,343 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-30 03:00:30,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11300080.0, 'new_value': 11700080.0}, {'field': 'total_amount', 'old_value': 11400080.0, 'new_value': 11800080.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 58}]
2025-05-30 03:00:30,344 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-30 03:00:30,778 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-30 03:00:30,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67537.0, 'new_value': 68336.0}, {'field': 'total_amount', 'old_value': 74351.16, 'new_value': 75150.16}, {'field': 'order_count', 'old_value': 467, 'new_value': 468}]
2025-05-30 03:00:30,779 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-30 03:00:31,237 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-30 03:00:31,237 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67109.62, 'new_value': 69164.21}, {'field': 'offline_amount', 'old_value': 111933.64, 'new_value': 114185.18}, {'field': 'total_amount', 'old_value': 179043.26, 'new_value': 183349.39}, {'field': 'order_count', 'old_value': 6214, 'new_value': 6365}]
2025-05-30 03:00:31,237 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-30 03:00:31,802 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-30 03:00:31,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 364923.0, 'new_value': 369064.0}, {'field': 'total_amount', 'old_value': 364923.0, 'new_value': 369064.0}, {'field': 'order_count', 'old_value': 227, 'new_value': 235}]
2025-05-30 03:00:31,803 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-30 03:00:32,294 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-30 03:00:32,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25065.79, 'new_value': 25071.79}, {'field': 'total_amount', 'old_value': 25131.34, 'new_value': 25137.34}, {'field': 'order_count', 'old_value': 231, 'new_value': 232}]
2025-05-30 03:00:32,295 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-30 03:00:32,733 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-30 03:00:32,734 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33746.15, 'new_value': 38684.17}, {'field': 'offline_amount', 'old_value': 167459.0, 'new_value': 168608.0}, {'field': 'total_amount', 'old_value': 201205.15, 'new_value': 207292.17}, {'field': 'order_count', 'old_value': 38, 'new_value': 40}]
2025-05-30 03:00:32,734 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-30 03:00:33,197 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-30 03:00:33,198 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61727.0, 'new_value': 62660.0}, {'field': 'total_amount', 'old_value': 62075.0, 'new_value': 63008.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 132}]
2025-05-30 03:00:33,198 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-30 03:00:33,725 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-30 03:00:33,725 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 944417.0, 'new_value': 954114.0}, {'field': 'total_amount', 'old_value': 944417.0, 'new_value': 954114.0}, {'field': 'order_count', 'old_value': 173, 'new_value': 178}]
2025-05-30 03:00:33,725 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-30 03:00:34,238 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-30 03:00:34,238 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 263046.93, 'new_value': 272355.96}, {'field': 'offline_amount', 'old_value': 112766.86, 'new_value': 114243.94}, {'field': 'total_amount', 'old_value': 375813.79, 'new_value': 386599.9}, {'field': 'order_count', 'old_value': 1519, 'new_value': 1608}]
2025-05-30 03:00:34,238 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-30 03:00:34,676 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-30 03:00:34,676 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24798.64, 'new_value': 25318.13}, {'field': 'offline_amount', 'old_value': 319783.23, 'new_value': 323150.76}, {'field': 'total_amount', 'old_value': 344581.87, 'new_value': 348468.89}, {'field': 'order_count', 'old_value': 1626, 'new_value': 1644}]
2025-05-30 03:00:34,676 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-30 03:00:35,112 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-30 03:00:35,112 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42761.96, 'new_value': 45170.02}, {'field': 'offline_amount', 'old_value': 596500.09, 'new_value': 609722.38}, {'field': 'total_amount', 'old_value': 639262.05, 'new_value': 654892.4}, {'field': 'order_count', 'old_value': 3382, 'new_value': 3437}]
2025-05-30 03:00:35,113 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-30 03:00:35,552 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-30 03:00:35,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 342330.0, 'new_value': 346853.4}, {'field': 'total_amount', 'old_value': 342330.0, 'new_value': 346853.4}, {'field': 'order_count', 'old_value': 3521, 'new_value': 3565}]
2025-05-30 03:00:35,553 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPU
2025-05-30 03:00:36,007 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPU
2025-05-30 03:00:36,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14177.0, 'new_value': 21657.0}, {'field': 'total_amount', 'old_value': 14177.0, 'new_value': 21657.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 5}]
2025-05-30 03:00:36,007 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-30 03:00:36,416 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-30 03:00:36,416 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243025.76, 'new_value': 247697.56}, {'field': 'total_amount', 'old_value': 243031.76, 'new_value': 247703.56}, {'field': 'order_count', 'old_value': 450, 'new_value': 464}]
2025-05-30 03:00:36,416 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-30 03:00:36,894 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-30 03:00:36,894 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 147770.24, 'new_value': 150770.24}, {'field': 'offline_amount', 'old_value': 515138.84, 'new_value': 528201.0}, {'field': 'total_amount', 'old_value': 662909.08, 'new_value': 678971.24}, {'field': 'order_count', 'old_value': 2620, 'new_value': 2770}]
2025-05-30 03:00:36,895 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-30 03:00:37,292 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-30 03:00:37,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141258.0, 'new_value': 150058.0}, {'field': 'total_amount', 'old_value': 141258.0, 'new_value': 150058.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-30 03:00:37,293 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-30 03:00:37,707 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-30 03:00:37,707 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 208382.0, 'new_value': 211633.0}, {'field': 'total_amount', 'old_value': 208515.0, 'new_value': 211766.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 152}]
2025-05-30 03:00:37,708 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-30 03:00:38,152 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-30 03:00:38,153 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112917.58, 'new_value': 114370.03}, {'field': 'total_amount', 'old_value': 118529.1, 'new_value': 119981.55}, {'field': 'order_count', 'old_value': 10929, 'new_value': 11033}]
2025-05-30 03:00:38,153 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-30 03:00:38,646 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-30 03:00:38,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81739.0, 'new_value': 84748.0}, {'field': 'total_amount', 'old_value': 81739.0, 'new_value': 84748.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 138}]
2025-05-30 03:00:38,646 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-30 03:00:39,122 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-30 03:00:39,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 405228.01, 'new_value': 411002.44}, {'field': 'total_amount', 'old_value': 405228.01, 'new_value': 411002.44}, {'field': 'order_count', 'old_value': 2026, 'new_value': 2071}]
2025-05-30 03:00:39,123 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-30 03:00:39,606 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-30 03:00:39,607 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73038.1, 'new_value': 74351.1}, {'field': 'total_amount', 'old_value': 73038.1, 'new_value': 74351.1}, {'field': 'order_count', 'old_value': 1647, 'new_value': 1652}]
2025-05-30 03:00:39,607 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-30 03:00:40,207 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-30 03:00:40,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 722133.0, 'new_value': 731777.0}, {'field': 'total_amount', 'old_value': 722133.0, 'new_value': 731777.0}, {'field': 'order_count', 'old_value': 2091, 'new_value': 2126}]
2025-05-30 03:00:40,208 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-30 03:00:40,675 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-30 03:00:40,675 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 902999.0, 'new_value': 932878.0}, {'field': 'total_amount', 'old_value': 902999.0, 'new_value': 932878.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 130}]
2025-05-30 03:00:40,675 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-30 03:00:41,161 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-30 03:00:41,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28894.0, 'new_value': 29092.0}, {'field': 'total_amount', 'old_value': 28894.0, 'new_value': 29092.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-30 03:00:41,162 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-30 03:00:41,618 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-30 03:00:41,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 627915.0, 'new_value': 637824.0}, {'field': 'total_amount', 'old_value': 630387.0, 'new_value': 640296.0}, {'field': 'order_count', 'old_value': 287, 'new_value': 293}]
2025-05-30 03:00:41,618 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-30 03:00:42,069 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-30 03:00:42,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214395.0, 'new_value': 216034.0}, {'field': 'total_amount', 'old_value': 214395.0, 'new_value': 216034.0}, {'field': 'order_count', 'old_value': 3384, 'new_value': 3389}]
2025-05-30 03:00:42,070 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-30 03:00:42,479 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-30 03:00:42,480 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 323561.0, 'new_value': 326011.0}, {'field': 'total_amount', 'old_value': 323561.0, 'new_value': 326011.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 90}]
2025-05-30 03:00:42,480 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-30 03:00:42,935 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-30 03:00:42,935 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 759775.0, 'new_value': 772916.0}, {'field': 'total_amount', 'old_value': 759775.0, 'new_value': 772916.0}, {'field': 'order_count', 'old_value': 184, 'new_value': 188}]
2025-05-30 03:00:42,935 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-30 03:00:43,335 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-30 03:00:43,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84396.0, 'new_value': 86940.0}, {'field': 'total_amount', 'old_value': 84396.0, 'new_value': 86940.0}, {'field': 'order_count', 'old_value': 226, 'new_value': 238}]
2025-05-30 03:00:43,335 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-30 03:00:43,768 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-30 03:00:43,768 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10376.81, 'new_value': 10443.71}, {'field': 'total_amount', 'old_value': 31676.81, 'new_value': 31743.71}, {'field': 'order_count', 'old_value': 161, 'new_value': 162}]
2025-05-30 03:00:43,768 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-30 03:00:44,219 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-30 03:00:44,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139560.7, 'new_value': 140993.0}, {'field': 'total_amount', 'old_value': 139560.7, 'new_value': 140993.0}, {'field': 'order_count', 'old_value': 277, 'new_value': 280}]
2025-05-30 03:00:44,220 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-30 03:00:44,656 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-30 03:00:44,656 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125253.6, 'new_value': 126229.6}, {'field': 'offline_amount', 'old_value': 80031.78, 'new_value': 81583.68}, {'field': 'total_amount', 'old_value': 205285.38, 'new_value': 207813.28}, {'field': 'order_count', 'old_value': 1385, 'new_value': 1400}]
2025-05-30 03:00:44,656 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-30 03:00:45,136 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-30 03:00:45,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 376764.5, 'new_value': 382705.6}, {'field': 'total_amount', 'old_value': 376764.5, 'new_value': 382705.6}, {'field': 'order_count', 'old_value': 464, 'new_value': 470}]
2025-05-30 03:00:45,137 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-30 03:00:45,592 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-30 03:00:45,592 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 993171.0, 'new_value': 1017109.0}, {'field': 'total_amount', 'old_value': 993171.0, 'new_value': 1017109.0}, {'field': 'order_count', 'old_value': 51110, 'new_value': 51133}]
2025-05-30 03:00:45,592 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-30 03:00:46,021 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-30 03:00:46,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 735069.0, 'new_value': 750767.0}, {'field': 'total_amount', 'old_value': 735069.0, 'new_value': 750767.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 86}]
2025-05-30 03:00:46,022 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-30 03:00:46,427 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-30 03:00:46,427 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 340588.52, 'new_value': 348513.65}, {'field': 'offline_amount', 'old_value': 1375633.57, 'new_value': 1402936.15}, {'field': 'total_amount', 'old_value': 1716222.09, 'new_value': 1751449.8}, {'field': 'order_count', 'old_value': 8574, 'new_value': 8763}]
2025-05-30 03:00:46,427 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-30 03:00:47,010 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-30 03:00:47,010 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 147284.1, 'new_value': 151294.8}, {'field': 'offline_amount', 'old_value': 122308.7, 'new_value': 125611.5}, {'field': 'total_amount', 'old_value': 269592.8, 'new_value': 276906.3}, {'field': 'order_count', 'old_value': 6352, 'new_value': 6515}]
2025-05-30 03:00:47,010 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-30 03:00:47,529 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-30 03:00:47,530 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 267303.74, 'new_value': 270838.74}, {'field': 'total_amount', 'old_value': 267303.74, 'new_value': 270838.74}, {'field': 'order_count', 'old_value': 1646, 'new_value': 1670}]
2025-05-30 03:00:47,530 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-30 03:00:47,993 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-30 03:00:47,994 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 265363.98, 'new_value': 273722.74}, {'field': 'offline_amount', 'old_value': 794733.47, 'new_value': 811339.47}, {'field': 'total_amount', 'old_value': 1060097.45, 'new_value': 1085062.21}, {'field': 'order_count', 'old_value': 6385, 'new_value': 6586}]
2025-05-30 03:00:47,994 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-30 03:00:48,400 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-30 03:00:48,401 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37707.09, 'new_value': 39657.11}, {'field': 'offline_amount', 'old_value': 381831.65, 'new_value': 388350.35}, {'field': 'total_amount', 'old_value': 419538.74, 'new_value': 428007.46}, {'field': 'order_count', 'old_value': 10139, 'new_value': 10235}]
2025-05-30 03:00:48,401 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-30 03:00:48,830 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-30 03:00:48,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38024.0, 'new_value': 39077.0}, {'field': 'total_amount', 'old_value': 38383.0, 'new_value': 39436.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 58}]
2025-05-30 03:00:48,831 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-30 03:00:49,274 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-30 03:00:49,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113533.1, 'new_value': 131986.1}, {'field': 'total_amount', 'old_value': 114338.1, 'new_value': 132791.1}, {'field': 'order_count', 'old_value': 16317, 'new_value': 16322}]
2025-05-30 03:00:49,275 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-30 03:00:49,722 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-30 03:00:49,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2008431.0, 'new_value': 2043384.0}, {'field': 'total_amount', 'old_value': 2008431.0, 'new_value': 2043384.0}, {'field': 'order_count', 'old_value': 8059, 'new_value': 8214}]
2025-05-30 03:00:49,722 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-30 03:00:50,220 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-30 03:00:50,220 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62865.86, 'new_value': 63805.54}, {'field': 'offline_amount', 'old_value': 48107.85, 'new_value': 48769.21}, {'field': 'total_amount', 'old_value': 110973.71, 'new_value': 112574.75}, {'field': 'order_count', 'old_value': 9382, 'new_value': 9533}]
2025-05-30 03:00:50,220 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-30 03:00:50,743 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-30 03:00:50,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 392579.9, 'new_value': 399076.9}, {'field': 'total_amount', 'old_value': 392579.9, 'new_value': 399076.9}]
2025-05-30 03:00:50,743 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-30 03:00:51,233 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-30 03:00:51,233 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30000.0, 'new_value': 60000.0}, {'field': 'offline_amount', 'old_value': 586014.0, 'new_value': 632079.0}, {'field': 'total_amount', 'old_value': 616014.0, 'new_value': 692079.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 103}]
2025-05-30 03:00:51,233 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-30 03:00:51,662 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-30 03:00:51,662 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 646150.15, 'new_value': 663811.15}, {'field': 'total_amount', 'old_value': 646150.15, 'new_value': 663811.15}, {'field': 'order_count', 'old_value': 563, 'new_value': 582}]
2025-05-30 03:00:51,665 - INFO - 日期 2025-05 处理完成 - 更新: 47 条，插入: 0 条，错误: 0 条
2025-05-30 03:00:51,665 - INFO - 数据同步完成！更新: 47 条，插入: 0 条，错误: 0 条
2025-05-30 03:00:51,666 - INFO - =================同步完成====================
2025-05-30 06:00:02,010 - INFO - =================使用默认全量同步=============
2025-05-30 06:00:03,499 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-30 06:00:03,500 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-30 06:00:03,531 - INFO - 开始处理日期: 2025-01
2025-05-30 06:00:03,533 - INFO - Request Parameters - Page 1:
2025-05-30 06:00:03,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:03,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:04,985 - INFO - Response - Page 1:
2025-05-30 06:00:05,186 - INFO - 第 1 页获取到 100 条记录
2025-05-30 06:00:05,186 - INFO - Request Parameters - Page 2:
2025-05-30 06:00:05,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:05,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:05,753 - INFO - Response - Page 2:
2025-05-30 06:00:05,953 - INFO - 第 2 页获取到 100 条记录
2025-05-30 06:00:05,953 - INFO - Request Parameters - Page 3:
2025-05-30 06:00:05,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:05,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:06,461 - INFO - Response - Page 3:
2025-05-30 06:00:06,661 - INFO - 第 3 页获取到 100 条记录
2025-05-30 06:00:06,661 - INFO - Request Parameters - Page 4:
2025-05-30 06:00:06,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:06,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:07,173 - INFO - Response - Page 4:
2025-05-30 06:00:07,373 - INFO - 第 4 页获取到 100 条记录
2025-05-30 06:00:07,373 - INFO - Request Parameters - Page 5:
2025-05-30 06:00:07,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:07,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:08,058 - INFO - Response - Page 5:
2025-05-30 06:00:08,258 - INFO - 第 5 页获取到 100 条记录
2025-05-30 06:00:08,258 - INFO - Request Parameters - Page 6:
2025-05-30 06:00:08,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:08,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:08,735 - INFO - Response - Page 6:
2025-05-30 06:00:08,935 - INFO - 第 6 页获取到 100 条记录
2025-05-30 06:00:08,935 - INFO - Request Parameters - Page 7:
2025-05-30 06:00:08,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:08,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:09,384 - INFO - Response - Page 7:
2025-05-30 06:00:09,584 - INFO - 第 7 页获取到 82 条记录
2025-05-30 06:00:09,584 - INFO - 查询完成，共获取到 682 条记录
2025-05-30 06:00:09,584 - INFO - 获取到 682 条表单数据
2025-05-30 06:00:09,596 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-30 06:00:09,608 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 06:00:09,608 - INFO - 开始处理日期: 2025-02
2025-05-30 06:00:09,608 - INFO - Request Parameters - Page 1:
2025-05-30 06:00:09,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:09,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:10,148 - INFO - Response - Page 1:
2025-05-30 06:00:10,348 - INFO - 第 1 页获取到 100 条记录
2025-05-30 06:00:10,348 - INFO - Request Parameters - Page 2:
2025-05-30 06:00:10,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:10,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:10,873 - INFO - Response - Page 2:
2025-05-30 06:00:11,073 - INFO - 第 2 页获取到 100 条记录
2025-05-30 06:00:11,073 - INFO - Request Parameters - Page 3:
2025-05-30 06:00:11,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:11,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:11,705 - INFO - Response - Page 3:
2025-05-30 06:00:11,906 - INFO - 第 3 页获取到 100 条记录
2025-05-30 06:00:11,906 - INFO - Request Parameters - Page 4:
2025-05-30 06:00:11,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:11,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:12,570 - INFO - Response - Page 4:
2025-05-30 06:00:12,770 - INFO - 第 4 页获取到 100 条记录
2025-05-30 06:00:12,770 - INFO - Request Parameters - Page 5:
2025-05-30 06:00:12,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:12,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:13,296 - INFO - Response - Page 5:
2025-05-30 06:00:13,496 - INFO - 第 5 页获取到 100 条记录
2025-05-30 06:00:13,496 - INFO - Request Parameters - Page 6:
2025-05-30 06:00:13,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:13,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:14,057 - INFO - Response - Page 6:
2025-05-30 06:00:14,257 - INFO - 第 6 页获取到 100 条记录
2025-05-30 06:00:14,257 - INFO - Request Parameters - Page 7:
2025-05-30 06:00:14,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:14,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:14,693 - INFO - Response - Page 7:
2025-05-30 06:00:14,893 - INFO - 第 7 页获取到 70 条记录
2025-05-30 06:00:14,893 - INFO - 查询完成，共获取到 670 条记录
2025-05-30 06:00:14,893 - INFO - 获取到 670 条表单数据
2025-05-30 06:00:14,905 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-30 06:00:14,917 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 06:00:14,917 - INFO - 开始处理日期: 2025-03
2025-05-30 06:00:14,917 - INFO - Request Parameters - Page 1:
2025-05-30 06:00:14,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:14,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:15,448 - INFO - Response - Page 1:
2025-05-30 06:00:15,649 - INFO - 第 1 页获取到 100 条记录
2025-05-30 06:00:15,649 - INFO - Request Parameters - Page 2:
2025-05-30 06:00:15,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:15,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:16,191 - INFO - Response - Page 2:
2025-05-30 06:00:16,391 - INFO - 第 2 页获取到 100 条记录
2025-05-30 06:00:16,391 - INFO - Request Parameters - Page 3:
2025-05-30 06:00:16,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:16,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:16,938 - INFO - Response - Page 3:
2025-05-30 06:00:17,138 - INFO - 第 3 页获取到 100 条记录
2025-05-30 06:00:17,138 - INFO - Request Parameters - Page 4:
2025-05-30 06:00:17,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:17,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:17,592 - INFO - Response - Page 4:
2025-05-30 06:00:17,792 - INFO - 第 4 页获取到 100 条记录
2025-05-30 06:00:17,792 - INFO - Request Parameters - Page 5:
2025-05-30 06:00:17,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:17,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:18,277 - INFO - Response - Page 5:
2025-05-30 06:00:18,477 - INFO - 第 5 页获取到 100 条记录
2025-05-30 06:00:18,477 - INFO - Request Parameters - Page 6:
2025-05-30 06:00:18,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:18,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:18,990 - INFO - Response - Page 6:
2025-05-30 06:00:19,191 - INFO - 第 6 页获取到 100 条记录
2025-05-30 06:00:19,191 - INFO - Request Parameters - Page 7:
2025-05-30 06:00:19,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:19,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:19,745 - INFO - Response - Page 7:
2025-05-30 06:00:19,945 - INFO - 第 7 页获取到 61 条记录
2025-05-30 06:00:19,945 - INFO - 查询完成，共获取到 661 条记录
2025-05-30 06:00:19,945 - INFO - 获取到 661 条表单数据
2025-05-30 06:00:19,957 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-30 06:00:19,969 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 06:00:19,969 - INFO - 开始处理日期: 2025-04
2025-05-30 06:00:19,970 - INFO - Request Parameters - Page 1:
2025-05-30 06:00:19,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:19,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:20,563 - INFO - Response - Page 1:
2025-05-30 06:00:20,764 - INFO - 第 1 页获取到 100 条记录
2025-05-30 06:00:20,764 - INFO - Request Parameters - Page 2:
2025-05-30 06:00:20,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:20,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:21,371 - INFO - Response - Page 2:
2025-05-30 06:00:21,571 - INFO - 第 2 页获取到 100 条记录
2025-05-30 06:00:21,571 - INFO - Request Parameters - Page 3:
2025-05-30 06:00:21,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:21,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:22,069 - INFO - Response - Page 3:
2025-05-30 06:00:22,269 - INFO - 第 3 页获取到 100 条记录
2025-05-30 06:00:22,269 - INFO - Request Parameters - Page 4:
2025-05-30 06:00:22,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:22,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:23,103 - INFO - Response - Page 4:
2025-05-30 06:00:23,304 - INFO - 第 4 页获取到 100 条记录
2025-05-30 06:00:23,304 - INFO - Request Parameters - Page 5:
2025-05-30 06:00:23,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:23,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:23,809 - INFO - Response - Page 5:
2025-05-30 06:00:24,009 - INFO - 第 5 页获取到 100 条记录
2025-05-30 06:00:24,009 - INFO - Request Parameters - Page 6:
2025-05-30 06:00:24,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:24,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:24,616 - INFO - Response - Page 6:
2025-05-30 06:00:24,817 - INFO - 第 6 页获取到 100 条记录
2025-05-30 06:00:24,817 - INFO - Request Parameters - Page 7:
2025-05-30 06:00:24,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:24,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:25,256 - INFO - Response - Page 7:
2025-05-30 06:00:25,456 - INFO - 第 7 页获取到 56 条记录
2025-05-30 06:00:25,456 - INFO - 查询完成，共获取到 656 条记录
2025-05-30 06:00:25,456 - INFO - 获取到 656 条表单数据
2025-05-30 06:00:25,469 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-30 06:00:25,481 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 06:00:25,481 - INFO - 开始处理日期: 2025-05
2025-05-30 06:00:25,481 - INFO - Request Parameters - Page 1:
2025-05-30 06:00:25,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:25,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:25,931 - INFO - Response - Page 1:
2025-05-30 06:00:26,131 - INFO - 第 1 页获取到 100 条记录
2025-05-30 06:00:26,131 - INFO - Request Parameters - Page 2:
2025-05-30 06:00:26,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:26,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:26,764 - INFO - Response - Page 2:
2025-05-30 06:00:26,964 - INFO - 第 2 页获取到 100 条记录
2025-05-30 06:00:26,964 - INFO - Request Parameters - Page 3:
2025-05-30 06:00:26,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:26,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:27,531 - INFO - Response - Page 3:
2025-05-30 06:00:27,732 - INFO - 第 3 页获取到 100 条记录
2025-05-30 06:00:27,732 - INFO - Request Parameters - Page 4:
2025-05-30 06:00:27,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:27,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:28,236 - INFO - Response - Page 4:
2025-05-30 06:00:28,437 - INFO - 第 4 页获取到 100 条记录
2025-05-30 06:00:28,437 - INFO - Request Parameters - Page 5:
2025-05-30 06:00:28,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:28,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:28,927 - INFO - Response - Page 5:
2025-05-30 06:00:29,127 - INFO - 第 5 页获取到 100 条记录
2025-05-30 06:00:29,127 - INFO - Request Parameters - Page 6:
2025-05-30 06:00:29,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:29,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:29,752 - INFO - Response - Page 6:
2025-05-30 06:00:29,952 - INFO - 第 6 页获取到 100 条记录
2025-05-30 06:00:29,952 - INFO - Request Parameters - Page 7:
2025-05-30 06:00:29,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 06:00:29,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 06:00:30,306 - INFO - Response - Page 7:
2025-05-30 06:00:30,506 - INFO - 第 7 页获取到 35 条记录
2025-05-30 06:00:30,506 - INFO - 查询完成，共获取到 635 条记录
2025-05-30 06:00:30,506 - INFO - 获取到 635 条表单数据
2025-05-30 06:00:30,518 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-30 06:00:30,526 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-30 06:00:30,999 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-30 06:00:30,999 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 227808.09, 'new_value': 234414.14}, {'field': 'offline_amount', 'old_value': 392946.92, 'new_value': 400946.92}, {'field': 'total_amount', 'old_value': 620755.01, 'new_value': 635361.06}, {'field': 'order_count', 'old_value': 1593, 'new_value': 1642}]
2025-05-30 06:00:31,003 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-30 06:00:31,003 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-30 06:00:31,004 - INFO - =================同步完成====================
2025-05-30 09:00:02,250 - INFO - =================使用默认全量同步=============
2025-05-30 09:00:03,767 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-30 09:00:03,768 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-30 09:00:03,796 - INFO - 开始处理日期: 2025-01
2025-05-30 09:00:03,799 - INFO - Request Parameters - Page 1:
2025-05-30 09:00:03,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:03,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:05,112 - INFO - Response - Page 1:
2025-05-30 09:00:05,312 - INFO - 第 1 页获取到 100 条记录
2025-05-30 09:00:05,312 - INFO - Request Parameters - Page 2:
2025-05-30 09:00:05,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:05,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:05,862 - INFO - Response - Page 2:
2025-05-30 09:00:06,062 - INFO - 第 2 页获取到 100 条记录
2025-05-30 09:00:06,062 - INFO - Request Parameters - Page 3:
2025-05-30 09:00:06,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:06,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:06,593 - INFO - Response - Page 3:
2025-05-30 09:00:06,794 - INFO - 第 3 页获取到 100 条记录
2025-05-30 09:00:06,794 - INFO - Request Parameters - Page 4:
2025-05-30 09:00:06,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:06,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:07,283 - INFO - Response - Page 4:
2025-05-30 09:00:07,483 - INFO - 第 4 页获取到 100 条记录
2025-05-30 09:00:07,483 - INFO - Request Parameters - Page 5:
2025-05-30 09:00:07,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:07,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:07,995 - INFO - Response - Page 5:
2025-05-30 09:00:08,196 - INFO - 第 5 页获取到 100 条记录
2025-05-30 09:00:08,196 - INFO - Request Parameters - Page 6:
2025-05-30 09:00:08,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:08,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:08,770 - INFO - Response - Page 6:
2025-05-30 09:00:08,970 - INFO - 第 6 页获取到 100 条记录
2025-05-30 09:00:08,970 - INFO - Request Parameters - Page 7:
2025-05-30 09:00:08,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:08,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:09,457 - INFO - Response - Page 7:
2025-05-30 09:00:09,659 - INFO - 第 7 页获取到 82 条记录
2025-05-30 09:00:09,659 - INFO - 查询完成，共获取到 682 条记录
2025-05-30 09:00:09,659 - INFO - 获取到 682 条表单数据
2025-05-30 09:00:09,670 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-30 09:00:09,681 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 09:00:09,682 - INFO - 开始处理日期: 2025-02
2025-05-30 09:00:09,682 - INFO - Request Parameters - Page 1:
2025-05-30 09:00:09,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:09,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:10,210 - INFO - Response - Page 1:
2025-05-30 09:00:10,412 - INFO - 第 1 页获取到 100 条记录
2025-05-30 09:00:10,412 - INFO - Request Parameters - Page 2:
2025-05-30 09:00:10,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:10,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:10,941 - INFO - Response - Page 2:
2025-05-30 09:00:11,141 - INFO - 第 2 页获取到 100 条记录
2025-05-30 09:00:11,141 - INFO - Request Parameters - Page 3:
2025-05-30 09:00:11,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:11,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:11,647 - INFO - Response - Page 3:
2025-05-30 09:00:11,848 - INFO - 第 3 页获取到 100 条记录
2025-05-30 09:00:11,848 - INFO - Request Parameters - Page 4:
2025-05-30 09:00:11,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:11,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:12,295 - INFO - Response - Page 4:
2025-05-30 09:00:12,495 - INFO - 第 4 页获取到 100 条记录
2025-05-30 09:00:12,495 - INFO - Request Parameters - Page 5:
2025-05-30 09:00:12,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:12,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:13,019 - INFO - Response - Page 5:
2025-05-30 09:00:13,219 - INFO - 第 5 页获取到 100 条记录
2025-05-30 09:00:13,219 - INFO - Request Parameters - Page 6:
2025-05-30 09:00:13,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:13,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:13,727 - INFO - Response - Page 6:
2025-05-30 09:00:13,928 - INFO - 第 6 页获取到 100 条记录
2025-05-30 09:00:13,928 - INFO - Request Parameters - Page 7:
2025-05-30 09:00:13,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:13,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:14,425 - INFO - Response - Page 7:
2025-05-30 09:00:14,625 - INFO - 第 7 页获取到 70 条记录
2025-05-30 09:00:14,625 - INFO - 查询完成，共获取到 670 条记录
2025-05-30 09:00:14,626 - INFO - 获取到 670 条表单数据
2025-05-30 09:00:14,639 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-30 09:00:14,651 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 09:00:14,651 - INFO - 开始处理日期: 2025-03
2025-05-30 09:00:14,651 - INFO - Request Parameters - Page 1:
2025-05-30 09:00:14,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:14,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:15,111 - INFO - Response - Page 1:
2025-05-30 09:00:15,311 - INFO - 第 1 页获取到 100 条记录
2025-05-30 09:00:15,311 - INFO - Request Parameters - Page 2:
2025-05-30 09:00:15,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:15,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:15,857 - INFO - Response - Page 2:
2025-05-30 09:00:16,057 - INFO - 第 2 页获取到 100 条记录
2025-05-30 09:00:16,057 - INFO - Request Parameters - Page 3:
2025-05-30 09:00:16,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:16,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:16,540 - INFO - Response - Page 3:
2025-05-30 09:00:16,742 - INFO - 第 3 页获取到 100 条记录
2025-05-30 09:00:16,742 - INFO - Request Parameters - Page 4:
2025-05-30 09:00:16,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:16,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:17,204 - INFO - Response - Page 4:
2025-05-30 09:00:17,405 - INFO - 第 4 页获取到 100 条记录
2025-05-30 09:00:17,405 - INFO - Request Parameters - Page 5:
2025-05-30 09:00:17,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:17,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:18,022 - INFO - Response - Page 5:
2025-05-30 09:00:18,223 - INFO - 第 5 页获取到 100 条记录
2025-05-30 09:00:18,223 - INFO - Request Parameters - Page 6:
2025-05-30 09:00:18,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:18,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:18,846 - INFO - Response - Page 6:
2025-05-30 09:00:19,046 - INFO - 第 6 页获取到 100 条记录
2025-05-30 09:00:19,046 - INFO - Request Parameters - Page 7:
2025-05-30 09:00:19,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:19,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:19,437 - INFO - Response - Page 7:
2025-05-30 09:00:19,637 - INFO - 第 7 页获取到 61 条记录
2025-05-30 09:00:19,637 - INFO - 查询完成，共获取到 661 条记录
2025-05-30 09:00:19,637 - INFO - 获取到 661 条表单数据
2025-05-30 09:00:19,649 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-30 09:00:19,660 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 09:00:19,660 - INFO - 开始处理日期: 2025-04
2025-05-30 09:00:19,660 - INFO - Request Parameters - Page 1:
2025-05-30 09:00:19,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:19,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:20,225 - INFO - Response - Page 1:
2025-05-30 09:00:20,425 - INFO - 第 1 页获取到 100 条记录
2025-05-30 09:00:20,425 - INFO - Request Parameters - Page 2:
2025-05-30 09:00:20,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:20,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:20,942 - INFO - Response - Page 2:
2025-05-30 09:00:21,142 - INFO - 第 2 页获取到 100 条记录
2025-05-30 09:00:21,142 - INFO - Request Parameters - Page 3:
2025-05-30 09:00:21,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:21,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:21,742 - INFO - Response - Page 3:
2025-05-30 09:00:21,942 - INFO - 第 3 页获取到 100 条记录
2025-05-30 09:00:21,942 - INFO - Request Parameters - Page 4:
2025-05-30 09:00:21,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:21,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:22,409 - INFO - Response - Page 4:
2025-05-30 09:00:22,610 - INFO - 第 4 页获取到 100 条记录
2025-05-30 09:00:22,610 - INFO - Request Parameters - Page 5:
2025-05-30 09:00:22,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:22,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:23,081 - INFO - Response - Page 5:
2025-05-30 09:00:23,283 - INFO - 第 5 页获取到 100 条记录
2025-05-30 09:00:23,283 - INFO - Request Parameters - Page 6:
2025-05-30 09:00:23,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:23,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:23,742 - INFO - Response - Page 6:
2025-05-30 09:00:23,943 - INFO - 第 6 页获取到 100 条记录
2025-05-30 09:00:23,943 - INFO - Request Parameters - Page 7:
2025-05-30 09:00:23,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:23,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:24,370 - INFO - Response - Page 7:
2025-05-30 09:00:24,571 - INFO - 第 7 页获取到 56 条记录
2025-05-30 09:00:24,571 - INFO - 查询完成，共获取到 656 条记录
2025-05-30 09:00:24,571 - INFO - 获取到 656 条表单数据
2025-05-30 09:00:24,582 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-30 09:00:24,594 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 09:00:24,594 - INFO - 开始处理日期: 2025-05
2025-05-30 09:00:24,594 - INFO - Request Parameters - Page 1:
2025-05-30 09:00:24,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:24,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:25,103 - INFO - Response - Page 1:
2025-05-30 09:00:25,304 - INFO - 第 1 页获取到 100 条记录
2025-05-30 09:00:25,304 - INFO - Request Parameters - Page 2:
2025-05-30 09:00:25,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:25,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:25,871 - INFO - Response - Page 2:
2025-05-30 09:00:26,073 - INFO - 第 2 页获取到 100 条记录
2025-05-30 09:00:26,073 - INFO - Request Parameters - Page 3:
2025-05-30 09:00:26,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:26,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:26,554 - INFO - Response - Page 3:
2025-05-30 09:00:26,755 - INFO - 第 3 页获取到 100 条记录
2025-05-30 09:00:26,755 - INFO - Request Parameters - Page 4:
2025-05-30 09:00:26,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:26,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:27,239 - INFO - Response - Page 4:
2025-05-30 09:00:27,439 - INFO - 第 4 页获取到 100 条记录
2025-05-30 09:00:27,439 - INFO - Request Parameters - Page 5:
2025-05-30 09:00:27,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:27,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:27,945 - INFO - Response - Page 5:
2025-05-30 09:00:28,145 - INFO - 第 5 页获取到 100 条记录
2025-05-30 09:00:28,145 - INFO - Request Parameters - Page 6:
2025-05-30 09:00:28,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:28,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:28,698 - INFO - Response - Page 6:
2025-05-30 09:00:28,898 - INFO - 第 6 页获取到 100 条记录
2025-05-30 09:00:28,898 - INFO - Request Parameters - Page 7:
2025-05-30 09:00:28,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 09:00:28,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 09:00:29,305 - INFO - Response - Page 7:
2025-05-30 09:00:29,505 - INFO - 第 7 页获取到 35 条记录
2025-05-30 09:00:29,505 - INFO - 查询完成，共获取到 635 条记录
2025-05-30 09:00:29,505 - INFO - 获取到 635 条表单数据
2025-05-30 09:00:29,517 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-30 09:00:29,517 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-30 09:00:29,937 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-30 09:00:29,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 320000.0, 'new_value': 323200.0}, {'field': 'total_amount', 'old_value': 320000.0, 'new_value': 323200.0}]
2025-05-30 09:00:29,938 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-30 09:00:30,412 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-30 09:00:30,412 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2277.0, 'new_value': 2427.0}, {'field': 'offline_amount', 'old_value': 46303.0, 'new_value': 47143.0}, {'field': 'total_amount', 'old_value': 48580.0, 'new_value': 49570.0}, {'field': 'order_count', 'old_value': 670, 'new_value': 684}]
2025-05-30 09:00:30,412 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-30 09:00:30,901 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-30 09:00:30,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 411885.0, 'new_value': 419865.0}, {'field': 'total_amount', 'old_value': 411885.0, 'new_value': 419865.0}, {'field': 'order_count', 'old_value': 307, 'new_value': 313}]
2025-05-30 09:00:30,901 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC
2025-05-30 09:00:31,369 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC
2025-05-30 09:00:31,369 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113270.0, 'new_value': 126395.0}, {'field': 'total_amount', 'old_value': 113270.0, 'new_value': 126395.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-05-30 09:00:31,369 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-30 09:00:31,863 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-30 09:00:31,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 433040.0, 'new_value': 461340.0}, {'field': 'total_amount', 'old_value': 433040.0, 'new_value': 461340.0}, {'field': 'order_count', 'old_value': 266, 'new_value': 282}]
2025-05-30 09:00:31,864 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-30 09:00:32,314 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-30 09:00:32,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51102.0, 'new_value': 52101.0}, {'field': 'total_amount', 'old_value': 51102.0, 'new_value': 52101.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-30 09:00:32,314 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-30 09:00:32,790 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-30 09:00:32,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 295049.0, 'new_value': 295317.0}, {'field': 'total_amount', 'old_value': 295049.0, 'new_value': 295317.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 59}]
2025-05-30 09:00:32,790 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-30 09:00:33,233 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-30 09:00:33,233 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64327.68, 'new_value': 66371.43}, {'field': 'offline_amount', 'old_value': 127063.35, 'new_value': 131117.35}, {'field': 'total_amount', 'old_value': 191391.03, 'new_value': 197488.78}, {'field': 'order_count', 'old_value': 2197, 'new_value': 2266}]
2025-05-30 09:00:33,233 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-30 09:00:33,706 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-30 09:00:33,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 322205.1, 'new_value': 324586.3}, {'field': 'total_amount', 'old_value': 437224.8, 'new_value': 439606.0}, {'field': 'order_count', 'old_value': 3676, 'new_value': 3750}]
2025-05-30 09:00:33,707 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-30 09:00:34,161 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-30 09:00:34,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117172.3, 'new_value': 121035.4}, {'field': 'total_amount', 'old_value': 117172.3, 'new_value': 121035.4}, {'field': 'order_count', 'old_value': 6460, 'new_value': 6687}]
2025-05-30 09:00:34,162 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-30 09:00:34,593 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-30 09:00:34,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157508.24, 'new_value': 163736.11}, {'field': 'total_amount', 'old_value': 157508.24, 'new_value': 163736.11}, {'field': 'order_count', 'old_value': 1865, 'new_value': 1928}]
2025-05-30 09:00:34,594 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-30 09:00:35,044 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-30 09:00:35,044 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6572.0, 'new_value': 6665.0}, {'field': 'total_amount', 'old_value': 44322.0, 'new_value': 44415.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 83}]
2025-05-30 09:00:35,044 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-30 09:00:35,484 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-30 09:00:35,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191895.0, 'new_value': 195517.0}, {'field': 'total_amount', 'old_value': 191895.0, 'new_value': 195517.0}, {'field': 'order_count', 'old_value': 399, 'new_value': 405}]
2025-05-30 09:00:35,484 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-30 09:00:35,943 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-30 09:00:35,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88223.0, 'new_value': 91575.0}, {'field': 'total_amount', 'old_value': 88223.0, 'new_value': 91575.0}, {'field': 'order_count', 'old_value': 752, 'new_value': 779}]
2025-05-30 09:00:35,943 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-30 09:00:36,374 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-30 09:00:36,374 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 192911.75, 'new_value': 195644.83}, {'field': 'offline_amount', 'old_value': 32250.41, 'new_value': 32547.38}, {'field': 'total_amount', 'old_value': 225162.16, 'new_value': 228192.21}, {'field': 'order_count', 'old_value': 833, 'new_value': 846}]
2025-05-30 09:00:36,374 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-30 09:00:36,881 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-30 09:00:36,882 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 181142.0, 'new_value': 187361.0}, {'field': 'offline_amount', 'old_value': 68110.91, 'new_value': 70738.65}, {'field': 'total_amount', 'old_value': 249252.91, 'new_value': 258099.65}, {'field': 'order_count', 'old_value': 1632, 'new_value': 1697}]
2025-05-30 09:00:36,882 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-30 09:00:37,368 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-30 09:00:37,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10045.0, 'new_value': 10447.0}, {'field': 'total_amount', 'old_value': 11538.0, 'new_value': 11940.0}, {'field': 'order_count', 'old_value': 235, 'new_value': 247}]
2025-05-30 09:00:37,368 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-30 09:00:37,834 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-30 09:00:37,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102745.19, 'new_value': 106694.27}, {'field': 'total_amount', 'old_value': 102745.19, 'new_value': 106694.27}, {'field': 'order_count', 'old_value': 2787, 'new_value': 2908}]
2025-05-30 09:00:37,835 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-30 09:00:38,280 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-30 09:00:38,280 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240436.3, 'new_value': 246405.3}, {'field': 'total_amount', 'old_value': 240436.3, 'new_value': 246405.3}, {'field': 'order_count', 'old_value': 70, 'new_value': 73}]
2025-05-30 09:00:38,280 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-30 09:00:38,676 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-30 09:00:38,677 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 174027.0, 'new_value': 178261.0}, {'field': 'offline_amount', 'old_value': 77219.18, 'new_value': 78310.18}, {'field': 'total_amount', 'old_value': 251246.18, 'new_value': 256571.18}, {'field': 'order_count', 'old_value': 1769, 'new_value': 1817}]
2025-05-30 09:00:38,677 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-30 09:00:39,096 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-30 09:00:39,096 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12267.62, 'new_value': 12809.12}, {'field': 'offline_amount', 'old_value': 194299.48, 'new_value': 199883.8}, {'field': 'total_amount', 'old_value': 206567.1, 'new_value': 212692.92}, {'field': 'order_count', 'old_value': 2274, 'new_value': 2346}]
2025-05-30 09:00:39,097 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-30 09:00:39,588 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-30 09:00:39,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35211.0, 'new_value': 35746.0}, {'field': 'total_amount', 'old_value': 36911.0, 'new_value': 37446.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 140}]
2025-05-30 09:00:39,588 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-30 09:00:40,091 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-30 09:00:40,091 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7879.7, 'new_value': 8051.83}, {'field': 'offline_amount', 'old_value': 115934.24, 'new_value': 118936.62}, {'field': 'total_amount', 'old_value': 123813.94, 'new_value': 126988.45}, {'field': 'order_count', 'old_value': 3004, 'new_value': 3093}]
2025-05-30 09:00:40,091 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-30 09:00:40,529 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-30 09:00:40,529 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26123.47, 'new_value': 26845.09}, {'field': 'total_amount', 'old_value': 26123.47, 'new_value': 26845.09}, {'field': 'order_count', 'old_value': 157, 'new_value': 162}]
2025-05-30 09:00:40,530 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-30 09:00:40,959 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-30 09:00:40,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190825.0, 'new_value': 197458.0}, {'field': 'total_amount', 'old_value': 190825.0, 'new_value': 197458.0}, {'field': 'order_count', 'old_value': 7194, 'new_value': 7460}]
2025-05-30 09:00:40,959 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-30 09:00:41,405 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-30 09:00:41,405 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156005.23, 'new_value': 162625.23}, {'field': 'total_amount', 'old_value': 156005.23, 'new_value': 162625.23}, {'field': 'order_count', 'old_value': 5673, 'new_value': 5921}]
2025-05-30 09:00:41,405 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-30 09:00:41,857 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-30 09:00:41,857 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38116.59, 'new_value': 42815.59}, {'field': 'total_amount', 'old_value': 71684.81, 'new_value': 76383.81}, {'field': 'order_count', 'old_value': 69, 'new_value': 72}]
2025-05-30 09:00:41,858 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-30 09:00:42,329 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-30 09:00:42,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22275.76, 'new_value': 22557.16}, {'field': 'total_amount', 'old_value': 22275.76, 'new_value': 22557.16}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-30 09:00:42,330 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-30 09:00:42,804 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-30 09:00:42,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52908.0, 'new_value': 53319.0}, {'field': 'total_amount', 'old_value': 52908.0, 'new_value': 53319.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 117}]
2025-05-30 09:00:42,805 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-30 09:00:43,204 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-30 09:00:43,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 150945.83, 'new_value': 153147.22}, {'field': 'offline_amount', 'old_value': 581378.17, 'new_value': 592049.17}, {'field': 'total_amount', 'old_value': 732324.0, 'new_value': 745196.39}, {'field': 'order_count', 'old_value': 943, 'new_value': 966}]
2025-05-30 09:00:43,204 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-30 09:00:43,709 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-30 09:00:43,710 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7295.85, 'new_value': 7489.74}, {'field': 'offline_amount', 'old_value': 225822.35, 'new_value': 231636.34}, {'field': 'total_amount', 'old_value': 233118.2, 'new_value': 239126.08}, {'field': 'order_count', 'old_value': 1462, 'new_value': 1503}]
2025-05-30 09:00:43,710 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-30 09:00:44,221 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-30 09:00:44,221 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1868559.78, 'new_value': 1905867.78}, {'field': 'total_amount', 'old_value': 1922004.88, 'new_value': 1959312.88}, {'field': 'order_count', 'old_value': 3513, 'new_value': 3613}]
2025-05-30 09:00:44,222 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-30 09:00:44,699 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-30 09:00:44,699 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71395.0, 'new_value': 74397.0}, {'field': 'total_amount', 'old_value': 100041.0, 'new_value': 103043.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 47}]
2025-05-30 09:00:44,699 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-30 09:00:45,143 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-30 09:00:45,144 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21469.0, 'new_value': 21585.0}, {'field': 'total_amount', 'old_value': 21469.0, 'new_value': 21585.0}, {'field': 'order_count', 'old_value': 370, 'new_value': 372}]
2025-05-30 09:00:45,144 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-30 09:00:45,591 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-30 09:00:45,591 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9602.75, 'new_value': 9987.95}, {'field': 'offline_amount', 'old_value': 19182.24, 'new_value': 20047.41}, {'field': 'total_amount', 'old_value': 28784.99, 'new_value': 30035.36}, {'field': 'order_count', 'old_value': 964, 'new_value': 1009}]
2025-05-30 09:00:45,591 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-30 09:00:46,076 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-30 09:00:46,076 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 201663.79, 'new_value': 207474.36}, {'field': 'offline_amount', 'old_value': 163691.31, 'new_value': 167761.81}, {'field': 'total_amount', 'old_value': 365355.1, 'new_value': 375236.17}, {'field': 'order_count', 'old_value': 3323, 'new_value': 3415}]
2025-05-30 09:00:46,077 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-30 09:00:46,654 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-30 09:00:46,654 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 293277.47, 'new_value': 298621.37}, {'field': 'total_amount', 'old_value': 293300.57, 'new_value': 298644.47}, {'field': 'order_count', 'old_value': 59, 'new_value': 61}]
2025-05-30 09:00:46,654 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-30 09:00:47,114 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-30 09:00:47,114 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 411281.9, 'new_value': 422992.9}, {'field': 'offline_amount', 'old_value': 115608.1, 'new_value': 118632.1}, {'field': 'total_amount', 'old_value': 526890.0, 'new_value': 541625.0}, {'field': 'order_count', 'old_value': 664, 'new_value': 685}]
2025-05-30 09:00:47,115 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-30 09:00:47,490 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-30 09:00:47,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31348.0, 'new_value': 31797.0}, {'field': 'total_amount', 'old_value': 31348.0, 'new_value': 31797.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 86}]
2025-05-30 09:00:47,491 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-30 09:00:47,994 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-30 09:00:47,994 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91587.7, 'new_value': 95587.7}, {'field': 'offline_amount', 'old_value': 11066.55, 'new_value': 11583.65}, {'field': 'total_amount', 'old_value': 102654.25, 'new_value': 107171.35}, {'field': 'order_count', 'old_value': 316, 'new_value': 334}]
2025-05-30 09:00:47,995 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-30 09:00:48,493 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-30 09:00:48,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77349.51, 'new_value': 78783.61}, {'field': 'total_amount', 'old_value': 77349.51, 'new_value': 78783.61}, {'field': 'order_count', 'old_value': 2228, 'new_value': 2285}]
2025-05-30 09:00:48,493 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-30 09:00:48,948 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-30 09:00:48,948 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 474202.25, 'new_value': 478784.13}, {'field': 'total_amount', 'old_value': 474202.25, 'new_value': 478784.13}, {'field': 'order_count', 'old_value': 589, 'new_value': 606}]
2025-05-30 09:00:48,948 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-30 09:00:49,456 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-30 09:00:49,456 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19129.71, 'new_value': 19979.43}, {'field': 'offline_amount', 'old_value': 491958.98, 'new_value': 503831.58}, {'field': 'total_amount', 'old_value': 511088.69, 'new_value': 523811.01}, {'field': 'order_count', 'old_value': 2053, 'new_value': 2108}]
2025-05-30 09:00:49,456 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-30 09:00:49,935 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-30 09:00:49,935 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94644.0, 'new_value': 96975.0}, {'field': 'offline_amount', 'old_value': 95668.23, 'new_value': 96942.23}, {'field': 'total_amount', 'old_value': 190312.23, 'new_value': 193917.23}, {'field': 'order_count', 'old_value': 222, 'new_value': 229}]
2025-05-30 09:00:49,936 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-30 09:00:50,423 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-30 09:00:50,423 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105094.12, 'new_value': 108121.12}, {'field': 'offline_amount', 'old_value': 1306896.37, 'new_value': 1359162.22}, {'field': 'total_amount', 'old_value': 1411990.49, 'new_value': 1467283.34}, {'field': 'order_count', 'old_value': 11398, 'new_value': 11837}]
2025-05-30 09:00:50,424 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-30 09:00:50,881 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-30 09:00:50,882 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88817.59, 'new_value': 90236.59}, {'field': 'total_amount', 'old_value': 88817.59, 'new_value': 90236.59}, {'field': 'order_count', 'old_value': 506, 'new_value': 515}]
2025-05-30 09:00:50,882 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-30 09:00:51,321 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-30 09:00:51,321 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9045.48, 'new_value': 9546.54}, {'field': 'offline_amount', 'old_value': 33027.0, 'new_value': 36872.0}, {'field': 'total_amount', 'old_value': 42072.48, 'new_value': 46418.54}, {'field': 'order_count', 'old_value': 228, 'new_value': 238}]
2025-05-30 09:00:51,322 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-30 09:00:51,731 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-30 09:00:51,731 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48361.86, 'new_value': 52546.67}, {'field': 'total_amount', 'old_value': 48883.46, 'new_value': 53068.27}, {'field': 'order_count', 'old_value': 429, 'new_value': 446}]
2025-05-30 09:00:51,731 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-30 09:00:52,194 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-30 09:00:52,194 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6545.0, 'new_value': 6739.0}, {'field': 'offline_amount', 'old_value': 29444.8, 'new_value': 30693.6}, {'field': 'total_amount', 'old_value': 35989.8, 'new_value': 37432.6}, {'field': 'order_count', 'old_value': 1382, 'new_value': 1432}]
2025-05-30 09:00:52,194 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-30 09:00:52,629 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-30 09:00:52,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94714.26, 'new_value': 96835.36}, {'field': 'total_amount', 'old_value': 94714.26, 'new_value': 96835.36}, {'field': 'order_count', 'old_value': 358, 'new_value': 367}]
2025-05-30 09:00:52,630 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-30 09:00:53,124 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-30 09:00:53,124 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31086.0, 'new_value': 31998.5}, {'field': 'offline_amount', 'old_value': 23601.5, 'new_value': 24010.5}, {'field': 'total_amount', 'old_value': 54687.5, 'new_value': 56009.0}, {'field': 'order_count', 'old_value': 297, 'new_value': 304}]
2025-05-30 09:00:53,124 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-30 09:00:53,638 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-30 09:00:53,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 206942.3, 'new_value': 214936.0}, {'field': 'total_amount', 'old_value': 206942.3, 'new_value': 214936.0}, {'field': 'order_count', 'old_value': 774, 'new_value': 796}]
2025-05-30 09:00:53,638 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-30 09:00:54,112 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-30 09:00:54,112 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 317741.0, 'new_value': 320374.0}, {'field': 'total_amount', 'old_value': 317741.0, 'new_value': 320374.0}, {'field': 'order_count', 'old_value': 267, 'new_value': 270}]
2025-05-30 09:00:54,113 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-30 09:00:54,570 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-30 09:00:54,570 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18740.79, 'new_value': 19963.45}, {'field': 'offline_amount', 'old_value': 302773.24, 'new_value': 314500.24}, {'field': 'total_amount', 'old_value': 321514.03, 'new_value': 334463.69}, {'field': 'order_count', 'old_value': 17924, 'new_value': 18604}]
2025-05-30 09:00:54,570 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-30 09:00:55,061 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-30 09:00:55,061 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55253.34, 'new_value': 57176.6}, {'field': 'offline_amount', 'old_value': 38151.0, 'new_value': 39656.0}, {'field': 'total_amount', 'old_value': 93404.34, 'new_value': 96832.6}, {'field': 'order_count', 'old_value': 1163, 'new_value': 1203}]
2025-05-30 09:00:55,062 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-30 09:00:55,560 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-30 09:00:55,560 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165948.42, 'new_value': 169373.02}, {'field': 'total_amount', 'old_value': 165948.42, 'new_value': 169373.02}, {'field': 'order_count', 'old_value': 827, 'new_value': 845}]
2025-05-30 09:00:55,561 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-30 09:00:56,022 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-30 09:00:56,022 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26003.92, 'new_value': 26843.11}, {'field': 'offline_amount', 'old_value': 48382.08, 'new_value': 50188.08}, {'field': 'total_amount', 'old_value': 74386.0, 'new_value': 77031.19}, {'field': 'order_count', 'old_value': 2685, 'new_value': 2780}]
2025-05-30 09:00:56,022 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-30 09:00:56,503 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-30 09:00:56,504 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83777.0, 'new_value': 86379.0}, {'field': 'total_amount', 'old_value': 86185.0, 'new_value': 88787.0}, {'field': 'order_count', 'old_value': 356, 'new_value': 367}]
2025-05-30 09:00:56,504 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-30 09:00:56,939 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-30 09:00:56,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67469.04, 'new_value': 68635.04}, {'field': 'total_amount', 'old_value': 88518.24, 'new_value': 89684.24}, {'field': 'order_count', 'old_value': 993, 'new_value': 1015}]
2025-05-30 09:00:56,940 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-30 09:00:57,453 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-30 09:00:57,453 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108444.4, 'new_value': 110960.7}, {'field': 'offline_amount', 'old_value': 154547.3, 'new_value': 159274.3}, {'field': 'total_amount', 'old_value': 262991.7, 'new_value': 270235.0}, {'field': 'order_count', 'old_value': 1669, 'new_value': 1705}]
2025-05-30 09:00:57,454 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-30 09:00:57,976 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-30 09:00:57,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26587.7, 'new_value': 28002.01}, {'field': 'offline_amount', 'old_value': 48366.69, 'new_value': 50055.08}, {'field': 'total_amount', 'old_value': 74954.39, 'new_value': 78057.09}, {'field': 'order_count', 'old_value': 3936, 'new_value': 4105}]
2025-05-30 09:00:57,977 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-30 09:00:58,370 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-30 09:00:58,370 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58161.0, 'new_value': 59213.0}, {'field': 'total_amount', 'old_value': 58510.0, 'new_value': 59562.0}, {'field': 'order_count', 'old_value': 107, 'new_value': 111}]
2025-05-30 09:00:58,370 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-30 09:00:58,855 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-30 09:00:58,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34713.0, 'new_value': 36340.0}, {'field': 'total_amount', 'old_value': 34713.0, 'new_value': 36340.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-30 09:00:58,856 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-30 09:00:59,377 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-30 09:00:59,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131182.33, 'new_value': 134031.33}, {'field': 'total_amount', 'old_value': 131182.33, 'new_value': 134031.33}, {'field': 'order_count', 'old_value': 3831, 'new_value': 3928}]
2025-05-30 09:00:59,378 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-30 09:00:59,804 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-30 09:00:59,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 294453.39, 'new_value': 307892.49}, {'field': 'total_amount', 'old_value': 432921.73, 'new_value': 446360.83}, {'field': 'order_count', 'old_value': 5087, 'new_value': 5234}]
2025-05-30 09:00:59,804 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-30 09:01:00,349 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-30 09:01:00,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37333.48, 'new_value': 41920.08}, {'field': 'total_amount', 'old_value': 37333.48, 'new_value': 41920.08}, {'field': 'order_count', 'old_value': 37, 'new_value': 40}]
2025-05-30 09:01:00,349 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-30 09:01:00,829 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-30 09:01:00,829 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 251532.0, 'new_value': 261552.0}, {'field': 'total_amount', 'old_value': 251532.0, 'new_value': 261552.0}, {'field': 'order_count', 'old_value': 20961, 'new_value': 21796}]
2025-05-30 09:01:00,829 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-30 09:01:01,390 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-30 09:01:01,390 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15760.2, 'new_value': 26784.2}, {'field': 'total_amount', 'old_value': 15760.2, 'new_value': 26784.2}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-30 09:01:01,391 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-30 09:01:01,845 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-30 09:01:01,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50071.7, 'new_value': 52013.1}, {'field': 'total_amount', 'old_value': 50071.7, 'new_value': 52013.1}, {'field': 'order_count', 'old_value': 2247, 'new_value': 2326}]
2025-05-30 09:01:01,845 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-30 09:01:02,245 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-30 09:01:02,246 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 143085.2, 'new_value': 155841.7}, {'field': 'total_amount', 'old_value': 274549.55, 'new_value': 287306.05}, {'field': 'order_count', 'old_value': 7333, 'new_value': 7670}]
2025-05-30 09:01:02,246 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGO
2025-05-30 09:01:02,698 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGO
2025-05-30 09:01:02,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27584.0, 'new_value': 34264.0}, {'field': 'total_amount', 'old_value': 27584.0, 'new_value': 34264.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-30 09:01:02,698 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-30 09:01:03,157 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-30 09:01:03,157 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63886.68, 'new_value': 66008.24}, {'field': 'offline_amount', 'old_value': 37251.34, 'new_value': 39222.8}, {'field': 'total_amount', 'old_value': 101138.02, 'new_value': 105231.04}, {'field': 'order_count', 'old_value': 5498, 'new_value': 5703}]
2025-05-30 09:01:03,158 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-30 09:01:03,568 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-30 09:01:03,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23060.08, 'new_value': 23428.08}, {'field': 'total_amount', 'old_value': 23060.08, 'new_value': 23428.08}, {'field': 'order_count', 'old_value': 194, 'new_value': 201}]
2025-05-30 09:01:03,569 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-30 09:01:04,003 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-30 09:01:04,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 458020.59, 'new_value': 466559.54}, {'field': 'total_amount', 'old_value': 458020.59, 'new_value': 466559.54}, {'field': 'order_count', 'old_value': 1630, 'new_value': 1665}]
2025-05-30 09:01:04,004 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-30 09:01:04,437 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-30 09:01:04,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 348109.5, 'new_value': 357376.8}, {'field': 'total_amount', 'old_value': 359385.7, 'new_value': 368653.0}, {'field': 'order_count', 'old_value': 8839, 'new_value': 9188}]
2025-05-30 09:01:04,437 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-30 09:01:04,857 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-30 09:01:04,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44369.68, 'new_value': 45878.95}, {'field': 'total_amount', 'old_value': 44369.68, 'new_value': 45878.95}, {'field': 'order_count', 'old_value': 5731, 'new_value': 5937}]
2025-05-30 09:01:04,857 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-30 09:01:05,366 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-30 09:01:05,366 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28866.61, 'new_value': 29769.08}, {'field': 'offline_amount', 'old_value': 36361.64, 'new_value': 37695.15}, {'field': 'total_amount', 'old_value': 65228.25, 'new_value': 67464.23}, {'field': 'order_count', 'old_value': 2957, 'new_value': 3058}]
2025-05-30 09:01:05,366 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-30 09:01:05,788 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-30 09:01:05,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102156.0, 'new_value': 106311.0}, {'field': 'total_amount', 'old_value': 107357.0, 'new_value': 111512.0}, {'field': 'order_count', 'old_value': 311, 'new_value': 323}]
2025-05-30 09:01:05,789 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-30 09:01:06,329 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-30 09:01:06,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 320265.1, 'new_value': 321171.1}, {'field': 'total_amount', 'old_value': 320265.1, 'new_value': 321171.1}, {'field': 'order_count', 'old_value': 81, 'new_value': 84}]
2025-05-30 09:01:06,329 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-30 09:01:06,790 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-30 09:01:06,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 504551.48, 'new_value': 522410.48}, {'field': 'total_amount', 'old_value': 504551.48, 'new_value': 522410.48}, {'field': 'order_count', 'old_value': 2583, 'new_value': 2692}]
2025-05-30 09:01:06,790 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-30 09:01:07,222 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-30 09:01:07,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144745.0, 'new_value': 145904.0}, {'field': 'total_amount', 'old_value': 144745.0, 'new_value': 145904.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 87}]
2025-05-30 09:01:07,222 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-30 09:01:07,694 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-30 09:01:07,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 197850.0, 'new_value': 199430.0}, {'field': 'total_amount', 'old_value': 197851.0, 'new_value': 199431.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-30 09:01:07,694 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-30 09:01:08,148 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-30 09:01:08,148 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47684.36, 'new_value': 48739.42}, {'field': 'offline_amount', 'old_value': 59481.41, 'new_value': 59513.27}, {'field': 'total_amount', 'old_value': 107165.77, 'new_value': 108252.69}, {'field': 'order_count', 'old_value': 374, 'new_value': 379}]
2025-05-30 09:01:08,148 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-30 09:01:08,613 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-30 09:01:08,613 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 342049.92, 'new_value': 350721.92}, {'field': 'total_amount', 'old_value': 354770.42, 'new_value': 363442.42}, {'field': 'order_count', 'old_value': 3077, 'new_value': 3131}]
2025-05-30 09:01:08,613 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-30 09:01:09,030 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-30 09:01:09,030 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23505.0, 'new_value': 24337.0}, {'field': 'total_amount', 'old_value': 23505.0, 'new_value': 24337.0}, {'field': 'order_count', 'old_value': 130, 'new_value': 135}]
2025-05-30 09:01:09,030 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-30 09:01:09,481 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-30 09:01:09,481 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30500.4, 'new_value': 31085.8}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 22.5}, {'field': 'total_amount', 'old_value': 30500.4, 'new_value': 31108.3}, {'field': 'order_count', 'old_value': 842, 'new_value': 871}]
2025-05-30 09:01:09,481 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-30 09:01:09,921 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-30 09:01:09,921 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7305.0, 'new_value': 7679.0}, {'field': 'offline_amount', 'old_value': 54766.89, 'new_value': 55716.49}, {'field': 'total_amount', 'old_value': 62071.89, 'new_value': 63395.49}, {'field': 'order_count', 'old_value': 576, 'new_value': 591}]
2025-05-30 09:01:09,922 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-30 09:01:10,372 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-30 09:01:10,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49491.8, 'new_value': 50171.8}, {'field': 'total_amount', 'old_value': 66233.4, 'new_value': 66913.4}, {'field': 'order_count', 'old_value': 109, 'new_value': 110}]
2025-05-30 09:01:10,372 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-30 09:01:10,803 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-30 09:01:10,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36458.74, 'new_value': 37357.95}, {'field': 'total_amount', 'old_value': 36458.74, 'new_value': 37357.95}, {'field': 'order_count', 'old_value': 1359, 'new_value': 1395}]
2025-05-30 09:01:10,804 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-30 09:01:11,317 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-30 09:01:11,317 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 901910.0, 'new_value': 925126.0}, {'field': 'total_amount', 'old_value': 901910.0, 'new_value': 925126.0}, {'field': 'order_count', 'old_value': 4072, 'new_value': 4192}]
2025-05-30 09:01:11,317 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-30 09:01:11,784 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-30 09:01:11,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43082.0, 'new_value': 43251.0}, {'field': 'total_amount', 'old_value': 43082.0, 'new_value': 43251.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-30 09:01:11,785 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-30 09:01:12,290 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-30 09:01:12,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56346.53, 'new_value': 57800.43}, {'field': 'offline_amount', 'old_value': 379094.71, 'new_value': 388601.25}, {'field': 'total_amount', 'old_value': 435441.24, 'new_value': 446401.68}, {'field': 'order_count', 'old_value': 2787, 'new_value': 2863}]
2025-05-30 09:01:12,290 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-30 09:01:12,751 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-30 09:01:12,751 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77275.0, 'new_value': 77695.0}, {'field': 'total_amount', 'old_value': 77275.0, 'new_value': 77695.0}, {'field': 'order_count', 'old_value': 2273, 'new_value': 2285}]
2025-05-30 09:01:12,752 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-30 09:01:13,165 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-30 09:01:13,165 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105755.77, 'new_value': 110064.35}, {'field': 'offline_amount', 'old_value': 117076.77, 'new_value': 120614.36}, {'field': 'total_amount', 'old_value': 222832.54, 'new_value': 230678.71}, {'field': 'order_count', 'old_value': 9134, 'new_value': 9434}]
2025-05-30 09:01:13,166 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-30 09:01:13,586 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-30 09:01:13,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57375.0, 'new_value': 61114.0}, {'field': 'total_amount', 'old_value': 57375.0, 'new_value': 61114.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 135}]
2025-05-30 09:01:13,587 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-30 09:01:14,048 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-30 09:01:14,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30861.0, 'new_value': 32181.0}, {'field': 'total_amount', 'old_value': 30861.0, 'new_value': 32181.0}, {'field': 'order_count', 'old_value': 182, 'new_value': 192}]
2025-05-30 09:01:14,049 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-30 09:01:14,518 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-30 09:01:14,518 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63442.44, 'new_value': 64748.97}, {'field': 'offline_amount', 'old_value': 49739.02, 'new_value': 50970.35}, {'field': 'total_amount', 'old_value': 113181.46, 'new_value': 115719.32}, {'field': 'order_count', 'old_value': 2273, 'new_value': 2336}]
2025-05-30 09:01:14,518 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-30 09:01:15,048 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-30 09:01:15,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2380000.0, 'new_value': 2430000.0}, {'field': 'total_amount', 'old_value': 2380000.0, 'new_value': 2430000.0}, {'field': 'order_count', 'old_value': 285, 'new_value': 286}]
2025-05-30 09:01:15,048 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-30 09:01:15,488 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-30 09:01:15,488 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207420.4, 'new_value': 212254.5}, {'field': 'total_amount', 'old_value': 207420.4, 'new_value': 212254.5}, {'field': 'order_count', 'old_value': 2690, 'new_value': 2749}]
2025-05-30 09:01:15,488 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKF
2025-05-30 09:01:15,983 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKF
2025-05-30 09:01:15,983 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30500.0, 'new_value': 36299.0}, {'field': 'total_amount', 'old_value': 30500.0, 'new_value': 36299.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-30 09:01:15,983 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-30 09:01:16,415 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-30 09:01:16,416 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48035.95, 'new_value': 49533.65}, {'field': 'offline_amount', 'old_value': 1209517.01, 'new_value': 1238290.24}, {'field': 'total_amount', 'old_value': 1257552.96, 'new_value': 1287823.89}, {'field': 'order_count', 'old_value': 6245, 'new_value': 6408}]
2025-05-30 09:01:16,416 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-30 09:01:16,850 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-30 09:01:16,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 637161.0, 'new_value': 681722.0}, {'field': 'total_amount', 'old_value': 637161.0, 'new_value': 681722.0}, {'field': 'order_count', 'old_value': 576, 'new_value': 603}]
2025-05-30 09:01:16,851 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-30 09:01:17,292 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-30 09:01:17,293 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 289100.66, 'new_value': 295226.56}, {'field': 'offline_amount', 'old_value': 158832.26, 'new_value': 162686.06}, {'field': 'total_amount', 'old_value': 447932.92, 'new_value': 457912.62}, {'field': 'order_count', 'old_value': 3335, 'new_value': 3371}]
2025-05-30 09:01:17,293 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-30 09:01:17,722 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-30 09:01:17,723 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 535000.0, 'new_value': 540000.0}, {'field': 'total_amount', 'old_value': 535000.0, 'new_value': 540000.0}, {'field': 'order_count', 'old_value': 152, 'new_value': 153}]
2025-05-30 09:01:17,723 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-30 09:01:18,151 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-30 09:01:18,151 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 515000.0, 'new_value': 520000.0}, {'field': 'total_amount', 'old_value': 515000.0, 'new_value': 520000.0}, {'field': 'order_count', 'old_value': 151, 'new_value': 152}]
2025-05-30 09:01:18,151 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-30 09:01:18,615 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-30 09:01:18,615 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3498674.0, 'new_value': 3548674.0}, {'field': 'total_amount', 'old_value': 3498674.0, 'new_value': 3548674.0}, {'field': 'order_count', 'old_value': 305, 'new_value': 306}]
2025-05-30 09:01:18,615 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-30 09:01:19,097 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-30 09:01:19,098 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107567.0, 'new_value': 112527.0}, {'field': 'offline_amount', 'old_value': 1270330.0, 'new_value': 1324670.0}, {'field': 'total_amount', 'old_value': 1377897.0, 'new_value': 1437197.0}, {'field': 'order_count', 'old_value': 34250, 'new_value': 35633}]
2025-05-30 09:01:19,098 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-30 09:01:19,558 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-30 09:01:19,558 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 423285.19, 'new_value': 440075.84}, {'field': 'total_amount', 'old_value': 436795.67, 'new_value': 453586.32}, {'field': 'order_count', 'old_value': 1402, 'new_value': 1449}]
2025-05-30 09:01:19,558 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-30 09:01:20,036 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-30 09:01:20,037 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59884.0, 'new_value': 62035.0}, {'field': 'offline_amount', 'old_value': 276863.0, 'new_value': 285018.0}, {'field': 'total_amount', 'old_value': 336747.0, 'new_value': 347053.0}, {'field': 'order_count', 'old_value': 300, 'new_value': 308}]
2025-05-30 09:01:20,037 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-30 09:01:20,569 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-30 09:01:20,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186534.73, 'new_value': 192616.58}, {'field': 'total_amount', 'old_value': 186534.73, 'new_value': 192616.58}, {'field': 'order_count', 'old_value': 9449, 'new_value': 9749}]
2025-05-30 09:01:20,569 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-30 09:01:21,024 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-30 09:01:21,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173692.0, 'new_value': 176131.0}, {'field': 'total_amount', 'old_value': 173692.0, 'new_value': 176131.0}, {'field': 'order_count', 'old_value': 787, 'new_value': 799}]
2025-05-30 09:01:21,024 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-30 09:01:21,510 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-30 09:01:21,510 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141101.8, 'new_value': 141783.6}, {'field': 'total_amount', 'old_value': 141101.8, 'new_value': 141783.6}, {'field': 'order_count', 'old_value': 3897, 'new_value': 3916}]
2025-05-30 09:01:21,510 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-30 09:01:21,939 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-30 09:01:21,939 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10776.0, 'new_value': 10956.0}, {'field': 'total_amount', 'old_value': 22250.0, 'new_value': 22430.0}, {'field': 'order_count', 'old_value': 126, 'new_value': 131}]
2025-05-30 09:01:21,940 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-30 09:01:22,358 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-30 09:01:22,358 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117715.8, 'new_value': 120719.6}, {'field': 'total_amount', 'old_value': 117715.8, 'new_value': 120719.6}, {'field': 'order_count', 'old_value': 559, 'new_value': 572}]
2025-05-30 09:01:22,358 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-30 09:01:22,771 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-30 09:01:22,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 286121.56, 'new_value': 287465.42}, {'field': 'total_amount', 'old_value': 286121.56, 'new_value': 287465.42}, {'field': 'order_count', 'old_value': 1636, 'new_value': 1642}]
2025-05-30 09:01:22,771 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-30 09:01:23,224 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-30 09:01:23,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100145.56, 'new_value': 102229.56}, {'field': 'total_amount', 'old_value': 100145.56, 'new_value': 102229.56}, {'field': 'order_count', 'old_value': 5163, 'new_value': 5260}]
2025-05-30 09:01:23,224 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-30 09:01:23,641 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-30 09:01:23,641 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5573.77, 'new_value': 8787.34}, {'field': 'total_amount', 'old_value': 94777.91, 'new_value': 97991.48}, {'field': 'order_count', 'old_value': 451, 'new_value': 467}]
2025-05-30 09:01:23,641 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-30 09:01:24,224 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-30 09:01:24,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5515.0, 'new_value': 5655.0}, {'field': 'total_amount', 'old_value': 13278.0, 'new_value': 13418.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 145}]
2025-05-30 09:01:24,224 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-30 09:01:24,823 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-30 09:01:24,823 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60565.35, 'new_value': 62253.79}, {'field': 'offline_amount', 'old_value': 62667.11, 'new_value': 65089.03}, {'field': 'total_amount', 'old_value': 123232.46, 'new_value': 127342.82}, {'field': 'order_count', 'old_value': 6134, 'new_value': 6341}]
2025-05-30 09:01:24,824 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-30 09:01:25,286 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-30 09:01:25,286 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 118644.43, 'new_value': 122792.05}, {'field': 'offline_amount', 'old_value': 129586.91, 'new_value': 133857.43}, {'field': 'total_amount', 'old_value': 248231.34, 'new_value': 256649.48}, {'field': 'order_count', 'old_value': 6291, 'new_value': 6523}]
2025-05-30 09:01:25,286 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-30 09:01:25,710 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-30 09:01:25,710 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1095459.0, 'new_value': 1135808.0}, {'field': 'total_amount', 'old_value': 1095459.0, 'new_value': 1135808.0}, {'field': 'order_count', 'old_value': 1259, 'new_value': 1306}]
2025-05-30 09:01:25,710 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-30 09:01:26,196 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-30 09:01:26,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233826.4, 'new_value': 243915.4}, {'field': 'total_amount', 'old_value': 239776.7, 'new_value': 249865.7}, {'field': 'order_count', 'old_value': 457, 'new_value': 468}]
2025-05-30 09:01:26,196 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-05-30 09:01:26,773 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-05-30 09:01:26,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119122.0, 'new_value': 136722.0}, {'field': 'total_amount', 'old_value': 119122.0, 'new_value': 136722.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 47}]
2025-05-30 09:01:26,773 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-30 09:01:27,176 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-30 09:01:27,177 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47610.65, 'new_value': 49035.65}, {'field': 'offline_amount', 'old_value': 139257.0, 'new_value': 146302.0}, {'field': 'total_amount', 'old_value': 186867.65, 'new_value': 195337.65}, {'field': 'order_count', 'old_value': 2053, 'new_value': 2138}]
2025-05-30 09:01:27,177 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-30 09:01:27,609 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-30 09:01:27,609 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 153975.0, 'new_value': 157633.0}, {'field': 'offline_amount', 'old_value': 103851.0, 'new_value': 105627.0}, {'field': 'total_amount', 'old_value': 257826.0, 'new_value': 263260.0}, {'field': 'order_count', 'old_value': 3471, 'new_value': 3567}]
2025-05-30 09:01:27,609 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-30 09:01:28,039 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-30 09:01:28,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8188.7, 'new_value': 8267.7}, {'field': 'offline_amount', 'old_value': 25546.1, 'new_value': 25676.9}, {'field': 'total_amount', 'old_value': 33734.8, 'new_value': 33944.6}, {'field': 'order_count', 'old_value': 335, 'new_value': 340}]
2025-05-30 09:01:28,040 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-30 09:01:28,407 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-30 09:01:28,407 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12174.32, 'new_value': 12433.92}, {'field': 'offline_amount', 'old_value': 191478.0, 'new_value': 195458.0}, {'field': 'total_amount', 'old_value': 203652.32, 'new_value': 207891.92}, {'field': 'order_count', 'old_value': 86, 'new_value': 88}]
2025-05-30 09:01:28,407 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-30 09:01:28,832 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-30 09:01:28,832 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35982.66, 'new_value': 39558.66}, {'field': 'offline_amount', 'old_value': 34935.82, 'new_value': 37013.12}, {'field': 'total_amount', 'old_value': 70918.48, 'new_value': 76571.78}, {'field': 'order_count', 'old_value': 318, 'new_value': 335}]
2025-05-30 09:01:28,832 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-30 09:01:29,458 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-30 09:01:29,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58980.0, 'new_value': 59514.7}, {'field': 'total_amount', 'old_value': 67218.0, 'new_value': 67752.7}, {'field': 'order_count', 'old_value': 524, 'new_value': 547}]
2025-05-30 09:01:29,459 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-30 09:01:29,913 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-30 09:01:29,913 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 247228.5, 'new_value': 253606.5}, {'field': 'total_amount', 'old_value': 247228.5, 'new_value': 253606.5}, {'field': 'order_count', 'old_value': 1203, 'new_value': 1231}]
2025-05-30 09:01:29,914 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-30 09:01:30,524 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-30 09:01:30,525 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59935.78, 'new_value': 60762.78}, {'field': 'total_amount', 'old_value': 66601.82, 'new_value': 67428.82}, {'field': 'order_count', 'old_value': 624, 'new_value': 635}]
2025-05-30 09:01:30,525 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-30 09:01:30,976 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-30 09:01:30,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 219396.24, 'new_value': 225620.74}, {'field': 'total_amount', 'old_value': 219396.24, 'new_value': 225620.74}, {'field': 'order_count', 'old_value': 844, 'new_value': 876}]
2025-05-30 09:01:30,976 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-30 09:01:31,374 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-30 09:01:31,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14554.0, 'new_value': 15702.0}, {'field': 'total_amount', 'old_value': 14554.0, 'new_value': 15702.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 28}]
2025-05-30 09:01:31,375 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-30 09:01:31,944 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-30 09:01:31,944 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 191849.6, 'new_value': 204455.6}, {'field': 'total_amount', 'old_value': 352065.38, 'new_value': 364671.38}]
2025-05-30 09:01:31,944 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-30 09:01:32,380 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-30 09:01:32,380 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 215087.0, 'new_value': 222981.0}, {'field': 'offline_amount', 'old_value': 191764.0, 'new_value': 198624.0}, {'field': 'total_amount', 'old_value': 406851.0, 'new_value': 421605.0}, {'field': 'order_count', 'old_value': 1150, 'new_value': 1189}]
2025-05-30 09:01:32,380 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-30 09:01:32,867 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-30 09:01:32,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 846257.01, 'new_value': 868960.51}, {'field': 'total_amount', 'old_value': 846257.01, 'new_value': 868960.51}, {'field': 'order_count', 'old_value': 4501, 'new_value': 4623}]
2025-05-30 09:01:32,867 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-30 09:01:33,266 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-30 09:01:33,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151070.33, 'new_value': 153905.78}, {'field': 'total_amount', 'old_value': 151070.33, 'new_value': 153905.78}, {'field': 'order_count', 'old_value': 10531, 'new_value': 10764}]
2025-05-30 09:01:33,266 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-30 09:01:33,695 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-30 09:01:33,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 463967.0, 'new_value': 467081.0}, {'field': 'total_amount', 'old_value': 463967.0, 'new_value': 467081.0}, {'field': 'order_count', 'old_value': 10536, 'new_value': 10606}]
2025-05-30 09:01:33,695 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-30 09:01:34,171 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-30 09:01:34,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108887.0, 'new_value': 110634.0}, {'field': 'total_amount', 'old_value': 108887.0, 'new_value': 110634.0}, {'field': 'order_count', 'old_value': 7351, 'new_value': 7485}]
2025-05-30 09:01:34,172 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-30 09:01:34,613 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-30 09:01:34,614 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162826.0, 'new_value': 170349.0}, {'field': 'total_amount', 'old_value': 167565.0, 'new_value': 175088.0}, {'field': 'order_count', 'old_value': 12458, 'new_value': 13036}]
2025-05-30 09:01:34,614 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-30 09:01:35,088 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-30 09:01:35,088 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34163.0, 'new_value': 37661.0}, {'field': 'total_amount', 'old_value': 34163.0, 'new_value': 37661.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 46}]
2025-05-30 09:01:35,089 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-30 09:01:35,490 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-30 09:01:35,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5359.97, 'new_value': 7869.05}, {'field': 'total_amount', 'old_value': 79279.05, 'new_value': 81788.13}, {'field': 'order_count', 'old_value': 1405, 'new_value': 1516}]
2025-05-30 09:01:35,491 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-30 09:01:35,943 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-30 09:01:35,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27326.88, 'new_value': 28148.88}, {'field': 'total_amount', 'old_value': 27326.88, 'new_value': 28148.88}, {'field': 'order_count', 'old_value': 126, 'new_value': 130}]
2025-05-30 09:01:35,943 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-30 09:01:36,486 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-30 09:01:36,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 375114.0, 'new_value': 391762.0}, {'field': 'total_amount', 'old_value': 375114.0, 'new_value': 391762.0}, {'field': 'order_count', 'old_value': 8120, 'new_value': 8450}]
2025-05-30 09:01:36,486 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-30 09:01:36,930 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-30 09:01:36,930 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27.9, 'new_value': 53.8}, {'field': 'total_amount', 'old_value': 34866.9, 'new_value': 34892.8}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-05-30 09:01:36,930 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-30 09:01:37,428 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-30 09:01:37,428 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-30 09:01:37,429 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-30 09:01:37,882 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-30 09:01:37,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 332644.01, 'new_value': 342536.51}, {'field': 'total_amount', 'old_value': 332644.01, 'new_value': 342536.51}, {'field': 'order_count', 'old_value': 911, 'new_value': 933}]
2025-05-30 09:01:37,882 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-30 09:01:38,374 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-30 09:01:38,374 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 14357.0}, {'field': 'offline_amount', 'old_value': 157637.0, 'new_value': 160341.0}, {'field': 'total_amount', 'old_value': 157637.0, 'new_value': 174698.0}, {'field': 'order_count', 'old_value': 637, 'new_value': 659}]
2025-05-30 09:01:38,374 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-30 09:01:38,885 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-30 09:01:38,885 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60056.0, 'new_value': 62176.0}, {'field': 'total_amount', 'old_value': 60056.0, 'new_value': 62176.0}, {'field': 'order_count', 'old_value': 1150, 'new_value': 1194}]
2025-05-30 09:01:38,885 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-30 09:01:39,301 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-30 09:01:39,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125911.0, 'new_value': 129263.0}, {'field': 'total_amount', 'old_value': 125911.0, 'new_value': 129263.0}, {'field': 'order_count', 'old_value': 1313, 'new_value': 1353}]
2025-05-30 09:01:39,301 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-30 09:01:39,792 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-30 09:01:39,793 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42019.0, 'new_value': 44709.0}, {'field': 'total_amount', 'old_value': 82144.4, 'new_value': 84834.4}, {'field': 'order_count', 'old_value': 2807, 'new_value': 2810}]
2025-05-30 09:01:39,793 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-30 09:01:40,189 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-30 09:01:40,189 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57524.81, 'new_value': 63252.77}, {'field': 'offline_amount', 'old_value': 62774.77, 'new_value': 63372.77}, {'field': 'total_amount', 'old_value': 120299.58, 'new_value': 126625.54}, {'field': 'order_count', 'old_value': 543, 'new_value': 578}]
2025-05-30 09:01:40,189 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-05-30 09:01:40,562 - INFO - 更新表单数据成功: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-05-30 09:01:40,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4691.55, 'new_value': 5785.55}, {'field': 'total_amount', 'old_value': 4691.55, 'new_value': 5785.55}, {'field': 'order_count', 'old_value': 264, 'new_value': 327}]
2025-05-30 09:01:40,562 - INFO - 开始更新记录 - 表单实例ID: FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC
2025-05-30 09:01:41,045 - INFO - 更新表单数据成功: FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC
2025-05-30 09:01:41,045 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35000.0, 'new_value': 40000.0}, {'field': 'total_amount', 'old_value': 35000.0, 'new_value': 40000.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-30 09:01:41,045 - INFO - 开始更新记录 - 表单实例ID: FINST-ORA66F81M7SV63GRAIEPN5723LJD2F3O5O8BM9B
2025-05-30 09:01:41,538 - INFO - 更新表单数据成功: FINST-ORA66F81M7SV63GRAIEPN5723LJD2F3O5O8BM9B
2025-05-30 09:01:41,539 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 671.0, 'new_value': 4356.0}, {'field': 'offline_amount', 'old_value': 198.0, 'new_value': 222.0}, {'field': 'total_amount', 'old_value': 869.0, 'new_value': 4578.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 127}]
2025-05-30 09:01:41,539 - INFO - 日期 2025-05 处理完成 - 更新: 155 条，插入: 0 条，错误: 0 条
2025-05-30 09:01:41,539 - INFO - 数据同步完成！更新: 155 条，插入: 0 条，错误: 0 条
2025-05-30 09:01:41,541 - INFO - =================同步完成====================
2025-05-30 12:00:02,071 - INFO - =================使用默认全量同步=============
2025-05-30 12:00:03,634 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-30 12:00:03,634 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-30 12:00:03,661 - INFO - 开始处理日期: 2025-01
2025-05-30 12:00:03,664 - INFO - Request Parameters - Page 1:
2025-05-30 12:00:03,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:03,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:04,864 - INFO - Response - Page 1:
2025-05-30 12:00:05,064 - INFO - 第 1 页获取到 100 条记录
2025-05-30 12:00:05,064 - INFO - Request Parameters - Page 2:
2025-05-30 12:00:05,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:05,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:05,865 - INFO - Response - Page 2:
2025-05-30 12:00:06,066 - INFO - 第 2 页获取到 100 条记录
2025-05-30 12:00:06,066 - INFO - Request Parameters - Page 3:
2025-05-30 12:00:06,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:06,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:06,852 - INFO - Response - Page 3:
2025-05-30 12:00:07,052 - INFO - 第 3 页获取到 100 条记录
2025-05-30 12:00:07,052 - INFO - Request Parameters - Page 4:
2025-05-30 12:00:07,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:07,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:07,548 - INFO - Response - Page 4:
2025-05-30 12:00:07,748 - INFO - 第 4 页获取到 100 条记录
2025-05-30 12:00:07,748 - INFO - Request Parameters - Page 5:
2025-05-30 12:00:07,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:07,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:08,314 - INFO - Response - Page 5:
2025-05-30 12:00:08,514 - INFO - 第 5 页获取到 100 条记录
2025-05-30 12:00:08,514 - INFO - Request Parameters - Page 6:
2025-05-30 12:00:08,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:08,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:09,027 - INFO - Response - Page 6:
2025-05-30 12:00:09,227 - INFO - 第 6 页获取到 100 条记录
2025-05-30 12:00:09,227 - INFO - Request Parameters - Page 7:
2025-05-30 12:00:09,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:09,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:09,746 - INFO - Response - Page 7:
2025-05-30 12:00:09,946 - INFO - 第 7 页获取到 82 条记录
2025-05-30 12:00:09,946 - INFO - 查询完成，共获取到 682 条记录
2025-05-30 12:00:09,946 - INFO - 获取到 682 条表单数据
2025-05-30 12:00:09,959 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-30 12:00:09,971 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 12:00:09,972 - INFO - 开始处理日期: 2025-02
2025-05-30 12:00:09,972 - INFO - Request Parameters - Page 1:
2025-05-30 12:00:09,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:09,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:10,960 - INFO - Response - Page 1:
2025-05-30 12:00:11,160 - INFO - 第 1 页获取到 100 条记录
2025-05-30 12:00:11,160 - INFO - Request Parameters - Page 2:
2025-05-30 12:00:11,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:11,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:11,757 - INFO - Response - Page 2:
2025-05-30 12:00:11,957 - INFO - 第 2 页获取到 100 条记录
2025-05-30 12:00:11,957 - INFO - Request Parameters - Page 3:
2025-05-30 12:00:11,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:11,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:12,520 - INFO - Response - Page 3:
2025-05-30 12:00:12,720 - INFO - 第 3 页获取到 100 条记录
2025-05-30 12:00:12,720 - INFO - Request Parameters - Page 4:
2025-05-30 12:00:12,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:12,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:13,299 - INFO - Response - Page 4:
2025-05-30 12:00:13,499 - INFO - 第 4 页获取到 100 条记录
2025-05-30 12:00:13,499 - INFO - Request Parameters - Page 5:
2025-05-30 12:00:13,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:13,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:14,045 - INFO - Response - Page 5:
2025-05-30 12:00:14,245 - INFO - 第 5 页获取到 100 条记录
2025-05-30 12:00:14,245 - INFO - Request Parameters - Page 6:
2025-05-30 12:00:14,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:14,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:14,731 - INFO - Response - Page 6:
2025-05-30 12:00:14,931 - INFO - 第 6 页获取到 100 条记录
2025-05-30 12:00:14,931 - INFO - Request Parameters - Page 7:
2025-05-30 12:00:14,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:14,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:15,409 - INFO - Response - Page 7:
2025-05-30 12:00:15,610 - INFO - 第 7 页获取到 70 条记录
2025-05-30 12:00:15,610 - INFO - 查询完成，共获取到 670 条记录
2025-05-30 12:00:15,610 - INFO - 获取到 670 条表单数据
2025-05-30 12:00:15,623 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-30 12:00:15,635 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 12:00:15,635 - INFO - 开始处理日期: 2025-03
2025-05-30 12:00:15,635 - INFO - Request Parameters - Page 1:
2025-05-30 12:00:15,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:15,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:16,201 - INFO - Response - Page 1:
2025-05-30 12:00:16,402 - INFO - 第 1 页获取到 100 条记录
2025-05-30 12:00:16,402 - INFO - Request Parameters - Page 2:
2025-05-30 12:00:16,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:16,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:17,046 - INFO - Response - Page 2:
2025-05-30 12:00:17,246 - INFO - 第 2 页获取到 100 条记录
2025-05-30 12:00:17,246 - INFO - Request Parameters - Page 3:
2025-05-30 12:00:17,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:17,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:17,719 - INFO - Response - Page 3:
2025-05-30 12:00:17,919 - INFO - 第 3 页获取到 100 条记录
2025-05-30 12:00:17,919 - INFO - Request Parameters - Page 4:
2025-05-30 12:00:17,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:17,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:18,402 - INFO - Response - Page 4:
2025-05-30 12:00:18,602 - INFO - 第 4 页获取到 100 条记录
2025-05-30 12:00:18,602 - INFO - Request Parameters - Page 5:
2025-05-30 12:00:18,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:18,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:19,073 - INFO - Response - Page 5:
2025-05-30 12:00:19,274 - INFO - 第 5 页获取到 100 条记录
2025-05-30 12:00:19,274 - INFO - Request Parameters - Page 6:
2025-05-30 12:00:19,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:19,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:19,854 - INFO - Response - Page 6:
2025-05-30 12:00:20,054 - INFO - 第 6 页获取到 100 条记录
2025-05-30 12:00:20,054 - INFO - Request Parameters - Page 7:
2025-05-30 12:00:20,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:20,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:20,627 - INFO - Response - Page 7:
2025-05-30 12:00:20,827 - INFO - 第 7 页获取到 61 条记录
2025-05-30 12:00:20,827 - INFO - 查询完成，共获取到 661 条记录
2025-05-30 12:00:20,827 - INFO - 获取到 661 条表单数据
2025-05-30 12:00:20,840 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-30 12:00:20,852 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 12:00:20,852 - INFO - 开始处理日期: 2025-04
2025-05-30 12:00:20,852 - INFO - Request Parameters - Page 1:
2025-05-30 12:00:20,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:20,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:21,448 - INFO - Response - Page 1:
2025-05-30 12:00:21,649 - INFO - 第 1 页获取到 100 条记录
2025-05-30 12:00:21,649 - INFO - Request Parameters - Page 2:
2025-05-30 12:00:21,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:21,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:22,113 - INFO - Response - Page 2:
2025-05-30 12:00:22,314 - INFO - 第 2 页获取到 100 条记录
2025-05-30 12:00:22,314 - INFO - Request Parameters - Page 3:
2025-05-30 12:00:22,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:22,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:22,866 - INFO - Response - Page 3:
2025-05-30 12:00:23,067 - INFO - 第 3 页获取到 100 条记录
2025-05-30 12:00:23,067 - INFO - Request Parameters - Page 4:
2025-05-30 12:00:23,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:23,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:23,623 - INFO - Response - Page 4:
2025-05-30 12:00:23,823 - INFO - 第 4 页获取到 100 条记录
2025-05-30 12:00:23,823 - INFO - Request Parameters - Page 5:
2025-05-30 12:00:23,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:23,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:24,256 - INFO - Response - Page 5:
2025-05-30 12:00:24,456 - INFO - 第 5 页获取到 100 条记录
2025-05-30 12:00:24,456 - INFO - Request Parameters - Page 6:
2025-05-30 12:00:24,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:24,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:24,974 - INFO - Response - Page 6:
2025-05-30 12:00:25,174 - INFO - 第 6 页获取到 100 条记录
2025-05-30 12:00:25,174 - INFO - Request Parameters - Page 7:
2025-05-30 12:00:25,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:25,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:25,579 - INFO - Response - Page 7:
2025-05-30 12:00:25,779 - INFO - 第 7 页获取到 56 条记录
2025-05-30 12:00:25,779 - INFO - 查询完成，共获取到 656 条记录
2025-05-30 12:00:25,779 - INFO - 获取到 656 条表单数据
2025-05-30 12:00:25,792 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-30 12:00:25,803 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 12:00:25,803 - INFO - 开始处理日期: 2025-05
2025-05-30 12:00:25,804 - INFO - Request Parameters - Page 1:
2025-05-30 12:00:25,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:25,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:26,382 - INFO - Response - Page 1:
2025-05-30 12:00:26,582 - INFO - 第 1 页获取到 100 条记录
2025-05-30 12:00:26,582 - INFO - Request Parameters - Page 2:
2025-05-30 12:00:26,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:26,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:27,129 - INFO - Response - Page 2:
2025-05-30 12:00:27,329 - INFO - 第 2 页获取到 100 条记录
2025-05-30 12:00:27,329 - INFO - Request Parameters - Page 3:
2025-05-30 12:00:27,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:27,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:27,861 - INFO - Response - Page 3:
2025-05-30 12:00:28,062 - INFO - 第 3 页获取到 100 条记录
2025-05-30 12:00:28,062 - INFO - Request Parameters - Page 4:
2025-05-30 12:00:28,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:28,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:28,605 - INFO - Response - Page 4:
2025-05-30 12:00:28,806 - INFO - 第 4 页获取到 100 条记录
2025-05-30 12:00:28,806 - INFO - Request Parameters - Page 5:
2025-05-30 12:00:28,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:28,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:29,355 - INFO - Response - Page 5:
2025-05-30 12:00:29,555 - INFO - 第 5 页获取到 100 条记录
2025-05-30 12:00:29,555 - INFO - Request Parameters - Page 6:
2025-05-30 12:00:29,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:29,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:30,041 - INFO - Response - Page 6:
2025-05-30 12:00:30,241 - INFO - 第 6 页获取到 100 条记录
2025-05-30 12:00:30,241 - INFO - Request Parameters - Page 7:
2025-05-30 12:00:30,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 12:00:30,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 12:00:30,662 - INFO - Response - Page 7:
2025-05-30 12:00:30,862 - INFO - 第 7 页获取到 35 条记录
2025-05-30 12:00:30,862 - INFO - 查询完成，共获取到 635 条记录
2025-05-30 12:00:30,863 - INFO - 获取到 635 条表单数据
2025-05-30 12:00:30,875 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-30 12:00:30,875 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-30 12:00:31,344 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-30 12:00:31,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57505.0, 'new_value': 60685.0}, {'field': 'total_amount', 'old_value': 59095.0, 'new_value': 62275.0}, {'field': 'order_count', 'old_value': 223, 'new_value': 235}]
2025-05-30 12:00:31,345 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-30 12:00:31,769 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-30 12:00:31,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209283.39, 'new_value': 227566.02}, {'field': 'total_amount', 'old_value': 209283.39, 'new_value': 227566.02}, {'field': 'order_count', 'old_value': 7730, 'new_value': 8317}]
2025-05-30 12:00:31,769 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-30 12:00:32,266 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-30 12:00:32,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 815759.98, 'new_value': 842233.98}, {'field': 'total_amount', 'old_value': 815759.98, 'new_value': 842233.98}, {'field': 'order_count', 'old_value': 2578, 'new_value': 2653}]
2025-05-30 12:00:32,267 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-30 12:00:32,689 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-30 12:00:32,689 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49949.4, 'new_value': 51778.07}, {'field': 'total_amount', 'old_value': 49949.4, 'new_value': 51778.07}, {'field': 'order_count', 'old_value': 9829, 'new_value': 10198}]
2025-05-30 12:00:32,690 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-30 12:00:33,142 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-30 12:00:33,142 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38388.87, 'new_value': 39543.72}, {'field': 'offline_amount', 'old_value': 17416.57, 'new_value': 18283.84}, {'field': 'total_amount', 'old_value': 55805.44, 'new_value': 57827.56}, {'field': 'order_count', 'old_value': 2800, 'new_value': 2876}]
2025-05-30 12:00:33,142 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-30 12:00:33,544 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-30 12:00:33,544 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 473306.0, 'new_value': 486633.0}, {'field': 'offline_amount', 'old_value': 358245.0, 'new_value': 380676.0}, {'field': 'total_amount', 'old_value': 831551.0, 'new_value': 867309.0}, {'field': 'order_count', 'old_value': 919, 'new_value': 942}]
2025-05-30 12:00:33,545 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-30 12:00:34,001 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-30 12:00:34,001 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33434.63, 'new_value': 35500.43}, {'field': 'offline_amount', 'old_value': 101838.58, 'new_value': 107646.91}, {'field': 'total_amount', 'old_value': 135273.21, 'new_value': 143147.34}, {'field': 'order_count', 'old_value': 3086, 'new_value': 3274}]
2025-05-30 12:00:34,001 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-30 12:00:34,483 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-30 12:00:34,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58440.0, 'new_value': 60620.0}, {'field': 'total_amount', 'old_value': 62560.0, 'new_value': 64740.0}, {'field': 'order_count', 'old_value': 608, 'new_value': 628}]
2025-05-30 12:00:34,484 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-30 12:00:34,980 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-30 12:00:34,981 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28155.0, 'new_value': 29028.4}, {'field': 'total_amount', 'old_value': 29147.0, 'new_value': 30020.4}, {'field': 'order_count', 'old_value': 3018, 'new_value': 3084}]
2025-05-30 12:00:34,981 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-30 12:00:35,411 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-30 12:00:35,411 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53061.3, 'new_value': 53421.1}, {'field': 'total_amount', 'old_value': 57021.3, 'new_value': 57381.1}, {'field': 'order_count', 'old_value': 452, 'new_value': 461}]
2025-05-30 12:00:35,411 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-30 12:00:35,900 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-30 12:00:35,900 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23288.62, 'new_value': 24055.77}, {'field': 'offline_amount', 'old_value': 28465.47, 'new_value': 29080.57}, {'field': 'total_amount', 'old_value': 51754.09, 'new_value': 53136.34}, {'field': 'order_count', 'old_value': 2562, 'new_value': 2634}]
2025-05-30 12:00:35,900 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-30 12:00:36,361 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-30 12:00:36,361 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70604.0, 'new_value': 78204.0}, {'field': 'total_amount', 'old_value': 70903.92, 'new_value': 78503.92}, {'field': 'order_count', 'old_value': 116, 'new_value': 121}]
2025-05-30 12:00:36,361 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-30 12:00:36,802 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-30 12:00:36,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 471401.71, 'new_value': 484790.71}, {'field': 'total_amount', 'old_value': 504584.71, 'new_value': 517973.71}, {'field': 'order_count', 'old_value': 478, 'new_value': 499}]
2025-05-30 12:00:36,803 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-30 12:00:37,222 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-30 12:00:37,223 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4137.24, 'new_value': 4688.48}, {'field': 'offline_amount', 'old_value': 86488.52, 'new_value': 95725.78}, {'field': 'total_amount', 'old_value': 90625.76, 'new_value': 100414.26}, {'field': 'order_count', 'old_value': 3545, 'new_value': 3966}]
2025-05-30 12:00:37,223 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-30 12:00:37,742 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-30 12:00:37,742 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58915.0, 'new_value': 61168.0}, {'field': 'offline_amount', 'old_value': 199462.98, 'new_value': 204031.98}, {'field': 'total_amount', 'old_value': 258377.98, 'new_value': 265199.98}, {'field': 'order_count', 'old_value': 1768, 'new_value': 1829}]
2025-05-30 12:00:37,743 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-30 12:00:38,198 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-30 12:00:38,198 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14267.96, 'new_value': 14541.54}, {'field': 'offline_amount', 'old_value': 41178.11, 'new_value': 42156.21}, {'field': 'total_amount', 'old_value': 55446.07, 'new_value': 56697.75}, {'field': 'order_count', 'old_value': 1027, 'new_value': 1054}]
2025-05-30 12:00:38,199 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-30 12:00:38,728 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-30 12:00:38,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 990079.3, 'new_value': 1010625.1}, {'field': 'total_amount', 'old_value': 1049775.3, 'new_value': 1070321.1}, {'field': 'order_count', 'old_value': 102, 'new_value': 104}]
2025-05-30 12:00:38,728 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-30 12:00:39,187 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-30 12:00:39,187 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15804.7, 'new_value': 15922.7}, {'field': 'total_amount', 'old_value': 75920.3, 'new_value': 76038.3}, {'field': 'order_count', 'old_value': 746, 'new_value': 747}]
2025-05-30 12:00:39,187 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-30 12:00:39,633 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-30 12:00:39,633 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29145.55, 'new_value': 30319.36}, {'field': 'offline_amount', 'old_value': 120782.18, 'new_value': 125383.18}, {'field': 'total_amount', 'old_value': 149927.73, 'new_value': 155702.54}, {'field': 'order_count', 'old_value': 2055, 'new_value': 2140}]
2025-05-30 12:00:39,633 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-30 12:00:40,052 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-30 12:00:40,053 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94278.0, 'new_value': 97406.0}, {'field': 'total_amount', 'old_value': 171323.0, 'new_value': 174451.0}, {'field': 'order_count', 'old_value': 2408, 'new_value': 2456}]
2025-05-30 12:00:40,053 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-30 12:00:40,576 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-30 12:00:40,576 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196344.62, 'new_value': 199343.87}, {'field': 'total_amount', 'old_value': 196344.62, 'new_value': 199343.87}, {'field': 'order_count', 'old_value': 302, 'new_value': 314}]
2025-05-30 12:00:40,576 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-30 12:00:40,995 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-30 12:00:40,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99277.54, 'new_value': 103896.13}, {'field': 'total_amount', 'old_value': 99277.54, 'new_value': 103896.13}, {'field': 'order_count', 'old_value': 3847, 'new_value': 4055}]
2025-05-30 12:00:40,995 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-30 12:00:41,420 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-30 12:00:41,420 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16031.0, 'new_value': 16328.0}, {'field': 'total_amount', 'old_value': 16031.0, 'new_value': 16328.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 44}]
2025-05-30 12:00:41,420 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-30 12:00:41,832 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-30 12:00:41,832 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107611.57, 'new_value': 113787.59}, {'field': 'offline_amount', 'old_value': 58476.95, 'new_value': 59460.45}, {'field': 'total_amount', 'old_value': 166088.52, 'new_value': 173248.04}, {'field': 'order_count', 'old_value': 9355, 'new_value': 9753}]
2025-05-30 12:00:41,832 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-30 12:00:42,230 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-30 12:00:42,230 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50834.78, 'new_value': 53906.58}, {'field': 'offline_amount', 'old_value': 52760.08, 'new_value': 55009.76}, {'field': 'total_amount', 'old_value': 103594.86, 'new_value': 108916.34}, {'field': 'order_count', 'old_value': 5464, 'new_value': 5745}]
2025-05-30 12:00:42,230 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-30 12:00:42,727 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-30 12:00:42,728 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60825.67, 'new_value': 63005.9}, {'field': 'offline_amount', 'old_value': 589416.1, 'new_value': 610987.06}, {'field': 'total_amount', 'old_value': 650241.77, 'new_value': 673992.96}, {'field': 'order_count', 'old_value': 2099, 'new_value': 2166}]
2025-05-30 12:00:42,728 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-30 12:00:43,156 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-30 12:00:43,156 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10759.61, 'new_value': 12515.31}, {'field': 'offline_amount', 'old_value': 127271.66, 'new_value': 137568.63}, {'field': 'total_amount', 'old_value': 138031.27, 'new_value': 150083.94}, {'field': 'order_count', 'old_value': 2312, 'new_value': 2548}]
2025-05-30 12:00:43,156 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-30 12:00:43,612 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-30 12:00:43,612 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32172.89, 'new_value': 33238.46}, {'field': 'offline_amount', 'old_value': 24080.33, 'new_value': 24885.61}, {'field': 'total_amount', 'old_value': 56253.22, 'new_value': 58124.07}, {'field': 'order_count', 'old_value': 3244, 'new_value': 3352}]
2025-05-30 12:00:43,612 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-30 12:00:44,065 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-30 12:00:44,065 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 191358.0, 'new_value': 201348.0}, {'field': 'total_amount', 'old_value': 291235.0, 'new_value': 301225.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 90}]
2025-05-30 12:00:44,065 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-30 12:00:44,528 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-30 12:00:44,528 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6099.74, 'new_value': 6128.34}, {'field': 'offline_amount', 'old_value': 137149.58, 'new_value': 140934.12}, {'field': 'total_amount', 'old_value': 143249.32, 'new_value': 147062.46}, {'field': 'order_count', 'old_value': 2298, 'new_value': 2363}]
2025-05-30 12:00:44,528 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-30 12:00:44,969 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-30 12:00:44,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 245369.0, 'new_value': 253936.0}, {'field': 'total_amount', 'old_value': 245369.0, 'new_value': 253936.0}, {'field': 'order_count', 'old_value': 1293, 'new_value': 1343}]
2025-05-30 12:00:44,969 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-30 12:00:45,393 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-30 12:00:45,393 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85847.32, 'new_value': 88853.78}, {'field': 'total_amount', 'old_value': 85847.32, 'new_value': 88853.78}, {'field': 'order_count', 'old_value': 4509, 'new_value': 4684}]
2025-05-30 12:00:45,393 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-30 12:00:45,819 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-30 12:00:45,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 232176.58, 'new_value': 238052.38}, {'field': 'total_amount', 'old_value': 232176.58, 'new_value': 238052.38}, {'field': 'order_count', 'old_value': 791, 'new_value': 815}]
2025-05-30 12:00:45,819 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-30 12:00:46,352 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-30 12:00:46,353 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84821.03, 'new_value': 86798.07}, {'field': 'total_amount', 'old_value': 89629.98, 'new_value': 91607.02}, {'field': 'order_count', 'old_value': 5344, 'new_value': 5482}]
2025-05-30 12:00:46,353 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-30 12:00:46,906 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-30 12:00:46,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162833.0, 'new_value': 168411.0}, {'field': 'total_amount', 'old_value': 162833.0, 'new_value': 168411.0}, {'field': 'order_count', 'old_value': 4118, 'new_value': 4274}]
2025-05-30 12:00:46,906 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-30 12:00:47,405 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-30 12:00:47,405 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96205.38, 'new_value': 99040.92}, {'field': 'offline_amount', 'old_value': 966944.26, 'new_value': 1007077.65}, {'field': 'total_amount', 'old_value': 1063149.64, 'new_value': 1106118.57}, {'field': 'order_count', 'old_value': 3355, 'new_value': 3474}]
2025-05-30 12:00:47,405 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-30 12:00:47,853 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-30 12:00:47,854 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36071.5, 'new_value': 36491.5}, {'field': 'total_amount', 'old_value': 36071.5, 'new_value': 36491.5}, {'field': 'order_count', 'old_value': 68, 'new_value': 70}]
2025-05-30 12:00:47,854 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-30 12:00:48,331 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-30 12:00:48,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 777811.74, 'new_value': 802235.28}, {'field': 'total_amount', 'old_value': 777811.74, 'new_value': 802235.28}, {'field': 'order_count', 'old_value': 4561, 'new_value': 4798}]
2025-05-30 12:00:48,331 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-30 12:00:48,834 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-30 12:00:48,834 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96154.0, 'new_value': 98153.0}, {'field': 'total_amount', 'old_value': 96154.0, 'new_value': 98153.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-30 12:00:48,835 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-30 12:00:49,284 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-30 12:00:49,285 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4922.0, 'new_value': 4961.0}, {'field': 'offline_amount', 'old_value': 22933.0, 'new_value': 23664.0}, {'field': 'total_amount', 'old_value': 27855.0, 'new_value': 28625.0}, {'field': 'order_count', 'old_value': 165, 'new_value': 169}]
2025-05-30 12:00:49,285 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-30 12:00:49,734 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-30 12:00:49,734 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 344372.68, 'new_value': 358300.95}, {'field': 'offline_amount', 'old_value': 809.0, 'new_value': 887.0}, {'field': 'total_amount', 'old_value': 345181.68, 'new_value': 359187.95}, {'field': 'order_count', 'old_value': 3980, 'new_value': 4148}]
2025-05-30 12:00:49,735 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-30 12:00:50,209 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-30 12:00:50,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202498.54, 'new_value': 207254.16}, {'field': 'total_amount', 'old_value': 202498.54, 'new_value': 207254.16}, {'field': 'order_count', 'old_value': 1165, 'new_value': 1190}]
2025-05-30 12:00:50,209 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-30 12:00:50,651 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-30 12:00:50,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 223091.82, 'new_value': 233074.63}, {'field': 'offline_amount', 'old_value': 459706.17, 'new_value': 468890.31}, {'field': 'total_amount', 'old_value': 682797.99, 'new_value': 701964.94}, {'field': 'order_count', 'old_value': 5119, 'new_value': 5290}]
2025-05-30 12:00:50,651 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-30 12:00:51,040 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-30 12:00:51,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30809.47, 'new_value': 31446.47}, {'field': 'total_amount', 'old_value': 30809.47, 'new_value': 31446.47}, {'field': 'order_count', 'old_value': 181, 'new_value': 188}]
2025-05-30 12:00:51,040 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-30 12:00:51,461 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-30 12:00:51,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75748.11, 'new_value': 84182.24}, {'field': 'total_amount', 'old_value': 75748.11, 'new_value': 84182.24}, {'field': 'order_count', 'old_value': 3406, 'new_value': 3812}]
2025-05-30 12:00:51,461 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-30 12:00:51,930 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-30 12:00:51,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 280463.65, 'new_value': 284736.45}, {'field': 'offline_amount', 'old_value': 137331.0, 'new_value': 139858.0}, {'field': 'total_amount', 'old_value': 417794.65, 'new_value': 424594.45}, {'field': 'order_count', 'old_value': 2128, 'new_value': 2166}]
2025-05-30 12:00:51,931 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-30 12:00:52,346 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-30 12:00:52,346 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40976.9, 'new_value': 41508.9}, {'field': 'total_amount', 'old_value': 40976.9, 'new_value': 41508.9}, {'field': 'order_count', 'old_value': 182, 'new_value': 185}]
2025-05-30 12:00:52,346 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-30 12:00:52,828 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-30 12:00:52,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60205.6, 'new_value': 61014.6}, {'field': 'total_amount', 'old_value': 60205.6, 'new_value': 61014.6}, {'field': 'order_count', 'old_value': 257, 'new_value': 265}]
2025-05-30 12:00:52,829 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-30 12:00:53,292 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-30 12:00:53,292 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 218113.62, 'new_value': 226490.62}, {'field': 'total_amount', 'old_value': 218113.62, 'new_value': 226490.62}, {'field': 'order_count', 'old_value': 11972, 'new_value': 12152}]
2025-05-30 12:00:53,292 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-30 12:00:53,805 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-30 12:00:53,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123886.0, 'new_value': 129012.0}, {'field': 'total_amount', 'old_value': 123889.0, 'new_value': 129015.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 53}]
2025-05-30 12:00:53,805 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-30 12:00:54,233 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-30 12:00:54,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192742.8, 'new_value': 194911.8}, {'field': 'total_amount', 'old_value': 200318.6, 'new_value': 202487.6}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-30 12:00:54,233 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-30 12:00:54,639 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-30 12:00:54,639 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52194.02, 'new_value': 54110.99}, {'field': 'offline_amount', 'old_value': 100142.7, 'new_value': 101903.83}, {'field': 'total_amount', 'old_value': 152336.72, 'new_value': 156014.82}, {'field': 'order_count', 'old_value': 5610, 'new_value': 5771}]
2025-05-30 12:00:54,639 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-30 12:00:55,081 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-30 12:00:55,081 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80521.49, 'new_value': 81121.49}, {'field': 'total_amount', 'old_value': 84290.59, 'new_value': 84890.59}, {'field': 'order_count', 'old_value': 419, 'new_value': 421}]
2025-05-30 12:00:55,081 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-30 12:00:55,534 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-30 12:00:55,534 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182106.0, 'new_value': 190615.0}, {'field': 'total_amount', 'old_value': 253925.0, 'new_value': 262434.0}, {'field': 'order_count', 'old_value': 5678, 'new_value': 5871}]
2025-05-30 12:00:55,534 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-30 12:00:55,976 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-30 12:00:55,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15509.7, 'new_value': 15924.57}, {'field': 'offline_amount', 'old_value': 321793.98, 'new_value': 327846.88}, {'field': 'total_amount', 'old_value': 337303.68, 'new_value': 343771.45}, {'field': 'order_count', 'old_value': 2319, 'new_value': 2360}]
2025-05-30 12:00:55,977 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-30 12:00:56,392 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-30 12:00:56,393 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121385.64, 'new_value': 125364.64}, {'field': 'total_amount', 'old_value': 126725.64, 'new_value': 130704.64}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-05-30 12:00:56,393 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-30 12:00:56,873 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-30 12:00:56,873 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 383802.8, 'new_value': 385081.8}, {'field': 'total_amount', 'old_value': 410344.8, 'new_value': 411623.8}, {'field': 'order_count', 'old_value': 96, 'new_value': 99}]
2025-05-30 12:00:56,874 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-30 12:00:57,227 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-30 12:00:57,227 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 169019.43, 'new_value': 174998.36}, {'field': 'offline_amount', 'old_value': 125010.45, 'new_value': 126303.45}, {'field': 'total_amount', 'old_value': 294029.88, 'new_value': 301301.81}, {'field': 'order_count', 'old_value': 2909, 'new_value': 3001}]
2025-05-30 12:00:57,227 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-30 12:00:57,666 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-30 12:00:57,666 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 534871.04, 'new_value': 536052.04}, {'field': 'offline_amount', 'old_value': 246784.9, 'new_value': 248379.9}, {'field': 'total_amount', 'old_value': 781655.94, 'new_value': 784431.94}, {'field': 'order_count', 'old_value': 6828, 'new_value': 6845}]
2025-05-30 12:00:57,666 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-30 12:00:58,101 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-30 12:00:58,101 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43038.5, 'new_value': 43557.5}, {'field': 'offline_amount', 'old_value': 578.0, 'new_value': 579.0}, {'field': 'total_amount', 'old_value': 43616.5, 'new_value': 44136.5}, {'field': 'order_count', 'old_value': 179, 'new_value': 180}]
2025-05-30 12:00:58,101 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-30 12:00:58,546 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-30 12:00:58,546 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10656.3, 'new_value': 11310.2}, {'field': 'total_amount', 'old_value': 68168.4, 'new_value': 68822.3}, {'field': 'order_count', 'old_value': 84, 'new_value': 87}]
2025-05-30 12:00:58,546 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-30 12:00:59,070 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-30 12:00:59,070 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 154596.28, 'new_value': 161852.65}, {'field': 'offline_amount', 'old_value': 481035.53, 'new_value': 491183.67}, {'field': 'total_amount', 'old_value': 635631.81, 'new_value': 653036.32}, {'field': 'order_count', 'old_value': 3057, 'new_value': 3101}]
2025-05-30 12:00:59,071 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-30 12:00:59,470 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-30 12:00:59,470 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 181419.7, 'new_value': 183507.6}, {'field': 'total_amount', 'old_value': 181419.7, 'new_value': 183507.6}, {'field': 'order_count', 'old_value': 424, 'new_value': 429}]
2025-05-30 12:00:59,470 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-30 12:00:59,867 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-30 12:00:59,867 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 209079.79, 'new_value': 215993.94}, {'field': 'total_amount', 'old_value': 329514.63, 'new_value': 336428.78}, {'field': 'order_count', 'old_value': 3441, 'new_value': 3515}]
2025-05-30 12:00:59,867 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-30 12:01:00,265 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-30 12:01:00,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16904.0, 'new_value': 17188.0}, {'field': 'total_amount', 'old_value': 16904.0, 'new_value': 17188.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 39}]
2025-05-30 12:01:00,266 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-30 12:01:00,753 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-30 12:01:00,753 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104076.01, 'new_value': 105514.26}, {'field': 'total_amount', 'old_value': 104076.01, 'new_value': 105514.26}, {'field': 'order_count', 'old_value': 3974, 'new_value': 4036}]
2025-05-30 12:01:00,753 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-30 12:01:01,239 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-30 12:01:01,240 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11140.93, 'new_value': 11291.98}, {'field': 'total_amount', 'old_value': 27868.26, 'new_value': 28019.31}, {'field': 'order_count', 'old_value': 119, 'new_value': 120}]
2025-05-30 12:01:01,240 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-30 12:01:01,683 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-30 12:01:01,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48524.0, 'new_value': 51267.0}, {'field': 'total_amount', 'old_value': 48524.0, 'new_value': 51267.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 100}]
2025-05-30 12:01:01,683 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-30 12:01:02,258 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-30 12:01:02,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115293.34, 'new_value': 116859.14}, {'field': 'total_amount', 'old_value': 115293.34, 'new_value': 116859.14}, {'field': 'order_count', 'old_value': 841, 'new_value': 855}]
2025-05-30 12:01:02,259 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-30 12:01:02,707 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-30 12:01:02,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3092.15, 'new_value': 3266.31}, {'field': 'offline_amount', 'old_value': 26411.08, 'new_value': 27602.98}, {'field': 'total_amount', 'old_value': 29503.23, 'new_value': 30869.29}, {'field': 'order_count', 'old_value': 1353, 'new_value': 1421}]
2025-05-30 12:01:02,707 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-30 12:01:03,186 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-30 12:01:03,186 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55823.0, 'new_value': 57850.0}, {'field': 'total_amount', 'old_value': 55823.0, 'new_value': 57850.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 39}]
2025-05-30 12:01:03,187 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-30 12:01:03,755 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-30 12:01:03,755 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8179.0, 'new_value': 8239.0}, {'field': 'total_amount', 'old_value': 8179.0, 'new_value': 8239.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-05-30 12:01:03,755 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-30 12:01:04,171 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-30 12:01:04,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111645.1, 'new_value': 112932.3}, {'field': 'total_amount', 'old_value': 111645.1, 'new_value': 112932.3}, {'field': 'order_count', 'old_value': 349, 'new_value': 353}]
2025-05-30 12:01:04,171 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-30 12:01:04,573 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-30 12:01:04,573 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14886.03, 'new_value': 15874.63}, {'field': 'offline_amount', 'old_value': 139512.77, 'new_value': 146130.65}, {'field': 'total_amount', 'old_value': 154398.8, 'new_value': 162005.28}, {'field': 'order_count', 'old_value': 4058, 'new_value': 4253}]
2025-05-30 12:01:04,574 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-30 12:01:05,043 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-30 12:01:05,043 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 385640.5, 'new_value': 399088.99}, {'field': 'total_amount', 'old_value': 619977.19, 'new_value': 633425.68}, {'field': 'order_count', 'old_value': 17687, 'new_value': 18180}]
2025-05-30 12:01:05,043 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-30 12:01:05,544 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-30 12:01:05,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47245.0, 'new_value': 47374.0}, {'field': 'total_amount', 'old_value': 47245.0, 'new_value': 47374.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 134}]
2025-05-30 12:01:05,544 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-30 12:01:06,011 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-30 12:01:06,012 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39557.0, 'new_value': 40577.0}, {'field': 'total_amount', 'old_value': 39557.0, 'new_value': 40577.0}, {'field': 'order_count', 'old_value': 120, 'new_value': 123}]
2025-05-30 12:01:06,012 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-30 12:01:06,470 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-30 12:01:06,470 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1419000.0, 'new_value': 1454000.0}, {'field': 'total_amount', 'old_value': 1419000.0, 'new_value': 1454000.0}, {'field': 'order_count', 'old_value': 347, 'new_value': 348}]
2025-05-30 12:01:06,471 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-30 12:01:06,920 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-30 12:01:06,921 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22098.01, 'new_value': 23467.76}, {'field': 'offline_amount', 'old_value': 35693.39, 'new_value': 37031.97}, {'field': 'total_amount', 'old_value': 57791.4, 'new_value': 60499.73}, {'field': 'order_count', 'old_value': 2703, 'new_value': 2849}]
2025-05-30 12:01:06,921 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-30 12:01:07,388 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-30 12:01:07,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 965106.16, 'new_value': 1024548.16}, {'field': 'total_amount', 'old_value': 965106.16, 'new_value': 1024548.16}, {'field': 'order_count', 'old_value': 3811, 'new_value': 4047}]
2025-05-30 12:01:07,389 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-30 12:01:07,772 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-30 12:01:07,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57679.0, 'new_value': 57846.0}, {'field': 'total_amount', 'old_value': 60833.0, 'new_value': 61000.0}, {'field': 'order_count', 'old_value': 230, 'new_value': 232}]
2025-05-30 12:01:07,773 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-30 12:01:08,250 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-30 12:01:08,250 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38633.9, 'new_value': 40091.2}, {'field': 'offline_amount', 'old_value': 253279.0, 'new_value': 261539.4}, {'field': 'total_amount', 'old_value': 291912.9, 'new_value': 301630.6}, {'field': 'order_count', 'old_value': 2393, 'new_value': 2501}]
2025-05-30 12:01:08,250 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-30 12:01:08,747 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-30 12:01:08,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7498.0, 'new_value': 7734.0}, {'field': 'total_amount', 'old_value': 7498.0, 'new_value': 7734.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-05-30 12:01:08,748 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-30 12:01:09,161 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-30 12:01:09,162 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 327128.43, 'new_value': 339057.86}, {'field': 'total_amount', 'old_value': 327128.43, 'new_value': 339057.86}, {'field': 'order_count', 'old_value': 9113, 'new_value': 9465}]
2025-05-30 12:01:09,162 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-30 12:01:09,606 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-30 12:01:09,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157261.54, 'new_value': 169268.54}, {'field': 'total_amount', 'old_value': 157261.54, 'new_value': 169268.54}, {'field': 'order_count', 'old_value': 14086, 'new_value': 14523}]
2025-05-30 12:01:09,607 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-30 12:01:10,064 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-30 12:01:10,064 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15283.96, 'new_value': 15886.97}, {'field': 'offline_amount', 'old_value': 100606.68, 'new_value': 103060.22}, {'field': 'total_amount', 'old_value': 115890.64, 'new_value': 118947.19}, {'field': 'order_count', 'old_value': 3069, 'new_value': 3148}]
2025-05-30 12:01:10,064 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-30 12:01:10,523 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-30 12:01:10,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119552.0, 'new_value': 122127.0}, {'field': 'total_amount', 'old_value': 119552.0, 'new_value': 122127.0}, {'field': 'order_count', 'old_value': 1199, 'new_value': 1228}]
2025-05-30 12:01:10,524 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-30 12:01:10,992 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-30 12:01:10,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84214.37, 'new_value': 84303.37}, {'field': 'total_amount', 'old_value': 116089.27, 'new_value': 116178.27}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-30 12:01:10,992 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-30 12:01:11,432 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-30 12:01:11,432 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12760.9, 'new_value': 13264.78}, {'field': 'offline_amount', 'old_value': 13296.61, 'new_value': 13522.51}, {'field': 'total_amount', 'old_value': 26057.51, 'new_value': 26787.29}, {'field': 'order_count', 'old_value': 2145, 'new_value': 2220}]
2025-05-30 12:01:11,432 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-30 12:01:11,848 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-30 12:01:11,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7060275.0, 'new_value': 7289927.0}, {'field': 'total_amount', 'old_value': 7060275.0, 'new_value': 7289927.0}, {'field': 'order_count', 'old_value': 119908, 'new_value': 123744}]
2025-05-30 12:01:11,849 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-30 12:01:12,263 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-30 12:01:12,264 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54384.0, 'new_value': 55650.0}, {'field': 'total_amount', 'old_value': 54384.0, 'new_value': 55650.0}, {'field': 'order_count', 'old_value': 393, 'new_value': 401}]
2025-05-30 12:01:12,264 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-30 12:01:12,695 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-30 12:01:12,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63714.57, 'new_value': 65653.37}, {'field': 'offline_amount', 'old_value': 422208.17, 'new_value': 426101.54}, {'field': 'total_amount', 'old_value': 485922.74, 'new_value': 491754.91}, {'field': 'order_count', 'old_value': 4058, 'new_value': 4111}]
2025-05-30 12:01:12,695 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-30 12:01:13,200 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-30 12:01:13,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 519267.5, 'new_value': 527045.5}, {'field': 'total_amount', 'old_value': 567293.48, 'new_value': 575071.48}, {'field': 'order_count', 'old_value': 4467, 'new_value': 4550}]
2025-05-30 12:01:13,200 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-30 12:01:13,652 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-30 12:01:13,652 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135016.0, 'new_value': 138416.0}, {'field': 'total_amount', 'old_value': 135016.0, 'new_value': 138416.0}, {'field': 'order_count', 'old_value': 4955, 'new_value': 4956}]
2025-05-30 12:01:13,652 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-30 12:01:14,119 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-30 12:01:14,119 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95653.37, 'new_value': 97859.57}, {'field': 'offline_amount', 'old_value': 41171.31, 'new_value': 41751.81}, {'field': 'total_amount', 'old_value': 136824.68, 'new_value': 139611.38}, {'field': 'order_count', 'old_value': 8410, 'new_value': 8615}]
2025-05-30 12:01:14,119 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-30 12:01:14,639 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-30 12:01:14,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35028.07, 'new_value': 36128.07}, {'field': 'total_amount', 'old_value': 35028.07, 'new_value': 36128.07}, {'field': 'order_count', 'old_value': 3347, 'new_value': 3348}]
2025-05-30 12:01:14,640 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-30 12:01:15,066 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-30 12:01:15,066 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180137.43, 'new_value': 181951.43}, {'field': 'total_amount', 'old_value': 180137.43, 'new_value': 181951.43}, {'field': 'order_count', 'old_value': 308, 'new_value': 313}]
2025-05-30 12:01:15,066 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-30 12:01:15,525 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-30 12:01:15,526 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61688.1, 'new_value': 63240.1}, {'field': 'offline_amount', 'old_value': 37467.64, 'new_value': 39552.64}, {'field': 'total_amount', 'old_value': 99155.74, 'new_value': 102792.74}, {'field': 'order_count', 'old_value': 11880, 'new_value': 12153}]
2025-05-30 12:01:15,526 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-30 12:01:16,088 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-30 12:01:16,088 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117678.98, 'new_value': 119700.98}, {'field': 'total_amount', 'old_value': 117678.98, 'new_value': 119700.98}, {'field': 'order_count', 'old_value': 1076, 'new_value': 1096}]
2025-05-30 12:01:16,089 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-30 12:01:16,789 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-30 12:01:16,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109803.78, 'new_value': 113457.78}, {'field': 'offline_amount', 'old_value': 274352.01, 'new_value': 280803.65}, {'field': 'total_amount', 'old_value': 384155.79, 'new_value': 394261.43}, {'field': 'order_count', 'old_value': 13012, 'new_value': 13395}]
2025-05-30 12:01:16,790 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-30 12:01:17,257 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-30 12:01:17,257 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104990.95, 'new_value': 112217.17}, {'field': 'offline_amount', 'old_value': 329354.28, 'new_value': 349422.37}, {'field': 'total_amount', 'old_value': 434345.23, 'new_value': 461639.54}, {'field': 'order_count', 'old_value': 5432, 'new_value': 5951}]
2025-05-30 12:01:17,257 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-30 12:01:17,709 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-30 12:01:17,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7764500.0, 'new_value': 7814500.0}, {'field': 'total_amount', 'old_value': 7764500.0, 'new_value': 7814500.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 72}]
2025-05-30 12:01:17,709 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-30 12:01:18,165 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-30 12:01:18,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105784.8, 'new_value': 106022.8}, {'field': 'total_amount', 'old_value': 113134.8, 'new_value': 113372.8}, {'field': 'order_count', 'old_value': 48, 'new_value': 49}]
2025-05-30 12:01:18,165 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-30 12:01:18,649 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-30 12:01:18,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84595.0, 'new_value': 87622.0}, {'field': 'total_amount', 'old_value': 84595.0, 'new_value': 87622.0}, {'field': 'order_count', 'old_value': 4794, 'new_value': 4958}]
2025-05-30 12:01:18,649 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-30 12:01:19,109 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-30 12:01:19,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51608.5, 'new_value': 57855.5}, {'field': 'total_amount', 'old_value': 51608.5, 'new_value': 57855.5}, {'field': 'order_count', 'old_value': 2590, 'new_value': 2896}]
2025-05-30 12:01:19,109 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-30 12:01:19,576 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-30 12:01:19,576 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54730.7, 'new_value': 56837.9}, {'field': 'total_amount', 'old_value': 57031.0, 'new_value': 59138.2}, {'field': 'order_count', 'old_value': 176, 'new_value': 183}]
2025-05-30 12:01:19,577 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-30 12:01:19,988 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-30 12:01:19,988 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 343076.85, 'new_value': 353401.07}, {'field': 'offline_amount', 'old_value': 19823.74, 'new_value': 22430.54}, {'field': 'total_amount', 'old_value': 362900.59, 'new_value': 375831.61}, {'field': 'order_count', 'old_value': 14476, 'new_value': 14942}]
2025-05-30 12:01:19,989 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-30 12:01:20,459 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-30 12:01:20,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125066.9, 'new_value': 126263.8}, {'field': 'total_amount', 'old_value': 146720.8, 'new_value': 147917.7}, {'field': 'order_count', 'old_value': 200, 'new_value': 202}]
2025-05-30 12:01:20,459 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-30 12:01:20,899 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-30 12:01:20,899 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54824.87, 'new_value': 58366.33}, {'field': 'offline_amount', 'old_value': 258068.3, 'new_value': 273249.3}, {'field': 'total_amount', 'old_value': 312893.17, 'new_value': 331615.63}, {'field': 'order_count', 'old_value': 6067, 'new_value': 6481}]
2025-05-30 12:01:20,900 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-30 12:01:21,311 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-30 12:01:21,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46358.0, 'new_value': 47768.0}, {'field': 'total_amount', 'old_value': 46735.0, 'new_value': 48145.0}, {'field': 'order_count', 'old_value': 250, 'new_value': 324}]
2025-05-30 12:01:21,312 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-30 12:01:21,733 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-30 12:01:21,733 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 448715.0, 'new_value': 449215.0}, {'field': 'total_amount', 'old_value': 457533.99, 'new_value': 458033.99}, {'field': 'order_count', 'old_value': 82, 'new_value': 83}]
2025-05-30 12:01:21,734 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-30 12:01:22,273 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-30 12:01:22,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 145288.23, 'new_value': 149221.58}, {'field': 'offline_amount', 'old_value': 295375.92, 'new_value': 300227.81}, {'field': 'total_amount', 'old_value': 440664.15, 'new_value': 449449.39}, {'field': 'order_count', 'old_value': 5502, 'new_value': 5607}]
2025-05-30 12:01:22,273 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-30 12:01:22,788 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-30 12:01:22,788 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37266.0, 'new_value': 38721.0}, {'field': 'offline_amount', 'old_value': 312567.0, 'new_value': 338486.0}, {'field': 'total_amount', 'old_value': 349833.0, 'new_value': 377207.0}, {'field': 'order_count', 'old_value': 322, 'new_value': 348}]
2025-05-30 12:01:22,788 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-30 12:01:23,237 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-30 12:01:23,237 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 340864.4, 'new_value': 354806.4}, {'field': 'total_amount', 'old_value': 340864.4, 'new_value': 354806.4}, {'field': 'order_count', 'old_value': 152, 'new_value': 155}]
2025-05-30 12:01:23,238 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-30 12:01:23,716 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-30 12:01:23,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83031.65, 'new_value': 83912.65}, {'field': 'total_amount', 'old_value': 83031.65, 'new_value': 83912.65}, {'field': 'order_count', 'old_value': 610, 'new_value': 616}]
2025-05-30 12:01:23,716 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-30 12:01:24,208 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-30 12:01:24,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 344481.42, 'new_value': 354288.85}, {'field': 'total_amount', 'old_value': 377561.63, 'new_value': 387369.06}, {'field': 'order_count', 'old_value': 15962, 'new_value': 16406}]
2025-05-30 12:01:24,208 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-30 12:01:24,700 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-30 12:01:24,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 456709.0, 'new_value': 478521.0}, {'field': 'total_amount', 'old_value': 456709.0, 'new_value': 478521.0}, {'field': 'order_count', 'old_value': 12572, 'new_value': 13186}]
2025-05-30 12:01:24,700 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-30 12:01:25,250 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-30 12:01:25,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 486404.13, 'new_value': 506404.13}, {'field': 'total_amount', 'old_value': 486404.13, 'new_value': 506404.13}, {'field': 'order_count', 'old_value': 882, 'new_value': 883}]
2025-05-30 12:01:25,251 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-30 12:01:25,748 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-30 12:01:25,748 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87731.48, 'new_value': 91430.28}, {'field': 'offline_amount', 'old_value': 271726.85, 'new_value': 275453.71}, {'field': 'total_amount', 'old_value': 359458.33, 'new_value': 366883.99}, {'field': 'order_count', 'old_value': 4230, 'new_value': 4276}]
2025-05-30 12:01:25,748 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-30 12:01:26,143 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-30 12:01:26,143 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16914.0, 'new_value': 17153.0}, {'field': 'total_amount', 'old_value': 16914.0, 'new_value': 17153.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 35}]
2025-05-30 12:01:26,144 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-30 12:01:26,599 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-30 12:01:26,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31938.0, 'new_value': 32268.0}, {'field': 'total_amount', 'old_value': 31938.0, 'new_value': 32268.0}, {'field': 'order_count', 'old_value': 307, 'new_value': 311}]
2025-05-30 12:01:26,600 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-30 12:01:27,049 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-30 12:01:27,049 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49654.83, 'new_value': 50601.56}, {'field': 'total_amount', 'old_value': 49654.83, 'new_value': 50601.56}, {'field': 'order_count', 'old_value': 199, 'new_value': 203}]
2025-05-30 12:01:27,050 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-30 12:01:27,497 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-30 12:01:27,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94489.0, 'new_value': 96293.0}, {'field': 'total_amount', 'old_value': 104400.0, 'new_value': 106204.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 61}]
2025-05-30 12:01:27,498 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-30 12:01:27,951 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-30 12:01:27,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100696.0, 'new_value': 100896.0}, {'field': 'total_amount', 'old_value': 100696.0, 'new_value': 100896.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-05-30 12:01:27,952 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-30 12:01:28,405 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-30 12:01:28,405 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117121.15, 'new_value': 119921.25}, {'field': 'total_amount', 'old_value': 117121.15, 'new_value': 119921.25}, {'field': 'order_count', 'old_value': 580, 'new_value': 593}]
2025-05-30 12:01:28,405 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-30 12:01:28,841 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-30 12:01:28,841 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55930.05, 'new_value': 63714.68}, {'field': 'offline_amount', 'old_value': 135572.6, 'new_value': 140628.22}, {'field': 'total_amount', 'old_value': 191502.65, 'new_value': 204342.9}, {'field': 'order_count', 'old_value': 10483, 'new_value': 11038}]
2025-05-30 12:01:28,842 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-30 12:01:29,400 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-30 12:01:29,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30404.0, 'new_value': 30612.0}, {'field': 'total_amount', 'old_value': 30404.0, 'new_value': 30612.0}, {'field': 'order_count', 'old_value': 324, 'new_value': 326}]
2025-05-30 12:01:29,401 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-30 12:01:29,872 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-30 12:01:29,872 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87442.7, 'new_value': 88798.08}, {'field': 'offline_amount', 'old_value': 124903.09, 'new_value': 125535.49}, {'field': 'total_amount', 'old_value': 212345.79, 'new_value': 214333.57}, {'field': 'order_count', 'old_value': 2223, 'new_value': 2250}]
2025-05-30 12:01:29,873 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-30 12:01:30,297 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-30 12:01:30,297 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1867138.51, 'new_value': 1913862.36}, {'field': 'offline_amount', 'old_value': 230785.59, 'new_value': 252688.59}, {'field': 'total_amount', 'old_value': 2097924.1, 'new_value': 2166550.95}, {'field': 'order_count', 'old_value': 7284, 'new_value': 7595}]
2025-05-30 12:01:30,297 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-30 12:01:30,752 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-30 12:01:30,752 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37582.0, 'new_value': 38982.0}, {'field': 'total_amount', 'old_value': 38958.0, 'new_value': 40358.0}, {'field': 'order_count', 'old_value': 3909, 'new_value': 3910}]
2025-05-30 12:01:30,752 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-30 12:01:31,206 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-30 12:01:31,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152992.0, 'new_value': 164350.0}, {'field': 'total_amount', 'old_value': 152992.0, 'new_value': 164350.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-05-30 12:01:31,207 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-30 12:01:31,641 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-30 12:01:31,641 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18714.2, 'new_value': 18793.1}, {'field': 'offline_amount', 'old_value': 46830.9, 'new_value': 46949.9}, {'field': 'total_amount', 'old_value': 65545.1, 'new_value': 65743.0}, {'field': 'order_count', 'old_value': 227, 'new_value': 232}]
2025-05-30 12:01:31,641 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-30 12:01:32,099 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-30 12:01:32,099 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11739.0, 'new_value': 12013.0}, {'field': 'total_amount', 'old_value': 13747.0, 'new_value': 14021.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 134}]
2025-05-30 12:01:32,099 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-30 12:01:32,555 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-30 12:01:32,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30550.0, 'new_value': 30669.0}, {'field': 'total_amount', 'old_value': 30550.0, 'new_value': 30669.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 141}]
2025-05-30 12:01:32,555 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-30 12:01:32,987 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-30 12:01:32,987 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85695.0, 'new_value': 90565.0}, {'field': 'total_amount', 'old_value': 85695.0, 'new_value': 90565.0}, {'field': 'order_count', 'old_value': 594, 'new_value': 631}]
2025-05-30 12:01:32,988 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-30 12:01:33,436 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-30 12:01:33,436 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 119023.3, 'new_value': 126333.6}, {'field': 'offline_amount', 'old_value': 153918.4, 'new_value': 154390.9}, {'field': 'total_amount', 'old_value': 272941.7, 'new_value': 280724.5}, {'field': 'order_count', 'old_value': 5507, 'new_value': 5663}]
2025-05-30 12:01:33,436 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-30 12:01:33,873 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-30 12:01:33,873 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 512928.86, 'new_value': 523440.75}, {'field': 'total_amount', 'old_value': 512928.86, 'new_value': 523440.75}, {'field': 'order_count', 'old_value': 7092, 'new_value': 7275}]
2025-05-30 12:01:33,873 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-30 12:01:34,289 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-30 12:01:34,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28428.9, 'new_value': 28507.9}, {'field': 'total_amount', 'old_value': 40428.9, 'new_value': 40507.9}, {'field': 'order_count', 'old_value': 108, 'new_value': 109}]
2025-05-30 12:01:34,289 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-30 12:01:34,738 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-30 12:01:34,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 677848.55, 'new_value': 698092.72}, {'field': 'total_amount', 'old_value': 679761.6, 'new_value': 700005.77}, {'field': 'order_count', 'old_value': 1628, 'new_value': 1692}]
2025-05-30 12:01:34,738 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-30 12:01:35,207 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-30 12:01:35,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96425.28, 'new_value': 99173.36}, {'field': 'total_amount', 'old_value': 96425.28, 'new_value': 99173.36}, {'field': 'order_count', 'old_value': 3017, 'new_value': 3107}]
2025-05-30 12:01:35,207 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-30 12:01:35,740 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-30 12:01:35,740 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10677.3, 'new_value': 11153.2}, {'field': 'offline_amount', 'old_value': 34629.66, 'new_value': 35148.74}, {'field': 'total_amount', 'old_value': 45306.96, 'new_value': 46301.94}, {'field': 'order_count', 'old_value': 1592, 'new_value': 1618}]
2025-05-30 12:01:35,740 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-30 12:01:36,167 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-30 12:01:36,168 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157859.26, 'new_value': 160393.23}, {'field': 'total_amount', 'old_value': 157859.26, 'new_value': 160393.23}, {'field': 'order_count', 'old_value': 4107, 'new_value': 4180}]
2025-05-30 12:01:36,168 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-30 12:01:36,642 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-30 12:01:36,642 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104760.0, 'new_value': 112760.0}, {'field': 'total_amount', 'old_value': 104760.0, 'new_value': 112760.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-30 12:01:36,643 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-30 12:01:37,139 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-30 12:01:37,139 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39898.91, 'new_value': 40946.57}, {'field': 'offline_amount', 'old_value': 338170.32, 'new_value': 346679.07}, {'field': 'total_amount', 'old_value': 378069.23, 'new_value': 387625.64}, {'field': 'order_count', 'old_value': 8738, 'new_value': 8894}]
2025-05-30 12:01:37,139 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-30 12:01:37,536 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-30 12:01:37,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 358134.48, 'new_value': 365554.65}, {'field': 'total_amount', 'old_value': 358134.48, 'new_value': 365554.65}, {'field': 'order_count', 'old_value': 3469, 'new_value': 3591}]
2025-05-30 12:01:37,536 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-30 12:01:37,999 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-30 12:01:37,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79610.0, 'new_value': 79967.0}, {'field': 'total_amount', 'old_value': 79610.0, 'new_value': 79967.0}, {'field': 'order_count', 'old_value': 366, 'new_value': 369}]
2025-05-30 12:01:37,999 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-30 12:01:38,508 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-30 12:01:38,508 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9117.94, 'new_value': 9740.14}, {'field': 'offline_amount', 'old_value': 137345.5, 'new_value': 147348.0}, {'field': 'total_amount', 'old_value': 146463.44, 'new_value': 157088.14}, {'field': 'order_count', 'old_value': 7341, 'new_value': 7808}]
2025-05-30 12:01:38,508 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-30 12:01:38,979 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-30 12:01:38,980 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 447588.9, 'new_value': 452441.6}, {'field': 'total_amount', 'old_value': 447588.9, 'new_value': 452441.6}, {'field': 'order_count', 'old_value': 2196, 'new_value': 2228}]
2025-05-30 12:01:38,980 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-30 12:01:39,444 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-30 12:01:39,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 144418.83, 'new_value': 148162.25}, {'field': 'offline_amount', 'old_value': 58893.22, 'new_value': 60089.21}, {'field': 'total_amount', 'old_value': 203312.05, 'new_value': 208251.46}, {'field': 'order_count', 'old_value': 12422, 'new_value': 12707}]
2025-05-30 12:01:39,445 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-30 12:01:39,931 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-30 12:01:39,932 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18897.19, 'new_value': 19159.87}, {'field': 'offline_amount', 'old_value': 143789.78, 'new_value': 147584.68}, {'field': 'total_amount', 'old_value': 162686.97, 'new_value': 166744.55}, {'field': 'order_count', 'old_value': 4877, 'new_value': 4992}]
2025-05-30 12:01:39,932 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-30 12:01:40,389 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-30 12:01:40,389 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109537.5, 'new_value': 116500.4}, {'field': 'total_amount', 'old_value': 109537.5, 'new_value': 116500.4}, {'field': 'order_count', 'old_value': 5478, 'new_value': 5716}]
2025-05-30 12:01:40,390 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-30 12:01:40,846 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-30 12:01:40,846 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7634.0, 'new_value': 8160.0}, {'field': 'offline_amount', 'old_value': 36519.0, 'new_value': 36525.0}, {'field': 'total_amount', 'old_value': 44153.0, 'new_value': 44685.0}, {'field': 'order_count', 'old_value': 333, 'new_value': 340}]
2025-05-30 12:01:40,846 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-30 12:01:41,298 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-30 12:01:41,298 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148478.61, 'new_value': 150978.61}, {'field': 'total_amount', 'old_value': 244131.16, 'new_value': 246631.16}, {'field': 'order_count', 'old_value': 10794, 'new_value': 10795}]
2025-05-30 12:01:41,299 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-30 12:01:41,782 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-30 12:01:41,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 224332.0, 'new_value': 231365.0}, {'field': 'total_amount', 'old_value': 224332.0, 'new_value': 231365.0}, {'field': 'order_count', 'old_value': 265, 'new_value': 266}]
2025-05-30 12:01:41,782 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-30 12:01:42,241 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-30 12:01:42,241 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 753256.13, 'new_value': 764074.87}, {'field': 'total_amount', 'old_value': 753256.13, 'new_value': 764074.87}, {'field': 'order_count', 'old_value': 14290, 'new_value': 14518}]
2025-05-30 12:01:42,242 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-30 12:01:42,764 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-30 12:01:42,764 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 293756.67, 'new_value': 302489.94}, {'field': 'total_amount', 'old_value': 305807.64, 'new_value': 314540.91}, {'field': 'order_count', 'old_value': 13259, 'new_value': 13644}]
2025-05-30 12:01:42,765 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-30 12:01:43,210 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-30 12:01:43,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1025194.0, 'new_value': 1106735.0}, {'field': 'total_amount', 'old_value': 1025194.0, 'new_value': 1106735.0}, {'field': 'order_count', 'old_value': 2199, 'new_value': 2347}]
2025-05-30 12:01:43,210 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMJA
2025-05-30 12:01:43,655 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMJA
2025-05-30 12:01:43,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13560.0, 'new_value': 13650.0}, {'field': 'total_amount', 'old_value': 13560.0, 'new_value': 13650.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 91}]
2025-05-30 12:01:43,656 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-30 12:01:44,038 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-30 12:01:44,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14169.0, 'new_value': 14449.0}, {'field': 'total_amount', 'old_value': 14169.0, 'new_value': 14449.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-30 12:01:44,038 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-30 12:01:44,444 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-30 12:01:44,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 801157.04, 'new_value': 822254.41}, {'field': 'total_amount', 'old_value': 801157.04, 'new_value': 822254.41}, {'field': 'order_count', 'old_value': 6047, 'new_value': 6239}]
2025-05-30 12:01:44,444 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-30 12:01:44,903 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-30 12:01:44,904 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 226911.0, 'new_value': 229308.0}, {'field': 'total_amount', 'old_value': 226911.0, 'new_value': 229308.0}, {'field': 'order_count', 'old_value': 719, 'new_value': 731}]
2025-05-30 12:01:44,904 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-30 12:01:45,351 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-30 12:01:45,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68928.64, 'new_value': 72137.19}, {'field': 'offline_amount', 'old_value': 494372.78, 'new_value': 504709.38}, {'field': 'total_amount', 'old_value': 563301.42, 'new_value': 576846.57}, {'field': 'order_count', 'old_value': 2755, 'new_value': 2820}]
2025-05-30 12:01:45,352 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-30 12:01:45,782 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-30 12:01:45,782 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1918.24, 'new_value': 1983.82}, {'field': 'offline_amount', 'old_value': 23397.12, 'new_value': 23507.89}, {'field': 'total_amount', 'old_value': 25315.36, 'new_value': 25491.71}, {'field': 'order_count', 'old_value': 909, 'new_value': 915}]
2025-05-30 12:01:45,783 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-30 12:01:46,230 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-30 12:01:46,230 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7731.53, 'new_value': 7890.13}, {'field': 'offline_amount', 'old_value': 403007.54, 'new_value': 413983.84}, {'field': 'total_amount', 'old_value': 410739.07, 'new_value': 421873.97}, {'field': 'order_count', 'old_value': 19773, 'new_value': 20213}]
2025-05-30 12:01:46,230 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-30 12:01:46,672 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-30 12:01:46,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237727.74, 'new_value': 246229.74}, {'field': 'total_amount', 'old_value': 237727.74, 'new_value': 246229.74}, {'field': 'order_count', 'old_value': 1321, 'new_value': 1367}]
2025-05-30 12:01:46,673 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-30 12:01:47,106 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-30 12:01:47,106 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71893.24, 'new_value': 73739.7}, {'field': 'offline_amount', 'old_value': 89561.22, 'new_value': 90469.71}, {'field': 'total_amount', 'old_value': 161454.46, 'new_value': 164209.41}, {'field': 'order_count', 'old_value': 7456, 'new_value': 7581}]
2025-05-30 12:01:47,106 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-30 12:01:47,604 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-30 12:01:47,604 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 407758.79, 'new_value': 417352.0}, {'field': 'total_amount', 'old_value': 429921.91, 'new_value': 439515.12}, {'field': 'order_count', 'old_value': 18374, 'new_value': 18786}]
2025-05-30 12:01:47,605 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-30 12:01:48,103 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-30 12:01:48,104 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31846.8, 'new_value': 33145.93}, {'field': 'offline_amount', 'old_value': 255531.74, 'new_value': 260261.14}, {'field': 'total_amount', 'old_value': 287378.54, 'new_value': 293407.07}, {'field': 'order_count', 'old_value': 9022, 'new_value': 9229}]
2025-05-30 12:01:48,104 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-30 12:01:48,698 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-30 12:01:48,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 405119.34, 'new_value': 422283.34}, {'field': 'total_amount', 'old_value': 405119.34, 'new_value': 422283.34}, {'field': 'order_count', 'old_value': 2925, 'new_value': 3083}]
2025-05-30 12:01:48,698 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-30 12:01:49,137 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-30 12:01:49,137 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96925.0, 'new_value': 97791.0}, {'field': 'total_amount', 'old_value': 112129.0, 'new_value': 112995.0}, {'field': 'order_count', 'old_value': 2603, 'new_value': 2625}]
2025-05-30 12:01:49,137 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-30 12:01:49,592 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-30 12:01:49,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111250.0, 'new_value': 115750.0}, {'field': 'total_amount', 'old_value': 111250.0, 'new_value': 115750.0}, {'field': 'order_count', 'old_value': 756, 'new_value': 757}]
2025-05-30 12:01:49,593 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-30 12:01:50,061 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-30 12:01:50,061 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200172.24, 'new_value': 209330.29}, {'field': 'total_amount', 'old_value': 200172.24, 'new_value': 209330.29}, {'field': 'order_count', 'old_value': 2621, 'new_value': 2764}]
2025-05-30 12:01:50,061 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-30 12:01:50,533 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-30 12:01:50,533 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152969.0, 'new_value': 153733.0}, {'field': 'total_amount', 'old_value': 152969.0, 'new_value': 153733.0}, {'field': 'order_count', 'old_value': 4891, 'new_value': 4914}]
2025-05-30 12:01:50,533 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-30 12:01:51,043 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-30 12:01:51,043 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141092.82, 'new_value': 141377.82}, {'field': 'offline_amount', 'old_value': 379067.28, 'new_value': 388488.28}, {'field': 'total_amount', 'old_value': 520160.1, 'new_value': 529866.1}, {'field': 'order_count', 'old_value': 4631, 'new_value': 4651}]
2025-05-30 12:01:51,043 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-30 12:01:51,499 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-30 12:01:51,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 307485.46, 'new_value': 354285.46}, {'field': 'total_amount', 'old_value': 308956.46, 'new_value': 355756.46}, {'field': 'order_count', 'old_value': 57, 'new_value': 60}]
2025-05-30 12:01:51,499 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-30 12:01:51,958 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-30 12:01:51,958 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 228213.42, 'new_value': 229588.42}, {'field': 'total_amount', 'old_value': 345203.32, 'new_value': 346578.32}, {'field': 'order_count', 'old_value': 636, 'new_value': 638}]
2025-05-30 12:01:51,958 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-30 12:01:52,435 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-30 12:01:52,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257089.76, 'new_value': 261457.39}, {'field': 'total_amount', 'old_value': 276263.19, 'new_value': 280630.82}, {'field': 'order_count', 'old_value': 5706, 'new_value': 5807}]
2025-05-30 12:01:52,435 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-30 12:01:52,834 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-30 12:01:52,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 767490.72, 'new_value': 788126.72}, {'field': 'total_amount', 'old_value': 767490.72, 'new_value': 788126.72}, {'field': 'order_count', 'old_value': 5925, 'new_value': 6114}]
2025-05-30 12:01:52,835 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-30 12:01:53,296 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-30 12:01:53,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84542.3, 'new_value': 87067.96}, {'field': 'total_amount', 'old_value': 84542.3, 'new_value': 87067.96}, {'field': 'order_count', 'old_value': 4938, 'new_value': 5076}]
2025-05-30 12:01:53,297 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-30 12:01:53,751 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-30 12:01:53,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61417.0, 'new_value': 61505.0}, {'field': 'total_amount', 'old_value': 61417.0, 'new_value': 61505.0}, {'field': 'order_count', 'old_value': 446, 'new_value': 456}]
2025-05-30 12:01:53,751 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-30 12:01:54,214 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-30 12:01:54,214 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16800.45, 'new_value': 17343.45}, {'field': 'offline_amount', 'old_value': 316037.0, 'new_value': 327808.0}, {'field': 'total_amount', 'old_value': 332837.45, 'new_value': 345151.45}, {'field': 'order_count', 'old_value': 1775, 'new_value': 1844}]
2025-05-30 12:01:54,214 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-30 12:01:54,680 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-30 12:01:54,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207400.0, 'new_value': 216200.0}, {'field': 'total_amount', 'old_value': 207400.0, 'new_value': 216200.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-30 12:01:54,680 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-30 12:01:55,188 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-30 12:01:55,189 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-30 12:01:55,189 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-30 12:01:55,616 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-30 12:01:55,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36351.7, 'new_value': 37253.7}, {'field': 'total_amount', 'old_value': 36351.7, 'new_value': 37253.7}, {'field': 'order_count', 'old_value': 215, 'new_value': 221}]
2025-05-30 12:01:55,617 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-30 12:01:56,082 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-30 12:01:56,082 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 352974.5, 'new_value': 364447.5}, {'field': 'total_amount', 'old_value': 352974.5, 'new_value': 364447.5}, {'field': 'order_count', 'old_value': 63, 'new_value': 66}]
2025-05-30 12:01:56,082 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-30 12:01:56,523 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-30 12:01:56,523 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13408.0, 'new_value': 13484.0}, {'field': 'offline_amount', 'old_value': 8684.0, 'new_value': 8760.0}, {'field': 'total_amount', 'old_value': 22092.0, 'new_value': 22244.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 99}]
2025-05-30 12:01:56,523 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-30 12:01:56,982 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-30 12:01:56,982 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 353555.0, 'new_value': 366475.0}, {'field': 'total_amount', 'old_value': 380645.0, 'new_value': 393565.0}, {'field': 'order_count', 'old_value': 8222, 'new_value': 8592}]
2025-05-30 12:01:56,982 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-30 12:01:57,479 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-30 12:01:57,479 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83735.52, 'new_value': 85451.89}, {'field': 'offline_amount', 'old_value': 214459.65, 'new_value': 217642.34}, {'field': 'total_amount', 'old_value': 298195.17, 'new_value': 303094.23}, {'field': 'order_count', 'old_value': 5774, 'new_value': 5920}]
2025-05-30 12:01:57,479 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-30 12:01:57,914 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-30 12:01:57,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109124.0, 'new_value': 115824.0}, {'field': 'total_amount', 'old_value': 109124.0, 'new_value': 115824.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-30 12:01:57,915 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-30 12:01:58,344 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-30 12:01:58,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 763148.24, 'new_value': 779083.3}, {'field': 'total_amount', 'old_value': 763148.24, 'new_value': 779083.3}, {'field': 'order_count', 'old_value': 8935, 'new_value': 9156}]
2025-05-30 12:01:58,344 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-30 12:01:58,858 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-30 12:01:58,858 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 213605.8, 'new_value': 219131.9}, {'field': 'offline_amount', 'old_value': 476527.2, 'new_value': 478027.2}, {'field': 'total_amount', 'old_value': 690133.0, 'new_value': 697159.1}, {'field': 'order_count', 'old_value': 4884, 'new_value': 4950}]
2025-05-30 12:01:58,859 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-30 12:01:59,333 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-30 12:01:59,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191650.0, 'new_value': 194440.0}, {'field': 'total_amount', 'old_value': 191650.0, 'new_value': 194440.0}, {'field': 'order_count', 'old_value': 3222, 'new_value': 3269}]
2025-05-30 12:01:59,334 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-30 12:01:59,723 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-30 12:01:59,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196322.74, 'new_value': 200130.75}, {'field': 'total_amount', 'old_value': 196322.74, 'new_value': 200130.75}, {'field': 'order_count', 'old_value': 8394, 'new_value': 8548}]
2025-05-30 12:01:59,724 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-30 12:02:00,228 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-30 12:02:00,229 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 256041.77, 'new_value': 262749.7}, {'field': 'total_amount', 'old_value': 256041.77, 'new_value': 262749.7}, {'field': 'order_count', 'old_value': 1990, 'new_value': 2044}]
2025-05-30 12:02:00,229 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-30 12:02:00,716 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-30 12:02:00,716 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2155.7, 'new_value': 2463.6}, {'field': 'offline_amount', 'old_value': 90532.7, 'new_value': 92518.6}, {'field': 'total_amount', 'old_value': 92688.4, 'new_value': 94982.2}, {'field': 'order_count', 'old_value': 586, 'new_value': 600}]
2025-05-30 12:02:00,717 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-30 12:02:01,224 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-30 12:02:01,224 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69942.93, 'new_value': 70082.63}, {'field': 'offline_amount', 'old_value': 101110.73, 'new_value': 104110.73}, {'field': 'total_amount', 'old_value': 171053.66, 'new_value': 174193.36}, {'field': 'order_count', 'old_value': 4756, 'new_value': 4837}]
2025-05-30 12:02:01,224 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-30 12:02:01,664 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-30 12:02:01,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53028.0, 'new_value': 53604.0}, {'field': 'total_amount', 'old_value': 53028.0, 'new_value': 53604.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 129}]
2025-05-30 12:02:01,665 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-30 12:02:02,185 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-30 12:02:02,185 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40310.07, 'new_value': 41310.07}, {'field': 'offline_amount', 'old_value': 51529.0, 'new_value': 53243.85}, {'field': 'total_amount', 'old_value': 91839.07, 'new_value': 94553.92}, {'field': 'order_count', 'old_value': 4495, 'new_value': 4634}]
2025-05-30 12:02:02,186 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-30 12:02:02,644 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-30 12:02:02,645 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24776.43, 'new_value': 25739.43}, {'field': 'offline_amount', 'old_value': 16844.94, 'new_value': 17165.94}, {'field': 'total_amount', 'old_value': 41621.37, 'new_value': 42905.37}, {'field': 'order_count', 'old_value': 1733, 'new_value': 1769}]
2025-05-30 12:02:02,645 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-30 12:02:03,117 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-30 12:02:03,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 326275.0, 'new_value': 334251.0}, {'field': 'total_amount', 'old_value': 326275.0, 'new_value': 334251.0}, {'field': 'order_count', 'old_value': 496, 'new_value': 505}]
2025-05-30 12:02:03,118 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-30 12:02:03,583 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-30 12:02:03,584 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12858.88, 'new_value': 13278.0}, {'field': 'offline_amount', 'old_value': 70023.14, 'new_value': 72725.32}, {'field': 'total_amount', 'old_value': 82882.02, 'new_value': 86003.32}, {'field': 'order_count', 'old_value': 1904, 'new_value': 1988}]
2025-05-30 12:02:03,584 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-30 12:02:04,071 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-30 12:02:04,072 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21264.25, 'new_value': 23177.55}, {'field': 'offline_amount', 'old_value': 54552.8, 'new_value': 55552.8}, {'field': 'total_amount', 'old_value': 75817.05, 'new_value': 78730.35}, {'field': 'order_count', 'old_value': 661, 'new_value': 668}]
2025-05-30 12:02:04,072 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-30 12:02:04,515 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-30 12:02:04,516 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134943.26, 'new_value': 140525.58}, {'field': 'offline_amount', 'old_value': 232414.23, 'new_value': 237243.22}, {'field': 'total_amount', 'old_value': 367357.49, 'new_value': 377768.8}, {'field': 'order_count', 'old_value': 11404, 'new_value': 11784}]
2025-05-30 12:02:04,516 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-30 12:02:05,042 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-30 12:02:05,042 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119155.0, 'new_value': 121875.0}, {'field': 'total_amount', 'old_value': 119155.0, 'new_value': 121875.0}, {'field': 'order_count', 'old_value': 506, 'new_value': 516}]
2025-05-30 12:02:05,042 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-30 12:02:05,564 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-30 12:02:05,564 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 188181.71, 'new_value': 192906.85}, {'field': 'offline_amount', 'old_value': 56366.28, 'new_value': 57046.16}, {'field': 'total_amount', 'old_value': 244547.99, 'new_value': 249953.01}, {'field': 'order_count', 'old_value': 14005, 'new_value': 14346}]
2025-05-30 12:02:05,564 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-30 12:02:05,980 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-30 12:02:05,980 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138682.3, 'new_value': 145149.9}, {'field': 'offline_amount', 'old_value': 34320.8, 'new_value': 35182.8}, {'field': 'total_amount', 'old_value': 173003.1, 'new_value': 180332.7}, {'field': 'order_count', 'old_value': 14120, 'new_value': 14678}]
2025-05-30 12:02:05,980 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-30 12:02:06,438 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-30 12:02:06,439 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 291294.66, 'new_value': 295814.73}, {'field': 'total_amount', 'old_value': 313782.06, 'new_value': 318302.13}, {'field': 'order_count', 'old_value': 1762, 'new_value': 1797}]
2025-05-30 12:02:06,439 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-30 12:02:06,960 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-30 12:02:06,960 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 166126.78, 'new_value': 172616.79}, {'field': 'offline_amount', 'old_value': 311870.99, 'new_value': 318692.39}, {'field': 'total_amount', 'old_value': 477997.77, 'new_value': 491309.18}, {'field': 'order_count', 'old_value': 4028, 'new_value': 4148}]
2025-05-30 12:02:06,960 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-30 12:02:07,412 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-30 12:02:07,412 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46027.02, 'new_value': 47709.15}, {'field': 'offline_amount', 'old_value': 26624.12, 'new_value': 27074.35}, {'field': 'total_amount', 'old_value': 72651.14, 'new_value': 74783.5}, {'field': 'order_count', 'old_value': 3225, 'new_value': 3321}]
2025-05-30 12:02:07,412 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-30 12:02:07,875 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-30 12:02:07,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16025.11, 'new_value': 16494.4}, {'field': 'offline_amount', 'old_value': 37350.8, 'new_value': 38260.4}, {'field': 'total_amount', 'old_value': 53375.91, 'new_value': 54754.8}, {'field': 'order_count', 'old_value': 2121, 'new_value': 2178}]
2025-05-30 12:02:07,876 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-30 12:02:08,287 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-30 12:02:08,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112155.0, 'new_value': 125955.0}, {'field': 'total_amount', 'old_value': 112155.0, 'new_value': 125955.0}, {'field': 'order_count', 'old_value': 1698, 'new_value': 1699}]
2025-05-30 12:02:08,287 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-30 12:02:08,724 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-30 12:02:08,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 841661.29, 'new_value': 858896.29}, {'field': 'total_amount', 'old_value': 841661.29, 'new_value': 858896.29}, {'field': 'order_count', 'old_value': 6869, 'new_value': 7038}]
2025-05-30 12:02:08,725 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-30 12:02:09,144 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-30 12:02:09,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 548378.0, 'new_value': 555770.0}, {'field': 'total_amount', 'old_value': 548378.0, 'new_value': 555770.0}, {'field': 'order_count', 'old_value': 3970, 'new_value': 4057}]
2025-05-30 12:02:09,144 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-30 12:02:09,615 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-30 12:02:09,616 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28778.04, 'new_value': 29628.3}, {'field': 'offline_amount', 'old_value': 31584.18, 'new_value': 32429.78}, {'field': 'total_amount', 'old_value': 60362.22, 'new_value': 62058.08}, {'field': 'order_count', 'old_value': 2926, 'new_value': 3016}]
2025-05-30 12:02:09,616 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-30 12:02:10,043 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-30 12:02:10,043 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4051.0, 'new_value': 4194.0}, {'field': 'offline_amount', 'old_value': 30434.8, 'new_value': 30735.8}, {'field': 'total_amount', 'old_value': 34485.8, 'new_value': 34929.8}, {'field': 'order_count', 'old_value': 1252, 'new_value': 1265}]
2025-05-30 12:02:10,044 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-30 12:02:10,530 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-30 12:02:10,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 133181.1, 'new_value': 140823.58}, {'field': 'total_amount', 'old_value': 140410.17, 'new_value': 148052.65}, {'field': 'order_count', 'old_value': 754, 'new_value': 784}]
2025-05-30 12:02:10,531 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-30 12:02:11,009 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-30 12:02:11,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5580.0, 'new_value': 5668.0}, {'field': 'offline_amount', 'old_value': 23017.0, 'new_value': 23697.0}, {'field': 'total_amount', 'old_value': 28597.0, 'new_value': 29365.0}, {'field': 'order_count', 'old_value': 219, 'new_value': 225}]
2025-05-30 12:02:11,009 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-30 12:02:11,438 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-30 12:02:11,438 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71598.0, 'new_value': 72811.0}, {'field': 'offline_amount', 'old_value': 354384.0, 'new_value': 363910.0}, {'field': 'total_amount', 'old_value': 425982.0, 'new_value': 436721.0}, {'field': 'order_count', 'old_value': 1666, 'new_value': 1735}]
2025-05-30 12:02:11,438 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-30 12:02:11,930 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-30 12:02:11,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1113259.0, 'new_value': 1129787.0}, {'field': 'total_amount', 'old_value': 1113259.0, 'new_value': 1129787.0}, {'field': 'order_count', 'old_value': 4923, 'new_value': 5003}]
2025-05-30 12:02:11,930 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-30 12:02:12,433 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-30 12:02:12,433 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13602287.0, 'new_value': 13830101.0}, {'field': 'total_amount', 'old_value': 13602287.0, 'new_value': 13830101.0}, {'field': 'order_count', 'old_value': 43656, 'new_value': 44549}]
2025-05-30 12:02:12,434 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-30 12:02:12,902 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-30 12:02:12,902 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4020288.73, 'new_value': 4115848.14}, {'field': 'total_amount', 'old_value': 4020288.73, 'new_value': 4115848.14}, {'field': 'order_count', 'old_value': 6921, 'new_value': 7101}]
2025-05-30 12:02:12,902 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-30 12:02:13,317 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-30 12:02:13,317 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 181814.69, 'new_value': 187771.92}, {'field': 'total_amount', 'old_value': 189254.33, 'new_value': 195211.56}, {'field': 'order_count', 'old_value': 13261, 'new_value': 13735}]
2025-05-30 12:02:13,318 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-30 12:02:13,720 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-30 12:02:13,720 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 314802.06, 'new_value': 322784.03}, {'field': 'offline_amount', 'old_value': 223361.65, 'new_value': 228275.36}, {'field': 'total_amount', 'old_value': 538163.71, 'new_value': 551059.39}, {'field': 'order_count', 'old_value': 21873, 'new_value': 22423}]
2025-05-30 12:02:13,721 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-30 12:02:14,230 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-30 12:02:14,230 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51133.67, 'new_value': 52503.38}, {'field': 'offline_amount', 'old_value': 74438.26, 'new_value': 75917.31}, {'field': 'total_amount', 'old_value': 125571.93, 'new_value': 128420.69}, {'field': 'order_count', 'old_value': 2662, 'new_value': 2747}]
2025-05-30 12:02:14,230 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-30 12:02:14,694 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-30 12:02:14,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 314142.0, 'new_value': 324557.0}, {'field': 'total_amount', 'old_value': 314142.0, 'new_value': 324557.0}, {'field': 'order_count', 'old_value': 392, 'new_value': 404}]
2025-05-30 12:02:14,694 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-30 12:02:15,140 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-30 12:02:15,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81414.0, 'new_value': 85994.0}, {'field': 'total_amount', 'old_value': 89394.0, 'new_value': 93974.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-30 12:02:15,140 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-30 12:02:15,605 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-30 12:02:15,605 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 333022.0, 'new_value': 336433.0}, {'field': 'total_amount', 'old_value': 333022.0, 'new_value': 336433.0}, {'field': 'order_count', 'old_value': 7273, 'new_value': 7351}]
2025-05-30 12:02:15,605 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-30 12:02:16,073 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-30 12:02:16,074 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58798.9, 'new_value': 60558.9}, {'field': 'total_amount', 'old_value': 58798.9, 'new_value': 60558.9}, {'field': 'order_count', 'old_value': 322, 'new_value': 332}]
2025-05-30 12:02:16,074 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-30 12:02:16,504 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-30 12:02:16,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73620.0, 'new_value': 75956.0}, {'field': 'total_amount', 'old_value': 73620.0, 'new_value': 75956.0}, {'field': 'order_count', 'old_value': 14396, 'new_value': 14943}]
2025-05-30 12:02:16,505 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-30 12:02:17,013 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-30 12:02:17,013 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109077.0, 'new_value': 112581.0}, {'field': 'total_amount', 'old_value': 109077.0, 'new_value': 112581.0}, {'field': 'order_count', 'old_value': 14396, 'new_value': 14943}]
2025-05-30 12:02:17,014 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-30 12:02:17,449 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-30 12:02:17,449 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79202.2, 'new_value': 79779.1}, {'field': 'total_amount', 'old_value': 79482.0, 'new_value': 80058.9}, {'field': 'order_count', 'old_value': 1170, 'new_value': 1180}]
2025-05-30 12:02:17,449 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-30 12:02:17,961 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-30 12:02:17,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214254.74, 'new_value': 221164.11}, {'field': 'total_amount', 'old_value': 214254.74, 'new_value': 221164.11}, {'field': 'order_count', 'old_value': 15384, 'new_value': 15957}]
2025-05-30 12:02:17,962 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-05-30 12:02:18,468 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-05-30 12:02:18,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2372405.75, 'new_value': 2684405.75}, {'field': 'total_amount', 'old_value': 3937655.75, 'new_value': 4249655.75}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-30 12:02:18,468 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-30 12:02:18,945 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-30 12:02:18,945 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30750.8, 'new_value': 31902.3}, {'field': 'offline_amount', 'old_value': 69606.5, 'new_value': 72149.3}, {'field': 'total_amount', 'old_value': 100357.3, 'new_value': 104051.6}, {'field': 'order_count', 'old_value': 3783, 'new_value': 3931}]
2025-05-30 12:02:18,945 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-30 12:02:19,464 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-30 12:02:19,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42106.54, 'new_value': 43812.61}, {'field': 'total_amount', 'old_value': 42106.54, 'new_value': 43812.61}, {'field': 'order_count', 'old_value': 1991, 'new_value': 2076}]
2025-05-30 12:02:19,465 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-30 12:02:19,951 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-30 12:02:19,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5611407.67, 'new_value': 5813884.67}, {'field': 'total_amount', 'old_value': 5611407.67, 'new_value': 5813884.67}, {'field': 'order_count', 'old_value': 116133, 'new_value': 119655}]
2025-05-30 12:02:19,952 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-30 12:02:20,397 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-30 12:02:20,397 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 581245.39, 'new_value': 597375.39}, {'field': 'total_amount', 'old_value': 586791.75, 'new_value': 602921.75}, {'field': 'order_count', 'old_value': 5989, 'new_value': 6114}]
2025-05-30 12:02:20,397 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-30 12:02:20,918 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-30 12:02:20,918 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 228007.98, 'new_value': 235962.11}, {'field': 'total_amount', 'old_value': 228007.98, 'new_value': 235962.11}, {'field': 'order_count', 'old_value': 4147, 'new_value': 4282}]
2025-05-30 12:02:20,918 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-30 12:02:21,347 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-30 12:02:21,347 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88506.36, 'new_value': 91719.4}, {'field': 'total_amount', 'old_value': 88506.36, 'new_value': 91719.4}, {'field': 'order_count', 'old_value': 9322, 'new_value': 9707}]
2025-05-30 12:02:21,347 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-30 12:02:21,793 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-30 12:02:21,793 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 546384.0, 'new_value': 593028.0}, {'field': 'total_amount', 'old_value': 546384.0, 'new_value': 593028.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 104}]
2025-05-30 12:02:21,793 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-30 12:02:22,255 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-30 12:02:22,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142163.44, 'new_value': 146730.1}, {'field': 'total_amount', 'old_value': 147131.64, 'new_value': 151698.3}, {'field': 'order_count', 'old_value': 3737, 'new_value': 3841}]
2025-05-30 12:02:22,255 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-30 12:02:22,753 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-30 12:02:22,753 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112131.0, 'new_value': 114547.0}, {'field': 'total_amount', 'old_value': 112131.0, 'new_value': 114547.0}, {'field': 'order_count', 'old_value': 275, 'new_value': 280}]
2025-05-30 12:02:22,753 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-30 12:02:23,211 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-30 12:02:23,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39668.0, 'new_value': 40689.0}, {'field': 'total_amount', 'old_value': 39668.0, 'new_value': 40689.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 129}]
2025-05-30 12:02:23,212 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-30 12:02:23,726 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-30 12:02:23,726 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113569.94, 'new_value': 117251.14}, {'field': 'offline_amount', 'old_value': 432732.4, 'new_value': 439740.8}, {'field': 'total_amount', 'old_value': 546302.34, 'new_value': 556991.94}, {'field': 'order_count', 'old_value': 4040, 'new_value': 4160}]
2025-05-30 12:02:23,726 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-30 12:02:24,162 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-30 12:02:24,162 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93958.0, 'new_value': 97323.0}, {'field': 'total_amount', 'old_value': 95808.0, 'new_value': 99173.0}, {'field': 'order_count', 'old_value': 530, 'new_value': 553}]
2025-05-30 12:02:24,162 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-30 12:02:24,587 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-30 12:02:24,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2038.5, 'new_value': 2120.5}, {'field': 'offline_amount', 'old_value': 54030.6, 'new_value': 59511.6}, {'field': 'total_amount', 'old_value': 56069.1, 'new_value': 61632.1}, {'field': 'order_count', 'old_value': 362, 'new_value': 383}]
2025-05-30 12:02:24,587 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-30 12:02:25,037 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-30 12:02:25,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37137.08, 'new_value': 40477.39}, {'field': 'total_amount', 'old_value': 102132.49, 'new_value': 105472.8}, {'field': 'order_count', 'old_value': 6703, 'new_value': 6944}]
2025-05-30 12:02:25,038 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-30 12:02:25,439 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-30 12:02:25,439 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66631.0, 'new_value': 73772.18}, {'field': 'total_amount', 'old_value': 179960.28, 'new_value': 187101.46}, {'field': 'order_count', 'old_value': 11840, 'new_value': 12350}]
2025-05-30 12:02:25,439 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-30 12:02:25,921 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-30 12:02:25,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1300841.33, 'new_value': 1338069.09}, {'field': 'total_amount', 'old_value': 1300841.33, 'new_value': 1338069.09}, {'field': 'order_count', 'old_value': 3836, 'new_value': 3956}]
2025-05-30 12:02:25,922 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-30 12:02:26,403 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-30 12:02:26,403 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 213575.8, 'new_value': 223428.8}, {'field': 'total_amount', 'old_value': 213575.8, 'new_value': 223428.8}, {'field': 'order_count', 'old_value': 7486, 'new_value': 7816}]
2025-05-30 12:02:26,404 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-30 12:02:26,871 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-30 12:02:26,872 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 811972.09, 'new_value': 832884.25}, {'field': 'total_amount', 'old_value': 811972.09, 'new_value': 832884.25}, {'field': 'order_count', 'old_value': 4419, 'new_value': 4581}]
2025-05-30 12:02:26,872 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-30 12:02:27,288 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-30 12:02:27,288 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1192808.32, 'new_value': 1228944.13}, {'field': 'total_amount', 'old_value': 1192808.32, 'new_value': 1228944.13}, {'field': 'order_count', 'old_value': 4195, 'new_value': 4309}]
2025-05-30 12:02:27,288 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-30 12:02:27,693 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-30 12:02:27,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 908401.31, 'new_value': 933984.51}, {'field': 'total_amount', 'old_value': 908401.31, 'new_value': 933984.51}, {'field': 'order_count', 'old_value': 2562, 'new_value': 2616}]
2025-05-30 12:02:27,693 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-30 12:02:28,229 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-30 12:02:28,229 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46873.0, 'new_value': 47502.0}, {'field': 'total_amount', 'old_value': 46873.0, 'new_value': 47502.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 34}]
2025-05-30 12:02:28,230 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-30 12:02:28,686 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-30 12:02:28,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28907.0, 'new_value': 30039.48}, {'field': 'total_amount', 'old_value': 55414.0, 'new_value': 56546.48}, {'field': 'order_count', 'old_value': 26545, 'new_value': 26556}]
2025-05-30 12:02:28,687 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-30 12:02:29,155 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-30 12:02:29,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50973.4, 'new_value': 53496.8}, {'field': 'total_amount', 'old_value': 53817.4, 'new_value': 56340.8}, {'field': 'order_count', 'old_value': 403, 'new_value': 417}]
2025-05-30 12:02:29,156 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-30 12:02:29,588 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-30 12:02:29,589 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15505.0, 'new_value': 22201.0}, {'field': 'offline_amount', 'old_value': 70984.0, 'new_value': 66513.0}, {'field': 'total_amount', 'old_value': 86489.0, 'new_value': 88714.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 154}]
2025-05-30 12:02:29,589 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-30 12:02:29,985 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-30 12:02:29,985 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 153344.0, 'new_value': 159266.0}, {'field': 'offline_amount', 'old_value': 106850.0, 'new_value': 111451.0}, {'field': 'total_amount', 'old_value': 260194.0, 'new_value': 270717.0}, {'field': 'order_count', 'old_value': 10664, 'new_value': 11082}]
2025-05-30 12:02:29,985 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-30 12:02:30,436 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-30 12:02:30,436 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139483.0, 'new_value': 143929.0}, {'field': 'total_amount', 'old_value': 139483.0, 'new_value': 143929.0}, {'field': 'order_count', 'old_value': 683, 'new_value': 693}]
2025-05-30 12:02:30,437 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-30 12:02:30,909 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-30 12:02:30,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 317285.0, 'new_value': 329185.0}, {'field': 'total_amount', 'old_value': 317285.0, 'new_value': 329185.0}, {'field': 'order_count', 'old_value': 747, 'new_value': 774}]
2025-05-30 12:02:30,909 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-30 12:02:31,375 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-30 12:02:31,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227141.0, 'new_value': 240651.0}, {'field': 'total_amount', 'old_value': 227141.0, 'new_value': 240651.0}, {'field': 'order_count', 'old_value': 23925, 'new_value': 25308}]
2025-05-30 12:02:31,376 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-30 12:02:31,849 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-30 12:02:31,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169763.82, 'new_value': 172263.82}, {'field': 'total_amount', 'old_value': 169763.82, 'new_value': 172263.82}, {'field': 'order_count', 'old_value': 1445, 'new_value': 1446}]
2025-05-30 12:02:31,849 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-30 12:02:32,272 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-30 12:02:32,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41396.0, 'new_value': 41792.0}, {'field': 'total_amount', 'old_value': 41396.0, 'new_value': 41792.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-05-30 12:02:32,273 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-30 12:02:32,718 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-30 12:02:32,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5092.6, 'new_value': 5412.6}, {'field': 'total_amount', 'old_value': 5092.6, 'new_value': 5412.6}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-30 12:02:32,718 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-30 12:02:33,185 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-30 12:02:33,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55032.66, 'new_value': 55809.66}, {'field': 'total_amount', 'old_value': 55032.66, 'new_value': 55809.66}, {'field': 'order_count', 'old_value': 935, 'new_value': 951}]
2025-05-30 12:02:33,185 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-30 12:02:33,639 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-30 12:02:33,639 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 137327.87, 'new_value': 141001.77}, {'field': 'offline_amount', 'old_value': 746853.01, 'new_value': 761937.67}, {'field': 'total_amount', 'old_value': 884180.88, 'new_value': 902939.44}, {'field': 'order_count', 'old_value': 2077, 'new_value': 2141}]
2025-05-30 12:02:33,639 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-30 12:02:34,064 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-30 12:02:34,064 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84109.6, 'new_value': 87678.6}, {'field': 'total_amount', 'old_value': 84109.6, 'new_value': 87678.6}, {'field': 'order_count', 'old_value': 49, 'new_value': 50}]
2025-05-30 12:02:34,064 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-30 12:02:34,586 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-30 12:02:34,586 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100454.75, 'new_value': 104949.04}, {'field': 'offline_amount', 'old_value': 1008810.69, 'new_value': 1056931.48}, {'field': 'total_amount', 'old_value': 1107391.11, 'new_value': 1160006.19}, {'field': 'order_count', 'old_value': 5202, 'new_value': 5448}]
2025-05-30 12:02:34,586 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-30 12:02:35,029 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-30 12:02:35,029 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130524.0, 'new_value': 136002.0}, {'field': 'total_amount', 'old_value': 130524.0, 'new_value': 136002.0}, {'field': 'order_count', 'old_value': 401, 'new_value': 417}]
2025-05-30 12:02:35,029 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-30 12:02:35,484 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-30 12:02:35,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83557.0, 'new_value': 95897.0}, {'field': 'total_amount', 'old_value': 88875.0, 'new_value': 101215.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 48}]
2025-05-30 12:02:35,485 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-30 12:02:35,956 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-30 12:02:35,956 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19414401.64, 'new_value': 20079549.5}, {'field': 'total_amount', 'old_value': 19414401.64, 'new_value': 20079549.5}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-30 12:02:35,956 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-30 12:02:36,408 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-30 12:02:36,408 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14680.0, 'new_value': 17767.0}, {'field': 'offline_amount', 'old_value': 42857.36, 'new_value': 43872.36}, {'field': 'total_amount', 'old_value': 57537.36, 'new_value': 61639.36}, {'field': 'order_count', 'old_value': 3499, 'new_value': 3699}]
2025-05-30 12:02:36,409 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-30 12:02:36,863 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-30 12:02:36,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201901.22, 'new_value': 209129.02}, {'field': 'total_amount', 'old_value': 201901.22, 'new_value': 209129.02}, {'field': 'order_count', 'old_value': 21369, 'new_value': 22116}]
2025-05-30 12:02:36,864 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-30 12:02:37,308 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-30 12:02:37,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19798.0, 'new_value': 20034.0}, {'field': 'total_amount', 'old_value': 19798.0, 'new_value': 20034.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 129}]
2025-05-30 12:02:37,308 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-30 12:02:37,770 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-30 12:02:37,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63314.21, 'new_value': 68235.28}, {'field': 'total_amount', 'old_value': 74170.65, 'new_value': 79091.72}, {'field': 'order_count', 'old_value': 4617, 'new_value': 4798}]
2025-05-30 12:02:37,770 - INFO - 开始更新记录 - 表单实例ID: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-30 12:02:38,232 - INFO - 更新表单数据成功: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-30 12:02:38,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143767.04, 'new_value': 158830.74}, {'field': 'total_amount', 'old_value': 143767.04, 'new_value': 158830.74}, {'field': 'order_count', 'old_value': 6596, 'new_value': 7248}]
2025-05-30 12:02:38,233 - INFO - 开始更新记录 - 表单实例ID: FINST-F7D66UA12CKVOE62735DBBS3ITMX3ODLPMVAMO5
2025-05-30 12:02:38,690 - INFO - 更新表单数据成功: FINST-F7D66UA12CKVOE62735DBBS3ITMX3ODLPMVAMO5
2025-05-30 12:02:38,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1581.38, 'new_value': 2892.38}, {'field': 'total_amount', 'old_value': 1581.38, 'new_value': 2892.38}, {'field': 'order_count', 'old_value': 310, 'new_value': 589}]
2025-05-30 12:02:38,690 - INFO - 开始更新记录 - 表单实例ID: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-05-30 12:02:39,145 - INFO - 更新表单数据成功: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-05-30 12:02:39,146 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21678.98, 'new_value': 24023.19}, {'field': 'total_amount', 'old_value': 21678.98, 'new_value': 24023.19}, {'field': 'order_count', 'old_value': 915, 'new_value': 1006}]
2025-05-30 12:02:39,146 - INFO - 日期 2025-05 处理完成 - 更新: 278 条，插入: 0 条，错误: 0 条
2025-05-30 12:02:39,146 - INFO - 数据同步完成！更新: 278 条，插入: 0 条，错误: 0 条
2025-05-30 12:02:39,148 - INFO - =================同步完成====================
2025-05-30 15:00:01,946 - INFO - =================使用默认全量同步=============
2025-05-30 15:00:03,481 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-30 15:00:03,482 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-30 15:00:03,510 - INFO - 开始处理日期: 2025-01
2025-05-30 15:00:03,513 - INFO - Request Parameters - Page 1:
2025-05-30 15:00:03,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:03,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:05,027 - INFO - Response - Page 1:
2025-05-30 15:00:05,227 - INFO - 第 1 页获取到 100 条记录
2025-05-30 15:00:05,227 - INFO - Request Parameters - Page 2:
2025-05-30 15:00:05,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:05,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:05,760 - INFO - Response - Page 2:
2025-05-30 15:00:05,960 - INFO - 第 2 页获取到 100 条记录
2025-05-30 15:00:05,960 - INFO - Request Parameters - Page 3:
2025-05-30 15:00:05,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:05,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:06,478 - INFO - Response - Page 3:
2025-05-30 15:00:06,678 - INFO - 第 3 页获取到 100 条记录
2025-05-30 15:00:06,678 - INFO - Request Parameters - Page 4:
2025-05-30 15:00:06,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:06,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:07,177 - INFO - Response - Page 4:
2025-05-30 15:00:07,377 - INFO - 第 4 页获取到 100 条记录
2025-05-30 15:00:07,377 - INFO - Request Parameters - Page 5:
2025-05-30 15:00:07,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:07,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:07,917 - INFO - Response - Page 5:
2025-05-30 15:00:08,117 - INFO - 第 5 页获取到 100 条记录
2025-05-30 15:00:08,117 - INFO - Request Parameters - Page 6:
2025-05-30 15:00:08,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:08,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:08,645 - INFO - Response - Page 6:
2025-05-30 15:00:08,845 - INFO - 第 6 页获取到 100 条记录
2025-05-30 15:00:08,845 - INFO - Request Parameters - Page 7:
2025-05-30 15:00:08,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:08,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:09,421 - INFO - Response - Page 7:
2025-05-30 15:00:09,621 - INFO - 第 7 页获取到 82 条记录
2025-05-30 15:00:09,621 - INFO - 查询完成，共获取到 682 条记录
2025-05-30 15:00:09,621 - INFO - 获取到 682 条表单数据
2025-05-30 15:00:09,635 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-30 15:00:09,648 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 15:00:09,648 - INFO - 开始处理日期: 2025-02
2025-05-30 15:00:09,648 - INFO - Request Parameters - Page 1:
2025-05-30 15:00:09,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:09,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:10,135 - INFO - Response - Page 1:
2025-05-30 15:00:10,335 - INFO - 第 1 页获取到 100 条记录
2025-05-30 15:00:10,335 - INFO - Request Parameters - Page 2:
2025-05-30 15:00:10,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:10,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:11,082 - INFO - Response - Page 2:
2025-05-30 15:00:11,282 - INFO - 第 2 页获取到 100 条记录
2025-05-30 15:00:11,282 - INFO - Request Parameters - Page 3:
2025-05-30 15:00:11,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:11,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:11,826 - INFO - Response - Page 3:
2025-05-30 15:00:12,027 - INFO - 第 3 页获取到 100 条记录
2025-05-30 15:00:12,027 - INFO - Request Parameters - Page 4:
2025-05-30 15:00:12,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:12,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:12,604 - INFO - Response - Page 4:
2025-05-30 15:00:12,804 - INFO - 第 4 页获取到 100 条记录
2025-05-30 15:00:12,804 - INFO - Request Parameters - Page 5:
2025-05-30 15:00:12,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:12,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:13,321 - INFO - Response - Page 5:
2025-05-30 15:00:13,522 - INFO - 第 5 页获取到 100 条记录
2025-05-30 15:00:13,522 - INFO - Request Parameters - Page 6:
2025-05-30 15:00:13,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:13,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:14,011 - INFO - Response - Page 6:
2025-05-30 15:00:14,211 - INFO - 第 6 页获取到 100 条记录
2025-05-30 15:00:14,211 - INFO - Request Parameters - Page 7:
2025-05-30 15:00:14,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:14,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:14,889 - INFO - Response - Page 7:
2025-05-30 15:00:15,089 - INFO - 第 7 页获取到 70 条记录
2025-05-30 15:00:15,089 - INFO - 查询完成，共获取到 670 条记录
2025-05-30 15:00:15,089 - INFO - 获取到 670 条表单数据
2025-05-30 15:00:15,102 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-30 15:00:15,113 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 15:00:15,113 - INFO - 开始处理日期: 2025-03
2025-05-30 15:00:15,113 - INFO - Request Parameters - Page 1:
2025-05-30 15:00:15,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:15,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:15,751 - INFO - Response - Page 1:
2025-05-30 15:00:15,951 - INFO - 第 1 页获取到 100 条记录
2025-05-30 15:00:15,951 - INFO - Request Parameters - Page 2:
2025-05-30 15:00:15,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:15,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:16,534 - INFO - Response - Page 2:
2025-05-30 15:00:16,735 - INFO - 第 2 页获取到 100 条记录
2025-05-30 15:00:16,735 - INFO - Request Parameters - Page 3:
2025-05-30 15:00:16,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:16,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:17,281 - INFO - Response - Page 3:
2025-05-30 15:00:17,482 - INFO - 第 3 页获取到 100 条记录
2025-05-30 15:00:17,482 - INFO - Request Parameters - Page 4:
2025-05-30 15:00:17,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:17,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:17,978 - INFO - Response - Page 4:
2025-05-30 15:00:18,178 - INFO - 第 4 页获取到 100 条记录
2025-05-30 15:00:18,178 - INFO - Request Parameters - Page 5:
2025-05-30 15:00:18,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:18,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:18,743 - INFO - Response - Page 5:
2025-05-30 15:00:18,943 - INFO - 第 5 页获取到 100 条记录
2025-05-30 15:00:18,943 - INFO - Request Parameters - Page 6:
2025-05-30 15:00:18,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:18,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:19,518 - INFO - Response - Page 6:
2025-05-30 15:00:19,719 - INFO - 第 6 页获取到 100 条记录
2025-05-30 15:00:19,719 - INFO - Request Parameters - Page 7:
2025-05-30 15:00:19,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:19,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:20,213 - INFO - Response - Page 7:
2025-05-30 15:00:20,414 - INFO - 第 7 页获取到 61 条记录
2025-05-30 15:00:20,414 - INFO - 查询完成，共获取到 661 条记录
2025-05-30 15:00:20,414 - INFO - 获取到 661 条表单数据
2025-05-30 15:00:20,427 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-30 15:00:20,438 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 15:00:20,438 - INFO - 开始处理日期: 2025-04
2025-05-30 15:00:20,438 - INFO - Request Parameters - Page 1:
2025-05-30 15:00:20,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:20,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:21,016 - INFO - Response - Page 1:
2025-05-30 15:00:21,216 - INFO - 第 1 页获取到 100 条记录
2025-05-30 15:00:21,216 - INFO - Request Parameters - Page 2:
2025-05-30 15:00:21,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:21,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:21,819 - INFO - Response - Page 2:
2025-05-30 15:00:22,020 - INFO - 第 2 页获取到 100 条记录
2025-05-30 15:00:22,020 - INFO - Request Parameters - Page 3:
2025-05-30 15:00:22,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:22,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:22,497 - INFO - Response - Page 3:
2025-05-30 15:00:22,697 - INFO - 第 3 页获取到 100 条记录
2025-05-30 15:00:22,697 - INFO - Request Parameters - Page 4:
2025-05-30 15:00:22,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:22,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:23,269 - INFO - Response - Page 4:
2025-05-30 15:00:23,469 - INFO - 第 4 页获取到 100 条记录
2025-05-30 15:00:23,469 - INFO - Request Parameters - Page 5:
2025-05-30 15:00:23,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:23,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:23,924 - INFO - Response - Page 5:
2025-05-30 15:00:24,124 - INFO - 第 5 页获取到 100 条记录
2025-05-30 15:00:24,124 - INFO - Request Parameters - Page 6:
2025-05-30 15:00:24,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:24,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:24,604 - INFO - Response - Page 6:
2025-05-30 15:00:24,804 - INFO - 第 6 页获取到 100 条记录
2025-05-30 15:00:24,804 - INFO - Request Parameters - Page 7:
2025-05-30 15:00:24,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:24,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:25,195 - INFO - Response - Page 7:
2025-05-30 15:00:25,396 - INFO - 第 7 页获取到 56 条记录
2025-05-30 15:00:25,396 - INFO - 查询完成，共获取到 656 条记录
2025-05-30 15:00:25,396 - INFO - 获取到 656 条表单数据
2025-05-30 15:00:25,407 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-30 15:00:25,419 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 15:00:25,419 - INFO - 开始处理日期: 2025-05
2025-05-30 15:00:25,419 - INFO - Request Parameters - Page 1:
2025-05-30 15:00:25,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:25,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:25,998 - INFO - Response - Page 1:
2025-05-30 15:00:26,198 - INFO - 第 1 页获取到 100 条记录
2025-05-30 15:00:26,198 - INFO - Request Parameters - Page 2:
2025-05-30 15:00:26,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:26,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:26,682 - INFO - Response - Page 2:
2025-05-30 15:00:26,882 - INFO - 第 2 页获取到 100 条记录
2025-05-30 15:00:26,882 - INFO - Request Parameters - Page 3:
2025-05-30 15:00:26,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:26,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:27,443 - INFO - Response - Page 3:
2025-05-30 15:00:27,644 - INFO - 第 3 页获取到 100 条记录
2025-05-30 15:00:27,644 - INFO - Request Parameters - Page 4:
2025-05-30 15:00:27,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:27,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:28,125 - INFO - Response - Page 4:
2025-05-30 15:00:28,326 - INFO - 第 4 页获取到 100 条记录
2025-05-30 15:00:28,326 - INFO - Request Parameters - Page 5:
2025-05-30 15:00:28,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:28,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:28,799 - INFO - Response - Page 5:
2025-05-30 15:00:28,999 - INFO - 第 5 页获取到 100 条记录
2025-05-30 15:00:28,999 - INFO - Request Parameters - Page 6:
2025-05-30 15:00:28,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:28,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:29,431 - INFO - Response - Page 6:
2025-05-30 15:00:29,631 - INFO - 第 6 页获取到 100 条记录
2025-05-30 15:00:29,631 - INFO - Request Parameters - Page 7:
2025-05-30 15:00:29,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 15:00:29,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 15:00:30,017 - INFO - Response - Page 7:
2025-05-30 15:00:30,217 - INFO - 第 7 页获取到 35 条记录
2025-05-30 15:00:30,217 - INFO - 查询完成，共获取到 635 条记录
2025-05-30 15:00:30,217 - INFO - 获取到 635 条表单数据
2025-05-30 15:00:30,229 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-30 15:00:30,237 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-30 15:00:30,689 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-30 15:00:30,689 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 218536.0, 'new_value': 219059.0}, {'field': 'total_amount', 'old_value': 218536.0, 'new_value': 219059.0}, {'field': 'order_count', 'old_value': 471, 'new_value': 472}]
2025-05-30 15:00:30,692 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-30 15:00:30,692 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-30 15:00:30,693 - INFO - =================同步完成====================
2025-05-30 18:00:01,997 - INFO - =================使用默认全量同步=============
2025-05-30 18:00:03,519 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-30 18:00:03,519 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-30 18:00:03,548 - INFO - 开始处理日期: 2025-01
2025-05-30 18:00:03,551 - INFO - Request Parameters - Page 1:
2025-05-30 18:00:03,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:03,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:04,505 - INFO - Response - Page 1:
2025-05-30 18:00:04,705 - INFO - 第 1 页获取到 100 条记录
2025-05-30 18:00:04,705 - INFO - Request Parameters - Page 2:
2025-05-30 18:00:04,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:04,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:05,601 - INFO - Response - Page 2:
2025-05-30 18:00:05,802 - INFO - 第 2 页获取到 100 条记录
2025-05-30 18:00:05,802 - INFO - Request Parameters - Page 3:
2025-05-30 18:00:05,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:05,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:06,282 - INFO - Response - Page 3:
2025-05-30 18:00:06,482 - INFO - 第 3 页获取到 100 条记录
2025-05-30 18:00:06,482 - INFO - Request Parameters - Page 4:
2025-05-30 18:00:06,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:06,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:07,001 - INFO - Response - Page 4:
2025-05-30 18:00:07,202 - INFO - 第 4 页获取到 100 条记录
2025-05-30 18:00:07,202 - INFO - Request Parameters - Page 5:
2025-05-30 18:00:07,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:07,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:07,734 - INFO - Response - Page 5:
2025-05-30 18:00:07,934 - INFO - 第 5 页获取到 100 条记录
2025-05-30 18:00:07,934 - INFO - Request Parameters - Page 6:
2025-05-30 18:00:07,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:07,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:08,478 - INFO - Response - Page 6:
2025-05-30 18:00:08,678 - INFO - 第 6 页获取到 100 条记录
2025-05-30 18:00:08,678 - INFO - Request Parameters - Page 7:
2025-05-30 18:00:08,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:08,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:09,192 - INFO - Response - Page 7:
2025-05-30 18:00:09,392 - INFO - 第 7 页获取到 82 条记录
2025-05-30 18:00:09,392 - INFO - 查询完成，共获取到 682 条记录
2025-05-30 18:00:09,392 - INFO - 获取到 682 条表单数据
2025-05-30 18:00:09,406 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-30 18:00:09,417 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 18:00:09,417 - INFO - 开始处理日期: 2025-02
2025-05-30 18:00:09,418 - INFO - Request Parameters - Page 1:
2025-05-30 18:00:09,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:09,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:10,017 - INFO - Response - Page 1:
2025-05-30 18:00:10,217 - INFO - 第 1 页获取到 100 条记录
2025-05-30 18:00:10,217 - INFO - Request Parameters - Page 2:
2025-05-30 18:00:10,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:10,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:10,743 - INFO - Response - Page 2:
2025-05-30 18:00:10,944 - INFO - 第 2 页获取到 100 条记录
2025-05-30 18:00:10,944 - INFO - Request Parameters - Page 3:
2025-05-30 18:00:10,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:10,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:11,426 - INFO - Response - Page 3:
2025-05-30 18:00:11,627 - INFO - 第 3 页获取到 100 条记录
2025-05-30 18:00:11,627 - INFO - Request Parameters - Page 4:
2025-05-30 18:00:11,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:11,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:12,127 - INFO - Response - Page 4:
2025-05-30 18:00:12,327 - INFO - 第 4 页获取到 100 条记录
2025-05-30 18:00:12,327 - INFO - Request Parameters - Page 5:
2025-05-30 18:00:12,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:12,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:12,799 - INFO - Response - Page 5:
2025-05-30 18:00:12,999 - INFO - 第 5 页获取到 100 条记录
2025-05-30 18:00:12,999 - INFO - Request Parameters - Page 6:
2025-05-30 18:00:12,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:12,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:13,473 - INFO - Response - Page 6:
2025-05-30 18:00:13,674 - INFO - 第 6 页获取到 100 条记录
2025-05-30 18:00:13,674 - INFO - Request Parameters - Page 7:
2025-05-30 18:00:13,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:13,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:14,152 - INFO - Response - Page 7:
2025-05-30 18:00:14,352 - INFO - 第 7 页获取到 70 条记录
2025-05-30 18:00:14,352 - INFO - 查询完成，共获取到 670 条记录
2025-05-30 18:00:14,352 - INFO - 获取到 670 条表单数据
2025-05-30 18:00:14,365 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-30 18:00:14,377 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 18:00:14,377 - INFO - 开始处理日期: 2025-03
2025-05-30 18:00:14,378 - INFO - Request Parameters - Page 1:
2025-05-30 18:00:14,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:14,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:14,840 - INFO - Response - Page 1:
2025-05-30 18:00:15,041 - INFO - 第 1 页获取到 100 条记录
2025-05-30 18:00:15,041 - INFO - Request Parameters - Page 2:
2025-05-30 18:00:15,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:15,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:15,580 - INFO - Response - Page 2:
2025-05-30 18:00:15,782 - INFO - 第 2 页获取到 100 条记录
2025-05-30 18:00:15,782 - INFO - Request Parameters - Page 3:
2025-05-30 18:00:15,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:15,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:16,272 - INFO - Response - Page 3:
2025-05-30 18:00:16,472 - INFO - 第 3 页获取到 100 条记录
2025-05-30 18:00:16,472 - INFO - Request Parameters - Page 4:
2025-05-30 18:00:16,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:16,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:16,927 - INFO - Response - Page 4:
2025-05-30 18:00:17,127 - INFO - 第 4 页获取到 100 条记录
2025-05-30 18:00:17,127 - INFO - Request Parameters - Page 5:
2025-05-30 18:00:17,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:17,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:17,681 - INFO - Response - Page 5:
2025-05-30 18:00:17,881 - INFO - 第 5 页获取到 100 条记录
2025-05-30 18:00:17,881 - INFO - Request Parameters - Page 6:
2025-05-30 18:00:17,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:17,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:18,394 - INFO - Response - Page 6:
2025-05-30 18:00:18,594 - INFO - 第 6 页获取到 100 条记录
2025-05-30 18:00:18,594 - INFO - Request Parameters - Page 7:
2025-05-30 18:00:18,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:18,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:19,026 - INFO - Response - Page 7:
2025-05-30 18:00:19,226 - INFO - 第 7 页获取到 61 条记录
2025-05-30 18:00:19,226 - INFO - 查询完成，共获取到 661 条记录
2025-05-30 18:00:19,227 - INFO - 获取到 661 条表单数据
2025-05-30 18:00:19,239 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-30 18:00:19,250 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 18:00:19,250 - INFO - 开始处理日期: 2025-04
2025-05-30 18:00:19,251 - INFO - Request Parameters - Page 1:
2025-05-30 18:00:19,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:19,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:19,904 - INFO - Response - Page 1:
2025-05-30 18:00:20,104 - INFO - 第 1 页获取到 100 条记录
2025-05-30 18:00:20,104 - INFO - Request Parameters - Page 2:
2025-05-30 18:00:20,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:20,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:20,643 - INFO - Response - Page 2:
2025-05-30 18:00:20,843 - INFO - 第 2 页获取到 100 条记录
2025-05-30 18:00:20,843 - INFO - Request Parameters - Page 3:
2025-05-30 18:00:20,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:20,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:21,365 - INFO - Response - Page 3:
2025-05-30 18:00:21,565 - INFO - 第 3 页获取到 100 条记录
2025-05-30 18:00:21,565 - INFO - Request Parameters - Page 4:
2025-05-30 18:00:21,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:21,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:22,021 - INFO - Response - Page 4:
2025-05-30 18:00:22,221 - INFO - 第 4 页获取到 100 条记录
2025-05-30 18:00:22,221 - INFO - Request Parameters - Page 5:
2025-05-30 18:00:22,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:22,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:22,685 - INFO - Response - Page 5:
2025-05-30 18:00:22,885 - INFO - 第 5 页获取到 100 条记录
2025-05-30 18:00:22,885 - INFO - Request Parameters - Page 6:
2025-05-30 18:00:22,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:22,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:23,382 - INFO - Response - Page 6:
2025-05-30 18:00:23,582 - INFO - 第 6 页获取到 100 条记录
2025-05-30 18:00:23,582 - INFO - Request Parameters - Page 7:
2025-05-30 18:00:23,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:23,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:24,035 - INFO - Response - Page 7:
2025-05-30 18:00:24,236 - INFO - 第 7 页获取到 56 条记录
2025-05-30 18:00:24,236 - INFO - 查询完成，共获取到 656 条记录
2025-05-30 18:00:24,236 - INFO - 获取到 656 条表单数据
2025-05-30 18:00:24,247 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-30 18:00:24,259 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 18:00:24,259 - INFO - 开始处理日期: 2025-05
2025-05-30 18:00:24,259 - INFO - Request Parameters - Page 1:
2025-05-30 18:00:24,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:24,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:24,782 - INFO - Response - Page 1:
2025-05-30 18:00:24,982 - INFO - 第 1 页获取到 100 条记录
2025-05-30 18:00:24,982 - INFO - Request Parameters - Page 2:
2025-05-30 18:00:24,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:24,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:25,462 - INFO - Response - Page 2:
2025-05-30 18:00:25,663 - INFO - 第 2 页获取到 100 条记录
2025-05-30 18:00:25,663 - INFO - Request Parameters - Page 3:
2025-05-30 18:00:25,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:25,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:26,174 - INFO - Response - Page 3:
2025-05-30 18:00:26,374 - INFO - 第 3 页获取到 100 条记录
2025-05-30 18:00:26,374 - INFO - Request Parameters - Page 4:
2025-05-30 18:00:26,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:26,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:26,906 - INFO - Response - Page 4:
2025-05-30 18:00:27,108 - INFO - 第 4 页获取到 100 条记录
2025-05-30 18:00:27,108 - INFO - Request Parameters - Page 5:
2025-05-30 18:00:27,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:27,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:27,556 - INFO - Response - Page 5:
2025-05-30 18:00:27,756 - INFO - 第 5 页获取到 100 条记录
2025-05-30 18:00:27,756 - INFO - Request Parameters - Page 6:
2025-05-30 18:00:27,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:27,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:28,406 - INFO - Response - Page 6:
2025-05-30 18:00:28,607 - INFO - 第 6 页获取到 100 条记录
2025-05-30 18:00:28,607 - INFO - Request Parameters - Page 7:
2025-05-30 18:00:28,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 18:00:28,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 18:00:28,996 - INFO - Response - Page 7:
2025-05-30 18:00:29,198 - INFO - 第 7 页获取到 35 条记录
2025-05-30 18:00:29,198 - INFO - 查询完成，共获取到 635 条记录
2025-05-30 18:00:29,198 - INFO - 获取到 635 条表单数据
2025-05-30 18:00:29,211 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-30 18:00:29,213 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-30 18:00:29,753 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-30 18:00:29,754 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59460.45, 'new_value': 66040.68}, {'field': 'total_amount', 'old_value': 173248.04, 'new_value': 179828.27}, {'field': 'order_count', 'old_value': 9753, 'new_value': 10132}]
2025-05-30 18:00:29,754 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-30 18:00:30,171 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-30 18:00:30,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55009.76, 'new_value': 58097.68}, {'field': 'total_amount', 'old_value': 108916.34, 'new_value': 112004.26}, {'field': 'order_count', 'old_value': 5745, 'new_value': 5909}]
2025-05-30 18:00:30,174 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-30 18:00:30,687 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-30 18:00:30,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1454000.0, 'new_value': 1468000.0}, {'field': 'total_amount', 'old_value': 1454000.0, 'new_value': 1468000.0}]
2025-05-30 18:00:30,689 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-30 18:00:31,190 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-30 18:00:31,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169268.54, 'new_value': 168304.54}, {'field': 'total_amount', 'old_value': 169268.54, 'new_value': 168304.54}, {'field': 'order_count', 'old_value': 14523, 'new_value': 14970}]
2025-05-30 18:00:31,191 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-30 18:00:31,695 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-30 18:00:31,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74126.0, 'new_value': 77109.0}, {'field': 'total_amount', 'old_value': 74126.0, 'new_value': 77109.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 80}]
2025-05-30 18:00:31,695 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-30 18:00:32,195 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-30 18:00:32,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49202.2, 'new_value': 49461.2}, {'field': 'total_amount', 'old_value': 49396.2, 'new_value': 49655.2}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-30 18:00:32,195 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-30 18:00:32,668 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-30 18:00:32,668 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138416.0, 'new_value': 140014.0}, {'field': 'total_amount', 'old_value': 138416.0, 'new_value': 140014.0}, {'field': 'order_count', 'old_value': 4956, 'new_value': 5111}]
2025-05-30 18:00:32,668 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-30 18:00:33,123 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-30 18:00:33,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36128.07, 'new_value': 36136.07}, {'field': 'total_amount', 'old_value': 36128.07, 'new_value': 36136.07}, {'field': 'order_count', 'old_value': 3348, 'new_value': 3444}]
2025-05-30 18:00:33,124 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-30 18:00:33,647 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-30 18:00:33,647 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7814500.0, 'new_value': 7858000.0}, {'field': 'total_amount', 'old_value': 7814500.0, 'new_value': 7858000.0}]
2025-05-30 18:00:33,648 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-30 18:00:34,122 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-30 18:00:34,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 449215.0, 'new_value': 448715.0}, {'field': 'total_amount', 'old_value': 458033.99, 'new_value': 457533.99}, {'field': 'order_count', 'old_value': 83, 'new_value': 82}]
2025-05-30 18:00:34,123 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-30 18:00:34,590 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-30 18:00:34,591 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 506404.13, 'new_value': 497940.33}, {'field': 'total_amount', 'old_value': 506404.13, 'new_value': 497940.33}, {'field': 'order_count', 'old_value': 883, 'new_value': 904}]
2025-05-30 18:00:34,591 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-30 18:00:35,034 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-30 18:00:35,034 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100896.0, 'new_value': 100796.0}, {'field': 'total_amount', 'old_value': 100896.0, 'new_value': 100796.0}]
2025-05-30 18:00:35,035 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-30 18:00:35,481 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-30 18:00:35,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38982.0, 'new_value': 39017.0}, {'field': 'total_amount', 'old_value': 40358.0, 'new_value': 40393.0}, {'field': 'order_count', 'old_value': 3910, 'new_value': 4038}]
2025-05-30 18:00:35,482 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-30 18:00:35,920 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-30 18:00:35,920 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150978.61, 'new_value': 157069.22}, {'field': 'total_amount', 'old_value': 246631.16, 'new_value': 252721.77}, {'field': 'order_count', 'old_value': 10795, 'new_value': 11215}]
2025-05-30 18:00:35,921 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-30 18:00:36,340 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-30 18:00:36,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14449.0, 'new_value': 17249.0}, {'field': 'total_amount', 'old_value': 14449.0, 'new_value': 17249.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-30 18:00:36,340 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-30 18:00:36,749 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-30 18:00:36,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115750.0, 'new_value': 115268.0}, {'field': 'total_amount', 'old_value': 115750.0, 'new_value': 115268.0}, {'field': 'order_count', 'old_value': 757, 'new_value': 780}]
2025-05-30 18:00:36,749 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-30 18:00:37,206 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-30 18:00:37,206 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27272.0, 'new_value': 31272.0}, {'field': 'total_amount', 'old_value': 27272.0, 'new_value': 31272.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-05-30 18:00:37,206 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-30 18:00:37,696 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-30 18:00:37,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1477900.0, 'new_value': 1563900.0}, {'field': 'total_amount', 'old_value': 1477900.0, 'new_value': 1563900.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 78}]
2025-05-30 18:00:37,696 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-30 18:00:38,372 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-30 18:00:38,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13963.67, 'new_value': 14332.67}, {'field': 'total_amount', 'old_value': 13963.67, 'new_value': 14332.67}, {'field': 'order_count', 'old_value': 398, 'new_value': 409}]
2025-05-30 18:00:38,373 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-30 18:00:38,815 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-30 18:00:38,815 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 364447.5, 'new_value': 374744.5}, {'field': 'total_amount', 'old_value': 364447.5, 'new_value': 374744.5}, {'field': 'order_count', 'old_value': 66, 'new_value': 68}]
2025-05-30 18:00:38,815 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-30 18:00:39,265 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-30 18:00:39,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59214.95, 'new_value': 61417.95}, {'field': 'total_amount', 'old_value': 62655.75, 'new_value': 64858.75}, {'field': 'order_count', 'old_value': 232, 'new_value': 241}]
2025-05-30 18:00:39,266 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-30 18:00:39,743 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-30 18:00:39,744 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25739.43, 'new_value': 26371.68}, {'field': 'offline_amount', 'old_value': 17165.94, 'new_value': 17400.94}, {'field': 'total_amount', 'old_value': 42905.37, 'new_value': 43772.62}, {'field': 'order_count', 'old_value': 1769, 'new_value': 1821}]
2025-05-30 18:00:39,744 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-30 18:00:40,086 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-30 18:00:40,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5193.0, 'new_value': 5592.0}, {'field': 'total_amount', 'old_value': 15369.0, 'new_value': 15768.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-05-30 18:00:40,086 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-30 18:00:40,552 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-30 18:00:40,552 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 172616.79, 'new_value': 178050.19}, {'field': 'offline_amount', 'old_value': 318692.39, 'new_value': 334592.41}, {'field': 'total_amount', 'old_value': 491309.18, 'new_value': 512642.6}, {'field': 'order_count', 'old_value': 4148, 'new_value': 4304}]
2025-05-30 18:00:40,554 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-30 18:00:41,010 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-30 18:00:41,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77000.0, 'new_value': 84000.0}, {'field': 'total_amount', 'old_value': 77000.0, 'new_value': 84000.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-30 18:00:41,011 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-30 18:00:41,444 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-30 18:00:41,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 172263.82, 'new_value': 175628.82}, {'field': 'total_amount', 'old_value': 172263.82, 'new_value': 175628.82}, {'field': 'order_count', 'old_value': 1446, 'new_value': 1485}]
2025-05-30 18:00:41,445 - INFO - 日期 2025-05 处理完成 - 更新: 26 条，插入: 0 条，错误: 0 条
2025-05-30 18:00:41,445 - INFO - 数据同步完成！更新: 26 条，插入: 0 条，错误: 0 条
2025-05-30 18:00:41,445 - INFO - =================同步完成====================
2025-05-30 21:00:02,039 - INFO - =================使用默认全量同步=============
2025-05-30 21:00:03,558 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-30 21:00:03,558 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-30 21:00:03,588 - INFO - 开始处理日期: 2025-01
2025-05-30 21:00:03,590 - INFO - Request Parameters - Page 1:
2025-05-30 21:00:03,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:03,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:04,632 - INFO - Response - Page 1:
2025-05-30 21:00:04,832 - INFO - 第 1 页获取到 100 条记录
2025-05-30 21:00:04,832 - INFO - Request Parameters - Page 2:
2025-05-30 21:00:04,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:04,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:05,594 - INFO - Response - Page 2:
2025-05-30 21:00:05,795 - INFO - 第 2 页获取到 100 条记录
2025-05-30 21:00:05,795 - INFO - Request Parameters - Page 3:
2025-05-30 21:00:05,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:05,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:06,256 - INFO - Response - Page 3:
2025-05-30 21:00:06,456 - INFO - 第 3 页获取到 100 条记录
2025-05-30 21:00:06,456 - INFO - Request Parameters - Page 4:
2025-05-30 21:00:06,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:06,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:06,992 - INFO - Response - Page 4:
2025-05-30 21:00:07,192 - INFO - 第 4 页获取到 100 条记录
2025-05-30 21:00:07,192 - INFO - Request Parameters - Page 5:
2025-05-30 21:00:07,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:07,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:07,737 - INFO - Response - Page 5:
2025-05-30 21:00:07,938 - INFO - 第 5 页获取到 100 条记录
2025-05-30 21:00:07,938 - INFO - Request Parameters - Page 6:
2025-05-30 21:00:07,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:07,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:08,446 - INFO - Response - Page 6:
2025-05-30 21:00:08,647 - INFO - 第 6 页获取到 100 条记录
2025-05-30 21:00:08,647 - INFO - Request Parameters - Page 7:
2025-05-30 21:00:08,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:08,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:09,175 - INFO - Response - Page 7:
2025-05-30 21:00:09,375 - INFO - 第 7 页获取到 82 条记录
2025-05-30 21:00:09,375 - INFO - 查询完成，共获取到 682 条记录
2025-05-30 21:00:09,375 - INFO - 获取到 682 条表单数据
2025-05-30 21:00:09,386 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-30 21:00:09,398 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 21:00:09,399 - INFO - 开始处理日期: 2025-02
2025-05-30 21:00:09,399 - INFO - Request Parameters - Page 1:
2025-05-30 21:00:09,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:09,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:09,887 - INFO - Response - Page 1:
2025-05-30 21:00:10,087 - INFO - 第 1 页获取到 100 条记录
2025-05-30 21:00:10,087 - INFO - Request Parameters - Page 2:
2025-05-30 21:00:10,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:10,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:10,594 - INFO - Response - Page 2:
2025-05-30 21:00:10,794 - INFO - 第 2 页获取到 100 条记录
2025-05-30 21:00:10,794 - INFO - Request Parameters - Page 3:
2025-05-30 21:00:10,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:10,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:11,257 - INFO - Response - Page 3:
2025-05-30 21:00:11,457 - INFO - 第 3 页获取到 100 条记录
2025-05-30 21:00:11,457 - INFO - Request Parameters - Page 4:
2025-05-30 21:00:11,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:11,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:11,962 - INFO - Response - Page 4:
2025-05-30 21:00:12,162 - INFO - 第 4 页获取到 100 条记录
2025-05-30 21:00:12,162 - INFO - Request Parameters - Page 5:
2025-05-30 21:00:12,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:12,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:13,124 - INFO - Response - Page 5:
2025-05-30 21:00:13,324 - INFO - 第 5 页获取到 100 条记录
2025-05-30 21:00:13,324 - INFO - Request Parameters - Page 6:
2025-05-30 21:00:13,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:13,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:13,822 - INFO - Response - Page 6:
2025-05-30 21:00:14,023 - INFO - 第 6 页获取到 100 条记录
2025-05-30 21:00:14,023 - INFO - Request Parameters - Page 7:
2025-05-30 21:00:14,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:14,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:14,490 - INFO - Response - Page 7:
2025-05-30 21:00:14,690 - INFO - 第 7 页获取到 70 条记录
2025-05-30 21:00:14,690 - INFO - 查询完成，共获取到 670 条记录
2025-05-30 21:00:14,690 - INFO - 获取到 670 条表单数据
2025-05-30 21:00:14,702 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-30 21:00:14,714 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 21:00:14,714 - INFO - 开始处理日期: 2025-03
2025-05-30 21:00:14,714 - INFO - Request Parameters - Page 1:
2025-05-30 21:00:14,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:14,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:15,282 - INFO - Response - Page 1:
2025-05-30 21:00:15,482 - INFO - 第 1 页获取到 100 条记录
2025-05-30 21:00:15,482 - INFO - Request Parameters - Page 2:
2025-05-30 21:00:15,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:15,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:16,017 - INFO - Response - Page 2:
2025-05-30 21:00:16,218 - INFO - 第 2 页获取到 100 条记录
2025-05-30 21:00:16,218 - INFO - Request Parameters - Page 3:
2025-05-30 21:00:16,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:16,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:16,707 - INFO - Response - Page 3:
2025-05-30 21:00:16,908 - INFO - 第 3 页获取到 100 条记录
2025-05-30 21:00:16,908 - INFO - Request Parameters - Page 4:
2025-05-30 21:00:16,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:16,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:17,434 - INFO - Response - Page 4:
2025-05-30 21:00:17,634 - INFO - 第 4 页获取到 100 条记录
2025-05-30 21:00:17,634 - INFO - Request Parameters - Page 5:
2025-05-30 21:00:17,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:17,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:18,117 - INFO - Response - Page 5:
2025-05-30 21:00:18,317 - INFO - 第 5 页获取到 100 条记录
2025-05-30 21:00:18,317 - INFO - Request Parameters - Page 6:
2025-05-30 21:00:18,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:18,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:18,783 - INFO - Response - Page 6:
2025-05-30 21:00:18,984 - INFO - 第 6 页获取到 100 条记录
2025-05-30 21:00:18,984 - INFO - Request Parameters - Page 7:
2025-05-30 21:00:18,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:18,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:19,396 - INFO - Response - Page 7:
2025-05-30 21:00:19,597 - INFO - 第 7 页获取到 61 条记录
2025-05-30 21:00:19,597 - INFO - 查询完成，共获取到 661 条记录
2025-05-30 21:00:19,597 - INFO - 获取到 661 条表单数据
2025-05-30 21:00:19,608 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-30 21:00:19,620 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 21:00:19,620 - INFO - 开始处理日期: 2025-04
2025-05-30 21:00:19,620 - INFO - Request Parameters - Page 1:
2025-05-30 21:00:19,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:19,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:20,177 - INFO - Response - Page 1:
2025-05-30 21:00:20,377 - INFO - 第 1 页获取到 100 条记录
2025-05-30 21:00:20,377 - INFO - Request Parameters - Page 2:
2025-05-30 21:00:20,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:20,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:20,832 - INFO - Response - Page 2:
2025-05-30 21:00:21,032 - INFO - 第 2 页获取到 100 条记录
2025-05-30 21:00:21,032 - INFO - Request Parameters - Page 3:
2025-05-30 21:00:21,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:21,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:21,548 - INFO - Response - Page 3:
2025-05-30 21:00:21,748 - INFO - 第 3 页获取到 100 条记录
2025-05-30 21:00:21,748 - INFO - Request Parameters - Page 4:
2025-05-30 21:00:21,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:21,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:22,205 - INFO - Response - Page 4:
2025-05-30 21:00:22,405 - INFO - 第 4 页获取到 100 条记录
2025-05-30 21:00:22,405 - INFO - Request Parameters - Page 5:
2025-05-30 21:00:22,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:22,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:22,948 - INFO - Response - Page 5:
2025-05-30 21:00:23,148 - INFO - 第 5 页获取到 100 条记录
2025-05-30 21:00:23,148 - INFO - Request Parameters - Page 6:
2025-05-30 21:00:23,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:23,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:23,633 - INFO - Response - Page 6:
2025-05-30 21:00:23,833 - INFO - 第 6 页获取到 100 条记录
2025-05-30 21:00:23,833 - INFO - Request Parameters - Page 7:
2025-05-30 21:00:23,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:23,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:24,202 - INFO - Response - Page 7:
2025-05-30 21:00:24,402 - INFO - 第 7 页获取到 56 条记录
2025-05-30 21:00:24,402 - INFO - 查询完成，共获取到 656 条记录
2025-05-30 21:00:24,402 - INFO - 获取到 656 条表单数据
2025-05-30 21:00:24,415 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-30 21:00:24,426 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 21:00:24,426 - INFO - 开始处理日期: 2025-05
2025-05-30 21:00:24,427 - INFO - Request Parameters - Page 1:
2025-05-30 21:00:24,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:24,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:24,953 - INFO - Response - Page 1:
2025-05-30 21:00:25,154 - INFO - 第 1 页获取到 100 条记录
2025-05-30 21:00:25,154 - INFO - Request Parameters - Page 2:
2025-05-30 21:00:25,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:25,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:25,738 - INFO - Response - Page 2:
2025-05-30 21:00:25,939 - INFO - 第 2 页获取到 100 条记录
2025-05-30 21:00:25,939 - INFO - Request Parameters - Page 3:
2025-05-30 21:00:25,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:25,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:26,538 - INFO - Response - Page 3:
2025-05-30 21:00:26,738 - INFO - 第 3 页获取到 100 条记录
2025-05-30 21:00:26,738 - INFO - Request Parameters - Page 4:
2025-05-30 21:00:26,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:26,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:27,234 - INFO - Response - Page 4:
2025-05-30 21:00:27,434 - INFO - 第 4 页获取到 100 条记录
2025-05-30 21:00:27,435 - INFO - Request Parameters - Page 5:
2025-05-30 21:00:27,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:27,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:28,013 - INFO - Response - Page 5:
2025-05-30 21:00:28,213 - INFO - 第 5 页获取到 100 条记录
2025-05-30 21:00:28,213 - INFO - Request Parameters - Page 6:
2025-05-30 21:00:28,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:28,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:28,762 - INFO - Response - Page 6:
2025-05-30 21:00:28,962 - INFO - 第 6 页获取到 100 条记录
2025-05-30 21:00:28,962 - INFO - Request Parameters - Page 7:
2025-05-30 21:00:28,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 21:00:28,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 21:00:29,370 - INFO - Response - Page 7:
2025-05-30 21:00:29,571 - INFO - 第 7 页获取到 35 条记录
2025-05-30 21:00:29,571 - INFO - 查询完成，共获取到 635 条记录
2025-05-30 21:00:29,572 - INFO - 获取到 635 条表单数据
2025-05-30 21:00:29,584 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-30 21:00:29,595 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 21:00:29,595 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-30 21:00:29,597 - INFO - =================同步完成====================
