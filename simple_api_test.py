#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
com.gooagoo.exportbill 账单导入接口测试工具
基于现有签名方法的专用接口测试文件

功能：
1. 账单导入接口测试
2. 支持结账单、退款单、日结单
3. 完整的参数验证和错误处理
4. 支持多种账单类型（美团、饿了么、抖音、京东外卖等）

作者：AI Assistant
创建时间：2025-01-06
参考文档：com.gooagoo.exportbill-账单导入.html
"""

import hashlib
import json
import logging
import time
import requests
from datetime import datetime, timedelta
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'exportbill_test_{datetime.now().strftime("%Y%m%d")}.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def generate_sign(params: Dict[str, Any], api_secret: str) -> str:
    """
    生成签名，采用MD5算法
    
    Args:
        params: 参数字典
        api_secret: API密钥
        
    Returns:
        str: 签名字符串
    """
    # 删除空值的参数
    params = {k: v for k, v in params.items() if v is not None and v != ""}
    
    # 按ASCII码从小到大排序
    sorted_keys = sorted(params)
    
    # 拼接成stringA
    stringA = '&'.join([f"{k}={params[k]}" for k in sorted_keys])
    
    # 拼接API密钥
    stringSignTemp = f"{stringA}&key={api_secret}"
    
    # MD5运算并转换为大写
    sign = hashlib.md5(stringSignTemp.encode('utf-8')).hexdigest().upper()
    
    logging.debug(f"签名字符串: {stringSignTemp}")
    logging.debug(f"生成签名: {sign}")
    
    return sign

def call_api(app_id: str, app_key: str, api_secret: str, method: str, 
             lower_method: str, url: str, business_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    调用数衍平台API
    
    Args:
        app_id: 应用ID
        app_key: 应用Key
        api_secret: API密钥
        method: 方法名
        lower_method: 业务接口名称
        url: 请求URL
        business_data: 业务数据
        
    Returns:
        Dict: 接口响应结果
    """
    try:
        timestamp = time.strftime("%Y%m%d%H%M%S", time.localtime())
        
        # 公共参数
        public_params = {
            "appId": app_id,
            "appKey": app_key,
            "method": method,
            "lowerMethod": lower_method,  # 业务接口名称
            "timestamp": timestamp,
            "messageFormat": "Json",
            "v": "1.0",
            "signMethod": "MD5"
        }
        
        # 将业务数据封装到 "data" 参数中
        data_param = json.dumps(business_data, ensure_ascii=False)
        all_params = {**public_params, "data": data_param}
        
        # 生成签名
        all_params["sign"] = generate_sign(all_params, api_secret)
        
        # 记录请求信息
        logging.info(f"请求URL: {url}")
        logging.info(f"请求参数: {all_params}")
        
        # 发送请求
        response = requests.post(url, data=all_params, timeout=30)
        result = response.json()
        
        # 记录响应信息
        logging.info(f"响应状态码: {response.status_code}")
        logging.info(f"响应结果: {result}")
        
        return result
        
    except requests.exceptions.Timeout:
        error_msg = "请求超时"
        logging.error(error_msg)
        return {"error": error_msg}
    except requests.exceptions.RequestException as e:
        error_msg = f"请求异常: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}
    except json.JSONDecodeError as e:
        error_msg = f"JSON解析失败: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}
    except Exception as e:
        error_msg = f"API调用失败: {str(e)}"
        logging.error(error_msg)
        return {"error": error_msg}



# ========== 全局配置区域 ==========

# API基础配置（基于现有配置）
API_CONFIG = {
    'appId': '0df229fdf08a45b1a61893db35d94bd6',
    'appKey': '2c9a4b5d97d35a000197df5707610001',
    'apiSecret': '7ECF1B4964034AB87527BDB32C51F457',
    'method': 'gogo.open.auto.routing',
    'lowerMethod': 'com.gooagoo.exportbill',  # 账单导入接口
    'url': 'http://api.gooagoo.com/oapi/rest'
}

# 测试环境配置（根据文档提供的测试参数）
TEST_CONFIG = {
    'appId': 'd1667ebbaa3e4935a7e09be1a50f0af5',
    'appKey': '2c968875814106ca0181adf9657d0005',
    'apiSecret': 'D22B58F177D6739D413C5FE24CD32ED0',
    'method': 'gogo.open.auto.routing',
    'lowerMethod': 'com.gooagoo.exportbill',
    'url': 'http://api.test.goago.cn/oapi/rest',  # 测试环境URL
    'terminalNumber': '6A53BB2D7CDE'  # 测试设备编号
}

# 账单类型映射
BILL_TYPE_MAPPING = {
    "1": "结账单",
    "6": "退款单",
    "3": "日结单"
}

# 细分账单类型映射
EXACT_BILL_TYPE_MAPPING = {
    "10102": "美团外卖单",
    "10103": "饿了么外卖单",
    "10104": "抖音外卖单",
    "10105": "京东外卖单",
    "10602": "美团外卖退款单",
    "10603": "饿了么外卖退款单",
    "10604": "抖音外卖退款单",
    "10605": "京东外卖退款单"
}

def main():
    """主函数 - 执行接口测试"""

    logging.info("=" * 60)
    logging.info("开始账单导入接口测试")
    logging.info("=" * 60)

    # 业务数据参数（仅包含必填参数）
    business_data = {
        # ========== 必填参数 ==========
        "exactBillType": "10102",  # 细分账单类型：美团外卖单
        "billSerialNumber": f"TEST{int(time.time())}",  # 票据流水号
        # "terminalNumber": TEST_CONFIG['terminalNumber'],  # 设备编号
        "terminalNumber": "BBBB00000271",  # 设备编号
        "saleTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # 销售时间
        "thirdPartyOrderNo": f"ORDER{int(time.time())}",  # 第三方订单号
        "receivableAmount": 55.22,  # 实收金额
        "totalNum": 10.0,  # 商品数量
        "totalFee": 60.00,  # 应收金额
        "paidAmount": 55.22,  # 实付金额
        "billType": "1"  # 账单类型：1-结账单
    }
    
    # ========== 执行测试 ==========

    # 选择使用测试环境还是生产环境
    use_test_env = False  # 设置为True使用测试环境，False使用生产环境

    if use_test_env:
        config = TEST_CONFIG
        logging.info("🧪 使用测试环境配置")
    else:
        config = API_CONFIG
        logging.info("🚀 使用生产环境配置")

    logging.info(f"接口名称: {config['lowerMethod']}")
    logging.info(f"请求地址: {config['url']}")
    logging.info(f"账单类型: {business_data.get('billType')} - {BILL_TYPE_MAPPING.get(business_data.get('billType'), '未知')}")
    logging.info(f"细分类型: {business_data.get('exactBillType')} - {EXACT_BILL_TYPE_MAPPING.get(business_data.get('exactBillType'), '未知')}")

    # 调用接口
    result = call_api(
        config['appId'],
        config['appKey'],
        config['apiSecret'],
        config['method'],
        config['lowerMethod'],
        config['url'],
        business_data
    )

    # 处理结果
    if "error" in result:
        logging.error(f"❌ 接口调用失败: {result['error']}")
        return

    # 检查响应状态
    if result.get('rescode') == 'OPEN_SUCCESS':
        logging.info("✅ 账单导入成功!")
        logging.info(f"📄 响应消息: {result.get('resmsg', '')}")
        logging.info(f"📊 业务数据: {result.get('data', '')}")

        # 记录测试结果
        logging.info(f"📊 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logging.info(f"� 请求参数: {json.dumps(business_data, ensure_ascii=False, indent=2)}")

    elif result.get('rescode') == 'OPEN_FAIL':
        logging.error(f"❌ 账单导入失败!")
        logging.error(f"   错误消息: {result.get('resmsg', '未知错误')}")
        logging.error(f"   业务数据: {result.get('data', '')}")

    else:
        logging.error(f"❌ 接口返回未知状态:")
        logging.error(f"   状态码: {result.get('rescode', '未知')}")
        logging.error(f"   错误信息: {result.get('resmsg', '未知错误')}")

    logging.info("=" * 60)
    logging.info("账单导入测试完成")
    logging.info("=" * 60)

def test_different_bill_types():
    """测试不同类型的账单"""

    logging.info("=" * 60)
    logging.info("开始测试不同类型的账单")
    logging.info("=" * 60)

    # 测试场景配置
    test_scenarios = [
        {
            "name": "美团外卖结账单",
            "billType": "1",
            "exactBillType": "10102",
            "receivableAmount": 88.88,
            "totalFee": 90.00
        },
        {
            "name": "饿了么外卖退款单",
            "billType": "6",
            "exactBillType": "10603",
            "receivableAmount": -25.50,
            "totalFee": -25.50
        },
        {
            "name": "抖音外卖结账单",
            "billType": "1",
            "exactBillType": "10104",
            "receivableAmount": 66.66,
            "totalFee": 70.00
        }
    ]

    config = TEST_CONFIG  # 使用测试环境

    for i, scenario in enumerate(test_scenarios, 1):
        logging.info(f"\n🧪 测试场景 {i}: {scenario['name']}")

        # 构建测试数据（仅必填参数）
        test_data = {
            "exactBillType": scenario["exactBillType"],
            "billSerialNumber": f"TEST{scenario['exactBillType']}{int(time.time())}",
            "terminalNumber": config['terminalNumber'],
            "saleTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "thirdPartyOrderNo": f"ORDER{scenario['exactBillType']}{int(time.time())}",
            "receivableAmount": scenario["receivableAmount"],
            "totalNum": 5.0,
            "totalFee": scenario["totalFee"],
            "paidAmount": scenario["receivableAmount"],
            "billType": scenario["billType"]
        }

        # 调用接口
        result = call_api(
            config['appId'],
            config['appKey'],
            config['apiSecret'],
            config['method'],
            config['lowerMethod'],
            config['url'],
            test_data
        )

        # 处理结果
        if result.get('rescode') == 'OPEN_SUCCESS':
            logging.info(f"   ✅ {scenario['name']} 测试成功")
        else:
            logging.error(f"   ❌ {scenario['name']} 测试失败: {result.get('resmsg', '未知错误')}")

        # 间隔1秒避免请求过快
        time.sleep(1)

    logging.info("\n" + "=" * 60)
    logging.info("不同类型账单测试完成")
    logging.info("=" * 60)

def show_menu():
    """显示测试菜单"""
    print("\n" + "=" * 60)
    print("com.gooagoo.exportbill 账单导入接口测试工具")
    print("=" * 60)
    print("请选择测试模式:")
    print("1. 基础账单导入测试")
    print("2. 多种账单类型测试")
    print("3. 自定义参数测试")
    print("4. 查看接口文档说明")
    print("0. 退出")
    print("=" * 60)

def show_api_info():
    """显示接口文档说明"""
    print("\n" + "=" * 60)
    print("com.gooagoo.exportbill 接口说明")
    print("=" * 60)
    print("📋 接口功能：第三方通过该接口将账单数据接入到数衍科技平台")
    print("🌐 接口地址：http://api.gooagoo.com/oapi/rest")
    print("📝 请求方式：POST")
    print("📄 数据格式：application/x-www-form-urlencoded")

    print("\n📊 账单类型说明：")
    for code, desc in BILL_TYPE_MAPPING.items():
        print(f"   {code} - {desc}")

    print("\n🍔 细分账单类型说明：")
    for code, desc in EXACT_BILL_TYPE_MAPPING.items():
        print(f"   {code} - {desc}")

    print("\n🔧 测试环境配置：")
    print(f"   URL: {TEST_CONFIG['url']}")
    print(f"   AppId: {TEST_CONFIG['appId']}")
    print(f"   设备编号: {TEST_CONFIG['terminalNumber']}")

    print("\n✅ 成功响应：rescode = 'OPEN_SUCCESS'")
    print("❌ 失败响应：rescode = 'OPEN_FAIL'")
    print("=" * 60)

def interactive_main():
    """交互式主函数"""
    while True:
        show_menu()
        try:
            choice = input("请输入选择 (0-4): ").strip()

            if choice == "0":
                print("👋 感谢使用，再见！")
                break
            elif choice == "1":
                print("\n🚀 开始基础账单导入测试...")
                main()
            elif choice == "2":
                print("\n🧪 开始多种账单类型测试...")
                test_different_bill_types()
            elif choice == "3":
                print("\n⚙️ 自定义参数测试")
                print("请修改代码中的 business_data 参数后重新运行")
                main()
            elif choice == "4":
                show_api_info()
            else:
                print("❌ 无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 发生错误: {str(e)}")

if __name__ == "__main__":
    # 检查是否有命令行参数
    import sys
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        if command == "basic":
            main()
        elif command == "multi":
            test_different_bill_types()
        elif command == "info":
            show_api_info()
        else:
            print(f"未知命令: {command}")
            print("可用命令: basic, multi, info")
    else:
        # 交互式模式
        interactive_main()
