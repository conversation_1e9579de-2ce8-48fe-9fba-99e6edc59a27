2025-07-24 01:30:33,530 - INFO - 使用默认增量同步（当天更新数据）
2025-07-24 01:30:33,530 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-24 01:30:33,530 - INFO - 查询参数: ('2025-07-24',)
2025-07-24 01:30:33,624 - INFO - MySQL查询成功，增量数据（日期: 2025-07-24），共获取 0 条记录
2025-07-24 01:30:33,624 - ERROR - 未获取到MySQL数据
2025-07-24 01:31:33,639 - INFO - 开始同步昨天与今天的销售数据: 2025-07-23 至 2025-07-24
2025-07-24 01:31:33,639 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-24 01:31:33,639 - INFO - 查询参数: ('2025-07-23', '2025-07-24')
2025-07-24 01:31:33,780 - INFO - MySQL查询成功，时间段: 2025-07-23 至 2025-07-24，共获取 88 条记录
2025-07-24 01:31:33,780 - INFO - 获取到 1 个日期需要处理: ['2025-07-23']
2025-07-24 01:31:33,780 - INFO - 开始处理日期: 2025-07-23
2025-07-24 01:31:33,780 - INFO - Request Parameters - Page 1:
2025-07-24 01:31:33,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 01:31:33,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 01:31:41,905 - ERROR - 处理日期 2025-07-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0D964D7C-65E7-7E59-B50E-EC107C4A56C2 Response: {'code': 'ServiceUnavailable', 'requestid': '0D964D7C-65E7-7E59-B50E-EC107C4A56C2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0D964D7C-65E7-7E59-B50E-EC107C4A56C2)
2025-07-24 01:31:41,905 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-24 01:31:41,905 - INFO - 同步完成
2025-07-24 04:30:33,630 - INFO - 使用默认增量同步（当天更新数据）
2025-07-24 04:30:33,630 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-24 04:30:33,630 - INFO - 查询参数: ('2025-07-24',)
2025-07-24 04:30:33,786 - INFO - MySQL查询成功，增量数据（日期: 2025-07-24），共获取 3 条记录
2025-07-24 04:30:33,786 - INFO - 获取到 1 个日期需要处理: ['2025-07-23']
2025-07-24 04:30:33,786 - INFO - 开始处理日期: 2025-07-23
2025-07-24 04:30:33,786 - INFO - Request Parameters - Page 1:
2025-07-24 04:30:33,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 04:30:33,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 04:30:41,895 - ERROR - 处理日期 2025-07-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E76E5615-4B48-7457-987D-7CD852FB7D20 Response: {'code': 'ServiceUnavailable', 'requestid': 'E76E5615-4B48-7457-987D-7CD852FB7D20', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E76E5615-4B48-7457-987D-7CD852FB7D20)
2025-07-24 04:30:41,895 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-24 04:31:41,911 - INFO - 开始同步昨天与今天的销售数据: 2025-07-23 至 2025-07-24
2025-07-24 04:31:41,911 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-24 04:31:41,911 - INFO - 查询参数: ('2025-07-23', '2025-07-24')
2025-07-24 04:31:42,051 - INFO - MySQL查询成功，时间段: 2025-07-23 至 2025-07-24，共获取 92 条记录
2025-07-24 04:31:42,051 - INFO - 获取到 1 个日期需要处理: ['2025-07-23']
2025-07-24 04:31:42,051 - INFO - 开始处理日期: 2025-07-23
2025-07-24 04:31:42,051 - INFO - Request Parameters - Page 1:
2025-07-24 04:31:42,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 04:31:42,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 04:31:42,879 - INFO - Response - Page 1:
2025-07-24 04:31:42,879 - INFO - 第 1 页获取到 50 条记录
2025-07-24 04:31:43,379 - INFO - Request Parameters - Page 2:
2025-07-24 04:31:43,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 04:31:43,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 04:31:44,036 - INFO - Response - Page 2:
2025-07-24 04:31:44,036 - INFO - 第 2 页获取到 18 条记录
2025-07-24 04:31:44,536 - INFO - 查询完成，共获取到 68 条记录
2025-07-24 04:31:44,536 - INFO - 获取到 68 条表单数据
2025-07-24 04:31:44,536 - INFO - 当前日期 2025-07-23 有 89 条MySQL数据需要处理
2025-07-24 04:31:44,536 - INFO - 开始批量插入 21 条新记录
2025-07-24 04:31:44,739 - INFO - 批量插入响应状态码: 200
2025-07-24 04:31:44,739 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 20:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1020', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '41728CB2-0682-7CEA-96A7-F103131F7CE0', 'x-acs-trace-id': '42f339a8be52e5063d308c51016810d2', 'etag': '19+WV/ql6e6yQnX0uPZb47w0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 04:31:44,739 - INFO - 批量插入响应体: {'result': ['FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMC5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMD5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDME5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMF5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMG5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMH5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMI5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMJ5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMK5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDML5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMM5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMN5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMO5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMP5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMQ5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMR5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMS5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMT5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMU5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMV5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMW5']}
2025-07-24 04:31:44,739 - INFO - 批量插入表单数据成功，批次 1，共 21 条记录
2025-07-24 04:31:44,739 - INFO - 成功插入的数据ID: ['FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMC5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMD5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDME5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMF5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMG5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMH5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMI5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMJ5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMK5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDML5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMM5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMN5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMO5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMP5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMQ5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMR5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMS5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMT5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMU5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMV5', 'FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMW5']
2025-07-24 04:31:49,754 - INFO - 批量插入完成，共 21 条记录
2025-07-24 04:31:49,754 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 21 条，错误: 0 条
2025-07-24 04:31:49,754 - INFO - 数据同步完成！更新: 0 条，插入: 21 条，错误: 0 条
2025-07-24 04:31:49,754 - INFO - 同步完成
2025-07-24 07:30:33,714 - INFO - 使用默认增量同步（当天更新数据）
2025-07-24 07:30:33,714 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-24 07:30:33,714 - INFO - 查询参数: ('2025-07-24',)
2025-07-24 07:30:33,870 - INFO - MySQL查询成功，增量数据（日期: 2025-07-24），共获取 5 条记录
2025-07-24 07:30:33,870 - INFO - 获取到 1 个日期需要处理: ['2025-07-23']
2025-07-24 07:30:33,870 - INFO - 开始处理日期: 2025-07-23
2025-07-24 07:30:33,870 - INFO - Request Parameters - Page 1:
2025-07-24 07:30:33,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 07:30:33,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 07:30:41,995 - ERROR - 处理日期 2025-07-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1C284554-063F-7125-895C-395978537ACB Response: {'code': 'ServiceUnavailable', 'requestid': '1C284554-063F-7125-895C-395978537ACB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1C284554-063F-7125-895C-395978537ACB)
2025-07-24 07:30:41,995 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-24 07:31:42,010 - INFO - 开始同步昨天与今天的销售数据: 2025-07-23 至 2025-07-24
2025-07-24 07:31:42,010 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-24 07:31:42,010 - INFO - 查询参数: ('2025-07-23', '2025-07-24')
2025-07-24 07:31:42,166 - INFO - MySQL查询成功，时间段: 2025-07-23 至 2025-07-24，共获取 109 条记录
2025-07-24 07:31:42,166 - INFO - 获取到 1 个日期需要处理: ['2025-07-23']
2025-07-24 07:31:42,166 - INFO - 开始处理日期: 2025-07-23
2025-07-24 07:31:42,166 - INFO - Request Parameters - Page 1:
2025-07-24 07:31:42,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 07:31:42,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 07:31:43,057 - INFO - Response - Page 1:
2025-07-24 07:31:43,057 - INFO - 第 1 页获取到 50 条记录
2025-07-24 07:31:43,573 - INFO - Request Parameters - Page 2:
2025-07-24 07:31:43,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 07:31:43,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 07:31:44,245 - INFO - Response - Page 2:
2025-07-24 07:31:44,245 - INFO - 第 2 页获取到 39 条记录
2025-07-24 07:31:44,760 - INFO - 查询完成，共获取到 89 条记录
2025-07-24 07:31:44,760 - INFO - 获取到 89 条表单数据
2025-07-24 07:31:44,760 - INFO - 当前日期 2025-07-23 有 106 条MySQL数据需要处理
2025-07-24 07:31:44,760 - INFO - 开始批量插入 17 条新记录
2025-07-24 07:31:44,932 - INFO - 批量插入响应状态码: 200
2025-07-24 07:31:44,932 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 23 Jul 2025 23:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '828', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-4735-7499-A737-6507D4669B37', 'x-acs-trace-id': 'a12e209d0d18200854309f62ce50d1e6', 'etag': '857aTxG49mMXtvQJVq0uggQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 07:31:44,932 - INFO - 批量插入响应体: {'result': ['FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDMY6', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDMZ6', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM07', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM17', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM27', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM37', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM47', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM57', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM67', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM77', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM87', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM97', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDMA7', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDMB7', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDMC7', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDMD7', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDME7']}
2025-07-24 07:31:44,932 - INFO - 批量插入表单数据成功，批次 1，共 17 条记录
2025-07-24 07:31:44,932 - INFO - 成功插入的数据ID: ['FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDMY6', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDMZ6', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM07', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM17', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM27', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM37', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM47', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM57', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM67', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM77', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM87', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDM97', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDMA7', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDMB7', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDMC7', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDMD7', 'FINST-I6E66WA1AIEXQD3DBIR7Q5SEWCJR2QKALLGDME7']
2025-07-24 07:31:49,948 - INFO - 批量插入完成，共 17 条记录
2025-07-24 07:31:49,948 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 17 条，错误: 0 条
2025-07-24 07:31:49,948 - INFO - 数据同步完成！更新: 0 条，插入: 17 条，错误: 0 条
2025-07-24 07:31:49,948 - INFO - 同步完成
2025-07-24 10:30:33,799 - INFO - 使用默认增量同步（当天更新数据）
2025-07-24 10:30:33,799 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-24 10:30:33,799 - INFO - 查询参数: ('2025-07-24',)
2025-07-24 10:30:33,956 - INFO - MySQL查询成功，增量数据（日期: 2025-07-24），共获取 140 条记录
2025-07-24 10:30:33,956 - INFO - 获取到 4 个日期需要处理: ['2025-07-01', '2025-07-22', '2025-07-23', '2025-07-24']
2025-07-24 10:30:33,956 - INFO - 开始处理日期: 2025-07-01
2025-07-24 10:30:33,971 - INFO - Request Parameters - Page 1:
2025-07-24 10:30:33,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 10:30:33,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751299200000, 1751385599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 10:30:42,096 - ERROR - 处理日期 2025-07-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3E24ED62-7506-77BC-B07A-662A5CA80283 Response: {'code': 'ServiceUnavailable', 'requestid': '3E24ED62-7506-77BC-B07A-662A5CA80283', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3E24ED62-7506-77BC-B07A-662A5CA80283)
2025-07-24 10:30:42,096 - INFO - 开始处理日期: 2025-07-22
2025-07-24 10:30:42,096 - INFO - Request Parameters - Page 1:
2025-07-24 10:30:42,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 10:30:42,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 10:30:50,221 - ERROR - 处理日期 2025-07-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8E3CA642-7FAC-7F71-9660-D858A926B2BD Response: {'code': 'ServiceUnavailable', 'requestid': '8E3CA642-7FAC-7F71-9660-D858A926B2BD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8E3CA642-7FAC-7F71-9660-D858A926B2BD)
2025-07-24 10:30:50,221 - INFO - 开始处理日期: 2025-07-23
2025-07-24 10:30:50,221 - INFO - Request Parameters - Page 1:
2025-07-24 10:30:50,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 10:30:50,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 10:30:54,752 - INFO - Response - Page 1:
2025-07-24 10:30:54,752 - INFO - 第 1 页获取到 50 条记录
2025-07-24 10:30:55,268 - INFO - Request Parameters - Page 2:
2025-07-24 10:30:55,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 10:30:55,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 10:30:55,971 - INFO - Response - Page 2:
2025-07-24 10:30:55,971 - INFO - 第 2 页获取到 50 条记录
2025-07-24 10:30:56,487 - INFO - Request Parameters - Page 3:
2025-07-24 10:30:56,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 10:30:56,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 10:30:57,018 - INFO - Response - Page 3:
2025-07-24 10:30:57,018 - INFO - 第 3 页获取到 6 条记录
2025-07-24 10:30:57,534 - INFO - 查询完成，共获取到 106 条记录
2025-07-24 10:30:57,534 - INFO - 获取到 106 条表单数据
2025-07-24 10:30:57,534 - INFO - 当前日期 2025-07-23 有 129 条MySQL数据需要处理
2025-07-24 10:30:57,534 - INFO - 开始批量插入 124 条新记录
2025-07-24 10:30:57,768 - INFO - 批量插入响应状态码: 200
2025-07-24 10:30:57,768 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 02:30:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7746720C-14DB-7872-B57B-7A373CDC30C8', 'x-acs-trace-id': 'e0e415b19c1d6a7245ba0f108bbb6116', 'etag': '21+5yNe6dTJnlecCrxb+6GA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 10:30:57,768 - INFO - 批量插入响应体: {'result': ['FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMO9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMP9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMQ9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMR9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMS9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMT9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMU9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMV9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMW9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMX9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMY9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMZ9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM0A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM1A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM2A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM3A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM4A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM5A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM6A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM7A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM8A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM9A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMAA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMBA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMCA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMDA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMEA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMFA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMGA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMHA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMIA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMJA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMKA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMLA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMMA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMNA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMOA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMPA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMQA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMRA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMSA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMTA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMUA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMVA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMWA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMXA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMYA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMZA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM0B', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM1B']}
2025-07-24 10:30:57,768 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-24 10:30:57,768 - INFO - 成功插入的数据ID: ['FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMO9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMP9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMQ9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMR9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMS9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMT9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMU9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMV9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMW9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMX9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMY9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMZ9', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM0A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM1A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM2A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM3A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM4A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM5A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM6A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM7A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM8A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM9A', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMAA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMBA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMCA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMDA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMEA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMFA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMGA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMHA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMIA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMJA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMKA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMLA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMMA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMNA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMOA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMPA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMQA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMRA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMSA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMTA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMUA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMVA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMWA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMXA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMYA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDMZA', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM0B', 'FINST-BD766BC13IEXQ8GMFRZ5R57D3NDO2QKRZRGDM1B']
2025-07-24 10:31:03,018 - INFO - 批量插入响应状态码: 200
2025-07-24 10:31:03,018 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 02:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2402', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '919281AB-D8AA-76DA-A19B-5629B11645A5', 'x-acs-trace-id': '04cd5b45cb06ab0a37f007b6ce1c5017', 'etag': '24aVckqTNdnKJknsLevUoxQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 10:31:03,018 - INFO - 批量插入响应体: {'result': ['FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMQ', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMR', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMS', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMT', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMU', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMV', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMW', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMX', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMY', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMZ', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM01', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM11', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM21', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM31', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM41', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM51', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM61', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM71', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM81', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM91', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMA1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMB1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMC1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMD1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDME1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMF1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMG1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMH1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMI1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMJ1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMK1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDML1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMM1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMN1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMO1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMP1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMQ1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMR1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMS1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMT1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMU1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMV1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMW1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMX1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMY1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMZ1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDM02', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDM12', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDM22', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDM32']}
2025-07-24 10:31:03,018 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-24 10:31:03,018 - INFO - 成功插入的数据ID: ['FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMQ', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMR', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMS', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMT', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMU', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMV', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMW', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMX', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMY', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMZ', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM01', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM11', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM21', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM31', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM41', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM51', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM61', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM71', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM81', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDM91', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMA1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMB1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMC1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMD1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDME1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMF1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMG1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3EMVZRGDMH1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMI1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMJ1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMK1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDML1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMM1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMN1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMO1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMP1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMQ1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMR1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMS1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMT1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMU1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMV1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMW1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMX1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMY1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDMZ1', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDM02', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDM12', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDM22', 'FINST-B9C660C1WHEX9FSO6615Y9F7EJ3Y3FMVZRGDM32']
2025-07-24 10:31:08,237 - INFO - 批量插入响应状态码: 200
2025-07-24 10:31:08,237 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 02:31:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1164', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9CAD4DBC-A8FF-72E9-8E3D-650B14980AF3', 'x-acs-trace-id': '6ec4d73ee187a0244a5a367bb1d72561', 'etag': '1l6OB9Jk40W7RmRL1q/WD/w4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 10:31:08,237 - INFO - 批量插入响应体: {'result': ['FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMT5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMU5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMV5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMW5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMX5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMY5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMZ5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM06', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM16', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM26', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM36', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM46', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM56', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM66', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM76', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM86', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM96', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMA6', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMB6', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMC6', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMD6', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDME6', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMF6', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMG6']}
2025-07-24 10:31:08,237 - INFO - 批量插入表单数据成功，批次 3，共 24 条记录
2025-07-24 10:31:08,237 - INFO - 成功插入的数据ID: ['FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMT5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMU5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMV5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMW5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMX5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMY5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMZ5', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM06', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM16', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM26', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM36', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM46', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM56', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM66', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM76', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM86', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDM96', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMA6', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMB6', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMC6', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMD6', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDME6', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMF6', 'FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMG6']
2025-07-24 10:31:13,252 - INFO - 批量插入完成，共 124 条记录
2025-07-24 10:31:13,252 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 124 条，错误: 0 条
2025-07-24 10:31:13,252 - INFO - 开始处理日期: 2025-07-24
2025-07-24 10:31:13,252 - INFO - Request Parameters - Page 1:
2025-07-24 10:31:13,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 10:31:13,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 10:31:13,721 - INFO - Response - Page 1:
2025-07-24 10:31:13,721 - INFO - 查询完成，共获取到 0 条记录
2025-07-24 10:31:13,721 - INFO - 获取到 0 条表单数据
2025-07-24 10:31:13,721 - INFO - 当前日期 2025-07-24 有 1 条MySQL数据需要处理
2025-07-24 10:31:13,721 - INFO - 开始批量插入 1 条新记录
2025-07-24 10:31:13,877 - INFO - 批量插入响应状态码: 200
2025-07-24 10:31:13,877 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 02:31:13 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F66B495A-7A96-72EB-9685-BD0FD9A23FF1', 'x-acs-trace-id': '82e0ecf0a7715b8a235bd311fdb6ebd2', 'etag': '55L8dXWRoKq2ssTNtAiW4aQ9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 10:31:13,877 - INFO - 批量插入响应体: {'result': ['FINST-8PF66V71N4FX6TJSB18QG4LC2ATZ2E040SGDMY']}
2025-07-24 10:31:13,877 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-24 10:31:13,877 - INFO - 成功插入的数据ID: ['FINST-8PF66V71N4FX6TJSB18QG4LC2ATZ2E040SGDMY']
2025-07-24 10:31:18,893 - INFO - 批量插入完成，共 1 条记录
2025-07-24 10:31:18,893 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-24 10:31:18,893 - INFO - 数据同步完成！更新: 0 条，插入: 125 条，错误: 2 条
2025-07-24 10:32:18,908 - INFO - 开始同步昨天与今天的销售数据: 2025-07-23 至 2025-07-24
2025-07-24 10:32:18,908 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-24 10:32:18,908 - INFO - 查询参数: ('2025-07-23', '2025-07-24')
2025-07-24 10:32:19,064 - INFO - MySQL查询成功，时间段: 2025-07-23 至 2025-07-24，共获取 465 条记录
2025-07-24 10:32:19,064 - INFO - 获取到 2 个日期需要处理: ['2025-07-23', '2025-07-24']
2025-07-24 10:32:19,080 - INFO - 开始处理日期: 2025-07-23
2025-07-24 10:32:19,080 - INFO - Request Parameters - Page 1:
2025-07-24 10:32:19,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 10:32:19,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 10:32:19,861 - INFO - Response - Page 1:
2025-07-24 10:32:19,861 - INFO - 第 1 页获取到 50 条记录
2025-07-24 10:32:20,361 - INFO - Request Parameters - Page 2:
2025-07-24 10:32:20,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 10:32:20,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 10:32:21,158 - INFO - Response - Page 2:
2025-07-24 10:32:21,158 - INFO - 第 2 页获取到 50 条记录
2025-07-24 10:32:21,658 - INFO - Request Parameters - Page 3:
2025-07-24 10:32:21,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 10:32:21,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 10:32:22,502 - INFO - Response - Page 3:
2025-07-24 10:32:22,502 - INFO - 第 3 页获取到 50 条记录
2025-07-24 10:32:23,018 - INFO - Request Parameters - Page 4:
2025-07-24 10:32:23,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 10:32:23,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 10:32:23,783 - INFO - Response - Page 4:
2025-07-24 10:32:23,783 - INFO - 第 4 页获取到 50 条记录
2025-07-24 10:32:24,283 - INFO - Request Parameters - Page 5:
2025-07-24 10:32:24,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 10:32:24,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 10:32:24,971 - INFO - Response - Page 5:
2025-07-24 10:32:24,971 - INFO - 第 5 页获取到 30 条记录
2025-07-24 10:32:25,471 - INFO - 查询完成，共获取到 230 条记录
2025-07-24 10:32:25,471 - INFO - 获取到 230 条表单数据
2025-07-24 10:32:25,471 - INFO - 当前日期 2025-07-23 有 456 条MySQL数据需要处理
2025-07-24 10:32:25,471 - INFO - 开始批量插入 226 条新记录
2025-07-24 10:32:25,721 - INFO - 批量插入响应状态码: 200
2025-07-24 10:32:25,721 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 02:32:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4BE058E5-B183-751A-B293-946300109AF7', 'x-acs-trace-id': '4f4bc6815ee0971dc6696da212acbd5a', 'etag': '2z5Qj1hcwc6Xgwq2Dg54aWA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 10:32:25,721 - INFO - 批量插入响应体: {'result': ['FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMRF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMSF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMTF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMUF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMVF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMWF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMXF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMYF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMZF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM0G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM1G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM2G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM3G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM4G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM5G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM6G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM7G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM8G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM9G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMAG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMBG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMCG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMDG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMEG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMFG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMGG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMHG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMIG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMJG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMKG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMLG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMMG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMNG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMOG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMPG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMQG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMRG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMSG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMTG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMUG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMVG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMWG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMXG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMYG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMZG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM0H', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM1H', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM2H', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM3H', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM4H']}
2025-07-24 10:32:25,721 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-24 10:32:25,721 - INFO - 成功插入的数据ID: ['FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMRF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMSF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMTF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMUF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMVF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMWF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMXF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMYF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMZF', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM0G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM1G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM2G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM3G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM4G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM5G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM6G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM7G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM8G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM9G', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMAG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMBG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMCG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMDG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMEG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMFG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMGG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMHG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMIG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMJG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMKG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMLG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMMG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMNG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMOG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMPG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMQG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMRG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMSG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMTG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMUG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMVG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMWG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMXG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMYG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDMZG', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM0H', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM1H', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM2H', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM3H', 'FINST-6I766IB1T4EXTTNHDLAWP4XO1V9T3XFN1SGDM4H']
2025-07-24 10:32:30,955 - INFO - 批量插入响应状态码: 200
2025-07-24 10:32:30,955 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 02:32:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1279EF1A-D50F-7EB3-98DB-B7FD688E0DB9', 'x-acs-trace-id': '82bef30135ece12421f86913301ed6e4', 'etag': '2aLbfFQgF7EPYdvzgYPzNFw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 10:32:30,955 - INFO - 批量插入响应体: {'result': ['FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDM8G', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDM9G', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMAG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMBG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMCG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMDG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMEG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMFG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMGG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMHG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMIG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMJG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMKG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMLG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMMG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMNG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMOG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMPG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMQG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMRG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMSG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMTG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMUG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMVG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMWG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMXG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMYG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMZG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM0H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM1H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM2H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM3H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM4H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM5H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM6H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM7H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM8H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM9H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMAH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMBH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMCH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMDH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMEH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMFH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMGH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMHH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMIH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMJH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMKH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMLH']}
2025-07-24 10:32:30,955 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-24 10:32:30,955 - INFO - 成功插入的数据ID: ['FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDM8G', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDM9G', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMAG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMBG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMCG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMDG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMEG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMFG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMGG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMHG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMIG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMJG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMKG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMLG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMMG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMNG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMOG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMPG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMQG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMRG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMSG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMTG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMUG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMVG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2EHR1SGDMWG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMXG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMYG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMZG', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM0H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM1H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM2H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM3H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM4H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM5H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM6H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM7H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM8H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDM9H', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMAH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMBH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMCH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMDH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMEH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMFH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMGH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMHH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMIH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMJH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMKH', 'FINST-KLF66HD1VHEXDDMI8SODAD1MLDYT2FHR1SGDMLH']
2025-07-24 10:32:36,221 - INFO - 批量插入响应状态码: 200
2025-07-24 10:32:36,221 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 02:32:36 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '84ED06C3-E318-70C8-B149-122B0B3B9493', 'x-acs-trace-id': '4a69fe2da27b97bc40df9667d22d7f7e', 'etag': '2PXu6RPRCMIlh9fp4dMIuVA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 10:32:36,221 - INFO - 批量插入响应体: {'result': ['FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDM71', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDM81', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDM91', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMA1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMB1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMC1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMD1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDME1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMF1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMG1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMH1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMI1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMJ1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMK1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDML1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMM1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMN1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMO1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMP1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMQ1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMR1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMS1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMT1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMU1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMV1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMW1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMX1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMY1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMZ1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM02', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM12', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM22', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM32', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM42', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM52', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM62', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM72', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM82', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM92', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMA2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMB2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMC2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMD2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDME2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMF2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMG2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMH2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMI2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMJ2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMK2']}
2025-07-24 10:32:36,221 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-24 10:32:36,221 - INFO - 成功插入的数据ID: ['FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDM71', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDM81', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDM91', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMA1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMB1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMC1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMD1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDME1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMF1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMG1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMH1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMI1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMJ1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMK1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDML1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMM1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMN1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMO1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMP1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMQ1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMR1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMS1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMT1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMU1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMV1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMW1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMX1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2HJV1SGDMY1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMZ1', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM02', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM12', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM22', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM32', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM42', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM52', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM62', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM72', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM82', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDM92', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMA2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMB2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMC2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMD2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDME2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMF2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMG2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMH2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMI2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMJ2', 'FINST-IQG66AD1E2FXLZPNATJEQ8D8LGKE2IJV1SGDMK2']
2025-07-24 10:32:41,486 - INFO - 批量插入响应状态码: 200
2025-07-24 10:32:41,486 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 02:32:41 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E905D40A-4A75-718D-999A-E70430C95731', 'x-acs-trace-id': 'd7c0e7bad7d60671bb6de08ce2b3d68d', 'etag': '2UPikyXtRE6dDEFe4y1HNiA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 10:32:41,486 - INFO - 批量插入响应体: {'result': ['FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMXC', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMYC', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMZC', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM0D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM1D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM2D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM3D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM4D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM5D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM6D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM7D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM8D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM9D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMAD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMBD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMCD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMDD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMED', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMFD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMGD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMHD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMID', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMJD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMKD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMLD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMMD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMND', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMOD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMPD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMQD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMRD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMSD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMTD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMUD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMVD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMWD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMXD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMYD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMZD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM0E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM1E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM2E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM3E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM4E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM5E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM6E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM7E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM8E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM9E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMAE']}
2025-07-24 10:32:41,486 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-24 10:32:41,486 - INFO - 成功插入的数据ID: ['FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMXC', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMYC', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMZC', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM0D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM1D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM2D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM3D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM4D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM5D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM6D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM7D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM8D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM9D', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMAD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMBD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMCD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMDD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMED', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMFD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMGD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMHD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMID', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMJD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMKD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMLD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMMD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMND', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMOD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMPD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMQD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMRD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMSD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMTD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMUD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMVD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMWD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMXD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMYD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMZD', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM0E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM1E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM2E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM3E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM4E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM5E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM6E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM7E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM8E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDM9E', 'FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMAE']
2025-07-24 10:32:46,720 - INFO - 批量插入响应状态码: 200
2025-07-24 10:32:46,720 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 02:32:46 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1236', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B238A6FC-D7FA-7E6B-A999-E0288A3D4D37', 'x-acs-trace-id': '3cddb5c7f3ed7af596132bfd4ad1f10f', 'etag': '1u3lOsKylUJq6hOc0lyfabA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 10:32:46,720 - INFO - 批量插入响应体: {'result': ['FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMC', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMD', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDME', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMF', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMG', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMH', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMI', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMJ', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMK', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDML', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMM', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMN', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMO', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMP', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMQ', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMR', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMS', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMT', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMU', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMV', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMW', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMX', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMY', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMZ', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDM01', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDM11']}
2025-07-24 10:32:46,720 - INFO - 批量插入表单数据成功，批次 5，共 26 条记录
2025-07-24 10:32:46,720 - INFO - 成功插入的数据ID: ['FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMC', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMD', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDME', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMF', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMG', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMH', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMI', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMJ', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMK', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDML', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMM', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMN', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMO', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMP', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMQ', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMR', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMS', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMT', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMU', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMV', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMW', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMX', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMY', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDMZ', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDM01', 'FINST-H7966UC10IEXBXVV6IR80D1MVN0O26N32SGDM11']
2025-07-24 10:32:51,736 - INFO - 批量插入完成，共 226 条记录
2025-07-24 10:32:51,736 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 226 条，错误: 0 条
2025-07-24 10:32:51,736 - INFO - 开始处理日期: 2025-07-24
2025-07-24 10:32:51,736 - INFO - Request Parameters - Page 1:
2025-07-24 10:32:51,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 10:32:51,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 10:32:52,220 - INFO - Response - Page 1:
2025-07-24 10:32:52,220 - INFO - 第 1 页获取到 1 条记录
2025-07-24 10:32:52,736 - INFO - 查询完成，共获取到 1 条记录
2025-07-24 10:32:52,736 - INFO - 获取到 1 条表单数据
2025-07-24 10:32:52,736 - INFO - 当前日期 2025-07-24 有 1 条MySQL数据需要处理
2025-07-24 10:32:52,736 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-24 10:32:52,736 - INFO - 数据同步完成！更新: 0 条，插入: 226 条，错误: 0 条
2025-07-24 10:32:52,736 - INFO - 同步完成
2025-07-24 13:30:33,666 - INFO - 使用默认增量同步（当天更新数据）
2025-07-24 13:30:33,666 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-24 13:30:33,666 - INFO - 查询参数: ('2025-07-24',)
2025-07-24 13:30:33,822 - INFO - MySQL查询成功，增量数据（日期: 2025-07-24），共获取 152 条记录
2025-07-24 13:30:33,822 - INFO - 获取到 4 个日期需要处理: ['2025-07-01', '2025-07-22', '2025-07-23', '2025-07-24']
2025-07-24 13:30:33,822 - INFO - 开始处理日期: 2025-07-01
2025-07-24 13:30:33,837 - INFO - Request Parameters - Page 1:
2025-07-24 13:30:33,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:30:33,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751299200000, 1751385599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:30:41,947 - ERROR - 处理日期 2025-07-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 82816201-7AC0-79D0-A48E-507BC2C1B6CD Response: {'code': 'ServiceUnavailable', 'requestid': '82816201-7AC0-79D0-A48E-507BC2C1B6CD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 82816201-7AC0-79D0-A48E-507BC2C1B6CD)
2025-07-24 13:30:41,947 - INFO - 开始处理日期: 2025-07-22
2025-07-24 13:30:41,947 - INFO - Request Parameters - Page 1:
2025-07-24 13:30:41,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:30:41,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:30:50,072 - INFO - Response - Page 1:
2025-07-24 13:30:50,072 - INFO - 第 1 页获取到 50 条记录
2025-07-24 13:30:50,587 - INFO - Request Parameters - Page 2:
2025-07-24 13:30:50,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:30:50,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:30:51,353 - INFO - Response - Page 2:
2025-07-24 13:30:51,353 - INFO - 第 2 页获取到 50 条记录
2025-07-24 13:30:51,869 - INFO - Request Parameters - Page 3:
2025-07-24 13:30:51,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:30:51,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:30:52,572 - INFO - Response - Page 3:
2025-07-24 13:30:52,572 - INFO - 第 3 页获取到 50 条记录
2025-07-24 13:30:53,087 - INFO - Request Parameters - Page 4:
2025-07-24 13:30:53,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:30:53,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:30:53,790 - INFO - Response - Page 4:
2025-07-24 13:30:53,790 - INFO - 第 4 页获取到 50 条记录
2025-07-24 13:30:54,290 - INFO - Request Parameters - Page 5:
2025-07-24 13:30:54,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:30:54,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:30:54,978 - INFO - Response - Page 5:
2025-07-24 13:30:54,978 - INFO - 第 5 页获取到 50 条记录
2025-07-24 13:30:55,494 - INFO - Request Parameters - Page 6:
2025-07-24 13:30:55,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:30:55,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:30:56,181 - INFO - Response - Page 6:
2025-07-24 13:30:56,181 - INFO - 第 6 页获取到 50 条记录
2025-07-24 13:30:56,681 - INFO - Request Parameters - Page 7:
2025-07-24 13:30:56,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:30:56,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:30:57,369 - INFO - Response - Page 7:
2025-07-24 13:30:57,369 - INFO - 第 7 页获取到 50 条记录
2025-07-24 13:30:57,884 - INFO - Request Parameters - Page 8:
2025-07-24 13:30:57,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:30:57,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:30:58,665 - INFO - Response - Page 8:
2025-07-24 13:30:58,665 - INFO - 第 8 页获取到 50 条记录
2025-07-24 13:30:59,181 - INFO - Request Parameters - Page 9:
2025-07-24 13:30:59,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:30:59,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:30:59,869 - INFO - Response - Page 9:
2025-07-24 13:30:59,869 - INFO - 第 9 页获取到 50 条记录
2025-07-24 13:31:00,384 - INFO - Request Parameters - Page 10:
2025-07-24 13:31:00,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:31:00,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:31:01,119 - INFO - Response - Page 10:
2025-07-24 13:31:01,119 - INFO - 第 10 页获取到 50 条记录
2025-07-24 13:31:01,619 - INFO - Request Parameters - Page 11:
2025-07-24 13:31:01,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:31:01,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:31:02,197 - INFO - Response - Page 11:
2025-07-24 13:31:02,197 - INFO - 第 11 页获取到 15 条记录
2025-07-24 13:31:02,697 - INFO - 查询完成，共获取到 515 条记录
2025-07-24 13:31:02,697 - INFO - 获取到 515 条表单数据
2025-07-24 13:31:02,697 - INFO - 当前日期 2025-07-22 有 6 条MySQL数据需要处理
2025-07-24 13:31:02,697 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMDU
2025-07-24 13:31:03,259 - INFO - 更新表单数据成功: FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMDU
2025-07-24 13:31:03,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4860.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4860.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 18}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/2beeee8e89df44938d7d3fed3e7ea44e.png?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=SBTu2fu139PWBYl9GKbDJ6PM3JM%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/be5da28ce5624680a315fb0107d3ce3a.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=vPq%2BbWrX1NwkLoHAVbyaBi5B2ms%3D'}]
2025-07-24 13:31:03,259 - INFO - 开始更新记录 - 表单实例ID: FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM81
2025-07-24 13:31:03,822 - INFO - 更新表单数据成功: FINST-07E66I9105EXVQIX66S888CI0HIS32VWLCFDM81
2025-07-24 13:31:03,822 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4860.0, 'new_value': 0.0}, {'field': 'total_amount', 'old_value': 4860.0, 'new_value': 0.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/596087912e6f4af09669c1a8064a4e25.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=7NM1eGBD2%2B1Gg9Ne023ainCB9zE%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/ff257f9ffbaf42e5bd1a8a031a56a567.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=c7Ui9HErfmecLspqtzVtmNpf620%3D'}]
2025-07-24 13:31:03,822 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMBU
2025-07-24 13:31:04,259 - INFO - 更新表单数据成功: FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMBU
2025-07-24 13:31:04,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1380.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1380.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/a4e21cf495404bcabf6782f5c6de24b2.png?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=oXVWJcf%2BXKnf5Xp%2FySfMADcD3vA%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/d74735c2e7de4d14b0472d8c0cad422f.png?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=Im7Qp7qZ40zRJBBTKvJ590R1RtU%3D'}]
2025-07-24 13:31:04,275 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMHU
2025-07-24 13:31:04,978 - INFO - 更新表单数据成功: FINST-6PF66691T6CXQEUC92RFCAUII36S3HX0MCFDMHU
2025-07-24 13:31:04,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4116.4, 'new_value': 8653.4}, {'field': 'total_amount', 'old_value': 4116.4, 'new_value': 8653.4}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/417ac6a9b3384e3c80ebdb44582bc9b8.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=H%2BsK4yIK%2FP5tmEc%2BUjdOk9V8elE%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/d989a55251dc412e88199eaaeaec806d.jpeg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=CTN2Zd0AYL9TfPSCE9W9%2BCJi%2FBw%3D'}]
2025-07-24 13:31:04,978 - INFO - 开始更新记录 - 表单实例ID: FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMCK
2025-07-24 13:31:05,478 - INFO - 更新表单数据成功: FINST-8LG66D71CMDXNU7UCSDXU8UA4P8P22SFGPFDMCK
2025-07-24 13:31:05,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39053.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 6758.0, 'new_value': 6574.0}, {'field': 'total_amount', 'old_value': 45811.0, 'new_value': 6574.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/8f0c2194584e4ddc97bbb05b540bd06e.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=6dHdI7XUux6lJoAvMwZgV4pUeB8%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/0b51cd3274104db99a9aa832b0fbefdd.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=MfVR%2BHmIxScT9dPPGzloWlemy%2F4%3D'}]
2025-07-24 13:31:05,478 - INFO - 开始批量插入 1 条新记录
2025-07-24 13:31:05,712 - INFO - 批量插入响应状态码: 200
2025-07-24 13:31:05,712 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 05:31:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7A5B9071-D2F3-72F3-BAE8-FDC520A1AB01', 'x-acs-trace-id': '55f780624ba56453b0fedb1fc012d327', 'etag': '6DraYuqGt9HY+pHYJCRO9Uw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 13:31:05,712 - INFO - 批量插入响应体: {'result': ['FINST-3PF66271F5FXFS3NB2G56DPXA2AK253FFYGDMI9']}
2025-07-24 13:31:05,712 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-24 13:31:05,712 - INFO - 成功插入的数据ID: ['FINST-3PF66271F5FXFS3NB2G56DPXA2AK253FFYGDMI9']
2025-07-24 13:31:10,728 - INFO - 批量插入完成，共 1 条记录
2025-07-24 13:31:10,728 - INFO - 日期 2025-07-22 处理完成 - 更新: 5 条，插入: 1 条，错误: 0 条
2025-07-24 13:31:10,728 - INFO - 开始处理日期: 2025-07-23
2025-07-24 13:31:10,728 - INFO - Request Parameters - Page 1:
2025-07-24 13:31:10,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:31:10,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:31:11,447 - INFO - Response - Page 1:
2025-07-24 13:31:11,447 - INFO - 第 1 页获取到 50 条记录
2025-07-24 13:31:11,962 - INFO - Request Parameters - Page 2:
2025-07-24 13:31:11,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:31:11,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:31:12,665 - INFO - Response - Page 2:
2025-07-24 13:31:12,665 - INFO - 第 2 页获取到 50 条记录
2025-07-24 13:31:13,165 - INFO - Request Parameters - Page 3:
2025-07-24 13:31:13,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:31:13,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:31:13,884 - INFO - Response - Page 3:
2025-07-24 13:31:13,884 - INFO - 第 3 页获取到 50 条记录
2025-07-24 13:31:14,400 - INFO - Request Parameters - Page 4:
2025-07-24 13:31:14,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:31:14,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:31:15,103 - INFO - Response - Page 4:
2025-07-24 13:31:15,103 - INFO - 第 4 页获取到 50 条记录
2025-07-24 13:31:15,618 - INFO - Request Parameters - Page 5:
2025-07-24 13:31:15,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:31:15,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:31:16,306 - INFO - Response - Page 5:
2025-07-24 13:31:16,306 - INFO - 第 5 页获取到 50 条记录
2025-07-24 13:31:16,822 - INFO - Request Parameters - Page 6:
2025-07-24 13:31:16,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:31:16,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:31:17,587 - INFO - Response - Page 6:
2025-07-24 13:31:17,587 - INFO - 第 6 页获取到 50 条记录
2025-07-24 13:31:18,087 - INFO - Request Parameters - Page 7:
2025-07-24 13:31:18,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:31:18,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:31:18,775 - INFO - Response - Page 7:
2025-07-24 13:31:18,775 - INFO - 第 7 页获取到 50 条记录
2025-07-24 13:31:19,290 - INFO - Request Parameters - Page 8:
2025-07-24 13:31:19,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:31:19,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:31:20,165 - INFO - Response - Page 8:
2025-07-24 13:31:20,165 - INFO - 第 8 页获取到 50 条记录
2025-07-24 13:31:20,681 - INFO - Request Parameters - Page 9:
2025-07-24 13:31:20,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:31:20,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:31:21,493 - INFO - Response - Page 9:
2025-07-24 13:31:21,493 - INFO - 第 9 页获取到 50 条记录
2025-07-24 13:31:22,009 - INFO - Request Parameters - Page 10:
2025-07-24 13:31:22,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:31:22,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:31:22,603 - INFO - Response - Page 10:
2025-07-24 13:31:22,603 - INFO - 第 10 页获取到 6 条记录
2025-07-24 13:31:23,118 - INFO - 查询完成，共获取到 456 条记录
2025-07-24 13:31:23,118 - INFO - 获取到 456 条表单数据
2025-07-24 13:31:23,118 - INFO - 当前日期 2025-07-23 有 136 条MySQL数据需要处理
2025-07-24 13:31:23,118 - INFO - 开始更新记录 - 表单实例ID: FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMD6
2025-07-24 13:31:23,759 - INFO - 更新表单数据成功: FINST-IQE66ZC1XDEX3J1WBV85R8QS5DUE2INZZRGDMD6
2025-07-24 13:31:23,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30000.0, 'new_value': 38190.94}, {'field': 'total_amount', 'old_value': 30000.0, 'new_value': 38190.94}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/e2982642963c4f0a91063583b313ca02.png?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=deqIcfg6xq1vnOm3J4itaSs1YhA%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/8163cef9afef4d13add7fa64cae437a8.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=kiKm6azVrQrCWYfPVWjZBtEEboo%3D'}]
2025-07-24 13:31:23,759 - INFO - 开始更新记录 - 表单实例ID: FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMPD
2025-07-24 13:31:24,337 - INFO - 更新表单数据成功: FINST-MRA66WC19EEXIJK6BZLH17LX5THR2JLZ1SGDMPD
2025-07-24 13:31:24,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8717.9, 'new_value': 8737.9}, {'field': 'total_amount', 'old_value': 8717.9, 'new_value': 8737.9}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/256574a68cdc41d389687c68061fe368.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=FlWPCOoTbn4CFQI8b3eWQCabP0A%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/d9f6b76ac4ef419cb6f909e0fd574009.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=o%2BxTNauw1G%2FCED6nJxyEwOJVmdA%3D'}]
2025-07-24 13:31:24,337 - INFO - 开始批量插入 6 条新记录
2025-07-24 13:31:24,493 - INFO - 批量插入响应状态码: 200
2025-07-24 13:31:24,493 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 05:31:24 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '300', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4E5B5C45-B9D1-7C02-893A-24EA52E1D2D9', 'x-acs-trace-id': '054b9af004d2189d98d7fe2c2d418bf4', 'etag': '3D+K4O9mCDWsCI6J7RRnZtQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 13:31:24,493 - INFO - 批量插入响应体: {'result': ['FINST-FPB66VB1YHEXXQTYAWG6KB1L3SQ92YKTFYGDMWI', 'FINST-FPB66VB1YHEXXQTYAWG6KB1L3SQ92YKTFYGDMXI', 'FINST-FPB66VB1YHEXXQTYAWG6KB1L3SQ92YKTFYGDMYI', 'FINST-FPB66VB1YHEXXQTYAWG6KB1L3SQ92YKTFYGDMZI', 'FINST-FPB66VB1YHEXXQTYAWG6KB1L3SQ92YKTFYGDM0J', 'FINST-FPB66VB1YHEXXQTYAWG6KB1L3SQ92YKTFYGDM1J']}
2025-07-24 13:31:24,493 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-07-24 13:31:24,493 - INFO - 成功插入的数据ID: ['FINST-FPB66VB1YHEXXQTYAWG6KB1L3SQ92YKTFYGDMWI', 'FINST-FPB66VB1YHEXXQTYAWG6KB1L3SQ92YKTFYGDMXI', 'FINST-FPB66VB1YHEXXQTYAWG6KB1L3SQ92YKTFYGDMYI', 'FINST-FPB66VB1YHEXXQTYAWG6KB1L3SQ92YKTFYGDMZI', 'FINST-FPB66VB1YHEXXQTYAWG6KB1L3SQ92YKTFYGDM0J', 'FINST-FPB66VB1YHEXXQTYAWG6KB1L3SQ92YKTFYGDM1J']
2025-07-24 13:31:29,509 - INFO - 批量插入完成，共 6 条记录
2025-07-24 13:31:29,509 - INFO - 日期 2025-07-23 处理完成 - 更新: 2 条，插入: 6 条，错误: 0 条
2025-07-24 13:31:29,509 - INFO - 开始处理日期: 2025-07-24
2025-07-24 13:31:29,509 - INFO - Request Parameters - Page 1:
2025-07-24 13:31:29,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:31:29,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:31:30,025 - INFO - Response - Page 1:
2025-07-24 13:31:30,025 - INFO - 第 1 页获取到 1 条记录
2025-07-24 13:31:30,540 - INFO - 查询完成，共获取到 1 条记录
2025-07-24 13:31:30,540 - INFO - 获取到 1 条表单数据
2025-07-24 13:31:30,540 - INFO - 当前日期 2025-07-24 有 3 条MySQL数据需要处理
2025-07-24 13:31:30,540 - INFO - 开始批量插入 2 条新记录
2025-07-24 13:31:30,712 - INFO - 批量插入响应状态码: 200
2025-07-24 13:31:30,712 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 05:31:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E66CABC6-62ED-784C-8962-36FD75E2DC44', 'x-acs-trace-id': 'fd1ed079724543cb0204d57581d17c41', 'etag': '15huNeWcZRD53FHoc7zUbcg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 13:31:30,712 - INFO - 批量插入响应体: {'result': ['FINST-H7966UC1G5FX5EX18BZTC5WFZMTH3RDYFYGDMQ4', 'FINST-H7966UC1G5FX5EX18BZTC5WFZMTH3RDYFYGDMR4']}
2025-07-24 13:31:30,712 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-24 13:31:30,712 - INFO - 成功插入的数据ID: ['FINST-H7966UC1G5FX5EX18BZTC5WFZMTH3RDYFYGDMQ4', 'FINST-H7966UC1G5FX5EX18BZTC5WFZMTH3RDYFYGDMR4']
2025-07-24 13:31:35,728 - INFO - 批量插入完成，共 2 条记录
2025-07-24 13:31:35,728 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-24 13:31:35,728 - INFO - 数据同步完成！更新: 7 条，插入: 9 条，错误: 1 条
2025-07-24 13:32:35,743 - INFO - 开始同步昨天与今天的销售数据: 2025-07-23 至 2025-07-24
2025-07-24 13:32:35,743 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-24 13:32:35,743 - INFO - 查询参数: ('2025-07-23', '2025-07-24')
2025-07-24 13:32:35,915 - INFO - MySQL查询成功，时间段: 2025-07-23 至 2025-07-24，共获取 494 条记录
2025-07-24 13:32:35,915 - INFO - 获取到 2 个日期需要处理: ['2025-07-23', '2025-07-24']
2025-07-24 13:32:35,915 - INFO - 开始处理日期: 2025-07-23
2025-07-24 13:32:35,915 - INFO - Request Parameters - Page 1:
2025-07-24 13:32:35,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:32:35,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:32:36,680 - INFO - Response - Page 1:
2025-07-24 13:32:36,680 - INFO - 第 1 页获取到 50 条记录
2025-07-24 13:32:37,196 - INFO - Request Parameters - Page 2:
2025-07-24 13:32:37,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:32:37,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:32:37,915 - INFO - Response - Page 2:
2025-07-24 13:32:37,915 - INFO - 第 2 页获取到 50 条记录
2025-07-24 13:32:38,430 - INFO - Request Parameters - Page 3:
2025-07-24 13:32:38,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:32:38,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:32:39,071 - INFO - Response - Page 3:
2025-07-24 13:32:39,071 - INFO - 第 3 页获取到 50 条记录
2025-07-24 13:32:39,571 - INFO - Request Parameters - Page 4:
2025-07-24 13:32:39,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:32:39,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:32:40,305 - INFO - Response - Page 4:
2025-07-24 13:32:40,305 - INFO - 第 4 页获取到 50 条记录
2025-07-24 13:32:40,805 - INFO - Request Parameters - Page 5:
2025-07-24 13:32:40,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:32:40,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:32:41,477 - INFO - Response - Page 5:
2025-07-24 13:32:41,477 - INFO - 第 5 页获取到 50 条记录
2025-07-24 13:32:41,993 - INFO - Request Parameters - Page 6:
2025-07-24 13:32:41,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:32:41,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:32:42,727 - INFO - Response - Page 6:
2025-07-24 13:32:42,727 - INFO - 第 6 页获取到 50 条记录
2025-07-24 13:32:43,243 - INFO - Request Parameters - Page 7:
2025-07-24 13:32:43,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:32:43,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:32:43,993 - INFO - Response - Page 7:
2025-07-24 13:32:43,993 - INFO - 第 7 页获取到 50 条记录
2025-07-24 13:32:44,493 - INFO - Request Parameters - Page 8:
2025-07-24 13:32:44,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:32:44,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:32:45,212 - INFO - Response - Page 8:
2025-07-24 13:32:45,212 - INFO - 第 8 页获取到 50 条记录
2025-07-24 13:32:45,727 - INFO - Request Parameters - Page 9:
2025-07-24 13:32:45,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:32:45,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:32:46,493 - INFO - Response - Page 9:
2025-07-24 13:32:46,493 - INFO - 第 9 页获取到 50 条记录
2025-07-24 13:32:47,008 - INFO - Request Parameters - Page 10:
2025-07-24 13:32:47,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:32:47,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:32:47,649 - INFO - Response - Page 10:
2025-07-24 13:32:47,649 - INFO - 第 10 页获取到 12 条记录
2025-07-24 13:32:48,149 - INFO - 查询完成，共获取到 462 条记录
2025-07-24 13:32:48,149 - INFO - 获取到 462 条表单数据
2025-07-24 13:32:48,149 - INFO - 当前日期 2025-07-23 有 480 条MySQL数据需要处理
2025-07-24 13:32:48,165 - INFO - 开始批量插入 18 条新记录
2025-07-24 13:32:48,352 - INFO - 批量插入响应状态码: 200
2025-07-24 13:32:48,352 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 05:32:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '876', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '72E44C44-FC5A-7955-B8B2-167A090C930E', 'x-acs-trace-id': 'f20db16e23dde37323bcb6298e71f081', 'etag': '89ohvvvyczph9NC3blH4Hgw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 13:32:48,352 - INFO - 批量插入响应体: {'result': ['FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMVH', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMWH', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMXH', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMYH', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMZH', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM0I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM1I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM2I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM3I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM4I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM5I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM6I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM7I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM8I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM9I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMAI', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMBI', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMCI']}
2025-07-24 13:32:48,352 - INFO - 批量插入表单数据成功，批次 1，共 18 条记录
2025-07-24 13:32:48,352 - INFO - 成功插入的数据ID: ['FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMVH', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMWH', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMXH', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMYH', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMZH', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM0I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM1I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM2I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM3I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM4I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM5I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM6I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM7I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM8I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDM9I', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMAI', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMBI', 'FINST-LR5668B1GFEXXDOY5R4UFA7J9HP42DAMHYGDMCI']
2025-07-24 13:32:53,368 - INFO - 批量插入完成，共 18 条记录
2025-07-24 13:32:53,368 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 18 条，错误: 0 条
2025-07-24 13:32:53,368 - INFO - 开始处理日期: 2025-07-24
2025-07-24 13:32:53,368 - INFO - Request Parameters - Page 1:
2025-07-24 13:32:53,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 13:32:53,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 13:32:53,915 - INFO - Response - Page 1:
2025-07-24 13:32:53,915 - INFO - 第 1 页获取到 3 条记录
2025-07-24 13:32:54,415 - INFO - 查询完成，共获取到 3 条记录
2025-07-24 13:32:54,415 - INFO - 获取到 3 条表单数据
2025-07-24 13:32:54,415 - INFO - 当前日期 2025-07-24 有 3 条MySQL数据需要处理
2025-07-24 13:32:54,415 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-24 13:32:54,415 - INFO - 数据同步完成！更新: 0 条，插入: 18 条，错误: 0 条
2025-07-24 13:32:54,415 - INFO - 同步完成
2025-07-24 16:30:33,659 - INFO - 使用默认增量同步（当天更新数据）
2025-07-24 16:30:33,659 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-24 16:30:33,659 - INFO - 查询参数: ('2025-07-24',)
2025-07-24 16:30:33,831 - INFO - MySQL查询成功，增量数据（日期: 2025-07-24），共获取 160 条记录
2025-07-24 16:30:33,831 - INFO - 获取到 4 个日期需要处理: ['2025-07-01', '2025-07-22', '2025-07-23', '2025-07-24']
2025-07-24 16:30:33,831 - INFO - 开始处理日期: 2025-07-01
2025-07-24 16:30:33,831 - INFO - Request Parameters - Page 1:
2025-07-24 16:30:33,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:30:33,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751299200000, 1751385599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:30:41,925 - ERROR - 处理日期 2025-07-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C5EED79F-E52E-7CF3-ABD5-6D9509140C10 Response: {'code': 'ServiceUnavailable', 'requestid': 'C5EED79F-E52E-7CF3-ABD5-6D9509140C10', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C5EED79F-E52E-7CF3-ABD5-6D9509140C10)
2025-07-24 16:30:41,925 - INFO - 开始处理日期: 2025-07-22
2025-07-24 16:30:41,925 - INFO - Request Parameters - Page 1:
2025-07-24 16:30:41,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:30:41,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:30:50,066 - ERROR - 处理日期 2025-07-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A2BD1CCD-**************-EB77952A878C Response: {'code': 'ServiceUnavailable', 'requestid': 'A2BD1CCD-**************-EB77952A878C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A2BD1CCD-**************-EB77952A878C)
2025-07-24 16:30:50,066 - INFO - 开始处理日期: 2025-07-23
2025-07-24 16:30:50,066 - INFO - Request Parameters - Page 1:
2025-07-24 16:30:50,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:30:50,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:30:50,784 - INFO - Response - Page 1:
2025-07-24 16:30:50,800 - INFO - 第 1 页获取到 50 条记录
2025-07-24 16:30:51,316 - INFO - Request Parameters - Page 2:
2025-07-24 16:30:51,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:30:51,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:30:52,097 - INFO - Response - Page 2:
2025-07-24 16:30:52,097 - INFO - 第 2 页获取到 50 条记录
2025-07-24 16:30:52,612 - INFO - Request Parameters - Page 3:
2025-07-24 16:30:52,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:30:52,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:30:53,394 - INFO - Response - Page 3:
2025-07-24 16:30:53,394 - INFO - 第 3 页获取到 50 条记录
2025-07-24 16:30:53,909 - INFO - Request Parameters - Page 4:
2025-07-24 16:30:53,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:30:53,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:30:54,644 - INFO - Response - Page 4:
2025-07-24 16:30:54,644 - INFO - 第 4 页获取到 50 条记录
2025-07-24 16:30:55,144 - INFO - Request Parameters - Page 5:
2025-07-24 16:30:55,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:30:55,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:30:55,831 - INFO - Response - Page 5:
2025-07-24 16:30:55,831 - INFO - 第 5 页获取到 50 条记录
2025-07-24 16:30:56,331 - INFO - Request Parameters - Page 6:
2025-07-24 16:30:56,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:30:56,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:30:57,034 - INFO - Response - Page 6:
2025-07-24 16:30:57,034 - INFO - 第 6 页获取到 50 条记录
2025-07-24 16:30:57,550 - INFO - Request Parameters - Page 7:
2025-07-24 16:30:57,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:30:57,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:30:58,284 - INFO - Response - Page 7:
2025-07-24 16:30:58,284 - INFO - 第 7 页获取到 50 条记录
2025-07-24 16:30:58,784 - INFO - Request Parameters - Page 8:
2025-07-24 16:30:58,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:30:58,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:30:59,487 - INFO - Response - Page 8:
2025-07-24 16:30:59,503 - INFO - 第 8 页获取到 50 条记录
2025-07-24 16:31:00,003 - INFO - Request Parameters - Page 9:
2025-07-24 16:31:00,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:31:00,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:31:00,706 - INFO - Response - Page 9:
2025-07-24 16:31:00,706 - INFO - 第 9 页获取到 50 条记录
2025-07-24 16:31:01,222 - INFO - Request Parameters - Page 10:
2025-07-24 16:31:01,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:31:01,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:31:01,847 - INFO - Response - Page 10:
2025-07-24 16:31:01,847 - INFO - 第 10 页获取到 30 条记录
2025-07-24 16:31:02,362 - INFO - 查询完成，共获取到 480 条记录
2025-07-24 16:31:02,362 - INFO - 获取到 480 条表单数据
2025-07-24 16:31:02,362 - INFO - 当前日期 2025-07-23 有 142 条MySQL数据需要处理
2025-07-24 16:31:02,362 - INFO - 开始批量插入 6 条新记录
2025-07-24 16:31:02,534 - INFO - 批量插入响应状态码: 200
2025-07-24 16:31:02,534 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 08:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '300', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6DBB7703-21D5-7A18-9194-4DD040810423', 'x-acs-trace-id': '7d1f9a45edbb1e77fbc94ccc2f68e231', 'etag': '39mzZTJmDa+VP/6vh8DFfsA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 16:31:02,534 - INFO - 批量插入响应体: {'result': ['FINST-L4E66Y61B7EXM990A6PVW62V732M2X0UU4HDMGJ', 'FINST-L4E66Y61B7EXM990A6PVW62V732M2X0UU4HDMHJ', 'FINST-L4E66Y61B7EXM990A6PVW62V732M2X0UU4HDMIJ', 'FINST-L4E66Y61B7EXM990A6PVW62V732M2X0UU4HDMJJ', 'FINST-L4E66Y61B7EXM990A6PVW62V732M2X0UU4HDMKJ', 'FINST-L4E66Y61B7EXM990A6PVW62V732M2X0UU4HDMLJ']}
2025-07-24 16:31:02,534 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-07-24 16:31:02,534 - INFO - 成功插入的数据ID: ['FINST-L4E66Y61B7EXM990A6PVW62V732M2X0UU4HDMGJ', 'FINST-L4E66Y61B7EXM990A6PVW62V732M2X0UU4HDMHJ', 'FINST-L4E66Y61B7EXM990A6PVW62V732M2X0UU4HDMIJ', 'FINST-L4E66Y61B7EXM990A6PVW62V732M2X0UU4HDMJJ', 'FINST-L4E66Y61B7EXM990A6PVW62V732M2X0UU4HDMKJ', 'FINST-L4E66Y61B7EXM990A6PVW62V732M2X0UU4HDMLJ']
2025-07-24 16:31:07,550 - INFO - 批量插入完成，共 6 条记录
2025-07-24 16:31:07,550 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 6 条，错误: 0 条
2025-07-24 16:31:07,550 - INFO - 开始处理日期: 2025-07-24
2025-07-24 16:31:07,550 - INFO - Request Parameters - Page 1:
2025-07-24 16:31:07,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:31:07,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:31:08,065 - INFO - Response - Page 1:
2025-07-24 16:31:08,065 - INFO - 第 1 页获取到 3 条记录
2025-07-24 16:31:08,581 - INFO - 查询完成，共获取到 3 条记录
2025-07-24 16:31:08,581 - INFO - 获取到 3 条表单数据
2025-07-24 16:31:08,581 - INFO - 当前日期 2025-07-24 有 3 条MySQL数据需要处理
2025-07-24 16:31:08,581 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-24 16:31:08,581 - INFO - 数据同步完成！更新: 0 条，插入: 6 条，错误: 2 条
2025-07-24 16:32:08,596 - INFO - 开始同步昨天与今天的销售数据: 2025-07-23 至 2025-07-24
2025-07-24 16:32:08,596 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-24 16:32:08,596 - INFO - 查询参数: ('2025-07-23', '2025-07-24')
2025-07-24 16:32:08,753 - INFO - MySQL查询成功，时间段: 2025-07-23 至 2025-07-24，共获取 518 条记录
2025-07-24 16:32:08,753 - INFO - 获取到 2 个日期需要处理: ['2025-07-23', '2025-07-24']
2025-07-24 16:32:08,768 - INFO - 开始处理日期: 2025-07-23
2025-07-24 16:32:08,768 - INFO - Request Parameters - Page 1:
2025-07-24 16:32:08,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:32:08,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:32:09,503 - INFO - Response - Page 1:
2025-07-24 16:32:09,503 - INFO - 第 1 页获取到 50 条记录
2025-07-24 16:32:10,003 - INFO - Request Parameters - Page 2:
2025-07-24 16:32:10,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:32:10,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:32:10,706 - INFO - Response - Page 2:
2025-07-24 16:32:10,706 - INFO - 第 2 页获取到 50 条记录
2025-07-24 16:32:11,221 - INFO - Request Parameters - Page 3:
2025-07-24 16:32:11,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:32:11,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:32:11,956 - INFO - Response - Page 3:
2025-07-24 16:32:11,956 - INFO - 第 3 页获取到 50 条记录
2025-07-24 16:32:12,456 - INFO - Request Parameters - Page 4:
2025-07-24 16:32:12,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:32:12,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:32:13,221 - INFO - Response - Page 4:
2025-07-24 16:32:13,221 - INFO - 第 4 页获取到 50 条记录
2025-07-24 16:32:13,737 - INFO - Request Parameters - Page 5:
2025-07-24 16:32:13,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:32:13,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:32:14,471 - INFO - Response - Page 5:
2025-07-24 16:32:14,471 - INFO - 第 5 页获取到 50 条记录
2025-07-24 16:32:14,987 - INFO - Request Parameters - Page 6:
2025-07-24 16:32:14,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:32:14,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:32:15,721 - INFO - Response - Page 6:
2025-07-24 16:32:15,721 - INFO - 第 6 页获取到 50 条记录
2025-07-24 16:32:16,221 - INFO - Request Parameters - Page 7:
2025-07-24 16:32:16,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:32:16,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:32:16,924 - INFO - Response - Page 7:
2025-07-24 16:32:16,924 - INFO - 第 7 页获取到 50 条记录
2025-07-24 16:32:17,440 - INFO - Request Parameters - Page 8:
2025-07-24 16:32:17,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:32:17,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:32:18,143 - INFO - Response - Page 8:
2025-07-24 16:32:18,143 - INFO - 第 8 页获取到 50 条记录
2025-07-24 16:32:18,659 - INFO - Request Parameters - Page 9:
2025-07-24 16:32:18,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:32:18,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:32:19,377 - INFO - Response - Page 9:
2025-07-24 16:32:19,377 - INFO - 第 9 页获取到 50 条记录
2025-07-24 16:32:19,893 - INFO - Request Parameters - Page 10:
2025-07-24 16:32:19,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:32:19,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:32:20,581 - INFO - Response - Page 10:
2025-07-24 16:32:20,581 - INFO - 第 10 页获取到 36 条记录
2025-07-24 16:32:21,096 - INFO - 查询完成，共获取到 486 条记录
2025-07-24 16:32:21,096 - INFO - 获取到 486 条表单数据
2025-07-24 16:32:21,096 - INFO - 当前日期 2025-07-23 有 501 条MySQL数据需要处理
2025-07-24 16:32:21,112 - INFO - 开始批量插入 15 条新记录
2025-07-24 16:32:21,299 - INFO - 批量插入响应状态码: 200
2025-07-24 16:32:21,299 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 08:32:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '732', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6770BCCF-E3B0-7431-8F5B-EF4CBF090B5A', 'x-acs-trace-id': 'd7e2793d087362a1bcf12434299ec92f', 'etag': '7hsnw87IoooRzU6TbrUqKzA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 16:32:21,299 - INFO - 批量插入响应体: {'result': ['FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMS2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMT2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMU2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMV2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMW2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMX2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMY2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMZ2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM03', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM13', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM23', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM33', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM43', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM53', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM63']}
2025-07-24 16:32:21,299 - INFO - 批量插入表单数据成功，批次 1，共 15 条记录
2025-07-24 16:32:21,299 - INFO - 成功插入的数据ID: ['FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMS2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMT2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMU2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMV2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMW2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMX2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMY2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDMZ2', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM03', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM13', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM23', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM33', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM43', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM53', 'FINST-PAB66N71UGFXE7CZ9TQRQ4RFEK972XSIW4HDM63']
2025-07-24 16:32:26,315 - INFO - 批量插入完成，共 15 条记录
2025-07-24 16:32:26,315 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 15 条，错误: 0 条
2025-07-24 16:32:26,315 - INFO - 开始处理日期: 2025-07-24
2025-07-24 16:32:26,315 - INFO - Request Parameters - Page 1:
2025-07-24 16:32:26,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 16:32:26,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 16:32:26,799 - INFO - Response - Page 1:
2025-07-24 16:32:26,799 - INFO - 第 1 页获取到 3 条记录
2025-07-24 16:32:27,315 - INFO - 查询完成，共获取到 3 条记录
2025-07-24 16:32:27,315 - INFO - 获取到 3 条表单数据
2025-07-24 16:32:27,315 - INFO - 当前日期 2025-07-24 有 3 条MySQL数据需要处理
2025-07-24 16:32:27,315 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-24 16:32:27,315 - INFO - 数据同步完成！更新: 0 条，插入: 15 条，错误: 0 条
2025-07-24 16:32:27,315 - INFO - 同步完成
2025-07-24 19:30:34,224 - INFO - 使用默认增量同步（当天更新数据）
2025-07-24 19:30:34,224 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-24 19:30:34,224 - INFO - 查询参数: ('2025-07-24',)
2025-07-24 19:30:34,380 - INFO - MySQL查询成功，增量数据（日期: 2025-07-24），共获取 162 条记录
2025-07-24 19:30:34,380 - INFO - 获取到 4 个日期需要处理: ['2025-07-01', '2025-07-22', '2025-07-23', '2025-07-24']
2025-07-24 19:30:34,396 - INFO - 开始处理日期: 2025-07-01
2025-07-24 19:30:34,396 - INFO - Request Parameters - Page 1:
2025-07-24 19:30:34,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:30:34,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751299200000, 1751385599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:30:42,524 - ERROR - 处理日期 2025-07-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EC731A34-F207-73F5-8488-EAA3F94D8692 Response: {'code': 'ServiceUnavailable', 'requestid': 'EC731A34-F207-73F5-8488-EAA3F94D8692', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EC731A34-F207-73F5-8488-EAA3F94D8692)
2025-07-24 19:30:42,524 - INFO - 开始处理日期: 2025-07-22
2025-07-24 19:30:42,524 - INFO - Request Parameters - Page 1:
2025-07-24 19:30:42,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:30:42,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:30:50,199 - INFO - Response - Page 1:
2025-07-24 19:30:50,199 - INFO - 第 1 页获取到 50 条记录
2025-07-24 19:30:50,699 - INFO - Request Parameters - Page 2:
2025-07-24 19:30:50,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:30:50,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:30:51,434 - INFO - Response - Page 2:
2025-07-24 19:30:51,434 - INFO - 第 2 页获取到 50 条记录
2025-07-24 19:30:51,934 - INFO - Request Parameters - Page 3:
2025-07-24 19:30:51,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:30:51,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:30:52,700 - INFO - Response - Page 3:
2025-07-24 19:30:52,700 - INFO - 第 3 页获取到 50 条记录
2025-07-24 19:30:53,216 - INFO - Request Parameters - Page 4:
2025-07-24 19:30:53,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:30:53,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:30:53,951 - INFO - Response - Page 4:
2025-07-24 19:30:53,951 - INFO - 第 4 页获取到 50 条记录
2025-07-24 19:30:54,466 - INFO - Request Parameters - Page 5:
2025-07-24 19:30:54,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:30:54,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:30:55,170 - INFO - Response - Page 5:
2025-07-24 19:30:55,170 - INFO - 第 5 页获取到 50 条记录
2025-07-24 19:30:55,670 - INFO - Request Parameters - Page 6:
2025-07-24 19:30:55,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:30:55,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:30:56,342 - INFO - Response - Page 6:
2025-07-24 19:30:56,342 - INFO - 第 6 页获取到 50 条记录
2025-07-24 19:30:56,858 - INFO - Request Parameters - Page 7:
2025-07-24 19:30:56,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:30:56,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:30:57,546 - INFO - Response - Page 7:
2025-07-24 19:30:57,546 - INFO - 第 7 页获取到 50 条记录
2025-07-24 19:30:58,062 - INFO - Request Parameters - Page 8:
2025-07-24 19:30:58,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:30:58,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:30:58,749 - INFO - Response - Page 8:
2025-07-24 19:30:58,749 - INFO - 第 8 页获取到 50 条记录
2025-07-24 19:30:59,265 - INFO - Request Parameters - Page 9:
2025-07-24 19:30:59,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:30:59,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:30:59,906 - INFO - Response - Page 9:
2025-07-24 19:30:59,906 - INFO - 第 9 页获取到 50 条记录
2025-07-24 19:31:00,406 - INFO - Request Parameters - Page 10:
2025-07-24 19:31:00,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:00,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:01,157 - INFO - Response - Page 10:
2025-07-24 19:31:01,157 - INFO - 第 10 页获取到 50 条记录
2025-07-24 19:31:01,657 - INFO - Request Parameters - Page 11:
2025-07-24 19:31:01,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:01,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:02,204 - INFO - Response - Page 11:
2025-07-24 19:31:02,204 - INFO - 第 11 页获取到 16 条记录
2025-07-24 19:31:02,704 - INFO - 查询完成，共获取到 516 条记录
2025-07-24 19:31:02,704 - INFO - 获取到 516 条表单数据
2025-07-24 19:31:02,704 - INFO - 当前日期 2025-07-22 有 6 条MySQL数据需要处理
2025-07-24 19:31:02,704 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-24 19:31:02,704 - INFO - 开始处理日期: 2025-07-23
2025-07-24 19:31:02,704 - INFO - Request Parameters - Page 1:
2025-07-24 19:31:02,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:02,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:03,423 - INFO - Response - Page 1:
2025-07-24 19:31:03,423 - INFO - 第 1 页获取到 50 条记录
2025-07-24 19:31:03,939 - INFO - Request Parameters - Page 2:
2025-07-24 19:31:03,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:03,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:04,736 - INFO - Response - Page 2:
2025-07-24 19:31:04,736 - INFO - 第 2 页获取到 50 条记录
2025-07-24 19:31:05,252 - INFO - Request Parameters - Page 3:
2025-07-24 19:31:05,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:05,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:06,018 - INFO - Response - Page 3:
2025-07-24 19:31:06,018 - INFO - 第 3 页获取到 50 条记录
2025-07-24 19:31:06,534 - INFO - Request Parameters - Page 4:
2025-07-24 19:31:06,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:06,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:07,300 - INFO - Response - Page 4:
2025-07-24 19:31:07,300 - INFO - 第 4 页获取到 50 条记录
2025-07-24 19:31:07,816 - INFO - Request Parameters - Page 5:
2025-07-24 19:31:07,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:07,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:08,519 - INFO - Response - Page 5:
2025-07-24 19:31:08,519 - INFO - 第 5 页获取到 50 条记录
2025-07-24 19:31:09,035 - INFO - Request Parameters - Page 6:
2025-07-24 19:31:09,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:09,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:09,738 - INFO - Response - Page 6:
2025-07-24 19:31:09,738 - INFO - 第 6 页获取到 50 条记录
2025-07-24 19:31:10,254 - INFO - Request Parameters - Page 7:
2025-07-24 19:31:10,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:10,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:10,957 - INFO - Response - Page 7:
2025-07-24 19:31:10,957 - INFO - 第 7 页获取到 50 条记录
2025-07-24 19:31:11,473 - INFO - Request Parameters - Page 8:
2025-07-24 19:31:11,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:11,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:12,224 - INFO - Response - Page 8:
2025-07-24 19:31:12,224 - INFO - 第 8 页获取到 50 条记录
2025-07-24 19:31:12,739 - INFO - Request Parameters - Page 9:
2025-07-24 19:31:12,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:12,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:13,427 - INFO - Response - Page 9:
2025-07-24 19:31:13,427 - INFO - 第 9 页获取到 50 条记录
2025-07-24 19:31:13,943 - INFO - Request Parameters - Page 10:
2025-07-24 19:31:13,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:13,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:14,599 - INFO - Response - Page 10:
2025-07-24 19:31:14,599 - INFO - 第 10 页获取到 50 条记录
2025-07-24 19:31:15,100 - INFO - Request Parameters - Page 11:
2025-07-24 19:31:15,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:15,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:15,615 - INFO - Response - Page 11:
2025-07-24 19:31:15,615 - INFO - 第 11 页获取到 1 条记录
2025-07-24 19:31:16,116 - INFO - 查询完成，共获取到 501 条记录
2025-07-24 19:31:16,116 - INFO - 获取到 501 条表单数据
2025-07-24 19:31:16,116 - INFO - 当前日期 2025-07-23 有 144 条MySQL数据需要处理
2025-07-24 19:31:16,116 - INFO - 开始批量插入 2 条新记录
2025-07-24 19:31:16,288 - INFO - 批量插入响应状态码: 200
2025-07-24 19:31:16,288 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 11:31:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F7F1F793-EAB2-7DFA-B11A-FBA5EAA5338A', 'x-acs-trace-id': 'afdc422ae68b456f03502b4d781d048e', 'etag': '1L9Z3kyDveyz96fKmNy0cOw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 19:31:16,288 - INFO - 批量插入响应体: {'result': ['FINST-SI7661815IEXOW7UDGD18CZH50EX3KRLABHDM4F', 'FINST-SI7661815IEXOW7UDGD18CZH50EX3LRLABHDM5F']}
2025-07-24 19:31:16,288 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-24 19:31:16,288 - INFO - 成功插入的数据ID: ['FINST-SI7661815IEXOW7UDGD18CZH50EX3KRLABHDM4F', 'FINST-SI7661815IEXOW7UDGD18CZH50EX3LRLABHDM5F']
2025-07-24 19:31:21,305 - INFO - 批量插入完成，共 2 条记录
2025-07-24 19:31:21,305 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-24 19:31:21,305 - INFO - 开始处理日期: 2025-07-24
2025-07-24 19:31:21,305 - INFO - Request Parameters - Page 1:
2025-07-24 19:31:21,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:31:21,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:31:21,821 - INFO - Response - Page 1:
2025-07-24 19:31:21,821 - INFO - 第 1 页获取到 3 条记录
2025-07-24 19:31:22,337 - INFO - 查询完成，共获取到 3 条记录
2025-07-24 19:31:22,337 - INFO - 获取到 3 条表单数据
2025-07-24 19:31:22,337 - INFO - 当前日期 2025-07-24 有 3 条MySQL数据需要处理
2025-07-24 19:31:22,337 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-24 19:31:22,337 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 1 条
2025-07-24 19:32:22,376 - INFO - 开始同步昨天与今天的销售数据: 2025-07-23 至 2025-07-24
2025-07-24 19:32:22,376 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-24 19:32:22,376 - INFO - 查询参数: ('2025-07-23', '2025-07-24')
2025-07-24 19:32:22,533 - INFO - MySQL查询成功，时间段: 2025-07-23 至 2025-07-24，共获取 520 条记录
2025-07-24 19:32:22,533 - INFO - 获取到 2 个日期需要处理: ['2025-07-23', '2025-07-24']
2025-07-24 19:32:22,548 - INFO - 开始处理日期: 2025-07-23
2025-07-24 19:32:22,548 - INFO - Request Parameters - Page 1:
2025-07-24 19:32:22,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:32:22,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:32:23,299 - INFO - Response - Page 1:
2025-07-24 19:32:23,299 - INFO - 第 1 页获取到 50 条记录
2025-07-24 19:32:23,814 - INFO - Request Parameters - Page 2:
2025-07-24 19:32:23,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:32:23,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:32:24,549 - INFO - Response - Page 2:
2025-07-24 19:32:24,549 - INFO - 第 2 页获取到 50 条记录
2025-07-24 19:32:25,065 - INFO - Request Parameters - Page 3:
2025-07-24 19:32:25,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:32:25,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:32:25,815 - INFO - Response - Page 3:
2025-07-24 19:32:25,815 - INFO - 第 3 页获取到 50 条记录
2025-07-24 19:32:26,315 - INFO - Request Parameters - Page 4:
2025-07-24 19:32:26,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:32:26,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:32:26,988 - INFO - Response - Page 4:
2025-07-24 19:32:26,988 - INFO - 第 4 页获取到 50 条记录
2025-07-24 19:32:27,488 - INFO - Request Parameters - Page 5:
2025-07-24 19:32:27,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:32:27,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:32:28,332 - INFO - Response - Page 5:
2025-07-24 19:32:28,332 - INFO - 第 5 页获取到 50 条记录
2025-07-24 19:32:28,848 - INFO - Request Parameters - Page 6:
2025-07-24 19:32:28,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:32:28,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:32:29,551 - INFO - Response - Page 6:
2025-07-24 19:32:29,551 - INFO - 第 6 页获取到 50 条记录
2025-07-24 19:32:30,051 - INFO - Request Parameters - Page 7:
2025-07-24 19:32:30,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:32:30,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:32:30,770 - INFO - Response - Page 7:
2025-07-24 19:32:30,770 - INFO - 第 7 页获取到 50 条记录
2025-07-24 19:32:31,271 - INFO - Request Parameters - Page 8:
2025-07-24 19:32:31,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:32:31,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:32:31,974 - INFO - Response - Page 8:
2025-07-24 19:32:31,974 - INFO - 第 8 页获取到 50 条记录
2025-07-24 19:32:32,474 - INFO - Request Parameters - Page 9:
2025-07-24 19:32:32,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:32:32,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:32:33,146 - INFO - Response - Page 9:
2025-07-24 19:32:33,146 - INFO - 第 9 页获取到 50 条记录
2025-07-24 19:32:33,646 - INFO - Request Parameters - Page 10:
2025-07-24 19:32:33,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:32:33,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:32:34,381 - INFO - Response - Page 10:
2025-07-24 19:32:34,381 - INFO - 第 10 页获取到 50 条记录
2025-07-24 19:32:34,897 - INFO - Request Parameters - Page 11:
2025-07-24 19:32:34,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:32:34,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:32:35,382 - INFO - Response - Page 11:
2025-07-24 19:32:35,382 - INFO - 第 11 页获取到 3 条记录
2025-07-24 19:32:35,897 - INFO - 查询完成，共获取到 503 条记录
2025-07-24 19:32:35,897 - INFO - 获取到 503 条表单数据
2025-07-24 19:32:35,897 - INFO - 当前日期 2025-07-23 有 503 条MySQL数据需要处理
2025-07-24 19:32:35,913 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-24 19:32:35,913 - INFO - 开始处理日期: 2025-07-24
2025-07-24 19:32:35,913 - INFO - Request Parameters - Page 1:
2025-07-24 19:32:35,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 19:32:35,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 19:32:36,413 - INFO - Response - Page 1:
2025-07-24 19:32:36,413 - INFO - 第 1 页获取到 3 条记录
2025-07-24 19:32:36,913 - INFO - 查询完成，共获取到 3 条记录
2025-07-24 19:32:36,913 - INFO - 获取到 3 条表单数据
2025-07-24 19:32:36,913 - INFO - 当前日期 2025-07-24 有 3 条MySQL数据需要处理
2025-07-24 19:32:36,913 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-24 19:32:36,913 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-24 19:32:36,913 - INFO - 同步完成
2025-07-24 22:30:33,992 - INFO - 使用默认增量同步（当天更新数据）
2025-07-24 22:30:33,992 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-24 22:30:33,992 - INFO - 查询参数: ('2025-07-24',)
2025-07-24 22:30:34,164 - INFO - MySQL查询成功，增量数据（日期: 2025-07-24），共获取 230 条记录
2025-07-24 22:30:34,164 - INFO - 获取到 4 个日期需要处理: ['2025-07-01', '2025-07-22', '2025-07-23', '2025-07-24']
2025-07-24 22:30:34,164 - INFO - 开始处理日期: 2025-07-01
2025-07-24 22:30:34,164 - INFO - Request Parameters - Page 1:
2025-07-24 22:30:34,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:34,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751299200000, 1751385599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:30:42,308 - ERROR - 处理日期 2025-07-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6DA937D9-B6CF-75BD-A80F-D5E11844F0F8 Response: {'code': 'ServiceUnavailable', 'requestid': '6DA937D9-B6CF-75BD-A80F-D5E11844F0F8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6DA937D9-B6CF-75BD-A80F-D5E11844F0F8)
2025-07-24 22:30:42,308 - INFO - 开始处理日期: 2025-07-22
2025-07-24 22:30:42,308 - INFO - Request Parameters - Page 1:
2025-07-24 22:30:42,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:42,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:30:45,715 - INFO - Response - Page 1:
2025-07-24 22:30:45,715 - INFO - 第 1 页获取到 50 条记录
2025-07-24 22:30:46,231 - INFO - Request Parameters - Page 2:
2025-07-24 22:30:46,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:46,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:30:46,934 - INFO - Response - Page 2:
2025-07-24 22:30:46,934 - INFO - 第 2 页获取到 50 条记录
2025-07-24 22:30:47,435 - INFO - Request Parameters - Page 3:
2025-07-24 22:30:47,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:47,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:30:48,122 - INFO - Response - Page 3:
2025-07-24 22:30:48,122 - INFO - 第 3 页获取到 50 条记录
2025-07-24 22:30:48,638 - INFO - Request Parameters - Page 4:
2025-07-24 22:30:48,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:48,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:30:49,357 - INFO - Response - Page 4:
2025-07-24 22:30:49,357 - INFO - 第 4 页获取到 50 条记录
2025-07-24 22:30:49,873 - INFO - Request Parameters - Page 5:
2025-07-24 22:30:49,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:49,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:30:50,592 - INFO - Response - Page 5:
2025-07-24 22:30:50,592 - INFO - 第 5 页获取到 50 条记录
2025-07-24 22:30:51,092 - INFO - Request Parameters - Page 6:
2025-07-24 22:30:51,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:51,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:30:51,811 - INFO - Response - Page 6:
2025-07-24 22:30:51,811 - INFO - 第 6 页获取到 50 条记录
2025-07-24 22:30:52,327 - INFO - Request Parameters - Page 7:
2025-07-24 22:30:52,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:52,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:30:52,984 - INFO - Response - Page 7:
2025-07-24 22:30:52,984 - INFO - 第 7 页获取到 50 条记录
2025-07-24 22:30:53,499 - INFO - Request Parameters - Page 8:
2025-07-24 22:30:53,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:53,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:30:54,187 - INFO - Response - Page 8:
2025-07-24 22:30:54,187 - INFO - 第 8 页获取到 50 条记录
2025-07-24 22:30:54,687 - INFO - Request Parameters - Page 9:
2025-07-24 22:30:54,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:54,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:30:55,407 - INFO - Response - Page 9:
2025-07-24 22:30:55,407 - INFO - 第 9 页获取到 50 条记录
2025-07-24 22:30:55,922 - INFO - Request Parameters - Page 10:
2025-07-24 22:30:55,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:55,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:30:56,626 - INFO - Response - Page 10:
2025-07-24 22:30:56,641 - INFO - 第 10 页获取到 50 条记录
2025-07-24 22:30:57,157 - INFO - Request Parameters - Page 11:
2025-07-24 22:30:57,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:57,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:30:57,736 - INFO - Response - Page 11:
2025-07-24 22:30:57,736 - INFO - 第 11 页获取到 16 条记录
2025-07-24 22:30:58,251 - INFO - 查询完成，共获取到 516 条记录
2025-07-24 22:30:58,251 - INFO - 获取到 516 条表单数据
2025-07-24 22:30:58,251 - INFO - 当前日期 2025-07-22 有 6 条MySQL数据需要处理
2025-07-24 22:30:58,251 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-24 22:30:58,251 - INFO - 开始处理日期: 2025-07-23
2025-07-24 22:30:58,251 - INFO - Request Parameters - Page 1:
2025-07-24 22:30:58,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:58,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:30:59,049 - INFO - Response - Page 1:
2025-07-24 22:30:59,049 - INFO - 第 1 页获取到 50 条记录
2025-07-24 22:30:59,549 - INFO - Request Parameters - Page 2:
2025-07-24 22:30:59,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:30:59,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:31:00,268 - INFO - Response - Page 2:
2025-07-24 22:31:00,268 - INFO - 第 2 页获取到 50 条记录
2025-07-24 22:31:00,768 - INFO - Request Parameters - Page 3:
2025-07-24 22:31:00,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:31:00,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:31:01,690 - INFO - Response - Page 3:
2025-07-24 22:31:01,690 - INFO - 第 3 页获取到 50 条记录
2025-07-24 22:31:02,206 - INFO - Request Parameters - Page 4:
2025-07-24 22:31:02,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:31:02,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:31:02,988 - INFO - Response - Page 4:
2025-07-24 22:31:02,988 - INFO - 第 4 页获取到 50 条记录
2025-07-24 22:31:03,503 - INFO - Request Parameters - Page 5:
2025-07-24 22:31:03,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:31:03,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:31:04,207 - INFO - Response - Page 5:
2025-07-24 22:31:04,207 - INFO - 第 5 页获取到 50 条记录
2025-07-24 22:31:04,707 - INFO - Request Parameters - Page 6:
2025-07-24 22:31:04,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:31:04,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:31:05,442 - INFO - Response - Page 6:
2025-07-24 22:31:05,442 - INFO - 第 6 页获取到 50 条记录
2025-07-24 22:31:05,942 - INFO - Request Parameters - Page 7:
2025-07-24 22:31:05,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:31:05,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:31:06,661 - INFO - Response - Page 7:
2025-07-24 22:31:06,661 - INFO - 第 7 页获取到 50 条记录
2025-07-24 22:31:07,177 - INFO - Request Parameters - Page 8:
2025-07-24 22:31:07,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:31:07,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:31:07,896 - INFO - Response - Page 8:
2025-07-24 22:31:07,896 - INFO - 第 8 页获取到 50 条记录
2025-07-24 22:31:08,396 - INFO - Request Parameters - Page 9:
2025-07-24 22:31:08,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:31:08,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:31:09,131 - INFO - Response - Page 9:
2025-07-24 22:31:09,131 - INFO - 第 9 页获取到 50 条记录
2025-07-24 22:31:09,647 - INFO - Request Parameters - Page 10:
2025-07-24 22:31:09,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:31:09,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:31:10,366 - INFO - Response - Page 10:
2025-07-24 22:31:10,366 - INFO - 第 10 页获取到 50 条记录
2025-07-24 22:31:10,881 - INFO - Request Parameters - Page 11:
2025-07-24 22:31:10,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:31:10,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:31:11,382 - INFO - Response - Page 11:
2025-07-24 22:31:11,382 - INFO - 第 11 页获取到 3 条记录
2025-07-24 22:31:11,882 - INFO - 查询完成，共获取到 503 条记录
2025-07-24 22:31:11,882 - INFO - 获取到 503 条表单数据
2025-07-24 22:31:11,882 - INFO - 当前日期 2025-07-23 有 146 条MySQL数据需要处理
2025-07-24 22:31:11,882 - INFO - 开始批量插入 2 条新记录
2025-07-24 22:31:12,054 - INFO - 批量插入响应状态码: 200
2025-07-24 22:31:12,069 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 14:31:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E477E519-007E-73E3-B13C-F265A07712B8', 'x-acs-trace-id': 'ca35c0d73d79316773648c54cae2c330', 'etag': '1U50Rr0vpoEPWjfN1ZxbHHA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 22:31:12,069 - INFO - 批量插入响应体: {'result': ['FINST-B9C660C1CHFXYTJYAI3MU83TQWZL39JWPHHDMV4', 'FINST-B9C660C1CHFXYTJYAI3MU83TQWZL39JWPHHDMW4']}
2025-07-24 22:31:12,069 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-24 22:31:12,069 - INFO - 成功插入的数据ID: ['FINST-B9C660C1CHFXYTJYAI3MU83TQWZL39JWPHHDMV4', 'FINST-B9C660C1CHFXYTJYAI3MU83TQWZL39JWPHHDMW4']
2025-07-24 22:31:17,087 - INFO - 批量插入完成，共 2 条记录
2025-07-24 22:31:17,087 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-24 22:31:17,087 - INFO - 开始处理日期: 2025-07-24
2025-07-24 22:31:17,087 - INFO - Request Parameters - Page 1:
2025-07-24 22:31:17,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:31:17,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:31:17,556 - INFO - Response - Page 1:
2025-07-24 22:31:17,556 - INFO - 第 1 页获取到 3 条记录
2025-07-24 22:31:18,056 - INFO - 查询完成，共获取到 3 条记录
2025-07-24 22:31:18,056 - INFO - 获取到 3 条表单数据
2025-07-24 22:31:18,056 - INFO - 当前日期 2025-07-24 有 65 条MySQL数据需要处理
2025-07-24 22:31:18,056 - INFO - 开始批量插入 62 条新记录
2025-07-24 22:31:18,306 - INFO - 批量插入响应状态码: 200
2025-07-24 22:31:18,306 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 14:31:13 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '37B54C9F-CECD-7379-A591-8362B766626A', 'x-acs-trace-id': '1997f669ffe9ee7f154ad52843ab6413', 'etag': '2jfzTE1kjQzd6J4w+Ub9KTA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 22:31:18,306 - INFO - 批量插入响应体: {'result': ['FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDMC3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDMD3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDME3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDMF3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDMG3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDMH3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDMI3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMJ3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMK3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDML3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMM3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMN3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMO3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMP3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMQ3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMR3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMS3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMT3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMU3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMV3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMW3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMX3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMY3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMZ3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM04', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM14', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM24', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM34', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM44', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM54', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM64', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM74', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM84', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM94', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMA4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMB4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMC4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMD4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDME4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMF4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMG4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMH4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMI4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMJ4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMK4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDML4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMM4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMN4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMO4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMP4']}
2025-07-24 22:31:18,306 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-24 22:31:18,306 - INFO - 成功插入的数据ID: ['FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDMC3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDMD3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDME3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDMF3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDMG3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDMH3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2GC1QHHDMI3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMJ3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMK3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDML3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMM3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMN3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMO3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMP3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMQ3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMR3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMS3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMT3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMU3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMV3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMW3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMX3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMY3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMZ3', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM04', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM14', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM24', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM34', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM44', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM54', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM64', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM74', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM84', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDM94', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMA4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMB4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMC4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMD4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDME4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMF4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMG4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMH4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMI4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMJ4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMK4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDML4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMM4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMN4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMO4', 'FINST-NDC66NB1KHFXOVBP750QZ3G7ONYZ2HC1QHHDMP4']
2025-07-24 22:31:23,480 - INFO - 批量插入响应状态码: 200
2025-07-24 22:31:23,480 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 14:31:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '576', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '976C1D6C-451B-77C5-AFBC-9869A64299D2', 'x-acs-trace-id': '037918387d202ac507bc747bcfb61957', 'etag': '5696vOuCqH+iLvnqIlnf8pw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 22:31:23,480 - INFO - 批量插入响应体: {'result': ['FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDM6', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDM7', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDM8', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDM9', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMA', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMB', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMC', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMD', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDME', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMF', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMG', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMH']}
2025-07-24 22:31:23,480 - INFO - 批量插入表单数据成功，批次 2，共 12 条记录
2025-07-24 22:31:23,480 - INFO - 成功插入的数据ID: ['FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDM6', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDM7', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDM8', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDM9', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMA', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMB', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMC', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMD', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDME', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMF', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMG', 'FINST-7I8669818PFXV7I7FHOMCCCIVP2T3CC5QHHDMH']
2025-07-24 22:31:28,498 - INFO - 批量插入完成，共 62 条记录
2025-07-24 22:31:28,498 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 62 条，错误: 0 条
2025-07-24 22:31:28,498 - INFO - 数据同步完成！更新: 0 条，插入: 64 条，错误: 1 条
2025-07-24 22:32:28,537 - INFO - 开始同步昨天与今天的销售数据: 2025-07-23 至 2025-07-24
2025-07-24 22:32:28,537 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-24 22:32:28,537 - INFO - 查询参数: ('2025-07-23', '2025-07-24')
2025-07-24 22:32:28,693 - INFO - MySQL查询成功，时间段: 2025-07-23 至 2025-07-24，共获取 592 条记录
2025-07-24 22:32:28,693 - INFO - 获取到 2 个日期需要处理: ['2025-07-23', '2025-07-24']
2025-07-24 22:32:28,709 - INFO - 开始处理日期: 2025-07-23
2025-07-24 22:32:28,709 - INFO - Request Parameters - Page 1:
2025-07-24 22:32:28,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:32:28,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:32:29,444 - INFO - Response - Page 1:
2025-07-24 22:32:29,444 - INFO - 第 1 页获取到 50 条记录
2025-07-24 22:32:29,960 - INFO - Request Parameters - Page 2:
2025-07-24 22:32:29,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:32:29,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:32:30,632 - INFO - Response - Page 2:
2025-07-24 22:32:30,632 - INFO - 第 2 页获取到 50 条记录
2025-07-24 22:32:31,148 - INFO - Request Parameters - Page 3:
2025-07-24 22:32:31,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:32:31,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:32:31,898 - INFO - Response - Page 3:
2025-07-24 22:32:31,898 - INFO - 第 3 页获取到 50 条记录
2025-07-24 22:32:32,414 - INFO - Request Parameters - Page 4:
2025-07-24 22:32:32,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:32:32,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:32:33,117 - INFO - Response - Page 4:
2025-07-24 22:32:33,117 - INFO - 第 4 页获取到 50 条记录
2025-07-24 22:32:33,617 - INFO - Request Parameters - Page 5:
2025-07-24 22:32:33,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:32:33,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:32:34,305 - INFO - Response - Page 5:
2025-07-24 22:32:34,305 - INFO - 第 5 页获取到 50 条记录
2025-07-24 22:32:34,805 - INFO - Request Parameters - Page 6:
2025-07-24 22:32:34,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:32:34,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:32:35,493 - INFO - Response - Page 6:
2025-07-24 22:32:35,493 - INFO - 第 6 页获取到 50 条记录
2025-07-24 22:32:35,993 - INFO - Request Parameters - Page 7:
2025-07-24 22:32:35,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:32:35,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:32:36,759 - INFO - Response - Page 7:
2025-07-24 22:32:36,775 - INFO - 第 7 页获取到 50 条记录
2025-07-24 22:32:37,291 - INFO - Request Parameters - Page 8:
2025-07-24 22:32:37,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:32:37,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:32:38,025 - INFO - Response - Page 8:
2025-07-24 22:32:38,025 - INFO - 第 8 页获取到 50 条记录
2025-07-24 22:32:38,541 - INFO - Request Parameters - Page 9:
2025-07-24 22:32:38,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:32:38,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:32:39,291 - INFO - Response - Page 9:
2025-07-24 22:32:39,291 - INFO - 第 9 页获取到 50 条记录
2025-07-24 22:32:39,807 - INFO - Request Parameters - Page 10:
2025-07-24 22:32:39,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:32:39,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:32:40,464 - INFO - Response - Page 10:
2025-07-24 22:32:40,464 - INFO - 第 10 页获取到 50 条记录
2025-07-24 22:32:40,964 - INFO - Request Parameters - Page 11:
2025-07-24 22:32:40,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:32:40,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:32:41,495 - INFO - Response - Page 11:
2025-07-24 22:32:41,495 - INFO - 第 11 页获取到 5 条记录
2025-07-24 22:32:41,996 - INFO - 查询完成，共获取到 505 条记录
2025-07-24 22:32:41,996 - INFO - 获取到 505 条表单数据
2025-07-24 22:32:41,996 - INFO - 当前日期 2025-07-23 有 505 条MySQL数据需要处理
2025-07-24 22:32:42,011 - INFO - 日期 2025-07-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-24 22:32:42,011 - INFO - 开始处理日期: 2025-07-24
2025-07-24 22:32:42,011 - INFO - Request Parameters - Page 1:
2025-07-24 22:32:42,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:32:42,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:32:42,699 - INFO - Response - Page 1:
2025-07-24 22:32:42,699 - INFO - 第 1 页获取到 50 条记录
2025-07-24 22:32:43,199 - INFO - Request Parameters - Page 2:
2025-07-24 22:32:43,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-24 22:32:43,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-24 22:32:43,746 - INFO - Response - Page 2:
2025-07-24 22:32:43,746 - INFO - 第 2 页获取到 15 条记录
2025-07-24 22:32:44,247 - INFO - 查询完成，共获取到 65 条记录
2025-07-24 22:32:44,247 - INFO - 获取到 65 条表单数据
2025-07-24 22:32:44,247 - INFO - 当前日期 2025-07-24 有 69 条MySQL数据需要处理
2025-07-24 22:32:44,247 - INFO - 开始批量插入 4 条新记录
2025-07-24 22:32:44,403 - INFO - 批量插入响应状态码: 200
2025-07-24 22:32:44,403 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Jul 2025 14:32:39 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B8CF6897-B9E0-7CE8-93A2-99416E7B32CC', 'x-acs-trace-id': 'b32540245f92274741a35aaced7413b2', 'etag': '2cqGETC2wRbFmREscF2PpJA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-24 22:32:44,403 - INFO - 批量插入响应体: {'result': ['FINST-YWD66FA1KHFXW65V9H8VS5FTSOE428RVRHHDMF4', 'FINST-YWD66FA1KHFXW65V9H8VS5FTSOE428RVRHHDMG4', 'FINST-YWD66FA1KHFXW65V9H8VS5FTSOE428RVRHHDMH4', 'FINST-YWD66FA1KHFXW65V9H8VS5FTSOE428RVRHHDMI4']}
2025-07-24 22:32:44,403 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-07-24 22:32:44,403 - INFO - 成功插入的数据ID: ['FINST-YWD66FA1KHFXW65V9H8VS5FTSOE428RVRHHDMF4', 'FINST-YWD66FA1KHFXW65V9H8VS5FTSOE428RVRHHDMG4', 'FINST-YWD66FA1KHFXW65V9H8VS5FTSOE428RVRHHDMH4', 'FINST-YWD66FA1KHFXW65V9H8VS5FTSOE428RVRHHDMI4']
2025-07-24 22:32:49,420 - INFO - 批量插入完成，共 4 条记录
2025-07-24 22:32:49,420 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-07-24 22:32:49,420 - INFO - 数据同步完成！更新: 0 条，插入: 4 条，错误: 0 条
2025-07-24 22:32:49,420 - INFO - 同步完成
