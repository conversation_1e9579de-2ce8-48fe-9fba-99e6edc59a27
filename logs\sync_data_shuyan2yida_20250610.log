2025-06-10 08:00:03,322 - INFO - ==================================================
2025-06-10 08:00:03,322 - INFO - 程序启动 - 版本 v1.0.0
2025-06-10 08:00:03,322 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250610.log
2025-06-10 08:00:03,322 - INFO - ==================================================
2025-06-10 08:00:03,322 - INFO - 程序入口点: __main__
2025-06-10 08:00:03,322 - INFO - ==================================================
2025-06-10 08:00:03,322 - INFO - 程序启动 - 版本 v1.0.1
2025-06-10 08:00:03,322 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250610.log
2025-06-10 08:00:03,322 - INFO - ==================================================
2025-06-10 08:00:03,322 - INFO - MySQL数据库连接成功
2025-06-10 08:00:03,634 - INFO - MySQL数据库连接成功
2025-06-10 08:00:03,634 - INFO - sales_data表已存在，无需创建
2025-06-10 08:00:03,634 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-06-10 08:00:03,634 - INFO - DataSyncManager初始化完成
2025-06-10 08:00:03,634 - INFO - 开始更新店铺映射表...
2025-06-10 08:00:03,634 - INFO - 开始获取最近7天的店铺信息，时间范围: 2025-06-03 至 2025-06-09
2025-06-10 08:00:03,634 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:03,634 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:03,634 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7FE4F104AF06FA459646354F417A3687'}
2025-06-10 08:00:04,666 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:04,666 - INFO - 日期 ******** 获取到 328 条店铺记录
2025-06-10 08:00:05,181 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:05,181 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:05,181 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5C145ADCC1B3740B89C1EB7A38FEF852'}
2025-06-10 08:00:06,275 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:06,275 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-10 08:00:06,775 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:06,775 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:06,775 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AB4494141DCC529F3657C79F9A80DE43'}
2025-06-10 08:00:07,509 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:07,509 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-10 08:00:08,025 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:08,025 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:08,025 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '97FE634C35CA0AA116DD2A93D4FB7D02'}
2025-06-10 08:00:09,009 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:09,009 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-10 08:00:09,525 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:09,525 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:09,525 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '26230B45054C7BD64E2BBC9854994E0F'}
2025-06-10 08:00:10,150 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:10,150 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-10 08:00:10,665 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:10,665 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:10,665 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AC83F58914BBB1C11D75B01BE4684292'}
2025-06-10 08:00:11,369 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:11,369 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-10 08:00:11,884 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:11,884 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:11,884 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '98C5AC89F29F114F09A2C8CFE4229AED'}
2025-06-10 08:00:12,697 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:12,697 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-10 08:00:13,400 - INFO - 店铺映射表更新完成，总计: 328条，成功: 328条 (更新: 328条, 插入: 0条)
2025-06-10 08:00:13,400 - INFO - 店铺映射表更新完成
2025-06-10 08:00:13,400 - INFO - 未提供日期参数，使用默认值
2025-06-10 08:00:13,400 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-06-10 08:00:13,400 - INFO - 开始综合数据同步流程...
2025-06-10 08:00:13,400 - INFO - 当前错误日期列表为空
2025-06-10 08:00:13,400 - INFO - 正在获取数衍平台日销售数据...
2025-06-10 08:00:13,400 - INFO - 开始获取最近7天的店铺信息，时间范围: 2025-06-03 至 2025-06-09
2025-06-10 08:00:13,400 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:13,400 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:13,400 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F17128D5CA3D29DEB2F354DECD76BA01'}
2025-06-10 08:00:14,369 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:14,369 - INFO - 日期 ******** 获取到 328 条店铺记录
2025-06-10 08:00:14,869 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:14,869 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:14,869 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1244F993A31AB500A9547EAB7CE23A90'}
2025-06-10 08:00:15,494 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:15,494 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-10 08:00:16,009 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:16,009 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:16,009 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2CA28DCFDD6CD8F1100FE690DABEB38C'}
2025-06-10 08:00:16,540 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:16,556 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-10 08:00:17,072 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:17,072 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:17,072 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8DA73808B0031D7ED77CF38278246B73'}
2025-06-10 08:00:17,619 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:17,619 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-10 08:00:18,134 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:18,134 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:18,134 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A606C8B8AEC26A8062FB410CDE7E630E'}
2025-06-10 08:00:18,728 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:18,728 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-10 08:00:19,259 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:19,259 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:19,259 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7540616B200CC88F9F930FE711F5C169'}
2025-06-10 08:00:19,759 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:19,775 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-10 08:00:20,290 - INFO - 查询日期 ******** 的店铺信息
2025-06-10 08:00:20,290 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:20,290 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '287CC4624C2741261C7DF0C9C900065D'}
2025-06-10 08:00:20,775 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:20,775 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-10 08:00:21,478 - INFO - 店铺映射表更新完成，总计: 328条，成功: 328条 (更新: 328条, 插入: 0条)
2025-06-10 08:00:21,478 - INFO - 查询数衍平台数据，时间段为: 2025-04-10, 2025-06-09
2025-06-10 08:00:21,478 - INFO - 正在获取********至********的数据
2025-06-10 08:00:21,478 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:21,478 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '539700880AA99A2166A49561F1EDD086'}
2025-06-10 08:00:22,931 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:22,931 - INFO - 过滤后保留 422 条记录
2025-06-10 08:00:24,931 - INFO - 正在获取********至********的数据
2025-06-10 08:00:24,931 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:24,931 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '40A9214864FB82DA8BA8E6308447F2A0'}
2025-06-10 08:00:26,197 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:26,197 - INFO - 过滤后保留 440 条记录
2025-06-10 08:00:28,212 - INFO - 正在获取********至********的数据
2025-06-10 08:00:28,212 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:28,212 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D414362C6948010C49D8E8DC253928F4'}
2025-06-10 08:00:29,275 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:29,275 - INFO - 过滤后保留 429 条记录
2025-06-10 08:00:31,290 - INFO - 正在获取********至********的数据
2025-06-10 08:00:31,290 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:31,290 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B789CD8AF51094C6F252ECB2103E0F61'}
2025-06-10 08:00:32,243 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:32,243 - INFO - 过滤后保留 429 条记录
2025-06-10 08:00:34,259 - INFO - 正在获取********至********的数据
2025-06-10 08:00:34,259 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:34,259 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4C87EA2302190767F5D94996A7E2D686'}
2025-06-10 08:00:35,181 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:35,197 - INFO - 过滤后保留 432 条记录
2025-06-10 08:00:37,212 - INFO - 正在获取********至********的数据
2025-06-10 08:00:37,212 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:37,212 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F11EE748022BF82C3FCACF50A892FB64'}
2025-06-10 08:00:38,181 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:38,181 - INFO - 过滤后保留 429 条记录
2025-06-10 08:00:40,181 - INFO - 正在获取********至********的数据
2025-06-10 08:00:40,181 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:40,181 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F9586422B8221BD77A4C6F7C7C0B4A2F'}
2025-06-10 08:00:41,103 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:41,118 - INFO - 过滤后保留 419 条记录
2025-06-10 08:00:43,134 - INFO - 正在获取********至********的数据
2025-06-10 08:00:43,134 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:43,134 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '577772B1D240F08B89F2E08F4B1B21C2'}
2025-06-10 08:00:44,087 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:44,087 - INFO - 过滤后保留 416 条记录
2025-06-10 08:00:46,103 - INFO - 正在获取********至********的数据
2025-06-10 08:00:46,103 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:46,103 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1B5C39D08587665507742CE49A789E10'}
2025-06-10 08:00:47,087 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:47,087 - INFO - 过滤后保留 434 条记录
2025-06-10 08:00:49,087 - INFO - 正在获取********至********的数据
2025-06-10 08:00:49,087 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:49,087 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '57B74B16FB9E27D9E5490413229ABF6E'}
2025-06-10 08:00:49,946 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:49,962 - INFO - 过滤后保留 424 条记录
2025-06-10 08:00:51,978 - INFO - 正在获取********至********的数据
2025-06-10 08:00:51,978 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:51,978 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6DE6AA4C17E78B7D18C9C337AFD56967'}
2025-06-10 08:00:52,915 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:52,931 - INFO - 过滤后保留 433 条记录
2025-06-10 08:00:54,931 - INFO - 正在获取********至********的数据
2025-06-10 08:00:54,931 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:54,931 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'BC8A1E0A71CD18FCF3FD98872698C134'}
2025-06-10 08:00:55,775 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:55,790 - INFO - 过滤后保留 424 条记录
2025-06-10 08:00:57,806 - INFO - 正在获取********至********的数据
2025-06-10 08:00:57,806 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:00:57,806 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '07966F92236DE18AC1F1C744C26E7A67'}
2025-06-10 08:00:58,696 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:00:58,696 - INFO - 过滤后保留 428 条记录
2025-06-10 08:01:00,712 - INFO - 正在获取********至********的数据
2025-06-10 08:01:00,712 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:00,712 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FE80978BB90DA5FBAAC2DBECDA2B456A'}
2025-06-10 08:01:01,540 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:01,540 - INFO - 过滤后保留 411 条记录
2025-06-10 08:01:03,556 - INFO - 正在获取********至********的数据
2025-06-10 08:01:03,556 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:03,556 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1DD5FE7722003416E41227A636DBA039'}
2025-06-10 08:01:04,353 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:04,353 - INFO - 过滤后保留 423 条记录
2025-06-10 08:01:06,368 - INFO - 正在获取********至********的数据
2025-06-10 08:01:06,368 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:06,368 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B568A40800686F7AFBBF3FF0A5C26AA9'}
2025-06-10 08:01:07,259 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:07,274 - INFO - 过滤后保留 436 条记录
2025-06-10 08:01:09,274 - INFO - 正在获取********至********的数据
2025-06-10 08:01:09,274 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:09,274 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B9FD622C2668AFB432D19F33764F6C4C'}
2025-06-10 08:01:10,149 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:10,149 - INFO - 过滤后保留 425 条记录
2025-06-10 08:01:12,165 - INFO - 正在获取********至********的数据
2025-06-10 08:01:12,165 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:12,165 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CE9364701539FF0D83F08CAC303F6746'}
2025-06-10 08:01:13,056 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:13,056 - INFO - 过滤后保留 417 条记录
2025-06-10 08:01:15,071 - INFO - 正在获取********至********的数据
2025-06-10 08:01:15,071 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:15,071 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A5B1E5F8012FA17E81868B176F54783F'}
2025-06-10 08:01:15,899 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:15,899 - INFO - 过滤后保留 425 条记录
2025-06-10 08:01:17,899 - INFO - 正在获取********至********的数据
2025-06-10 08:01:17,899 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:17,899 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '77988A15A18D4F290452894305A43919'}
2025-06-10 08:01:18,743 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:18,759 - INFO - 过滤后保留 426 条记录
2025-06-10 08:01:20,774 - INFO - 正在获取********至********的数据
2025-06-10 08:01:20,774 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:20,774 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '858BA16AE8C43F3220BA126D0CE9A650'}
2025-06-10 08:01:21,571 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:21,571 - INFO - 过滤后保留 421 条记录
2025-06-10 08:01:23,587 - INFO - 正在获取********至********的数据
2025-06-10 08:01:23,587 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:23,587 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B8B818DE842265CC392A845ED856300D'}
2025-06-10 08:01:24,415 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:24,415 - INFO - 过滤后保留 416 条记录
2025-06-10 08:01:26,431 - INFO - 正在获取********至********的数据
2025-06-10 08:01:26,431 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:26,431 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '860B4C21D4FF78045C7BDDAAA5715BC5'}
2025-06-10 08:01:27,243 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:27,243 - INFO - 过滤后保留 424 条记录
2025-06-10 08:01:29,259 - INFO - 正在获取********至********的数据
2025-06-10 08:01:29,259 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:29,259 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FA73B8C979F69E026C7A7B2EC76F83AD'}
2025-06-10 08:01:30,040 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:30,040 - INFO - 过滤后保留 410 条记录
2025-06-10 08:01:32,056 - INFO - 正在获取********至********的数据
2025-06-10 08:01:32,056 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:32,056 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E7D7E277515C3D1ED8F01E1B988B7213'}
2025-06-10 08:01:32,790 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:32,790 - INFO - 过滤后保留 414 条记录
2025-06-10 08:01:34,806 - INFO - 正在获取********至********的数据
2025-06-10 08:01:34,806 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:34,806 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0980A0FCA541BBC8A698A6BF8B31F2E0'}
2025-06-10 08:01:35,571 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:35,587 - INFO - 过滤后保留 417 条记录
2025-06-10 08:01:37,602 - INFO - 正在获取********至********的数据
2025-06-10 08:01:37,602 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:37,602 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0BB0F019A2029E58FCFA94527C65DD7E'}
2025-06-10 08:01:38,306 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:38,321 - INFO - 过滤后保留 407 条记录
2025-06-10 08:01:40,337 - INFO - 正在获取********至********的数据
2025-06-10 08:01:40,337 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:40,337 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '12E12C6544320D68097393938B5682F6'}
2025-06-10 08:01:41,087 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:41,087 - INFO - 过滤后保留 397 条记录
2025-06-10 08:01:43,102 - INFO - 正在获取********至********的数据
2025-06-10 08:01:43,102 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:43,102 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AC02352A833441AD3B86F3D193E7EAAC'}
2025-06-10 08:01:43,899 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:43,899 - INFO - 过滤后保留 405 条记录
2025-06-10 08:01:45,915 - INFO - 正在获取********至********的数据
2025-06-10 08:01:45,915 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:45,915 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9D2DF1AC4EF64780845DB2F75C9A6270'}
2025-06-10 08:01:46,712 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:46,727 - INFO - 过滤后保留 405 条记录
2025-06-10 08:01:48,743 - INFO - 正在获取********至********的数据
2025-06-10 08:01:48,743 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-10 08:01:48,743 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'BBA155941614A6761AE07CF943D6140E'}
2025-06-10 08:01:49,352 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-10 08:01:49,352 - INFO - 过滤后保留 192 条记录
2025-06-10 08:01:51,368 - INFO - 开始保存数据到MySQL数据库，共 12830 条记录待处理
2025-06-10 08:01:51,587 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-11
2025-06-10 08:01:51,587 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:51,587 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-10
2025-06-10 08:01:51,587 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:51,743 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-11
2025-06-10 08:01:51,743 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:51,743 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-10
2025-06-10 08:01:51,743 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:51,977 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-13
2025-06-10 08:01:51,977 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:51,977 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-12
2025-06-10 08:01:51,977 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:52,134 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-13
2025-06-10 08:01:52,134 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:52,149 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-12
2025-06-10 08:01:52,149 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:52,368 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-15
2025-06-10 08:01:52,368 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:52,368 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-14
2025-06-10 08:01:52,368 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:52,524 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-15
2025-06-10 08:01:52,524 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:52,524 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-14
2025-06-10 08:01:52,524 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:52,743 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-16
2025-06-10 08:01:52,743 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:52,915 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-17
2025-06-10 08:01:52,915 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:52,915 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-16
2025-06-10 08:01:52,915 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:53,149 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-19
2025-06-10 08:01:53,149 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:53,305 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-19
2025-06-10 08:01:53,305 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:53,305 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-18
2025-06-10 08:01:53,305 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:53,540 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-20
2025-06-10 08:01:53,540 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:53,696 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-21
2025-06-10 08:01:53,696 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:53,712 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-20
2025-06-10 08:01:53,712 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:53,915 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-22
2025-06-10 08:01:53,915 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:54,071 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-23
2025-06-10 08:01:54,071 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:54,071 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-22
2025-06-10 08:01:54,071 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:54,290 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-25
2025-06-10 08:01:54,290 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:54,446 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-25
2025-06-10 08:01:54,446 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:54,446 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-24
2025-06-10 08:01:54,446 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:54,665 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-27
2025-06-10 08:01:54,665 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:54,665 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-26
2025-06-10 08:01:54,680 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:54,837 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-27
2025-06-10 08:01:54,837 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:54,837 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-26
2025-06-10 08:01:54,837 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:55,055 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-29
2025-06-10 08:01:55,055 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:55,055 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-28
2025-06-10 08:01:55,055 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:55,212 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-29
2025-06-10 08:01:55,212 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:55,212 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-28
2025-06-10 08:01:55,212 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:55,446 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-01
2025-06-10 08:01:55,446 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:55,446 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-04-30
2025-06-10 08:01:55,446 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:55,602 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-01
2025-06-10 08:01:55,602 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:55,602 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-04-30
2025-06-10 08:01:55,602 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:55,759 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-03
2025-06-10 08:01:55,759 - INFO - 变更字段: recommend_amount: 1544.1 -> 537.0, daily_bill_amount: 1544.1 -> 537.0
2025-06-10 08:01:55,759 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-02
2025-06-10 08:01:55,759 - INFO - 变更字段: recommend_amount: 191.0 -> 78.0, daily_bill_amount: 191.0 -> 78.0
2025-06-10 08:01:55,821 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-03
2025-06-10 08:01:55,821 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:55,837 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-02
2025-06-10 08:01:55,837 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:55,993 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-03
2025-06-10 08:01:55,993 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:55,993 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-02
2025-06-10 08:01:55,993 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:56,212 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-05
2025-06-10 08:01:56,212 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:56,212 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-04
2025-06-10 08:01:56,212 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:56,368 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-05
2025-06-10 08:01:56,368 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:56,368 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-04
2025-06-10 08:01:56,368 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:56,509 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-06
2025-06-10 08:01:56,509 - INFO - 变更字段: recommend_amount: 55.0 -> 0.0, daily_bill_amount: 55.0 -> 0.0
2025-06-10 08:01:56,587 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-07
2025-06-10 08:01:56,587 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:56,587 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-06
2025-06-10 08:01:56,587 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:56,727 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-07
2025-06-10 08:01:56,727 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:56,727 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-06
2025-06-10 08:01:56,727 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:56,884 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-09
2025-06-10 08:01:56,884 - INFO - 变更字段: recommend_amount: 9803.8 -> 8477.4, daily_bill_amount: 9803.8 -> 8477.4
2025-06-10 08:01:56,884 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-09
2025-06-10 08:01:56,884 - INFO - 变更字段: recommend_amount: 250.0 -> 182.0, daily_bill_amount: 250.0 -> 182.0
2025-06-10 08:01:56,962 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-08
2025-06-10 08:01:56,962 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:57,118 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-09
2025-06-10 08:01:57,118 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:57,118 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-08
2025-06-10 08:01:57,118 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:57,274 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-11
2025-06-10 08:01:57,274 - INFO - 变更字段: recommend_amount: 17946.4 -> 15410.9, daily_bill_amount: 17946.4 -> 15410.9
2025-06-10 08:01:57,274 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-10
2025-06-10 08:01:57,274 - INFO - 变更字段: recommend_amount: 11487.0 -> 10989.3, daily_bill_amount: 11487.0 -> 10989.3
2025-06-10 08:01:57,274 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-10
2025-06-10 08:01:57,274 - INFO - 变更字段: recommend_amount: 655.0 -> 224.0, daily_bill_amount: 655.0 -> 224.0
2025-06-10 08:01:57,352 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-11
2025-06-10 08:01:57,352 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:57,352 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-10
2025-06-10 08:01:57,352 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:57,509 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-11
2025-06-10 08:01:57,509 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:57,524 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-10
2025-06-10 08:01:57,524 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:57,665 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-13
2025-06-10 08:01:57,665 - INFO - 变更字段: recommend_amount: 116.0 -> 0.0, daily_bill_amount: 116.0 -> 0.0
2025-06-10 08:01:57,665 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-12
2025-06-10 08:01:57,665 - INFO - 变更字段: recommend_amount: 201.0 -> 0.0, daily_bill_amount: 201.0 -> 0.0
2025-06-10 08:01:57,743 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-12
2025-06-10 08:01:57,743 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:57,899 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-13
2025-06-10 08:01:57,899 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:57,899 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-12
2025-06-10 08:01:57,899 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:58,055 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-15
2025-06-10 08:01:58,055 - INFO - 变更字段: recommend_amount: 11966.5 -> 6119.9, daily_bill_amount: 11966.5 -> 6119.9
2025-06-10 08:01:58,055 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-14
2025-06-10 08:01:58,055 - INFO - 变更字段: recommend_amount: 7868.1 -> 5965.9, daily_bill_amount: 7868.1 -> 5965.9
2025-06-10 08:01:58,055 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-14
2025-06-10 08:01:58,055 - INFO - 变更字段: recommend_amount: 1701.2 -> 22.0, daily_bill_amount: 1701.2 -> 22.0
2025-06-10 08:01:58,134 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-15
2025-06-10 08:01:58,134 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:58,134 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-14
2025-06-10 08:01:58,134 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:58,274 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-15
2025-06-10 08:01:58,274 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:58,274 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-14
2025-06-10 08:01:58,274 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:58,430 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-17
2025-06-10 08:01:58,430 - INFO - 变更字段: recommend_amount: 13170.8 -> 12474.8, daily_bill_amount: 13170.8 -> 12474.8
2025-06-10 08:01:58,430 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-16
2025-06-10 08:01:58,430 - INFO - 变更字段: recommend_amount: 11316.1 -> 8199.0, daily_bill_amount: 11316.1 -> 8199.0
2025-06-10 08:01:58,430 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-17
2025-06-10 08:01:58,430 - INFO - 变更字段: recommend_amount: 8550.0 -> 8336.0, daily_bill_amount: 8550.0 -> 8336.0
2025-06-10 08:01:58,430 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-16
2025-06-10 08:01:58,430 - INFO - 变更字段: recommend_amount: 1468.9 -> 150.0, daily_bill_amount: 1468.9 -> 150.0
2025-06-10 08:01:58,508 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-17
2025-06-10 08:01:58,508 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:58,665 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-17
2025-06-10 08:01:58,665 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:58,665 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-16
2025-06-10 08:01:58,665 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:58,805 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-19
2025-06-10 08:01:58,805 - INFO - 变更字段: recommend_amount: 15688.2 -> 4953.3, daily_bill_amount: 15688.2 -> 4953.3
2025-06-10 08:01:58,805 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-18
2025-06-10 08:01:58,805 - INFO - 变更字段: recommend_amount: 22027.0 -> 11594.1, daily_bill_amount: 22027.0 -> 11594.1
2025-06-10 08:01:58,821 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-19
2025-06-10 08:01:58,821 - INFO - 变更字段: recommend_amount: 6046.0 -> 5765.0, daily_bill_amount: 6046.0 -> 5765.0
2025-06-10 08:01:58,884 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-18
2025-06-10 08:01:58,884 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:59,055 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-19
2025-06-10 08:01:59,055 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:59,055 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-18
2025-06-10 08:01:59,055 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:59,212 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-21
2025-06-10 08:01:59,212 - INFO - 变更字段: recommend_amount: 11443.6 -> 8439.2, daily_bill_amount: 11443.6 -> 8439.2
2025-06-10 08:01:59,212 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-20
2025-06-10 08:01:59,212 - INFO - 变更字段: recommend_amount: 27662.7 -> 12656.0, daily_bill_amount: 27662.7 -> 12656.0
2025-06-10 08:01:59,212 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-20
2025-06-10 08:01:59,212 - INFO - 变更字段: recommend_amount: 6426.5 -> 6252.0, daily_bill_amount: 6426.5 -> 6252.0
2025-06-10 08:01:59,274 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-21
2025-06-10 08:01:59,274 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:59,290 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-20
2025-06-10 08:01:59,290 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:59,446 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-21
2025-06-10 08:01:59,446 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:59,446 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-20
2025-06-10 08:01:59,446 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:59,602 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-23
2025-06-10 08:01:59,602 - INFO - 变更字段: recommend_amount: 10781.8 -> 8384.2, daily_bill_amount: 10781.8 -> 8384.2
2025-06-10 08:01:59,602 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-22
2025-06-10 08:01:59,602 - INFO - 变更字段: recommend_amount: 9265.1 -> 6684.6, daily_bill_amount: 9265.1 -> 6684.6
2025-06-10 08:01:59,680 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-23
2025-06-10 08:01:59,680 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:59,680 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-22
2025-06-10 08:01:59,680 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:01:59,837 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-23
2025-06-10 08:01:59,837 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:59,837 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-22
2025-06-10 08:01:59,837 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:01:59,993 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-25
2025-06-10 08:01:59,993 - INFO - 变更字段: recommend_amount: 18247.6 -> 10878.2, daily_bill_amount: 18247.6 -> 10878.2
2025-06-10 08:01:59,993 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-24
2025-06-10 08:01:59,993 - INFO - 变更字段: recommend_amount: 18865.6 -> 9443.3, daily_bill_amount: 18865.6 -> 9443.3
2025-06-10 08:02:00,071 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-25
2025-06-10 08:02:00,071 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:02:00,071 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-24
2025-06-10 08:02:00,071 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:02:00,227 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-25
2025-06-10 08:02:00,227 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:00,243 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-24
2025-06-10 08:02:00,243 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:00,399 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-27
2025-06-10 08:02:00,399 - INFO - 变更字段: recommend_amount: 27151.0 -> 5801.4, daily_bill_amount: 27151.0 -> 5801.4
2025-06-10 08:02:00,399 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-26
2025-06-10 08:02:00,399 - INFO - 变更字段: recommend_amount: 13459.1 -> 5405.9, daily_bill_amount: 13459.1 -> 5405.9
2025-06-10 08:02:00,399 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-27
2025-06-10 08:02:00,399 - INFO - 变更字段: recommend_amount: 382.0 -> 30.0, daily_bill_amount: 382.0 -> 30.0
2025-06-10 08:02:00,477 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-27
2025-06-10 08:02:00,477 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:02:00,618 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-27
2025-06-10 08:02:00,633 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:00,633 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-26
2025-06-10 08:02:00,633 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:00,774 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-29
2025-06-10 08:02:00,774 - INFO - 变更字段: recommend_amount: 38055.2 -> 15417.8, daily_bill_amount: 38055.2 -> 15417.8
2025-06-10 08:02:00,774 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-28
2025-06-10 08:02:00,774 - INFO - 变更字段: recommend_amount: 24001.9 -> 8193.3, daily_bill_amount: 24001.9 -> 8193.3
2025-06-10 08:02:00,774 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-29
2025-06-10 08:02:00,774 - INFO - 变更字段: recommend_amount: 196.0 -> 88.0, daily_bill_amount: 196.0 -> 88.0
2025-06-10 08:02:00,852 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-29
2025-06-10 08:02:00,852 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:02:01,008 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-29
2025-06-10 08:02:01,008 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:01,008 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-28
2025-06-10 08:02:01,008 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:01,149 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-31
2025-06-10 08:02:01,149 - INFO - 变更字段: recommend_amount: 24566.4 -> 11285.9, daily_bill_amount: 24566.4 -> 11285.9
2025-06-10 08:02:01,149 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-30
2025-06-10 08:02:01,149 - INFO - 变更字段: recommend_amount: 28350.4 -> 10474.3, daily_bill_amount: 28350.4 -> 10474.3
2025-06-10 08:02:01,165 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-31
2025-06-10 08:02:01,165 - INFO - 变更字段: recommend_amount: 1092.8 -> 0.0, daily_bill_amount: 1092.8 -> 0.0
2025-06-10 08:02:01,165 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-30
2025-06-10 08:02:01,165 - INFO - 变更字段: recommend_amount: 108.9 -> 0.0, daily_bill_amount: 108.9 -> 0.0
2025-06-10 08:02:01,227 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-05-31
2025-06-10 08:02:01,227 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:02:01,399 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-31
2025-06-10 08:02:01,399 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:01,399 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-30
2025-06-10 08:02:01,399 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:01,618 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-06-01
2025-06-10 08:02:01,618 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:02:01,774 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-02
2025-06-10 08:02:01,774 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:01,774 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-01
2025-06-10 08:02:01,774 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:01,977 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-06-04
2025-06-10 08:02:01,977 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:02:01,977 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-06-03
2025-06-10 08:02:01,977 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:02:02,118 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-04
2025-06-10 08:02:02,118 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:02,118 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-03
2025-06-10 08:02:02,118 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:02,274 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-06-06
2025-06-10 08:02:02,274 - INFO - 变更字段: recommend_amount: 6197.16 -> 6202.16, amount: 6197 -> 6202, count: 259 -> 260, online_amount: 4777.71 -> 4782.71, online_count: 199 -> 200
2025-06-10 08:02:02,337 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-06-06
2025-06-10 08:02:02,337 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:02:02,337 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-06-05
2025-06-10 08:02:02,337 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:02:02,493 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-06
2025-06-10 08:02:02,493 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:02,493 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-05
2025-06-10 08:02:02,493 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:02,493 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-06-08
2025-06-10 08:02:02,493 - INFO - 变更字段: recommend_amount: 0.0 -> 3619.6, daily_bill_amount: 0.0 -> 3619.6
2025-06-10 08:02:02,555 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDSD911K3E7Q2OV4FVC7C800148P, sale_time=2025-06-08
2025-06-10 08:02:02,555 - INFO - 变更字段: amount: 3255 -> 3272, count: 159 -> 161, instore_amount: 3376.89 -> 3393.89, instore_count: 159 -> 161
2025-06-10 08:02:02,555 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDRLHCKFK97Q2OV4FVC7BS00148D, sale_time=2025-06-08
2025-06-10 08:02:02,555 - INFO - 变更字段: amount: 4824 -> 4812
2025-06-10 08:02:02,555 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDQDVL1EG67Q2OV4FVC7B800147P, sale_time=2025-06-08
2025-06-10 08:02:02,555 - INFO - 变更字段: recommend_amount: 0.0 -> 2266.61, daily_bill_amount: 0.0 -> 2266.61, amount: 1304 -> 1312, count: 69 -> 71, online_amount: 945.7 -> 953.1, online_count: 52 -> 54
2025-06-10 08:02:02,571 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDJ5HVP5F47Q2OV4FVC77O001449, sale_time=2025-06-08
2025-06-10 08:02:02,571 - INFO - 变更字段: recommend_amount: 4399.0 -> 12392.6, daily_bill_amount: 0.0 -> 12392.6
2025-06-10 08:02:02,571 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDJ5HVP5F47Q2OV4FVC77O001449, sale_time=2025-06-07
2025-06-10 08:02:02,571 - INFO - 变更字段: recommend_amount: 5579.4 -> 14869.85, daily_bill_amount: 0.0 -> 14869.85
2025-06-10 08:02:02,587 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCIR7D5JD7Q2OVBN4IS7U001D40, sale_time=2025-06-08
2025-06-10 08:02:02,587 - INFO - 变更字段: amount: 4321 -> 4631, count: 11 -> 12, instore_amount: 4321.0 -> 4631.0, instore_count: 11 -> 12
2025-06-10 08:02:02,602 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINSC23H1J3M7Q2OV392410L00148H, sale_time=2025-06-07
2025-06-10 08:02:02,602 - INFO - 变更字段: amount: 2485 -> 4167, count: 5 -> 6, instore_amount: 2485.0 -> 4167.0, instore_count: 5 -> 6
2025-06-10 08:02:02,618 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-06-08
2025-06-10 08:02:02,618 - INFO - 变更字段: recommend_amount: 3161.38 -> 3265.28, amount: 3161 -> 3265, count: 172 -> 175, online_amount: 2781.81 -> 2885.71, online_count: 150 -> 153
2025-06-10 08:02:02,633 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEO8LGKTTH42F6DB81RH8V001P57, sale_time=2025-06-08
2025-06-10 08:02:02,633 - INFO - 变更字段: recommend_amount: 1864.33 -> 1872.33, amount: 1864 -> 1872, instore_amount: 1923.72 -> 1931.72
2025-06-10 08:02:02,633 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-06-08
2025-06-10 08:02:02,633 - INFO - 变更字段: recommend_amount: 506.0 -> 1856.0, daily_bill_amount: 506.0 -> 1856.0
2025-06-10 08:02:02,633 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-06-08
2025-06-10 08:02:02,633 - INFO - 变更字段: recommend_amount: 8887.14 -> 8976.84, amount: 8887 -> 8976, count: 155 -> 156, instore_amount: 6268.74 -> 6358.44, instore_count: 115 -> 116
2025-06-10 08:02:02,649 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-06-08
2025-06-10 08:02:02,649 - INFO - 变更字段: recommend_amount: 2175.6 -> 2179.14, amount: 2175 -> 2179, count: 145 -> 146, online_amount: 1063.49 -> 1067.03, online_count: 83 -> 84
2025-06-10 08:02:02,665 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-06-07
2025-06-10 08:02:02,665 - INFO - 变更字段: recommend_amount: 6556.27 -> 6559.27, amount: 6556 -> 6559, count: 296 -> 297, online_amount: 4232.66 -> 4235.66, online_count: 201 -> 202
2025-06-10 08:02:02,665 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEC0U8246I0I86N3H2U10A001F9G, sale_time=2025-06-07
2025-06-10 08:02:02,665 - INFO - 变更字段: amount: 9232 -> 9759, count: 106 -> 107, instore_amount: 5953.64 -> 6481.14, instore_count: 26 -> 27
2025-06-10 08:02:02,665 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-06-08
2025-06-10 08:02:02,665 - INFO - 变更字段: recommend_amount: 0.0 -> 16734.06, daily_bill_amount: 0.0 -> 16734.06
2025-06-10 08:02:02,680 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEA4041D6F0I86N3H2U1V9001F8F, sale_time=2025-06-08
2025-06-10 08:02:02,680 - INFO - 变更字段: amount: 28184 -> 28291, count: 187 -> 192, online_amount: 1712.36 -> 1819.66, online_count: 60 -> 65
2025-06-10 08:02:02,680 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE85EF5FB90I86N3H2U1U9001F7F, sale_time=2025-06-08
2025-06-10 08:02:02,680 - INFO - 变更字段: amount: 29788 -> 30039, count: 278 -> 279, instore_amount: 23290.5 -> 23541.5, instore_count: 160 -> 161
2025-06-10 08:02:02,680 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-06-08
2025-06-10 08:02:02,680 - INFO - 变更字段: amount: 47636 -> 49346, count: 330 -> 337, instore_amount: 27913.4 -> 29623.4, instore_count: 167 -> 174
2025-06-10 08:02:02,712 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-06-08
2025-06-10 08:02:02,712 - INFO - 变更字段: amount: 8858 -> 8918, count: 320 -> 324, online_amount: 4997.11 -> 5056.41, online_count: 192 -> 196
2025-06-10 08:02:02,712 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-06-07
2025-06-10 08:02:02,712 - INFO - 变更字段: online_amount: 5570.9 -> 5570.2
2025-06-10 08:02:02,727 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-06-08
2025-06-10 08:02:02,727 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:02:02,727 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJGUIA2QR0I86N3H2U1JH001ESN, sale_time=2025-06-07
2025-06-10 08:02:02,727 - INFO - 变更字段: shop_entity_name: 依幂 -> EAMIEYZ
2025-06-10 08:02:02,727 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-06-08
2025-06-10 08:02:02,727 - INFO - 变更字段: amount: 2618 -> 3403, count: 26 -> 35, instore_amount: 2026.63 -> 2810.89, instore_count: 18 -> 27
2025-06-10 08:02:02,743 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDF8HFHI690I86N3H2U1H9001EQF, sale_time=2025-06-08
2025-06-10 08:02:02,743 - INFO - 变更字段: amount: 1387 -> 1855, count: 6 -> 7, instore_amount: 1387.0 -> 1855.0, instore_count: 6 -> 7
2025-06-10 08:02:02,774 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDVBEGSM760I86N3H2U12H001EBN, sale_time=2025-06-08
2025-06-10 08:02:02,774 - INFO - 变更字段: recommend_amount: 4652.0 -> 7681.4, amount: 4652 -> 7681, count: 1 -> 2, instore_amount: 4652.0 -> 7681.4, instore_count: 1 -> 2
2025-06-10 08:02:02,774 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDUDM4PLNS0I86N3H2U121001EB7, sale_time=2025-06-08
2025-06-10 08:02:02,774 - INFO - 变更字段: amount: 8534 -> 8677, count: 282 -> 284, online_amount: 4928.9 -> 5072.1, online_count: 80 -> 82
2025-06-10 08:02:02,774 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-08
2025-06-10 08:02:02,774 - INFO - 变更字段: amount: 4382 -> 4420, count: 299 -> 307, online_amount: 4298.91 -> 4337.43, online_count: 275 -> 283
2025-06-10 08:02:02,790 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSK489TE20I86N3H2U114001EAA, sale_time=2025-06-08
2025-06-10 08:02:02,790 - INFO - 变更字段: recommend_amount: 4510.79 -> 5053.9, amount: 4510 -> 5053, count: 161 -> 162, instore_amount: 4422.29 -> 4965.4, instore_count: 159 -> 160
2025-06-10 08:02:02,790 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSC7N3PHM0I86N3H2U10T001EA3, sale_time=2025-06-08
2025-06-10 08:02:02,790 - INFO - 变更字段: amount: 5014 -> 5015, count: 239 -> 240, instore_amount: 739.2 -> 740.21, instore_count: 46 -> 47
2025-06-10 08:02:02,790 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDS341MMSU0I86N3H2U10P001E9V, sale_time=2025-06-08
2025-06-10 08:02:02,790 - INFO - 变更字段: amount: 1308 -> 1551, count: 41 -> 46, instore_amount: 1330.0 -> 1572.9, instore_count: 41 -> 46
2025-06-10 08:02:02,790 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDRR6FJ7A60I86N3H2U10L001E9R, sale_time=2025-06-08
2025-06-10 08:02:02,790 - INFO - 变更字段: recommend_amount: 5871.78 -> 5878.4, amount: 5871 -> 5878, count: 351 -> 352, instore_amount: 2834.28 -> 2840.9, instore_count: 207 -> 208
2025-06-10 08:02:02,790 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-08
2025-06-10 08:02:02,790 - INFO - 变更字段: amount: 6114 -> 6100, count: 426 -> 427, instore_amount: 3994.7 -> 4017.2, instore_count: 264 -> 267, online_amount: 2172.0 -> 2157.0, online_count: 162 -> 160
2025-06-10 08:02:02,790 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-07
2025-06-10 08:02:02,790 - INFO - 变更字段: instore_amount: 4688.75 -> 4690.85, instore_count: 291 -> 292, online_amount: 2145.5 -> 2143.4, online_count: 188 -> 187
2025-06-10 08:02:02,821 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FHI5VTHC3RHRI7Q2OVAE57DT4001C39, sale_time=2025-06-08
2025-06-10 08:02:02,821 - INFO - 变更字段: amount: 34606 -> 35176, count: 126 -> 127, instore_amount: 34606.0 -> 35176.0, instore_count: 126 -> 127
2025-06-10 08:02:02,837 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B, sale_time=2025-06-08
2025-06-10 08:02:02,837 - INFO - 变更字段: recommend_amount: 6921.21 -> 6926.72, amount: 6921 -> 6926, count: 373 -> 375, instore_amount: 3734.13 -> 3759.43, instore_count: 202 -> 204
2025-06-10 08:02:02,837 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0NLURMV9D3IKSIOCDI7R2001G23, sale_time=2025-06-08
2025-06-10 08:02:02,837 - INFO - 变更字段: recommend_amount: 0.0 -> 7506.7, daily_bill_amount: 0.0 -> 7506.7
2025-06-10 08:02:02,852 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK, sale_time=2025-06-08
2025-06-10 08:02:02,852 - INFO - 变更字段: amount: 12607 -> 13152, count: 62 -> 64, instore_amount: 11978.3 -> 12523.3, instore_count: 53 -> 55
2025-06-10 08:02:02,852 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-06-08
2025-06-10 08:02:02,852 - INFO - 变更字段: recommend_amount: 25846.4 -> 25864.15, daily_bill_amount: 25846.4 -> 25864.15, amount: 12470 -> 12469, online_amount: 889.25 -> 888.25
2025-06-10 08:02:02,868 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-06-07
2025-06-10 08:02:02,868 - INFO - 变更字段: amount: 21594 -> 21597, online_amount: 933.05 -> 936.05
2025-06-10 08:02:02,883 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-06-08
2025-06-10 08:02:02,883 - INFO - 变更字段: amount: 25902 -> 28052, count: 144 -> 146, instore_amount: 23040.19 -> 25190.19, instore_count: 113 -> 115
2025-06-10 08:02:02,899 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIQP8527T06E7AERKQ83JQ001UNS, sale_time=2025-06-08
2025-06-10 08:02:02,899 - INFO - 变更字段: amount: 1288 -> 1248
2025-06-10 08:02:02,899 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-06-08
2025-06-10 08:02:02,899 - INFO - 变更字段: recommend_amount: 0.0 -> 2481.13, daily_bill_amount: 0.0 -> 2481.13
2025-06-10 08:02:02,915 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-08
2025-06-10 08:02:02,915 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:02,915 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-07
2025-06-10 08:02:02,915 - INFO - 变更字段: shop_entity_name: SUBWAY -> 赛百味SUBWAY
2025-06-10 08:02:03,149 - INFO - MySQL数据保存完成，统计信息：
2025-06-10 08:02:03,149 - INFO - - 总记录数: 12830
2025-06-10 08:02:03,149 - INFO - - 成功插入: 198
2025-06-10 08:02:03,149 - INFO - - 成功更新: 186
2025-06-10 08:02:03,149 - INFO - - 无需更新: 12446
2025-06-10 08:02:03,149 - INFO - - 处理失败: 0
2025-06-10 08:02:03,165 - INFO - 成功获取数衍平台数据，共 12830 条记录
2025-06-10 08:02:03,165 - INFO - 正在更新MySQL月度汇总数据...
2025-06-10 08:02:03,196 - INFO - 月度数据表清空完成
2025-06-10 08:02:03,508 - INFO - 月度汇总数据更新完成，处理了 1404 条汇总记录
2025-06-10 08:02:03,508 - INFO - 成功更新月度汇总数据，共 1404 条记录
2025-06-10 08:02:03,508 - INFO - 正在获取宜搭日销售表单数据...
2025-06-10 08:02:03,508 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-04-10 00:00:00 至 2025-06-09 23:59:59
2025-06-10 08:02:03,508 - INFO - 查询分段 1: 2025-04-10 至 2025-04-11
2025-06-10 08:02:03,508 - INFO - 查询日期范围: 2025-04-10 至 2025-04-11，使用分页查询，每页 100 条记录
2025-06-10 08:02:03,508 - INFO - Request Parameters - Page 1:
2025-06-10 08:02:03,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:03,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400508, 1744300800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:07,274 - INFO - API请求耗时: 3766ms
2025-06-10 08:02:07,274 - INFO - Response - Page 1
2025-06-10 08:02:07,274 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:02:07,774 - INFO - Request Parameters - Page 2:
2025-06-10 08:02:07,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:07,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400508, 1744300800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:08,399 - INFO - API请求耗时: 625ms
2025-06-10 08:02:08,399 - INFO - Response - Page 2
2025-06-10 08:02:08,399 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:02:08,899 - INFO - Request Parameters - Page 3:
2025-06-10 08:02:08,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:08,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400508, 1744300800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:17,024 - ERROR - API请求失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 75E973B2-EF74-74B0-AAA9-5D8941BD3F2E Response: {'code': 'ServiceUnavailable', 'requestid': '75E973B2-EF74-74B0-AAA9-5D8941BD3F2E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-10 08:02:17,024 - WARNING - 已将错误日期范围添加到排除列表: 2025-04-10 至 2025-04-11
2025-06-10 08:02:17,024 - ERROR - 服务不可用，将等待后重试
2025-06-10 08:02:17,024 - ERROR - 获取第 3 页数据时出错: 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 75E973B2-EF74-74B0-AAA9-5D8941BD3F2E Response: {'code': 'ServiceUnavailable', 'requestid': '75E973B2-EF74-74B0-AAA9-5D8941BD3F2E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-10 08:02:17,024 - WARNING - 服务暂时不可用，等待 12 秒后重试...
2025-06-10 08:02:29,040 - WARNING - 由于服务不稳定，返回已获取的 200 条记录
2025-06-10 08:02:29,040 - INFO - 分段 1 查询成功，获取到 200 条记录
2025-06-10 08:02:30,055 - INFO - 查询分段 2: 2025-04-12 至 2025-04-13
2025-06-10 08:02:30,055 - INFO - 查询日期范围: 2025-04-12 至 2025-04-13，使用分页查询，每页 100 条记录
2025-06-10 08:02:30,055 - INFO - Request Parameters - Page 1:
2025-06-10 08:02:30,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:30,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200508, 1744473600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:30,743 - INFO - API请求耗时: 687ms
2025-06-10 08:02:30,743 - INFO - Response - Page 1
2025-06-10 08:02:30,743 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:02:31,243 - INFO - Request Parameters - Page 2:
2025-06-10 08:02:31,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:31,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200508, 1744473600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:31,977 - INFO - API请求耗时: 734ms
2025-06-10 08:02:31,977 - INFO - Response - Page 2
2025-06-10 08:02:31,977 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:02:32,493 - INFO - Request Parameters - Page 3:
2025-06-10 08:02:32,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:32,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200508, 1744473600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:33,211 - INFO - API请求耗时: 719ms
2025-06-10 08:02:33,211 - INFO - Response - Page 3
2025-06-10 08:02:33,211 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:02:33,711 - INFO - Request Parameters - Page 4:
2025-06-10 08:02:33,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:33,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200508, 1744473600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:34,399 - INFO - API请求耗时: 687ms
2025-06-10 08:02:34,399 - INFO - Response - Page 4
2025-06-10 08:02:34,399 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:02:34,915 - INFO - Request Parameters - Page 5:
2025-06-10 08:02:34,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:34,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744387200508, 1744473600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:35,493 - INFO - API请求耗时: 578ms
2025-06-10 08:02:35,493 - INFO - Response - Page 5
2025-06-10 08:02:35,493 - INFO - 第 5 页获取到 38 条记录
2025-06-10 08:02:35,493 - INFO - 查询完成，共获取到 438 条记录
2025-06-10 08:02:35,493 - INFO - 分段 2 查询成功，获取到 438 条记录
2025-06-10 08:02:36,493 - INFO - 查询分段 3: 2025-04-14 至 2025-04-15
2025-06-10 08:02:36,493 - INFO - 查询日期范围: 2025-04-14 至 2025-04-15，使用分页查询，每页 100 条记录
2025-06-10 08:02:36,493 - INFO - Request Parameters - Page 1:
2025-06-10 08:02:36,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:36,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000508, 1744646400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:37,274 - INFO - API请求耗时: 781ms
2025-06-10 08:02:37,274 - INFO - Response - Page 1
2025-06-10 08:02:37,274 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:02:37,774 - INFO - Request Parameters - Page 2:
2025-06-10 08:02:37,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:37,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000508, 1744646400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:38,493 - INFO - API请求耗时: 719ms
2025-06-10 08:02:38,493 - INFO - Response - Page 2
2025-06-10 08:02:38,493 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:02:38,993 - INFO - Request Parameters - Page 3:
2025-06-10 08:02:38,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:38,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000508, 1744646400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:39,774 - INFO - API请求耗时: 781ms
2025-06-10 08:02:39,774 - INFO - Response - Page 3
2025-06-10 08:02:39,774 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:02:40,289 - INFO - Request Parameters - Page 4:
2025-06-10 08:02:40,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:40,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000508, 1744646400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:40,961 - INFO - API请求耗时: 672ms
2025-06-10 08:02:40,961 - INFO - Response - Page 4
2025-06-10 08:02:40,961 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:02:41,461 - INFO - Request Parameters - Page 5:
2025-06-10 08:02:41,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:41,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000508, 1744646400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:42,008 - INFO - API请求耗时: 547ms
2025-06-10 08:02:42,008 - INFO - Response - Page 5
2025-06-10 08:02:42,024 - INFO - 第 5 页获取到 28 条记录
2025-06-10 08:02:42,024 - INFO - 查询完成，共获取到 428 条记录
2025-06-10 08:02:42,024 - INFO - 分段 3 查询成功，获取到 428 条记录
2025-06-10 08:02:43,024 - INFO - 查询分段 4: 2025-04-16 至 2025-04-17
2025-06-10 08:02:43,024 - INFO - 查询日期范围: 2025-04-16 至 2025-04-17，使用分页查询，每页 100 条记录
2025-06-10 08:02:43,024 - INFO - Request Parameters - Page 1:
2025-06-10 08:02:43,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:43,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800508, 1744819200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:43,680 - INFO - API请求耗时: 656ms
2025-06-10 08:02:43,680 - INFO - Response - Page 1
2025-06-10 08:02:43,680 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:02:44,196 - INFO - Request Parameters - Page 2:
2025-06-10 08:02:44,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:44,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800508, 1744819200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:44,977 - INFO - API请求耗时: 781ms
2025-06-10 08:02:44,977 - INFO - Response - Page 2
2025-06-10 08:02:44,977 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:02:45,493 - INFO - Request Parameters - Page 3:
2025-06-10 08:02:45,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:45,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800508, 1744819200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:46,289 - INFO - API请求耗时: 797ms
2025-06-10 08:02:46,289 - INFO - Response - Page 3
2025-06-10 08:02:46,289 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:02:46,789 - INFO - Request Parameters - Page 4:
2025-06-10 08:02:46,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:46,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800508, 1744819200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:47,711 - INFO - API请求耗时: 922ms
2025-06-10 08:02:47,711 - INFO - Response - Page 4
2025-06-10 08:02:47,711 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:02:48,227 - INFO - Request Parameters - Page 5:
2025-06-10 08:02:48,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:48,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800508, 1744819200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:48,711 - INFO - API请求耗时: 484ms
2025-06-10 08:02:48,711 - INFO - Response - Page 5
2025-06-10 08:02:48,711 - INFO - 第 5 页获取到 38 条记录
2025-06-10 08:02:48,711 - INFO - 查询完成，共获取到 438 条记录
2025-06-10 08:02:48,711 - INFO - 分段 4 查询成功，获取到 438 条记录
2025-06-10 08:02:49,711 - INFO - 查询分段 5: 2025-04-18 至 2025-04-19
2025-06-10 08:02:49,711 - INFO - 查询日期范围: 2025-04-18 至 2025-04-19，使用分页查询，每页 100 条记录
2025-06-10 08:02:49,711 - INFO - Request Parameters - Page 1:
2025-06-10 08:02:49,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:49,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600508, 1744992000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:50,477 - INFO - API请求耗时: 766ms
2025-06-10 08:02:50,477 - INFO - Response - Page 1
2025-06-10 08:02:50,477 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:02:50,977 - INFO - Request Parameters - Page 2:
2025-06-10 08:02:50,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:50,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600508, 1744992000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:51,664 - INFO - API请求耗时: 687ms
2025-06-10 08:02:51,664 - INFO - Response - Page 2
2025-06-10 08:02:51,664 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:02:52,164 - INFO - Request Parameters - Page 3:
2025-06-10 08:02:52,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:52,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600508, 1744992000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:52,914 - INFO - API请求耗时: 750ms
2025-06-10 08:02:52,914 - INFO - Response - Page 3
2025-06-10 08:02:52,914 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:02:53,430 - INFO - Request Parameters - Page 4:
2025-06-10 08:02:53,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:53,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600508, 1744992000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:54,164 - INFO - API请求耗时: 734ms
2025-06-10 08:02:54,164 - INFO - Response - Page 4
2025-06-10 08:02:54,164 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:02:54,664 - INFO - Request Parameters - Page 5:
2025-06-10 08:02:54,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:54,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600508, 1744992000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:55,258 - INFO - API请求耗时: 594ms
2025-06-10 08:02:55,258 - INFO - Response - Page 5
2025-06-10 08:02:55,258 - INFO - 第 5 页获取到 30 条记录
2025-06-10 08:02:55,258 - INFO - 查询完成，共获取到 430 条记录
2025-06-10 08:02:55,258 - INFO - 分段 5 查询成功，获取到 430 条记录
2025-06-10 08:02:56,258 - INFO - 查询分段 6: 2025-04-20 至 2025-04-21
2025-06-10 08:02:56,258 - INFO - 查询日期范围: 2025-04-20 至 2025-04-21，使用分页查询，每页 100 条记录
2025-06-10 08:02:56,258 - INFO - Request Parameters - Page 1:
2025-06-10 08:02:56,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:56,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400508, 1745164800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:56,961 - INFO - API请求耗时: 703ms
2025-06-10 08:02:56,961 - INFO - Response - Page 1
2025-06-10 08:02:56,961 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:02:57,461 - INFO - Request Parameters - Page 2:
2025-06-10 08:02:57,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:57,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400508, 1745164800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:58,133 - INFO - API请求耗时: 672ms
2025-06-10 08:02:58,133 - INFO - Response - Page 2
2025-06-10 08:02:58,133 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:02:58,633 - INFO - Request Parameters - Page 3:
2025-06-10 08:02:58,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:58,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400508, 1745164800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:02:59,399 - INFO - API请求耗时: 766ms
2025-06-10 08:02:59,399 - INFO - Response - Page 3
2025-06-10 08:02:59,399 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:02:59,914 - INFO - Request Parameters - Page 4:
2025-06-10 08:02:59,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:02:59,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400508, 1745164800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:00,602 - INFO - API请求耗时: 687ms
2025-06-10 08:03:00,602 - INFO - Response - Page 4
2025-06-10 08:03:00,602 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:03:01,117 - INFO - Request Parameters - Page 5:
2025-06-10 08:03:01,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:01,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400508, 1745164800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:01,586 - INFO - API请求耗时: 469ms
2025-06-10 08:03:01,586 - INFO - Response - Page 5
2025-06-10 08:03:01,586 - INFO - 第 5 页获取到 26 条记录
2025-06-10 08:03:01,586 - INFO - 查询完成，共获取到 426 条记录
2025-06-10 08:03:01,586 - INFO - 分段 6 查询成功，获取到 426 条记录
2025-06-10 08:03:02,602 - INFO - 查询分段 7: 2025-04-22 至 2025-04-23
2025-06-10 08:03:02,602 - INFO - 查询日期范围: 2025-04-22 至 2025-04-23，使用分页查询，每页 100 条记录
2025-06-10 08:03:02,602 - INFO - Request Parameters - Page 1:
2025-06-10 08:03:02,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:02,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200508, 1745337600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:03,242 - INFO - API请求耗时: 641ms
2025-06-10 08:03:03,242 - INFO - Response - Page 1
2025-06-10 08:03:03,242 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:03:03,742 - INFO - Request Parameters - Page 2:
2025-06-10 08:03:03,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:03,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200508, 1745337600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:04,711 - INFO - API请求耗时: 969ms
2025-06-10 08:03:04,711 - INFO - Response - Page 2
2025-06-10 08:03:04,711 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:03:05,227 - INFO - Request Parameters - Page 3:
2025-06-10 08:03:05,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:05,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200508, 1745337600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:06,039 - INFO - API请求耗时: 812ms
2025-06-10 08:03:06,039 - INFO - Response - Page 3
2025-06-10 08:03:06,039 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:03:06,555 - INFO - Request Parameters - Page 4:
2025-06-10 08:03:06,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:06,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200508, 1745337600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:07,258 - INFO - API请求耗时: 703ms
2025-06-10 08:03:07,258 - INFO - Response - Page 4
2025-06-10 08:03:07,258 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:03:07,774 - INFO - Request Parameters - Page 5:
2025-06-10 08:03:07,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:07,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200508, 1745337600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:08,211 - INFO - API请求耗时: 437ms
2025-06-10 08:03:08,211 - INFO - Response - Page 5
2025-06-10 08:03:08,211 - INFO - 第 5 页获取到 14 条记录
2025-06-10 08:03:08,211 - INFO - 查询完成，共获取到 414 条记录
2025-06-10 08:03:08,211 - INFO - 分段 7 查询成功，获取到 414 条记录
2025-06-10 08:03:09,211 - INFO - 查询分段 8: 2025-04-24 至 2025-04-25
2025-06-10 08:03:09,211 - INFO - 查询日期范围: 2025-04-24 至 2025-04-25，使用分页查询，每页 100 条记录
2025-06-10 08:03:09,211 - INFO - Request Parameters - Page 1:
2025-06-10 08:03:09,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:09,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000508, 1745510400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:09,992 - INFO - API请求耗时: 781ms
2025-06-10 08:03:09,992 - INFO - Response - Page 1
2025-06-10 08:03:10,008 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:03:10,524 - INFO - Request Parameters - Page 2:
2025-06-10 08:03:10,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:10,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000508, 1745510400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:11,211 - INFO - API请求耗时: 687ms
2025-06-10 08:03:11,211 - INFO - Response - Page 2
2025-06-10 08:03:11,211 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:03:11,711 - INFO - Request Parameters - Page 3:
2025-06-10 08:03:11,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:11,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000508, 1745510400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:12,321 - INFO - API请求耗时: 609ms
2025-06-10 08:03:12,321 - INFO - Response - Page 3
2025-06-10 08:03:12,321 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:03:12,821 - INFO - Request Parameters - Page 4:
2025-06-10 08:03:12,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:12,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000508, 1745510400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:13,492 - INFO - API请求耗时: 672ms
2025-06-10 08:03:13,492 - INFO - Response - Page 4
2025-06-10 08:03:13,492 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:03:14,008 - INFO - Request Parameters - Page 5:
2025-06-10 08:03:14,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:14,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000508, 1745510400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:14,633 - INFO - API请求耗时: 625ms
2025-06-10 08:03:14,633 - INFO - Response - Page 5
2025-06-10 08:03:14,633 - INFO - 第 5 页获取到 18 条记录
2025-06-10 08:03:14,633 - INFO - 查询完成，共获取到 418 条记录
2025-06-10 08:03:14,633 - INFO - 分段 8 查询成功，获取到 418 条记录
2025-06-10 08:03:15,649 - INFO - 查询分段 9: 2025-04-26 至 2025-04-27
2025-06-10 08:03:15,649 - INFO - 查询日期范围: 2025-04-26 至 2025-04-27，使用分页查询，每页 100 条记录
2025-06-10 08:03:15,649 - INFO - Request Parameters - Page 1:
2025-06-10 08:03:15,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:15,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800508, 1745683200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:16,414 - INFO - API请求耗时: 766ms
2025-06-10 08:03:16,414 - INFO - Response - Page 1
2025-06-10 08:03:16,414 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:03:16,914 - INFO - Request Parameters - Page 2:
2025-06-10 08:03:16,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:16,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800508, 1745683200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:17,508 - INFO - API请求耗时: 594ms
2025-06-10 08:03:17,508 - INFO - Response - Page 2
2025-06-10 08:03:17,508 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:03:18,008 - INFO - Request Parameters - Page 3:
2025-06-10 08:03:18,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:18,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800508, 1745683200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:18,711 - INFO - API请求耗时: 703ms
2025-06-10 08:03:18,711 - INFO - Response - Page 3
2025-06-10 08:03:18,711 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:03:19,227 - INFO - Request Parameters - Page 4:
2025-06-10 08:03:19,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:19,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800508, 1745683200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:20,117 - INFO - API请求耗时: 891ms
2025-06-10 08:03:20,117 - INFO - Response - Page 4
2025-06-10 08:03:20,117 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:03:20,617 - INFO - Request Parameters - Page 5:
2025-06-10 08:03:20,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:20,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800508, 1745683200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:21,195 - INFO - API请求耗时: 578ms
2025-06-10 08:03:21,195 - INFO - Response - Page 5
2025-06-10 08:03:21,195 - INFO - 第 5 页获取到 32 条记录
2025-06-10 08:03:21,195 - INFO - 查询完成，共获取到 432 条记录
2025-06-10 08:03:21,195 - INFO - 分段 9 查询成功，获取到 432 条记录
2025-06-10 08:03:22,211 - INFO - 查询分段 10: 2025-04-28 至 2025-04-29
2025-06-10 08:03:22,211 - INFO - 查询日期范围: 2025-04-28 至 2025-04-29，使用分页查询，每页 100 条记录
2025-06-10 08:03:22,211 - INFO - Request Parameters - Page 1:
2025-06-10 08:03:22,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:22,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600508, 1745856000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:22,836 - INFO - API请求耗时: 625ms
2025-06-10 08:03:22,836 - INFO - Response - Page 1
2025-06-10 08:03:22,836 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:03:23,352 - INFO - Request Parameters - Page 2:
2025-06-10 08:03:23,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:23,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600508, 1745856000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:24,024 - INFO - API请求耗时: 672ms
2025-06-10 08:03:24,024 - INFO - Response - Page 2
2025-06-10 08:03:24,024 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:03:24,524 - INFO - Request Parameters - Page 3:
2025-06-10 08:03:24,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:24,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600508, 1745856000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:25,164 - INFO - API请求耗时: 641ms
2025-06-10 08:03:25,164 - INFO - Response - Page 3
2025-06-10 08:03:25,164 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:03:25,680 - INFO - Request Parameters - Page 4:
2025-06-10 08:03:25,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:25,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600508, 1745856000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:26,336 - INFO - API请求耗时: 656ms
2025-06-10 08:03:26,336 - INFO - Response - Page 4
2025-06-10 08:03:26,336 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:03:26,852 - INFO - Request Parameters - Page 5:
2025-06-10 08:03:26,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:26,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600508, 1745856000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:27,367 - INFO - API请求耗时: 516ms
2025-06-10 08:03:27,367 - INFO - Response - Page 5
2025-06-10 08:03:27,367 - INFO - 第 5 页获取到 24 条记录
2025-06-10 08:03:27,367 - INFO - 查询完成，共获取到 424 条记录
2025-06-10 08:03:27,367 - INFO - 分段 10 查询成功，获取到 424 条记录
2025-06-10 08:03:28,383 - INFO - 查询分段 11: 2025-04-30 至 2025-05-01
2025-06-10 08:03:28,383 - INFO - 查询日期范围: 2025-04-30 至 2025-05-01，使用分页查询，每页 100 条记录
2025-06-10 08:03:28,383 - INFO - Request Parameters - Page 1:
2025-06-10 08:03:28,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:28,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400508, 1746028800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:29,055 - INFO - API请求耗时: 672ms
2025-06-10 08:03:29,055 - INFO - Response - Page 1
2025-06-10 08:03:29,055 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:03:29,570 - INFO - Request Parameters - Page 2:
2025-06-10 08:03:29,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:29,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400508, 1746028800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:30,320 - INFO - API请求耗时: 750ms
2025-06-10 08:03:30,320 - INFO - Response - Page 2
2025-06-10 08:03:30,320 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:03:30,820 - INFO - Request Parameters - Page 3:
2025-06-10 08:03:30,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:30,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400508, 1746028800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:31,633 - INFO - API请求耗时: 812ms
2025-06-10 08:03:31,633 - INFO - Response - Page 3
2025-06-10 08:03:31,633 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:03:32,149 - INFO - Request Parameters - Page 4:
2025-06-10 08:03:32,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:32,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400508, 1746028800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:32,852 - INFO - API请求耗时: 703ms
2025-06-10 08:03:32,852 - INFO - Response - Page 4
2025-06-10 08:03:32,852 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:03:33,367 - INFO - Request Parameters - Page 5:
2025-06-10 08:03:33,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:33,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400508, 1746028800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:33,883 - INFO - API请求耗时: 516ms
2025-06-10 08:03:33,883 - INFO - Response - Page 5
2025-06-10 08:03:33,883 - INFO - 第 5 页获取到 32 条记录
2025-06-10 08:03:33,883 - INFO - 查询完成，共获取到 432 条记录
2025-06-10 08:03:33,883 - INFO - 分段 11 查询成功，获取到 432 条记录
2025-06-10 08:03:34,898 - INFO - 查询分段 12: 2025-05-02 至 2025-05-03
2025-06-10 08:03:34,898 - INFO - 查询日期范围: 2025-05-02 至 2025-05-03，使用分页查询，每页 100 条记录
2025-06-10 08:03:34,898 - INFO - Request Parameters - Page 1:
2025-06-10 08:03:34,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:34,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200508, 1746201600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:35,633 - INFO - API请求耗时: 734ms
2025-06-10 08:03:35,633 - INFO - Response - Page 1
2025-06-10 08:03:35,633 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:03:36,133 - INFO - Request Parameters - Page 2:
2025-06-10 08:03:36,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:36,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200508, 1746201600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:36,914 - INFO - API请求耗时: 781ms
2025-06-10 08:03:36,914 - INFO - Response - Page 2
2025-06-10 08:03:36,914 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:03:37,430 - INFO - Request Parameters - Page 3:
2025-06-10 08:03:37,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:37,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200508, 1746201600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:38,133 - INFO - API请求耗时: 703ms
2025-06-10 08:03:38,133 - INFO - Response - Page 3
2025-06-10 08:03:38,133 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:03:38,648 - INFO - Request Parameters - Page 4:
2025-06-10 08:03:38,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:38,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200508, 1746201600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:39,602 - INFO - API请求耗时: 953ms
2025-06-10 08:03:39,602 - INFO - Response - Page 4
2025-06-10 08:03:39,602 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:03:40,102 - INFO - Request Parameters - Page 5:
2025-06-10 08:03:40,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:40,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200508, 1746201600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:40,695 - INFO - API请求耗时: 594ms
2025-06-10 08:03:40,695 - INFO - Response - Page 5
2025-06-10 08:03:40,695 - INFO - 第 5 页获取到 26 条记录
2025-06-10 08:03:40,695 - INFO - 查询完成，共获取到 426 条记录
2025-06-10 08:03:40,695 - INFO - 分段 12 查询成功，获取到 426 条记录
2025-06-10 08:03:41,695 - INFO - 查询分段 13: 2025-05-04 至 2025-05-05
2025-06-10 08:03:41,695 - INFO - 查询日期范围: 2025-05-04 至 2025-05-05，使用分页查询，每页 100 条记录
2025-06-10 08:03:41,695 - INFO - Request Parameters - Page 1:
2025-06-10 08:03:41,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:41,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000508, 1746374400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:42,367 - INFO - API请求耗时: 672ms
2025-06-10 08:03:42,367 - INFO - Response - Page 1
2025-06-10 08:03:42,367 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:03:42,867 - INFO - Request Parameters - Page 2:
2025-06-10 08:03:42,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:42,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000508, 1746374400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:43,461 - INFO - API请求耗时: 594ms
2025-06-10 08:03:43,461 - INFO - Response - Page 2
2025-06-10 08:03:43,461 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:03:43,961 - INFO - Request Parameters - Page 3:
2025-06-10 08:03:43,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:43,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000508, 1746374400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:44,648 - INFO - API请求耗时: 687ms
2025-06-10 08:03:44,648 - INFO - Response - Page 3
2025-06-10 08:03:44,648 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:03:45,148 - INFO - Request Parameters - Page 4:
2025-06-10 08:03:45,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:45,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000508, 1746374400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:45,867 - INFO - API请求耗时: 719ms
2025-06-10 08:03:45,867 - INFO - Response - Page 4
2025-06-10 08:03:45,867 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:03:46,383 - INFO - Request Parameters - Page 5:
2025-06-10 08:03:46,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:46,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000508, 1746374400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:46,914 - INFO - API请求耗时: 531ms
2025-06-10 08:03:46,914 - INFO - Response - Page 5
2025-06-10 08:03:46,914 - INFO - 第 5 页获取到 26 条记录
2025-06-10 08:03:46,914 - INFO - 查询完成，共获取到 426 条记录
2025-06-10 08:03:46,914 - INFO - 分段 13 查询成功，获取到 426 条记录
2025-06-10 08:03:47,914 - INFO - 查询分段 14: 2025-05-06 至 2025-05-07
2025-06-10 08:03:47,914 - INFO - 查询日期范围: 2025-05-06 至 2025-05-07，使用分页查询，每页 100 条记录
2025-06-10 08:03:47,914 - INFO - Request Parameters - Page 1:
2025-06-10 08:03:47,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:47,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800508, 1746547200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:48,648 - INFO - API请求耗时: 734ms
2025-06-10 08:03:48,648 - INFO - Response - Page 1
2025-06-10 08:03:48,648 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:03:49,148 - INFO - Request Parameters - Page 2:
2025-06-10 08:03:49,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:49,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800508, 1746547200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:49,852 - INFO - API请求耗时: 703ms
2025-06-10 08:03:49,852 - INFO - Response - Page 2
2025-06-10 08:03:49,852 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:03:50,352 - INFO - Request Parameters - Page 3:
2025-06-10 08:03:50,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:50,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800508, 1746547200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:51,039 - INFO - API请求耗时: 687ms
2025-06-10 08:03:51,039 - INFO - Response - Page 3
2025-06-10 08:03:51,039 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:03:51,555 - INFO - Request Parameters - Page 4:
2025-06-10 08:03:51,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:51,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800508, 1746547200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:52,211 - INFO - API请求耗时: 656ms
2025-06-10 08:03:52,211 - INFO - Response - Page 4
2025-06-10 08:03:52,211 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:03:52,711 - INFO - Request Parameters - Page 5:
2025-06-10 08:03:52,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:52,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800508, 1746547200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:53,133 - INFO - API请求耗时: 422ms
2025-06-10 08:03:53,133 - INFO - Response - Page 5
2025-06-10 08:03:53,133 - INFO - 第 5 页获取到 14 条记录
2025-06-10 08:03:53,133 - INFO - 查询完成，共获取到 414 条记录
2025-06-10 08:03:53,133 - INFO - 分段 14 查询成功，获取到 414 条记录
2025-06-10 08:03:54,133 - INFO - 查询分段 15: 2025-05-08 至 2025-05-09
2025-06-10 08:03:54,133 - INFO - 查询日期范围: 2025-05-08 至 2025-05-09，使用分页查询，每页 100 条记录
2025-06-10 08:03:54,133 - INFO - Request Parameters - Page 1:
2025-06-10 08:03:54,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:54,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600508, 1746720000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:54,820 - INFO - API请求耗时: 687ms
2025-06-10 08:03:54,820 - INFO - Response - Page 1
2025-06-10 08:03:54,820 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:03:55,336 - INFO - Request Parameters - Page 2:
2025-06-10 08:03:55,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:55,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600508, 1746720000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:55,992 - INFO - API请求耗时: 656ms
2025-06-10 08:03:55,992 - INFO - Response - Page 2
2025-06-10 08:03:55,992 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:03:56,508 - INFO - Request Parameters - Page 3:
2025-06-10 08:03:56,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:56,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600508, 1746720000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:57,242 - INFO - API请求耗时: 734ms
2025-06-10 08:03:57,242 - INFO - Response - Page 3
2025-06-10 08:03:57,242 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:03:57,742 - INFO - Request Parameters - Page 4:
2025-06-10 08:03:57,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:57,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600508, 1746720000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:58,414 - INFO - API请求耗时: 672ms
2025-06-10 08:03:58,414 - INFO - Response - Page 4
2025-06-10 08:03:58,414 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:03:58,914 - INFO - Request Parameters - Page 5:
2025-06-10 08:03:58,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:03:58,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600508, 1746720000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:03:59,398 - INFO - API请求耗时: 484ms
2025-06-10 08:03:59,398 - INFO - Response - Page 5
2025-06-10 08:03:59,398 - INFO - 第 5 页获取到 30 条记录
2025-06-10 08:03:59,398 - INFO - 查询完成，共获取到 430 条记录
2025-06-10 08:03:59,398 - INFO - 分段 15 查询成功，获取到 430 条记录
2025-06-10 08:04:00,398 - INFO - 查询分段 16: 2025-05-10 至 2025-05-11
2025-06-10 08:04:00,398 - INFO - 查询日期范围: 2025-05-10 至 2025-05-11，使用分页查询，每页 100 条记录
2025-06-10 08:04:00,398 - INFO - Request Parameters - Page 1:
2025-06-10 08:04:00,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:00,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400508, 1746892800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:01,133 - INFO - API请求耗时: 734ms
2025-06-10 08:04:01,133 - INFO - Response - Page 1
2025-06-10 08:04:01,133 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:04:01,633 - INFO - Request Parameters - Page 2:
2025-06-10 08:04:01,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:01,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400508, 1746892800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:02,305 - INFO - API请求耗时: 672ms
2025-06-10 08:04:02,305 - INFO - Response - Page 2
2025-06-10 08:04:02,305 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:04:02,805 - INFO - Request Parameters - Page 3:
2025-06-10 08:04:02,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:02,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400508, 1746892800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:03,601 - INFO - API请求耗时: 797ms
2025-06-10 08:04:03,601 - INFO - Response - Page 3
2025-06-10 08:04:03,601 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:04:04,101 - INFO - Request Parameters - Page 4:
2025-06-10 08:04:04,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:04,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400508, 1746892800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:04,930 - INFO - API请求耗时: 828ms
2025-06-10 08:04:04,930 - INFO - Response - Page 4
2025-06-10 08:04:04,945 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:04:05,445 - INFO - Request Parameters - Page 5:
2025-06-10 08:04:05,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:05,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400508, 1746892800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:06,023 - INFO - API请求耗时: 578ms
2025-06-10 08:04:06,023 - INFO - Response - Page 5
2025-06-10 08:04:06,023 - INFO - 第 5 页获取到 36 条记录
2025-06-10 08:04:06,023 - INFO - 查询完成，共获取到 436 条记录
2025-06-10 08:04:06,023 - INFO - 分段 16 查询成功，获取到 436 条记录
2025-06-10 08:04:07,023 - INFO - 查询分段 17: 2025-05-12 至 2025-05-13
2025-06-10 08:04:07,023 - INFO - 查询日期范围: 2025-05-12 至 2025-05-13，使用分页查询，每页 100 条记录
2025-06-10 08:04:07,023 - INFO - Request Parameters - Page 1:
2025-06-10 08:04:07,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:07,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200508, 1747065600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:07,773 - INFO - API请求耗时: 750ms
2025-06-10 08:04:07,773 - INFO - Response - Page 1
2025-06-10 08:04:07,773 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:04:08,289 - INFO - Request Parameters - Page 2:
2025-06-10 08:04:08,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:08,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200508, 1747065600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:09,055 - INFO - API请求耗时: 766ms
2025-06-10 08:04:09,055 - INFO - Response - Page 2
2025-06-10 08:04:09,055 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:04:09,570 - INFO - Request Parameters - Page 3:
2025-06-10 08:04:09,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:09,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200508, 1747065600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:10,305 - INFO - API请求耗时: 734ms
2025-06-10 08:04:10,305 - INFO - Response - Page 3
2025-06-10 08:04:10,305 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:04:10,820 - INFO - Request Parameters - Page 4:
2025-06-10 08:04:10,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:10,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200508, 1747065600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:11,570 - INFO - API请求耗时: 750ms
2025-06-10 08:04:11,570 - INFO - Response - Page 4
2025-06-10 08:04:11,570 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:04:12,070 - INFO - Request Parameters - Page 5:
2025-06-10 08:04:12,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:12,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200508, 1747065600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:12,617 - INFO - API请求耗时: 547ms
2025-06-10 08:04:12,617 - INFO - Response - Page 5
2025-06-10 08:04:12,617 - INFO - 第 5 页获取到 20 条记录
2025-06-10 08:04:12,617 - INFO - 查询完成，共获取到 420 条记录
2025-06-10 08:04:12,617 - INFO - 分段 17 查询成功，获取到 420 条记录
2025-06-10 08:04:13,633 - INFO - 查询分段 18: 2025-05-14 至 2025-05-15
2025-06-10 08:04:13,633 - INFO - 查询日期范围: 2025-05-14 至 2025-05-15，使用分页查询，每页 100 条记录
2025-06-10 08:04:13,633 - INFO - Request Parameters - Page 1:
2025-06-10 08:04:13,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:13,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000508, 1747238400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:14,429 - INFO - API请求耗时: 781ms
2025-06-10 08:04:14,429 - INFO - Response - Page 1
2025-06-10 08:04:14,429 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:04:14,945 - INFO - Request Parameters - Page 2:
2025-06-10 08:04:14,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:14,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000508, 1747238400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:15,773 - INFO - API请求耗时: 828ms
2025-06-10 08:04:15,773 - INFO - Response - Page 2
2025-06-10 08:04:15,773 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:04:16,289 - INFO - Request Parameters - Page 3:
2025-06-10 08:04:16,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:16,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000508, 1747238400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:17,023 - INFO - API请求耗时: 734ms
2025-06-10 08:04:17,023 - INFO - Response - Page 3
2025-06-10 08:04:17,023 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:04:17,523 - INFO - Request Parameters - Page 4:
2025-06-10 08:04:17,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:17,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000508, 1747238400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:18,320 - INFO - API请求耗时: 797ms
2025-06-10 08:04:18,320 - INFO - Response - Page 4
2025-06-10 08:04:18,320 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:04:18,836 - INFO - Request Parameters - Page 5:
2025-06-10 08:04:18,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:18,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000508, 1747238400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:19,398 - INFO - API请求耗时: 562ms
2025-06-10 08:04:19,398 - INFO - Response - Page 5
2025-06-10 08:04:19,398 - INFO - 第 5 页获取到 20 条记录
2025-06-10 08:04:19,398 - INFO - 查询完成，共获取到 420 条记录
2025-06-10 08:04:19,398 - INFO - 分段 18 查询成功，获取到 420 条记录
2025-06-10 08:04:20,414 - INFO - 查询分段 19: 2025-05-16 至 2025-05-17
2025-06-10 08:04:20,414 - INFO - 查询日期范围: 2025-05-16 至 2025-05-17，使用分页查询，每页 100 条记录
2025-06-10 08:04:20,414 - INFO - Request Parameters - Page 1:
2025-06-10 08:04:20,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:20,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800508, 1747411200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:21,195 - INFO - API请求耗时: 781ms
2025-06-10 08:04:21,195 - INFO - Response - Page 1
2025-06-10 08:04:21,195 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:04:21,695 - INFO - Request Parameters - Page 2:
2025-06-10 08:04:21,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:21,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800508, 1747411200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:22,429 - INFO - API请求耗时: 734ms
2025-06-10 08:04:22,445 - INFO - Response - Page 2
2025-06-10 08:04:22,445 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:04:22,961 - INFO - Request Parameters - Page 3:
2025-06-10 08:04:22,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:22,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800508, 1747411200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:23,695 - INFO - API请求耗时: 734ms
2025-06-10 08:04:23,695 - INFO - Response - Page 3
2025-06-10 08:04:23,695 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:04:24,211 - INFO - Request Parameters - Page 4:
2025-06-10 08:04:24,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:24,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800508, 1747411200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:25,023 - INFO - API请求耗时: 812ms
2025-06-10 08:04:25,023 - INFO - Response - Page 4
2025-06-10 08:04:25,023 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:04:25,539 - INFO - Request Parameters - Page 5:
2025-06-10 08:04:25,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:25,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800508, 1747411200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:26,117 - INFO - API请求耗时: 578ms
2025-06-10 08:04:26,117 - INFO - Response - Page 5
2025-06-10 08:04:26,117 - INFO - 第 5 页获取到 30 条记录
2025-06-10 08:04:26,117 - INFO - 查询完成，共获取到 430 条记录
2025-06-10 08:04:26,117 - INFO - 分段 19 查询成功，获取到 430 条记录
2025-06-10 08:04:27,133 - INFO - 查询分段 20: 2025-05-18 至 2025-05-19
2025-06-10 08:04:27,133 - INFO - 查询日期范围: 2025-05-18 至 2025-05-19，使用分页查询，每页 100 条记录
2025-06-10 08:04:27,133 - INFO - Request Parameters - Page 1:
2025-06-10 08:04:27,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:27,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600508, 1747584000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:27,789 - INFO - API请求耗时: 656ms
2025-06-10 08:04:27,789 - INFO - Response - Page 1
2025-06-10 08:04:27,789 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:04:28,304 - INFO - Request Parameters - Page 2:
2025-06-10 08:04:28,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:28,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600508, 1747584000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:29,086 - INFO - API请求耗时: 781ms
2025-06-10 08:04:29,086 - INFO - Response - Page 2
2025-06-10 08:04:29,086 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:04:29,601 - INFO - Request Parameters - Page 3:
2025-06-10 08:04:29,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:29,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600508, 1747584000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:30,336 - INFO - API请求耗时: 734ms
2025-06-10 08:04:30,336 - INFO - Response - Page 3
2025-06-10 08:04:30,336 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:04:30,836 - INFO - Request Parameters - Page 4:
2025-06-10 08:04:30,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:30,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600508, 1747584000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:31,523 - INFO - API请求耗时: 687ms
2025-06-10 08:04:31,523 - INFO - Response - Page 4
2025-06-10 08:04:31,523 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:04:32,039 - INFO - Request Parameters - Page 5:
2025-06-10 08:04:32,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:32,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600508, 1747584000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:32,648 - INFO - API请求耗时: 609ms
2025-06-10 08:04:32,648 - INFO - Response - Page 5
2025-06-10 08:04:32,648 - INFO - 第 5 页获取到 20 条记录
2025-06-10 08:04:32,648 - INFO - 查询完成，共获取到 420 条记录
2025-06-10 08:04:32,648 - INFO - 分段 20 查询成功，获取到 420 条记录
2025-06-10 08:04:33,648 - INFO - 查询分段 21: 2025-05-20 至 2025-05-21
2025-06-10 08:04:33,648 - INFO - 查询日期范围: 2025-05-20 至 2025-05-21，使用分页查询，每页 100 条记录
2025-06-10 08:04:33,648 - INFO - Request Parameters - Page 1:
2025-06-10 08:04:33,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:33,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400508, 1747756800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:34,414 - INFO - API请求耗时: 766ms
2025-06-10 08:04:34,414 - INFO - Response - Page 1
2025-06-10 08:04:34,414 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:04:34,914 - INFO - Request Parameters - Page 2:
2025-06-10 08:04:34,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:34,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400508, 1747756800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:35,679 - INFO - API请求耗时: 766ms
2025-06-10 08:04:35,679 - INFO - Response - Page 2
2025-06-10 08:04:35,679 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:04:36,179 - INFO - Request Parameters - Page 3:
2025-06-10 08:04:36,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:36,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400508, 1747756800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:37,007 - INFO - API请求耗时: 828ms
2025-06-10 08:04:37,007 - INFO - Response - Page 3
2025-06-10 08:04:37,007 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:04:37,507 - INFO - Request Parameters - Page 4:
2025-06-10 08:04:37,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:37,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400508, 1747756800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:38,226 - INFO - API请求耗时: 719ms
2025-06-10 08:04:38,226 - INFO - Response - Page 4
2025-06-10 08:04:38,242 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:04:38,757 - INFO - Request Parameters - Page 5:
2025-06-10 08:04:38,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:38,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400508, 1747756800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:39,242 - INFO - API请求耗时: 484ms
2025-06-10 08:04:39,242 - INFO - Response - Page 5
2025-06-10 08:04:39,242 - INFO - 第 5 页获取到 16 条记录
2025-06-10 08:04:39,242 - INFO - 查询完成，共获取到 416 条记录
2025-06-10 08:04:39,242 - INFO - 分段 21 查询成功，获取到 416 条记录
2025-06-10 08:04:40,257 - INFO - 查询分段 22: 2025-05-22 至 2025-05-23
2025-06-10 08:04:40,257 - INFO - 查询日期范围: 2025-05-22 至 2025-05-23，使用分页查询，每页 100 条记录
2025-06-10 08:04:40,257 - INFO - Request Parameters - Page 1:
2025-06-10 08:04:40,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:40,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200508, 1747929600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:40,945 - INFO - API请求耗时: 688ms
2025-06-10 08:04:40,945 - INFO - Response - Page 1
2025-06-10 08:04:40,945 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:04:41,445 - INFO - Request Parameters - Page 2:
2025-06-10 08:04:41,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:41,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200508, 1747929600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:42,211 - INFO - API请求耗时: 766ms
2025-06-10 08:04:42,211 - INFO - Response - Page 2
2025-06-10 08:04:42,211 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:04:42,711 - INFO - Request Parameters - Page 3:
2025-06-10 08:04:42,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:42,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200508, 1747929600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:43,445 - INFO - API请求耗时: 734ms
2025-06-10 08:04:43,445 - INFO - Response - Page 3
2025-06-10 08:04:43,445 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:04:43,945 - INFO - Request Parameters - Page 4:
2025-06-10 08:04:43,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:43,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200508, 1747929600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:44,695 - INFO - API请求耗时: 750ms
2025-06-10 08:04:44,711 - INFO - Response - Page 4
2025-06-10 08:04:44,711 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:04:45,226 - INFO - Request Parameters - Page 5:
2025-06-10 08:04:45,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:45,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200508, 1747929600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:45,664 - INFO - API请求耗时: 437ms
2025-06-10 08:04:45,664 - INFO - Response - Page 5
2025-06-10 08:04:45,664 - INFO - 第 5 页获取到 16 条记录
2025-06-10 08:04:45,664 - INFO - 查询完成，共获取到 416 条记录
2025-06-10 08:04:45,664 - INFO - 分段 22 查询成功，获取到 416 条记录
2025-06-10 08:04:46,679 - INFO - 查询分段 23: 2025-05-24 至 2025-05-25
2025-06-10 08:04:46,679 - INFO - 查询日期范围: 2025-05-24 至 2025-05-25，使用分页查询，每页 100 条记录
2025-06-10 08:04:46,679 - INFO - Request Parameters - Page 1:
2025-06-10 08:04:46,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:46,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000508, 1748102400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:47,539 - INFO - API请求耗时: 859ms
2025-06-10 08:04:47,539 - INFO - Response - Page 1
2025-06-10 08:04:47,539 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:04:48,054 - INFO - Request Parameters - Page 2:
2025-06-10 08:04:48,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:48,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000508, 1748102400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:48,742 - INFO - API请求耗时: 687ms
2025-06-10 08:04:48,742 - INFO - Response - Page 2
2025-06-10 08:04:48,742 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:04:49,257 - INFO - Request Parameters - Page 3:
2025-06-10 08:04:49,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:49,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000508, 1748102400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:49,898 - INFO - API请求耗时: 641ms
2025-06-10 08:04:49,898 - INFO - Response - Page 3
2025-06-10 08:04:49,898 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:04:50,414 - INFO - Request Parameters - Page 4:
2025-06-10 08:04:50,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:50,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000508, 1748102400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:51,132 - INFO - API请求耗时: 719ms
2025-06-10 08:04:51,132 - INFO - Response - Page 4
2025-06-10 08:04:51,132 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:04:51,632 - INFO - Request Parameters - Page 5:
2025-06-10 08:04:51,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:51,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000508, 1748102400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:52,164 - INFO - API请求耗时: 531ms
2025-06-10 08:04:52,164 - INFO - Response - Page 5
2025-06-10 08:04:52,164 - INFO - 第 5 页获取到 18 条记录
2025-06-10 08:04:52,164 - INFO - 查询完成，共获取到 418 条记录
2025-06-10 08:04:52,164 - INFO - 分段 23 查询成功，获取到 418 条记录
2025-06-10 08:04:53,164 - INFO - 查询分段 24: 2025-05-26 至 2025-05-27
2025-06-10 08:04:53,164 - INFO - 查询日期范围: 2025-05-26 至 2025-05-27，使用分页查询，每页 100 条记录
2025-06-10 08:04:53,164 - INFO - Request Parameters - Page 1:
2025-06-10 08:04:53,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:53,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800508, 1748275200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:54,039 - INFO - API请求耗时: 875ms
2025-06-10 08:04:54,039 - INFO - Response - Page 1
2025-06-10 08:04:54,039 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:04:54,554 - INFO - Request Parameters - Page 2:
2025-06-10 08:04:54,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:54,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800508, 1748275200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:55,195 - INFO - API请求耗时: 641ms
2025-06-10 08:04:55,195 - INFO - Response - Page 2
2025-06-10 08:04:55,195 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:04:55,710 - INFO - Request Parameters - Page 3:
2025-06-10 08:04:55,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:55,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800508, 1748275200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:56,414 - INFO - API请求耗时: 703ms
2025-06-10 08:04:56,414 - INFO - Response - Page 3
2025-06-10 08:04:56,414 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:04:56,914 - INFO - Request Parameters - Page 4:
2025-06-10 08:04:56,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:56,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800508, 1748275200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:57,648 - INFO - API请求耗时: 734ms
2025-06-10 08:04:57,648 - INFO - Response - Page 4
2025-06-10 08:04:57,648 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:04:58,148 - INFO - Request Parameters - Page 5:
2025-06-10 08:04:58,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:58,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800508, 1748275200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:04:58,632 - INFO - API请求耗时: 484ms
2025-06-10 08:04:58,632 - INFO - Response - Page 5
2025-06-10 08:04:58,632 - INFO - 第 5 页获取到 10 条记录
2025-06-10 08:04:58,632 - INFO - 查询完成，共获取到 410 条记录
2025-06-10 08:04:58,632 - INFO - 分段 24 查询成功，获取到 410 条记录
2025-06-10 08:04:59,648 - INFO - 查询分段 25: 2025-05-28 至 2025-05-29
2025-06-10 08:04:59,648 - INFO - 查询日期范围: 2025-05-28 至 2025-05-29，使用分页查询，每页 100 条记录
2025-06-10 08:04:59,648 - INFO - Request Parameters - Page 1:
2025-06-10 08:04:59,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:04:59,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600508, 1748448000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:00,304 - INFO - API请求耗时: 656ms
2025-06-10 08:05:00,304 - INFO - Response - Page 1
2025-06-10 08:05:00,304 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:05:00,820 - INFO - Request Parameters - Page 2:
2025-06-10 08:05:00,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:00,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600508, 1748448000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:01,601 - INFO - API请求耗时: 781ms
2025-06-10 08:05:01,601 - INFO - Response - Page 2
2025-06-10 08:05:01,601 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:05:02,101 - INFO - Request Parameters - Page 3:
2025-06-10 08:05:02,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:02,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600508, 1748448000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:02,804 - INFO - API请求耗时: 703ms
2025-06-10 08:05:02,804 - INFO - Response - Page 3
2025-06-10 08:05:02,804 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:05:03,320 - INFO - Request Parameters - Page 4:
2025-06-10 08:05:03,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:03,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600508, 1748448000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:04,117 - INFO - API请求耗时: 797ms
2025-06-10 08:05:04,117 - INFO - Response - Page 4
2025-06-10 08:05:04,117 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:05:04,632 - INFO - Request Parameters - Page 5:
2025-06-10 08:05:04,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:04,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600508, 1748448000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:05,117 - INFO - API请求耗时: 484ms
2025-06-10 08:05:05,117 - INFO - Response - Page 5
2025-06-10 08:05:05,132 - INFO - 第 5 页获取到 12 条记录
2025-06-10 08:05:05,132 - INFO - 查询完成，共获取到 412 条记录
2025-06-10 08:05:05,132 - INFO - 分段 25 查询成功，获取到 412 条记录
2025-06-10 08:05:06,132 - INFO - 查询分段 26: 2025-05-30 至 2025-05-31
2025-06-10 08:05:06,132 - INFO - 查询日期范围: 2025-05-30 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-10 08:05:06,132 - INFO - Request Parameters - Page 1:
2025-06-10 08:05:06,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:06,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400508, 1748620800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:06,898 - INFO - API请求耗时: 766ms
2025-06-10 08:05:06,898 - INFO - Response - Page 1
2025-06-10 08:05:06,898 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:05:07,414 - INFO - Request Parameters - Page 2:
2025-06-10 08:05:07,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:07,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400508, 1748620800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:08,179 - INFO - API请求耗时: 766ms
2025-06-10 08:05:08,179 - INFO - Response - Page 2
2025-06-10 08:05:08,179 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:05:08,679 - INFO - Request Parameters - Page 3:
2025-06-10 08:05:08,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:08,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400508, 1748620800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:09,429 - INFO - API请求耗时: 750ms
2025-06-10 08:05:09,429 - INFO - Response - Page 3
2025-06-10 08:05:09,429 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:05:09,929 - INFO - Request Parameters - Page 4:
2025-06-10 08:05:09,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:09,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400508, 1748620800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:10,648 - INFO - API请求耗时: 719ms
2025-06-10 08:05:10,648 - INFO - Response - Page 4
2025-06-10 08:05:10,648 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:05:11,148 - INFO - Request Parameters - Page 5:
2025-06-10 08:05:11,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:11,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400508, 1748620800508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:11,648 - INFO - API请求耗时: 500ms
2025-06-10 08:05:11,648 - INFO - Response - Page 5
2025-06-10 08:05:11,648 - INFO - 第 5 页获取到 18 条记录
2025-06-10 08:05:11,648 - INFO - 查询完成，共获取到 418 条记录
2025-06-10 08:05:11,648 - INFO - 分段 26 查询成功，获取到 418 条记录
2025-06-10 08:05:12,663 - INFO - 查询分段 27: 2025-06-01 至 2025-06-02
2025-06-10 08:05:12,663 - INFO - 查询日期范围: 2025-06-01 至 2025-06-02，使用分页查询，每页 100 条记录
2025-06-10 08:05:12,663 - INFO - Request Parameters - Page 1:
2025-06-10 08:05:12,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:12,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200508, 1748793600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:13,351 - INFO - API请求耗时: 687ms
2025-06-10 08:05:13,351 - INFO - Response - Page 1
2025-06-10 08:05:13,351 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:05:13,867 - INFO - Request Parameters - Page 2:
2025-06-10 08:05:13,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:13,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200508, 1748793600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:14,601 - INFO - API请求耗时: 734ms
2025-06-10 08:05:14,601 - INFO - Response - Page 2
2025-06-10 08:05:14,601 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:05:15,101 - INFO - Request Parameters - Page 3:
2025-06-10 08:05:15,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:15,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200508, 1748793600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:15,820 - INFO - API请求耗时: 719ms
2025-06-10 08:05:15,820 - INFO - Response - Page 3
2025-06-10 08:05:15,820 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:05:16,320 - INFO - Request Parameters - Page 4:
2025-06-10 08:05:16,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:16,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200508, 1748793600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:16,992 - INFO - API请求耗时: 672ms
2025-06-10 08:05:16,992 - INFO - Response - Page 4
2025-06-10 08:05:16,992 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:05:17,507 - INFO - Request Parameters - Page 5:
2025-06-10 08:05:17,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:17,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200508, 1748793600508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:17,913 - INFO - API请求耗时: 406ms
2025-06-10 08:05:17,913 - INFO - Response - Page 5
2025-06-10 08:05:17,929 - INFO - 第 5 页获取到 2 条记录
2025-06-10 08:05:17,929 - INFO - 查询完成，共获取到 402 条记录
2025-06-10 08:05:17,929 - INFO - 分段 27 查询成功，获取到 402 条记录
2025-06-10 08:05:18,929 - INFO - 查询分段 28: 2025-06-03 至 2025-06-04
2025-06-10 08:05:18,929 - INFO - 查询日期范围: 2025-06-03 至 2025-06-04，使用分页查询，每页 100 条记录
2025-06-10 08:05:18,929 - INFO - Request Parameters - Page 1:
2025-06-10 08:05:18,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:18,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000508, 1748966400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:19,554 - INFO - API请求耗时: 625ms
2025-06-10 08:05:19,554 - INFO - Response - Page 1
2025-06-10 08:05:19,554 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:05:20,070 - INFO - Request Parameters - Page 2:
2025-06-10 08:05:20,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:20,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000508, 1748966400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:20,695 - INFO - API请求耗时: 625ms
2025-06-10 08:05:20,695 - INFO - Response - Page 2
2025-06-10 08:05:20,695 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:05:21,195 - INFO - Request Parameters - Page 3:
2025-06-10 08:05:21,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:21,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000508, 1748966400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:21,898 - INFO - API请求耗时: 703ms
2025-06-10 08:05:21,898 - INFO - Response - Page 3
2025-06-10 08:05:21,898 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:05:22,398 - INFO - Request Parameters - Page 4:
2025-06-10 08:05:22,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:22,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000508, 1748966400508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:23,179 - INFO - API请求耗时: 781ms
2025-06-10 08:05:23,179 - INFO - Response - Page 4
2025-06-10 08:05:23,179 - INFO - 第 4 页获取到 96 条记录
2025-06-10 08:05:23,179 - INFO - 查询完成，共获取到 396 条记录
2025-06-10 08:05:23,179 - INFO - 分段 28 查询成功，获取到 396 条记录
2025-06-10 08:05:24,195 - INFO - 查询分段 29: 2025-06-05 至 2025-06-06
2025-06-10 08:05:24,195 - INFO - 查询日期范围: 2025-06-05 至 2025-06-06，使用分页查询，每页 100 条记录
2025-06-10 08:05:24,195 - INFO - Request Parameters - Page 1:
2025-06-10 08:05:24,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:24,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800508, 1749139200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:24,960 - INFO - API请求耗时: 766ms
2025-06-10 08:05:24,960 - INFO - Response - Page 1
2025-06-10 08:05:24,960 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:05:25,476 - INFO - Request Parameters - Page 2:
2025-06-10 08:05:25,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:25,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800508, 1749139200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:26,117 - INFO - API请求耗时: 641ms
2025-06-10 08:05:26,117 - INFO - Response - Page 2
2025-06-10 08:05:26,117 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:05:26,617 - INFO - Request Parameters - Page 3:
2025-06-10 08:05:26,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:26,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800508, 1749139200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:27,429 - INFO - API请求耗时: 812ms
2025-06-10 08:05:27,429 - INFO - Response - Page 3
2025-06-10 08:05:27,429 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:05:27,929 - INFO - Request Parameters - Page 4:
2025-06-10 08:05:27,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:27,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800508, 1749139200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:28,695 - INFO - API请求耗时: 766ms
2025-06-10 08:05:28,695 - INFO - Response - Page 4
2025-06-10 08:05:28,695 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:05:29,210 - INFO - Request Parameters - Page 5:
2025-06-10 08:05:29,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:29,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800508, 1749139200508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:29,648 - INFO - API请求耗时: 438ms
2025-06-10 08:05:29,648 - INFO - Response - Page 5
2025-06-10 08:05:29,648 - INFO - 第 5 页获取到 8 条记录
2025-06-10 08:05:29,648 - INFO - 查询完成，共获取到 408 条记录
2025-06-10 08:05:29,648 - INFO - 分段 29 查询成功，获取到 408 条记录
2025-06-10 08:05:30,663 - INFO - 查询分段 30: 2025-06-07 至 2025-06-08
2025-06-10 08:05:30,663 - INFO - 查询日期范围: 2025-06-07 至 2025-06-08，使用分页查询，每页 100 条记录
2025-06-10 08:05:30,663 - INFO - Request Parameters - Page 1:
2025-06-10 08:05:30,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:30,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600508, 1749312000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:31,445 - INFO - API请求耗时: 781ms
2025-06-10 08:05:31,445 - INFO - Response - Page 1
2025-06-10 08:05:31,445 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:05:31,960 - INFO - Request Parameters - Page 2:
2025-06-10 08:05:31,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:31,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600508, 1749312000508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:32,632 - INFO - API请求耗时: 672ms
2025-06-10 08:05:32,632 - INFO - Response - Page 2
2025-06-10 08:05:32,632 - INFO - 第 2 页获取到 98 条记录
2025-06-10 08:05:32,632 - INFO - 查询完成，共获取到 198 条记录
2025-06-10 08:05:32,632 - INFO - 分段 30 查询成功，获取到 198 条记录
2025-06-10 08:05:33,648 - INFO - 查询分段 31: 2025-06-09 至 2025-06-09
2025-06-10 08:05:33,648 - INFO - 查询日期范围: 2025-06-09 至 2025-06-09，使用分页查询，每页 100 条记录
2025-06-10 08:05:33,648 - INFO - Request Parameters - Page 1:
2025-06-10 08:05:33,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:05:33,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749398400508, 1749484799508], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:05:33,945 - INFO - API请求耗时: 297ms
2025-06-10 08:05:33,945 - INFO - Response - Page 1
2025-06-10 08:05:33,960 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-10 08:05:33,960 - INFO - 查询完成，共获取到 0 条记录
2025-06-10 08:05:33,960 - WARNING - 分段 31 查询返回空数据
2025-06-10 08:05:34,976 - INFO - 宜搭每日表单数据查询完成，共 31 个分段，成功获取 12196 条记录，失败 0 次
2025-06-10 08:05:34,976 - INFO - 成功获取宜搭日销售表单数据，共 12196 条记录
2025-06-10 08:05:34,976 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-10 08:05:34,976 - INFO - 开始对比和同步日销售数据...
2025-06-10 08:05:35,304 - INFO - 成功创建宜搭日销售数据索引，共 6297 条记录
2025-06-10 08:05:35,304 - INFO - 开始处理数衍数据，共 12830 条记录
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: F56E8E23D2584556A30D1378611DF4AE
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: F56E8E23D2584556A30D1378611DF4AE
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: D384CB5088914FB296DE32297895B8D6
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: BF554F536BF14762AEB7110E7BD583B7
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: BF554F536BF14762AEB7110E7BD583B7
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: AC07B70DB49845A8A52846E099EBC515
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: AC07B70DB49845A8A52846E099EBC515
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: AA76628FACEC4C13BD44C8280B45416D
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: AA76628FACEC4C13BD44C8280B45416D
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: A93C60005A8F41B092F6C5A8C21577CB
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: A93C60005A8F41B092F6C5A8C21577CB
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 78655ECA4A32471AB7842F8DE2018120
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 78655ECA4A32471AB7842F8DE2018120
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 6B7571A27AF84C73B4FC04CCBDB83D9B
2025-06-10 08:05:35,304 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 6B7571A27AF84C73B4FC04CCBDB83D9B
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 6692283A183D432BAE322E1032539CE8
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 6692283A183D432BAE322E1032539CE8
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 3BB9A16AE8544997965802FAA3B83381
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 3BB9A16AE8544997965802FAA3B83381
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1INM7RG8OF1EDQ0UI43B5T3TBA0016KV
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1IBBJ3RNCSAVNO7A70STAEF09Q001IPV
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1IBBJ3RNCSAVNO7A70STAEF09Q001IPV
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1I6E0VAU3IFJEQ22MH147FMU0M0013E8
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1I6E0VAU3IFJEQ22MH147FMU0M0013E8
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HVORJ88U7D2IL1AIB692RTFU8001185
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HVORJ88U7D2IL1AIB692RTFU8001185
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HVOQTVVFR41OA22BBK6R0G53G001SV2
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HVOQTVVFR41OA22BBK6R0G53G001SV2
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HV6P9SGUVGG9S36QDA69ST70J0015SA
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HV6P9SGUVGG9S36QDA69ST70J0015SA
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HOE1A3UTAESD606LODAUCEHAF001M2A
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HOE1A3UTAESD606LODAUCEHAF001M2A
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HINIR4GO8E5NM5U25UDHUFEGO001L3K
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HINIR4GO8E5NM5U25UDHUFEGO001L3K
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HI85875BG16NA6U1G19QP117C001UB5
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HI85875BG16NA6U1G19QP117C001UB5
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HHDS9O4DUBH2L6U1G19QP11V40018CI
2025-06-10 08:05:35,320 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HHDS9O4DUBH2L6U1G19QP11V40018CI
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HHDRIR0P38R666U1G19QP11V00018CE
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HHDRIR0P38R666U1G19QP11V00018CE
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HB8D7KAC5K1G3723F7K257LIN001TSQ
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HB8D7KAC5K1G3723F7K257LIN001TSQ
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9UCRQKEOIF52ASKKUBQUNH0018FA
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9UCRQKEOIF52ASKKUBQUNH0018FA
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9S64J8E8R652ASKKUBQUMU0018EN
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9S64J8E8R652ASKKUBQUMU0018EN
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9PGR3703D752ASKKUBQUM50018DU
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9PGR3703D752ASKKUBQUM50018DU
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE2TORA7KI7Q2OV4FVC7FC0014BT
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE2TORA7KI7Q2OV4FVC7FC0014BT
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE29HIJ7QK7Q2OV4FVC7F40014BL
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE29HIJ7QK7Q2OV4FVC7F40014BL
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE1H27HPQN7Q2OV4FVC7EO0014B9
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE1H27HPQN7Q2OV4FVC7EO0014B9
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE0PKSKM647Q2OV4FVC7EC0014AT
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE0PKSKM647Q2OV4FVC7EC0014AT
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDVG061A0H7Q2OV4FVC7DO0014A9
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDVG061A0H7Q2OV4FVC7DO0014A9
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDV84M589R7Q2OV4FVC7DK0014A5
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDV84M589R7Q2OV4FVC7DK0014A5
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDV0D9P6J27Q2OV4FVC7DG0014A1
2025-06-10 08:05:35,335 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDV0D9P6J27Q2OV4FVC7DG0014A1
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDUNDNMH3D7Q2OV4FVC7DC00149T
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDUNDNMH3D7Q2OV4FVC7DC00149T
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDTG15Q4SH7Q2OV4FVC7CO001499
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDTG15Q4SH7Q2OV4FVC7CO001499
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDT8J32K6E7Q2OV4FVC7CK001495
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDT8J32K6E7Q2OV4FVC7CK001495
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSTUTUPTG7Q2OV4FVC7CG001491
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSTUTUPTG7Q2OV4FVC7CG001491
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSM3EAQJB7Q2OV4FVC7CC00148T
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSM3EAQJB7Q2OV4FVC7CC00148T
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSD911K3E7Q2OV4FVC7C800148P
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSD911K3E7Q2OV4FVC7C800148P
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDS537TI7U7Q2OV4FVC7C400148L
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDS537TI7U7Q2OV4FVC7C400148L
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDRLHCKFK97Q2OV4FVC7BS00148D
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDRLHCKFK97Q2OV4FVC7BS00148D
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDQDVL1EG67Q2OV4FVC7B800147P
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDQDVL1EG67Q2OV4FVC7B800147P
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDOE3O1J9R7Q2OV4FVC7A800146P
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDOE3O1J9R7Q2OV4FVC7A800146P
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDN6L4LMS87Q2OV4FVC79K001465
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDN6L4LMS87Q2OV4FVC79K001465
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDMMR23CHP7Q2OV4FVC79C00145T
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDMMR23CHP7Q2OV4FVC79C00145T
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDKDOANT3H7Q2OV4FVC78800144P
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDKDOANT3H7Q2OV4FVC78800144P
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDJ5HVP5F47Q2OV4FVC77O001449
2025-06-10 08:05:35,351 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDJ5HVP5F47Q2OV4FVC77O001449
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTE3QTM66G7Q2OVBN4IS8M001D4O
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTE3QTM66G7Q2OVBN4IS8M001D4O
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTD2B070E67Q2OVBN4IS86001D48
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTD2B070E67Q2OVBN4IS86001D48
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTCIR7D5JD7Q2OVBN4IS7U001D40
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTCIR7D5JD7Q2OVBN4IS7U001D40
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTAKS3N4EI7Q2OVBN4IS76001D38
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTAKS3N4EI7Q2OVBN4IS76001D38
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTA575PTKJ7Q2OVBN4IS72001D34
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTA575PTKJ7Q2OVBN4IS72001D34
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT72H2L6227Q2OVBN4IS62001D24
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT72H2L6227Q2OVBN4IS62001D24
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6QJG5GQO7Q2OVBN4IS5U001D20
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6QJG5GQO7Q2OVBN4IS5U001D20
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S
2025-06-10 08:05:35,366 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINSCOR6MVCC7Q2OV392411100148T
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINSCOR6MVCC7Q2OV392411100148T
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINSC23H1J3M7Q2OV392410L00148H
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINSC23H1J3M7Q2OV392410L00148H
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINPMIJNMLUJ7Q2OV392410G00148C
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINPMIJNMLUJ7Q2OV392410G00148C
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: A48FB8F8F66644F59454F3E73DFCEB92
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: A48FB8F8F66644F59454F3E73DFCEB92
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 7987833E6DE549FCBAC0AAF7A1D27E61
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 7987833E6DE549FCBAC0AAF7A1D27E61
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 6FEB527E4B354363BD1420A3FF0FB3E3
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 6FEB527E4B354363BD1420A3FF0FB3E3
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 6D1F9FC749FA44C6B70CA818C3E7FB77
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 6D1F9FC749FA44C6B70CA818C3E7FB77
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 48AFA71F437742278E0CD956382F1110
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 48AFA71F437742278E0CD956382F1110
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1I16HSQFKC90AM6QNN0HOT12RK0013M7
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1I16HSQFKC90AM6QNN0HOT12RK0013M7
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1I16GTCIOI81KU0UR9LEHSI4JM001PFU
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1I16GTCIOI81KU0UR9LEHSI4JM001PFU
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGETHMN6B8P42F6DB81RHC2001P8A
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGETHMN6B8P42F6DB81RHC2001P8A
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERRPAJGP042F6DB81RHB6001P7E
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERRPAJGP042F6DB81RHB6001P7E
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERL0RJ5BM42F6DB81RHB2001P7A
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERL0RJ5BM42F6DB81RHB2001P7A
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERF6AAOTL42F6DB81RHAU001P76
2025-06-10 08:05:35,382 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERF6AAOTL42F6DB81RHAU001P76
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEQIR94JOQ42F6DB81RHAE001P6M
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEQIR94JOQ42F6DB81RHAE001P6M
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEQ5682UCT42F6DB81RHA6001P6E
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEQ5682UCT42F6DB81RHA6001P6E
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEPVAL5TUK42F6DB81RHA2001P6A
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEPVAL5TUK42F6DB81RHA2001P6A
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEOTOKMGDD42F6DB81RH9E001P5M
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEOTOKMGDD42F6DB81RH9E001P5M
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEO8LGKTTH42F6DB81RH8V001P57
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEO8LGKTTH42F6DB81RH8V001P57
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEO2283NPC42F6DB81RH8Q001P52
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEO2283NPC42F6DB81RH8Q001P52
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEMMCI75GD42F6DB81RH81001P49
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEMMCI75GD42F6DB81RH81001P49
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEMFCSIPU442F6DB81RH7T001P45
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEMFCSIPU442F6DB81RH7T001P45
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEM2RU37BD42F6DB81RH7L001P3T
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEKSIE6DGB42F6DB81RH77001P3F
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEKSIE6DGB42F6DB81RH77001P3F
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEKLLRRBCQ42F6DB81RH73001P3B
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEKLLRRBCQ42F6DB81RH73001P3B
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEJQAOTB2542F6DB81RH6O001P30
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEJQAOTB2542F6DB81RH6O001P30
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEJ92AGQAT42F6DB81RH6G001P2O
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEJ92AGQAT42F6DB81RH6G001P2O
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 101F34500A0D43DF833463DEFB95F423
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 101F34500A0D43DF833463DEFB95F423
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: EC9758A692DF47FBA8F7C97344079C9E
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: EC9758A692DF47FBA8F7C97344079C9E
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: E214E9E9A4534AE1943BBACB09056E2E
2025-06-10 08:05:35,398 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: E214E9E9A4534AE1943BBACB09056E2E
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: B5A5FB25D4B04323BCABB528AF5E427E
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: B5A5FB25D4B04323BCABB528AF5E427E
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: B358872E0DBE420182AF77D4C47644F6
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: B358872E0DBE420182AF77D4C47644F6
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 6245DA9460784DDC85246900484DAA79
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 6245DA9460784DDC85246900484DAA79
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1IFRR231GUB4QN7QBECDAL3H28001J69
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1IFRR231GUB4QN7QBECDAL3H28001J69
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1IFRPJDJBMAPL77QBECDAL3H1H001J5I
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1IFRPJDJBMAPL77QBECDAL3H1H001J5I
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HO90F90E71NK12I1UUTD5AE7C001O7G
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HO90F90E71NK12I1UUTD5AE7C001O7G
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HLFG4AKMT96P82UAQ9ONTBKHO001HHS
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HLFG4AKMT96P82UAQ9ONTBKHO001HHS
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEC0U8246I0I86N3H2U10A001F9G
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEC0U8246I0I86N3H2U10A001F9G
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEBP5Q03GB0I86N3H2U106001F9C
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEBP5Q03GB0I86N3H2U106001F9C
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEBHLLHVNF0I86N3H2U102001F98
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEBHLLHVNF0I86N3H2U102001F98
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEB9O4F53S0I86N3H2U1VT001F93
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEB9O4F53S0I86N3H2U1VT001F93
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEAIN5DMKK0I86N3H2U1VH001F8N
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEAIN5DMKK0I86N3H2U1VH001F8N
2025-06-10 08:05:35,413 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEAB2RFI0N0I86N3H2U1VD001F8J
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEAB2RFI0N0I86N3H2U1VD001F8J
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEA4041D6F0I86N3H2U1V9001F8F
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEA4041D6F0I86N3H2U1V9001F8F
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE85EF5FB90I86N3H2U1U9001F7F
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE85EF5FB90I86N3H2U1U9001F7F
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE7UKVCV6D0I86N3H2U1U5001F7B
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE7UKVCV6D0I86N3H2U1U5001F7B
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE4GES1U770I86N3H2U1SF001F5L
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE4GES1U770I86N3H2U1SF001F5L
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE48KDQ6AS0I86N3H2U1SB001F5H
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE48KDQ6AS0I86N3H2U1SB001F5H
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE3PJ2TE9A0I86N3H2U1S3001F59
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE3PJ2TE9A0I86N3H2U1S3001F59
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE39GOJVP40I86N3H2U1RR001F51
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE39GOJVP40I86N3H2U1RR001F51
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE2CVHLBFV0I86N3H2U1RH001F4N
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE2CVHLBFV0I86N3H2U1RH001F4N
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE25DAIM3B0I86N3H2U1RD001F4J
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE25DAIM3B0I86N3H2U1RD001F4J
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE1T4E94FK0I86N3H2U1R9001F4F
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE1T4E94FK0I86N3H2U1R9001F4F
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE1EFF1UHG0I86N3H2U1R1001F47
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE0A2N9KG60I86N3H2U1QB001F3H
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE0A2N9KG60I86N3H2U1QB001F3H
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVQHT7NSH0I86N3H2U1Q2001F38
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVQHT7NSH0I86N3H2U1Q2001F38
2025-06-10 08:05:35,429 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVIE6UN1G0I86N3H2U1PU001F34
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVIE6UN1G0I86N3H2U1PU001F34
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVBILA3OJ0I86N3H2U1PQ001F30
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVBILA3OJ0I86N3H2U1PQ001F30
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDUSSKMEM60I86N3H2U1PI001F2O
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDUSSKMEM60I86N3H2U1PI001F2O
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDQR0OPQVI0I86N3H2U1NE001F0K
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDPJQDDVRC0I86N3H2U1MO001EVU
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDPJQDDVRC0I86N3H2U1MO001EVU
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDP34FLR400I86N3H2U1MG001EVM
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDP34FLR400I86N3H2U1MG001EVM
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDOR4RUGJG0I86N3H2U1MC001EVI
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDOR4RUGJG0I86N3H2U1MC001EVI
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJP082LC00I86N3H2U1JM001ESS
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJP082LC00I86N3H2U1JM001ESS
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJGUIA2QR0I86N3H2U1JH001ESN
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJGUIA2QR0I86N3H2U1JH001ESN
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDG7SC9VNO0I86N3H2U1HP001EQV
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDG7SC9VNO0I86N3H2U1HP001EQV
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDF8HFHI690I86N3H2U1H9001EQF
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDF8HFHI690I86N3H2U1H9001EQF
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDF11AKIN30I86N3H2U1H5001EQB
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDEQ2M9E710I86N3H2U1H1001EQ7
2025-06-10 08:05:35,445 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDEQ2M9E710I86N3H2U1H1001EQ7
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDEISQT8KE0I86N3H2U1GT001EQ3
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDEISQT8KE0I86N3H2U1GT001EQ3
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDCE3748SO0I86N3H2U1FP001EOV
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDCE3748SO0I86N3H2U1FP001EOV
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDAAQ1NK8J0I86N3H2U1EL001ENR
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9H11376450I86N3H2U19G001EIM
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9GHTM38HU0I86N3H2U198001EIE
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9G31FV3GL0I86N3H2U190001EI6
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9G31FV3GL0I86N3H2U190001EI6
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9EG92S1SB0I86N3H2U188001EHE
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9EG92S1SB0I86N3H2U188001EHE
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9D9OBM41R0I86N3H2U17K001EGQ
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCE13J2R9CI0I86N3H2U13D001ECJ
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCE13J2R9CI0I86N3H2U13D001ECJ
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCE0JS3BF7R0I86N3H2U135001ECB
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCE0JS3BF7R0I86N3H2U135001ECB
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDVSS4V3460I86N3H2U12P001EBV
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDVSS4V3460I86N3H2U12P001EBV
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDUDM4PLNS0I86N3H2U121001EB7
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDUDM4PLNS0I86N3H2U121001EB7
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDU5QKVU3D0I86N3H2U11T001EB3
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDU5QKVU3D0I86N3H2U11T001EB3
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDTTV5HD0Q0I86N3H2U11P001EAV
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDTTV5HD0Q0I86N3H2U11P001EAV
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDSK489TE20I86N3H2U114001EAA
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDSK489TE20I86N3H2U114001EAA
2025-06-10 08:05:35,460 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDSC7N3PHM0I86N3H2U10T001EA3
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDSC7N3PHM0I86N3H2U10T001EA3
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDS341MMSU0I86N3H2U10P001E9V
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDS341MMSU0I86N3H2U10P001E9V
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDRR6FJ7A60I86N3H2U10L001E9R
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDRR6FJ7A60I86N3H2U10L001E9R
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQQG9THS10I86N3H2U108001E9E
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQQG9THS10I86N3H2U108001E9E
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQJKO8O0D0I86N3H2U104001E9A
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQJKO8O0D0I86N3H2U104001E9A
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQ4GA7M630I86N3H2U1VS001E92
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQ4GA7M630I86N3H2U1VS001E92
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDOM98AG2E0I86N3H2U1V8001E8E
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDOM98AG2E0I86N3H2U1V8001E8E
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDNMK1P3900I86N3H2U1UO001E7U
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDNMK1P3900I86N3H2U1UO001E7U
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDN4LTS5B80I86N3H2U1UG001E7M
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDN4LTS5B80I86N3H2U1UG001E7M
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDMFCJQF4F0I86N3H2U1UC001E7I
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDMFCJQF4F0I86N3H2U1UC001E7I
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 3F059827C9E04DEAA6B50797867EC52B
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 3F059827C9E04DEAA6B50797867EC52B
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1HRQIR9VACRH722I1UUTD5AEGF001EGK
2025-06-10 08:05:35,476 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1HRQIR9VACRH722I1UUTD5AEGF001EGK
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1GPFAB4PUHT7OL7Q2OV4FVC7US001R67
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MRVUM0P77G7Q2OV78BKOG4001PUK
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MRVUM0P77G7Q2OV78BKOG4001PUK
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MR50JEM3SR7Q2OVAE57DM4001Q85
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MR50JEM3SR7Q2OVAE57DM4001Q85
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MO2IE70S367Q2OVAE57DLH001Q7I
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MO2IE70S367Q2OVAE57DLH001Q7I
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0TQNGGEI53IKSIOCDI7U6001G57
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0TQNGGEI53IKSIOCDI7U6001G57
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0NLURMV9D3IKSIOCDI7R2001G23
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0NLURMV9D3IKSIOCDI7R2001G23
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0LAK18ROU3IKSIOCDI7Q6001G17
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0LAK18ROU3IKSIOCDI7Q6001G17
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0J21PQQ9M3IKSIOCDI7P2001G03
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR
2025-06-10 08:05:35,491 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0A4NM87423IKSIOCDI7KU001FRV
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0A4NM87423IKSIOCDI7KU001FRV
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ08R852VK83IKSIOCDI7KE001FRF
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ08R852VK83IKSIOCDI7KE001FRF
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE3GMAAFL56AJB6QM8HA860011R4
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE3GMAAFL56AJB6QM8HA860011R4
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE38FF3LOL6AJB6QM8HA820011R0
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE38FF3LOL6AJB6QM8HA820011R0
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE2PLB45826AJB6QM8HA7Q0011QO
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE2PLB45826AJB6QM8HA7Q0011QO
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE29O5UH0D6AJB6QM8HA7I0011QG
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE29O5UH0D6AJB6QM8HA7I0011QG
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRD2F2MCTBD6AJB6QM8HA650011P3
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRD2F2MCTBD6AJB6QM8HA650011P3
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRD0GVFB5C86AJB6QM8HA590011O7
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRD0GVFB5C86AJB6QM8HA590011O7
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: E79F261889C1492982227C207062C267
2025-06-10 08:05:35,507 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: E79F261889C1492982227C207062C267
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: C207460918D74AAAB2E154B47B74F863
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: C207460918D74AAAB2E154B47B74F863
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 41D3E4ED4CEA49C09C36DE504B997534
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 41D3E4ED4CEA49C09C36DE504B997534
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUISOVAPU1P7AV8LHQQGIDU001EK7
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUISOVAPU1P7AV8LHQQGIDU001EK7
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUISLI3BD9P7AV8LHQQGIDR001EK4
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUISLI3BD9P7AV8LHQQGIDR001EK4
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIS7OTCALF7AV8LHQQGIDO001EK1
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIS7OTCALF7AV8LHQQGIDO001EK1
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIS34G4B697AV8LHQQGIDL001EJU
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIS34G4B697AV8LHQQGIDL001EJU
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRL782OM57AV8LHQQGIDF001EJO
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRL782OM57AV8LHQQGIDF001EJO
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRGKS2JA27AV8LHQQGIDC001EJL
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRGKS2JA27AV8LHQQGIDC001EJL
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRBVE2CQT7AV8LHQQGID9001EJI
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRBVE2CQT7AV8LHQQGID9001EJI
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIR7B0BL957AV8LHQQGID6001EJF
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIR7B0BL957AV8LHQQGID6001EJF
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIR2ETOHPO6E7AERKQ83K0001UO2
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIR2ETOHPO6E7AERKQ83K0001UO2
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQTRHD5AN6E7AERKQ83JT001UNV
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQTRHD5AN6E7AERKQ83JT001UNV
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQP8527T06E7AERKQ83JQ001UNS
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQP8527T06E7AERKQ83JQ001UNS
2025-06-10 08:05:35,523 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQG1BD1C36E7AERKQ83JN001UNP
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQG1BD1C36E7AERKQ83JN001UNP
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQ716B84L7AV8LHQQGID0001EJ9
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQ716B84L7AV8LHQQGID0001EJ9
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPOL2K5B16E7AERKQ83JE001UNG
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPOL2K5B16E7AERKQ83JE001UNG
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPK0KE3MN6E7AERKQ83JB001UND
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPK0KE3MN6E7AERKQ83JB001UND
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPGJC2SUF6E7AERKQ83J8001UNA
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPGJC2SUF6E7AERKQ83J8001UNA
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPC0VTE5M6E7AERKQ83J5001UN7
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPC0VTE5M6E7AERKQ83J5001UN7
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIP3GSKTFR6E7AERKQ83J2001UN4
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIP3GSKTFR6E7AERKQ83J2001UN4
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 14EB204A0BDE44888B43308269C1626A
2025-06-10 08:05:35,538 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 14EB204A0BDE44888B43308269C1626A
2025-06-10 08:05:37,570 - INFO - 更新表单数据成功: FINST-OJ666W718A2WDOFD85OPH5LU2HVX2Q6UJONBM031
2025-06-10 08:05:37,570 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1544.1, 'new_value': 537.0}, {'field': 'dailyBillAmount', 'old_value': 1544.1, 'new_value': 537.0}]
2025-06-10 08:05:38,491 - INFO - 更新表单数据成功: FINST-MLF66PA1Z72WLMW46L5E24ZF0L703I3TKONBML7
2025-06-10 08:05:38,507 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 9803.8, 'new_value': 8477.4}, {'field': 'dailyBillAmount', 'old_value': 9803.8, 'new_value': 8477.4}]
2025-06-10 08:05:39,007 - INFO - 更新表单数据成功: FINST-MLF66PA1Z72WLMW46L5E24ZF0L703I3TKONBMP7
2025-06-10 08:05:39,007 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 250.0, 'new_value': 182.0}, {'field': 'dailyBillAmount', 'old_value': 250.0, 'new_value': 182.0}]
2025-06-10 08:05:39,554 - INFO - 更新表单数据成功: FINST-LFA66G91XB2WLLNW6GTRO7PFCB7F25L3LONBM601
2025-06-10 08:05:39,554 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 17946.4, 'new_value': 15410.9}, {'field': 'dailyBillAmount', 'old_value': 17946.4, 'new_value': 15410.9}]
2025-06-10 08:05:40,163 - INFO - 更新表单数据成功: FINST-2PF66TC1CM2WH3308DOQ74XNH31O2EBELONBMS5
2025-06-10 08:05:40,163 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 116.0, 'new_value': 0.0}, {'field': 'dailyBillAmount', 'old_value': 116.0, 'new_value': 0.0}]
2025-06-10 08:05:40,757 - INFO - 更新表单数据成功: FINST-IQE66ZC1ZN2WO4TM9M1M664GXTZH29URLONBMES
2025-06-10 08:05:40,757 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 11966.5, 'new_value': 6119.9}, {'field': 'dailyBillAmount', 'old_value': 11966.5, 'new_value': 6119.9}]
2025-06-10 08:05:41,366 - INFO - 更新表单数据成功: FINST-NU966I81CM4WIF35ANIG39F0JSXW2GJ2MONBMH3
2025-06-10 08:05:41,366 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 13170.8, 'new_value': 12474.8}, {'field': 'dailyBillAmount', 'old_value': 13170.8, 'new_value': 12474.8}]
2025-06-10 08:05:41,804 - INFO - 更新表单数据成功: FINST-NU966I81CM4WIF35ANIG39F0JSXW2GJ2MONBML3
2025-06-10 08:05:41,804 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 8550.0, 'new_value': 8336.0}, {'field': 'dailyBillAmount', 'old_value': 8550.0, 'new_value': 8336.0}]
2025-06-10 08:05:42,398 - INFO - 更新表单数据成功: FINST-M1B66ED1OW3WX2L39NP7GB0IQ2IK3MDDMONBM7I
2025-06-10 08:05:42,398 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 15688.2, 'new_value': 4953.3}, {'field': 'dailyBillAmount', 'old_value': 15688.2, 'new_value': 4953.3}]
2025-06-10 08:05:42,882 - INFO - 更新表单数据成功: FINST-M1B66ED1OW3WX2L39NP7GB0IQ2IK3MDDMONBMBI
2025-06-10 08:05:42,882 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6046.0, 'new_value': 5765.0}, {'field': 'dailyBillAmount', 'old_value': 6046.0, 'new_value': 5765.0}]
2025-06-10 08:05:43,445 - INFO - 更新表单数据成功: FINST-8PF66V719O2W1MYPEPGCZ48KY9UA2X2OMONBM89
2025-06-10 08:05:43,445 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 11443.6, 'new_value': 8439.2}, {'field': 'dailyBillAmount', 'old_value': 11443.6, 'new_value': 8439.2}]
2025-06-10 08:05:44,054 - INFO - 更新表单数据成功: FINST-L5766E71MK4WIOXR6ZSLM79VSUV13KPYMONBMZ8
2025-06-10 08:05:44,054 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 10781.8, 'new_value': 8384.2}, {'field': 'dailyBillAmount', 'old_value': 10781.8, 'new_value': 8384.2}]
2025-06-10 08:05:44,695 - INFO - 更新表单数据成功: FINST-5A966081KO4W1B5QAOAP9606HUJB3O5CNONBM73
2025-06-10 08:05:44,695 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 18247.6, 'new_value': 10878.2}, {'field': 'dailyBillAmount', 'old_value': 18247.6, 'new_value': 10878.2}]
2025-06-10 08:05:45,304 - INFO - 更新表单数据成功: FINST-LLF66B71VC2WPA2BF0N3CABZC5NQ3PWMNONBMQ2
2025-06-10 08:05:45,304 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 27151.0, 'new_value': 5801.4}, {'field': 'dailyBillAmount', 'old_value': 27151.0, 'new_value': 5801.4}]
2025-06-10 08:05:45,773 - INFO - 更新表单数据成功: FINST-LLF66B71VC2WPA2BF0N3CABZC5NQ3PWMNONBMU2
2025-06-10 08:05:45,773 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 382.0, 'new_value': 30.0}, {'field': 'dailyBillAmount', 'old_value': 382.0, 'new_value': 30.0}]
2025-06-10 08:05:46,351 - INFO - 更新表单数据成功: FINST-2PF66CD1OQ2WA3B28N9LM7MANRWZ1RQXNONBMPU
2025-06-10 08:05:46,351 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 38055.2, 'new_value': 15417.8}, {'field': 'dailyBillAmount', 'old_value': 38055.2, 'new_value': 15417.8}]
2025-06-10 08:05:46,804 - INFO - 更新表单数据成功: FINST-2PF66CD1OQ2WA3B28N9LM7MANRWZ1RQXNONBMTU
2025-06-10 08:05:46,804 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 196.0, 'new_value': 88.0}, {'field': 'dailyBillAmount', 'old_value': 196.0, 'new_value': 88.0}]
2025-06-10 08:05:47,398 - INFO - 更新表单数据成功: FINST-OJ666W713B2WHO36DHUWC5P1ATXB2ME8OONBMU7
2025-06-10 08:05:47,398 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 24566.4, 'new_value': 11285.9}, {'field': 'dailyBillAmount', 'old_value': 24566.4, 'new_value': 11285.9}]
2025-06-10 08:05:47,945 - INFO - 更新表单数据成功: FINST-OJ666W713B2WHO36DHUWC5P1ATXB2ME8OONBMY7
2025-06-10 08:05:47,945 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1092.8, 'new_value': 0.0}, {'field': 'dailyBillAmount', 'old_value': 1092.8, 'new_value': 0.0}]
2025-06-10 08:05:48,835 - INFO - 更新表单数据成功: FINST-2FD66I715C2W6DAT9TWWM5SF8E9M3QE4PONBMAF
2025-06-10 08:05:48,835 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6197.16, 'new_value': 6202.16}, {'field': 'amount', 'old_value': 6197.16, 'new_value': 6202.16}, {'field': 'count', 'old_value': 259, 'new_value': 260}, {'field': 'onlineAmount', 'old_value': 4777.71, 'new_value': 4782.71}, {'field': 'onlineCount', 'old_value': 199, 'new_value': 200}]
2025-06-10 08:05:49,366 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3M4N2COBM8A
2025-06-10 08:05:49,366 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3619.6}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3619.6}]
2025-06-10 08:05:49,866 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBM6B
2025-06-10 08:05:49,866 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_********, 变更字段: [{'field': 'amount', 'old_value': 3255.19, 'new_value': 3272.19}, {'field': 'count', 'old_value': 159, 'new_value': 161}, {'field': 'instoreAmount', 'old_value': 3376.89, 'new_value': 3393.89}, {'field': 'instoreCount', 'old_value': 159, 'new_value': 161}]
2025-06-10 08:05:50,319 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBM7B
2025-06-10 08:05:50,319 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_********, 变更字段: [{'field': 'amount', 'old_value': 4824.16, 'new_value': 4812.360000000001}]
2025-06-10 08:05:50,757 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBM8B
2025-06-10 08:05:50,757 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2266.61}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2266.61}, {'field': 'amount', 'old_value': 1304.7, 'new_value': 1312.1}, {'field': 'count', 'old_value': 69, 'new_value': 71}, {'field': 'onlineAmount', 'old_value': 945.7, 'new_value': 953.1}, {'field': 'onlineCount', 'old_value': 52, 'new_value': 54}]
2025-06-10 08:05:51,304 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBMDB
2025-06-10 08:05:51,304 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4399.0, 'new_value': 12392.6}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 12392.6}]
2025-06-10 08:05:51,804 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBMHB
2025-06-10 08:05:51,804 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_********, 变更字段: [{'field': 'amount', 'old_value': 4321.0, 'new_value': 4631.0}, {'field': 'count', 'old_value': 11, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 4321.0, 'new_value': 4631.0}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 12}]
2025-06-10 08:05:52,304 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBMZB
2025-06-10 08:05:52,304 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3161.38, 'new_value': 3265.28}, {'field': 'amount', 'old_value': 3161.38, 'new_value': 3265.28}, {'field': 'count', 'old_value': 172, 'new_value': 175}, {'field': 'onlineAmount', 'old_value': 2781.81, 'new_value': 2885.71}, {'field': 'onlineCount', 'old_value': 150, 'new_value': 153}]
2025-06-10 08:05:52,819 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBM3C
2025-06-10 08:05:52,819 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1864.33, 'new_value': 1872.33}, {'field': 'amount', 'old_value': 1864.33, 'new_value': 1872.33}, {'field': 'instoreAmount', 'old_value': 1923.72, 'new_value': 1931.72}]
2025-06-10 08:05:53,241 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBM8C
2025-06-10 08:05:53,241 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 506.0, 'new_value': 1856.0}, {'field': 'dailyBillAmount', 'old_value': 506.0, 'new_value': 1856.0}]
2025-06-10 08:05:53,757 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBMAC
2025-06-10 08:05:53,757 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 8887.14, 'new_value': 8976.84}, {'field': 'amount', 'old_value': 8887.14, 'new_value': 8976.84}, {'field': 'count', 'old_value': 155, 'new_value': 156}, {'field': 'instoreAmount', 'old_value': 6268.74, 'new_value': 6358.44}, {'field': 'instoreCount', 'old_value': 115, 'new_value': 116}]
2025-06-10 08:05:54,257 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBMEC
2025-06-10 08:05:54,257 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2175.6, 'new_value': 2179.14}, {'field': 'amount', 'old_value': 2175.6, 'new_value': 2179.14}, {'field': 'count', 'old_value': 145, 'new_value': 146}, {'field': 'onlineAmount', 'old_value': 1063.49, 'new_value': 1067.03}, {'field': 'onlineCount', 'old_value': 83, 'new_value': 84}]
2025-06-10 08:05:54,694 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T3N4N2COBMMC
2025-06-10 08:05:54,694 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16734.06}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16734.06}]
2025-06-10 08:05:55,148 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBMQC
2025-06-10 08:05:55,148 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_********, 变更字段: [{'field': 'amount', 'old_value': 28184.1, 'new_value': 28291.4}, {'field': 'count', 'old_value': 187, 'new_value': 192}, {'field': 'onlineAmount', 'old_value': 1712.36, 'new_value': 1819.66}, {'field': 'onlineCount', 'old_value': 60, 'new_value': 65}]
2025-06-10 08:05:55,616 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBMRC
2025-06-10 08:05:55,616 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_********, 变更字段: [{'field': 'amount', 'old_value': 29788.75, 'new_value': 30039.75}, {'field': 'count', 'old_value': 278, 'new_value': 279}, {'field': 'instoreAmount', 'old_value': 23290.5, 'new_value': 23541.5}, {'field': 'instoreCount', 'old_value': 160, 'new_value': 161}]
2025-06-10 08:05:56,148 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBMSC
2025-06-10 08:05:56,148 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_********, 变更字段: [{'field': 'amount', 'old_value': 47636.92, 'new_value': 49346.92}, {'field': 'count', 'old_value': 330, 'new_value': 337}, {'field': 'instoreAmount', 'old_value': 27913.4, 'new_value': 29623.4}, {'field': 'instoreCount', 'old_value': 167, 'new_value': 174}]
2025-06-10 08:05:56,679 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBM3D
2025-06-10 08:05:56,679 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'amount', 'old_value': 8858.81, 'new_value': 8918.11}, {'field': 'count', 'old_value': 320, 'new_value': 324}, {'field': 'onlineAmount', 'old_value': 4997.11, 'new_value': 5056.41}, {'field': 'onlineCount', 'old_value': 192, 'new_value': 196}]
2025-06-10 08:05:57,085 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBMED
2025-06-10 08:05:57,101 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_********, 变更字段: [{'field': 'amount', 'old_value': 2618.98, 'new_value': 3403.24}, {'field': 'count', 'old_value': 26, 'new_value': 35}, {'field': 'instoreAmount', 'old_value': 2026.63, 'new_value': 2810.89}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 27}]
2025-06-10 08:05:57,569 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBMHD
2025-06-10 08:05:57,569 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_********, 变更字段: [{'field': 'amount', 'old_value': 1387.0, 'new_value': 1855.0}, {'field': 'count', 'old_value': 6, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 1387.0, 'new_value': 1855.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 7}]
2025-06-10 08:05:58,054 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBMXD
2025-06-10 08:05:58,069 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4652.0, 'new_value': 7681.4}, {'field': 'amount', 'old_value': 4652.0, 'new_value': 7681.4}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 4652.0, 'new_value': 7681.4}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-10 08:05:58,523 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBMYD
2025-06-10 08:05:58,523 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_********, 变更字段: [{'field': 'amount', 'old_value': 8534.66, 'new_value': 8677.86}, {'field': 'count', 'old_value': 282, 'new_value': 284}, {'field': 'onlineAmount', 'old_value': 4928.9, 'new_value': 5072.1}, {'field': 'onlineCount', 'old_value': 80, 'new_value': 82}]
2025-06-10 08:05:58,991 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBMZD
2025-06-10 08:05:58,991 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_********, 变更字段: [{'field': 'amount', 'old_value': 4382.01, 'new_value': 4420.530000000001}, {'field': 'count', 'old_value': 299, 'new_value': 307}, {'field': 'onlineAmount', 'old_value': 4298.91, 'new_value': 4337.43}, {'field': 'onlineCount', 'old_value': 275, 'new_value': 283}]
2025-06-10 08:05:59,476 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBM1E
2025-06-10 08:05:59,476 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4510.79, 'new_value': 5053.9}, {'field': 'amount', 'old_value': 4510.79, 'new_value': 5053.9}, {'field': 'count', 'old_value': 161, 'new_value': 162}, {'field': 'instoreAmount', 'old_value': 4422.29, 'new_value': 4965.4}, {'field': 'instoreCount', 'old_value': 159, 'new_value': 160}]
2025-06-10 08:05:59,929 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBM2E
2025-06-10 08:05:59,929 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_********, 变更字段: [{'field': 'amount', 'old_value': 5014.089999999999, 'new_value': 5015.099999999999}, {'field': 'count', 'old_value': 239, 'new_value': 240}, {'field': 'instoreAmount', 'old_value': 739.2, 'new_value': 740.21}, {'field': 'instoreCount', 'old_value': 46, 'new_value': 47}]
2025-06-10 08:06:00,398 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBM3E
2025-06-10 08:06:00,398 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_********, 变更字段: [{'field': 'amount', 'old_value': 1308.6, 'new_value': 1551.5}, {'field': 'count', 'old_value': 41, 'new_value': 46}, {'field': 'instoreAmount', 'old_value': 1330.0, 'new_value': 1572.9}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 46}]
2025-06-10 08:06:00,882 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBM4E
2025-06-10 08:06:00,882 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5871.78, 'new_value': 5878.4}, {'field': 'amount', 'old_value': 5871.78, 'new_value': 5878.4}, {'field': 'count', 'old_value': 351, 'new_value': 352}, {'field': 'instoreAmount', 'old_value': 2834.28, 'new_value': 2840.9}, {'field': 'instoreCount', 'old_value': 207, 'new_value': 208}]
2025-06-10 08:06:01,398 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12LTP2COBM6E
2025-06-10 08:06:01,398 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_********, 变更字段: [{'field': 'amount', 'old_value': 6114.2, 'new_value': 6100.0199999999995}, {'field': 'count', 'old_value': 426, 'new_value': 427}, {'field': 'instoreAmount', 'old_value': 3994.7, 'new_value': 4017.2}, {'field': 'instoreCount', 'old_value': 264, 'new_value': 267}, {'field': 'onlineAmount', 'old_value': 2172.0, 'new_value': 2157.0}, {'field': 'onlineCount', 'old_value': 162, 'new_value': 160}]
2025-06-10 08:06:01,898 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12MTP2COBMIE
2025-06-10 08:06:01,898 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_********, 变更字段: [{'field': 'amount', 'old_value': 34606.0, 'new_value': 35176.0}, {'field': 'count', 'old_value': 126, 'new_value': 127}, {'field': 'instoreAmount', 'old_value': 34606.0, 'new_value': 35176.0}, {'field': 'instoreCount', 'old_value': 126, 'new_value': 127}]
2025-06-10 08:06:02,413 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12MTP2COBMQE
2025-06-10 08:06:02,413 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6921.21, 'new_value': 6926.72}, {'field': 'amount', 'old_value': 6921.21, 'new_value': 6926.72}, {'field': 'count', 'old_value': 373, 'new_value': 375}, {'field': 'instoreAmount', 'old_value': 3734.13, 'new_value': 3759.43}, {'field': 'instoreCount', 'old_value': 202, 'new_value': 204}]
2025-06-10 08:06:02,882 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12MTP2COBMRE
2025-06-10 08:06:02,882 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7506.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7506.7}]
2025-06-10 08:06:03,382 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12MTP2COBMYE
2025-06-10 08:06:03,382 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_********, 变更字段: [{'field': 'amount', 'old_value': 12607.16, 'new_value': 13152.16}, {'field': 'count', 'old_value': 62, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 11978.3, 'new_value': 12523.3}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 55}]
2025-06-10 08:06:03,851 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12MTP2COBM0F
2025-06-10 08:06:03,851 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 25846.4, 'new_value': 25864.15}, {'field': 'dailyBillAmount', 'old_value': 25846.4, 'new_value': 25864.15}, {'field': 'amount', 'old_value': 12470.29, 'new_value': 12469.29}, {'field': 'onlineAmount', 'old_value': 889.25, 'new_value': 888.25}]
2025-06-10 08:06:04,413 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12MTP2COBM8F
2025-06-10 08:06:04,429 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_********, 变更字段: [{'field': 'amount', 'old_value': 25902.350000000002, 'new_value': 28052.350000000002}, {'field': 'count', 'old_value': 144, 'new_value': 146}, {'field': 'instoreAmount', 'old_value': 23040.19, 'new_value': 25190.19}, {'field': 'instoreCount', 'old_value': 113, 'new_value': 115}]
2025-06-10 08:06:05,023 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12MTP2COBMHF
2025-06-10 08:06:05,023 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_********, 变更字段: [{'field': 'amount', 'old_value': 1288.93, 'new_value': 1248.3300000000002}]
2025-06-10 08:06:05,569 - INFO - 更新表单数据成功: FINST-MLF662B18S2W1CWT724D07P2DLNQ3NAS2COBMTU
2025-06-10 08:06:05,569 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2481.13}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2481.13}]
2025-06-10 08:06:05,773 - INFO - 正在批量插入每日数据，批次 1/64，共 100 条记录
2025-06-10 08:06:06,304 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-06-10 08:06:09,319 - INFO - 正在批量插入每日数据，批次 2/64，共 100 条记录
2025-06-10 08:06:09,866 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-06-10 08:06:12,882 - INFO - 正在批量插入每日数据，批次 3/64，共 100 条记录
2025-06-10 08:06:13,241 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-06-10 08:06:16,257 - INFO - 正在批量插入每日数据，批次 4/64，共 100 条记录
2025-06-10 08:06:16,647 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-06-10 08:06:19,663 - INFO - 正在批量插入每日数据，批次 5/64，共 100 条记录
2025-06-10 08:06:20,101 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-06-10 08:06:23,116 - INFO - 正在批量插入每日数据，批次 6/64，共 100 条记录
2025-06-10 08:06:23,585 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-06-10 08:06:26,600 - INFO - 正在批量插入每日数据，批次 7/64，共 100 条记录
2025-06-10 08:06:26,976 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-06-10 08:06:29,991 - INFO - 正在批量插入每日数据，批次 8/64，共 100 条记录
2025-06-10 08:06:30,429 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-06-10 08:06:33,429 - INFO - 正在批量插入每日数据，批次 9/64，共 100 条记录
2025-06-10 08:06:33,835 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-06-10 08:06:36,850 - INFO - 正在批量插入每日数据，批次 10/64，共 100 条记录
2025-06-10 08:06:37,241 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-06-10 08:06:40,257 - INFO - 正在批量插入每日数据，批次 11/64，共 100 条记录
2025-06-10 08:06:40,710 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-06-10 08:06:43,725 - INFO - 正在批量插入每日数据，批次 12/64，共 100 条记录
2025-06-10 08:06:44,116 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-06-10 08:06:47,132 - INFO - 正在批量插入每日数据，批次 13/64，共 100 条记录
2025-06-10 08:06:47,491 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-06-10 08:06:50,507 - INFO - 正在批量插入每日数据，批次 14/64，共 100 条记录
2025-06-10 08:06:50,928 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-06-10 08:06:53,944 - INFO - 正在批量插入每日数据，批次 15/64，共 100 条记录
2025-06-10 08:06:54,382 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-06-10 08:06:57,397 - INFO - 正在批量插入每日数据，批次 16/64，共 100 条记录
2025-06-10 08:06:57,866 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-06-10 08:07:00,882 - INFO - 正在批量插入每日数据，批次 17/64，共 100 条记录
2025-06-10 08:07:01,335 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-06-10 08:07:04,350 - INFO - 正在批量插入每日数据，批次 18/64，共 100 条记录
2025-06-10 08:07:04,772 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-06-10 08:07:07,788 - INFO - 正在批量插入每日数据，批次 19/64，共 100 条记录
2025-06-10 08:07:08,225 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-06-10 08:07:11,241 - INFO - 正在批量插入每日数据，批次 20/64，共 100 条记录
2025-06-10 08:07:11,678 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-06-10 08:07:14,694 - INFO - 正在批量插入每日数据，批次 21/64，共 100 条记录
2025-06-10 08:07:15,100 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-06-10 08:07:18,116 - INFO - 正在批量插入每日数据，批次 22/64，共 100 条记录
2025-06-10 08:07:18,585 - INFO - 批量插入每日数据成功，批次 22，100 条记录
2025-06-10 08:07:21,600 - INFO - 正在批量插入每日数据，批次 23/64，共 100 条记录
2025-06-10 08:07:22,038 - INFO - 批量插入每日数据成功，批次 23，100 条记录
2025-06-10 08:07:25,053 - INFO - 正在批量插入每日数据，批次 24/64，共 100 条记录
2025-06-10 08:07:25,475 - INFO - 批量插入每日数据成功，批次 24，100 条记录
2025-06-10 08:07:28,491 - INFO - 正在批量插入每日数据，批次 25/64，共 100 条记录
2025-06-10 08:07:28,975 - INFO - 批量插入每日数据成功，批次 25，100 条记录
2025-06-10 08:07:31,991 - INFO - 正在批量插入每日数据，批次 26/64，共 100 条记录
2025-06-10 08:07:32,459 - INFO - 批量插入每日数据成功，批次 26，100 条记录
2025-06-10 08:07:35,475 - INFO - 正在批量插入每日数据，批次 27/64，共 100 条记录
2025-06-10 08:07:35,928 - INFO - 批量插入每日数据成功，批次 27，100 条记录
2025-06-10 08:07:38,944 - INFO - 正在批量插入每日数据，批次 28/64，共 100 条记录
2025-06-10 08:07:39,381 - INFO - 批量插入每日数据成功，批次 28，100 条记录
2025-06-10 08:07:42,397 - INFO - 正在批量插入每日数据，批次 29/64，共 100 条记录
2025-06-10 08:07:42,834 - INFO - 批量插入每日数据成功，批次 29，100 条记录
2025-06-10 08:07:45,850 - INFO - 正在批量插入每日数据，批次 30/64，共 100 条记录
2025-06-10 08:07:46,272 - INFO - 批量插入每日数据成功，批次 30，100 条记录
2025-06-10 08:07:49,287 - INFO - 正在批量插入每日数据，批次 31/64，共 100 条记录
2025-06-10 08:07:49,725 - INFO - 批量插入每日数据成功，批次 31，100 条记录
2025-06-10 08:07:52,741 - INFO - 正在批量插入每日数据，批次 32/64，共 100 条记录
2025-06-10 08:07:53,147 - INFO - 批量插入每日数据成功，批次 32，100 条记录
2025-06-10 08:07:56,162 - INFO - 正在批量插入每日数据，批次 33/64，共 100 条记录
2025-06-10 08:07:56,600 - INFO - 批量插入每日数据成功，批次 33，100 条记录
2025-06-10 08:07:59,616 - INFO - 正在批量插入每日数据，批次 34/64，共 100 条记录
2025-06-10 08:08:00,022 - INFO - 批量插入每日数据成功，批次 34，100 条记录
2025-06-10 08:08:03,037 - INFO - 正在批量插入每日数据，批次 35/64，共 100 条记录
2025-06-10 08:08:03,444 - INFO - 批量插入每日数据成功，批次 35，100 条记录
2025-06-10 08:08:06,459 - INFO - 正在批量插入每日数据，批次 36/64，共 100 条记录
2025-06-10 08:08:06,850 - INFO - 批量插入每日数据成功，批次 36，100 条记录
2025-06-10 08:08:09,865 - INFO - 正在批量插入每日数据，批次 37/64，共 100 条记录
2025-06-10 08:08:10,319 - INFO - 批量插入每日数据成功，批次 37，100 条记录
2025-06-10 08:08:13,334 - INFO - 正在批量插入每日数据，批次 38/64，共 100 条记录
2025-06-10 08:08:13,756 - INFO - 批量插入每日数据成功，批次 38，100 条记录
2025-06-10 08:08:16,756 - INFO - 正在批量插入每日数据，批次 39/64，共 100 条记录
2025-06-10 08:08:17,147 - INFO - 批量插入每日数据成功，批次 39，100 条记录
2025-06-10 08:08:20,162 - INFO - 正在批量插入每日数据，批次 40/64，共 100 条记录
2025-06-10 08:08:20,553 - INFO - 批量插入每日数据成功，批次 40，100 条记录
2025-06-10 08:08:23,568 - INFO - 正在批量插入每日数据，批次 41/64，共 100 条记录
2025-06-10 08:08:24,022 - INFO - 批量插入每日数据成功，批次 41，100 条记录
2025-06-10 08:08:27,037 - INFO - 正在批量插入每日数据，批次 42/64，共 100 条记录
2025-06-10 08:08:27,428 - INFO - 批量插入每日数据成功，批次 42，100 条记录
2025-06-10 08:08:30,443 - INFO - 正在批量插入每日数据，批次 43/64，共 100 条记录
2025-06-10 08:08:30,850 - INFO - 批量插入每日数据成功，批次 43，100 条记录
2025-06-10 08:08:33,865 - INFO - 正在批量插入每日数据，批次 44/64，共 100 条记录
2025-06-10 08:08:34,350 - INFO - 批量插入每日数据成功，批次 44，100 条记录
2025-06-10 08:08:37,365 - INFO - 正在批量插入每日数据，批次 45/64，共 100 条记录
2025-06-10 08:08:37,772 - INFO - 批量插入每日数据成功，批次 45，100 条记录
2025-06-10 08:08:40,787 - INFO - 正在批量插入每日数据，批次 46/64，共 100 条记录
2025-06-10 08:08:41,240 - INFO - 批量插入每日数据成功，批次 46，100 条记录
2025-06-10 08:08:44,256 - INFO - 正在批量插入每日数据，批次 47/64，共 100 条记录
2025-06-10 08:08:44,662 - INFO - 批量插入每日数据成功，批次 47，100 条记录
2025-06-10 08:08:47,678 - INFO - 正在批量插入每日数据，批次 48/64，共 100 条记录
2025-06-10 08:08:48,115 - INFO - 批量插入每日数据成功，批次 48，100 条记录
2025-06-10 08:08:51,131 - INFO - 正在批量插入每日数据，批次 49/64，共 100 条记录
2025-06-10 08:08:51,600 - INFO - 批量插入每日数据成功，批次 49，100 条记录
2025-06-10 08:08:54,615 - INFO - 正在批量插入每日数据，批次 50/64，共 100 条记录
2025-06-10 08:08:55,021 - INFO - 批量插入每日数据成功，批次 50，100 条记录
2025-06-10 08:08:58,053 - INFO - 正在批量插入每日数据，批次 51/64，共 100 条记录
2025-06-10 08:08:58,506 - INFO - 批量插入每日数据成功，批次 51，100 条记录
2025-06-10 08:09:01,521 - INFO - 正在批量插入每日数据，批次 52/64，共 100 条记录
2025-06-10 08:09:01,959 - INFO - 批量插入每日数据成功，批次 52，100 条记录
2025-06-10 08:09:04,974 - INFO - 正在批量插入每日数据，批次 53/64，共 100 条记录
2025-06-10 08:09:05,474 - INFO - 批量插入每日数据成功，批次 53，100 条记录
2025-06-10 08:09:08,474 - INFO - 正在批量插入每日数据，批次 54/64，共 100 条记录
2025-06-10 08:09:09,349 - INFO - 批量插入每日数据成功，批次 54，100 条记录
2025-06-10 08:09:12,365 - INFO - 正在批量插入每日数据，批次 55/64，共 100 条记录
2025-06-10 08:09:12,756 - INFO - 批量插入每日数据成功，批次 55，100 条记录
2025-06-10 08:09:15,771 - INFO - 正在批量插入每日数据，批次 56/64，共 100 条记录
2025-06-10 08:09:16,209 - INFO - 批量插入每日数据成功，批次 56，100 条记录
2025-06-10 08:09:19,224 - INFO - 正在批量插入每日数据，批次 57/64，共 100 条记录
2025-06-10 08:09:19,615 - INFO - 批量插入每日数据成功，批次 57，100 条记录
2025-06-10 08:09:22,631 - INFO - 正在批量插入每日数据，批次 58/64，共 100 条记录
2025-06-10 08:09:23,021 - INFO - 批量插入每日数据成功，批次 58，100 条记录
2025-06-10 08:09:26,037 - INFO - 正在批量插入每日数据，批次 59/64，共 100 条记录
2025-06-10 08:09:26,459 - INFO - 批量插入每日数据成功，批次 59，100 条记录
2025-06-10 08:09:29,474 - INFO - 正在批量插入每日数据，批次 60/64，共 100 条记录
2025-06-10 08:09:29,896 - INFO - 批量插入每日数据成功，批次 60，100 条记录
2025-06-10 08:09:32,912 - INFO - 正在批量插入每日数据，批次 61/64，共 100 条记录
2025-06-10 08:09:33,349 - INFO - 批量插入每日数据成功，批次 61，100 条记录
2025-06-10 08:09:36,365 - INFO - 正在批量插入每日数据，批次 62/64，共 100 条记录
2025-06-10 08:09:36,709 - INFO - 批量插入每日数据成功，批次 62，100 条记录
2025-06-10 08:09:39,740 - INFO - 正在批量插入每日数据，批次 63/64，共 100 条记录
2025-06-10 08:09:40,209 - INFO - 批量插入每日数据成功，批次 63，100 条记录
2025-06-10 08:09:43,224 - INFO - 正在批量插入每日数据，批次 64/64，共 11 条记录
2025-06-10 08:09:43,396 - INFO - 批量插入每日数据成功，批次 64，11 条记录
2025-06-10 08:09:46,396 - INFO - 批量插入每日数据完成: 总计 6311 条，成功 6311 条，失败 0 条
2025-06-10 08:09:46,396 - INFO - 批量插入日销售数据完成，共 6311 条记录
2025-06-10 08:09:46,396 - INFO - 日销售数据同步完成！更新: 54 条，插入: 6311 条，错误: 0 条，跳过: 6465 条
2025-06-10 08:09:46,396 - INFO - 正在获取宜搭月销售表单数据...
2025-06-10 08:09:46,396 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-06-01 至 2025-06-30
2025-06-10 08:09:46,396 - INFO - 查询月度分段 1: 2024-06-01 至 2024-08-31
2025-06-10 08:09:46,396 - INFO - 查询日期范围: 2024-06-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-10 08:09:46,396 - INFO - Request Parameters - Page 1:
2025-06-10 08:09:46,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:46,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:09:47,271 - INFO - API请求耗时: 875ms
2025-06-10 08:09:47,271 - INFO - Response - Page 1
2025-06-10 08:09:47,271 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-10 08:09:47,271 - INFO - 查询完成，共获取到 0 条记录
2025-06-10 08:09:47,271 - WARNING - 月度分段 1 查询返回空数据
2025-06-10 08:09:47,271 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-06-10 08:09:47,271 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-06-10 08:09:47,271 - INFO - Request Parameters - Page 1:
2025-06-10 08:09:47,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:47,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:09:47,505 - INFO - API请求耗时: 234ms
2025-06-10 08:09:47,505 - INFO - Response - Page 1
2025-06-10 08:09:47,505 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-10 08:09:47,505 - INFO - 查询完成，共获取到 0 条记录
2025-06-10 08:09:47,505 - WARNING - 单月查询返回空数据: 2024-06
2025-06-10 08:09:48,005 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-06-10 08:09:48,005 - INFO - Request Parameters - Page 1:
2025-06-10 08:09:48,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:48,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:09:48,193 - INFO - API请求耗时: 187ms
2025-06-10 08:09:48,193 - INFO - Response - Page 1
2025-06-10 08:09:48,193 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-10 08:09:48,193 - INFO - 查询完成，共获取到 0 条记录
2025-06-10 08:09:48,193 - WARNING - 单月查询返回空数据: 2024-07
2025-06-10 08:09:48,709 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-10 08:09:48,709 - INFO - Request Parameters - Page 1:
2025-06-10 08:09:48,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:48,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:09:48,912 - INFO - API请求耗时: 203ms
2025-06-10 08:09:48,912 - INFO - Response - Page 1
2025-06-10 08:09:48,912 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-10 08:09:48,912 - INFO - 查询完成，共获取到 0 条记录
2025-06-10 08:09:48,912 - WARNING - 单月查询返回空数据: 2024-08
2025-06-10 08:09:50,443 - INFO - 查询月度分段 2: 2024-09-01 至 2024-11-30
2025-06-10 08:09:50,443 - INFO - 查询日期范围: 2024-09-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-10 08:09:50,443 - INFO - Request Parameters - Page 1:
2025-06-10 08:09:50,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:50,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:09:50,646 - INFO - API请求耗时: 203ms
2025-06-10 08:09:50,646 - INFO - Response - Page 1
2025-06-10 08:09:50,646 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-10 08:09:50,646 - INFO - 查询完成，共获取到 0 条记录
2025-06-10 08:09:50,646 - WARNING - 月度分段 2 查询返回空数据
2025-06-10 08:09:50,646 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-06-10 08:09:50,646 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-06-10 08:09:50,646 - INFO - Request Parameters - Page 1:
2025-06-10 08:09:50,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:50,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:09:50,880 - INFO - API请求耗时: 219ms
2025-06-10 08:09:50,880 - INFO - Response - Page 1
2025-06-10 08:09:50,880 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-10 08:09:50,880 - INFO - 查询完成，共获取到 0 条记录
2025-06-10 08:09:50,880 - WARNING - 单月查询返回空数据: 2024-09
2025-06-10 08:09:51,380 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-06-10 08:09:51,380 - INFO - Request Parameters - Page 1:
2025-06-10 08:09:51,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:51,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:09:51,599 - INFO - API请求耗时: 219ms
2025-06-10 08:09:51,599 - INFO - Response - Page 1
2025-06-10 08:09:51,599 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-10 08:09:51,599 - INFO - 查询完成，共获取到 0 条记录
2025-06-10 08:09:51,599 - WARNING - 单月查询返回空数据: 2024-10
2025-06-10 08:09:52,115 - INFO - 查询日期范围: 2024-11-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-10 08:09:52,115 - INFO - Request Parameters - Page 1:
2025-06-10 08:09:52,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:52,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:09:52,318 - INFO - API请求耗时: 203ms
2025-06-10 08:09:52,318 - INFO - Response - Page 1
2025-06-10 08:09:52,318 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-10 08:09:52,318 - INFO - 查询完成，共获取到 0 条记录
2025-06-10 08:09:52,318 - WARNING - 单月查询返回空数据: 2024-11
2025-06-10 08:09:53,849 - INFO - 查询月度分段 3: 2024-12-01 至 2025-02-28
2025-06-10 08:09:53,849 - INFO - 查询日期范围: 2024-12-01 至 2025-02-28，使用分页查询，每页 100 条记录
2025-06-10 08:09:53,849 - INFO - Request Parameters - Page 1:
2025-06-10 08:09:53,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:53,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:09:54,427 - INFO - API请求耗时: 578ms
2025-06-10 08:09:54,427 - INFO - Response - Page 1
2025-06-10 08:09:54,427 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:09:54,927 - INFO - Request Parameters - Page 2:
2025-06-10 08:09:54,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:54,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:09:55,521 - INFO - API请求耗时: 594ms
2025-06-10 08:09:55,521 - INFO - Response - Page 2
2025-06-10 08:09:55,521 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:09:56,021 - INFO - Request Parameters - Page 3:
2025-06-10 08:09:56,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:56,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:09:56,568 - INFO - API请求耗时: 547ms
2025-06-10 08:09:56,568 - INFO - Response - Page 3
2025-06-10 08:09:56,568 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:09:57,068 - INFO - Request Parameters - Page 4:
2025-06-10 08:09:57,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:57,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:09:57,615 - INFO - API请求耗时: 547ms
2025-06-10 08:09:57,615 - INFO - Response - Page 4
2025-06-10 08:09:57,615 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:09:58,130 - INFO - Request Parameters - Page 5:
2025-06-10 08:09:58,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:58,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:09:58,646 - INFO - API请求耗时: 516ms
2025-06-10 08:09:58,646 - INFO - Response - Page 5
2025-06-10 08:09:58,646 - INFO - 第 5 页获取到 94 条记录
2025-06-10 08:09:58,646 - INFO - 查询完成，共获取到 494 条记录
2025-06-10 08:09:58,646 - INFO - 月度分段 3 查询成功，获取到 494 条记录
2025-06-10 08:09:59,662 - INFO - 查询月度分段 4: 2025-03-01 至 2025-05-31
2025-06-10 08:09:59,662 - INFO - 查询日期范围: 2025-03-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-10 08:09:59,662 - INFO - Request Parameters - Page 1:
2025-06-10 08:09:59,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:09:59,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:10:00,146 - INFO - API请求耗时: 484ms
2025-06-10 08:10:00,146 - INFO - Response - Page 1
2025-06-10 08:10:00,146 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:10:00,662 - INFO - Request Parameters - Page 2:
2025-06-10 08:10:00,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:10:00,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:10:01,271 - INFO - API请求耗时: 609ms
2025-06-10 08:10:01,271 - INFO - Response - Page 2
2025-06-10 08:10:01,271 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:10:01,771 - INFO - Request Parameters - Page 3:
2025-06-10 08:10:01,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:10:01,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:10:02,318 - INFO - API请求耗时: 547ms
2025-06-10 08:10:02,318 - INFO - Response - Page 3
2025-06-10 08:10:02,318 - INFO - 第 3 页获取到 100 条记录
2025-06-10 08:10:02,818 - INFO - Request Parameters - Page 4:
2025-06-10 08:10:02,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:10:02,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:10:03,412 - INFO - API请求耗时: 594ms
2025-06-10 08:10:03,412 - INFO - Response - Page 4
2025-06-10 08:10:03,412 - INFO - 第 4 页获取到 100 条记录
2025-06-10 08:10:03,912 - INFO - Request Parameters - Page 5:
2025-06-10 08:10:03,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:10:03,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:10:04,458 - INFO - API请求耗时: 547ms
2025-06-10 08:10:04,458 - INFO - Response - Page 5
2025-06-10 08:10:04,458 - INFO - 第 5 页获取到 100 条记录
2025-06-10 08:10:04,958 - INFO - Request Parameters - Page 6:
2025-06-10 08:10:04,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:10:04,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:10:05,583 - INFO - API请求耗时: 625ms
2025-06-10 08:10:05,583 - INFO - Response - Page 6
2025-06-10 08:10:05,583 - INFO - 第 6 页获取到 100 条记录
2025-06-10 08:10:06,083 - INFO - Request Parameters - Page 7:
2025-06-10 08:10:06,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:10:06,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:10:06,724 - INFO - API请求耗时: 641ms
2025-06-10 08:10:06,724 - INFO - Response - Page 7
2025-06-10 08:10:06,724 - INFO - 第 7 页获取到 98 条记录
2025-06-10 08:10:06,724 - INFO - 查询完成，共获取到 698 条记录
2025-06-10 08:10:06,724 - INFO - 月度分段 4 查询成功，获取到 698 条记录
2025-06-10 08:10:07,724 - INFO - 查询月度分段 5: 2025-06-01 至 2025-06-30
2025-06-10 08:10:07,724 - INFO - 查询日期范围: 2025-06-01 至 2025-06-30，使用分页查询，每页 100 条记录
2025-06-10 08:10:07,724 - INFO - Request Parameters - Page 1:
2025-06-10 08:10:07,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:10:07,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:10:08,271 - INFO - API请求耗时: 547ms
2025-06-10 08:10:08,271 - INFO - Response - Page 1
2025-06-10 08:10:08,271 - INFO - 第 1 页获取到 100 条记录
2025-06-10 08:10:08,787 - INFO - Request Parameters - Page 2:
2025-06-10 08:10:08,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:10:08,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:10:09,302 - INFO - API请求耗时: 516ms
2025-06-10 08:10:09,302 - INFO - Response - Page 2
2025-06-10 08:10:09,302 - INFO - 第 2 页获取到 100 条记录
2025-06-10 08:10:09,818 - INFO - Request Parameters - Page 3:
2025-06-10 08:10:09,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-10 08:10:09,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-10 08:10:10,162 - INFO - API请求耗时: 344ms
2025-06-10 08:10:10,162 - INFO - Response - Page 3
2025-06-10 08:10:10,162 - INFO - 第 3 页获取到 11 条记录
2025-06-10 08:10:10,162 - INFO - 查询完成，共获取到 211 条记录
2025-06-10 08:10:10,162 - INFO - 月度分段 5 查询成功，获取到 211 条记录
2025-06-10 08:10:11,177 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1403 条记录，失败 0 次
2025-06-10 08:10:11,177 - INFO - 成功获取宜搭月销售表单数据，共 1403 条记录
2025-06-10 08:10:11,177 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-10 08:10:11,177 - INFO - 正在从MySQL获取月度汇总数据...
2025-06-10 08:10:11,224 - INFO - 成功获取MySQL月度汇总数据，共 1404 条记录
2025-06-10 08:10:11,943 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250610.xlsx
2025-06-10 08:10:12,005 - INFO - 成功创建宜搭月销售数据索引，共 1403 条记录
2025-06-10 08:10:12,037 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:12,037 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:12,490 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4I
2025-06-10 08:10:12,490 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 218725.95, 'new_value': 240562.52}, {'field': 'dailyBillAmount', 'old_value': 218725.95, 'new_value': 240562.52}, {'field': 'amount', 'old_value': 163700.75, 'new_value': 179336.26}, {'field': 'count', 'old_value': 810, 'new_value': 890}, {'field': 'instoreAmount', 'old_value': 163700.75, 'new_value': 179336.26}, {'field': 'instoreCount', 'old_value': 810, 'new_value': 890}]
2025-06-10 08:10:12,490 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:12,974 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5I
2025-06-10 08:10:12,974 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 165258.24, 'new_value': 176540.61}, {'field': 'dailyBillAmount', 'old_value': 165258.24, 'new_value': 176540.61}, {'field': 'amount', 'old_value': 250766.0, 'new_value': 271910.0}, {'field': 'count', 'old_value': 902, 'new_value': 990}, {'field': 'instoreAmount', 'old_value': 249571.0, 'new_value': 270715.0}, {'field': 'instoreCount', 'old_value': 896, 'new_value': 983}, {'field': 'onlineAmount', 'old_value': 1195.0, 'new_value': 1290.0}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 7}]
2025-06-10 08:10:12,974 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:13,365 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6I
2025-06-10 08:10:13,365 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15877.8, 'new_value': 19349.1}, {'field': 'dailyBillAmount', 'old_value': 15877.8, 'new_value': 19349.1}, {'field': 'amount', 'old_value': 19957.1, 'new_value': 23848.4}, {'field': 'count', 'old_value': 93, 'new_value': 106}, {'field': 'onlineAmount', 'old_value': 12531.6, 'new_value': 16422.9}, {'field': 'onlineCount', 'old_value': 84, 'new_value': 97}]
2025-06-10 08:10:13,365 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:13,849 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7I
2025-06-10 08:10:13,849 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38450.1, 'new_value': 40530.1}, {'field': 'amount', 'old_value': 38450.1, 'new_value': 40530.1}, {'field': 'count', 'old_value': 40, 'new_value': 43}, {'field': 'instoreAmount', 'old_value': 38450.1, 'new_value': 40530.1}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 43}]
2025-06-10 08:10:13,849 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:14,333 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9I
2025-06-10 08:10:14,333 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 209798.07, 'new_value': 231241.68}, {'field': 'dailyBillAmount', 'old_value': 209798.07, 'new_value': 231241.68}, {'field': 'amount', 'old_value': 40184.37, 'new_value': 47009.45}, {'field': 'count', 'old_value': 208, 'new_value': 241}, {'field': 'instoreAmount', 'old_value': 40184.37, 'new_value': 47009.45}, {'field': 'instoreCount', 'old_value': 208, 'new_value': 241}]
2025-06-10 08:10:14,333 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:14,802 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8I
2025-06-10 08:10:14,802 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 232370.27, 'new_value': 257114.69}, {'field': 'dailyBillAmount', 'old_value': 232370.27, 'new_value': 257114.69}, {'field': 'amount', 'old_value': 140377.57, 'new_value': 154949.77}, {'field': 'count', 'old_value': 989, 'new_value': 1098}, {'field': 'instoreAmount', 'old_value': 126837.23, 'new_value': 139371.63}, {'field': 'instoreCount', 'old_value': 542, 'new_value': 595}, {'field': 'onlineAmount', 'old_value': 15769.45, 'new_value': 17849.45}, {'field': 'onlineCount', 'old_value': 447, 'new_value': 503}]
2025-06-10 08:10:14,802 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:15,333 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1I
2025-06-10 08:10:15,333 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 67744.59, 'new_value': 73808.65}, {'field': 'dailyBillAmount', 'old_value': 67744.59, 'new_value': 73808.65}, {'field': 'amount', 'old_value': 1936.1, 'new_value': 2169.7}, {'field': 'count', 'old_value': 26, 'new_value': 27}, {'field': 'onlineAmount', 'old_value': 1936.1, 'new_value': 2169.7}, {'field': 'onlineCount', 'old_value': 26, 'new_value': 27}]
2025-06-10 08:10:15,333 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:15,771 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2I
2025-06-10 08:10:15,771 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 164590.5, 'new_value': 177823.2}, {'field': 'dailyBillAmount', 'old_value': 164590.5, 'new_value': 177823.2}, {'field': 'amount', 'old_value': 90424.9, 'new_value': 100051.6}, {'field': 'count', 'old_value': 817, 'new_value': 917}, {'field': 'instoreAmount', 'old_value': 42765.6, 'new_value': 46681.5}, {'field': 'instoreCount', 'old_value': 332, 'new_value': 367}, {'field': 'onlineAmount', 'old_value': 47720.2, 'new_value': 53491.3}, {'field': 'onlineCount', 'old_value': 485, 'new_value': 550}]
2025-06-10 08:10:15,771 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:16,224 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3I
2025-06-10 08:10:16,224 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 101049.17, 'new_value': 109531.7}, {'field': 'dailyBillAmount', 'old_value': 101049.17, 'new_value': 109531.7}, {'field': 'amount', 'old_value': 102396.24, 'new_value': 111021.94}, {'field': 'count', 'old_value': 653, 'new_value': 718}, {'field': 'instoreAmount', 'old_value': 94462.13, 'new_value': 102327.13}, {'field': 'instoreCount', 'old_value': 546, 'new_value': 598}, {'field': 'onlineAmount', 'old_value': 8063.01, 'new_value': 8913.71}, {'field': 'onlineCount', 'old_value': 107, 'new_value': 120}]
2025-06-10 08:10:16,349 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:16,802 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUJ
2025-06-10 08:10:16,802 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 277285.47, 'new_value': 306487.66}, {'field': 'count', 'old_value': 369, 'new_value': 409}, {'field': 'instoreAmount', 'old_value': 277285.47, 'new_value': 306487.66}, {'field': 'instoreCount', 'old_value': 369, 'new_value': 409}]
2025-06-10 08:10:16,802 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:17,302 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTJ
2025-06-10 08:10:17,302 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 117533.64, 'new_value': 132015.24}, {'field': 'dailyBillAmount', 'old_value': 117533.64, 'new_value': 132015.24}, {'field': 'amount', 'old_value': 129509.64, 'new_value': 143991.24}, {'field': 'count', 'old_value': 411, 'new_value': 452}, {'field': 'instoreAmount', 'old_value': 129509.64, 'new_value': 143991.24}, {'field': 'instoreCount', 'old_value': 411, 'new_value': 452}]
2025-06-10 08:10:17,302 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:17,755 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSJ
2025-06-10 08:10:17,755 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7021.93, 'new_value': 9802.23}, {'field': 'dailyBillAmount', 'old_value': 7021.93, 'new_value': 9802.23}, {'field': 'amount', 'old_value': 7021.93, 'new_value': 9802.23}, {'field': 'count', 'old_value': 11, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 7021.93, 'new_value': 9802.23}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 14}]
2025-06-10 08:10:17,755 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:18,177 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRJ
2025-06-10 08:10:18,177 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11222.9, 'new_value': 11829.9}, {'field': 'dailyBillAmount', 'old_value': 11222.9, 'new_value': 11829.9}, {'field': 'amount', 'old_value': 26501.91, 'new_value': 27323.41}, {'field': 'count', 'old_value': 168, 'new_value': 182}, {'field': 'instoreAmount', 'old_value': 26501.91, 'new_value': 27323.41}, {'field': 'instoreCount', 'old_value': 168, 'new_value': 182}]
2025-06-10 08:10:18,177 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:18,599 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMI1
2025-06-10 08:10:18,599 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_8711865ACD3B4C2AADD3843CA2A204D9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25100.0, 'new_value': 26940.0}, {'field': 'amount', 'old_value': 25100.0, 'new_value': 26940.0}, {'field': 'count', 'old_value': 3, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 25100.0, 'new_value': 26940.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 4}]
2025-06-10 08:10:18,599 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:19,068 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQJ
2025-06-10 08:10:19,068 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78C3CF1DDB8443E8A4A4D425F7CA9756_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 60848.04, 'new_value': 67823.44}, {'field': 'dailyBillAmount', 'old_value': 54646.04, 'new_value': 61621.44}, {'field': 'amount', 'old_value': 60848.04, 'new_value': 67823.44}, {'field': 'count', 'old_value': 304, 'new_value': 342}, {'field': 'instoreAmount', 'old_value': 60848.04, 'new_value': 67823.44}, {'field': 'instoreCount', 'old_value': 304, 'new_value': 342}]
2025-06-10 08:10:19,068 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:19,474 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOJ
2025-06-10 08:10:19,474 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12424.54, 'new_value': 13508.53}, {'field': 'amount', 'old_value': 12424.54, 'new_value': 13508.53}, {'field': 'count', 'old_value': 116, 'new_value': 129}, {'field': 'instoreAmount', 'old_value': 12424.54, 'new_value': 13508.53}, {'field': 'instoreCount', 'old_value': 116, 'new_value': 129}]
2025-06-10 08:10:19,474 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:19,943 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNJ
2025-06-10 08:10:19,943 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 96086.0, 'new_value': 108509.9}, {'field': 'amount', 'old_value': 96086.0, 'new_value': 108509.9}, {'field': 'count', 'old_value': 158, 'new_value': 181}, {'field': 'instoreAmount', 'old_value': 96086.0, 'new_value': 108509.9}, {'field': 'instoreCount', 'old_value': 158, 'new_value': 181}]
2025-06-10 08:10:19,943 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:20,427 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMJ
2025-06-10 08:10:20,427 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12641.19, 'new_value': 14017.16}, {'field': 'dailyBillAmount', 'old_value': 12641.19, 'new_value': 14017.16}, {'field': 'amount', 'old_value': 14240.98, 'new_value': 15749.25}, {'field': 'count', 'old_value': 396, 'new_value': 441}, {'field': 'instoreAmount', 'old_value': 14240.98, 'new_value': 15749.25}, {'field': 'instoreCount', 'old_value': 396, 'new_value': 441}]
2025-06-10 08:10:20,427 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:20,865 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLJ
2025-06-10 08:10:20,865 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 65728.48, 'new_value': 72929.58}, {'field': 'dailyBillAmount', 'old_value': 56180.48, 'new_value': 63381.58}, {'field': 'amount', 'old_value': 65728.48, 'new_value': 72929.58}, {'field': 'count', 'old_value': 273, 'new_value': 303}, {'field': 'instoreAmount', 'old_value': 65728.48, 'new_value': 72929.58}, {'field': 'instoreCount', 'old_value': 273, 'new_value': 303}]
2025-06-10 08:10:20,865 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:21,302 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKJ
2025-06-10 08:10:21,302 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-06, 变更字段: [{'field': 'amount', 'old_value': 1065.1, 'new_value': 1124.0}, {'field': 'count', 'old_value': 16, 'new_value': 18}, {'field': 'instoreAmount', 'old_value': 1065.1, 'new_value': 1124.0}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 18}]
2025-06-10 08:10:21,302 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:21,833 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJJ
2025-06-10 08:10:21,833 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41935.28, 'new_value': 50870.54}, {'field': 'dailyBillAmount', 'old_value': 41935.28, 'new_value': 50870.54}, {'field': 'amount', 'old_value': 19696.9, 'new_value': 25321.94}, {'field': 'count', 'old_value': 228, 'new_value': 284}, {'field': 'instoreAmount', 'old_value': 18876.61, 'new_value': 24241.76}, {'field': 'instoreCount', 'old_value': 188, 'new_value': 237}, {'field': 'onlineAmount', 'old_value': 1315.5, 'new_value': 1614.39}, {'field': 'onlineCount', 'old_value': 40, 'new_value': 47}]
2025-06-10 08:10:21,833 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:22,286 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHJ
2025-06-10 08:10:22,286 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 65616.16, 'new_value': 74146.71}, {'field': 'dailyBillAmount', 'old_value': 65616.16, 'new_value': 74146.71}, {'field': 'amount', 'old_value': 80013.6, 'new_value': 89848.6}, {'field': 'count', 'old_value': 439, 'new_value': 493}, {'field': 'instoreAmount', 'old_value': 80013.6, 'new_value': 89848.6}, {'field': 'instoreCount', 'old_value': 439, 'new_value': 493}]
2025-06-10 08:10:22,286 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:22,771 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWI
2025-06-10 08:10:22,771 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 105919.91, 'new_value': 117797.48}, {'field': 'dailyBillAmount', 'old_value': 105919.91, 'new_value': 117797.48}, {'field': 'amount', 'old_value': 60315.72, 'new_value': 65714.56}, {'field': 'count', 'old_value': 242, 'new_value': 263}, {'field': 'instoreAmount', 'old_value': 61318.2, 'new_value': 66729.2}, {'field': 'instoreCount', 'old_value': 242, 'new_value': 263}]
2025-06-10 08:10:22,771 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:23,224 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGJ
2025-06-10 08:10:23,224 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 115940.07, 'new_value': 128865.3}, {'field': 'dailyBillAmount', 'old_value': 115940.07, 'new_value': 128865.3}, {'field': 'amount', 'old_value': 52500.9, 'new_value': 58466.74}, {'field': 'count', 'old_value': 535, 'new_value': 598}, {'field': 'instoreAmount', 'old_value': 26005.69, 'new_value': 28730.89}, {'field': 'instoreCount', 'old_value': 195, 'new_value': 215}, {'field': 'onlineAmount', 'old_value': 26495.21, 'new_value': 29735.85}, {'field': 'onlineCount', 'old_value': 340, 'new_value': 383}]
2025-06-10 08:10:23,224 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:23,740 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFJ
2025-06-10 08:10:23,740 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35369.81, 'new_value': 40887.21}, {'field': 'dailyBillAmount', 'old_value': 35369.81, 'new_value': 40887.21}, {'field': 'amount', 'old_value': 31265.98, 'new_value': 34567.98}, {'field': 'count', 'old_value': 128, 'new_value': 143}, {'field': 'instoreAmount', 'old_value': 31111.3, 'new_value': 34413.3}, {'field': 'instoreCount', 'old_value': 125, 'new_value': 140}]
2025-06-10 08:10:23,740 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:24,193 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEJ
2025-06-10 08:10:24,193 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20374.67, 'new_value': 23265.02}, {'field': 'amount', 'old_value': 20374.67, 'new_value': 23265.02}, {'field': 'count', 'old_value': 933, 'new_value': 1064}, {'field': 'instoreAmount', 'old_value': 21563.87, 'new_value': 24622.03}, {'field': 'instoreCount', 'old_value': 933, 'new_value': 1064}]
2025-06-10 08:10:24,193 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:24,615 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBI
2025-06-10 08:10:24,615 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-06, 变更字段: [{'field': 'amount', 'old_value': 39230.0, 'new_value': 43624.0}, {'field': 'count', 'old_value': 61, 'new_value': 65}, {'field': 'instoreAmount', 'old_value': 39230.0, 'new_value': 43624.0}, {'field': 'instoreCount', 'old_value': 61, 'new_value': 65}]
2025-06-10 08:10:24,615 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:25,083 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCI
2025-06-10 08:10:25,083 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 372206.69, 'new_value': 395693.07}, {'field': 'dailyBillAmount', 'old_value': 372206.69, 'new_value': 395693.07}, {'field': 'amount', 'old_value': -170561.15, 'new_value': -169167.94}, {'field': 'count', 'old_value': 337, 'new_value': 374}, {'field': 'instoreAmount', 'old_value': 198235.33, 'new_value': 221516.4}, {'field': 'instoreCount', 'old_value': 337, 'new_value': 374}]
2025-06-10 08:10:25,083 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:25,490 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDI
2025-06-10 08:10:25,490 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 118510.0, 'new_value': 133717.0}, {'field': 'amount', 'old_value': 118510.0, 'new_value': 133717.0}, {'field': 'count', 'old_value': 447, 'new_value': 502}, {'field': 'instoreAmount', 'old_value': 118510.0, 'new_value': 133717.0}, {'field': 'instoreCount', 'old_value': 447, 'new_value': 502}]
2025-06-10 08:10:25,490 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:25,974 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEI
2025-06-10 08:10:25,974 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 127432.8, 'new_value': 139952.03}, {'field': 'dailyBillAmount', 'old_value': 116004.4, 'new_value': 128067.63}, {'field': 'amount', 'old_value': 127432.8, 'new_value': 139952.03}, {'field': 'count', 'old_value': 406, 'new_value': 442}, {'field': 'instoreAmount', 'old_value': 127432.8, 'new_value': 139952.03}, {'field': 'instoreCount', 'old_value': 406, 'new_value': 442}]
2025-06-10 08:10:25,974 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:26,396 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMG1
2025-06-10 08:10:26,411 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 48745.72, 'new_value': 58642.65}, {'field': 'dailyBillAmount', 'old_value': 48745.72, 'new_value': 58642.65}, {'field': 'amount', 'old_value': 3375.7, 'new_value': 3688.7}, {'field': 'count', 'old_value': 14, 'new_value': 15}, {'field': 'instoreAmount', 'old_value': 3590.3, 'new_value': 4011.3}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 15}]
2025-06-10 08:10:26,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:26,974 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVJ
2025-06-10 08:10:26,974 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37329.4, 'new_value': 40949.0}, {'field': 'dailyBillAmount', 'old_value': 37329.4, 'new_value': 40949.0}, {'field': 'amount', 'old_value': 11266.5, 'new_value': 11814.3}, {'field': 'count', 'old_value': 49, 'new_value': 54}, {'field': 'instoreAmount', 'old_value': 11266.5, 'new_value': 11814.3}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 54}]
2025-06-10 08:10:26,974 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:27,521 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGI
2025-06-10 08:10:27,521 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45352.68, 'new_value': 51208.9}, {'field': 'dailyBillAmount', 'old_value': 25457.76, 'new_value': 30890.37}, {'field': 'amount', 'old_value': 45352.68, 'new_value': 51208.9}, {'field': 'count', 'old_value': 1501, 'new_value': 1674}, {'field': 'instoreAmount', 'old_value': 40982.72, 'new_value': 45494.04}, {'field': 'instoreCount', 'old_value': 1379, 'new_value': 1534}, {'field': 'onlineAmount', 'old_value': 4369.96, 'new_value': 5714.86}, {'field': 'onlineCount', 'old_value': 122, 'new_value': 140}]
2025-06-10 08:10:27,521 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:28,021 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHI
2025-06-10 08:10:28,021 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 82419.83, 'new_value': 93596.83}, {'field': 'dailyBillAmount', 'old_value': 82378.0, 'new_value': 93555.0}, {'field': 'amount', 'old_value': 66066.19, 'new_value': 77243.19}, {'field': 'count', 'old_value': 72, 'new_value': 81}, {'field': 'instoreAmount', 'old_value': 65250.0, 'new_value': 76427.0}, {'field': 'instoreCount', 'old_value': 67, 'new_value': 76}]
2025-06-10 08:10:28,021 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:28,474 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMII
2025-06-10 08:10:28,474 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 132207.73, 'new_value': 154713.47}, {'field': 'dailyBillAmount', 'old_value': 132207.73, 'new_value': 154713.47}, {'field': 'amount', 'old_value': 132207.73, 'new_value': 154713.47}, {'field': 'count', 'old_value': 132, 'new_value': 151}, {'field': 'instoreAmount', 'old_value': 132207.73, 'new_value': 154713.47}, {'field': 'instoreCount', 'old_value': 132, 'new_value': 151}]
2025-06-10 08:10:28,490 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:28,911 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKI
2025-06-10 08:10:28,911 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26904.2, 'new_value': 30475.9}, {'field': 'dailyBillAmount', 'old_value': 26904.2, 'new_value': 30475.9}, {'field': 'amount', 'old_value': 30932.9, 'new_value': 34814.6}, {'field': 'count', 'old_value': 91, 'new_value': 102}, {'field': 'instoreAmount', 'old_value': 30932.9, 'new_value': 34814.6}, {'field': 'instoreCount', 'old_value': 91, 'new_value': 102}]
2025-06-10 08:10:28,911 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:29,396 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDJ
2025-06-10 08:10:29,396 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9664.0, 'new_value': 10202.0}, {'field': 'dailyBillAmount', 'old_value': 9664.0, 'new_value': 10202.0}, {'field': 'amount', 'old_value': 9664.0, 'new_value': 10202.0}, {'field': 'count', 'old_value': 27, 'new_value': 29}, {'field': 'instoreAmount', 'old_value': 10051.0, 'new_value': 10589.0}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 29}]
2025-06-10 08:10:29,396 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:29,927 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMI
2025-06-10 08:10:29,927 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 61885.56, 'new_value': 75940.22}, {'field': 'dailyBillAmount', 'old_value': 61885.56, 'new_value': 75940.22}, {'field': 'amount', 'old_value': 81689.8, 'new_value': 99517.7}, {'field': 'count', 'old_value': 429, 'new_value': 518}, {'field': 'instoreAmount', 'old_value': 81787.8, 'new_value': 99615.7}, {'field': 'instoreCount', 'old_value': 429, 'new_value': 518}]
2025-06-10 08:10:29,927 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:30,365 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNI
2025-06-10 08:10:30,380 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50954.86, 'new_value': 59061.35}, {'field': 'dailyBillAmount', 'old_value': 50954.86, 'new_value': 59061.35}, {'field': 'amount', 'old_value': 5453.63, 'new_value': 7157.07}, {'field': 'count', 'old_value': 416, 'new_value': 535}, {'field': 'instoreAmount', 'old_value': 6442.33, 'new_value': 8264.97}, {'field': 'instoreCount', 'old_value': 416, 'new_value': 535}]
2025-06-10 08:10:30,380 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:30,896 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPI
2025-06-10 08:10:30,896 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 84793.7, 'new_value': 92999.7}, {'field': 'dailyBillAmount', 'old_value': 84793.7, 'new_value': 92999.7}, {'field': 'amount', 'old_value': 85061.97, 'new_value': 93267.97}, {'field': 'count', 'old_value': 227, 'new_value': 250}, {'field': 'instoreAmount', 'old_value': 85061.97, 'new_value': 93267.97}, {'field': 'instoreCount', 'old_value': 227, 'new_value': 250}]
2025-06-10 08:10:30,896 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:31,521 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQI
2025-06-10 08:10:31,521 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 69321.15, 'new_value': 93570.18}, {'field': 'dailyBillAmount', 'old_value': 59342.75, 'new_value': 93570.18}, {'field': 'amount', 'old_value': 26116.7, 'new_value': 28703.7}, {'field': 'count', 'old_value': 64, 'new_value': 70}, {'field': 'instoreAmount', 'old_value': 26116.7, 'new_value': 28703.7}, {'field': 'instoreCount', 'old_value': 64, 'new_value': 70}]
2025-06-10 08:10:31,521 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:31,974 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRI
2025-06-10 08:10:31,974 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 114788.96, 'new_value': 127189.2}, {'field': 'dailyBillAmount', 'old_value': 114788.96, 'new_value': 127189.2}, {'field': 'amount', 'old_value': 44928.3, 'new_value': 50623.7}, {'field': 'count', 'old_value': 174, 'new_value': 197}, {'field': 'instoreAmount', 'old_value': 44928.3, 'new_value': 50623.7}, {'field': 'instoreCount', 'old_value': 174, 'new_value': 197}]
2025-06-10 08:10:31,974 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:32,411 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSI
2025-06-10 08:10:32,411 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30591.03, 'new_value': 34557.26}, {'field': 'dailyBillAmount', 'old_value': 30591.03, 'new_value': 34557.26}, {'field': 'amount', 'old_value': 6483.85, 'new_value': 7116.37}, {'field': 'count', 'old_value': 245, 'new_value': 268}, {'field': 'instoreAmount', 'old_value': 1718.9, 'new_value': 1980.4}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 50}, {'field': 'onlineAmount', 'old_value': 4842.07, 'new_value': 5228.59}, {'field': 'onlineCount', 'old_value': 202, 'new_value': 218}]
2025-06-10 08:10:32,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:32,896 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTI
2025-06-10 08:10:32,896 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50369.63, 'new_value': 56330.74}, {'field': 'dailyBillAmount', 'old_value': 50369.63, 'new_value': 56330.74}, {'field': 'amount', 'old_value': 8753.27, 'new_value': 10774.88}, {'field': 'count', 'old_value': 224, 'new_value': 267}, {'field': 'instoreAmount', 'old_value': 7021.32, 'new_value': 8650.08}, {'field': 'instoreCount', 'old_value': 180, 'new_value': 214}, {'field': 'onlineAmount', 'old_value': 1756.1, 'new_value': 2148.95}, {'field': 'onlineCount', 'old_value': 44, 'new_value': 53}]
2025-06-10 08:10:32,896 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:33,411 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUI
2025-06-10 08:10:33,411 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-06, 变更字段: [{'field': 'amount', 'old_value': 9472.6, 'new_value': 9811.6}, {'field': 'count', 'old_value': 333, 'new_value': 348}, {'field': 'instoreAmount', 'old_value': 9572.6, 'new_value': 9911.6}, {'field': 'instoreCount', 'old_value': 333, 'new_value': 348}]
2025-06-10 08:10:33,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:33,880 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVI
2025-06-10 08:10:33,880 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15595.48, 'new_value': 17862.09}, {'field': 'dailyBillAmount', 'old_value': 15595.48, 'new_value': 17862.09}, {'field': 'amount', 'old_value': 9049.21, 'new_value': 10176.51}, {'field': 'count', 'old_value': 497, 'new_value': 552}, {'field': 'instoreAmount', 'old_value': 2758.77, 'new_value': 3163.77}, {'field': 'instoreCount', 'old_value': 120, 'new_value': 134}, {'field': 'onlineAmount', 'old_value': 6564.84, 'new_value': 7287.24}, {'field': 'onlineCount', 'old_value': 377, 'new_value': 418}]
2025-06-10 08:10:33,880 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:34,318 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCJ
2025-06-10 08:10:34,318 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 58449.7, 'new_value': 62583.1}, {'field': 'dailyBillAmount', 'old_value': 58449.7, 'new_value': 62583.1}, {'field': 'amount', 'old_value': 68637.7, 'new_value': 74082.1}, {'field': 'count', 'old_value': 251, 'new_value': 274}, {'field': 'instoreAmount', 'old_value': 69545.7, 'new_value': 74990.1}, {'field': 'instoreCount', 'old_value': 251, 'new_value': 274}]
2025-06-10 08:10:34,318 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:34,740 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBJ
2025-06-10 08:10:34,740 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32596.42, 'new_value': 35892.58}, {'field': 'dailyBillAmount', 'old_value': 32596.42, 'new_value': 35892.58}, {'field': 'amount', 'old_value': 33633.34, 'new_value': 37045.0}, {'field': 'count', 'old_value': 169, 'new_value': 184}, {'field': 'instoreAmount', 'old_value': 31625.66, 'new_value': 35037.32}, {'field': 'instoreCount', 'old_value': 147, 'new_value': 162}]
2025-06-10 08:10:34,740 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:35,161 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAJ
2025-06-10 08:10:35,161 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 47710.09, 'new_value': 54523.09}, {'field': 'dailyBillAmount', 'old_value': 46751.12, 'new_value': 53525.77}, {'field': 'amount', 'old_value': 47710.09, 'new_value': 54523.09}, {'field': 'count', 'old_value': 618, 'new_value': 706}, {'field': 'instoreAmount', 'old_value': 46052.62, 'new_value': 52691.62}, {'field': 'instoreCount', 'old_value': 594, 'new_value': 680}, {'field': 'onlineAmount', 'old_value': 1711.47, 'new_value': 1885.47}, {'field': 'onlineCount', 'old_value': 24, 'new_value': 26}]
2025-06-10 08:10:35,161 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:35,568 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLI
2025-06-10 08:10:35,568 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25335.94, 'new_value': 26356.94}, {'field': 'amount', 'old_value': 25335.94, 'new_value': 26356.94}, {'field': 'count', 'old_value': 39, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 25335.94, 'new_value': 26356.94}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 42}]
2025-06-10 08:10:35,583 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:36,036 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9J
2025-06-10 08:10:36,036 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 49524.55, 'new_value': 52570.85}, {'field': 'dailyBillAmount', 'old_value': 49524.55, 'new_value': 52570.85}, {'field': 'amount', 'old_value': 29820.36, 'new_value': 31832.57}, {'field': 'count', 'old_value': 742, 'new_value': 801}, {'field': 'instoreAmount', 'old_value': 26420.0, 'new_value': 27788.9}, {'field': 'instoreCount', 'old_value': 636, 'new_value': 682}, {'field': 'onlineAmount', 'old_value': 4274.01, 'new_value': 4917.32}, {'field': 'onlineCount', 'old_value': 106, 'new_value': 119}]
2025-06-10 08:10:36,036 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:36,458 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8J
2025-06-10 08:10:36,458 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 27569.5, 'new_value': 30019.5}, {'field': 'dailyBillAmount', 'old_value': 27569.5, 'new_value': 30019.5}, {'field': 'amount', 'old_value': 27569.5, 'new_value': 30019.5}, {'field': 'count', 'old_value': 80, 'new_value': 92}, {'field': 'instoreAmount', 'old_value': 28616.1, 'new_value': 31066.1}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 92}]
2025-06-10 08:10:36,474 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:36,896 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7J
2025-06-10 08:10:36,896 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18132.0, 'new_value': 20660.0}, {'field': 'dailyBillAmount', 'old_value': 18132.0, 'new_value': 20660.0}, {'field': 'amount', 'old_value': 20224.0, 'new_value': 22564.0}, {'field': 'count', 'old_value': 32, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 20792.0, 'new_value': 23132.0}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 36}]
2025-06-10 08:10:36,896 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:37,333 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6J
2025-06-10 08:10:37,333 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25208.64, 'new_value': 33138.64}, {'field': 'dailyBillAmount', 'old_value': 25208.64, 'new_value': 33138.64}, {'field': 'amount', 'old_value': 24016.64, 'new_value': 30706.64}, {'field': 'count', 'old_value': 101, 'new_value': 117}, {'field': 'instoreAmount', 'old_value': 24255.64, 'new_value': 30945.64}, {'field': 'instoreCount', 'old_value': 101, 'new_value': 117}]
2025-06-10 08:10:37,333 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:37,755 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5J
2025-06-10 08:10:37,755 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 95700.0, 'new_value': 103741.5}, {'field': 'dailyBillAmount', 'old_value': 95700.0, 'new_value': 103741.5}, {'field': 'amount', 'old_value': 39043.5, 'new_value': 42834.0}, {'field': 'count', 'old_value': 110, 'new_value': 120}, {'field': 'instoreAmount', 'old_value': 39043.5, 'new_value': 42834.0}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 120}]
2025-06-10 08:10:37,755 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:38,208 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4J
2025-06-10 08:10:38,208 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29006.0, 'new_value': 30489.3}, {'field': 'amount', 'old_value': 29006.0, 'new_value': 30489.3}, {'field': 'count', 'old_value': 661, 'new_value': 710}, {'field': 'instoreAmount', 'old_value': 29386.3, 'new_value': 30905.6}, {'field': 'instoreCount', 'old_value': 661, 'new_value': 710}]
2025-06-10 08:10:38,208 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:38,614 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3J
2025-06-10 08:10:38,614 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-06, 变更字段: [{'field': 'amount', 'old_value': 16737.01, 'new_value': 18245.02}, {'field': 'count', 'old_value': 211, 'new_value': 234}, {'field': 'instoreAmount', 'old_value': 16737.01, 'new_value': 18245.02}, {'field': 'instoreCount', 'old_value': 211, 'new_value': 234}]
2025-06-10 08:10:38,614 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:39,114 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2J
2025-06-10 08:10:39,114 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-06, 变更字段: [{'field': 'amount', 'old_value': 12325.68, 'new_value': 13064.78}, {'field': 'count', 'old_value': 490, 'new_value': 524}, {'field': 'onlineAmount', 'old_value': 6810.3, 'new_value': 7588.09}, {'field': 'onlineCount', 'old_value': 307, 'new_value': 341}]
2025-06-10 08:10:39,114 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:39,536 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1J
2025-06-10 08:10:39,536 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 27968.26, 'new_value': 31339.14}, {'field': 'dailyBillAmount', 'old_value': 27968.26, 'new_value': 31339.14}, {'field': 'amount', 'old_value': 28759.21, 'new_value': 32274.66}, {'field': 'count', 'old_value': 1618, 'new_value': 1809}, {'field': 'instoreAmount', 'old_value': 15526.51, 'new_value': 17787.46}, {'field': 'instoreCount', 'old_value': 824, 'new_value': 935}, {'field': 'onlineAmount', 'old_value': 13819.11, 'new_value': 15122.61}, {'field': 'onlineCount', 'old_value': 794, 'new_value': 874}]
2025-06-10 08:10:39,536 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:39,943 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0J
2025-06-10 08:10:39,943 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17228.0, 'new_value': 19737.0}, {'field': 'dailyBillAmount', 'old_value': 17228.0, 'new_value': 19737.0}, {'field': 'amount', 'old_value': 17193.0, 'new_value': 19702.0}, {'field': 'count', 'old_value': 336, 'new_value': 383}, {'field': 'instoreAmount', 'old_value': 17193.0, 'new_value': 19702.0}, {'field': 'instoreCount', 'old_value': 336, 'new_value': 383}]
2025-06-10 08:10:39,958 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:40,380 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZI
2025-06-10 08:10:40,380 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 97092.8, 'new_value': 104075.4}, {'field': 'dailyBillAmount', 'old_value': 97092.8, 'new_value': 104075.4}, {'field': 'amount', 'old_value': 69113.05, 'new_value': 75994.45}, {'field': 'count', 'old_value': 181, 'new_value': 200}, {'field': 'instoreAmount', 'old_value': 69342.0, 'new_value': 76223.4}, {'field': 'instoreCount', 'old_value': 181, 'new_value': 200}]
2025-06-10 08:10:40,380 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:40,818 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXI
2025-06-10 08:10:40,818 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 58147.7, 'new_value': 65292.03}, {'field': 'dailyBillAmount', 'old_value': 58147.7, 'new_value': 65292.03}, {'field': 'amount', 'old_value': 28634.0, 'new_value': 33519.48}, {'field': 'count', 'old_value': 1339, 'new_value': 1507}, {'field': 'instoreAmount', 'old_value': 29621.12, 'new_value': 34588.23}, {'field': 'instoreCount', 'old_value': 1339, 'new_value': 1507}]
2025-06-10 08:10:40,818 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:41,255 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYI
2025-06-10 08:10:41,255 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 180630.8, 'new_value': 194225.9}, {'field': 'dailyBillAmount', 'old_value': 180630.8, 'new_value': 194225.9}, {'field': 'amount', 'old_value': 180630.8, 'new_value': 194225.9}, {'field': 'count', 'old_value': 220, 'new_value': 238}, {'field': 'instoreAmount', 'old_value': 180630.8, 'new_value': 194225.9}, {'field': 'instoreCount', 'old_value': 220, 'new_value': 238}]
2025-06-10 08:10:41,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:42,021 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMOK
2025-06-10 08:10:42,021 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24867.0, 'new_value': 26165.0}, {'field': 'amount', 'old_value': 24867.0, 'new_value': 26165.0}, {'field': 'count', 'old_value': 22, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 24867.0, 'new_value': 26165.0}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 24}]
2025-06-10 08:10:42,021 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:42,536 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMNK
2025-06-10 08:10:42,536 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8818.0, 'new_value': 10839.0}, {'field': 'amount', 'old_value': 8818.0, 'new_value': 10839.0}, {'field': 'count', 'old_value': 18, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 8818.0, 'new_value': 10839.0}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 21}]
2025-06-10 08:10:42,536 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:43,036 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMLK
2025-06-10 08:10:43,036 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 165294.0, 'new_value': 180291.0}, {'field': 'dailyBillAmount', 'old_value': 165294.0, 'new_value': 180291.0}, {'field': 'amount', 'old_value': 182041.0, 'new_value': 197038.0}, {'field': 'count', 'old_value': 26, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 182041.0, 'new_value': 197038.0}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 28}]
2025-06-10 08:10:43,052 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:43,489 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMKK
2025-06-10 08:10:43,489 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22297.0, 'new_value': 23531.0}, {'field': 'amount', 'old_value': 22297.0, 'new_value': 23531.0}, {'field': 'count', 'old_value': 55, 'new_value': 59}, {'field': 'instoreAmount', 'old_value': 22297.0, 'new_value': 23531.0}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 59}]
2025-06-10 08:10:43,489 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:44,005 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMFK
2025-06-10 08:10:44,005 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-06, 变更字段: [{'field': 'amount', 'old_value': 78301.0, 'new_value': 89359.0}, {'field': 'count', 'old_value': 74, 'new_value': 81}, {'field': 'instoreAmount', 'old_value': 85122.0, 'new_value': 96180.0}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 81}]
2025-06-10 08:10:44,005 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:44,536 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMJK
2025-06-10 08:10:44,536 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17735.0, 'new_value': 18329.0}, {'field': 'dailyBillAmount', 'old_value': 9732.0, 'new_value': 10326.0}, {'field': 'amount', 'old_value': 16302.0, 'new_value': 16741.0}, {'field': 'count', 'old_value': 23, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 16302.0, 'new_value': 16741.0}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 24}]
2025-06-10 08:10:44,536 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:44,958 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMIK
2025-06-10 08:10:44,958 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19327.0, 'new_value': 20476.0}, {'field': 'dailyBillAmount', 'old_value': 19327.0, 'new_value': 20476.0}, {'field': 'amount', 'old_value': 6753.0, 'new_value': 7041.0}, {'field': 'count', 'old_value': 19, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 6753.0, 'new_value': 7041.0}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 20}]
2025-06-10 08:10:44,974 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:45,474 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMGK
2025-06-10 08:10:45,474 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56711.4, 'new_value': 60582.1}, {'field': 'dailyBillAmount', 'old_value': 56711.4, 'new_value': 60582.1}, {'field': 'amount', 'old_value': 57341.61, 'new_value': 60896.11}, {'field': 'count', 'old_value': 132, 'new_value': 142}, {'field': 'instoreAmount', 'old_value': 58787.51, 'new_value': 62675.61}, {'field': 'instoreCount', 'old_value': 132, 'new_value': 142}]
2025-06-10 08:10:45,474 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:45,927 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMEK
2025-06-10 08:10:45,927 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38455.57, 'new_value': 40340.58}, {'field': 'dailyBillAmount', 'old_value': 38455.57, 'new_value': 40340.58}, {'field': 'amount', 'old_value': 40386.04, 'new_value': 42452.74}, {'field': 'count', 'old_value': 1368, 'new_value': 1451}, {'field': 'instoreAmount', 'old_value': 40339.74, 'new_value': 42387.74}, {'field': 'instoreCount', 'old_value': 1365, 'new_value': 1447}, {'field': 'onlineAmount', 'old_value': 106.2, 'new_value': 124.9}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 4}]
2025-06-10 08:10:45,927 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:46,427 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLS
2025-06-10 08:10:46,427 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 149418.43, 'new_value': 166152.49}, {'field': 'dailyBillAmount', 'old_value': 149418.43, 'new_value': 166152.49}, {'field': 'amount', 'old_value': 143217.28, 'new_value': 152258.04}, {'field': 'count', 'old_value': 642, 'new_value': 680}, {'field': 'instoreAmount', 'old_value': 143217.28, 'new_value': 152258.04}, {'field': 'instoreCount', 'old_value': 642, 'new_value': 680}]
2025-06-10 08:10:46,427 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:46,880 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMDK
2025-06-10 08:10:46,880 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22520.86, 'new_value': 24347.9}, {'field': 'dailyBillAmount', 'old_value': 22520.86, 'new_value': 24347.9}, {'field': 'amount', 'old_value': 26154.25, 'new_value': 28359.71}, {'field': 'count', 'old_value': 910, 'new_value': 1000}, {'field': 'instoreAmount', 'old_value': 9878.49, 'new_value': 10492.35}, {'field': 'instoreCount', 'old_value': 332, 'new_value': 359}, {'field': 'onlineAmount', 'old_value': 16478.16, 'new_value': 18084.76}, {'field': 'onlineCount', 'old_value': 578, 'new_value': 641}]
2025-06-10 08:10:46,880 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:47,333 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMCK
2025-06-10 08:10:47,333 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14911.8, 'new_value': 17941.2}, {'field': 'amount', 'old_value': 14911.8, 'new_value': 17940.8}, {'field': 'count', 'old_value': 11, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 14911.8, 'new_value': 17941.2}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 12}]
2025-06-10 08:10:47,333 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:47,786 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMBK
2025-06-10 08:10:47,786 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 66017.02, 'new_value': 71448.39}, {'field': 'dailyBillAmount', 'old_value': 66017.02, 'new_value': 71448.39}, {'field': 'amount', 'old_value': 54610.37, 'new_value': 60065.33}, {'field': 'count', 'old_value': 1708, 'new_value': 1834}, {'field': 'instoreAmount', 'old_value': 30207.89, 'new_value': 32670.51}, {'field': 'instoreCount', 'old_value': 1259, 'new_value': 1338}, {'field': 'onlineAmount', 'old_value': 27874.1, 'new_value': 30931.3}, {'field': 'onlineCount', 'old_value': 449, 'new_value': 496}]
2025-06-10 08:10:47,786 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:48,271 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMAK
2025-06-10 08:10:48,286 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 57451.92, 'new_value': 61936.5}, {'field': 'dailyBillAmount', 'old_value': 57451.92, 'new_value': 61936.5}, {'field': 'amount', 'old_value': 34334.19, 'new_value': 37716.12}, {'field': 'count', 'old_value': 2491, 'new_value': 2720}, {'field': 'instoreAmount', 'old_value': 2175.5, 'new_value': 2269.5}, {'field': 'instoreCount', 'old_value': 151, 'new_value': 159}, {'field': 'onlineAmount', 'old_value': 33559.57, 'new_value': 36905.03}, {'field': 'onlineCount', 'old_value': 2340, 'new_value': 2561}]
2025-06-10 08:10:48,286 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:48,786 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM9K
2025-06-10 08:10:48,786 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-06, 变更字段: [{'field': 'amount', 'old_value': 53302.85, 'new_value': 59501.75}, {'field': 'count', 'old_value': 2803, 'new_value': 3070}, {'field': 'instoreAmount', 'old_value': 55987.93, 'new_value': 62314.73}, {'field': 'instoreCount', 'old_value': 2790, 'new_value': 3057}]
2025-06-10 08:10:48,786 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:49,177 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM8K
2025-06-10 08:10:49,177 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 39090.87, 'new_value': 47518.7}, {'field': 'amount', 'old_value': 39090.87, 'new_value': 47517.8}, {'field': 'count', 'old_value': 1125, 'new_value': 1186}, {'field': 'instoreAmount', 'old_value': 36984.0, 'new_value': 45241.33}, {'field': 'instoreCount', 'old_value': 1100, 'new_value': 1158}, {'field': 'onlineAmount', 'old_value': 2114.17, 'new_value': 2284.67}, {'field': 'onlineCount', 'old_value': 25, 'new_value': 28}]
2025-06-10 08:10:49,177 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:49,614 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7K
2025-06-10 08:10:49,630 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 49807.32, 'new_value': 56028.01}, {'field': 'dailyBillAmount', 'old_value': 49807.32, 'new_value': 56028.01}, {'field': 'amount', 'old_value': 33407.25, 'new_value': 37568.53}, {'field': 'count', 'old_value': 1601, 'new_value': 1784}, {'field': 'instoreAmount', 'old_value': 6288.2, 'new_value': 6845.62}, {'field': 'instoreCount', 'old_value': 397, 'new_value': 438}, {'field': 'onlineAmount', 'old_value': 27789.51, 'new_value': 31517.41}, {'field': 'onlineCount', 'old_value': 1204, 'new_value': 1346}]
2025-06-10 08:10:49,630 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:50,052 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6K
2025-06-10 08:10:50,052 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50313.79, 'new_value': 54738.52}, {'field': 'dailyBillAmount', 'old_value': 50313.79, 'new_value': 54738.52}, {'field': 'amount', 'old_value': 10028.94, 'new_value': 11346.94}, {'field': 'count', 'old_value': 322, 'new_value': 366}, {'field': 'instoreAmount', 'old_value': 10216.88, 'new_value': 11552.68}, {'field': 'instoreCount', 'old_value': 322, 'new_value': 366}]
2025-06-10 08:10:50,052 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:50,552 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5K
2025-06-10 08:10:50,552 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37960.35, 'new_value': 41033.22}, {'field': 'amount', 'old_value': 37960.35, 'new_value': 41032.82}, {'field': 'count', 'old_value': 2184, 'new_value': 2361}, {'field': 'instoreAmount', 'old_value': 18711.71, 'new_value': 19623.28}, {'field': 'instoreCount', 'old_value': 1266, 'new_value': 1344}, {'field': 'onlineAmount', 'old_value': 19248.64, 'new_value': 21409.94}, {'field': 'onlineCount', 'old_value': 918, 'new_value': 1017}]
2025-06-10 08:10:50,552 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:51,036 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4K
2025-06-10 08:10:51,036 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 88590.03, 'new_value': 94494.02}, {'field': 'dailyBillAmount', 'old_value': 88590.03, 'new_value': 94494.02}, {'field': 'amount', 'old_value': 83011.29, 'new_value': 87913.89}, {'field': 'count', 'old_value': 2504, 'new_value': 2661}, {'field': 'instoreAmount', 'old_value': 83288.29, 'new_value': 88190.89}, {'field': 'instoreCount', 'old_value': 2504, 'new_value': 2661}]
2025-06-10 08:10:51,052 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:51,411 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3K
2025-06-10 08:10:51,411 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38637.4, 'new_value': 44540.41}, {'field': 'dailyBillAmount', 'old_value': 38637.4, 'new_value': 44540.41}, {'field': 'amount', 'old_value': 49843.14, 'new_value': 54593.91}, {'field': 'count', 'old_value': 3256, 'new_value': 3561}, {'field': 'instoreAmount', 'old_value': 36641.69, 'new_value': 40398.51}, {'field': 'instoreCount', 'old_value': 2171, 'new_value': 2393}, {'field': 'onlineAmount', 'old_value': 14154.55, 'new_value': 15355.15}, {'field': 'onlineCount', 'old_value': 1085, 'new_value': 1168}]
2025-06-10 08:10:51,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:51,864 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2K
2025-06-10 08:10:51,864 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-06, 变更字段: [{'field': 'amount', 'old_value': -4031.83, 'new_value': -4276.83}, {'field': 'count', 'old_value': 33, 'new_value': 37}, {'field': 'instoreAmount', 'old_value': 938.0, 'new_value': 953.0}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 10}, {'field': 'onlineAmount', 'old_value': 575.0, 'new_value': 613.0}, {'field': 'onlineCount', 'old_value': 25, 'new_value': 27}]
2025-06-10 08:10:51,864 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:52,349 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1K
2025-06-10 08:10:52,349 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 166242.41, 'new_value': 185594.36}, {'field': 'amount', 'old_value': 166242.41, 'new_value': 185594.36}, {'field': 'count', 'old_value': 3730, 'new_value': 4203}, {'field': 'instoreAmount', 'old_value': 121662.26, 'new_value': 135027.46}, {'field': 'instoreCount', 'old_value': 2507, 'new_value': 2809}, {'field': 'onlineAmount', 'old_value': 44580.15, 'new_value': 50566.9}, {'field': 'onlineCount', 'old_value': 1223, 'new_value': 1394}]
2025-06-10 08:10:52,349 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:52,849 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0K
2025-06-10 08:10:52,849 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 203886.33, 'new_value': 220705.35}, {'field': 'dailyBillAmount', 'old_value': 203886.33, 'new_value': 220705.35}, {'field': 'amount', 'old_value': 182898.08, 'new_value': 191966.48}, {'field': 'count', 'old_value': 3294, 'new_value': 3438}, {'field': 'instoreAmount', 'old_value': 169508.18, 'new_value': 177945.38}, {'field': 'instoreCount', 'old_value': 3019, 'new_value': 3150}, {'field': 'onlineAmount', 'old_value': 14026.9, 'new_value': 14658.1}, {'field': 'onlineCount', 'old_value': 275, 'new_value': 288}]
2025-06-10 08:10:52,849 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:53,364 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3S
2025-06-10 08:10:53,364 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 192057.82, 'new_value': 208197.78}, {'field': 'dailyBillAmount', 'old_value': 192057.82, 'new_value': 208197.78}, {'field': 'amount', 'old_value': 192739.23, 'new_value': 208095.89}, {'field': 'count', 'old_value': 1983, 'new_value': 2145}, {'field': 'instoreAmount', 'old_value': 155090.39, 'new_value': 166611.28}, {'field': 'instoreCount', 'old_value': 783, 'new_value': 835}, {'field': 'onlineAmount', 'old_value': 38689.99, 'new_value': 42625.36}, {'field': 'onlineCount', 'old_value': 1200, 'new_value': 1310}]
2025-06-10 08:10:53,364 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:53,786 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYJ
2025-06-10 08:10:53,786 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 112002.11, 'new_value': 117325.81}, {'field': 'dailyBillAmount', 'old_value': 112002.11, 'new_value': 117325.81}, {'field': 'amount', 'old_value': 55260.84, 'new_value': 58573.21}, {'field': 'count', 'old_value': 1190, 'new_value': 1269}, {'field': 'instoreAmount', 'old_value': 48019.13, 'new_value': 50563.65}, {'field': 'instoreCount', 'old_value': 1039, 'new_value': 1099}, {'field': 'onlineAmount', 'old_value': 7979.71, 'new_value': 8854.26}, {'field': 'onlineCount', 'old_value': 151, 'new_value': 170}]
2025-06-10 08:10:53,786 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:54,208 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWJ
2025-06-10 08:10:54,208 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 66685.01, 'new_value': 69947.48}, {'field': 'amount', 'old_value': 66685.01, 'new_value': 69947.48}, {'field': 'count', 'old_value': 722, 'new_value': 766}, {'field': 'instoreAmount', 'old_value': 46677.44, 'new_value': 49108.62}, {'field': 'instoreCount', 'old_value': 441, 'new_value': 468}, {'field': 'onlineAmount', 'old_value': 21660.25, 'new_value': 22491.54}, {'field': 'onlineCount', 'old_value': 281, 'new_value': 298}]
2025-06-10 08:10:54,208 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:54,817 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZJ
2025-06-10 08:10:54,817 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 91497.8, 'new_value': 96361.5}, {'field': 'dailyBillAmount', 'old_value': 135300.1, 'new_value': 146516.5}, {'field': 'amount', 'old_value': 91497.8, 'new_value': 96361.5}, {'field': 'count', 'old_value': 347, 'new_value': 368}, {'field': 'instoreAmount', 'old_value': 93403.5, 'new_value': 98437.1}, {'field': 'instoreCount', 'old_value': 347, 'new_value': 368}]
2025-06-10 08:10:54,817 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:55,271 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWR
2025-06-10 08:10:55,271 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9865.2, 'new_value': 10711.7}, {'field': 'amount', 'old_value': 9865.2, 'new_value': 10711.7}, {'field': 'count', 'old_value': 32, 'new_value': 35}, {'field': 'instoreAmount', 'old_value': 9865.2, 'new_value': 10711.7}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 35}]
2025-06-10 08:10:55,271 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:55,739 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUR
2025-06-10 08:10:55,739 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21299.0, 'new_value': 25288.0}, {'field': 'amount', 'old_value': 21299.0, 'new_value': 25288.0}, {'field': 'count', 'old_value': 32, 'new_value': 35}, {'field': 'instoreAmount', 'old_value': 22097.0, 'new_value': 26086.0}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 35}]
2025-06-10 08:10:55,739 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:56,224 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTR
2025-06-10 08:10:56,224 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 368084.67, 'new_value': 378699.03}, {'field': 'dailyBillAmount', 'old_value': 368084.67, 'new_value': 378699.03}, {'field': 'amount', 'old_value': 22856.44, 'new_value': 24552.97}, {'field': 'count', 'old_value': 237, 'new_value': 259}, {'field': 'instoreAmount', 'old_value': 18323.73, 'new_value': 19517.72}, {'field': 'instoreCount', 'old_value': 173, 'new_value': 188}, {'field': 'onlineAmount', 'old_value': 4651.3, 'new_value': 5154.08}, {'field': 'onlineCount', 'old_value': 64, 'new_value': 71}]
2025-06-10 08:10:56,224 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:56,661 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSR
2025-06-10 08:10:56,661 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2779.0, 'new_value': 3066.0}, {'field': 'dailyBillAmount', 'old_value': 2779.0, 'new_value': 3066.0}, {'field': 'amount', 'old_value': 16290.0, 'new_value': 16863.0}, {'field': 'count', 'old_value': 42, 'new_value': 44}, {'field': 'instoreAmount', 'old_value': 16488.0, 'new_value': 17061.0}, {'field': 'instoreCount', 'old_value': 42, 'new_value': 44}]
2025-06-10 08:10:56,661 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:57,099 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRR
2025-06-10 08:10:57,099 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10875.7, 'new_value': 12139.7}, {'field': 'amount', 'old_value': 10875.7, 'new_value': 12139.7}, {'field': 'count', 'old_value': 64, 'new_value': 73}, {'field': 'instoreAmount', 'old_value': 10983.7, 'new_value': 12247.7}, {'field': 'instoreCount', 'old_value': 64, 'new_value': 73}]
2025-06-10 08:10:57,099 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:57,614 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMSK
2025-06-10 08:10:57,614 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7060.0, 'new_value': 8132.0}, {'field': 'dailyBillAmount', 'old_value': 7060.0, 'new_value': 8132.0}, {'field': 'amount', 'old_value': 6587.0, 'new_value': 8127.0}, {'field': 'count', 'old_value': 30, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 7117.0, 'new_value': 8657.0}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 36}]
2025-06-10 08:10:57,614 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:58,052 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMQK
2025-06-10 08:10:58,052 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17880.07, 'new_value': 20595.57}, {'field': 'dailyBillAmount', 'old_value': 17880.07, 'new_value': 20595.57}, {'field': 'amount', 'old_value': 16557.13, 'new_value': 18854.13}, {'field': 'count', 'old_value': 172, 'new_value': 191}, {'field': 'instoreAmount', 'old_value': 14711.7, 'new_value': 17048.2}, {'field': 'instoreCount', 'old_value': 149, 'new_value': 166}, {'field': 'onlineAmount', 'old_value': 1846.93, 'new_value': 1946.43}, {'field': 'onlineCount', 'old_value': 23, 'new_value': 25}]
2025-06-10 08:10:58,052 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:58,505 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXJ
2025-06-10 08:10:58,505 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 113943.46, 'new_value': 125956.79}, {'field': 'dailyBillAmount', 'old_value': 113943.46, 'new_value': 125956.79}, {'field': 'amount', 'old_value': 9679.45, 'new_value': 10249.07}, {'field': 'count', 'old_value': 274, 'new_value': 295}, {'field': 'instoreAmount', 'old_value': 10441.82, 'new_value': 11253.64}, {'field': 'instoreCount', 'old_value': 274, 'new_value': 295}]
2025-06-10 08:10:58,505 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:58,927 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMPK
2025-06-10 08:10:58,927 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 85769.1, 'new_value': 93674.6}, {'field': 'dailyBillAmount', 'old_value': 85769.1, 'new_value': 93674.6}, {'field': 'amount', 'old_value': 84935.4, 'new_value': 92840.9}, {'field': 'count', 'old_value': 107, 'new_value': 118}, {'field': 'instoreAmount', 'old_value': 85912.4, 'new_value': 93817.9}, {'field': 'instoreCount', 'old_value': 107, 'new_value': 118}]
2025-06-10 08:10:58,927 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:59,442 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXR
2025-06-10 08:10:59,442 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11202.0, 'new_value': 13949.0}, {'field': 'dailyBillAmount', 'old_value': 11202.0, 'new_value': 13949.0}, {'field': 'amount', 'old_value': 11273.0, 'new_value': 14031.0}, {'field': 'count', 'old_value': 27, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 12908.0, 'new_value': 15666.0}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 31}]
2025-06-10 08:10:59,442 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:10:59,896 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYR
2025-06-10 08:10:59,896 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 87267.65, 'new_value': 93087.87}, {'field': 'dailyBillAmount', 'old_value': 77500.05, 'new_value': 83320.27}, {'field': 'amount', 'old_value': 84869.64, 'new_value': 89903.86}, {'field': 'count', 'old_value': 464, 'new_value': 477}, {'field': 'instoreAmount', 'old_value': 85867.64, 'new_value': 90901.86}, {'field': 'instoreCount', 'old_value': 464, 'new_value': 477}]
2025-06-10 08:10:59,896 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:00,333 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1S
2025-06-10 08:11:00,333 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 90296.69, 'new_value': 94701.87}, {'field': 'dailyBillAmount', 'old_value': 90296.69, 'new_value': 94701.87}, {'field': 'amount', 'old_value': 89779.69, 'new_value': 94106.87}, {'field': 'count', 'old_value': 512, 'new_value': 539}, {'field': 'instoreAmount', 'old_value': 89779.69, 'new_value': 94106.87}, {'field': 'instoreCount', 'old_value': 512, 'new_value': 539}]
2025-06-10 08:11:00,333 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:00,708 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2S
2025-06-10 08:11:00,708 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29440.22, 'new_value': 32325.41}, {'field': 'dailyBillAmount', 'old_value': 29440.22, 'new_value': 32325.41}, {'field': 'amount', 'old_value': 15662.54, 'new_value': 16584.59}, {'field': 'count', 'old_value': 1417, 'new_value': 1523}, {'field': 'instoreAmount', 'old_value': 16047.51, 'new_value': 17011.46}, {'field': 'instoreCount', 'old_value': 1417, 'new_value': 1523}]
2025-06-10 08:11:00,708 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:01,130 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4S
2025-06-10 08:11:01,130 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 70964.0, 'new_value': 74021.3}, {'field': 'amount', 'old_value': 70964.0, 'new_value': 74021.3}, {'field': 'count', 'old_value': 446, 'new_value': 467}, {'field': 'instoreAmount', 'old_value': 70964.0, 'new_value': 74021.3}, {'field': 'instoreCount', 'old_value': 446, 'new_value': 467}]
2025-06-10 08:11:01,130 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:01,614 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5S
2025-06-10 08:11:01,614 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-06, 变更字段: [{'field': 'amount', 'old_value': 48900.57, 'new_value': 56295.47}, {'field': 'count', 'old_value': 1940, 'new_value': 2276}, {'field': 'instoreAmount', 'old_value': 16026.65, 'new_value': 18379.45}, {'field': 'instoreCount', 'old_value': 622, 'new_value': 744}, {'field': 'onlineAmount', 'old_value': 33687.82, 'new_value': 38784.2}, {'field': 'onlineCount', 'old_value': 1318, 'new_value': 1532}]
2025-06-10 08:11:01,614 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:02,067 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6S
2025-06-10 08:11:02,067 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25743.6, 'new_value': 28290.91}, {'field': 'dailyBillAmount', 'old_value': 25743.6, 'new_value': 28290.91}, {'field': 'amount', 'old_value': 38615.63, 'new_value': 43029.92}, {'field': 'count', 'old_value': 1979, 'new_value': 2180}, {'field': 'instoreAmount', 'old_value': 21201.76, 'new_value': 23321.75}, {'field': 'instoreCount', 'old_value': 1184, 'new_value': 1309}, {'field': 'onlineAmount', 'old_value': 18097.57, 'new_value': 20428.87}, {'field': 'onlineCount', 'old_value': 795, 'new_value': 871}]
2025-06-10 08:11:02,083 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:02,489 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8S
2025-06-10 08:11:02,489 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21016.0, 'new_value': 21614.0}, {'field': 'amount', 'old_value': 21016.0, 'new_value': 21614.0}, {'field': 'count', 'old_value': 16, 'new_value': 17}, {'field': 'instoreAmount', 'old_value': 21016.0, 'new_value': 21614.0}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 17}]
2025-06-10 08:11:02,489 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:02,864 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9S
2025-06-10 08:11:02,864 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38928.82, 'new_value': 41722.45}, {'field': 'dailyBillAmount', 'old_value': 38928.82, 'new_value': 41722.45}, {'field': 'amount', 'old_value': 20024.26, 'new_value': 21906.37}, {'field': 'count', 'old_value': 1409, 'new_value': 1568}, {'field': 'instoreAmount', 'old_value': 3734.8, 'new_value': 3804.8}, {'field': 'instoreCount', 'old_value': 154, 'new_value': 163}, {'field': 'onlineAmount', 'old_value': 16289.46, 'new_value': 18101.57}, {'field': 'onlineCount', 'old_value': 1255, 'new_value': 1405}]
2025-06-10 08:11:02,864 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:03,364 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAS
2025-06-10 08:11:03,364 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-06, 变更字段: [{'field': 'amount', 'old_value': 98614.17, 'new_value': 106164.4}, {'field': 'count', 'old_value': 861, 'new_value': 952}, {'field': 'instoreAmount', 'old_value': 80981.87, 'new_value': 87051.79}, {'field': 'instoreCount', 'old_value': 520, 'new_value': 578}, {'field': 'onlineAmount', 'old_value': 17632.3, 'new_value': 19112.61}, {'field': 'onlineCount', 'old_value': 341, 'new_value': 374}]
2025-06-10 08:11:03,364 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:03,849 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBS
2025-06-10 08:11:03,849 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 136307.78, 'new_value': 144704.16}, {'field': 'dailyBillAmount', 'old_value': 136307.78, 'new_value': 144704.16}, {'field': 'amount', 'old_value': 122370.76, 'new_value': 128759.16}, {'field': 'count', 'old_value': 689, 'new_value': 732}, {'field': 'instoreAmount', 'old_value': 124734.76, 'new_value': 131123.16}, {'field': 'instoreCount', 'old_value': 689, 'new_value': 732}]
2025-06-10 08:11:03,849 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:04,364 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCS
2025-06-10 08:11:04,380 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 322930.86, 'new_value': 354968.09}, {'field': 'dailyBillAmount', 'old_value': 322930.86, 'new_value': 354968.09}, {'field': 'amount', 'old_value': 353614.46, 'new_value': 389800.7}, {'field': 'count', 'old_value': 2020, 'new_value': 2224}, {'field': 'instoreAmount', 'old_value': 268457.36, 'new_value': 294298.3}, {'field': 'instoreCount', 'old_value': 1076, 'new_value': 1173}, {'field': 'onlineAmount', 'old_value': 86830.66, 'new_value': 97458.86}, {'field': 'onlineCount', 'old_value': 944, 'new_value': 1051}]
2025-06-10 08:11:04,380 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:04,802 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDS
2025-06-10 08:11:04,802 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 111189.44, 'new_value': 117886.13}, {'field': 'dailyBillAmount', 'old_value': 111189.44, 'new_value': 117886.13}, {'field': 'amount', 'old_value': 157502.45, 'new_value': 167053.73}, {'field': 'count', 'old_value': 764, 'new_value': 826}, {'field': 'instoreAmount', 'old_value': 148224.76, 'new_value': 156765.8}, {'field': 'instoreCount', 'old_value': 599, 'new_value': 633}, {'field': 'onlineAmount', 'old_value': 9546.24, 'new_value': 10581.78}, {'field': 'onlineCount', 'old_value': 165, 'new_value': 193}]
2025-06-10 08:11:04,817 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:05,380 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMES
2025-06-10 08:11:05,380 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 134923.28, 'new_value': 143348.14}, {'field': 'dailyBillAmount', 'old_value': 134923.28, 'new_value': 143348.14}, {'field': 'amount', 'old_value': 128421.6, 'new_value': 136697.8}, {'field': 'count', 'old_value': 562, 'new_value': 606}, {'field': 'instoreAmount', 'old_value': 130291.4, 'new_value': 138567.6}, {'field': 'instoreCount', 'old_value': 562, 'new_value': 606}]
2025-06-10 08:11:05,380 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:05,911 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFS
2025-06-10 08:11:05,911 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 279436.34, 'new_value': 299160.47}, {'field': 'amount', 'old_value': 279436.34, 'new_value': 299160.47}, {'field': 'count', 'old_value': 2115, 'new_value': 2300}, {'field': 'instoreAmount', 'old_value': 279436.34, 'new_value': 299160.47}, {'field': 'instoreCount', 'old_value': 2115, 'new_value': 2300}]
2025-06-10 08:11:05,911 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:06,302 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGS
2025-06-10 08:11:06,302 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 262405.26, 'new_value': 281579.79}, {'field': 'dailyBillAmount', 'old_value': 262405.26, 'new_value': 281579.79}, {'field': 'amount', 'old_value': 300918.85, 'new_value': 324349.22}, {'field': 'count', 'old_value': 2074, 'new_value': 2263}, {'field': 'instoreAmount', 'old_value': 173458.4, 'new_value': 186445.5}, {'field': 'instoreCount', 'old_value': 934, 'new_value': 1015}, {'field': 'onlineAmount', 'old_value': 131733.3, 'new_value': 142442.9}, {'field': 'onlineCount', 'old_value': 1140, 'new_value': 1248}]
2025-06-10 08:11:06,317 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:06,771 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHS
2025-06-10 08:11:06,771 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 167158.67, 'new_value': 179016.14}, {'field': 'dailyBillAmount', 'old_value': 167158.67, 'new_value': 179016.14}, {'field': 'amount', 'old_value': 166566.41, 'new_value': 178173.25}, {'field': 'count', 'old_value': 1635, 'new_value': 1796}, {'field': 'instoreAmount', 'old_value': 125108.96, 'new_value': 131017.66}, {'field': 'instoreCount', 'old_value': 833, 'new_value': 887}, {'field': 'onlineAmount', 'old_value': 41872.92, 'new_value': 47656.21}, {'field': 'onlineCount', 'old_value': 802, 'new_value': 909}]
2025-06-10 08:11:06,771 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:07,192 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIS
2025-06-10 08:11:07,192 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 184180.23, 'new_value': 199250.12}, {'field': 'dailyBillAmount', 'old_value': 184180.23, 'new_value': 199250.12}, {'field': 'amount', 'old_value': 185175.28, 'new_value': 200355.34}, {'field': 'count', 'old_value': 1479, 'new_value': 1625}, {'field': 'instoreAmount', 'old_value': 168294.9, 'new_value': 181504.84}, {'field': 'instoreCount', 'old_value': 885, 'new_value': 959}, {'field': 'onlineAmount', 'old_value': 17006.46, 'new_value': 18999.78}, {'field': 'onlineCount', 'old_value': 594, 'new_value': 666}]
2025-06-10 08:11:07,208 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:07,661 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJS
2025-06-10 08:11:07,661 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 46324.4, 'new_value': 53702.4}, {'field': 'amount', 'old_value': 46324.4, 'new_value': 53702.4}, {'field': 'count', 'old_value': 201, 'new_value': 225}, {'field': 'instoreAmount', 'old_value': 46324.4, 'new_value': 53702.4}, {'field': 'instoreCount', 'old_value': 201, 'new_value': 225}]
2025-06-10 08:11:07,661 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:08,099 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKS
2025-06-10 08:11:08,099 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 130115.24, 'new_value': 154272.62}, {'field': 'dailyBillAmount', 'old_value': 130115.24, 'new_value': 154272.62}, {'field': 'amount', 'old_value': -100454.16, 'new_value': -106093.78}, {'field': 'count', 'old_value': 297, 'new_value': 309}, {'field': 'instoreAmount', 'old_value': 2478.0, 'new_value': 2496.0}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 110}, {'field': 'onlineAmount', 'old_value': 4478.64, 'new_value': 4736.02}, {'field': 'onlineCount', 'old_value': 188, 'new_value': 199}]
2025-06-10 08:11:08,099 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:08,536 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMS
2025-06-10 08:11:08,536 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 171854.15, 'new_value': 185881.26}, {'field': 'dailyBillAmount', 'old_value': 171854.15, 'new_value': 185881.26}, {'field': 'amount', 'old_value': 50206.9, 'new_value': 53570.0}, {'field': 'count', 'old_value': 216, 'new_value': 225}, {'field': 'instoreAmount', 'old_value': 50375.6, 'new_value': 53738.7}, {'field': 'instoreCount', 'old_value': 206, 'new_value': 215}]
2025-06-10 08:11:08,536 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:09,099 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNS
2025-06-10 08:11:09,099 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 119796.73, 'new_value': 128253.49}, {'field': 'dailyBillAmount', 'old_value': 119796.73, 'new_value': 128253.49}, {'field': 'amount', 'old_value': 117594.33, 'new_value': 125584.6}, {'field': 'count', 'old_value': 702, 'new_value': 744}, {'field': 'instoreAmount', 'old_value': 112804.53, 'new_value': 120567.51}, {'field': 'instoreCount', 'old_value': 570, 'new_value': 605}, {'field': 'onlineAmount', 'old_value': 4847.86, 'new_value': 5075.15}, {'field': 'onlineCount', 'old_value': 132, 'new_value': 139}]
2025-06-10 08:11:09,099 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:09,521 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOS
2025-06-10 08:11:09,521 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 118240.79, 'new_value': 127778.48}, {'field': 'dailyBillAmount', 'old_value': 118240.79, 'new_value': 127778.48}, {'field': 'amount', 'old_value': 50810.84, 'new_value': 54973.52}, {'field': 'count', 'old_value': 678, 'new_value': 751}, {'field': 'instoreAmount', 'old_value': 35087.24, 'new_value': 37938.04}, {'field': 'instoreCount', 'old_value': 199, 'new_value': 211}, {'field': 'onlineAmount', 'old_value': 15723.6, 'new_value': 17036.41}, {'field': 'onlineCount', 'old_value': 479, 'new_value': 540}]
2025-06-10 08:11:09,536 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:09,974 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPS
2025-06-10 08:11:09,974 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 47276.86, 'new_value': 52196.66}, {'field': 'amount', 'old_value': 47276.45, 'new_value': 52195.82}, {'field': 'count', 'old_value': 2215, 'new_value': 2452}, {'field': 'instoreAmount', 'old_value': 12613.42, 'new_value': 13964.11}, {'field': 'instoreCount', 'old_value': 522, 'new_value': 580}, {'field': 'onlineAmount', 'old_value': 35613.51, 'new_value': 39396.72}, {'field': 'onlineCount', 'old_value': 1693, 'new_value': 1872}]
2025-06-10 08:11:09,989 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:10,458 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQS
2025-06-10 08:11:10,458 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16222.0, 'new_value': 16370.0}, {'field': 'amount', 'old_value': 16222.0, 'new_value': 16370.0}, {'field': 'count', 'old_value': 66, 'new_value': 67}, {'field': 'instoreAmount', 'old_value': 16222.0, 'new_value': 16370.0}, {'field': 'instoreCount', 'old_value': 66, 'new_value': 67}]
2025-06-10 08:11:10,458 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:10,958 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRS
2025-06-10 08:11:10,958 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 137099.43, 'new_value': 147742.83}, {'field': 'dailyBillAmount', 'old_value': 137099.43, 'new_value': 147742.83}, {'field': 'amount', 'old_value': 54727.99, 'new_value': 59396.49}, {'field': 'count', 'old_value': 1052, 'new_value': 1149}, {'field': 'instoreAmount', 'old_value': 55125.8, 'new_value': 59795.3}, {'field': 'instoreCount', 'old_value': 1052, 'new_value': 1149}]
2025-06-10 08:11:10,958 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:11,364 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTS
2025-06-10 08:11:11,364 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14321.39, 'new_value': 15532.65}, {'field': 'amount', 'old_value': 14321.39, 'new_value': 15532.51}, {'field': 'count', 'old_value': 861, 'new_value': 941}, {'field': 'instoreAmount', 'old_value': 5939.88, 'new_value': 6410.38}, {'field': 'instoreCount', 'old_value': 304, 'new_value': 326}, {'field': 'onlineAmount', 'old_value': 8628.66, 'new_value': 9382.82}, {'field': 'onlineCount', 'old_value': 557, 'new_value': 615}]
2025-06-10 08:11:11,364 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:11,802 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUS
2025-06-10 08:11:11,802 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18318.7, 'new_value': 19130.7}, {'field': 'amount', 'old_value': 18318.7, 'new_value': 19130.7}, {'field': 'count', 'old_value': 53, 'new_value': 56}, {'field': 'instoreAmount', 'old_value': 18318.7, 'new_value': 19130.7}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 56}]
2025-06-10 08:11:11,802 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:12,286 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZR
2025-06-10 08:11:12,286 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 49043.0, 'new_value': 49101.0}, {'field': 'amount', 'old_value': 49043.0, 'new_value': 49101.0}, {'field': 'count', 'old_value': 279, 'new_value': 280}, {'field': 'instoreAmount', 'old_value': 53103.0, 'new_value': 53161.0}, {'field': 'instoreCount', 'old_value': 279, 'new_value': 280}]
2025-06-10 08:11:12,317 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:12,786 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZS
2025-06-10 08:11:12,786 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 43141.83, 'new_value': 48603.53}, {'field': 'dailyBillAmount', 'old_value': 43139.54, 'new_value': 48612.17}, {'field': 'amount', 'old_value': 43141.83, 'new_value': 48602.81}, {'field': 'count', 'old_value': 2388, 'new_value': 2705}, {'field': 'instoreAmount', 'old_value': 21613.21, 'new_value': 24104.36}, {'field': 'instoreCount', 'old_value': 1138, 'new_value': 1284}, {'field': 'onlineAmount', 'old_value': 21798.27, 'new_value': 24817.61}, {'field': 'onlineCount', 'old_value': 1250, 'new_value': 1421}]
2025-06-10 08:11:12,786 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:13,317 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0T
2025-06-10 08:11:13,317 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23727.32, 'new_value': 26361.2}, {'field': 'amount', 'old_value': 23727.32, 'new_value': 26361.2}, {'field': 'count', 'old_value': 1486, 'new_value': 1648}, {'field': 'instoreAmount', 'old_value': 13135.97, 'new_value': 14527.52}, {'field': 'instoreCount', 'old_value': 729, 'new_value': 798}, {'field': 'onlineAmount', 'old_value': 11890.59, 'new_value': 13184.72}, {'field': 'onlineCount', 'old_value': 757, 'new_value': 850}]
2025-06-10 08:11:13,317 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:13,786 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1T
2025-06-10 08:11:13,786 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-06, 变更字段: [{'field': 'amount', 'old_value': 64230.86, 'new_value': 68816.52}, {'field': 'count', 'old_value': 631, 'new_value': 690}, {'field': 'instoreAmount', 'old_value': 64256.06, 'new_value': 68841.72}, {'field': 'instoreCount', 'old_value': 631, 'new_value': 690}]
2025-06-10 08:11:13,786 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:14,333 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2T
2025-06-10 08:11:14,333 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35008.92, 'new_value': 37952.88}, {'field': 'dailyBillAmount', 'old_value': 36400.47, 'new_value': 39648.03}, {'field': 'amount', 'old_value': 35008.92, 'new_value': 37952.88}, {'field': 'count', 'old_value': 914, 'new_value': 991}, {'field': 'instoreAmount', 'old_value': 32022.39, 'new_value': 34832.67}, {'field': 'instoreCount', 'old_value': 669, 'new_value': 735}, {'field': 'onlineAmount', 'old_value': 3016.83, 'new_value': 3150.51}, {'field': 'onlineCount', 'old_value': 245, 'new_value': 256}]
2025-06-10 08:11:14,333 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:14,817 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVS
2025-06-10 08:11:14,817 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 58941.72, 'new_value': 62758.33}, {'field': 'dailyBillAmount', 'old_value': 48053.1, 'new_value': 51042.5}, {'field': 'amount', 'old_value': 58941.72, 'new_value': 62758.33}, {'field': 'count', 'old_value': 865, 'new_value': 938}, {'field': 'instoreAmount', 'old_value': 55931.1, 'new_value': 59274.5}, {'field': 'instoreCount', 'old_value': 726, 'new_value': 778}, {'field': 'onlineAmount', 'old_value': 3134.62, 'new_value': 3607.83}, {'field': 'onlineCount', 'old_value': 139, 'new_value': 160}]
2025-06-10 08:11:14,817 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:15,239 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWS
2025-06-10 08:11:15,239 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10804.28, 'new_value': 11523.88}, {'field': 'amount', 'old_value': 10804.28, 'new_value': 11523.88}, {'field': 'count', 'old_value': 477, 'new_value': 512}, {'field': 'instoreAmount', 'old_value': 9532.48, 'new_value': 10138.08}, {'field': 'instoreCount', 'old_value': 436, 'new_value': 467}, {'field': 'onlineAmount', 'old_value': 1306.2, 'new_value': 1420.2}, {'field': 'onlineCount', 'old_value': 41, 'new_value': 45}]
2025-06-10 08:11:15,239 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:15,692 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXS
2025-06-10 08:11:15,692 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 136111.7, 'new_value': 143692.2}, {'field': 'dailyBillAmount', 'old_value': 136111.7, 'new_value': 143692.2}, {'field': 'amount', 'old_value': 176544.51, 'new_value': 187904.21}, {'field': 'count', 'old_value': 1549, 'new_value': 1686}, {'field': 'instoreAmount', 'old_value': 167817.84, 'new_value': 177864.5}, {'field': 'instoreCount', 'old_value': 1066, 'new_value': 1144}, {'field': 'onlineAmount', 'old_value': 12216.09, 'new_value': 13596.03}, {'field': 'onlineCount', 'old_value': 483, 'new_value': 542}]
2025-06-10 08:11:15,692 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:16,177 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYS
2025-06-10 08:11:16,177 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37942.83, 'new_value': 45449.53}, {'field': 'dailyBillAmount', 'old_value': 37942.83, 'new_value': 45449.53}, {'field': 'amount', 'old_value': 14165.56, 'new_value': 14990.1}, {'field': 'count', 'old_value': 223, 'new_value': 244}, {'field': 'instoreAmount', 'old_value': 10016.78, 'new_value': 10412.53}, {'field': 'instoreCount', 'old_value': 140, 'new_value': 151}, {'field': 'onlineAmount', 'old_value': 4278.16, 'new_value': 4706.95}, {'field': 'onlineCount', 'old_value': 83, 'new_value': 93}]
2025-06-10 08:11:16,239 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:16,692 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGT
2025-06-10 08:11:16,692 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 272818.43, 'new_value': 288637.16}, {'field': 'dailyBillAmount', 'old_value': 272818.43, 'new_value': 288637.16}, {'field': 'amount', 'old_value': 197368.3, 'new_value': 207023.9}, {'field': 'count', 'old_value': 1343, 'new_value': 1421}, {'field': 'instoreAmount', 'old_value': 142582.4, 'new_value': 147969.2}, {'field': 'instoreCount', 'old_value': 1084, 'new_value': 1143}, {'field': 'onlineAmount', 'old_value': 54785.9, 'new_value': 59054.7}, {'field': 'onlineCount', 'old_value': 259, 'new_value': 278}]
2025-06-10 08:11:16,708 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:17,161 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHT
2025-06-10 08:11:17,161 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 339327.66, 'new_value': 372453.03}, {'field': 'amount', 'old_value': 339327.66, 'new_value': 372453.03}, {'field': 'count', 'old_value': 1176, 'new_value': 1288}, {'field': 'instoreAmount', 'old_value': 339184.66, 'new_value': 372310.03}, {'field': 'instoreCount', 'old_value': 1175, 'new_value': 1287}]
2025-06-10 08:11:17,161 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:17,552 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIT
2025-06-10 08:11:17,552 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 166751.79, 'new_value': 190718.54}, {'field': 'dailyBillAmount', 'old_value': 149594.64, 'new_value': 169638.14}, {'field': 'amount', 'old_value': 166751.79, 'new_value': 190718.54}, {'field': 'count', 'old_value': 1060, 'new_value': 1229}, {'field': 'instoreAmount', 'old_value': 152210.48, 'new_value': 173732.21}, {'field': 'instoreCount', 'old_value': 658, 'new_value': 739}, {'field': 'onlineAmount', 'old_value': 14665.9, 'new_value': 17158.66}, {'field': 'onlineCount', 'old_value': 402, 'new_value': 490}]
2025-06-10 08:11:17,552 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:18,036 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJT
2025-06-10 08:11:18,036 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 124467.82, 'new_value': 137378.37}, {'field': 'dailyBillAmount', 'old_value': 110549.43, 'new_value': 123309.98}, {'field': 'amount', 'old_value': 124467.82, 'new_value': 137378.37}, {'field': 'count', 'old_value': 388, 'new_value': 432}, {'field': 'instoreAmount', 'old_value': 113029.3, 'new_value': 124558.7}, {'field': 'instoreCount', 'old_value': 294, 'new_value': 324}, {'field': 'onlineAmount', 'old_value': 11605.35, 'new_value': 12986.5}, {'field': 'onlineCount', 'old_value': 94, 'new_value': 108}]
2025-06-10 08:11:18,036 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:18,458 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3T
2025-06-10 08:11:18,458 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 62025.76, 'new_value': 72852.0}, {'field': 'dailyBillAmount', 'old_value': 62025.76, 'new_value': 72852.0}, {'field': 'amount', 'old_value': 8728.67, 'new_value': 9839.09}, {'field': 'count', 'old_value': 294, 'new_value': 339}, {'field': 'instoreAmount', 'old_value': 9487.8, 'new_value': 10840.12}, {'field': 'instoreCount', 'old_value': 294, 'new_value': 339}]
2025-06-10 08:11:18,458 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:18,864 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAT
2025-06-10 08:11:18,864 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34212.98, 'new_value': 38242.65}, {'field': 'dailyBillAmount', 'old_value': 17380.37, 'new_value': 18965.27}, {'field': 'amount', 'old_value': 34212.98, 'new_value': 38242.65}, {'field': 'count', 'old_value': 869, 'new_value': 973}, {'field': 'instoreAmount', 'old_value': 18960.8, 'new_value': 20489.7}, {'field': 'instoreCount', 'old_value': 477, 'new_value': 518}, {'field': 'onlineAmount', 'old_value': 15921.18, 'new_value': 18421.95}, {'field': 'onlineCount', 'old_value': 392, 'new_value': 455}]
2025-06-10 08:11:18,864 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:19,380 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9T
2025-06-10 08:11:19,380 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-06, 变更字段: [{'field': 'amount', 'old_value': 1935.98, 'new_value': 2224.83}, {'field': 'count', 'old_value': 93, 'new_value': 107}, {'field': 'onlineAmount', 'old_value': 1990.05, 'new_value': 2278.9}, {'field': 'onlineCount', 'old_value': 93, 'new_value': 107}]
2025-06-10 08:11:19,380 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:19,755 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8T
2025-06-10 08:11:19,755 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31815.95, 'new_value': 40283.97}, {'field': 'dailyBillAmount', 'old_value': 31815.95, 'new_value': 40283.97}, {'field': 'amount', 'old_value': 1808.15, 'new_value': 2268.03}, {'field': 'count', 'old_value': 69, 'new_value': 87}, {'field': 'instoreAmount', 'old_value': 1808.15, 'new_value': 2268.03}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 87}]
2025-06-10 08:11:19,755 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:20,333 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMT
2025-06-10 08:11:20,333 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 47285.8, 'new_value': 53805.8}, {'field': 'dailyBillAmount', 'old_value': 47285.8, 'new_value': 53805.8}, {'field': 'amount', 'old_value': 46213.0, 'new_value': 52571.0}, {'field': 'count', 'old_value': 83, 'new_value': 92}, {'field': 'instoreAmount', 'old_value': 46213.0, 'new_value': 52571.0}, {'field': 'instoreCount', 'old_value': 83, 'new_value': 92}]
2025-06-10 08:11:20,333 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:20,770 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7T
2025-06-10 08:11:20,770 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56565.96, 'new_value': 64346.94}, {'field': 'dailyBillAmount', 'old_value': 52948.76, 'new_value': 60729.74}, {'field': 'amount', 'old_value': 39642.69, 'new_value': 44649.5}, {'field': 'count', 'old_value': 1226, 'new_value': 1398}, {'field': 'instoreAmount', 'old_value': 8292.34, 'new_value': 9237.59}, {'field': 'instoreCount', 'old_value': 188, 'new_value': 210}, {'field': 'onlineAmount', 'old_value': 31525.48, 'new_value': 35646.84}, {'field': 'onlineCount', 'old_value': 1038, 'new_value': 1188}]
2025-06-10 08:11:20,770 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:21,177 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6T
2025-06-10 08:11:21,177 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 112307.34, 'new_value': 124555.76}, {'field': 'amount', 'old_value': 112307.34, 'new_value': 124555.76}, {'field': 'count', 'old_value': 1145, 'new_value': 1317}, {'field': 'instoreAmount', 'old_value': 105436.2, 'new_value': 116399.3}, {'field': 'instoreCount', 'old_value': 914, 'new_value': 1041}, {'field': 'onlineAmount', 'old_value': 7938.65, 'new_value': 9293.37}, {'field': 'onlineCount', 'old_value': 231, 'new_value': 276}]
2025-06-10 08:11:21,177 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:21,567 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5T
2025-06-10 08:11:21,567 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 5795.84, 'new_value': 6796.13}, {'field': 'count', 'old_value': 292, 'new_value': 350}, {'field': 'onlineAmount', 'old_value': 5854.47, 'new_value': 6854.76}, {'field': 'onlineCount', 'old_value': 292, 'new_value': 350}]
2025-06-10 08:11:21,567 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:22,005 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQT
2025-06-10 08:11:22,005 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 115482.86, 'new_value': 127085.16}, {'field': 'dailyBillAmount', 'old_value': 115482.86, 'new_value': 127085.16}, {'field': 'amount', 'old_value': 118963.32, 'new_value': 130460.27}, {'field': 'count', 'old_value': 3314, 'new_value': 3669}, {'field': 'instoreAmount', 'old_value': 110850.91, 'new_value': 121547.11}, {'field': 'instoreCount', 'old_value': 2854, 'new_value': 3160}, {'field': 'onlineAmount', 'old_value': 9958.68, 'new_value': 11003.43}, {'field': 'onlineCount', 'old_value': 460, 'new_value': 509}]
2025-06-10 08:11:22,005 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:22,474 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPT
2025-06-10 08:11:22,474 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24458.83, 'new_value': 26826.4}, {'field': 'amount', 'old_value': 24458.83, 'new_value': 26826.4}, {'field': 'count', 'old_value': 230, 'new_value': 255}, {'field': 'instoreAmount', 'old_value': 24774.99, 'new_value': 27142.56}, {'field': 'instoreCount', 'old_value': 230, 'new_value': 255}]
2025-06-10 08:11:22,474 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:22,849 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOT
2025-06-10 08:11:22,849 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6980.24, 'new_value': 7296.84}, {'field': 'amount', 'old_value': 6980.24, 'new_value': 7296.84}, {'field': 'count', 'old_value': 146, 'new_value': 156}, {'field': 'instoreAmount', 'old_value': 6990.02, 'new_value': 7306.62}, {'field': 'instoreCount', 'old_value': 146, 'new_value': 156}]
2025-06-10 08:11:22,849 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:23,317 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNT
2025-06-10 08:11:23,317 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36121.78, 'new_value': 47092.58}, {'field': 'dailyBillAmount', 'old_value': 36121.78, 'new_value': 47092.58}, {'field': 'amount', 'old_value': 30214.17, 'new_value': 38950.07}, {'field': 'count', 'old_value': 38, 'new_value': 45}, {'field': 'instoreAmount', 'old_value': 32829.25, 'new_value': 41565.15}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 39}]
2025-06-10 08:11:23,317 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:23,708 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKT
2025-06-10 08:11:23,708 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 240833.31, 'new_value': 263326.4}, {'field': 'amount', 'old_value': 240833.31, 'new_value': 263326.4}, {'field': 'count', 'old_value': 1445, 'new_value': 1619}, {'field': 'instoreAmount', 'old_value': 218915.01, 'new_value': 238192.01}, {'field': 'instoreCount', 'old_value': 785, 'new_value': 858}, {'field': 'onlineAmount', 'old_value': 21918.51, 'new_value': 25170.42}, {'field': 'onlineCount', 'old_value': 660, 'new_value': 761}]
2025-06-10 08:11:23,708 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:24,145 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLT
2025-06-10 08:11:24,145 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 229536.41, 'new_value': 262324.92}, {'field': 'dailyBillAmount', 'old_value': 229536.41, 'new_value': 262324.92}, {'field': 'amount', 'old_value': 216064.37, 'new_value': 245335.26}, {'field': 'count', 'old_value': 1114, 'new_value': 1258}, {'field': 'instoreAmount', 'old_value': 196172.83, 'new_value': 223585.87}, {'field': 'instoreCount', 'old_value': 902, 'new_value': 1023}, {'field': 'onlineAmount', 'old_value': 20273.1, 'new_value': 22131.3}, {'field': 'onlineCount', 'old_value': 212, 'new_value': 235}]
2025-06-10 08:11:24,145 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:24,583 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4T
2025-06-10 08:11:24,583 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 180117.36, 'new_value': 194749.32}, {'field': 'dailyBillAmount', 'old_value': 180117.36, 'new_value': 194749.32}, {'field': 'amount', 'old_value': 17088.2, 'new_value': 18442.7}, {'field': 'count', 'old_value': 91, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 17088.2, 'new_value': 18442.7}, {'field': 'instoreCount', 'old_value': 91, 'new_value': 97}]
2025-06-10 08:11:24,583 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:25,020 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFT
2025-06-10 08:11:25,020 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 71264.58, 'new_value': 80309.26}, {'field': 'dailyBillAmount', 'old_value': 71264.58, 'new_value': 80309.26}, {'field': 'amount', 'old_value': 46834.22, 'new_value': 51488.11}, {'field': 'count', 'old_value': 1183, 'new_value': 1344}, {'field': 'instoreAmount', 'old_value': 29892.02, 'new_value': 34559.87}, {'field': 'instoreCount', 'old_value': 599, 'new_value': 689}, {'field': 'onlineAmount', 'old_value': 22209.6, 'new_value': 24273.14}, {'field': 'onlineCount', 'old_value': 584, 'new_value': 655}]
2025-06-10 08:11:25,020 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:25,489 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBT
2025-06-10 08:11:25,489 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45693.79, 'new_value': 52944.48}, {'field': 'amount', 'old_value': 45693.79, 'new_value': 52944.48}, {'field': 'count', 'old_value': 2215, 'new_value': 2577}, {'field': 'instoreAmount', 'old_value': 46425.39, 'new_value': 53742.43}, {'field': 'instoreCount', 'old_value': 2215, 'new_value': 2577}]
2025-06-10 08:11:25,505 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:25,911 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCT
2025-06-10 08:11:25,927 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18464.27, 'new_value': 20752.82}, {'field': 'dailyBillAmount', 'old_value': 18464.27, 'new_value': 20752.82}, {'field': 'amount', 'old_value': 11679.39, 'new_value': 13234.65}, {'field': 'count', 'old_value': 553, 'new_value': 624}, {'field': 'instoreAmount', 'old_value': 4672.19, 'new_value': 5606.28}, {'field': 'instoreCount', 'old_value': 159, 'new_value': 178}, {'field': 'onlineAmount', 'old_value': 7017.46, 'new_value': 7679.56}, {'field': 'onlineCount', 'old_value': 394, 'new_value': 446}]
2025-06-10 08:11:25,927 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:26,364 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDT
2025-06-10 08:11:26,364 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20592.1, 'new_value': 23590.52}, {'field': 'amount', 'old_value': 20592.1, 'new_value': 23590.52}, {'field': 'count', 'old_value': 671, 'new_value': 773}, {'field': 'instoreAmount', 'old_value': 9662.2, 'new_value': 11513.31}, {'field': 'instoreCount', 'old_value': 417, 'new_value': 488}, {'field': 'onlineAmount', 'old_value': 10962.52, 'new_value': 12185.26}, {'field': 'onlineCount', 'old_value': 254, 'new_value': 285}]
2025-06-10 08:11:26,364 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:26,802 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMET
2025-06-10 08:11:26,802 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13216.21, 'new_value': 14746.98}, {'field': 'amount', 'old_value': 13216.21, 'new_value': 14746.98}, {'field': 'count', 'old_value': 335, 'new_value': 375}, {'field': 'instoreAmount', 'old_value': 10580.1, 'new_value': 11765.6}, {'field': 'instoreCount', 'old_value': 280, 'new_value': 312}, {'field': 'onlineAmount', 'old_value': 2896.11, 'new_value': 3241.38}, {'field': 'onlineCount', 'old_value': 55, 'new_value': 63}]
2025-06-10 08:11:26,817 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:27,255 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMYT
2025-06-10 08:11:27,255 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 91602.0, 'new_value': 96261.85}, {'field': 'dailyBillAmount', 'old_value': 91602.0, 'new_value': 96261.85}, {'field': 'amount', 'old_value': 96443.6, 'new_value': 101127.6}, {'field': 'count', 'old_value': 682, 'new_value': 734}, {'field': 'instoreAmount', 'old_value': 97117.6, 'new_value': 101801.6}, {'field': 'instoreCount', 'old_value': 682, 'new_value': 734}]
2025-06-10 08:11:27,255 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:27,708 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMXT
2025-06-10 08:11:27,708 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 116296.25, 'new_value': 132270.67}, {'field': 'dailyBillAmount', 'old_value': 116296.25, 'new_value': 132270.67}, {'field': 'amount', 'old_value': 191242.39, 'new_value': 212792.03}, {'field': 'count', 'old_value': 318, 'new_value': 362}, {'field': 'instoreAmount', 'old_value': 188912.79, 'new_value': 210062.63}, {'field': 'instoreCount', 'old_value': 304, 'new_value': 347}, {'field': 'onlineAmount', 'old_value': 2329.6, 'new_value': 2729.4}, {'field': 'onlineCount', 'old_value': 14, 'new_value': 15}]
2025-06-10 08:11:27,708 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:28,161 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMWT
2025-06-10 08:11:28,161 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 286370.92, 'new_value': 323145.33}, {'field': 'dailyBillAmount', 'old_value': 286370.92, 'new_value': 323145.33}, {'field': 'amount', 'old_value': 322970.74, 'new_value': 359745.15}, {'field': 'count', 'old_value': 1416, 'new_value': 1565}, {'field': 'instoreAmount', 'old_value': 322970.74, 'new_value': 359745.15}, {'field': 'instoreCount', 'old_value': 1416, 'new_value': 1565}]
2025-06-10 08:11:28,177 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:28,567 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMVT
2025-06-10 08:11:28,567 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 94948.69, 'new_value': 100682.95}, {'field': 'dailyBillAmount', 'old_value': 94948.69, 'new_value': 100682.95}, {'field': 'amount', 'old_value': 93571.14, 'new_value': 99621.24}, {'field': 'count', 'old_value': 477, 'new_value': 519}, {'field': 'instoreAmount', 'old_value': 89024.8, 'new_value': 94609.6}, {'field': 'instoreCount', 'old_value': 403, 'new_value': 435}, {'field': 'onlineAmount', 'old_value': 5541.62, 'new_value': 6007.08}, {'field': 'onlineCount', 'old_value': 74, 'new_value': 84}]
2025-06-10 08:11:28,567 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:29,052 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMUT
2025-06-10 08:11:29,052 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 561170.3, 'new_value': 631753.9}, {'field': 'dailyBillAmount', 'old_value': 561170.3, 'new_value': 631753.9}, {'field': 'amount', 'old_value': 486840.0, 'new_value': 548409.0}, {'field': 'count', 'old_value': 1154, 'new_value': 1304}, {'field': 'instoreAmount', 'old_value': 509306.0, 'new_value': 577678.0}, {'field': 'instoreCount', 'old_value': 1154, 'new_value': 1304}]
2025-06-10 08:11:29,067 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:29,583 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMST
2025-06-10 08:11:29,583 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 362014.9, 'new_value': 403077.1}, {'field': 'amount', 'old_value': 362014.9, 'new_value': 403077.1}, {'field': 'count', 'old_value': 1168, 'new_value': 1290}, {'field': 'instoreAmount', 'old_value': 363959.9, 'new_value': 405022.1}, {'field': 'instoreCount', 'old_value': 1168, 'new_value': 1290}]
2025-06-10 08:11:29,583 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:30,020 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTT
2025-06-10 08:11:30,020 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 289218.48, 'new_value': 331977.26}, {'field': 'dailyBillAmount', 'old_value': 289218.48, 'new_value': 331977.26}, {'field': 'amount', 'old_value': 205084.11, 'new_value': 232642.42}, {'field': 'count', 'old_value': 787, 'new_value': 895}, {'field': 'instoreAmount', 'old_value': 201799.28, 'new_value': 228116.45}, {'field': 'instoreCount', 'old_value': 464, 'new_value': 532}, {'field': 'onlineAmount', 'old_value': 9682.7, 'new_value': 10924.4}, {'field': 'onlineCount', 'old_value': 323, 'new_value': 363}]
2025-06-10 08:11:30,020 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:30,474 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRT
2025-06-10 08:11:30,474 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 215804.21, 'new_value': 232672.47}, {'field': 'dailyBillAmount', 'old_value': 215804.21, 'new_value': 232672.47}, {'field': 'amount', 'old_value': 203069.88, 'new_value': 220689.31}, {'field': 'count', 'old_value': 544, 'new_value': 645}, {'field': 'instoreAmount', 'old_value': 207596.29, 'new_value': 224464.55}, {'field': 'instoreCount', 'old_value': 454, 'new_value': 524}, {'field': 'onlineAmount', 'old_value': 2539.95, 'new_value': 3291.12}, {'field': 'onlineCount', 'old_value': 90, 'new_value': 121}]
2025-06-10 08:11:30,520 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-10 08:11:30,927 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-06-10 08:11:30,927 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 449864.0, 'new_value': 273988.4}, {'field': 'dailyBillAmount', 'old_value': 449864.0, 'new_value': 273988.4}]
2025-06-10 08:11:30,942 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-10 08:11:31,364 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-06-10 08:11:31,364 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68825.4, 'new_value': 61505.0}, {'field': 'dailyBillAmount', 'old_value': 68825.4, 'new_value': 61505.0}]
2025-06-10 08:11:31,364 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:31,895 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMPW
2025-06-10 08:11:31,895 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 55834.96, 'new_value': 64541.86}, {'field': 'dailyBillAmount', 'old_value': 55834.96, 'new_value': 64541.86}, {'field': 'amount', 'old_value': 59135.55, 'new_value': 68032.55}, {'field': 'count', 'old_value': 408, 'new_value': 448}, {'field': 'instoreAmount', 'old_value': 55595.0, 'new_value': 64331.0}, {'field': 'instoreCount', 'old_value': 350, 'new_value': 388}, {'field': 'onlineAmount', 'old_value': 3572.55, 'new_value': 3733.55}, {'field': 'onlineCount', 'old_value': 58, 'new_value': 60}]
2025-06-10 08:11:31,895 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:32,317 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMFU
2025-06-10 08:11:32,317 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 43478.8, 'new_value': 46446.8}, {'field': 'dailyBillAmount', 'old_value': 43478.8, 'new_value': 46446.8}, {'field': 'amount', 'old_value': 52388.0, 'new_value': 55908.0}, {'field': 'count', 'old_value': 219, 'new_value': 234}, {'field': 'instoreAmount', 'old_value': 52388.0, 'new_value': 55908.0}, {'field': 'instoreCount', 'old_value': 219, 'new_value': 234}]
2025-06-10 08:11:32,317 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:32,770 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMGU
2025-06-10 08:11:32,786 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-06, 变更字段: [{'field': 'amount', 'old_value': 17823.0, 'new_value': 20220.0}, {'field': 'count', 'old_value': 97, 'new_value': 107}, {'field': 'instoreAmount', 'old_value': 17823.0, 'new_value': 20220.0}, {'field': 'instoreCount', 'old_value': 97, 'new_value': 107}]
2025-06-10 08:11:32,786 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:33,380 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMHU
2025-06-10 08:11:33,380 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32644.0, 'new_value': 36691.0}, {'field': 'amount', 'old_value': 32644.0, 'new_value': 36691.0}, {'field': 'count', 'old_value': 359, 'new_value': 399}, {'field': 'instoreAmount', 'old_value': 32644.0, 'new_value': 36691.0}, {'field': 'instoreCount', 'old_value': 359, 'new_value': 399}]
2025-06-10 08:11:33,380 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:33,817 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMIU
2025-06-10 08:11:33,817 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7565.87, 'new_value': 8314.16}, {'field': 'dailyBillAmount', 'old_value': 7565.87, 'new_value': 8314.16}, {'field': 'amount', 'old_value': 857.19, 'new_value': 918.84}, {'field': 'count', 'old_value': 27, 'new_value': 30}, {'field': 'instoreAmount', 'old_value': 1090.19, 'new_value': 1157.34}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 30}]
2025-06-10 08:11:33,817 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:34,348 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMMW
2025-06-10 08:11:34,348 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-06, 变更字段: [{'field': 'amount', 'old_value': 14569.0, 'new_value': 15252.0}, {'field': 'count', 'old_value': 69, 'new_value': 75}, {'field': 'instoreAmount', 'old_value': 14569.0, 'new_value': 15252.0}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 75}]
2025-06-10 08:11:34,348 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:34,848 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMNW
2025-06-10 08:11:34,848 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23829.59, 'new_value': 26237.92}, {'field': 'dailyBillAmount', 'old_value': 23829.59, 'new_value': 26237.92}, {'field': 'amount', 'old_value': 17986.65, 'new_value': 19952.65}, {'field': 'count', 'old_value': 564, 'new_value': 624}, {'field': 'instoreAmount', 'old_value': 17635.65, 'new_value': 19585.65}, {'field': 'instoreCount', 'old_value': 547, 'new_value': 606}, {'field': 'onlineAmount', 'old_value': 351.0, 'new_value': 367.0}, {'field': 'onlineCount', 'old_value': 17, 'new_value': 18}]
2025-06-10 08:11:34,864 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:35,317 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMOW
2025-06-10 08:11:35,317 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30897.89, 'new_value': 33350.08}, {'field': 'dailyBillAmount', 'old_value': 30897.89, 'new_value': 33350.08}, {'field': 'amount', 'old_value': 30258.3, 'new_value': 32642.3}, {'field': 'count', 'old_value': 145, 'new_value': 160}, {'field': 'instoreAmount', 'old_value': 29955.6, 'new_value': 32339.6}, {'field': 'instoreCount', 'old_value': 133, 'new_value': 148}]
2025-06-10 08:11:35,317 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:35,786 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMZT
2025-06-10 08:11:35,786 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-06, 变更字段: [{'field': 'amount', 'old_value': 62341.8, 'new_value': 67322.8}, {'field': 'count', 'old_value': 415, 'new_value': 445}, {'field': 'instoreAmount', 'old_value': 63508.0, 'new_value': 68430.0}, {'field': 'instoreCount', 'old_value': 401, 'new_value': 430}, {'field': 'onlineAmount', 'old_value': 691.8, 'new_value': 750.8}, {'field': 'onlineCount', 'old_value': 14, 'new_value': 15}]
2025-06-10 08:11:35,786 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:36,239 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM0U
2025-06-10 08:11:36,239 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8721.4, 'new_value': 10281.4}, {'field': 'dailyBillAmount', 'old_value': 8721.4, 'new_value': 10281.4}]
2025-06-10 08:11:36,239 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:36,723 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM1U
2025-06-10 08:11:36,723 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-06, 变更字段: [{'field': 'amount', 'old_value': 29483.57, 'new_value': 31998.44}, {'field': 'count', 'old_value': 1623, 'new_value': 1767}, {'field': 'instoreAmount', 'old_value': 4089.73, 'new_value': 4452.04}, {'field': 'instoreCount', 'old_value': 357, 'new_value': 396}, {'field': 'onlineAmount', 'old_value': 26729.0, 'new_value': 28983.5}, {'field': 'onlineCount', 'old_value': 1266, 'new_value': 1371}]
2025-06-10 08:11:36,723 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:37,270 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM2U
2025-06-10 08:11:37,270 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 70075.56, 'new_value': 78275.66}, {'field': 'amount', 'old_value': 70075.56, 'new_value': 78274.82}, {'field': 'count', 'old_value': 1260, 'new_value': 1434}, {'field': 'instoreAmount', 'old_value': 57777.73, 'new_value': 64242.86}, {'field': 'instoreCount', 'old_value': 1036, 'new_value': 1187}, {'field': 'onlineAmount', 'old_value': 12297.83, 'new_value': 14032.8}, {'field': 'onlineCount', 'old_value': 224, 'new_value': 247}]
2025-06-10 08:11:37,270 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:37,739 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM3U
2025-06-10 08:11:37,739 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11310.0, 'new_value': 13201.6}, {'field': 'dailyBillAmount', 'old_value': 4872.8, 'new_value': 7769.0}, {'field': 'amount', 'old_value': 11310.0, 'new_value': 13201.6}, {'field': 'count', 'old_value': 70, 'new_value': 78}, {'field': 'instoreAmount', 'old_value': 11310.0, 'new_value': 13201.6}, {'field': 'instoreCount', 'old_value': 70, 'new_value': 78}]
2025-06-10 08:11:37,739 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:38,333 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM4U
2025-06-10 08:11:38,333 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4050.0, 'new_value': 5400.0}, {'field': 'dailyBillAmount', 'old_value': 4050.0, 'new_value': 5400.0}, {'field': 'amount', 'old_value': 9065.7, 'new_value': 9572.7}, {'field': 'count', 'old_value': 82, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 9233.7, 'new_value': 9740.7}, {'field': 'instoreCount', 'old_value': 82, 'new_value': 90}]
2025-06-10 08:11:38,333 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:38,895 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMEU
2025-06-10 08:11:38,895 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37859.38, 'new_value': 44478.64}, {'field': 'dailyBillAmount', 'old_value': 37859.38, 'new_value': 44478.64}, {'field': 'amount', 'old_value': 27323.71, 'new_value': 32435.11}, {'field': 'count', 'old_value': 248, 'new_value': 296}, {'field': 'instoreAmount', 'old_value': 27323.71, 'new_value': 32435.11}, {'field': 'instoreCount', 'old_value': 248, 'new_value': 296}]
2025-06-10 08:11:38,895 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:39,473 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM6U
2025-06-10 08:11:39,473 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 77649.6, 'new_value': 84535.1}, {'field': 'dailyBillAmount', 'old_value': 77649.6, 'new_value': 84535.1}, {'field': 'amount', 'old_value': 57062.33, 'new_value': 63129.33}, {'field': 'count', 'old_value': 1864, 'new_value': 2102}, {'field': 'instoreAmount', 'old_value': 53824.79, 'new_value': 59708.99}, {'field': 'instoreCount', 'old_value': 1770, 'new_value': 2004}, {'field': 'onlineAmount', 'old_value': 3592.54, 'new_value': 3775.34}, {'field': 'onlineCount', 'old_value': 94, 'new_value': 98}]
2025-06-10 08:11:39,489 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:39,989 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM7U
2025-06-10 08:11:39,989 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18887.3, 'new_value': 20104.1}, {'field': 'dailyBillAmount', 'old_value': 18887.3, 'new_value': 20104.1}, {'field': 'amount', 'old_value': 17565.4, 'new_value': 18629.4}, {'field': 'count', 'old_value': 110, 'new_value': 115}, {'field': 'instoreAmount', 'old_value': 18092.9, 'new_value': 19156.9}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 115}]
2025-06-10 08:11:39,989 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:40,442 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM8U
2025-06-10 08:11:40,442 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19029.94, 'new_value': 22257.47}, {'field': 'dailyBillAmount', 'old_value': 19029.94, 'new_value': 22257.47}]
2025-06-10 08:11:40,442 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:40,895 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM9U
2025-06-10 08:11:40,895 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16001.63, 'new_value': 17954.21}, {'field': 'amount', 'old_value': 16000.66, 'new_value': 17952.91}, {'field': 'count', 'old_value': 980, 'new_value': 1105}, {'field': 'instoreAmount', 'old_value': 16171.56, 'new_value': 18137.17}, {'field': 'instoreCount', 'old_value': 980, 'new_value': 1105}]
2025-06-10 08:11:40,911 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:41,333 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMAU
2025-06-10 08:11:41,333 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24406.59, 'new_value': 27681.74}, {'field': 'dailyBillAmount', 'old_value': 24406.59, 'new_value': 27681.74}, {'field': 'amount', 'old_value': 24859.7, 'new_value': 28203.05}, {'field': 'count', 'old_value': 1250, 'new_value': 1412}, {'field': 'instoreAmount', 'old_value': 22481.4, 'new_value': 25597.7}, {'field': 'instoreCount', 'old_value': 1119, 'new_value': 1268}, {'field': 'onlineAmount', 'old_value': 2577.09, 'new_value': 2804.14}, {'field': 'onlineCount', 'old_value': 131, 'new_value': 144}]
2025-06-10 08:11:41,333 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:41,802 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMBU
2025-06-10 08:11:41,802 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19297.46, 'new_value': 21513.15}, {'field': 'amount', 'old_value': 19297.46, 'new_value': 21513.15}, {'field': 'count', 'old_value': 903, 'new_value': 1012}, {'field': 'instoreAmount', 'old_value': 10922.57, 'new_value': 12447.87}, {'field': 'instoreCount', 'old_value': 540, 'new_value': 615}, {'field': 'onlineAmount', 'old_value': 8412.71, 'new_value': 9103.1}, {'field': 'onlineCount', 'old_value': 363, 'new_value': 397}]
2025-06-10 08:11:41,802 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:42,192 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMCU
2025-06-10 08:11:42,192 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14698.2, 'new_value': 16549.96}, {'field': 'dailyBillAmount', 'old_value': 14698.2, 'new_value': 16549.96}, {'field': 'amount', 'old_value': 10054.47, 'new_value': 11470.51}, {'field': 'count', 'old_value': 379, 'new_value': 434}, {'field': 'instoreAmount', 'old_value': 10200.87, 'new_value': 11616.91}, {'field': 'instoreCount', 'old_value': 379, 'new_value': 434}]
2025-06-10 08:11:42,192 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:42,583 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMDU
2025-06-10 08:11:42,583 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21745.99, 'new_value': 24235.03}, {'field': 'amount', 'old_value': 21745.99, 'new_value': 24234.75}, {'field': 'count', 'old_value': 1295, 'new_value': 1426}, {'field': 'instoreAmount', 'old_value': 4760.08, 'new_value': 5052.01}, {'field': 'instoreCount', 'old_value': 233, 'new_value': 249}, {'field': 'onlineAmount', 'old_value': 17551.48, 'new_value': 19764.19}, {'field': 'onlineCount', 'old_value': 1062, 'new_value': 1177}]
2025-06-10 08:11:42,598 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-10 08:11:43,067 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM5U
2025-06-10 08:11:43,067 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18210.0, 'new_value': 20256.0}, {'field': 'dailyBillAmount', 'old_value': 18210.0, 'new_value': 20256.0}]
2025-06-10 08:11:43,067 - WARNING - 批量插入月度数据失败，将在 2 秒后重试 (尝试 1/3): Object of type Decimal is not JSON serializable
2025-06-10 08:11:45,067 - WARNING - 批量插入月度数据失败，将在 4 秒后重试 (尝试 2/3): Object of type Decimal is not JSON serializable
2025-06-10 08:11:49,083 - ERROR - 批量插入月度数据失败达到最大重试次数，跳过当前批次: Object of type Decimal is not JSON serializable
2025-06-10 08:11:52,098 - INFO - 批量插入月度数据完成: 总计 1 条，成功 0 条，失败 1 条
2025-06-10 08:11:52,098 - INFO - 批量插入月销售数据完成，共 1 条记录
2025-06-10 08:11:52,098 - INFO - 月销售数据同步完成！更新: 195 条，插入: 1 条，错误: 0 条，跳过: 1208 条
2025-06-10 08:11:52,098 - INFO - 综合数据同步流程完成！
2025-06-10 08:11:52,145 - INFO - 综合数据同步完成
2025-06-10 08:11:52,145 - INFO - MySQL数据库连接已关闭
2025-06-10 08:11:52,145 - INFO - ==================================================
2025-06-10 08:11:52,145 - INFO - 程序退出
2025-06-10 08:11:52,145 - INFO - ==================================================
