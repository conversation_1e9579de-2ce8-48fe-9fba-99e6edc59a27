2025-05-05 00:30:34,543 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 00:30:34,543 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 00:30:34,543 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 00:30:34,606 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 0 条记录
2025-05-05 00:30:34,606 - ERROR - 未获取到MySQL数据
2025-05-05 00:31:34,698 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 00:31:34,698 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 00:31:34,698 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 00:31:34,745 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 00:31:34,745 - ERROR - 未获取到MySQL数据
2025-05-05 00:31:34,745 - INFO - 同步完成
2025-05-05 01:30:34,764 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 01:30:34,764 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 01:30:34,764 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 01:30:34,811 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 5 条记录
2025-05-05 01:30:34,811 - INFO - 获取到 1 个日期需要处理: ['2025-05-04']
2025-05-05 01:30:34,811 - INFO - 开始处理日期: 2025-05-04
2025-05-05 01:30:34,826 - INFO - Request Parameters - Page 1:
2025-05-05 01:30:34,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 01:30:34,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 01:30:42,962 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FBA65D44-7225-7750-9F39-BBC248A3E739 Response: {'code': 'ServiceUnavailable', 'requestid': 'FBA65D44-7225-7750-9F39-BBC248A3E739', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FBA65D44-7225-7750-9F39-BBC248A3E739)
2025-05-05 01:30:42,962 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-05 01:31:43,054 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 01:31:43,054 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 01:31:43,054 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 01:31:43,101 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 01:31:43,101 - ERROR - 未获取到MySQL数据
2025-05-05 01:31:43,101 - INFO - 同步完成
2025-05-05 02:30:34,766 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 02:30:34,766 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 02:30:34,766 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 02:30:34,828 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 5 条记录
2025-05-05 02:30:34,828 - INFO - 获取到 1 个日期需要处理: ['2025-05-04']
2025-05-05 02:30:34,828 - INFO - 开始处理日期: 2025-05-04
2025-05-05 02:30:34,828 - INFO - Request Parameters - Page 1:
2025-05-05 02:30:34,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 02:30:34,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 02:30:42,964 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 278650A2-2E5E-7F3D-8ED4-D40F01C9EABA Response: {'code': 'ServiceUnavailable', 'requestid': '278650A2-2E5E-7F3D-8ED4-D40F01C9EABA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 278650A2-2E5E-7F3D-8ED4-D40F01C9EABA)
2025-05-05 02:30:42,964 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-05 02:31:43,056 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 02:31:43,056 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 02:31:43,056 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 02:31:43,103 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 02:31:43,103 - ERROR - 未获取到MySQL数据
2025-05-05 02:31:43,103 - INFO - 同步完成
2025-05-05 03:30:34,548 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 03:30:34,548 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 03:30:34,564 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 03:30:34,611 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 5 条记录
2025-05-05 03:30:34,611 - INFO - 获取到 1 个日期需要处理: ['2025-05-04']
2025-05-05 03:30:34,611 - INFO - 开始处理日期: 2025-05-04
2025-05-05 03:30:34,611 - INFO - Request Parameters - Page 1:
2025-05-05 03:30:34,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:30:34,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:30:42,793 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6C181D50-0CC6-7CAC-BC4C-EC6F49C8FBE2 Response: {'code': 'ServiceUnavailable', 'requestid': '6C181D50-0CC6-7CAC-BC4C-EC6F49C8FBE2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6C181D50-0CC6-7CAC-BC4C-EC6F49C8FBE2)
2025-05-05 03:30:42,793 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-05 03:31:42,885 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 03:31:42,885 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 03:31:42,885 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 03:31:42,932 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 03:31:42,932 - ERROR - 未获取到MySQL数据
2025-05-05 03:31:42,932 - INFO - 同步完成
2025-05-05 04:30:34,581 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 04:30:34,581 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 04:30:34,581 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 04:30:34,644 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 5 条记录
2025-05-05 04:30:34,644 - INFO - 获取到 1 个日期需要处理: ['2025-05-04']
2025-05-05 04:30:34,644 - INFO - 开始处理日期: 2025-05-04
2025-05-05 04:30:34,644 - INFO - Request Parameters - Page 1:
2025-05-05 04:30:34,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 04:30:34,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 04:30:42,764 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 053D9D69-E74F-7CF9-A4D6-C8479090B9DB Response: {'code': 'ServiceUnavailable', 'requestid': '053D9D69-E74F-7CF9-A4D6-C8479090B9DB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 053D9D69-E74F-7CF9-A4D6-C8479090B9DB)
2025-05-05 04:30:42,764 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-05 04:31:42,856 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 04:31:42,856 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 04:31:42,856 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 04:31:42,903 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 04:31:42,903 - ERROR - 未获取到MySQL数据
2025-05-05 04:31:42,903 - INFO - 同步完成
2025-05-05 05:30:34,584 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 05:30:34,584 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 05:30:34,584 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 05:30:34,631 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 6 条记录
2025-05-05 05:30:34,631 - INFO - 获取到 1 个日期需要处理: ['2025-05-04']
2025-05-05 05:30:34,631 - INFO - 开始处理日期: 2025-05-04
2025-05-05 05:30:34,647 - INFO - Request Parameters - Page 1:
2025-05-05 05:30:34,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 05:30:34,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 05:30:42,782 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 19869EC1-68DC-7E4E-A57D-F93C0D4599E3 Response: {'code': 'ServiceUnavailable', 'requestid': '19869EC1-68DC-7E4E-A57D-F93C0D4599E3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 19869EC1-68DC-7E4E-A57D-F93C0D4599E3)
2025-05-05 05:30:42,782 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-05 05:31:42,874 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 05:31:42,874 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 05:31:42,874 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 05:31:42,921 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 05:31:42,921 - ERROR - 未获取到MySQL数据
2025-05-05 05:31:42,921 - INFO - 同步完成
2025-05-05 06:30:34,380 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 06:30:34,380 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 06:30:34,380 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 06:30:34,443 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 9 条记录
2025-05-05 06:30:34,443 - INFO - 获取到 1 个日期需要处理: ['2025-05-04']
2025-05-05 06:30:34,443 - INFO - 开始处理日期: 2025-05-04
2025-05-05 06:30:34,443 - INFO - Request Parameters - Page 1:
2025-05-05 06:30:34,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:30:34,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:30:42,593 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D077D101-5ECE-75C5-BD3E-B381CA82519E Response: {'code': 'ServiceUnavailable', 'requestid': 'D077D101-5ECE-75C5-BD3E-B381CA82519E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D077D101-5ECE-75C5-BD3E-B381CA82519E)
2025-05-05 06:30:42,593 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-05 06:31:42,682 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 06:31:42,682 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 06:31:42,682 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 06:31:42,728 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 06:31:42,728 - ERROR - 未获取到MySQL数据
2025-05-05 06:31:42,728 - INFO - 同步完成
2025-05-05 07:30:33,781 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 07:30:33,781 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 07:30:33,781 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 07:30:33,843 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 11 条记录
2025-05-05 07:30:33,843 - INFO - 获取到 1 个日期需要处理: ['2025-05-04']
2025-05-05 07:30:33,843 - INFO - 开始处理日期: 2025-05-04
2025-05-05 07:30:33,843 - INFO - Request Parameters - Page 1:
2025-05-05 07:30:33,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 07:30:33,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 07:30:41,968 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 89BF92C7-5E6E-738E-AB54-12E4CDF3CF44 Response: {'code': 'ServiceUnavailable', 'requestid': '89BF92C7-5E6E-738E-AB54-12E4CDF3CF44', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 89BF92C7-5E6E-738E-AB54-12E4CDF3CF44)
2025-05-05 07:30:41,968 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-05 07:31:41,984 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 07:31:41,984 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 07:31:41,984 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 07:31:42,030 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 07:31:42,030 - ERROR - 未获取到MySQL数据
2025-05-05 07:31:42,030 - INFO - 同步完成
2025-05-05 08:30:33,993 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 08:30:33,993 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 08:30:33,993 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 08:30:34,040 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 26 条记录
2025-05-05 08:30:34,040 - INFO - 获取到 1 个日期需要处理: ['2025-05-04']
2025-05-05 08:30:34,040 - INFO - 开始处理日期: 2025-05-04
2025-05-05 08:30:34,040 - INFO - Request Parameters - Page 1:
2025-05-05 08:30:34,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 08:30:34,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 08:30:42,165 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F2A904B8-8C8C-7732-B255-E23D255A4B36 Response: {'code': 'ServiceUnavailable', 'requestid': 'F2A904B8-8C8C-7732-B255-E23D255A4B36', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F2A904B8-8C8C-7732-B255-E23D255A4B36)
2025-05-05 08:30:42,165 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-05 08:31:42,180 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 08:31:42,180 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 08:31:42,180 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 08:31:42,227 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 08:31:42,227 - ERROR - 未获取到MySQL数据
2025-05-05 08:31:42,227 - INFO - 同步完成
2025-05-05 09:30:33,908 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 09:30:33,908 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 09:30:33,908 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 09:30:33,955 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 96 条记录
2025-05-05 09:30:33,955 - INFO - 获取到 1 个日期需要处理: ['2025-05-04']
2025-05-05 09:30:33,955 - INFO - 开始处理日期: 2025-05-04
2025-05-05 09:30:33,970 - INFO - Request Parameters - Page 1:
2025-05-05 09:30:33,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:30:33,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:30:42,095 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 617B8690-19E0-7A42-8108-7165732276A8 Response: {'code': 'ServiceUnavailable', 'requestid': '617B8690-19E0-7A42-8108-7165732276A8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 617B8690-19E0-7A42-8108-7165732276A8)
2025-05-05 09:30:42,095 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-05 09:31:42,111 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 09:31:42,111 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 09:31:42,111 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 09:31:42,157 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 09:31:42,157 - ERROR - 未获取到MySQL数据
2025-05-05 09:31:42,157 - INFO - 同步完成
2025-05-05 10:30:33,932 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 10:30:33,932 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 10:30:33,932 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 10:30:33,995 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 119 条记录
2025-05-05 10:30:33,995 - INFO - 获取到 3 个日期需要处理: ['2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 10:30:33,995 - INFO - 开始处理日期: 2025-05-03
2025-05-05 10:30:33,995 - INFO - Request Parameters - Page 1:
2025-05-05 10:30:33,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 10:30:33,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 10:30:42,135 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6277CD03-170D-734C-8089-FC4762E070C6 Response: {'code': 'ServiceUnavailable', 'requestid': '6277CD03-170D-734C-8089-FC4762E070C6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6277CD03-170D-734C-8089-FC4762E070C6)
2025-05-05 10:30:42,135 - INFO - 开始处理日期: 2025-05-04
2025-05-05 10:30:42,135 - INFO - Request Parameters - Page 1:
2025-05-05 10:30:42,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 10:30:42,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 10:30:43,135 - INFO - Response - Page 1:
2025-05-05 10:30:43,135 - INFO - 第 1 页获取到 100 条记录
2025-05-05 10:30:43,338 - INFO - Request Parameters - Page 2:
2025-05-05 10:30:43,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 10:30:43,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 10:30:51,463 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5279EA2B-8EBB-74F9-B37C-E14CAE8947AE Response: {'code': 'ServiceUnavailable', 'requestid': '5279EA2B-8EBB-74F9-B37C-E14CAE8947AE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5279EA2B-8EBB-74F9-B37C-E14CAE8947AE)
2025-05-05 10:30:51,463 - INFO - 开始处理日期: 2025-05-05
2025-05-05 10:30:51,463 - INFO - Request Parameters - Page 1:
2025-05-05 10:30:51,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 10:30:51,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 10:30:57,026 - INFO - Response - Page 1:
2025-05-05 10:30:57,026 - INFO - 查询完成，共获取到 0 条记录
2025-05-05 10:30:57,026 - INFO - 获取到 0 条表单数据
2025-05-05 10:30:57,026 - INFO - 当前日期 2025-05-05 有 1 条MySQL数据需要处理
2025-05-05 10:30:57,026 - INFO - 开始批量插入 1 条新记录
2025-05-05 10:30:57,198 - INFO - 批量插入响应状态码: 200
2025-05-05 10:30:57,198 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 05 May 2025 02:30:28 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B51B9A29-0B8A-7E0D-9706-39A8D4ACF035', 'x-acs-trace-id': '4d4b02a294667cc4ff421c1f837be841', 'etag': '6E/MYLk/y9m9+riDoO0a+Yw1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-05 10:30:57,198 - INFO - 批量插入响应体: {'result': ['FINST-N3G66S811KZUAVN4AL31Z579D9RX2DPZQGAAM7R1']}
2025-05-05 10:30:57,198 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-05 10:30:57,198 - INFO - 成功插入的数据ID: ['FINST-N3G66S811KZUAVN4AL31Z579D9RX2DPZQGAAM7R1']
2025-05-05 10:31:02,213 - INFO - 批量插入完成，共 1 条记录
2025-05-05 10:31:02,213 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-05 10:31:02,213 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 2 条
2025-05-05 10:32:02,229 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 10:32:02,229 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 10:32:02,229 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 10:32:02,275 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 10:32:02,275 - ERROR - 未获取到MySQL数据
2025-05-05 10:32:02,275 - INFO - 同步完成
2025-05-05 11:30:33,738 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 11:30:33,738 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 11:30:33,738 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 11:30:33,800 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 119 条记录
2025-05-05 11:30:33,800 - INFO - 获取到 3 个日期需要处理: ['2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 11:30:33,800 - INFO - 开始处理日期: 2025-05-03
2025-05-05 11:30:33,816 - INFO - Request Parameters - Page 1:
2025-05-05 11:30:33,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 11:30:33,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 11:30:41,925 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3C299B58-1740-792E-90EA-F24FA1F4E08C Response: {'code': 'ServiceUnavailable', 'requestid': '3C299B58-1740-792E-90EA-F24FA1F4E08C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3C299B58-1740-792E-90EA-F24FA1F4E08C)
2025-05-05 11:30:41,925 - INFO - 开始处理日期: 2025-05-04
2025-05-05 11:30:41,925 - INFO - Request Parameters - Page 1:
2025-05-05 11:30:41,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 11:30:41,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 11:30:50,050 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DA5EC748-58A2-771B-AED7-F515175E93DA Response: {'code': 'ServiceUnavailable', 'requestid': 'DA5EC748-58A2-771B-AED7-F515175E93DA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DA5EC748-58A2-771B-AED7-F515175E93DA)
2025-05-05 11:30:50,050 - INFO - 开始处理日期: 2025-05-05
2025-05-05 11:30:50,050 - INFO - Request Parameters - Page 1:
2025-05-05 11:30:50,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 11:30:50,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 11:30:50,519 - INFO - Response - Page 1:
2025-05-05 11:30:50,519 - INFO - 第 1 页获取到 1 条记录
2025-05-05 11:30:50,722 - INFO - 查询完成，共获取到 1 条记录
2025-05-05 11:30:50,722 - INFO - 获取到 1 条表单数据
2025-05-05 11:30:50,722 - INFO - 当前日期 2025-05-05 有 1 条MySQL数据需要处理
2025-05-05 11:30:50,722 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 11:30:50,722 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-05 11:31:50,737 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 11:31:50,737 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 11:31:50,737 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 11:31:50,784 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 11:31:50,784 - ERROR - 未获取到MySQL数据
2025-05-05 11:31:50,784 - INFO - 同步完成
2025-05-05 12:30:33,825 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 12:30:33,825 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 12:30:33,825 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 12:30:33,887 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 119 条记录
2025-05-05 12:30:33,887 - INFO - 获取到 3 个日期需要处理: ['2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 12:30:33,887 - INFO - 开始处理日期: 2025-05-03
2025-05-05 12:30:33,887 - INFO - Request Parameters - Page 1:
2025-05-05 12:30:33,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:30:33,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:30:42,012 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C6E3B98A-5CD8-7A98-BFA7-9D1F1926B679 Response: {'code': 'ServiceUnavailable', 'requestid': 'C6E3B98A-5CD8-7A98-BFA7-9D1F1926B679', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C6E3B98A-5CD8-7A98-BFA7-9D1F1926B679)
2025-05-05 12:30:42,012 - INFO - 开始处理日期: 2025-05-04
2025-05-05 12:30:42,012 - INFO - Request Parameters - Page 1:
2025-05-05 12:30:42,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:30:42,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:30:47,340 - INFO - Response - Page 1:
2025-05-05 12:30:47,340 - INFO - 第 1 页获取到 100 条记录
2025-05-05 12:30:47,543 - INFO - Request Parameters - Page 2:
2025-05-05 12:30:47,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:30:47,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:30:48,247 - INFO - Response - Page 2:
2025-05-05 12:30:48,247 - INFO - 第 2 页获取到 100 条记录
2025-05-05 12:30:48,450 - INFO - Request Parameters - Page 3:
2025-05-05 12:30:48,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:30:48,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:30:56,559 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3C1B7F1F-657A-7056-8EC6-0448213EC4D6 Response: {'code': 'ServiceUnavailable', 'requestid': '3C1B7F1F-657A-7056-8EC6-0448213EC4D6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3C1B7F1F-657A-7056-8EC6-0448213EC4D6)
2025-05-05 12:30:56,559 - INFO - 开始处理日期: 2025-05-05
2025-05-05 12:30:56,559 - INFO - Request Parameters - Page 1:
2025-05-05 12:30:56,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:30:56,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:30:57,106 - INFO - Response - Page 1:
2025-05-05 12:30:57,106 - INFO - 第 1 页获取到 1 条记录
2025-05-05 12:30:57,309 - INFO - 查询完成，共获取到 1 条记录
2025-05-05 12:30:57,309 - INFO - 获取到 1 条表单数据
2025-05-05 12:30:57,309 - INFO - 当前日期 2025-05-05 有 1 条MySQL数据需要处理
2025-05-05 12:30:57,309 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 12:30:57,309 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-05 12:31:57,324 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 12:31:57,324 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 12:31:57,324 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 12:31:57,371 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 12:31:57,371 - ERROR - 未获取到MySQL数据
2025-05-05 12:31:57,371 - INFO - 同步完成
2025-05-05 13:30:33,927 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 13:30:33,927 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 13:30:33,927 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 13:30:33,990 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 122 条记录
2025-05-05 13:30:33,990 - INFO - 获取到 3 个日期需要处理: ['2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 13:30:33,990 - INFO - 开始处理日期: 2025-05-03
2025-05-05 13:30:33,990 - INFO - Request Parameters - Page 1:
2025-05-05 13:30:33,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 13:30:33,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 13:30:42,115 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BC71ABA9-7FED-7E35-A895-6EAD99D4BCE7 Response: {'code': 'ServiceUnavailable', 'requestid': 'BC71ABA9-7FED-7E35-A895-6EAD99D4BCE7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BC71ABA9-7FED-7E35-A895-6EAD99D4BCE7)
2025-05-05 13:30:42,115 - INFO - 开始处理日期: 2025-05-04
2025-05-05 13:30:42,115 - INFO - Request Parameters - Page 1:
2025-05-05 13:30:42,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 13:30:42,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 13:30:50,224 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5C65832B-CA54-7875-A7DB-83A5C9288052 Response: {'code': 'ServiceUnavailable', 'requestid': '5C65832B-CA54-7875-A7DB-83A5C9288052', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5C65832B-CA54-7875-A7DB-83A5C9288052)
2025-05-05 13:30:50,224 - INFO - 开始处理日期: 2025-05-05
2025-05-05 13:30:50,224 - INFO - Request Parameters - Page 1:
2025-05-05 13:30:50,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 13:30:50,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 13:30:50,724 - INFO - Response - Page 1:
2025-05-05 13:30:50,724 - INFO - 第 1 页获取到 1 条记录
2025-05-05 13:30:50,927 - INFO - 查询完成，共获取到 1 条记录
2025-05-05 13:30:50,927 - INFO - 获取到 1 条表单数据
2025-05-05 13:30:50,927 - INFO - 当前日期 2025-05-05 有 1 条MySQL数据需要处理
2025-05-05 13:30:50,927 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 13:30:50,927 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-05 13:31:50,942 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 13:31:50,942 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 13:31:50,942 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 13:31:50,989 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 13:31:50,989 - ERROR - 未获取到MySQL数据
2025-05-05 13:31:50,989 - INFO - 同步完成
2025-05-05 14:30:33,780 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 14:30:33,780 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 14:30:33,780 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 14:30:33,842 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 123 条记录
2025-05-05 14:30:33,842 - INFO - 获取到 3 个日期需要处理: ['2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 14:30:33,842 - INFO - 开始处理日期: 2025-05-03
2025-05-05 14:30:33,842 - INFO - Request Parameters - Page 1:
2025-05-05 14:30:33,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 14:30:33,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 14:30:41,967 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 105A8CFC-ACAB-7ACF-84D9-16036AD0C4D6 Response: {'code': 'ServiceUnavailable', 'requestid': '105A8CFC-ACAB-7ACF-84D9-16036AD0C4D6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 105A8CFC-ACAB-7ACF-84D9-16036AD0C4D6)
2025-05-05 14:30:41,967 - INFO - 开始处理日期: 2025-05-04
2025-05-05 14:30:41,967 - INFO - Request Parameters - Page 1:
2025-05-05 14:30:41,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 14:30:41,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 14:30:50,092 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 47032046-FB3F-7747-AF77-F757D50082DF Response: {'code': 'ServiceUnavailable', 'requestid': '47032046-FB3F-7747-AF77-F757D50082DF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 47032046-FB3F-7747-AF77-F757D50082DF)
2025-05-05 14:30:50,092 - INFO - 开始处理日期: 2025-05-05
2025-05-05 14:30:50,092 - INFO - Request Parameters - Page 1:
2025-05-05 14:30:50,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 14:30:50,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 14:30:50,576 - INFO - Response - Page 1:
2025-05-05 14:30:50,576 - INFO - 第 1 页获取到 1 条记录
2025-05-05 14:30:50,780 - INFO - 查询完成，共获取到 1 条记录
2025-05-05 14:30:50,780 - INFO - 获取到 1 条表单数据
2025-05-05 14:30:50,780 - INFO - 当前日期 2025-05-05 有 1 条MySQL数据需要处理
2025-05-05 14:30:50,780 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 14:30:50,780 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-05 14:31:50,795 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 14:31:50,795 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 14:31:50,795 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 14:31:50,842 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 14:31:50,842 - ERROR - 未获取到MySQL数据
2025-05-05 14:31:50,842 - INFO - 同步完成
2025-05-05 15:30:33,773 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 15:30:33,773 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 15:30:33,773 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 15:30:33,835 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 123 条记录
2025-05-05 15:30:33,835 - INFO - 获取到 3 个日期需要处理: ['2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 15:30:33,835 - INFO - 开始处理日期: 2025-05-03
2025-05-05 15:30:33,835 - INFO - Request Parameters - Page 1:
2025-05-05 15:30:33,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:30:33,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:30:41,960 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7B52032A-09D7-72D4-A127-D620FB082793 Response: {'code': 'ServiceUnavailable', 'requestid': '7B52032A-09D7-72D4-A127-D620FB082793', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7B52032A-09D7-72D4-A127-D620FB082793)
2025-05-05 15:30:41,960 - INFO - 开始处理日期: 2025-05-04
2025-05-05 15:30:41,960 - INFO - Request Parameters - Page 1:
2025-05-05 15:30:41,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:30:41,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:30:42,101 - ERROR - 处理日期 2025-05-04 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 112CF7D8-6AD4-7C0B-8A1A-F17743EBFD0F Response: {'requestid': '112CF7D8-6AD4-7C0B-8A1A-F17743EBFD0F', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 112CF7D8-6AD4-7C0B-8A1A-F17743EBFD0F)
2025-05-05 15:30:42,101 - INFO - 开始处理日期: 2025-05-05
2025-05-05 15:30:42,101 - INFO - Request Parameters - Page 1:
2025-05-05 15:30:42,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:30:42,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:30:47,163 - INFO - Response - Page 1:
2025-05-05 15:30:47,163 - INFO - 第 1 页获取到 1 条记录
2025-05-05 15:30:47,366 - INFO - 查询完成，共获取到 1 条记录
2025-05-05 15:30:47,366 - INFO - 获取到 1 条表单数据
2025-05-05 15:30:47,366 - INFO - 当前日期 2025-05-05 有 1 条MySQL数据需要处理
2025-05-05 15:30:47,366 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 15:30:47,366 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-05 15:31:47,382 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 15:31:47,382 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 15:31:47,382 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 15:31:47,429 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 15:31:47,429 - ERROR - 未获取到MySQL数据
2025-05-05 15:31:47,429 - INFO - 同步完成
2025-05-05 16:30:33,766 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 16:30:33,766 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 16:30:33,766 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 16:30:33,828 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 134 条记录
2025-05-05 16:30:33,828 - INFO - 获取到 4 个日期需要处理: ['2025-05-01', '2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 16:30:33,828 - INFO - 开始处理日期: 2025-05-01
2025-05-05 16:30:33,828 - INFO - Request Parameters - Page 1:
2025-05-05 16:30:33,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 16:30:33,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 16:30:41,953 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AB41F976-6311-77F9-8F62-20EA2250569F Response: {'code': 'ServiceUnavailable', 'requestid': 'AB41F976-6311-77F9-8F62-20EA2250569F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AB41F976-6311-77F9-8F62-20EA2250569F)
2025-05-05 16:30:41,953 - INFO - 开始处理日期: 2025-05-03
2025-05-05 16:30:41,953 - INFO - Request Parameters - Page 1:
2025-05-05 16:30:41,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 16:30:41,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 16:30:42,985 - INFO - Response - Page 1:
2025-05-05 16:30:42,985 - INFO - 第 1 页获取到 100 条记录
2025-05-05 16:30:43,188 - INFO - Request Parameters - Page 2:
2025-05-05 16:30:43,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 16:30:43,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 16:30:51,297 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3A868319-2DC8-7DFC-A132-65DA05206BF2 Response: {'code': 'ServiceUnavailable', 'requestid': '3A868319-2DC8-7DFC-A132-65DA05206BF2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3A868319-2DC8-7DFC-A132-65DA05206BF2)
2025-05-05 16:30:51,297 - INFO - 开始处理日期: 2025-05-04
2025-05-05 16:30:51,297 - INFO - Request Parameters - Page 1:
2025-05-05 16:30:51,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 16:30:51,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 16:30:56,578 - INFO - Response - Page 1:
2025-05-05 16:30:56,578 - INFO - 第 1 页获取到 100 条记录
2025-05-05 16:30:56,781 - INFO - Request Parameters - Page 2:
2025-05-05 16:30:56,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 16:30:56,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 16:30:57,485 - INFO - Response - Page 2:
2025-05-05 16:30:57,485 - INFO - 第 2 页获取到 100 条记录
2025-05-05 16:30:57,688 - INFO - Request Parameters - Page 3:
2025-05-05 16:30:57,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 16:30:57,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 16:30:58,328 - INFO - Response - Page 3:
2025-05-05 16:30:58,328 - INFO - 第 3 页获取到 40 条记录
2025-05-05 16:30:58,531 - INFO - 查询完成，共获取到 240 条记录
2025-05-05 16:30:58,531 - INFO - 获取到 240 条表单数据
2025-05-05 16:30:58,531 - INFO - 当前日期 2025-05-04 有 131 条MySQL数据需要处理
2025-05-05 16:30:58,531 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMIT1
2025-05-05 16:30:59,031 - INFO - 更新表单数据成功: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMIT1
2025-05-05 16:30:59,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120000.0, 'new_value': 180000.0}, {'field': 'total_amount', 'old_value': 120000.0, 'new_value': 180000.0}]
2025-05-05 16:30:59,031 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMJT1
2025-05-05 16:30:59,531 - INFO - 更新表单数据成功: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMJT1
2025-05-05 16:30:59,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 400000.0, 'new_value': 450000.0}, {'field': 'total_amount', 'old_value': 400000.0, 'new_value': 450000.0}]
2025-05-05 16:30:59,531 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMA11
2025-05-05 16:31:00,078 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMA11
2025-05-05 16:31:00,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16980.0, 'new_value': 21257.5}, {'field': 'total_amount', 'old_value': 16980.0, 'new_value': 21257.5}, {'field': 'order_count', 'old_value': 6, 'new_value': 3}]
2025-05-05 16:31:00,078 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMJ11
2025-05-05 16:31:00,516 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMJ11
2025-05-05 16:31:00,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12522.0, 'new_value': 11449.9}, {'field': 'total_amount', 'old_value': 12522.0, 'new_value': 11449.9}, {'field': 'order_count', 'old_value': 98, 'new_value': 102}]
2025-05-05 16:31:00,516 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMM11
2025-05-05 16:31:01,063 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMM11
2025-05-05 16:31:01,063 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6188.0, 'new_value': 3908.0}, {'field': 'total_amount', 'old_value': 6188.0, 'new_value': 3908.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 28}]
2025-05-05 16:31:01,063 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMS11
2025-05-05 16:31:01,656 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMS11
2025-05-05 16:31:01,656 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38525.0, 'new_value': 37586.0}, {'field': 'total_amount', 'old_value': 38525.0, 'new_value': 37586.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 63}]
2025-05-05 16:31:01,656 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMZ11
2025-05-05 16:31:02,188 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMZ11
2025-05-05 16:31:02,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 287146.0, 'new_value': 269916.0}, {'field': 'total_amount', 'old_value': 287146.0, 'new_value': 269916.0}, {'field': 'order_count', 'old_value': 5103, 'new_value': 4318}]
2025-05-05 16:31:02,188 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM321
2025-05-05 16:31:02,594 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM321
2025-05-05 16:31:02,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2285.0, 'new_value': 3768.9}, {'field': 'total_amount', 'old_value': 2285.0, 'new_value': 3768.9}, {'field': 'order_count', 'old_value': 123, 'new_value': 194}]
2025-05-05 16:31:02,594 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM421
2025-05-05 16:31:03,078 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM421
2025-05-05 16:31:03,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9500.0, 'new_value': 9390.39}, {'field': 'total_amount', 'old_value': 9500.0, 'new_value': 9390.39}, {'field': 'order_count', 'old_value': 372, 'new_value': 370}]
2025-05-05 16:31:03,078 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AME21
2025-05-05 16:31:03,563 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AME21
2025-05-05 16:31:03,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1948.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1948.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-05-05 16:31:03,563 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMH21
2025-05-05 16:31:04,000 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMH21
2025-05-05 16:31:04,000 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2689.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2689.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-05 16:31:04,000 - INFO - 开始批量插入 118 条新记录
2025-05-05 16:31:04,297 - INFO - 批量插入响应状态码: 200
2025-05-05 16:31:04,297 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 05 May 2025 08:30:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4832', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '99708EB1-5DB1-78E5-B47B-6E88DB4AEF63', 'x-acs-trace-id': 'fbed5f99f69773a78e3e3cdbe74ed260', 'etag': '4JJOZlTiUSDfxZabybjgMfw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-05 16:31:04,297 - INFO - 批量插入响应体: {'result': ['FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMSX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMTX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMUX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMVX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMWX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMXX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMYX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMZX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM0Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM1Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM2Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM3Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM4Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM5Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM6Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM7Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM8Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM9Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMAY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMBY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMCY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMDY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMEY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMFY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMGY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMHY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMIY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMJY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMKY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMLY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMMY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMNY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMOY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMPY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMQY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMRY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMSY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMTY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMUY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMVY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMWY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMXY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMYY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMZY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM0Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM1Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM2Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM3Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM4Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM5Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM6Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM7Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM8Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM9Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMAZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMBZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMCZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMDZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMEZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMFZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMGZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMHZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMIZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMJZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMKZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMLZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMMZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMNZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMOZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMPZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMQZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMRZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMSZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMTZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMUZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMVZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMWZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMXZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMYZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMZZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM001', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM101', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM201', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM301', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM401', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM501', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM601', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM701', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM801', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM901', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMA01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMB01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMC01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMD01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAME01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMF01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMG01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMH01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMI01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMJ01']}
2025-05-05 16:31:04,297 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-05 16:31:04,297 - INFO - 成功插入的数据ID: ['FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMSX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMTX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMUX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMVX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMWX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMXX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMYX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMZX', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM0Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM1Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM2Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM3Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM4Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM5Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM6Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM7Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM8Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAM9Y', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMAY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMBY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMCY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMDY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMEY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMFY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMGY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMHY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMIY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2HY3MTAAMJY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMKY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMLY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMMY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMNY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMOY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMPY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMQY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMRY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMSY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMTY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMUY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMVY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMWY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMXY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMYY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMZY', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM0Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM1Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM2Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM3Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM4Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM5Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM6Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM7Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM8Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM9Z', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMAZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMBZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMCZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMDZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMEZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMFZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMGZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMHZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMIZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMJZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMKZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMLZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMMZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMNZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMOZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMPZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMQZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMRZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMSZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMTZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMUZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMVZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMWZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMXZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMYZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMZZ', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM001', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM101', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM201', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM301', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM401', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM501', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM601', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM701', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM801', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAM901', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMA01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMB01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMC01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMD01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAME01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMF01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMG01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMH01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMI01', 'FINST-3ME66E8158ZU03BK6YHVI8N0E5FS2IY3MTAAMJ01']
2025-05-05 16:31:09,484 - INFO - 批量插入响应状态码: 200
2025-05-05 16:31:09,484 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 05 May 2025 08:30:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '894', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '533AD976-3D32-745B-BF74-00BC569F1231', 'x-acs-trace-id': 'a9be71f039260119b1b2befed28af067', 'etag': '8dSjCGWnGuKM0f/wsg+Wxpw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-05 16:31:09,484 - INFO - 批量插入响应体: {'result': ['FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMBV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMCV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMDV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMEV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMFV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMGV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMHV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMIV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMJV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMKV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMLV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMMV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMNV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMOV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMPV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMQV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMRV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMSV1']}
2025-05-05 16:31:09,484 - INFO - 批量插入表单数据成功，批次 2，共 18 条记录
2025-05-05 16:31:09,484 - INFO - 成功插入的数据ID: ['FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMBV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMCV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMDV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMEV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMFV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMGV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMHV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMIV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMJV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMKV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMLV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMMV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMNV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMOV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMPV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMQV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMRV1', 'FINST-7PF66BA1Z11VRSZJ7CZ9SBBZ1BGJ2NY7MTAAMSV1']
2025-05-05 16:31:14,500 - INFO - 批量插入完成，共 118 条记录
2025-05-05 16:31:14,500 - INFO - 日期 2025-05-04 处理完成 - 更新: 11 条，插入: 118 条，错误: 0 条
2025-05-05 16:31:14,500 - INFO - 开始处理日期: 2025-05-05
2025-05-05 16:31:14,500 - INFO - Request Parameters - Page 1:
2025-05-05 16:31:14,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 16:31:14,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 16:31:14,969 - INFO - Response - Page 1:
2025-05-05 16:31:14,969 - INFO - 第 1 页获取到 1 条记录
2025-05-05 16:31:15,172 - INFO - 查询完成，共获取到 1 条记录
2025-05-05 16:31:15,172 - INFO - 获取到 1 条表单数据
2025-05-05 16:31:15,172 - INFO - 当前日期 2025-05-05 有 1 条MySQL数据需要处理
2025-05-05 16:31:15,172 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 16:31:15,172 - INFO - 数据同步完成！更新: 11 条，插入: 118 条，错误: 2 条
2025-05-05 16:32:15,187 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 16:32:15,187 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 16:32:15,187 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 16:32:15,234 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 16:32:15,234 - ERROR - 未获取到MySQL数据
2025-05-05 16:32:15,234 - INFO - 同步完成
2025-05-05 17:30:33,978 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 17:30:33,978 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 17:30:33,978 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 17:30:34,040 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 139 条记录
2025-05-05 17:30:34,040 - INFO - 获取到 5 个日期需要处理: ['2025-04-30', '2025-05-01', '2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 17:30:34,040 - INFO - 开始处理日期: 2025-04-30
2025-05-05 17:30:34,040 - INFO - Request Parameters - Page 1:
2025-05-05 17:30:34,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 17:30:34,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 17:30:42,368 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BE5B9426-B122-7BF6-829D-59A47F13A168 Response: {'code': 'ServiceUnavailable', 'requestid': 'BE5B9426-B122-7BF6-829D-59A47F13A168', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BE5B9426-B122-7BF6-829D-59A47F13A168)
2025-05-05 17:30:42,368 - INFO - 开始处理日期: 2025-05-01
2025-05-05 17:30:42,368 - INFO - Request Parameters - Page 1:
2025-05-05 17:30:42,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 17:30:42,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 17:30:50,478 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 99FB61E9-996D-7F40-8662-D287217008B2 Response: {'code': 'ServiceUnavailable', 'requestid': '99FB61E9-996D-7F40-8662-D287217008B2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 99FB61E9-996D-7F40-8662-D287217008B2)
2025-05-05 17:30:50,478 - INFO - 开始处理日期: 2025-05-03
2025-05-05 17:30:50,478 - INFO - Request Parameters - Page 1:
2025-05-05 17:30:50,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 17:30:50,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 17:30:51,181 - INFO - Response - Page 1:
2025-05-05 17:30:51,197 - INFO - 第 1 页获取到 100 条记录
2025-05-05 17:30:51,400 - INFO - Request Parameters - Page 2:
2025-05-05 17:30:51,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 17:30:51,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 17:30:52,072 - INFO - Response - Page 2:
2025-05-05 17:30:52,072 - INFO - 第 2 页获取到 100 条记录
2025-05-05 17:30:52,275 - INFO - Request Parameters - Page 3:
2025-05-05 17:30:52,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 17:30:52,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 17:30:55,368 - INFO - Response - Page 3:
2025-05-05 17:30:55,368 - INFO - 第 3 页获取到 76 条记录
2025-05-05 17:30:55,572 - INFO - 查询完成，共获取到 276 条记录
2025-05-05 17:30:55,572 - INFO - 获取到 276 条表单数据
2025-05-05 17:30:55,572 - INFO - 当前日期 2025-05-03 有 1 条MySQL数据需要处理
2025-05-05 17:30:55,572 - INFO - 开始批量插入 1 条新记录
2025-05-05 17:30:55,728 - INFO - 批量插入响应状态码: 200
2025-05-05 17:30:55,743 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 05 May 2025 09:30:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '49A0C944-DAFD-7593-99B0-D692EC186053', 'x-acs-trace-id': '90b27760820127570e37c961673781f5', 'etag': '6C/lhIpISMmyA7dgl0hNRJA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-05 17:30:55,743 - INFO - 批量插入响应体: {'result': ['FINST-3PF66V71YOZU4YVKE7C749S3H7QP3F53RVAAMBV']}
2025-05-05 17:30:55,743 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-05 17:30:55,743 - INFO - 成功插入的数据ID: ['FINST-3PF66V71YOZU4YVKE7C749S3H7QP3F53RVAAMBV']
2025-05-05 17:31:00,759 - INFO - 批量插入完成，共 1 条记录
2025-05-05 17:31:00,759 - INFO - 日期 2025-05-03 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-05 17:31:00,759 - INFO - 开始处理日期: 2025-05-04
2025-05-05 17:31:00,759 - INFO - Request Parameters - Page 1:
2025-05-05 17:31:00,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 17:31:00,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 17:31:01,415 - INFO - Response - Page 1:
2025-05-05 17:31:01,415 - INFO - 第 1 页获取到 100 条记录
2025-05-05 17:31:01,618 - INFO - Request Parameters - Page 2:
2025-05-05 17:31:01,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 17:31:01,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 17:31:02,415 - INFO - Response - Page 2:
2025-05-05 17:31:02,415 - INFO - 第 2 页获取到 100 条记录
2025-05-05 17:31:02,618 - INFO - Request Parameters - Page 3:
2025-05-05 17:31:02,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 17:31:02,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 17:31:03,275 - INFO - Response - Page 3:
2025-05-05 17:31:03,275 - INFO - 第 3 页获取到 100 条记录
2025-05-05 17:31:03,478 - INFO - Request Parameters - Page 4:
2025-05-05 17:31:03,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 17:31:03,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 17:31:04,165 - INFO - Response - Page 4:
2025-05-05 17:31:04,165 - INFO - 第 4 页获取到 58 条记录
2025-05-05 17:31:04,368 - INFO - 查询完成，共获取到 358 条记录
2025-05-05 17:31:04,368 - INFO - 获取到 358 条表单数据
2025-05-05 17:31:04,368 - INFO - 当前日期 2025-05-04 有 135 条MySQL数据需要处理
2025-05-05 17:31:04,368 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM911
2025-05-05 17:31:04,837 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM911
2025-05-05 17:31:04,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6722.0, 'new_value': 8610.99}, {'field': 'total_amount', 'old_value': 6722.0, 'new_value': 8610.99}, {'field': 'order_count', 'old_value': 198, 'new_value': 539}]
2025-05-05 17:31:04,837 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMC11
2025-05-05 17:31:05,321 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMC11
2025-05-05 17:31:05,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18000.0, 'new_value': 13913.0}, {'field': 'total_amount', 'old_value': 18000.0, 'new_value': 13913.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 2}]
2025-05-05 17:31:05,321 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM921
2025-05-05 17:31:05,728 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AM921
2025-05-05 17:31:05,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-05-05 17:31:05,728 - INFO - 开始更新记录 - 表单实例ID: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMB21
2025-05-05 17:31:06,165 - INFO - 更新表单数据成功: FINST-6PF66691A3ZUU7FH6NXGPAGUSU112CBS6T9AMB21
2025-05-05 17:31:06,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-05 17:31:06,165 - INFO - 日期 2025-05-04 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-05-05 17:31:06,165 - INFO - 开始处理日期: 2025-05-05
2025-05-05 17:31:06,165 - INFO - Request Parameters - Page 1:
2025-05-05 17:31:06,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 17:31:06,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 17:31:06,618 - INFO - Response - Page 1:
2025-05-05 17:31:06,618 - INFO - 第 1 页获取到 1 条记录
2025-05-05 17:31:06,821 - INFO - 查询完成，共获取到 1 条记录
2025-05-05 17:31:06,821 - INFO - 获取到 1 条表单数据
2025-05-05 17:31:06,821 - INFO - 当前日期 2025-05-05 有 1 条MySQL数据需要处理
2025-05-05 17:31:06,821 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 17:31:06,821 - INFO - 数据同步完成！更新: 4 条，插入: 1 条，错误: 2 条
2025-05-05 17:32:06,837 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 17:32:06,837 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 17:32:06,837 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 17:32:06,884 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 17:32:06,884 - ERROR - 未获取到MySQL数据
2025-05-05 17:32:06,884 - INFO - 同步完成
2025-05-05 18:30:33,783 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 18:30:33,783 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 18:30:33,783 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 18:30:33,846 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 139 条记录
2025-05-05 18:30:33,846 - INFO - 获取到 5 个日期需要处理: ['2025-04-30', '2025-05-01', '2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 18:30:33,846 - INFO - 开始处理日期: 2025-04-30
2025-05-05 18:30:33,846 - INFO - Request Parameters - Page 1:
2025-05-05 18:30:33,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:30:33,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:30:41,971 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0067DA78-99F5-7BE7-90E5-0F9D1428A008 Response: {'code': 'ServiceUnavailable', 'requestid': '0067DA78-99F5-7BE7-90E5-0F9D1428A008', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0067DA78-99F5-7BE7-90E5-0F9D1428A008)
2025-05-05 18:30:41,971 - INFO - 开始处理日期: 2025-05-01
2025-05-05 18:30:41,971 - INFO - Request Parameters - Page 1:
2025-05-05 18:30:41,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:30:41,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:30:50,096 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6223A788-A312-777A-B472-08D416358A22 Response: {'code': 'ServiceUnavailable', 'requestid': '6223A788-A312-777A-B472-08D416358A22', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6223A788-A312-777A-B472-08D416358A22)
2025-05-05 18:30:50,096 - INFO - 开始处理日期: 2025-05-03
2025-05-05 18:30:50,096 - INFO - Request Parameters - Page 1:
2025-05-05 18:30:50,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:30:50,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:30:50,877 - INFO - Response - Page 1:
2025-05-05 18:30:50,877 - INFO - 第 1 页获取到 100 条记录
2025-05-05 18:30:51,080 - INFO - Request Parameters - Page 2:
2025-05-05 18:30:51,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:30:51,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:30:51,799 - INFO - Response - Page 2:
2025-05-05 18:30:51,799 - INFO - 第 2 页获取到 100 条记录
2025-05-05 18:30:52,002 - INFO - Request Parameters - Page 3:
2025-05-05 18:30:52,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:30:52,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:30:52,658 - INFO - Response - Page 3:
2025-05-05 18:30:52,658 - INFO - 第 3 页获取到 77 条记录
2025-05-05 18:30:52,861 - INFO - 查询完成，共获取到 277 条记录
2025-05-05 18:30:52,861 - INFO - 获取到 277 条表单数据
2025-05-05 18:30:52,861 - INFO - 当前日期 2025-05-03 有 1 条MySQL数据需要处理
2025-05-05 18:30:52,861 - INFO - 日期 2025-05-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 18:30:52,861 - INFO - 开始处理日期: 2025-05-04
2025-05-05 18:30:52,861 - INFO - Request Parameters - Page 1:
2025-05-05 18:30:52,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:30:52,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:30:53,596 - INFO - Response - Page 1:
2025-05-05 18:30:53,596 - INFO - 第 1 页获取到 100 条记录
2025-05-05 18:30:53,799 - INFO - Request Parameters - Page 2:
2025-05-05 18:30:53,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:30:53,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:30:54,565 - INFO - Response - Page 2:
2025-05-05 18:30:54,565 - INFO - 第 2 页获取到 100 条记录
2025-05-05 18:30:54,768 - INFO - Request Parameters - Page 3:
2025-05-05 18:30:54,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:30:54,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:30:55,455 - INFO - Response - Page 3:
2025-05-05 18:30:55,455 - INFO - 第 3 页获取到 100 条记录
2025-05-05 18:30:55,658 - INFO - Request Parameters - Page 4:
2025-05-05 18:30:55,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:30:55,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:30:56,315 - INFO - Response - Page 4:
2025-05-05 18:30:56,315 - INFO - 第 4 页获取到 58 条记录
2025-05-05 18:30:56,518 - INFO - 查询完成，共获取到 358 条记录
2025-05-05 18:30:56,518 - INFO - 获取到 358 条表单数据
2025-05-05 18:30:56,518 - INFO - 当前日期 2025-05-04 有 135 条MySQL数据需要处理
2025-05-05 18:30:56,518 - INFO - 日期 2025-05-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 18:30:56,518 - INFO - 开始处理日期: 2025-05-05
2025-05-05 18:30:56,518 - INFO - Request Parameters - Page 1:
2025-05-05 18:30:56,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:30:56,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:30:56,971 - INFO - Response - Page 1:
2025-05-05 18:30:56,971 - INFO - 第 1 页获取到 1 条记录
2025-05-05 18:30:57,174 - INFO - 查询完成，共获取到 1 条记录
2025-05-05 18:30:57,174 - INFO - 获取到 1 条表单数据
2025-05-05 18:30:57,174 - INFO - 当前日期 2025-05-05 有 1 条MySQL数据需要处理
2025-05-05 18:30:57,174 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 18:30:57,174 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-05 18:31:57,189 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 18:31:57,189 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 18:31:57,189 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 18:31:57,236 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 18:31:57,236 - ERROR - 未获取到MySQL数据
2025-05-05 18:31:57,236 - INFO - 同步完成
2025-05-05 19:30:33,759 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 19:30:33,759 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 19:30:33,759 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 19:30:33,806 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 139 条记录
2025-05-05 19:30:33,806 - INFO - 获取到 5 个日期需要处理: ['2025-04-30', '2025-05-01', '2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 19:30:33,821 - INFO - 开始处理日期: 2025-04-30
2025-05-05 19:30:33,821 - INFO - Request Parameters - Page 1:
2025-05-05 19:30:33,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 19:30:33,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 19:30:41,931 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3338DBAB-25BA-7DE8-A62F-FA73D7F9B9E3 Response: {'code': 'ServiceUnavailable', 'requestid': '3338DBAB-25BA-7DE8-A62F-FA73D7F9B9E3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3338DBAB-25BA-7DE8-A62F-FA73D7F9B9E3)
2025-05-05 19:30:41,931 - INFO - 开始处理日期: 2025-05-01
2025-05-05 19:30:41,931 - INFO - Request Parameters - Page 1:
2025-05-05 19:30:41,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 19:30:41,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 19:30:50,071 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 63CA6A6F-4CF4-714B-AF7C-BF1D73FBBC3A Response: {'code': 'ServiceUnavailable', 'requestid': '63CA6A6F-4CF4-714B-AF7C-BF1D73FBBC3A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 63CA6A6F-4CF4-714B-AF7C-BF1D73FBBC3A)
2025-05-05 19:30:50,071 - INFO - 开始处理日期: 2025-05-03
2025-05-05 19:30:50,071 - INFO - Request Parameters - Page 1:
2025-05-05 19:30:50,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 19:30:50,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 19:30:50,837 - INFO - Response - Page 1:
2025-05-05 19:30:50,837 - INFO - 第 1 页获取到 100 条记录
2025-05-05 19:30:51,040 - INFO - Request Parameters - Page 2:
2025-05-05 19:30:51,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 19:30:51,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 19:30:51,774 - INFO - Response - Page 2:
2025-05-05 19:30:51,774 - INFO - 第 2 页获取到 100 条记录
2025-05-05 19:30:51,977 - INFO - Request Parameters - Page 3:
2025-05-05 19:30:51,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 19:30:51,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 19:30:52,649 - INFO - Response - Page 3:
2025-05-05 19:30:52,649 - INFO - 第 3 页获取到 77 条记录
2025-05-05 19:30:52,852 - INFO - 查询完成，共获取到 277 条记录
2025-05-05 19:30:52,852 - INFO - 获取到 277 条表单数据
2025-05-05 19:30:52,852 - INFO - 当前日期 2025-05-03 有 1 条MySQL数据需要处理
2025-05-05 19:30:52,852 - INFO - 日期 2025-05-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 19:30:52,852 - INFO - 开始处理日期: 2025-05-04
2025-05-05 19:30:52,852 - INFO - Request Parameters - Page 1:
2025-05-05 19:30:52,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 19:30:52,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 19:30:53,977 - INFO - Response - Page 1:
2025-05-05 19:30:53,977 - INFO - 第 1 页获取到 100 条记录
2025-05-05 19:30:54,180 - INFO - Request Parameters - Page 2:
2025-05-05 19:30:54,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 19:30:54,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 19:30:54,899 - INFO - Response - Page 2:
2025-05-05 19:30:54,899 - INFO - 第 2 页获取到 100 条记录
2025-05-05 19:30:55,102 - INFO - Request Parameters - Page 3:
2025-05-05 19:30:55,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 19:30:55,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 19:30:55,805 - INFO - Response - Page 3:
2025-05-05 19:30:55,805 - INFO - 第 3 页获取到 100 条记录
2025-05-05 19:30:56,009 - INFO - Request Parameters - Page 4:
2025-05-05 19:30:56,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 19:30:56,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 19:30:56,696 - INFO - Response - Page 4:
2025-05-05 19:30:56,696 - INFO - 第 4 页获取到 58 条记录
2025-05-05 19:30:56,899 - INFO - 查询完成，共获取到 358 条记录
2025-05-05 19:30:56,899 - INFO - 获取到 358 条表单数据
2025-05-05 19:30:56,899 - INFO - 当前日期 2025-05-04 有 135 条MySQL数据需要处理
2025-05-05 19:30:56,899 - INFO - 日期 2025-05-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 19:30:56,899 - INFO - 开始处理日期: 2025-05-05
2025-05-05 19:30:56,899 - INFO - Request Parameters - Page 1:
2025-05-05 19:30:56,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 19:30:56,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 19:30:57,352 - INFO - Response - Page 1:
2025-05-05 19:30:57,352 - INFO - 第 1 页获取到 1 条记录
2025-05-05 19:30:57,555 - INFO - 查询完成，共获取到 1 条记录
2025-05-05 19:30:57,555 - INFO - 获取到 1 条表单数据
2025-05-05 19:30:57,555 - INFO - 当前日期 2025-05-05 有 1 条MySQL数据需要处理
2025-05-05 19:30:57,555 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 19:30:57,555 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-05 19:31:57,571 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 19:31:57,571 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 19:31:57,571 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 19:31:57,618 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 19:31:57,618 - ERROR - 未获取到MySQL数据
2025-05-05 19:31:57,618 - INFO - 同步完成
2025-05-05 20:30:33,720 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 20:30:33,720 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 20:30:33,720 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 20:30:33,783 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 139 条记录
2025-05-05 20:30:33,783 - INFO - 获取到 5 个日期需要处理: ['2025-04-30', '2025-05-01', '2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 20:30:33,783 - INFO - 开始处理日期: 2025-04-30
2025-05-05 20:30:33,783 - INFO - Request Parameters - Page 1:
2025-05-05 20:30:33,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 20:30:33,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 20:30:41,908 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D8C750D9-7E56-70AD-8AA4-781A2928B3AA Response: {'code': 'ServiceUnavailable', 'requestid': 'D8C750D9-7E56-70AD-8AA4-781A2928B3AA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D8C750D9-7E56-70AD-8AA4-781A2928B3AA)
2025-05-05 20:30:41,908 - INFO - 开始处理日期: 2025-05-01
2025-05-05 20:30:41,908 - INFO - Request Parameters - Page 1:
2025-05-05 20:30:41,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 20:30:41,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 20:30:46,377 - INFO - Response - Page 1:
2025-05-05 20:30:46,377 - INFO - 第 1 页获取到 100 条记录
2025-05-05 20:30:46,580 - INFO - Request Parameters - Page 2:
2025-05-05 20:30:46,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 20:30:46,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 20:30:47,345 - INFO - Response - Page 2:
2025-05-05 20:30:47,345 - INFO - 第 2 页获取到 100 条记录
2025-05-05 20:30:47,549 - INFO - Request Parameters - Page 3:
2025-05-05 20:30:47,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 20:30:47,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 20:30:48,267 - INFO - Response - Page 3:
2025-05-05 20:30:48,267 - INFO - 第 3 页获取到 64 条记录
2025-05-05 20:30:48,470 - INFO - 查询完成，共获取到 264 条记录
2025-05-05 20:30:48,470 - INFO - 获取到 264 条表单数据
2025-05-05 20:30:48,470 - INFO - 当前日期 2025-05-01 有 1 条MySQL数据需要处理
2025-05-05 20:30:48,470 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMRH
2025-05-05 20:30:48,908 - INFO - 更新表单数据成功: FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMRH
2025-05-05 20:30:48,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12525.0, 'new_value': 29338.42}, {'field': 'total_amount', 'old_value': 12525.0, 'new_value': 29338.42}, {'field': 'order_count', 'old_value': 50, 'new_value': 184}]
2025-05-05 20:30:48,908 - INFO - 日期 2025-05-01 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-05 20:30:48,908 - INFO - 开始处理日期: 2025-05-03
2025-05-05 20:30:48,908 - INFO - Request Parameters - Page 1:
2025-05-05 20:30:48,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 20:30:48,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 20:30:57,033 - ERROR - 处理日期 2025-05-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CBD1CC7D-A903-7923-BF13-62B4C34428BF Response: {'code': 'ServiceUnavailable', 'requestid': 'CBD1CC7D-A903-7923-BF13-62B4C34428BF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CBD1CC7D-A903-7923-BF13-62B4C34428BF)
2025-05-05 20:30:57,033 - INFO - 开始处理日期: 2025-05-04
2025-05-05 20:30:57,033 - INFO - Request Parameters - Page 1:
2025-05-05 20:30:57,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 20:30:57,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 20:30:57,736 - INFO - Response - Page 1:
2025-05-05 20:30:57,736 - INFO - 第 1 页获取到 100 条记录
2025-05-05 20:30:57,939 - INFO - Request Parameters - Page 2:
2025-05-05 20:30:57,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 20:30:57,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 20:30:58,752 - INFO - Response - Page 2:
2025-05-05 20:30:58,752 - INFO - 第 2 页获取到 100 条记录
2025-05-05 20:30:58,955 - INFO - Request Parameters - Page 3:
2025-05-05 20:30:58,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 20:30:58,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 20:30:59,658 - INFO - Response - Page 3:
2025-05-05 20:30:59,658 - INFO - 第 3 页获取到 100 条记录
2025-05-05 20:30:59,861 - INFO - Request Parameters - Page 4:
2025-05-05 20:30:59,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 20:30:59,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 20:31:00,658 - INFO - Response - Page 4:
2025-05-05 20:31:00,658 - INFO - 第 4 页获取到 58 条记录
2025-05-05 20:31:00,861 - INFO - 查询完成，共获取到 358 条记录
2025-05-05 20:31:00,861 - INFO - 获取到 358 条表单数据
2025-05-05 20:31:00,861 - INFO - 当前日期 2025-05-04 有 135 条MySQL数据需要处理
2025-05-05 20:31:00,861 - INFO - 日期 2025-05-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 20:31:00,861 - INFO - 开始处理日期: 2025-05-05
2025-05-05 20:31:00,861 - INFO - Request Parameters - Page 1:
2025-05-05 20:31:00,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 20:31:00,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 20:31:01,330 - INFO - Response - Page 1:
2025-05-05 20:31:01,330 - INFO - 第 1 页获取到 1 条记录
2025-05-05 20:31:01,533 - INFO - 查询完成，共获取到 1 条记录
2025-05-05 20:31:01,533 - INFO - 获取到 1 条表单数据
2025-05-05 20:31:01,533 - INFO - 当前日期 2025-05-05 有 1 条MySQL数据需要处理
2025-05-05 20:31:01,533 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 20:31:01,533 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 2 条
2025-05-05 20:32:01,548 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 20:32:01,548 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 20:32:01,548 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 20:32:01,595 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 20:32:01,595 - ERROR - 未获取到MySQL数据
2025-05-05 20:32:01,595 - INFO - 同步完成
2025-05-05 21:30:33,901 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 21:30:33,901 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 21:30:33,901 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 21:30:33,964 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 143 条记录
2025-05-05 21:30:33,964 - INFO - 获取到 5 个日期需要处理: ['2025-04-30', '2025-05-01', '2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 21:30:33,964 - INFO - 开始处理日期: 2025-04-30
2025-05-05 21:30:33,979 - INFO - Request Parameters - Page 1:
2025-05-05 21:30:33,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:30:33,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:30:42,088 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AFC0A5F1-118F-7DF5-9209-E95C1604AA27 Response: {'code': 'ServiceUnavailable', 'requestid': 'AFC0A5F1-118F-7DF5-9209-E95C1604AA27', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AFC0A5F1-118F-7DF5-9209-E95C1604AA27)
2025-05-05 21:30:42,088 - INFO - 开始处理日期: 2025-05-01
2025-05-05 21:30:42,088 - INFO - Request Parameters - Page 1:
2025-05-05 21:30:42,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:30:42,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:30:50,213 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FD6A5E61-E806-7DF4-9442-018648AA23BC Response: {'code': 'ServiceUnavailable', 'requestid': 'FD6A5E61-E806-7DF4-9442-018648AA23BC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FD6A5E61-E806-7DF4-9442-018648AA23BC)
2025-05-05 21:30:50,213 - INFO - 开始处理日期: 2025-05-03
2025-05-05 21:30:50,213 - INFO - Request Parameters - Page 1:
2025-05-05 21:30:50,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:30:50,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:30:54,557 - INFO - Response - Page 1:
2025-05-05 21:30:54,557 - INFO - 第 1 页获取到 100 条记录
2025-05-05 21:30:54,760 - INFO - Request Parameters - Page 2:
2025-05-05 21:30:54,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:30:54,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:30:55,557 - INFO - Response - Page 2:
2025-05-05 21:30:55,557 - INFO - 第 2 页获取到 100 条记录
2025-05-05 21:30:55,760 - INFO - Request Parameters - Page 3:
2025-05-05 21:30:55,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:30:55,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:30:56,510 - INFO - Response - Page 3:
2025-05-05 21:30:56,510 - INFO - 第 3 页获取到 77 条记录
2025-05-05 21:30:56,713 - INFO - 查询完成，共获取到 277 条记录
2025-05-05 21:30:56,713 - INFO - 获取到 277 条表单数据
2025-05-05 21:30:56,713 - INFO - 当前日期 2025-05-03 有 1 条MySQL数据需要处理
2025-05-05 21:30:56,713 - INFO - 日期 2025-05-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 21:30:56,713 - INFO - 开始处理日期: 2025-05-04
2025-05-05 21:30:56,713 - INFO - Request Parameters - Page 1:
2025-05-05 21:30:56,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:30:56,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:30:57,370 - INFO - Response - Page 1:
2025-05-05 21:30:57,370 - INFO - 第 1 页获取到 100 条记录
2025-05-05 21:30:57,573 - INFO - Request Parameters - Page 2:
2025-05-05 21:30:57,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:30:57,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:30:58,307 - INFO - Response - Page 2:
2025-05-05 21:30:58,307 - INFO - 第 2 页获取到 100 条记录
2025-05-05 21:30:58,510 - INFO - Request Parameters - Page 3:
2025-05-05 21:30:58,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:30:58,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:30:59,213 - INFO - Response - Page 3:
2025-05-05 21:30:59,213 - INFO - 第 3 页获取到 100 条记录
2025-05-05 21:30:59,417 - INFO - Request Parameters - Page 4:
2025-05-05 21:30:59,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:30:59,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:31:00,120 - INFO - Response - Page 4:
2025-05-05 21:31:00,120 - INFO - 第 4 页获取到 58 条记录
2025-05-05 21:31:00,323 - INFO - 查询完成，共获取到 358 条记录
2025-05-05 21:31:00,323 - INFO - 获取到 358 条表单数据
2025-05-05 21:31:00,323 - INFO - 当前日期 2025-05-04 有 135 条MySQL数据需要处理
2025-05-05 21:31:00,323 - INFO - 日期 2025-05-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 21:31:00,323 - INFO - 开始处理日期: 2025-05-05
2025-05-05 21:31:00,323 - INFO - Request Parameters - Page 1:
2025-05-05 21:31:00,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:31:00,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:31:00,760 - INFO - Response - Page 1:
2025-05-05 21:31:00,760 - INFO - 第 1 页获取到 1 条记录
2025-05-05 21:31:00,963 - INFO - 查询完成，共获取到 1 条记录
2025-05-05 21:31:00,963 - INFO - 获取到 1 条表单数据
2025-05-05 21:31:00,963 - INFO - 当前日期 2025-05-05 有 5 条MySQL数据需要处理
2025-05-05 21:31:00,963 - INFO - 开始批量插入 4 条新记录
2025-05-05 21:31:01,104 - INFO - 批量插入响应状态码: 200
2025-05-05 21:31:01,104 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 05 May 2025 13:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FA6DA19B-8847-7308-B546-3A523C40F66A', 'x-acs-trace-id': 'ddc2e3478440c61817cfcaae1f26d8be', 'etag': '2K4ea//Dy/VFQZLSeVIcBIg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-05 21:31:01,104 - INFO - 批量插入响应体: {'result': ['FINST-2FD66I7144ZU0914EQP3B9AAEYS427MGC4BAMIX', 'FINST-2FD66I7144ZU0914EQP3B9AAEYS427MGC4BAMJX', 'FINST-2FD66I7144ZU0914EQP3B9AAEYS427MGC4BAMKX', 'FINST-2FD66I7144ZU0914EQP3B9AAEYS427MGC4BAMLX']}
2025-05-05 21:31:01,104 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-05-05 21:31:01,104 - INFO - 成功插入的数据ID: ['FINST-2FD66I7144ZU0914EQP3B9AAEYS427MGC4BAMIX', 'FINST-2FD66I7144ZU0914EQP3B9AAEYS427MGC4BAMJX', 'FINST-2FD66I7144ZU0914EQP3B9AAEYS427MGC4BAMKX', 'FINST-2FD66I7144ZU0914EQP3B9AAEYS427MGC4BAMLX']
2025-05-05 21:31:06,120 - INFO - 批量插入完成，共 4 条记录
2025-05-05 21:31:06,120 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-05-05 21:31:06,120 - INFO - 数据同步完成！更新: 0 条，插入: 4 条，错误: 2 条
2025-05-05 21:32:06,135 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 21:32:06,135 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 21:32:06,135 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 21:32:06,182 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 21:32:06,182 - ERROR - 未获取到MySQL数据
2025-05-05 21:32:06,182 - INFO - 同步完成
2025-05-05 22:30:33,722 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 22:30:33,722 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 22:30:33,722 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 22:30:33,785 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 242 条记录
2025-05-05 22:30:33,785 - INFO - 获取到 5 个日期需要处理: ['2025-04-30', '2025-05-01', '2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 22:30:33,800 - INFO - 开始处理日期: 2025-04-30
2025-05-05 22:30:33,800 - INFO - Request Parameters - Page 1:
2025-05-05 22:30:33,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 22:30:33,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 22:30:41,925 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3BCC1D6D-EEBF-74A7-A0FA-E10062578778 Response: {'code': 'ServiceUnavailable', 'requestid': '3BCC1D6D-EEBF-74A7-A0FA-E10062578778', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3BCC1D6D-EEBF-74A7-A0FA-E10062578778)
2025-05-05 22:30:41,925 - INFO - 开始处理日期: 2025-05-01
2025-05-05 22:30:41,925 - INFO - Request Parameters - Page 1:
2025-05-05 22:30:41,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 22:30:41,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 22:30:50,035 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0CA9233A-08FF-7D20-8985-C13490BFB63B Response: {'code': 'ServiceUnavailable', 'requestid': '0CA9233A-08FF-7D20-8985-C13490BFB63B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0CA9233A-08FF-7D20-8985-C13490BFB63B)
2025-05-05 22:30:50,035 - INFO - 开始处理日期: 2025-05-03
2025-05-05 22:30:50,035 - INFO - Request Parameters - Page 1:
2025-05-05 22:30:50,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 22:30:50,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 22:30:50,738 - INFO - Response - Page 1:
2025-05-05 22:30:50,738 - INFO - 第 1 页获取到 100 条记录
2025-05-05 22:30:50,941 - INFO - Request Parameters - Page 2:
2025-05-05 22:30:50,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 22:30:50,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 22:30:51,628 - INFO - Response - Page 2:
2025-05-05 22:30:51,628 - INFO - 第 2 页获取到 100 条记录
2025-05-05 22:30:51,831 - INFO - Request Parameters - Page 3:
2025-05-05 22:30:51,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 22:30:51,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 22:30:54,097 - INFO - Response - Page 3:
2025-05-05 22:30:54,097 - INFO - 第 3 页获取到 77 条记录
2025-05-05 22:30:54,300 - INFO - 查询完成，共获取到 277 条记录
2025-05-05 22:30:54,300 - INFO - 获取到 277 条表单数据
2025-05-05 22:30:54,300 - INFO - 当前日期 2025-05-03 有 1 条MySQL数据需要处理
2025-05-05 22:30:54,300 - INFO - 日期 2025-05-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 22:30:54,300 - INFO - 开始处理日期: 2025-05-04
2025-05-05 22:30:54,300 - INFO - Request Parameters - Page 1:
2025-05-05 22:30:54,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 22:30:54,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 22:30:54,941 - INFO - Response - Page 1:
2025-05-05 22:30:54,941 - INFO - 第 1 页获取到 100 条记录
2025-05-05 22:30:55,144 - INFO - Request Parameters - Page 2:
2025-05-05 22:30:55,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 22:30:55,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 22:30:55,753 - INFO - Response - Page 2:
2025-05-05 22:30:55,753 - INFO - 第 2 页获取到 100 条记录
2025-05-05 22:30:55,956 - INFO - Request Parameters - Page 3:
2025-05-05 22:30:55,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 22:30:55,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 22:30:56,706 - INFO - Response - Page 3:
2025-05-05 22:30:56,706 - INFO - 第 3 页获取到 100 条记录
2025-05-05 22:30:56,910 - INFO - Request Parameters - Page 4:
2025-05-05 22:30:56,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 22:30:56,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 22:30:57,644 - INFO - Response - Page 4:
2025-05-05 22:30:57,644 - INFO - 第 4 页获取到 58 条记录
2025-05-05 22:30:57,847 - INFO - 查询完成，共获取到 358 条记录
2025-05-05 22:30:57,847 - INFO - 获取到 358 条表单数据
2025-05-05 22:30:57,847 - INFO - 当前日期 2025-05-04 有 139 条MySQL数据需要处理
2025-05-05 22:30:57,847 - INFO - 开始批量插入 4 条新记录
2025-05-05 22:30:57,988 - INFO - 批量插入响应状态码: 200
2025-05-05 22:30:57,988 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 05 May 2025 14:30:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '208', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7D27D56E-52A5-74E1-94DA-6FC42C5D8DBE', 'x-acs-trace-id': 'bef7ecbe71ff32ce3faafe77e48b295d', 'etag': '2bW8ZAddhF4ktdqru/NBYBg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-05 22:30:57,988 - INFO - 批量插入响应体: {'result': ['FINST-YPE66RB1XO0V3H94FQI0VAG7IEQ8380KH6BAM7J1', 'FINST-YPE66RB1XO0V3H94FQI0VAG7IEQ8380KH6BAM8J1', 'FINST-YPE66RB1XO0V3H94FQI0VAG7IEQ8380KH6BAM9J1', 'FINST-YPE66RB1XO0V3H94FQI0VAG7IEQ8380KH6BAMAJ1']}
2025-05-05 22:30:57,988 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-05-05 22:30:57,988 - INFO - 成功插入的数据ID: ['FINST-YPE66RB1XO0V3H94FQI0VAG7IEQ8380KH6BAM7J1', 'FINST-YPE66RB1XO0V3H94FQI0VAG7IEQ8380KH6BAM8J1', 'FINST-YPE66RB1XO0V3H94FQI0VAG7IEQ8380KH6BAM9J1', 'FINST-YPE66RB1XO0V3H94FQI0VAG7IEQ8380KH6BAMAJ1']
2025-05-05 22:31:03,003 - INFO - 批量插入完成，共 4 条记录
2025-05-05 22:31:03,003 - INFO - 日期 2025-05-04 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-05-05 22:31:03,003 - INFO - 开始处理日期: 2025-05-05
2025-05-05 22:31:03,003 - INFO - Request Parameters - Page 1:
2025-05-05 22:31:03,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 22:31:03,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 22:31:03,519 - INFO - Response - Page 1:
2025-05-05 22:31:03,519 - INFO - 第 1 页获取到 5 条记录
2025-05-05 22:31:03,722 - INFO - 查询完成，共获取到 5 条记录
2025-05-05 22:31:03,722 - INFO - 获取到 5 条表单数据
2025-05-05 22:31:03,722 - INFO - 当前日期 2025-05-05 有 100 条MySQL数据需要处理
2025-05-05 22:31:03,722 - INFO - 开始批量插入 95 条新记录
2025-05-05 22:31:03,972 - INFO - 批量插入响应状态码: 200
2025-05-05 22:31:03,972 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 05 May 2025 14:31:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4572', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E6D780F3-97BE-7CDC-A7D2-30D7B7116A86', 'x-acs-trace-id': '89106450972ffac52e08fc7f9b369468', 'etag': '4zFs9lHTQ3GswaSnzCznvCA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-05 22:31:03,972 - INFO - 批量插入响应体: {'result': ['FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMVH', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMWH', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMXH', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMYH', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMZH', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM0I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM1I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM2I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM3I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM4I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM5I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM6I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM7I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM8I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM9I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMAI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMBI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMCI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMDI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMEI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMFI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMGI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMHI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMII', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMJI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMKI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMLI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMMI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMNI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMOI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMPI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMQI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMRI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMSI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMTI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMUI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMVI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMWI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMXI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMYI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMZI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM0J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM1J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM2J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM3J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM4J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM5J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM6J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM7J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM8J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM9J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMAJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMBJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMCJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMDJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMEJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMFJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMGJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMHJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMIJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMJJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMKJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMLJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMMJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMNJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMOJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMPJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMQJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMRJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMSJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMTJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMUJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMVJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMWJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMXJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMYJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMZJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM0K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM1K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM2K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM3K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM4K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM5K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM6K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM7K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM8K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM9K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMAK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMBK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMCK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMDK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMEK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMFK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMGK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMHK']}
2025-05-05 22:31:03,972 - INFO - 批量插入表单数据成功，批次 1，共 95 条记录
2025-05-05 22:31:03,972 - INFO - 成功插入的数据ID: ['FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMVH', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMWH', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMXH', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMYH', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMZH', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM0I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM1I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM2I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM3I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM4I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM5I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM6I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM7I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM8I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM9I', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMAI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMBI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMCI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMDI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMEI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMFI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMGI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMHI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMII', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMJI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMKI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMLI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMMI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMNI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMOI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMPI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMQI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMRI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMSI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMTI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMUI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMVI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMWI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMXI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMYI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMZI', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM0J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM1J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM2J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM3J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM4J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM5J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM6J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM7J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM8J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM9J', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMAJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMBJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMCJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMDJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMEJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMFJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMGJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMHJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMIJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMJJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMKJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMLJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMMJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMNJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMOJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMPJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMQJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMRJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMSJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMTJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMUJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMVJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMWJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMXJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMYJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMZJ', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM0K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM1K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM2K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM3K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM4K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM5K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM6K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM7K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM8K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAM9K', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMAK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMBK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMCK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMDK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMEK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMFK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMGK', 'FINST-XBF66071HQ0VO729AA5BU86GUF5U2DMOH6BAMHK']
2025-05-05 22:31:08,988 - INFO - 批量插入完成，共 95 条记录
2025-05-05 22:31:08,988 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 95 条，错误: 0 条
2025-05-05 22:31:08,988 - INFO - 数据同步完成！更新: 0 条，插入: 99 条，错误: 2 条
2025-05-05 22:32:09,003 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 22:32:09,003 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 22:32:09,003 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 22:32:09,050 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 22:32:09,050 - ERROR - 未获取到MySQL数据
2025-05-05 22:32:09,050 - INFO - 同步完成
2025-05-05 23:30:34,412 - INFO - 使用默认增量同步（当天更新数据）
2025-05-05 23:30:34,412 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 23:30:34,412 - INFO - 查询参数: ('2025-05-05',)
2025-05-05 23:30:34,490 - INFO - MySQL查询成功，增量数据（日期: 2025-05-05），共获取 331 条记录
2025-05-05 23:30:34,490 - INFO - 获取到 6 个日期需要处理: ['2025-04-30', '2025-05-01', '2025-05-02', '2025-05-03', '2025-05-04', '2025-05-05']
2025-05-05 23:30:34,490 - INFO - 开始处理日期: 2025-04-30
2025-05-05 23:30:34,490 - INFO - Request Parameters - Page 1:
2025-05-05 23:30:34,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 23:30:34,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 23:30:42,608 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FC925C22-7409-72EB-BAA1-3F95EF07F215 Response: {'code': 'ServiceUnavailable', 'requestid': 'FC925C22-7409-72EB-BAA1-3F95EF07F215', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FC925C22-7409-72EB-BAA1-3F95EF07F215)
2025-05-05 23:30:42,608 - INFO - 开始处理日期: 2025-05-01
2025-05-05 23:30:42,608 - INFO - Request Parameters - Page 1:
2025-05-05 23:30:42,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 23:30:42,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 23:30:50,757 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4B3AA136-5512-75DB-8335-4D4A81B4A659 Response: {'code': 'ServiceUnavailable', 'requestid': '4B3AA136-5512-75DB-8335-4D4A81B4A659', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4B3AA136-5512-75DB-8335-4D4A81B4A659)
2025-05-05 23:30:50,757 - INFO - 开始处理日期: 2025-05-02
2025-05-05 23:30:50,757 - INFO - Request Parameters - Page 1:
2025-05-05 23:30:50,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 23:30:50,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 23:30:55,278 - INFO - Response - Page 1:
2025-05-05 23:30:55,278 - INFO - 第 1 页获取到 100 条记录
2025-05-05 23:30:55,481 - INFO - Request Parameters - Page 2:
2025-05-05 23:30:55,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 23:30:55,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 23:30:56,154 - INFO - Response - Page 2:
2025-05-05 23:30:56,154 - INFO - 第 2 页获取到 65 条记录
2025-05-05 23:30:56,357 - INFO - 查询完成，共获取到 165 条记录
2025-05-05 23:30:56,357 - INFO - 获取到 165 条表单数据
2025-05-05 23:30:56,357 - INFO - 当前日期 2025-05-02 有 1 条MySQL数据需要处理
2025-05-05 23:30:56,357 - INFO - 开始批量插入 1 条新记录
2025-05-05 23:30:56,513 - INFO - 批量插入响应状态码: 200
2025-05-05 23:30:56,513 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 05 May 2025 15:30:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A97CCE51-1855-74BE-A671-A32F123E2A3D', 'x-acs-trace-id': '2d87132007888cf0d73f5f1e5d6f4ded', 'etag': '6N8p7gODd7JwAqTqB74F4+g0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-05 23:30:56,513 - INFO - 批量插入响应体: {'result': ['FINST-2K666OB1JNZUD50NEUNAUDSXKXHK242NM8BAMTS']}
2025-05-05 23:30:56,513 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-05 23:30:56,513 - INFO - 成功插入的数据ID: ['FINST-2K666OB1JNZUD50NEUNAUDSXKXHK242NM8BAMTS']
2025-05-05 23:31:01,534 - INFO - 批量插入完成，共 1 条记录
2025-05-05 23:31:01,534 - INFO - 日期 2025-05-02 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-05 23:31:01,534 - INFO - 开始处理日期: 2025-05-03
2025-05-05 23:31:01,534 - INFO - Request Parameters - Page 1:
2025-05-05 23:31:01,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 23:31:01,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 23:31:02,191 - INFO - Response - Page 1:
2025-05-05 23:31:02,191 - INFO - 第 1 页获取到 100 条记录
2025-05-05 23:31:02,395 - INFO - Request Parameters - Page 2:
2025-05-05 23:31:02,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 23:31:02,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 23:31:03,239 - INFO - Response - Page 2:
2025-05-05 23:31:03,239 - INFO - 第 2 页获取到 100 条记录
2025-05-05 23:31:03,443 - INFO - Request Parameters - Page 3:
2025-05-05 23:31:03,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 23:31:03,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746201600000, 1746287999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 23:31:04,115 - INFO - Response - Page 3:
2025-05-05 23:31:04,115 - INFO - 第 3 页获取到 77 条记录
2025-05-05 23:31:04,318 - INFO - 查询完成，共获取到 277 条记录
2025-05-05 23:31:04,318 - INFO - 获取到 277 条表单数据
2025-05-05 23:31:04,318 - INFO - 当前日期 2025-05-03 有 1 条MySQL数据需要处理
2025-05-05 23:31:04,318 - INFO - 日期 2025-05-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 23:31:04,318 - INFO - 开始处理日期: 2025-05-04
2025-05-05 23:31:04,318 - INFO - Request Parameters - Page 1:
2025-05-05 23:31:04,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 23:31:04,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 23:31:05,038 - INFO - Response - Page 1:
2025-05-05 23:31:05,038 - INFO - 第 1 页获取到 100 条记录
2025-05-05 23:31:05,241 - INFO - Request Parameters - Page 2:
2025-05-05 23:31:05,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 23:31:05,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 23:31:05,929 - INFO - Response - Page 2:
2025-05-05 23:31:05,929 - INFO - 第 2 页获取到 100 条记录
2025-05-05 23:31:06,133 - INFO - Request Parameters - Page 3:
2025-05-05 23:31:06,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 23:31:06,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 23:31:06,805 - INFO - Response - Page 3:
2025-05-05 23:31:06,805 - INFO - 第 3 页获取到 100 条记录
2025-05-05 23:31:07,009 - INFO - Request Parameters - Page 4:
2025-05-05 23:31:07,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 23:31:07,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746288000000, 1746374399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 23:31:07,650 - INFO - Response - Page 4:
2025-05-05 23:31:07,666 - INFO - 第 4 页获取到 62 条记录
2025-05-05 23:31:07,869 - INFO - 查询完成，共获取到 362 条记录
2025-05-05 23:31:07,869 - INFO - 获取到 362 条表单数据
2025-05-05 23:31:07,869 - INFO - 当前日期 2025-05-04 有 140 条MySQL数据需要处理
2025-05-05 23:31:07,869 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMTU1
2025-05-05 23:31:08,276 - INFO - 更新表单数据成功: FINST-LLF66FD1X20VS5KU5FSZJCFLLFCC2OCN1R9AMTU1
2025-05-05 23:31:08,276 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 5915.6}, {'field': 'total_amount', 'old_value': 10516.0, 'new_value': 16431.6}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-05 23:31:08,291 - INFO - 日期 2025-05-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-05 23:31:08,291 - INFO - 开始处理日期: 2025-05-05
2025-05-05 23:31:08,291 - INFO - Request Parameters - Page 1:
2025-05-05 23:31:08,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 23:31:08,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 23:31:09,026 - INFO - Response - Page 1:
2025-05-05 23:31:09,026 - INFO - 第 1 页获取到 100 条记录
2025-05-05 23:31:09,230 - INFO - Request Parameters - Page 2:
2025-05-05 23:31:09,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 23:31:09,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746374400000, 1746460799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 23:31:09,636 - INFO - Response - Page 2:
2025-05-05 23:31:09,636 - INFO - 查询完成，共获取到 100 条记录
2025-05-05 23:31:09,636 - INFO - 获取到 100 条表单数据
2025-05-05 23:31:09,652 - INFO - 当前日期 2025-05-05 有 187 条MySQL数据需要处理
2025-05-05 23:31:09,652 - INFO - 开始批量插入 87 条新记录
2025-05-05 23:31:09,965 - INFO - 批量插入响应状态码: 200
2025-05-05 23:31:09,965 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 05 May 2025 15:31:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4188', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EAA97879-5E04-792F-BBCE-4BF6CB588751', 'x-acs-trace-id': '901a8adade760aaf1abc0df29feb3467', 'etag': '4chbdQZTdLrvvQHlMdH/FFQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-05 23:31:09,965 - INFO - 批量插入响应体: {'result': ['FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM7D', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM8D', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM9D', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMAD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMBD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMCD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMDD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMED', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMFD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMGD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMHD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMID', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMJD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMKD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMLD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMMD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMND', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMOD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMPD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMQD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMRD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMSD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMTD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMUD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMVD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMWD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMXD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMYD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMZD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM0E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM1E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM2E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM3E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM4E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM5E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM6E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM7E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM8E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM9E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMAE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMBE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMCE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMDE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMEE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMFE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMGE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMHE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMIE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMJE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMKE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMLE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMME', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMNE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMOE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMPE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMQE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMRE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMSE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMTE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMUE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMVE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMWE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMXE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMYE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMZE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM0F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM1F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM2F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM3F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM4F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM5F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM6F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM7F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM8F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM9F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMAF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMBF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMCF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMDF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMEF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMFF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMGF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMHF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMIF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMJF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMKF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMLF']}
2025-05-05 23:31:09,965 - INFO - 批量插入表单数据成功，批次 1，共 87 条记录
2025-05-05 23:31:09,965 - INFO - 成功插入的数据ID: ['FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM7D', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM8D', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM9D', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMAD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMBD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMCD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMDD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMED', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMFD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMGD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMHD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMID', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMJD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMKD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMLD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMMD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMND', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMOD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMPD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMQD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMRD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMSD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMTD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMUD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMVD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMWD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMXD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMYD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMZD', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM0E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM1E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM2E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM3E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM4E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM5E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM6E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM7E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM8E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAM9E', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMAE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMBE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMCE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMDE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMEE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMFE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMGE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMHE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMIE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMJE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMKE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMLE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMME', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMNE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMOE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMPE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V39FXM8BAMQE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMRE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMSE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMTE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMUE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMVE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMWE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMXE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMYE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMZE', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM0F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM1F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM2F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM3F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM4F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM5F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM6F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM7F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM8F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAM9F', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMAF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMBF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMCF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMDF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMEF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMFF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMGF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMHF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMIF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMJF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMKF', 'FINST-1PF66VA1UR5V261ADN2985KHLX3V3AFXM8BAMLF']
2025-05-05 23:31:14,986 - INFO - 批量插入完成，共 87 条记录
2025-05-05 23:31:14,986 - INFO - 日期 2025-05-05 处理完成 - 更新: 0 条，插入: 87 条，错误: 0 条
2025-05-05 23:31:14,986 - INFO - 数据同步完成！更新: 1 条，插入: 88 条，错误: 2 条
2025-05-05 23:32:15,064 - INFO - 开始同步昨天与今天的销售数据: 20250504 至 *************-05-05 23:32:15,064 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-05 23:32:15,064 - INFO - 查询参数: ('20250504', '20250505')
2025-05-05 23:32:15,111 - INFO - MySQL查询成功，时间段: 20250504 至 20250505，共获取 0 条记录
2025-05-05 23:32:15,111 - ERROR - 未获取到MySQL数据
2025-05-05 23:32:15,111 - INFO - 同步完成
