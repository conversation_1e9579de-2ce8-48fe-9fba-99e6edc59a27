2025-05-04 00:00:03,911 - INFO - =================使用默认全量同步=============
2025-05-04 00:00:05,084 - INFO - MySQL查询成功，共获取 3206 条记录
2025-05-04 00:00:05,084 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-04 00:00:05,116 - INFO - 开始处理日期: 2025-01
2025-05-04 00:00:05,116 - INFO - Request Parameters - Page 1:
2025-05-04 00:00:05,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:05,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:06,007 - INFO - Response - Page 1:
2025-05-04 00:00:06,210 - INFO - 第 1 页获取到 100 条记录
2025-05-04 00:00:06,210 - INFO - Request Parameters - Page 2:
2025-05-04 00:00:06,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:06,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:07,008 - INFO - Response - Page 2:
2025-05-04 00:00:07,212 - INFO - 第 2 页获取到 100 条记录
2025-05-04 00:00:07,212 - INFO - Request Parameters - Page 3:
2025-05-04 00:00:07,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:07,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:07,696 - INFO - Response - Page 3:
2025-05-04 00:00:07,900 - INFO - 第 3 页获取到 100 条记录
2025-05-04 00:00:07,900 - INFO - Request Parameters - Page 4:
2025-05-04 00:00:07,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:07,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:08,416 - INFO - Response - Page 4:
2025-05-04 00:00:08,619 - INFO - 第 4 页获取到 100 条记录
2025-05-04 00:00:08,619 - INFO - Request Parameters - Page 5:
2025-05-04 00:00:08,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:08,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:09,026 - INFO - Response - Page 5:
2025-05-04 00:00:09,229 - INFO - 第 5 页获取到 100 条记录
2025-05-04 00:00:09,229 - INFO - Request Parameters - Page 6:
2025-05-04 00:00:09,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:09,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:09,667 - INFO - Response - Page 6:
2025-05-04 00:00:09,871 - INFO - 第 6 页获取到 100 条记录
2025-05-04 00:00:09,871 - INFO - Request Parameters - Page 7:
2025-05-04 00:00:09,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:09,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:10,309 - INFO - Response - Page 7:
2025-05-04 00:00:10,512 - INFO - 第 7 页获取到 82 条记录
2025-05-04 00:00:10,512 - INFO - 查询完成，共获取到 682 条记录
2025-05-04 00:00:10,512 - INFO - 获取到 682 条表单数据
2025-05-04 00:00:10,512 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-04 00:00:10,528 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 00:00:10,528 - INFO - 开始处理日期: 2025-02
2025-05-04 00:00:10,528 - INFO - Request Parameters - Page 1:
2025-05-04 00:00:10,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:10,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:11,012 - INFO - Response - Page 1:
2025-05-04 00:00:11,216 - INFO - 第 1 页获取到 100 条记录
2025-05-04 00:00:11,216 - INFO - Request Parameters - Page 2:
2025-05-04 00:00:11,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:11,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:11,669 - INFO - Response - Page 2:
2025-05-04 00:00:11,873 - INFO - 第 2 页获取到 100 条记录
2025-05-04 00:00:11,873 - INFO - Request Parameters - Page 3:
2025-05-04 00:00:11,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:11,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:12,358 - INFO - Response - Page 3:
2025-05-04 00:00:12,561 - INFO - 第 3 页获取到 100 条记录
2025-05-04 00:00:12,561 - INFO - Request Parameters - Page 4:
2025-05-04 00:00:12,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:12,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:13,030 - INFO - Response - Page 4:
2025-05-04 00:00:13,234 - INFO - 第 4 页获取到 100 条记录
2025-05-04 00:00:13,234 - INFO - Request Parameters - Page 5:
2025-05-04 00:00:13,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:13,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:13,687 - INFO - Response - Page 5:
2025-05-04 00:00:13,890 - INFO - 第 5 页获取到 100 条记录
2025-05-04 00:00:13,890 - INFO - Request Parameters - Page 6:
2025-05-04 00:00:13,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:13,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:14,344 - INFO - Response - Page 6:
2025-05-04 00:00:14,547 - INFO - 第 6 页获取到 100 条记录
2025-05-04 00:00:14,547 - INFO - Request Parameters - Page 7:
2025-05-04 00:00:14,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:14,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:14,970 - INFO - Response - Page 7:
2025-05-04 00:00:15,173 - INFO - 第 7 页获取到 70 条记录
2025-05-04 00:00:15,173 - INFO - 查询完成，共获取到 670 条记录
2025-05-04 00:00:15,173 - INFO - 获取到 670 条表单数据
2025-05-04 00:00:15,173 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-04 00:00:15,189 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 00:00:15,189 - INFO - 开始处理日期: 2025-03
2025-05-04 00:00:15,189 - INFO - Request Parameters - Page 1:
2025-05-04 00:00:15,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:15,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:15,627 - INFO - Response - Page 1:
2025-05-04 00:00:15,830 - INFO - 第 1 页获取到 100 条记录
2025-05-04 00:00:15,830 - INFO - Request Parameters - Page 2:
2025-05-04 00:00:15,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:15,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:16,315 - INFO - Response - Page 2:
2025-05-04 00:00:16,518 - INFO - 第 2 页获取到 100 条记录
2025-05-04 00:00:16,518 - INFO - Request Parameters - Page 3:
2025-05-04 00:00:16,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:16,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:17,003 - INFO - Response - Page 3:
2025-05-04 00:00:17,207 - INFO - 第 3 页获取到 100 条记录
2025-05-04 00:00:17,207 - INFO - Request Parameters - Page 4:
2025-05-04 00:00:17,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:17,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:17,644 - INFO - Response - Page 4:
2025-05-04 00:00:17,848 - INFO - 第 4 页获取到 100 条记录
2025-05-04 00:00:17,848 - INFO - Request Parameters - Page 5:
2025-05-04 00:00:17,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:17,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:18,317 - INFO - Response - Page 5:
2025-05-04 00:00:18,520 - INFO - 第 5 页获取到 100 条记录
2025-05-04 00:00:18,520 - INFO - Request Parameters - Page 6:
2025-05-04 00:00:18,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:18,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:18,974 - INFO - Response - Page 6:
2025-05-04 00:00:19,177 - INFO - 第 6 页获取到 100 条记录
2025-05-04 00:00:19,177 - INFO - Request Parameters - Page 7:
2025-05-04 00:00:19,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:19,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:19,584 - INFO - Response - Page 7:
2025-05-04 00:00:19,787 - INFO - 第 7 页获取到 61 条记录
2025-05-04 00:00:19,787 - INFO - 查询完成，共获取到 661 条记录
2025-05-04 00:00:19,787 - INFO - 获取到 661 条表单数据
2025-05-04 00:00:19,787 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-04 00:00:19,803 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 00:00:19,803 - INFO - 开始处理日期: 2025-04
2025-05-04 00:00:19,803 - INFO - Request Parameters - Page 1:
2025-05-04 00:00:19,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:19,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:20,272 - INFO - Response - Page 1:
2025-05-04 00:00:20,476 - INFO - 第 1 页获取到 100 条记录
2025-05-04 00:00:20,476 - INFO - Request Parameters - Page 2:
2025-05-04 00:00:20,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:20,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:20,914 - INFO - Response - Page 2:
2025-05-04 00:00:21,117 - INFO - 第 2 页获取到 100 条记录
2025-05-04 00:00:21,117 - INFO - Request Parameters - Page 3:
2025-05-04 00:00:21,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:21,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:21,602 - INFO - Response - Page 3:
2025-05-04 00:00:21,805 - INFO - 第 3 页获取到 100 条记录
2025-05-04 00:00:21,805 - INFO - Request Parameters - Page 4:
2025-05-04 00:00:21,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:21,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:22,259 - INFO - Response - Page 4:
2025-05-04 00:00:22,462 - INFO - 第 4 页获取到 100 条记录
2025-05-04 00:00:22,462 - INFO - Request Parameters - Page 5:
2025-05-04 00:00:22,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:22,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:22,947 - INFO - Response - Page 5:
2025-05-04 00:00:23,150 - INFO - 第 5 页获取到 100 条记录
2025-05-04 00:00:23,150 - INFO - Request Parameters - Page 6:
2025-05-04 00:00:23,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:23,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:23,729 - INFO - Response - Page 6:
2025-05-04 00:00:23,948 - INFO - 第 6 页获取到 100 条记录
2025-05-04 00:00:23,948 - INFO - Request Parameters - Page 7:
2025-05-04 00:00:23,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:23,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:24,308 - INFO - Response - Page 7:
2025-05-04 00:00:24,511 - INFO - 第 7 页获取到 27 条记录
2025-05-04 00:00:24,511 - INFO - 查询完成，共获取到 627 条记录
2025-05-04 00:00:24,511 - INFO - 获取到 627 条表单数据
2025-05-04 00:00:24,511 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-04 00:00:24,527 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 00:00:24,527 - INFO - 开始处理日期: 2025-05
2025-05-04 00:00:24,527 - INFO - Request Parameters - Page 1:
2025-05-04 00:00:24,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:24,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:25,121 - INFO - Response - Page 1:
2025-05-04 00:00:25,325 - INFO - 第 1 页获取到 100 条记录
2025-05-04 00:00:25,325 - INFO - Request Parameters - Page 2:
2025-05-04 00:00:25,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:25,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:25,809 - INFO - Response - Page 2:
2025-05-04 00:00:26,013 - INFO - 第 2 页获取到 100 条记录
2025-05-04 00:00:26,013 - INFO - Request Parameters - Page 3:
2025-05-04 00:00:26,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:26,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:26,419 - INFO - Response - Page 3:
2025-05-04 00:00:26,623 - INFO - 第 3 页获取到 100 条记录
2025-05-04 00:00:26,623 - INFO - Request Parameters - Page 4:
2025-05-04 00:00:26,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:26,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:27,092 - INFO - Response - Page 4:
2025-05-04 00:00:27,295 - INFO - 第 4 页获取到 100 条记录
2025-05-04 00:00:27,295 - INFO - Request Parameters - Page 5:
2025-05-04 00:00:27,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:27,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:27,796 - INFO - Response - Page 5:
2025-05-04 00:00:27,999 - INFO - 第 5 页获取到 100 条记录
2025-05-04 00:00:27,999 - INFO - Request Parameters - Page 6:
2025-05-04 00:00:27,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 00:00:27,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 00:00:28,390 - INFO - Response - Page 6:
2025-05-04 00:00:28,594 - INFO - 第 6 页获取到 66 条记录
2025-05-04 00:00:28,594 - INFO - 查询完成，共获取到 566 条记录
2025-05-04 00:00:28,594 - INFO - 获取到 566 条表单数据
2025-05-04 00:00:28,594 - INFO - 当前日期 2025-05 有 566 条MySQL数据需要处理
2025-05-04 00:00:28,594 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-04 00:00:28,985 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-04 00:00:28,985 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 100000.0}, {'field': 'offline_amount', 'old_value': 600000.0, 'new_value': 1000000.0}, {'field': 'total_amount', 'old_value': 600000.0, 'new_value': 1100000.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 5}]
2025-05-04 00:00:28,985 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-04 00:00:29,360 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-04 00:00:29,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6940.5, 'new_value': 7348.5}, {'field': 'total_amount', 'old_value': 6940.5, 'new_value': 7348.5}, {'field': 'order_count', 'old_value': 19, 'new_value': 21}]
2025-05-04 00:00:29,360 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDC
2025-05-04 00:00:29,814 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDC
2025-05-04 00:00:29,814 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4661.0, 'new_value': 10330.0}, {'field': 'total_amount', 'old_value': 4661.0, 'new_value': 10330.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 203}]
2025-05-04 00:00:29,814 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-04 00:00:30,205 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-04 00:00:30,205 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10545.0, 'new_value': 13810.0}, {'field': 'offline_amount', 'old_value': 9832.0, 'new_value': 15675.28}, {'field': 'total_amount', 'old_value': 20377.0, 'new_value': 29485.28}, {'field': 'order_count', 'old_value': 405, 'new_value': 613}]
2025-05-04 00:00:30,205 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-04 00:00:30,596 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-04 00:00:30,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1620.0}, {'field': 'total_amount', 'old_value': 1220.0, 'new_value': 2840.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 31}]
2025-05-04 00:00:30,596 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-04 00:00:31,049 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-04 00:00:31,049 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4800.0, 'new_value': 5300.0}, {'field': 'total_amount', 'old_value': 4800.0, 'new_value': 5300.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-04 00:00:31,049 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-04 00:00:31,409 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-04 00:00:31,409 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2158.55, 'new_value': 3043.4}, {'field': 'offline_amount', 'old_value': 21671.0, 'new_value': 30316.0}, {'field': 'total_amount', 'old_value': 23829.55, 'new_value': 33359.4}, {'field': 'order_count', 'old_value': 464, 'new_value': 595}]
2025-05-04 00:00:31,425 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-04 00:00:31,831 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-04 00:00:31,831 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6076.0, 'new_value': 10053.0}, {'field': 'offline_amount', 'old_value': 9042.0, 'new_value': 13180.0}, {'field': 'total_amount', 'old_value': 15118.0, 'new_value': 23233.0}, {'field': 'order_count', 'old_value': 373, 'new_value': 555}]
2025-05-04 00:00:31,831 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-04 00:00:32,269 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-04 00:00:32,269 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4145.3, 'new_value': 6892.4}, {'field': 'offline_amount', 'old_value': 10144.0, 'new_value': 15508.0}, {'field': 'total_amount', 'old_value': 14289.3, 'new_value': 22400.4}, {'field': 'order_count', 'old_value': 268, 'new_value': 402}]
2025-05-04 00:00:32,269 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-04 00:00:32,645 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-04 00:00:32,645 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1056.0}, {'field': 'offline_amount', 'old_value': 6522.74, 'new_value': 14713.46}, {'field': 'total_amount', 'old_value': 6522.74, 'new_value': 15769.46}, {'field': 'order_count', 'old_value': 22, 'new_value': 37}]
2025-05-04 00:00:32,645 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-04 00:00:33,036 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-04 00:00:33,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 276.0, 'new_value': 552.0}, {'field': 'total_amount', 'old_value': 276.0, 'new_value': 552.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-04 00:00:33,036 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-04 00:00:33,411 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-04 00:00:33,411 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7752.0, 'new_value': 11018.0}, {'field': 'total_amount', 'old_value': 7752.0, 'new_value': 11018.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 87}]
2025-05-04 00:00:33,411 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-04 00:00:33,802 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-04 00:00:33,802 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5461.15, 'new_value': 7020.9}, {'field': 'offline_amount', 'old_value': 77757.05, 'new_value': 102978.3}, {'field': 'total_amount', 'old_value': 83218.2, 'new_value': 109999.2}, {'field': 'order_count', 'old_value': 326, 'new_value': 429}]
2025-05-04 00:00:33,802 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-04 00:00:34,162 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-04 00:00:34,162 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2350.32, 'new_value': 2638.32}, {'field': 'offline_amount', 'old_value': 9473.0, 'new_value': 11066.0}, {'field': 'total_amount', 'old_value': 11823.32, 'new_value': 13704.32}, {'field': 'order_count', 'old_value': 12, 'new_value': 15}]
2025-05-04 00:00:34,162 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-04 00:00:34,537 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-04 00:00:34,537 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2206.55, 'new_value': 2761.45}, {'field': 'total_amount', 'old_value': 2206.55, 'new_value': 2761.45}, {'field': 'order_count', 'old_value': 34, 'new_value': 42}]
2025-05-04 00:00:34,537 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-04 00:00:34,928 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-04 00:00:34,928 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5109.21, 'new_value': 7577.06}, {'field': 'offline_amount', 'old_value': 13588.29, 'new_value': 18459.74}, {'field': 'total_amount', 'old_value': 18697.5, 'new_value': 26036.8}, {'field': 'order_count', 'old_value': 576, 'new_value': 805}]
2025-05-04 00:00:34,928 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-04 00:00:35,351 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-04 00:00:35,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48039.0, 'new_value': 75486.0}, {'field': 'total_amount', 'old_value': 48039.0, 'new_value': 75486.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 32}]
2025-05-04 00:00:35,351 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-04 00:00:35,820 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-04 00:00:35,820 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14295.26, 'new_value': 21503.25}, {'field': 'offline_amount', 'old_value': 73824.31, 'new_value': 102421.11}, {'field': 'total_amount', 'old_value': 88119.57, 'new_value': 123924.36}, {'field': 'order_count', 'old_value': 505, 'new_value': 734}]
2025-05-04 00:00:35,820 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-04 00:00:36,289 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-04 00:00:36,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7553.42, 'new_value': 9973.08}, {'field': 'total_amount', 'old_value': 7553.42, 'new_value': 9973.08}, {'field': 'order_count', 'old_value': 70, 'new_value': 91}]
2025-05-04 00:00:36,289 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-04 00:00:36,649 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-04 00:00:36,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 857.0, 'new_value': 1647.0}, {'field': 'total_amount', 'old_value': 857.0, 'new_value': 1647.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-04 00:00:36,649 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-04 00:00:37,009 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-04 00:00:37,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5867.0, 'new_value': 8408.0}, {'field': 'total_amount', 'old_value': 5867.0, 'new_value': 8408.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 42}]
2025-05-04 00:00:37,009 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-04 00:00:37,447 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-04 00:00:37,447 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18014.0, 'new_value': 21438.0}, {'field': 'total_amount', 'old_value': 18015.0, 'new_value': 21439.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 00:00:37,447 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-04 00:00:37,838 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-04 00:00:37,838 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29105.69, 'new_value': 37565.49}, {'field': 'total_amount', 'old_value': 29105.69, 'new_value': 37565.49}, {'field': 'order_count', 'old_value': 107, 'new_value': 118}]
2025-05-04 00:00:37,838 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-04 00:00:38,244 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-04 00:00:38,244 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1226.0, 'new_value': 4202.0}, {'field': 'total_amount', 'old_value': 1355.0, 'new_value': 4331.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-04 00:00:38,244 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-04 00:00:38,620 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-04 00:00:38,620 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 179169.52, 'new_value': 234587.52}, {'field': 'offline_amount', 'old_value': 26635.0, 'new_value': 54300.91}, {'field': 'total_amount', 'old_value': 205804.52, 'new_value': 288888.43}, {'field': 'order_count', 'old_value': 2335, 'new_value': 3086}]
2025-05-04 00:00:38,620 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-04 00:00:39,011 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-04 00:00:39,011 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5897.0, 'new_value': 12362.9}, {'field': 'offline_amount', 'old_value': 1.0, 'new_value': 25.0}, {'field': 'total_amount', 'old_value': 5898.0, 'new_value': 12387.9}, {'field': 'order_count', 'old_value': 51, 'new_value': 53}]
2025-05-04 00:00:39,011 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-04 00:00:39,386 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-04 00:00:39,386 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9298.0, 'new_value': 10997.0}, {'field': 'total_amount', 'old_value': 9298.0, 'new_value': 10997.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 00:00:39,386 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-04 00:00:39,809 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-04 00:00:39,809 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 540.0, 'new_value': 1134.0}, {'field': 'offline_amount', 'old_value': 1480.0, 'new_value': 4318.0}, {'field': 'total_amount', 'old_value': 2020.0, 'new_value': 5452.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-05-04 00:00:39,809 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-04 00:00:40,168 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-04 00:00:40,168 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9709.0, 'new_value': 13506.0}, {'field': 'total_amount', 'old_value': 9709.0, 'new_value': 13506.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 24}]
2025-05-04 00:00:40,168 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-04 00:00:40,575 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-04 00:00:40,575 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136768.0, 'new_value': 180259.0}, {'field': 'total_amount', 'old_value': 136768.0, 'new_value': 180259.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 23}]
2025-05-04 00:00:40,575 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-04 00:00:41,123 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-04 00:00:41,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24434.7, 'new_value': 38213.0}, {'field': 'total_amount', 'old_value': 24434.7, 'new_value': 38213.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 80}]
2025-05-04 00:00:41,123 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-04 00:00:41,576 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-04 00:00:41,576 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24888.75, 'new_value': 38452.71}, {'field': 'offline_amount', 'old_value': 19342.26, 'new_value': 24064.46}, {'field': 'total_amount', 'old_value': 44231.01, 'new_value': 62517.17}, {'field': 'order_count', 'old_value': 183, 'new_value': 251}]
2025-05-04 00:00:41,576 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-04 00:00:42,030 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-04 00:00:42,030 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49310.57, 'new_value': 67144.47}, {'field': 'total_amount', 'old_value': 49310.57, 'new_value': 67144.47}, {'field': 'order_count', 'old_value': 266, 'new_value': 388}]
2025-05-04 00:00:42,030 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-04 00:00:42,452 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-04 00:00:42,452 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1476.09, 'new_value': 2093.95}, {'field': 'offline_amount', 'old_value': 36254.99, 'new_value': 56561.03}, {'field': 'total_amount', 'old_value': 37731.08, 'new_value': 58654.98}, {'field': 'order_count', 'old_value': 182, 'new_value': 283}]
2025-05-04 00:00:42,452 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-04 00:00:42,796 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-04 00:00:42,796 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2286.31, 'new_value': 4214.36}, {'field': 'offline_amount', 'old_value': 70127.6, 'new_value': 106575.23}, {'field': 'total_amount', 'old_value': 72413.91, 'new_value': 110789.59}, {'field': 'order_count', 'old_value': 448, 'new_value': 678}]
2025-05-04 00:00:42,796 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-04 00:00:43,203 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-04 00:00:43,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5423.0, 'new_value': 7972.0}, {'field': 'total_amount', 'old_value': 5423.0, 'new_value': 7972.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 14}]
2025-05-04 00:00:43,203 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-04 00:00:43,578 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-04 00:00:43,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6920.0, 'new_value': 11495.0}, {'field': 'total_amount', 'old_value': 6920.0, 'new_value': 11495.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 47}]
2025-05-04 00:00:43,578 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-04 00:00:43,985 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-04 00:00:43,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62628.0, 'new_value': 72239.0}, {'field': 'total_amount', 'old_value': 62628.0, 'new_value': 72239.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 53}]
2025-05-04 00:00:43,985 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-04 00:00:44,423 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-04 00:00:44,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63222.0, 'new_value': 89982.6}, {'field': 'total_amount', 'old_value': 63222.0, 'new_value': 89982.6}, {'field': 'order_count', 'old_value': 703, 'new_value': 982}]
2025-05-04 00:00:44,423 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-04 00:00:44,861 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-04 00:00:44,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 274706.68, 'new_value': 382715.92}, {'field': 'total_amount', 'old_value': 274706.68, 'new_value': 382715.92}, {'field': 'order_count', 'old_value': 1565, 'new_value': 2357}]
2025-05-04 00:00:44,861 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-04 00:00:45,314 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-04 00:00:45,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48513.6, 'new_value': 67006.6}, {'field': 'total_amount', 'old_value': 48513.6, 'new_value': 67006.6}, {'field': 'order_count', 'old_value': 76, 'new_value': 111}]
2025-05-04 00:00:45,314 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-04 00:00:45,674 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-04 00:00:45,674 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24015.0, 'new_value': 32274.0}, {'field': 'total_amount', 'old_value': 24015.0, 'new_value': 32274.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 19}]
2025-05-04 00:00:45,674 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-04 00:00:46,065 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-04 00:00:46,065 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19034.0, 'new_value': 25935.0}, {'field': 'total_amount', 'old_value': 19034.0, 'new_value': 25935.0}, {'field': 'order_count', 'old_value': 1774, 'new_value': 2398}]
2025-05-04 00:00:46,065 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-04 00:00:46,425 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-04 00:00:46,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2966.6, 'new_value': 3708.2}, {'field': 'total_amount', 'old_value': 2966.6, 'new_value': 3708.2}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 00:00:46,425 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-04 00:00:46,816 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-04 00:00:46,816 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15792.0, 'new_value': 22174.0}, {'field': 'total_amount', 'old_value': 15792.0, 'new_value': 22174.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 27}]
2025-05-04 00:00:46,816 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-04 00:00:47,191 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-04 00:00:47,191 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54388.05, 'new_value': 81237.13}, {'field': 'total_amount', 'old_value': 54388.05, 'new_value': 81237.13}, {'field': 'order_count', 'old_value': 242, 'new_value': 357}]
2025-05-04 00:00:47,191 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-04 00:00:47,583 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-04 00:00:47,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13197.0, 'new_value': 17596.0}, {'field': 'total_amount', 'old_value': 13197.0, 'new_value': 17596.0}, {'field': 'order_count', 'old_value': 13197, 'new_value': 17596}]
2025-05-04 00:00:47,583 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-04 00:00:47,974 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-04 00:00:47,974 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28294.0, 'new_value': 70569.0}, {'field': 'total_amount', 'old_value': 28294.0, 'new_value': 70569.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-04 00:00:47,989 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-04 00:00:48,380 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-04 00:00:48,380 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1408.0, 'new_value': 8590.0}, {'field': 'total_amount', 'old_value': 1408.0, 'new_value': 8590.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 8}]
2025-05-04 00:00:48,380 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-04 00:00:48,849 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-04 00:00:48,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9221.9, 'new_value': 10884.7}, {'field': 'total_amount', 'old_value': 9355.1, 'new_value': 11017.9}, {'field': 'order_count', 'old_value': 29, 'new_value': 34}]
2025-05-04 00:00:48,849 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2V
2025-05-04 00:00:49,225 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2V
2025-05-04 00:00:49,225 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 399900.0, 'new_value': 1599900.0}, {'field': 'total_amount', 'old_value': 399900.0, 'new_value': 1599900.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 4}]
2025-05-04 00:00:49,225 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-04 00:00:49,632 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-04 00:00:49,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15462.0, 'new_value': 21262.0}, {'field': 'total_amount', 'old_value': 15462.0, 'new_value': 21262.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-05-04 00:00:49,632 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-04 00:00:50,101 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-04 00:00:50,101 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5711.93, 'new_value': 8968.58}, {'field': 'offline_amount', 'old_value': 37307.54, 'new_value': 50805.56}, {'field': 'total_amount', 'old_value': 43019.47, 'new_value': 59774.14}, {'field': 'order_count', 'old_value': 561, 'new_value': 749}]
2025-05-04 00:00:50,101 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-04 00:00:50,523 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-04 00:00:50,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12853.8, 'new_value': 19575.3}, {'field': 'total_amount', 'old_value': 12853.8, 'new_value': 19575.3}, {'field': 'order_count', 'old_value': 32, 'new_value': 49}]
2025-05-04 00:00:50,523 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-04 00:00:50,883 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-04 00:00:50,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3890.36, 'new_value': 5601.62}, {'field': 'total_amount', 'old_value': 3890.36, 'new_value': 5601.62}, {'field': 'order_count', 'old_value': 78, 'new_value': 108}]
2025-05-04 00:00:50,883 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-04 00:00:51,290 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-04 00:00:51,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4886.38, 'new_value': 7079.94}, {'field': 'total_amount', 'old_value': 4886.38, 'new_value': 7079.94}, {'field': 'order_count', 'old_value': 20, 'new_value': 29}]
2025-05-04 00:00:51,290 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-04 00:00:51,728 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-04 00:00:51,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14723.0, 'new_value': 24127.0}, {'field': 'total_amount', 'old_value': 16055.39, 'new_value': 25459.39}, {'field': 'order_count', 'old_value': 18, 'new_value': 24}]
2025-05-04 00:00:51,728 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-04 00:00:52,197 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-04 00:00:52,197 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47643.0, 'new_value': 58587.0}, {'field': 'total_amount', 'old_value': 47643.0, 'new_value': 58587.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 10}]
2025-05-04 00:00:52,197 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-04 00:00:52,572 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-04 00:00:52,572 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10236.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 10236.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-05-04 00:00:52,572 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-04 00:00:52,963 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-04 00:00:52,963 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26478.0, 'new_value': 38339.0}, {'field': 'total_amount', 'old_value': 26808.0, 'new_value': 38669.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 31}]
2025-05-04 00:00:52,963 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-04 00:00:53,339 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-04 00:00:53,339 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2299.9, 'new_value': 2999.9}, {'field': 'offline_amount', 'old_value': 256.0, 'new_value': 273.0}, {'field': 'total_amount', 'old_value': 2555.9, 'new_value': 3272.9}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-05-04 00:00:53,339 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-04 00:00:53,698 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-04 00:00:53,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32722.0, 'new_value': 44596.0}, {'field': 'total_amount', 'old_value': 32722.0, 'new_value': 44596.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 69}]
2025-05-04 00:00:53,698 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-04 00:00:54,136 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-04 00:00:54,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49457.14, 'new_value': 67713.37}, {'field': 'total_amount', 'old_value': 49457.14, 'new_value': 67713.37}, {'field': 'order_count', 'old_value': 307, 'new_value': 444}]
2025-05-04 00:00:54,136 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-04 00:00:54,574 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-04 00:00:54,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17856.0, 'new_value': 26562.0}, {'field': 'total_amount', 'old_value': 17856.0, 'new_value': 26562.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 125}]
2025-05-04 00:00:54,574 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-04 00:00:54,997 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-04 00:00:54,997 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 234.0, 'new_value': 312.0}, {'field': 'offline_amount', 'old_value': 2983.8, 'new_value': 3855.6}, {'field': 'total_amount', 'old_value': 3217.8, 'new_value': 4167.6}, {'field': 'order_count', 'old_value': 41, 'new_value': 57}]
2025-05-04 00:00:54,997 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-04 00:00:55,419 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-04 00:00:55,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4808.0, 'new_value': 7616.0}, {'field': 'total_amount', 'old_value': 4892.0, 'new_value': 7700.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 65}]
2025-05-04 00:00:55,435 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-04 00:00:55,841 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-04 00:00:55,841 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3649.78, 'new_value': 6191.85}, {'field': 'offline_amount', 'old_value': 59022.9, 'new_value': 88421.23}, {'field': 'total_amount', 'old_value': 62672.68, 'new_value': 94613.08}, {'field': 'order_count', 'old_value': 252, 'new_value': 380}]
2025-05-04 00:00:55,841 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-04 00:00:56,217 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-04 00:00:56,217 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67539.0, 'new_value': 111509.0}, {'field': 'total_amount', 'old_value': 67539.0, 'new_value': 111509.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 31}]
2025-05-04 00:00:56,217 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-04 00:00:56,670 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-04 00:00:56,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109253.4, 'new_value': 153648.71}, {'field': 'total_amount', 'old_value': 109253.4, 'new_value': 153648.71}, {'field': 'order_count', 'old_value': 678, 'new_value': 953}]
2025-05-04 00:00:56,670 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-04 00:00:57,155 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-04 00:00:57,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14420.0, 'new_value': 21023.0}, {'field': 'total_amount', 'old_value': 14420.0, 'new_value': 21023.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 48}]
2025-05-04 00:00:57,155 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-04 00:00:57,562 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-04 00:00:57,562 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2347.7, 'new_value': 3931.4}, {'field': 'total_amount', 'old_value': 2347.7, 'new_value': 3931.4}, {'field': 'order_count', 'old_value': 33, 'new_value': 56}]
2025-05-04 00:00:57,562 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-04 00:00:57,969 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-04 00:00:57,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41763.4, 'new_value': 52655.8}, {'field': 'total_amount', 'old_value': 41763.4, 'new_value': 52655.8}, {'field': 'order_count', 'old_value': 68, 'new_value': 85}]
2025-05-04 00:00:57,969 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-04 00:00:58,313 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-04 00:00:58,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39057.0, 'new_value': 46617.0}, {'field': 'total_amount', 'old_value': 39057.0, 'new_value': 46617.0}, {'field': 'order_count', 'old_value': 1271, 'new_value': 1518}]
2025-05-04 00:00:58,313 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-04 00:00:58,719 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-04 00:00:58,719 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106.0, 'new_value': 156.0}, {'field': 'offline_amount', 'old_value': 70896.0, 'new_value': 101137.0}, {'field': 'total_amount', 'old_value': 71002.0, 'new_value': 101293.0}, {'field': 'order_count', 'old_value': 310, 'new_value': 434}]
2025-05-04 00:00:58,719 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-04 00:00:59,095 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-04 00:00:59,095 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49869.44, 'new_value': 72528.2}, {'field': 'offline_amount', 'old_value': 2854.6, 'new_value': 3906.6}, {'field': 'total_amount', 'old_value': 52724.04, 'new_value': 76434.8}, {'field': 'order_count', 'old_value': 96, 'new_value': 135}]
2025-05-04 00:00:59,095 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-04 00:00:59,439 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-04 00:00:59,439 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15767.0, 'new_value': 21869.0}, {'field': 'offline_amount', 'old_value': 14663.82, 'new_value': 21594.62}, {'field': 'total_amount', 'old_value': 30430.82, 'new_value': 43463.62}, {'field': 'order_count', 'old_value': 204, 'new_value': 281}]
2025-05-04 00:00:59,439 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-04 00:00:59,814 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-04 00:00:59,814 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114275.0, 'new_value': 167724.0}, {'field': 'total_amount', 'old_value': 114275.0, 'new_value': 167724.0}, {'field': 'order_count', 'old_value': 120, 'new_value': 160}]
2025-05-04 00:00:59,814 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-04 00:01:00,268 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-04 00:01:00,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85595.0, 'new_value': 119094.0}, {'field': 'total_amount', 'old_value': 85595.0, 'new_value': 119094.0}, {'field': 'order_count', 'old_value': 529, 'new_value': 765}]
2025-05-04 00:01:00,268 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-04 00:01:00,628 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-04 00:01:00,628 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121383.0, 'new_value': 143380.0}, {'field': 'total_amount', 'old_value': 121383.0, 'new_value': 143380.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-05-04 00:01:00,628 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-04 00:01:00,987 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-04 00:01:00,987 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 9500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 9500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-04 00:01:00,987 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-04 00:01:01,363 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-04 00:01:01,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 350000.0, 'new_value': 470000.0}, {'field': 'total_amount', 'old_value': 350000.0, 'new_value': 470000.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 00:01:01,363 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-04 00:01:01,769 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-04 00:01:01,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43936.4, 'new_value': 59699.5}, {'field': 'total_amount', 'old_value': 43936.4, 'new_value': 59699.5}, {'field': 'order_count', 'old_value': 229, 'new_value': 314}]
2025-05-04 00:01:01,769 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-04 00:01:02,145 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-04 00:01:02,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33868.0, 'new_value': 49580.0}, {'field': 'total_amount', 'old_value': 33868.0, 'new_value': 49580.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-04 00:01:02,145 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-04 00:01:02,583 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-04 00:01:02,583 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31239.8, 'new_value': 61372.5}, {'field': 'offline_amount', 'old_value': 120154.4, 'new_value': 151978.4}, {'field': 'total_amount', 'old_value': 151394.2, 'new_value': 213350.9}, {'field': 'order_count', 'old_value': 778, 'new_value': 1436}]
2025-05-04 00:01:02,583 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-04 00:01:02,958 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-04 00:01:02,958 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3455.32, 'new_value': 4766.91}, {'field': 'offline_amount', 'old_value': 54939.46, 'new_value': 77452.42}, {'field': 'total_amount', 'old_value': 58394.78, 'new_value': 82219.33}, {'field': 'order_count', 'old_value': 344, 'new_value': 470}]
2025-05-04 00:01:02,958 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-04 00:01:03,334 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-04 00:01:03,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14749.5, 'new_value': 24888.7}, {'field': 'total_amount', 'old_value': 15571.3, 'new_value': 25710.5}, {'field': 'order_count', 'old_value': 95, 'new_value': 149}]
2025-05-04 00:01:03,334 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-04 00:01:03,756 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-04 00:01:03,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8800.0, 'new_value': 11664.0}, {'field': 'total_amount', 'old_value': 8800.0, 'new_value': 11664.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 32}]
2025-05-04 00:01:03,756 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-04 00:01:04,131 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-04 00:01:04,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130000.0, 'new_value': 155000.0}, {'field': 'total_amount', 'old_value': 130000.0, 'new_value': 155000.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 00:01:04,131 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-04 00:01:04,554 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-04 00:01:04,554 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150000.0, 'new_value': 180000.0}, {'field': 'total_amount', 'old_value': 150000.0, 'new_value': 180000.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 00:01:04,554 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-04 00:01:04,945 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-04 00:01:04,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 748674.0, 'new_value': 1195817.0}, {'field': 'total_amount', 'old_value': 748674.0, 'new_value': 1195817.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 00:01:04,945 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-04 00:01:05,304 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-04 00:01:05,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5962.0, 'new_value': 6736.0}, {'field': 'total_amount', 'old_value': 5962.0, 'new_value': 6736.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-04 00:01:05,304 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-04 00:01:05,805 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-04 00:01:05,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23160.1, 'new_value': 40677.6}, {'field': 'total_amount', 'old_value': 45647.5, 'new_value': 63165.0}, {'field': 'order_count', 'old_value': 235, 'new_value': 328}]
2025-05-04 00:01:05,805 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-04 00:01:06,274 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-04 00:01:06,274 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26679.05, 'new_value': 43177.14}, {'field': 'offline_amount', 'old_value': 86000.0, 'new_value': 122000.0}, {'field': 'total_amount', 'old_value': 112679.05, 'new_value': 165177.14}, {'field': 'order_count', 'old_value': 168, 'new_value': 257}]
2025-05-04 00:01:06,274 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-04 00:01:06,665 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-04 00:01:06,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 345439.0, 'new_value': 487348.0}, {'field': 'total_amount', 'old_value': 345439.0, 'new_value': 487348.0}, {'field': 'order_count', 'old_value': 1217, 'new_value': 1779}]
2025-05-04 00:01:06,681 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-04 00:01:07,056 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-04 00:01:07,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49123.46, 'new_value': 69738.96}, {'field': 'total_amount', 'old_value': 49123.46, 'new_value': 69738.96}, {'field': 'order_count', 'old_value': 237, 'new_value': 352}]
2025-05-04 00:01:07,056 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-04 00:01:07,432 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-04 00:01:07,432 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2985.42, 'new_value': 5230.27}, {'field': 'offline_amount', 'old_value': 8335.07, 'new_value': 11514.11}, {'field': 'total_amount', 'old_value': 11320.49, 'new_value': 16744.38}, {'field': 'order_count', 'old_value': 810, 'new_value': 1196}]
2025-05-04 00:01:07,432 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUP
2025-05-04 00:01:07,823 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUP
2025-05-04 00:01:07,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4750.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4750.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-04 00:01:07,823 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-04 00:01:08,167 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-04 00:01:08,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81295.0, 'new_value': 106875.92}, {'field': 'total_amount', 'old_value': 81295.0, 'new_value': 106875.92}]
2025-05-04 00:01:08,167 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-04 00:01:08,527 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-04 00:01:08,527 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2450.0, 'new_value': 6694.0}, {'field': 'total_amount', 'old_value': 2450.0, 'new_value': 6694.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 00:01:08,527 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-04 00:01:08,902 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-04 00:01:08,902 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46944.0, 'new_value': 48951.0}, {'field': 'total_amount', 'old_value': 46944.0, 'new_value': 48951.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-05-04 00:01:08,902 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-04 00:01:09,309 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-04 00:01:09,309 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64396.0, 'new_value': 102209.0}, {'field': 'total_amount', 'old_value': 64396.0, 'new_value': 102209.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 75}]
2025-05-04 00:01:09,309 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-04 00:01:09,700 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-04 00:01:09,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83981.0, 'new_value': 113540.0}, {'field': 'total_amount', 'old_value': 83981.0, 'new_value': 113540.0}, {'field': 'order_count', 'old_value': 312, 'new_value': 504}]
2025-05-04 00:01:09,700 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-04 00:01:10,106 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-04 00:01:10,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49049.0, 'new_value': 76101.0}, {'field': 'total_amount', 'old_value': 49049.0, 'new_value': 76101.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 17}]
2025-05-04 00:01:10,106 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-04 00:01:10,466 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-04 00:01:10,466 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 2011.0}, {'field': 'offline_amount', 'old_value': 5864.0, 'new_value': 7730.0}, {'field': 'total_amount', 'old_value': 5864.0, 'new_value': 9741.0}, {'field': 'order_count', 'old_value': 498, 'new_value': 695}]
2025-05-04 00:01:10,466 - INFO - 日期 2025-05 处理完成 - 更新: 104 条，插入: 0 条，错误: 0 条
2025-05-04 00:01:10,466 - INFO - 数据同步完成！更新: 104 条，插入: 0 条，错误: 0 条
2025-05-04 00:01:10,466 - INFO - =================同步完成====================
2025-05-04 03:00:04,110 - INFO - =================使用默认全量同步=============
2025-05-04 03:00:05,283 - INFO - MySQL查询成功，共获取 3206 条记录
2025-05-04 03:00:05,283 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-04 03:00:05,314 - INFO - 开始处理日期: 2025-01
2025-05-04 03:00:05,314 - INFO - Request Parameters - Page 1:
2025-05-04 03:00:05,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:05,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:06,253 - INFO - Response - Page 1:
2025-05-04 03:00:06,456 - INFO - 第 1 页获取到 100 条记录
2025-05-04 03:00:06,456 - INFO - Request Parameters - Page 2:
2025-05-04 03:00:06,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:06,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:07,144 - INFO - Response - Page 2:
2025-05-04 03:00:07,348 - INFO - 第 2 页获取到 100 条记录
2025-05-04 03:00:07,348 - INFO - Request Parameters - Page 3:
2025-05-04 03:00:07,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:07,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:08,036 - INFO - Response - Page 3:
2025-05-04 03:00:08,239 - INFO - 第 3 页获取到 100 条记录
2025-05-04 03:00:08,239 - INFO - Request Parameters - Page 4:
2025-05-04 03:00:08,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:08,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:08,724 - INFO - Response - Page 4:
2025-05-04 03:00:08,927 - INFO - 第 4 页获取到 100 条记录
2025-05-04 03:00:08,927 - INFO - Request Parameters - Page 5:
2025-05-04 03:00:08,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:08,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:09,459 - INFO - Response - Page 5:
2025-05-04 03:00:09,663 - INFO - 第 5 页获取到 100 条记录
2025-05-04 03:00:09,663 - INFO - Request Parameters - Page 6:
2025-05-04 03:00:09,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:09,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:10,132 - INFO - Response - Page 6:
2025-05-04 03:00:10,335 - INFO - 第 6 页获取到 100 条记录
2025-05-04 03:00:10,335 - INFO - Request Parameters - Page 7:
2025-05-04 03:00:10,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:10,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:10,742 - INFO - Response - Page 7:
2025-05-04 03:00:10,945 - INFO - 第 7 页获取到 82 条记录
2025-05-04 03:00:10,945 - INFO - 查询完成，共获取到 682 条记录
2025-05-04 03:00:10,945 - INFO - 获取到 682 条表单数据
2025-05-04 03:00:10,945 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-04 03:00:10,961 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 03:00:10,961 - INFO - 开始处理日期: 2025-02
2025-05-04 03:00:10,961 - INFO - Request Parameters - Page 1:
2025-05-04 03:00:10,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:10,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:11,477 - INFO - Response - Page 1:
2025-05-04 03:00:11,680 - INFO - 第 1 页获取到 100 条记录
2025-05-04 03:00:11,680 - INFO - Request Parameters - Page 2:
2025-05-04 03:00:11,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:11,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:12,118 - INFO - Response - Page 2:
2025-05-04 03:00:12,322 - INFO - 第 2 页获取到 100 条记录
2025-05-04 03:00:12,322 - INFO - Request Parameters - Page 3:
2025-05-04 03:00:12,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:12,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:12,744 - INFO - Response - Page 3:
2025-05-04 03:00:12,947 - INFO - 第 3 页获取到 100 条记录
2025-05-04 03:00:12,947 - INFO - Request Parameters - Page 4:
2025-05-04 03:00:12,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:12,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:13,448 - INFO - Response - Page 4:
2025-05-04 03:00:13,651 - INFO - 第 4 页获取到 100 条记录
2025-05-04 03:00:13,651 - INFO - Request Parameters - Page 5:
2025-05-04 03:00:13,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:13,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:14,089 - INFO - Response - Page 5:
2025-05-04 03:00:14,292 - INFO - 第 5 页获取到 100 条记录
2025-05-04 03:00:14,292 - INFO - Request Parameters - Page 6:
2025-05-04 03:00:14,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:14,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:14,762 - INFO - Response - Page 6:
2025-05-04 03:00:14,965 - INFO - 第 6 页获取到 100 条记录
2025-05-04 03:00:14,965 - INFO - Request Parameters - Page 7:
2025-05-04 03:00:14,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:14,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:15,434 - INFO - Response - Page 7:
2025-05-04 03:00:15,638 - INFO - 第 7 页获取到 70 条记录
2025-05-04 03:00:15,638 - INFO - 查询完成，共获取到 670 条记录
2025-05-04 03:00:15,638 - INFO - 获取到 670 条表单数据
2025-05-04 03:00:15,638 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-04 03:00:15,653 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 03:00:15,653 - INFO - 开始处理日期: 2025-03
2025-05-04 03:00:15,653 - INFO - Request Parameters - Page 1:
2025-05-04 03:00:15,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:15,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:16,107 - INFO - Response - Page 1:
2025-05-04 03:00:16,310 - INFO - 第 1 页获取到 100 条记录
2025-05-04 03:00:16,310 - INFO - Request Parameters - Page 2:
2025-05-04 03:00:16,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:16,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:16,842 - INFO - Response - Page 2:
2025-05-04 03:00:17,045 - INFO - 第 2 页获取到 100 条记录
2025-05-04 03:00:17,045 - INFO - Request Parameters - Page 3:
2025-05-04 03:00:17,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:17,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:17,499 - INFO - Response - Page 3:
2025-05-04 03:00:17,702 - INFO - 第 3 页获取到 100 条记录
2025-05-04 03:00:17,702 - INFO - Request Parameters - Page 4:
2025-05-04 03:00:17,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:17,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:18,172 - INFO - Response - Page 4:
2025-05-04 03:00:18,375 - INFO - 第 4 页获取到 100 条记录
2025-05-04 03:00:18,375 - INFO - Request Parameters - Page 5:
2025-05-04 03:00:18,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:18,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:18,875 - INFO - Response - Page 5:
2025-05-04 03:00:19,079 - INFO - 第 5 页获取到 100 条记录
2025-05-04 03:00:19,079 - INFO - Request Parameters - Page 6:
2025-05-04 03:00:19,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:19,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:19,564 - INFO - Response - Page 6:
2025-05-04 03:00:19,767 - INFO - 第 6 页获取到 100 条记录
2025-05-04 03:00:19,767 - INFO - Request Parameters - Page 7:
2025-05-04 03:00:19,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:19,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:20,174 - INFO - Response - Page 7:
2025-05-04 03:00:20,377 - INFO - 第 7 页获取到 61 条记录
2025-05-04 03:00:20,377 - INFO - 查询完成，共获取到 661 条记录
2025-05-04 03:00:20,377 - INFO - 获取到 661 条表单数据
2025-05-04 03:00:20,377 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-04 03:00:20,393 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 03:00:20,393 - INFO - 开始处理日期: 2025-04
2025-05-04 03:00:20,393 - INFO - Request Parameters - Page 1:
2025-05-04 03:00:20,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:20,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:20,909 - INFO - Response - Page 1:
2025-05-04 03:00:21,112 - INFO - 第 1 页获取到 100 条记录
2025-05-04 03:00:21,112 - INFO - Request Parameters - Page 2:
2025-05-04 03:00:21,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:21,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:21,550 - INFO - Response - Page 2:
2025-05-04 03:00:21,753 - INFO - 第 2 页获取到 100 条记录
2025-05-04 03:00:21,753 - INFO - Request Parameters - Page 3:
2025-05-04 03:00:21,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:21,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:22,223 - INFO - Response - Page 3:
2025-05-04 03:00:22,426 - INFO - 第 3 页获取到 100 条记录
2025-05-04 03:00:22,426 - INFO - Request Parameters - Page 4:
2025-05-04 03:00:22,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:22,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:22,895 - INFO - Response - Page 4:
2025-05-04 03:00:23,099 - INFO - 第 4 页获取到 100 条记录
2025-05-04 03:00:23,099 - INFO - Request Parameters - Page 5:
2025-05-04 03:00:23,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:23,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:23,584 - INFO - Response - Page 5:
2025-05-04 03:00:23,787 - INFO - 第 5 页获取到 100 条记录
2025-05-04 03:00:23,787 - INFO - Request Parameters - Page 6:
2025-05-04 03:00:23,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:23,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:24,319 - INFO - Response - Page 6:
2025-05-04 03:00:24,522 - INFO - 第 6 页获取到 100 条记录
2025-05-04 03:00:24,522 - INFO - Request Parameters - Page 7:
2025-05-04 03:00:24,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:24,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:24,897 - INFO - Response - Page 7:
2025-05-04 03:00:25,101 - INFO - 第 7 页获取到 27 条记录
2025-05-04 03:00:25,101 - INFO - 查询完成，共获取到 627 条记录
2025-05-04 03:00:25,101 - INFO - 获取到 627 条表单数据
2025-05-04 03:00:25,101 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-04 03:00:25,116 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 03:00:25,116 - INFO - 开始处理日期: 2025-05
2025-05-04 03:00:25,116 - INFO - Request Parameters - Page 1:
2025-05-04 03:00:25,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:25,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:25,648 - INFO - Response - Page 1:
2025-05-04 03:00:25,852 - INFO - 第 1 页获取到 100 条记录
2025-05-04 03:00:25,852 - INFO - Request Parameters - Page 2:
2025-05-04 03:00:25,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:25,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:26,336 - INFO - Response - Page 2:
2025-05-04 03:00:26,540 - INFO - 第 2 页获取到 100 条记录
2025-05-04 03:00:26,540 - INFO - Request Parameters - Page 3:
2025-05-04 03:00:26,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:26,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:27,009 - INFO - Response - Page 3:
2025-05-04 03:00:27,212 - INFO - 第 3 页获取到 100 条记录
2025-05-04 03:00:27,212 - INFO - Request Parameters - Page 4:
2025-05-04 03:00:27,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:27,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:27,650 - INFO - Response - Page 4:
2025-05-04 03:00:27,854 - INFO - 第 4 页获取到 100 条记录
2025-05-04 03:00:27,854 - INFO - Request Parameters - Page 5:
2025-05-04 03:00:27,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:27,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:28,417 - INFO - Response - Page 5:
2025-05-04 03:00:28,620 - INFO - 第 5 页获取到 100 条记录
2025-05-04 03:00:28,620 - INFO - Request Parameters - Page 6:
2025-05-04 03:00:28,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 03:00:28,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 03:00:29,011 - INFO - Response - Page 6:
2025-05-04 03:00:29,215 - INFO - 第 6 页获取到 66 条记录
2025-05-04 03:00:29,215 - INFO - 查询完成，共获取到 566 条记录
2025-05-04 03:00:29,215 - INFO - 获取到 566 条表单数据
2025-05-04 03:00:29,215 - INFO - 当前日期 2025-05 有 566 条MySQL数据需要处理
2025-05-04 03:00:29,230 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-04 03:00:29,699 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-04 03:00:29,699 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29829.06, 'new_value': 42535.44}, {'field': 'offline_amount', 'old_value': 164717.58, 'new_value': 247108.54}, {'field': 'total_amount', 'old_value': 194546.64, 'new_value': 289643.98}, {'field': 'order_count', 'old_value': 919, 'new_value': 1359}]
2025-05-04 03:00:29,699 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-04 03:00:30,137 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-04 03:00:30,137 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21380.65, 'new_value': 32445.59}, {'field': 'offline_amount', 'old_value': 103451.92, 'new_value': 152796.74}, {'field': 'total_amount', 'old_value': 124832.57, 'new_value': 185242.33}, {'field': 'order_count', 'old_value': 581, 'new_value': 848}]
2025-05-04 03:00:30,137 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-04 03:00:30,575 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-04 03:00:30,575 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105398.12, 'new_value': 149779.94}, {'field': 'total_amount', 'old_value': 105398.12, 'new_value': 149779.94}, {'field': 'order_count', 'old_value': 679, 'new_value': 1000}]
2025-05-04 03:00:30,575 - INFO - 日期 2025-05 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-05-04 03:00:30,575 - INFO - 数据同步完成！更新: 3 条，插入: 0 条，错误: 0 条
2025-05-04 03:00:30,575 - INFO - =================同步完成====================
2025-05-04 06:00:01,920 - INFO - =================使用默认全量同步=============
2025-05-04 06:00:03,092 - INFO - MySQL查询成功，共获取 3206 条记录
2025-05-04 06:00:03,092 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-04 06:00:03,124 - INFO - 开始处理日期: 2025-01
2025-05-04 06:00:03,124 - INFO - Request Parameters - Page 1:
2025-05-04 06:00:03,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:03,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:04,202 - INFO - Response - Page 1:
2025-05-04 06:00:04,405 - INFO - 第 1 页获取到 100 条记录
2025-05-04 06:00:04,405 - INFO - Request Parameters - Page 2:
2025-05-04 06:00:04,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:04,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:05,124 - INFO - Response - Page 2:
2025-05-04 06:00:05,327 - INFO - 第 2 页获取到 100 条记录
2025-05-04 06:00:05,327 - INFO - Request Parameters - Page 3:
2025-05-04 06:00:05,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:05,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:05,874 - INFO - Response - Page 3:
2025-05-04 06:00:06,077 - INFO - 第 3 页获取到 100 条记录
2025-05-04 06:00:06,077 - INFO - Request Parameters - Page 4:
2025-05-04 06:00:06,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:06,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:06,608 - INFO - Response - Page 4:
2025-05-04 06:00:06,811 - INFO - 第 4 页获取到 100 条记录
2025-05-04 06:00:06,811 - INFO - Request Parameters - Page 5:
2025-05-04 06:00:06,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:06,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:07,311 - INFO - Response - Page 5:
2025-05-04 06:00:07,514 - INFO - 第 5 页获取到 100 条记录
2025-05-04 06:00:07,514 - INFO - Request Parameters - Page 6:
2025-05-04 06:00:07,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:07,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:07,999 - INFO - Response - Page 6:
2025-05-04 06:00:08,202 - INFO - 第 6 页获取到 100 条记录
2025-05-04 06:00:08,202 - INFO - Request Parameters - Page 7:
2025-05-04 06:00:08,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:08,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:08,655 - INFO - Response - Page 7:
2025-05-04 06:00:08,858 - INFO - 第 7 页获取到 82 条记录
2025-05-04 06:00:08,858 - INFO - 查询完成，共获取到 682 条记录
2025-05-04 06:00:08,858 - INFO - 获取到 682 条表单数据
2025-05-04 06:00:08,858 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-04 06:00:08,874 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 06:00:08,874 - INFO - 开始处理日期: 2025-02
2025-05-04 06:00:08,874 - INFO - Request Parameters - Page 1:
2025-05-04 06:00:08,874 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:08,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:09,420 - INFO - Response - Page 1:
2025-05-04 06:00:09,624 - INFO - 第 1 页获取到 100 条记录
2025-05-04 06:00:09,624 - INFO - Request Parameters - Page 2:
2025-05-04 06:00:09,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:09,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:10,139 - INFO - Response - Page 2:
2025-05-04 06:00:10,342 - INFO - 第 2 页获取到 100 条记录
2025-05-04 06:00:10,342 - INFO - Request Parameters - Page 3:
2025-05-04 06:00:10,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:10,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:10,827 - INFO - Response - Page 3:
2025-05-04 06:00:11,030 - INFO - 第 3 页获取到 100 条记录
2025-05-04 06:00:11,030 - INFO - Request Parameters - Page 4:
2025-05-04 06:00:11,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:11,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:11,483 - INFO - Response - Page 4:
2025-05-04 06:00:11,686 - INFO - 第 4 页获取到 100 条记录
2025-05-04 06:00:11,686 - INFO - Request Parameters - Page 5:
2025-05-04 06:00:11,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:11,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:12,202 - INFO - Response - Page 5:
2025-05-04 06:00:12,405 - INFO - 第 5 页获取到 100 条记录
2025-05-04 06:00:12,405 - INFO - Request Parameters - Page 6:
2025-05-04 06:00:12,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:12,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:12,874 - INFO - Response - Page 6:
2025-05-04 06:00:13,077 - INFO - 第 6 页获取到 100 条记录
2025-05-04 06:00:13,077 - INFO - Request Parameters - Page 7:
2025-05-04 06:00:13,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:13,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:13,561 - INFO - Response - Page 7:
2025-05-04 06:00:13,764 - INFO - 第 7 页获取到 70 条记录
2025-05-04 06:00:13,764 - INFO - 查询完成，共获取到 670 条记录
2025-05-04 06:00:13,764 - INFO - 获取到 670 条表单数据
2025-05-04 06:00:13,764 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-04 06:00:13,780 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 06:00:13,780 - INFO - 开始处理日期: 2025-03
2025-05-04 06:00:13,780 - INFO - Request Parameters - Page 1:
2025-05-04 06:00:13,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:13,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:14,264 - INFO - Response - Page 1:
2025-05-04 06:00:14,467 - INFO - 第 1 页获取到 100 条记录
2025-05-04 06:00:14,467 - INFO - Request Parameters - Page 2:
2025-05-04 06:00:14,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:14,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:14,889 - INFO - Response - Page 2:
2025-05-04 06:00:15,092 - INFO - 第 2 页获取到 100 条记录
2025-05-04 06:00:15,092 - INFO - Request Parameters - Page 3:
2025-05-04 06:00:15,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:15,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:15,514 - INFO - Response - Page 3:
2025-05-04 06:00:15,717 - INFO - 第 3 页获取到 100 条记录
2025-05-04 06:00:15,717 - INFO - Request Parameters - Page 4:
2025-05-04 06:00:15,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:15,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:16,155 - INFO - Response - Page 4:
2025-05-04 06:00:16,358 - INFO - 第 4 页获取到 100 条记录
2025-05-04 06:00:16,358 - INFO - Request Parameters - Page 5:
2025-05-04 06:00:16,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:16,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:16,874 - INFO - Response - Page 5:
2025-05-04 06:00:17,077 - INFO - 第 5 页获取到 100 条记录
2025-05-04 06:00:17,077 - INFO - Request Parameters - Page 6:
2025-05-04 06:00:17,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:17,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:17,670 - INFO - Response - Page 6:
2025-05-04 06:00:17,874 - INFO - 第 6 页获取到 100 条记录
2025-05-04 06:00:17,874 - INFO - Request Parameters - Page 7:
2025-05-04 06:00:17,874 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:17,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:18,249 - INFO - Response - Page 7:
2025-05-04 06:00:18,452 - INFO - 第 7 页获取到 61 条记录
2025-05-04 06:00:18,452 - INFO - 查询完成，共获取到 661 条记录
2025-05-04 06:00:18,452 - INFO - 获取到 661 条表单数据
2025-05-04 06:00:18,452 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-04 06:00:18,467 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 06:00:18,467 - INFO - 开始处理日期: 2025-04
2025-05-04 06:00:18,467 - INFO - Request Parameters - Page 1:
2025-05-04 06:00:18,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:18,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:18,920 - INFO - Response - Page 1:
2025-05-04 06:00:19,123 - INFO - 第 1 页获取到 100 条记录
2025-05-04 06:00:19,123 - INFO - Request Parameters - Page 2:
2025-05-04 06:00:19,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:19,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:19,608 - INFO - Response - Page 2:
2025-05-04 06:00:19,811 - INFO - 第 2 页获取到 100 条记录
2025-05-04 06:00:19,811 - INFO - Request Parameters - Page 3:
2025-05-04 06:00:19,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:19,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:20,358 - INFO - Response - Page 3:
2025-05-04 06:00:20,561 - INFO - 第 3 页获取到 100 条记录
2025-05-04 06:00:20,561 - INFO - Request Parameters - Page 4:
2025-05-04 06:00:20,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:20,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:21,108 - INFO - Response - Page 4:
2025-05-04 06:00:21,311 - INFO - 第 4 页获取到 100 条记录
2025-05-04 06:00:21,311 - INFO - Request Parameters - Page 5:
2025-05-04 06:00:21,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:21,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:21,780 - INFO - Response - Page 5:
2025-05-04 06:00:21,983 - INFO - 第 5 页获取到 100 条记录
2025-05-04 06:00:21,983 - INFO - Request Parameters - Page 6:
2025-05-04 06:00:21,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:21,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:22,420 - INFO - Response - Page 6:
2025-05-04 06:00:22,623 - INFO - 第 6 页获取到 100 条记录
2025-05-04 06:00:22,623 - INFO - Request Parameters - Page 7:
2025-05-04 06:00:22,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:22,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:22,983 - INFO - Response - Page 7:
2025-05-04 06:00:23,186 - INFO - 第 7 页获取到 27 条记录
2025-05-04 06:00:23,186 - INFO - 查询完成，共获取到 627 条记录
2025-05-04 06:00:23,186 - INFO - 获取到 627 条表单数据
2025-05-04 06:00:23,186 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-04 06:00:23,202 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 06:00:23,202 - INFO - 开始处理日期: 2025-05
2025-05-04 06:00:23,202 - INFO - Request Parameters - Page 1:
2025-05-04 06:00:23,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:23,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:23,670 - INFO - Response - Page 1:
2025-05-04 06:00:23,873 - INFO - 第 1 页获取到 100 条记录
2025-05-04 06:00:23,873 - INFO - Request Parameters - Page 2:
2025-05-04 06:00:23,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:23,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:24,295 - INFO - Response - Page 2:
2025-05-04 06:00:24,498 - INFO - 第 2 页获取到 100 条记录
2025-05-04 06:00:24,498 - INFO - Request Parameters - Page 3:
2025-05-04 06:00:24,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:24,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:24,967 - INFO - Response - Page 3:
2025-05-04 06:00:25,170 - INFO - 第 3 页获取到 100 条记录
2025-05-04 06:00:25,170 - INFO - Request Parameters - Page 4:
2025-05-04 06:00:25,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:25,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:25,670 - INFO - Response - Page 4:
2025-05-04 06:00:25,873 - INFO - 第 4 页获取到 100 条记录
2025-05-04 06:00:25,873 - INFO - Request Parameters - Page 5:
2025-05-04 06:00:25,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:25,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:26,342 - INFO - Response - Page 5:
2025-05-04 06:00:26,545 - INFO - 第 5 页获取到 100 条记录
2025-05-04 06:00:26,545 - INFO - Request Parameters - Page 6:
2025-05-04 06:00:26,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 06:00:26,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 06:00:27,030 - INFO - Response - Page 6:
2025-05-04 06:00:27,233 - INFO - 第 6 页获取到 66 条记录
2025-05-04 06:00:27,233 - INFO - 查询完成，共获取到 566 条记录
2025-05-04 06:00:27,233 - INFO - 获取到 566 条表单数据
2025-05-04 06:00:27,233 - INFO - 当前日期 2025-05 有 566 条MySQL数据需要处理
2025-05-04 06:00:27,248 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-04 06:00:27,702 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-04 06:00:27,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130441.55, 'new_value': 190401.55}, {'field': 'total_amount', 'old_value': 130441.55, 'new_value': 190401.55}, {'field': 'order_count', 'old_value': 326, 'new_value': 469}]
2025-05-04 06:00:27,702 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-04 06:00:28,108 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-04 06:00:28,108 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12071.5, 'new_value': 18522.2}, {'field': 'offline_amount', 'old_value': 10767.8, 'new_value': 15615.5}, {'field': 'total_amount', 'old_value': 22839.3, 'new_value': 34137.7}, {'field': 'order_count', 'old_value': 554, 'new_value': 842}]
2025-05-04 06:00:28,108 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-04 06:00:28,545 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-04 06:00:28,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9707.0, 'new_value': 13675.0}, {'field': 'total_amount', 'old_value': 9707.0, 'new_value': 13675.0}, {'field': 'order_count', 'old_value': 400, 'new_value': 570}]
2025-05-04 06:00:28,545 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-04 06:00:29,092 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-04 06:00:29,092 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34713.0, 'new_value': 54519.0}, {'field': 'total_amount', 'old_value': 34713.0, 'new_value': 54519.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 63}]
2025-05-04 06:00:29,092 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-04 06:00:29,545 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-04 06:00:29,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10735.0, 'new_value': 15085.0}, {'field': 'total_amount', 'old_value': 10735.0, 'new_value': 15085.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 70}]
2025-05-04 06:00:29,545 - INFO - 日期 2025-05 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-05-04 06:00:29,545 - INFO - 数据同步完成！更新: 5 条，插入: 0 条，错误: 0 条
2025-05-04 06:00:29,545 - INFO - =================同步完成====================
2025-05-04 09:00:01,869 - INFO - =================使用默认全量同步=============
2025-05-04 09:00:03,041 - INFO - MySQL查询成功，共获取 3206 条记录
2025-05-04 09:00:03,041 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-04 09:00:03,072 - INFO - 开始处理日期: 2025-01
2025-05-04 09:00:03,072 - INFO - Request Parameters - Page 1:
2025-05-04 09:00:03,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:03,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:04,119 - INFO - Response - Page 1:
2025-05-04 09:00:04,322 - INFO - 第 1 页获取到 100 条记录
2025-05-04 09:00:04,322 - INFO - Request Parameters - Page 2:
2025-05-04 09:00:04,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:04,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:04,775 - INFO - Response - Page 2:
2025-05-04 09:00:04,978 - INFO - 第 2 页获取到 100 条记录
2025-05-04 09:00:04,978 - INFO - Request Parameters - Page 3:
2025-05-04 09:00:04,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:04,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:05,431 - INFO - Response - Page 3:
2025-05-04 09:00:05,634 - INFO - 第 3 页获取到 100 条记录
2025-05-04 09:00:05,634 - INFO - Request Parameters - Page 4:
2025-05-04 09:00:05,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:05,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:06,322 - INFO - Response - Page 4:
2025-05-04 09:00:06,525 - INFO - 第 4 页获取到 100 条记录
2025-05-04 09:00:06,525 - INFO - Request Parameters - Page 5:
2025-05-04 09:00:06,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:06,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:06,947 - INFO - Response - Page 5:
2025-05-04 09:00:07,150 - INFO - 第 5 页获取到 100 条记录
2025-05-04 09:00:07,150 - INFO - Request Parameters - Page 6:
2025-05-04 09:00:07,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:07,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:07,681 - INFO - Response - Page 6:
2025-05-04 09:00:07,884 - INFO - 第 6 页获取到 100 条记录
2025-05-04 09:00:07,884 - INFO - Request Parameters - Page 7:
2025-05-04 09:00:07,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:07,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:08,447 - INFO - Response - Page 7:
2025-05-04 09:00:08,650 - INFO - 第 7 页获取到 82 条记录
2025-05-04 09:00:08,650 - INFO - 查询完成，共获取到 682 条记录
2025-05-04 09:00:08,650 - INFO - 获取到 682 条表单数据
2025-05-04 09:00:08,650 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-04 09:00:08,665 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 09:00:08,665 - INFO - 开始处理日期: 2025-02
2025-05-04 09:00:08,665 - INFO - Request Parameters - Page 1:
2025-05-04 09:00:08,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:08,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:09,134 - INFO - Response - Page 1:
2025-05-04 09:00:09,337 - INFO - 第 1 页获取到 100 条记录
2025-05-04 09:00:09,337 - INFO - Request Parameters - Page 2:
2025-05-04 09:00:09,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:09,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:09,759 - INFO - Response - Page 2:
2025-05-04 09:00:09,962 - INFO - 第 2 页获取到 100 条记录
2025-05-04 09:00:09,962 - INFO - Request Parameters - Page 3:
2025-05-04 09:00:09,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:09,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:10,431 - INFO - Response - Page 3:
2025-05-04 09:00:10,634 - INFO - 第 3 页获取到 100 条记录
2025-05-04 09:00:10,634 - INFO - Request Parameters - Page 4:
2025-05-04 09:00:10,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:10,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:11,119 - INFO - Response - Page 4:
2025-05-04 09:00:11,322 - INFO - 第 4 页获取到 100 条记录
2025-05-04 09:00:11,322 - INFO - Request Parameters - Page 5:
2025-05-04 09:00:11,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:11,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:11,884 - INFO - Response - Page 5:
2025-05-04 09:00:12,087 - INFO - 第 5 页获取到 100 条记录
2025-05-04 09:00:12,087 - INFO - Request Parameters - Page 6:
2025-05-04 09:00:12,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:12,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:12,509 - INFO - Response - Page 6:
2025-05-04 09:00:12,712 - INFO - 第 6 页获取到 100 条记录
2025-05-04 09:00:12,712 - INFO - Request Parameters - Page 7:
2025-05-04 09:00:12,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:12,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:13,165 - INFO - Response - Page 7:
2025-05-04 09:00:13,369 - INFO - 第 7 页获取到 70 条记录
2025-05-04 09:00:13,369 - INFO - 查询完成，共获取到 670 条记录
2025-05-04 09:00:13,369 - INFO - 获取到 670 条表单数据
2025-05-04 09:00:13,369 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-04 09:00:13,384 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 09:00:13,384 - INFO - 开始处理日期: 2025-03
2025-05-04 09:00:13,384 - INFO - Request Parameters - Page 1:
2025-05-04 09:00:13,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:13,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:13,869 - INFO - Response - Page 1:
2025-05-04 09:00:14,072 - INFO - 第 1 页获取到 100 条记录
2025-05-04 09:00:14,072 - INFO - Request Parameters - Page 2:
2025-05-04 09:00:14,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:14,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:14,587 - INFO - Response - Page 2:
2025-05-04 09:00:14,790 - INFO - 第 2 页获取到 100 条记录
2025-05-04 09:00:14,790 - INFO - Request Parameters - Page 3:
2025-05-04 09:00:14,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:14,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:15,306 - INFO - Response - Page 3:
2025-05-04 09:00:15,509 - INFO - 第 3 页获取到 100 条记录
2025-05-04 09:00:15,509 - INFO - Request Parameters - Page 4:
2025-05-04 09:00:15,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:15,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:15,962 - INFO - Response - Page 4:
2025-05-04 09:00:16,165 - INFO - 第 4 页获取到 100 条记录
2025-05-04 09:00:16,165 - INFO - Request Parameters - Page 5:
2025-05-04 09:00:16,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:16,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:16,775 - INFO - Response - Page 5:
2025-05-04 09:00:16,978 - INFO - 第 5 页获取到 100 条记录
2025-05-04 09:00:16,978 - INFO - Request Parameters - Page 6:
2025-05-04 09:00:16,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:16,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:17,540 - INFO - Response - Page 6:
2025-05-04 09:00:17,744 - INFO - 第 6 页获取到 100 条记录
2025-05-04 09:00:17,744 - INFO - Request Parameters - Page 7:
2025-05-04 09:00:17,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:17,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:18,165 - INFO - Response - Page 7:
2025-05-04 09:00:18,369 - INFO - 第 7 页获取到 61 条记录
2025-05-04 09:00:18,369 - INFO - 查询完成，共获取到 661 条记录
2025-05-04 09:00:18,369 - INFO - 获取到 661 条表单数据
2025-05-04 09:00:18,369 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-04 09:00:18,384 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 09:00:18,384 - INFO - 开始处理日期: 2025-04
2025-05-04 09:00:18,384 - INFO - Request Parameters - Page 1:
2025-05-04 09:00:18,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:18,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:18,900 - INFO - Response - Page 1:
2025-05-04 09:00:19,103 - INFO - 第 1 页获取到 100 条记录
2025-05-04 09:00:19,103 - INFO - Request Parameters - Page 2:
2025-05-04 09:00:19,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:19,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:19,556 - INFO - Response - Page 2:
2025-05-04 09:00:19,759 - INFO - 第 2 页获取到 100 条记录
2025-05-04 09:00:19,759 - INFO - Request Parameters - Page 3:
2025-05-04 09:00:19,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:19,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:20,212 - INFO - Response - Page 3:
2025-05-04 09:00:20,415 - INFO - 第 3 页获取到 100 条记录
2025-05-04 09:00:20,415 - INFO - Request Parameters - Page 4:
2025-05-04 09:00:20,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:20,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:20,884 - INFO - Response - Page 4:
2025-05-04 09:00:21,087 - INFO - 第 4 页获取到 100 条记录
2025-05-04 09:00:21,087 - INFO - Request Parameters - Page 5:
2025-05-04 09:00:21,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:21,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:21,525 - INFO - Response - Page 5:
2025-05-04 09:00:21,728 - INFO - 第 5 页获取到 100 条记录
2025-05-04 09:00:21,728 - INFO - Request Parameters - Page 6:
2025-05-04 09:00:21,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:21,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:22,212 - INFO - Response - Page 6:
2025-05-04 09:00:22,415 - INFO - 第 6 页获取到 100 条记录
2025-05-04 09:00:22,415 - INFO - Request Parameters - Page 7:
2025-05-04 09:00:22,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:22,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:22,806 - INFO - Response - Page 7:
2025-05-04 09:00:23,009 - INFO - 第 7 页获取到 27 条记录
2025-05-04 09:00:23,009 - INFO - 查询完成，共获取到 627 条记录
2025-05-04 09:00:23,009 - INFO - 获取到 627 条表单数据
2025-05-04 09:00:23,009 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-04 09:00:23,025 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 09:00:23,025 - INFO - 开始处理日期: 2025-05
2025-05-04 09:00:23,025 - INFO - Request Parameters - Page 1:
2025-05-04 09:00:23,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:23,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:23,540 - INFO - Response - Page 1:
2025-05-04 09:00:23,744 - INFO - 第 1 页获取到 100 条记录
2025-05-04 09:00:23,744 - INFO - Request Parameters - Page 2:
2025-05-04 09:00:23,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:23,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:24,228 - INFO - Response - Page 2:
2025-05-04 09:00:24,431 - INFO - 第 2 页获取到 100 条记录
2025-05-04 09:00:24,431 - INFO - Request Parameters - Page 3:
2025-05-04 09:00:24,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:24,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:24,931 - INFO - Response - Page 3:
2025-05-04 09:00:25,134 - INFO - 第 3 页获取到 100 条记录
2025-05-04 09:00:25,134 - INFO - Request Parameters - Page 4:
2025-05-04 09:00:25,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:25,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:25,650 - INFO - Response - Page 4:
2025-05-04 09:00:25,853 - INFO - 第 4 页获取到 100 条记录
2025-05-04 09:00:25,853 - INFO - Request Parameters - Page 5:
2025-05-04 09:00:25,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:25,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:26,306 - INFO - Response - Page 5:
2025-05-04 09:00:26,509 - INFO - 第 5 页获取到 100 条记录
2025-05-04 09:00:26,509 - INFO - Request Parameters - Page 6:
2025-05-04 09:00:26,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 09:00:26,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 09:00:26,931 - INFO - Response - Page 6:
2025-05-04 09:00:27,134 - INFO - 第 6 页获取到 66 条记录
2025-05-04 09:00:27,134 - INFO - 查询完成，共获取到 566 条记录
2025-05-04 09:00:27,134 - INFO - 获取到 566 条表单数据
2025-05-04 09:00:27,134 - INFO - 当前日期 2025-05 有 566 条MySQL数据需要处理
2025-05-04 09:00:27,134 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-04 09:00:27,650 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-04 09:00:27,650 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 152.9}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 152.9}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-05-04 09:00:27,650 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-04 09:00:28,040 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-04 09:00:28,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1704.0, 'new_value': 4763.8}, {'field': 'total_amount', 'old_value': 1704.0, 'new_value': 4763.8}, {'field': 'order_count', 'old_value': 51, 'new_value': 60}]
2025-05-04 09:00:28,056 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-04 09:00:28,478 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-04 09:00:28,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37668.0, 'new_value': 78759.0}, {'field': 'offline_amount', 'old_value': 19773.0, 'new_value': 30756.0}, {'field': 'total_amount', 'old_value': 57441.0, 'new_value': 109515.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 137}]
2025-05-04 09:00:28,478 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-04 09:00:28,915 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-04 09:00:28,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 27850.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 27850.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 5}]
2025-05-04 09:00:28,915 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-04 09:00:29,368 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-04 09:00:29,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 20000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 20000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 5}]
2025-05-04 09:00:29,368 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-04 09:00:29,822 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-04 09:00:29,822 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 350.0, 'new_value': 2712.0}, {'field': 'total_amount', 'old_value': 350.0, 'new_value': 2712.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 7}]
2025-05-04 09:00:29,822 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-04 09:00:30,275 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-04 09:00:30,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 554.41, 'new_value': 954.91}, {'field': 'total_amount', 'old_value': 554.41, 'new_value': 954.91}, {'field': 'order_count', 'old_value': 39, 'new_value': 80}]
2025-05-04 09:00:30,275 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-04 09:00:30,712 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-04 09:00:30,712 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1320.0}, {'field': 'offline_amount', 'old_value': 1908.0, 'new_value': 4390.0}, {'field': 'total_amount', 'old_value': 1908.0, 'new_value': 5710.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 17}]
2025-05-04 09:00:30,712 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-04 09:00:31,118 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-04 09:00:31,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5837.82, 'new_value': 8441.34}, {'field': 'total_amount', 'old_value': 5837.82, 'new_value': 8441.34}, {'field': 'order_count', 'old_value': 9, 'new_value': 13}]
2025-05-04 09:00:31,118 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-04 09:00:31,540 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-04 09:00:31,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4128.7, 'new_value': 6226.8}, {'field': 'total_amount', 'old_value': 4128.7, 'new_value': 6226.8}, {'field': 'order_count', 'old_value': 19, 'new_value': 31}]
2025-05-04 09:00:31,540 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-04 09:00:32,009 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-04 09:00:32,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 150.0, 'new_value': 200.0}, {'field': 'offline_amount', 'old_value': 1510.0, 'new_value': 2760.0}, {'field': 'total_amount', 'old_value': 1660.0, 'new_value': 2960.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 13}]
2025-05-04 09:00:32,009 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-04 09:00:32,478 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-04 09:00:32,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1201.86, 'new_value': 1649.2}, {'field': 'offline_amount', 'old_value': 13487.2, 'new_value': 17808.89}, {'field': 'total_amount', 'old_value': 14689.06, 'new_value': 19458.09}, {'field': 'order_count', 'old_value': 318, 'new_value': 417}]
2025-05-04 09:00:32,478 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-04 09:00:32,900 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-04 09:00:32,900 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2572.14, 'new_value': 5679.06}, {'field': 'offline_amount', 'old_value': 1483.6, 'new_value': 2544.5}, {'field': 'total_amount', 'old_value': 4055.74, 'new_value': 8223.56}, {'field': 'order_count', 'old_value': 116, 'new_value': 233}]
2025-05-04 09:00:32,900 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-04 09:00:33,353 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-04 09:00:33,353 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 778.0, 'new_value': 5668.0}, {'field': 'total_amount', 'old_value': 778.0, 'new_value': 5668.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 5}]
2025-05-04 09:00:33,353 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-04 09:00:33,775 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-04 09:00:33,775 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1999.0, 'new_value': 11774.0}, {'field': 'total_amount', 'old_value': 17997.0, 'new_value': 27772.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 8}]
2025-05-04 09:00:33,775 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-04 09:00:34,228 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-04 09:00:34,228 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90.44, 'new_value': 256.16}, {'field': 'offline_amount', 'old_value': 10976.78, 'new_value': 17525.74}, {'field': 'total_amount', 'old_value': 11067.22, 'new_value': 17781.9}, {'field': 'order_count', 'old_value': 50, 'new_value': 90}]
2025-05-04 09:00:34,228 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-04 09:00:34,665 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-04 09:00:34,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2381.0, 'new_value': 3145.0}, {'field': 'total_amount', 'old_value': 2381.0, 'new_value': 3145.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 15}]
2025-05-04 09:00:34,665 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-04 09:00:35,087 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-04 09:00:35,087 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1259.46, 'new_value': 1365.35}, {'field': 'offline_amount', 'old_value': 20383.1, 'new_value': 25727.1}, {'field': 'total_amount', 'old_value': 21642.56, 'new_value': 27092.45}, {'field': 'order_count', 'old_value': 147, 'new_value': 190}]
2025-05-04 09:00:35,087 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-04 09:00:35,556 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-04 09:00:35,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9339.0, 'new_value': 18252.4}, {'field': 'total_amount', 'old_value': 9339.0, 'new_value': 18252.4}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-04 09:00:35,556 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-04 09:00:36,056 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-04 09:00:36,056 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2982.07, 'new_value': 4495.77}, {'field': 'offline_amount', 'old_value': 14730.35, 'new_value': 21237.55}, {'field': 'total_amount', 'old_value': 17712.42, 'new_value': 25733.32}, {'field': 'order_count', 'old_value': 609, 'new_value': 885}]
2025-05-04 09:00:36,056 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-04 09:00:36,493 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-04 09:00:36,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16790.49, 'new_value': 23176.63}, {'field': 'total_amount', 'old_value': 16790.49, 'new_value': 23176.63}, {'field': 'order_count', 'old_value': 590, 'new_value': 831}]
2025-05-04 09:00:36,493 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-04 09:00:36,962 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-04 09:00:36,962 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6048.12, 'new_value': 8083.52}, {'field': 'offline_amount', 'old_value': 77927.91, 'new_value': 179782.55}, {'field': 'total_amount', 'old_value': 83976.03, 'new_value': 187866.07}, {'field': 'order_count', 'old_value': 711, 'new_value': 1131}]
2025-05-04 09:00:36,962 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-04 09:00:37,368 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-04 09:00:37,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1850.1, 'new_value': 3660.7}, {'field': 'total_amount', 'old_value': 2852.8, 'new_value': 4663.4}, {'field': 'order_count', 'old_value': 14, 'new_value': 21}]
2025-05-04 09:00:37,368 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-04 09:00:37,728 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-04 09:00:37,728 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8006.56, 'new_value': 13907.56}, {'field': 'total_amount', 'old_value': 8006.56, 'new_value': 13907.56}, {'field': 'order_count', 'old_value': 49, 'new_value': 81}]
2025-05-04 09:00:37,728 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-04 09:00:38,228 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-04 09:00:38,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10783.0, 'new_value': 16204.0}, {'field': 'total_amount', 'old_value': 10783.0, 'new_value': 16204.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 8}]
2025-05-04 09:00:38,228 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-04 09:00:38,681 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-04 09:00:38,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17662.2, 'new_value': 28992.0}, {'field': 'total_amount', 'old_value': 17662.2, 'new_value': 28992.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 80}]
2025-05-04 09:00:38,681 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-04 09:00:39,212 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-04 09:00:39,212 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10832.21, 'new_value': 16904.79}, {'field': 'offline_amount', 'old_value': 54153.35, 'new_value': 81834.84}, {'field': 'total_amount', 'old_value': 64985.56, 'new_value': 98739.63}, {'field': 'order_count', 'old_value': 1337, 'new_value': 2051}]
2025-05-04 09:00:39,212 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-04 09:00:39,650 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-04 09:00:39,650 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5823.0, 'new_value': 11466.0}, {'field': 'total_amount', 'old_value': 5823.0, 'new_value': 11466.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 26}]
2025-05-04 09:00:39,650 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-04 09:00:40,165 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-04 09:00:40,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8418.0, 'new_value': 9899.0}, {'field': 'total_amount', 'old_value': 8418.0, 'new_value': 9899.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 26}]
2025-05-04 09:00:40,165 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-04 09:00:40,540 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-04 09:00:40,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3603.4, 'new_value': 4801.3}, {'field': 'offline_amount', 'old_value': 3210.9, 'new_value': 4313.9}, {'field': 'total_amount', 'old_value': 6814.3, 'new_value': 9115.2}, {'field': 'order_count', 'old_value': 33, 'new_value': 45}]
2025-05-04 09:00:40,540 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-04 09:00:40,993 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-04 09:00:40,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1262.55, 'new_value': 1736.78}, {'field': 'offline_amount', 'old_value': 20740.6, 'new_value': 30990.6}, {'field': 'total_amount', 'old_value': 22003.15, 'new_value': 32727.38}, {'field': 'order_count', 'old_value': 1035, 'new_value': 1546}]
2025-05-04 09:00:40,993 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-04 09:00:41,415 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-04 09:00:41,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1794.0, 'new_value': 2290.0}, {'field': 'total_amount', 'old_value': 1794.0, 'new_value': 2290.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-04 09:00:41,415 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-04 09:00:41,775 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-04 09:00:41,775 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4969.31, 'new_value': 6147.72}, {'field': 'offline_amount', 'old_value': 1939.0, 'new_value': 3316.0}, {'field': 'total_amount', 'old_value': 6908.31, 'new_value': 9463.72}, {'field': 'order_count', 'old_value': 92, 'new_value': 130}]
2025-05-04 09:00:41,775 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-04 09:00:42,181 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-04 09:00:42,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81829.37, 'new_value': 119520.09}, {'field': 'total_amount', 'old_value': 81829.37, 'new_value': 119520.09}, {'field': 'order_count', 'old_value': 283, 'new_value': 403}]
2025-05-04 09:00:42,181 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-04 09:00:42,665 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-04 09:00:42,665 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5068.5, 'new_value': 8870.79}, {'field': 'offline_amount', 'old_value': 3347.89, 'new_value': 6732.52}, {'field': 'total_amount', 'old_value': 8416.39, 'new_value': 15603.31}, {'field': 'order_count', 'old_value': 305, 'new_value': 565}]
2025-05-04 09:00:42,665 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-04 09:00:43,056 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-04 09:00:43,056 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1153.38, 'new_value': 1238.48}, {'field': 'offline_amount', 'old_value': 11592.54, 'new_value': 15561.0}, {'field': 'total_amount', 'old_value': 12745.92, 'new_value': 16799.48}, {'field': 'order_count', 'old_value': 420, 'new_value': 578}]
2025-05-04 09:00:43,056 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-04 09:00:43,525 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-04 09:00:43,525 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3350.0, 'new_value': 6780.0}, {'field': 'total_amount', 'old_value': 3350.0, 'new_value': 6780.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 157}]
2025-05-04 09:00:43,525 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-04 09:00:43,978 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-04 09:00:43,978 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 430.22, 'new_value': 715.11}, {'field': 'offline_amount', 'old_value': 2166.9, 'new_value': 3200.2}, {'field': 'total_amount', 'old_value': 2597.12, 'new_value': 3915.31}, {'field': 'order_count', 'old_value': 163, 'new_value': 248}]
2025-05-04 09:00:43,978 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-04 09:00:44,431 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-04 09:00:44,431 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5890.09, 'new_value': 9171.36}, {'field': 'offline_amount', 'old_value': 67148.03, 'new_value': 90510.98}, {'field': 'total_amount', 'old_value': 73038.12, 'new_value': 99682.34}, {'field': 'order_count', 'old_value': 545, 'new_value': 755}]
2025-05-04 09:00:44,431 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-04 09:00:44,931 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-04 09:00:44,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10869.98, 'new_value': 15614.38}, {'field': 'offline_amount', 'old_value': 7133.06, 'new_value': 10433.76}, {'field': 'total_amount', 'old_value': 18003.04, 'new_value': 26048.14}, {'field': 'order_count', 'old_value': 1355, 'new_value': 1788}]
2025-05-04 09:00:44,931 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5M
2025-05-04 09:00:45,322 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5M
2025-05-04 09:00:45,322 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 798.55, 'new_value': 1178.65}, {'field': 'offline_amount', 'old_value': 5988.6, 'new_value': 8990.6}, {'field': 'total_amount', 'old_value': 6787.15, 'new_value': 10169.25}, {'field': 'order_count', 'old_value': 281, 'new_value': 430}]
2025-05-04 09:00:45,322 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-04 09:00:45,806 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-04 09:00:45,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17259.2, 'new_value': 22227.5}, {'field': 'total_amount', 'old_value': 17259.2, 'new_value': 22227.5}, {'field': 'order_count', 'old_value': 149, 'new_value': 208}]
2025-05-04 09:00:45,806 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-04 09:00:46,337 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-04 09:00:46,337 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6650.15, 'new_value': 10594.25}, {'field': 'offline_amount', 'old_value': 36320.08, 'new_value': 51352.79}, {'field': 'total_amount', 'old_value': 42970.23, 'new_value': 61947.04}, {'field': 'order_count', 'old_value': 1279, 'new_value': 1877}]
2025-05-04 09:00:46,337 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-04 09:00:46,743 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-04 09:00:46,743 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41299.89, 'new_value': 56056.95}, {'field': 'offline_amount', 'old_value': 2125.1, 'new_value': 3334.5}, {'field': 'total_amount', 'old_value': 43424.99, 'new_value': 59391.45}, {'field': 'order_count', 'old_value': 1561, 'new_value': 2170}]
2025-05-04 09:00:46,743 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-04 09:00:47,259 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-04 09:00:47,259 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 937.6, 'new_value': 1727.2}, {'field': 'offline_amount', 'old_value': 12943.0, 'new_value': 18204.0}, {'field': 'total_amount', 'old_value': 13880.6, 'new_value': 19931.2}, {'field': 'order_count', 'old_value': 24, 'new_value': 35}]
2025-05-04 09:00:47,259 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-04 09:00:47,665 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-04 09:00:47,665 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13368.24, 'new_value': 18804.19}, {'field': 'offline_amount', 'old_value': 38206.87, 'new_value': 54076.52}, {'field': 'total_amount', 'old_value': 51575.11, 'new_value': 72880.71}, {'field': 'order_count', 'old_value': 534, 'new_value': 816}]
2025-05-04 09:00:47,665 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-04 09:00:48,165 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-04 09:00:48,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13992.3, 'new_value': 18839.1}, {'field': 'total_amount', 'old_value': 13992.3, 'new_value': 18839.1}, {'field': 'order_count', 'old_value': 111, 'new_value': 137}]
2025-05-04 09:00:48,165 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-04 09:00:48,634 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-04 09:00:48,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 686.0, 'new_value': 2610.0}, {'field': 'total_amount', 'old_value': 686.0, 'new_value': 2610.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-04 09:00:48,634 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-04 09:00:49,134 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-04 09:00:49,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5840.0, 'new_value': 8639.0}, {'field': 'total_amount', 'old_value': 5840.0, 'new_value': 8639.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 81}]
2025-05-04 09:00:49,134 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-04 09:00:49,571 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-04 09:00:49,571 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8260.02, 'new_value': 12517.77}, {'field': 'offline_amount', 'old_value': 23258.0, 'new_value': 32107.6}, {'field': 'total_amount', 'old_value': 31518.02, 'new_value': 44625.37}, {'field': 'order_count', 'old_value': 266, 'new_value': 398}]
2025-05-04 09:00:49,571 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-04 09:00:49,993 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-04 09:00:49,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1540.0, 'new_value': 2282.0}, {'field': 'total_amount', 'old_value': 1540.0, 'new_value': 2282.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 24}]
2025-05-04 09:00:49,993 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-04 09:00:50,446 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-04 09:00:50,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4865.0, 'new_value': 7285.0}, {'field': 'total_amount', 'old_value': 4865.0, 'new_value': 7285.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 32}]
2025-05-04 09:00:50,446 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-04 09:00:50,884 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-04 09:00:50,884 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16907.5, 'new_value': 23576.8}, {'field': 'offline_amount', 'old_value': 26697.3, 'new_value': 36886.0}, {'field': 'total_amount', 'old_value': 43604.8, 'new_value': 60462.8}, {'field': 'order_count', 'old_value': 872, 'new_value': 1209}]
2025-05-04 09:00:50,884 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-04 09:00:51,337 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-04 09:00:51,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64452.52, 'new_value': 92730.61}, {'field': 'total_amount', 'old_value': 64452.52, 'new_value': 92730.61}, {'field': 'order_count', 'old_value': 788, 'new_value': 1145}]
2025-05-04 09:00:51,337 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-04 09:00:51,759 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-04 09:00:51,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10811.4, 'new_value': 15542.23}, {'field': 'total_amount', 'old_value': 10811.4, 'new_value': 15542.23}, {'field': 'order_count', 'old_value': 341, 'new_value': 489}]
2025-05-04 09:00:51,759 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-04 09:00:52,212 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-04 09:00:52,212 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 723.08, 'new_value': 1140.16}, {'field': 'offline_amount', 'old_value': 4394.61, 'new_value': 6362.72}, {'field': 'total_amount', 'old_value': 5117.69, 'new_value': 7502.88}, {'field': 'order_count', 'old_value': 191, 'new_value': 270}]
2025-05-04 09:00:52,212 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-04 09:00:52,618 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-04 09:00:52,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24494.78, 'new_value': 35861.18}, {'field': 'total_amount', 'old_value': 24494.78, 'new_value': 35861.18}, {'field': 'order_count', 'old_value': 601, 'new_value': 874}]
2025-05-04 09:00:52,618 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-04 09:00:53,103 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-04 09:00:53,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5407.58, 'new_value': 7338.09}, {'field': 'offline_amount', 'old_value': 53212.22, 'new_value': 74599.77}, {'field': 'total_amount', 'old_value': 58619.8, 'new_value': 81937.86}, {'field': 'order_count', 'old_value': 1393, 'new_value': 2001}]
2025-05-04 09:00:53,103 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-04 09:00:53,556 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-04 09:00:53,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73073.9, 'new_value': 96898.6}, {'field': 'total_amount', 'old_value': 73073.9, 'new_value': 96898.6}, {'field': 'order_count', 'old_value': 307, 'new_value': 424}]
2025-05-04 09:00:53,556 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-04 09:00:54,025 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-04 09:00:54,025 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14059.17, 'new_value': 23187.26}, {'field': 'offline_amount', 'old_value': 9017.21, 'new_value': 12143.89}, {'field': 'total_amount', 'old_value': 23076.38, 'new_value': 35331.15}, {'field': 'order_count', 'old_value': 1300, 'new_value': 2052}]
2025-05-04 09:00:54,025 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-04 09:00:54,478 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-04 09:00:54,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35822.0, 'new_value': 46976.0}, {'field': 'total_amount', 'old_value': 35822.0, 'new_value': 46976.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 37}]
2025-05-04 09:00:54,478 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-04 09:00:54,931 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-04 09:00:54,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125549.45, 'new_value': 173259.7}, {'field': 'total_amount', 'old_value': 125549.45, 'new_value': 173259.7}, {'field': 'order_count', 'old_value': 2499, 'new_value': 3724}]
2025-05-04 09:00:54,931 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-04 09:00:55,368 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-04 09:00:55,368 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2482.71, 'new_value': 5356.7}, {'field': 'offline_amount', 'old_value': 8138.85, 'new_value': 15930.67}, {'field': 'total_amount', 'old_value': 10621.56, 'new_value': 21287.37}, {'field': 'order_count', 'old_value': 414, 'new_value': 824}]
2025-05-04 09:00:55,368 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-04 09:00:55,775 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-04 09:00:55,775 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35036.13, 'new_value': 48560.05}, {'field': 'total_amount', 'old_value': 35036.13, 'new_value': 48560.05}, {'field': 'order_count', 'old_value': 1379, 'new_value': 1920}]
2025-05-04 09:00:55,775 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-04 09:00:56,228 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-04 09:00:56,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49011.0, 'new_value': 63836.0}, {'field': 'total_amount', 'old_value': 49011.0, 'new_value': 63836.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 169}]
2025-05-04 09:00:56,228 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-04 09:00:56,696 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-04 09:00:56,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 165.73, 'new_value': 315.15}, {'field': 'offline_amount', 'old_value': 5255.42, 'new_value': 8288.61}, {'field': 'total_amount', 'old_value': 5421.15, 'new_value': 8603.76}, {'field': 'order_count', 'old_value': 195, 'new_value': 301}]
2025-05-04 09:00:56,696 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-04 09:00:57,165 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-04 09:00:57,165 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 501.58, 'new_value': 745.58}, {'field': 'offline_amount', 'old_value': 53663.2, 'new_value': 79066.2}, {'field': 'total_amount', 'old_value': 54164.78, 'new_value': 79811.78}, {'field': 'order_count', 'old_value': 2468, 'new_value': 3513}]
2025-05-04 09:00:57,165 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-04 09:00:57,618 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-04 09:00:57,618 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3871.9, 'new_value': 5912.3}, {'field': 'offline_amount', 'old_value': 32672.13, 'new_value': 46916.17}, {'field': 'total_amount', 'old_value': 36544.03, 'new_value': 52828.47}, {'field': 'order_count', 'old_value': 212, 'new_value': 303}]
2025-05-04 09:00:57,618 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-04 09:00:58,134 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-04 09:00:58,134 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18076.0, 'new_value': 26581.0}, {'field': 'total_amount', 'old_value': 18076.0, 'new_value': 26581.0}, {'field': 'order_count', 'old_value': 563, 'new_value': 801}]
2025-05-04 09:00:58,134 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-04 09:00:58,525 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-04 09:00:58,525 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6935.47, 'new_value': 10050.21}, {'field': 'offline_amount', 'old_value': 13987.96, 'new_value': 19450.99}, {'field': 'total_amount', 'old_value': 20923.43, 'new_value': 29501.2}, {'field': 'order_count', 'old_value': 937, 'new_value': 1314}]
2025-05-04 09:00:58,525 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-04 09:00:58,993 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-04 09:00:58,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50712.91, 'new_value': 73641.37}, {'field': 'total_amount', 'old_value': 50712.91, 'new_value': 73641.37}, {'field': 'order_count', 'old_value': 1980, 'new_value': 2868}]
2025-05-04 09:00:58,993 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-04 09:00:59,462 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-04 09:00:59,462 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 157.07, 'new_value': 898.45}, {'field': 'offline_amount', 'old_value': 37073.3, 'new_value': 47808.8}, {'field': 'total_amount', 'old_value': 37230.37, 'new_value': 48707.25}, {'field': 'order_count', 'old_value': 1094, 'new_value': 1415}]
2025-05-04 09:00:59,462 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-04 09:00:59,931 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-04 09:00:59,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25132.68, 'new_value': 40555.62}, {'field': 'total_amount', 'old_value': 44306.11, 'new_value': 59729.05}, {'field': 'order_count', 'old_value': 925, 'new_value': 1267}]
2025-05-04 09:00:59,931 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-04 09:01:00,384 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-04 09:01:00,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12530.54, 'new_value': 16608.7}, {'field': 'total_amount', 'old_value': 12530.54, 'new_value': 16608.7}, {'field': 'order_count', 'old_value': 566, 'new_value': 797}]
2025-05-04 09:01:00,384 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-04 09:01:00,837 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-04 09:01:00,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 470000.0, 'new_value': 550000.0}, {'field': 'total_amount', 'old_value': 470000.0, 'new_value': 550000.0}]
2025-05-04 09:01:00,837 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-04 09:01:01,306 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-04 09:01:01,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22658.7, 'new_value': 33713.7}, {'field': 'total_amount', 'old_value': 22658.7, 'new_value': 33713.7}, {'field': 'order_count', 'old_value': 254, 'new_value': 390}]
2025-05-04 09:01:01,306 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-04 09:01:01,790 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-04 09:01:01,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5259.0, 'new_value': 7729.0}, {'field': 'total_amount', 'old_value': 5259.0, 'new_value': 7729.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 37}]
2025-05-04 09:01:01,790 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-04 09:01:02,212 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-04 09:01:02,212 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2732.6, 'new_value': 4629.61}, {'field': 'offline_amount', 'old_value': 143767.64, 'new_value': 198967.12}, {'field': 'total_amount', 'old_value': 146500.24, 'new_value': 203596.73}, {'field': 'order_count', 'old_value': 485, 'new_value': 759}]
2025-05-04 09:01:02,212 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-04 09:01:02,650 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-04 09:01:02,650 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22263.93, 'new_value': 24124.74}, {'field': 'offline_amount', 'old_value': 16076.43, 'new_value': 27971.9}, {'field': 'total_amount', 'old_value': 38340.36, 'new_value': 52096.64}, {'field': 'order_count', 'old_value': 497, 'new_value': 718}]
2025-05-04 09:01:02,650 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-04 09:01:03,087 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-04 09:01:03,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30005.0, 'new_value': 44962.0}, {'field': 'total_amount', 'old_value': 31005.0, 'new_value': 45962.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 25}]
2025-05-04 09:01:03,087 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-04 09:01:03,571 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-04 09:01:03,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5247.85, 'new_value': 6913.15}, {'field': 'total_amount', 'old_value': 5247.85, 'new_value': 6913.15}, {'field': 'order_count', 'old_value': 22, 'new_value': 30}]
2025-05-04 09:01:03,571 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-04 09:01:04,009 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-04 09:01:04,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35785.0, 'new_value': 50602.0}, {'field': 'total_amount', 'old_value': 35785.0, 'new_value': 50602.0}, {'field': 'order_count', 'old_value': 591, 'new_value': 810}]
2025-05-04 09:01:04,009 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-04 09:01:04,446 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-04 09:01:04,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28011.6, 'new_value': 39419.51}, {'field': 'total_amount', 'old_value': 28011.6, 'new_value': 39419.51}, {'field': 'order_count', 'old_value': 1116, 'new_value': 1552}]
2025-05-04 09:01:04,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-04 09:01:04,946 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-04 09:01:04,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29259.52, 'new_value': 41928.33}, {'field': 'total_amount', 'old_value': 29259.52, 'new_value': 41928.33}, {'field': 'order_count', 'old_value': 215, 'new_value': 305}]
2025-05-04 09:01:04,946 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-04 09:01:05,478 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-04 09:01:05,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11620.6, 'new_value': 19039.6}, {'field': 'total_amount', 'old_value': 22274.73, 'new_value': 29693.73}, {'field': 'order_count', 'old_value': 708, 'new_value': 944}]
2025-05-04 09:01:05,478 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-04 09:01:05,962 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-04 09:01:05,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155000.0, 'new_value': 230000.0}, {'field': 'total_amount', 'old_value': 155000.0, 'new_value': 230000.0}]
2025-05-04 09:01:05,962 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-04 09:01:06,462 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-04 09:01:06,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180000.0, 'new_value': 250000.0}, {'field': 'total_amount', 'old_value': 180000.0, 'new_value': 250000.0}]
2025-05-04 09:01:06,462 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-04 09:01:06,962 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-04 09:01:06,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1195817.0, 'new_value': 1248674.0}, {'field': 'total_amount', 'old_value': 1195817.0, 'new_value': 1248674.0}]
2025-05-04 09:01:06,962 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-04 09:01:07,478 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-04 09:01:07,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3452.89, 'new_value': 5790.41}, {'field': 'offline_amount', 'old_value': 4911.53, 'new_value': 6899.78}, {'field': 'total_amount', 'old_value': 8364.42, 'new_value': 12690.19}, {'field': 'order_count', 'old_value': 291, 'new_value': 469}]
2025-05-04 09:01:07,478 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-04 09:01:07,946 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-04 09:01:07,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31121.0, 'new_value': 44499.0}, {'field': 'total_amount', 'old_value': 31121.0, 'new_value': 44499.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 81}]
2025-05-04 09:01:07,946 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-04 09:01:08,353 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-04 09:01:08,353 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1690.0, 'new_value': 4473.0}, {'field': 'total_amount', 'old_value': 1690.0, 'new_value': 4473.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 25}]
2025-05-04 09:01:08,353 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-04 09:01:08,806 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-04 09:01:08,806 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5428.37, 'new_value': 8947.27}, {'field': 'offline_amount', 'old_value': 24983.79, 'new_value': 36126.95}, {'field': 'total_amount', 'old_value': 30412.16, 'new_value': 45074.22}, {'field': 'order_count', 'old_value': 1061, 'new_value': 1429}]
2025-05-04 09:01:08,806 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-04 09:01:09,274 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-04 09:01:09,274 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14208.75, 'new_value': 19915.44}, {'field': 'offline_amount', 'old_value': 8501.12, 'new_value': 12263.51}, {'field': 'total_amount', 'old_value': 22709.87, 'new_value': 32178.95}, {'field': 'order_count', 'old_value': 1140, 'new_value': 1631}]
2025-05-04 09:01:09,274 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-04 09:01:09,712 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-04 09:01:09,712 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3839.7, 'new_value': 5459.22}, {'field': 'offline_amount', 'old_value': 4452.84, 'new_value': 5491.66}, {'field': 'total_amount', 'old_value': 8292.54, 'new_value': 10950.88}, {'field': 'order_count', 'old_value': 316, 'new_value': 428}]
2025-05-04 09:01:09,712 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-04 09:01:10,149 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-04 09:01:10,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1646.26, 'new_value': 2492.14}, {'field': 'offline_amount', 'old_value': 6120.5, 'new_value': 8846.1}, {'field': 'total_amount', 'old_value': 7766.76, 'new_value': 11338.24}, {'field': 'order_count', 'old_value': 304, 'new_value': 445}]
2025-05-04 09:01:10,149 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-04 09:01:10,634 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-04 09:01:10,634 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 707.3}, {'field': 'offline_amount', 'old_value': 10000.0, 'new_value': 16190.8}, {'field': 'total_amount', 'old_value': 10000.0, 'new_value': 16898.1}, {'field': 'order_count', 'old_value': 230, 'new_value': 438}]
2025-05-04 09:01:10,634 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-04 09:01:11,071 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-04 09:01:11,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40079.0, 'new_value': 52484.0}, {'field': 'total_amount', 'old_value': 40079.0, 'new_value': 52484.0}, {'field': 'order_count', 'old_value': 492, 'new_value': 651}]
2025-05-04 09:01:11,071 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-04 09:01:11,493 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-04 09:01:11,493 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 416.0, 'new_value': 653.0}, {'field': 'offline_amount', 'old_value': 4775.8, 'new_value': 7729.8}, {'field': 'total_amount', 'old_value': 5191.8, 'new_value': 8382.8}, {'field': 'order_count', 'old_value': 186, 'new_value': 309}]
2025-05-04 09:01:11,493 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-04 09:01:12,009 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-04 09:01:12,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 146.0, 'new_value': 189.0}, {'field': 'offline_amount', 'old_value': 7026.0, 'new_value': 10075.0}, {'field': 'total_amount', 'old_value': 7172.0, 'new_value': 10264.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 79}]
2025-05-04 09:01:12,009 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-04 09:01:12,431 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-04 09:01:12,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201549.17, 'new_value': 247901.68}, {'field': 'total_amount', 'old_value': 201549.17, 'new_value': 247901.68}, {'field': 'order_count', 'old_value': 606, 'new_value': 847}]
2025-05-04 09:01:12,431 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-04 09:01:12,915 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-04 09:01:12,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19548.34, 'new_value': 27313.54}, {'field': 'total_amount', 'old_value': 19548.34, 'new_value': 27313.54}, {'field': 'order_count', 'old_value': 1291, 'new_value': 1807}]
2025-05-04 09:01:12,915 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-04 09:01:13,337 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-04 09:01:13,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105841.0, 'new_value': 160345.0}, {'field': 'total_amount', 'old_value': 105841.0, 'new_value': 160345.0}, {'field': 'order_count', 'old_value': 2357, 'new_value': 3576}]
2025-05-04 09:01:13,337 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-04 09:01:13,806 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-04 09:01:13,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13987.0, 'new_value': 20563.0}, {'field': 'total_amount', 'old_value': 13987.0, 'new_value': 20563.0}, {'field': 'order_count', 'old_value': 1045, 'new_value': 1460}]
2025-05-04 09:01:13,806 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-04 09:01:14,321 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-04 09:01:14,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 538970.0, 'new_value': 766863.0}, {'field': 'total_amount', 'old_value': 538970.0, 'new_value': 766863.0}, {'field': 'order_count', 'old_value': 10129, 'new_value': 14509}]
2025-05-04 09:01:14,321 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-04 09:01:14,774 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-04 09:01:14,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9997.09, 'new_value': 15820.72}, {'field': 'total_amount', 'old_value': 9997.09, 'new_value': 15820.72}, {'field': 'order_count', 'old_value': 129, 'new_value': 216}]
2025-05-04 09:01:14,774 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-04 09:01:15,274 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-04 09:01:15,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14902.3, 'new_value': 22747.7}, {'field': 'total_amount', 'old_value': 14902.3, 'new_value': 22747.7}, {'field': 'order_count', 'old_value': 347, 'new_value': 513}]
2025-05-04 09:01:15,274 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-04 09:01:15,790 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-04 09:01:15,790 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5286.48, 'new_value': 9849.27}, {'field': 'offline_amount', 'old_value': 63188.9, 'new_value': 87490.6}, {'field': 'total_amount', 'old_value': 68475.38, 'new_value': 97339.87}, {'field': 'order_count', 'old_value': 355, 'new_value': 514}]
2025-05-04 09:01:15,790 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-04 09:01:16,228 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-04 09:01:16,228 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 3440.37}, {'field': 'total_amount', 'old_value': 8310.24, 'new_value': 11750.61}, {'field': 'order_count', 'old_value': 508, 'new_value': 729}]
2025-05-04 09:01:16,228 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-04 09:01:16,665 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-04 09:01:16,665 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 3579.98}, {'field': 'total_amount', 'old_value': 8738.17, 'new_value': 12318.15}, {'field': 'order_count', 'old_value': 510, 'new_value': 723}]
2025-05-04 09:01:16,665 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-04 09:01:17,134 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-04 09:01:17,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92396.48, 'new_value': 127688.77}, {'field': 'total_amount', 'old_value': 92396.48, 'new_value': 127688.77}, {'field': 'order_count', 'old_value': 359, 'new_value': 502}]
2025-05-04 09:01:17,134 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-04 09:01:17,603 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-04 09:01:17,603 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 907.6, 'new_value': 1378.6}, {'field': 'total_amount', 'old_value': 907.6, 'new_value': 1378.6}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 09:01:17,603 - INFO - 日期 2025-05 处理完成 - 更新: 111 条，插入: 0 条，错误: 0 条
2025-05-04 09:01:17,603 - INFO - 数据同步完成！更新: 111 条，插入: 0 条，错误: 0 条
2025-05-04 09:01:17,603 - INFO - =================同步完成====================
2025-05-04 12:00:01,895 - INFO - =================使用默认全量同步=============
2025-05-04 12:00:03,098 - INFO - MySQL查询成功，共获取 3220 条记录
2025-05-04 12:00:03,098 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-04 12:00:03,129 - INFO - 开始处理日期: 2025-01
2025-05-04 12:00:03,129 - INFO - Request Parameters - Page 1:
2025-05-04 12:00:03,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:03,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:04,129 - INFO - Response - Page 1:
2025-05-04 12:00:04,332 - INFO - 第 1 页获取到 100 条记录
2025-05-04 12:00:04,332 - INFO - Request Parameters - Page 2:
2025-05-04 12:00:04,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:04,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:04,911 - INFO - Response - Page 2:
2025-05-04 12:00:05,114 - INFO - 第 2 页获取到 100 条记录
2025-05-04 12:00:05,114 - INFO - Request Parameters - Page 3:
2025-05-04 12:00:05,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:05,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:05,864 - INFO - Response - Page 3:
2025-05-04 12:00:06,067 - INFO - 第 3 页获取到 100 条记录
2025-05-04 12:00:06,067 - INFO - Request Parameters - Page 4:
2025-05-04 12:00:06,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:06,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:06,614 - INFO - Response - Page 4:
2025-05-04 12:00:06,817 - INFO - 第 4 页获取到 100 条记录
2025-05-04 12:00:06,817 - INFO - Request Parameters - Page 5:
2025-05-04 12:00:06,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:06,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:07,286 - INFO - Response - Page 5:
2025-05-04 12:00:07,489 - INFO - 第 5 页获取到 100 条记录
2025-05-04 12:00:07,489 - INFO - Request Parameters - Page 6:
2025-05-04 12:00:07,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:07,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:08,004 - INFO - Response - Page 6:
2025-05-04 12:00:08,207 - INFO - 第 6 页获取到 100 条记录
2025-05-04 12:00:08,207 - INFO - Request Parameters - Page 7:
2025-05-04 12:00:08,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:08,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:08,770 - INFO - Response - Page 7:
2025-05-04 12:00:08,973 - INFO - 第 7 页获取到 82 条记录
2025-05-04 12:00:08,973 - INFO - 查询完成，共获取到 682 条记录
2025-05-04 12:00:08,973 - INFO - 获取到 682 条表单数据
2025-05-04 12:00:08,973 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-04 12:00:08,989 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 12:00:08,989 - INFO - 开始处理日期: 2025-02
2025-05-04 12:00:08,989 - INFO - Request Parameters - Page 1:
2025-05-04 12:00:08,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:08,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:09,520 - INFO - Response - Page 1:
2025-05-04 12:00:09,723 - INFO - 第 1 页获取到 100 条记录
2025-05-04 12:00:09,723 - INFO - Request Parameters - Page 2:
2025-05-04 12:00:09,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:09,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:10,223 - INFO - Response - Page 2:
2025-05-04 12:00:10,426 - INFO - 第 2 页获取到 100 条记录
2025-05-04 12:00:10,426 - INFO - Request Parameters - Page 3:
2025-05-04 12:00:10,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:10,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:11,004 - INFO - Response - Page 3:
2025-05-04 12:00:11,207 - INFO - 第 3 页获取到 100 条记录
2025-05-04 12:00:11,207 - INFO - Request Parameters - Page 4:
2025-05-04 12:00:11,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:11,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:11,645 - INFO - Response - Page 4:
2025-05-04 12:00:11,848 - INFO - 第 4 页获取到 100 条记录
2025-05-04 12:00:11,848 - INFO - Request Parameters - Page 5:
2025-05-04 12:00:11,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:11,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:12,301 - INFO - Response - Page 5:
2025-05-04 12:00:12,504 - INFO - 第 5 页获取到 100 条记录
2025-05-04 12:00:12,504 - INFO - Request Parameters - Page 6:
2025-05-04 12:00:12,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:12,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:12,973 - INFO - Response - Page 6:
2025-05-04 12:00:13,176 - INFO - 第 6 页获取到 100 条记录
2025-05-04 12:00:13,176 - INFO - Request Parameters - Page 7:
2025-05-04 12:00:13,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:13,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:13,645 - INFO - Response - Page 7:
2025-05-04 12:00:13,848 - INFO - 第 7 页获取到 70 条记录
2025-05-04 12:00:13,848 - INFO - 查询完成，共获取到 670 条记录
2025-05-04 12:00:13,848 - INFO - 获取到 670 条表单数据
2025-05-04 12:00:13,848 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-04 12:00:13,864 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 12:00:13,864 - INFO - 开始处理日期: 2025-03
2025-05-04 12:00:13,864 - INFO - Request Parameters - Page 1:
2025-05-04 12:00:13,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:13,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:14,364 - INFO - Response - Page 1:
2025-05-04 12:00:14,567 - INFO - 第 1 页获取到 100 条记录
2025-05-04 12:00:14,567 - INFO - Request Parameters - Page 2:
2025-05-04 12:00:14,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:14,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:15,051 - INFO - Response - Page 2:
2025-05-04 12:00:15,254 - INFO - 第 2 页获取到 100 条记录
2025-05-04 12:00:15,254 - INFO - Request Parameters - Page 3:
2025-05-04 12:00:15,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:15,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:15,739 - INFO - Response - Page 3:
2025-05-04 12:00:15,942 - INFO - 第 3 页获取到 100 条记录
2025-05-04 12:00:15,942 - INFO - Request Parameters - Page 4:
2025-05-04 12:00:15,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:15,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:16,504 - INFO - Response - Page 4:
2025-05-04 12:00:16,707 - INFO - 第 4 页获取到 100 条记录
2025-05-04 12:00:16,707 - INFO - Request Parameters - Page 5:
2025-05-04 12:00:16,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:16,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:17,192 - INFO - Response - Page 5:
2025-05-04 12:00:17,395 - INFO - 第 5 页获取到 100 条记录
2025-05-04 12:00:17,395 - INFO - Request Parameters - Page 6:
2025-05-04 12:00:17,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:17,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:17,942 - INFO - Response - Page 6:
2025-05-04 12:00:18,145 - INFO - 第 6 页获取到 100 条记录
2025-05-04 12:00:18,145 - INFO - Request Parameters - Page 7:
2025-05-04 12:00:18,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:18,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:18,551 - INFO - Response - Page 7:
2025-05-04 12:00:18,754 - INFO - 第 7 页获取到 61 条记录
2025-05-04 12:00:18,754 - INFO - 查询完成，共获取到 661 条记录
2025-05-04 12:00:18,754 - INFO - 获取到 661 条表单数据
2025-05-04 12:00:18,754 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-04 12:00:18,770 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 12:00:18,770 - INFO - 开始处理日期: 2025-04
2025-05-04 12:00:18,770 - INFO - Request Parameters - Page 1:
2025-05-04 12:00:18,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:18,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:19,239 - INFO - Response - Page 1:
2025-05-04 12:00:19,442 - INFO - 第 1 页获取到 100 条记录
2025-05-04 12:00:19,442 - INFO - Request Parameters - Page 2:
2025-05-04 12:00:19,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:19,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:19,910 - INFO - Response - Page 2:
2025-05-04 12:00:20,114 - INFO - 第 2 页获取到 100 条记录
2025-05-04 12:00:20,114 - INFO - Request Parameters - Page 3:
2025-05-04 12:00:20,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:20,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:20,614 - INFO - Response - Page 3:
2025-05-04 12:00:20,817 - INFO - 第 3 页获取到 100 条记录
2025-05-04 12:00:20,817 - INFO - Request Parameters - Page 4:
2025-05-04 12:00:20,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:20,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:21,270 - INFO - Response - Page 4:
2025-05-04 12:00:21,473 - INFO - 第 4 页获取到 100 条记录
2025-05-04 12:00:21,473 - INFO - Request Parameters - Page 5:
2025-05-04 12:00:21,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:21,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:21,957 - INFO - Response - Page 5:
2025-05-04 12:00:22,160 - INFO - 第 5 页获取到 100 条记录
2025-05-04 12:00:22,160 - INFO - Request Parameters - Page 6:
2025-05-04 12:00:22,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:22,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:22,629 - INFO - Response - Page 6:
2025-05-04 12:00:22,832 - INFO - 第 6 页获取到 100 条记录
2025-05-04 12:00:22,832 - INFO - Request Parameters - Page 7:
2025-05-04 12:00:22,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:22,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:23,176 - INFO - Response - Page 7:
2025-05-04 12:00:23,379 - INFO - 第 7 页获取到 27 条记录
2025-05-04 12:00:23,379 - INFO - 查询完成，共获取到 627 条记录
2025-05-04 12:00:23,379 - INFO - 获取到 627 条表单数据
2025-05-04 12:00:23,379 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-04 12:00:23,379 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MX9
2025-05-04 12:00:23,895 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MX9
2025-05-04 12:00:23,895 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 192502.8, 'new_value': 202446.21}, {'field': 'offline_amount', 'old_value': 26114.11, 'new_value': 27048.11}, {'field': 'total_amount', 'old_value': 218616.91, 'new_value': 229494.32}, {'field': 'order_count', 'old_value': 6267, 'new_value': 6611}]
2025-05-04 12:00:23,895 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2B
2025-05-04 12:00:24,348 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2B
2025-05-04 12:00:24,348 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44405.34, 'new_value': 47944.06}, {'field': 'total_amount', 'old_value': 91168.36, 'new_value': 94707.08}, {'field': 'order_count', 'old_value': 3245, 'new_value': 3393}]
2025-05-04 12:00:24,348 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXB
2025-05-04 12:00:24,817 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXB
2025-05-04 12:00:24,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86713.68, 'new_value': 89723.63}, {'field': 'offline_amount', 'old_value': 124149.87, 'new_value': 128975.9}, {'field': 'total_amount', 'old_value': 210863.55, 'new_value': 218699.53}, {'field': 'order_count', 'old_value': 7361, 'new_value': 7644}]
2025-05-04 12:00:24,817 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN6
2025-05-04 12:00:25,254 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN6
2025-05-04 12:00:25,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39731.68, 'new_value': 41611.94}, {'field': 'offline_amount', 'old_value': 304842.0, 'new_value': 318058.03}, {'field': 'total_amount', 'old_value': 344573.68, 'new_value': 359669.97}, {'field': 'order_count', 'old_value': 10419, 'new_value': 10835}]
2025-05-04 12:00:25,254 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS6
2025-05-04 12:00:25,785 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS6
2025-05-04 12:00:25,801 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28525.48, 'new_value': 30565.64}, {'field': 'offline_amount', 'old_value': 60811.55, 'new_value': 62961.09}, {'field': 'total_amount', 'old_value': 89337.03, 'new_value': 93526.73}, {'field': 'order_count', 'old_value': 789, 'new_value': 826}]
2025-05-04 12:00:25,801 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M07
2025-05-04 12:00:26,348 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M07
2025-05-04 12:00:26,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104538.0, 'new_value': 109312.94}, {'field': 'total_amount', 'old_value': 110787.1, 'new_value': 115562.04}, {'field': 'order_count', 'old_value': 2776, 'new_value': 2904}]
2025-05-04 12:00:26,348 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M67
2025-05-04 12:00:26,801 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M67
2025-05-04 12:00:26,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 591037.77, 'new_value': 614547.17}, {'field': 'total_amount', 'old_value': 591037.77, 'new_value': 614547.17}, {'field': 'order_count', 'old_value': 5962, 'new_value': 6198}]
2025-05-04 12:00:26,801 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQI
2025-05-04 12:00:27,176 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQI
2025-05-04 12:00:27,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 245555.35, 'new_value': 254944.35}, {'field': 'total_amount', 'old_value': 245555.35, 'new_value': 254944.35}, {'field': 'order_count', 'old_value': 10774, 'new_value': 11222}]
2025-05-04 12:00:27,192 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME1
2025-05-04 12:00:27,614 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME1
2025-05-04 12:00:27,614 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179172.9, 'new_value': 186258.9}, {'field': 'total_amount', 'old_value': 179172.9, 'new_value': 186258.9}, {'field': 'order_count', 'old_value': 19119, 'new_value': 19836}]
2025-05-04 12:00:27,614 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU6
2025-05-04 12:00:28,082 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU6
2025-05-04 12:00:28,082 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159764.96, 'new_value': 166169.96}, {'field': 'total_amount', 'old_value': 159764.96, 'new_value': 166169.96}, {'field': 'order_count', 'old_value': 3782, 'new_value': 3950}]
2025-05-04 12:00:28,082 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-05-04 12:00:28,520 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-05-04 12:00:28,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43617.59, 'new_value': 49087.95}, {'field': 'total_amount', 'old_value': 43617.59, 'new_value': 49087.95}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-04 12:00:28,520 - INFO - 开始更新记录 - 表单实例ID: FINST-NLF66581Q4VURCQVEAQGU8UJUISX2V9JF4Z9MPF1
2025-05-04 12:00:28,973 - INFO - 更新表单数据成功: FINST-NLF66581Q4VURCQVEAQGU8UJUISX2V9JF4Z9MPF1
2025-05-04 12:00:28,973 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26698.25, 'new_value': 39569.56}, {'field': 'total_amount', 'old_value': 41337.25, 'new_value': 54208.56}, {'field': 'order_count', 'old_value': 1038, 'new_value': 1322}]
2025-05-04 12:00:28,973 - INFO - 日期 2025-04 处理完成 - 更新: 12 条，插入: 0 条，错误: 0 条
2025-05-04 12:00:28,973 - INFO - 开始处理日期: 2025-05
2025-05-04 12:00:28,973 - INFO - Request Parameters - Page 1:
2025-05-04 12:00:28,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:28,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:29,426 - INFO - Response - Page 1:
2025-05-04 12:00:29,629 - INFO - 第 1 页获取到 100 条记录
2025-05-04 12:00:29,629 - INFO - Request Parameters - Page 2:
2025-05-04 12:00:29,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:29,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:30,114 - INFO - Response - Page 2:
2025-05-04 12:00:30,317 - INFO - 第 2 页获取到 100 条记录
2025-05-04 12:00:30,317 - INFO - Request Parameters - Page 3:
2025-05-04 12:00:30,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:30,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:30,817 - INFO - Response - Page 3:
2025-05-04 12:00:31,020 - INFO - 第 3 页获取到 100 条记录
2025-05-04 12:00:31,020 - INFO - Request Parameters - Page 4:
2025-05-04 12:00:31,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:31,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:31,629 - INFO - Response - Page 4:
2025-05-04 12:00:31,832 - INFO - 第 4 页获取到 100 条记录
2025-05-04 12:00:31,832 - INFO - Request Parameters - Page 5:
2025-05-04 12:00:31,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:31,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:32,332 - INFO - Response - Page 5:
2025-05-04 12:00:32,535 - INFO - 第 5 页获取到 100 条记录
2025-05-04 12:00:32,535 - INFO - Request Parameters - Page 6:
2025-05-04 12:00:32,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 12:00:32,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 12:00:32,973 - INFO - Response - Page 6:
2025-05-04 12:00:33,176 - INFO - 第 6 页获取到 66 条记录
2025-05-04 12:00:33,176 - INFO - 查询完成，共获取到 566 条记录
2025-05-04 12:00:33,176 - INFO - 获取到 566 条表单数据
2025-05-04 12:00:33,176 - INFO - 当前日期 2025-05 有 580 条MySQL数据需要处理
2025-05-04 12:00:33,176 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-04 12:00:33,660 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-04 12:00:33,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5820.0, 'new_value': 8920.0}, {'field': 'total_amount', 'old_value': 5960.0, 'new_value': 9060.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 109}]
2025-05-04 12:00:33,660 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-04 12:00:34,160 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-04 12:00:34,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39119.0, 'new_value': 53747.0}, {'field': 'total_amount', 'old_value': 39119.0, 'new_value': 53747.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 33}]
2025-05-04 12:00:34,160 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-04 12:00:34,629 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-04 12:00:34,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1060.0, 'new_value': 1325.0}, {'field': 'total_amount', 'old_value': 1060.0, 'new_value': 1325.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-04 12:00:34,629 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-04 12:00:35,067 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-04 12:00:35,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5998.66, 'new_value': 9943.9}, {'field': 'total_amount', 'old_value': 5998.66, 'new_value': 9943.9}, {'field': 'order_count', 'old_value': 278, 'new_value': 539}]
2025-05-04 12:00:35,067 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-04 12:00:35,520 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-04 12:00:35,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82248.07, 'new_value': 117012.18}, {'field': 'total_amount', 'old_value': 82248.07, 'new_value': 117012.18}, {'field': 'order_count', 'old_value': 264, 'new_value': 385}]
2025-05-04 12:00:35,520 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-04 12:00:35,973 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-04 12:00:35,973 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1247.0}, {'field': 'total_amount', 'old_value': 2897.0, 'new_value': 4144.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-04 12:00:35,973 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-04 12:00:36,426 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-04 12:00:36,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19960.0, 'new_value': 27950.0}, {'field': 'total_amount', 'old_value': 19960.0, 'new_value': 27950.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 13}]
2025-05-04 12:00:36,426 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-04 12:00:36,848 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-04 12:00:36,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55803.0, 'new_value': 74803.0}, {'field': 'total_amount', 'old_value': 55902.0, 'new_value': 74902.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 23}]
2025-05-04 12:00:36,848 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-04 12:00:37,332 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-04 12:00:37,332 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31708.0, 'new_value': 39414.0}, {'field': 'offline_amount', 'old_value': 35245.0, 'new_value': 51593.0}, {'field': 'total_amount', 'old_value': 66953.0, 'new_value': 91007.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 95}]
2025-05-04 12:00:37,348 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-04 12:00:37,832 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-04 12:00:37,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14931.0, 'new_value': 21813.0}, {'field': 'total_amount', 'old_value': 14931.0, 'new_value': 21813.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 18}]
2025-05-04 12:00:37,832 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-04 12:00:38,301 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-04 12:00:38,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 350.0}, {'field': 'offline_amount', 'old_value': 1200.5, 'new_value': 1401.5}, {'field': 'total_amount', 'old_value': 1200.5, 'new_value': 1751.5}, {'field': 'order_count', 'old_value': 84, 'new_value': 114}]
2025-05-04 12:00:38,301 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-04 12:00:38,770 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-04 12:00:38,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9185.0, 'new_value': 26971.0}, {'field': 'total_amount', 'old_value': 9185.0, 'new_value': 26971.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 10}]
2025-05-04 12:00:38,770 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-04 12:00:39,332 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-04 12:00:39,332 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6013.32, 'new_value': 8766.46}, {'field': 'offline_amount', 'old_value': 10089.0, 'new_value': 13656.0}, {'field': 'total_amount', 'old_value': 16102.32, 'new_value': 22422.46}, {'field': 'order_count', 'old_value': 179, 'new_value': 240}]
2025-05-04 12:00:39,332 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-04 12:00:39,817 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-04 12:00:39,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2651.0, 'new_value': 4048.0}, {'field': 'offline_amount', 'old_value': 4768.0, 'new_value': 6172.0}, {'field': 'total_amount', 'old_value': 7419.0, 'new_value': 10220.0}, {'field': 'order_count', 'old_value': 307, 'new_value': 430}]
2025-05-04 12:00:39,817 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-04 12:00:40,301 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-04 12:00:40,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47641.3, 'new_value': 70299.7}, {'field': 'total_amount', 'old_value': 47641.3, 'new_value': 70299.7}, {'field': 'order_count', 'old_value': 519, 'new_value': 756}]
2025-05-04 12:00:40,301 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-04 12:00:40,754 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-04 12:00:40,754 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7584.0, 'new_value': 11358.0}, {'field': 'total_amount', 'old_value': 7584.0, 'new_value': 11358.0}, {'field': 'order_count', 'old_value': 389, 'new_value': 580}]
2025-05-04 12:00:40,754 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-04 12:00:41,176 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-04 12:00:41,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3438.0, 'new_value': 3928.0}, {'field': 'total_amount', 'old_value': 3438.0, 'new_value': 3928.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-04 12:00:41,176 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-04 12:00:41,613 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-04 12:00:41,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42664.0, 'new_value': 67250.0}, {'field': 'total_amount', 'old_value': 42664.0, 'new_value': 67250.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 58}]
2025-05-04 12:00:41,613 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-04 12:00:42,082 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-04 12:00:42,082 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9845.89, 'new_value': 13973.42}, {'field': 'total_amount', 'old_value': 9845.89, 'new_value': 13973.42}, {'field': 'order_count', 'old_value': 99, 'new_value': 137}]
2025-05-04 12:00:42,082 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-04 12:00:42,582 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-04 12:00:42,582 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27850.0, 'new_value': 42230.0}, {'field': 'total_amount', 'old_value': 27850.0, 'new_value': 42230.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 9}]
2025-05-04 12:00:42,582 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-04 12:00:43,129 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-04 12:00:43,129 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20000.0, 'new_value': 62225.13}, {'field': 'total_amount', 'old_value': 20000.0, 'new_value': 62225.13}, {'field': 'order_count', 'old_value': 5, 'new_value': 11}]
2025-05-04 12:00:43,129 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-04 12:00:43,598 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-04 12:00:43,598 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12166.4, 'new_value': 14360.6}, {'field': 'total_amount', 'old_value': 12166.4, 'new_value': 14360.6}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-04 12:00:43,598 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-04 12:00:44,082 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-04 12:00:44,082 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 851.0, 'new_value': 1486.0}, {'field': 'total_amount', 'old_value': 3227.0, 'new_value': 3862.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 13}]
2025-05-04 12:00:44,082 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-04 12:00:44,582 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-04 12:00:44,582 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15504.0, 'new_value': 19133.0}, {'field': 'total_amount', 'old_value': 15504.0, 'new_value': 19133.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 40}]
2025-05-04 12:00:44,582 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-04 12:00:45,035 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-04 12:00:45,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 653.34, 'new_value': 2452.06}, {'field': 'total_amount', 'old_value': 653.34, 'new_value': 2452.06}, {'field': 'order_count', 'old_value': 4, 'new_value': 9}]
2025-05-04 12:00:45,035 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-04 12:00:45,567 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-04 12:00:45,567 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27391.66, 'new_value': 39623.11}, {'field': 'offline_amount', 'old_value': 6204.09, 'new_value': 7861.73}, {'field': 'total_amount', 'old_value': 33595.75, 'new_value': 47484.84}, {'field': 'order_count', 'old_value': 118, 'new_value': 163}]
2025-05-04 12:00:45,567 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-04 12:00:46,020 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-04 12:00:46,020 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22934.0, 'new_value': 42343.0}, {'field': 'offline_amount', 'old_value': 10069.2, 'new_value': 14151.0}, {'field': 'total_amount', 'old_value': 33003.2, 'new_value': 56494.0}, {'field': 'order_count', 'old_value': 196, 'new_value': 330}]
2025-05-04 12:00:46,020 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-04 12:00:46,457 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-04 12:00:46,457 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 844.0, 'new_value': 1217.0}, {'field': 'total_amount', 'old_value': 962.0, 'new_value': 1335.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 28}]
2025-05-04 12:00:46,457 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-04 12:00:46,957 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-04 12:00:46,957 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7089.43, 'new_value': 10395.94}, {'field': 'total_amount', 'old_value': 7089.43, 'new_value': 10395.94}, {'field': 'order_count', 'old_value': 171, 'new_value': 241}]
2025-05-04 12:00:46,957 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-04 12:00:47,426 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-04 12:00:47,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24805.0, 'new_value': 26225.0}, {'field': 'total_amount', 'old_value': 24805.0, 'new_value': 26225.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-05-04 12:00:47,426 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-04 12:00:47,879 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-04 12:00:47,879 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17959.0, 'new_value': 26437.0}, {'field': 'offline_amount', 'old_value': 5903.7, 'new_value': 10057.5}, {'field': 'total_amount', 'old_value': 23862.7, 'new_value': 36494.5}, {'field': 'order_count', 'old_value': 155, 'new_value': 235}]
2025-05-04 12:00:47,879 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-04 12:00:48,410 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-04 12:00:48,410 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 687.93, 'new_value': 1091.68}, {'field': 'offline_amount', 'old_value': 18730.96, 'new_value': 23815.05}, {'field': 'total_amount', 'old_value': 19418.89, 'new_value': 24906.73}, {'field': 'order_count', 'old_value': 188, 'new_value': 251}]
2025-05-04 12:00:48,410 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-04 12:00:48,879 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-04 12:00:48,879 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1782.0, 'new_value': 1944.0}, {'field': 'total_amount', 'old_value': 5598.0, 'new_value': 5760.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-04 12:00:48,879 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-04 12:00:49,332 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-04 12:00:49,332 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18408.0, 'new_value': 25902.0}, {'field': 'total_amount', 'old_value': 18408.0, 'new_value': 25902.0}, {'field': 'order_count', 'old_value': 101, 'new_value': 138}]
2025-05-04 12:00:49,332 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-04 12:00:49,801 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-04 12:00:49,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21070.19, 'new_value': 31485.43}, {'field': 'total_amount', 'old_value': 21070.19, 'new_value': 31485.43}, {'field': 'order_count', 'old_value': 68, 'new_value': 99}]
2025-05-04 12:00:49,801 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-04 12:00:50,317 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-04 12:00:50,317 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11981.0, 'new_value': 17143.0}, {'field': 'total_amount', 'old_value': 11981.0, 'new_value': 17143.0}, {'field': 'order_count', 'old_value': 272, 'new_value': 391}]
2025-05-04 12:00:50,317 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-04 12:00:50,832 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-04 12:00:50,832 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6132.41, 'new_value': 9000.38}, {'field': 'offline_amount', 'old_value': 82778.1, 'new_value': 125743.9}, {'field': 'total_amount', 'old_value': 88910.51, 'new_value': 134744.28}, {'field': 'order_count', 'old_value': 255, 'new_value': 392}]
2025-05-04 12:00:50,832 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-04 12:00:51,348 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-04 12:00:51,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2502.46, 'new_value': 4534.48}, {'field': 'total_amount', 'old_value': 2502.46, 'new_value': 4534.48}, {'field': 'order_count', 'old_value': 6, 'new_value': 9}]
2025-05-04 12:00:51,348 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-04 12:00:51,754 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-04 12:00:51,754 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11954.0, 'new_value': 17167.0}, {'field': 'total_amount', 'old_value': 11954.0, 'new_value': 17167.0}, {'field': 'order_count', 'old_value': 375, 'new_value': 550}]
2025-05-04 12:00:51,754 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-04 12:00:52,192 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-04 12:00:52,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75907.06, 'new_value': 107775.39}, {'field': 'total_amount', 'old_value': 75907.06, 'new_value': 107775.39}, {'field': 'order_count', 'old_value': 533, 'new_value': 760}]
2025-05-04 12:00:52,192 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-04 12:00:52,707 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-04 12:00:52,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7933.0, 'new_value': 8033.0}, {'field': 'offline_amount', 'old_value': 31633.0, 'new_value': 33749.0}, {'field': 'total_amount', 'old_value': 39566.0, 'new_value': 41782.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-04 12:00:52,707 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-04 12:00:53,176 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-04 12:00:53,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6099.0, 'new_value': 7787.0}, {'field': 'total_amount', 'old_value': 6099.0, 'new_value': 7787.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 12:00:53,176 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-04 12:00:53,645 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-04 12:00:53,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8116.82, 'new_value': 13289.82}, {'field': 'total_amount', 'old_value': 8116.82, 'new_value': 13289.82}, {'field': 'order_count', 'old_value': 315, 'new_value': 490}]
2025-05-04 12:00:53,645 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-04 12:00:54,192 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-04 12:00:54,192 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 238.9, 'new_value': 6888.9}, {'field': 'total_amount', 'old_value': 238.9, 'new_value': 6888.9}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 12:00:54,192 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-04 12:00:54,613 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-04 12:00:54,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 937.5, 'new_value': 1288.62}, {'field': 'total_amount', 'old_value': 937.5, 'new_value': 1288.62}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 12:00:54,613 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-04 12:00:55,067 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-04 12:00:55,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8920.0, 'new_value': 22730.0}, {'field': 'total_amount', 'old_value': 8920.0, 'new_value': 22730.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 29}]
2025-05-04 12:00:55,067 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-04 12:00:55,488 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-04 12:00:55,488 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50427.0, 'new_value': 62541.0}, {'field': 'total_amount', 'old_value': 50427.0, 'new_value': 62541.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 97}]
2025-05-04 12:00:55,488 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-04 12:00:55,895 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-04 12:00:55,895 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25120.08, 'new_value': 31669.08}, {'field': 'total_amount', 'old_value': 25120.08, 'new_value': 31669.08}, {'field': 'order_count', 'old_value': 122, 'new_value': 158}]
2025-05-04 12:00:55,895 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-04 12:00:56,301 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-04 12:00:56,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225482.1, 'new_value': 341885.4}, {'field': 'total_amount', 'old_value': 225482.1, 'new_value': 341885.4}, {'field': 'order_count', 'old_value': 351, 'new_value': 539}]
2025-05-04 12:00:56,301 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-04 12:00:56,817 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-04 12:00:56,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12896.75, 'new_value': 20467.75}, {'field': 'offline_amount', 'old_value': 10791.0, 'new_value': 12920.0}, {'field': 'total_amount', 'old_value': 23687.75, 'new_value': 33387.75}, {'field': 'order_count', 'old_value': 110, 'new_value': 167}]
2025-05-04 12:00:56,817 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-04 12:00:57,332 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-04 12:00:57,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4766.0, 'new_value': 7986.0}, {'field': 'total_amount', 'old_value': 9507.0, 'new_value': 12727.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-04 12:00:57,332 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-04 12:00:57,785 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-04 12:00:57,785 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4002.0, 'new_value': 6438.0}, {'field': 'total_amount', 'old_value': 4002.0, 'new_value': 6438.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 111}]
2025-05-04 12:00:57,785 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-04 12:00:58,254 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-04 12:00:58,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 687.8, 'new_value': 919.0}, {'field': 'offline_amount', 'old_value': 1595.47, 'new_value': 2291.67}, {'field': 'total_amount', 'old_value': 2283.27, 'new_value': 3210.67}, {'field': 'order_count', 'old_value': 74, 'new_value': 101}]
2025-05-04 12:00:58,254 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-04 12:00:58,660 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-04 12:00:58,660 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16579.06, 'new_value': 23628.7}, {'field': 'offline_amount', 'old_value': 14330.94, 'new_value': 18908.28}, {'field': 'total_amount', 'old_value': 30910.0, 'new_value': 42536.98}, {'field': 'order_count', 'old_value': 234, 'new_value': 325}]
2025-05-04 12:00:58,660 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-04 12:00:59,160 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-04 12:00:59,160 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55358.0, 'new_value': 78327.0}, {'field': 'offline_amount', 'old_value': 1142.0, 'new_value': 5242.0}, {'field': 'total_amount', 'old_value': 56500.0, 'new_value': 83569.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 103}]
2025-05-04 12:00:59,160 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-04 12:00:59,660 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-04 12:00:59,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2554.0, 'new_value': 5211.0}, {'field': 'total_amount', 'old_value': 2554.0, 'new_value': 5211.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 12}]
2025-05-04 12:00:59,660 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-04 12:01:00,113 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-04 12:01:00,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17896.0, 'new_value': 27920.0}, {'field': 'total_amount', 'old_value': 20577.0, 'new_value': 30601.0}, {'field': 'order_count', 'old_value': 438, 'new_value': 660}]
2025-05-04 12:01:00,113 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-04 12:01:00,551 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-04 12:01:00,551 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1062.37, 'new_value': 1534.14}, {'field': 'offline_amount', 'old_value': 30771.5, 'new_value': 42544.2}, {'field': 'total_amount', 'old_value': 31833.87, 'new_value': 44078.34}, {'field': 'order_count', 'old_value': 195, 'new_value': 278}]
2025-05-04 12:01:00,551 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-04 12:01:01,098 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-04 12:01:01,098 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18992.43, 'new_value': 25834.29}, {'field': 'offline_amount', 'old_value': 11916.0, 'new_value': 24052.45}, {'field': 'total_amount', 'old_value': 30908.43, 'new_value': 49886.74}, {'field': 'order_count', 'old_value': 232, 'new_value': 420}]
2025-05-04 12:01:01,098 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-04 12:01:01,566 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-04 12:01:01,566 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8180.0, 'new_value': 9796.1}, {'field': 'total_amount', 'old_value': 8603.65, 'new_value': 10219.75}, {'field': 'order_count', 'old_value': 29, 'new_value': 32}]
2025-05-04 12:01:01,566 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-04 12:01:01,988 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-04 12:01:01,988 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8151.54, 'new_value': 12205.14}, {'field': 'total_amount', 'old_value': 8151.54, 'new_value': 12205.14}, {'field': 'order_count', 'old_value': 213, 'new_value': 317}]
2025-05-04 12:01:01,988 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-04 12:01:02,395 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-04 12:01:02,395 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7453.85, 'new_value': 11357.62}, {'field': 'offline_amount', 'old_value': 57127.81, 'new_value': 76457.8}, {'field': 'total_amount', 'old_value': 64581.66, 'new_value': 87815.42}, {'field': 'order_count', 'old_value': 375, 'new_value': 533}]
2025-05-04 12:01:02,395 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-04 12:01:02,801 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-04 12:01:02,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29898.75, 'new_value': 49066.86}, {'field': 'total_amount', 'old_value': 29898.75, 'new_value': 49066.86}, {'field': 'order_count', 'old_value': 66, 'new_value': 91}]
2025-05-04 12:01:02,801 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-04 12:01:03,238 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-04 12:01:03,238 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1384.59, 'new_value': 3034.04}, {'field': 'offline_amount', 'old_value': 52214.32, 'new_value': 77594.85}, {'field': 'total_amount', 'old_value': 53598.91, 'new_value': 80628.89}, {'field': 'order_count', 'old_value': 187, 'new_value': 272}]
2025-05-04 12:01:03,238 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-04 12:01:03,676 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-04 12:01:03,676 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14482.0, 'new_value': 21180.0}, {'field': 'offline_amount', 'old_value': 4899.0, 'new_value': 9986.0}, {'field': 'total_amount', 'old_value': 19381.0, 'new_value': 31166.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 33}]
2025-05-04 12:01:03,676 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-04 12:01:04,113 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-04 12:01:04,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 722.0, 'new_value': 821.0}, {'field': 'total_amount', 'old_value': 722.0, 'new_value': 821.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-04 12:01:04,113 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-04 12:01:04,551 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-04 12:01:04,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3823.0, 'new_value': 6285.0}, {'field': 'total_amount', 'old_value': 3823.0, 'new_value': 6285.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 12:01:04,551 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-04 12:01:05,004 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-04 12:01:05,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4191.0, 'new_value': 4345.0}, {'field': 'total_amount', 'old_value': 4191.0, 'new_value': 4345.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-04 12:01:05,004 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-04 12:01:05,457 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-04 12:01:05,457 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 440.0, 'new_value': 778.0}, {'field': 'total_amount', 'old_value': 440.0, 'new_value': 778.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-04 12:01:05,457 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-04 12:01:05,910 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-04 12:01:05,910 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 698.09, 'new_value': 994.84}, {'field': 'offline_amount', 'old_value': 1197.0, 'new_value': 1296.0}, {'field': 'total_amount', 'old_value': 1895.09, 'new_value': 2290.84}, {'field': 'order_count', 'old_value': 17, 'new_value': 33}]
2025-05-04 12:01:05,910 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-04 12:01:06,363 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-04 12:01:06,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 338300.6, 'new_value': 488300.6}, {'field': 'total_amount', 'old_value': 338300.6, 'new_value': 488300.6}, {'field': 'order_count', 'old_value': 192, 'new_value': 193}]
2025-05-04 12:01:06,363 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-04 12:01:06,941 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-04 12:01:06,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8348.81, 'new_value': 10501.26}, {'field': 'total_amount', 'old_value': 8348.81, 'new_value': 10501.26}, {'field': 'order_count', 'old_value': 54, 'new_value': 80}]
2025-05-04 12:01:06,941 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-04 12:01:07,363 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-04 12:01:07,363 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 356.0, 'new_value': 575.0}, {'field': 'offline_amount', 'old_value': 2529.3, 'new_value': 3449.1}, {'field': 'total_amount', 'old_value': 2885.3, 'new_value': 4024.1}, {'field': 'order_count', 'old_value': 110, 'new_value': 157}]
2025-05-04 12:01:07,363 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-04 12:01:07,785 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-04 12:01:07,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13998.74, 'new_value': 20762.8}, {'field': 'total_amount', 'old_value': 13998.74, 'new_value': 20762.8}, {'field': 'order_count', 'old_value': 21, 'new_value': 44}]
2025-05-04 12:01:07,785 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-04 12:01:08,270 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-04 12:01:08,270 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140000.0, 'new_value': 200000.0}, {'field': 'total_amount', 'old_value': 140000.0, 'new_value': 200000.0}, {'field': 'order_count', 'old_value': 321, 'new_value': 322}]
2025-05-04 12:01:08,270 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-04 12:01:08,738 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-04 12:01:08,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9012.0, 'new_value': 26332.0}, {'field': 'total_amount', 'old_value': 9012.0, 'new_value': 26332.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 10}]
2025-05-04 12:01:08,738 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAL
2025-05-04 12:01:09,176 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAL
2025-05-04 12:01:09,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2399.0, 'new_value': 4298.0}, {'field': 'total_amount', 'old_value': 2399.0, 'new_value': 4298.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-04 12:01:09,176 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-04 12:01:09,660 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-04 12:01:09,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13040.0, 'new_value': 16900.0}, {'field': 'total_amount', 'old_value': 13040.0, 'new_value': 16900.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-05-04 12:01:09,660 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-04 12:01:10,066 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-04 12:01:10,066 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20728.0, 'new_value': 32895.0}, {'field': 'total_amount', 'old_value': 20728.0, 'new_value': 32895.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 121}]
2025-05-04 12:01:10,066 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-04 12:01:10,582 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-04 12:01:10,582 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4368.0, 'new_value': 11948.0}, {'field': 'total_amount', 'old_value': 4368.0, 'new_value': 11948.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 5}]
2025-05-04 12:01:10,582 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-04 12:01:11,051 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-04 12:01:11,051 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23424.42, 'new_value': 32508.71}, {'field': 'total_amount', 'old_value': 23424.42, 'new_value': 32508.71}, {'field': 'order_count', 'old_value': 566, 'new_value': 795}]
2025-05-04 12:01:11,051 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-04 12:01:11,473 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-04 12:01:11,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5530.0, 'new_value': 7210.0}, {'field': 'total_amount', 'old_value': 5530.0, 'new_value': 7210.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-04 12:01:11,473 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-04 12:01:11,895 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-04 12:01:11,895 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12982.0, 'new_value': 20973.78}, {'field': 'total_amount', 'old_value': 12982.0, 'new_value': 20973.78}, {'field': 'order_count', 'old_value': 60, 'new_value': 97}]
2025-05-04 12:01:11,895 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-04 12:01:12,410 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-04 12:01:12,410 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1959.41, 'new_value': 2976.43}, {'field': 'offline_amount', 'old_value': 2722.9, 'new_value': 3982.9}, {'field': 'total_amount', 'old_value': 4682.31, 'new_value': 6959.33}, {'field': 'order_count', 'old_value': 166, 'new_value': 247}]
2025-05-04 12:01:12,410 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-04 12:01:12,863 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-04 12:01:12,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2037.0, 'new_value': 3778.0}, {'field': 'total_amount', 'old_value': 4445.0, 'new_value': 6186.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 27}]
2025-05-04 12:01:12,863 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-04 12:01:13,363 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-04 12:01:13,363 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5054.0, 'new_value': 7223.0}, {'field': 'offline_amount', 'old_value': 7245.87, 'new_value': 11171.77}, {'field': 'total_amount', 'old_value': 12299.87, 'new_value': 18394.77}, {'field': 'order_count', 'old_value': 127, 'new_value': 185}]
2025-05-04 12:01:13,363 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-04 12:01:13,832 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-04 12:01:13,832 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9946.3, 'new_value': 16347.2}, {'field': 'offline_amount', 'old_value': 10568.96, 'new_value': 14187.47}, {'field': 'total_amount', 'old_value': 20515.26, 'new_value': 30534.67}, {'field': 'order_count', 'old_value': 158, 'new_value': 235}]
2025-05-04 12:01:13,832 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-04 12:01:14,332 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-04 12:01:14,332 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 877.75, 'new_value': 1298.73}, {'field': 'offline_amount', 'old_value': 2408.42, 'new_value': 3106.3}, {'field': 'total_amount', 'old_value': 3286.17, 'new_value': 4405.03}, {'field': 'order_count', 'old_value': 166, 'new_value': 228}]
2025-05-04 12:01:14,332 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-04 12:01:14,801 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-04 12:01:14,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7569.0, 'new_value': 12738.0}, {'field': 'total_amount', 'old_value': 7569.0, 'new_value': 12738.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 21}]
2025-05-04 12:01:14,801 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-04 12:01:15,238 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-04 12:01:15,238 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 638753.0, 'new_value': 895928.0}, {'field': 'total_amount', 'old_value': 638753.0, 'new_value': 895928.0}, {'field': 'order_count', 'old_value': 8974, 'new_value': 13123}]
2025-05-04 12:01:15,238 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-04 12:01:15,645 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-04 12:01:15,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9464.0, 'new_value': 13690.0}, {'field': 'total_amount', 'old_value': 9464.0, 'new_value': 13690.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 88}]
2025-05-04 12:01:15,645 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-04 12:01:16,113 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-04 12:01:16,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3082.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3082.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-04 12:01:16,113 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-04 12:01:16,566 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-04 12:01:16,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70615.0, 'new_value': 99901.0}, {'field': 'total_amount', 'old_value': 70615.0, 'new_value': 99901.0}, {'field': 'order_count', 'old_value': 538, 'new_value': 765}]
2025-05-04 12:01:16,566 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-04 12:01:17,035 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-04 12:01:17,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7391.0, 'new_value': 9393.0}, {'field': 'total_amount', 'old_value': 7391.0, 'new_value': 9393.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-05-04 12:01:17,035 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-04 12:01:17,551 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-04 12:01:17,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16341.51, 'new_value': 21958.86}, {'field': 'total_amount', 'old_value': 16341.51, 'new_value': 21958.86}, {'field': 'order_count', 'old_value': 445, 'new_value': 603}]
2025-05-04 12:01:17,551 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-04 12:01:18,004 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-04 12:01:18,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9300.0, 'new_value': 12937.0}, {'field': 'total_amount', 'old_value': 9300.0, 'new_value': 12937.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 6}]
2025-05-04 12:01:18,004 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-04 12:01:18,457 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-04 12:01:18,457 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13962.68, 'new_value': 20215.74}, {'field': 'offline_amount', 'old_value': 26111.2, 'new_value': 32177.85}, {'field': 'total_amount', 'old_value': 40073.88, 'new_value': 52393.59}, {'field': 'order_count', 'old_value': 381, 'new_value': 529}]
2025-05-04 12:01:18,457 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-04 12:01:18,926 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-04 12:01:18,926 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5482.28, 'new_value': 7647.28}, {'field': 'total_amount', 'old_value': 5482.28, 'new_value': 7647.28}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-05-04 12:01:18,926 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-04 12:01:19,410 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-04 12:01:19,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16264.0, 'new_value': 24373.0}, {'field': 'total_amount', 'old_value': 16264.0, 'new_value': 24373.0}, {'field': 'order_count', 'old_value': 558, 'new_value': 844}]
2025-05-04 12:01:19,410 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-04 12:01:19,941 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-04 12:01:19,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3246.0, 'new_value': 4643.0}, {'field': 'total_amount', 'old_value': 3246.0, 'new_value': 4643.0}, {'field': 'order_count', 'old_value': 280, 'new_value': 439}]
2025-05-04 12:01:19,941 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-04 12:01:20,395 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-04 12:01:20,395 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16584.0, 'new_value': 23460.0}, {'field': 'total_amount', 'old_value': 16584.0, 'new_value': 23460.0}, {'field': 'order_count', 'old_value': 1382, 'new_value': 1955}]
2025-05-04 12:01:20,395 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-04 12:01:20,879 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-04 12:01:20,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6000.0, 'new_value': 9000.0}, {'field': 'total_amount', 'old_value': 6000.0, 'new_value': 9000.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 12:01:20,879 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-04 12:01:21,348 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-04 12:01:21,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5375.9, 'new_value': 9123.3}, {'field': 'total_amount', 'old_value': 5375.9, 'new_value': 9123.3}, {'field': 'order_count', 'old_value': 52, 'new_value': 92}]
2025-05-04 12:01:21,348 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-04 12:01:21,848 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-04 12:01:21,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2038000.0, 'new_value': 2562200.0}, {'field': 'total_amount', 'old_value': 2038000.0, 'new_value': 2562200.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 12:01:21,848 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-04 12:01:22,254 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-04 12:01:22,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8648.9, 'new_value': 11760.4}, {'field': 'total_amount', 'old_value': 8648.9, 'new_value': 11760.4}, {'field': 'order_count', 'old_value': 12, 'new_value': 18}]
2025-05-04 12:01:22,254 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-04 12:01:22,707 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-04 12:01:22,707 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4596.05, 'new_value': 6664.8}, {'field': 'total_amount', 'old_value': 4596.05, 'new_value': 6664.8}, {'field': 'order_count', 'old_value': 177, 'new_value': 263}]
2025-05-04 12:01:22,707 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-04 12:01:23,144 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-04 12:01:23,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39046.0, 'new_value': 51196.0}, {'field': 'total_amount', 'old_value': 39046.0, 'new_value': 51196.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-05-04 12:01:23,144 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-04 12:01:23,566 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-04 12:01:23,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13018.81, 'new_value': 26413.81}, {'field': 'total_amount', 'old_value': 30207.41, 'new_value': 43602.41}, {'field': 'order_count', 'old_value': 715, 'new_value': 1020}]
2025-05-04 12:01:23,566 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-04 12:01:24,035 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-04 12:01:24,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64044.98, 'new_value': 83691.59}, {'field': 'total_amount', 'old_value': 64044.98, 'new_value': 83691.59}, {'field': 'order_count', 'old_value': 108, 'new_value': 151}]
2025-05-04 12:01:24,035 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGO
2025-05-04 12:01:24,473 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGO
2025-05-04 12:01:24,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3888.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3888.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-04 12:01:24,473 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-04 12:01:24,894 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-04 12:01:24,894 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3193.0, 'new_value': 5044.0}, {'field': 'total_amount', 'old_value': 3193.0, 'new_value': 5044.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 56}]
2025-05-04 12:01:24,894 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-04 12:01:25,348 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-04 12:01:25,348 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4785.7, 'new_value': 6760.22}, {'field': 'offline_amount', 'old_value': 3818.73, 'new_value': 5238.09}, {'field': 'total_amount', 'old_value': 8604.43, 'new_value': 11998.31}, {'field': 'order_count', 'old_value': 451, 'new_value': 636}]
2025-05-04 12:01:25,348 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-04 12:01:25,801 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-04 12:01:25,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7754.0, 'new_value': 8313.0}, {'field': 'total_amount', 'old_value': 7754.0, 'new_value': 8313.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-04 12:01:25,801 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-04 12:01:26,191 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-04 12:01:26,191 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2004.68, 'new_value': 2326.88}, {'field': 'total_amount', 'old_value': 2004.68, 'new_value': 2326.88}, {'field': 'order_count', 'old_value': 31, 'new_value': 35}]
2025-05-04 12:01:26,191 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-04 12:01:26,629 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-04 12:01:26,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49708.96, 'new_value': 74877.77}, {'field': 'total_amount', 'old_value': 49708.96, 'new_value': 74877.77}, {'field': 'order_count', 'old_value': 183, 'new_value': 267}]
2025-05-04 12:01:26,629 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-04 12:01:27,144 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-04 12:01:27,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24776.4, 'new_value': 34150.7}, {'field': 'total_amount', 'old_value': 24776.4, 'new_value': 34150.7}, {'field': 'order_count', 'old_value': 584, 'new_value': 851}]
2025-05-04 12:01:27,144 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-04 12:01:27,629 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-04 12:01:27,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2844.85, 'new_value': 4221.72}, {'field': 'total_amount', 'old_value': 2844.85, 'new_value': 4221.72}, {'field': 'order_count', 'old_value': 370, 'new_value': 537}]
2025-05-04 12:01:27,629 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-04 12:01:28,066 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-04 12:01:28,066 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2105.42, 'new_value': 3121.74}, {'field': 'offline_amount', 'old_value': 3327.8, 'new_value': 4752.8}, {'field': 'total_amount', 'old_value': 5433.22, 'new_value': 7874.54}, {'field': 'order_count', 'old_value': 207, 'new_value': 302}]
2025-05-04 12:01:28,066 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-04 12:01:28,473 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-04 12:01:28,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3948.0, 'new_value': 6214.0}, {'field': 'total_amount', 'old_value': 3948.0, 'new_value': 6214.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 13}]
2025-05-04 12:01:28,473 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-04 12:01:29,004 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-04 12:01:29,004 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 161852.08, 'new_value': 233237.08}, {'field': 'offline_amount', 'old_value': 2174.0, 'new_value': 16223.45}, {'field': 'total_amount', 'old_value': 164026.08, 'new_value': 249460.53}, {'field': 'order_count', 'old_value': 520, 'new_value': 783}]
2025-05-04 12:01:29,004 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-04 12:01:29,504 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-04 12:01:29,504 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1579.0, 'new_value': 3244.0}, {'field': 'total_amount', 'old_value': 2955.0, 'new_value': 4620.0}, {'field': 'order_count', 'old_value': 318, 'new_value': 486}]
2025-05-04 12:01:29,504 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-04 12:01:29,988 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-04 12:01:29,988 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15760.0, 'new_value': 17721.0}, {'field': 'total_amount', 'old_value': 15760.0, 'new_value': 17721.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-05-04 12:01:29,988 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-04 12:01:30,426 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-04 12:01:30,426 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 875.1, 'new_value': 5014.3}, {'field': 'offline_amount', 'old_value': 4375.0, 'new_value': 5635.0}, {'field': 'total_amount', 'old_value': 5250.1, 'new_value': 10649.3}, {'field': 'order_count', 'old_value': 20, 'new_value': 30}]
2025-05-04 12:01:30,426 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-04 12:01:30,848 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-04 12:01:30,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48402.0, 'new_value': 74820.4}, {'field': 'total_amount', 'old_value': 48402.0, 'new_value': 74820.4}, {'field': 'order_count', 'old_value': 242, 'new_value': 367}]
2025-05-04 12:01:30,848 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-04 12:01:31,301 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-04 12:01:31,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29906.0, 'new_value': 54215.0}, {'field': 'total_amount', 'old_value': 29906.0, 'new_value': 54215.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 28}]
2025-05-04 12:01:31,301 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-04 12:01:31,801 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-04 12:01:31,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61832.34, 'new_value': 86870.45}, {'field': 'total_amount', 'old_value': 61832.34, 'new_value': 86870.45}, {'field': 'order_count', 'old_value': 126, 'new_value': 176}]
2025-05-04 12:01:31,801 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-04 12:01:32,269 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-04 12:01:32,269 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11290.0, 'new_value': 18150.0}, {'field': 'total_amount', 'old_value': 11290.0, 'new_value': 18150.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 12:01:32,269 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-04 12:01:32,816 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-04 12:01:32,816 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2883.77, 'new_value': 3116.84}, {'field': 'offline_amount', 'old_value': 5033.52, 'new_value': 9701.94}, {'field': 'total_amount', 'old_value': 7917.29, 'new_value': 12818.78}, {'field': 'order_count', 'old_value': 29, 'new_value': 49}]
2025-05-04 12:01:32,816 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-04 12:01:33,269 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-04 12:01:33,269 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39481.0, 'new_value': 44710.0}, {'field': 'total_amount', 'old_value': 39481.0, 'new_value': 44710.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-05-04 12:01:33,269 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-04 12:01:33,723 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-04 12:01:33,723 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48960.0, 'new_value': 75618.0}, {'field': 'offline_amount', 'old_value': 2760.0, 'new_value': 3072.0}, {'field': 'total_amount', 'old_value': 51720.0, 'new_value': 78690.0}, {'field': 'order_count', 'old_value': 226, 'new_value': 427}]
2025-05-04 12:01:33,723 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-04 12:01:34,144 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-04 12:01:34,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2913.0, 'new_value': 3611.0}, {'field': 'total_amount', 'old_value': 2913.0, 'new_value': 3611.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 21}]
2025-05-04 12:01:34,144 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-04 12:01:34,566 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-04 12:01:34,566 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1117.0, 'new_value': 1937.1}, {'field': 'total_amount', 'old_value': 1117.0, 'new_value': 1937.1}, {'field': 'order_count', 'old_value': 47, 'new_value': 74}]
2025-05-04 12:01:34,566 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-04 12:01:35,019 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-04 12:01:35,019 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 972.0, 'new_value': 1809.0}, {'field': 'offline_amount', 'old_value': 5503.8, 'new_value': 8012.0}, {'field': 'total_amount', 'old_value': 6475.8, 'new_value': 9821.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 94}]
2025-05-04 12:01:35,019 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-04 12:01:35,488 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-04 12:01:35,504 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1043.0, 'new_value': 1728.0}, {'field': 'total_amount', 'old_value': 4921.0, 'new_value': 5606.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 9}]
2025-05-04 12:01:35,504 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-04 12:01:35,957 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-04 12:01:35,957 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1840.5, 'new_value': 2802.91}, {'field': 'total_amount', 'old_value': 1840.5, 'new_value': 2802.91}, {'field': 'order_count', 'old_value': 62, 'new_value': 90}]
2025-05-04 12:01:35,957 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-04 12:01:36,394 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-04 12:01:36,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79882.62, 'new_value': 118474.44}, {'field': 'total_amount', 'old_value': 79882.62, 'new_value': 118474.44}, {'field': 'order_count', 'old_value': 517, 'new_value': 752}]
2025-05-04 12:01:36,394 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-04 12:01:36,910 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-04 12:01:36,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143027.0, 'new_value': 193493.0}, {'field': 'total_amount', 'old_value': 143027.0, 'new_value': 193493.0}, {'field': 'order_count', 'old_value': 658, 'new_value': 904}]
2025-05-04 12:01:36,910 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-04 12:01:37,410 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-04 12:01:37,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17567.86, 'new_value': 25874.86}, {'field': 'total_amount', 'old_value': 17567.86, 'new_value': 25874.86}, {'field': 'order_count', 'old_value': 95, 'new_value': 139}]
2025-05-04 12:01:37,410 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-04 12:01:37,832 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-04 12:01:37,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21330.0, 'new_value': 23210.0}, {'field': 'total_amount', 'old_value': 21330.0, 'new_value': 23210.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-04 12:01:37,832 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-04 12:01:38,301 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-04 12:01:38,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16061.0, 'new_value': 30402.0}, {'field': 'total_amount', 'old_value': 31265.0, 'new_value': 45606.0}, {'field': 'order_count', 'old_value': 736, 'new_value': 1086}]
2025-05-04 12:01:38,301 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMAE
2025-05-04 12:01:38,785 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMAE
2025-05-04 12:01:38,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-04 12:01:38,785 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-04 12:01:39,285 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-04 12:01:39,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21072.0, 'new_value': 27150.0}, {'field': 'total_amount', 'old_value': 21072.0, 'new_value': 27150.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 115}]
2025-05-04 12:01:39,285 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-04 12:01:39,722 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-04 12:01:39,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4100.0, 'new_value': 4299.0}, {'field': 'total_amount', 'old_value': 4100.0, 'new_value': 4299.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 12:01:39,722 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-04 12:01:40,160 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-04 12:01:40,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25000.0, 'new_value': 34800.0}, {'field': 'total_amount', 'old_value': 25000.0, 'new_value': 34800.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-04 12:01:40,160 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-04 12:01:40,566 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-04 12:01:40,566 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6564.82, 'new_value': 9196.4}, {'field': 'offline_amount', 'old_value': 13324.1, 'new_value': 18557.15}, {'field': 'total_amount', 'old_value': 19888.92, 'new_value': 27753.55}, {'field': 'order_count', 'old_value': 749, 'new_value': 1063}]
2025-05-04 12:01:40,566 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-04 12:01:41,097 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-04 12:01:41,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6068.0, 'new_value': 7999.0}, {'field': 'total_amount', 'old_value': 6068.0, 'new_value': 7999.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 20}]
2025-05-04 12:01:41,097 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-04 12:01:41,535 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-04 12:01:41,535 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 833.0, 'new_value': 1387.92}, {'field': 'total_amount', 'old_value': 833.0, 'new_value': 1387.92}, {'field': 'order_count', 'old_value': 24, 'new_value': 29}]
2025-05-04 12:01:41,535 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-04 12:01:41,941 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-04 12:01:41,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16380.0, 'new_value': 23960.0}, {'field': 'total_amount', 'old_value': 16380.0, 'new_value': 23960.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-05-04 12:01:41,941 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-04 12:01:42,332 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-04 12:01:42,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194457.9, 'new_value': 249910.8}, {'field': 'total_amount', 'old_value': 194457.9, 'new_value': 249910.8}, {'field': 'order_count', 'old_value': 216, 'new_value': 303}]
2025-05-04 12:01:42,332 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-04 12:01:42,801 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-04 12:01:42,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1837.0, 'new_value': 2417.0}, {'field': 'total_amount', 'old_value': 1837.0, 'new_value': 2417.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 12}]
2025-05-04 12:01:42,801 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-04 12:01:43,269 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-04 12:01:43,269 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4937.0, 'new_value': 7884.0}, {'field': 'offline_amount', 'old_value': 4906.0, 'new_value': 7645.0}, {'field': 'total_amount', 'old_value': 9843.0, 'new_value': 15529.0}, {'field': 'order_count', 'old_value': 181, 'new_value': 281}]
2025-05-04 12:01:43,269 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-04 12:01:43,754 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-04 12:01:43,754 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 982.0, 'new_value': 2326.0}, {'field': 'offline_amount', 'old_value': 24781.0, 'new_value': 36427.0}, {'field': 'total_amount', 'old_value': 25763.0, 'new_value': 38753.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 185}]
2025-05-04 12:01:43,754 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-04 12:01:44,222 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-04 12:01:44,222 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38000.0, 'new_value': 40800.0}, {'field': 'total_amount', 'old_value': 38000.0, 'new_value': 40800.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 12:01:44,222 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-04 12:01:44,769 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-04 12:01:44,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6089.5, 'new_value': 8400.5}, {'field': 'total_amount', 'old_value': 6089.5, 'new_value': 8400.5}, {'field': 'order_count', 'old_value': 23, 'new_value': 35}]
2025-05-04 12:01:44,769 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-04 12:01:45,160 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-04 12:01:45,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84963.83, 'new_value': 127708.53}, {'field': 'total_amount', 'old_value': 84963.83, 'new_value': 127708.53}, {'field': 'order_count', 'old_value': 715, 'new_value': 1125}]
2025-05-04 12:01:45,160 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-04 12:01:45,613 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-04 12:01:45,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34217.0, 'new_value': 60086.0}, {'field': 'total_amount', 'old_value': 34217.0, 'new_value': 60086.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 57}]
2025-05-04 12:01:45,613 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-04 12:01:46,051 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-04 12:01:46,051 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32649.81, 'new_value': 47285.55}, {'field': 'offline_amount', 'old_value': 24091.03, 'new_value': 33287.91}, {'field': 'total_amount', 'old_value': 56740.84, 'new_value': 80573.46}, {'field': 'order_count', 'old_value': 294, 'new_value': 459}]
2025-05-04 12:01:46,051 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-04 12:01:46,535 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-04 12:01:46,535 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24415.47, 'new_value': 32517.89}, {'field': 'offline_amount', 'old_value': 1785.8, 'new_value': 2820.0}, {'field': 'total_amount', 'old_value': 26201.27, 'new_value': 35337.89}, {'field': 'order_count', 'old_value': 952, 'new_value': 1321}]
2025-05-04 12:01:46,535 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-04 12:01:47,035 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-04 12:01:47,035 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8257.0, 'new_value': 11316.0}, {'field': 'offline_amount', 'old_value': 91143.0, 'new_value': 137184.0}, {'field': 'total_amount', 'old_value': 99400.0, 'new_value': 148500.0}, {'field': 'order_count', 'old_value': 2080, 'new_value': 2952}]
2025-05-04 12:01:47,035 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-04 12:01:47,488 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-04 12:01:47,488 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48305.71, 'new_value': 68921.14}, {'field': 'total_amount', 'old_value': 48305.71, 'new_value': 68921.14}, {'field': 'order_count', 'old_value': 166, 'new_value': 238}]
2025-05-04 12:01:47,488 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-04 12:01:47,926 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-04 12:01:47,926 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20662.0, 'new_value': 27280.0}, {'field': 'total_amount', 'old_value': 23344.0, 'new_value': 29962.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 19}]
2025-05-04 12:01:47,926 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-04 12:01:48,441 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-04 12:01:48,441 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1926.5, 'new_value': 2889.64}, {'field': 'offline_amount', 'old_value': 3659.5, 'new_value': 4511.5}, {'field': 'total_amount', 'old_value': 5586.0, 'new_value': 7401.14}, {'field': 'order_count', 'old_value': 294, 'new_value': 379}]
2025-05-04 12:01:48,441 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-04 12:01:48,894 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-04 12:01:48,894 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3559.3, 'new_value': 4833.4}, {'field': 'offline_amount', 'old_value': 10303.1, 'new_value': 15271.8}, {'field': 'total_amount', 'old_value': 13862.4, 'new_value': 20105.2}, {'field': 'order_count', 'old_value': 169, 'new_value': 234}]
2025-05-04 12:01:48,894 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-04 12:01:49,363 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-04 12:01:49,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7170.0, 'new_value': 11764.0}, {'field': 'total_amount', 'old_value': 7170.0, 'new_value': 11764.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 54}]
2025-05-04 12:01:49,363 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-04 12:01:49,832 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-04 12:01:49,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21645.66, 'new_value': 33365.49}, {'field': 'total_amount', 'old_value': 21645.66, 'new_value': 33365.49}, {'field': 'order_count', 'old_value': 963, 'new_value': 1535}]
2025-05-04 12:01:49,832 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-04 12:01:50,347 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-04 12:01:50,347 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19500.9, 'new_value': 27051.5}, {'field': 'total_amount', 'old_value': 19500.9, 'new_value': 27051.5}, {'field': 'order_count', 'old_value': 87, 'new_value': 122}]
2025-05-04 12:01:50,347 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-04 12:01:50,816 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-04 12:01:50,816 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31585.0, 'new_value': 44274.4}, {'field': 'total_amount', 'old_value': 31585.0, 'new_value': 44274.4}, {'field': 'order_count', 'old_value': 867, 'new_value': 1211}]
2025-05-04 12:01:50,816 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-04 12:01:51,332 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-04 12:01:51,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1623.0, 'new_value': 2486.0}, {'field': 'total_amount', 'old_value': 3138.0, 'new_value': 4001.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 17}]
2025-05-04 12:01:51,332 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-04 12:01:51,754 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-04 12:01:51,754 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14745.86, 'new_value': 21967.52}, {'field': 'offline_amount', 'old_value': 40699.5, 'new_value': 58354.78}, {'field': 'total_amount', 'old_value': 55445.36, 'new_value': 80322.3}, {'field': 'order_count', 'old_value': 376, 'new_value': 562}]
2025-05-04 12:01:51,754 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-04 12:01:52,207 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-04 12:01:52,207 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13499.3, 'new_value': 20071.2}, {'field': 'total_amount', 'old_value': 13499.3, 'new_value': 20071.2}, {'field': 'order_count', 'old_value': 58, 'new_value': 93}]
2025-05-04 12:01:52,207 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-04 12:01:52,754 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-04 12:01:52,754 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12888.77, 'new_value': 17834.81}, {'field': 'total_amount', 'old_value': 12888.77, 'new_value': 17834.81}, {'field': 'order_count', 'old_value': 29, 'new_value': 55}]
2025-05-04 12:01:52,754 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-04 12:01:53,254 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-04 12:01:53,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1461.0, 'new_value': 1818.0}, {'field': 'offline_amount', 'old_value': 255.0, 'new_value': 955.0}, {'field': 'total_amount', 'old_value': 1716.0, 'new_value': 2773.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 29}]
2025-05-04 12:01:53,254 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-04 12:01:53,707 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-04 12:01:53,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3266.29, 'new_value': 5380.1}, {'field': 'offline_amount', 'old_value': 2494.25, 'new_value': 3836.64}, {'field': 'total_amount', 'old_value': 5760.54, 'new_value': 9216.74}, {'field': 'order_count', 'old_value': 318, 'new_value': 508}]
2025-05-04 12:01:53,707 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-04 12:01:54,160 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-04 12:01:54,160 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5635.68, 'new_value': 8185.19}, {'field': 'offline_amount', 'old_value': 8376.97, 'new_value': 12525.63}, {'field': 'total_amount', 'old_value': 14012.65, 'new_value': 20710.82}, {'field': 'order_count', 'old_value': 319, 'new_value': 476}]
2025-05-04 12:01:54,160 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-04 12:01:54,613 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-04 12:01:54,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7499.0, 'new_value': 30995.0}, {'field': 'total_amount', 'old_value': 7499.0, 'new_value': 30995.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 5}]
2025-05-04 12:01:54,613 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-04 12:01:55,129 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-04 12:01:55,129 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129436.0, 'new_value': 171668.0}, {'field': 'total_amount', 'old_value': 129436.0, 'new_value': 171668.0}, {'field': 'order_count', 'old_value': 148, 'new_value': 199}]
2025-05-04 12:01:55,129 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-04 12:01:55,582 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-04 12:01:55,582 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2252.7, 'new_value': 3725.3}, {'field': 'offline_amount', 'old_value': 42324.0, 'new_value': 54394.0}, {'field': 'total_amount', 'old_value': 44576.7, 'new_value': 58119.3}, {'field': 'order_count', 'old_value': 73, 'new_value': 101}]
2025-05-04 12:01:55,582 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-04 12:01:56,035 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-04 12:01:56,035 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4253.6, 'new_value': 6788.2}, {'field': 'offline_amount', 'old_value': 5413.0, 'new_value': 10121.0}, {'field': 'total_amount', 'old_value': 9666.6, 'new_value': 16909.2}, {'field': 'order_count', 'old_value': 156, 'new_value': 243}]
2025-05-04 12:01:56,035 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-04 12:01:56,410 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-04 12:01:56,410 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13551.0, 'new_value': 18790.0}, {'field': 'offline_amount', 'old_value': 9712.0, 'new_value': 15344.0}, {'field': 'total_amount', 'old_value': 23263.0, 'new_value': 34134.0}, {'field': 'order_count', 'old_value': 294, 'new_value': 407}]
2025-05-04 12:01:56,410 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-04 12:01:56,879 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-04 12:01:56,879 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1505.8, 'new_value': 1990.7}, {'field': 'offline_amount', 'old_value': 1661.84, 'new_value': 2691.84}, {'field': 'total_amount', 'old_value': 3167.64, 'new_value': 4682.54}, {'field': 'order_count', 'old_value': 40, 'new_value': 60}]
2025-05-04 12:01:56,879 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-04 12:01:57,332 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-04 12:01:57,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2858.0, 'new_value': 15884.0}, {'field': 'total_amount', 'old_value': 5497.2, 'new_value': 18523.2}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-05-04 12:01:57,332 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-04 12:01:57,769 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-04 12:01:57,769 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6301.09, 'new_value': 7939.97}, {'field': 'total_amount', 'old_value': 6301.09, 'new_value': 7939.97}, {'field': 'order_count', 'old_value': 30, 'new_value': 37}]
2025-05-04 12:01:57,769 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-04 12:01:58,191 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-04 12:01:58,191 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2064.0, 'new_value': 2818.0}, {'field': 'offline_amount', 'old_value': 3374.0, 'new_value': 4276.0}, {'field': 'total_amount', 'old_value': 5438.0, 'new_value': 7094.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 31}]
2025-05-04 12:01:58,191 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-04 12:01:58,613 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-04 12:01:58,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27551.4, 'new_value': 37552.4}, {'field': 'total_amount', 'old_value': 27551.4, 'new_value': 37552.4}, {'field': 'order_count', 'old_value': 155, 'new_value': 211}]
2025-05-04 12:01:58,613 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-04 12:01:59,050 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-04 12:01:59,050 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 614.0, 'new_value': 1010.0}, {'field': 'offline_amount', 'old_value': 1436.0, 'new_value': 2435.0}, {'field': 'total_amount', 'old_value': 2050.0, 'new_value': 3445.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 22}]
2025-05-04 12:01:59,050 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-04 12:01:59,472 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-04 12:01:59,488 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 471.0, 'new_value': 1363.0}, {'field': 'offline_amount', 'old_value': 4547.52, 'new_value': 5826.07}, {'field': 'total_amount', 'old_value': 5018.52, 'new_value': 7189.07}, {'field': 'order_count', 'old_value': 47, 'new_value': 66}]
2025-05-04 12:01:59,488 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-04 12:01:59,910 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-04 12:01:59,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14564.0, 'new_value': 24236.25}, {'field': 'total_amount', 'old_value': 14564.0, 'new_value': 24236.25}, {'field': 'order_count', 'old_value': 68, 'new_value': 71}]
2025-05-04 12:01:59,910 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-04 12:02:00,347 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-04 12:02:00,347 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9273.0, 'new_value': 12748.0}, {'field': 'offline_amount', 'old_value': 22504.0, 'new_value': 33569.0}, {'field': 'total_amount', 'old_value': 31777.0, 'new_value': 46317.0}, {'field': 'order_count', 'old_value': 143, 'new_value': 217}]
2025-05-04 12:02:00,347 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-04 12:02:00,847 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-04 12:02:00,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185589.0, 'new_value': 266544.0}, {'field': 'total_amount', 'old_value': 185589.0, 'new_value': 266544.0}, {'field': 'order_count', 'old_value': 759, 'new_value': 1079}]
2025-05-04 12:02:00,847 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-04 12:02:01,285 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-04 12:02:01,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1151053.0, 'new_value': 2159404.0}, {'field': 'total_amount', 'old_value': 1151053.0, 'new_value': 2159404.0}, {'field': 'order_count', 'old_value': 3387, 'new_value': 6226}]
2025-05-04 12:02:01,285 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMOC
2025-05-04 12:02:01,722 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMOC
2025-05-04 12:02:01,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17118.0, 'new_value': 32174.0}, {'field': 'total_amount', 'old_value': 17118.0, 'new_value': 32174.0}, {'field': 'order_count', 'old_value': 350, 'new_value': 650}]
2025-05-04 12:02:01,738 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-04 12:02:02,175 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-04 12:02:02,175 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 524337.89, 'new_value': 767744.84}, {'field': 'total_amount', 'old_value': 524337.89, 'new_value': 767744.84}, {'field': 'order_count', 'old_value': 814, 'new_value': 1216}]
2025-05-04 12:02:02,191 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-04 12:02:02,597 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-04 12:02:02,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7285.36, 'new_value': 13046.22}, {'field': 'total_amount', 'old_value': 7285.36, 'new_value': 13046.22}, {'field': 'order_count', 'old_value': 496, 'new_value': 894}]
2025-05-04 12:02:02,597 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-04 12:02:03,097 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-04 12:02:03,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11938.0, 'new_value': 23960.0}, {'field': 'total_amount', 'old_value': 11938.0, 'new_value': 23960.0}, {'field': 'order_count', 'old_value': 289, 'new_value': 529}]
2025-05-04 12:02:03,097 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-04 12:02:03,550 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-04 12:02:03,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26550.0, 'new_value': 42088.0}, {'field': 'total_amount', 'old_value': 26550.0, 'new_value': 42088.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 85}]
2025-05-04 12:02:03,550 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-04 12:02:04,035 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-04 12:02:04,035 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26634.41, 'new_value': 37715.92}, {'field': 'offline_amount', 'old_value': 34123.95, 'new_value': 46238.35}, {'field': 'total_amount', 'old_value': 60758.36, 'new_value': 83954.27}, {'field': 'order_count', 'old_value': 1938, 'new_value': 2849}]
2025-05-04 12:02:04,035 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-04 12:02:04,535 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-04 12:02:04,535 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29821.0, 'new_value': 48879.7}, {'field': 'total_amount', 'old_value': 29821.0, 'new_value': 48879.7}]
2025-05-04 12:02:04,535 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-04 12:02:04,957 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-04 12:02:04,957 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66755.0, 'new_value': 95046.0}, {'field': 'total_amount', 'old_value': 66755.0, 'new_value': 95046.0}, {'field': 'order_count', 'old_value': 1476, 'new_value': 2082}]
2025-05-04 12:02:04,957 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-04 12:02:05,410 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-04 12:02:05,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4617.4, 'new_value': 6511.0}, {'field': 'total_amount', 'old_value': 4617.4, 'new_value': 6511.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 36}]
2025-05-04 12:02:05,410 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-04 12:02:05,863 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-04 12:02:05,863 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19763.0, 'new_value': 28009.0}, {'field': 'offline_amount', 'old_value': 10910.0, 'new_value': 17541.0}, {'field': 'total_amount', 'old_value': 30673.0, 'new_value': 45550.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 145}]
2025-05-04 12:02:05,863 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-04 12:02:06,316 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-04 12:02:06,316 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7526.0, 'new_value': 11200.0}, {'field': 'total_amount', 'old_value': 7526.0, 'new_value': 11200.0}, {'field': 'order_count', 'old_value': 511, 'new_value': 761}]
2025-05-04 12:02:06,316 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-04 12:02:06,754 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-04 12:02:06,754 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5483.0, 'new_value': 11098.0}, {'field': 'total_amount', 'old_value': 5483.0, 'new_value': 11098.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 6}]
2025-05-04 12:02:06,754 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-04 12:02:07,207 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-04 12:02:07,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14943.6, 'new_value': 21101.4}, {'field': 'total_amount', 'old_value': 14963.4, 'new_value': 21121.2}, {'field': 'order_count', 'old_value': 243, 'new_value': 351}]
2025-05-04 12:02:07,207 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-04 12:02:07,816 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-04 12:02:07,816 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1894.1, 'new_value': 2669.6}, {'field': 'offline_amount', 'old_value': 6487.7, 'new_value': 9640.6}, {'field': 'total_amount', 'old_value': 8381.8, 'new_value': 12310.2}, {'field': 'order_count', 'old_value': 304, 'new_value': 430}]
2025-05-04 12:02:07,816 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-04 12:02:08,285 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-04 12:02:08,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1280.33, 'new_value': 2651.66}, {'field': 'total_amount', 'old_value': 1280.33, 'new_value': 2651.66}, {'field': 'order_count', 'old_value': 5, 'new_value': 10}]
2025-05-04 12:02:08,285 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-04 12:02:08,738 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-04 12:02:08,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11041.25, 'new_value': 16272.83}, {'field': 'total_amount', 'old_value': 11041.25, 'new_value': 16272.83}, {'field': 'order_count', 'old_value': 260, 'new_value': 401}]
2025-05-04 12:02:08,738 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-04 12:02:09,144 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-04 12:02:09,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23599.0, 'new_value': 34150.0}, {'field': 'total_amount', 'old_value': 23599.0, 'new_value': 34150.0}, {'field': 'order_count', 'old_value': 537, 'new_value': 775}]
2025-05-04 12:02:09,144 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-04 12:02:09,566 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-04 12:02:09,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2819.0, 'new_value': 3449.0}, {'field': 'total_amount', 'old_value': 2819.0, 'new_value': 3449.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-04 12:02:09,582 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-04 12:02:10,004 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-04 12:02:10,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32609.0, 'new_value': 32935.0}, {'field': 'total_amount', 'old_value': 32609.0, 'new_value': 32935.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-04 12:02:10,004 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-04 12:02:10,425 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-04 12:02:10,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124753.86, 'new_value': 179596.69}, {'field': 'total_amount', 'old_value': 124753.86, 'new_value': 179596.69}, {'field': 'order_count', 'old_value': 376, 'new_value': 548}]
2025-05-04 12:02:10,425 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-04 12:02:10,847 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-04 12:02:10,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9746.0, 'new_value': 13434.0}, {'field': 'total_amount', 'old_value': 9746.0, 'new_value': 13434.0}, {'field': 'order_count', 'old_value': 327, 'new_value': 460}]
2025-05-04 12:02:10,847 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-04 12:02:11,300 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-04 12:02:11,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80240.4, 'new_value': 119759.7}, {'field': 'total_amount', 'old_value': 80240.4, 'new_value': 119759.7}, {'field': 'order_count', 'old_value': 272, 'new_value': 412}]
2025-05-04 12:02:11,300 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-04 12:02:11,769 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-04 12:02:11,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34922.78, 'new_value': 52999.97}, {'field': 'total_amount', 'old_value': 34922.78, 'new_value': 52999.97}, {'field': 'order_count', 'old_value': 98, 'new_value': 144}]
2025-05-04 12:02:11,769 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-04 12:02:12,285 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-04 12:02:12,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2000.0, 'new_value': 6320.0}, {'field': 'total_amount', 'old_value': 2000.0, 'new_value': 6320.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 375}]
2025-05-04 12:02:12,285 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-04 12:02:12,722 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-04 12:02:12,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8428.0, 'new_value': 11361.0}, {'field': 'total_amount', 'old_value': 11054.0, 'new_value': 13987.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-04 12:02:12,722 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFG
2025-05-04 12:02:13,191 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFG
2025-05-04 12:02:13,191 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 2136.0}, {'field': 'offline_amount', 'old_value': 3450.0, 'new_value': 6002.0}, {'field': 'total_amount', 'old_value': 3450.0, 'new_value': 8138.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 110}]
2025-05-04 12:02:13,191 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-04 12:02:13,660 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-04 12:02:13,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8235.0, 'new_value': 11457.0}, {'field': 'total_amount', 'old_value': 8235.0, 'new_value': 11457.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 12:02:13,660 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-04 12:02:14,113 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-04 12:02:14,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11212.0, 'new_value': 25442.0}, {'field': 'total_amount', 'old_value': 11212.0, 'new_value': 25442.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 87}]
2025-05-04 12:02:14,113 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-04 12:02:14,597 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-04 12:02:14,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1340.0, 'new_value': 5640.0}, {'field': 'total_amount', 'old_value': 1340.0, 'new_value': 5640.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 12}]
2025-05-04 12:02:14,597 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-04 12:02:15,066 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-04 12:02:15,066 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2783.0, 'new_value': 4298.0}, {'field': 'total_amount', 'old_value': 2783.0, 'new_value': 4298.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 81}]
2025-05-04 12:02:15,066 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-04 12:02:15,488 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-04 12:02:15,488 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6225.0, 'new_value': 9048.0}, {'field': 'total_amount', 'old_value': 6225.0, 'new_value': 9048.0}, {'field': 'order_count', 'old_value': 559, 'new_value': 820}]
2025-05-04 12:02:15,488 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-04 12:02:16,004 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-04 12:02:16,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15050.0, 'new_value': 21916.0}, {'field': 'total_amount', 'old_value': 15050.0, 'new_value': 21916.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 161}]
2025-05-04 12:02:16,004 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-04 12:02:16,425 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-04 12:02:16,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23298.16, 'new_value': 33713.01}, {'field': 'total_amount', 'old_value': 23298.16, 'new_value': 33713.01}, {'field': 'order_count', 'old_value': 153, 'new_value': 204}]
2025-05-04 12:02:16,425 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-04 12:02:16,879 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-04 12:02:16,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9398.0, 'new_value': 31392.0}, {'field': 'total_amount', 'old_value': 9398.0, 'new_value': 31392.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 30}]
2025-05-04 12:02:16,879 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRG
2025-05-04 12:02:17,347 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRG
2025-05-04 12:02:17,347 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1500.0, 'new_value': 4300.0}, {'field': 'total_amount', 'old_value': 1500.0, 'new_value': 4300.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-04 12:02:17,347 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-04 12:02:17,785 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-04 12:02:17,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5280.0, 'new_value': 13780.0}, {'field': 'total_amount', 'old_value': 5280.0, 'new_value': 13780.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 12:02:17,785 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-04 12:02:18,238 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-04 12:02:18,238 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3040.0, 'new_value': 4670.0}, {'field': 'offline_amount', 'old_value': 4266.0, 'new_value': 4346.0}, {'field': 'total_amount', 'old_value': 7306.0, 'new_value': 9016.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 19}]
2025-05-04 12:02:18,238 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-04 12:02:18,754 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-04 12:02:18,754 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4829.85, 'new_value': 10469.35}, {'field': 'total_amount', 'old_value': 4829.85, 'new_value': 10469.35}, {'field': 'order_count', 'old_value': 76, 'new_value': 130}]
2025-05-04 12:02:18,754 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-04 12:02:19,269 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-04 12:02:19,269 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20486.38, 'new_value': 25419.48}, {'field': 'offline_amount', 'old_value': 41869.71, 'new_value': 65412.8}, {'field': 'total_amount', 'old_value': 62356.09, 'new_value': 90832.28}, {'field': 'order_count', 'old_value': 171, 'new_value': 253}]
2025-05-04 12:02:19,269 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-04 12:02:19,707 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-04 12:02:19,707 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2756.0, 'new_value': 9449.0}, {'field': 'total_amount', 'old_value': 2756.0, 'new_value': 9449.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 7}]
2025-05-04 12:02:19,707 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-04 12:02:20,144 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-04 12:02:20,144 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2395.12, 'new_value': 4269.45}, {'field': 'offline_amount', 'old_value': 27244.41, 'new_value': 50852.62}, {'field': 'total_amount', 'old_value': 29639.53, 'new_value': 53247.74}, {'field': 'order_count', 'old_value': 127, 'new_value': 235}]
2025-05-04 12:02:20,144 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-04 12:02:20,644 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-04 12:02:20,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3017.0, 'new_value': 11540.0}, {'field': 'total_amount', 'old_value': 3017.0, 'new_value': 11540.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 37}]
2025-05-04 12:02:20,644 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-04 12:02:21,160 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-04 12:02:21,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 938.48, 'new_value': 2438.48}, {'field': 'total_amount', 'old_value': 938.48, 'new_value': 2438.48}, {'field': 'order_count', 'old_value': 27, 'new_value': 87}]
2025-05-04 12:02:21,160 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-04 12:02:21,566 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-04 12:02:21,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 500.0, 'new_value': 2900.0}, {'field': 'total_amount', 'old_value': 500.0, 'new_value': 2900.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-04 12:02:21,566 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-04 12:02:22,019 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-04 12:02:22,019 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1928940.0, 'new_value': 2729947.0}, {'field': 'total_amount', 'old_value': 1928940.0, 'new_value': 2729947.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-04 12:02:22,019 - INFO - 开始批量插入 14 条新记录
2025-05-04 12:02:22,160 - INFO - 批量插入响应状态码: 200
2025-05-04 12:02:22,160 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 04 May 2025 04:00:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '684', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '23A6DC7B-DE89-7F7F-B8A3-E7A6E69B6624', 'x-acs-trace-id': '8424f7686f400b34d45649ac61189c6d', 'etag': '6WgmZBPOeXTskt/ILtjPdFQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-04 12:02:22,160 - INFO - 批量插入响应体: {'result': ['FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMDQ', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ']}
2025-05-04 12:02:22,160 - INFO - 批量插入表单数据成功，批次 1，共 14 条记录
2025-05-04 12:02:22,160 - INFO - 成功插入的数据ID: ['FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMDQ', 'FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ']
2025-05-04 12:02:25,175 - INFO - 批量插入完成，共 14 条记录
2025-05-04 12:02:25,175 - INFO - 日期 2025-05 处理完成 - 更新: 235 条，插入: 14 条，错误: 0 条
2025-05-04 12:02:25,175 - INFO - 数据同步完成！更新: 247 条，插入: 14 条，错误: 0 条
2025-05-04 12:02:25,175 - INFO - =================同步完成====================
2025-05-04 15:00:01,890 - INFO - =================使用默认全量同步=============
2025-05-04 15:00:03,078 - INFO - MySQL查询成功，共获取 3220 条记录
2025-05-04 15:00:03,078 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-04 15:00:03,109 - INFO - 开始处理日期: 2025-01
2025-05-04 15:00:03,109 - INFO - Request Parameters - Page 1:
2025-05-04 15:00:03,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:03,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:04,406 - INFO - Response - Page 1:
2025-05-04 15:00:04,609 - INFO - 第 1 页获取到 100 条记录
2025-05-04 15:00:04,609 - INFO - Request Parameters - Page 2:
2025-05-04 15:00:04,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:04,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:05,359 - INFO - Response - Page 2:
2025-05-04 15:00:05,562 - INFO - 第 2 页获取到 100 条记录
2025-05-04 15:00:05,562 - INFO - Request Parameters - Page 3:
2025-05-04 15:00:05,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:05,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:06,140 - INFO - Response - Page 3:
2025-05-04 15:00:06,343 - INFO - 第 3 页获取到 100 条记录
2025-05-04 15:00:06,343 - INFO - Request Parameters - Page 4:
2025-05-04 15:00:06,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:06,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:06,843 - INFO - Response - Page 4:
2025-05-04 15:00:07,046 - INFO - 第 4 页获取到 100 条记录
2025-05-04 15:00:07,046 - INFO - Request Parameters - Page 5:
2025-05-04 15:00:07,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:07,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:07,546 - INFO - Response - Page 5:
2025-05-04 15:00:07,749 - INFO - 第 5 页获取到 100 条记录
2025-05-04 15:00:07,749 - INFO - Request Parameters - Page 6:
2025-05-04 15:00:07,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:07,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:08,234 - INFO - Response - Page 6:
2025-05-04 15:00:08,437 - INFO - 第 6 页获取到 100 条记录
2025-05-04 15:00:08,437 - INFO - Request Parameters - Page 7:
2025-05-04 15:00:08,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:08,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:08,890 - INFO - Response - Page 7:
2025-05-04 15:00:09,093 - INFO - 第 7 页获取到 82 条记录
2025-05-04 15:00:09,093 - INFO - 查询完成，共获取到 682 条记录
2025-05-04 15:00:09,093 - INFO - 获取到 682 条表单数据
2025-05-04 15:00:09,093 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-04 15:00:09,109 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 15:00:09,109 - INFO - 开始处理日期: 2025-02
2025-05-04 15:00:09,109 - INFO - Request Parameters - Page 1:
2025-05-04 15:00:09,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:09,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:09,578 - INFO - Response - Page 1:
2025-05-04 15:00:09,781 - INFO - 第 1 页获取到 100 条记录
2025-05-04 15:00:09,781 - INFO - Request Parameters - Page 2:
2025-05-04 15:00:09,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:09,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:10,343 - INFO - Response - Page 2:
2025-05-04 15:00:10,546 - INFO - 第 2 页获取到 100 条记录
2025-05-04 15:00:10,546 - INFO - Request Parameters - Page 3:
2025-05-04 15:00:10,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:10,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:11,062 - INFO - Response - Page 3:
2025-05-04 15:00:11,265 - INFO - 第 3 页获取到 100 条记录
2025-05-04 15:00:11,265 - INFO - Request Parameters - Page 4:
2025-05-04 15:00:11,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:11,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:11,781 - INFO - Response - Page 4:
2025-05-04 15:00:11,984 - INFO - 第 4 页获取到 100 条记录
2025-05-04 15:00:11,984 - INFO - Request Parameters - Page 5:
2025-05-04 15:00:11,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:11,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:12,484 - INFO - Response - Page 5:
2025-05-04 15:00:12,687 - INFO - 第 5 页获取到 100 条记录
2025-05-04 15:00:12,687 - INFO - Request Parameters - Page 6:
2025-05-04 15:00:12,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:12,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:13,202 - INFO - Response - Page 6:
2025-05-04 15:00:13,406 - INFO - 第 6 页获取到 100 条记录
2025-05-04 15:00:13,406 - INFO - Request Parameters - Page 7:
2025-05-04 15:00:13,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:13,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:13,843 - INFO - Response - Page 7:
2025-05-04 15:00:14,046 - INFO - 第 7 页获取到 70 条记录
2025-05-04 15:00:14,046 - INFO - 查询完成，共获取到 670 条记录
2025-05-04 15:00:14,046 - INFO - 获取到 670 条表单数据
2025-05-04 15:00:14,046 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-04 15:00:14,062 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 15:00:14,062 - INFO - 开始处理日期: 2025-03
2025-05-04 15:00:14,062 - INFO - Request Parameters - Page 1:
2025-05-04 15:00:14,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:14,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:14,562 - INFO - Response - Page 1:
2025-05-04 15:00:14,765 - INFO - 第 1 页获取到 100 条记录
2025-05-04 15:00:14,765 - INFO - Request Parameters - Page 2:
2025-05-04 15:00:14,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:14,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:15,218 - INFO - Response - Page 2:
2025-05-04 15:00:15,421 - INFO - 第 2 页获取到 100 条记录
2025-05-04 15:00:15,421 - INFO - Request Parameters - Page 3:
2025-05-04 15:00:15,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:15,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:16,031 - INFO - Response - Page 3:
2025-05-04 15:00:16,234 - INFO - 第 3 页获取到 100 条记录
2025-05-04 15:00:16,234 - INFO - Request Parameters - Page 4:
2025-05-04 15:00:16,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:16,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:16,671 - INFO - Response - Page 4:
2025-05-04 15:00:16,874 - INFO - 第 4 页获取到 100 条记录
2025-05-04 15:00:16,874 - INFO - Request Parameters - Page 5:
2025-05-04 15:00:16,874 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:16,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:17,359 - INFO - Response - Page 5:
2025-05-04 15:00:17,577 - INFO - 第 5 页获取到 100 条记录
2025-05-04 15:00:17,577 - INFO - Request Parameters - Page 6:
2025-05-04 15:00:17,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:17,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:18,062 - INFO - Response - Page 6:
2025-05-04 15:00:18,265 - INFO - 第 6 页获取到 100 条记录
2025-05-04 15:00:18,265 - INFO - Request Parameters - Page 7:
2025-05-04 15:00:18,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:18,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:18,765 - INFO - Response - Page 7:
2025-05-04 15:00:18,968 - INFO - 第 7 页获取到 61 条记录
2025-05-04 15:00:18,968 - INFO - 查询完成，共获取到 661 条记录
2025-05-04 15:00:18,968 - INFO - 获取到 661 条表单数据
2025-05-04 15:00:18,968 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-04 15:00:18,984 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 15:00:18,984 - INFO - 开始处理日期: 2025-04
2025-05-04 15:00:18,984 - INFO - Request Parameters - Page 1:
2025-05-04 15:00:18,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:18,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:19,515 - INFO - Response - Page 1:
2025-05-04 15:00:19,718 - INFO - 第 1 页获取到 100 条记录
2025-05-04 15:00:19,718 - INFO - Request Parameters - Page 2:
2025-05-04 15:00:19,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:19,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:20,249 - INFO - Response - Page 2:
2025-05-04 15:00:20,452 - INFO - 第 2 页获取到 100 条记录
2025-05-04 15:00:20,452 - INFO - Request Parameters - Page 3:
2025-05-04 15:00:20,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:20,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:20,999 - INFO - Response - Page 3:
2025-05-04 15:00:21,202 - INFO - 第 3 页获取到 100 条记录
2025-05-04 15:00:21,202 - INFO - Request Parameters - Page 4:
2025-05-04 15:00:21,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:21,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:21,640 - INFO - Response - Page 4:
2025-05-04 15:00:21,843 - INFO - 第 4 页获取到 100 条记录
2025-05-04 15:00:21,843 - INFO - Request Parameters - Page 5:
2025-05-04 15:00:21,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:21,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:22,374 - INFO - Response - Page 5:
2025-05-04 15:00:22,577 - INFO - 第 5 页获取到 100 条记录
2025-05-04 15:00:22,577 - INFO - Request Parameters - Page 6:
2025-05-04 15:00:22,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:22,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:23,077 - INFO - Response - Page 6:
2025-05-04 15:00:23,281 - INFO - 第 6 页获取到 100 条记录
2025-05-04 15:00:23,281 - INFO - Request Parameters - Page 7:
2025-05-04 15:00:23,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:23,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:23,687 - INFO - Response - Page 7:
2025-05-04 15:00:23,890 - INFO - 第 7 页获取到 27 条记录
2025-05-04 15:00:23,890 - INFO - 查询完成，共获取到 627 条记录
2025-05-04 15:00:23,890 - INFO - 获取到 627 条表单数据
2025-05-04 15:00:23,890 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-04 15:00:23,906 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 15:00:23,906 - INFO - 开始处理日期: 2025-05
2025-05-04 15:00:23,906 - INFO - Request Parameters - Page 1:
2025-05-04 15:00:23,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:23,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:24,577 - INFO - Response - Page 1:
2025-05-04 15:00:24,781 - INFO - 第 1 页获取到 100 条记录
2025-05-04 15:00:24,781 - INFO - Request Parameters - Page 2:
2025-05-04 15:00:24,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:24,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:25,218 - INFO - Response - Page 2:
2025-05-04 15:00:25,421 - INFO - 第 2 页获取到 100 条记录
2025-05-04 15:00:25,421 - INFO - Request Parameters - Page 3:
2025-05-04 15:00:25,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:25,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:25,859 - INFO - Response - Page 3:
2025-05-04 15:00:26,062 - INFO - 第 3 页获取到 100 条记录
2025-05-04 15:00:26,062 - INFO - Request Parameters - Page 4:
2025-05-04 15:00:26,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:26,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:26,515 - INFO - Response - Page 4:
2025-05-04 15:00:26,718 - INFO - 第 4 页获取到 100 条记录
2025-05-04 15:00:26,718 - INFO - Request Parameters - Page 5:
2025-05-04 15:00:26,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:26,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:27,187 - INFO - Response - Page 5:
2025-05-04 15:00:27,390 - INFO - 第 5 页获取到 100 条记录
2025-05-04 15:00:27,390 - INFO - Request Parameters - Page 6:
2025-05-04 15:00:27,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 15:00:27,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 15:00:27,890 - INFO - Response - Page 6:
2025-05-04 15:00:28,093 - INFO - 第 6 页获取到 80 条记录
2025-05-04 15:00:28,093 - INFO - 查询完成，共获取到 580 条记录
2025-05-04 15:00:28,093 - INFO - 获取到 580 条表单数据
2025-05-04 15:00:28,093 - INFO - 当前日期 2025-05 有 580 条MySQL数据需要处理
2025-05-04 15:00:28,109 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-04 15:00:28,577 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-04 15:00:28,577 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3149.0, 'new_value': 4358.0}, {'field': 'total_amount', 'old_value': 3149.0, 'new_value': 4358.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 18}]
2025-05-04 15:00:28,577 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-04 15:00:28,577 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-04 15:00:28,577 - INFO - =================同步完成====================
2025-05-04 18:00:01,854 - INFO - =================使用默认全量同步=============
2025-05-04 18:00:03,026 - INFO - MySQL查询成功，共获取 3220 条记录
2025-05-04 18:00:03,026 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-04 18:00:03,057 - INFO - 开始处理日期: 2025-01
2025-05-04 18:00:03,057 - INFO - Request Parameters - Page 1:
2025-05-04 18:00:03,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:03,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:04,182 - INFO - Response - Page 1:
2025-05-04 18:00:04,385 - INFO - 第 1 页获取到 100 条记录
2025-05-04 18:00:04,385 - INFO - Request Parameters - Page 2:
2025-05-04 18:00:04,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:04,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:04,885 - INFO - Response - Page 2:
2025-05-04 18:00:05,088 - INFO - 第 2 页获取到 100 条记录
2025-05-04 18:00:05,088 - INFO - Request Parameters - Page 3:
2025-05-04 18:00:05,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:05,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:05,620 - INFO - Response - Page 3:
2025-05-04 18:00:05,823 - INFO - 第 3 页获取到 100 条记录
2025-05-04 18:00:05,823 - INFO - Request Parameters - Page 4:
2025-05-04 18:00:05,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:05,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:06,323 - INFO - Response - Page 4:
2025-05-04 18:00:06,526 - INFO - 第 4 页获取到 100 条记录
2025-05-04 18:00:06,526 - INFO - Request Parameters - Page 5:
2025-05-04 18:00:06,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:06,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:06,979 - INFO - Response - Page 5:
2025-05-04 18:00:07,182 - INFO - 第 5 页获取到 100 条记录
2025-05-04 18:00:07,182 - INFO - Request Parameters - Page 6:
2025-05-04 18:00:07,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:07,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:07,698 - INFO - Response - Page 6:
2025-05-04 18:00:07,901 - INFO - 第 6 页获取到 100 条记录
2025-05-04 18:00:07,901 - INFO - Request Parameters - Page 7:
2025-05-04 18:00:07,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:07,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:08,370 - INFO - Response - Page 7:
2025-05-04 18:00:08,573 - INFO - 第 7 页获取到 82 条记录
2025-05-04 18:00:08,573 - INFO - 查询完成，共获取到 682 条记录
2025-05-04 18:00:08,573 - INFO - 获取到 682 条表单数据
2025-05-04 18:00:08,573 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-04 18:00:08,588 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 18:00:08,588 - INFO - 开始处理日期: 2025-02
2025-05-04 18:00:08,588 - INFO - Request Parameters - Page 1:
2025-05-04 18:00:08,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:08,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:09,042 - INFO - Response - Page 1:
2025-05-04 18:00:09,245 - INFO - 第 1 页获取到 100 条记录
2025-05-04 18:00:09,245 - INFO - Request Parameters - Page 2:
2025-05-04 18:00:09,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:09,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:09,807 - INFO - Response - Page 2:
2025-05-04 18:00:10,010 - INFO - 第 2 页获取到 100 条记录
2025-05-04 18:00:10,010 - INFO - Request Parameters - Page 3:
2025-05-04 18:00:10,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:10,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:10,526 - INFO - Response - Page 3:
2025-05-04 18:00:10,729 - INFO - 第 3 页获取到 100 条记录
2025-05-04 18:00:10,729 - INFO - Request Parameters - Page 4:
2025-05-04 18:00:10,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:10,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:11,276 - INFO - Response - Page 4:
2025-05-04 18:00:11,479 - INFO - 第 4 页获取到 100 条记录
2025-05-04 18:00:11,479 - INFO - Request Parameters - Page 5:
2025-05-04 18:00:11,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:11,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:12,010 - INFO - Response - Page 5:
2025-05-04 18:00:12,213 - INFO - 第 5 页获取到 100 条记录
2025-05-04 18:00:12,213 - INFO - Request Parameters - Page 6:
2025-05-04 18:00:12,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:12,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:12,651 - INFO - Response - Page 6:
2025-05-04 18:00:12,854 - INFO - 第 6 页获取到 100 条记录
2025-05-04 18:00:12,854 - INFO - Request Parameters - Page 7:
2025-05-04 18:00:12,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:12,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:13,323 - INFO - Response - Page 7:
2025-05-04 18:00:13,526 - INFO - 第 7 页获取到 70 条记录
2025-05-04 18:00:13,526 - INFO - 查询完成，共获取到 670 条记录
2025-05-04 18:00:13,526 - INFO - 获取到 670 条表单数据
2025-05-04 18:00:13,526 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-04 18:00:13,542 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 18:00:13,542 - INFO - 开始处理日期: 2025-03
2025-05-04 18:00:13,542 - INFO - Request Parameters - Page 1:
2025-05-04 18:00:13,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:13,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:14,073 - INFO - Response - Page 1:
2025-05-04 18:00:14,276 - INFO - 第 1 页获取到 100 条记录
2025-05-04 18:00:14,276 - INFO - Request Parameters - Page 2:
2025-05-04 18:00:14,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:14,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:14,729 - INFO - Response - Page 2:
2025-05-04 18:00:14,932 - INFO - 第 2 页获取到 100 条记录
2025-05-04 18:00:14,932 - INFO - Request Parameters - Page 3:
2025-05-04 18:00:14,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:14,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:15,401 - INFO - Response - Page 3:
2025-05-04 18:00:15,604 - INFO - 第 3 页获取到 100 条记录
2025-05-04 18:00:15,604 - INFO - Request Parameters - Page 4:
2025-05-04 18:00:15,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:15,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:16,088 - INFO - Response - Page 4:
2025-05-04 18:00:16,291 - INFO - 第 4 页获取到 100 条记录
2025-05-04 18:00:16,291 - INFO - Request Parameters - Page 5:
2025-05-04 18:00:16,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:16,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:16,776 - INFO - Response - Page 5:
2025-05-04 18:00:16,979 - INFO - 第 5 页获取到 100 条记录
2025-05-04 18:00:16,979 - INFO - Request Parameters - Page 6:
2025-05-04 18:00:16,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:16,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:17,401 - INFO - Response - Page 6:
2025-05-04 18:00:17,604 - INFO - 第 6 页获取到 100 条记录
2025-05-04 18:00:17,604 - INFO - Request Parameters - Page 7:
2025-05-04 18:00:17,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:17,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:18,026 - INFO - Response - Page 7:
2025-05-04 18:00:18,229 - INFO - 第 7 页获取到 61 条记录
2025-05-04 18:00:18,229 - INFO - 查询完成，共获取到 661 条记录
2025-05-04 18:00:18,229 - INFO - 获取到 661 条表单数据
2025-05-04 18:00:18,229 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-04 18:00:18,245 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 18:00:18,245 - INFO - 开始处理日期: 2025-04
2025-05-04 18:00:18,245 - INFO - Request Parameters - Page 1:
2025-05-04 18:00:18,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:18,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:18,698 - INFO - Response - Page 1:
2025-05-04 18:00:18,901 - INFO - 第 1 页获取到 100 条记录
2025-05-04 18:00:18,901 - INFO - Request Parameters - Page 2:
2025-05-04 18:00:18,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:18,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:19,416 - INFO - Response - Page 2:
2025-05-04 18:00:19,620 - INFO - 第 2 页获取到 100 条记录
2025-05-04 18:00:19,620 - INFO - Request Parameters - Page 3:
2025-05-04 18:00:19,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:19,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:20,166 - INFO - Response - Page 3:
2025-05-04 18:00:20,370 - INFO - 第 3 页获取到 100 条记录
2025-05-04 18:00:20,370 - INFO - Request Parameters - Page 4:
2025-05-04 18:00:20,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:20,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:20,932 - INFO - Response - Page 4:
2025-05-04 18:00:21,135 - INFO - 第 4 页获取到 100 条记录
2025-05-04 18:00:21,135 - INFO - Request Parameters - Page 5:
2025-05-04 18:00:21,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:21,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:21,651 - INFO - Response - Page 5:
2025-05-04 18:00:21,854 - INFO - 第 5 页获取到 100 条记录
2025-05-04 18:00:21,854 - INFO - Request Parameters - Page 6:
2025-05-04 18:00:21,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:21,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:22,354 - INFO - Response - Page 6:
2025-05-04 18:00:22,557 - INFO - 第 6 页获取到 100 条记录
2025-05-04 18:00:22,557 - INFO - Request Parameters - Page 7:
2025-05-04 18:00:22,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:22,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:22,901 - INFO - Response - Page 7:
2025-05-04 18:00:23,104 - INFO - 第 7 页获取到 27 条记录
2025-05-04 18:00:23,104 - INFO - 查询完成，共获取到 627 条记录
2025-05-04 18:00:23,104 - INFO - 获取到 627 条表单数据
2025-05-04 18:00:23,104 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-04 18:00:23,120 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 18:00:23,120 - INFO - 开始处理日期: 2025-05
2025-05-04 18:00:23,120 - INFO - Request Parameters - Page 1:
2025-05-04 18:00:23,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:23,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:23,666 - INFO - Response - Page 1:
2025-05-04 18:00:23,870 - INFO - 第 1 页获取到 100 条记录
2025-05-04 18:00:23,870 - INFO - Request Parameters - Page 2:
2025-05-04 18:00:23,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:23,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:24,385 - INFO - Response - Page 2:
2025-05-04 18:00:24,588 - INFO - 第 2 页获取到 100 条记录
2025-05-04 18:00:24,588 - INFO - Request Parameters - Page 3:
2025-05-04 18:00:24,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:24,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:24,995 - INFO - Response - Page 3:
2025-05-04 18:00:25,198 - INFO - 第 3 页获取到 100 条记录
2025-05-04 18:00:25,198 - INFO - Request Parameters - Page 4:
2025-05-04 18:00:25,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:25,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:25,651 - INFO - Response - Page 4:
2025-05-04 18:00:25,854 - INFO - 第 4 页获取到 100 条记录
2025-05-04 18:00:25,854 - INFO - Request Parameters - Page 5:
2025-05-04 18:00:25,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:25,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:26,323 - INFO - Response - Page 5:
2025-05-04 18:00:26,526 - INFO - 第 5 页获取到 100 条记录
2025-05-04 18:00:26,526 - INFO - Request Parameters - Page 6:
2025-05-04 18:00:26,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 18:00:26,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 18:00:26,995 - INFO - Response - Page 6:
2025-05-04 18:00:27,198 - INFO - 第 6 页获取到 80 条记录
2025-05-04 18:00:27,198 - INFO - 查询完成，共获取到 580 条记录
2025-05-04 18:00:27,198 - INFO - 获取到 580 条表单数据
2025-05-04 18:00:27,198 - INFO - 当前日期 2025-05 有 580 条MySQL数据需要处理
2025-05-04 18:00:27,198 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-04 18:00:27,651 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-04 18:00:27,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 919.0, 'new_value': 910.0}, {'field': 'offline_amount', 'old_value': 2291.67, 'new_value': 2303.87}, {'field': 'total_amount', 'old_value': 3210.67, 'new_value': 3213.87}, {'field': 'order_count', 'old_value': 101, 'new_value': 106}]
2025-05-04 18:00:27,651 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-04 18:00:27,651 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-04 18:00:27,651 - INFO - =================同步完成====================
2025-05-04 21:00:01,946 - INFO - =================使用默认全量同步=============
2025-05-04 21:00:03,118 - INFO - MySQL查询成功，共获取 3220 条记录
2025-05-04 21:00:03,118 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-04 21:00:03,149 - INFO - 开始处理日期: 2025-01
2025-05-04 21:00:03,149 - INFO - Request Parameters - Page 1:
2025-05-04 21:00:03,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:03,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:04,133 - INFO - Response - Page 1:
2025-05-04 21:00:04,336 - INFO - 第 1 页获取到 100 条记录
2025-05-04 21:00:04,336 - INFO - Request Parameters - Page 2:
2025-05-04 21:00:04,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:04,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:05,040 - INFO - Response - Page 2:
2025-05-04 21:00:05,243 - INFO - 第 2 页获取到 100 条记录
2025-05-04 21:00:05,243 - INFO - Request Parameters - Page 3:
2025-05-04 21:00:05,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:05,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:05,727 - INFO - Response - Page 3:
2025-05-04 21:00:05,930 - INFO - 第 3 页获取到 100 条记录
2025-05-04 21:00:05,930 - INFO - Request Parameters - Page 4:
2025-05-04 21:00:05,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:05,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:06,399 - INFO - Response - Page 4:
2025-05-04 21:00:06,602 - INFO - 第 4 页获取到 100 条记录
2025-05-04 21:00:06,602 - INFO - Request Parameters - Page 5:
2025-05-04 21:00:06,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:06,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:07,133 - INFO - Response - Page 5:
2025-05-04 21:00:07,336 - INFO - 第 5 页获取到 100 条记录
2025-05-04 21:00:07,336 - INFO - Request Parameters - Page 6:
2025-05-04 21:00:07,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:07,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:07,852 - INFO - Response - Page 6:
2025-05-04 21:00:08,055 - INFO - 第 6 页获取到 100 条记录
2025-05-04 21:00:08,055 - INFO - Request Parameters - Page 7:
2025-05-04 21:00:08,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:08,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:08,493 - INFO - Response - Page 7:
2025-05-04 21:00:08,696 - INFO - 第 7 页获取到 82 条记录
2025-05-04 21:00:08,696 - INFO - 查询完成，共获取到 682 条记录
2025-05-04 21:00:08,696 - INFO - 获取到 682 条表单数据
2025-05-04 21:00:08,696 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-04 21:00:08,711 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 21:00:08,711 - INFO - 开始处理日期: 2025-02
2025-05-04 21:00:08,711 - INFO - Request Parameters - Page 1:
2025-05-04 21:00:08,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:08,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:09,290 - INFO - Response - Page 1:
2025-05-04 21:00:09,493 - INFO - 第 1 页获取到 100 条记录
2025-05-04 21:00:09,493 - INFO - Request Parameters - Page 2:
2025-05-04 21:00:09,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:09,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:09,915 - INFO - Response - Page 2:
2025-05-04 21:00:10,118 - INFO - 第 2 页获取到 100 条记录
2025-05-04 21:00:10,118 - INFO - Request Parameters - Page 3:
2025-05-04 21:00:10,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:10,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:10,633 - INFO - Response - Page 3:
2025-05-04 21:00:10,836 - INFO - 第 3 页获取到 100 条记录
2025-05-04 21:00:10,836 - INFO - Request Parameters - Page 4:
2025-05-04 21:00:10,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:10,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:11,290 - INFO - Response - Page 4:
2025-05-04 21:00:11,493 - INFO - 第 4 页获取到 100 条记录
2025-05-04 21:00:11,493 - INFO - Request Parameters - Page 5:
2025-05-04 21:00:11,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:11,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:11,961 - INFO - Response - Page 5:
2025-05-04 21:00:12,165 - INFO - 第 5 页获取到 100 条记录
2025-05-04 21:00:12,165 - INFO - Request Parameters - Page 6:
2025-05-04 21:00:12,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:12,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:12,602 - INFO - Response - Page 6:
2025-05-04 21:00:12,805 - INFO - 第 6 页获取到 100 条记录
2025-05-04 21:00:12,805 - INFO - Request Parameters - Page 7:
2025-05-04 21:00:12,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:12,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:13,274 - INFO - Response - Page 7:
2025-05-04 21:00:13,477 - INFO - 第 7 页获取到 70 条记录
2025-05-04 21:00:13,477 - INFO - 查询完成，共获取到 670 条记录
2025-05-04 21:00:13,477 - INFO - 获取到 670 条表单数据
2025-05-04 21:00:13,477 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-04 21:00:13,493 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 21:00:13,493 - INFO - 开始处理日期: 2025-03
2025-05-04 21:00:13,493 - INFO - Request Parameters - Page 1:
2025-05-04 21:00:13,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:13,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:13,930 - INFO - Response - Page 1:
2025-05-04 21:00:14,133 - INFO - 第 1 页获取到 100 条记录
2025-05-04 21:00:14,133 - INFO - Request Parameters - Page 2:
2025-05-04 21:00:14,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:14,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:14,540 - INFO - Response - Page 2:
2025-05-04 21:00:14,743 - INFO - 第 2 页获取到 100 条记录
2025-05-04 21:00:14,743 - INFO - Request Parameters - Page 3:
2025-05-04 21:00:14,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:14,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:15,430 - INFO - Response - Page 3:
2025-05-04 21:00:15,633 - INFO - 第 3 页获取到 100 条记录
2025-05-04 21:00:15,633 - INFO - Request Parameters - Page 4:
2025-05-04 21:00:15,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:15,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:16,133 - INFO - Response - Page 4:
2025-05-04 21:00:16,336 - INFO - 第 4 页获取到 100 条记录
2025-05-04 21:00:16,336 - INFO - Request Parameters - Page 5:
2025-05-04 21:00:16,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:16,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:16,852 - INFO - Response - Page 5:
2025-05-04 21:00:17,055 - INFO - 第 5 页获取到 100 条记录
2025-05-04 21:00:17,055 - INFO - Request Parameters - Page 6:
2025-05-04 21:00:17,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:17,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:17,477 - INFO - Response - Page 6:
2025-05-04 21:00:17,680 - INFO - 第 6 页获取到 100 条记录
2025-05-04 21:00:17,680 - INFO - Request Parameters - Page 7:
2025-05-04 21:00:17,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:17,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:18,055 - INFO - Response - Page 7:
2025-05-04 21:00:18,258 - INFO - 第 7 页获取到 61 条记录
2025-05-04 21:00:18,258 - INFO - 查询完成，共获取到 661 条记录
2025-05-04 21:00:18,258 - INFO - 获取到 661 条表单数据
2025-05-04 21:00:18,258 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-04 21:00:18,274 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 21:00:18,274 - INFO - 开始处理日期: 2025-04
2025-05-04 21:00:18,274 - INFO - Request Parameters - Page 1:
2025-05-04 21:00:18,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:18,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:18,711 - INFO - Response - Page 1:
2025-05-04 21:00:18,914 - INFO - 第 1 页获取到 100 条记录
2025-05-04 21:00:18,914 - INFO - Request Parameters - Page 2:
2025-05-04 21:00:18,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:18,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:19,477 - INFO - Response - Page 2:
2025-05-04 21:00:19,680 - INFO - 第 2 页获取到 100 条记录
2025-05-04 21:00:19,680 - INFO - Request Parameters - Page 3:
2025-05-04 21:00:19,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:19,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:20,180 - INFO - Response - Page 3:
2025-05-04 21:00:20,383 - INFO - 第 3 页获取到 100 条记录
2025-05-04 21:00:20,383 - INFO - Request Parameters - Page 4:
2025-05-04 21:00:20,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:20,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:20,789 - INFO - Response - Page 4:
2025-05-04 21:00:20,993 - INFO - 第 4 页获取到 100 条记录
2025-05-04 21:00:20,993 - INFO - Request Parameters - Page 5:
2025-05-04 21:00:20,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:20,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:21,493 - INFO - Response - Page 5:
2025-05-04 21:00:21,696 - INFO - 第 5 页获取到 100 条记录
2025-05-04 21:00:21,696 - INFO - Request Parameters - Page 6:
2025-05-04 21:00:21,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:21,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:22,149 - INFO - Response - Page 6:
2025-05-04 21:00:22,352 - INFO - 第 6 页获取到 100 条记录
2025-05-04 21:00:22,352 - INFO - Request Parameters - Page 7:
2025-05-04 21:00:22,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:22,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:22,743 - INFO - Response - Page 7:
2025-05-04 21:00:22,946 - INFO - 第 7 页获取到 27 条记录
2025-05-04 21:00:22,946 - INFO - 查询完成，共获取到 627 条记录
2025-05-04 21:00:22,946 - INFO - 获取到 627 条表单数据
2025-05-04 21:00:22,946 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-04 21:00:22,961 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-04 21:00:22,961 - INFO - 开始处理日期: 2025-05
2025-05-04 21:00:22,961 - INFO - Request Parameters - Page 1:
2025-05-04 21:00:22,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:22,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:23,430 - INFO - Response - Page 1:
2025-05-04 21:00:23,633 - INFO - 第 1 页获取到 100 条记录
2025-05-04 21:00:23,633 - INFO - Request Parameters - Page 2:
2025-05-04 21:00:23,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:23,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:24,133 - INFO - Response - Page 2:
2025-05-04 21:00:24,336 - INFO - 第 2 页获取到 100 条记录
2025-05-04 21:00:24,336 - INFO - Request Parameters - Page 3:
2025-05-04 21:00:24,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:24,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:24,899 - INFO - Response - Page 3:
2025-05-04 21:00:25,102 - INFO - 第 3 页获取到 100 条记录
2025-05-04 21:00:25,102 - INFO - Request Parameters - Page 4:
2025-05-04 21:00:25,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:25,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:25,539 - INFO - Response - Page 4:
2025-05-04 21:00:25,743 - INFO - 第 4 页获取到 100 条记录
2025-05-04 21:00:25,743 - INFO - Request Parameters - Page 5:
2025-05-04 21:00:25,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:25,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:26,196 - INFO - Response - Page 5:
2025-05-04 21:00:26,399 - INFO - 第 5 页获取到 100 条记录
2025-05-04 21:00:26,399 - INFO - Request Parameters - Page 6:
2025-05-04 21:00:26,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-04 21:00:26,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-04 21:00:26,821 - INFO - Response - Page 6:
2025-05-04 21:00:27,024 - INFO - 第 6 页获取到 80 条记录
2025-05-04 21:00:27,024 - INFO - 查询完成，共获取到 580 条记录
2025-05-04 21:00:27,024 - INFO - 获取到 580 条表单数据
2025-05-04 21:00:27,024 - INFO - 当前日期 2025-05 有 580 条MySQL数据需要处理
2025-05-04 21:00:27,024 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-04 21:00:27,461 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-04 21:00:27,461 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13810.0, 'new_value': 19947.0}, {'field': 'offline_amount', 'old_value': 15675.28, 'new_value': 19808.28}, {'field': 'total_amount', 'old_value': 29485.28, 'new_value': 39755.28}, {'field': 'order_count', 'old_value': 613, 'new_value': 816}]
2025-05-04 21:00:27,477 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-04 21:00:27,477 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-04 21:00:27,477 - INFO - =================同步完成====================
