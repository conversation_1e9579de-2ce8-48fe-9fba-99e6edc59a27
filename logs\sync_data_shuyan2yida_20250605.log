2025-06-05 08:00:03,177 - INFO - ==================================================
2025-06-05 08:00:03,177 - INFO - 程序启动 - 版本 v1.0.0
2025-06-05 08:00:03,177 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250605.log
2025-06-05 08:00:03,177 - INFO - ==================================================
2025-06-05 08:00:03,177 - INFO - 程序入口点: __main__
2025-06-05 08:00:03,177 - INFO - ==================================================
2025-06-05 08:00:03,177 - INFO - 程序启动 - 版本 v1.0.1
2025-06-05 08:00:03,177 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250605.log
2025-06-05 08:00:03,177 - INFO - ==================================================
2025-06-05 08:00:03,489 - INFO - 数据库文件已存在: data\sales_data.db
2025-06-05 08:00:03,489 - INFO - sales_data表已存在，无需创建
2025-06-05 08:00:03,489 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-06-05 08:00:03,489 - INFO - DataSyncManager初始化完成
2025-06-05 08:00:03,489 - INFO - 未提供日期参数，使用默认值
2025-06-05 08:00:03,489 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-06-05 08:00:03,489 - INFO - 开始综合数据同步流程...
2025-06-05 08:00:03,489 - INFO - 正在获取数衍平台日销售数据...
2025-06-05 08:00:03,489 - INFO - 查询数衍平台数据，时间段为: 2025-04-05, 2025-06-04
2025-06-05 08:00:03,489 - INFO - 正在获取********至********的数据
2025-06-05 08:00:03,489 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:03,489 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '96640CBB3FA33E6CE807133570E7B015'}
2025-06-05 08:00:05,568 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:05,568 - INFO - 过滤后保留 431 条记录
2025-06-05 08:00:07,583 - INFO - 正在获取********至********的数据
2025-06-05 08:00:07,583 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:07,583 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FA27F032097E4DEC9B58A4FC5D550C92'}
2025-06-05 08:00:08,927 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:08,943 - INFO - 过滤后保留 426 条记录
2025-06-05 08:00:10,943 - INFO - 正在获取********至********的数据
2025-06-05 08:00:10,943 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:10,943 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '422913432485B03B9B0C9CE204726DB7'}
2025-06-05 08:00:11,943 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:11,943 - INFO - 过滤后保留 423 条记录
2025-06-05 08:00:13,958 - INFO - 正在获取********至********的数据
2025-06-05 08:00:13,958 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:13,958 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9781A068AE69E9DE187BB7308A49B5E0'}
2025-06-05 08:00:15,005 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:15,005 - INFO - 过滤后保留 432 条记录
2025-06-05 08:00:17,021 - INFO - 正在获取********至********的数据
2025-06-05 08:00:17,021 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:17,021 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '92D59527C8B4ED3A3818705AC6BD4E7C'}
2025-06-05 08:00:17,974 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:17,974 - INFO - 过滤后保留 434 条记录
2025-06-05 08:00:19,974 - INFO - 正在获取********至********的数据
2025-06-05 08:00:19,974 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:19,974 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '863A0FB30746A0C0B3DA2D837DB2F108'}
2025-06-05 08:00:21,146 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:21,161 - INFO - 过滤后保留 424 条记录
2025-06-05 08:00:23,161 - INFO - 正在获取********至********的数据
2025-06-05 08:00:23,161 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:23,161 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '249391A898B567B0E7800F4977F49010'}
2025-06-05 08:00:24,099 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:24,099 - INFO - 过滤后保留 436 条记录
2025-06-05 08:00:26,114 - INFO - 正在获取********至********的数据
2025-06-05 08:00:26,114 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:26,114 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7A2A9CE09B2603257C68B1611394FA8E'}
2025-06-05 08:00:27,161 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:27,161 - INFO - 过滤后保留 431 条记录
2025-06-05 08:00:29,177 - INFO - 正在获取********至********的数据
2025-06-05 08:00:29,177 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:29,177 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '238EB1712E45B21DE28B9AA416D1EBF3'}
2025-06-05 08:00:30,099 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:30,114 - INFO - 过滤后保留 425 条记录
2025-06-05 08:00:32,130 - INFO - 正在获取********至********的数据
2025-06-05 08:00:32,130 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:32,130 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AE55E92B543B7A4E98FE195C96FEE5E5'}
2025-06-05 08:00:33,146 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:33,161 - INFO - 过滤后保留 414 条记录
2025-06-05 08:00:35,177 - INFO - 正在获取********至********的数据
2025-06-05 08:00:35,177 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:35,177 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '36AF2408ED0DB28EF033B464EDE7CFE5'}
2025-06-05 08:00:36,286 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:36,286 - INFO - 过滤后保留 427 条记录
2025-06-05 08:00:38,302 - INFO - 正在获取********至********的数据
2025-06-05 08:00:38,302 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:38,302 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '68795770B86C896CFB663EF64C6A464E'}
2025-06-05 08:00:39,239 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:39,239 - INFO - 过滤后保留 428 条记录
2025-06-05 08:00:41,255 - INFO - 正在获取********至********的数据
2025-06-05 08:00:41,255 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:41,255 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C7D53A4657561BD2E0FFA2CC0BAAEBA1'}
2025-06-05 08:00:42,161 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:42,161 - INFO - 过滤后保留 429 条记录
2025-06-05 08:00:44,177 - INFO - 正在获取********至********的数据
2025-06-05 08:00:44,177 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:44,177 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CBFD6CFAB40CDCE4E472FB2B0FCFD3E1'}
2025-06-05 08:00:45,286 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:45,302 - INFO - 过滤后保留 425 条记录
2025-06-05 08:00:47,317 - INFO - 正在获取********至********的数据
2025-06-05 08:00:47,317 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:47,317 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7F469262337C63B00A65405149CF8FE0'}
2025-06-05 08:00:48,599 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:48,614 - INFO - 过滤后保留 426 条记录
2025-06-05 08:00:50,630 - INFO - 正在获取********至********的数据
2025-06-05 08:00:50,630 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:50,630 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '45FE2BB36BFB3DFE0064F8A33A896B65'}
2025-06-05 08:00:51,645 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:51,645 - INFO - 过滤后保留 413 条记录
2025-06-05 08:00:53,661 - INFO - 正在获取********至********的数据
2025-06-05 08:00:53,661 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:53,661 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-06-05 08:00:54,755 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:54,755 - INFO - 过滤后保留 413 条记录
2025-06-05 08:00:56,770 - INFO - 正在获取********至********的数据
2025-06-05 08:00:56,770 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:56,770 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '71ABA2B79033D030950B1C6CB94CD8F6'}
2025-06-05 08:00:57,817 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:00:57,817 - INFO - 过滤后保留 432 条记录
2025-06-05 08:00:59,833 - INFO - 正在获取********至********的数据
2025-06-05 08:00:59,833 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:00:59,833 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '22D98FF7862F06AEA7FD59D38072FE0E'}
2025-06-05 08:01:00,755 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:01:00,755 - INFO - 过滤后保留 433 条记录
2025-06-05 08:01:02,770 - INFO - 正在获取********至********的数据
2025-06-05 08:01:02,770 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:01:02,770 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A0D84A8256DD3803E11ECC003E717545'}
2025-06-05 08:01:03,817 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:01:03,833 - INFO - 过滤后保留 417 条记录
2025-06-05 08:01:05,848 - INFO - 正在获取********至********的数据
2025-06-05 08:01:05,848 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:01:05,848 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B6D22445BA5FA3383D317840620027B7'}
2025-06-05 08:01:07,005 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:01:07,005 - INFO - 过滤后保留 420 条记录
2025-06-05 08:01:09,020 - INFO - 正在获取********至********的数据
2025-06-05 08:01:09,020 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:01:09,020 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '73BA4BC35E21584ADAEC1C6220755B6D'}
2025-06-05 08:01:10,223 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:01:10,223 - INFO - 过滤后保留 431 条记录
2025-06-05 08:01:12,239 - INFO - 正在获取********至********的数据
2025-06-05 08:01:12,239 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:01:12,239 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'BB997F19A7116009AAD34F0804336726'}
2025-06-05 08:01:13,114 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:01:13,114 - INFO - 过滤后保留 423 条记录
2025-06-05 08:01:15,145 - INFO - 正在获取********至********的数据
2025-06-05 08:01:15,145 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:01:15,145 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C40BEA344C9D74AB428F5B0CBE7E9004'}
2025-06-05 08:01:16,755 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:01:16,755 - INFO - 过滤后保留 416 条记录
2025-06-05 08:01:18,770 - INFO - 正在获取********至********的数据
2025-06-05 08:01:18,770 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:01:18,770 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1135943D42B99B7F3FFDC0A912450F80'}
2025-06-05 08:01:19,911 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:01:19,927 - INFO - 过滤后保留 423 条记录
2025-06-05 08:01:21,942 - INFO - 正在获取********至********的数据
2025-06-05 08:01:21,942 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:01:21,942 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '01FBC97D80AC9423B8F1BF0027C6B689'}
2025-06-05 08:01:23,130 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:01:23,145 - INFO - 过滤后保留 414 条记录
2025-06-05 08:01:25,161 - INFO - 正在获取********至********的数据
2025-06-05 08:01:25,161 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:01:25,161 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DC257CDC617FD70FE221258A03315A7A'}
2025-06-05 08:01:26,348 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:01:26,348 - INFO - 过滤后保留 413 条记录
2025-06-05 08:01:28,364 - INFO - 正在获取********至********的数据
2025-06-05 08:01:28,364 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:01:28,364 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F1AFBA019E749AC02B83C70163CBFDAD'}
2025-06-05 08:01:29,411 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:01:29,426 - INFO - 过滤后保留 414 条记录
2025-06-05 08:01:31,442 - INFO - 正在获取********至********的数据
2025-06-05 08:01:31,442 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:01:31,442 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FEB37C4EF11A3999C3D6DEE712606AD8'}
2025-06-05 08:01:32,614 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:01:32,630 - INFO - 过滤后保留 414 条记录
2025-06-05 08:01:34,645 - INFO - 正在获取********至********的数据
2025-06-05 08:01:34,645 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:01:34,645 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '55F124C671A810D95D921AE86540AAC8'}
2025-06-05 08:01:35,942 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:01:35,958 - INFO - 过滤后保留 400 条记录
2025-06-05 08:01:37,973 - INFO - 正在获取********至********的数据
2025-06-05 08:01:37,973 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-05 08:01:37,973 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6E3E321F5A6075C9489FD15F8B13D9C2'}
2025-06-05 08:01:38,833 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-05 08:01:38,833 - INFO - 过滤后保留 196 条记录
2025-06-05 08:01:40,848 - INFO - 开始保存数据到SQLite数据库，共 12883 条记录待处理
2025-06-05 08:01:41,254 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-01
2025-06-05 08:01:41,254 - INFO - 变更字段: recommend_amount: 0.0 -> 554.41, daily_bill_amount: 0.0 -> 554.41
2025-06-05 08:01:41,254 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-02
2025-06-05 08:01:41,254 - INFO - 变更字段: recommend_amount: 7059.0 -> 3067.0, amount: 7059 -> 3067
2025-06-05 08:01:41,254 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-01
2025-06-05 08:01:41,254 - INFO - 变更字段: recommend_amount: 6969.0 -> 4105.0, amount: 6969 -> 4105
2025-06-05 08:01:41,254 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-02
2025-06-05 08:01:41,254 - INFO - 变更字段: recommend_amount: 2651.96 -> 3381.0, amount: 2651 -> 3381, count: 178 -> 179, instore_amount: 758.94 -> 1487.98, instore_count: 47 -> 48
2025-06-05 08:01:41,254 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-01
2025-06-05 08:01:41,254 - INFO - 变更字段: recommend_amount: 3797.83 -> 3997.0, amount: 3797 -> 3997, count: 164 -> 165, instore_amount: 747.62 -> 946.79, instore_count: 48 -> 49
2025-06-05 08:01:41,270 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-02
2025-06-05 08:01:41,270 - INFO - 变更字段: recommend_amount: 0.0 -> 6898.1, daily_bill_amount: 0.0 -> 6898.1
2025-06-05 08:01:41,270 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-01
2025-06-05 08:01:41,270 - INFO - 变更字段: recommend_amount: 0.0 -> 10000.0, daily_bill_amount: 0.0 -> 10000.0
2025-06-05 08:01:41,270 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-02
2025-06-05 08:01:41,270 - INFO - 变更字段: recommend_amount: 9916.01 -> 10500.0, amount: 9916 -> 10500, count: 174 -> 175, instore_amount: 9322.93 -> 9906.92, instore_count: 165 -> 166
2025-06-05 08:01:41,270 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-01
2025-06-05 08:01:41,270 - INFO - 变更字段: recommend_amount: 12478.25 -> 10900.0, amount: 12478 -> 10900
2025-06-05 08:01:41,270 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-02
2025-06-05 08:01:41,270 - INFO - 变更字段: recommend_amount: 7796.64 -> 7186.92, daily_bill_amount: 7796.64 -> 7186.92
2025-06-05 08:01:41,270 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-01
2025-06-05 08:01:41,270 - INFO - 变更字段: recommend_amount: 9242.69 -> 8416.39, daily_bill_amount: 9242.69 -> 8416.39
2025-06-05 08:01:41,286 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-04
2025-06-05 08:01:41,286 - INFO - 变更字段: recommend_amount: 6707.0 -> 2561.0, amount: 6707 -> 2561
2025-06-05 08:01:41,286 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-03
2025-06-05 08:01:41,286 - INFO - 变更字段: recommend_amount: 6064.0 -> 3092.0, amount: 6064 -> 3092
2025-06-05 08:01:41,286 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-04
2025-06-05 08:01:41,286 - INFO - 变更字段: recommend_amount: 3092.07 -> 3924.0, amount: 3092 -> 3924, count: 219 -> 220, instore_amount: 828.56 -> 1660.49, instore_count: 35 -> 36
2025-06-05 08:01:41,286 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-03
2025-06-05 08:01:41,286 - INFO - 变更字段: recommend_amount: 3098.54 -> 4038.8, amount: 3098 -> 4038, count: 215 -> 216, instore_amount: 941.07 -> 1881.33, instore_count: 50 -> 51
2025-06-05 08:01:41,286 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-04
2025-06-05 08:01:41,286 - INFO - 变更字段: recommend_amount: 0.0 -> 8266.3, daily_bill_amount: 0.0 -> 8266.3
2025-06-05 08:01:41,286 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-03
2025-06-05 08:01:41,286 - INFO - 变更字段: recommend_amount: 0.0 -> 6102.5, daily_bill_amount: 0.0 -> 6102.5
2025-06-05 08:01:41,286 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-04
2025-06-05 08:01:41,286 - INFO - 变更字段: recommend_amount: 8438.55 -> 8637.0, amount: 8438 -> 8637, count: 142 -> 143, instore_amount: 7969.69 -> 8168.14, instore_count: 134 -> 135
2025-06-05 08:01:41,286 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-03
2025-06-05 08:01:41,286 - INFO - 变更字段: recommend_amount: 10603.52 -> 9561.0, amount: 10603 -> 9561
2025-06-05 08:01:41,286 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-04
2025-06-05 08:01:41,286 - INFO - 变更字段: recommend_amount: 7731.21 -> 6994.84, daily_bill_amount: 7731.21 -> 6994.84
2025-06-05 08:01:41,286 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-03
2025-06-05 08:01:41,286 - INFO - 变更字段: recommend_amount: 7366.52 -> 6622.05, daily_bill_amount: 7366.52 -> 6622.05
2025-06-05 08:01:41,301 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-06
2025-06-05 08:01:41,301 - INFO - 变更字段: recommend_amount: 749.31 -> 2524.45, daily_bill_amount: 749.31 -> 2524.45
2025-06-05 08:01:41,301 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-06
2025-06-05 08:01:41,301 - INFO - 变更字段: recommend_amount: 5640.0 -> 2977.0, amount: 5640 -> 2977
2025-06-05 08:01:41,301 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-05
2025-06-05 08:01:41,301 - INFO - 变更字段: recommend_amount: 5908.0 -> 3031.0, amount: 5908 -> 3031
2025-06-05 08:01:41,301 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-06
2025-06-05 08:01:41,317 - INFO - 变更字段: recommend_amount: 2135.82 -> 3231.0, amount: 2135 -> 3231, count: 195 -> 196, instore_amount: 271.76 -> 1366.94, instore_count: 21 -> 22
2025-06-05 08:01:41,317 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-05
2025-06-05 08:01:41,317 - INFO - 变更字段: recommend_amount: 3604.8 -> 4642.0, amount: 3604 -> 4642, count: 257 -> 258, instore_amount: 809.49 -> 1846.69, instore_count: 39 -> 40
2025-06-05 08:01:41,317 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-06
2025-06-05 08:01:41,317 - INFO - 变更字段: recommend_amount: 0.0 -> 5347.4, daily_bill_amount: 0.0 -> 5347.4
2025-06-05 08:01:41,317 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-05
2025-06-05 08:01:41,317 - INFO - 变更字段: recommend_amount: 0.0 -> 10123.5, daily_bill_amount: 0.0 -> 10123.5
2025-06-05 08:01:41,317 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-06
2025-06-05 08:01:41,317 - INFO - 变更字段: recommend_amount: 8834.81 -> 7939.0, amount: 8834 -> 7939
2025-06-05 08:01:41,317 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-05
2025-06-05 08:01:41,317 - INFO - 变更字段: recommend_amount: 9557.66 -> 8637.0, amount: 9557 -> 8637
2025-06-05 08:01:41,317 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-06
2025-06-05 08:01:41,317 - INFO - 变更字段: recommend_amount: 6136.03 -> 5559.64, daily_bill_amount: 6136.03 -> 5559.64
2025-06-05 08:01:41,317 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-05
2025-06-05 08:01:41,317 - INFO - 变更字段: recommend_amount: 7240.92 -> 6580.86, daily_bill_amount: 7240.92 -> 6580.86
2025-06-05 08:01:41,317 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-06
2025-06-05 08:01:41,317 - INFO - 变更字段: recommend_amount: 0.0 -> 5634.3, daily_bill_amount: 0.0 -> 5634.3
2025-06-05 08:01:41,333 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-08
2025-06-05 08:01:41,333 - INFO - 变更字段: recommend_amount: 1913.98 -> 2269.93, daily_bill_amount: 1913.98 -> 2269.93
2025-06-05 08:01:41,333 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-07
2025-06-05 08:01:41,333 - INFO - 变更字段: recommend_amount: 1964.28 -> 2697.65, daily_bill_amount: 1964.28 -> 2697.65
2025-06-05 08:01:41,333 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-08
2025-06-05 08:01:41,333 - INFO - 变更字段: recommend_amount: 5852.0 -> 2928.0, amount: 5852 -> 2928
2025-06-05 08:01:41,333 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-07
2025-06-05 08:01:41,333 - INFO - 变更字段: recommend_amount: 6643.0 -> 2438.0, amount: 6643 -> 2438
2025-06-05 08:01:41,333 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-08
2025-06-05 08:01:41,333 - INFO - 变更字段: recommend_amount: 2800.31 -> 3175.0, amount: 2800 -> 3175, count: 191 -> 192, instore_amount: 802.14 -> 1176.83, instore_count: 77 -> 78
2025-06-05 08:01:41,333 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-07
2025-06-05 08:01:41,333 - INFO - 变更字段: recommend_amount: 2610.52 -> 3618.0, amount: 2610 -> 3618, count: 191 -> 192, instore_amount: 612.36 -> 1619.84, instore_count: 46 -> 47
2025-06-05 08:01:41,333 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-07
2025-06-05 08:01:41,333 - INFO - 变更字段: recommend_amount: 0.0 -> 6455.3, daily_bill_amount: 0.0 -> 6455.3
2025-06-05 08:01:41,333 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-08
2025-06-05 08:01:41,333 - INFO - 变更字段: recommend_amount: 5348.05 -> 4544.0, amount: 5348 -> 4544
2025-06-05 08:01:41,333 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-07
2025-06-05 08:01:41,333 - INFO - 变更字段: recommend_amount: 7353.32 -> 167.0, amount: 7353 -> 167
2025-06-05 08:01:41,333 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-08
2025-06-05 08:01:41,333 - INFO - 变更字段: recommend_amount: 0.0 -> 5347.91, daily_bill_amount: 0.0 -> 5347.91
2025-06-05 08:01:41,333 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-07
2025-06-05 08:01:41,333 - INFO - 变更字段: recommend_amount: 0.0 -> 6394.21, daily_bill_amount: 0.0 -> 6394.21
2025-06-05 08:01:41,348 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-07
2025-06-05 08:01:41,348 - INFO - 变更字段: recommend_amount: 0.0 -> 6786.92, daily_bill_amount: 0.0 -> 6786.92
2025-06-05 08:01:41,364 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-10
2025-06-05 08:01:41,364 - INFO - 变更字段: recommend_amount: 891.25 -> 1339.25, daily_bill_amount: 891.25 -> 1339.25
2025-06-05 08:01:41,364 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-09
2025-06-05 08:01:41,364 - INFO - 变更字段: recommend_amount: 1800.26 -> 2222.19, daily_bill_amount: 1800.26 -> 2222.19
2025-06-05 08:01:41,364 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-10
2025-06-05 08:01:41,364 - INFO - 变更字段: recommend_amount: 5736.0 -> 2960.0, amount: 5736 -> 2960
2025-06-05 08:01:41,364 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-09
2025-06-05 08:01:41,364 - INFO - 变更字段: recommend_amount: 4711.0 -> 2211.0, amount: 4711 -> 2211
2025-06-05 08:01:41,364 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-10
2025-06-05 08:01:41,364 - INFO - 变更字段: recommend_amount: 2397.47 -> 2856.0, amount: 2397 -> 2856, count: 153 -> 154, instore_amount: 493.72 -> 952.25, instore_count: 33 -> 34
2025-06-05 08:01:41,364 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-09
2025-06-05 08:01:41,364 - INFO - 变更字段: recommend_amount: 3383.06 -> 3327.0, amount: 3383 -> 3327
2025-06-05 08:01:41,364 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-10
2025-06-05 08:01:41,364 - INFO - 变更字段: recommend_amount: 8384.25 -> 8373.0, amount: 8384 -> 8373
2025-06-05 08:01:41,364 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-09
2025-06-05 08:01:41,364 - INFO - 变更字段: recommend_amount: 7347.87 -> 6525.0, amount: 7347 -> 6525
2025-06-05 08:01:41,364 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-10
2025-06-05 08:01:41,364 - INFO - 变更字段: recommend_amount: 0.0 -> 7595.64, daily_bill_amount: 0.0 -> 7595.64
2025-06-05 08:01:41,364 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-09
2025-06-05 08:01:41,364 - INFO - 变更字段: recommend_amount: 0.0 -> 6959.27, daily_bill_amount: 0.0 -> 6959.27
2025-06-05 08:01:41,379 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-10
2025-06-05 08:01:41,379 - INFO - 变更字段: recommend_amount: 0.0 -> 2794.2, daily_bill_amount: 0.0 -> 2794.2
2025-06-05 08:01:41,379 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=6D1F9FC749FA44C6B70CA818C3E7FB77, sale_time=2025-05-11
2025-06-05 08:01:41,379 - INFO - 变更字段: recommend_amount: 0.0 -> 4718.0, daily_bill_amount: 0.0 -> 4718.0
2025-06-05 08:01:41,379 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-12
2025-06-05 08:01:41,379 - INFO - 变更字段: recommend_amount: 1971.5 -> 2614.36, daily_bill_amount: 1971.5 -> 2614.36
2025-06-05 08:01:41,379 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-12
2025-06-05 08:01:41,379 - INFO - 变更字段: recommend_amount: 6204.0 -> 2043.0, amount: 6204 -> 2043
2025-06-05 08:01:41,379 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-11
2025-06-05 08:01:41,379 - INFO - 变更字段: recommend_amount: 2487.0 -> 2047.0, amount: 2487 -> 2047
2025-06-05 08:01:41,379 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-12
2025-06-05 08:01:41,379 - INFO - 变更字段: recommend_amount: 2857.47 -> 2786.0, amount: 2857 -> 2786
2025-06-05 08:01:41,379 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-11
2025-06-05 08:01:41,379 - INFO - 变更字段: recommend_amount: 3542.11 -> 3676.0, amount: 3542 -> 3676, count: 158 -> 159, instore_amount: 598.58 -> 732.47, instore_count: 31 -> 32
2025-06-05 08:01:41,379 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-12
2025-06-05 08:01:41,379 - INFO - 变更字段: recommend_amount: 6988.14 -> 7375.0, amount: 6988 -> 7375, count: 144 -> 145, instore_amount: 6292.06 -> 6678.92, instore_count: 136 -> 137
2025-06-05 08:01:41,379 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-11
2025-06-05 08:01:41,379 - INFO - 变更字段: recommend_amount: 8660.33 -> 9565.0, amount: 8660 -> 9565, count: 159 -> 160, instore_amount: 7748.82 -> 8653.49, instore_count: 141 -> 142
2025-06-05 08:01:41,379 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-12
2025-06-05 08:01:41,379 - INFO - 变更字段: recommend_amount: 0.0 -> 6251.7, daily_bill_amount: 0.0 -> 6251.7
2025-06-05 08:01:41,379 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-11
2025-06-05 08:01:41,379 - INFO - 变更字段: recommend_amount: 0.0 -> 7556.91, daily_bill_amount: 0.0 -> 7556.91
2025-06-05 08:01:41,395 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-12
2025-06-05 08:01:41,395 - INFO - 变更字段: recommend_amount: 0.0 -> 4593.74, daily_bill_amount: 0.0 -> 4593.74
2025-06-05 08:01:41,395 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-11
2025-06-05 08:01:41,395 - INFO - 变更字段: recommend_amount: 0.0 -> 2334.21, daily_bill_amount: 0.0 -> 2334.21
2025-06-05 08:01:41,395 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-14
2025-06-05 08:01:41,395 - INFO - 变更字段: amount: 916 -> 1144, count: 3 -> 5, instore_amount: 916.9 -> 1144.9, instore_count: 3 -> 5
2025-06-05 08:01:41,411 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-14
2025-06-05 08:01:41,411 - INFO - 变更字段: recommend_amount: 2016.47 -> 2856.41, daily_bill_amount: 2016.47 -> 2856.41
2025-06-05 08:01:41,411 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-13
2025-06-05 08:01:41,411 - INFO - 变更字段: recommend_amount: 1918.02 -> 2630.08, daily_bill_amount: 1918.02 -> 2630.08
2025-06-05 08:01:41,411 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-14
2025-06-05 08:01:41,411 - INFO - 变更字段: recommend_amount: 4146.0 -> 1530.0, amount: 4146 -> 1530
2025-06-05 08:01:41,411 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-13
2025-06-05 08:01:41,411 - INFO - 变更字段: recommend_amount: 4254.0 -> 2044.0, amount: 4254 -> 2044
2025-06-05 08:01:41,411 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-14
2025-06-05 08:01:41,411 - INFO - 变更字段: recommend_amount: 2592.06 -> 3010.0, amount: 2592 -> 3010, count: 180 -> 181, instore_amount: 765.17 -> 1183.11, instore_count: 50 -> 51
2025-06-05 08:01:41,411 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-13
2025-06-05 08:01:41,411 - INFO - 变更字段: recommend_amount: 2967.21 -> 2813.0, amount: 2967 -> 2813
2025-06-05 08:01:41,411 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-14
2025-06-05 08:01:41,411 - INFO - 变更字段: recommend_amount: 6821.79 -> 6881.0, amount: 6821 -> 6881, count: 136 -> 137, instore_amount: 6674.89 -> 6734.1, instore_count: 132 -> 133
2025-06-05 08:01:41,411 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-13
2025-06-05 08:01:41,411 - INFO - 变更字段: recommend_amount: 9659.11 -> 9469.0, amount: 9659 -> 9469
2025-06-05 08:01:41,411 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-14
2025-06-05 08:01:41,411 - INFO - 变更字段: recommend_amount: 0.0 -> 7636.02, daily_bill_amount: 0.0 -> 7636.02
2025-06-05 08:01:41,411 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-13
2025-06-05 08:01:41,411 - INFO - 变更字段: recommend_amount: 0.0 -> 6716.79, daily_bill_amount: 0.0 -> 6716.79
2025-06-05 08:01:41,426 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-14
2025-06-05 08:01:41,426 - INFO - 变更字段: recommend_amount: 0.0 -> 5566.34, daily_bill_amount: 0.0 -> 5566.34
2025-06-05 08:01:41,426 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-16
2025-06-05 08:01:41,426 - INFO - 变更字段: recommend_amount: 1942.47 -> 2635.99, daily_bill_amount: 1942.47 -> 2635.99
2025-06-05 08:01:41,426 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-15
2025-06-05 08:01:41,426 - INFO - 变更字段: recommend_amount: 1896.54 -> 2581.52, daily_bill_amount: 1896.54 -> 2581.52
2025-06-05 08:01:41,426 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-16
2025-06-05 08:01:41,426 - INFO - 变更字段: recommend_amount: 4974.0 -> 1472.0, amount: 4974 -> 1472
2025-06-05 08:01:41,426 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-15
2025-06-05 08:01:41,426 - INFO - 变更字段: recommend_amount: 3649.0 -> 1246.0, amount: 3649 -> 1246
2025-06-05 08:01:41,426 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-16
2025-06-05 08:01:41,426 - INFO - 变更字段: recommend_amount: 2933.04 -> 3592.0, amount: 2933 -> 3592, count: 223 -> 224, instore_amount: 1000.52 -> 1659.48, instore_count: 114 -> 115
2025-06-05 08:01:41,426 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-15
2025-06-05 08:01:41,426 - INFO - 变更字段: recommend_amount: 2034.08 -> 2207.0, amount: 2034 -> 2207, count: 116 -> 117, instore_amount: 717.37 -> 890.28, instore_count: 34 -> 35
2025-06-05 08:01:41,442 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-16
2025-06-05 08:01:41,442 - INFO - 变更字段: recommend_amount: 6860.01 -> 6788.0, amount: 6860 -> 6788
2025-06-05 08:01:41,442 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-15
2025-06-05 08:01:41,442 - INFO - 变更字段: recommend_amount: 7817.8 -> 6425.0, amount: 7817 -> 6425
2025-06-05 08:01:41,442 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-16
2025-06-05 08:01:41,442 - INFO - 变更字段: recommend_amount: 0.0 -> 10124.49, daily_bill_amount: 0.0 -> 10124.49
2025-06-05 08:01:41,442 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-15
2025-06-05 08:01:41,442 - INFO - 变更字段: recommend_amount: 0.0 -> 5740.89, daily_bill_amount: 0.0 -> 5740.89
2025-06-05 08:01:41,442 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-18
2025-06-05 08:01:41,442 - INFO - 变更字段: amount: 554 -> 2098, count: 2 -> 9, instore_amount: 554.0 -> 2098.6, instore_count: 2 -> 9
2025-06-05 08:01:41,458 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-18
2025-06-05 08:01:41,458 - INFO - 变更字段: recommend_amount: 634.65 -> 647.45, daily_bill_amount: 634.65 -> 647.45
2025-06-05 08:01:41,458 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-17
2025-06-05 08:01:41,458 - INFO - 变更字段: recommend_amount: 508.0 -> 1239.7, daily_bill_amount: 508.0 -> 1239.7
2025-06-05 08:01:41,458 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-18
2025-06-05 08:01:41,458 - INFO - 变更字段: recommend_amount: 4514.0 -> 2881.0, amount: 4514 -> 2881
2025-06-05 08:01:41,458 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-17
2025-06-05 08:01:41,458 - INFO - 变更字段: recommend_amount: 5114.0 -> 2142.0, amount: 5114 -> 2142
2025-06-05 08:01:41,458 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-18
2025-06-05 08:01:41,458 - INFO - 变更字段: recommend_amount: 3125.42 -> 3384.0, amount: 3125 -> 3384, count: 177 -> 178, instore_amount: 942.56 -> 1201.14, instore_count: 73 -> 74
2025-06-05 08:01:41,458 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-17
2025-06-05 08:01:41,458 - INFO - 变更字段: recommend_amount: 3059.29 -> 3518.0, amount: 3059 -> 3518, count: 159 -> 160, instore_amount: 1131.68 -> 1590.39, instore_count: 57 -> 58
2025-06-05 08:01:41,458 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-18
2025-06-05 08:01:41,458 - INFO - 变更字段: recommend_amount: 11658.98 -> 10700.0, amount: 11658 -> 10700
2025-06-05 08:01:41,458 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-17
2025-06-05 08:01:41,458 - INFO - 变更字段: recommend_amount: 11553.37 -> 9933.0, amount: 11553 -> 9933
2025-06-05 08:01:41,458 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-18
2025-06-05 08:01:41,458 - INFO - 变更字段: recommend_amount: 0.0 -> 7797.24, daily_bill_amount: 0.0 -> 7797.24
2025-06-05 08:01:41,458 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-17
2025-06-05 08:01:41,458 - INFO - 变更字段: recommend_amount: 0.0 -> 8295.74, daily_bill_amount: 0.0 -> 8295.74
2025-06-05 08:01:41,473 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-18
2025-06-05 08:01:41,473 - INFO - 变更字段: recommend_amount: 0.0 -> 3494.59, daily_bill_amount: 0.0 -> 3494.59
2025-06-05 08:01:41,473 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-20
2025-06-05 08:01:41,473 - INFO - 变更字段: recommend_amount: 2576.43 -> 3443.12, daily_bill_amount: 2576.43 -> 3443.12
2025-06-05 08:01:41,473 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-19
2025-06-05 08:01:41,473 - INFO - 变更字段: recommend_amount: 2419.54 -> 2491.9, daily_bill_amount: 2419.54 -> 2491.9
2025-06-05 08:01:41,473 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-20
2025-06-05 08:01:41,473 - INFO - 变更字段: recommend_amount: 4281.0 -> 1730.0, amount: 4281 -> 1730
2025-06-05 08:01:41,489 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-19
2025-06-05 08:01:41,489 - INFO - 变更字段: recommend_amount: 5462.0 -> 2782.0, amount: 5462 -> 2782
2025-06-05 08:01:41,489 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-20
2025-06-05 08:01:41,489 - INFO - 变更字段: recommend_amount: 3343.19 -> 3805.0, amount: 3343 -> 3805, count: 197 -> 198, instore_amount: 964.48 -> 1426.29, instore_count: 43 -> 44
2025-06-05 08:01:41,489 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-19
2025-06-05 08:01:41,489 - INFO - 变更字段: recommend_amount: 2025.87 -> 2230.0, amount: 2025 -> 2230, count: 134 -> 135, instore_amount: 562.34 -> 766.47, instore_count: 38 -> 39
2025-06-05 08:01:41,489 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-20
2025-06-05 08:01:41,489 - INFO - 变更字段: recommend_amount: 9263.94 -> 6927.0, amount: 9263 -> 6927
2025-06-05 08:01:41,489 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-19
2025-06-05 08:01:41,489 - INFO - 变更字段: recommend_amount: 7557.54 -> 6913.0, amount: 7557 -> 6913
2025-06-05 08:01:41,489 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-20
2025-06-05 08:01:41,489 - INFO - 变更字段: recommend_amount: 0.0 -> 6878.84, daily_bill_amount: 0.0 -> 6878.84
2025-06-05 08:01:41,489 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-19
2025-06-05 08:01:41,489 - INFO - 变更字段: recommend_amount: 0.0 -> 5429.16, daily_bill_amount: 0.0 -> 5429.16
2025-06-05 08:01:41,505 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-20
2025-06-05 08:01:41,505 - INFO - 变更字段: recommend_amount: 0.0 -> 6139.72, daily_bill_amount: 0.0 -> 6139.72
2025-06-05 08:01:41,505 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-22
2025-06-05 08:01:41,505 - INFO - 变更字段: recommend_amount: 1942.69 -> 2399.79, daily_bill_amount: 1942.69 -> 2399.79
2025-06-05 08:01:41,505 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-21
2025-06-05 08:01:41,505 - INFO - 变更字段: recommend_amount: 2199.04 -> 3361.68, daily_bill_amount: 2199.04 -> 3361.68
2025-06-05 08:01:41,505 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-22
2025-06-05 08:01:41,505 - INFO - 变更字段: recommend_amount: 4968.0 -> 2195.0, amount: 4968 -> 2195
2025-06-05 08:01:41,505 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-21
2025-06-05 08:01:41,505 - INFO - 变更字段: recommend_amount: 4852.0 -> 1665.0, amount: 4852 -> 1665
2025-06-05 08:01:41,505 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-22
2025-06-05 08:01:41,505 - INFO - 变更字段: recommend_amount: 2311.52 -> 2410.0, amount: 2311 -> 2410, count: 100 -> 101, instore_amount: 783.54 -> 882.02, instore_count: 16 -> 17
2025-06-05 08:01:41,505 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-21
2025-06-05 08:01:41,505 - INFO - 变更字段: recommend_amount: 2513.57 -> 2787.0, amount: 2513 -> 2787, count: 132 -> 133, instore_amount: 657.16 -> 930.58, instore_count: 28 -> 29
2025-06-05 08:01:41,505 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-22
2025-06-05 08:01:41,505 - INFO - 变更字段: recommend_amount: 9636.74 -> 5604.0, amount: 9636 -> 5604
2025-06-05 08:01:41,505 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-21
2025-06-05 08:01:41,505 - INFO - 变更字段: recommend_amount: 8235.53 -> 7526.0, amount: 8235 -> 7526
2025-06-05 08:01:41,505 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-22
2025-06-05 08:01:41,505 - INFO - 变更字段: recommend_amount: 0.0 -> 5062.11, daily_bill_amount: 0.0 -> 5062.11
2025-06-05 08:01:41,505 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-21
2025-06-05 08:01:41,505 - INFO - 变更字段: recommend_amount: 0.0 -> 6633.79, daily_bill_amount: 0.0 -> 6633.79
2025-06-05 08:01:41,520 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-21
2025-06-05 08:01:41,520 - INFO - 变更字段: amount: 5936 -> 6056, count: 106 -> 108, instore_amount: 5110.64 -> 5230.24, instore_count: 85 -> 87
2025-06-05 08:01:41,536 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=6D1F9FC749FA44C6B70CA818C3E7FB77, sale_time=2025-05-24
2025-06-05 08:01:41,536 - INFO - 变更字段: recommend_amount: 0.0 -> 1556.0, daily_bill_amount: 0.0 -> 1556.0
2025-06-05 08:01:41,536 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-23
2025-06-05 08:01:41,536 - INFO - 变更字段: recommend_amount: 1362.14 -> 639.84, daily_bill_amount: 1362.14 -> 639.84
2025-06-05 08:01:41,536 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-24
2025-06-05 08:01:41,536 - INFO - 变更字段: recommend_amount: 5030.0 -> 4046.0, amount: 5030 -> 4046
2025-06-05 08:01:41,536 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-23
2025-06-05 08:01:41,536 - INFO - 变更字段: recommend_amount: 5108.0 -> 2676.0, amount: 5108 -> 2676
2025-06-05 08:01:41,536 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-24
2025-06-05 08:01:41,536 - INFO - 变更字段: recommend_amount: 2814.81 -> 3223.0, amount: 2814 -> 3223, count: 153 -> 154, instore_amount: 732.79 -> 1140.98, instore_count: 28 -> 29
2025-06-05 08:01:41,536 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-23
2025-06-05 08:01:41,536 - INFO - 变更字段: recommend_amount: 2044.92 -> 2407.0, amount: 2044 -> 2407, count: 113 -> 114, instore_amount: 499.49 -> 861.57, instore_count: 19 -> 20
2025-06-05 08:01:41,536 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-24
2025-06-05 08:01:41,536 - INFO - 变更字段: recommend_amount: 9872.44 -> 8790.0, amount: 9872 -> 8790
2025-06-05 08:01:41,536 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-23
2025-06-05 08:01:41,536 - INFO - 变更字段: recommend_amount: 7082.02 -> 5805.0, amount: 7082 -> 5805
2025-06-05 08:01:41,536 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-24
2025-06-05 08:01:41,536 - INFO - 变更字段: recommend_amount: 0.0 -> 7163.41, daily_bill_amount: 0.0 -> 7163.41
2025-06-05 08:01:41,536 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-23
2025-06-05 08:01:41,536 - INFO - 变更字段: recommend_amount: 0.0 -> 6273.41, daily_bill_amount: 0.0 -> 6273.41
2025-06-05 08:01:41,551 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-26
2025-06-05 08:01:41,551 - INFO - 变更字段: recommend_amount: 2121.66 -> 2237.27, daily_bill_amount: 2121.66 -> 2237.27
2025-06-05 08:01:41,551 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-26
2025-06-05 08:01:41,551 - INFO - 变更字段: recommend_amount: 5809.0 -> 844.0, amount: 5809 -> 844
2025-06-05 08:01:41,551 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-25
2025-06-05 08:01:41,551 - INFO - 变更字段: recommend_amount: 3833.0 -> 3007.0, amount: 3833 -> 3007
2025-06-05 08:01:41,551 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-26
2025-06-05 08:01:41,551 - INFO - 变更字段: recommend_amount: 1802.76 -> 2220.0, amount: 1802 -> 2220, count: 113 -> 114, instore_amount: 367.35 -> 784.59, instore_count: 17 -> 18
2025-06-05 08:01:41,551 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-25
2025-06-05 08:01:41,551 - INFO - 变更字段: recommend_amount: 2774.9 -> 3164.0, amount: 2774 -> 3164, count: 140 -> 141, instore_amount: 561.2 -> 950.3, instore_count: 24 -> 25
2025-06-05 08:01:41,551 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-26
2025-06-05 08:01:41,551 - INFO - 变更字段: recommend_amount: 8941.35 -> 7795.62, amount: 8941 -> 7795
2025-06-05 08:01:41,551 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-25
2025-06-05 08:01:41,551 - INFO - 变更字段: recommend_amount: 9712.59 -> 8814.0, amount: 9712 -> 8814
2025-06-05 08:01:41,551 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-26
2025-06-05 08:01:41,551 - INFO - 变更字段: recommend_amount: 0.0 -> 6090.9, daily_bill_amount: 0.0 -> 6090.9
2025-06-05 08:01:41,551 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-25
2025-06-05 08:01:41,551 - INFO - 变更字段: recommend_amount: 0.0 -> 6010.51, daily_bill_amount: 0.0 -> 6010.51
2025-06-05 08:01:41,567 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-26
2025-06-05 08:01:41,567 - INFO - 变更字段: recommend_amount: 0.0 -> 6198.69, daily_bill_amount: 0.0 -> 6198.69
2025-06-05 08:01:41,567 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-25
2025-06-05 08:01:41,567 - INFO - 变更字段: recommend_amount: 0.0 -> 2387.29, daily_bill_amount: 0.0 -> 2387.29
2025-06-05 08:01:41,567 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HVORJ88U7D2IL1AIB692RTFU8001185, sale_time=2025-05-28
2025-06-05 08:01:41,567 - INFO - 变更字段: amount: 3426 -> 3374
2025-06-05 08:01:41,583 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-28
2025-06-05 08:01:41,583 - INFO - 变更字段: recommend_amount: 1697.49 -> 2888.26, daily_bill_amount: 1697.49 -> 2888.26
2025-06-05 08:01:41,583 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-27
2025-06-05 08:01:41,583 - INFO - 变更字段: recommend_amount: 1725.86 -> 2149.49, daily_bill_amount: 1725.86 -> 2149.49
2025-06-05 08:01:41,583 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-28
2025-06-05 08:01:41,583 - INFO - 变更字段: recommend_amount: 3855.0 -> 1727.0, amount: 3855 -> 1727
2025-06-05 08:01:41,583 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-27
2025-06-05 08:01:41,583 - INFO - 变更字段: recommend_amount: 4012.0 -> 1771.0, amount: 4012 -> 1771
2025-06-05 08:01:41,583 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-28
2025-06-05 08:01:41,583 - INFO - 变更字段: recommend_amount: 2595.97 -> 2815.0, amount: 2595 -> 2815, count: 144 -> 145, instore_amount: 513.26 -> 732.29, instore_count: 23 -> 24
2025-06-05 08:01:41,583 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-27
2025-06-05 08:01:41,583 - INFO - 变更字段: recommend_amount: 1932.23 -> 2397.0, amount: 1932 -> 2397, count: 112 -> 113, instore_amount: 438.93 -> 903.7, instore_count: 20 -> 21
2025-06-05 08:01:41,583 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-28
2025-06-05 08:01:41,583 - INFO - 变更字段: recommend_amount: 11498.33 -> 10000.0, amount: 11498 -> 10000
2025-06-05 08:01:41,583 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-27
2025-06-05 08:01:41,583 - INFO - 变更字段: recommend_amount: 7792.33 -> 7020.0, amount: 7792 -> 7020
2025-06-05 08:01:41,583 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-28
2025-06-05 08:01:41,583 - INFO - 变更字段: recommend_amount: 0.0 -> 5681.69, daily_bill_amount: 0.0 -> 5681.69
2025-06-05 08:01:41,583 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-27
2025-06-05 08:01:41,583 - INFO - 变更字段: recommend_amount: 0.0 -> 5571.9, daily_bill_amount: 0.0 -> 5571.9
2025-06-05 08:01:41,598 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-28
2025-06-05 08:01:41,598 - INFO - 变更字段: recommend_amount: 0.0 -> 5475.73, daily_bill_amount: 0.0 -> 5475.73
2025-06-05 08:01:41,598 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-27
2025-06-05 08:01:41,598 - INFO - 变更字段: recommend_amount: 0.0 -> 5328.21, daily_bill_amount: 0.0 -> 5328.21
2025-06-05 08:01:41,598 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-30
2025-06-05 08:01:41,598 - INFO - 变更字段: recommend_amount: 1738.89 -> 746.64, daily_bill_amount: 1738.89 -> 746.64
2025-06-05 08:01:41,598 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-29
2025-06-05 08:01:41,598 - INFO - 变更字段: recommend_amount: 1503.57 -> 969.64, daily_bill_amount: 1503.57 -> 969.64
2025-06-05 08:01:41,598 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-30
2025-06-05 08:01:41,598 - INFO - 变更字段: recommend_amount: 7762.0 -> 3144.0, amount: 7762 -> 3144
2025-06-05 08:01:41,598 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-29
2025-06-05 08:01:41,598 - INFO - 变更字段: recommend_amount: 4497.0 -> 534.7, amount: 4497 -> 534
2025-06-05 08:01:41,598 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-30
2025-06-05 08:01:41,614 - INFO - 变更字段: recommend_amount: 2736.73 -> 4428.0, amount: 2736 -> 4428, count: 162 -> 163, instore_amount: 448.02 -> 2139.29, instore_count: 23 -> 24
2025-06-05 08:01:41,614 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-29
2025-06-05 08:01:41,614 - INFO - 变更字段: recommend_amount: 2775.06 -> 3027.0, amount: 2775 -> 3027, count: 137 -> 138, instore_amount: 570.24 -> 822.18, instore_count: 28 -> 29
2025-06-05 08:01:41,614 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-30
2025-06-05 08:01:41,614 - INFO - 变更字段: recommend_amount: 13516.25 -> 12700.0, amount: 13516 -> 12700
2025-06-05 08:01:41,614 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-29
2025-06-05 08:01:41,614 - INFO - 变更字段: recommend_amount: 10526.62 -> 8377.0, amount: 10526 -> 8377
2025-06-05 08:01:41,614 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-30
2025-06-05 08:01:41,614 - INFO - 变更字段: recommend_amount: 0.0 -> 7608.26, daily_bill_amount: 0.0 -> 7608.26
2025-06-05 08:01:41,614 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-29
2025-06-05 08:01:41,614 - INFO - 变更字段: recommend_amount: 0.0 -> 6235.27, daily_bill_amount: 0.0 -> 6235.27
2025-06-05 08:01:41,614 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-30
2025-06-05 08:01:41,614 - INFO - 变更字段: recommend_amount: 0.0 -> 5899.85, daily_bill_amount: 0.0 -> 5899.85
2025-06-05 08:01:41,629 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-29
2025-06-05 08:01:41,629 - INFO - 变更字段: recommend_amount: 0.0 -> 6950.29, daily_bill_amount: 0.0 -> 6950.29
2025-06-05 08:01:41,629 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIP3GSKTFR6E7AERKQ83J2001UN4, sale_time=2025-05-29
2025-06-05 08:01:41,629 - INFO - 变更字段: amount: 2228 -> 2433, count: 14 -> 15, instore_amount: 2497.7 -> 2702.7, instore_count: 14 -> 15
2025-06-05 08:01:41,629 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGETHMN6B8P42F6DB81RHC2001P8A, sale_time=2025-05-31
2025-06-05 08:01:41,629 - INFO - 变更字段: recommend_amount: 6177.0 -> 3844.0, amount: 6177 -> 3844
2025-06-05 08:01:41,629 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-31
2025-06-05 08:01:41,629 - INFO - 变更字段: recommend_amount: 3897.1 -> 3958.0, amount: 3897 -> 3957, count: 212 -> 213, instore_amount: 924.18 -> 985.08, instore_count: 44 -> 45
2025-06-05 08:01:41,629 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-31
2025-06-05 08:01:41,629 - INFO - 变更字段: recommend_amount: 10380.6 -> 9973.0, amount: 10380 -> 9973
2025-06-05 08:01:41,629 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-31
2025-06-05 08:01:41,629 - INFO - 变更字段: recommend_amount: 0.0 -> 5695.93, daily_bill_amount: 0.0 -> 5695.93
2025-06-05 08:01:41,645 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOM98AG2E0I86N3H2U1V8001E8E, sale_time=2025-06-01
2025-06-05 08:01:41,645 - INFO - 变更字段: count: 66 -> 67, instore_count: 66 -> 67
2025-06-05 08:01:41,645 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOM98AG2E0I86N3H2U1V8001E8E, sale_time=2025-05-31
2025-06-05 08:01:41,645 - INFO - 变更字段: recommend_amount: 22752.3 -> 22832.2, amount: 22752 -> 22832, count: 72 -> 73, instore_amount: 23071.3 -> 23151.2, instore_count: 72 -> 73
2025-06-05 08:01:41,645 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-06-01
2025-06-05 08:01:41,645 - INFO - 变更字段: recommend_amount: 0.0 -> 3950.17, daily_bill_amount: 0.0 -> 3950.17
2025-06-05 08:01:41,645 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-05-31
2025-06-05 08:01:41,645 - INFO - 变更字段: recommend_amount: 0.0 -> 2024.05, daily_bill_amount: 0.0 -> 2024.05
2025-06-05 08:01:41,645 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-06-03
2025-06-05 08:01:41,645 - INFO - 变更字段: recommend_amount: 0.0 -> 2669.5, daily_bill_amount: 0.0 -> 2669.5
2025-06-05 08:01:41,645 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=6692283A183D432BAE322E1032539CE8, sale_time=2025-06-03
2025-06-05 08:01:41,645 - INFO - 变更字段: daily_bill_amount: 0.0 -> 11492.0
2025-06-05 08:01:41,645 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE2TORA7KI7Q2OV4FVC7FC0014BT, sale_time=2025-06-03
2025-06-05 08:01:41,645 - INFO - 变更字段: amount: 1419 -> 1449, count: 44 -> 45, online_amount: 127.45 -> 157.05, online_count: 4 -> 5
2025-06-05 08:01:41,645 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDTG15Q4SH7Q2OV4FVC7CO001499, sale_time=2025-06-03
2025-06-05 08:01:41,645 - INFO - 变更字段: amount: 2964 -> 2973, count: 136 -> 137, online_amount: 1662.1 -> 1670.4, online_count: 66 -> 67
2025-06-05 08:01:41,661 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDQDVL1EG67Q2OV4FVC7B800147P, sale_time=2025-06-03
2025-06-05 08:01:41,661 - INFO - 变更字段: amount: 823 -> 831, count: 48 -> 50, online_amount: 610.7 -> 618.81, online_count: 38 -> 40
2025-06-05 08:01:41,661 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-06-03
2025-06-05 08:01:41,661 - INFO - 变更字段: amount: 115 -> 404, count: 1 -> 2, instore_amount: 115.0 -> 404.0, instore_count: 1 -> 2
2025-06-05 08:01:41,661 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-06-03
2025-06-05 08:01:41,661 - INFO - 变更字段: recommend_amount: 1718.96 -> 1785.06, amount: 1718 -> 1785, count: 115 -> 121, instore_amount: 475.95 -> 486.95, instore_count: 22 -> 23, online_amount: 1243.01 -> 1298.11, online_count: 93 -> 98
2025-06-05 08:01:41,661 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-06-03
2025-06-05 08:01:41,661 - INFO - 变更字段: recommend_amount: 8630.84 -> 8702.24, amount: 8630 -> 8702, count: 155 -> 158, instore_amount: 8050.2 -> 8121.6, instore_count: 137 -> 140
2025-06-05 08:01:41,661 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-06-02
2025-06-05 08:01:41,661 - INFO - 变更字段: recommend_amount: 7176.74 -> 7281.44, amount: 7176 -> 7281, count: 130 -> 131, instore_amount: 6325.45 -> 6430.15, instore_count: 110 -> 111
2025-06-05 08:01:41,661 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-06-03
2025-06-05 08:01:41,661 - INFO - 变更字段: amount: 2462 -> 2502, count: 139 -> 142, instore_amount: 426.46 -> 433.97, instore_count: 43 -> 44, online_amount: 2161.0 -> 2193.0, online_count: 96 -> 98
2025-06-05 08:01:41,661 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-06-03
2025-06-05 08:01:41,661 - INFO - 变更字段: recommend_amount: 853.48 -> 877.68, amount: 853 -> 877, count: 63 -> 64, instore_amount: 176.7 -> 192.7, instore_count: 11 -> 12, online_amount: 689.38 -> 697.58
2025-06-05 08:01:41,661 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-06-02
2025-06-05 08:01:41,661 - INFO - 变更字段: recommend_amount: 5774.61 -> 5782.41, amount: 5774 -> 5782, count: 294 -> 295, online_amount: 4339.62 -> 4347.42, online_count: 229 -> 230
2025-06-05 08:01:41,661 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-06-03
2025-06-05 08:01:41,661 - INFO - 变更字段: recommend_amount: 0.0 -> 623.0, daily_bill_amount: 0.0 -> 623.0
2025-06-05 08:01:41,661 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEC0U8246I0I86N3H2U10A001F9G, sale_time=2025-06-03
2025-06-05 08:01:41,661 - INFO - 变更字段: amount: 3242 -> 3374, count: 63 -> 67, instore_amount: 2001.69 -> 2134.42, instore_count: 13 -> 17
2025-06-05 08:01:41,661 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-06-03
2025-06-05 08:01:41,661 - INFO - 变更字段: recommend_amount: 0.0 -> 13147.19, daily_bill_amount: 0.0 -> 13147.19
2025-06-05 08:01:41,661 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-06-03
2025-06-05 08:01:41,661 - INFO - 变更字段: amount: 20458 -> 20658, count: 163 -> 164, instore_amount: 6409.5 -> 6609.5, instore_count: 34 -> 35
2025-06-05 08:01:41,661 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVBILA3OJ0I86N3H2U1PQ001F30, sale_time=2025-06-03
2025-06-05 08:01:41,661 - INFO - 变更字段: amount: 9695 -> 9888, count: 216 -> 218, instore_amount: 6737.76 -> 6780.96, instore_count: 43 -> 44, online_amount: 3093.37 -> 3243.57, online_count: 173 -> 174
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDF8HFHI690I86N3H2U1H9001EQF, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: amount: 1488 -> 1276, count: 5 -> 6, instore_amount: 1488.0 -> 1615.0, instore_count: 5 -> 6
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEQ2M9E710I86N3H2U1H1001EQ7, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: recommend_amount: 0.0 -> 2004.4, daily_bill_amount: 0.0 -> 2004.4
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9D9OBM41R0I86N3H2U17K001EGQ, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: recommend_amount: 0.0 -> 2512.6, daily_bill_amount: 0.0 -> 2512.6
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: amount: 3742 -> 3788, count: 236 -> 243, online_amount: 3824.62 -> 3870.82, online_count: 232 -> 239
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: amount: 4482 -> 4461, count: 286 -> 288, instore_amount: 3064.25 -> 3069.65, instore_count: 185 -> 190, online_amount: 1483.7 -> 1481.4, online_count: 101 -> 98
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-02
2025-06-05 08:01:41,676 - INFO - 变更字段: instore_amount: 4974.09 -> 4977.09, instore_count: 322 -> 323, online_amount: 1802.1 -> 1799.1, online_count: 134 -> 133
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: amount: 4094 -> 4235, count: 68 -> 71, online_amount: 1017.99 -> 1158.49, online_count: 23 -> 26
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDNMK1P3900I86N3H2U1UO001E7U, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: amount: 4632 -> 4905, count: 80 -> 85, instore_amount: 3838.35 -> 3877.35, instore_count: 65 -> 66, online_amount: 794.48 -> 1028.38, online_count: 15 -> 19
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDNMK1P3900I86N3H2U1UO001E7U, sale_time=2025-06-02
2025-06-05 08:01:41,676 - INFO - 变更字段: amount: 7102 -> 7182, count: 169 -> 170, instore_amount: 7135.26 -> 7215.06, instore_count: 165 -> 166
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: amount: 1620 -> 2008, count: 16 -> 17, instore_amount: 388.0 -> 776.0, instore_count: 1 -> 2
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: recommend_amount: 0.0 -> 24357.02, daily_bill_amount: 0.0 -> 24357.02
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0NLURMV9D3IKSIOCDI7R2001G23, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: recommend_amount: 0.0 -> 3209.97, daily_bill_amount: 0.0 -> 3209.97, amount: 550 -> 573, count: 17 -> 18, instore_amount: 426.78 -> 449.08, instore_count: 12 -> 13
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: amount: 11586 -> 11615, count: 167 -> 169, instore_amount: 10250.72 -> 10279.6, instore_count: 101 -> 103
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE38FF3LOL6AJB6QM8HA820011R0, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: amount: 8914 -> 10970, count: 25 -> 26, instore_amount: 8836.0 -> 10892.0, instore_count: 24 -> 25
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: amount: 25411 -> 30396, count: 114 -> 116, instore_amount: 25758.21 -> 30743.21, instore_count: 51 -> 53
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: amount: 19394 -> 20868, count: 105 -> 107, instore_amount: 17989.34 -> 19463.34, instore_count: 83 -> 85
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-06-03
2025-06-05 08:01:41,676 - INFO - 变更字段: recommend_amount: 0.0 -> 5194.72, daily_bill_amount: 0.0 -> 5194.72
2025-06-05 08:01:41,676 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-06-02
2025-06-05 08:01:41,692 - INFO - 变更字段: recommend_amount: 0.0 -> 1432.11, daily_bill_amount: 0.0 -> 1432.11
2025-06-05 08:01:41,692 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-03
2025-06-05 08:01:41,692 - INFO - 变更字段: amount: 994 -> 970
2025-06-05 08:01:41,926 - INFO - SQLite数据保存完成，统计信息：
2025-06-05 08:01:41,926 - INFO - - 总记录数: 12883
2025-06-05 08:01:41,926 - INFO - - 成功插入: 204
2025-06-05 08:01:41,926 - INFO - - 成功更新: 215
2025-06-05 08:01:41,926 - INFO - - 无需更新: 12464
2025-06-05 08:01:41,926 - INFO - - 处理失败: 0
2025-06-05 08:01:47,317 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_20250605.xlsx
2025-06-05 08:01:47,333 - INFO - 成功获取数衍平台数据，共 12883 条记录
2025-06-05 08:01:47,333 - INFO - 正在更新SQLite月度汇总数据...
2025-06-05 08:01:47,333 - INFO - 月度数据sqllite清空完成
2025-06-05 08:01:47,614 - INFO - 月度汇总数据更新完成，处理了 1402 条汇总记录
2025-06-05 08:01:47,614 - INFO - 成功更新月度汇总数据，共 1402 条记录
2025-06-05 08:01:47,614 - INFO - 正在获取宜搭日销售表单数据...
2025-06-05 08:01:47,614 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-04-05 00:00:00 至 2025-06-04 23:59:59
2025-06-05 08:01:47,614 - INFO - 查询分段 1: 2025-04-05 至 2025-04-06
2025-06-05 08:01:47,614 - INFO - 查询日期范围: 2025-04-05 至 2025-04-06，使用分页查询，每页 100 条记录
2025-06-05 08:01:47,614 - INFO - Request Parameters - Page 1:
2025-06-05 08:01:47,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:01:47,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743782400614, 1743868800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:01:55,770 - ERROR - API请求失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9BE5ED52-2D5A-77D3-8C1A-FD995C0E5C6F Response: {'code': 'ServiceUnavailable', 'requestid': '9BE5ED52-2D5A-77D3-8C1A-FD995C0E5C6F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-05 08:01:55,770 - ERROR - 服务不可用，将等待后重试
2025-06-05 08:01:55,770 - ERROR - 获取第 1 页数据时出错: 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9BE5ED52-2D5A-77D3-8C1A-FD995C0E5C6F Response: {'code': 'ServiceUnavailable', 'requestid': '9BE5ED52-2D5A-77D3-8C1A-FD995C0E5C6F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-05 08:01:55,770 - WARNING - 服务暂时不可用，等待 6 秒后重试...
2025-06-05 08:02:01,786 - WARNING - 服务暂时不可用，将等待更长时间: 10秒
2025-06-05 08:02:01,786 - WARNING - 获取表单数据失败 (尝试 1/3): 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9BE5ED52-2D5A-77D3-8C1A-FD995C0E5C6F Response: {'code': 'ServiceUnavailable', 'requestid': '9BE5ED52-2D5A-77D3-8C1A-FD995C0E5C6F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}，将在 10 秒后重试...
2025-06-05 08:02:11,801 - INFO - Request Parameters - Page 1:
2025-06-05 08:02:11,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:11,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743782400614, 1743868800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:16,723 - INFO - API请求耗时: 4922ms
2025-06-05 08:02:16,723 - INFO - Response - Page 1
2025-06-05 08:02:16,723 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:02:17,239 - INFO - Request Parameters - Page 2:
2025-06-05 08:02:17,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:17,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743782400614, 1743868800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:17,989 - INFO - API请求耗时: 750ms
2025-06-05 08:02:17,989 - INFO - Response - Page 2
2025-06-05 08:02:17,989 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:02:18,489 - INFO - Request Parameters - Page 3:
2025-06-05 08:02:18,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:18,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743782400614, 1743868800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:19,286 - INFO - API请求耗时: 797ms
2025-06-05 08:02:19,286 - INFO - Response - Page 3
2025-06-05 08:02:19,286 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:02:19,801 - INFO - Request Parameters - Page 4:
2025-06-05 08:02:19,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:19,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743782400614, 1743868800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:20,629 - INFO - API请求耗时: 828ms
2025-06-05 08:02:20,629 - INFO - Response - Page 4
2025-06-05 08:02:20,629 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:02:21,145 - INFO - Request Parameters - Page 5:
2025-06-05 08:02:21,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:21,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743782400614, 1743868800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:21,692 - INFO - API请求耗时: 547ms
2025-06-05 08:02:21,692 - INFO - Response - Page 5
2025-06-05 08:02:21,692 - INFO - 第 5 页获取到 30 条记录
2025-06-05 08:02:21,692 - INFO - 查询完成，共获取到 430 条记录
2025-06-05 08:02:21,692 - INFO - 分段 1 查询成功，获取到 430 条记录
2025-06-05 08:02:22,707 - INFO - 查询分段 2: 2025-04-07 至 2025-04-08
2025-06-05 08:02:22,707 - INFO - 查询日期范围: 2025-04-07 至 2025-04-08，使用分页查询，每页 100 条记录
2025-06-05 08:02:22,707 - INFO - Request Parameters - Page 1:
2025-06-05 08:02:22,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:22,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200614, 1744041600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:23,410 - INFO - API请求耗时: 703ms
2025-06-05 08:02:23,410 - INFO - Response - Page 1
2025-06-05 08:02:23,410 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:02:23,926 - INFO - Request Parameters - Page 2:
2025-06-05 08:02:23,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:23,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200614, 1744041600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:24,629 - INFO - API请求耗时: 703ms
2025-06-05 08:02:24,629 - INFO - Response - Page 2
2025-06-05 08:02:24,629 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:02:25,129 - INFO - Request Parameters - Page 3:
2025-06-05 08:02:25,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:25,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200614, 1744041600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:25,832 - INFO - API请求耗时: 703ms
2025-06-05 08:02:25,832 - INFO - Response - Page 3
2025-06-05 08:02:25,832 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:02:26,332 - INFO - Request Parameters - Page 4:
2025-06-05 08:02:26,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:26,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200614, 1744041600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:27,067 - INFO - API请求耗时: 734ms
2025-06-05 08:02:27,067 - INFO - Response - Page 4
2025-06-05 08:02:27,067 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:02:27,567 - INFO - Request Parameters - Page 5:
2025-06-05 08:02:27,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:27,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200614, 1744041600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:28,207 - INFO - API请求耗时: 641ms
2025-06-05 08:02:28,207 - INFO - Response - Page 5
2025-06-05 08:02:28,207 - INFO - 第 5 页获取到 28 条记录
2025-06-05 08:02:28,207 - INFO - 查询完成，共获取到 428 条记录
2025-06-05 08:02:28,207 - INFO - 分段 2 查询成功，获取到 428 条记录
2025-06-05 08:02:29,223 - INFO - 查询分段 3: 2025-04-09 至 2025-04-10
2025-06-05 08:02:29,223 - INFO - 查询日期范围: 2025-04-09 至 2025-04-10，使用分页查询，每页 100 条记录
2025-06-05 08:02:29,223 - INFO - Request Parameters - Page 1:
2025-06-05 08:02:29,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:29,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000614, 1744214400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:29,895 - INFO - API请求耗时: 672ms
2025-06-05 08:02:29,895 - INFO - Response - Page 1
2025-06-05 08:02:29,895 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:02:30,395 - INFO - Request Parameters - Page 2:
2025-06-05 08:02:30,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:30,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000614, 1744214400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:31,176 - INFO - API请求耗时: 781ms
2025-06-05 08:02:31,176 - INFO - Response - Page 2
2025-06-05 08:02:31,176 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:02:31,676 - INFO - Request Parameters - Page 3:
2025-06-05 08:02:31,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:31,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000614, 1744214400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:32,442 - INFO - API请求耗时: 766ms
2025-06-05 08:02:32,442 - INFO - Response - Page 3
2025-06-05 08:02:32,457 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:02:32,957 - INFO - Request Parameters - Page 4:
2025-06-05 08:02:32,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:32,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000614, 1744214400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:33,692 - INFO - API请求耗时: 734ms
2025-06-05 08:02:33,692 - INFO - Response - Page 4
2025-06-05 08:02:33,692 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:02:34,192 - INFO - Request Parameters - Page 5:
2025-06-05 08:02:34,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:34,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000614, 1744214400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:34,707 - INFO - API请求耗时: 516ms
2025-06-05 08:02:34,707 - INFO - Response - Page 5
2025-06-05 08:02:34,707 - INFO - 第 5 页获取到 22 条记录
2025-06-05 08:02:34,707 - INFO - 查询完成，共获取到 422 条记录
2025-06-05 08:02:34,707 - INFO - 分段 3 查询成功，获取到 422 条记录
2025-06-05 08:02:35,723 - INFO - 查询分段 4: 2025-04-11 至 2025-04-12
2025-06-05 08:02:35,723 - INFO - 查询日期范围: 2025-04-11 至 2025-04-12，使用分页查询，每页 100 条记录
2025-06-05 08:02:35,723 - INFO - Request Parameters - Page 1:
2025-06-05 08:02:35,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:35,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800614, 1744387200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:36,395 - INFO - API请求耗时: 672ms
2025-06-05 08:02:36,395 - INFO - Response - Page 1
2025-06-05 08:02:36,395 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:02:36,910 - INFO - Request Parameters - Page 2:
2025-06-05 08:02:36,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:36,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800614, 1744387200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:37,660 - INFO - API请求耗时: 750ms
2025-06-05 08:02:37,660 - INFO - Response - Page 2
2025-06-05 08:02:37,660 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:02:38,176 - INFO - Request Parameters - Page 3:
2025-06-05 08:02:38,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:38,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800614, 1744387200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:38,926 - INFO - API请求耗时: 750ms
2025-06-05 08:02:38,926 - INFO - Response - Page 3
2025-06-05 08:02:38,926 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:02:39,426 - INFO - Request Parameters - Page 4:
2025-06-05 08:02:39,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:39,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800614, 1744387200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:40,160 - INFO - API请求耗时: 734ms
2025-06-05 08:02:40,160 - INFO - Response - Page 4
2025-06-05 08:02:40,160 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:02:40,660 - INFO - Request Parameters - Page 5:
2025-06-05 08:02:40,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:40,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800614, 1744387200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:41,285 - INFO - API请求耗时: 625ms
2025-06-05 08:02:41,285 - INFO - Response - Page 5
2025-06-05 08:02:41,285 - INFO - 第 5 页获取到 42 条记录
2025-06-05 08:02:41,285 - INFO - 查询完成，共获取到 442 条记录
2025-06-05 08:02:41,285 - INFO - 分段 4 查询成功，获取到 442 条记录
2025-06-05 08:02:42,301 - INFO - 查询分段 5: 2025-04-13 至 2025-04-14
2025-06-05 08:02:42,301 - INFO - 查询日期范围: 2025-04-13 至 2025-04-14，使用分页查询，每页 100 条记录
2025-06-05 08:02:42,301 - INFO - Request Parameters - Page 1:
2025-06-05 08:02:42,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:42,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600614, 1744560000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:43,082 - INFO - API请求耗时: 781ms
2025-06-05 08:02:43,082 - INFO - Response - Page 1
2025-06-05 08:02:43,082 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:02:43,598 - INFO - Request Parameters - Page 2:
2025-06-05 08:02:43,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:43,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600614, 1744560000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:44,301 - INFO - API请求耗时: 703ms
2025-06-05 08:02:44,301 - INFO - Response - Page 2
2025-06-05 08:02:44,301 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:02:44,817 - INFO - Request Parameters - Page 3:
2025-06-05 08:02:44,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:44,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600614, 1744560000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:45,723 - INFO - API请求耗时: 906ms
2025-06-05 08:02:45,738 - INFO - Response - Page 3
2025-06-05 08:02:45,738 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:02:46,238 - INFO - Request Parameters - Page 4:
2025-06-05 08:02:46,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:46,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600614, 1744560000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:46,957 - INFO - API请求耗时: 719ms
2025-06-05 08:02:46,957 - INFO - Response - Page 4
2025-06-05 08:02:46,957 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:02:47,473 - INFO - Request Parameters - Page 5:
2025-06-05 08:02:47,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:47,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600614, 1744560000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:48,051 - INFO - API请求耗时: 578ms
2025-06-05 08:02:48,051 - INFO - Response - Page 5
2025-06-05 08:02:48,051 - INFO - 第 5 页获取到 30 条记录
2025-06-05 08:02:48,051 - INFO - 查询完成，共获取到 430 条记录
2025-06-05 08:02:48,067 - INFO - 分段 5 查询成功，获取到 430 条记录
2025-06-05 08:02:49,082 - INFO - 查询分段 6: 2025-04-15 至 2025-04-16
2025-06-05 08:02:49,082 - INFO - 查询日期范围: 2025-04-15 至 2025-04-16，使用分页查询，每页 100 条记录
2025-06-05 08:02:49,082 - INFO - Request Parameters - Page 1:
2025-06-05 08:02:49,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:49,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400614, 1744732800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:49,863 - INFO - API请求耗时: 781ms
2025-06-05 08:02:49,863 - INFO - Response - Page 1
2025-06-05 08:02:49,863 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:02:50,363 - INFO - Request Parameters - Page 2:
2025-06-05 08:02:50,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:50,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400614, 1744732800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:51,035 - INFO - API请求耗时: 672ms
2025-06-05 08:02:51,035 - INFO - Response - Page 2
2025-06-05 08:02:51,035 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:02:51,551 - INFO - Request Parameters - Page 3:
2025-06-05 08:02:51,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:51,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400614, 1744732800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:52,301 - INFO - API请求耗时: 750ms
2025-06-05 08:02:52,301 - INFO - Response - Page 3
2025-06-05 08:02:52,320 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:02:52,832 - INFO - Request Parameters - Page 4:
2025-06-05 08:02:52,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:52,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400614, 1744732800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:53,582 - INFO - API请求耗时: 750ms
2025-06-05 08:02:53,582 - INFO - Response - Page 4
2025-06-05 08:02:53,582 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:02:54,098 - INFO - Request Parameters - Page 5:
2025-06-05 08:02:54,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:54,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400614, 1744732800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:54,645 - INFO - API请求耗时: 547ms
2025-06-05 08:02:54,645 - INFO - Response - Page 5
2025-06-05 08:02:54,645 - INFO - 第 5 页获取到 20 条记录
2025-06-05 08:02:54,645 - INFO - 查询完成，共获取到 420 条记录
2025-06-05 08:02:54,645 - INFO - 分段 6 查询成功，获取到 420 条记录
2025-06-05 08:02:55,660 - INFO - 查询分段 7: 2025-04-17 至 2025-04-18
2025-06-05 08:02:55,660 - INFO - 查询日期范围: 2025-04-17 至 2025-04-18，使用分页查询，每页 100 条记录
2025-06-05 08:02:55,660 - INFO - Request Parameters - Page 1:
2025-06-05 08:02:55,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:55,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200614, 1744905600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:56,410 - INFO - API请求耗时: 750ms
2025-06-05 08:02:56,410 - INFO - Response - Page 1
2025-06-05 08:02:56,410 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:02:56,910 - INFO - Request Parameters - Page 2:
2025-06-05 08:02:56,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:56,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200614, 1744905600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:57,567 - INFO - API请求耗时: 657ms
2025-06-05 08:02:57,567 - INFO - Response - Page 2
2025-06-05 08:02:57,567 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:02:58,082 - INFO - Request Parameters - Page 3:
2025-06-05 08:02:58,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:58,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200614, 1744905600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:02:58,816 - INFO - API请求耗时: 734ms
2025-06-05 08:02:58,816 - INFO - Response - Page 3
2025-06-05 08:02:58,816 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:02:59,316 - INFO - Request Parameters - Page 4:
2025-06-05 08:02:59,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:02:59,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200614, 1744905600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:00,004 - INFO - API请求耗时: 687ms
2025-06-05 08:03:00,004 - INFO - Response - Page 4
2025-06-05 08:03:00,004 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:03:00,504 - INFO - Request Parameters - Page 5:
2025-06-05 08:03:00,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:00,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200614, 1744905600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:01,082 - INFO - API请求耗时: 578ms
2025-06-05 08:03:01,082 - INFO - Response - Page 5
2025-06-05 08:03:01,082 - INFO - 第 5 页获取到 34 条记录
2025-06-05 08:03:01,082 - INFO - 查询完成，共获取到 434 条记录
2025-06-05 08:03:01,082 - INFO - 分段 7 查询成功，获取到 434 条记录
2025-06-05 08:03:02,098 - INFO - 查询分段 8: 2025-04-19 至 2025-04-20
2025-06-05 08:03:02,098 - INFO - 查询日期范围: 2025-04-19 至 2025-04-20，使用分页查询，每页 100 条记录
2025-06-05 08:03:02,098 - INFO - Request Parameters - Page 1:
2025-06-05 08:03:02,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:02,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000614, 1745078400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:02,785 - INFO - API请求耗时: 672ms
2025-06-05 08:03:02,785 - INFO - Response - Page 1
2025-06-05 08:03:02,785 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:03:03,285 - INFO - Request Parameters - Page 2:
2025-06-05 08:03:03,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:03,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000614, 1745078400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:03,988 - INFO - API请求耗时: 703ms
2025-06-05 08:03:03,988 - INFO - Response - Page 2
2025-06-05 08:03:03,988 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:03:04,488 - INFO - Request Parameters - Page 3:
2025-06-05 08:03:04,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:04,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000614, 1745078400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:05,145 - INFO - API请求耗时: 656ms
2025-06-05 08:03:05,145 - INFO - Response - Page 3
2025-06-05 08:03:05,145 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:03:05,645 - INFO - Request Parameters - Page 4:
2025-06-05 08:03:05,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:05,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000614, 1745078400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:06,363 - INFO - API请求耗时: 719ms
2025-06-05 08:03:06,363 - INFO - Response - Page 4
2025-06-05 08:03:06,363 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:03:06,863 - INFO - Request Parameters - Page 5:
2025-06-05 08:03:06,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:06,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000614, 1745078400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:07,457 - INFO - API请求耗时: 594ms
2025-06-05 08:03:07,457 - INFO - Response - Page 5
2025-06-05 08:03:07,457 - INFO - 第 5 页获取到 32 条记录
2025-06-05 08:03:07,457 - INFO - 查询完成，共获取到 432 条记录
2025-06-05 08:03:07,457 - INFO - 分段 8 查询成功，获取到 432 条记录
2025-06-05 08:03:08,457 - INFO - 查询分段 9: 2025-04-21 至 2025-04-22
2025-06-05 08:03:08,457 - INFO - 查询日期范围: 2025-04-21 至 2025-04-22，使用分页查询，每页 100 条记录
2025-06-05 08:03:08,457 - INFO - Request Parameters - Page 1:
2025-06-05 08:03:08,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:08,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800614, 1745251200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:09,098 - INFO - API请求耗时: 641ms
2025-06-05 08:03:09,098 - INFO - Response - Page 1
2025-06-05 08:03:09,098 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:03:09,613 - INFO - Request Parameters - Page 2:
2025-06-05 08:03:09,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:09,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800614, 1745251200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:10,270 - INFO - API请求耗时: 656ms
2025-06-05 08:03:10,270 - INFO - Response - Page 2
2025-06-05 08:03:10,285 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:03:10,801 - INFO - Request Parameters - Page 3:
2025-06-05 08:03:10,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:10,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800614, 1745251200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:11,520 - INFO - API请求耗时: 719ms
2025-06-05 08:03:11,520 - INFO - Response - Page 3
2025-06-05 08:03:11,520 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:03:12,035 - INFO - Request Parameters - Page 4:
2025-06-05 08:03:12,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:12,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800614, 1745251200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:12,848 - INFO - API请求耗时: 812ms
2025-06-05 08:03:12,848 - INFO - Response - Page 4
2025-06-05 08:03:12,848 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:03:13,348 - INFO - Request Parameters - Page 5:
2025-06-05 08:03:13,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:13,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800614, 1745251200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:13,957 - INFO - API请求耗时: 609ms
2025-06-05 08:03:13,957 - INFO - Response - Page 5
2025-06-05 08:03:13,957 - INFO - 第 5 页获取到 24 条记录
2025-06-05 08:03:13,957 - INFO - 查询完成，共获取到 424 条记录
2025-06-05 08:03:13,957 - INFO - 分段 9 查询成功，获取到 424 条记录
2025-06-05 08:03:14,957 - INFO - 查询分段 10: 2025-04-23 至 2025-04-24
2025-06-05 08:03:14,957 - INFO - 查询日期范围: 2025-04-23 至 2025-04-24，使用分页查询，每页 100 条记录
2025-06-05 08:03:14,957 - INFO - Request Parameters - Page 1:
2025-06-05 08:03:14,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:14,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600614, 1745424000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:15,629 - INFO - API请求耗时: 672ms
2025-06-05 08:03:15,629 - INFO - Response - Page 1
2025-06-05 08:03:15,629 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:03:16,144 - INFO - Request Parameters - Page 2:
2025-06-05 08:03:16,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:16,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600614, 1745424000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:16,848 - INFO - API请求耗时: 703ms
2025-06-05 08:03:16,848 - INFO - Response - Page 2
2025-06-05 08:03:16,848 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:03:17,348 - INFO - Request Parameters - Page 3:
2025-06-05 08:03:17,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:17,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600614, 1745424000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:18,098 - INFO - API请求耗时: 750ms
2025-06-05 08:03:18,098 - INFO - Response - Page 3
2025-06-05 08:03:18,098 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:03:18,613 - INFO - Request Parameters - Page 4:
2025-06-05 08:03:18,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:18,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600614, 1745424000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:19,394 - INFO - API请求耗时: 781ms
2025-06-05 08:03:19,394 - INFO - Response - Page 4
2025-06-05 08:03:19,394 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:03:19,910 - INFO - Request Parameters - Page 5:
2025-06-05 08:03:19,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:19,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600614, 1745424000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:20,410 - INFO - API请求耗时: 500ms
2025-06-05 08:03:20,410 - INFO - Response - Page 5
2025-06-05 08:03:20,410 - INFO - 第 5 页获取到 14 条记录
2025-06-05 08:03:20,410 - INFO - 查询完成，共获取到 414 条记录
2025-06-05 08:03:20,410 - INFO - 分段 10 查询成功，获取到 414 条记录
2025-06-05 08:03:21,426 - INFO - 查询分段 11: 2025-04-25 至 2025-04-26
2025-06-05 08:03:21,426 - INFO - 查询日期范围: 2025-04-25 至 2025-04-26，使用分页查询，每页 100 条记录
2025-06-05 08:03:21,426 - INFO - Request Parameters - Page 1:
2025-06-05 08:03:21,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:21,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400614, 1745596800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:22,160 - INFO - API请求耗时: 734ms
2025-06-05 08:03:22,160 - INFO - Response - Page 1
2025-06-05 08:03:22,160 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:03:22,676 - INFO - Request Parameters - Page 2:
2025-06-05 08:03:22,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:22,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400614, 1745596800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:23,348 - INFO - API请求耗时: 672ms
2025-06-05 08:03:23,348 - INFO - Response - Page 2
2025-06-05 08:03:23,348 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:03:23,848 - INFO - Request Parameters - Page 3:
2025-06-05 08:03:23,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:23,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400614, 1745596800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:24,535 - INFO - API请求耗时: 687ms
2025-06-05 08:03:24,535 - INFO - Response - Page 3
2025-06-05 08:03:24,535 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:03:25,051 - INFO - Request Parameters - Page 4:
2025-06-05 08:03:25,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:25,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400614, 1745596800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:25,785 - INFO - API请求耗时: 734ms
2025-06-05 08:03:25,785 - INFO - Response - Page 4
2025-06-05 08:03:25,785 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:03:26,285 - INFO - Request Parameters - Page 5:
2025-06-05 08:03:26,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:26,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400614, 1745596800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:26,785 - INFO - API请求耗时: 500ms
2025-06-05 08:03:26,785 - INFO - Response - Page 5
2025-06-05 08:03:26,785 - INFO - 第 5 页获取到 36 条记录
2025-06-05 08:03:26,785 - INFO - 查询完成，共获取到 436 条记录
2025-06-05 08:03:26,785 - INFO - 分段 11 查询成功，获取到 436 条记录
2025-06-05 08:03:27,801 - INFO - 查询分段 12: 2025-04-27 至 2025-04-28
2025-06-05 08:03:27,801 - INFO - 查询日期范围: 2025-04-27 至 2025-04-28，使用分页查询，每页 100 条记录
2025-06-05 08:03:27,801 - INFO - Request Parameters - Page 1:
2025-06-05 08:03:27,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:27,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200614, 1745769600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:28,535 - INFO - API请求耗时: 734ms
2025-06-05 08:03:28,535 - INFO - Response - Page 1
2025-06-05 08:03:28,535 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:03:29,035 - INFO - Request Parameters - Page 2:
2025-06-05 08:03:29,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:29,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200614, 1745769600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:29,754 - INFO - API请求耗时: 719ms
2025-06-05 08:03:29,754 - INFO - Response - Page 2
2025-06-05 08:03:29,754 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:03:30,269 - INFO - Request Parameters - Page 3:
2025-06-05 08:03:30,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:30,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200614, 1745769600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:30,926 - INFO - API请求耗时: 656ms
2025-06-05 08:03:30,941 - INFO - Response - Page 3
2025-06-05 08:03:30,941 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:03:31,441 - INFO - Request Parameters - Page 4:
2025-06-05 08:03:31,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:31,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200614, 1745769600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:32,129 - INFO - API请求耗时: 687ms
2025-06-05 08:03:32,129 - INFO - Response - Page 4
2025-06-05 08:03:32,129 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:03:32,629 - INFO - Request Parameters - Page 5:
2025-06-05 08:03:32,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:32,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200614, 1745769600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:33,144 - INFO - API请求耗时: 516ms
2025-06-05 08:03:33,144 - INFO - Response - Page 5
2025-06-05 08:03:33,144 - INFO - 第 5 页获取到 24 条记录
2025-06-05 08:03:33,144 - INFO - 查询完成，共获取到 424 条记录
2025-06-05 08:03:33,144 - INFO - 分段 12 查询成功，获取到 424 条记录
2025-06-05 08:03:34,160 - INFO - 查询分段 13: 2025-04-29 至 2025-04-30
2025-06-05 08:03:34,160 - INFO - 查询日期范围: 2025-04-29 至 2025-04-30，使用分页查询，每页 100 条记录
2025-06-05 08:03:34,160 - INFO - Request Parameters - Page 1:
2025-06-05 08:03:34,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:34,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000614, 1745942400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:34,863 - INFO - API请求耗时: 703ms
2025-06-05 08:03:34,863 - INFO - Response - Page 1
2025-06-05 08:03:34,879 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:03:35,394 - INFO - Request Parameters - Page 2:
2025-06-05 08:03:35,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:35,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000614, 1745942400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:36,066 - INFO - API请求耗时: 672ms
2025-06-05 08:03:36,082 - INFO - Response - Page 2
2025-06-05 08:03:36,082 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:03:36,597 - INFO - Request Parameters - Page 3:
2025-06-05 08:03:36,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:36,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000614, 1745942400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:37,379 - INFO - API请求耗时: 781ms
2025-06-05 08:03:37,379 - INFO - Response - Page 3
2025-06-05 08:03:37,379 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:03:37,894 - INFO - Request Parameters - Page 4:
2025-06-05 08:03:37,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:37,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000614, 1745942400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:38,660 - INFO - API请求耗时: 766ms
2025-06-05 08:03:38,660 - INFO - Response - Page 4
2025-06-05 08:03:38,660 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:03:39,176 - INFO - Request Parameters - Page 5:
2025-06-05 08:03:39,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:39,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000614, 1745942400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:39,754 - INFO - API请求耗时: 578ms
2025-06-05 08:03:39,754 - INFO - Response - Page 5
2025-06-05 08:03:39,754 - INFO - 第 5 页获取到 34 条记录
2025-06-05 08:03:39,754 - INFO - 查询完成，共获取到 434 条记录
2025-06-05 08:03:39,754 - INFO - 分段 13 查询成功，获取到 434 条记录
2025-06-05 08:03:40,769 - INFO - 查询分段 14: 2025-05-01 至 2025-05-02
2025-06-05 08:03:40,769 - INFO - 查询日期范围: 2025-05-01 至 2025-05-02，使用分页查询，每页 100 条记录
2025-06-05 08:03:40,769 - INFO - Request Parameters - Page 1:
2025-06-05 08:03:40,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:40,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800614, 1746115200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:41,441 - INFO - API请求耗时: 672ms
2025-06-05 08:03:41,441 - INFO - Response - Page 1
2025-06-05 08:03:41,441 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:03:41,957 - INFO - Request Parameters - Page 2:
2025-06-05 08:03:41,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:41,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800614, 1746115200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:42,629 - INFO - API请求耗时: 672ms
2025-06-05 08:03:42,629 - INFO - Response - Page 2
2025-06-05 08:03:42,629 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:03:43,129 - INFO - Request Parameters - Page 3:
2025-06-05 08:03:43,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:43,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800614, 1746115200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:43,910 - INFO - API请求耗时: 781ms
2025-06-05 08:03:43,910 - INFO - Response - Page 3
2025-06-05 08:03:43,910 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:03:44,410 - INFO - Request Parameters - Page 4:
2025-06-05 08:03:44,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:44,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800614, 1746115200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:45,113 - INFO - API请求耗时: 703ms
2025-06-05 08:03:45,113 - INFO - Response - Page 4
2025-06-05 08:03:45,113 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:03:45,629 - INFO - Request Parameters - Page 5:
2025-06-05 08:03:45,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:45,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800614, 1746115200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:46,129 - INFO - API请求耗时: 500ms
2025-06-05 08:03:46,129 - INFO - Response - Page 5
2025-06-05 08:03:46,129 - INFO - 第 5 页获取到 18 条记录
2025-06-05 08:03:46,129 - INFO - 查询完成，共获取到 418 条记录
2025-06-05 08:03:46,129 - INFO - 分段 14 查询成功，获取到 418 条记录
2025-06-05 08:03:47,144 - INFO - 查询分段 15: 2025-05-03 至 2025-05-04
2025-06-05 08:03:47,144 - INFO - 查询日期范围: 2025-05-03 至 2025-05-04，使用分页查询，每页 100 条记录
2025-06-05 08:03:47,144 - INFO - Request Parameters - Page 1:
2025-06-05 08:03:47,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:47,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600614, 1746288000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:47,801 - INFO - API请求耗时: 656ms
2025-06-05 08:03:47,801 - INFO - Response - Page 1
2025-06-05 08:03:47,801 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:03:48,316 - INFO - Request Parameters - Page 2:
2025-06-05 08:03:48,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:48,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600614, 1746288000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:49,097 - INFO - API请求耗时: 781ms
2025-06-05 08:03:49,097 - INFO - Response - Page 2
2025-06-05 08:03:49,097 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:03:49,597 - INFO - Request Parameters - Page 3:
2025-06-05 08:03:49,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:49,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600614, 1746288000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:50,332 - INFO - API请求耗时: 734ms
2025-06-05 08:03:50,332 - INFO - Response - Page 3
2025-06-05 08:03:50,332 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:03:50,832 - INFO - Request Parameters - Page 4:
2025-06-05 08:03:50,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:50,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600614, 1746288000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:51,550 - INFO - API请求耗时: 719ms
2025-06-05 08:03:51,550 - INFO - Response - Page 4
2025-06-05 08:03:51,550 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:03:52,051 - INFO - Request Parameters - Page 5:
2025-06-05 08:03:52,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:52,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600614, 1746288000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:52,629 - INFO - API请求耗时: 578ms
2025-06-05 08:03:52,629 - INFO - Response - Page 5
2025-06-05 08:03:52,629 - INFO - 第 5 页获取到 28 条记录
2025-06-05 08:03:52,629 - INFO - 查询完成，共获取到 428 条记录
2025-06-05 08:03:52,629 - INFO - 分段 15 查询成功，获取到 428 条记录
2025-06-05 08:03:53,629 - INFO - 查询分段 16: 2025-05-05 至 2025-05-06
2025-06-05 08:03:53,629 - INFO - 查询日期范围: 2025-05-05 至 2025-05-06，使用分页查询，每页 100 条记录
2025-06-05 08:03:53,629 - INFO - Request Parameters - Page 1:
2025-06-05 08:03:53,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:53,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400614, 1746460800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:54,347 - INFO - API请求耗时: 719ms
2025-06-05 08:03:54,347 - INFO - Response - Page 1
2025-06-05 08:03:54,347 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:03:54,847 - INFO - Request Parameters - Page 2:
2025-06-05 08:03:54,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:54,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400614, 1746460800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:55,894 - INFO - API请求耗时: 1047ms
2025-06-05 08:03:55,894 - INFO - Response - Page 2
2025-06-05 08:03:55,894 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:03:56,394 - INFO - Request Parameters - Page 3:
2025-06-05 08:03:56,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:56,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400614, 1746460800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:57,082 - INFO - API请求耗时: 687ms
2025-06-05 08:03:57,082 - INFO - Response - Page 3
2025-06-05 08:03:57,082 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:03:57,582 - INFO - Request Parameters - Page 4:
2025-06-05 08:03:57,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:57,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400614, 1746460800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:58,285 - INFO - API请求耗时: 703ms
2025-06-05 08:03:58,285 - INFO - Response - Page 4
2025-06-05 08:03:58,285 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:03:58,800 - INFO - Request Parameters - Page 5:
2025-06-05 08:03:58,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:03:58,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400614, 1746460800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:03:59,160 - INFO - API请求耗时: 359ms
2025-06-05 08:03:59,160 - INFO - Response - Page 5
2025-06-05 08:03:59,160 - INFO - 第 5 页获取到 2 条记录
2025-06-05 08:03:59,160 - INFO - 查询完成，共获取到 402 条记录
2025-06-05 08:03:59,160 - INFO - 分段 16 查询成功，获取到 402 条记录
2025-06-05 08:04:00,175 - INFO - 查询分段 17: 2025-05-07 至 2025-05-08
2025-06-05 08:04:00,175 - INFO - 查询日期范围: 2025-05-07 至 2025-05-08，使用分页查询，每页 100 条记录
2025-06-05 08:04:00,175 - INFO - Request Parameters - Page 1:
2025-06-05 08:04:00,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:00,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200614, 1746633600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:00,847 - INFO - API请求耗时: 672ms
2025-06-05 08:04:00,863 - INFO - Response - Page 1
2025-06-05 08:04:00,863 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:04:01,363 - INFO - Request Parameters - Page 2:
2025-06-05 08:04:01,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:01,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200614, 1746633600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:02,050 - INFO - API请求耗时: 687ms
2025-06-05 08:04:02,050 - INFO - Response - Page 2
2025-06-05 08:04:02,050 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:04:02,566 - INFO - Request Parameters - Page 3:
2025-06-05 08:04:02,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:02,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200614, 1746633600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:03,285 - INFO - API请求耗时: 719ms
2025-06-05 08:04:03,285 - INFO - Response - Page 3
2025-06-05 08:04:03,285 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:04:03,785 - INFO - Request Parameters - Page 4:
2025-06-05 08:04:03,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:03,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200614, 1746633600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:04,597 - INFO - API请求耗时: 812ms
2025-06-05 08:04:04,597 - INFO - Response - Page 4
2025-06-05 08:04:04,597 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:04:05,097 - INFO - Request Parameters - Page 5:
2025-06-05 08:04:05,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:05,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200614, 1746633600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:05,691 - INFO - API请求耗时: 594ms
2025-06-05 08:04:05,691 - INFO - Response - Page 5
2025-06-05 08:04:05,691 - INFO - 第 5 页获取到 14 条记录
2025-06-05 08:04:05,691 - INFO - 查询完成，共获取到 414 条记录
2025-06-05 08:04:05,691 - INFO - 分段 17 查询成功，获取到 414 条记录
2025-06-05 08:04:06,707 - INFO - 查询分段 18: 2025-05-09 至 2025-05-10
2025-06-05 08:04:06,707 - INFO - 查询日期范围: 2025-05-09 至 2025-05-10，使用分页查询，每页 100 条记录
2025-06-05 08:04:06,707 - INFO - Request Parameters - Page 1:
2025-06-05 08:04:06,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:06,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000614, 1746806400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:07,457 - INFO - API请求耗时: 750ms
2025-06-05 08:04:07,457 - INFO - Response - Page 1
2025-06-05 08:04:07,457 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:04:07,957 - INFO - Request Parameters - Page 2:
2025-06-05 08:04:07,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:07,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000614, 1746806400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:08,613 - INFO - API请求耗时: 656ms
2025-06-05 08:04:08,613 - INFO - Response - Page 2
2025-06-05 08:04:08,613 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:04:09,113 - INFO - Request Parameters - Page 3:
2025-06-05 08:04:09,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:09,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000614, 1746806400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:09,925 - INFO - API请求耗时: 812ms
2025-06-05 08:04:09,925 - INFO - Response - Page 3
2025-06-05 08:04:09,925 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:04:10,425 - INFO - Request Parameters - Page 4:
2025-06-05 08:04:10,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:10,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000614, 1746806400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:11,113 - INFO - API请求耗时: 687ms
2025-06-05 08:04:11,113 - INFO - Response - Page 4
2025-06-05 08:04:11,113 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:04:11,613 - INFO - Request Parameters - Page 5:
2025-06-05 08:04:11,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:11,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000614, 1746806400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:12,160 - INFO - API请求耗时: 547ms
2025-06-05 08:04:12,175 - INFO - Response - Page 5
2025-06-05 08:04:12,175 - INFO - 第 5 页获取到 36 条记录
2025-06-05 08:04:12,175 - INFO - 查询完成，共获取到 436 条记录
2025-06-05 08:04:12,175 - INFO - 分段 18 查询成功，获取到 436 条记录
2025-06-05 08:04:13,175 - INFO - 查询分段 19: 2025-05-11 至 2025-05-12
2025-06-05 08:04:13,175 - INFO - 查询日期范围: 2025-05-11 至 2025-05-12，使用分页查询，每页 100 条记录
2025-06-05 08:04:13,175 - INFO - Request Parameters - Page 1:
2025-06-05 08:04:13,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:13,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800614, 1746979200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:13,988 - INFO - API请求耗时: 813ms
2025-06-05 08:04:13,988 - INFO - Response - Page 1
2025-06-05 08:04:13,988 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:04:14,504 - INFO - Request Parameters - Page 2:
2025-06-05 08:04:14,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:14,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800614, 1746979200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:15,269 - INFO - API请求耗时: 766ms
2025-06-05 08:04:15,269 - INFO - Response - Page 2
2025-06-05 08:04:15,269 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:04:15,785 - INFO - Request Parameters - Page 3:
2025-06-05 08:04:15,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:15,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800614, 1746979200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:16,613 - INFO - API请求耗时: 828ms
2025-06-05 08:04:16,613 - INFO - Response - Page 3
2025-06-05 08:04:16,613 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:04:17,113 - INFO - Request Parameters - Page 4:
2025-06-05 08:04:17,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:17,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800614, 1746979200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:17,957 - INFO - API请求耗时: 844ms
2025-06-05 08:04:17,957 - INFO - Response - Page 4
2025-06-05 08:04:17,957 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:04:18,472 - INFO - Request Parameters - Page 5:
2025-06-05 08:04:18,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:18,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800614, 1746979200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:18,988 - INFO - API请求耗时: 516ms
2025-06-05 08:04:18,988 - INFO - Response - Page 5
2025-06-05 08:04:18,988 - INFO - 第 5 页获取到 28 条记录
2025-06-05 08:04:18,988 - INFO - 查询完成，共获取到 428 条记录
2025-06-05 08:04:18,988 - INFO - 分段 19 查询成功，获取到 428 条记录
2025-06-05 08:04:19,988 - INFO - 查询分段 20: 2025-05-13 至 2025-05-14
2025-06-05 08:04:19,988 - INFO - 查询日期范围: 2025-05-13 至 2025-05-14，使用分页查询，每页 100 条记录
2025-06-05 08:04:19,988 - INFO - Request Parameters - Page 1:
2025-06-05 08:04:19,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:19,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600614, 1747152000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:20,707 - INFO - API请求耗时: 719ms
2025-06-05 08:04:20,707 - INFO - Response - Page 1
2025-06-05 08:04:20,722 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:04:21,238 - INFO - Request Parameters - Page 2:
2025-06-05 08:04:21,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:21,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600614, 1747152000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:21,910 - INFO - API请求耗时: 672ms
2025-06-05 08:04:21,910 - INFO - Response - Page 2
2025-06-05 08:04:21,910 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:04:22,410 - INFO - Request Parameters - Page 3:
2025-06-05 08:04:22,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:22,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600614, 1747152000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:23,097 - INFO - API请求耗时: 687ms
2025-06-05 08:04:23,097 - INFO - Response - Page 3
2025-06-05 08:04:23,097 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:04:23,613 - INFO - Request Parameters - Page 4:
2025-06-05 08:04:23,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:23,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600614, 1747152000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:24,457 - INFO - API请求耗时: 844ms
2025-06-05 08:04:24,457 - INFO - Response - Page 4
2025-06-05 08:04:24,457 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:04:24,957 - INFO - Request Parameters - Page 5:
2025-06-05 08:04:24,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:24,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600614, 1747152000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:25,441 - INFO - API请求耗时: 484ms
2025-06-05 08:04:25,441 - INFO - Response - Page 5
2025-06-05 08:04:25,441 - INFO - 第 5 页获取到 14 条记录
2025-06-05 08:04:25,441 - INFO - 查询完成，共获取到 414 条记录
2025-06-05 08:04:25,441 - INFO - 分段 20 查询成功，获取到 414 条记录
2025-06-05 08:04:26,457 - INFO - 查询分段 21: 2025-05-15 至 2025-05-16
2025-06-05 08:04:26,457 - INFO - 查询日期范围: 2025-05-15 至 2025-05-16，使用分页查询，每页 100 条记录
2025-06-05 08:04:26,457 - INFO - Request Parameters - Page 1:
2025-06-05 08:04:26,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:26,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400614, 1747324800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:27,207 - INFO - API请求耗时: 750ms
2025-06-05 08:04:27,207 - INFO - Response - Page 1
2025-06-05 08:04:27,207 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:04:27,722 - INFO - Request Parameters - Page 2:
2025-06-05 08:04:27,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:27,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400614, 1747324800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:28,378 - INFO - API请求耗时: 656ms
2025-06-05 08:04:28,378 - INFO - Response - Page 2
2025-06-05 08:04:28,378 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:04:28,878 - INFO - Request Parameters - Page 3:
2025-06-05 08:04:28,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:28,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400614, 1747324800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:29,566 - INFO - API请求耗时: 687ms
2025-06-05 08:04:29,566 - INFO - Response - Page 3
2025-06-05 08:04:29,566 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:04:30,082 - INFO - Request Parameters - Page 4:
2025-06-05 08:04:30,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:30,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400614, 1747324800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:30,847 - INFO - API请求耗时: 766ms
2025-06-05 08:04:30,847 - INFO - Response - Page 4
2025-06-05 08:04:30,847 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:04:31,363 - INFO - Request Parameters - Page 5:
2025-06-05 08:04:31,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:31,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400614, 1747324800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:31,894 - INFO - API请求耗时: 531ms
2025-06-05 08:04:31,894 - INFO - Response - Page 5
2025-06-05 08:04:31,894 - INFO - 第 5 页获取到 20 条记录
2025-06-05 08:04:31,894 - INFO - 查询完成，共获取到 420 条记录
2025-06-05 08:04:31,894 - INFO - 分段 21 查询成功，获取到 420 条记录
2025-06-05 08:04:32,910 - INFO - 查询分段 22: 2025-05-17 至 2025-05-18
2025-06-05 08:04:32,910 - INFO - 查询日期范围: 2025-05-17 至 2025-05-18，使用分页查询，每页 100 条记录
2025-06-05 08:04:32,910 - INFO - Request Parameters - Page 1:
2025-06-05 08:04:32,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:32,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200614, 1747497600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:33,597 - INFO - API请求耗时: 687ms
2025-06-05 08:04:33,597 - INFO - Response - Page 1
2025-06-05 08:04:33,597 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:04:34,097 - INFO - Request Parameters - Page 2:
2025-06-05 08:04:34,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:34,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200614, 1747497600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:34,910 - INFO - API请求耗时: 812ms
2025-06-05 08:04:34,910 - INFO - Response - Page 2
2025-06-05 08:04:34,910 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:04:35,425 - INFO - Request Parameters - Page 3:
2025-06-05 08:04:35,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:35,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200614, 1747497600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:36,113 - INFO - API请求耗时: 687ms
2025-06-05 08:04:36,113 - INFO - Response - Page 3
2025-06-05 08:04:36,113 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:04:36,613 - INFO - Request Parameters - Page 4:
2025-06-05 08:04:36,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:36,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200614, 1747497600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:37,410 - INFO - API请求耗时: 797ms
2025-06-05 08:04:37,410 - INFO - Response - Page 4
2025-06-05 08:04:37,410 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:04:37,925 - INFO - Request Parameters - Page 5:
2025-06-05 08:04:37,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:37,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200614, 1747497600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:38,441 - INFO - API请求耗时: 516ms
2025-06-05 08:04:38,441 - INFO - Response - Page 5
2025-06-05 08:04:38,441 - INFO - 第 5 页获取到 32 条记录
2025-06-05 08:04:38,441 - INFO - 查询完成，共获取到 432 条记录
2025-06-05 08:04:38,441 - INFO - 分段 22 查询成功，获取到 432 条记录
2025-06-05 08:04:39,441 - INFO - 查询分段 23: 2025-05-19 至 2025-05-20
2025-06-05 08:04:39,441 - INFO - 查询日期范围: 2025-05-19 至 2025-05-20，使用分页查询，每页 100 条记录
2025-06-05 08:04:39,441 - INFO - Request Parameters - Page 1:
2025-06-05 08:04:39,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:39,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000614, 1747670400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:40,144 - INFO - API请求耗时: 703ms
2025-06-05 08:04:40,144 - INFO - Response - Page 1
2025-06-05 08:04:40,144 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:04:40,660 - INFO - Request Parameters - Page 2:
2025-06-05 08:04:40,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:40,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000614, 1747670400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:41,503 - INFO - API请求耗时: 844ms
2025-06-05 08:04:41,503 - INFO - Response - Page 2
2025-06-05 08:04:41,503 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:04:42,003 - INFO - Request Parameters - Page 3:
2025-06-05 08:04:42,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:42,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000614, 1747670400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:42,675 - INFO - API请求耗时: 672ms
2025-06-05 08:04:42,675 - INFO - Response - Page 3
2025-06-05 08:04:42,675 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:04:43,191 - INFO - Request Parameters - Page 4:
2025-06-05 08:04:43,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:43,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000614, 1747670400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:43,956 - INFO - API请求耗时: 766ms
2025-06-05 08:04:43,956 - INFO - Response - Page 4
2025-06-05 08:04:43,956 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:04:44,472 - INFO - Request Parameters - Page 5:
2025-06-05 08:04:44,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:44,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000614, 1747670400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:45,019 - INFO - API请求耗时: 547ms
2025-06-05 08:04:45,035 - INFO - Response - Page 5
2025-06-05 08:04:45,035 - INFO - 第 5 页获取到 26 条记录
2025-06-05 08:04:45,035 - INFO - 查询完成，共获取到 426 条记录
2025-06-05 08:04:45,035 - INFO - 分段 23 查询成功，获取到 426 条记录
2025-06-05 08:04:46,035 - INFO - 查询分段 24: 2025-05-21 至 2025-05-22
2025-06-05 08:04:46,035 - INFO - 查询日期范围: 2025-05-21 至 2025-05-22，使用分页查询，每页 100 条记录
2025-06-05 08:04:46,035 - INFO - Request Parameters - Page 1:
2025-06-05 08:04:46,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:46,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800614, 1747843200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:46,847 - INFO - API请求耗时: 812ms
2025-06-05 08:04:46,847 - INFO - Response - Page 1
2025-06-05 08:04:46,847 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:04:47,347 - INFO - Request Parameters - Page 2:
2025-06-05 08:04:47,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:47,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800614, 1747843200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:48,144 - INFO - API请求耗时: 797ms
2025-06-05 08:04:48,144 - INFO - Response - Page 2
2025-06-05 08:04:48,144 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:04:48,660 - INFO - Request Parameters - Page 3:
2025-06-05 08:04:48,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:48,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800614, 1747843200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:49,363 - INFO - API请求耗时: 703ms
2025-06-05 08:04:49,363 - INFO - Response - Page 3
2025-06-05 08:04:49,363 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:04:49,863 - INFO - Request Parameters - Page 4:
2025-06-05 08:04:49,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:49,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800614, 1747843200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:50,644 - INFO - API请求耗时: 781ms
2025-06-05 08:04:50,644 - INFO - Response - Page 4
2025-06-05 08:04:50,644 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:04:51,159 - INFO - Request Parameters - Page 5:
2025-06-05 08:04:51,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:51,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800614, 1747843200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:51,613 - INFO - API请求耗时: 453ms
2025-06-05 08:04:51,613 - INFO - Response - Page 5
2025-06-05 08:04:51,613 - INFO - 第 5 页获取到 16 条记录
2025-06-05 08:04:51,613 - INFO - 查询完成，共获取到 416 条记录
2025-06-05 08:04:51,613 - INFO - 分段 24 查询成功，获取到 416 条记录
2025-06-05 08:04:52,628 - INFO - 查询分段 25: 2025-05-23 至 2025-05-24
2025-06-05 08:04:52,628 - INFO - 查询日期范围: 2025-05-23 至 2025-05-24，使用分页查询，每页 100 条记录
2025-06-05 08:04:52,628 - INFO - Request Parameters - Page 1:
2025-06-05 08:04:52,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:52,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600614, 1748016000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:53,300 - INFO - API请求耗时: 672ms
2025-06-05 08:04:53,300 - INFO - Response - Page 1
2025-06-05 08:04:53,300 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:04:53,816 - INFO - Request Parameters - Page 2:
2025-06-05 08:04:53,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:53,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600614, 1748016000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:54,488 - INFO - API请求耗时: 672ms
2025-06-05 08:04:54,488 - INFO - Response - Page 2
2025-06-05 08:04:54,488 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:04:55,003 - INFO - Request Parameters - Page 3:
2025-06-05 08:04:55,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:55,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600614, 1748016000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:55,659 - INFO - API请求耗时: 656ms
2025-06-05 08:04:55,659 - INFO - Response - Page 3
2025-06-05 08:04:55,659 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:04:56,159 - INFO - Request Parameters - Page 4:
2025-06-05 08:04:56,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:56,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600614, 1748016000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:56,941 - INFO - API请求耗时: 781ms
2025-06-05 08:04:56,941 - INFO - Response - Page 4
2025-06-05 08:04:56,941 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:04:57,441 - INFO - Request Parameters - Page 5:
2025-06-05 08:04:57,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:57,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600614, 1748016000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:58,019 - INFO - API请求耗时: 578ms
2025-06-05 08:04:58,019 - INFO - Response - Page 5
2025-06-05 08:04:58,019 - INFO - 第 5 页获取到 30 条记录
2025-06-05 08:04:58,019 - INFO - 查询完成，共获取到 430 条记录
2025-06-05 08:04:58,019 - INFO - 分段 25 查询成功，获取到 430 条记录
2025-06-05 08:04:59,019 - INFO - 查询分段 26: 2025-05-25 至 2025-05-26
2025-06-05 08:04:59,019 - INFO - 查询日期范围: 2025-05-25 至 2025-05-26，使用分页查询，每页 100 条记录
2025-06-05 08:04:59,019 - INFO - Request Parameters - Page 1:
2025-06-05 08:04:59,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:04:59,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400614, 1748188800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:04:59,706 - INFO - API请求耗时: 687ms
2025-06-05 08:04:59,706 - INFO - Response - Page 1
2025-06-05 08:04:59,706 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:05:00,222 - INFO - Request Parameters - Page 2:
2025-06-05 08:05:00,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:00,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400614, 1748188800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:01,050 - INFO - API请求耗时: 828ms
2025-06-05 08:05:01,050 - INFO - Response - Page 2
2025-06-05 08:05:01,050 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:05:01,566 - INFO - Request Parameters - Page 3:
2025-06-05 08:05:01,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:01,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400614, 1748188800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:02,284 - INFO - API请求耗时: 719ms
2025-06-05 08:05:02,284 - INFO - Response - Page 3
2025-06-05 08:05:02,284 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:05:02,800 - INFO - Request Parameters - Page 4:
2025-06-05 08:05:02,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:02,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400614, 1748188800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:03,597 - INFO - API请求耗时: 797ms
2025-06-05 08:05:03,597 - INFO - Response - Page 4
2025-06-05 08:05:03,597 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:05:04,113 - INFO - Request Parameters - Page 5:
2025-06-05 08:05:04,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:04,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400614, 1748188800614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:04,706 - INFO - API请求耗时: 594ms
2025-06-05 08:05:04,706 - INFO - Response - Page 5
2025-06-05 08:05:04,706 - INFO - 第 5 页获取到 10 条记录
2025-06-05 08:05:04,706 - INFO - 查询完成，共获取到 410 条记录
2025-06-05 08:05:04,706 - INFO - 分段 26 查询成功，获取到 410 条记录
2025-06-05 08:05:05,706 - INFO - 查询分段 27: 2025-05-27 至 2025-05-28
2025-06-05 08:05:05,706 - INFO - 查询日期范围: 2025-05-27 至 2025-05-28，使用分页查询，每页 100 条记录
2025-06-05 08:05:05,706 - INFO - Request Parameters - Page 1:
2025-06-05 08:05:05,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:05,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200614, 1748361600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:06,566 - INFO - API请求耗时: 859ms
2025-06-05 08:05:06,566 - INFO - Response - Page 1
2025-06-05 08:05:06,566 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:05:07,066 - INFO - Request Parameters - Page 2:
2025-06-05 08:05:07,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:07,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200614, 1748361600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:07,769 - INFO - API请求耗时: 703ms
2025-06-05 08:05:07,769 - INFO - Response - Page 2
2025-06-05 08:05:07,769 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:05:08,269 - INFO - Request Parameters - Page 3:
2025-06-05 08:05:08,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:08,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200614, 1748361600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:09,003 - INFO - API请求耗时: 734ms
2025-06-05 08:05:09,003 - INFO - Response - Page 3
2025-06-05 08:05:09,003 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:05:09,503 - INFO - Request Parameters - Page 4:
2025-06-05 08:05:09,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:09,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200614, 1748361600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:10,425 - INFO - API请求耗时: 922ms
2025-06-05 08:05:10,425 - INFO - Response - Page 4
2025-06-05 08:05:10,425 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:05:10,941 - INFO - Request Parameters - Page 5:
2025-06-05 08:05:10,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:10,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200614, 1748361600614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:11,425 - INFO - API请求耗时: 484ms
2025-06-05 08:05:11,425 - INFO - Response - Page 5
2025-06-05 08:05:11,425 - INFO - 第 5 页获取到 16 条记录
2025-06-05 08:05:11,425 - INFO - 查询完成，共获取到 416 条记录
2025-06-05 08:05:11,425 - INFO - 分段 27 查询成功，获取到 416 条记录
2025-06-05 08:05:12,441 - INFO - 查询分段 28: 2025-05-29 至 2025-05-30
2025-06-05 08:05:12,441 - INFO - 查询日期范围: 2025-05-29 至 2025-05-30，使用分页查询，每页 100 条记录
2025-06-05 08:05:12,441 - INFO - Request Parameters - Page 1:
2025-06-05 08:05:12,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:12,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000614, 1748534400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:13,081 - INFO - API请求耗时: 641ms
2025-06-05 08:05:13,081 - INFO - Response - Page 1
2025-06-05 08:05:13,081 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:05:13,581 - INFO - Request Parameters - Page 2:
2025-06-05 08:05:13,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:13,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000614, 1748534400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:14,284 - INFO - API请求耗时: 703ms
2025-06-05 08:05:14,284 - INFO - Response - Page 2
2025-06-05 08:05:14,284 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:05:14,784 - INFO - Request Parameters - Page 3:
2025-06-05 08:05:14,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:14,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000614, 1748534400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:15,519 - INFO - API请求耗时: 734ms
2025-06-05 08:05:15,519 - INFO - Response - Page 3
2025-06-05 08:05:15,519 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:05:16,034 - INFO - Request Parameters - Page 4:
2025-06-05 08:05:16,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:16,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000614, 1748534400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:16,925 - INFO - API请求耗时: 891ms
2025-06-05 08:05:16,925 - INFO - Response - Page 4
2025-06-05 08:05:16,925 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:05:17,441 - INFO - Request Parameters - Page 5:
2025-06-05 08:05:17,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:17,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000614, 1748534400614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:17,956 - INFO - API请求耗时: 516ms
2025-06-05 08:05:17,956 - INFO - Response - Page 5
2025-06-05 08:05:17,956 - INFO - 第 5 页获取到 16 条记录
2025-06-05 08:05:17,956 - INFO - 查询完成，共获取到 416 条记录
2025-06-05 08:05:17,956 - INFO - 分段 28 查询成功，获取到 416 条记录
2025-06-05 08:05:18,956 - INFO - 查询分段 29: 2025-05-31 至 2025-06-01
2025-06-05 08:05:18,956 - INFO - 查询日期范围: 2025-05-31 至 2025-06-01，使用分页查询，每页 100 条记录
2025-06-05 08:05:18,956 - INFO - Request Parameters - Page 1:
2025-06-05 08:05:18,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:18,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800614, 1748707200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:19,691 - INFO - API请求耗时: 734ms
2025-06-05 08:05:19,691 - INFO - Response - Page 1
2025-06-05 08:05:19,691 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:05:20,206 - INFO - Request Parameters - Page 2:
2025-06-05 08:05:20,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:20,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800614, 1748707200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:20,925 - INFO - API请求耗时: 719ms
2025-06-05 08:05:20,925 - INFO - Response - Page 2
2025-06-05 08:05:20,925 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:05:21,441 - INFO - Request Parameters - Page 3:
2025-06-05 08:05:21,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:21,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800614, 1748707200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:22,159 - INFO - API请求耗时: 719ms
2025-06-05 08:05:22,159 - INFO - Response - Page 3
2025-06-05 08:05:22,159 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:05:22,675 - INFO - Request Parameters - Page 4:
2025-06-05 08:05:22,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:22,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800614, 1748707200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:23,362 - INFO - API请求耗时: 687ms
2025-06-05 08:05:23,362 - INFO - Response - Page 4
2025-06-05 08:05:23,362 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:05:23,862 - INFO - Request Parameters - Page 5:
2025-06-05 08:05:23,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:23,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800614, 1748707200614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:24,347 - INFO - API请求耗时: 484ms
2025-06-05 08:05:24,347 - INFO - Response - Page 5
2025-06-05 08:05:24,347 - INFO - 第 5 页获取到 10 条记录
2025-06-05 08:05:24,347 - INFO - 查询完成，共获取到 410 条记录
2025-06-05 08:05:24,347 - INFO - 分段 29 查询成功，获取到 410 条记录
2025-06-05 08:05:25,362 - INFO - 查询分段 30: 2025-06-02 至 2025-06-03
2025-06-05 08:05:25,362 - INFO - 查询日期范围: 2025-06-02 至 2025-06-03，使用分页查询，每页 100 条记录
2025-06-05 08:05:25,362 - INFO - Request Parameters - Page 1:
2025-06-05 08:05:25,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:25,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600614, 1748880000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:26,112 - INFO - API请求耗时: 750ms
2025-06-05 08:05:26,112 - INFO - Response - Page 1
2025-06-05 08:05:26,112 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:05:26,612 - INFO - Request Parameters - Page 2:
2025-06-05 08:05:26,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:26,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600614, 1748880000614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:27,362 - INFO - API请求耗时: 750ms
2025-06-05 08:05:27,362 - INFO - Response - Page 2
2025-06-05 08:05:27,362 - INFO - 第 2 页获取到 98 条记录
2025-06-05 08:05:27,362 - INFO - 查询完成，共获取到 198 条记录
2025-06-05 08:05:27,362 - INFO - 分段 30 查询成功，获取到 198 条记录
2025-06-05 08:05:28,378 - INFO - 查询分段 31: 2025-06-04 至 2025-06-04
2025-06-05 08:05:28,378 - INFO - 查询日期范围: 2025-06-04 至 2025-06-04，使用分页查询，每页 100 条记录
2025-06-05 08:05:28,378 - INFO - Request Parameters - Page 1:
2025-06-05 08:05:28,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:05:28,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748966400614, 1749052799614], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:05:28,737 - INFO - API请求耗时: 359ms
2025-06-05 08:05:28,737 - INFO - Response - Page 1
2025-06-05 08:05:28,737 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-05 08:05:28,737 - INFO - 查询完成，共获取到 0 条记录
2025-06-05 08:05:28,737 - WARNING - 分段 31 查询返回空数据
2025-06-05 08:05:29,753 - INFO - 宜搭每日表单数据查询完成，共 31 个分段，成功获取 12484 条记录，失败 0 次
2025-06-05 08:05:29,753 - INFO - 成功获取宜搭日销售表单数据，共 12484 条记录
2025-06-05 08:05:29,753 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-05 08:05:29,753 - INFO - 开始对比和同步日销售数据...
2025-06-05 08:05:30,081 - INFO - 成功创建宜搭日销售数据索引，共 6341 条记录
2025-06-05 08:05:30,081 - INFO - 开始处理数衍数据，共 12883 条记录
2025-06-05 08:05:30,706 - INFO - 更新表单数据成功: FINST-737662B1WNUV5VLPDVZ5RCYXND773F2CYKEBMRY
2025-06-05 08:05:30,706 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7059.0, 'new_value': 3067.0}, {'field': 'amount', 'old_value': 7059.0, 'new_value': 3067.0}]
2025-06-05 08:05:31,237 - INFO - 更新表单数据成功: FINST-737662B1WNUV5VLPDVZ5RCYXND773F2CYKEBMZY
2025-06-05 08:05:31,237 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2651.96, 'new_value': 3381.0}, {'field': 'amount', 'old_value': 2651.96, 'new_value': 3381.0}, {'field': 'count', 'old_value': 178, 'new_value': 179}, {'field': 'instoreAmount', 'old_value': 758.94, 'new_value': 1487.98}, {'field': 'instoreCount', 'old_value': 47, 'new_value': 48}]
2025-06-05 08:05:31,659 - INFO - 更新表单数据成功: FINST-737662B1WNUV5VLPDVZ5RCYXND773F2CYKEBMDZ
2025-06-05 08:05:31,659 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6898.1}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6898.1}]
2025-06-05 08:05:32,081 - INFO - 更新表单数据成功: FINST-737662B1WNUV5VLPDVZ5RCYXND773F2CYKEBMLZ
2025-06-05 08:05:32,081 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 9916.01, 'new_value': 10500.0}, {'field': 'amount', 'old_value': 9916.01, 'new_value': 10500.0}, {'field': 'count', 'old_value': 174, 'new_value': 175}, {'field': 'instoreAmount', 'old_value': 9322.93, 'new_value': 9906.92}, {'field': 'instoreCount', 'old_value': 165, 'new_value': 166}]
2025-06-05 08:05:32,519 - INFO - 更新表单数据成功: FINST-737662B1WNUV5VLPDVZ5RCYXND773F2CYKEBMNZ
2025-06-05 08:05:32,519 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7796.64, 'new_value': 7186.92}, {'field': 'dailyBillAmount', 'old_value': 7796.64, 'new_value': 7186.92}]
2025-06-05 08:05:33,081 - INFO - 更新表单数据成功: FINST-AI866781PPXVA2K467VLNAJT99EL20WMYKEBMEJ
2025-06-05 08:05:33,081 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6707.0, 'new_value': 2561.0}, {'field': 'amount', 'old_value': 6707.0, 'new_value': 2561.0}]
2025-06-05 08:05:33,534 - INFO - 更新表单数据成功: FINST-AI866781PPXVA2K467VLNAJT99EL20WMYKEBMMJ
2025-06-05 08:05:33,534 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3092.07, 'new_value': 3924.0}, {'field': 'amount', 'old_value': 3092.0699999999997, 'new_value': 3924.0}, {'field': 'count', 'old_value': 219, 'new_value': 220}, {'field': 'instoreAmount', 'old_value': 828.56, 'new_value': 1660.49}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 36}]
2025-06-05 08:05:34,050 - INFO - 更新表单数据成功: FINST-AI866781PPXVA2K467VLNAJT99EL20WMYKEBM0K
2025-06-05 08:05:34,050 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8266.3}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8266.3}]
2025-06-05 08:05:34,597 - INFO - 更新表单数据成功: FINST-AI866781PPXVA2K467VLNAJT99EL20WMYKEBM8K
2025-06-05 08:05:34,597 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 8438.55, 'new_value': 8637.0}, {'field': 'amount', 'old_value': 8438.55, 'new_value': 8637.0}, {'field': 'count', 'old_value': 142, 'new_value': 143}, {'field': 'instoreAmount', 'old_value': 7969.69, 'new_value': 8168.14}, {'field': 'instoreCount', 'old_value': 134, 'new_value': 135}]
2025-06-05 08:05:35,112 - INFO - 更新表单数据成功: FINST-AI866781PPXVA2K467VLNAJT99EL20WMYKEBMAK
2025-06-05 08:05:35,112 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7731.21, 'new_value': 6994.84}, {'field': 'dailyBillAmount', 'old_value': 7731.21, 'new_value': 6994.84}]
2025-06-05 08:05:35,534 - INFO - 更新表单数据成功: FINST-7PF66BA1W5RVRU3IA9JW496PUGGC3DGXYKEBM761
2025-06-05 08:05:35,534 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 749.31, 'new_value': 2524.45}, {'field': 'dailyBillAmount', 'old_value': 749.31, 'new_value': 2524.45}]
2025-06-05 08:05:36,034 - INFO - 更新表单数据成功: FINST-7PF66BA1W5RVRU3IA9JW496PUGGC3DGXYKEBM961
2025-06-05 08:05:36,034 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5640.0, 'new_value': 2977.0}, {'field': 'amount', 'old_value': 5640.0, 'new_value': 2977.0}]
2025-06-05 08:05:36,425 - INFO - 更新表单数据成功: FINST-7PF66BA1W5RVRU3IA9JW496PUGGC3DGXYKEBMH61
2025-06-05 08:05:36,425 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2135.82, 'new_value': 3231.0}, {'field': 'amount', 'old_value': 2135.82, 'new_value': 3231.0}, {'field': 'count', 'old_value': 195, 'new_value': 196}, {'field': 'instoreAmount', 'old_value': 271.76, 'new_value': 1366.94}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 22}]
2025-06-05 08:05:36,800 - INFO - 更新表单数据成功: FINST-7PF66BA1W5RVRU3IA9JW496PUGGC3EGXYKEBMU61
2025-06-05 08:05:36,800 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5347.4}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5347.4}]
2025-06-05 08:05:37,190 - INFO - 更新表单数据成功: FINST-7PF66BA1W5RVRU3IA9JW496PUGGC3EGXYKEBM271
2025-06-05 08:05:37,190 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 8834.81, 'new_value': 7939.0}, {'field': 'amount', 'old_value': 8834.81, 'new_value': 7939.0}]
2025-06-05 08:05:37,550 - INFO - 更新表单数据成功: FINST-7PF66BA1W5RVRU3IA9JW496PUGGC3EGXYKEBM471
2025-06-05 08:05:37,550 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6136.03, 'new_value': 5559.64}, {'field': 'dailyBillAmount', 'old_value': 6136.03, 'new_value': 5559.64}]
2025-06-05 08:05:38,034 - INFO - 更新表单数据成功: FINST-U896687114SV8QAXDVWZY9O67THR3ZL5ZKEBMMQ
2025-06-05 08:05:38,034 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5634.3}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5634.3}]
2025-06-05 08:05:38,472 - INFO - 更新表单数据成功: FINST-X2F66HC1NFPVB5H07CQDXCCW2UKH2LA8ZKEBMXO
2025-06-05 08:05:38,472 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1913.98, 'new_value': 2269.93}, {'field': 'dailyBillAmount', 'old_value': 1913.98, 'new_value': 2269.93}]
2025-06-05 08:05:38,987 - INFO - 更新表单数据成功: FINST-X2F66HC1NFPVB5H07CQDXCCW2UKH2LA8ZKEBMZO
2025-06-05 08:05:38,987 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5852.0, 'new_value': 2928.0}, {'field': 'amount', 'old_value': 5852.0, 'new_value': 2928.0}]
2025-06-05 08:05:39,425 - INFO - 更新表单数据成功: FINST-X2F66HC1NFPVB5H07CQDXCCW2UKH2LA8ZKEBM7P
2025-06-05 08:05:39,425 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2800.31, 'new_value': 3175.0}, {'field': 'amount', 'old_value': 2800.31, 'new_value': 3175.0}, {'field': 'count', 'old_value': 191, 'new_value': 192}, {'field': 'instoreAmount', 'old_value': 802.14, 'new_value': 1176.83}, {'field': 'instoreCount', 'old_value': 77, 'new_value': 78}]
2025-06-05 08:05:39,894 - INFO - 更新表单数据成功: FINST-X2F66HC1NFPVB5H07CQDXCCW2UKH2LA8ZKEBMTP
2025-06-05 08:05:39,894 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5348.05, 'new_value': 4544.0}, {'field': 'amount', 'old_value': 5348.05, 'new_value': 4544.0}]
2025-06-05 08:05:40,362 - INFO - 更新表单数据成功: FINST-X2F66HC1NFPVB5H07CQDXCCW2UKH2LA8ZKEBMVP
2025-06-05 08:05:40,362 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5347.91}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5347.91}]
2025-06-05 08:05:40,815 - INFO - 更新表单数据成功: FINST-LFA66G91UIUV3ITZDDLSWCJZCJ6Z191JZKEBMP7
2025-06-05 08:05:40,815 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 891.25, 'new_value': 1339.25}, {'field': 'dailyBillAmount', 'old_value': 891.25, 'new_value': 1339.25}]
2025-06-05 08:05:41,269 - INFO - 更新表单数据成功: FINST-LFA66G91UIUV3ITZDDLSWCJZCJ6Z191JZKEBMR7
2025-06-05 08:05:41,269 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5736.0, 'new_value': 2960.0}, {'field': 'amount', 'old_value': 5736.0, 'new_value': 2960.0}]
2025-06-05 08:05:41,769 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH25PLZKEBMGT
2025-06-05 08:05:41,769 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2397.47, 'new_value': 2856.0}, {'field': 'amount', 'old_value': 2397.47, 'new_value': 2856.0}, {'field': 'count', 'old_value': 153, 'new_value': 154}, {'field': 'instoreAmount', 'old_value': 493.72, 'new_value': 952.25}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 34}]
2025-06-05 08:05:42,190 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26PLZKEBM2U
2025-06-05 08:05:42,190 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 8384.25, 'new_value': 8373.0}, {'field': 'amount', 'old_value': 8384.25, 'new_value': 8373.0}]
2025-06-05 08:05:42,612 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH26PLZKEBM4U
2025-06-05 08:05:42,612 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7595.64}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7595.64}]
2025-06-05 08:05:43,050 - INFO - 更新表单数据成功: FINST-W4G66DA1J7XV5F089P823C8RE1X03BYQZKEBM42
2025-06-05 08:05:43,050 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2794.2}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2794.2}]
2025-06-05 08:05:43,456 - INFO - 更新表单数据成功: FINST-3PF66X616BVVT8UG9XAO48AGNFR42B8WZKEBMFD
2025-06-05 08:05:43,456 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1971.5, 'new_value': 2614.36}, {'field': 'dailyBillAmount', 'old_value': 1971.5, 'new_value': 2614.36}]
2025-06-05 08:05:43,925 - INFO - 更新表单数据成功: FINST-3PF66X616BVVT8UG9XAO48AGNFR42B8WZKEBMHD
2025-06-05 08:05:43,925 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6204.0, 'new_value': 2043.0}, {'field': 'amount', 'old_value': 6204.0, 'new_value': 2043.0}]
2025-06-05 08:05:44,378 - INFO - 更新表单数据成功: FINST-3PF66X616BVVT8UG9XAO48AGNFR42B8WZKEBMPD
2025-06-05 08:05:44,378 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2857.47, 'new_value': 2786.0}, {'field': 'amount', 'old_value': 2857.47, 'new_value': 2786.0}]
2025-06-05 08:05:44,831 - INFO - 更新表单数据成功: FINST-3PF66X616BVVT8UG9XAO48AGNFR42B8WZKEBM9E
2025-06-05 08:05:44,831 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6988.14, 'new_value': 7375.0}, {'field': 'amount', 'old_value': 6988.14, 'new_value': 7375.0}, {'field': 'count', 'old_value': 144, 'new_value': 145}, {'field': 'instoreAmount', 'old_value': 6292.06, 'new_value': 6678.92}, {'field': 'instoreCount', 'old_value': 136, 'new_value': 137}]
2025-06-05 08:05:45,253 - INFO - 更新表单数据成功: FINST-3PF66X616BVVT8UG9XAO48AGNFR42B8WZKEBMBE
2025-06-05 08:05:45,253 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6251.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6251.7}]
2025-06-05 08:05:45,800 - INFO - 更新表单数据成功: FINST-S0E660A1SISV563KCERY4CPODIZZ1XI10LEBM131
2025-06-05 08:05:45,800 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4593.74}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4593.74}]
2025-06-05 08:05:46,237 - INFO - 更新表单数据成功: FINST-OLF66581LJVVLM65AA3PT41N7E1X2Z740LEBMX21
2025-06-05 08:05:46,237 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_********, 变更字段: [{'field': 'amount', 'old_value': 916.9, 'new_value': 1144.9}, {'field': 'count', 'old_value': 3, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 916.9, 'new_value': 1144.9}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 5}]
2025-06-05 08:05:46,690 - INFO - 更新表单数据成功: FINST-AJF66F71V8UV548O96RVVAT4GJKN31W60LEBMAG
2025-06-05 08:05:46,690 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2016.47, 'new_value': 2856.41}, {'field': 'dailyBillAmount', 'old_value': 2016.47, 'new_value': 2856.41}]
2025-06-05 08:05:47,144 - INFO - 更新表单数据成功: FINST-AJF66F71V8UV548O96RVVAT4GJKN31W60LEBMCG
2025-06-05 08:05:47,144 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4146.0, 'new_value': 1530.0}, {'field': 'amount', 'old_value': 4146.0, 'new_value': 1530.0}]
2025-06-05 08:05:47,612 - INFO - 更新表单数据成功: FINST-AJF66F71V8UV548O96RVVAT4GJKN31W60LEBMKG
2025-06-05 08:05:47,612 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2592.06, 'new_value': 3010.0}, {'field': 'amount', 'old_value': 2592.06, 'new_value': 3010.0}, {'field': 'count', 'old_value': 180, 'new_value': 181}, {'field': 'instoreAmount', 'old_value': 765.17, 'new_value': 1183.11}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 51}]
2025-06-05 08:05:48,050 - INFO - 更新表单数据成功: FINST-AJF66F71V8UV548O96RVVAT4GJKN32W60LEBM6H
2025-06-05 08:05:48,050 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6821.79, 'new_value': 6881.0}, {'field': 'amount', 'old_value': 6821.79, 'new_value': 6881.0}, {'field': 'count', 'old_value': 136, 'new_value': 137}, {'field': 'instoreAmount', 'old_value': 6674.89, 'new_value': 6734.1}, {'field': 'instoreCount', 'old_value': 132, 'new_value': 133}]
2025-06-05 08:05:48,456 - INFO - 更新表单数据成功: FINST-AJF66F71V8UV548O96RVVAT4GJKN32W60LEBM8H
2025-06-05 08:05:48,456 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7636.02}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7636.02}]
2025-06-05 08:05:48,909 - INFO - 更新表单数据成功: FINST-6IF66PC1Q6VVD8ZUB1S4TCQI80D03OTE0LEBMBG
2025-06-05 08:05:48,909 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5566.34}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5566.34}]
2025-06-05 08:05:49,409 - INFO - 更新表单数据成功: FINST-2FD66I71X8VVZ6ZFEIR6JDTAKMJ62BHH0LEBMTL
2025-06-05 08:05:49,409 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1942.47, 'new_value': 2635.99}, {'field': 'dailyBillAmount', 'old_value': 1942.47, 'new_value': 2635.99}]
2025-06-05 08:05:49,878 - INFO - 更新表单数据成功: FINST-2FD66I71X8VVZ6ZFEIR6JDTAKMJ62BHH0LEBMVL
2025-06-05 08:05:49,878 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4974.0, 'new_value': 1472.0}, {'field': 'amount', 'old_value': 4974.0, 'new_value': 1472.0}]
2025-06-05 08:05:50,393 - INFO - 更新表单数据成功: FINST-2FD66I71X8VVZ6ZFEIR6JDTAKMJ62BHH0LEBM3M
2025-06-05 08:05:50,393 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2933.04, 'new_value': 3592.0}, {'field': 'amount', 'old_value': 2933.04, 'new_value': 3592.0}, {'field': 'count', 'old_value': 223, 'new_value': 224}, {'field': 'instoreAmount', 'old_value': 1000.52, 'new_value': 1659.48}, {'field': 'instoreCount', 'old_value': 114, 'new_value': 115}]
2025-06-05 08:05:50,831 - INFO - 更新表单数据成功: FINST-2FD66I71X8VVZ6ZFEIR6JDTAKMJ62CHH0LEBMOM
2025-06-05 08:05:50,831 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6860.01, 'new_value': 6788.0}, {'field': 'amount', 'old_value': 6860.01, 'new_value': 6788.0}]
2025-06-05 08:05:51,268 - INFO - 更新表单数据成功: FINST-2FD66I71X8VVZ6ZFEIR6JDTAKMJ62CHH0LEBMQM
2025-06-05 08:05:51,268 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10124.49}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10124.49}]
2025-06-05 08:05:51,690 - INFO - 更新表单数据成功: FINST-K7666JC1UNUVYU0Y7ZHGWAQA946G2KDP0LEBMXR
2025-06-05 08:05:51,690 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_********, 变更字段: [{'field': 'amount', 'old_value': 554.0, 'new_value': 2098.6}, {'field': 'count', 'old_value': 2, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 554.0, 'new_value': 2098.6}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 9}]
2025-06-05 08:05:52,128 - INFO - 更新表单数据成功: FINST-UW966371CESVP1N8ACK6C8AQV8E3391S0LEBMAL
2025-06-05 08:05:52,128 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 634.65, 'new_value': 647.45}, {'field': 'dailyBillAmount', 'old_value': 634.65, 'new_value': 647.45}]
2025-06-05 08:05:52,659 - INFO - 更新表单数据成功: FINST-UW966371CESVP1N8ACK6C8AQV8E3391S0LEBMCL
2025-06-05 08:05:52,659 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4514.0, 'new_value': 2881.0}, {'field': 'amount', 'old_value': 4514.0, 'new_value': 2881.0}]
2025-06-05 08:05:53,128 - INFO - 更新表单数据成功: FINST-UW966371CESVP1N8ACK6C8AQV8E3391S0LEBMKL
2025-06-05 08:05:53,128 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3125.42, 'new_value': 3384.0}, {'field': 'amount', 'old_value': 3125.4199999999996, 'new_value': 3384.0}, {'field': 'count', 'old_value': 177, 'new_value': 178}, {'field': 'instoreAmount', 'old_value': 942.56, 'new_value': 1201.14}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 74}]
2025-06-05 08:05:53,503 - INFO - 更新表单数据成功: FINST-AEF66BC1DHVVYX8P8M6CI7Z5UVCK2FQU0LEBMUB
2025-06-05 08:05:53,503 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 11658.98, 'new_value': 10700.0}, {'field': 'amount', 'old_value': 11658.98, 'new_value': 10700.0}]
2025-06-05 08:05:53,972 - INFO - 更新表单数据成功: FINST-AEF66BC1DHVVYX8P8M6CI7Z5UVCK2FQU0LEBMWB
2025-06-05 08:05:53,972 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7797.24}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7797.24}]
2025-06-05 08:05:54,534 - INFO - 更新表单数据成功: FINST-1MD668B1C8YVSZY06TVIHCOJO78L2S201LEBMY1
2025-06-05 08:05:54,534 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3494.59}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3494.59}]
2025-06-05 08:05:55,018 - INFO - 更新表单数据成功: FINST-MLF669B1DCVVELABDZ63O84EN5EL2OE51LEBMRL
2025-06-05 08:05:55,018 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2576.43, 'new_value': 3443.12}, {'field': 'dailyBillAmount', 'old_value': 2576.43, 'new_value': 3443.12}]
2025-06-05 08:05:55,534 - INFO - 更新表单数据成功: FINST-MLF669B1DCVVELABDZ63O84EN5EL2OE51LEBMTL
2025-06-05 08:05:55,534 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4281.0, 'new_value': 1730.0}, {'field': 'amount', 'old_value': 4281.0, 'new_value': 1730.0}]
2025-06-05 08:05:56,143 - INFO - 更新表单数据成功: FINST-MLF669B1DCVVELABDZ63O84EN5EL2OE51LEBM1M
2025-06-05 08:05:56,143 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3343.19, 'new_value': 3805.0}, {'field': 'amount', 'old_value': 3343.19, 'new_value': 3805.0}, {'field': 'count', 'old_value': 197, 'new_value': 198}, {'field': 'instoreAmount', 'old_value': 964.48, 'new_value': 1426.29}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 44}]
2025-06-05 08:05:56,722 - INFO - 更新表单数据成功: FINST-MLF669B1DCVVELABDZ63O84EN5EL2PE51LEBMNM
2025-06-05 08:05:56,722 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 9263.94, 'new_value': 6927.0}, {'field': 'amount', 'old_value': 9263.94, 'new_value': 6927.0}]
2025-06-05 08:05:57,222 - INFO - 更新表单数据成功: FINST-MLF669B1DCVVELABDZ63O84EN5EL2PE51LEBMPM
2025-06-05 08:05:57,222 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6878.84}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6878.84}]
2025-06-05 08:05:57,753 - INFO - 更新表单数据成功: FINST-OJ666W71P8UVA76H95IY55E1GVRG3GWA1LEBM85
2025-06-05 08:05:57,753 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6139.72}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6139.72}]
2025-06-05 08:05:58,175 - INFO - 更新表单数据成功: FINST-Z7B66WA1LSVVD1CE9J24D9O1FVHI2V6G1LEBM5E
2025-06-05 08:05:58,175 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1942.69, 'new_value': 2399.79}, {'field': 'dailyBillAmount', 'old_value': 1942.69, 'new_value': 2399.79}]
2025-06-05 08:05:58,643 - INFO - 更新表单数据成功: FINST-Z7B66WA1LSVVD1CE9J24D9O1FVHI2V6G1LEBM7E
2025-06-05 08:05:58,643 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4968.0, 'new_value': 2195.0}, {'field': 'amount', 'old_value': 4968.0, 'new_value': 2195.0}]
2025-06-05 08:05:59,097 - INFO - 更新表单数据成功: FINST-Z7B66WA1LSVVD1CE9J24D9O1FVHI2V6G1LEBMFE
2025-06-05 08:05:59,097 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2311.52, 'new_value': 2410.0}, {'field': 'amount', 'old_value': 2311.52, 'new_value': 2410.0}, {'field': 'count', 'old_value': 100, 'new_value': 101}, {'field': 'instoreAmount', 'old_value': 783.54, 'new_value': 882.02}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 17}]
2025-06-05 08:05:59,503 - INFO - 更新表单数据成功: FINST-Z7B66WA1LSVVD1CE9J24D9O1FVHI2V6G1LEBM1F
2025-06-05 08:05:59,503 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 9636.74, 'new_value': 5604.0}, {'field': 'amount', 'old_value': 9636.74, 'new_value': 5604.0}]
2025-06-05 08:05:59,972 - INFO - 更新表单数据成功: FINST-Z7B66WA1LSVVD1CE9J24D9O1FVHI2V6G1LEBM3F
2025-06-05 08:05:59,972 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5062.11}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5062.11}]
2025-06-05 08:06:00,503 - INFO - 更新表单数据成功: FINST-OLC66Z61EGVVMOT49KHIGCA1FJ4Z1CQQ1LEBMSS
2025-06-05 08:06:00,503 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1556.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1556.0}]
2025-06-05 08:06:01,018 - INFO - 更新表单数据成功: FINST-OLC66Z61EGVVMOT49KHIGCA1FJ4Z1CQQ1LEBM0T
2025-06-05 08:06:01,018 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5030.0, 'new_value': 4046.0}, {'field': 'amount', 'old_value': 5030.0, 'new_value': 4046.0}]
2025-06-05 08:06:01,597 - INFO - 更新表单数据成功: FINST-OLC66Z61EGVVMOT49KHIGCA1FJ4Z1DQQ1LEBM8T
2025-06-05 08:06:01,597 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2814.81, 'new_value': 3223.0}, {'field': 'amount', 'old_value': 2814.81, 'new_value': 3223.0}, {'field': 'count', 'old_value': 153, 'new_value': 154}, {'field': 'instoreAmount', 'old_value': 732.79, 'new_value': 1140.98}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 29}]
2025-06-05 08:06:02,112 - INFO - 更新表单数据成功: FINST-OLC66Z61EGVVMOT49KHIGCA1FJ4Z1DQQ1LEBMTT
2025-06-05 08:06:02,112 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 9872.44, 'new_value': 8790.0}, {'field': 'amount', 'old_value': 9872.44, 'new_value': 8790.0}]
2025-06-05 08:06:02,581 - INFO - 更新表单数据成功: FINST-OLC66Z61EGVVMOT49KHIGCA1FJ4Z1DQQ1LEBMVT
2025-06-05 08:06:02,581 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7163.41}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7163.41}]
2025-06-05 08:06:03,081 - INFO - 更新表单数据成功: FINST-3PF66O71R9VV8NWIATMOQAUPDNL82RD12LEBMWN
2025-06-05 08:06:03,081 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2121.66, 'new_value': 2237.27}, {'field': 'dailyBillAmount', 'old_value': 2121.66, 'new_value': 2237.27}]
2025-06-05 08:06:03,503 - INFO - 更新表单数据成功: FINST-3PF66O71R9VV8NWIATMOQAUPDNL82SD12LEBMYN
2025-06-05 08:06:03,503 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5809.0, 'new_value': 844.0}, {'field': 'amount', 'old_value': 5809.0, 'new_value': 844.0}]
2025-06-05 08:06:03,972 - INFO - 更新表单数据成功: FINST-3PF66O71R9VV8NWIATMOQAUPDNL82SD12LEBM6O
2025-06-05 08:06:03,972 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1802.76, 'new_value': 2220.0}, {'field': 'amount', 'old_value': 1802.76, 'new_value': 2220.0}, {'field': 'count', 'old_value': 113, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 367.35, 'new_value': 784.59}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 18}]
2025-06-05 08:06:04,440 - INFO - 更新表单数据成功: FINST-****************************************
2025-06-05 08:06:04,440 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 8941.35, 'new_value': 7795.62}, {'field': 'amount', 'old_value': 8941.35, 'new_value': 7795.620000000001}]
2025-06-05 08:06:04,847 - INFO - 更新表单数据成功: FINST-3PF66X61ZHSVJXG1ETEMXCGNDZ992J242LEBM3A1
2025-06-05 08:06:04,847 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6090.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6090.9}]
2025-06-05 08:06:05,440 - INFO - 更新表单数据成功: FINST-6PF66691GDSVWTNYCTVNHBULRWKO28C92LEBMZS
2025-06-05 08:06:05,440 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6198.69}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6198.69}]
2025-06-05 08:06:05,909 - INFO - 更新表单数据成功: FINST-6PF66691GDSVWTNYCTVNHBULRWKO28C92LEBMYT
2025-06-05 08:06:05,909 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_********, 变更字段: [{'field': 'amount', 'old_value': 3426.37, 'new_value': 3374.97}]
2025-06-05 08:06:06,362 - INFO - 更新表单数据成功: FINST-IOC66G71LCYVYDGKCHUXA9AOYGNQ3O0C2LEBMG2
2025-06-05 08:06:06,362 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1697.49, 'new_value': 2888.26}, {'field': 'dailyBillAmount', 'old_value': 1697.49, 'new_value': 2888.26}]
2025-06-05 08:06:06,800 - INFO - 更新表单数据成功: FINST-IOC66G71LCYVYDGKCHUXA9AOYGNQ3O0C2LEBMI2
2025-06-05 08:06:06,800 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3855.0, 'new_value': 1727.0}, {'field': 'amount', 'old_value': 3855.0, 'new_value': 1727.0}]
2025-06-05 08:06:07,268 - INFO - 更新表单数据成功: FINST-IOC66G71LCYVYDGKCHUXA9AOYGNQ3O0C2LEBMQ2
2025-06-05 08:06:07,268 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2595.97, 'new_value': 2815.0}, {'field': 'amount', 'old_value': 2595.97, 'new_value': 2815.0}, {'field': 'count', 'old_value': 144, 'new_value': 145}, {'field': 'instoreAmount', 'old_value': 513.26, 'new_value': 732.29}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 24}]
2025-06-05 08:06:07,706 - INFO - 更新表单数据成功: FINST-YWD66FA1QLVVCVS06LIS8432MWW230OE2LEBMIR
2025-06-05 08:06:07,706 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 11498.33, 'new_value': 10000.0}, {'field': 'amount', 'old_value': 11498.33, 'new_value': 10000.0}]
2025-06-05 08:06:08,096 - INFO - 更新表单数据成功: FINST-YWD66FA1QLVVCVS06LIS8432MWW230OE2LEBMKR
2025-06-05 08:06:08,096 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5681.69}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5681.69}]
2025-06-05 08:06:08,597 - INFO - 更新表单数据成功: FINST-YWD66FA1QLVVCVS06LIS8432MWW230OE2LEBM3T
2025-06-05 08:06:08,597 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7964.56}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7964.56}]
2025-06-05 08:06:09,050 - INFO - 更新表单数据成功: FINST-F7D66UA197SVSZMPF3TX6BZD7NRJ2TYJ2LEBMPL
2025-06-05 08:06:09,050 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5475.73}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5475.73}]
2025-06-05 08:06:09,518 - INFO - 更新表单数据成功: FINST-XO8662C1AEUV3DNCC02EF5TKUH5T2TKM2LEBMG51
2025-06-05 08:06:09,518 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1738.89, 'new_value': 746.64}, {'field': 'dailyBillAmount', 'old_value': 1738.89, 'new_value': 746.64}]
2025-06-05 08:06:09,987 - INFO - 更新表单数据成功: FINST-EZD66RB16BVVD7LU8X9QJ56RYTYE3TAP2LEBMY9
2025-06-05 08:06:09,987 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7762.0, 'new_value': 3144.0}, {'field': 'amount', 'old_value': 7762.0, 'new_value': 3144.0}]
2025-06-05 08:06:10,487 - INFO - 更新表单数据成功: FINST-EZD66RB16BVVD7LU8X9QJ56RYTYE3TAP2LEBM6A
2025-06-05 08:06:10,487 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2736.73, 'new_value': 4428.0}, {'field': 'amount', 'old_value': 2736.73, 'new_value': 4428.0}, {'field': 'count', 'old_value': 162, 'new_value': 163}, {'field': 'instoreAmount', 'old_value': 448.02, 'new_value': 2139.29}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 24}]
2025-06-05 08:06:11,003 - INFO - 更新表单数据成功: FINST-EZD66RB16BVVD7LU8X9QJ56RYTYE3UAP2LEBMSA
2025-06-05 08:06:11,003 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 13516.25, 'new_value': 12700.0}, {'field': 'amount', 'old_value': 13516.25, 'new_value': 12700.0}]
2025-06-05 08:06:11,456 - INFO - 更新表单数据成功: FINST-EZD66RB16BVVD7LU8X9QJ56RYTYE3UAP2LEBMUA
2025-06-05 08:06:11,456 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7608.26}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7608.26}]
2025-06-05 08:06:12,003 - INFO - 更新表单数据成功: FINST-EZD66RB16BVVD7LU8X9QJ56RYTYE3UAP2LEBMEC
2025-06-05 08:06:12,003 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9805.18}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9805.18}]
2025-06-05 08:06:12,487 - INFO - 更新表单数据成功: FINST-PAB66N710QOV8VW1C4GQBC58Z2GM33OU2LEBM1M1
2025-06-05 08:06:12,487 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3448.12, 'new_value': 3447.43}, {'field': 'amount', 'old_value': 3448.1200000000003, 'new_value': 3447.4300000000003}]
2025-06-05 08:06:12,940 - INFO - 更新表单数据成功: FINST-PAB66N710QOV8VW1C4GQBC58Z2GM33OU2LEBMBM1
2025-06-05 08:06:12,940 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7725.24, 'new_value': 9465.24}, {'field': 'amount', 'old_value': 7725.24, 'new_value': 9465.24}, {'field': 'count', 'old_value': 97, 'new_value': 99}, {'field': 'instoreAmount', 'old_value': 7412.1, 'new_value': 9152.1}, {'field': 'instoreCount', 'old_value': 86, 'new_value': 88}]
2025-06-05 08:06:13,471 - INFO - 更新表单数据成功: FINST-PAB66N710QOV8VW1C4GQBC58Z2GM33OU2LEBMTN1
2025-06-05 08:06:13,471 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5899.85}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5899.85}]
2025-06-05 08:06:13,940 - INFO - 更新表单数据成功: FINST-3Z966E918CYVZYFMF58ZX8GZL5QK3IBX2LEBMR1
2025-06-05 08:06:13,940 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_********, 变更字段: [{'field': 'amount', 'old_value': 912.84, 'new_value': 916.34}, {'field': 'count', 'old_value': 33, 'new_value': 34}, {'field': 'onlineAmount', 'old_value': 601.14, 'new_value': 604.64}, {'field': 'onlineCount', 'old_value': 26, 'new_value': 27}]
2025-06-05 08:06:14,362 - INFO - 更新表单数据成功: FINST-3Z966E918CYVZYFMF58ZX8GZL5QK3IBX2LEBMB2
2025-06-05 08:06:14,362 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_********, 变更字段: [{'field': 'count', 'old_value': 23, 'new_value': 24}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 24}]
2025-06-05 08:06:14,768 - INFO - 更新表单数据成功: FINST-7PF66N9127UVOP6O8CH4U8KBFC1B2BYZ2LEBM26
2025-06-05 08:06:14,768 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5306.21, 'new_value': 5829.71}, {'field': 'amount', 'old_value': 5306.21, 'new_value': 5829.71}, {'field': 'count', 'old_value': 115, 'new_value': 120}, {'field': 'instoreAmount', 'old_value': 3723.3, 'new_value': 4246.8}, {'field': 'instoreCount', 'old_value': 81, 'new_value': 86}]
2025-06-05 08:06:15,284 - INFO - 更新表单数据成功: FINST-7PF66N9127UVOP6O8CH4U8KBFC1B2CYZ2LEBMP7
2025-06-05 08:06:15,284 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'amount', 'old_value': 6319.67, 'new_value': 6414.05}, {'field': 'count', 'old_value': 235, 'new_value': 241}, {'field': 'instoreAmount', 'old_value': 2480.8, 'new_value': 2493.4}, {'field': 'instoreCount', 'old_value': 86, 'new_value': 87}, {'field': 'onlineAmount', 'old_value': 4100.17, 'new_value': 4181.95}, {'field': 'onlineCount', 'old_value': 149, 'new_value': 154}]
2025-06-05 08:06:15,612 - INFO - 更新表单数据成功: FINST-737662B1HOUVTCBWDF3FW5NRH8I63ZL23LEBMZ8
2025-06-05 08:06:15,612 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_********, 变更字段: [{'field': 'amount', 'old_value': 45661.310000000005, 'new_value': 45666.71000000001}, {'field': 'count', 'old_value': 335, 'new_value': 336}, {'field': 'instoreAmount', 'old_value': 41500.47, 'new_value': 41505.87}, {'field': 'instoreCount', 'old_value': 193, 'new_value': 194}]
2025-06-05 08:06:16,034 - INFO - 更新表单数据成功: FINST-737662B1HOUVTCBWDF3FW5NRH8I63ZL23LEBMNA
2025-06-05 08:06:16,034 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_********, 变更字段: [{'field': 'amount', 'old_value': 5575.59, 'new_value': 5575.6}, {'field': 'count', 'old_value': 440, 'new_value': 441}, {'field': 'onlineAmount', 'old_value': 5371.82, 'new_value': 5371.83}, {'field': 'onlineCount', 'old_value': 402, 'new_value': 403}]
2025-06-05 08:06:16,440 - INFO - 更新表单数据成功: FINST-737662B1HOUVTCBWDF3FW5NRH8I63ZL23LEBM9B
2025-06-05 08:06:16,440 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_********, 变更字段: [{'field': 'count', 'old_value': 66, 'new_value': 67}, {'field': 'instoreCount', 'old_value': 66, 'new_value': 67}]
2025-06-05 08:06:16,862 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2HB53LEBMVD
2025-06-05 08:06:16,862 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3950.17}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3950.17}]
2025-06-05 08:06:17,284 - INFO - 更新表单数据成功: FINST-N79668C1GBVVHGYR8V4OZ6P3WVYK2HB53LEBM5E
2025-06-05 08:06:17,284 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_********, 变更字段: [{'field': 'amount', 'old_value': 1056.17, 'new_value': 1045.3700000000001}]
2025-06-05 08:06:17,800 - INFO - 更新表单数据成功: FINST-R1A66H91MXZV61RX93R936YNEK9D3IWNY6HBM23
2025-06-05 08:06:17,800 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2669.5}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2669.5}]
2025-06-05 08:06:18,190 - INFO - 更新表单数据成功: FINST-R1A66H91MXZV61RX93R936YNEK9D3IWNY6HBMA3
2025-06-05 08:06:18,190 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_********, 变更字段: [{'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 11492.0}]
2025-06-05 08:06:18,675 - INFO - 更新表单数据成功: FINST-R1A66H91MXZV61RX93R936YNEK9D3IWNY6HBMO3
2025-06-05 08:06:18,675 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_********, 变更字段: [{'field': 'amount', 'old_value': 1419.55, 'new_value': 1449.15}, {'field': 'count', 'old_value': 44, 'new_value': 45}, {'field': 'onlineAmount', 'old_value': 127.45, 'new_value': 157.05}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 5}]
2025-06-05 08:06:19,143 - INFO - 更新表单数据成功: FINST-R1A66H91MXZV61RX93R936YNEK9D3IWNY6HBMW3
2025-06-05 08:06:19,143 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_********, 变更字段: [{'field': 'amount', 'old_value': 2964.98, 'new_value': 2973.28}, {'field': 'count', 'old_value': 136, 'new_value': 137}, {'field': 'onlineAmount', 'old_value': 1662.1, 'new_value': 1670.4}, {'field': 'onlineCount', 'old_value': 66, 'new_value': 67}]
2025-06-05 08:06:19,487 - INFO - 更新表单数据成功: FINST-R1A66H91MXZV61RX93R936YNEK9D3IWNY6HBM24
2025-06-05 08:06:19,487 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_********, 变更字段: [{'field': 'amount', 'old_value': 823.7, 'new_value': 831.81}, {'field': 'count', 'old_value': 48, 'new_value': 50}, {'field': 'onlineAmount', 'old_value': 610.7, 'new_value': 618.81}, {'field': 'onlineCount', 'old_value': 38, 'new_value': 40}]
2025-06-05 08:06:19,956 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM23LQY6HBM8B
2025-06-05 08:06:19,956 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_********, 变更字段: [{'field': 'amount', 'old_value': 115.0, 'new_value': 404.0}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 115.0, 'new_value': 404.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-05 08:06:20,409 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBMNB
2025-06-05 08:06:20,409 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1718.96, 'new_value': 1785.06}, {'field': 'amount', 'old_value': 1718.96, 'new_value': 1785.06}, {'field': 'count', 'old_value': 115, 'new_value': 121}, {'field': 'instoreAmount', 'old_value': 475.95, 'new_value': 486.95}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 23}, {'field': 'onlineAmount', 'old_value': 1243.01, 'new_value': 1298.11}, {'field': 'onlineCount', 'old_value': 93, 'new_value': 98}]
2025-06-05 08:06:21,065 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBMYB
2025-06-05 08:06:21,065 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 8630.84, 'new_value': 8702.24}, {'field': 'amount', 'old_value': 8630.84, 'new_value': 8702.24}, {'field': 'count', 'old_value': 155, 'new_value': 158}, {'field': 'instoreAmount', 'old_value': 8050.2, 'new_value': 8121.6}, {'field': 'instoreCount', 'old_value': 137, 'new_value': 140}]
2025-06-05 08:06:21,565 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBMZB
2025-06-05 08:06:21,565 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'amount', 'old_value': 2462.76, 'new_value': 2502.27}, {'field': 'count', 'old_value': 139, 'new_value': 142}, {'field': 'instoreAmount', 'old_value': 426.46, 'new_value': 433.97}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 44}, {'field': 'onlineAmount', 'old_value': 2161.0, 'new_value': 2193.0}, {'field': 'onlineCount', 'old_value': 96, 'new_value': 98}]
2025-06-05 08:06:22,034 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBM2C
2025-06-05 08:06:22,034 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 853.48, 'new_value': 877.68}, {'field': 'amount', 'old_value': 853.48, 'new_value': 877.68}, {'field': 'count', 'old_value': 63, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 176.7, 'new_value': 192.7}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 12}, {'field': 'onlineAmount', 'old_value': 689.38, 'new_value': 697.58}]
2025-06-05 08:06:22,550 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBM7C
2025-06-05 08:06:22,550 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 623.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 623.0}]
2025-06-05 08:06:22,971 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBM8C
2025-06-05 08:06:22,971 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_********, 变更字段: [{'field': 'amount', 'old_value': 3242.18, 'new_value': 3374.91}, {'field': 'count', 'old_value': 63, 'new_value': 67}, {'field': 'instoreAmount', 'old_value': 2001.69, 'new_value': 2134.42}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 17}]
2025-06-05 08:06:23,487 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBMBC
2025-06-05 08:06:23,487 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 13147.19}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 13147.19}]
2025-06-05 08:06:23,987 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBMGC
2025-06-05 08:06:23,987 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_********, 变更字段: [{'field': 'amount', 'old_value': 20458.9, 'new_value': 20658.9}, {'field': 'count', 'old_value': 163, 'new_value': 164}, {'field': 'instoreAmount', 'old_value': 6409.5, 'new_value': 6609.5}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 35}]
2025-06-05 08:06:24,393 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBMRC
2025-06-05 08:06:24,393 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_********, 变更字段: [{'field': 'amount', 'old_value': 9695.38, 'new_value': 9888.78}, {'field': 'count', 'old_value': 216, 'new_value': 218}, {'field': 'instoreAmount', 'old_value': 6737.76, 'new_value': 6780.96}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 44}, {'field': 'onlineAmount', 'old_value': 3093.37, 'new_value': 3243.57}, {'field': 'onlineCount', 'old_value': 173, 'new_value': 174}]
2025-06-05 08:06:24,815 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBM4D
2025-06-05 08:06:24,815 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_********, 变更字段: [{'field': 'amount', 'old_value': 1488.0, 'new_value': 1276.0}, {'field': 'count', 'old_value': 5, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 1488.0, 'new_value': 1615.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 6}]
2025-06-05 08:06:25,190 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBM5D
2025-06-05 08:06:25,190 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2004.4}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2004.4}]
2025-06-05 08:06:25,706 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBMCD
2025-06-05 08:06:25,706 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2512.6}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2512.6}]
2025-06-05 08:06:26,175 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBMHD
2025-06-05 08:06:26,175 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_********, 变更字段: [{'field': 'amount', 'old_value': 3742.02, 'new_value': 3788.2200000000003}, {'field': 'count', 'old_value': 236, 'new_value': 243}, {'field': 'onlineAmount', 'old_value': 3824.62, 'new_value': 3870.82}, {'field': 'onlineCount', 'old_value': 232, 'new_value': 239}]
2025-06-05 08:06:26,596 - INFO - 更新表单数据成功: FINST-EEC66XC1QPZVJTZS9X6WV5IF7RKB395TY6HBMH9
2025-06-05 08:06:26,596 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_********, 变更字段: [{'field': 'amount', 'old_value': 4482.25, 'new_value': 4461.650000000001}, {'field': 'count', 'old_value': 286, 'new_value': 288}, {'field': 'instoreAmount', 'old_value': 3064.25, 'new_value': 3069.65}, {'field': 'instoreCount', 'old_value': 185, 'new_value': 190}, {'field': 'onlineAmount', 'old_value': 1483.7, 'new_value': 1481.4}, {'field': 'onlineCount', 'old_value': 101, 'new_value': 98}]
2025-06-05 08:06:27,034 - INFO - 更新表单数据成功: FINST-EEC66XC1QPZVJTZS9X6WV5IF7RKB395TY6HBMK9
2025-06-05 08:06:27,034 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 4094.63, 'new_value': 4235.13}, {'field': 'count', 'old_value': 68, 'new_value': 71}, {'field': 'onlineAmount', 'old_value': 1017.99, 'new_value': 1158.49}, {'field': 'onlineCount', 'old_value': 23, 'new_value': 26}]
2025-06-05 08:06:27,471 - INFO - 更新表单数据成功: FINST-EEC66XC1QPZVJTZS9X6WV5IF7RKB395TY6HBMM9
2025-06-05 08:06:27,471 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_********, 变更字段: [{'field': 'amount', 'old_value': 4632.83, 'new_value': 4905.73}, {'field': 'count', 'old_value': 80, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 3838.35, 'new_value': 3877.35}, {'field': 'instoreCount', 'old_value': 65, 'new_value': 66}, {'field': 'onlineAmount', 'old_value': 794.48, 'new_value': 1028.38}, {'field': 'onlineCount', 'old_value': 15, 'new_value': 19}]
2025-06-05 08:06:28,003 - INFO - 更新表单数据成功: FINST-EEC66XC1QPZVJTZS9X6WV5IF7RKB395TY6HBMS9
2025-06-05 08:06:28,003 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_********, 变更字段: [{'field': 'amount', 'old_value': 1620.3, 'new_value': 2008.3000000000002}, {'field': 'count', 'old_value': 16, 'new_value': 17}, {'field': 'instoreAmount', 'old_value': 388.0, 'new_value': 776.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-05 08:06:28,440 - INFO - 更新表单数据成功: FINST-EEC66XC1QPZVJTZS9X6WV5IF7RKB395TY6HBMU9
2025-06-05 08:06:28,440 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 24357.02}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 24357.02}]
2025-06-05 08:06:28,956 - INFO - 更新表单数据成功: FINST-EEC66XC1QPZVJTZS9X6WV5IF7RKB395TY6HBM2A
2025-06-05 08:06:28,956 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3209.97}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3209.97}, {'field': 'amount', 'old_value': 550.87, 'new_value': 573.17}, {'field': 'count', 'old_value': 17, 'new_value': 18}, {'field': 'instoreAmount', 'old_value': 426.78, 'new_value': 449.08}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 13}]
2025-06-05 08:06:29,424 - INFO - 更新表单数据成功: FINST-EEC66XC1QPZVJTZS9X6WV5IF7RKB395TY6HBM3A
2025-06-05 08:06:29,424 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_********, 变更字段: [{'field': 'amount', 'old_value': 11586.74, 'new_value': 11615.62}, {'field': 'count', 'old_value': 167, 'new_value': 169}, {'field': 'instoreAmount', 'old_value': 10250.72, 'new_value': 10279.6}, {'field': 'instoreCount', 'old_value': 101, 'new_value': 103}]
2025-06-05 08:06:29,862 - INFO - 更新表单数据成功: FINST-EEC66XC1QPZVJTZS9X6WV5IF7RKB395TY6HBM7A
2025-06-05 08:06:29,862 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_********, 变更字段: [{'field': 'amount', 'old_value': 8914.3, 'new_value': 10970.3}, {'field': 'count', 'old_value': 25, 'new_value': 26}, {'field': 'instoreAmount', 'old_value': 8836.0, 'new_value': 10892.0}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 25}]
2025-06-05 08:06:30,378 - INFO - 更新表单数据成功: FINST-EEC66XC1QPZVJTZS9X6WV5IF7RKB395TY6HBMBA
2025-06-05 08:06:30,378 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_********, 变更字段: [{'field': 'amount', 'old_value': 25411.699999999997, 'new_value': 30396.699999999997}, {'field': 'count', 'old_value': 114, 'new_value': 116}, {'field': 'instoreAmount', 'old_value': 25758.21, 'new_value': 30743.21}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 53}]
2025-06-05 08:06:30,831 - INFO - 更新表单数据成功: FINST-EEC66XC1QPZVJTZS9X6WV5IF7RKB395TY6HBMJA
2025-06-05 08:06:30,831 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_********, 变更字段: [{'field': 'amount', 'old_value': 19394.98, 'new_value': 20868.98}, {'field': 'count', 'old_value': 105, 'new_value': 107}, {'field': 'instoreAmount', 'old_value': 17989.34, 'new_value': 19463.34}, {'field': 'instoreCount', 'old_value': 83, 'new_value': 85}]
2025-06-05 08:06:31,331 - INFO - 更新表单数据成功: FINST-EEC66XC1QPZVJTZS9X6WV5IF7RKB395TY6HBMWA
2025-06-05 08:06:31,331 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5194.72}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5194.72}]
2025-06-05 08:06:31,784 - INFO - 更新表单数据成功: FINST-EEC66XC1QPZVJTZS9X6WV5IF7RKB395TY6HBM1B
2025-06-05 08:06:31,784 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_********, 变更字段: [{'field': 'amount', 'old_value': 994.08, 'new_value': 970.1100000000001}]
2025-06-05 08:06:31,924 - INFO - 正在批量插入每日数据，批次 1/66，共 100 条记录
2025-06-05 08:06:32,315 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-06-05 08:06:35,331 - INFO - 正在批量插入每日数据，批次 2/66，共 100 条记录
2025-06-05 08:06:35,768 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-06-05 08:06:38,784 - INFO - 正在批量插入每日数据，批次 3/66，共 100 条记录
2025-06-05 08:06:39,174 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-06-05 08:06:42,190 - INFO - 正在批量插入每日数据，批次 4/66，共 100 条记录
2025-06-05 08:06:42,643 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-06-05 08:06:45,659 - INFO - 正在批量插入每日数据，批次 5/66，共 100 条记录
2025-06-05 08:06:46,002 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-06-05 08:06:49,018 - INFO - 正在批量插入每日数据，批次 6/66，共 100 条记录
2025-06-05 08:06:49,518 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-06-05 08:06:52,534 - INFO - 正在批量插入每日数据，批次 7/66，共 100 条记录
2025-06-05 08:06:52,956 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-06-05 08:06:55,971 - INFO - 正在批量插入每日数据，批次 8/66，共 100 条记录
2025-06-05 08:06:56,377 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-06-05 08:06:59,393 - INFO - 正在批量插入每日数据，批次 9/66，共 100 条记录
2025-06-05 08:06:59,768 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-06-05 08:07:02,784 - INFO - 正在批量插入每日数据，批次 10/66，共 100 条记录
2025-06-05 08:07:03,159 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-06-05 08:07:06,174 - INFO - 正在批量插入每日数据，批次 11/66，共 100 条记录
2025-06-05 08:07:06,643 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-06-05 08:07:09,659 - INFO - 正在批量插入每日数据，批次 12/66，共 100 条记录
2025-06-05 08:07:10,080 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-06-05 08:07:13,096 - INFO - 正在批量插入每日数据，批次 13/66，共 100 条记录
2025-06-05 08:07:13,455 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-06-05 08:07:16,471 - INFO - 正在批量插入每日数据，批次 14/66，共 100 条记录
2025-06-05 08:07:16,893 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-06-05 08:07:19,909 - INFO - 正在批量插入每日数据，批次 15/66，共 100 条记录
2025-06-05 08:07:20,284 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-06-05 08:07:23,299 - INFO - 正在批量插入每日数据，批次 16/66，共 100 条记录
2025-06-05 08:07:23,752 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-06-05 08:07:26,768 - INFO - 正在批量插入每日数据，批次 17/66，共 100 条记录
2025-06-05 08:07:27,174 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-06-05 08:07:30,190 - INFO - 正在批量插入每日数据，批次 18/66，共 100 条记录
2025-06-05 08:07:30,627 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-06-05 08:07:33,643 - INFO - 正在批量插入每日数据，批次 19/66，共 100 条记录
2025-06-05 08:07:34,190 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-06-05 08:07:37,190 - INFO - 正在批量插入每日数据，批次 20/66，共 100 条记录
2025-06-05 08:07:37,752 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-06-05 08:07:40,768 - INFO - 正在批量插入每日数据，批次 21/66，共 100 条记录
2025-06-05 08:07:41,174 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-06-05 08:07:44,205 - INFO - 正在批量插入每日数据，批次 22/66，共 100 条记录
2025-06-05 08:07:44,674 - INFO - 批量插入每日数据成功，批次 22，100 条记录
2025-06-05 08:07:47,690 - INFO - 正在批量插入每日数据，批次 23/66，共 100 条记录
2025-06-05 08:07:48,111 - INFO - 批量插入每日数据成功，批次 23，100 条记录
2025-06-05 08:07:51,127 - INFO - 正在批量插入每日数据，批次 24/66，共 100 条记录
2025-06-05 08:07:51,549 - INFO - 批量插入每日数据成功，批次 24，100 条记录
2025-06-05 08:07:54,565 - INFO - 正在批量插入每日数据，批次 25/66，共 100 条记录
2025-06-05 08:07:54,986 - INFO - 批量插入每日数据成功，批次 25，100 条记录
2025-06-05 08:07:58,002 - INFO - 正在批量插入每日数据，批次 26/66，共 100 条记录
2025-06-05 08:07:58,518 - INFO - 批量插入每日数据成功，批次 26，100 条记录
2025-06-05 08:08:01,533 - INFO - 正在批量插入每日数据，批次 27/66，共 100 条记录
2025-06-05 08:08:01,877 - INFO - 批量插入每日数据成功，批次 27，100 条记录
2025-06-05 08:08:04,893 - INFO - 正在批量插入每日数据，批次 28/66，共 100 条记录
2025-06-05 08:08:05,314 - INFO - 批量插入每日数据成功，批次 28，100 条记录
2025-06-05 08:08:08,330 - INFO - 正在批量插入每日数据，批次 29/66，共 100 条记录
2025-06-05 08:08:08,721 - INFO - 批量插入每日数据成功，批次 29，100 条记录
2025-06-05 08:08:11,736 - INFO - 正在批量插入每日数据，批次 30/66，共 100 条记录
2025-06-05 08:08:12,096 - INFO - 批量插入每日数据成功，批次 30，100 条记录
2025-06-05 08:08:15,111 - INFO - 正在批量插入每日数据，批次 31/66，共 100 条记录
2025-06-05 08:08:15,564 - INFO - 批量插入每日数据成功，批次 31，100 条记录
2025-06-05 08:08:18,580 - INFO - 正在批量插入每日数据，批次 32/66，共 100 条记录
2025-06-05 08:08:18,971 - INFO - 批量插入每日数据成功，批次 32，100 条记录
2025-06-05 08:08:21,986 - INFO - 正在批量插入每日数据，批次 33/66，共 100 条记录
2025-06-05 08:08:22,424 - INFO - 批量插入每日数据成功，批次 33，100 条记录
2025-06-05 08:08:25,439 - INFO - 正在批量插入每日数据，批次 34/66，共 100 条记录
2025-06-05 08:08:25,814 - INFO - 批量插入每日数据成功，批次 34，100 条记录
2025-06-05 08:08:28,830 - INFO - 正在批量插入每日数据，批次 35/66，共 100 条记录
2025-06-05 08:08:29,221 - INFO - 批量插入每日数据成功，批次 35，100 条记录
2025-06-05 08:08:32,236 - INFO - 正在批量插入每日数据，批次 36/66，共 100 条记录
2025-06-05 08:08:32,658 - INFO - 批量插入每日数据成功，批次 36，100 条记录
2025-06-05 08:08:35,689 - INFO - 正在批量插入每日数据，批次 37/66，共 100 条记录
2025-06-05 08:08:36,080 - INFO - 批量插入每日数据成功，批次 37，100 条记录
2025-06-05 08:08:39,096 - INFO - 正在批量插入每日数据，批次 38/66，共 100 条记录
2025-06-05 08:08:39,486 - INFO - 批量插入每日数据成功，批次 38，100 条记录
2025-06-05 08:08:42,502 - INFO - 正在批量插入每日数据，批次 39/66，共 100 条记录
2025-06-05 08:08:43,017 - INFO - 批量插入每日数据成功，批次 39，100 条记录
2025-06-05 08:08:46,033 - INFO - 正在批量插入每日数据，批次 40/66，共 100 条记录
2025-06-05 08:08:46,455 - INFO - 批量插入每日数据成功，批次 40，100 条记录
2025-06-05 08:08:49,470 - INFO - 正在批量插入每日数据，批次 41/66，共 100 条记录
2025-06-05 08:08:49,877 - INFO - 批量插入每日数据成功，批次 41，100 条记录
2025-06-05 08:08:52,892 - INFO - 正在批量插入每日数据，批次 42/66，共 100 条记录
2025-06-05 08:08:53,424 - INFO - 批量插入每日数据成功，批次 42，100 条记录
2025-06-05 08:08:56,439 - INFO - 正在批量插入每日数据，批次 43/66，共 100 条记录
2025-06-05 08:08:56,799 - INFO - 批量插入每日数据成功，批次 43，100 条记录
2025-06-05 08:08:59,814 - INFO - 正在批量插入每日数据，批次 44/66，共 100 条记录
2025-06-05 08:09:00,252 - INFO - 批量插入每日数据成功，批次 44，100 条记录
2025-06-05 08:09:03,267 - INFO - 正在批量插入每日数据，批次 45/66，共 100 条记录
2025-06-05 08:09:03,767 - INFO - 批量插入每日数据成功，批次 45，100 条记录
2025-06-05 08:09:06,783 - INFO - 正在批量插入每日数据，批次 46/66，共 100 条记录
2025-06-05 08:09:07,220 - INFO - 批量插入每日数据成功，批次 46，100 条记录
2025-06-05 08:09:10,236 - INFO - 正在批量插入每日数据，批次 47/66，共 100 条记录
2025-06-05 08:09:10,705 - INFO - 批量插入每日数据成功，批次 47，100 条记录
2025-06-05 08:09:13,720 - INFO - 正在批量插入每日数据，批次 48/66，共 100 条记录
2025-06-05 08:09:14,111 - INFO - 批量插入每日数据成功，批次 48，100 条记录
2025-06-05 08:09:17,127 - INFO - 正在批量插入每日数据，批次 49/66，共 100 条记录
2025-06-05 08:09:17,548 - INFO - 批量插入每日数据成功，批次 49，100 条记录
2025-06-05 08:09:20,564 - INFO - 正在批量插入每日数据，批次 50/66，共 100 条记录
2025-06-05 08:09:21,048 - INFO - 批量插入每日数据成功，批次 50，100 条记录
2025-06-05 08:09:24,064 - INFO - 正在批量插入每日数据，批次 51/66，共 100 条记录
2025-06-05 08:09:24,501 - INFO - 批量插入每日数据成功，批次 51，100 条记录
2025-06-05 08:09:27,517 - INFO - 正在批量插入每日数据，批次 52/66，共 100 条记录
2025-06-05 08:09:27,923 - INFO - 批量插入每日数据成功，批次 52，100 条记录
2025-06-05 08:09:30,923 - INFO - 正在批量插入每日数据，批次 53/66，共 100 条记录
2025-06-05 08:09:31,298 - INFO - 批量插入每日数据成功，批次 53，100 条记录
2025-06-05 08:09:34,314 - INFO - 正在批量插入每日数据，批次 54/66，共 100 条记录
2025-06-05 08:09:34,751 - INFO - 批量插入每日数据成功，批次 54，100 条记录
2025-06-05 08:09:37,767 - INFO - 正在批量插入每日数据，批次 55/66，共 100 条记录
2025-06-05 08:09:38,173 - INFO - 批量插入每日数据成功，批次 55，100 条记录
2025-06-05 08:09:41,189 - INFO - 正在批量插入每日数据，批次 56/66，共 100 条记录
2025-06-05 08:09:41,689 - INFO - 批量插入每日数据成功，批次 56，100 条记录
2025-06-05 08:09:44,704 - INFO - 正在批量插入每日数据，批次 57/66，共 100 条记录
2025-06-05 08:09:45,079 - INFO - 批量插入每日数据成功，批次 57，100 条记录
2025-06-05 08:09:48,095 - INFO - 正在批量插入每日数据，批次 58/66，共 100 条记录
2025-06-05 08:09:48,548 - INFO - 批量插入每日数据成功，批次 58，100 条记录
2025-06-05 08:09:51,564 - INFO - 正在批量插入每日数据，批次 59/66，共 100 条记录
2025-06-05 08:09:51,939 - INFO - 批量插入每日数据成功，批次 59，100 条记录
2025-06-05 08:09:54,954 - INFO - 正在批量插入每日数据，批次 60/66，共 100 条记录
2025-06-05 08:09:55,658 - INFO - 批量插入每日数据成功，批次 60，100 条记录
2025-06-05 08:09:58,673 - INFO - 正在批量插入每日数据，批次 61/66，共 100 条记录
2025-06-05 08:09:59,048 - INFO - 批量插入每日数据成功，批次 61，100 条记录
2025-06-05 08:10:02,064 - INFO - 正在批量插入每日数据，批次 62/66，共 100 条记录
2025-06-05 08:10:02,486 - INFO - 批量插入每日数据成功，批次 62，100 条记录
2025-06-05 08:10:05,501 - INFO - 正在批量插入每日数据，批次 63/66，共 100 条记录
2025-06-05 08:10:05,954 - INFO - 批量插入每日数据成功，批次 63，100 条记录
2025-06-05 08:10:08,970 - INFO - 正在批量插入每日数据，批次 64/66，共 100 条记录
2025-06-05 08:10:09,501 - INFO - 批量插入每日数据成功，批次 64，100 条记录
2025-06-05 08:10:12,517 - INFO - 正在批量插入每日数据，批次 65/66，共 100 条记录
2025-06-05 08:10:12,954 - INFO - 批量插入每日数据成功，批次 65，100 条记录
2025-06-05 08:10:15,970 - INFO - 正在批量插入每日数据，批次 66/66，共 42 条记录
2025-06-05 08:10:16,220 - INFO - 批量插入每日数据成功，批次 66，42 条记录
2025-06-05 08:10:19,235 - INFO - 批量插入每日数据完成: 总计 6542 条，成功 6542 条，失败 0 条
2025-06-05 08:10:19,235 - INFO - 批量插入日销售数据完成，共 6542 条记录
2025-06-05 08:10:19,235 - INFO - 日销售数据同步完成！更新: 132 条，插入: 6542 条，错误: 0 条，跳过: 6209 条
2025-06-05 08:10:19,235 - INFO - 正在获取宜搭月销售表单数据...
2025-06-05 08:10:19,235 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-06-01 至 2025-06-30
2025-06-05 08:10:19,235 - INFO - 查询月度分段 1: 2024-06-01 至 2024-08-31
2025-06-05 08:10:19,235 - INFO - 查询日期范围: 2024-06-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-05 08:10:19,235 - INFO - Request Parameters - Page 1:
2025-06-05 08:10:19,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:19,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:20,064 - INFO - API请求耗时: 828ms
2025-06-05 08:10:20,064 - INFO - Response - Page 1
2025-06-05 08:10:20,064 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-05 08:10:20,064 - INFO - 查询完成，共获取到 0 条记录
2025-06-05 08:10:20,064 - WARNING - 月度分段 1 查询返回空数据
2025-06-05 08:10:20,064 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-06-05 08:10:20,064 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-06-05 08:10:20,064 - INFO - Request Parameters - Page 1:
2025-06-05 08:10:20,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:20,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:20,282 - INFO - API请求耗时: 219ms
2025-06-05 08:10:20,282 - INFO - Response - Page 1
2025-06-05 08:10:20,282 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-05 08:10:20,282 - INFO - 查询完成，共获取到 0 条记录
2025-06-05 08:10:20,282 - WARNING - 单月查询返回空数据: 2024-06
2025-06-05 08:10:20,782 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-06-05 08:10:20,782 - INFO - Request Parameters - Page 1:
2025-06-05 08:10:20,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:20,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:21,017 - INFO - API请求耗时: 234ms
2025-06-05 08:10:21,017 - INFO - Response - Page 1
2025-06-05 08:10:21,017 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-05 08:10:21,017 - INFO - 查询完成，共获取到 0 条记录
2025-06-05 08:10:21,017 - WARNING - 单月查询返回空数据: 2024-07
2025-06-05 08:10:21,517 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-05 08:10:21,517 - INFO - Request Parameters - Page 1:
2025-06-05 08:10:21,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:21,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:21,735 - INFO - API请求耗时: 219ms
2025-06-05 08:10:21,735 - INFO - Response - Page 1
2025-06-05 08:10:21,735 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-05 08:10:21,735 - INFO - 查询完成，共获取到 0 条记录
2025-06-05 08:10:21,735 - WARNING - 单月查询返回空数据: 2024-08
2025-06-05 08:10:23,251 - INFO - 查询月度分段 2: 2024-09-01 至 2024-11-30
2025-06-05 08:10:23,251 - INFO - 查询日期范围: 2024-09-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-05 08:10:23,251 - INFO - Request Parameters - Page 1:
2025-06-05 08:10:23,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:23,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:23,470 - INFO - API请求耗时: 219ms
2025-06-05 08:10:23,470 - INFO - Response - Page 1
2025-06-05 08:10:23,470 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-05 08:10:23,470 - INFO - 查询完成，共获取到 0 条记录
2025-06-05 08:10:23,470 - WARNING - 月度分段 2 查询返回空数据
2025-06-05 08:10:23,470 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-06-05 08:10:23,470 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-06-05 08:10:23,470 - INFO - Request Parameters - Page 1:
2025-06-05 08:10:23,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:23,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:23,689 - INFO - API请求耗时: 219ms
2025-06-05 08:10:23,689 - INFO - Response - Page 1
2025-06-05 08:10:23,689 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-05 08:10:23,689 - INFO - 查询完成，共获取到 0 条记录
2025-06-05 08:10:23,689 - WARNING - 单月查询返回空数据: 2024-09
2025-06-05 08:10:24,204 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-06-05 08:10:24,204 - INFO - Request Parameters - Page 1:
2025-06-05 08:10:24,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:24,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:24,423 - INFO - API请求耗时: 219ms
2025-06-05 08:10:24,423 - INFO - Response - Page 1
2025-06-05 08:10:24,423 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-05 08:10:24,423 - INFO - 查询完成，共获取到 0 条记录
2025-06-05 08:10:24,423 - WARNING - 单月查询返回空数据: 2024-10
2025-06-05 08:10:24,923 - INFO - 查询日期范围: 2024-11-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-05 08:10:24,923 - INFO - Request Parameters - Page 1:
2025-06-05 08:10:24,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:24,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:25,157 - INFO - API请求耗时: 234ms
2025-06-05 08:10:25,157 - INFO - Response - Page 1
2025-06-05 08:10:25,157 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-05 08:10:25,157 - INFO - 查询完成，共获取到 0 条记录
2025-06-05 08:10:25,157 - WARNING - 单月查询返回空数据: 2024-11
2025-06-05 08:10:26,673 - INFO - 查询月度分段 3: 2024-12-01 至 2025-02-28
2025-06-05 08:10:26,673 - INFO - 查询日期范围: 2024-12-01 至 2025-02-28，使用分页查询，每页 100 条记录
2025-06-05 08:10:26,673 - INFO - Request Parameters - Page 1:
2025-06-05 08:10:26,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:26,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:27,376 - INFO - API请求耗时: 703ms
2025-06-05 08:10:27,376 - INFO - Response - Page 1
2025-06-05 08:10:27,376 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:10:27,892 - INFO - Request Parameters - Page 2:
2025-06-05 08:10:27,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:27,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:28,532 - INFO - API请求耗时: 641ms
2025-06-05 08:10:28,532 - INFO - Response - Page 2
2025-06-05 08:10:28,532 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:10:29,048 - INFO - Request Parameters - Page 3:
2025-06-05 08:10:29,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:29,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:29,548 - INFO - API请求耗时: 500ms
2025-06-05 08:10:29,548 - INFO - Response - Page 3
2025-06-05 08:10:29,548 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:10:30,064 - INFO - Request Parameters - Page 4:
2025-06-05 08:10:30,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:30,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:30,626 - INFO - API请求耗时: 562ms
2025-06-05 08:10:30,626 - INFO - Response - Page 4
2025-06-05 08:10:30,626 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:10:31,142 - INFO - Request Parameters - Page 5:
2025-06-05 08:10:31,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:31,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:31,610 - INFO - API请求耗时: 469ms
2025-06-05 08:10:31,610 - INFO - Response - Page 5
2025-06-05 08:10:31,626 - INFO - 第 5 页获取到 94 条记录
2025-06-05 08:10:31,626 - INFO - 查询完成，共获取到 494 条记录
2025-06-05 08:10:31,626 - INFO - 月度分段 3 查询成功，获取到 494 条记录
2025-06-05 08:10:32,626 - INFO - 查询月度分段 4: 2025-03-01 至 2025-05-31
2025-06-05 08:10:32,626 - INFO - 查询日期范围: 2025-03-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-05 08:10:32,626 - INFO - Request Parameters - Page 1:
2025-06-05 08:10:32,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:32,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:33,220 - INFO - API请求耗时: 594ms
2025-06-05 08:10:33,220 - INFO - Response - Page 1
2025-06-05 08:10:33,220 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:10:33,735 - INFO - Request Parameters - Page 2:
2025-06-05 08:10:33,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:33,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:34,376 - INFO - API请求耗时: 641ms
2025-06-05 08:10:34,376 - INFO - Response - Page 2
2025-06-05 08:10:34,376 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:10:34,892 - INFO - Request Parameters - Page 3:
2025-06-05 08:10:34,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:34,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:35,470 - INFO - API请求耗时: 578ms
2025-06-05 08:10:35,470 - INFO - Response - Page 3
2025-06-05 08:10:35,470 - INFO - 第 3 页获取到 100 条记录
2025-06-05 08:10:35,970 - INFO - Request Parameters - Page 4:
2025-06-05 08:10:35,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:35,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:36,595 - INFO - API请求耗时: 625ms
2025-06-05 08:10:36,595 - INFO - Response - Page 4
2025-06-05 08:10:36,595 - INFO - 第 4 页获取到 100 条记录
2025-06-05 08:10:37,110 - INFO - Request Parameters - Page 5:
2025-06-05 08:10:37,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:37,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:37,813 - INFO - API请求耗时: 703ms
2025-06-05 08:10:37,813 - INFO - Response - Page 5
2025-06-05 08:10:37,813 - INFO - 第 5 页获取到 100 条记录
2025-06-05 08:10:38,313 - INFO - Request Parameters - Page 6:
2025-06-05 08:10:38,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:38,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:38,876 - INFO - API请求耗时: 562ms
2025-06-05 08:10:38,876 - INFO - Response - Page 6
2025-06-05 08:10:38,876 - INFO - 第 6 页获取到 100 条记录
2025-06-05 08:10:39,392 - INFO - Request Parameters - Page 7:
2025-06-05 08:10:39,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:39,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:39,970 - INFO - API请求耗时: 578ms
2025-06-05 08:10:39,970 - INFO - Response - Page 7
2025-06-05 08:10:39,970 - INFO - 第 7 页获取到 98 条记录
2025-06-05 08:10:39,970 - INFO - 查询完成，共获取到 698 条记录
2025-06-05 08:10:39,970 - INFO - 月度分段 4 查询成功，获取到 698 条记录
2025-06-05 08:10:40,985 - INFO - 查询月度分段 5: 2025-06-01 至 2025-06-30
2025-06-05 08:10:40,985 - INFO - 查询日期范围: 2025-06-01 至 2025-06-30，使用分页查询，每页 100 条记录
2025-06-05 08:10:40,985 - INFO - Request Parameters - Page 1:
2025-06-05 08:10:40,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:40,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:41,626 - INFO - API请求耗时: 641ms
2025-06-05 08:10:41,626 - INFO - Response - Page 1
2025-06-05 08:10:41,626 - INFO - 第 1 页获取到 100 条记录
2025-06-05 08:10:42,142 - INFO - Request Parameters - Page 2:
2025-06-05 08:10:42,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:42,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:42,673 - INFO - API请求耗时: 531ms
2025-06-05 08:10:42,673 - INFO - Response - Page 2
2025-06-05 08:10:42,673 - INFO - 第 2 页获取到 100 条记录
2025-06-05 08:10:43,173 - INFO - Request Parameters - Page 3:
2025-06-05 08:10:43,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-05 08:10:43,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-05 08:10:43,485 - INFO - API请求耗时: 313ms
2025-06-05 08:10:43,485 - INFO - Response - Page 3
2025-06-05 08:10:43,485 - INFO - 第 3 页获取到 10 条记录
2025-06-05 08:10:43,485 - INFO - 查询完成，共获取到 210 条记录
2025-06-05 08:10:43,485 - INFO - 月度分段 5 查询成功，获取到 210 条记录
2025-06-05 08:10:44,501 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1402 条记录，失败 0 次
2025-06-05 08:10:44,501 - INFO - 成功获取宜搭月销售表单数据，共 1402 条记录
2025-06-05 08:10:44,501 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-05 08:10:44,501 - INFO - 正在从SQLite获取月度汇总数据...
2025-06-05 08:10:44,501 - INFO - 成功获取SQLite月度汇总数据，共 1402 条记录
2025-06-05 08:10:44,579 - INFO - 成功创建宜搭月销售数据索引，共 1402 条记录
2025-06-05 08:10:44,579 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:45,095 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1I
2025-06-05 08:10:45,095 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 27389.15, 'new_value': 33849.64}, {'field': 'dailyBillAmount', 'old_value': 27389.15, 'new_value': 33849.64}, {'field': 'amount', 'old_value': 692.4, 'new_value': 1103.2}, {'field': 'count', 'old_value': 7, 'new_value': 13}, {'field': 'onlineAmount', 'old_value': 692.4, 'new_value': 1103.2}, {'field': 'onlineCount', 'old_value': 7, 'new_value': 13}]
2025-06-05 08:10:45,095 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:45,532 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2I
2025-06-05 08:10:45,532 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 67931.37, 'new_value': 80489.39}, {'field': 'dailyBillAmount', 'old_value': 67931.37, 'new_value': 80489.39}, {'field': 'amount', 'old_value': 37809.1, 'new_value': 47126.9}, {'field': 'count', 'old_value': 341, 'new_value': 437}, {'field': 'instoreAmount', 'old_value': 15976.0, 'new_value': 19001.5}, {'field': 'instoreCount', 'old_value': 116, 'new_value': 140}, {'field': 'onlineAmount', 'old_value': 21833.9, 'new_value': 28126.2}, {'field': 'onlineCount', 'old_value': 225, 'new_value': 297}]
2025-06-05 08:10:45,532 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:46,017 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3I
2025-06-05 08:10:46,017 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40124.91, 'new_value': 48640.25}, {'field': 'dailyBillAmount', 'old_value': 40124.91, 'new_value': 48640.25}, {'field': 'amount', 'old_value': 40701.76, 'new_value': 49187.36}, {'field': 'count', 'old_value': 255, 'new_value': 313}, {'field': 'instoreAmount', 'old_value': 37375.83, 'new_value': 45251.73}, {'field': 'instoreCount', 'old_value': 215, 'new_value': 259}, {'field': 'onlineAmount', 'old_value': 3384.91, 'new_value': 3994.61}, {'field': 'onlineCount', 'old_value': 40, 'new_value': 54}]
2025-06-05 08:10:46,017 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:46,501 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4I
2025-06-05 08:10:46,501 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 63298.58, 'new_value': 87655.6}, {'field': 'dailyBillAmount', 'old_value': 63298.58, 'new_value': 87655.6}, {'field': 'amount', 'old_value': 68602.0, 'new_value': 84808.7}, {'field': 'count', 'old_value': 331, 'new_value': 406}, {'field': 'instoreAmount', 'old_value': 68602.1, 'new_value': 84808.8}, {'field': 'instoreCount', 'old_value': 331, 'new_value': 406}]
2025-06-05 08:10:46,501 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:46,938 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5I
2025-06-05 08:10:46,938 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 74897.76000000001, 'new_value': 83520.51000000001}, {'field': 'dailyBillAmount', 'old_value': 74897.76000000001, 'new_value': 83520.51000000001}, {'field': 'amount', 'old_value': 96079.6, 'new_value': 119924.1}, {'field': 'count', 'old_value': 330, 'new_value': 410}, {'field': 'instoreAmount', 'old_value': 95696.0, 'new_value': 118997.0}, {'field': 'instoreCount', 'old_value': 327, 'new_value': 406}, {'field': 'onlineAmount', 'old_value': 383.79999999999995, 'new_value': 927.3}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 4}]
2025-06-05 08:10:46,938 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:47,392 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6I
2025-06-05 08:10:47,392 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4965.1, 'new_value': 7356.5}, {'field': 'dailyBillAmount', 'old_value': 4965.1, 'new_value': 7356.5}, {'field': 'amount', 'old_value': 5400.4, 'new_value': 8873.9}, {'field': 'count', 'old_value': 35, 'new_value': 49}, {'field': 'instoreAmount', 'old_value': 2776.0, 'new_value': 3826.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 6}, {'field': 'onlineAmount', 'old_value': 3012.7, 'new_value': 5436.5}, {'field': 'onlineCount', 'old_value': 32, 'new_value': 43}]
2025-06-05 08:10:47,392 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:47,907 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7I
2025-06-05 08:10:47,907 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9676.0, 'new_value': 15946.1}, {'field': 'amount', 'old_value': 9676.0, 'new_value': 15946.1}, {'field': 'count', 'old_value': 8, 'new_value': 17}, {'field': 'instoreAmount', 'old_value': 9676.0, 'new_value': 15946.1}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 17}]
2025-06-05 08:10:47,907 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:48,329 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8I
2025-06-05 08:10:48,329 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 82276.52, 'new_value': 107380.0}, {'field': 'dailyBillAmount', 'old_value': 82276.52, 'new_value': 107380.0}, {'field': 'amount', 'old_value': 53371.69, 'new_value': 67040.79000000001}, {'field': 'count', 'old_value': 358, 'new_value': 470}, {'field': 'instoreAmount', 'old_value': 49733.99, 'new_value': 61534.19}, {'field': 'instoreCount', 'old_value': 216, 'new_value': 267}, {'field': 'onlineAmount', 'old_value': 4816.200000000001, 'new_value': 7013.1}, {'field': 'onlineCount', 'old_value': 142, 'new_value': 203}]
2025-06-05 08:10:48,329 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:48,829 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9I
2025-06-05 08:10:48,829 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 78762.62, 'new_value': 96823.04}, {'field': 'dailyBillAmount', 'old_value': 78762.62, 'new_value': 96823.04}, {'field': 'amount', 'old_value': 15349.0, 'new_value': 18249.0}, {'field': 'count', 'old_value': 70, 'new_value': 93}, {'field': 'instoreAmount', 'old_value': 15349.0, 'new_value': 18249.0}, {'field': 'instoreCount', 'old_value': 70, 'new_value': 93}]
2025-06-05 08:10:48,829 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:10:49,282 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-06-05 08:10:49,282 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'amount', 'old_value': 118852.23, 'new_value': 118799.86}]
2025-06-05 08:10:49,282 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:10:49,767 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-06-05 08:10:49,767 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 40043.8, 'new_value': 41814.9}, {'field': 'count', 'old_value': 158, 'new_value': 167}, {'field': 'instoreAmount', 'old_value': 40046.8, 'new_value': 41819.4}, {'field': 'instoreCount', 'old_value': 158, 'new_value': 167}]
2025-06-05 08:10:49,767 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:50,235 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBI
2025-06-05 08:10:50,235 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-06, 变更字段: [{'field': 'amount', 'old_value': 8018.0, 'new_value': 11435.0}, {'field': 'count', 'old_value': 13, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 8018.0, 'new_value': 11435.0}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 16}]
2025-06-05 08:10:50,235 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:50,688 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCI
2025-06-05 08:10:50,688 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 163475.92, 'new_value': 191736.47}, {'field': 'dailyBillAmount', 'old_value': 163475.92, 'new_value': 191736.47}, {'field': 'amount', 'old_value': -59077.32000000001, 'new_value': -68902.43000000001}, {'field': 'count', 'old_value': 164, 'new_value': 190}, {'field': 'instoreAmount', 'old_value': 104616.52, 'new_value': 121104.49}, {'field': 'instoreCount', 'old_value': 164, 'new_value': 190}]
2025-06-05 08:10:50,688 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:51,220 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDI
2025-06-05 08:10:51,220 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 44231.0, 'new_value': 50662.0}, {'field': 'amount', 'old_value': 44231.0, 'new_value': 50662.0}, {'field': 'count', 'old_value': 167, 'new_value': 201}, {'field': 'instoreAmount', 'old_value': 44231.0, 'new_value': 50662.0}, {'field': 'instoreCount', 'old_value': 167, 'new_value': 201}]
2025-06-05 08:10:51,220 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:51,642 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEI
2025-06-05 08:10:51,642 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56117.619999999995, 'new_value': 66365.76}, {'field': 'dailyBillAmount', 'old_value': 48687.020000000004, 'new_value': 58479.16}, {'field': 'amount', 'old_value': 56117.619999999995, 'new_value': 66365.76}, {'field': 'count', 'old_value': 181, 'new_value': 219}, {'field': 'instoreAmount', 'old_value': 56117.619999999995, 'new_value': 66365.76}, {'field': 'instoreCount', 'old_value': 181, 'new_value': 219}]
2025-06-05 08:10:51,642 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:52,048 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMG1
2025-06-05 08:10:52,048 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-06, 变更字段: [{'field': 'amount', 'old_value': 684.9, 'new_value': 1711.2}, {'field': 'count', 'old_value': 2, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 744.0, 'new_value': 1770.3}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 7}]
2025-06-05 08:10:52,048 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:52,595 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFI
2025-06-05 08:10:52,595 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14082.79, 'new_value': 15596.6}, {'field': 'dailyBillAmount', 'old_value': 14082.79, 'new_value': 15596.6}, {'field': 'amount', 'old_value': 7719.52, 'new_value': 8578.57}, {'field': 'count', 'old_value': 134, 'new_value': 150}, {'field': 'instoreAmount', 'old_value': 7991.77, 'new_value': 8850.82}, {'field': 'instoreCount', 'old_value': 134, 'new_value': 150}]
2025-06-05 08:10:52,595 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:53,157 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGI
2025-06-05 08:10:53,157 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16561.71, 'new_value': 23894.12}, {'field': 'amount', 'old_value': 16561.35, 'new_value': 23893.76}, {'field': 'count', 'old_value': 538, 'new_value': 785}, {'field': 'instoreAmount', 'old_value': 15108.68, 'new_value': 21743.440000000002}, {'field': 'instoreCount', 'old_value': 497, 'new_value': 725}, {'field': 'onlineAmount', 'old_value': 1453.03, 'new_value': 2150.68}, {'field': 'onlineCount', 'old_value': 41, 'new_value': 60}]
2025-06-05 08:10:53,157 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:53,626 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHI
2025-06-05 08:10:53,626 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21461.69, 'new_value': 28912.83}, {'field': 'dailyBillAmount', 'old_value': 21420.0, 'new_value': 28871.0}, {'field': 'amount', 'old_value': 15454.69, 'new_value': 22905.83}, {'field': 'count', 'old_value': 30, 'new_value': 37}, {'field': 'instoreAmount', 'old_value': 15413.0, 'new_value': 22728.0}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 35}, {'field': 'onlineAmount', 'old_value': 41.69, 'new_value': 177.82999999999998}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 2}]
2025-06-05 08:10:53,626 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:54,063 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMII
2025-06-05 08:10:54,063 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 43536.11, 'new_value': 52130.75}, {'field': 'dailyBillAmount', 'old_value': 43536.11, 'new_value': 52130.75}, {'field': 'amount', 'old_value': 43536.11, 'new_value': 52130.75}, {'field': 'count', 'old_value': 49, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 43536.11, 'new_value': 52130.75}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 61}]
2025-06-05 08:10:54,063 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:54,485 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKI
2025-06-05 08:10:54,501 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12227.099999999999, 'new_value': 14788.8}, {'field': 'dailyBillAmount', 'old_value': 12227.099999999999, 'new_value': 14788.8}, {'field': 'amount', 'old_value': 13676.099999999999, 'new_value': 16237.8}, {'field': 'count', 'old_value': 40, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 13676.099999999999, 'new_value': 16237.8}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 48}]
2025-06-05 08:10:54,501 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:54,970 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLI
2025-06-05 08:10:54,970 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5138.0, 'new_value': 12035.0}, {'field': 'amount', 'old_value': 5138.0, 'new_value': 12035.0}, {'field': 'count', 'old_value': 11, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 5138.0, 'new_value': 12035.0}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 21}]
2025-06-05 08:10:54,970 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:55,376 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMI
2025-06-05 08:10:55,392 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-06, 变更字段: [{'field': 'amount', 'old_value': 47142.0, 'new_value': 53909.0}, {'field': 'count', 'old_value': 224, 'new_value': 266}, {'field': 'instoreAmount', 'old_value': 47142.3, 'new_value': 53909.3}, {'field': 'instoreCount', 'old_value': 224, 'new_value': 266}]
2025-06-05 08:10:55,392 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:55,845 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNI
2025-06-05 08:10:55,845 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18762.42, 'new_value': 26241.34}, {'field': 'dailyBillAmount', 'old_value': 18762.42, 'new_value': 26241.34}, {'field': 'amount', 'old_value': 756.13, 'new_value': 2267.14}, {'field': 'count', 'old_value': 90, 'new_value': 198}, {'field': 'instoreAmount', 'old_value': 1337.62, 'new_value': 2966.53}, {'field': 'instoreCount', 'old_value': 90, 'new_value': 198}]
2025-06-05 08:10:55,845 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:56,407 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPI
2025-06-05 08:10:56,407 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41053.6, 'new_value': 44657.4}, {'field': 'dailyBillAmount', 'old_value': 41053.6, 'new_value': 44657.4}, {'field': 'amount', 'old_value': 41109.6, 'new_value': 44713.4}, {'field': 'count', 'old_value': 100, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 41109.6, 'new_value': 44713.4}, {'field': 'instoreCount', 'old_value': 100, 'new_value': 114}]
2025-06-05 08:10:56,407 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:56,860 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQI
2025-06-05 08:10:56,860 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 33371.47, 'new_value': 40979.04}, {'field': 'dailyBillAmount', 'old_value': 33371.47, 'new_value': 40979.04}, {'field': 'amount', 'old_value': 11907.4, 'new_value': 12746.4}, {'field': 'count', 'old_value': 28, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 11907.4, 'new_value': 12746.4}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 31}]
2025-06-05 08:10:56,860 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:57,329 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRI
2025-06-05 08:10:57,329 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 46698.4, 'new_value': 55820.67}, {'field': 'dailyBillAmount', 'old_value': 46698.4, 'new_value': 55820.67}, {'field': 'amount', 'old_value': 17954.6, 'new_value': 22160.2}, {'field': 'count', 'old_value': 73, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 17954.6, 'new_value': 22160.2}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 88}]
2025-06-05 08:10:57,329 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:57,766 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMH1
2025-06-05 08:10:57,766 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDL7K17VIP7Q2OV4FVC78K001455_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3962.0, 'new_value': 4589.0}, {'field': 'amount', 'old_value': 3962.0, 'new_value': 4589.0}, {'field': 'count', 'old_value': 3, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 3962.0, 'new_value': 4589.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 5}]
2025-06-05 08:10:57,766 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:58,235 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSI
2025-06-05 08:10:58,235 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11075.57, 'new_value': 14333.0}, {'field': 'dailyBillAmount', 'old_value': 11075.57, 'new_value': 14333.0}, {'field': 'amount', 'old_value': 2242.34, 'new_value': 2875.65}, {'field': 'count', 'old_value': 88, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 651.3, 'new_value': 752.5}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 18}, {'field': 'onlineAmount', 'old_value': 1591.73, 'new_value': 2123.84}, {'field': 'onlineCount', 'old_value': 73, 'new_value': 96}]
2025-06-05 08:10:58,235 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:58,673 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTI
2025-06-05 08:10:58,673 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19174.98, 'new_value': 26071.47}, {'field': 'dailyBillAmount', 'old_value': 19174.98, 'new_value': 26071.47}, {'field': 'amount', 'old_value': 4009.6, 'new_value': 4815.44}, {'field': 'count', 'old_value': 88, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 3400.26, 'new_value': 4068.96}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 91}, {'field': 'onlineAmount', 'old_value': 620.41, 'new_value': 757.55}, {'field': 'onlineCount', 'old_value': 15, 'new_value': 19}]
2025-06-05 08:10:58,673 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:59,204 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUI
2025-06-05 08:10:59,204 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6910.6, 'new_value': 7242.6}, {'field': 'dailyBillAmount', 'old_value': 6910.6, 'new_value': 7242.6}, {'field': 'amount', 'old_value': 6503.6, 'new_value': 6835.6}, {'field': 'count', 'old_value': 151, 'new_value': 164}, {'field': 'instoreAmount', 'old_value': 6503.6, 'new_value': 6835.6}, {'field': 'instoreCount', 'old_value': 151, 'new_value': 164}]
2025-06-05 08:10:59,204 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:10:59,688 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVI
2025-06-05 08:10:59,688 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7491.879999999999, 'new_value': 9031.16}, {'field': 'dailyBillAmount', 'old_value': 7491.879999999999, 'new_value': 9031.16}, {'field': 'amount', 'old_value': 3454.94, 'new_value': 4520.84}, {'field': 'count', 'old_value': 201, 'new_value': 256}, {'field': 'instoreAmount', 'old_value': 1140.52, 'new_value': 1816.9199999999998}, {'field': 'instoreCount', 'old_value': 56, 'new_value': 74}, {'field': 'onlineAmount', 'old_value': 2359.0299999999997, 'new_value': 2929.34}, {'field': 'onlineCount', 'old_value': 145, 'new_value': 182}]
2025-06-05 08:10:59,688 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:00,173 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWI
2025-06-05 08:11:00,173 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 39512.48, 'new_value': 51326.44}, {'field': 'dailyBillAmount', 'old_value': 39512.48, 'new_value': 51326.44}, {'field': 'amount', 'old_value': 24166.97, 'new_value': 29130.97}, {'field': 'count', 'old_value': 106, 'new_value': 126}, {'field': 'instoreAmount', 'old_value': 24999.52, 'new_value': 29963.52}, {'field': 'instoreCount', 'old_value': 106, 'new_value': 126}]
2025-06-05 08:11:00,173 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:00,626 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXI
2025-06-05 08:11:00,626 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23303.54, 'new_value': 29719.67}, {'field': 'dailyBillAmount', 'old_value': 23303.54, 'new_value': 29719.67}, {'field': 'amount', 'old_value': 10782.630000000001, 'new_value': 13549.51}, {'field': 'count', 'old_value': 524, 'new_value': 671}, {'field': 'instoreAmount', 'old_value': 11107.17, 'new_value': 13989.35}, {'field': 'instoreCount', 'old_value': 524, 'new_value': 671}]
2025-06-05 08:11:00,641 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:01,032 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYI
2025-06-05 08:11:01,032 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 73925.3, 'new_value': 91916.3}, {'field': 'dailyBillAmount', 'old_value': 73925.3, 'new_value': 91916.3}, {'field': 'amount', 'old_value': 73925.3, 'new_value': 91916.3}, {'field': 'count', 'old_value': 94, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 73925.3, 'new_value': 91916.3}, {'field': 'instoreCount', 'old_value': 94, 'new_value': 114}]
2025-06-05 08:11:01,032 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:01,516 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZI
2025-06-05 08:11:01,516 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 51099.7, 'new_value': 56035.1}, {'field': 'dailyBillAmount', 'old_value': 51099.7, 'new_value': 56035.1}, {'field': 'amount', 'old_value': 37887.9, 'new_value': 40076.5}, {'field': 'count', 'old_value': 91, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 37887.9, 'new_value': 40076.5}, {'field': 'instoreCount', 'old_value': 91, 'new_value': 97}]
2025-06-05 08:11:01,516 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:01,985 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0J
2025-06-05 08:11:01,985 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6046.0, 'new_value': 8327.0}, {'field': 'dailyBillAmount', 'old_value': 6046.0, 'new_value': 8327.0}, {'field': 'amount', 'old_value': 6046.0, 'new_value': 8327.0}, {'field': 'count', 'old_value': 121, 'new_value': 166}, {'field': 'instoreAmount', 'old_value': 6046.0, 'new_value': 8327.0}, {'field': 'instoreCount', 'old_value': 121, 'new_value': 166}]
2025-06-05 08:11:02,001 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:02,423 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1J
2025-06-05 08:11:02,423 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11467.43, 'new_value': 14095.76}, {'field': 'dailyBillAmount', 'old_value': 11467.43, 'new_value': 14095.76}, {'field': 'amount', 'old_value': 11548.37, 'new_value': 14257.94}, {'field': 'count', 'old_value': 603, 'new_value': 766}, {'field': 'instoreAmount', 'old_value': 6205.320000000001, 'new_value': 7557.6900000000005}, {'field': 'instoreCount', 'old_value': 321, 'new_value': 402}, {'field': 'onlineAmount', 'old_value': 5534.15, 'new_value': 6965.83}, {'field': 'onlineCount', 'old_value': 282, 'new_value': 364}]
2025-06-05 08:11:02,423 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:02,891 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2J
2025-06-05 08:11:02,891 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-06, 变更字段: [{'field': 'amount', 'old_value': 7966.959999999999, 'new_value': 8742.46}, {'field': 'count', 'old_value': 297, 'new_value': 336}, {'field': 'onlineAmount', 'old_value': 2427.36, 'new_value': 3202.86}, {'field': 'onlineCount', 'old_value': 114, 'new_value': 153}]
2025-06-05 08:11:02,891 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:03,360 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3J
2025-06-05 08:11:03,360 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-06, 变更字段: [{'field': 'amount', 'old_value': 4163.22, 'new_value': 6860.9}, {'field': 'count', 'old_value': 71, 'new_value': 104}, {'field': 'instoreAmount', 'old_value': 4164.11, 'new_value': 6861.79}, {'field': 'instoreCount', 'old_value': 71, 'new_value': 104}]
2025-06-05 08:11:03,360 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:03,782 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4J
2025-06-05 08:11:03,782 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12806.8, 'new_value': 14866.9}, {'field': 'amount', 'old_value': 12806.0, 'new_value': 14866.1}, {'field': 'count', 'old_value': 269, 'new_value': 322}, {'field': 'instoreAmount', 'old_value': 12857.2, 'new_value': 15103.2}, {'field': 'instoreCount', 'old_value': 269, 'new_value': 322}]
2025-06-05 08:11:03,782 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:04,235 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5J
2025-06-05 08:11:04,235 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 53273.0, 'new_value': 59398.5}, {'field': 'dailyBillAmount', 'old_value': 53273.0, 'new_value': 59398.5}, {'field': 'amount', 'old_value': 21350.0, 'new_value': 24656.5}, {'field': 'count', 'old_value': 61, 'new_value': 72}, {'field': 'instoreAmount', 'old_value': 21350.0, 'new_value': 24656.5}, {'field': 'instoreCount', 'old_value': 61, 'new_value': 72}]
2025-06-05 08:11:04,235 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:04,657 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6J
2025-06-05 08:11:04,657 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12774.5, 'new_value': 13853.04}, {'field': 'dailyBillAmount', 'old_value': 12774.5, 'new_value': 13853.04}, {'field': 'amount', 'old_value': 12322.34, 'new_value': 13400.880000000001}, {'field': 'count', 'old_value': 51, 'new_value': 55}, {'field': 'instoreAmount', 'old_value': 12561.5, 'new_value': 13640.04}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 55}]
2025-06-05 08:11:04,657 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:05,110 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7J
2025-06-05 08:11:05,110 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8998.0, 'new_value': 9387.0}, {'field': 'dailyBillAmount', 'old_value': 8998.0, 'new_value': 9387.0}, {'field': 'amount', 'old_value': 11285.0, 'new_value': 11674.0}, {'field': 'count', 'old_value': 15, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 11285.0, 'new_value': 11674.0}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 16}]
2025-06-05 08:11:05,110 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:05,579 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8J
2025-06-05 08:11:05,579 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14189.900000000001, 'new_value': 15242.900000000001}, {'field': 'dailyBillAmount', 'old_value': 14189.900000000001, 'new_value': 15242.900000000001}, {'field': 'amount', 'old_value': 14189.8, 'new_value': 15242.8}, {'field': 'count', 'old_value': 33, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 15000.5, 'new_value': 16053.5}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 38}]
2025-06-05 08:11:05,579 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:06,063 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9J
2025-06-05 08:11:06,063 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23141.2, 'new_value': 26770.4}, {'field': 'dailyBillAmount', 'old_value': 23141.2, 'new_value': 26770.4}, {'field': 'amount', 'old_value': 13575.55, 'new_value': 15502.22}, {'field': 'count', 'old_value': 313, 'new_value': 372}, {'field': 'instoreAmount', 'old_value': 13035.16, 'new_value': 14638.75}, {'field': 'instoreCount', 'old_value': 280, 'new_value': 327}, {'field': 'onlineAmount', 'old_value': 1360.1599999999999, 'new_value': 1723.29}, {'field': 'onlineCount', 'old_value': 33, 'new_value': 45}]
2025-06-05 08:11:06,063 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:06,641 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAJ
2025-06-05 08:11:06,641 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18027.27, 'new_value': 24283.59}, {'field': 'dailyBillAmount', 'old_value': 17973.620000000003, 'new_value': 24197.22}, {'field': 'amount', 'old_value': 18026.28, 'new_value': 24282.6}, {'field': 'count', 'old_value': 223, 'new_value': 306}, {'field': 'instoreAmount', 'old_value': 17785.62, 'new_value': 23897.62}, {'field': 'instoreCount', 'old_value': 220, 'new_value': 301}, {'field': 'onlineAmount', 'old_value': 241.65, 'new_value': 385.96999999999997}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 5}]
2025-06-05 08:11:06,641 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:07,110 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBJ
2025-06-05 08:11:07,110 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12259.37, 'new_value': 14913.08}, {'field': 'dailyBillAmount', 'old_value': 12259.37, 'new_value': 14913.08}, {'field': 'amount', 'old_value': 12752.27, 'new_value': 15540.44}, {'field': 'count', 'old_value': 70, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 11401.26, 'new_value': 14088.82}, {'field': 'instoreCount', 'old_value': 59, 'new_value': 73}, {'field': 'onlineAmount', 'old_value': 1352.34, 'new_value': 1452.9499999999998}, {'field': 'onlineCount', 'old_value': 11, 'new_value': 12}]
2025-06-05 08:11:07,110 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:07,626 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCJ
2025-06-05 08:11:07,626 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23637.9, 'new_value': 26036.9}, {'field': 'dailyBillAmount', 'old_value': 23637.9, 'new_value': 26036.9}, {'field': 'amount', 'old_value': 28511.9, 'new_value': 31373.9}, {'field': 'count', 'old_value': 107, 'new_value': 119}, {'field': 'instoreAmount', 'old_value': 29323.9, 'new_value': 32185.9}, {'field': 'instoreCount', 'old_value': 107, 'new_value': 119}]
2025-06-05 08:11:07,626 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:08,095 - INFO - 更新表单数据成功: FINST-90D66XA12PZVBSKCD49BM87RNO2X30KE17HBMG6
2025-06-05 08:11:08,095 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-06, 变更字段: [{'field': 'amount', 'old_value': 699.0, 'new_value': 1441.4}, {'field': 'count', 'old_value': 1, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 699.0, 'new_value': 1441.4}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 3}]
2025-06-05 08:11:08,095 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:08,579 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEJ
2025-06-05 08:11:08,579 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6652.86, 'new_value': 10136.08}, {'field': 'amount', 'old_value': 6652.65, 'new_value': 10135.87}, {'field': 'count', 'old_value': 318, 'new_value': 455}, {'field': 'instoreAmount', 'old_value': 7108.4400000000005, 'new_value': 10749.34}, {'field': 'instoreCount', 'old_value': 318, 'new_value': 455}]
2025-06-05 08:11:08,579 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:09,016 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFJ
2025-06-05 08:11:09,016 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-06, 变更字段: [{'field': 'amount', 'old_value': 12489.48, 'new_value': 15856.48}, {'field': 'count', 'old_value': 54, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 12335.7, 'new_value': 15702.7}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 61}]
2025-06-05 08:11:09,016 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:09,501 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGJ
2025-06-05 08:11:09,501 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 48982.520000000004, 'new_value': 59961.19}, {'field': 'dailyBillAmount', 'old_value': 48982.520000000004, 'new_value': 59961.19}, {'field': 'amount', 'old_value': 18746.43, 'new_value': 23264.23}, {'field': 'count', 'old_value': 181, 'new_value': 234}, {'field': 'instoreAmount', 'old_value': 10612.9, 'new_value': 12782.4}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 93}, {'field': 'onlineAmount', 'old_value': 8134.2, 'new_value': 10482.5}, {'field': 'onlineCount', 'old_value': 105, 'new_value': 141}]
2025-06-05 08:11:09,501 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:10,063 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHJ
2025-06-05 08:11:10,063 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30329.379999999997, 'new_value': 35890.479999999996}, {'field': 'dailyBillAmount', 'old_value': 30329.379999999997, 'new_value': 35890.479999999996}, {'field': 'amount', 'old_value': 39772.0, 'new_value': 45913.0}, {'field': 'count', 'old_value': 208, 'new_value': 241}, {'field': 'instoreAmount', 'old_value': 39772.8, 'new_value': 45913.8}, {'field': 'instoreCount', 'old_value': 208, 'new_value': 241}]
2025-06-05 08:11:10,063 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:10,641 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMIJ
2025-06-05 08:11:10,641 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29939.1, 'new_value': 30138.1}, {'field': 'amount', 'old_value': 29939.1, 'new_value': 30138.1}, {'field': 'count', 'old_value': 9, 'new_value': 10}, {'field': 'instoreAmount', 'old_value': 29939.1, 'new_value': 30138.1}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 10}]
2025-06-05 08:11:10,657 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:11,079 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJJ
2025-06-05 08:11:11,079 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19171.67, 'new_value': 26014.5}, {'field': 'dailyBillAmount', 'old_value': 19171.67, 'new_value': 26014.5}, {'field': 'amount', 'old_value': 8765.04, 'new_value': 11803.92}, {'field': 'count', 'old_value': 97, 'new_value': 137}, {'field': 'instoreAmount', 'old_value': 8670.89, 'new_value': 11491.22}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 118}, {'field': 'onlineAmount', 'old_value': 381.4, 'new_value': 651.7}, {'field': 'onlineCount', 'old_value': 10, 'new_value': 19}]
2025-06-05 08:11:11,079 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:11,548 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLJ
2025-06-05 08:11:11,548 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 27426.42, 'new_value': 32969.32}, {'field': 'dailyBillAmount', 'old_value': 27426.42, 'new_value': 32969.32}, {'field': 'amount', 'old_value': 27426.42, 'new_value': 32969.32}, {'field': 'count', 'old_value': 98, 'new_value': 128}, {'field': 'instoreAmount', 'old_value': 27426.42, 'new_value': 32969.32}, {'field': 'instoreCount', 'old_value': 98, 'new_value': 128}]
2025-06-05 08:11:11,548 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:12,079 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMJ
2025-06-05 08:11:12,079 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5014.04, 'new_value': 6048.82}, {'field': 'dailyBillAmount', 'old_value': 5014.04, 'new_value': 6048.82}, {'field': 'amount', 'old_value': 5404.37, 'new_value': 6568.75}, {'field': 'count', 'old_value': 149, 'new_value': 183}, {'field': 'instoreAmount', 'old_value': 5405.34, 'new_value': 6569.72}, {'field': 'instoreCount', 'old_value': 149, 'new_value': 183}]
2025-06-05 08:11:12,079 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:12,548 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNJ
2025-06-05 08:11:12,548 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37578.0, 'new_value': 49527.0}, {'field': 'dailyBillAmount', 'old_value': 12440.0, 'new_value': 23932.0}, {'field': 'amount', 'old_value': 37578.0, 'new_value': 49527.0}, {'field': 'count', 'old_value': 64, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 37578.0, 'new_value': 49527.0}, {'field': 'instoreCount', 'old_value': 64, 'new_value': 85}]
2025-06-05 08:11:12,548 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:12,969 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOJ
2025-06-05 08:11:12,969 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5783.35, 'new_value': 6867.95}, {'field': 'amount', 'old_value': 5782.41, 'new_value': 6867.01}, {'field': 'count', 'old_value': 51, 'new_value': 59}, {'field': 'instoreAmount', 'old_value': 5783.35, 'new_value': 6867.95}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 59}]
2025-06-05 08:11:12,985 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:13,407 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPJ
2025-06-05 08:11:13,407 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 52772.0, 'new_value': 65771.0}, {'field': 'amount', 'old_value': 52772.0, 'new_value': 65771.0}, {'field': 'count', 'old_value': 14, 'new_value': 15}, {'field': 'instoreAmount', 'old_value': 52772.0, 'new_value': 65771.0}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 15}]
2025-06-05 08:11:13,407 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:13,938 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQJ
2025-06-05 08:11:13,938 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78C3CF1DDB8443E8A4A4D425F7CA9756_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 27408.0, 'new_value': 34957.0}, {'field': 'dailyBillAmount', 'old_value': 24096.0, 'new_value': 31645.0}, {'field': 'amount', 'old_value': 27408.0, 'new_value': 34957.0}, {'field': 'count', 'old_value': 134, 'new_value': 168}, {'field': 'instoreAmount', 'old_value': 27408.0, 'new_value': 34957.0}, {'field': 'instoreCount', 'old_value': 134, 'new_value': 168}]
2025-06-05 08:11:13,938 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:14,423 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRJ
2025-06-05 08:11:14,423 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-06, 变更字段: [{'field': 'amount', 'old_value': 8237.7, 'new_value': 9531.1}, {'field': 'count', 'old_value': 72, 'new_value': 87}, {'field': 'instoreAmount', 'old_value': 8238.5, 'new_value': 9531.9}, {'field': 'instoreCount', 'old_value': 72, 'new_value': 87}]
2025-06-05 08:11:14,423 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:14,891 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSJ
2025-06-05 08:11:14,891 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2390.9, 'new_value': 2810.2000000000003}, {'field': 'dailyBillAmount', 'old_value': 2390.9, 'new_value': 2810.2000000000003}, {'field': 'amount', 'old_value': 2390.9, 'new_value': 2810.2000000000003}, {'field': 'count', 'old_value': 4, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 2390.9, 'new_value': 2810.2000000000003}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 5}]
2025-06-05 08:11:14,891 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:15,344 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTJ
2025-06-05 08:11:15,344 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-06, 变更字段: [{'field': 'amount', 'old_value': 45924.0, 'new_value': 57900.0}, {'field': 'count', 'old_value': 141, 'new_value': 182}, {'field': 'instoreAmount', 'old_value': 45924.72, 'new_value': 57900.72}, {'field': 'instoreCount', 'old_value': 141, 'new_value': 182}]
2025-06-05 08:11:15,344 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:15,891 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUJ
2025-06-05 08:11:15,891 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 111916.88, 'new_value': 138841.93}, {'field': 'count', 'old_value': 147, 'new_value': 192}, {'field': 'instoreAmount', 'old_value': 111916.88, 'new_value': 138841.93}, {'field': 'instoreCount', 'old_value': 147, 'new_value': 192}]
2025-06-05 08:11:15,891 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:16,360 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVJ
2025-06-05 08:11:16,360 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12846.099999999999, 'new_value': 15515.599999999999}, {'field': 'dailyBillAmount', 'old_value': 12846.099999999999, 'new_value': 15515.599999999999}, {'field': 'amount', 'old_value': 5838.4, 'new_value': 6959.9}, {'field': 'count', 'old_value': 23, 'new_value': 29}, {'field': 'instoreAmount', 'old_value': 5838.4, 'new_value': 6959.9}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 29}]
2025-06-05 08:11:16,376 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:11:16,751 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-06-05 08:11:16,751 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 335002.2, 'new_value': 335082.1}, {'field': 'amount', 'old_value': 334999.7, 'new_value': 335079.4}, {'field': 'count', 'old_value': 1303, 'new_value': 1304}, {'field': 'instoreAmount', 'old_value': 340969.8, 'new_value': 341049.7}, {'field': 'instoreCount', 'old_value': 1303, 'new_value': 1304}]
2025-06-05 08:11:16,751 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:11:17,188 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-06-05 08:11:17,188 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'amount', 'old_value': 527466.55, 'new_value': 527586.55}, {'field': 'count', 'old_value': 9880, 'new_value': 9882}, {'field': 'instoreAmount', 'old_value': 493325.15, 'new_value': 493444.75}, {'field': 'instoreCount', 'old_value': 9211, 'new_value': 9213}]
2025-06-05 08:11:17,188 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:17,704 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWJ
2025-06-05 08:11:17,704 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37831.9, 'new_value': 41407.51}, {'field': 'amount', 'old_value': 37830.97, 'new_value': 41406.58}, {'field': 'count', 'old_value': 379, 'new_value': 434}, {'field': 'instoreAmount', 'old_value': 29056.01, 'new_value': 30384.47}, {'field': 'instoreCount', 'old_value': 262, 'new_value': 282}, {'field': 'onlineAmount', 'old_value': 9491.09, 'new_value': 11950.02}, {'field': 'onlineCount', 'old_value': 117, 'new_value': 152}]
2025-06-05 08:11:17,704 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:18,188 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXJ
2025-06-05 08:11:18,188 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 44665.78, 'new_value': 55964.36}, {'field': 'dailyBillAmount', 'old_value': 44665.78, 'new_value': 55964.36}, {'field': 'amount', 'old_value': 3792.36, 'new_value': 4591.84}, {'field': 'count', 'old_value': 92, 'new_value': 136}, {'field': 'instoreAmount', 'old_value': 4116.639999999999, 'new_value': 5055.94}, {'field': 'instoreCount', 'old_value': 92, 'new_value': 136}]
2025-06-05 08:11:18,188 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:18,641 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYJ
2025-06-05 08:11:18,641 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 62038.89, 'new_value': 67145.23999999999}, {'field': 'dailyBillAmount', 'old_value': 62038.89, 'new_value': 67145.23999999999}, {'field': 'amount', 'old_value': 25891.23, 'new_value': 29044.84}, {'field': 'count', 'old_value': 540, 'new_value': 605}, {'field': 'instoreAmount', 'old_value': 25425.91, 'new_value': 27346.809999999998}, {'field': 'instoreCount', 'old_value': 520, 'new_value': 564}, {'field': 'onlineAmount', 'old_value': 988.48, 'new_value': 2307.6200000000003}, {'field': 'onlineCount', 'old_value': 20, 'new_value': 41}]
2025-06-05 08:11:18,641 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:19,079 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZJ
2025-06-05 08:11:19,079 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36348.4, 'new_value': 44284.799999999996}, {'field': 'dailyBillAmount', 'old_value': 59018.0, 'new_value': 72194.1}, {'field': 'amount', 'old_value': 36347.3, 'new_value': 44283.7}, {'field': 'count', 'old_value': 138, 'new_value': 162}, {'field': 'instoreAmount', 'old_value': 37470.3, 'new_value': 45406.7}, {'field': 'instoreCount', 'old_value': 138, 'new_value': 162}]
2025-06-05 08:11:19,079 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:19,532 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0K
2025-06-05 08:11:19,532 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 139885.03, 'new_value': 152106.43}, {'field': 'dailyBillAmount', 'old_value': 139885.03, 'new_value': 152106.43}, {'field': 'amount', 'old_value': 115965.63, 'new_value': 119896.16}, {'field': 'count', 'old_value': 1978, 'new_value': 2052}, {'field': 'instoreAmount', 'old_value': 109979.53, 'new_value': 112052.93000000001}, {'field': 'instoreCount', 'old_value': 1859, 'new_value': 1902}, {'field': 'onlineAmount', 'old_value': 6414.8, 'new_value': 8272.06}, {'field': 'onlineCount', 'old_value': 119, 'new_value': 150}]
2025-06-05 08:11:19,532 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:19,938 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1K
2025-06-05 08:11:19,938 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 65348.46000000001, 'new_value': 82238.06}, {'field': 'amount', 'old_value': 65347.600000000006, 'new_value': 82237.2}, {'field': 'count', 'old_value': 1463, 'new_value': 1857}, {'field': 'instoreAmount', 'old_value': 47889.46, 'new_value': 60124.76}, {'field': 'instoreCount', 'old_value': 971, 'new_value': 1242}, {'field': 'onlineAmount', 'old_value': 17459.0, 'new_value': 22113.3}, {'field': 'onlineCount', 'old_value': 492, 'new_value': 615}]
2025-06-05 08:11:19,938 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:20,438 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2K
2025-06-05 08:11:20,438 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-06, 变更字段: [{'field': 'amount', 'old_value': -2122.46, 'new_value': -2050.19}, {'field': 'count', 'old_value': 11, 'new_value': 17}, {'field': 'instoreAmount', 'old_value': 549.0, 'new_value': 771.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 5}, {'field': 'onlineAmount', 'old_value': 157.0, 'new_value': 270.0}, {'field': 'onlineCount', 'old_value': 7, 'new_value': 12}]
2025-06-05 08:11:20,438 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:20,938 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3K
2025-06-05 08:11:20,938 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-06, 变更字段: [{'field': 'amount', 'old_value': 18957.25, 'new_value': 24511.25}, {'field': 'count', 'old_value': 1243, 'new_value': 1578}, {'field': 'instoreAmount', 'old_value': 14225.19, 'new_value': 18502.04}, {'field': 'instoreCount', 'old_value': 839, 'new_value': 1083}, {'field': 'onlineAmount', 'old_value': 5194.45, 'new_value': 6544.48}, {'field': 'onlineCount', 'old_value': 404, 'new_value': 495}]
2025-06-05 08:11:20,938 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:21,376 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4K
2025-06-05 08:11:21,376 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 44675.659999999996, 'new_value': 49785.06}, {'field': 'dailyBillAmount', 'old_value': 44675.659999999996, 'new_value': 49785.06}, {'field': 'amount', 'old_value': 44051.86, 'new_value': 48487.86}, {'field': 'count', 'old_value': 1269, 'new_value': 1423}, {'field': 'instoreAmount', 'old_value': 44160.06, 'new_value': 48634.36}, {'field': 'instoreCount', 'old_value': 1269, 'new_value': 1423}]
2025-06-05 08:11:21,376 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:21,798 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5K
2025-06-05 08:11:21,798 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16654.48, 'new_value': 19639.57}, {'field': 'amount', 'old_value': 16654.440000000002, 'new_value': 19639.530000000002}, {'field': 'count', 'old_value': 927, 'new_value': 1113}, {'field': 'instoreAmount', 'old_value': 9252.84, 'new_value': 10321.93}, {'field': 'instoreCount', 'old_value': 588, 'new_value': 686}, {'field': 'onlineAmount', 'old_value': 7401.64, 'new_value': 9317.64}, {'field': 'onlineCount', 'old_value': 339, 'new_value': 427}]
2025-06-05 08:11:21,798 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:22,251 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6K
2025-06-05 08:11:22,251 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23133.75, 'new_value': 26530.81}, {'field': 'dailyBillAmount', 'old_value': 23133.75, 'new_value': 26530.81}, {'field': 'amount', 'old_value': 4412.42, 'new_value': 5312.64}, {'field': 'count', 'old_value': 134, 'new_value': 173}, {'field': 'instoreAmount', 'old_value': 4439.88, 'new_value': 5412.78}, {'field': 'instoreCount', 'old_value': 134, 'new_value': 173}]
2025-06-05 08:11:22,251 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:22,844 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7K
2025-06-05 08:11:22,844 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-06, 变更字段: [{'field': 'amount', 'old_value': 12690.060000000001, 'new_value': 15935.62}, {'field': 'count', 'old_value': 625, 'new_value': 760}, {'field': 'instoreAmount', 'old_value': 2663.79, 'new_value': 3131.4500000000003}, {'field': 'instoreCount', 'old_value': 176, 'new_value': 210}, {'field': 'onlineAmount', 'old_value': 10374.8, 'new_value': 13156.9}, {'field': 'onlineCount', 'old_value': 449, 'new_value': 550}]
2025-06-05 08:11:22,844 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:23,344 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM8K
2025-06-05 08:11:23,344 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18612.43, 'new_value': 24302.940000000002}, {'field': 'amount', 'old_value': 18611.88, 'new_value': 24302.39}, {'field': 'count', 'old_value': 546, 'new_value': 618}, {'field': 'instoreAmount', 'old_value': 17749.66, 'new_value': 23375.670000000002}, {'field': 'instoreCount', 'old_value': 534, 'new_value': 604}, {'field': 'onlineAmount', 'old_value': 870.07, 'new_value': 934.57}, {'field': 'onlineCount', 'old_value': 12, 'new_value': 14}]
2025-06-05 08:11:23,344 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:23,829 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM9K
2025-06-05 08:11:23,829 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-06, 变更字段: [{'field': 'amount', 'old_value': 20074.03, 'new_value': 25892.45}, {'field': 'count', 'old_value': 1112, 'new_value': 1416}, {'field': 'instoreAmount', 'old_value': 21163.33, 'new_value': 27102.370000000003}, {'field': 'instoreCount', 'old_value': 1099, 'new_value': 1403}]
2025-06-05 08:11:23,829 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:24,282 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMAK
2025-06-05 08:11:24,282 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-06, 变更字段: [{'field': 'amount', 'old_value': 14071.02, 'new_value': 17864.52}, {'field': 'count', 'old_value': 1017, 'new_value': 1265}, {'field': 'instoreAmount', 'old_value': 1018.0, 'new_value': 1072.0}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 76}, {'field': 'onlineAmount', 'old_value': 13731.189999999999, 'new_value': 17556.21}, {'field': 'onlineCount', 'old_value': 948, 'new_value': 1189}]
2025-06-05 08:11:24,282 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:24,798 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMBK
2025-06-05 08:11:24,798 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34043.63, 'new_value': 36613.44}, {'field': 'dailyBillAmount', 'old_value': 34043.63, 'new_value': 36613.44}, {'field': 'amount', 'old_value': 26876.2, 'new_value': 29509.22}, {'field': 'count', 'old_value': 811, 'new_value': 896}, {'field': 'instoreAmount', 'old_value': 15814.73, 'new_value': 17321.55}, {'field': 'instoreCount', 'old_value': 643, 'new_value': 695}, {'field': 'onlineAmount', 'old_value': 12071.4, 'new_value': 13339.4}, {'field': 'onlineCount', 'old_value': 168, 'new_value': 201}]
2025-06-05 08:11:24,798 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:25,219 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMCK
2025-06-05 08:11:25,219 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1578.0, 'new_value': 1746.0}, {'field': 'amount', 'old_value': 1578.0, 'new_value': 1746.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 1578.0, 'new_value': 1746.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-06-05 08:11:25,235 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:25,626 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMDK
2025-06-05 08:11:25,626 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9051.59, 'new_value': 11201.49}, {'field': 'dailyBillAmount', 'old_value': 9051.59, 'new_value': 11201.49}, {'field': 'amount', 'old_value': 10211.29, 'new_value': 12689.23}, {'field': 'count', 'old_value': 344, 'new_value': 445}, {'field': 'instoreAmount', 'old_value': 3960.56, 'new_value': 4620.96}, {'field': 'instoreCount', 'old_value': 150, 'new_value': 174}, {'field': 'onlineAmount', 'old_value': 6355.0, 'new_value': 8183.04}, {'field': 'onlineCount', 'old_value': 194, 'new_value': 271}]
2025-06-05 08:11:25,626 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:26,141 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMEK
2025-06-05 08:11:26,141 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19899.57, 'new_value': 21085.65}, {'field': 'dailyBillAmount', 'old_value': 19899.57, 'new_value': 21085.65}, {'field': 'amount', 'old_value': 20560.52, 'new_value': 21887.13}, {'field': 'count', 'old_value': 647, 'new_value': 711}, {'field': 'instoreAmount', 'old_value': 20582.96, 'new_value': 21867.37}, {'field': 'instoreCount', 'old_value': 646, 'new_value': 709}, {'field': 'onlineAmount', 'old_value': 37.9, 'new_value': 80.1}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 2}]
2025-06-05 08:11:26,141 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:26,548 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMFK
2025-06-05 08:11:26,548 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45597.0, 'new_value': 52215.0}, {'field': 'dailyBillAmount', 'old_value': 45597.0, 'new_value': 52215.0}, {'field': 'amount', 'old_value': 53410.0, 'new_value': 60758.0}, {'field': 'count', 'old_value': 40, 'new_value': 49}, {'field': 'instoreAmount', 'old_value': 55512.0, 'new_value': 63854.0}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 49}]
2025-06-05 08:11:26,548 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:27,063 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMGK
2025-06-05 08:11:27,063 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25609.800000000003, 'new_value': 28122.4}, {'field': 'dailyBillAmount', 'old_value': 25609.800000000003, 'new_value': 28122.4}, {'field': 'amount', 'old_value': 28755.6, 'new_value': 28854.6}, {'field': 'count', 'old_value': 59, 'new_value': 60}, {'field': 'instoreAmount', 'old_value': 29190.01, 'new_value': 29289.01}, {'field': 'instoreCount', 'old_value': 59, 'new_value': 60}]
2025-06-05 08:11:27,063 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:27,501 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMHK
2025-06-05 08:11:27,501 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4678.0, 'new_value': 4837.0}, {'field': 'amount', 'old_value': 4678.0, 'new_value': 4837.0}, {'field': 'count', 'old_value': 15, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 4678.0, 'new_value': 4837.0}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 16}]
2025-06-05 08:11:27,501 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:27,969 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMIK
2025-06-05 08:11:27,969 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7961.0, 'new_value': 10386.0}, {'field': 'dailyBillAmount', 'old_value': 7961.0, 'new_value': 10386.0}, {'field': 'amount', 'old_value': 1834.0, 'new_value': 3486.0}, {'field': 'count', 'old_value': 6, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 1834.0, 'new_value': 3486.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 11}]
2025-06-05 08:11:27,969 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:28,469 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMJK
2025-06-05 08:11:28,469 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8372.0, 'new_value': 9569.0}, {'field': 'dailyBillAmount', 'old_value': 3088.0, 'new_value': 4285.0}, {'field': 'amount', 'old_value': 8372.0, 'new_value': 9569.0}, {'field': 'count', 'old_value': 9, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 8372.0, 'new_value': 9569.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 11}]
2025-06-05 08:11:28,469 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:28,844 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMKK
2025-06-05 08:11:28,844 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11846.5, 'new_value': 13258.5}, {'field': 'amount', 'old_value': 11846.5, 'new_value': 13258.5}, {'field': 'count', 'old_value': 29, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 11846.5, 'new_value': 13258.5}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 32}]
2025-06-05 08:11:28,844 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:29,251 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMMK
2025-06-05 08:11:29,251 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18947.0, 'new_value': 23150.0}, {'field': 'amount', 'old_value': 18947.0, 'new_value': 23150.0}, {'field': 'count', 'old_value': 6, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 18947.0, 'new_value': 23150.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 7}]
2025-06-05 08:11:29,251 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:29,673 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMNK
2025-06-05 08:11:29,673 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4474.0, 'new_value': 5013.0}, {'field': 'amount', 'old_value': 4474.0, 'new_value': 5013.0}, {'field': 'count', 'old_value': 8, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 4474.0, 'new_value': 5013.0}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 9}]
2025-06-05 08:11:29,673 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:30,141 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMPK
2025-06-05 08:11:30,141 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 39135.8, 'new_value': 45162.5}, {'field': 'dailyBillAmount', 'old_value': 39135.8, 'new_value': 45162.5}, {'field': 'amount', 'old_value': 38302.2, 'new_value': 44328.9}, {'field': 'count', 'old_value': 51, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 39030.6, 'new_value': 45057.3}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 61}]
2025-06-05 08:11:30,141 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:30,657 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMQK
2025-06-05 08:11:30,657 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4544.1}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4544.1}, {'field': 'amount', 'old_value': 1727.1999999999998, 'new_value': 3938.7999999999997}, {'field': 'count', 'old_value': 20, 'new_value': 33}, {'field': 'instoreAmount', 'old_value': 968.6, 'new_value': 3138.4}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 21}, {'field': 'onlineAmount', 'old_value': 759.0, 'new_value': 802.3}, {'field': 'onlineCount', 'old_value': 11, 'new_value': 12}]
2025-06-05 08:11:30,657 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:31,204 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMRK
2025-06-05 08:11:31,204 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1120.0, 'new_value': 1208.0}, {'field': 'amount', 'old_value': 1120.0, 'new_value': 1208.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 1120.0, 'new_value': 1208.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-06-05 08:11:31,204 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:31,704 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMSK
2025-06-05 08:11:31,704 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3365.0, 'new_value': 3738.0}, {'field': 'dailyBillAmount', 'old_value': 3365.0, 'new_value': 3738.0}, {'field': 'amount', 'old_value': 3572.0, 'new_value': 3733.0}, {'field': 'count', 'old_value': 14, 'new_value': 17}, {'field': 'instoreAmount', 'old_value': 3763.0, 'new_value': 4263.0}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 17}]
2025-06-05 08:11:31,704 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:32,188 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRR
2025-06-05 08:11:32,188 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6695.700000000001, 'new_value': 7250.700000000001}, {'field': 'amount', 'old_value': 6694.9, 'new_value': 7249.9}, {'field': 'count', 'old_value': 37, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 6803.700000000001, 'new_value': 7358.700000000001}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 42}]
2025-06-05 08:11:32,188 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:32,735 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSR
2025-06-05 08:11:32,735 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1277.0, 'new_value': 1503.0}, {'field': 'dailyBillAmount', 'old_value': 1277.0, 'new_value': 1503.0}, {'field': 'amount', 'old_value': 10195.0, 'new_value': 10872.0}, {'field': 'count', 'old_value': 21, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 10195.0, 'new_value': 10872.0}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 24}]
2025-06-05 08:11:32,751 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:33,188 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTR
2025-06-05 08:11:33,188 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 221790.78, 'new_value': 231348.68999999997}, {'field': 'dailyBillAmount', 'old_value': 221790.78, 'new_value': 231348.68999999997}, {'field': 'amount', 'old_value': 13574.84, 'new_value': 14251.58}, {'field': 'count', 'old_value': 137, 'new_value': 147}, {'field': 'instoreAmount', 'old_value': 10748.859999999999, 'new_value': 11232.67}, {'field': 'instoreCount', 'old_value': 100, 'new_value': 107}, {'field': 'onlineAmount', 'old_value': 2944.2599999999998, 'new_value': 3137.19}, {'field': 'onlineCount', 'old_value': 37, 'new_value': 40}]
2025-06-05 08:11:33,188 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:33,657 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVR
2025-06-05 08:11:33,657 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1046.0, 'new_value': 1435.0}, {'field': 'amount', 'old_value': 1046.0, 'new_value': 1435.0}, {'field': 'count', 'old_value': 3, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 1046.0, 'new_value': 1435.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 4}]
2025-06-05 08:11:33,657 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:34,126 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWR
2025-06-05 08:11:34,126 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5738.799999999999, 'new_value': 7825.0}, {'field': 'amount', 'old_value': 5738.799999999999, 'new_value': 7825.0}, {'field': 'count', 'old_value': 18, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 5738.799999999999, 'new_value': 7825.0}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 21}]
2025-06-05 08:11:34,126 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:34,563 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYR
2025-06-05 08:11:34,563 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 42567.08, 'new_value': 46155.119999999995}, {'field': 'dailyBillAmount', 'old_value': 40767.24, 'new_value': 43639.71}, {'field': 'amount', 'old_value': 40432.03, 'new_value': 44020.07}, {'field': 'count', 'old_value': 239, 'new_value': 255}, {'field': 'instoreAmount', 'old_value': 40432.67, 'new_value': 44020.71}, {'field': 'instoreCount', 'old_value': 239, 'new_value': 255}]
2025-06-05 08:11:34,563 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:35,001 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZR
2025-06-05 08:11:35,001 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29358.0, 'new_value': 29773.0}, {'field': 'amount', 'old_value': 29358.0, 'new_value': 29773.0}, {'field': 'count', 'old_value': 151, 'new_value': 153}, {'field': 'instoreAmount', 'old_value': 30402.0, 'new_value': 30817.0}, {'field': 'instoreCount', 'old_value': 151, 'new_value': 153}]
2025-06-05 08:11:35,001 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:35,485 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0S
2025-06-05 08:11:35,485 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDQR0OPQVI0I86N3H2U1NE001F0K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8780.0, 'new_value': 10280.0}, {'field': 'amount', 'old_value': 8780.0, 'new_value': 10280.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 8780.0, 'new_value': 10280.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-06-05 08:11:35,485 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:35,954 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1S
2025-06-05 08:11:35,954 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 39878.5, 'new_value': 44287.0}, {'field': 'dailyBillAmount', 'old_value': 39878.5, 'new_value': 44287.0}, {'field': 'amount', 'old_value': 39582.5, 'new_value': 43991.0}, {'field': 'count', 'old_value': 215, 'new_value': 246}, {'field': 'instoreAmount', 'old_value': 39582.5, 'new_value': 43991.0}, {'field': 'instoreCount', 'old_value': 215, 'new_value': 246}]
2025-06-05 08:11:35,954 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:36,626 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2S
2025-06-05 08:11:36,626 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12326.470000000001, 'new_value': 13886.86}, {'field': 'dailyBillAmount', 'old_value': 12326.470000000001, 'new_value': 13886.86}, {'field': 'amount', 'old_value': 7710.52, 'new_value': 8421.72}, {'field': 'count', 'old_value': 633, 'new_value': 721}, {'field': 'instoreAmount', 'old_value': 7936.43, 'new_value': 8669.63}, {'field': 'instoreCount', 'old_value': 633, 'new_value': 721}]
2025-06-05 08:11:36,626 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:37,141 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3S
2025-06-05 08:11:37,141 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 91100.37, 'new_value': 104994.06}, {'field': 'dailyBillAmount', 'old_value': 91100.37, 'new_value': 104994.06}, {'field': 'amount', 'old_value': 90162.38, 'new_value': 103680.18}, {'field': 'count', 'old_value': 881, 'new_value': 1078}, {'field': 'instoreAmount', 'old_value': 79198.81, 'new_value': 88315.41}, {'field': 'instoreCount', 'old_value': 386, 'new_value': 441}, {'field': 'onlineAmount', 'old_value': 11378.68, 'new_value': 15783.16}, {'field': 'onlineCount', 'old_value': 495, 'new_value': 637}]
2025-06-05 08:11:37,141 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:37,610 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4S
2025-06-05 08:11:37,610 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35466.5, 'new_value': 39003.3}, {'field': 'amount', 'old_value': 35466.5, 'new_value': 39003.3}, {'field': 'count', 'old_value': 206, 'new_value': 232}, {'field': 'instoreAmount', 'old_value': 35466.5, 'new_value': 39003.3}, {'field': 'instoreCount', 'old_value': 206, 'new_value': 232}]
2025-06-05 08:11:37,626 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:38,047 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5S
2025-06-05 08:11:38,047 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-06, 变更字段: [{'field': 'amount', 'old_value': 16562.84, 'new_value': 21256.11}, {'field': 'count', 'old_value': 622, 'new_value': 845}, {'field': 'instoreAmount', 'old_value': 4481.9, 'new_value': 5936.3}, {'field': 'instoreCount', 'old_value': 162, 'new_value': 232}, {'field': 'onlineAmount', 'old_value': 12429.59, 'new_value': 15793.119999999999}, {'field': 'onlineCount', 'old_value': 460, 'new_value': 613}]
2025-06-05 08:11:38,047 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:38,579 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6S
2025-06-05 08:11:38,579 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11596.130000000001, 'new_value': 13613.29}, {'field': 'dailyBillAmount', 'old_value': 11596.130000000001, 'new_value': 13613.29}, {'field': 'amount', 'old_value': 16316.2, 'new_value': 19668.04}, {'field': 'count', 'old_value': 795, 'new_value': 956}, {'field': 'instoreAmount', 'old_value': 9899.99, 'new_value': 11173.130000000001}, {'field': 'instoreCount', 'old_value': 512, 'new_value': 594}, {'field': 'onlineAmount', 'old_value': 6571.92, 'new_value': 8786.72}, {'field': 'onlineCount', 'old_value': 283, 'new_value': 362}]
2025-06-05 08:11:38,579 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:39,016 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8S
2025-06-05 08:11:39,016 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9479.0, 'new_value': 9619.0}, {'field': 'amount', 'old_value': 9479.0, 'new_value': 9619.0}, {'field': 'count', 'old_value': 3, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 9479.0, 'new_value': 9619.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 5}]
2025-06-05 08:11:39,016 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:39,579 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9S
2025-06-05 08:11:39,579 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17740.92, 'new_value': 20260.32}, {'field': 'dailyBillAmount', 'old_value': 17740.92, 'new_value': 20260.32}, {'field': 'amount', 'old_value': 7507.43, 'new_value': 9166.93}, {'field': 'count', 'old_value': 452, 'new_value': 560}, {'field': 'instoreAmount', 'old_value': 1746.6, 'new_value': 1868.5}, {'field': 'instoreCount', 'old_value': 79, 'new_value': 83}, {'field': 'onlineAmount', 'old_value': 5761.43, 'new_value': 7299.03}, {'field': 'onlineCount', 'old_value': 373, 'new_value': 477}]
2025-06-05 08:11:39,579 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:40,094 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAS
2025-06-05 08:11:40,094 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 55972.07, 'new_value': 64509.8}, {'field': 'dailyBillAmount', 'old_value': 55972.07, 'new_value': 64509.8}, {'field': 'amount', 'old_value': 52797.91, 'new_value': 61055.71}, {'field': 'count', 'old_value': 403, 'new_value': 493}, {'field': 'instoreAmount', 'old_value': 43694.82, 'new_value': 49922.72}, {'field': 'instoreCount', 'old_value': 243, 'new_value': 292}, {'field': 'onlineAmount', 'old_value': 9104.2, 'new_value': 11134.1}, {'field': 'onlineCount', 'old_value': 160, 'new_value': 201}]
2025-06-05 08:11:40,094 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:40,563 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBS
2025-06-05 08:11:40,563 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 62976.82, 'new_value': 73852.42}, {'field': 'dailyBillAmount', 'old_value': 62976.82, 'new_value': 73852.42}, {'field': 'amount', 'old_value': 58832.6, 'new_value': 68926.7}, {'field': 'count', 'old_value': 312, 'new_value': 376}, {'field': 'instoreAmount', 'old_value': 59242.1, 'new_value': 69432.2}, {'field': 'instoreCount', 'old_value': 312, 'new_value': 376}]
2025-06-05 08:11:40,563 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:40,985 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCS
2025-06-05 08:11:40,985 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 146854.93, 'new_value': 167430.84}, {'field': 'dailyBillAmount', 'old_value': 146854.93, 'new_value': 167430.84}, {'field': 'amount', 'old_value': 162201.96, 'new_value': 184129.18}, {'field': 'count', 'old_value': 860, 'new_value': 1041}, {'field': 'instoreAmount', 'old_value': 129685.7, 'new_value': 145106.19999999998}, {'field': 'instoreCount', 'old_value': 503, 'new_value': 578}, {'field': 'onlineAmount', 'old_value': 32832.42, 'new_value': 39339.14}, {'field': 'onlineCount', 'old_value': 357, 'new_value': 463}]
2025-06-05 08:11:40,985 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:41,454 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDS
2025-06-05 08:11:41,454 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 53388.3, 'new_value': 58451.950000000004}, {'field': 'dailyBillAmount', 'old_value': 53388.3, 'new_value': 58451.950000000004}, {'field': 'amount', 'old_value': 71810.6, 'new_value': 79096.1}, {'field': 'count', 'old_value': 319, 'new_value': 372}, {'field': 'instoreAmount', 'old_value': 67784.2, 'new_value': 73957.8}, {'field': 'instoreCount', 'old_value': 262, 'new_value': 294}, {'field': 'onlineAmount', 'old_value': 4232.51, 'new_value': 5383.61}, {'field': 'onlineCount', 'old_value': 57, 'new_value': 78}]
2025-06-05 08:11:41,454 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:41,954 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMES
2025-06-05 08:11:41,954 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 60594.94, 'new_value': 68060.77}, {'field': 'dailyBillAmount', 'old_value': 60594.94, 'new_value': 68060.77}, {'field': 'amount', 'old_value': 57812.5, 'new_value': 64891.7}, {'field': 'count', 'old_value': 237, 'new_value': 264}, {'field': 'instoreAmount', 'old_value': 58829.4, 'new_value': 65908.6}, {'field': 'instoreCount', 'old_value': 237, 'new_value': 264}]
2025-06-05 08:11:41,954 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:42,376 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFS
2025-06-05 08:11:42,376 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 132559.93, 'new_value': 144416.53}, {'field': 'amount', 'old_value': 132559.73, 'new_value': 144416.33000000002}, {'field': 'count', 'old_value': 910, 'new_value': 1034}, {'field': 'instoreAmount', 'old_value': 132559.93, 'new_value': 144416.53}, {'field': 'instoreCount', 'old_value': 910, 'new_value': 1034}]
2025-06-05 08:11:42,376 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:42,907 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGS
2025-06-05 08:11:42,907 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 114711.54999999999, 'new_value': 133658.91999999998}, {'field': 'dailyBillAmount', 'old_value': 114711.54999999999, 'new_value': 133658.91999999998}, {'field': 'amount', 'old_value': 132156.9, 'new_value': 153970.26}, {'field': 'count', 'old_value': 875, 'new_value': 1046}, {'field': 'instoreAmount', 'old_value': 81498.3, 'new_value': 90091.5}, {'field': 'instoreCount', 'old_value': 407, 'new_value': 455}, {'field': 'onlineAmount', 'old_value': 52483.2, 'new_value': 65978.1}, {'field': 'onlineCount', 'old_value': 468, 'new_value': 591}]
2025-06-05 08:11:42,907 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:43,344 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHS
2025-06-05 08:11:43,344 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 80017.26000000001, 'new_value': 88180.28}, {'field': 'dailyBillAmount', 'old_value': 80017.26000000001, 'new_value': 88180.28}, {'field': 'amount', 'old_value': 80107.86, 'new_value': 87818.61}, {'field': 'count', 'old_value': 686, 'new_value': 809}, {'field': 'instoreAmount', 'old_value': 64413.759999999995, 'new_value': 68857.86}, {'field': 'instoreCount', 'old_value': 389, 'new_value': 431}, {'field': 'onlineAmount', 'old_value': 15813.130000000001, 'new_value': 19271.68}, {'field': 'onlineCount', 'old_value': 297, 'new_value': 378}]
2025-06-05 08:11:43,344 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:43,797 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIS
2025-06-05 08:11:43,797 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 80558.9, 'new_value': 95830.12999999999}, {'field': 'dailyBillAmount', 'old_value': 80558.9, 'new_value': 95830.12999999999}, {'field': 'amount', 'old_value': 80713.32, 'new_value': 96246.78}, {'field': 'count', 'old_value': 624, 'new_value': 772}, {'field': 'instoreAmount', 'old_value': 73395.12, 'new_value': 86704.89}, {'field': 'instoreCount', 'old_value': 372, 'new_value': 447}, {'field': 'onlineAmount', 'old_value': 7344.66, 'new_value': 9568.35}, {'field': 'onlineCount', 'old_value': 252, 'new_value': 325}]
2025-06-05 08:11:43,797 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:44,282 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJS
2025-06-05 08:11:44,282 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14812.4, 'new_value': 20196.4}, {'field': 'amount', 'old_value': 14812.4, 'new_value': 20196.4}, {'field': 'count', 'old_value': 88, 'new_value': 103}, {'field': 'instoreAmount', 'old_value': 14812.4, 'new_value': 20196.4}, {'field': 'instoreCount', 'old_value': 88, 'new_value': 103}]
2025-06-05 08:11:44,282 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:44,782 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKS
2025-06-05 08:11:44,782 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 79141.62, 'new_value': 83510.64}, {'field': 'dailyBillAmount', 'old_value': 79141.62, 'new_value': 83510.64}, {'field': 'amount', 'old_value': -52095.9, 'new_value': -55308.08}, {'field': 'count', 'old_value': 122, 'new_value': 151}, {'field': 'instoreAmount', 'old_value': 1304.5, 'new_value': 1592.5}, {'field': 'instoreCount', 'old_value': 56, 'new_value': 63}, {'field': 'onlineAmount', 'old_value': 1754.2199999999998, 'new_value': 2375.54}, {'field': 'onlineCount', 'old_value': 66, 'new_value': 88}]
2025-06-05 08:11:44,782 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:45,235 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLS
2025-06-05 08:11:45,235 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38321.12, 'new_value': 51468.310000000005}, {'field': 'dailyBillAmount', 'old_value': 38321.12, 'new_value': 51468.310000000005}, {'field': 'amount', 'old_value': 66459.22, 'new_value': 73055.92}, {'field': 'count', 'old_value': 287, 'new_value': 322}, {'field': 'instoreAmount', 'old_value': 66459.98, 'new_value': 73056.68}, {'field': 'instoreCount', 'old_value': 287, 'new_value': 322}]
2025-06-05 08:11:45,235 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:45,688 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMS
2025-06-05 08:11:45,688 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 81396.21, 'new_value': 95396.48000000001}, {'field': 'dailyBillAmount', 'old_value': 81396.21, 'new_value': 95396.48000000001}, {'field': 'amount', 'old_value': 26792.5, 'new_value': 29144.1}, {'field': 'count', 'old_value': 118, 'new_value': 126}, {'field': 'instoreAmount', 'old_value': 27598.0, 'new_value': 29919.0}, {'field': 'instoreCount', 'old_value': 116, 'new_value': 123}, {'field': 'onlineAmount', 'old_value': 234.5, 'new_value': 265.1}, {'field': 'onlineCount', 'old_value': 2, 'new_value': 3}]
2025-06-05 08:11:45,688 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:46,172 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNS
2025-06-05 08:11:46,172 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 53954.01, 'new_value': 60421.780000000006}, {'field': 'dailyBillAmount', 'old_value': 53954.01, 'new_value': 60421.780000000006}, {'field': 'amount', 'old_value': 53164.56, 'new_value': 59486.399999999994}, {'field': 'count', 'old_value': 278, 'new_value': 328}, {'field': 'instoreAmount', 'old_value': 51940.899999999994, 'new_value': 58044.27}, {'field': 'instoreCount', 'old_value': 243, 'new_value': 287}, {'field': 'onlineAmount', 'old_value': 1223.94, 'new_value': 1442.4099999999999}, {'field': 'onlineCount', 'old_value': 35, 'new_value': 41}]
2025-06-05 08:11:46,172 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:46,751 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOS
2025-06-05 08:11:46,751 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 54895.07, 'new_value': 63329.159999999996}, {'field': 'dailyBillAmount', 'old_value': 54895.07, 'new_value': 63329.159999999996}, {'field': 'amount', 'old_value': 24949.94, 'new_value': 28673.0}, {'field': 'count', 'old_value': 314, 'new_value': 369}, {'field': 'instoreAmount', 'old_value': 19689.940000000002, 'new_value': 21206.79}, {'field': 'instoreCount', 'old_value': 120, 'new_value': 135}, {'field': 'onlineAmount', 'old_value': 5260.94, 'new_value': 7468.0599999999995}, {'field': 'onlineCount', 'old_value': 194, 'new_value': 234}]
2025-06-05 08:11:46,751 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:47,204 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMK1
2025-06-05 08:11:47,204 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7461.0, 'new_value': 8084.0}, {'field': 'dailyBillAmount', 'old_value': 7461.0, 'new_value': 8084.0}, {'field': 'amount', 'old_value': 8084.0, 'new_value': 10000.0}, {'field': 'count', 'old_value': 5, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 8084.0, 'new_value': 10000.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 7}]
2025-06-05 08:11:47,204 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:47,657 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPS
2025-06-05 08:11:47,657 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18099.45, 'new_value': 22896.84}, {'field': 'amount', 'old_value': 18098.84, 'new_value': 22896.43}, {'field': 'count', 'old_value': 895, 'new_value': 1124}, {'field': 'instoreAmount', 'old_value': 4507.62, 'new_value': 5697.01}, {'field': 'instoreCount', 'old_value': 197, 'new_value': 240}, {'field': 'onlineAmount', 'old_value': 13964.43, 'new_value': 17618.03}, {'field': 'onlineCount', 'old_value': 698, 'new_value': 884}]
2025-06-05 08:11:47,657 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:48,094 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQS
2025-06-05 08:11:48,094 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8426.0, 'new_value': 9110.0}, {'field': 'amount', 'old_value': 8426.0, 'new_value': 9110.0}, {'field': 'count', 'old_value': 37, 'new_value': 41}, {'field': 'instoreAmount', 'old_value': 8426.0, 'new_value': 9110.0}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 41}]
2025-06-05 08:11:48,094 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:48,547 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRS
2025-06-05 08:11:48,547 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 65027.93, 'new_value': 72739.13}, {'field': 'dailyBillAmount', 'old_value': 65027.93, 'new_value': 72739.13}, {'field': 'amount', 'old_value': 28673.9, 'new_value': 31324.9}, {'field': 'count', 'old_value': 486, 'new_value': 554}, {'field': 'instoreAmount', 'old_value': 28774.5, 'new_value': 31465.7}, {'field': 'instoreCount', 'old_value': 486, 'new_value': 554}]
2025-06-05 08:11:48,547 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:49,000 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTS
2025-06-05 08:11:49,000 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5911.68, 'new_value': 7144.85}, {'field': 'amount', 'old_value': 5910.48, 'new_value': 7142.97}, {'field': 'count', 'old_value': 339, 'new_value': 412}, {'field': 'instoreAmount', 'old_value': 3122.6, 'new_value': 3436.2}, {'field': 'instoreCount', 'old_value': 151, 'new_value': 170}, {'field': 'onlineAmount', 'old_value': 2911.32, 'new_value': 3907.41}, {'field': 'onlineCount', 'old_value': 188, 'new_value': 242}]
2025-06-05 08:11:49,000 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:49,469 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUS
2025-06-05 08:11:49,469 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12709.2, 'new_value': 13457.7}, {'field': 'amount', 'old_value': 12709.2, 'new_value': 13457.7}, {'field': 'count', 'old_value': 32, 'new_value': 34}, {'field': 'instoreAmount', 'old_value': 12709.2, 'new_value': 13457.7}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 34}]
2025-06-05 08:11:49,469 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:49,954 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVS
2025-06-05 08:11:49,954 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28478.06, 'new_value': 31900.86}, {'field': 'dailyBillAmount', 'old_value': 24168.1, 'new_value': 26310.4}, {'field': 'amount', 'old_value': 28477.690000000002, 'new_value': 31900.49}, {'field': 'count', 'old_value': 363, 'new_value': 431}, {'field': 'instoreAmount', 'old_value': 27686.6, 'new_value': 30676.1}, {'field': 'instoreCount', 'old_value': 323, 'new_value': 372}, {'field': 'onlineAmount', 'old_value': 820.46, 'new_value': 1253.76}, {'field': 'onlineCount', 'old_value': 40, 'new_value': 59}]
2025-06-05 08:11:49,954 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:50,391 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWS
2025-06-05 08:11:50,391 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4827.0, 'new_value': 5733.7}, {'field': 'amount', 'old_value': 4826.66, 'new_value': 5733.36}, {'field': 'count', 'old_value': 205, 'new_value': 249}, {'field': 'instoreAmount', 'old_value': 4191.7, 'new_value': 4949.5}, {'field': 'instoreCount', 'old_value': 186, 'new_value': 225}, {'field': 'onlineAmount', 'old_value': 669.7, 'new_value': 818.6}, {'field': 'onlineCount', 'old_value': 19, 'new_value': 24}]
2025-06-05 08:11:50,391 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:50,766 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXS
2025-06-05 08:11:50,766 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 70041.52, 'new_value': 78441.62000000001}, {'field': 'dailyBillAmount', 'old_value': 70041.52, 'new_value': 78441.62000000001}, {'field': 'amount', 'old_value': 88481.74, 'new_value': 100396.2}, {'field': 'count', 'old_value': 622, 'new_value': 794}, {'field': 'instoreAmount', 'old_value': 85988.58, 'new_value': 96534.05}, {'field': 'instoreCount', 'old_value': 443, 'new_value': 554}, {'field': 'onlineAmount', 'old_value': 4517.48, 'new_value': 5887.09}, {'field': 'onlineCount', 'old_value': 179, 'new_value': 240}]
2025-06-05 08:11:50,766 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:51,204 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYS
2025-06-05 08:11:51,204 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16265.060000000001, 'new_value': 19475.03}, {'field': 'dailyBillAmount', 'old_value': 16265.060000000001, 'new_value': 19475.03}, {'field': 'amount', 'old_value': 6132.17, 'new_value': 7467.7300000000005}, {'field': 'count', 'old_value': 63, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 4301.6, 'new_value': 5408.94}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 54}, {'field': 'onlineAmount', 'old_value': 1854.49, 'new_value': 2082.88}, {'field': 'onlineCount', 'old_value': 28, 'new_value': 36}]
2025-06-05 08:11:51,204 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:51,907 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZS
2025-06-05 08:11:51,907 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14140.14, 'new_value': 19269.41}, {'field': 'dailyBillAmount', 'old_value': 14170.93, 'new_value': 19354.67}, {'field': 'amount', 'old_value': 14140.14, 'new_value': 19269.41}, {'field': 'count', 'old_value': 754, 'new_value': 1036}, {'field': 'instoreAmount', 'old_value': 6828.849999999999, 'new_value': 9201.25}, {'field': 'instoreCount', 'old_value': 308, 'new_value': 439}, {'field': 'onlineAmount', 'old_value': 7488.040000000001, 'new_value': 10305.710000000001}, {'field': 'onlineCount', 'old_value': 446, 'new_value': 597}]
2025-06-05 08:11:51,907 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:52,422 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0T
2025-06-05 08:11:52,422 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9688.98, 'new_value': 12075.1}, {'field': 'amount', 'old_value': 9688.77, 'new_value': 12074.89}, {'field': 'count', 'old_value': 586, 'new_value': 760}, {'field': 'instoreAmount', 'old_value': 5671.25, 'new_value': 6597.16}, {'field': 'instoreCount', 'old_value': 301, 'new_value': 357}, {'field': 'onlineAmount', 'old_value': 4444.95, 'new_value': 6307.53}, {'field': 'onlineCount', 'old_value': 285, 'new_value': 403}]
2025-06-05 08:11:52,422 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:52,907 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1T
2025-06-05 08:11:52,907 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-06, 变更字段: [{'field': 'amount', 'old_value': 29766.92, 'new_value': 36206.32}, {'field': 'count', 'old_value': 250, 'new_value': 343}, {'field': 'instoreAmount', 'old_value': 29780.399999999998, 'new_value': 36219.799999999996}, {'field': 'instoreCount', 'old_value': 250, 'new_value': 343}]
2025-06-05 08:11:52,907 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:53,407 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2T
2025-06-05 08:11:53,407 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14269.07, 'new_value': 16550.27}, {'field': 'dailyBillAmount', 'old_value': 14686.27, 'new_value': 17223.66}, {'field': 'amount', 'old_value': 14268.52, 'new_value': 16549.72}, {'field': 'count', 'old_value': 376, 'new_value': 450}, {'field': 'instoreAmount', 'old_value': 12983.26, 'new_value': 14887.35}, {'field': 'instoreCount', 'old_value': 267, 'new_value': 307}, {'field': 'onlineAmount', 'old_value': 1285.81, 'new_value': 1674.52}, {'field': 'onlineCount', 'old_value': 109, 'new_value': 143}]
2025-06-05 08:11:53,407 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:11:53,922 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-06-05 08:11:53,922 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'amount', 'old_value': 69976.96, 'new_value': 70181.16}, {'field': 'count', 'old_value': 344, 'new_value': 345}, {'field': 'instoreAmount', 'old_value': 70472.66, 'new_value': 70677.66}, {'field': 'instoreCount', 'old_value': 344, 'new_value': 345}]
2025-06-05 08:11:53,922 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:11:54,407 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-06-05 08:11:54,407 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 62375.36, 'new_value': 139652.16999999998}, {'field': 'dailyBillAmount', 'old_value': 62375.36, 'new_value': 139652.16999999998}]
2025-06-05 08:11:54,407 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:54,907 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3T
2025-06-05 08:11:54,907 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19672.27, 'new_value': 30004.39}, {'field': 'dailyBillAmount', 'old_value': 19672.27, 'new_value': 30004.39}, {'field': 'amount', 'old_value': 2699.08, 'new_value': 3930.05}, {'field': 'count', 'old_value': 98, 'new_value': 139}, {'field': 'instoreAmount', 'old_value': 3027.82, 'new_value': 4451.97}, {'field': 'instoreCount', 'old_value': 98, 'new_value': 139}]
2025-06-05 08:11:54,907 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:55,375 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4T
2025-06-05 08:11:55,375 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 75932.94, 'new_value': 90356.14}, {'field': 'dailyBillAmount', 'old_value': 75932.94, 'new_value': 90356.14}, {'field': 'amount', 'old_value': 6271.0, 'new_value': 8323.0}, {'field': 'count', 'old_value': 34, 'new_value': 45}, {'field': 'instoreAmount', 'old_value': 6271.0, 'new_value': 8323.0}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 45}]
2025-06-05 08:11:55,375 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:55,938 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5T
2025-06-05 08:11:55,938 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 1968.85, 'new_value': 2862.83}, {'field': 'count', 'old_value': 98, 'new_value': 141}, {'field': 'onlineAmount', 'old_value': 1992.53, 'new_value': 2886.51}, {'field': 'onlineCount', 'old_value': 98, 'new_value': 141}]
2025-06-05 08:11:55,938 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:56,391 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6T
2025-06-05 08:11:56,391 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40777.55, 'new_value': 55883.89}, {'field': 'amount', 'old_value': 40776.64, 'new_value': 55882.98}, {'field': 'count', 'old_value': 388, 'new_value': 555}, {'field': 'instoreAmount', 'old_value': 37772.4, 'new_value': 51832.600000000006}, {'field': 'instoreCount', 'old_value': 312, 'new_value': 443}, {'field': 'onlineAmount', 'old_value': 3275.76, 'new_value': 4337.13}, {'field': 'onlineCount', 'old_value': 76, 'new_value': 112}]
2025-06-05 08:11:56,391 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:56,891 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7T
2025-06-05 08:11:56,891 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18169.5, 'new_value': 25957.53}, {'field': 'dailyBillAmount', 'old_value': 14552.300000000001, 'new_value': 22340.33}, {'field': 'amount', 'old_value': 13176.61, 'new_value': 19015.04}, {'field': 'count', 'old_value': 432, 'new_value': 571}, {'field': 'instoreAmount', 'old_value': 2897.58, 'new_value': 3834.88}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 78}, {'field': 'onlineAmount', 'old_value': 10360.21, 'new_value': 15267.32}, {'field': 'onlineCount', 'old_value': 383, 'new_value': 493}]
2025-06-05 08:11:56,891 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:57,344 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8T
2025-06-05 08:11:57,344 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 15921.960000000001}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 15921.960000000001}, {'field': 'amount', 'old_value': 587.24, 'new_value': 1021.23}, {'field': 'count', 'old_value': 22, 'new_value': 40}, {'field': 'instoreAmount', 'old_value': 587.54, 'new_value': 1021.53}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 40}]
2025-06-05 08:11:57,344 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:57,860 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9T
2025-06-05 08:11:57,860 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-06, 变更字段: [{'field': 'amount', 'old_value': 940.23, 'new_value': 1202.2}, {'field': 'count', 'old_value': 46, 'new_value': 59}, {'field': 'onlineAmount', 'old_value': 994.3, 'new_value': 1256.27}, {'field': 'onlineCount', 'old_value': 46, 'new_value': 59}]
2025-06-05 08:11:57,860 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:58,313 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAT
2025-06-05 08:11:58,313 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12371.89, 'new_value': 15654.210000000001}, {'field': 'dailyBillAmount', 'old_value': 7143.320000000001, 'new_value': 8730.720000000001}, {'field': 'amount', 'old_value': 12371.0, 'new_value': 15653.32}, {'field': 'count', 'old_value': 328, 'new_value': 404}, {'field': 'instoreAmount', 'old_value': 7708.49, 'new_value': 9695.39}, {'field': 'instoreCount', 'old_value': 195, 'new_value': 239}, {'field': 'onlineAmount', 'old_value': 4934.639999999999, 'new_value': 6370.26}, {'field': 'onlineCount', 'old_value': 133, 'new_value': 165}]
2025-06-05 08:11:58,313 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:58,766 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBT
2025-06-05 08:11:58,766 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13851.98, 'new_value': 20601.51}, {'field': 'amount', 'old_value': 13851.71, 'new_value': 20601.239999999998}, {'field': 'count', 'old_value': 665, 'new_value': 935}, {'field': 'instoreAmount', 'old_value': 14025.43, 'new_value': 20863.67}, {'field': 'instoreCount', 'old_value': 665, 'new_value': 935}]
2025-06-05 08:11:58,766 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:59,204 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCT
2025-06-05 08:11:59,204 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5902.96, 'new_value': 8340.2}, {'field': 'dailyBillAmount', 'old_value': 5902.96, 'new_value': 8340.2}, {'field': 'amount', 'old_value': 3295.52, 'new_value': 4847.83}, {'field': 'count', 'old_value': 165, 'new_value': 240}, {'field': 'instoreAmount', 'old_value': 1402.6, 'new_value': 1830.45}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 69}, {'field': 'onlineAmount', 'old_value': 1893.8700000000001, 'new_value': 3018.33}, {'field': 'onlineCount', 'old_value': 110, 'new_value': 171}]
2025-06-05 08:11:59,204 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:11:59,766 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDT
2025-06-05 08:11:59,766 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8007.34, 'new_value': 10511.189999999999}, {'field': 'amount', 'old_value': 8007.32, 'new_value': 10511.17}, {'field': 'count', 'old_value': 266, 'new_value': 341}, {'field': 'instoreAmount', 'old_value': 4046.33, 'new_value': 5316.8}, {'field': 'instoreCount', 'old_value': 167, 'new_value': 217}, {'field': 'onlineAmount', 'old_value': 3961.0099999999998, 'new_value': 5194.39}, {'field': 'onlineCount', 'old_value': 99, 'new_value': 124}]
2025-06-05 08:11:59,766 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:00,204 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMET
2025-06-05 08:12:00,204 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4820.45, 'new_value': 6829.49}, {'field': 'amount', 'old_value': 4819.610000000001, 'new_value': 6828.65}, {'field': 'count', 'old_value': 121, 'new_value': 170}, {'field': 'instoreAmount', 'old_value': 4346.9, 'new_value': 5809.0}, {'field': 'instoreCount', 'old_value': 107, 'new_value': 148}, {'field': 'onlineAmount', 'old_value': 571.55, 'new_value': 1216.49}, {'field': 'onlineCount', 'old_value': 14, 'new_value': 22}]
2025-06-05 08:12:00,204 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:00,719 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFT
2025-06-05 08:12:00,719 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24995.26, 'new_value': 35136.08}, {'field': 'dailyBillAmount', 'old_value': 24995.26, 'new_value': 35136.08}, {'field': 'amount', 'old_value': 15279.0, 'new_value': 22091.68}, {'field': 'count', 'old_value': 389, 'new_value': 566}, {'field': 'instoreAmount', 'old_value': 9254.5, 'new_value': 13022.900000000001}, {'field': 'instoreCount', 'old_value': 194, 'new_value': 282}, {'field': 'onlineAmount', 'old_value': 7502.73, 'new_value': 11028.15}, {'field': 'onlineCount', 'old_value': 195, 'new_value': 284}]
2025-06-05 08:12:00,719 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:01,157 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGT
2025-06-05 08:12:01,157 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 114287.20999999999, 'new_value': 132487.25}, {'field': 'dailyBillAmount', 'old_value': 114287.20999999999, 'new_value': 132487.25}, {'field': 'amount', 'old_value': 91469.8, 'new_value': 102692.8}, {'field': 'count', 'old_value': 590, 'new_value': 677}, {'field': 'instoreAmount', 'old_value': 72540.8, 'new_value': 78531.40000000001}, {'field': 'instoreCount', 'old_value': 500, 'new_value': 563}, {'field': 'onlineAmount', 'old_value': 18929.8, 'new_value': 24162.199999999997}, {'field': 'onlineCount', 'old_value': 90, 'new_value': 114}]
2025-06-05 08:12:01,157 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:01,610 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHT
2025-06-05 08:12:01,610 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 133273.76, 'new_value': 163851.56}, {'field': 'amount', 'old_value': 133273.46, 'new_value': 163851.26}, {'field': 'count', 'old_value': 456, 'new_value': 563}, {'field': 'instoreAmount', 'old_value': 133130.76, 'new_value': 163708.56}, {'field': 'instoreCount', 'old_value': 455, 'new_value': 562}]
2025-06-05 08:12:01,610 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:02,016 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIT
2025-06-05 08:12:02,016 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 64456.89, 'new_value': 81135.05}, {'field': 'dailyBillAmount', 'old_value': 58065.33, 'new_value': 71745.48}, {'field': 'amount', 'old_value': 64456.15, 'new_value': 81134.31}, {'field': 'count', 'old_value': 390, 'new_value': 525}, {'field': 'instoreAmount', 'old_value': 58657.95, 'new_value': 73371.85}, {'field': 'instoreCount', 'old_value': 254, 'new_value': 320}, {'field': 'onlineAmount', 'old_value': 5842.5, 'new_value': 7806.76}, {'field': 'onlineCount', 'old_value': 136, 'new_value': 205}]
2025-06-05 08:12:02,016 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:02,485 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJT
2025-06-05 08:12:02,485 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 48182.13, 'new_value': 59663.77}, {'field': 'dailyBillAmount', 'old_value': 34396.04, 'new_value': 45877.68}, {'field': 'amount', 'old_value': 48182.13, 'new_value': 59663.77}, {'field': 'count', 'old_value': 156, 'new_value': 180}, {'field': 'instoreAmount', 'old_value': 43023.0, 'new_value': 54145.4}, {'field': 'instoreCount', 'old_value': 114, 'new_value': 135}, {'field': 'onlineAmount', 'old_value': 5325.96, 'new_value': 5685.2}, {'field': 'onlineCount', 'old_value': 42, 'new_value': 45}]
2025-06-05 08:12:02,500 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:02,938 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKT
2025-06-05 08:12:02,938 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 91941.69, 'new_value': 114956.47}, {'field': 'amount', 'old_value': 91940.73999999999, 'new_value': 114955.51999999999}, {'field': 'count', 'old_value': 500, 'new_value': 702}, {'field': 'instoreAmount', 'old_value': 84875.1, 'new_value': 104558.1}, {'field': 'instoreCount', 'old_value': 303, 'new_value': 378}, {'field': 'onlineAmount', 'old_value': 7066.59, 'new_value': 10398.37}, {'field': 'onlineCount', 'old_value': 197, 'new_value': 324}]
2025-06-05 08:12:02,938 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:03,313 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLT
2025-06-05 08:12:03,313 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 93235.51999999999, 'new_value': 115632.76999999999}, {'field': 'dailyBillAmount', 'old_value': 93235.51999999999, 'new_value': 115632.76999999999}, {'field': 'amount', 'old_value': 76869.98, 'new_value': 99900.58}, {'field': 'count', 'old_value': 406, 'new_value': 531}, {'field': 'instoreAmount', 'old_value': 70196.93, 'new_value': 90599.44}, {'field': 'instoreCount', 'old_value': 337, 'new_value': 431}, {'field': 'onlineAmount', 'old_value': 6931.650000000001, 'new_value': 9560.720000000001}, {'field': 'onlineCount', 'old_value': 69, 'new_value': 100}]
2025-06-05 08:12:03,313 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:03,750 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMT
2025-06-05 08:12:03,750 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19979.0, 'new_value': 30110.8}, {'field': 'dailyBillAmount', 'old_value': 19979.0, 'new_value': 30110.8}, {'field': 'amount', 'old_value': 19428.0, 'new_value': 29388.0}, {'field': 'count', 'old_value': 33, 'new_value': 49}, {'field': 'instoreAmount', 'old_value': 19428.0, 'new_value': 29388.0}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 49}]
2025-06-05 08:12:03,750 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:04,188 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNT
2025-06-05 08:12:04,188 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15724.119999999999, 'new_value': 21641.78}, {'field': 'dailyBillAmount', 'old_value': 15724.119999999999, 'new_value': 21641.78}, {'field': 'amount', 'old_value': 16349.8, 'new_value': 19701.66}, {'field': 'count', 'old_value': 21, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 17225.89, 'new_value': 22764.55}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 23}]
2025-06-05 08:12:04,188 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:04,657 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOT
2025-06-05 08:12:04,657 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2512.15, 'new_value': 3083.39}, {'field': 'amount', 'old_value': 2511.91, 'new_value': 3083.15}, {'field': 'count', 'old_value': 48, 'new_value': 60}, {'field': 'instoreAmount', 'old_value': 2512.15, 'new_value': 3083.39}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 60}]
2025-06-05 08:12:04,657 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:05,063 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPT
2025-06-05 08:12:05,063 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7305.3099999999995, 'new_value': 9421.539999999999}, {'field': 'amount', 'old_value': 7304.32, 'new_value': 9420.55}, {'field': 'count', 'old_value': 73, 'new_value': 94}, {'field': 'instoreAmount', 'old_value': 7305.3099999999995, 'new_value': 9421.539999999999}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 94}]
2025-06-05 08:12:05,063 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:05,688 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQT
2025-06-05 08:12:05,688 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 43040.99, 'new_value': 55494.28}, {'field': 'dailyBillAmount', 'old_value': 43040.99, 'new_value': 55494.28}, {'field': 'amount', 'old_value': 46738.86, 'new_value': 58724.7}, {'field': 'count', 'old_value': 1240, 'new_value': 1585}, {'field': 'instoreAmount', 'old_value': 43961.65, 'new_value': 55128.85}, {'field': 'instoreCount', 'old_value': 1092, 'new_value': 1381}, {'field': 'onlineAmount', 'old_value': 3235.73, 'new_value': 4404.77}, {'field': 'onlineCount', 'old_value': 148, 'new_value': 204}]
2025-06-05 08:12:05,688 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:06,094 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRT
2025-06-05 08:12:06,094 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 67227.68, 'new_value': 90969.4}, {'field': 'dailyBillAmount', 'old_value': 67227.68, 'new_value': 90969.4}, {'field': 'amount', 'old_value': 67705.36, 'new_value': 89344.15}, {'field': 'count', 'old_value': 204, 'new_value': 277}, {'field': 'instoreAmount', 'old_value': 66875.14, 'new_value': 89321.18000000001}, {'field': 'instoreCount', 'old_value': 170, 'new_value': 228}, {'field': 'onlineAmount', 'old_value': 1183.65, 'new_value': 1494.68}, {'field': 'onlineCount', 'old_value': 34, 'new_value': 49}]
2025-06-05 08:12:06,094 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:06,610 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMST
2025-06-05 08:12:06,610 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 126650.76000000001, 'new_value': 168159.97}, {'field': 'amount', 'old_value': 126650.70999999999, 'new_value': 168159.91999999998}, {'field': 'count', 'old_value': 447, 'new_value': 572}, {'field': 'instoreAmount', 'old_value': 128595.76000000001, 'new_value': 170104.97}, {'field': 'instoreCount', 'old_value': 447, 'new_value': 572}]
2025-06-05 08:12:06,610 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:07,032 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTT
2025-06-05 08:12:07,032 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 58481.149999999994, 'new_value': 101784.06}, {'field': 'dailyBillAmount', 'old_value': 58481.149999999994, 'new_value': 101784.06}, {'field': 'amount', 'old_value': 66843.39, 'new_value': 108890.39}, {'field': 'count', 'old_value': 269, 'new_value': 410}, {'field': 'instoreAmount', 'old_value': 67164.35, 'new_value': 107885.25}, {'field': 'instoreCount', 'old_value': 156, 'new_value': 245}, {'field': 'onlineAmount', 'old_value': 3693.5, 'new_value': 5274.2}, {'field': 'onlineCount', 'old_value': 113, 'new_value': 165}]
2025-06-05 08:12:07,032 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:07,469 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMUT
2025-06-05 08:12:07,469 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 210108.96, 'new_value': 266023.2}, {'field': 'dailyBillAmount', 'old_value': 210108.96, 'new_value': 266023.2}, {'field': 'amount', 'old_value': 174273.0, 'new_value': 208137.0}, {'field': 'count', 'old_value': 407, 'new_value': 489}, {'field': 'instoreAmount', 'old_value': 185555.0, 'new_value': 221602.0}, {'field': 'instoreCount', 'old_value': 407, 'new_value': 489}]
2025-06-05 08:12:07,469 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:07,891 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMVT
2025-06-05 08:12:07,891 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36526.32, 'new_value': 45188.37}, {'field': 'dailyBillAmount', 'old_value': 36526.32, 'new_value': 45188.37}, {'field': 'amount', 'old_value': 36276.53, 'new_value': 44938.58}, {'field': 'count', 'old_value': 183, 'new_value': 230}, {'field': 'instoreAmount', 'old_value': 34366.7, 'new_value': 42502.3}, {'field': 'instoreCount', 'old_value': 155, 'new_value': 192}, {'field': 'onlineAmount', 'old_value': 2283.24, 'new_value': 2809.69}, {'field': 'onlineCount', 'old_value': 28, 'new_value': 38}]
2025-06-05 08:12:07,891 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:08,313 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMWT
2025-06-05 08:12:08,313 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 86414.9, 'new_value': 118707.81}, {'field': 'dailyBillAmount', 'old_value': 86414.9, 'new_value': 118707.81}, {'field': 'amount', 'old_value': 122676.76999999999, 'new_value': 154969.68}, {'field': 'count', 'old_value': 526, 'new_value': 683}, {'field': 'instoreAmount', 'old_value': 122676.82, 'new_value': 154969.73}, {'field': 'instoreCount', 'old_value': 526, 'new_value': 683}]
2025-06-05 08:12:08,313 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:08,672 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMXT
2025-06-05 08:12:08,672 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37042.63, 'new_value': 50323.27}, {'field': 'dailyBillAmount', 'old_value': 37042.63, 'new_value': 50323.27}, {'field': 'amount', 'old_value': 65410.3, 'new_value': 89436.1}, {'field': 'count', 'old_value': 104, 'new_value': 143}, {'field': 'instoreAmount', 'old_value': 64455.0, 'new_value': 88277.8}, {'field': 'instoreCount', 'old_value': 99, 'new_value': 136}, {'field': 'onlineAmount', 'old_value': 956.3, 'new_value': 1159.6}, {'field': 'onlineCount', 'old_value': 5, 'new_value': 7}]
2025-06-05 08:12:08,672 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:09,141 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMYT
2025-06-05 08:12:09,141 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45399.49, 'new_value': 47769.520000000004}, {'field': 'dailyBillAmount', 'old_value': 45399.49, 'new_value': 47769.520000000004}, {'field': 'amount', 'old_value': 49868.3, 'new_value': 51871.3}, {'field': 'count', 'old_value': 323, 'new_value': 339}, {'field': 'instoreAmount', 'old_value': 49868.3, 'new_value': 51871.3}, {'field': 'instoreCount', 'old_value': 323, 'new_value': 339}]
2025-06-05 08:12:09,141 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:12:09,688 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-06-05 08:12:09,688 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45514.01, 'new_value': 208112.69}, {'field': 'dailyBillAmount', 'old_value': 45514.01, 'new_value': 208112.69}]
2025-06-05 08:12:09,688 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:12:10,204 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-06-05 08:12:10,204 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 282298.1, 'new_value': 249163.62}, {'field': 'amount', 'old_value': 282285.48, 'new_value': 249163.0}, {'field': 'count', 'old_value': 5197, 'new_value': 5202}, {'field': 'instoreAmount', 'old_value': 252494.93, 'new_value': 254628.11}, {'field': 'instoreCount', 'old_value': 4608, 'new_value': 4613}]
2025-06-05 08:12:10,204 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:12:10,688 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-06-05 08:12:10,688 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 396670.9, 'new_value': 449864.0}, {'field': 'dailyBillAmount', 'old_value': 396670.9, 'new_value': 449864.0}]
2025-06-05 08:12:10,688 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:12:11,125 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-06-05 08:12:11,125 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 88055.72, 'new_value': 100341.44}, {'field': 'dailyBillAmount', 'old_value': 88055.72, 'new_value': 100341.44}]
2025-06-05 08:12:11,125 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:12:11,532 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-06-05 08:12:11,532 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 86251.69, 'new_value': 100046.8}, {'field': 'amount', 'old_value': 86241.14, 'new_value': 100045.0}, {'field': 'count', 'old_value': 5060, 'new_value': 5088}, {'field': 'instoreAmount', 'old_value': 21661.49, 'new_value': 35738.32}, {'field': 'instoreCount', 'old_value': 1212, 'new_value': 1240}]
2025-06-05 08:12:11,532 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:12:11,985 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-06-05 08:12:11,985 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 162277.0, 'new_value': 74740.7}, {'field': 'amount', 'old_value': 162277.0, 'new_value': 74740.0}]
2025-06-05 08:12:11,985 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:12:12,422 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-06-05 08:12:12,422 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40946.24, 'new_value': 51593.22}, {'field': 'dailyBillAmount', 'old_value': 40946.24, 'new_value': 51593.22}]
2025-06-05 08:12:12,422 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-05 08:12:12,969 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-06-05 08:12:12,969 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54913.0, 'new_value': 61187.0}, {'field': 'dailyBillAmount', 'old_value': 54913.0, 'new_value': 61187.0}]
2025-06-05 08:12:12,969 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:13,469 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMZT
2025-06-05 08:12:13,469 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35254.51, 'new_value': 43502.73}, {'field': 'dailyBillAmount', 'old_value': 35254.51, 'new_value': 43502.73}, {'field': 'amount', 'old_value': 26078.2, 'new_value': 33763.2}, {'field': 'count', 'old_value': 158, 'new_value': 198}, {'field': 'instoreAmount', 'old_value': 26827.0, 'new_value': 34762.0}, {'field': 'instoreCount', 'old_value': 154, 'new_value': 192}, {'field': 'onlineAmount', 'old_value': 278.2, 'new_value': 362.2}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 6}]
2025-06-05 08:12:13,469 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:13,953 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM0U
2025-06-05 08:12:13,953 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1864.9, 'new_value': 3284.4}, {'field': 'dailyBillAmount', 'old_value': 1864.9, 'new_value': 3284.4}]
2025-06-05 08:12:13,953 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:14,391 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM1U
2025-06-05 08:12:14,391 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-06, 变更字段: [{'field': 'amount', 'old_value': 11024.95, 'new_value': 13485.24}, {'field': 'count', 'old_value': 623, 'new_value': 788}, {'field': 'instoreAmount', 'old_value': 1502.71, 'new_value': 1945.99}, {'field': 'instoreCount', 'old_value': 131, 'new_value': 178}, {'field': 'onlineAmount', 'old_value': 10177.8, 'new_value': 12400.5}, {'field': 'onlineCount', 'old_value': 492, 'new_value': 610}]
2025-06-05 08:12:14,391 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:14,891 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM2U
2025-06-05 08:12:14,891 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21637.29, 'new_value': 28657.15}, {'field': 'amount', 'old_value': 21635.84, 'new_value': 28655.760000000002}, {'field': 'count', 'old_value': 405, 'new_value': 545}, {'field': 'instoreAmount', 'old_value': 18622.45, 'new_value': 24471.989999999998}, {'field': 'instoreCount', 'old_value': 333, 'new_value': 446}, {'field': 'onlineAmount', 'old_value': 3014.84, 'new_value': 4185.16}, {'field': 'onlineCount', 'old_value': 72, 'new_value': 99}]
2025-06-05 08:12:14,891 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:15,375 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM3U
2025-06-05 08:12:15,375 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5049.8, 'new_value': 6139.2}, {'field': 'amount', 'old_value': 5049.4, 'new_value': 6138.8}, {'field': 'count', 'old_value': 27, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 5049.8, 'new_value': 6139.2}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 36}]
2025-06-05 08:12:15,375 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:15,844 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM4U
2025-06-05 08:12:15,844 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-06, 变更字段: [{'field': 'amount', 'old_value': 4005.0, 'new_value': 4714.3}, {'field': 'count', 'old_value': 40, 'new_value': 46}, {'field': 'instoreAmount', 'old_value': 4173.3, 'new_value': 4882.6}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 46}]
2025-06-05 08:12:15,844 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:16,313 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM5U
2025-06-05 08:12:16,313 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8067.0, 'new_value': 8346.0}, {'field': 'dailyBillAmount', 'old_value': 8067.0, 'new_value': 8346.0}]
2025-06-05 08:12:16,313 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:16,766 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM6U
2025-06-05 08:12:16,766 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31629.399999999998, 'new_value': 38446.0}, {'field': 'dailyBillAmount', 'old_value': 31629.399999999998, 'new_value': 38446.0}, {'field': 'amount', 'old_value': 23217.800000000003, 'new_value': 27913.16}, {'field': 'count', 'old_value': 666, 'new_value': 825}, {'field': 'instoreAmount', 'old_value': 21682.01, 'new_value': 26134.97}, {'field': 'instoreCount', 'old_value': 625, 'new_value': 778}, {'field': 'onlineAmount', 'old_value': 1721.78, 'new_value': 1964.1799999999998}, {'field': 'onlineCount', 'old_value': 41, 'new_value': 47}]
2025-06-05 08:12:16,766 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:17,219 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM7U
2025-06-05 08:12:17,219 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6763.099999999999, 'new_value': 7940.299999999999}, {'field': 'dailyBillAmount', 'old_value': 6763.099999999999, 'new_value': 7940.299999999999}, {'field': 'amount', 'old_value': 6762.2, 'new_value': 7889.4}, {'field': 'count', 'old_value': 41, 'new_value': 53}, {'field': 'instoreAmount', 'old_value': 7000.9, 'new_value': 8417.8}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 53}]
2025-06-05 08:12:17,219 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:17,625 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM8U
2025-06-05 08:12:17,625 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6356.48, 'new_value': 9487.49}, {'field': 'dailyBillAmount', 'old_value': 6356.48, 'new_value': 9487.49}]
2025-06-05 08:12:17,641 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:18,110 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM9U
2025-06-05 08:12:18,110 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6082.83, 'new_value': 8376.14}, {'field': 'amount', 'old_value': 6082.2300000000005, 'new_value': 8375.54}, {'field': 'count', 'old_value': 377, 'new_value': 499}, {'field': 'instoreAmount', 'old_value': 6135.64, 'new_value': 8436.11}, {'field': 'instoreCount', 'old_value': 377, 'new_value': 499}]
2025-06-05 08:12:18,110 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:18,563 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMAU
2025-06-05 08:12:18,563 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8472.95, 'new_value': 11521.58}, {'field': 'dailyBillAmount', 'old_value': 8472.95, 'new_value': 11521.58}, {'field': 'amount', 'old_value': 8373.7, 'new_value': 11517.89}, {'field': 'count', 'old_value': 405, 'new_value': 572}, {'field': 'instoreAmount', 'old_value': 7655.1, 'new_value': 10641.4}, {'field': 'instoreCount', 'old_value': 366, 'new_value': 522}, {'field': 'onlineAmount', 'old_value': 739.17, 'new_value': 919.26}, {'field': 'onlineCount', 'old_value': 39, 'new_value': 50}]
2025-06-05 08:12:18,563 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:19,000 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMBU
2025-06-05 08:12:19,000 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7457.7300000000005, 'new_value': 9218.89}, {'field': 'amount', 'old_value': 7457.58, 'new_value': 9218.74}, {'field': 'count', 'old_value': 318, 'new_value': 418}, {'field': 'instoreAmount', 'old_value': 4128.280000000001, 'new_value': 5243.64}, {'field': 'instoreCount', 'old_value': 181, 'new_value': 249}, {'field': 'onlineAmount', 'old_value': 3367.27, 'new_value': 4013.07}, {'field': 'onlineCount', 'old_value': 137, 'new_value': 169}]
2025-06-05 08:12:19,000 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:19,469 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMCU
2025-06-05 08:12:19,469 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5060.59, 'new_value': 6696.18}, {'field': 'dailyBillAmount', 'old_value': 5060.59, 'new_value': 6696.18}, {'field': 'amount', 'old_value': 3347.3100000000004, 'new_value': 4500.01}, {'field': 'count', 'old_value': 125, 'new_value': 170}, {'field': 'instoreAmount', 'old_value': 3447.85, 'new_value': 4614.15}, {'field': 'instoreCount', 'old_value': 125, 'new_value': 170}]
2025-06-05 08:12:19,469 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:19,875 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMDU
2025-06-05 08:12:19,875 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7586.99, 'new_value': 9564.52}, {'field': 'amount', 'old_value': 7586.96, 'new_value': 9564.43}, {'field': 'count', 'old_value': 478, 'new_value': 597}, {'field': 'instoreAmount', 'old_value': 1671.74, 'new_value': 2277.15}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 117}, {'field': 'onlineAmount', 'old_value': 6001.0199999999995, 'new_value': 7440.139999999999}, {'field': 'onlineCount', 'old_value': 393, 'new_value': 480}]
2025-06-05 08:12:19,875 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:20,313 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMEU
2025-06-05 08:12:20,313 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13227.82, 'new_value': 18939.54}, {'field': 'dailyBillAmount', 'old_value': 13227.82, 'new_value': 18939.54}, {'field': 'amount', 'old_value': 9736.9, 'new_value': 14357.8}, {'field': 'count', 'old_value': 87, 'new_value': 125}, {'field': 'instoreAmount', 'old_value': 9737.4, 'new_value': 14358.3}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 125}]
2025-06-05 08:12:20,313 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:20,766 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMFU
2025-06-05 08:12:20,766 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19706.8, 'new_value': 22222.8}, {'field': 'dailyBillAmount', 'old_value': 19706.8, 'new_value': 22222.8}, {'field': 'amount', 'old_value': 23356.0, 'new_value': 26353.0}, {'field': 'count', 'old_value': 90, 'new_value': 105}, {'field': 'instoreAmount', 'old_value': 23356.0, 'new_value': 26353.0}, {'field': 'instoreCount', 'old_value': 90, 'new_value': 105}]
2025-06-05 08:12:20,766 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:21,250 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMGU
2025-06-05 08:12:21,250 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-06, 变更字段: [{'field': 'amount', 'old_value': 8303.0, 'new_value': 9612.0}, {'field': 'count', 'old_value': 43, 'new_value': 50}, {'field': 'instoreAmount', 'old_value': 8303.0, 'new_value': 9612.0}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 50}]
2025-06-05 08:12:21,250 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:21,688 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMHU
2025-06-05 08:12:21,688 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11710.0, 'new_value': 15895.0}, {'field': 'amount', 'old_value': 11710.0, 'new_value': 15895.0}, {'field': 'count', 'old_value': 143, 'new_value': 191}, {'field': 'instoreAmount', 'old_value': 11710.0, 'new_value': 15895.0}, {'field': 'instoreCount', 'old_value': 143, 'new_value': 191}]
2025-06-05 08:12:21,688 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:22,172 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMIU
2025-06-05 08:12:22,172 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1631.04, 'new_value': 3275.05}, {'field': 'dailyBillAmount', 'old_value': 1631.04, 'new_value': 3275.05}, {'field': 'amount', 'old_value': 319.79999999999995, 'new_value': 527.3}, {'field': 'count', 'old_value': 12, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 450.59999999999997, 'new_value': 677.4}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 16}]
2025-06-05 08:12:22,172 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:22,735 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMMW
2025-06-05 08:12:22,735 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-06, 变更字段: [{'field': 'amount', 'old_value': 5765.0, 'new_value': 7247.0}, {'field': 'count', 'old_value': 32, 'new_value': 37}, {'field': 'instoreAmount', 'old_value': 5765.0, 'new_value': 7247.0}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 37}]
2025-06-05 08:12:22,735 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:23,157 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMNW
2025-06-05 08:12:23,157 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8415.52, 'new_value': 10845.53}, {'field': 'dailyBillAmount', 'old_value': 8415.52, 'new_value': 10845.53}, {'field': 'amount', 'old_value': 6218.84, 'new_value': 8169.54}, {'field': 'count', 'old_value': 198, 'new_value': 263}, {'field': 'instoreAmount', 'old_value': 6028.17, 'new_value': 7960.97}, {'field': 'instoreCount', 'old_value': 189, 'new_value': 253}, {'field': 'onlineAmount', 'old_value': 191.1, 'new_value': 209.0}, {'field': 'onlineCount', 'old_value': 9, 'new_value': 10}]
2025-06-05 08:12:23,157 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:23,594 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMOW
2025-06-05 08:12:23,594 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9795.84, 'new_value': 12526.41}, {'field': 'dailyBillAmount', 'old_value': 9795.84, 'new_value': 12526.41}, {'field': 'amount', 'old_value': 9440.400000000001, 'new_value': 12192.2}, {'field': 'count', 'old_value': 52, 'new_value': 68}, {'field': 'instoreAmount', 'old_value': 9410.4, 'new_value': 12132.4}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 64}, {'field': 'onlineAmount', 'old_value': 110.80000000000001, 'new_value': 140.6}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 4}]
2025-06-05 08:12:23,594 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-05 08:12:24,078 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMPW
2025-06-05 08:12:24,078 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23705.97, 'new_value': 31854.2}, {'field': 'dailyBillAmount', 'old_value': 23705.97, 'new_value': 31854.2}, {'field': 'amount', 'old_value': 25286.0, 'new_value': 33790.3}, {'field': 'count', 'old_value': 167, 'new_value': 210}, {'field': 'instoreAmount', 'old_value': 23936.0, 'new_value': 32268.0}, {'field': 'instoreCount', 'old_value': 141, 'new_value': 179}, {'field': 'onlineAmount', 'old_value': 1382.0, 'new_value': 1554.3}, {'field': 'onlineCount', 'old_value': 26, 'new_value': 31}]
2025-06-05 08:12:24,078 - INFO - 月销售数据同步完成！更新: 211 条，插入: 0 条，错误: 0 条，跳过: 1191 条
2025-06-05 08:12:24,078 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-7 至 2025-6
2025-06-05 08:12:24,735 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250605.xlsx
2025-06-05 08:12:24,735 - INFO - 综合数据同步流程完成！
2025-06-05 08:12:24,797 - INFO - 综合数据同步完成
2025-06-05 08:12:24,797 - INFO - ==================================================
2025-06-05 08:12:24,797 - INFO - 程序退出
2025-06-05 08:12:24,797 - INFO - ==================================================
