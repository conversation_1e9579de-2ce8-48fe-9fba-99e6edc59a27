2025-04-28 00:00:03,450 - INFO - =================使用默认全量同步=============
2025-04-28 00:00:04,560 - INFO - MySQL查询成功，共获取 2639 条记录
2025-04-28 00:00:04,560 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-28 00:00:04,592 - INFO - 开始处理日期: 2025-01
2025-04-28 00:00:04,592 - INFO - Request Parameters - Page 1:
2025-04-28 00:00:04,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:04,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:05,483 - INFO - Response - Page 1:
2025-04-28 00:00:05,686 - INFO - 第 1 页获取到 100 条记录
2025-04-28 00:00:05,686 - INFO - Request Parameters - Page 2:
2025-04-28 00:00:05,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:05,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:06,202 - INFO - Response - Page 2:
2025-04-28 00:00:06,406 - INFO - 第 2 页获取到 100 条记录
2025-04-28 00:00:06,406 - INFO - Request Parameters - Page 3:
2025-04-28 00:00:06,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:06,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:06,906 - INFO - Response - Page 3:
2025-04-28 00:00:07,109 - INFO - 第 3 页获取到 100 条记录
2025-04-28 00:00:07,109 - INFO - Request Parameters - Page 4:
2025-04-28 00:00:07,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:07,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:07,625 - INFO - Response - Page 4:
2025-04-28 00:00:07,829 - INFO - 第 4 页获取到 100 条记录
2025-04-28 00:00:07,829 - INFO - Request Parameters - Page 5:
2025-04-28 00:00:07,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:07,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:08,282 - INFO - Response - Page 5:
2025-04-28 00:00:08,486 - INFO - 第 5 页获取到 100 条记录
2025-04-28 00:00:08,486 - INFO - Request Parameters - Page 6:
2025-04-28 00:00:08,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:08,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:09,095 - INFO - Response - Page 6:
2025-04-28 00:00:09,299 - INFO - 第 6 页获取到 100 条记录
2025-04-28 00:00:09,299 - INFO - Request Parameters - Page 7:
2025-04-28 00:00:09,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:09,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:09,737 - INFO - Response - Page 7:
2025-04-28 00:00:09,940 - INFO - 第 7 页获取到 82 条记录
2025-04-28 00:00:09,940 - INFO - 查询完成，共获取到 682 条记录
2025-04-28 00:00:09,940 - INFO - 获取到 682 条表单数据
2025-04-28 00:00:09,940 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-28 00:00:09,956 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 00:00:09,956 - INFO - 开始处理日期: 2025-02
2025-04-28 00:00:09,956 - INFO - Request Parameters - Page 1:
2025-04-28 00:00:09,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:09,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:10,456 - INFO - Response - Page 1:
2025-04-28 00:00:10,659 - INFO - 第 1 页获取到 100 条记录
2025-04-28 00:00:10,659 - INFO - Request Parameters - Page 2:
2025-04-28 00:00:10,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:10,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:11,144 - INFO - Response - Page 2:
2025-04-28 00:00:11,347 - INFO - 第 2 页获取到 100 条记录
2025-04-28 00:00:11,347 - INFO - Request Parameters - Page 3:
2025-04-28 00:00:11,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:11,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:11,848 - INFO - Response - Page 3:
2025-04-28 00:00:12,051 - INFO - 第 3 页获取到 100 条记录
2025-04-28 00:00:12,051 - INFO - Request Parameters - Page 4:
2025-04-28 00:00:12,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:12,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:12,489 - INFO - Response - Page 4:
2025-04-28 00:00:12,692 - INFO - 第 4 页获取到 100 条记录
2025-04-28 00:00:12,692 - INFO - Request Parameters - Page 5:
2025-04-28 00:00:12,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:12,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:13,208 - INFO - Response - Page 5:
2025-04-28 00:00:13,412 - INFO - 第 5 页获取到 100 条记录
2025-04-28 00:00:13,412 - INFO - Request Parameters - Page 6:
2025-04-28 00:00:13,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:13,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:13,850 - INFO - Response - Page 6:
2025-04-28 00:00:14,053 - INFO - 第 6 页获取到 100 条记录
2025-04-28 00:00:14,053 - INFO - Request Parameters - Page 7:
2025-04-28 00:00:14,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:14,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:14,506 - INFO - Response - Page 7:
2025-04-28 00:00:14,710 - INFO - 第 7 页获取到 70 条记录
2025-04-28 00:00:14,710 - INFO - 查询完成，共获取到 670 条记录
2025-04-28 00:00:14,710 - INFO - 获取到 670 条表单数据
2025-04-28 00:00:14,710 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-28 00:00:14,725 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 00:00:14,725 - INFO - 开始处理日期: 2025-03
2025-04-28 00:00:14,725 - INFO - Request Parameters - Page 1:
2025-04-28 00:00:14,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:14,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:15,226 - INFO - Response - Page 1:
2025-04-28 00:00:15,429 - INFO - 第 1 页获取到 100 条记录
2025-04-28 00:00:15,429 - INFO - Request Parameters - Page 2:
2025-04-28 00:00:15,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:15,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:16,023 - INFO - Response - Page 2:
2025-04-28 00:00:16,227 - INFO - 第 2 页获取到 100 条记录
2025-04-28 00:00:16,227 - INFO - Request Parameters - Page 3:
2025-04-28 00:00:16,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:16,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:16,633 - INFO - Response - Page 3:
2025-04-28 00:00:16,837 - INFO - 第 3 页获取到 100 条记录
2025-04-28 00:00:16,837 - INFO - Request Parameters - Page 4:
2025-04-28 00:00:16,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:16,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:17,321 - INFO - Response - Page 4:
2025-04-28 00:00:17,525 - INFO - 第 4 页获取到 100 条记录
2025-04-28 00:00:17,525 - INFO - Request Parameters - Page 5:
2025-04-28 00:00:17,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:17,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:18,041 - INFO - Response - Page 5:
2025-04-28 00:00:18,244 - INFO - 第 5 页获取到 100 条记录
2025-04-28 00:00:18,244 - INFO - Request Parameters - Page 6:
2025-04-28 00:00:18,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:18,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:18,838 - INFO - Response - Page 6:
2025-04-28 00:00:19,057 - INFO - 第 6 页获取到 100 条记录
2025-04-28 00:00:19,057 - INFO - Request Parameters - Page 7:
2025-04-28 00:00:19,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:19,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:19,480 - INFO - Response - Page 7:
2025-04-28 00:00:19,683 - INFO - 第 7 页获取到 61 条记录
2025-04-28 00:00:19,683 - INFO - 查询完成，共获取到 661 条记录
2025-04-28 00:00:19,683 - INFO - 获取到 661 条表单数据
2025-04-28 00:00:19,683 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-28 00:00:19,699 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 00:00:19,699 - INFO - 开始处理日期: 2025-04
2025-04-28 00:00:19,699 - INFO - Request Parameters - Page 1:
2025-04-28 00:00:19,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:19,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:20,183 - INFO - Response - Page 1:
2025-04-28 00:00:20,387 - INFO - 第 1 页获取到 100 条记录
2025-04-28 00:00:20,387 - INFO - Request Parameters - Page 2:
2025-04-28 00:00:20,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:20,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:20,856 - INFO - Response - Page 2:
2025-04-28 00:00:21,059 - INFO - 第 2 页获取到 100 条记录
2025-04-28 00:00:21,059 - INFO - Request Parameters - Page 3:
2025-04-28 00:00:21,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:21,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:21,560 - INFO - Response - Page 3:
2025-04-28 00:00:21,763 - INFO - 第 3 页获取到 100 条记录
2025-04-28 00:00:21,763 - INFO - Request Parameters - Page 4:
2025-04-28 00:00:21,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:21,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:22,216 - INFO - Response - Page 4:
2025-04-28 00:00:22,420 - INFO - 第 4 页获取到 100 条记录
2025-04-28 00:00:22,420 - INFO - Request Parameters - Page 5:
2025-04-28 00:00:22,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:22,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:22,904 - INFO - Response - Page 5:
2025-04-28 00:00:23,108 - INFO - 第 5 页获取到 100 条记录
2025-04-28 00:00:23,108 - INFO - Request Parameters - Page 6:
2025-04-28 00:00:23,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:23,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:23,655 - INFO - Response - Page 6:
2025-04-28 00:00:23,858 - INFO - 第 6 页获取到 100 条记录
2025-04-28 00:00:23,858 - INFO - Request Parameters - Page 7:
2025-04-28 00:00:23,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 00:00:23,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 00:00:24,202 - INFO - Response - Page 7:
2025-04-28 00:00:24,406 - INFO - 第 7 页获取到 26 条记录
2025-04-28 00:00:24,406 - INFO - 查询完成，共获取到 626 条记录
2025-04-28 00:00:24,406 - INFO - 获取到 626 条表单数据
2025-04-28 00:00:24,406 - INFO - 当前日期 2025-04 有 626 条MySQL数据需要处理
2025-04-28 00:00:24,406 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN5
2025-04-28 00:00:24,859 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN5
2025-04-28 00:00:24,859 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21639.86, 'new_value': 22244.78}, {'field': 'offline_amount', 'old_value': 240252.61, 'new_value': 245119.88}, {'field': 'total_amount', 'old_value': 261892.47, 'new_value': 267364.66}, {'field': 'order_count', 'old_value': 1259, 'new_value': 1287}]
2025-04-28 00:00:24,859 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M89
2025-04-28 00:00:25,360 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M89
2025-04-28 00:00:25,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14595.3, 'new_value': 14604.3}, {'field': 'total_amount', 'old_value': 14595.3, 'new_value': 14604.3}, {'field': 'order_count', 'old_value': 156, 'new_value': 157}]
2025-04-28 00:00:25,360 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M99
2025-04-28 00:00:25,813 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M99
2025-04-28 00:00:25,813 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39025.21, 'new_value': 40141.38}, {'field': 'offline_amount', 'old_value': 290623.1, 'new_value': 299070.28}, {'field': 'total_amount', 'old_value': 329648.31, 'new_value': 339211.66}, {'field': 'order_count', 'old_value': 2280, 'new_value': 2351}]
2025-04-28 00:00:25,813 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MT9
2025-04-28 00:00:26,235 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MT9
2025-04-28 00:00:26,235 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37520.27, 'new_value': 38973.95}, {'field': 'offline_amount', 'old_value': 510286.59, 'new_value': 525519.92}, {'field': 'total_amount', 'old_value': 547806.86, 'new_value': 564493.87}, {'field': 'order_count', 'old_value': 4824, 'new_value': 4987}]
2025-04-28 00:00:26,235 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPA
2025-04-28 00:00:26,689 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPA
2025-04-28 00:00:26,689 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 310034.63, 'new_value': 321429.85}, {'field': 'total_amount', 'old_value': 310034.63, 'new_value': 321429.85}, {'field': 'order_count', 'old_value': 1582, 'new_value': 1640}]
2025-04-28 00:00:26,689 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN5
2025-04-28 00:00:27,064 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN5
2025-04-28 00:00:27,064 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86100.66, 'new_value': 88740.68}, {'field': 'offline_amount', 'old_value': 169159.84, 'new_value': 174309.91}, {'field': 'total_amount', 'old_value': 255260.5, 'new_value': 263050.59}, {'field': 'order_count', 'old_value': 3661, 'new_value': 3726}]
2025-04-28 00:00:27,064 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO5
2025-04-28 00:00:27,502 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO5
2025-04-28 00:00:27,502 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5516.4, 'new_value': 5650.2}, {'field': 'total_amount', 'old_value': 31316.4, 'new_value': 31450.2}, {'field': 'order_count', 'old_value': 83, 'new_value': 85}]
2025-04-28 00:00:27,502 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV5
2025-04-28 00:00:27,956 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV5
2025-04-28 00:00:27,956 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49221.37, 'new_value': 49901.32}, {'field': 'total_amount', 'old_value': 49221.37, 'new_value': 49901.32}, {'field': 'order_count', 'old_value': 212, 'new_value': 215}]
2025-04-28 00:00:27,956 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX5
2025-04-28 00:00:28,347 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX5
2025-04-28 00:00:28,347 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1900.0, 'new_value': 2000.0}, {'field': 'offline_amount', 'old_value': 20535.98, 'new_value': 21530.54}, {'field': 'total_amount', 'old_value': 22435.98, 'new_value': 23530.54}, {'field': 'order_count', 'old_value': 439, 'new_value': 452}]
2025-04-28 00:00:28,347 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV6
2025-04-28 00:00:28,769 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV6
2025-04-28 00:00:28,769 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 161299.47, 'new_value': 180161.14}, {'field': 'total_amount', 'old_value': 566486.14, 'new_value': 585347.81}, {'field': 'order_count', 'old_value': 2553, 'new_value': 2620}]
2025-04-28 00:00:28,769 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW6
2025-04-28 00:00:29,176 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW6
2025-04-28 00:00:29,176 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 221367.56, 'new_value': 229366.26}, {'field': 'offline_amount', 'old_value': 540135.68, 'new_value': 561802.35}, {'field': 'total_amount', 'old_value': 761503.24, 'new_value': 791168.61}, {'field': 'order_count', 'old_value': 4627, 'new_value': 4782}]
2025-04-28 00:00:29,176 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX7
2025-04-28 00:00:29,551 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX7
2025-04-28 00:00:29,551 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92945.8, 'new_value': 94931.8}, {'field': 'offline_amount', 'old_value': 53073.44, 'new_value': 54208.24}, {'field': 'total_amount', 'old_value': 146019.24, 'new_value': 149140.04}, {'field': 'order_count', 'old_value': 965, 'new_value': 987}]
2025-04-28 00:00:29,551 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHH
2025-04-28 00:00:30,004 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHH
2025-04-28 00:00:30,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 236350.29, 'new_value': 245659.19}, {'field': 'total_amount', 'old_value': 236350.29, 'new_value': 245659.19}, {'field': 'order_count', 'old_value': 1439, 'new_value': 1494}]
2025-04-28 00:00:30,004 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIH
2025-04-28 00:00:30,411 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIH
2025-04-28 00:00:30,411 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 227601.38, 'new_value': 234764.25}, {'field': 'offline_amount', 'old_value': 54367.98, 'new_value': 55394.85}, {'field': 'total_amount', 'old_value': 281969.36, 'new_value': 290159.1}, {'field': 'order_count', 'old_value': 1121, 'new_value': 1158}]
2025-04-28 00:00:30,411 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1I
2025-04-28 00:00:30,818 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1I
2025-04-28 00:00:30,818 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48664.15, 'new_value': 50036.36}, {'field': 'offline_amount', 'old_value': 39610.18, 'new_value': 40780.03}, {'field': 'total_amount', 'old_value': 88274.33, 'new_value': 90816.39}, {'field': 'order_count', 'old_value': 6714, 'new_value': 6893}]
2025-04-28 00:00:30,818 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZI
2025-04-28 00:00:31,224 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZI
2025-04-28 00:00:31,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235613.2, 'new_value': 240011.6}, {'field': 'total_amount', 'old_value': 235613.2, 'new_value': 240011.6}, {'field': 'order_count', 'old_value': 971, 'new_value': 996}]
2025-04-28 00:00:31,224 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ
2025-04-28 00:00:31,662 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ
2025-04-28 00:00:31,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 289444.11, 'new_value': 295990.26}, {'field': 'offline_amount', 'old_value': 1422720.95, 'new_value': 1474393.98}, {'field': 'total_amount', 'old_value': 1712165.06, 'new_value': 1770384.24}, {'field': 'order_count', 'old_value': 8756, 'new_value': 9078}]
2025-04-28 00:00:31,662 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME7
2025-04-28 00:00:32,116 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME7
2025-04-28 00:00:32,116 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140321.26, 'new_value': 143773.71}, {'field': 'total_amount', 'old_value': 140321.26, 'new_value': 143773.71}, {'field': 'order_count', 'old_value': 11191, 'new_value': 11551}]
2025-04-28 00:00:32,116 - INFO - 日期 2025-04 处理完成 - 更新: 18 条，插入: 0 条，错误: 0 条
2025-04-28 00:00:32,116 - INFO - 数据同步完成！更新: 18 条，插入: 0 条，错误: 0 条
2025-04-28 00:00:32,116 - INFO - =================同步完成====================
2025-04-28 03:00:03,775 - INFO - =================使用默认全量同步=============
2025-04-28 03:00:04,885 - INFO - MySQL查询成功，共获取 2639 条记录
2025-04-28 03:00:04,885 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-28 03:00:04,901 - INFO - 开始处理日期: 2025-01
2025-04-28 03:00:04,901 - INFO - Request Parameters - Page 1:
2025-04-28 03:00:04,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:04,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:05,746 - INFO - Response - Page 1:
2025-04-28 03:00:05,949 - INFO - 第 1 页获取到 100 条记录
2025-04-28 03:00:05,949 - INFO - Request Parameters - Page 2:
2025-04-28 03:00:05,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:05,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:06,418 - INFO - Response - Page 2:
2025-04-28 03:00:06,621 - INFO - 第 2 页获取到 100 条记录
2025-04-28 03:00:06,621 - INFO - Request Parameters - Page 3:
2025-04-28 03:00:06,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:06,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:07,262 - INFO - Response - Page 3:
2025-04-28 03:00:07,466 - INFO - 第 3 页获取到 100 条记录
2025-04-28 03:00:07,466 - INFO - Request Parameters - Page 4:
2025-04-28 03:00:07,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:07,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:07,904 - INFO - Response - Page 4:
2025-04-28 03:00:08,107 - INFO - 第 4 页获取到 100 条记录
2025-04-28 03:00:08,107 - INFO - Request Parameters - Page 5:
2025-04-28 03:00:08,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:08,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:08,607 - INFO - Response - Page 5:
2025-04-28 03:00:08,811 - INFO - 第 5 页获取到 100 条记录
2025-04-28 03:00:08,811 - INFO - Request Parameters - Page 6:
2025-04-28 03:00:08,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:08,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:09,280 - INFO - Response - Page 6:
2025-04-28 03:00:09,483 - INFO - 第 6 页获取到 100 条记录
2025-04-28 03:00:09,483 - INFO - Request Parameters - Page 7:
2025-04-28 03:00:09,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:09,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:09,937 - INFO - Response - Page 7:
2025-04-28 03:00:10,140 - INFO - 第 7 页获取到 82 条记录
2025-04-28 03:00:10,140 - INFO - 查询完成，共获取到 682 条记录
2025-04-28 03:00:10,140 - INFO - 获取到 682 条表单数据
2025-04-28 03:00:10,140 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-28 03:00:10,156 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 03:00:10,156 - INFO - 开始处理日期: 2025-02
2025-04-28 03:00:10,156 - INFO - Request Parameters - Page 1:
2025-04-28 03:00:10,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:10,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:10,640 - INFO - Response - Page 1:
2025-04-28 03:00:10,844 - INFO - 第 1 页获取到 100 条记录
2025-04-28 03:00:10,844 - INFO - Request Parameters - Page 2:
2025-04-28 03:00:10,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:10,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:11,329 - INFO - Response - Page 2:
2025-04-28 03:00:11,532 - INFO - 第 2 页获取到 100 条记录
2025-04-28 03:00:11,532 - INFO - Request Parameters - Page 3:
2025-04-28 03:00:11,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:11,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:12,048 - INFO - Response - Page 3:
2025-04-28 03:00:12,251 - INFO - 第 3 页获取到 100 条记录
2025-04-28 03:00:12,251 - INFO - Request Parameters - Page 4:
2025-04-28 03:00:12,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:12,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:12,736 - INFO - Response - Page 4:
2025-04-28 03:00:12,939 - INFO - 第 4 页获取到 100 条记录
2025-04-28 03:00:12,939 - INFO - Request Parameters - Page 5:
2025-04-28 03:00:12,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:12,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:13,393 - INFO - Response - Page 5:
2025-04-28 03:00:13,612 - INFO - 第 5 页获取到 100 条记录
2025-04-28 03:00:13,612 - INFO - Request Parameters - Page 6:
2025-04-28 03:00:13,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:13,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:14,097 - INFO - Response - Page 6:
2025-04-28 03:00:14,300 - INFO - 第 6 页获取到 100 条记录
2025-04-28 03:00:14,300 - INFO - Request Parameters - Page 7:
2025-04-28 03:00:14,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:14,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:14,769 - INFO - Response - Page 7:
2025-04-28 03:00:14,972 - INFO - 第 7 页获取到 70 条记录
2025-04-28 03:00:14,972 - INFO - 查询完成，共获取到 670 条记录
2025-04-28 03:00:14,972 - INFO - 获取到 670 条表单数据
2025-04-28 03:00:14,972 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-28 03:00:14,988 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 03:00:14,988 - INFO - 开始处理日期: 2025-03
2025-04-28 03:00:14,988 - INFO - Request Parameters - Page 1:
2025-04-28 03:00:14,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:14,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:15,457 - INFO - Response - Page 1:
2025-04-28 03:00:15,660 - INFO - 第 1 页获取到 100 条记录
2025-04-28 03:00:15,660 - INFO - Request Parameters - Page 2:
2025-04-28 03:00:15,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:15,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:16,114 - INFO - Response - Page 2:
2025-04-28 03:00:16,317 - INFO - 第 2 页获取到 100 条记录
2025-04-28 03:00:16,317 - INFO - Request Parameters - Page 3:
2025-04-28 03:00:16,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:16,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:16,818 - INFO - Response - Page 3:
2025-04-28 03:00:17,021 - INFO - 第 3 页获取到 100 条记录
2025-04-28 03:00:17,021 - INFO - Request Parameters - Page 4:
2025-04-28 03:00:17,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:17,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:17,490 - INFO - Response - Page 4:
2025-04-28 03:00:17,694 - INFO - 第 4 页获取到 100 条记录
2025-04-28 03:00:17,694 - INFO - Request Parameters - Page 5:
2025-04-28 03:00:17,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:17,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:18,178 - INFO - Response - Page 5:
2025-04-28 03:00:18,382 - INFO - 第 5 页获取到 100 条记录
2025-04-28 03:00:18,382 - INFO - Request Parameters - Page 6:
2025-04-28 03:00:18,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:18,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:18,851 - INFO - Response - Page 6:
2025-04-28 03:00:19,054 - INFO - 第 6 页获取到 100 条记录
2025-04-28 03:00:19,054 - INFO - Request Parameters - Page 7:
2025-04-28 03:00:19,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:19,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:19,445 - INFO - Response - Page 7:
2025-04-28 03:00:19,648 - INFO - 第 7 页获取到 61 条记录
2025-04-28 03:00:19,648 - INFO - 查询完成，共获取到 661 条记录
2025-04-28 03:00:19,648 - INFO - 获取到 661 条表单数据
2025-04-28 03:00:19,648 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-28 03:00:19,664 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 03:00:19,664 - INFO - 开始处理日期: 2025-04
2025-04-28 03:00:19,664 - INFO - Request Parameters - Page 1:
2025-04-28 03:00:19,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:19,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:20,149 - INFO - Response - Page 1:
2025-04-28 03:00:20,352 - INFO - 第 1 页获取到 100 条记录
2025-04-28 03:00:20,352 - INFO - Request Parameters - Page 2:
2025-04-28 03:00:20,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:20,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:20,790 - INFO - Response - Page 2:
2025-04-28 03:00:20,993 - INFO - 第 2 页获取到 100 条记录
2025-04-28 03:00:20,993 - INFO - Request Parameters - Page 3:
2025-04-28 03:00:20,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:20,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:21,478 - INFO - Response - Page 3:
2025-04-28 03:00:21,681 - INFO - 第 3 页获取到 100 条记录
2025-04-28 03:00:21,681 - INFO - Request Parameters - Page 4:
2025-04-28 03:00:21,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:21,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:22,197 - INFO - Response - Page 4:
2025-04-28 03:00:22,401 - INFO - 第 4 页获取到 100 条记录
2025-04-28 03:00:22,401 - INFO - Request Parameters - Page 5:
2025-04-28 03:00:22,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:22,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:22,901 - INFO - Response - Page 5:
2025-04-28 03:00:23,105 - INFO - 第 5 页获取到 100 条记录
2025-04-28 03:00:23,105 - INFO - Request Parameters - Page 6:
2025-04-28 03:00:23,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:23,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:23,574 - INFO - Response - Page 6:
2025-04-28 03:00:23,777 - INFO - 第 6 页获取到 100 条记录
2025-04-28 03:00:23,777 - INFO - Request Parameters - Page 7:
2025-04-28 03:00:23,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 03:00:23,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 03:00:24,074 - INFO - Response - Page 7:
2025-04-28 03:00:24,277 - INFO - 第 7 页获取到 26 条记录
2025-04-28 03:00:24,277 - INFO - 查询完成，共获取到 626 条记录
2025-04-28 03:00:24,277 - INFO - 获取到 626 条表单数据
2025-04-28 03:00:24,277 - INFO - 当前日期 2025-04 有 626 条MySQL数据需要处理
2025-04-28 03:00:24,293 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MFB
2025-04-28 03:00:24,747 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MFB
2025-04-28 03:00:24,747 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28260.78, 'new_value': 39762.92}, {'field': 'offline_amount', 'old_value': 324402.15, 'new_value': 325398.75}, {'field': 'total_amount', 'old_value': 352662.93, 'new_value': 365161.67}, {'field': 'order_count', 'old_value': 658, 'new_value': 678}]
2025-04-28 03:00:24,747 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M8
2025-04-28 03:00:25,200 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M8
2025-04-28 03:00:25,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36837.92, 'new_value': 39612.92}, {'field': 'total_amount', 'old_value': 69474.3, 'new_value': 72249.3}, {'field': 'order_count', 'old_value': 2559, 'new_value': 2651}]
2025-04-28 03:00:25,200 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MZ1
2025-04-28 03:00:25,716 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MZ1
2025-04-28 03:00:25,716 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 225514.19, 'new_value': 233858.48}, {'field': 'offline_amount', 'old_value': 491741.05, 'new_value': 513741.05}, {'field': 'total_amount', 'old_value': 717255.24, 'new_value': 747599.53}, {'field': 'order_count', 'old_value': 1608, 'new_value': 1686}]
2025-04-28 03:00:25,716 - INFO - 日期 2025-04 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-04-28 03:00:25,716 - INFO - 数据同步完成！更新: 3 条，插入: 0 条，错误: 0 条
2025-04-28 03:00:25,716 - INFO - =================同步完成====================
2025-04-28 06:00:03,369 - INFO - =================使用默认全量同步=============
2025-04-28 06:00:04,479 - INFO - MySQL查询成功，共获取 2639 条记录
2025-04-28 06:00:04,479 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-28 06:00:04,495 - INFO - 开始处理日期: 2025-01
2025-04-28 06:00:04,495 - INFO - Request Parameters - Page 1:
2025-04-28 06:00:04,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:04,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:05,402 - INFO - Response - Page 1:
2025-04-28 06:00:05,605 - INFO - 第 1 页获取到 100 条记录
2025-04-28 06:00:05,605 - INFO - Request Parameters - Page 2:
2025-04-28 06:00:05,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:05,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:06,371 - INFO - Response - Page 2:
2025-04-28 06:00:06,574 - INFO - 第 2 页获取到 100 条记录
2025-04-28 06:00:06,574 - INFO - Request Parameters - Page 3:
2025-04-28 06:00:06,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:06,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:07,091 - INFO - Response - Page 3:
2025-04-28 06:00:07,294 - INFO - 第 3 页获取到 100 条记录
2025-04-28 06:00:07,294 - INFO - Request Parameters - Page 4:
2025-04-28 06:00:07,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:07,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:07,794 - INFO - Response - Page 4:
2025-04-28 06:00:07,998 - INFO - 第 4 页获取到 100 条记录
2025-04-28 06:00:07,998 - INFO - Request Parameters - Page 5:
2025-04-28 06:00:07,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:07,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:08,514 - INFO - Response - Page 5:
2025-04-28 06:00:08,717 - INFO - 第 5 页获取到 100 条记录
2025-04-28 06:00:08,717 - INFO - Request Parameters - Page 6:
2025-04-28 06:00:08,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:08,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:09,311 - INFO - Response - Page 6:
2025-04-28 06:00:09,514 - INFO - 第 6 页获取到 100 条记录
2025-04-28 06:00:09,514 - INFO - Request Parameters - Page 7:
2025-04-28 06:00:09,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:09,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:09,968 - INFO - Response - Page 7:
2025-04-28 06:00:10,171 - INFO - 第 7 页获取到 82 条记录
2025-04-28 06:00:10,171 - INFO - 查询完成，共获取到 682 条记录
2025-04-28 06:00:10,171 - INFO - 获取到 682 条表单数据
2025-04-28 06:00:10,171 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-28 06:00:10,187 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 06:00:10,187 - INFO - 开始处理日期: 2025-02
2025-04-28 06:00:10,187 - INFO - Request Parameters - Page 1:
2025-04-28 06:00:10,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:10,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:10,797 - INFO - Response - Page 1:
2025-04-28 06:00:11,000 - INFO - 第 1 页获取到 100 条记录
2025-04-28 06:00:11,000 - INFO - Request Parameters - Page 2:
2025-04-28 06:00:11,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:11,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:11,516 - INFO - Response - Page 2:
2025-04-28 06:00:11,719 - INFO - 第 2 页获取到 100 条记录
2025-04-28 06:00:11,719 - INFO - Request Parameters - Page 3:
2025-04-28 06:00:11,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:11,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:12,204 - INFO - Response - Page 3:
2025-04-28 06:00:12,407 - INFO - 第 3 页获取到 100 条记录
2025-04-28 06:00:12,407 - INFO - Request Parameters - Page 4:
2025-04-28 06:00:12,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:12,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:12,877 - INFO - Response - Page 4:
2025-04-28 06:00:13,080 - INFO - 第 4 页获取到 100 条记录
2025-04-28 06:00:13,080 - INFO - Request Parameters - Page 5:
2025-04-28 06:00:13,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:13,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:13,549 - INFO - Response - Page 5:
2025-04-28 06:00:13,752 - INFO - 第 5 页获取到 100 条记录
2025-04-28 06:00:13,752 - INFO - Request Parameters - Page 6:
2025-04-28 06:00:13,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:13,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:14,253 - INFO - Response - Page 6:
2025-04-28 06:00:14,456 - INFO - 第 6 页获取到 100 条记录
2025-04-28 06:00:14,456 - INFO - Request Parameters - Page 7:
2025-04-28 06:00:14,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:14,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:14,847 - INFO - Response - Page 7:
2025-04-28 06:00:15,050 - INFO - 第 7 页获取到 70 条记录
2025-04-28 06:00:15,050 - INFO - 查询完成，共获取到 670 条记录
2025-04-28 06:00:15,050 - INFO - 获取到 670 条表单数据
2025-04-28 06:00:15,050 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-28 06:00:15,066 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 06:00:15,066 - INFO - 开始处理日期: 2025-03
2025-04-28 06:00:15,066 - INFO - Request Parameters - Page 1:
2025-04-28 06:00:15,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:15,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:15,551 - INFO - Response - Page 1:
2025-04-28 06:00:15,754 - INFO - 第 1 页获取到 100 条记录
2025-04-28 06:00:15,754 - INFO - Request Parameters - Page 2:
2025-04-28 06:00:15,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:15,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:16,223 - INFO - Response - Page 2:
2025-04-28 06:00:16,426 - INFO - 第 2 页获取到 100 条记录
2025-04-28 06:00:16,426 - INFO - Request Parameters - Page 3:
2025-04-28 06:00:16,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:16,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:16,911 - INFO - Response - Page 3:
2025-04-28 06:00:17,114 - INFO - 第 3 页获取到 100 条记录
2025-04-28 06:00:17,114 - INFO - Request Parameters - Page 4:
2025-04-28 06:00:17,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:17,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:17,584 - INFO - Response - Page 4:
2025-04-28 06:00:17,787 - INFO - 第 4 页获取到 100 条记录
2025-04-28 06:00:17,787 - INFO - Request Parameters - Page 5:
2025-04-28 06:00:17,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:17,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:18,350 - INFO - Response - Page 5:
2025-04-28 06:00:18,553 - INFO - 第 5 页获取到 100 条记录
2025-04-28 06:00:18,553 - INFO - Request Parameters - Page 6:
2025-04-28 06:00:18,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:18,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:19,007 - INFO - Response - Page 6:
2025-04-28 06:00:19,210 - INFO - 第 6 页获取到 100 条记录
2025-04-28 06:00:19,210 - INFO - Request Parameters - Page 7:
2025-04-28 06:00:19,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:19,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:19,648 - INFO - Response - Page 7:
2025-04-28 06:00:19,851 - INFO - 第 7 页获取到 61 条记录
2025-04-28 06:00:19,851 - INFO - 查询完成，共获取到 661 条记录
2025-04-28 06:00:19,851 - INFO - 获取到 661 条表单数据
2025-04-28 06:00:19,851 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-28 06:00:19,867 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 06:00:19,867 - INFO - 开始处理日期: 2025-04
2025-04-28 06:00:19,867 - INFO - Request Parameters - Page 1:
2025-04-28 06:00:19,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:19,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:20,336 - INFO - Response - Page 1:
2025-04-28 06:00:20,539 - INFO - 第 1 页获取到 100 条记录
2025-04-28 06:00:20,539 - INFO - Request Parameters - Page 2:
2025-04-28 06:00:20,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:20,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:20,993 - INFO - Response - Page 2:
2025-04-28 06:00:21,196 - INFO - 第 2 页获取到 100 条记录
2025-04-28 06:00:21,196 - INFO - Request Parameters - Page 3:
2025-04-28 06:00:21,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:21,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:21,728 - INFO - Response - Page 3:
2025-04-28 06:00:21,931 - INFO - 第 3 页获取到 100 条记录
2025-04-28 06:00:21,931 - INFO - Request Parameters - Page 4:
2025-04-28 06:00:21,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:21,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:22,400 - INFO - Response - Page 4:
2025-04-28 06:00:22,603 - INFO - 第 4 页获取到 100 条记录
2025-04-28 06:00:22,603 - INFO - Request Parameters - Page 5:
2025-04-28 06:00:22,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:22,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:23,041 - INFO - Response - Page 5:
2025-04-28 06:00:23,245 - INFO - 第 5 页获取到 100 条记录
2025-04-28 06:00:23,245 - INFO - Request Parameters - Page 6:
2025-04-28 06:00:23,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:23,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:23,729 - INFO - Response - Page 6:
2025-04-28 06:00:23,933 - INFO - 第 6 页获取到 100 条记录
2025-04-28 06:00:23,933 - INFO - Request Parameters - Page 7:
2025-04-28 06:00:23,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 06:00:23,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 06:00:24,339 - INFO - Response - Page 7:
2025-04-28 06:00:24,543 - INFO - 第 7 页获取到 26 条记录
2025-04-28 06:00:24,543 - INFO - 查询完成，共获取到 626 条记录
2025-04-28 06:00:24,543 - INFO - 获取到 626 条表单数据
2025-04-28 06:00:24,543 - INFO - 当前日期 2025-04 有 626 条MySQL数据需要处理
2025-04-28 06:00:24,558 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 06:00:24,558 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 06:00:24,558 - INFO - =================同步完成====================
2025-04-28 09:00:01,865 - INFO - =================使用默认全量同步=============
2025-04-28 09:00:02,974 - INFO - MySQL查询成功，共获取 2639 条记录
2025-04-28 09:00:02,974 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-28 09:00:02,990 - INFO - 开始处理日期: 2025-01
2025-04-28 09:00:02,990 - INFO - Request Parameters - Page 1:
2025-04-28 09:00:02,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:02,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:03,880 - INFO - Response - Page 1:
2025-04-28 09:00:04,084 - INFO - 第 1 页获取到 100 条记录
2025-04-28 09:00:04,084 - INFO - Request Parameters - Page 2:
2025-04-28 09:00:04,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:04,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:04,771 - INFO - Response - Page 2:
2025-04-28 09:00:04,974 - INFO - 第 2 页获取到 100 条记录
2025-04-28 09:00:04,974 - INFO - Request Parameters - Page 3:
2025-04-28 09:00:04,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:04,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:05,537 - INFO - Response - Page 3:
2025-04-28 09:00:05,740 - INFO - 第 3 页获取到 100 条记录
2025-04-28 09:00:05,740 - INFO - Request Parameters - Page 4:
2025-04-28 09:00:05,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:05,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:06,349 - INFO - Response - Page 4:
2025-04-28 09:00:06,552 - INFO - 第 4 页获取到 100 条记录
2025-04-28 09:00:06,552 - INFO - Request Parameters - Page 5:
2025-04-28 09:00:06,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:06,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:07,037 - INFO - Response - Page 5:
2025-04-28 09:00:07,240 - INFO - 第 5 页获取到 100 条记录
2025-04-28 09:00:07,240 - INFO - Request Parameters - Page 6:
2025-04-28 09:00:07,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:07,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:07,693 - INFO - Response - Page 6:
2025-04-28 09:00:07,896 - INFO - 第 6 页获取到 100 条记录
2025-04-28 09:00:07,896 - INFO - Request Parameters - Page 7:
2025-04-28 09:00:07,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:07,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:08,318 - INFO - Response - Page 7:
2025-04-28 09:00:08,521 - INFO - 第 7 页获取到 82 条记录
2025-04-28 09:00:08,521 - INFO - 查询完成，共获取到 682 条记录
2025-04-28 09:00:08,521 - INFO - 获取到 682 条表单数据
2025-04-28 09:00:08,521 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-28 09:00:08,537 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 09:00:08,537 - INFO - 开始处理日期: 2025-02
2025-04-28 09:00:08,537 - INFO - Request Parameters - Page 1:
2025-04-28 09:00:08,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:08,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:09,005 - INFO - Response - Page 1:
2025-04-28 09:00:09,209 - INFO - 第 1 页获取到 100 条记录
2025-04-28 09:00:09,209 - INFO - Request Parameters - Page 2:
2025-04-28 09:00:09,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:09,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:09,724 - INFO - Response - Page 2:
2025-04-28 09:00:09,927 - INFO - 第 2 页获取到 100 条记录
2025-04-28 09:00:09,927 - INFO - Request Parameters - Page 3:
2025-04-28 09:00:09,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:09,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:10,443 - INFO - Response - Page 3:
2025-04-28 09:00:10,646 - INFO - 第 3 页获取到 100 条记录
2025-04-28 09:00:10,646 - INFO - Request Parameters - Page 4:
2025-04-28 09:00:10,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:10,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:11,224 - INFO - Response - Page 4:
2025-04-28 09:00:11,427 - INFO - 第 4 页获取到 100 条记录
2025-04-28 09:00:11,427 - INFO - Request Parameters - Page 5:
2025-04-28 09:00:11,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:11,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:11,927 - INFO - Response - Page 5:
2025-04-28 09:00:12,130 - INFO - 第 5 页获取到 100 条记录
2025-04-28 09:00:12,130 - INFO - Request Parameters - Page 6:
2025-04-28 09:00:12,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:12,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:12,568 - INFO - Response - Page 6:
2025-04-28 09:00:12,771 - INFO - 第 6 页获取到 100 条记录
2025-04-28 09:00:12,771 - INFO - Request Parameters - Page 7:
2025-04-28 09:00:12,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:12,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:13,224 - INFO - Response - Page 7:
2025-04-28 09:00:13,427 - INFO - 第 7 页获取到 70 条记录
2025-04-28 09:00:13,427 - INFO - 查询完成，共获取到 670 条记录
2025-04-28 09:00:13,427 - INFO - 获取到 670 条表单数据
2025-04-28 09:00:13,427 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-28 09:00:13,443 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 09:00:13,443 - INFO - 开始处理日期: 2025-03
2025-04-28 09:00:13,443 - INFO - Request Parameters - Page 1:
2025-04-28 09:00:13,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:13,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:13,943 - INFO - Response - Page 1:
2025-04-28 09:00:14,146 - INFO - 第 1 页获取到 100 条记录
2025-04-28 09:00:14,146 - INFO - Request Parameters - Page 2:
2025-04-28 09:00:14,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:14,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:14,599 - INFO - Response - Page 2:
2025-04-28 09:00:14,802 - INFO - 第 2 页获取到 100 条记录
2025-04-28 09:00:14,802 - INFO - Request Parameters - Page 3:
2025-04-28 09:00:14,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:14,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:15,271 - INFO - Response - Page 3:
2025-04-28 09:00:15,474 - INFO - 第 3 页获取到 100 条记录
2025-04-28 09:00:15,474 - INFO - Request Parameters - Page 4:
2025-04-28 09:00:15,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:15,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:15,943 - INFO - Response - Page 4:
2025-04-28 09:00:16,146 - INFO - 第 4 页获取到 100 条记录
2025-04-28 09:00:16,146 - INFO - Request Parameters - Page 5:
2025-04-28 09:00:16,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:16,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:16,615 - INFO - Response - Page 5:
2025-04-28 09:00:16,818 - INFO - 第 5 页获取到 100 条记录
2025-04-28 09:00:16,818 - INFO - Request Parameters - Page 6:
2025-04-28 09:00:16,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:16,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:17,349 - INFO - Response - Page 6:
2025-04-28 09:00:17,552 - INFO - 第 6 页获取到 100 条记录
2025-04-28 09:00:17,552 - INFO - Request Parameters - Page 7:
2025-04-28 09:00:17,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:17,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:18,052 - INFO - Response - Page 7:
2025-04-28 09:00:18,255 - INFO - 第 7 页获取到 61 条记录
2025-04-28 09:00:18,255 - INFO - 查询完成，共获取到 661 条记录
2025-04-28 09:00:18,255 - INFO - 获取到 661 条表单数据
2025-04-28 09:00:18,255 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-28 09:00:18,271 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 09:00:18,271 - INFO - 开始处理日期: 2025-04
2025-04-28 09:00:18,271 - INFO - Request Parameters - Page 1:
2025-04-28 09:00:18,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:18,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:18,896 - INFO - Response - Page 1:
2025-04-28 09:00:19,099 - INFO - 第 1 页获取到 100 条记录
2025-04-28 09:00:19,099 - INFO - Request Parameters - Page 2:
2025-04-28 09:00:19,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:19,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:19,646 - INFO - Response - Page 2:
2025-04-28 09:00:19,849 - INFO - 第 2 页获取到 100 条记录
2025-04-28 09:00:19,849 - INFO - Request Parameters - Page 3:
2025-04-28 09:00:19,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:19,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:20,302 - INFO - Response - Page 3:
2025-04-28 09:00:20,505 - INFO - 第 3 页获取到 100 条记录
2025-04-28 09:00:20,505 - INFO - Request Parameters - Page 4:
2025-04-28 09:00:20,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:20,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:21,021 - INFO - Response - Page 4:
2025-04-28 09:00:21,224 - INFO - 第 4 页获取到 100 条记录
2025-04-28 09:00:21,224 - INFO - Request Parameters - Page 5:
2025-04-28 09:00:21,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:21,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:21,724 - INFO - Response - Page 5:
2025-04-28 09:00:21,927 - INFO - 第 5 页获取到 100 条记录
2025-04-28 09:00:21,927 - INFO - Request Parameters - Page 6:
2025-04-28 09:00:21,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:21,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:22,412 - INFO - Response - Page 6:
2025-04-28 09:00:22,615 - INFO - 第 6 页获取到 100 条记录
2025-04-28 09:00:22,615 - INFO - Request Parameters - Page 7:
2025-04-28 09:00:22,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 09:00:22,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 09:00:22,958 - INFO - Response - Page 7:
2025-04-28 09:00:23,162 - INFO - 第 7 页获取到 26 条记录
2025-04-28 09:00:23,162 - INFO - 查询完成，共获取到 626 条记录
2025-04-28 09:00:23,162 - INFO - 获取到 626 条表单数据
2025-04-28 09:00:23,162 - INFO - 当前日期 2025-04 有 626 条MySQL数据需要处理
2025-04-28 09:00:23,162 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M03
2025-04-28 09:00:23,740 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M03
2025-04-28 09:00:23,740 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 136097.0, 'new_value': 145507.0}, {'field': 'total_amount', 'old_value': 401050.9, 'new_value': 410460.9}]
2025-04-28 09:00:23,740 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M13
2025-04-28 09:00:24,208 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M13
2025-04-28 09:00:24,208 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16024.47, 'new_value': 16769.91}, {'field': 'offline_amount', 'old_value': 42562.14, 'new_value': 44299.47}, {'field': 'total_amount', 'old_value': 58586.61, 'new_value': 61069.38}, {'field': 'order_count', 'old_value': 3115, 'new_value': 3248}]
2025-04-28 09:00:24,208 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M33
2025-04-28 09:00:24,708 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M33
2025-04-28 09:00:24,708 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82293.75, 'new_value': 85615.81}, {'field': 'offline_amount', 'old_value': 221397.98, 'new_value': 227990.35}, {'field': 'total_amount', 'old_value': 303691.73, 'new_value': 313606.16}, {'field': 'order_count', 'old_value': 10740, 'new_value': 11135}]
2025-04-28 09:00:24,708 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M63
2025-04-28 09:00:25,130 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M63
2025-04-28 09:00:25,130 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170000.0, 'new_value': 175000.0}, {'field': 'total_amount', 'old_value': 170000.0, 'new_value': 175000.0}, {'field': 'order_count', 'old_value': 246, 'new_value': 247}]
2025-04-28 09:00:25,130 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M73
2025-04-28 09:00:25,662 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M73
2025-04-28 09:00:25,662 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170000.0, 'new_value': 175000.0}, {'field': 'total_amount', 'old_value': 170000.0, 'new_value': 175000.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 151}]
2025-04-28 09:00:25,662 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M83
2025-04-28 09:00:26,162 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M83
2025-04-28 09:00:26,162 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1700000.0, 'new_value': 1750000.0}, {'field': 'total_amount', 'old_value': 1700000.0, 'new_value': 1750000.0}, {'field': 'order_count', 'old_value': 351, 'new_value': 352}]
2025-04-28 09:00:26,162 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-04-28 09:00:26,583 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-04-28 09:00:26,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1800000.0, 'new_value': 1850000.0}, {'field': 'total_amount', 'old_value': 1800000.0, 'new_value': 1850000.0}, {'field': 'order_count', 'old_value': 494, 'new_value': 495}]
2025-04-28 09:00:26,583 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ME3
2025-04-28 09:00:27,052 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ME3
2025-04-28 09:00:27,052 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 277159.0, 'new_value': 286063.0}, {'field': 'total_amount', 'old_value': 277159.0, 'new_value': 286063.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 103}]
2025-04-28 09:00:27,052 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ML3
2025-04-28 09:00:27,490 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ML3
2025-04-28 09:00:27,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19489.9, 'new_value': 20293.9}, {'field': 'offline_amount', 'old_value': 29103.35, 'new_value': 29774.35}, {'field': 'total_amount', 'old_value': 48593.25, 'new_value': 50068.25}, {'field': 'order_count', 'old_value': 2324, 'new_value': 2398}]
2025-04-28 09:00:27,490 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MO3
2025-04-28 09:00:27,896 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MO3
2025-04-28 09:00:27,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92379.0, 'new_value': 103782.0}, {'field': 'total_amount', 'old_value': 92379.0, 'new_value': 103782.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 121}]
2025-04-28 09:00:27,896 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR3
2025-04-28 09:00:28,365 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR3
2025-04-28 09:00:28,365 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9621.0, 'new_value': 9977.0}, {'field': 'total_amount', 'old_value': 10211.0, 'new_value': 10567.0}, {'field': 'order_count', 'old_value': 198, 'new_value': 208}]
2025-04-28 09:00:28,365 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS3
2025-04-28 09:00:28,802 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS3
2025-04-28 09:00:28,802 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106989.0, 'new_value': 110573.0}, {'field': 'total_amount', 'old_value': 106989.0, 'new_value': 110573.0}, {'field': 'order_count', 'old_value': 5908, 'new_value': 6106}]
2025-04-28 09:00:28,802 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT3
2025-04-28 09:00:29,365 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT3
2025-04-28 09:00:29,365 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63686.08, 'new_value': 65186.67}, {'field': 'offline_amount', 'old_value': 126275.55, 'new_value': 130785.35}, {'field': 'total_amount', 'old_value': 189961.63, 'new_value': 195972.02}, {'field': 'order_count', 'old_value': 2079, 'new_value': 2151}]
2025-04-28 09:00:29,365 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ3
2025-04-28 09:00:29,818 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ3
2025-04-28 09:00:29,818 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61414.0, 'new_value': 63442.0}, {'field': 'offline_amount', 'old_value': 116972.16, 'new_value': 118157.85}, {'field': 'total_amount', 'old_value': 178386.16, 'new_value': 181599.85}, {'field': 'order_count', 'old_value': 238, 'new_value': 246}]
2025-04-28 09:00:29,818 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M64
2025-04-28 09:00:30,255 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M64
2025-04-28 09:00:30,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65263.0, 'new_value': 67787.0}, {'field': 'total_amount', 'old_value': 65263.0, 'new_value': 67787.0}, {'field': 'order_count', 'old_value': 1213, 'new_value': 1249}]
2025-04-28 09:00:30,255 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA4
2025-04-28 09:00:30,693 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA4
2025-04-28 09:00:30,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93244.6, 'new_value': 95364.3}, {'field': 'total_amount', 'old_value': 93244.6, 'new_value': 95364.3}, {'field': 'order_count', 'old_value': 602, 'new_value': 615}]
2025-04-28 09:00:30,693 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME4
2025-04-28 09:00:31,146 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME4
2025-04-28 09:00:31,146 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 147827.0, 'new_value': 155156.0}, {'field': 'offline_amount', 'old_value': 52758.66, 'new_value': 54700.11}, {'field': 'total_amount', 'old_value': 200585.66, 'new_value': 209856.11}, {'field': 'order_count', 'old_value': 1365, 'new_value': 1428}]
2025-04-28 09:00:31,146 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI4
2025-04-28 09:00:31,552 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI4
2025-04-28 09:00:31,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100011.06, 'new_value': 103215.36}, {'field': 'total_amount', 'old_value': 100011.06, 'new_value': 103215.36}, {'field': 'order_count', 'old_value': 2660, 'new_value': 2747}]
2025-04-28 09:00:31,552 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ4
2025-04-28 09:00:32,037 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ4
2025-04-28 09:00:32,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40384.0, 'new_value': 41181.0}, {'field': 'total_amount', 'old_value': 40384.0, 'new_value': 41181.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 126}]
2025-04-28 09:00:32,037 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML4
2025-04-28 09:00:32,474 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML4
2025-04-28 09:00:32,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37389.9, 'new_value': 38578.9}, {'field': 'total_amount', 'old_value': 37389.9, 'new_value': 38578.9}, {'field': 'order_count', 'old_value': 1389, 'new_value': 1428}]
2025-04-28 09:00:32,474 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM4
2025-04-28 09:00:33,052 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM4
2025-04-28 09:00:33,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11938.69, 'new_value': 12300.32}, {'field': 'offline_amount', 'old_value': 184075.26, 'new_value': 190145.51}, {'field': 'total_amount', 'old_value': 196013.95, 'new_value': 202445.83}, {'field': 'order_count', 'old_value': 2099, 'new_value': 2171}]
2025-04-28 09:00:33,052 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS4
2025-04-28 09:00:33,458 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS4
2025-04-28 09:00:33,458 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 188191.1, 'new_value': 191271.1}, {'field': 'offline_amount', 'old_value': 66540.02, 'new_value': 70395.36}, {'field': 'total_amount', 'old_value': 254731.12, 'new_value': 261666.46}, {'field': 'order_count', 'old_value': 1810, 'new_value': 1875}]
2025-04-28 09:00:33,458 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M95
2025-04-28 09:00:34,021 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M95
2025-04-28 09:00:34,021 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26767.84, 'new_value': 27522.84}, {'field': 'offline_amount', 'old_value': 19708.7, 'new_value': 21031.7}, {'field': 'total_amount', 'old_value': 46476.54, 'new_value': 48554.54}, {'field': 'order_count', 'old_value': 235, 'new_value': 242}]
2025-04-28 09:00:34,021 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB5
2025-04-28 09:00:34,458 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB5
2025-04-28 09:00:34,458 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21995.4, 'new_value': 22145.4}, {'field': 'offline_amount', 'old_value': 49757.07, 'new_value': 50201.87}, {'field': 'total_amount', 'old_value': 71752.47, 'new_value': 72347.27}, {'field': 'order_count', 'old_value': 838, 'new_value': 852}]
2025-04-28 09:00:34,458 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC5
2025-04-28 09:00:34,865 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC5
2025-04-28 09:00:34,865 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140396.27, 'new_value': 144523.94}, {'field': 'total_amount', 'old_value': 140396.27, 'new_value': 144523.94}, {'field': 'order_count', 'old_value': 1647, 'new_value': 1694}]
2025-04-28 09:00:34,865 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME5
2025-04-28 09:00:35,302 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME5
2025-04-28 09:00:35,302 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 213721.9, 'new_value': 216267.7}, {'field': 'total_amount', 'old_value': 213721.9, 'new_value': 216267.7}, {'field': 'order_count', 'old_value': 2627, 'new_value': 2687}]
2025-04-28 09:00:35,302 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ5
2025-04-28 09:00:35,833 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ5
2025-04-28 09:00:35,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2755.0, 'new_value': 3954.0}, {'field': 'total_amount', 'old_value': 2755.0, 'new_value': 3954.0}, {'field': 'order_count', 'old_value': 403, 'new_value': 405}]
2025-04-28 09:00:35,833 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO5
2025-04-28 09:00:36,271 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO5
2025-04-28 09:00:36,287 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3667.0, 'new_value': 3888.0}, {'field': 'offline_amount', 'old_value': 10703.0, 'new_value': 20528.0}, {'field': 'total_amount', 'old_value': 14370.0, 'new_value': 24416.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 52}]
2025-04-28 09:00:36,287 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M79
2025-04-28 09:00:36,755 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M79
2025-04-28 09:00:36,755 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 466641.75, 'new_value': 488115.99}, {'field': 'total_amount', 'old_value': 466641.75, 'new_value': 488115.99}, {'field': 'order_count', 'old_value': 2093, 'new_value': 2208}]
2025-04-28 09:00:36,755 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MF9
2025-04-28 09:00:37,255 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MF9
2025-04-28 09:00:37,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120968.4, 'new_value': 124833.4}, {'field': 'total_amount', 'old_value': 147281.21, 'new_value': 151146.21}, {'field': 'order_count', 'old_value': 3604, 'new_value': 3698}]
2025-04-28 09:00:37,255 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MG9
2025-04-28 09:00:37,708 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MG9
2025-04-28 09:00:37,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24881.62, 'new_value': 24887.62}, {'field': 'total_amount', 'old_value': 24881.62, 'new_value': 24887.62}, {'field': 'order_count', 'old_value': 144, 'new_value': 524}]
2025-04-28 09:00:37,708 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MH9
2025-04-28 09:00:38,115 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MH9
2025-04-28 09:00:38,115 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22710.1, 'new_value': 24167.02}, {'field': 'total_amount', 'old_value': 22710.1, 'new_value': 24167.02}, {'field': 'order_count', 'old_value': 38, 'new_value': 40}]
2025-04-28 09:00:38,115 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MK9
2025-04-28 09:00:38,599 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MK9
2025-04-28 09:00:38,599 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171087.0, 'new_value': 183471.0}, {'field': 'total_amount', 'old_value': 171087.0, 'new_value': 183471.0}, {'field': 'order_count', 'old_value': 344, 'new_value': 366}]
2025-04-28 09:00:38,599 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9ML9
2025-04-28 09:00:39,052 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9ML9
2025-04-28 09:00:39,052 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5928.5, 'new_value': 6126.5}, {'field': 'total_amount', 'old_value': 6315.5, 'new_value': 6513.5}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-04-28 09:00:39,052 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MM9
2025-04-28 09:00:39,505 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MM9
2025-04-28 09:00:39,505 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 142534.57, 'new_value': 146652.97}, {'field': 'offline_amount', 'old_value': 130121.98, 'new_value': 136651.14}, {'field': 'total_amount', 'old_value': 272656.55, 'new_value': 283304.11}, {'field': 'order_count', 'old_value': 869, 'new_value': 895}]
2025-04-28 09:00:39,505 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MN9
2025-04-28 09:00:39,990 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MN9
2025-04-28 09:00:39,990 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27788.0, 'new_value': 37575.0}, {'field': 'total_amount', 'old_value': 27788.0, 'new_value': 37575.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 133}]
2025-04-28 09:00:39,990 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MP9
2025-04-28 09:00:40,490 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MP9
2025-04-28 09:00:40,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82504.0, 'new_value': 88197.0}, {'field': 'offline_amount', 'old_value': 210300.0, 'new_value': 212598.0}, {'field': 'total_amount', 'old_value': 292804.0, 'new_value': 300795.0}, {'field': 'order_count', 'old_value': 274, 'new_value': 284}]
2025-04-28 09:00:40,490 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MR9
2025-04-28 09:00:40,974 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MR9
2025-04-28 09:00:40,974 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41778.8, 'new_value': 45238.8}, {'field': 'offline_amount', 'old_value': 458802.0, 'new_value': 478552.0}, {'field': 'total_amount', 'old_value': 500580.8, 'new_value': 523790.8}, {'field': 'order_count', 'old_value': 66, 'new_value': 68}]
2025-04-28 09:00:40,974 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MV9
2025-04-28 09:00:41,552 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MV9
2025-04-28 09:00:41,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 265099.05, 'new_value': 273089.95}, {'field': 'total_amount', 'old_value': 265099.05, 'new_value': 273089.95}, {'field': 'order_count', 'old_value': 1029, 'new_value': 1055}]
2025-04-28 09:00:41,552 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZ9
2025-04-28 09:00:42,005 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZ9
2025-04-28 09:00:42,005 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42856.61, 'new_value': 44057.48}, {'field': 'offline_amount', 'old_value': 36903.0, 'new_value': 37652.0}, {'field': 'total_amount', 'old_value': 79759.61, 'new_value': 81709.48}, {'field': 'order_count', 'old_value': 1062, 'new_value': 1086}]
2025-04-28 09:00:42,005 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0A
2025-04-28 09:00:42,521 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0A
2025-04-28 09:00:42,521 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32757.02, 'new_value': 32778.19}, {'field': 'offline_amount', 'old_value': 81890.65, 'new_value': 84453.58}, {'field': 'total_amount', 'old_value': 114647.67, 'new_value': 117231.77}, {'field': 'order_count', 'old_value': 468, 'new_value': 478}]
2025-04-28 09:00:42,521 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2A
2025-04-28 09:00:42,943 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2A
2025-04-28 09:00:42,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186334.0, 'new_value': 203532.0}, {'field': 'total_amount', 'old_value': 203431.0, 'new_value': 220629.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 44}]
2025-04-28 09:00:42,943 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3A
2025-04-28 09:00:43,380 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3A
2025-04-28 09:00:43,380 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23424.42, 'new_value': 24108.42}, {'field': 'total_amount', 'old_value': 23424.42, 'new_value': 24108.42}, {'field': 'order_count', 'old_value': 243, 'new_value': 250}]
2025-04-28 09:00:43,380 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6A
2025-04-28 09:00:43,802 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6A
2025-04-28 09:00:43,802 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 181243.3, 'new_value': 187629.63}, {'field': 'offline_amount', 'old_value': 147445.58, 'new_value': 153689.08}, {'field': 'total_amount', 'old_value': 328688.88, 'new_value': 341318.71}, {'field': 'order_count', 'old_value': 2851, 'new_value': 2955}]
2025-04-28 09:00:43,802 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAA
2025-04-28 09:00:44,240 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAA
2025-04-28 09:00:44,240 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74018.66, 'new_value': 76169.35}, {'field': 'total_amount', 'old_value': 75600.0, 'new_value': 77750.69}, {'field': 'order_count', 'old_value': 2978, 'new_value': 3066}]
2025-04-28 09:00:44,240 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCA
2025-04-28 09:00:44,661 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCA
2025-04-28 09:00:44,661 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 142404.05, 'new_value': 144550.14}, {'field': 'offline_amount', 'old_value': 29529.05, 'new_value': 31377.17}, {'field': 'total_amount', 'old_value': 171933.1, 'new_value': 175927.31}, {'field': 'order_count', 'old_value': 712, 'new_value': 725}]
2025-04-28 09:00:44,661 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDA
2025-04-28 09:00:45,099 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDA
2025-04-28 09:00:45,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87169.21, 'new_value': 90158.21}, {'field': 'total_amount', 'old_value': 87169.21, 'new_value': 90158.21}, {'field': 'order_count', 'old_value': 357, 'new_value': 411}]
2025-04-28 09:00:45,115 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEA
2025-04-28 09:00:45,693 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEA
2025-04-28 09:00:45,693 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 176981.66, 'new_value': 188163.79}, {'field': 'offline_amount', 'old_value': 360355.01, 'new_value': 367107.52}, {'field': 'total_amount', 'old_value': 537336.67, 'new_value': 555271.31}, {'field': 'order_count', 'old_value': 4031, 'new_value': 4195}]
2025-04-28 09:00:45,693 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGA
2025-04-28 09:00:46,224 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGA
2025-04-28 09:00:46,224 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52575.21, 'new_value': 54252.71}, {'field': 'offline_amount', 'old_value': 327712.2, 'new_value': 336314.8}, {'field': 'total_amount', 'old_value': 380287.41, 'new_value': 390567.51}, {'field': 'order_count', 'old_value': 2588, 'new_value': 2660}]
2025-04-28 09:00:46,224 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHA
2025-04-28 09:00:46,677 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHA
2025-04-28 09:00:46,677 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10066.5, 'new_value': 10997.5}, {'field': 'offline_amount', 'old_value': 14861.7, 'new_value': 14862.7}, {'field': 'total_amount', 'old_value': 24928.2, 'new_value': 25860.2}, {'field': 'order_count', 'old_value': 74, 'new_value': 75}]
2025-04-28 09:00:46,677 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIA
2025-04-28 09:00:47,099 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIA
2025-04-28 09:00:47,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21220.0, 'new_value': 22403.0}, {'field': 'total_amount', 'old_value': 21220.0, 'new_value': 22403.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 66}]
2025-04-28 09:00:47,099 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLA
2025-04-28 09:00:47,583 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLA
2025-04-28 09:00:47,583 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103355.55, 'new_value': 107153.42}, {'field': 'offline_amount', 'old_value': 304332.76, 'new_value': 312173.32}, {'field': 'total_amount', 'old_value': 407688.31, 'new_value': 419326.74}, {'field': 'order_count', 'old_value': 10461, 'new_value': 10780}]
2025-04-28 09:00:47,583 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQA
2025-04-28 09:00:47,990 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQA
2025-04-28 09:00:47,990 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39115.29, 'new_value': 41938.78}, {'field': 'total_amount', 'old_value': 39115.29, 'new_value': 41938.78}, {'field': 'order_count', 'old_value': 170, 'new_value': 182}]
2025-04-28 09:00:47,990 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRA
2025-04-28 09:00:48,443 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRA
2025-04-28 09:00:48,443 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68063.72, 'new_value': 70131.54}, {'field': 'total_amount', 'old_value': 68063.72, 'new_value': 70131.54}, {'field': 'order_count', 'old_value': 1877, 'new_value': 1953}]
2025-04-28 09:00:48,443 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSA
2025-04-28 09:00:48,943 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSA
2025-04-28 09:00:48,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251980.89, 'new_value': 260390.94}, {'field': 'total_amount', 'old_value': 252013.89, 'new_value': 260423.94}, {'field': 'order_count', 'old_value': 1881, 'new_value': 1958}]
2025-04-28 09:00:48,943 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTA
2025-04-28 09:00:49,411 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTA
2025-04-28 09:00:49,411 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 348246.6, 'new_value': 365833.0}, {'field': 'offline_amount', 'old_value': 20046.0, 'new_value': 20098.0}, {'field': 'total_amount', 'old_value': 368292.6, 'new_value': 385931.0}, {'field': 'order_count', 'old_value': 3519, 'new_value': 3663}]
2025-04-28 09:00:49,411 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUA
2025-04-28 09:00:49,911 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUA
2025-04-28 09:00:49,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51736.5, 'new_value': 53155.8}, {'field': 'total_amount', 'old_value': 51736.5, 'new_value': 53155.8}, {'field': 'order_count', 'old_value': 422, 'new_value': 436}]
2025-04-28 09:00:49,911 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVA
2025-04-28 09:00:50,349 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVA
2025-04-28 09:00:50,349 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121565.86, 'new_value': 126289.19}, {'field': 'offline_amount', 'old_value': 275908.95, 'new_value': 281122.35}, {'field': 'total_amount', 'old_value': 397474.81, 'new_value': 407411.54}, {'field': 'order_count', 'old_value': 3694, 'new_value': 3817}]
2025-04-28 09:00:50,349 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWA
2025-04-28 09:00:50,771 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWA
2025-04-28 09:00:50,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169103.42, 'new_value': 174442.32}, {'field': 'total_amount', 'old_value': 169103.42, 'new_value': 174442.32}, {'field': 'order_count', 'old_value': 779, 'new_value': 805}]
2025-04-28 09:00:50,771 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYA
2025-04-28 09:00:51,255 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYA
2025-04-28 09:00:51,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20323.0, 'new_value': 21963.0}, {'field': 'total_amount', 'old_value': 20323.0, 'new_value': 21963.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-04-28 09:00:51,255 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZA
2025-04-28 09:00:51,646 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZA
2025-04-28 09:00:51,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51897.0, 'new_value': 55803.0}, {'field': 'total_amount', 'old_value': 52294.0, 'new_value': 56200.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 94}]
2025-04-28 09:00:51,646 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0B
2025-04-28 09:00:52,146 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0B
2025-04-28 09:00:52,146 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97976.3, 'new_value': 101875.2}, {'field': 'total_amount', 'old_value': 97976.3, 'new_value': 101875.2}, {'field': 'order_count', 'old_value': 506, 'new_value': 525}]
2025-04-28 09:00:52,146 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3B
2025-04-28 09:00:52,583 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3B
2025-04-28 09:00:52,583 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 229776.0, 'new_value': 238272.0}, {'field': 'total_amount', 'old_value': 229776.0, 'new_value': 238272.0}, {'field': 'order_count', 'old_value': 19148, 'new_value': 19856}]
2025-04-28 09:00:52,583 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4B
2025-04-28 09:00:53,115 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4B
2025-04-28 09:00:53,115 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14831.26, 'new_value': 15466.86}, {'field': 'total_amount', 'old_value': 22792.13, 'new_value': 23427.73}, {'field': 'order_count', 'old_value': 85, 'new_value': 88}]
2025-04-28 09:00:53,115 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5B
2025-04-28 09:00:53,521 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5B
2025-04-28 09:00:53,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1529460.48, 'new_value': 1627853.48}, {'field': 'total_amount', 'old_value': 1529460.48, 'new_value': 1627853.48}, {'field': 'order_count', 'old_value': 3275, 'new_value': 3423}]
2025-04-28 09:00:53,521 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6B
2025-04-28 09:00:54,052 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6B
2025-04-28 09:00:54,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40824.37, 'new_value': 42550.58}, {'field': 'offline_amount', 'old_value': 339280.73, 'new_value': 347279.61}, {'field': 'total_amount', 'old_value': 380105.1, 'new_value': 389830.19}, {'field': 'order_count', 'old_value': 3354, 'new_value': 3447}]
2025-04-28 09:00:54,052 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7B
2025-04-28 09:00:54,474 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7B
2025-04-28 09:00:54,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35010.0, 'new_value': 35249.0}, {'field': 'total_amount', 'old_value': 35010.0, 'new_value': 35249.0}, {'field': 'order_count', 'old_value': 118, 'new_value': 119}]
2025-04-28 09:00:54,474 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8B
2025-04-28 09:00:54,880 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8B
2025-04-28 09:00:54,880 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7464.4, 'new_value': 7603.2}, {'field': 'offline_amount', 'old_value': 19251.15, 'new_value': 19770.53}, {'field': 'total_amount', 'old_value': 26715.55, 'new_value': 27373.73}, {'field': 'order_count', 'old_value': 880, 'new_value': 905}]
2025-04-28 09:00:54,880 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAB
2025-04-28 09:00:55,614 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAB
2025-04-28 09:00:55,614 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61116.07, 'new_value': 62587.55}, {'field': 'offline_amount', 'old_value': 81274.43, 'new_value': 83716.82}, {'field': 'total_amount', 'old_value': 142390.5, 'new_value': 146304.37}, {'field': 'order_count', 'old_value': 6734, 'new_value': 6780}]
2025-04-28 09:00:55,614 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCB
2025-04-28 09:00:56,130 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCB
2025-04-28 09:00:56,130 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 460117.1, 'new_value': 480090.1}, {'field': 'total_amount', 'old_value': 569893.1, 'new_value': 589866.1}, {'field': 'order_count', 'old_value': 755, 'new_value': 776}]
2025-04-28 09:00:56,130 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEB
2025-04-28 09:00:56,583 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEB
2025-04-28 09:00:56,583 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111206.1, 'new_value': 114080.6}, {'field': 'total_amount', 'old_value': 127648.2, 'new_value': 130522.7}, {'field': 'order_count', 'old_value': 361, 'new_value': 372}]
2025-04-28 09:00:56,583 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGB
2025-04-28 09:00:57,036 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGB
2025-04-28 09:00:57,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79680.0, 'new_value': 83964.0}, {'field': 'total_amount', 'old_value': 82261.0, 'new_value': 86545.0}, {'field': 'order_count', 'old_value': 330, 'new_value': 347}]
2025-04-28 09:00:57,036 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MKB
2025-04-28 09:00:57,536 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MKB
2025-04-28 09:00:57,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23141.27, 'new_value': 23909.05}, {'field': 'offline_amount', 'old_value': 38066.54, 'new_value': 39235.14}, {'field': 'total_amount', 'old_value': 61207.81, 'new_value': 63144.19}, {'field': 'order_count', 'old_value': 2552, 'new_value': 2638}]
2025-04-28 09:00:57,536 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMB
2025-04-28 09:00:57,943 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMB
2025-04-28 09:00:57,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26835.6, 'new_value': 27951.5}, {'field': 'total_amount', 'old_value': 26835.6, 'new_value': 27951.5}, {'field': 'order_count', 'old_value': 125, 'new_value': 130}]
2025-04-28 09:00:57,943 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSB
2025-04-28 09:00:58,318 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSB
2025-04-28 09:00:58,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7100.0, 'new_value': 7310.0}, {'field': 'offline_amount', 'old_value': 27979.3, 'new_value': 28979.0}, {'field': 'total_amount', 'old_value': 35079.3, 'new_value': 36289.0}, {'field': 'order_count', 'old_value': 1339, 'new_value': 1382}]
2025-04-28 09:00:58,333 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUB
2025-04-28 09:00:58,818 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUB
2025-04-28 09:00:58,818 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 363569.58, 'new_value': 381799.59}, {'field': 'total_amount', 'old_value': 363570.58, 'new_value': 381800.59}, {'field': 'order_count', 'old_value': 615, 'new_value': 631}]
2025-04-28 09:00:58,818 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVB
2025-04-28 09:00:59,271 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVB
2025-04-28 09:00:59,271 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54669.29, 'new_value': 56565.53}, {'field': 'offline_amount', 'old_value': 71365.04, 'new_value': 73626.45}, {'field': 'total_amount', 'old_value': 126034.33, 'new_value': 130191.98}, {'field': 'order_count', 'old_value': 4982, 'new_value': 5151}]
2025-04-28 09:00:59,271 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYB
2025-04-28 09:00:59,739 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYB
2025-04-28 09:00:59,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 253976.97, 'new_value': 255234.16}, {'field': 'offline_amount', 'old_value': 64649.9, 'new_value': 69780.67}, {'field': 'total_amount', 'old_value': 318626.87, 'new_value': 325014.83}, {'field': 'order_count', 'old_value': 2173, 'new_value': 2242}]
2025-04-28 09:00:59,739 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP5
2025-04-28 09:01:00,208 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP5
2025-04-28 09:01:00,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147465.41, 'new_value': 153130.41}, {'field': 'total_amount', 'old_value': 147465.41, 'new_value': 153130.41}, {'field': 'order_count', 'old_value': 5206, 'new_value': 5401}]
2025-04-28 09:01:00,208 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR5
2025-04-28 09:01:00,739 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR5
2025-04-28 09:01:00,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13204.06, 'new_value': 13604.77}, {'field': 'offline_amount', 'old_value': 484541.41, 'new_value': 497706.54}, {'field': 'total_amount', 'old_value': 497745.47, 'new_value': 511311.31}, {'field': 'order_count', 'old_value': 1998, 'new_value': 2070}]
2025-04-28 09:01:00,739 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW5
2025-04-28 09:01:01,161 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW5
2025-04-28 09:01:01,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25980.0, 'new_value': 27178.0}, {'field': 'total_amount', 'old_value': 25980.0, 'new_value': 27178.0}, {'field': 'order_count', 'old_value': 238, 'new_value': 253}]
2025-04-28 09:01:01,161 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M06
2025-04-28 09:01:01,646 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M06
2025-04-28 09:01:01,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27449.55, 'new_value': 28683.79}, {'field': 'offline_amount', 'old_value': 50296.58, 'new_value': 52131.54}, {'field': 'total_amount', 'old_value': 77746.13, 'new_value': 80815.33}, {'field': 'order_count', 'old_value': 2781, 'new_value': 2882}]
2025-04-28 09:01:01,646 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M26
2025-04-28 09:01:02,114 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M26
2025-04-28 09:01:02,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57066.75, 'new_value': 60244.75}, {'field': 'total_amount', 'old_value': 57066.75, 'new_value': 60244.75}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-04-28 09:01:02,114 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M56
2025-04-28 09:01:02,552 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M56
2025-04-28 09:01:02,552 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9000.5, 'new_value': 9199.5}, {'field': 'total_amount', 'old_value': 92742.98, 'new_value': 92941.98}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-04-28 09:01:02,552 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-04-28 09:01:02,974 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-04-28 09:01:02,974 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 623226.02, 'new_value': 643576.02}, {'field': 'total_amount', 'old_value': 623226.02, 'new_value': 643576.02}, {'field': 'order_count', 'old_value': 827, 'new_value': 852}]
2025-04-28 09:01:02,974 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC6
2025-04-28 09:01:03,396 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC6
2025-04-28 09:01:03,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122897.1, 'new_value': 126876.44}, {'field': 'total_amount', 'old_value': 122897.1, 'new_value': 126876.44}, {'field': 'order_count', 'old_value': 3573, 'new_value': 3689}]
2025-04-28 09:01:03,396 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD6
2025-04-28 09:01:03,880 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD6
2025-04-28 09:01:03,880 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53479.64, 'new_value': 55306.95}, {'field': 'offline_amount', 'old_value': 24242.03, 'new_value': 24777.23}, {'field': 'total_amount', 'old_value': 77721.67, 'new_value': 80084.18}, {'field': 'order_count', 'old_value': 3150, 'new_value': 3260}]
2025-04-28 09:01:03,880 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ6
2025-04-28 09:01:04,302 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ6
2025-04-28 09:01:04,302 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8270.66, 'new_value': 9490.52}, {'field': 'offline_amount', 'old_value': 77170.88, 'new_value': 81656.1}, {'field': 'total_amount', 'old_value': 85441.54, 'new_value': 91146.62}, {'field': 'order_count', 'old_value': 2431, 'new_value': 2530}]
2025-04-28 09:01:04,302 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO6
2025-04-28 09:01:04,864 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO6
2025-04-28 09:01:04,864 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7050.3, 'new_value': 7595.2}, {'field': 'offline_amount', 'old_value': 60488.1, 'new_value': 63286.1}, {'field': 'total_amount', 'old_value': 67538.4, 'new_value': 70881.3}, {'field': 'order_count', 'old_value': 59, 'new_value': 62}]
2025-04-28 09:01:04,864 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP6
2025-04-28 09:01:05,349 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP6
2025-04-28 09:01:05,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37457.0, 'new_value': 40179.0}, {'field': 'total_amount', 'old_value': 37457.0, 'new_value': 40179.0}, {'field': 'order_count', 'old_value': 101, 'new_value': 107}]
2025-04-28 09:01:05,349 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ6
2025-04-28 09:01:05,771 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ6
2025-04-28 09:01:05,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149979.8, 'new_value': 155565.78}, {'field': 'total_amount', 'old_value': 149979.8, 'new_value': 155565.78}, {'field': 'order_count', 'old_value': 5270, 'new_value': 5507}]
2025-04-28 09:01:05,771 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M17
2025-04-28 09:01:06,239 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M17
2025-04-28 09:01:06,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28850.6, 'new_value': 32301.6}, {'field': 'total_amount', 'old_value': 28850.6, 'new_value': 32301.6}, {'field': 'order_count', 'old_value': 306, 'new_value': 324}]
2025-04-28 09:01:06,239 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M27
2025-04-28 09:01:06,708 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M27
2025-04-28 09:01:06,708 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89603.63, 'new_value': 93077.24}, {'field': 'offline_amount', 'old_value': 63054.4, 'new_value': 64596.8}, {'field': 'total_amount', 'old_value': 152658.03, 'new_value': 157674.04}, {'field': 'order_count', 'old_value': 8226, 'new_value': 8526}]
2025-04-28 09:01:06,708 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M37
2025-04-28 09:01:07,177 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M37
2025-04-28 09:01:07,177 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40616.18, 'new_value': 41190.69}, {'field': 'offline_amount', 'old_value': 241403.36, 'new_value': 247473.42}, {'field': 'total_amount', 'old_value': 282019.54, 'new_value': 288664.11}, {'field': 'order_count', 'old_value': 6871, 'new_value': 7068}]
2025-04-28 09:01:07,177 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M47
2025-04-28 09:01:07,677 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M47
2025-04-28 09:01:07,677 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9235.89, 'new_value': 9416.85}, {'field': 'offline_amount', 'old_value': 14088.95, 'new_value': 14534.67}, {'field': 'total_amount', 'old_value': 23324.84, 'new_value': 23951.52}, {'field': 'order_count', 'old_value': 1564, 'new_value': 1598}]
2025-04-28 09:01:07,677 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M57
2025-04-28 09:01:08,114 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M57
2025-04-28 09:01:08,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 276056.75, 'new_value': 286291.22}, {'field': 'total_amount', 'old_value': 291815.55, 'new_value': 302050.02}, {'field': 'order_count', 'old_value': 12248, 'new_value': 12674}]
2025-04-28 09:01:08,114 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M97
2025-04-28 09:01:08,521 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M97
2025-04-28 09:01:08,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52247.45, 'new_value': 53682.15}, {'field': 'total_amount', 'old_value': 52247.45, 'new_value': 53682.15}, {'field': 'order_count', 'old_value': 2399, 'new_value': 2475}]
2025-04-28 09:01:08,521 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA7
2025-04-28 09:01:08,989 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA7
2025-04-28 09:01:08,989 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18832.27, 'new_value': 19031.27}, {'field': 'total_amount', 'old_value': 21622.27, 'new_value': 21821.27}, {'field': 'order_count', 'old_value': 41, 'new_value': 42}]
2025-04-28 09:01:08,989 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD7
2025-04-28 09:01:09,396 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD7
2025-04-28 09:01:09,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45831.6, 'new_value': 48991.6}, {'field': 'total_amount', 'old_value': 72171.4, 'new_value': 75331.4}, {'field': 'order_count', 'old_value': 95, 'new_value': 96}]
2025-04-28 09:01:09,396 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG7
2025-04-28 09:01:09,833 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG7
2025-04-28 09:01:09,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39819.07, 'new_value': 42230.3}, {'field': 'total_amount', 'old_value': 40009.07, 'new_value': 42420.3}, {'field': 'order_count', 'old_value': 400, 'new_value': 415}]
2025-04-28 09:01:09,833 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH7
2025-04-28 09:01:10,255 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH7
2025-04-28 09:01:10,255 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 124701.0, 'new_value': 128753.0}, {'field': 'offline_amount', 'old_value': 108266.0, 'new_value': 111060.0}, {'field': 'total_amount', 'old_value': 232967.0, 'new_value': 239813.0}, {'field': 'order_count', 'old_value': 9222, 'new_value': 9305}]
2025-04-28 09:01:10,255 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ7
2025-04-28 09:01:10,708 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ7
2025-04-28 09:01:10,708 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 282028.72, 'new_value': 290119.71}, {'field': 'offline_amount', 'old_value': 15616.66, 'new_value': 16016.06}, {'field': 'total_amount', 'old_value': 297645.38, 'new_value': 306135.77}, {'field': 'order_count', 'old_value': 10667, 'new_value': 10934}]
2025-04-28 09:01:10,708 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK7
2025-04-28 09:01:11,114 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK7
2025-04-28 09:01:11,114 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9180.6, 'new_value': 9492.6}, {'field': 'offline_amount', 'old_value': 8711.6, 'new_value': 8857.6}, {'field': 'total_amount', 'old_value': 17892.2, 'new_value': 18350.2}, {'field': 'order_count', 'old_value': 154, 'new_value': 160}]
2025-04-28 09:01:11,114 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML7
2025-04-28 09:01:11,536 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML7
2025-04-28 09:01:11,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191570.0, 'new_value': 199995.0}, {'field': 'total_amount', 'old_value': 191570.0, 'new_value': 199995.0}, {'field': 'order_count', 'old_value': 212, 'new_value': 230}]
2025-04-28 09:01:11,536 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO7
2025-04-28 09:01:11,989 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO7
2025-04-28 09:01:11,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 556623.54, 'new_value': 574857.36}, {'field': 'total_amount', 'old_value': 556623.54, 'new_value': 574857.36}, {'field': 'order_count', 'old_value': 11061, 'new_value': 11344}]
2025-04-28 09:01:11,989 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ7
2025-04-28 09:01:12,349 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ7
2025-04-28 09:01:12,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 289212.6, 'new_value': 297988.2}, {'field': 'total_amount', 'old_value': 289212.6, 'new_value': 297988.2}, {'field': 'order_count', 'old_value': 8342, 'new_value': 8606}]
2025-04-28 09:01:12,364 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU7
2025-04-28 09:01:12,786 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU7
2025-04-28 09:01:12,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104540.77, 'new_value': 110472.17}, {'field': 'total_amount', 'old_value': 230641.97, 'new_value': 236573.37}, {'field': 'order_count', 'old_value': 6343, 'new_value': 6513}]
2025-04-28 09:01:12,786 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY7
2025-04-28 09:01:13,255 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY7
2025-04-28 09:01:13,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48051.8, 'new_value': 49088.8}, {'field': 'total_amount', 'old_value': 48051.8, 'new_value': 49088.8}, {'field': 'order_count', 'old_value': 64, 'new_value': 66}]
2025-04-28 09:01:13,255 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ7
2025-04-28 09:01:13,693 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ7
2025-04-28 09:01:13,693 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46153.36, 'new_value': 46935.87}, {'field': 'offline_amount', 'old_value': 53935.9, 'new_value': 56252.19}, {'field': 'total_amount', 'old_value': 100089.26, 'new_value': 103188.06}, {'field': 'order_count', 'old_value': 3665, 'new_value': 3726}]
2025-04-28 09:01:13,693 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M08
2025-04-28 09:01:14,114 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M08
2025-04-28 09:01:14,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76885.57, 'new_value': 79892.73}, {'field': 'total_amount', 'old_value': 76885.57, 'new_value': 79892.73}, {'field': 'order_count', 'old_value': 2261, 'new_value': 2342}]
2025-04-28 09:01:14,114 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M18
2025-04-28 09:01:14,646 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M18
2025-04-28 09:01:14,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79717.66, 'new_value': 82916.06}, {'field': 'offline_amount', 'old_value': 116579.99, 'new_value': 120917.48}, {'field': 'total_amount', 'old_value': 196297.65, 'new_value': 203833.54}, {'field': 'order_count', 'old_value': 7902, 'new_value': 8194}]
2025-04-28 09:01:14,646 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M28
2025-04-28 09:01:15,099 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M28
2025-04-28 09:01:15,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107129.0, 'new_value': 109692.0}, {'field': 'total_amount', 'old_value': 107876.0, 'new_value': 110439.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 51}]
2025-04-28 09:01:15,099 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M78
2025-04-28 09:01:15,552 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M78
2025-04-28 09:01:15,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 205156.09, 'new_value': 211258.1}, {'field': 'total_amount', 'old_value': 205156.09, 'new_value': 211258.1}, {'field': 'order_count', 'old_value': 4315, 'new_value': 4425}]
2025-04-28 09:01:15,552 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M88
2025-04-28 09:01:16,068 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M88
2025-04-28 09:01:16,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 381534.48, 'new_value': 395222.79}, {'field': 'total_amount', 'old_value': 419860.26, 'new_value': 433548.57}, {'field': 'order_count', 'old_value': 1730, 'new_value': 1785}]
2025-04-28 09:01:16,068 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA8
2025-04-28 09:01:16,521 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA8
2025-04-28 09:01:16,521 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74858.35, 'new_value': 76632.44}, {'field': 'offline_amount', 'old_value': 42031.0, 'new_value': 43289.0}, {'field': 'total_amount', 'old_value': 116889.35, 'new_value': 119921.44}, {'field': 'order_count', 'old_value': 5911, 'new_value': 6117}]
2025-04-28 09:01:16,521 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8H
2025-04-28 09:01:17,021 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8H
2025-04-28 09:01:17,021 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 487188.1, 'new_value': 499242.93}, {'field': 'total_amount', 'old_value': 506251.6, 'new_value': 518306.43}, {'field': 'order_count', 'old_value': 1974, 'new_value': 2020}]
2025-04-28 09:01:17,021 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9H
2025-04-28 09:01:17,442 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9H
2025-04-28 09:01:17,442 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65094.0, 'new_value': 72968.0}, {'field': 'total_amount', 'old_value': 71513.0, 'new_value': 79387.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 50}]
2025-04-28 09:01:17,442 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAH
2025-04-28 09:01:17,880 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAH
2025-04-28 09:01:17,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 348515.78, 'new_value': 359172.13}, {'field': 'total_amount', 'old_value': 348515.78, 'new_value': 359172.13}, {'field': 'order_count', 'old_value': 15119, 'new_value': 15591}]
2025-04-28 09:01:17,880 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDH
2025-04-28 09:01:18,333 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDH
2025-04-28 09:01:18,333 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7840.57, 'new_value': 8588.74}, {'field': 'offline_amount', 'old_value': 34626.98, 'new_value': 35417.08}, {'field': 'total_amount', 'old_value': 42467.55, 'new_value': 44005.82}, {'field': 'order_count', 'old_value': 1896, 'new_value': 1970}]
2025-04-28 09:01:18,333 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMH
2025-04-28 09:01:18,755 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMH
2025-04-28 09:01:18,755 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190151.0, 'new_value': 196997.0}, {'field': 'total_amount', 'old_value': 190151.0, 'new_value': 196997.0}, {'field': 'order_count', 'old_value': 587, 'new_value': 609}]
2025-04-28 09:01:18,755 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNH
2025-04-28 09:01:19,208 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNH
2025-04-28 09:01:19,208 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30541.15, 'new_value': 31298.83}, {'field': 'offline_amount', 'old_value': 192391.97, 'new_value': 197860.97}, {'field': 'total_amount', 'old_value': 222933.12, 'new_value': 229159.8}, {'field': 'order_count', 'old_value': 7565, 'new_value': 7679}]
2025-04-28 09:01:19,208 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOH
2025-04-28 09:01:19,614 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOH
2025-04-28 09:01:19,614 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58073.0, 'new_value': 58766.0}, {'field': 'total_amount', 'old_value': 58073.0, 'new_value': 58766.0}, {'field': 'order_count', 'old_value': 3871, 'new_value': 3891}]
2025-04-28 09:01:19,614 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQH
2025-04-28 09:01:20,052 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQH
2025-04-28 09:01:20,052 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 320653.0, 'new_value': 338207.0}, {'field': 'total_amount', 'old_value': 320653.0, 'new_value': 338207.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 132}]
2025-04-28 09:01:20,052 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRH
2025-04-28 09:01:20,536 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRH
2025-04-28 09:01:20,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 302.0}, {'field': 'total_amount', 'old_value': 16426.6, 'new_value': 16728.6}, {'field': 'order_count', 'old_value': 165, 'new_value': 167}]
2025-04-28 09:01:20,536 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTH
2025-04-28 09:01:21,052 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTH
2025-04-28 09:01:21,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48672.27, 'new_value': 50743.73}, {'field': 'offline_amount', 'old_value': 432459.65, 'new_value': 442354.91}, {'field': 'total_amount', 'old_value': 481131.92, 'new_value': 493098.64}, {'field': 'order_count', 'old_value': 2172, 'new_value': 2220}]
2025-04-28 09:01:21,052 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZH
2025-04-28 09:01:21,505 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZH
2025-04-28 09:01:21,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24849.0, 'new_value': 26409.0}, {'field': 'total_amount', 'old_value': 26052.8, 'new_value': 27612.8}, {'field': 'order_count', 'old_value': 267, 'new_value': 282}]
2025-04-28 09:01:21,505 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0I
2025-04-28 09:01:22,036 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0I
2025-04-28 09:01:22,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84171.98, 'new_value': 94001.98}, {'field': 'total_amount', 'old_value': 86469.98, 'new_value': 96299.98}, {'field': 'order_count', 'old_value': 40, 'new_value': 45}]
2025-04-28 09:01:22,036 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2I
2025-04-28 09:01:22,505 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2I
2025-04-28 09:01:22,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244682.11, 'new_value': 248720.11}, {'field': 'total_amount', 'old_value': 249448.11, 'new_value': 253486.11}, {'field': 'order_count', 'old_value': 3552, 'new_value': 3606}]
2025-04-28 09:01:22,505 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5I
2025-04-28 09:01:22,974 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5I
2025-04-28 09:01:22,974 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29875.0, 'new_value': 31303.0}, {'field': 'total_amount', 'old_value': 29875.0, 'new_value': 31303.0}, {'field': 'order_count', 'old_value': 187, 'new_value': 195}]
2025-04-28 09:01:22,974 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9I
2025-04-28 09:01:23,442 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9I
2025-04-28 09:01:23,442 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 303000.0, 'new_value': 339000.0}, {'field': 'total_amount', 'old_value': 303000.0, 'new_value': 339000.0}]
2025-04-28 09:01:23,442 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAI
2025-04-28 09:01:23,989 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAI
2025-04-28 09:01:23,989 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93733.0, 'new_value': 96450.36}, {'field': 'offline_amount', 'old_value': 192122.41, 'new_value': 197922.51}, {'field': 'total_amount', 'old_value': 285855.41, 'new_value': 294372.87}, {'field': 'order_count', 'old_value': 8786, 'new_value': 9054}]
2025-04-28 09:01:23,989 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDI
2025-04-28 09:01:24,489 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDI
2025-04-28 09:01:24,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119908.0, 'new_value': 124022.0}, {'field': 'total_amount', 'old_value': 119908.0, 'new_value': 124022.0}, {'field': 'order_count', 'old_value': 536, 'new_value': 556}]
2025-04-28 09:01:24,489 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFI
2025-04-28 09:01:24,927 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFI
2025-04-28 09:01:24,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 566648.0, 'new_value': 580260.0}, {'field': 'total_amount', 'old_value': 577686.0, 'new_value': 591298.0}, {'field': 'order_count', 'old_value': 497, 'new_value': 509}]
2025-04-28 09:01:24,927 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGI
2025-04-28 09:01:25,458 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGI
2025-04-28 09:01:25,458 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47508.6, 'new_value': 49058.08}, {'field': 'offline_amount', 'old_value': 179631.72, 'new_value': 183916.17}, {'field': 'total_amount', 'old_value': 227140.32, 'new_value': 232974.25}, {'field': 'order_count', 'old_value': 4415, 'new_value': 4560}]
2025-04-28 09:01:25,458 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJI
2025-04-28 09:01:25,864 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJI
2025-04-28 09:01:25,864 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227298.77, 'new_value': 234933.92}, {'field': 'total_amount', 'old_value': 227298.77, 'new_value': 234933.92}, {'field': 'order_count', 'old_value': 1740, 'new_value': 1809}]
2025-04-28 09:01:25,864 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNI
2025-04-28 09:01:26,364 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNI
2025-04-28 09:01:26,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27829.0, 'new_value': 28579.0}, {'field': 'total_amount', 'old_value': 27829.0, 'new_value': 28579.0}, {'field': 'order_count', 'old_value': 136, 'new_value': 141}]
2025-04-28 09:01:26,364 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOI
2025-04-28 09:01:26,802 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOI
2025-04-28 09:01:26,802 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 250325.7, 'new_value': 257025.5}, {'field': 'total_amount', 'old_value': 250325.7, 'new_value': 257025.5}, {'field': 'order_count', 'old_value': 5289, 'new_value': 5491}]
2025-04-28 09:01:26,802 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVI
2025-04-28 09:01:27,271 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVI
2025-04-28 09:01:27,271 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63600.0, 'new_value': 65720.0}, {'field': 'total_amount', 'old_value': 63610.0, 'new_value': 65730.0}, {'field': 'order_count', 'old_value': 240, 'new_value': 248}]
2025-04-28 09:01:27,271 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXI
2025-04-28 09:01:27,692 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXI
2025-04-28 09:01:27,692 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42038.38, 'new_value': 43273.08}, {'field': 'offline_amount', 'old_value': 1055052.21, 'new_value': 1080869.88}, {'field': 'total_amount', 'old_value': 1097090.59, 'new_value': 1124142.96}, {'field': 'order_count', 'old_value': 5372, 'new_value': 5502}]
2025-04-28 09:01:27,692 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3J
2025-04-28 09:01:28,130 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3J
2025-04-28 09:01:28,130 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11740.0, 'new_value': 12699.0}, {'field': 'total_amount', 'old_value': 11740.0, 'new_value': 12699.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 31}]
2025-04-28 09:01:28,130 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-04-28 09:01:28,567 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-04-28 09:01:28,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28792.0, 'new_value': 29032.0}, {'field': 'total_amount', 'old_value': 28792.0, 'new_value': 29032.0}, {'field': 'order_count', 'old_value': 147, 'new_value': 148}]
2025-04-28 09:01:28,567 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6J
2025-04-28 09:01:28,974 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6J
2025-04-28 09:01:28,974 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86635.4, 'new_value': 88187.2}, {'field': 'offline_amount', 'old_value': 158140.9, 'new_value': 162616.1}, {'field': 'total_amount', 'old_value': 244776.3, 'new_value': 250803.3}, {'field': 'order_count', 'old_value': 4390, 'new_value': 4510}]
2025-04-28 09:01:28,974 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9J
2025-04-28 09:01:29,489 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9J
2025-04-28 09:01:29,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45556.64, 'new_value': 47519.02}, {'field': 'total_amount', 'old_value': 45556.64, 'new_value': 47519.02}, {'field': 'order_count', 'old_value': 734, 'new_value': 756}]
2025-04-28 09:01:29,489 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAJ
2025-04-28 09:01:29,958 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAJ
2025-04-28 09:01:29,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166213.56, 'new_value': 170352.48}, {'field': 'total_amount', 'old_value': 166213.56, 'new_value': 170352.48}, {'field': 'order_count', 'old_value': 7254, 'new_value': 7469}]
2025-04-28 09:01:29,958 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBJ
2025-04-28 09:01:30,396 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBJ
2025-04-28 09:01:30,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77332.4, 'new_value': 80216.45}, {'field': 'total_amount', 'old_value': 77332.4, 'new_value': 80216.45}, {'field': 'order_count', 'old_value': 275, 'new_value': 286}]
2025-04-28 09:01:30,396 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCJ
2025-04-28 09:01:30,927 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCJ
2025-04-28 09:01:30,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82098.0, 'new_value': 82297.0}, {'field': 'offline_amount', 'old_value': 28283.0, 'new_value': 31697.0}, {'field': 'total_amount', 'old_value': 110381.0, 'new_value': 113994.0}, {'field': 'order_count', 'old_value': 145, 'new_value': 150}]
2025-04-28 09:01:30,927 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEJ
2025-04-28 09:01:31,349 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEJ
2025-04-28 09:01:31,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1004965.0, 'new_value': 1035423.0}, {'field': 'total_amount', 'old_value': 1004965.0, 'new_value': 1035423.0}, {'field': 'order_count', 'old_value': 4259, 'new_value': 4400}]
2025-04-28 09:01:31,349 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJJ
2025-04-28 09:01:31,911 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJJ
2025-04-28 09:01:31,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86739.6, 'new_value': 89900.6}, {'field': 'total_amount', 'old_value': 86739.6, 'new_value': 89900.6}, {'field': 'order_count', 'old_value': 287, 'new_value': 295}]
2025-04-28 09:01:31,911 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKJ
2025-04-28 09:01:32,333 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKJ
2025-04-28 09:01:32,333 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10590.92, 'new_value': 10763.22}, {'field': 'offline_amount', 'old_value': 396664.13, 'new_value': 406654.13}, {'field': 'total_amount', 'old_value': 407255.05, 'new_value': 417417.35}, {'field': 'order_count', 'old_value': 17065, 'new_value': 17554}]
2025-04-28 09:01:32,333 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMJ
2025-04-28 09:01:32,771 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMJ
2025-04-28 09:01:32,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 229487.38, 'new_value': 243062.38}, {'field': 'total_amount', 'old_value': 229487.38, 'new_value': 243062.38}, {'field': 'order_count', 'old_value': 47, 'new_value': 51}]
2025-04-28 09:01:32,771 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNJ
2025-04-28 09:01:33,239 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNJ
2025-04-28 09:01:33,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101470.0, 'new_value': 106508.0}, {'field': 'total_amount', 'old_value': 101470.0, 'new_value': 106508.0}, {'field': 'order_count', 'old_value': 274, 'new_value': 287}]
2025-04-28 09:01:33,239 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQJ
2025-04-28 09:01:33,833 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQJ
2025-04-28 09:01:33,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140752.0, 'new_value': 143552.0}, {'field': 'total_amount', 'old_value': 140752.0, 'new_value': 143552.0}, {'field': 'order_count', 'old_value': 2464, 'new_value': 2507}]
2025-04-28 09:01:33,833 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVJ
2025-04-28 09:01:34,411 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVJ
2025-04-28 09:01:34,411 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2872.01, 'new_value': 2958.41}, {'field': 'offline_amount', 'old_value': 14666.07, 'new_value': 15099.56}, {'field': 'total_amount', 'old_value': 17538.08, 'new_value': 18057.97}, {'field': 'order_count', 'old_value': 633, 'new_value': 650}]
2025-04-28 09:01:34,411 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWJ
2025-04-28 09:01:34,864 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWJ
2025-04-28 09:01:34,864 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74778.8, 'new_value': 75215.4}, {'field': 'total_amount', 'old_value': 74778.8, 'new_value': 75215.4}, {'field': 'order_count', 'old_value': 2176, 'new_value': 2190}]
2025-04-28 09:01:34,864 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M2
2025-04-28 09:01:35,317 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M2
2025-04-28 09:01:35,317 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 268653.34, 'new_value': 273560.38}, {'field': 'total_amount', 'old_value': 268653.34, 'new_value': 273560.38}, {'field': 'order_count', 'old_value': 1906, 'new_value': 1957}]
2025-04-28 09:01:35,317 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M4
2025-04-28 09:01:35,817 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M4
2025-04-28 09:01:35,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98001.24, 'new_value': 101458.71}, {'field': 'total_amount', 'old_value': 137023.08, 'new_value': 140480.55}, {'field': 'order_count', 'old_value': 3949, 'new_value': 4036}]
2025-04-28 09:01:35,817 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M6
2025-04-28 09:01:36,239 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M6
2025-04-28 09:01:36,239 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106628.1, 'new_value': 109942.1}, {'field': 'total_amount', 'old_value': 106628.1, 'new_value': 109942.1}, {'field': 'order_count', 'old_value': 516, 'new_value': 538}]
2025-04-28 09:01:36,239 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M7
2025-04-28 09:01:36,630 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M7
2025-04-28 09:01:36,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179545.06, 'new_value': 192060.58}, {'field': 'total_amount', 'old_value': 179545.06, 'new_value': 192060.58}, {'field': 'order_count', 'old_value': 737, 'new_value': 778}]
2025-04-28 09:01:36,630 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M9
2025-04-28 09:01:37,067 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M9
2025-04-28 09:01:37,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178579.7, 'new_value': 183514.1}, {'field': 'total_amount', 'old_value': 178579.7, 'new_value': 183514.1}, {'field': 'order_count', 'old_value': 854, 'new_value': 878}]
2025-04-28 09:01:37,067 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB
2025-04-28 09:01:37,505 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB
2025-04-28 09:01:37,505 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15763.86, 'new_value': 16972.87}, {'field': 'offline_amount', 'old_value': 306548.3, 'new_value': 321328.25}, {'field': 'total_amount', 'old_value': 322312.16, 'new_value': 338301.12}, {'field': 'order_count', 'old_value': 16470, 'new_value': 17138}]
2025-04-28 09:01:37,505 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC
2025-04-28 09:01:37,958 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC
2025-04-28 09:01:37,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165247.0, 'new_value': 170191.0}, {'field': 'total_amount', 'old_value': 165247.0, 'new_value': 170191.0}, {'field': 'order_count', 'old_value': 11811, 'new_value': 12168}]
2025-04-28 09:01:37,958 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME
2025-04-28 09:01:38,458 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME
2025-04-28 09:01:38,458 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47268.29, 'new_value': 48502.83}, {'field': 'offline_amount', 'old_value': 45706.8, 'new_value': 46993.61}, {'field': 'total_amount', 'old_value': 92975.09, 'new_value': 95496.44}, {'field': 'order_count', 'old_value': 4902, 'new_value': 5043}]
2025-04-28 09:01:38,458 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH
2025-04-28 09:01:38,942 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH
2025-04-28 09:01:38,942 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11385.22, 'new_value': 11643.24}, {'field': 'offline_amount', 'old_value': 141543.0, 'new_value': 146803.0}, {'field': 'total_amount', 'old_value': 152928.22, 'new_value': 158446.24}, {'field': 'order_count', 'old_value': 81, 'new_value': 84}]
2025-04-28 09:01:38,942 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML
2025-04-28 09:01:39,411 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML
2025-04-28 09:01:39,411 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127723.0, 'new_value': 137724.0}, {'field': 'total_amount', 'old_value': 152894.0, 'new_value': 162895.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 59}]
2025-04-28 09:01:39,411 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO
2025-04-28 09:01:39,927 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO
2025-04-28 09:01:39,942 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48135.0, 'new_value': 50676.0}, {'field': 'offline_amount', 'old_value': 44007.98, 'new_value': 45523.98}, {'field': 'total_amount', 'old_value': 92142.98, 'new_value': 96199.98}, {'field': 'order_count', 'old_value': 319, 'new_value': 337}]
2025-04-28 09:01:39,942 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP
2025-04-28 09:01:40,380 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP
2025-04-28 09:01:40,380 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84182.95, 'new_value': 87255.64}, {'field': 'total_amount', 'old_value': 86414.36, 'new_value': 89487.05}, {'field': 'order_count', 'old_value': 457, 'new_value': 471}]
2025-04-28 09:01:40,380 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS
2025-04-28 09:01:40,786 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS
2025-04-28 09:01:40,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109458.0, 'new_value': 113767.0}, {'field': 'total_amount', 'old_value': 111350.0, 'new_value': 115659.0}, {'field': 'order_count', 'old_value': 538, 'new_value': 556}]
2025-04-28 09:01:40,786 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU
2025-04-28 09:01:41,255 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU
2025-04-28 09:01:41,255 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3690.0, 'new_value': 3748.0}, {'field': 'offline_amount', 'old_value': 20202.8, 'new_value': 20827.8}, {'field': 'total_amount', 'old_value': 23892.8, 'new_value': 24575.8}, {'field': 'order_count', 'old_value': 876, 'new_value': 899}]
2025-04-28 09:01:41,255 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY
2025-04-28 09:01:41,708 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY
2025-04-28 09:01:41,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238245.0, 'new_value': 253941.0}, {'field': 'total_amount', 'old_value': 238246.0, 'new_value': 253942.0}, {'field': 'order_count', 'old_value': 389, 'new_value': 408}]
2025-04-28 09:01:41,708 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ
2025-04-28 09:01:42,145 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ
2025-04-28 09:01:42,145 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38296.91, 'new_value': 40114.21}, {'field': 'offline_amount', 'old_value': 95647.0, 'new_value': 98436.0}, {'field': 'total_amount', 'old_value': 133943.91, 'new_value': 138550.21}, {'field': 'order_count', 'old_value': 1647, 'new_value': 1713}]
2025-04-28 09:01:42,145 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M01
2025-04-28 09:01:42,599 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M01
2025-04-28 09:01:42,599 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68429.31, 'new_value': 69352.33}, {'field': 'total_amount', 'old_value': 70260.69, 'new_value': 71183.71}, {'field': 'order_count', 'old_value': 620, 'new_value': 634}]
2025-04-28 09:01:42,599 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M11
2025-04-28 09:01:43,099 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M11
2025-04-28 09:01:43,099 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11845.0, 'new_value': 11888.0}, {'field': 'offline_amount', 'old_value': 78554.0, 'new_value': 81404.0}, {'field': 'total_amount', 'old_value': 90399.0, 'new_value': 93292.0}, {'field': 'order_count', 'old_value': 671, 'new_value': 696}]
2025-04-28 09:01:43,099 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M21
2025-04-28 09:01:43,474 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M21
2025-04-28 09:01:43,474 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74015.0, 'new_value': 75827.0}, {'field': 'offline_amount', 'old_value': 315821.0, 'new_value': 329542.0}, {'field': 'total_amount', 'old_value': 389836.0, 'new_value': 405369.0}, {'field': 'order_count', 'old_value': 1547, 'new_value': 1617}]
2025-04-28 09:01:43,474 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M51
2025-04-28 09:01:43,895 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M51
2025-04-28 09:01:43,895 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 242042.7, 'new_value': 247940.2}, {'field': 'total_amount', 'old_value': 242042.7, 'new_value': 247940.2}, {'field': 'order_count', 'old_value': 1089, 'new_value': 1121}]
2025-04-28 09:01:43,895 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M61
2025-04-28 09:01:44,333 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M61
2025-04-28 09:01:44,333 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 144096.0, 'new_value': 147146.0}, {'field': 'offline_amount', 'old_value': 74861.0, 'new_value': 75528.0}, {'field': 'total_amount', 'old_value': 218957.0, 'new_value': 222674.0}, {'field': 'order_count', 'old_value': 502, 'new_value': 512}]
2025-04-28 09:01:44,333 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M91
2025-04-28 09:01:44,724 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M91
2025-04-28 09:01:44,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 158578.0, 'new_value': 163824.0}, {'field': 'offline_amount', 'old_value': 233429.0, 'new_value': 234769.0}, {'field': 'total_amount', 'old_value': 392007.0, 'new_value': 398593.0}, {'field': 'order_count', 'old_value': 884, 'new_value': 925}]
2025-04-28 09:01:44,739 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA1
2025-04-28 09:01:45,161 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA1
2025-04-28 09:01:45,161 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 208045.93, 'new_value': 215437.9}, {'field': 'offline_amount', 'old_value': 43607.52, 'new_value': 44631.68}, {'field': 'total_amount', 'old_value': 251653.45, 'new_value': 260069.58}, {'field': 'order_count', 'old_value': 13394, 'new_value': 13883}]
2025-04-28 09:01:45,161 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC1
2025-04-28 09:01:45,583 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC1
2025-04-28 09:01:45,583 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11815.0, 'new_value': 12171.0}, {'field': 'offline_amount', 'old_value': 143850.0, 'new_value': 156660.0}, {'field': 'total_amount', 'old_value': 155665.0, 'new_value': 168831.0}, {'field': 'order_count', 'old_value': 265, 'new_value': 288}]
2025-04-28 09:01:45,583 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG1
2025-04-28 09:01:46,083 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG1
2025-04-28 09:01:46,083 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3420.0, 'new_value': 3500.0}, {'field': 'offline_amount', 'old_value': 42410.0, 'new_value': 43300.0}, {'field': 'total_amount', 'old_value': 45830.0, 'new_value': 46800.0}, {'field': 'order_count', 'old_value': 624, 'new_value': 637}]
2025-04-28 09:01:46,083 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML1
2025-04-28 09:01:46,536 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML1
2025-04-28 09:01:46,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24371.9, 'new_value': 24888.9}, {'field': 'total_amount', 'old_value': 24371.9, 'new_value': 24888.9}, {'field': 'order_count', 'old_value': 126, 'new_value': 130}]
2025-04-28 09:01:46,536 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP1
2025-04-28 09:01:46,942 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP1
2025-04-28 09:01:46,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 343954.0, 'new_value': 354463.0}, {'field': 'total_amount', 'old_value': 354744.0, 'new_value': 365253.0}, {'field': 'order_count', 'old_value': 282, 'new_value': 292}]
2025-04-28 09:01:46,942 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV1
2025-04-28 09:01:47,395 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV1
2025-04-28 09:01:47,395 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12962.62, 'new_value': 13163.3}, {'field': 'offline_amount', 'old_value': 31195.0, 'new_value': 31711.0}, {'field': 'total_amount', 'old_value': 44157.62, 'new_value': 44874.3}, {'field': 'order_count', 'old_value': 266, 'new_value': 274}]
2025-04-28 09:01:47,395 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX1
2025-04-28 09:01:47,817 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX1
2025-04-28 09:01:47,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 857851.0, 'new_value': 1581451.0}, {'field': 'total_amount', 'old_value': 4463791.0, 'new_value': 5187391.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-04-28 09:01:47,817 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY1
2025-04-28 09:01:48,239 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY1
2025-04-28 09:01:48,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4200.0, 'new_value': 7488.0}, {'field': 'total_amount', 'old_value': 12100.0, 'new_value': 15388.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-04-28 09:01:48,239 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M02
2025-04-28 09:01:48,692 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M02
2025-04-28 09:01:48,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121335.94, 'new_value': 124992.78}, {'field': 'total_amount', 'old_value': 121335.94, 'new_value': 124992.78}, {'field': 'order_count', 'old_value': 8608, 'new_value': 8886}]
2025-04-28 09:01:48,692 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M12
2025-04-28 09:01:49,224 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M12
2025-04-28 09:01:49,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 615015.35, 'new_value': 659196.13}, {'field': 'total_amount', 'old_value': 615015.35, 'new_value': 659196.13}, {'field': 'order_count', 'old_value': 3555, 'new_value': 3704}]
2025-04-28 09:01:49,224 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M62
2025-04-28 09:01:49,661 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M62
2025-04-28 09:01:49,661 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105218.92, 'new_value': 111110.5}, {'field': 'total_amount', 'old_value': 176798.43, 'new_value': 182690.01}, {'field': 'order_count', 'old_value': 11558, 'new_value': 11967}]
2025-04-28 09:01:49,661 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M82
2025-04-28 09:01:50,114 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M82
2025-04-28 09:01:50,114 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53555.4, 'new_value': 56748.6}, {'field': 'total_amount', 'old_value': 89524.42, 'new_value': 92717.62}, {'field': 'order_count', 'old_value': 6150, 'new_value': 6349}]
2025-04-28 09:01:50,114 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MB2
2025-04-28 09:01:50,552 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MB2
2025-04-28 09:01:50,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 337493.0, 'new_value': 348467.0}, {'field': 'total_amount', 'old_value': 337493.0, 'new_value': 348467.0}, {'field': 'order_count', 'old_value': 7491, 'new_value': 7749}]
2025-04-28 09:01:50,552 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MD2
2025-04-28 09:01:51,036 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MD2
2025-04-28 09:01:51,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25831.7, 'new_value': 26977.6}, {'field': 'offline_amount', 'old_value': 72666.1, 'new_value': 75059.5}, {'field': 'total_amount', 'old_value': 98497.8, 'new_value': 102037.1}, {'field': 'order_count', 'old_value': 4146, 'new_value': 4277}]
2025-04-28 09:01:51,036 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MH2
2025-04-28 09:01:51,474 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MH2
2025-04-28 09:01:51,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 431005.68, 'new_value': 443046.54}, {'field': 'total_amount', 'old_value': 433489.26, 'new_value': 445530.12}, {'field': 'order_count', 'old_value': 6757, 'new_value': 6949}]
2025-04-28 09:01:51,474 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MJ2
2025-04-28 09:01:51,958 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MJ2
2025-04-28 09:01:51,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209005.0, 'new_value': 216098.0}, {'field': 'total_amount', 'old_value': 209005.0, 'new_value': 216098.0}, {'field': 'order_count', 'old_value': 7420, 'new_value': 7674}]
2025-04-28 09:01:51,974 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MO2
2025-04-28 09:01:52,333 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MO2
2025-04-28 09:01:52,333 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10717.9, 'new_value': 11329.9}, {'field': 'offline_amount', 'old_value': 32809.42, 'new_value': 32886.42}, {'field': 'total_amount', 'old_value': 43527.32, 'new_value': 44216.32}, {'field': 'order_count', 'old_value': 445, 'new_value': 456}]
2025-04-28 09:01:52,333 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MS2
2025-04-28 09:01:52,755 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MS2
2025-04-28 09:01:52,755 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 369330.0, 'new_value': 386190.0}, {'field': 'total_amount', 'old_value': 369330.0, 'new_value': 386190.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 148}]
2025-04-28 09:01:52,755 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M26
2025-04-28 09:01:53,224 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M26
2025-04-28 09:01:53,224 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16817.58, 'new_value': 17684.9}, {'field': 'offline_amount', 'old_value': 33300.68, 'new_value': 34400.71}, {'field': 'total_amount', 'old_value': 50118.26, 'new_value': 52085.61}, {'field': 'order_count', 'old_value': 2336, 'new_value': 2440}]
2025-04-28 09:01:53,224 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME6
2025-04-28 09:01:53,739 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME6
2025-04-28 09:01:53,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4550156.95, 'new_value': 4722361.57}, {'field': 'total_amount', 'old_value': 4550156.95, 'new_value': 4722361.57}, {'field': 'order_count', 'old_value': 137322, 'new_value': 140787}]
2025-04-28 09:01:53,739 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH6
2025-04-28 09:01:54,177 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH6
2025-04-28 09:01:54,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 250637.0, 'new_value': 260707.0}, {'field': 'total_amount', 'old_value': 250640.0, 'new_value': 260710.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 67}]
2025-04-28 09:01:54,177 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK6
2025-04-28 09:01:54,661 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK6
2025-04-28 09:01:54,661 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 296623.5, 'new_value': 302146.3}, {'field': 'total_amount', 'old_value': 296623.5, 'new_value': 302146.3}, {'field': 'order_count', 'old_value': 95, 'new_value': 99}]
2025-04-28 09:01:54,661 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN6
2025-04-28 09:01:55,114 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN6
2025-04-28 09:01:55,114 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99936.0, 'new_value': 103316.0}, {'field': 'offline_amount', 'old_value': 1183938.0, 'new_value': 1227758.0}, {'field': 'total_amount', 'old_value': 1283874.0, 'new_value': 1331074.0}, {'field': 'order_count', 'old_value': 29716, 'new_value': 30886}]
2025-04-28 09:01:55,114 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M37
2025-04-28 09:01:55,583 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M37
2025-04-28 09:01:55,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 361939.0, 'new_value': 367343.0}, {'field': 'total_amount', 'old_value': 373101.0, 'new_value': 378505.0}, {'field': 'order_count', 'old_value': 8374, 'new_value': 8500}]
2025-04-28 09:01:55,583 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M97
2025-04-28 09:01:55,989 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M97
2025-04-28 09:01:55,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 360160.8, 'new_value': 373149.4}, {'field': 'total_amount', 'old_value': 360160.8, 'new_value': 373149.4}, {'field': 'order_count', 'old_value': 1457, 'new_value': 1511}]
2025-04-28 09:01:55,989 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF7
2025-04-28 09:01:56,614 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF7
2025-04-28 09:01:56,614 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46847.9, 'new_value': 56777.9}, {'field': 'total_amount', 'old_value': 47643.9, 'new_value': 57573.9}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-04-28 09:01:56,614 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI7
2025-04-28 09:01:57,036 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI7
2025-04-28 09:01:57,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 122103.62, 'new_value': 125387.12}, {'field': 'offline_amount', 'old_value': 231593.15, 'new_value': 237687.09}, {'field': 'total_amount', 'old_value': 353696.77, 'new_value': 363074.21}, {'field': 'order_count', 'old_value': 4181, 'new_value': 4284}]
2025-04-28 09:01:57,036 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MJ7
2025-04-28 09:01:57,552 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MJ7
2025-04-28 09:01:57,552 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1000.0, 'new_value': 1079.9}, {'field': 'total_amount', 'old_value': 10038.0, 'new_value': 10117.9}, {'field': 'order_count', 'old_value': 1007, 'new_value': 1008}]
2025-04-28 09:01:57,552 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MX1
2025-04-28 09:01:58,114 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MX1
2025-04-28 09:01:58,114 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2649.0, 'new_value': 9407.0}, {'field': 'offline_amount', 'old_value': 157708.0, 'new_value': 160464.0}, {'field': 'total_amount', 'old_value': 160357.0, 'new_value': 169871.0}, {'field': 'order_count', 'old_value': 657, 'new_value': 679}]
2025-04-28 09:01:58,114 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M12
2025-04-28 09:01:58,520 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M12
2025-04-28 09:01:58,520 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23870.15, 'new_value': 30118.86}, {'field': 'offline_amount', 'old_value': 341402.05, 'new_value': 346895.18}, {'field': 'total_amount', 'old_value': 365272.2, 'new_value': 377014.04}, {'field': 'order_count', 'old_value': 4221, 'new_value': 4370}]
2025-04-28 09:01:58,520 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MA2
2025-04-28 09:01:58,942 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MA2
2025-04-28 09:01:58,942 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 188521.71, 'new_value': 198430.71}, {'field': 'offline_amount', 'old_value': 162750.09, 'new_value': 169948.09}, {'field': 'total_amount', 'old_value': 351271.8, 'new_value': 368378.8}, {'field': 'order_count', 'old_value': 1166, 'new_value': 1231}]
2025-04-28 09:01:58,942 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MD2
2025-04-28 09:01:59,395 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MD2
2025-04-28 09:01:59,395 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97511.0, 'new_value': 129071.0}, {'field': 'total_amount', 'old_value': 97511.0, 'new_value': 129071.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 12}]
2025-04-28 09:01:59,395 - INFO - 日期 2025-04 处理完成 - 更新: 208 条，插入: 0 条，错误: 0 条
2025-04-28 09:01:59,395 - INFO - 数据同步完成！更新: 208 条，插入: 0 条，错误: 0 条
2025-04-28 09:01:59,395 - INFO - =================同步完成====================
2025-04-28 12:00:01,891 - INFO - =================使用默认全量同步=============
2025-04-28 12:00:03,062 - INFO - MySQL查询成功，共获取 2639 条记录
2025-04-28 12:00:03,062 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-28 12:00:03,094 - INFO - 开始处理日期: 2025-01
2025-04-28 12:00:03,094 - INFO - Request Parameters - Page 1:
2025-04-28 12:00:03,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:03,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:04,062 - INFO - Response - Page 1:
2025-04-28 12:00:04,265 - INFO - 第 1 页获取到 100 条记录
2025-04-28 12:00:04,265 - INFO - Request Parameters - Page 2:
2025-04-28 12:00:04,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:04,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:04,812 - INFO - Response - Page 2:
2025-04-28 12:00:05,015 - INFO - 第 2 页获取到 100 条记录
2025-04-28 12:00:05,015 - INFO - Request Parameters - Page 3:
2025-04-28 12:00:05,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:05,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:05,547 - INFO - Response - Page 3:
2025-04-28 12:00:05,750 - INFO - 第 3 页获取到 100 条记录
2025-04-28 12:00:05,750 - INFO - Request Parameters - Page 4:
2025-04-28 12:00:05,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:05,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:06,406 - INFO - Response - Page 4:
2025-04-28 12:00:06,609 - INFO - 第 4 页获取到 100 条记录
2025-04-28 12:00:06,609 - INFO - Request Parameters - Page 5:
2025-04-28 12:00:06,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:06,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:07,109 - INFO - Response - Page 5:
2025-04-28 12:00:07,312 - INFO - 第 5 页获取到 100 条记录
2025-04-28 12:00:07,312 - INFO - Request Parameters - Page 6:
2025-04-28 12:00:07,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:07,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:07,859 - INFO - Response - Page 6:
2025-04-28 12:00:08,062 - INFO - 第 6 页获取到 100 条记录
2025-04-28 12:00:08,062 - INFO - Request Parameters - Page 7:
2025-04-28 12:00:08,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:08,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:08,515 - INFO - Response - Page 7:
2025-04-28 12:00:08,719 - INFO - 第 7 页获取到 82 条记录
2025-04-28 12:00:08,719 - INFO - 查询完成，共获取到 682 条记录
2025-04-28 12:00:08,719 - INFO - 获取到 682 条表单数据
2025-04-28 12:00:08,719 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-28 12:00:08,734 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 12:00:08,734 - INFO - 开始处理日期: 2025-02
2025-04-28 12:00:08,734 - INFO - Request Parameters - Page 1:
2025-04-28 12:00:08,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:08,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:09,219 - INFO - Response - Page 1:
2025-04-28 12:00:09,422 - INFO - 第 1 页获取到 100 条记录
2025-04-28 12:00:09,422 - INFO - Request Parameters - Page 2:
2025-04-28 12:00:09,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:09,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:09,906 - INFO - Response - Page 2:
2025-04-28 12:00:10,109 - INFO - 第 2 页获取到 100 条记录
2025-04-28 12:00:10,109 - INFO - Request Parameters - Page 3:
2025-04-28 12:00:10,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:10,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:10,594 - INFO - Response - Page 3:
2025-04-28 12:00:10,797 - INFO - 第 3 页获取到 100 条记录
2025-04-28 12:00:10,797 - INFO - Request Parameters - Page 4:
2025-04-28 12:00:10,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:10,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:11,297 - INFO - Response - Page 4:
2025-04-28 12:00:11,500 - INFO - 第 4 页获取到 100 条记录
2025-04-28 12:00:11,500 - INFO - Request Parameters - Page 5:
2025-04-28 12:00:11,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:11,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:11,969 - INFO - Response - Page 5:
2025-04-28 12:00:12,172 - INFO - 第 5 页获取到 100 条记录
2025-04-28 12:00:12,172 - INFO - Request Parameters - Page 6:
2025-04-28 12:00:12,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:12,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:12,625 - INFO - Response - Page 6:
2025-04-28 12:00:12,828 - INFO - 第 6 页获取到 100 条记录
2025-04-28 12:00:12,828 - INFO - Request Parameters - Page 7:
2025-04-28 12:00:12,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:12,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:13,297 - INFO - Response - Page 7:
2025-04-28 12:00:13,500 - INFO - 第 7 页获取到 70 条记录
2025-04-28 12:00:13,500 - INFO - 查询完成，共获取到 670 条记录
2025-04-28 12:00:13,500 - INFO - 获取到 670 条表单数据
2025-04-28 12:00:13,500 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-28 12:00:13,515 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 12:00:13,515 - INFO - 开始处理日期: 2025-03
2025-04-28 12:00:13,515 - INFO - Request Parameters - Page 1:
2025-04-28 12:00:13,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:13,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:14,062 - INFO - Response - Page 1:
2025-04-28 12:00:14,265 - INFO - 第 1 页获取到 100 条记录
2025-04-28 12:00:14,265 - INFO - Request Parameters - Page 2:
2025-04-28 12:00:14,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:14,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:14,812 - INFO - Response - Page 2:
2025-04-28 12:00:15,015 - INFO - 第 2 页获取到 100 条记录
2025-04-28 12:00:15,015 - INFO - Request Parameters - Page 3:
2025-04-28 12:00:15,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:15,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:15,640 - INFO - Response - Page 3:
2025-04-28 12:00:15,844 - INFO - 第 3 页获取到 100 条记录
2025-04-28 12:00:15,844 - INFO - Request Parameters - Page 4:
2025-04-28 12:00:15,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:15,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:16,281 - INFO - Response - Page 4:
2025-04-28 12:00:16,484 - INFO - 第 4 页获取到 100 条记录
2025-04-28 12:00:16,484 - INFO - Request Parameters - Page 5:
2025-04-28 12:00:16,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:16,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:16,953 - INFO - Response - Page 5:
2025-04-28 12:00:17,156 - INFO - 第 5 页获取到 100 条记录
2025-04-28 12:00:17,156 - INFO - Request Parameters - Page 6:
2025-04-28 12:00:17,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:17,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:17,625 - INFO - Response - Page 6:
2025-04-28 12:00:17,828 - INFO - 第 6 页获取到 100 条记录
2025-04-28 12:00:17,828 - INFO - Request Parameters - Page 7:
2025-04-28 12:00:17,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:17,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:18,359 - INFO - Response - Page 7:
2025-04-28 12:00:18,562 - INFO - 第 7 页获取到 61 条记录
2025-04-28 12:00:18,562 - INFO - 查询完成，共获取到 661 条记录
2025-04-28 12:00:18,562 - INFO - 获取到 661 条表单数据
2025-04-28 12:00:18,562 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-28 12:00:18,578 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 12:00:18,578 - INFO - 开始处理日期: 2025-04
2025-04-28 12:00:18,578 - INFO - Request Parameters - Page 1:
2025-04-28 12:00:18,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:18,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:19,078 - INFO - Response - Page 1:
2025-04-28 12:00:19,281 - INFO - 第 1 页获取到 100 条记录
2025-04-28 12:00:19,281 - INFO - Request Parameters - Page 2:
2025-04-28 12:00:19,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:19,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:19,844 - INFO - Response - Page 2:
2025-04-28 12:00:20,047 - INFO - 第 2 页获取到 100 条记录
2025-04-28 12:00:20,047 - INFO - Request Parameters - Page 3:
2025-04-28 12:00:20,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:20,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:20,547 - INFO - Response - Page 3:
2025-04-28 12:00:20,750 - INFO - 第 3 页获取到 100 条记录
2025-04-28 12:00:20,750 - INFO - Request Parameters - Page 4:
2025-04-28 12:00:20,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:20,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:21,250 - INFO - Response - Page 4:
2025-04-28 12:00:21,453 - INFO - 第 4 页获取到 100 条记录
2025-04-28 12:00:21,453 - INFO - Request Parameters - Page 5:
2025-04-28 12:00:21,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:21,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:22,015 - INFO - Response - Page 5:
2025-04-28 12:00:22,219 - INFO - 第 5 页获取到 100 条记录
2025-04-28 12:00:22,219 - INFO - Request Parameters - Page 6:
2025-04-28 12:00:22,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:22,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:22,672 - INFO - Response - Page 6:
2025-04-28 12:00:22,875 - INFO - 第 6 页获取到 100 条记录
2025-04-28 12:00:22,875 - INFO - Request Parameters - Page 7:
2025-04-28 12:00:22,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 12:00:22,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 12:00:23,203 - INFO - Response - Page 7:
2025-04-28 12:00:23,406 - INFO - 第 7 页获取到 26 条记录
2025-04-28 12:00:23,406 - INFO - 查询完成，共获取到 626 条记录
2025-04-28 12:00:23,406 - INFO - 获取到 626 条表单数据
2025-04-28 12:00:23,406 - INFO - 当前日期 2025-04 有 626 条MySQL数据需要处理
2025-04-28 12:00:23,406 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MZ2
2025-04-28 12:00:23,906 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MZ2
2025-04-28 12:00:23,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39539.2, 'new_value': 40902.75}, {'field': 'total_amount', 'old_value': 39746.2, 'new_value': 41109.75}, {'field': 'order_count', 'old_value': 5038, 'new_value': 5224}]
2025-04-28 12:00:23,922 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MC3
2025-04-28 12:00:24,531 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MC3
2025-04-28 12:00:24,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28022.71, 'new_value': 29565.71}, {'field': 'offline_amount', 'old_value': 15367.98, 'new_value': 15928.98}, {'field': 'total_amount', 'old_value': 43390.69, 'new_value': 45494.69}, {'field': 'order_count', 'old_value': 1936, 'new_value': 2034}]
2025-04-28 12:00:24,531 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MD3
2025-04-28 12:00:25,015 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MD3
2025-04-28 12:00:25,015 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 358161.0, 'new_value': 375849.0}, {'field': 'offline_amount', 'old_value': 675197.0, 'new_value': 682812.0}, {'field': 'total_amount', 'old_value': 1033358.0, 'new_value': 1058661.0}, {'field': 'order_count', 'old_value': 972, 'new_value': 1006}]
2025-04-28 12:00:25,015 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MF3
2025-04-28 12:00:25,562 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MF3
2025-04-28 12:00:25,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23278.26, 'new_value': 23670.26}, {'field': 'total_amount', 'old_value': 23278.26, 'new_value': 23670.26}, {'field': 'order_count', 'old_value': 93, 'new_value': 96}]
2025-04-28 12:00:25,562 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MH3
2025-04-28 12:00:25,984 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MH3
2025-04-28 12:00:25,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65487.1, 'new_value': 68247.1}, {'field': 'total_amount', 'old_value': 65487.1, 'new_value': 68247.1}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-04-28 12:00:25,984 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MI3
2025-04-28 12:00:26,500 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MI3
2025-04-28 12:00:26,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237938.0, 'new_value': 243336.0}, {'field': 'total_amount', 'old_value': 237938.0, 'new_value': 243336.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 42}]
2025-04-28 12:00:26,500 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MJ3
2025-04-28 12:00:26,968 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MJ3
2025-04-28 12:00:26,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185334.0, 'new_value': 191894.0}, {'field': 'total_amount', 'old_value': 185334.0, 'new_value': 191894.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-04-28 12:00:26,968 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MM3
2025-04-28 12:00:27,547 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MM3
2025-04-28 12:00:27,547 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3987.73, 'new_value': 6473.73}, {'field': 'offline_amount', 'old_value': 23041.41, 'new_value': 25527.41}, {'field': 'total_amount', 'old_value': 27029.14, 'new_value': 32001.14}, {'field': 'order_count', 'old_value': 2027, 'new_value': 4513}]
2025-04-28 12:00:27,547 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MN3
2025-04-28 12:00:28,031 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MN3
2025-04-28 12:00:28,031 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5480.0, 'new_value': 8060.0}, {'field': 'total_amount', 'old_value': 68440.0, 'new_value': 71020.0}, {'field': 'order_count', 'old_value': 702, 'new_value': 724}]
2025-04-28 12:00:28,031 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ3
2025-04-28 12:00:28,515 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ3
2025-04-28 12:00:28,515 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 560889.0, 'new_value': 562239.0}, {'field': 'total_amount', 'old_value': 647082.0, 'new_value': 648432.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 107}]
2025-04-28 12:00:28,515 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU3
2025-04-28 12:00:29,000 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU3
2025-04-28 12:00:29,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11658.62, 'new_value': 12131.39}, {'field': 'offline_amount', 'old_value': 49184.04, 'new_value': 52350.64}, {'field': 'total_amount', 'old_value': 60842.66, 'new_value': 64482.03}, {'field': 'order_count', 'old_value': 1179, 'new_value': 1227}]
2025-04-28 12:00:29,000 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV3
2025-04-28 12:00:29,484 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV3
2025-04-28 12:00:29,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56020.7, 'new_value': 57334.7}, {'field': 'total_amount', 'old_value': 60534.05, 'new_value': 61848.05}, {'field': 'order_count', 'old_value': 3902, 'new_value': 3925}]
2025-04-28 12:00:29,484 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY3
2025-04-28 12:00:29,922 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY3
2025-04-28 12:00:29,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44738.59, 'new_value': 46793.53}, {'field': 'total_amount', 'old_value': 44738.59, 'new_value': 46793.53}, {'field': 'order_count', 'old_value': 8950, 'new_value': 9361}]
2025-04-28 12:00:29,922 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M04
2025-04-28 12:00:30,328 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M04
2025-04-28 12:00:30,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76181.0, 'new_value': 79633.0}, {'field': 'total_amount', 'old_value': 139656.0, 'new_value': 143108.0}, {'field': 'order_count', 'old_value': 1971, 'new_value': 2029}]
2025-04-28 12:00:30,328 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M14
2025-04-28 12:00:30,797 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M14
2025-04-28 12:00:30,797 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51410.0, 'new_value': 55172.0}, {'field': 'offline_amount', 'old_value': 135681.0, 'new_value': 140171.0}, {'field': 'total_amount', 'old_value': 187091.0, 'new_value': 195343.0}, {'field': 'order_count', 'old_value': 3719, 'new_value': 3869}]
2025-04-28 12:00:30,797 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M34
2025-04-28 12:00:31,265 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M34
2025-04-28 12:00:31,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22760.82, 'new_value': 22898.82}, {'field': 'total_amount', 'old_value': 22816.62, 'new_value': 22954.62}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-04-28 12:00:31,265 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M74
2025-04-28 12:00:31,765 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M74
2025-04-28 12:00:31,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53346.55, 'new_value': 55446.24}, {'field': 'total_amount', 'old_value': 53346.55, 'new_value': 55446.24}, {'field': 'order_count', 'old_value': 2890, 'new_value': 3048}]
2025-04-28 12:00:31,765 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M84
2025-04-28 12:00:32,203 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M84
2025-04-28 12:00:32,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6812.55, 'new_value': 6866.55}, {'field': 'total_amount', 'old_value': 6812.55, 'new_value': 6866.55}, {'field': 'order_count', 'old_value': 939, 'new_value': 942}]
2025-04-28 12:00:32,203 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB4
2025-04-28 12:00:32,656 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB4
2025-04-28 12:00:32,656 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89858.0, 'new_value': 90390.0}, {'field': 'total_amount', 'old_value': 89858.0, 'new_value': 90390.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 43}]
2025-04-28 12:00:32,656 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF4
2025-04-28 12:00:33,109 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF4
2025-04-28 12:00:33,109 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6523.8, 'new_value': 6618.7}, {'field': 'offline_amount', 'old_value': 44350.0, 'new_value': 45681.5}, {'field': 'total_amount', 'old_value': 50873.8, 'new_value': 52300.2}, {'field': 'order_count', 'old_value': 520, 'new_value': 538}]
2025-04-28 12:00:33,109 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG4
2025-04-28 12:00:33,625 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG4
2025-04-28 12:00:33,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 939352.0, 'new_value': 974426.0}, {'field': 'total_amount', 'old_value': 939352.0, 'new_value': 974426.0}, {'field': 'order_count', 'old_value': 155, 'new_value': 160}]
2025-04-28 12:00:33,625 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH4
2025-04-28 12:00:34,078 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH4
2025-04-28 12:00:34,078 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8937.44, 'new_value': 9181.44}, {'field': 'offline_amount', 'old_value': 209649.15, 'new_value': 219009.15}, {'field': 'total_amount', 'old_value': 218586.59, 'new_value': 228190.59}, {'field': 'order_count', 'old_value': 1221, 'new_value': 1252}]
2025-04-28 12:00:34,078 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK4
2025-04-28 12:00:34,562 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK4
2025-04-28 12:00:34,562 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67668.2, 'new_value': 73408.7}, {'field': 'total_amount', 'old_value': 172377.52, 'new_value': 178118.02}, {'field': 'order_count', 'old_value': 5989, 'new_value': 6179}]
2025-04-28 12:00:34,562 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN4
2025-04-28 12:00:35,015 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN4
2025-04-28 12:00:35,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 642947.7, 'new_value': 689601.0}, {'field': 'total_amount', 'old_value': 642947.7, 'new_value': 689601.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 87}]
2025-04-28 12:00:35,015 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP4
2025-04-28 12:00:35,531 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP4
2025-04-28 12:00:35,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102155.0, 'new_value': 120936.0}, {'field': 'offline_amount', 'old_value': 124590.0, 'new_value': 132689.0}, {'field': 'total_amount', 'old_value': 226745.0, 'new_value': 253625.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 68}]
2025-04-28 12:00:35,531 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ4
2025-04-28 12:00:35,906 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ4
2025-04-28 12:00:35,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106038.9, 'new_value': 108875.9}, {'field': 'offline_amount', 'old_value': 166924.25, 'new_value': 173638.25}, {'field': 'total_amount', 'old_value': 272963.15, 'new_value': 282514.15}, {'field': 'order_count', 'old_value': 5937, 'new_value': 6156}]
2025-04-28 12:00:35,906 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR4
2025-04-28 12:00:36,406 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR4
2025-04-28 12:00:36,406 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86205.71, 'new_value': 88067.75}, {'field': 'offline_amount', 'old_value': 805390.78, 'new_value': 832752.27}, {'field': 'total_amount', 'old_value': 891596.49, 'new_value': 920820.02}, {'field': 'order_count', 'old_value': 4007, 'new_value': 4101}]
2025-04-28 12:00:36,406 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT4
2025-04-28 12:00:37,015 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT4
2025-04-28 12:00:37,015 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62860.93, 'new_value': 66204.55}, {'field': 'offline_amount', 'old_value': 662367.21, 'new_value': 687458.35}, {'field': 'total_amount', 'old_value': 725228.14, 'new_value': 753662.9}, {'field': 'order_count', 'old_value': 2972, 'new_value': 3081}]
2025-04-28 12:00:37,015 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU4
2025-04-28 12:00:37,484 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU4
2025-04-28 12:00:37,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41050.9, 'new_value': 42780.8}, {'field': 'total_amount', 'old_value': 41050.9, 'new_value': 42780.8}, {'field': 'order_count', 'old_value': 246, 'new_value': 259}]
2025-04-28 12:00:37,484 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MW4
2025-04-28 12:00:37,953 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MW4
2025-04-28 12:00:37,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81425.35, 'new_value': 82704.28}, {'field': 'total_amount', 'old_value': 84494.68, 'new_value': 85773.61}, {'field': 'order_count', 'old_value': 4991, 'new_value': 5079}]
2025-04-28 12:00:37,953 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX4
2025-04-28 12:00:38,375 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX4
2025-04-28 12:00:38,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 634907.33, 'new_value': 658674.25}, {'field': 'total_amount', 'old_value': 634907.33, 'new_value': 658674.25}, {'field': 'order_count', 'old_value': 4495, 'new_value': 4700}]
2025-04-28 12:00:38,375 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY4
2025-04-28 12:00:38,828 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY4
2025-04-28 12:00:38,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32197.64, 'new_value': 34997.64}, {'field': 'total_amount', 'old_value': 32197.64, 'new_value': 34997.64}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-04-28 12:00:38,828 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ4
2025-04-28 12:00:39,281 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ4
2025-04-28 12:00:39,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86055.0, 'new_value': 89573.0}, {'field': 'total_amount', 'old_value': 86055.0, 'new_value': 89573.0}, {'field': 'order_count', 'old_value': 756, 'new_value': 785}]
2025-04-28 12:00:39,281 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M25
2025-04-28 12:00:39,781 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M25
2025-04-28 12:00:39,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 226214.0, 'new_value': 246260.0}, {'field': 'total_amount', 'old_value': 243756.0, 'new_value': 263802.0}, {'field': 'order_count', 'old_value': 224, 'new_value': 233}]
2025-04-28 12:00:39,781 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M35
2025-04-28 12:00:40,375 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M35
2025-04-28 12:00:40,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87545.0, 'new_value': 90055.0}, {'field': 'total_amount', 'old_value': 87545.0, 'new_value': 90055.0}, {'field': 'order_count', 'old_value': 4828, 'new_value': 5190}]
2025-04-28 12:00:40,375 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M65
2025-04-28 12:00:40,890 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M65
2025-04-28 12:00:40,890 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1787.58, 'new_value': 1862.28}, {'field': 'offline_amount', 'old_value': 103122.25, 'new_value': 104483.55}, {'field': 'total_amount', 'old_value': 104909.83, 'new_value': 106345.83}, {'field': 'order_count', 'old_value': 2614, 'new_value': 2660}]
2025-04-28 12:00:40,890 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M75
2025-04-28 12:00:41,375 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M75
2025-04-28 12:00:41,375 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56945.59, 'new_value': 59874.51}, {'field': 'offline_amount', 'old_value': 595303.64, 'new_value': 615583.3}, {'field': 'total_amount', 'old_value': 652249.23, 'new_value': 675457.81}, {'field': 'order_count', 'old_value': 2113, 'new_value': 2191}]
2025-04-28 12:00:41,375 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA5
2025-04-28 12:00:41,828 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA5
2025-04-28 12:00:41,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210851.96, 'new_value': 217547.13}, {'field': 'total_amount', 'old_value': 210851.96, 'new_value': 217547.13}, {'field': 'order_count', 'old_value': 1074, 'new_value': 1113}]
2025-04-28 12:00:41,828 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF5
2025-04-28 12:00:42,234 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF5
2025-04-28 12:00:42,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37915.0, 'new_value': 39270.0}, {'field': 'total_amount', 'old_value': 41922.0, 'new_value': 43277.0}, {'field': 'order_count', 'old_value': 200, 'new_value': 207}]
2025-04-28 12:00:42,234 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH5
2025-04-28 12:00:42,718 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH5
2025-04-28 12:00:42,718 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61372.65, 'new_value': 63918.71}, {'field': 'offline_amount', 'old_value': 45012.18, 'new_value': 47372.9}, {'field': 'total_amount', 'old_value': 106384.83, 'new_value': 111291.61}, {'field': 'order_count', 'old_value': 3649, 'new_value': 3809}]
2025-04-28 12:00:42,718 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI5
2025-04-28 12:00:43,156 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI5
2025-04-28 12:00:43,156 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136642.29, 'new_value': 142761.4}, {'field': 'total_amount', 'old_value': 136642.29, 'new_value': 142761.4}, {'field': 'order_count', 'old_value': 245, 'new_value': 257}]
2025-04-28 12:00:43,156 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK5
2025-04-28 12:00:43,687 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK5
2025-04-28 12:00:43,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20736.0, 'new_value': 21889.0}, {'field': 'total_amount', 'old_value': 25883.0, 'new_value': 27036.0}, {'field': 'order_count', 'old_value': 162, 'new_value': 166}]
2025-04-28 12:00:43,687 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML5
2025-04-28 12:00:44,078 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML5
2025-04-28 12:00:44,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126698.35, 'new_value': 132301.12}, {'field': 'total_amount', 'old_value': 128875.08, 'new_value': 134477.85}, {'field': 'order_count', 'old_value': 570, 'new_value': 610}]
2025-04-28 12:00:44,078 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ5
2025-04-28 12:00:44,531 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ5
2025-04-28 12:00:44,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 201294.0, 'new_value': 220670.0}, {'field': 'total_amount', 'old_value': 213795.0, 'new_value': 233171.0}, {'field': 'order_count', 'old_value': 1166, 'new_value': 1246}]
2025-04-28 12:00:44,531 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MB9
2025-04-28 12:00:45,015 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MB9
2025-04-28 12:00:45,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101291.5, 'new_value': 104266.1}, {'field': 'total_amount', 'old_value': 101291.5, 'new_value': 104266.1}, {'field': 'order_count', 'old_value': 198, 'new_value': 204}]
2025-04-28 12:00:45,031 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MC9
2025-04-28 12:00:45,437 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MC9
2025-04-28 12:00:45,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1637104.38, 'new_value': 1694809.47}, {'field': 'total_amount', 'old_value': 1637104.38, 'new_value': 1694809.47}, {'field': 'order_count', 'old_value': 12891, 'new_value': 13353}]
2025-04-28 12:00:45,437 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MD9
2025-04-28 12:00:46,047 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MD9
2025-04-28 12:00:46,047 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22825.42, 'new_value': 23945.07}, {'field': 'offline_amount', 'old_value': 39656.67, 'new_value': 41342.94}, {'field': 'total_amount', 'old_value': 62482.09, 'new_value': 65288.01}, {'field': 'order_count', 'old_value': 2564, 'new_value': 2661}]
2025-04-28 12:00:46,047 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9ME9
2025-04-28 12:00:46,468 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9ME9
2025-04-28 12:00:46,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50568.0, 'new_value': 50878.0}, {'field': 'total_amount', 'old_value': 50568.0, 'new_value': 50878.0}, {'field': 'order_count', 'old_value': 912, 'new_value': 915}]
2025-04-28 12:00:46,468 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MG9
2025-04-28 12:00:46,968 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MG9
2025-04-28 12:00:46,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24887.62, 'new_value': 25262.02}, {'field': 'total_amount', 'old_value': 24887.62, 'new_value': 25262.02}, {'field': 'order_count', 'old_value': 524, 'new_value': 150}]
2025-04-28 12:00:46,968 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MI9
2025-04-28 12:00:47,484 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MI9
2025-04-28 12:00:47,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191315.27, 'new_value': 210354.56}, {'field': 'total_amount', 'old_value': 191315.27, 'new_value': 210354.56}, {'field': 'order_count', 'old_value': 311, 'new_value': 330}]
2025-04-28 12:00:47,484 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJ9
2025-04-28 12:00:47,937 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJ9
2025-04-28 12:00:47,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 279262.04, 'new_value': 292328.04}, {'field': 'total_amount', 'old_value': 279262.04, 'new_value': 292328.04}, {'field': 'order_count', 'old_value': 187, 'new_value': 197}]
2025-04-28 12:00:47,937 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MO9
2025-04-28 12:00:48,359 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MO9
2025-04-28 12:00:48,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95875.08, 'new_value': 97784.42}, {'field': 'total_amount', 'old_value': 95875.08, 'new_value': 97784.42}, {'field': 'order_count', 'old_value': 176, 'new_value': 180}]
2025-04-28 12:00:48,359 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-04-28 12:00:48,781 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-04-28 12:00:48,781 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 382423.1, 'new_value': 395625.73}, {'field': 'offline_amount', 'old_value': 1243.0, 'new_value': 1443.0}, {'field': 'total_amount', 'old_value': 383666.1, 'new_value': 397068.73}, {'field': 'order_count', 'old_value': 4617, 'new_value': 4757}]
2025-04-28 12:00:48,781 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MS9
2025-04-28 12:00:49,250 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MS9
2025-04-28 12:00:49,250 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33834.5, 'new_value': 33902.5}, {'field': 'offline_amount', 'old_value': 48.0, 'new_value': 49.0}, {'field': 'total_amount', 'old_value': 33882.5, 'new_value': 33951.5}, {'field': 'order_count', 'old_value': 66, 'new_value': 69}]
2025-04-28 12:00:49,250 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MU9
2025-04-28 12:00:49,671 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MU9
2025-04-28 12:00:49,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32987.0, 'new_value': 35607.0}, {'field': 'total_amount', 'old_value': 77987.0, 'new_value': 80607.0}, {'field': 'order_count', 'old_value': 10892, 'new_value': 10893}]
2025-04-28 12:00:49,671 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MW9
2025-04-28 12:00:50,093 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MW9
2025-04-28 12:00:50,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62566.0, 'new_value': 62865.0}, {'field': 'total_amount', 'old_value': 62566.0, 'new_value': 62865.0}, {'field': 'order_count', 'old_value': 242, 'new_value': 243}]
2025-04-28 12:00:50,093 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MX9
2025-04-28 12:00:50,531 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MX9
2025-04-28 12:00:50,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 168960.85, 'new_value': 175842.78}, {'field': 'offline_amount', 'old_value': 24161.16, 'new_value': 25018.11}, {'field': 'total_amount', 'old_value': 193122.01, 'new_value': 200860.89}, {'field': 'order_count', 'old_value': 5404, 'new_value': 5675}]
2025-04-28 12:00:50,531 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1A
2025-04-28 12:00:51,000 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1A
2025-04-28 12:00:51,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6682.6, 'new_value': 8480.6}, {'field': 'offline_amount', 'old_value': 99707.9, 'new_value': 101205.9}, {'field': 'total_amount', 'old_value': 106390.5, 'new_value': 109686.5}, {'field': 'order_count', 'old_value': 139, 'new_value': 145}]
2025-04-28 12:00:51,000 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4A
2025-04-28 12:00:51,453 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4A
2025-04-28 12:00:51,453 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6991.7, 'new_value': 7291.3}, {'field': 'offline_amount', 'old_value': 40529.0, 'new_value': 40805.0}, {'field': 'total_amount', 'old_value': 47520.7, 'new_value': 48096.3}, {'field': 'order_count', 'old_value': 499, 'new_value': 509}]
2025-04-28 12:00:51,453 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7A
2025-04-28 12:00:51,890 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7A
2025-04-28 12:00:51,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86017.0, 'new_value': 88616.0}, {'field': 'total_amount', 'old_value': 90626.27, 'new_value': 93225.27}, {'field': 'order_count', 'old_value': 77, 'new_value': 78}]
2025-04-28 12:00:51,890 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8A
2025-04-28 12:00:52,375 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8A
2025-04-28 12:00:52,375 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 158486.08, 'new_value': 167627.68}, {'field': 'offline_amount', 'old_value': 661277.84, 'new_value': 683813.48}, {'field': 'total_amount', 'old_value': 819763.92, 'new_value': 851441.16}, {'field': 'order_count', 'old_value': 1849, 'new_value': 1926}]
2025-04-28 12:00:52,375 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-04-28 12:00:52,812 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-04-28 12:00:52,812 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19769.0, 'new_value': 20145.76}, {'field': 'offline_amount', 'old_value': 368000.7, 'new_value': 377865.9}, {'field': 'total_amount', 'old_value': 387769.7, 'new_value': 398011.66}, {'field': 'order_count', 'old_value': 2791, 'new_value': 2857}]
2025-04-28 12:00:52,812 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJA
2025-04-28 12:00:53,359 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJA
2025-04-28 12:00:53,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 379662.16, 'new_value': 398276.16}, {'field': 'total_amount', 'old_value': 379662.16, 'new_value': 398276.16}, {'field': 'order_count', 'old_value': 69, 'new_value': 74}]
2025-04-28 12:00:53,359 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXA
2025-04-28 12:00:53,796 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXA
2025-04-28 12:00:53,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44986.0, 'new_value': 46869.0}, {'field': 'total_amount', 'old_value': 51413.9, 'new_value': 53296.9}, {'field': 'order_count', 'old_value': 60, 'new_value': 62}]
2025-04-28 12:00:53,796 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1B
2025-04-28 12:00:54,218 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1B
2025-04-28 12:00:54,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70274.4, 'new_value': 73628.3}, {'field': 'total_amount', 'old_value': 70274.4, 'new_value': 73628.3}, {'field': 'order_count', 'old_value': 195, 'new_value': 206}]
2025-04-28 12:00:54,218 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2B
2025-04-28 12:00:54,734 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2B
2025-04-28 12:00:54,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38617.48, 'new_value': 42929.16}, {'field': 'total_amount', 'old_value': 79144.15, 'new_value': 83455.83}, {'field': 'order_count', 'old_value': 2811, 'new_value': 2961}]
2025-04-28 12:00:54,734 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9B
2025-04-28 12:00:55,140 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9B
2025-04-28 12:00:55,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79632.0, 'new_value': 90531.0}, {'field': 'total_amount', 'old_value': 79632.0, 'new_value': 90531.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-04-28 12:00:55,140 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MBB
2025-04-28 12:00:55,593 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MBB
2025-04-28 12:00:55,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42664.76, 'new_value': 43460.36}, {'field': 'total_amount', 'old_value': 42664.76, 'new_value': 43460.36}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-04-28 12:00:55,593 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDB
2025-04-28 12:00:56,078 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDB
2025-04-28 12:00:56,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45348.0, 'new_value': 46751.0}, {'field': 'total_amount', 'old_value': 45978.0, 'new_value': 47381.0}, {'field': 'order_count', 'old_value': 176, 'new_value': 182}]
2025-04-28 12:00:56,078 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHB
2025-04-28 12:00:56,484 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHB
2025-04-28 12:00:56,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32202.2, 'new_value': 32675.2}, {'field': 'total_amount', 'old_value': 32202.2, 'new_value': 32675.2}, {'field': 'order_count', 'old_value': 136, 'new_value': 140}]
2025-04-28 12:00:56,484 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIB
2025-04-28 12:00:56,906 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIB
2025-04-28 12:00:56,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25020.9, 'new_value': 25369.7}, {'field': 'offline_amount', 'old_value': 55244.4, 'new_value': 55952.3}, {'field': 'total_amount', 'old_value': 80265.3, 'new_value': 81322.0}, {'field': 'order_count', 'old_value': 223, 'new_value': 230}]
2025-04-28 12:00:56,906 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJB
2025-04-28 12:00:57,343 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJB
2025-04-28 12:00:57,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49592.0, 'new_value': 51939.0}, {'field': 'total_amount', 'old_value': 49592.0, 'new_value': 51939.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 65}]
2025-04-28 12:00:57,343 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLB
2025-04-28 12:00:57,843 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLB
2025-04-28 12:00:57,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38382.7, 'new_value': 38901.4}, {'field': 'total_amount', 'old_value': 38582.4, 'new_value': 39101.1}, {'field': 'order_count', 'old_value': 120, 'new_value': 122}]
2025-04-28 12:00:57,843 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNB
2025-04-28 12:00:58,328 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNB
2025-04-28 12:00:58,328 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12446.92, 'new_value': 247452.72}, {'field': 'offline_amount', 'old_value': 141246.0, 'new_value': 157748.0}, {'field': 'total_amount', 'old_value': 153692.92, 'new_value': 405200.72}, {'field': 'order_count', 'old_value': 113, 'new_value': 166}]
2025-04-28 12:00:58,328 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOB
2025-04-28 12:00:58,796 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOB
2025-04-28 12:00:58,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136445.28, 'new_value': 138717.28}, {'field': 'total_amount', 'old_value': 136445.28, 'new_value': 138717.28}, {'field': 'order_count', 'old_value': 122, 'new_value': 123}]
2025-04-28 12:00:58,796 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPB
2025-04-28 12:00:59,218 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPB
2025-04-28 12:00:59,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 272931.0, 'new_value': 280731.0}, {'field': 'total_amount', 'old_value': 401551.0, 'new_value': 409351.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 86}]
2025-04-28 12:00:59,218 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQB
2025-04-28 12:00:59,656 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQB
2025-04-28 12:00:59,656 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53615.8, 'new_value': 53715.8}, {'field': 'total_amount', 'old_value': 53615.8, 'new_value': 53715.8}, {'field': 'order_count', 'old_value': 290, 'new_value': 292}]
2025-04-28 12:00:59,656 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRB
2025-04-28 12:01:00,109 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRB
2025-04-28 12:01:00,109 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 180650.0, 'new_value': 229150.0}, {'field': 'total_amount', 'old_value': 437121.0, 'new_value': 485621.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 110}]
2025-04-28 12:01:00,109 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTB
2025-04-28 12:01:00,578 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTB
2025-04-28 12:01:00,578 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 148500.0, 'new_value': 198900.0}, {'field': 'total_amount', 'old_value': 357721.0, 'new_value': 408121.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 96}]
2025-04-28 12:01:00,578 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXB
2025-04-28 12:01:01,015 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXB
2025-04-28 12:01:01,015 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76014.03, 'new_value': 79966.52}, {'field': 'offline_amount', 'old_value': 109147.68, 'new_value': 113483.92}, {'field': 'total_amount', 'old_value': 185161.71, 'new_value': 193450.44}, {'field': 'order_count', 'old_value': 6390, 'new_value': 6705}]
2025-04-28 12:01:01,015 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS5
2025-04-28 12:01:01,515 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS5
2025-04-28 12:01:01,515 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31858.0, 'new_value': 33203.0}, {'field': 'total_amount', 'old_value': 33010.0, 'new_value': 34355.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 75}]
2025-04-28 12:01:01,531 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT5
2025-04-28 12:01:02,000 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT5
2025-04-28 12:01:02,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14531.56, 'new_value': 15065.05}, {'field': 'offline_amount', 'old_value': 151902.52, 'new_value': 157989.03}, {'field': 'total_amount', 'old_value': 166434.08, 'new_value': 173054.08}, {'field': 'order_count', 'old_value': 4111, 'new_value': 4260}]
2025-04-28 12:01:02,000 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU5
2025-04-28 12:01:02,625 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU5
2025-04-28 12:01:02,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60789.0, 'new_value': 66788.0}, {'field': 'total_amount', 'old_value': 60789.0, 'new_value': 66788.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-04-28 12:01:02,625 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY5
2025-04-28 12:01:03,093 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY5
2025-04-28 12:01:03,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225251.9, 'new_value': 230733.7}, {'field': 'total_amount', 'old_value': 230432.1, 'new_value': 235913.9}, {'field': 'order_count', 'old_value': 2645, 'new_value': 2702}]
2025-04-28 12:01:03,093 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-04-28 12:01:03,562 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-04-28 12:01:03,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 226261.0, 'new_value': 231146.0}, {'field': 'total_amount', 'old_value': 226261.0, 'new_value': 231146.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 60}]
2025-04-28 12:01:03,562 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M46
2025-04-28 12:01:03,968 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M46
2025-04-28 12:01:03,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78306.3, 'new_value': 87520.42}, {'field': 'total_amount', 'old_value': 316878.91, 'new_value': 326093.03}, {'field': 'order_count', 'old_value': 604, 'new_value': 621}]
2025-04-28 12:01:03,984 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M66
2025-04-28 12:01:04,515 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M66
2025-04-28 12:01:04,515 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2655.3, 'new_value': 3262.3}, {'field': 'offline_amount', 'old_value': 116740.1, 'new_value': 119780.8}, {'field': 'total_amount', 'old_value': 119395.4, 'new_value': 123043.1}, {'field': 'order_count', 'old_value': 532, 'new_value': 554}]
2025-04-28 12:01:04,515 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M86
2025-04-28 12:01:05,000 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M86
2025-04-28 12:01:05,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59914.62, 'new_value': 62826.43}, {'field': 'offline_amount', 'old_value': 68556.39, 'new_value': 70215.32}, {'field': 'total_amount', 'old_value': 128471.01, 'new_value': 133041.75}, {'field': 'order_count', 'old_value': 5593, 'new_value': 5813}]
2025-04-28 12:01:05,000 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M96
2025-04-28 12:01:05,531 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M96
2025-04-28 12:01:05,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4938600.0, 'new_value': 5338500.0}, {'field': 'total_amount', 'old_value': 4938600.0, 'new_value': 5338500.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-04-28 12:01:05,531 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF6
2025-04-28 12:01:05,953 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF6
2025-04-28 12:01:05,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5468.0, 'new_value': 5539.0}, {'field': 'offline_amount', 'old_value': 373352.0, 'new_value': 391234.0}, {'field': 'total_amount', 'old_value': 378820.0, 'new_value': 396773.0}, {'field': 'order_count', 'old_value': 208, 'new_value': 218}]
2025-04-28 12:01:05,953 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG6
2025-04-28 12:01:06,437 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG6
2025-04-28 12:01:06,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63164.0, 'new_value': 65168.0}, {'field': 'total_amount', 'old_value': 63164.0, 'new_value': 65168.0}, {'field': 'order_count', 'old_value': 158, 'new_value': 165}]
2025-04-28 12:01:06,437 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI6
2025-04-28 12:01:06,906 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI6
2025-04-28 12:01:06,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 222027.0, 'new_value': 235829.0}, {'field': 'total_amount', 'old_value': 246902.0, 'new_value': 260704.0}, {'field': 'order_count', 'old_value': 5404, 'new_value': 5429}]
2025-04-28 12:01:06,906 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK6
2025-04-28 12:01:07,359 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK6
2025-04-28 12:01:07,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 327867.69, 'new_value': 339274.81}, {'field': 'total_amount', 'old_value': 327867.69, 'new_value': 339274.81}, {'field': 'order_count', 'old_value': 8480, 'new_value': 8798}]
2025-04-28 12:01:07,359 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM6
2025-04-28 12:01:07,828 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM6
2025-04-28 12:01:07,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27199.0, 'new_value': 27828.0}, {'field': 'total_amount', 'old_value': 27199.0, 'new_value': 27828.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-04-28 12:01:07,828 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN6
2025-04-28 12:01:08,406 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN6
2025-04-28 12:01:08,406 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35324.05, 'new_value': 36756.83}, {'field': 'offline_amount', 'old_value': 273377.8, 'new_value': 285132.2}, {'field': 'total_amount', 'old_value': 308701.85, 'new_value': 321889.03}, {'field': 'order_count', 'old_value': 9304, 'new_value': 9731}]
2025-04-28 12:01:08,406 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR6
2025-04-28 12:01:08,843 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR6
2025-04-28 12:01:08,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59465.0, 'new_value': 62144.0}, {'field': 'total_amount', 'old_value': 59465.0, 'new_value': 62144.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 99}]
2025-04-28 12:01:08,843 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS6
2025-04-28 12:01:09,281 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS6
2025-04-28 12:01:09,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24764.3, 'new_value': 25959.41}, {'field': 'offline_amount', 'old_value': 55079.6, 'new_value': 57366.18}, {'field': 'total_amount', 'old_value': 79843.9, 'new_value': 83325.59}, {'field': 'order_count', 'old_value': 703, 'new_value': 734}]
2025-04-28 12:01:09,281 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT6
2025-04-28 12:01:09,703 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT6
2025-04-28 12:01:09,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192309.0, 'new_value': 199904.0}, {'field': 'total_amount', 'old_value': 192309.0, 'new_value': 199904.0}, {'field': 'order_count', 'old_value': 263, 'new_value': 276}]
2025-04-28 12:01:09,703 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX6
2025-04-28 12:01:10,171 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX6
2025-04-28 12:01:10,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 410644.0, 'new_value': 422536.0}, {'field': 'total_amount', 'old_value': 410644.0, 'new_value': 422536.0}, {'field': 'order_count', 'old_value': 11785, 'new_value': 12169}]
2025-04-28 12:01:10,171 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ6
2025-04-28 12:01:10,593 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ6
2025-04-28 12:01:10,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23345.1, 'new_value': 23912.1}, {'field': 'total_amount', 'old_value': 26846.2, 'new_value': 27413.2}, {'field': 'order_count', 'old_value': 313, 'new_value': 315}]
2025-04-28 12:01:10,593 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M07
2025-04-28 12:01:10,999 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M07
2025-04-28 12:01:10,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90780.34, 'new_value': 95044.3}, {'field': 'total_amount', 'old_value': 97029.44, 'new_value': 101293.4}, {'field': 'order_count', 'old_value': 2412, 'new_value': 2517}]
2025-04-28 12:01:10,999 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M67
2025-04-28 12:01:11,421 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M67
2025-04-28 12:01:11,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 543581.27, 'new_value': 557461.67}, {'field': 'total_amount', 'old_value': 543581.27, 'new_value': 557461.67}, {'field': 'order_count', 'old_value': 5476, 'new_value': 5638}]
2025-04-28 12:01:11,421 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M77
2025-04-28 12:01:11,921 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M77
2025-04-28 12:01:11,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8233.0, 'new_value': 8498.0}, {'field': 'total_amount', 'old_value': 8234.0, 'new_value': 8499.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 61}]
2025-04-28 12:01:11,921 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M87
2025-04-28 12:01:12,359 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M87
2025-04-28 12:01:12,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38833.0, 'new_value': 41402.0}, {'field': 'total_amount', 'old_value': 42509.0, 'new_value': 45078.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 83}]
2025-04-28 12:01:12,359 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB7
2025-04-28 12:01:12,796 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB7
2025-04-28 12:01:12,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118222.0, 'new_value': 120270.0}, {'field': 'total_amount', 'old_value': 118222.0, 'new_value': 120270.0}, {'field': 'order_count', 'old_value': 3770, 'new_value': 3832}]
2025-04-28 12:01:12,796 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC7
2025-04-28 12:01:13,249 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC7
2025-04-28 12:01:13,249 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 553496.7, 'new_value': 580061.09}, {'field': 'total_amount', 'old_value': 636365.21, 'new_value': 662929.6}, {'field': 'order_count', 'old_value': 4676, 'new_value': 4869}]
2025-04-28 12:01:13,249 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME7
2025-04-28 12:01:13,656 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME7
2025-04-28 12:01:13,656 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 701073.0, 'new_value': 722029.0}, {'field': 'total_amount', 'old_value': 701073.0, 'new_value': 722029.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 77}]
2025-04-28 12:01:13,656 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI7
2025-04-28 12:01:14,203 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI7
2025-04-28 12:01:14,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1127237.0, 'new_value': 1161391.0}, {'field': 'total_amount', 'old_value': 1127237.0, 'new_value': 1161391.0}, {'field': 'order_count', 'old_value': 1180, 'new_value': 1211}]
2025-04-28 12:01:14,203 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM7
2025-04-28 12:01:14,609 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM7
2025-04-28 12:01:14,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199513.31, 'new_value': 204492.71}, {'field': 'total_amount', 'old_value': 199513.31, 'new_value': 204492.71}, {'field': 'order_count', 'old_value': 649, 'new_value': 664}]
2025-04-28 12:01:14,609 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP7
2025-04-28 12:01:15,046 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP7
2025-04-28 12:01:15,046 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130944.9, 'new_value': 136657.4}, {'field': 'total_amount', 'old_value': 130944.9, 'new_value': 136657.4}, {'field': 'order_count', 'old_value': 244, 'new_value': 253}]
2025-04-28 12:01:15,046 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS7
2025-04-28 12:01:15,484 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS7
2025-04-28 12:01:15,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 567235.0, 'new_value': 604872.0}, {'field': 'total_amount', 'old_value': 567235.0, 'new_value': 604872.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 97}]
2025-04-28 12:01:15,484 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT7
2025-04-28 12:01:15,906 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT7
2025-04-28 12:01:15,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40061.0, 'new_value': 40775.0}, {'field': 'total_amount', 'old_value': 40061.0, 'new_value': 40775.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 131}]
2025-04-28 12:01:15,906 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV7
2025-04-28 12:01:16,499 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV7
2025-04-28 12:01:16,499 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80237.7, 'new_value': 83370.69}, {'field': 'offline_amount', 'old_value': 258376.68, 'new_value': 269367.63}, {'field': 'total_amount', 'old_value': 338614.38, 'new_value': 352738.32}, {'field': 'order_count', 'old_value': 16238, 'new_value': 16963}]
2025-04-28 12:01:16,499 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW7
2025-04-28 12:01:17,015 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW7
2025-04-28 12:01:17,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68995.5, 'new_value': 74994.5}, {'field': 'total_amount', 'old_value': 93238.24, 'new_value': 99237.24}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-04-28 12:01:17,015 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M48
2025-04-28 12:01:17,421 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M48
2025-04-28 12:01:17,421 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151474.79, 'new_value': 157514.51}, {'field': 'offline_amount', 'old_value': 425408.17, 'new_value': 439179.17}, {'field': 'total_amount', 'old_value': 576882.96, 'new_value': 596693.68}, {'field': 'order_count', 'old_value': 2891, 'new_value': 3085}]
2025-04-28 12:01:17,421 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M58
2025-04-28 12:01:17,828 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M58
2025-04-28 12:01:17,828 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 145157.99, 'new_value': 150239.63}, {'field': 'offline_amount', 'old_value': 114909.92, 'new_value': 117358.92}, {'field': 'total_amount', 'old_value': 260067.91, 'new_value': 267598.55}, {'field': 'order_count', 'old_value': 2607, 'new_value': 2691}]
2025-04-28 12:01:17,828 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M68
2025-04-28 12:01:18,374 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M68
2025-04-28 12:01:18,374 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55887.1, 'new_value': 61985.3}, {'field': 'offline_amount', 'old_value': 540512.6, 'new_value': 542260.6}, {'field': 'total_amount', 'old_value': 596399.7, 'new_value': 604245.9}, {'field': 'order_count', 'old_value': 1265, 'new_value': 1333}]
2025-04-28 12:01:18,374 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M98
2025-04-28 12:01:18,843 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M98
2025-04-28 12:01:18,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135532.29, 'new_value': 141273.25}, {'field': 'total_amount', 'old_value': 177246.19, 'new_value': 182987.15}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-04-28 12:01:18,843 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBH
2025-04-28 12:01:19,328 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBH
2025-04-28 12:01:19,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 596172.98, 'new_value': 613748.98}, {'field': 'total_amount', 'old_value': 596172.98, 'new_value': 613748.98}, {'field': 'order_count', 'old_value': 6307, 'new_value': 6529}]
2025-04-28 12:01:19,328 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEH
2025-04-28 12:01:19,765 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEH
2025-04-28 12:01:19,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9304.88, 'new_value': 9547.05}, {'field': 'offline_amount', 'old_value': 61980.82, 'new_value': 64126.37}, {'field': 'total_amount', 'old_value': 71285.7, 'new_value': 73673.42}, {'field': 'order_count', 'old_value': 2886, 'new_value': 2981}]
2025-04-28 12:01:19,765 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFH
2025-04-28 12:01:20,218 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFH
2025-04-28 12:01:20,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 671481.29, 'new_value': 690965.97}, {'field': 'total_amount', 'old_value': 672011.5, 'new_value': 691496.18}, {'field': 'order_count', 'old_value': 1583, 'new_value': 1636}]
2025-04-28 12:01:20,218 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJH
2025-04-28 12:01:20,671 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJH
2025-04-28 12:01:20,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193783.01, 'new_value': 199202.71}, {'field': 'total_amount', 'old_value': 201295.31, 'new_value': 206715.01}, {'field': 'order_count', 'old_value': 373, 'new_value': 387}]
2025-04-28 12:01:20,671 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPH
2025-04-28 12:01:21,171 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPH
2025-04-28 12:01:21,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104388.0, 'new_value': 107908.0}, {'field': 'total_amount', 'old_value': 107746.0, 'new_value': 111266.0}, {'field': 'order_count', 'old_value': 175, 'new_value': 201}]
2025-04-28 12:01:21,171 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWH
2025-04-28 12:01:21,734 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWH
2025-04-28 12:01:21,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237175.48, 'new_value': 248003.23}, {'field': 'total_amount', 'old_value': 237175.48, 'new_value': 248003.23}, {'field': 'order_count', 'old_value': 1314, 'new_value': 1374}]
2025-04-28 12:01:21,734 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXH
2025-04-28 12:01:22,156 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXH
2025-04-28 12:01:22,156 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 522922.28, 'new_value': 537906.28}, {'field': 'total_amount', 'old_value': 528619.28, 'new_value': 543603.28}, {'field': 'order_count', 'old_value': 362, 'new_value': 373}]
2025-04-28 12:01:22,156 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3I
2025-04-28 12:01:22,578 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3I
2025-04-28 12:01:22,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 639363.0, 'new_value': 644722.0}, {'field': 'total_amount', 'old_value': 639363.0, 'new_value': 644722.0}, {'field': 'order_count', 'old_value': 137, 'new_value': 141}]
2025-04-28 12:01:22,578 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4I
2025-04-28 12:01:23,031 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4I
2025-04-28 12:01:23,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 572993.45, 'new_value': 595364.45}, {'field': 'total_amount', 'old_value': 572993.45, 'new_value': 595364.45}, {'field': 'order_count', 'old_value': 1945, 'new_value': 2004}]
2025-04-28 12:01:23,031 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7I
2025-04-28 12:01:23,468 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7I
2025-04-28 12:01:23,468 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71488.78, 'new_value': 75632.32}, {'field': 'offline_amount', 'old_value': 1292400.14, 'new_value': 1337633.71}, {'field': 'total_amount', 'old_value': 1363888.92, 'new_value': 1413266.03}, {'field': 'order_count', 'old_value': 9482, 'new_value': 9883}]
2025-04-28 12:01:23,468 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9I
2025-04-28 12:01:23,953 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9I
2025-04-28 12:01:23,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 339000.0, 'new_value': 303000.0}, {'field': 'total_amount', 'old_value': 339000.0, 'new_value': 303000.0}]
2025-04-28 12:01:23,953 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBI
2025-04-28 12:01:24,406 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBI
2025-04-28 12:01:24,406 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1269.1, 'new_value': 1626.6}, {'field': 'offline_amount', 'old_value': 27039.8, 'new_value': 27930.71}, {'field': 'total_amount', 'old_value': 28308.9, 'new_value': 29557.31}, {'field': 'order_count', 'old_value': 1177, 'new_value': 1214}]
2025-04-28 12:01:24,406 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCI
2025-04-28 12:01:24,843 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCI
2025-04-28 12:01:24,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 815687.84, 'new_value': 842643.88}, {'field': 'total_amount', 'old_value': 815687.84, 'new_value': 842643.88}, {'field': 'order_count', 'old_value': 5561, 'new_value': 5792}]
2025-04-28 12:01:24,843 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEI
2025-04-28 12:01:25,343 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEI
2025-04-28 12:01:25,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 154797.0, 'new_value': 162247.0}, {'field': 'offline_amount', 'old_value': 116103.0, 'new_value': 120538.0}, {'field': 'total_amount', 'old_value': 270900.0, 'new_value': 282785.0}, {'field': 'order_count', 'old_value': 10356, 'new_value': 10890}]
2025-04-28 12:01:25,343 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLI
2025-04-28 12:01:25,781 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLI
2025-04-28 12:01:25,781 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112280.26, 'new_value': 118616.56}, {'field': 'offline_amount', 'old_value': 112794.15, 'new_value': 116465.45}, {'field': 'total_amount', 'old_value': 225074.41, 'new_value': 235082.01}, {'field': 'order_count', 'old_value': 5161, 'new_value': 5382}]
2025-04-28 12:01:25,781 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQI
2025-04-28 12:01:26,265 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQI
2025-04-28 12:01:26,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 217215.85, 'new_value': 225959.85}, {'field': 'total_amount', 'old_value': 217215.85, 'new_value': 225959.85}, {'field': 'order_count', 'old_value': 9456, 'new_value': 9889}]
2025-04-28 12:01:26,265 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRI
2025-04-28 12:01:26,781 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRI
2025-04-28 12:01:26,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107029.81, 'new_value': 111250.81}, {'field': 'total_amount', 'old_value': 118546.81, 'new_value': 122767.81}, {'field': 'order_count', 'old_value': 938, 'new_value': 970}]
2025-04-28 12:01:26,781 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSI
2025-04-28 12:01:27,234 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSI
2025-04-28 12:01:27,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1719490.0, 'new_value': 1789078.0}, {'field': 'total_amount', 'old_value': 1719490.0, 'new_value': 1789078.0}, {'field': 'order_count', 'old_value': 7027, 'new_value': 7292}]
2025-04-28 12:01:27,234 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MUI
2025-04-28 12:01:27,718 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MUI
2025-04-28 12:01:27,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71000.0, 'new_value': 101000.0}, {'field': 'total_amount', 'old_value': 71000.0, 'new_value': 101000.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-04-28 12:01:27,718 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYI
2025-04-28 12:01:28,093 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYI
2025-04-28 12:01:28,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34975.24, 'new_value': 36596.24}, {'field': 'total_amount', 'old_value': 35075.44, 'new_value': 36696.44}, {'field': 'order_count', 'old_value': 283, 'new_value': 298}]
2025-04-28 12:01:28,093 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0J
2025-04-28 12:01:28,546 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0J
2025-04-28 12:01:28,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129569.02, 'new_value': 134641.22}, {'field': 'total_amount', 'old_value': 152836.4, 'new_value': 157908.6}, {'field': 'order_count', 'old_value': 12079, 'new_value': 12327}]
2025-04-28 12:01:28,546 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1J
2025-04-28 12:01:28,999 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1J
2025-04-28 12:01:28,999 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 977534.0, 'new_value': 1035202.0}, {'field': 'offline_amount', 'old_value': 491661.0, 'new_value': 511528.0}, {'field': 'total_amount', 'old_value': 1469195.0, 'new_value': 1546730.0}, {'field': 'order_count', 'old_value': 1213, 'new_value': 1282}]
2025-04-28 12:01:28,999 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2J
2025-04-28 12:01:29,421 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2J
2025-04-28 12:01:29,421 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 100.0}, {'field': 'total_amount', 'old_value': 127308.81, 'new_value': 127408.81}, {'field': 'order_count', 'old_value': 76, 'new_value': 77}]
2025-04-28 12:01:29,421 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8J
2025-04-28 12:01:29,874 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8J
2025-04-28 12:01:29,874 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4757.0, 'new_value': 4987.0}, {'field': 'offline_amount', 'old_value': 22469.0, 'new_value': 23969.0}, {'field': 'total_amount', 'old_value': 27226.0, 'new_value': 28956.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 142}]
2025-04-28 12:01:29,874 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGJ
2025-04-28 12:01:30,374 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGJ
2025-04-28 12:01:30,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157901.0, 'new_value': 164617.0}, {'field': 'total_amount', 'old_value': 157901.0, 'new_value': 164617.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 35}]
2025-04-28 12:01:30,374 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHJ
2025-04-28 12:01:30,796 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHJ
2025-04-28 12:01:30,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12640000.0, 'new_value': 13040000.0}, {'field': 'total_amount', 'old_value': 12640001.0, 'new_value': 13040001.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 66}]
2025-04-28 12:01:30,796 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIJ
2025-04-28 12:01:31,202 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIJ
2025-04-28 12:01:31,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 901663.2, 'new_value': 949668.9}, {'field': 'total_amount', 'old_value': 901663.2, 'new_value': 949668.9}, {'field': 'order_count', 'old_value': 1152, 'new_value': 1215}]
2025-04-28 12:01:31,202 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLJ
2025-04-28 12:01:31,640 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLJ
2025-04-28 12:01:31,640 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 210148.45, 'new_value': 216974.91}, {'field': 'offline_amount', 'old_value': 42416.57, 'new_value': 43000.97}, {'field': 'total_amount', 'old_value': 252565.02, 'new_value': 259975.88}, {'field': 'order_count', 'old_value': 12236, 'new_value': 12475}]
2025-04-28 12:01:31,640 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-04-28 12:01:32,109 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-04-28 12:01:32,109 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54869.44, 'new_value': 56613.36}, {'field': 'offline_amount', 'old_value': 52967.55, 'new_value': 54302.13}, {'field': 'total_amount', 'old_value': 107836.99, 'new_value': 110915.49}, {'field': 'order_count', 'old_value': 2688, 'new_value': 2779}]
2025-04-28 12:01:32,109 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSJ
2025-04-28 12:01:32,546 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSJ
2025-04-28 12:01:32,546 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1622362.12, 'new_value': 1686143.62}, {'field': 'offline_amount', 'old_value': 282962.0, 'new_value': 283352.0}, {'field': 'total_amount', 'old_value': 1905324.12, 'new_value': 1969495.62}, {'field': 'order_count', 'old_value': 9346, 'new_value': 9602}]
2025-04-28 12:01:32,546 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXJ
2025-04-28 12:01:33,015 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXJ
2025-04-28 12:01:33,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46374.0, 'new_value': 47050.0}, {'field': 'total_amount', 'old_value': 46374.0, 'new_value': 47050.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 113}]
2025-04-28 12:01:33,015 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZJ
2025-04-28 12:01:33,484 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZJ
2025-04-28 12:01:33,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46110.0, 'new_value': 50310.0}, {'field': 'total_amount', 'old_value': 46110.0, 'new_value': 50310.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-04-28 12:01:33,484 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD
2025-04-28 12:01:33,952 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD
2025-04-28 12:01:33,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66179.0, 'new_value': 78859.0}, {'field': 'total_amount', 'old_value': 66179.0, 'new_value': 78859.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-04-28 12:01:33,952 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF
2025-04-28 12:01:34,359 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF
2025-04-28 12:01:34,359 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17686.4, 'new_value': 18339.1}, {'field': 'offline_amount', 'old_value': 191062.9, 'new_value': 200115.3}, {'field': 'total_amount', 'old_value': 208749.3, 'new_value': 218454.4}, {'field': 'order_count', 'old_value': 6481, 'new_value': 6751}]
2025-04-28 12:01:34,359 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG
2025-04-28 12:01:34,781 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG
2025-04-28 12:01:34,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1033956.0, 'new_value': 1053112.0}, {'field': 'total_amount', 'old_value': 1034121.0, 'new_value': 1053277.0}, {'field': 'order_count', 'old_value': 1201, 'new_value': 1224}]
2025-04-28 12:01:34,781 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI
2025-04-28 12:01:35,218 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI
2025-04-28 12:01:35,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 454820.8, 'new_value': 472726.14}, {'field': 'total_amount', 'old_value': 454820.8, 'new_value': 472726.14}, {'field': 'order_count', 'old_value': 3489, 'new_value': 3614}]
2025-04-28 12:01:35,218 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ
2025-04-28 12:01:35,718 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ
2025-04-28 12:01:35,718 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74572.28, 'new_value': 78226.97}, {'field': 'offline_amount', 'old_value': 913349.8, 'new_value': 944374.97}, {'field': 'total_amount', 'old_value': 963908.88, 'new_value': 994934.05}, {'field': 'order_count', 'old_value': 3916, 'new_value': 4095}]
2025-04-28 12:01:35,718 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM
2025-04-28 12:01:36,249 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM
2025-04-28 12:01:36,249 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49995.4, 'new_value': 52358.4}, {'field': 'total_amount', 'old_value': 49995.4, 'new_value': 52358.4}, {'field': 'order_count', 'old_value': 275, 'new_value': 287}]
2025-04-28 12:01:36,249 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN
2025-04-28 12:01:36,734 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN
2025-04-28 12:01:36,734 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30319.92, 'new_value': 30955.02}, {'field': 'total_amount', 'old_value': 30319.92, 'new_value': 30955.02}, {'field': 'order_count', 'old_value': 947, 'new_value': 984}]
2025-04-28 12:01:36,734 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MR
2025-04-28 12:01:37,171 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MR
2025-04-28 12:01:37,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51847.0, 'new_value': 51908.0}, {'field': 'total_amount', 'old_value': 51847.0, 'new_value': 51908.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 55}]
2025-04-28 12:01:37,171 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT
2025-04-28 12:01:37,656 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT
2025-04-28 12:01:37,656 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112219.78, 'new_value': 115715.86}, {'field': 'offline_amount', 'old_value': 127810.9, 'new_value': 131530.85}, {'field': 'total_amount', 'old_value': 240030.68, 'new_value': 247246.71}, {'field': 'order_count', 'old_value': 6174, 'new_value': 6364}]
2025-04-28 12:01:37,656 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX
2025-04-28 12:01:38,093 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX
2025-04-28 12:01:38,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18994.9, 'new_value': 19148.9}, {'field': 'total_amount', 'old_value': 18994.9, 'new_value': 19148.9}, {'field': 'order_count', 'old_value': 120, 'new_value': 125}]
2025-04-28 12:01:38,109 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M31
2025-04-28 12:01:38,624 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M31
2025-04-28 12:01:38,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 529393.5, 'new_value': 563227.4}, {'field': 'total_amount', 'old_value': 529393.5, 'new_value': 563227.4}, {'field': 'order_count', 'old_value': 172, 'new_value': 181}]
2025-04-28 12:01:38,624 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M41
2025-04-28 12:01:39,234 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M41
2025-04-28 12:01:39,234 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 14, 'new_value': 10}]
2025-04-28 12:01:39,234 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M71
2025-04-28 12:01:39,671 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M71
2025-04-28 12:01:39,671 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82747.2, 'new_value': 85914.85}, {'field': 'offline_amount', 'old_value': 247962.55, 'new_value': 257083.59}, {'field': 'total_amount', 'old_value': 330709.75, 'new_value': 342998.44}, {'field': 'order_count', 'old_value': 2246, 'new_value': 2337}]
2025-04-28 12:01:39,671 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB1
2025-04-28 12:01:40,109 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB1
2025-04-28 12:01:40,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201693.0, 'new_value': 208678.0}, {'field': 'total_amount', 'old_value': 217567.0, 'new_value': 224552.0}, {'field': 'order_count', 'old_value': 306, 'new_value': 314}]
2025-04-28 12:01:40,109 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME1
2025-04-28 12:01:40,609 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME1
2025-04-28 12:01:40,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155642.9, 'new_value': 163077.9}, {'field': 'total_amount', 'old_value': 155642.9, 'new_value': 163077.9}, {'field': 'order_count', 'old_value': 16558, 'new_value': 17351}]
2025-04-28 12:01:40,609 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF1
2025-04-28 12:01:41,093 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF1
2025-04-28 12:01:41,093 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54305.07, 'new_value': 56150.42}, {'field': 'offline_amount', 'old_value': 65520.61, 'new_value': 68099.4}, {'field': 'total_amount', 'old_value': 119825.68, 'new_value': 124249.82}, {'field': 'order_count', 'old_value': 6051, 'new_value': 6280}]
2025-04-28 12:01:41,093 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ1
2025-04-28 12:01:41,687 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ1
2025-04-28 12:01:41,687 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5005.0, 'new_value': 5437.0}, {'field': 'offline_amount', 'old_value': 26872.0, 'new_value': 27580.0}, {'field': 'total_amount', 'old_value': 31877.0, 'new_value': 33017.0}, {'field': 'order_count', 'old_value': 776, 'new_value': 817}]
2025-04-28 12:01:41,687 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MK1
2025-04-28 12:01:42,171 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MK1
2025-04-28 12:01:42,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88682.96, 'new_value': 91657.56}, {'field': 'total_amount', 'old_value': 88682.96, 'new_value': 91657.56}, {'field': 'order_count', 'old_value': 7933, 'new_value': 8245}]
2025-04-28 12:01:42,171 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM1
2025-04-28 12:01:42,640 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM1
2025-04-28 12:01:42,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194369.26, 'new_value': 203064.49}, {'field': 'total_amount', 'old_value': 194369.26, 'new_value': 203064.49}, {'field': 'order_count', 'old_value': 14290, 'new_value': 14850}]
2025-04-28 12:01:42,640 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN1
2025-04-28 12:01:43,077 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN1
2025-04-28 12:01:43,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46393.8, 'new_value': 48393.8}, {'field': 'total_amount', 'old_value': 52398.8, 'new_value': 54398.8}, {'field': 'order_count', 'old_value': 95, 'new_value': 100}]
2025-04-28 12:01:43,077 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO1
2025-04-28 12:01:43,577 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO1
2025-04-28 12:01:43,577 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225115.0, 'new_value': 235441.0}, {'field': 'total_amount', 'old_value': 225115.0, 'new_value': 235441.0}, {'field': 'order_count', 'old_value': 24262, 'new_value': 25358}]
2025-04-28 12:01:43,577 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ1
2025-04-28 12:01:44,093 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ1
2025-04-28 12:01:44,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 213227.5, 'new_value': 220294.81}, {'field': 'total_amount', 'old_value': 218228.51, 'new_value': 225295.82}, {'field': 'order_count', 'old_value': 3776, 'new_value': 3913}]
2025-04-28 12:01:44,093 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS1
2025-04-28 12:01:44,530 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS1
2025-04-28 12:01:44,530 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20339.87, 'new_value': 21220.9}, {'field': 'offline_amount', 'old_value': 30672.26, 'new_value': 31398.26}, {'field': 'total_amount', 'old_value': 51012.13, 'new_value': 52619.16}, {'field': 'order_count', 'old_value': 2513, 'new_value': 2605}]
2025-04-28 12:01:44,530 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT1
2025-04-28 12:01:45,046 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT1
2025-04-28 12:01:45,046 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192429.0, 'new_value': 200015.0}, {'field': 'total_amount', 'old_value': 195947.0, 'new_value': 203533.0}, {'field': 'order_count', 'old_value': 29214, 'new_value': 29381}]
2025-04-28 12:01:45,046 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ1
2025-04-28 12:01:45,468 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ1
2025-04-28 12:01:45,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95530.0, 'new_value': 98130.0}, {'field': 'total_amount', 'old_value': 95530.0, 'new_value': 98130.0}, {'field': 'order_count', 'old_value': 6510, 'new_value': 6709}]
2025-04-28 12:01:45,468 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M32
2025-04-28 12:01:45,952 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M32
2025-04-28 12:01:45,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190780.46, 'new_value': 191068.46}, {'field': 'total_amount', 'old_value': 193978.76, 'new_value': 194266.76}, {'field': 'order_count', 'old_value': 74, 'new_value': 76}]
2025-04-28 12:01:45,952 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M72
2025-04-28 12:01:46,452 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M72
2025-04-28 12:01:46,452 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42567.0, 'new_value': 44247.0}, {'field': 'total_amount', 'old_value': 42567.0, 'new_value': 44247.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-04-28 12:01:46,452 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9M92
2025-04-28 12:01:46,905 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9M92
2025-04-28 12:01:46,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57219.0, 'new_value': 61039.0}, {'field': 'total_amount', 'old_value': 57219.0, 'new_value': 61039.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-04-28 12:01:46,905 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MA2
2025-04-28 12:01:47,421 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MA2
2025-04-28 12:01:47,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29100.0, 'new_value': 31800.0}, {'field': 'total_amount', 'old_value': 29100.0, 'new_value': 31800.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-04-28 12:01:47,421 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MC2
2025-04-28 12:01:47,827 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MC2
2025-04-28 12:01:47,827 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 293256.19, 'new_value': 304207.62}, {'field': 'total_amount', 'old_value': 293256.19, 'new_value': 304207.62}, {'field': 'order_count', 'old_value': 849, 'new_value': 881}]
2025-04-28 12:01:47,827 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-04-28 12:01:48,249 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-04-28 12:01:48,249 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14331702.0, 'new_value': 15039947.0}, {'field': 'total_amount', 'old_value': 14331702.0, 'new_value': 15039947.0}, {'field': 'order_count', 'old_value': 42798, 'new_value': 44553}]
2025-04-28 12:01:48,249 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ML2
2025-04-28 12:01:48,827 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ML2
2025-04-28 12:01:48,827 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1111368.1, 'new_value': 1145775.33}, {'field': 'total_amount', 'old_value': 1111368.1, 'new_value': 1145775.33}, {'field': 'order_count', 'old_value': 3394, 'new_value': 3516}]
2025-04-28 12:01:48,827 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MN2
2025-04-28 12:01:49,312 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MN2
2025-04-28 12:01:49,312 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1056739.47, 'new_value': 1090191.0}, {'field': 'total_amount', 'old_value': 1056739.47, 'new_value': 1090191.0}, {'field': 'order_count', 'old_value': 3657, 'new_value': 3780}]
2025-04-28 12:01:49,312 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MP2
2025-04-28 12:01:49,843 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MP2
2025-04-28 12:01:49,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 719517.53, 'new_value': 740093.53}, {'field': 'total_amount', 'old_value': 719517.53, 'new_value': 740093.53}, {'field': 'order_count', 'old_value': 3468, 'new_value': 3589}]
2025-04-28 12:01:49,843 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MT2
2025-04-28 12:01:50,343 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MT2
2025-04-28 12:01:50,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 322138.0, 'new_value': 338206.0}, {'field': 'total_amount', 'old_value': 322138.0, 'new_value': 338206.0}, {'field': 'order_count', 'old_value': 6497, 'new_value': 6847}]
2025-04-28 12:01:50,343 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M16
2025-04-28 12:01:50,765 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M16
2025-04-28 12:01:50,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72314.81, 'new_value': 74693.88}, {'field': 'total_amount', 'old_value': 72314.81, 'new_value': 74693.88}, {'field': 'order_count', 'old_value': 1201, 'new_value': 1241}]
2025-04-28 12:01:50,765 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M36
2025-04-28 12:01:51,296 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M36
2025-04-28 12:01:51,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 206390.7, 'new_value': 215421.65}, {'field': 'total_amount', 'old_value': 206390.7, 'new_value': 215421.65}, {'field': 'order_count', 'old_value': 14599, 'new_value': 15234}]
2025-04-28 12:01:51,296 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M56
2025-04-28 12:01:51,812 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M56
2025-04-28 12:01:51,812 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23539.55, 'new_value': 24502.69}, {'field': 'offline_amount', 'old_value': 18086.55, 'new_value': 18871.55}, {'field': 'total_amount', 'old_value': 41626.1, 'new_value': 43374.24}, {'field': 'order_count', 'old_value': 2184, 'new_value': 2250}]
2025-04-28 12:01:51,812 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M66
2025-04-28 12:01:52,265 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M66
2025-04-28 12:01:52,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 294927.0, 'new_value': 305808.0}, {'field': 'total_amount', 'old_value': 294927.0, 'new_value': 305808.0}, {'field': 'order_count', 'old_value': 12337, 'new_value': 12433}]
2025-04-28 12:01:52,265 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M76
2025-04-28 12:01:52,734 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M76
2025-04-28 12:01:52,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 730099.19, 'new_value': 751451.69}, {'field': 'total_amount', 'old_value': 730099.19, 'new_value': 751451.69}, {'field': 'order_count', 'old_value': 5990, 'new_value': 6177}]
2025-04-28 12:01:52,734 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M86
2025-04-28 12:01:53,140 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M86
2025-04-28 12:01:53,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128453.17, 'new_value': 131376.83}, {'field': 'total_amount', 'old_value': 128453.17, 'new_value': 131376.83}, {'field': 'order_count', 'old_value': 3327, 'new_value': 3415}]
2025-04-28 12:01:53,140 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA6
2025-04-28 12:01:53,624 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA6
2025-04-28 12:01:53,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15880.0, 'new_value': 19080.0}, {'field': 'total_amount', 'old_value': 15880.0, 'new_value': 19080.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-04-28 12:01:53,624 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB6
2025-04-28 12:01:54,109 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB6
2025-04-28 12:01:54,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29480.0, 'new_value': 48280.0}, {'field': 'total_amount', 'old_value': 39080.0, 'new_value': 57880.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-04-28 12:01:54,109 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC6
2025-04-28 12:01:54,546 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC6
2025-04-28 12:01:54,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177469.17, 'new_value': 185026.77}, {'field': 'total_amount', 'old_value': 177469.17, 'new_value': 185026.77}, {'field': 'order_count', 'old_value': 19610, 'new_value': 20429}]
2025-04-28 12:01:54,546 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD6
2025-04-28 12:01:54,968 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD6
2025-04-28 12:01:54,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15001.0, 'new_value': 15289.0}, {'field': 'total_amount', 'old_value': 15001.0, 'new_value': 15289.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-04-28 12:01:54,968 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF6
2025-04-28 12:01:55,499 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF6
2025-04-28 12:01:55,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1268130.0, 'new_value': 1307836.0}, {'field': 'total_amount', 'old_value': 1268130.0, 'new_value': 1307836.0}, {'field': 'order_count', 'old_value': 5302, 'new_value': 5505}]
2025-04-28 12:01:55,499 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG6
2025-04-28 12:01:55,905 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG6
2025-04-28 12:01:55,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46572.0, 'new_value': 48708.0}, {'field': 'total_amount', 'old_value': 46572.0, 'new_value': 48708.0}, {'field': 'order_count', 'old_value': 294, 'new_value': 307}]
2025-04-28 12:01:55,905 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI6
2025-04-28 12:01:56,343 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI6
2025-04-28 12:01:56,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 750059.17, 'new_value': 771896.17}, {'field': 'total_amount', 'old_value': 786128.15, 'new_value': 807965.15}, {'field': 'order_count', 'old_value': 2003, 'new_value': 2053}]
2025-04-28 12:01:56,343 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML6
2025-04-28 12:01:56,843 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML6
2025-04-28 12:01:56,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 368082.0, 'new_value': 377882.0}, {'field': 'total_amount', 'old_value': 368082.0, 'new_value': 377882.0}, {'field': 'order_count', 'old_value': 662, 'new_value': 684}]
2025-04-28 12:01:56,843 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM6
2025-04-28 12:01:57,249 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM6
2025-04-28 12:01:57,249 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60096.11, 'new_value': 64022.02}, {'field': 'total_amount', 'old_value': 60096.11, 'new_value': 64022.02}, {'field': 'order_count', 'old_value': 1009, 'new_value': 1077}]
2025-04-28 12:01:57,249 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO6
2025-04-28 12:01:57,874 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO6
2025-04-28 12:01:57,874 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17422.08, 'new_value': 18329.08}, {'field': 'offline_amount', 'old_value': 246733.0, 'new_value': 273080.0}, {'field': 'total_amount', 'old_value': 264155.08, 'new_value': 291409.08}, {'field': 'order_count', 'old_value': 1309, 'new_value': 1435}]
2025-04-28 12:01:57,874 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ6
2025-04-28 12:01:58,296 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ6
2025-04-28 12:01:58,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52159.0, 'new_value': 77909.0}, {'field': 'total_amount', 'old_value': 72891.0, 'new_value': 98641.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 36}]
2025-04-28 12:01:58,296 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR6
2025-04-28 12:01:58,749 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR6
2025-04-28 12:01:58,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67560.0, 'new_value': 72160.0}, {'field': 'total_amount', 'old_value': 72000.0, 'new_value': 76600.0}, {'field': 'order_count', 'old_value': 13450, 'new_value': 13451}]
2025-04-28 12:01:58,749 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT6
2025-04-28 12:01:59,202 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT6
2025-04-28 12:01:59,202 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91229.74, 'new_value': 94824.54}, {'field': 'offline_amount', 'old_value': 342014.64, 'new_value': 350386.04}, {'field': 'total_amount', 'old_value': 433244.38, 'new_value': 445210.58}, {'field': 'order_count', 'old_value': 3167, 'new_value': 3282}]
2025-04-28 12:01:59,202 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU6
2025-04-28 12:01:59,671 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU6
2025-04-28 12:01:59,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141498.96, 'new_value': 147653.96}, {'field': 'total_amount', 'old_value': 141498.96, 'new_value': 147653.96}, {'field': 'order_count', 'old_value': 3303, 'new_value': 3472}]
2025-04-28 12:01:59,671 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW6
2025-04-28 12:02:00,109 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW6
2025-04-28 12:02:00,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 587515.35, 'new_value': 607664.76}, {'field': 'total_amount', 'old_value': 587515.35, 'new_value': 607664.76}, {'field': 'order_count', 'old_value': 4828, 'new_value': 5016}]
2025-04-28 12:02:00,109 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MX6
2025-04-28 12:02:00,609 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MX6
2025-04-28 12:02:00,609 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22393.9, 'new_value': 25593.9}, {'field': 'offline_amount', 'old_value': 106283.0, 'new_value': 108046.0}, {'field': 'total_amount', 'old_value': 128676.9, 'new_value': 133639.9}, {'field': 'order_count', 'old_value': 2617, 'new_value': 2741}]
2025-04-28 12:02:00,609 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY6
2025-04-28 12:02:01,046 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY6
2025-04-28 12:02:01,046 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 278118.91, 'new_value': 288982.91}, {'field': 'total_amount', 'old_value': 278118.91, 'new_value': 288982.91}, {'field': 'order_count', 'old_value': 5875, 'new_value': 6125}]
2025-04-28 12:02:01,046 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M17
2025-04-28 12:02:01,468 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M17
2025-04-28 12:02:01,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 217024.43, 'new_value': 223004.43}, {'field': 'total_amount', 'old_value': 217024.43, 'new_value': 223004.43}, {'field': 'order_count', 'old_value': 8054, 'new_value': 8327}]
2025-04-28 12:02:01,468 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M27
2025-04-28 12:02:01,952 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M27
2025-04-28 12:02:01,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164540.0, 'new_value': 180275.0}, {'field': 'total_amount', 'old_value': 263512.0, 'new_value': 279247.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 69}]
2025-04-28 12:02:01,952 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M47
2025-04-28 12:02:02,390 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M47
2025-04-28 12:02:02,390 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133141.31, 'new_value': 138404.96}, {'field': 'total_amount', 'old_value': 133141.31, 'new_value': 138404.96}, {'field': 'order_count', 'old_value': 3132, 'new_value': 3248}]
2025-04-28 12:02:02,390 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M57
2025-04-28 12:02:02,984 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M57
2025-04-28 12:02:02,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65594.0, 'new_value': 65722.0}, {'field': 'total_amount', 'old_value': 65594.0, 'new_value': 65722.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-04-28 12:02:02,984 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M67
2025-04-28 12:02:03,437 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M67
2025-04-28 12:02:03,437 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103939.5, 'new_value': 106899.6}, {'field': 'offline_amount', 'old_value': 27818.4, 'new_value': 28612.9}, {'field': 'total_amount', 'old_value': 131757.9, 'new_value': 135512.5}, {'field': 'order_count', 'old_value': 11375, 'new_value': 11713}]
2025-04-28 12:02:03,437 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M87
2025-04-28 12:02:03,859 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M87
2025-04-28 12:02:03,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65910.85, 'new_value': 73323.85}, {'field': 'total_amount', 'old_value': 65910.85, 'new_value': 73323.85}, {'field': 'order_count', 'old_value': 163, 'new_value': 178}]
2025-04-28 12:02:03,859 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA7
2025-04-28 12:02:04,265 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA7
2025-04-28 12:02:04,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3514527.51, 'new_value': 3617930.66}, {'field': 'total_amount', 'old_value': 3514527.51, 'new_value': 3617930.66}, {'field': 'order_count', 'old_value': 6075, 'new_value': 6274}]
2025-04-28 12:02:04,265 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB7
2025-04-28 12:02:04,749 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB7
2025-04-28 12:02:04,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41387.92, 'new_value': 42987.29}, {'field': 'total_amount', 'old_value': 41387.92, 'new_value': 42987.29}, {'field': 'order_count', 'old_value': 1918, 'new_value': 1992}]
2025-04-28 12:02:04,749 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD7
2025-04-28 12:02:05,218 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD7
2025-04-28 12:02:05,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21999.0, 'new_value': 23887.0}, {'field': 'total_amount', 'old_value': 21999.0, 'new_value': 23887.0}, {'field': 'order_count', 'old_value': 211, 'new_value': 227}]
2025-04-28 12:02:05,218 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG7
2025-04-28 12:02:05,780 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG7
2025-04-28 12:02:05,780 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79194.0, 'new_value': 81551.0}, {'field': 'offline_amount', 'old_value': 143408.0, 'new_value': 148358.0}, {'field': 'total_amount', 'old_value': 222602.0, 'new_value': 229909.0}, {'field': 'order_count', 'old_value': 5356, 'new_value': 5528}]
2025-04-28 12:02:05,780 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK7
2025-04-28 12:02:06,187 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK7
2025-04-28 12:02:06,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53387.0, 'new_value': 64347.0}, {'field': 'total_amount', 'old_value': 62361.2, 'new_value': 73321.2}, {'field': 'order_count', 'old_value': 48, 'new_value': 50}]
2025-04-28 12:02:06,187 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML7
2025-04-28 12:02:06,593 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML7
2025-04-28 12:02:06,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162591.0, 'new_value': 174322.0}, {'field': 'total_amount', 'old_value': 165411.0, 'new_value': 177142.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 66}]
2025-04-28 12:02:06,593 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO7
2025-04-28 12:02:07,077 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO7
2025-04-28 12:02:07,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 355520.0, 'new_value': 369040.0}, {'field': 'total_amount', 'old_value': 355520.0, 'new_value': 369040.0}, {'field': 'order_count', 'old_value': 8812, 'new_value': 9172}]
2025-04-28 12:02:07,077 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ7
2025-04-28 12:02:07,499 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ7
2025-04-28 12:02:07,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80010.0, 'new_value': 81559.0}, {'field': 'total_amount', 'old_value': 80010.0, 'new_value': 81559.0}, {'field': 'order_count', 'old_value': 1417, 'new_value': 1461}]
2025-04-28 12:02:07,499 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA8
2025-04-28 12:02:07,968 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA8
2025-04-28 12:02:07,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76954.0, 'new_value': 80934.0}, {'field': 'total_amount', 'old_value': 76954.0, 'new_value': 80934.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-04-28 12:02:07,968 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK8
2025-04-28 12:02:08,437 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK8
2025-04-28 12:02:08,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47520.0, 'new_value': 51320.0}, {'field': 'total_amount', 'old_value': 47520.0, 'new_value': 51320.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 29}]
2025-04-28 12:02:08,437 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM8
2025-04-28 12:02:08,858 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM8
2025-04-28 12:02:08,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26110.0, 'new_value': 29610.0}, {'field': 'total_amount', 'old_value': 26110.0, 'new_value': 29610.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-04-28 12:02:08,858 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MV1
2025-04-28 12:02:09,374 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MV1
2025-04-28 12:02:09,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124311.0, 'new_value': 128704.0}, {'field': 'total_amount', 'old_value': 124311.0, 'new_value': 128704.0}, {'field': 'order_count', 'old_value': 1223, 'new_value': 1262}]
2025-04-28 12:02:09,374 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MW1
2025-04-28 12:02:09,843 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MW1
2025-04-28 12:02:09,843 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 296799.85, 'new_value': 307054.34}, {'field': 'offline_amount', 'old_value': 252537.33, 'new_value': 263713.85}, {'field': 'total_amount', 'old_value': 549337.18, 'new_value': 570768.19}, {'field': 'order_count', 'old_value': 15269, 'new_value': 15781}]
2025-04-28 12:02:09,843 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MY1
2025-04-28 12:02:10,296 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MY1
2025-04-28 12:02:10,296 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71618.19, 'new_value': 75354.77}, {'field': 'offline_amount', 'old_value': 77626.17, 'new_value': 79914.49}, {'field': 'total_amount', 'old_value': 149244.36, 'new_value': 155269.26}, {'field': 'order_count', 'old_value': 6172, 'new_value': 6425}]
2025-04-28 12:02:10,296 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-04-28 12:02:10,765 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-04-28 12:02:10,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151416.0, 'new_value': 154839.0}, {'field': 'total_amount', 'old_value': 169230.0, 'new_value': 172653.0}, {'field': 'order_count', 'old_value': 711, 'new_value': 736}]
2025-04-28 12:02:10,765 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-04-28 12:02:11,233 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-04-28 12:02:11,233 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 5602.0}, {'field': 'total_amount', 'old_value': 121864.0, 'new_value': 127466.0}, {'field': 'order_count', 'old_value': 421, 'new_value': 439}]
2025-04-28 12:02:11,233 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M32
2025-04-28 12:02:11,702 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M32
2025-04-28 12:02:11,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38595.0, 'new_value': 40395.0}, {'field': 'total_amount', 'old_value': 112927.0, 'new_value': 114727.0}, {'field': 'order_count', 'old_value': 193, 'new_value': 194}]
2025-04-28 12:02:11,702 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M52
2025-04-28 12:02:12,155 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M52
2025-04-28 12:02:12,155 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8050.8, 'new_value': 8060.7}, {'field': 'offline_amount', 'old_value': 43295.5, 'new_value': 43795.5}, {'field': 'total_amount', 'old_value': 51346.3, 'new_value': 51856.2}, {'field': 'order_count', 'old_value': 492, 'new_value': 494}]
2025-04-28 12:02:12,155 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M82
2025-04-28 12:02:12,624 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M82
2025-04-28 12:02:12,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14338803.18, 'new_value': 14890295.61}, {'field': 'total_amount', 'old_value': 14338803.18, 'new_value': 14890295.61}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-04-28 12:02:12,624 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-04-28 12:02:13,077 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-04-28 12:02:13,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22211.0, 'new_value': 29338.25}, {'field': 'total_amount', 'old_value': 22211.0, 'new_value': 29338.25}, {'field': 'order_count', 'old_value': 5, 'new_value': 24}]
2025-04-28 12:02:13,077 - INFO - 开始更新记录 - 表单实例ID: FINST-NLF66581Q4VURCQVEAQGU8UJUISX2V9JF4Z9MPF1
2025-04-28 12:02:13,515 - INFO - 更新表单数据成功: FINST-NLF66581Q4VURCQVEAQGU8UJUISX2V9JF4Z9MPF1
2025-04-28 12:02:13,515 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3608.51, 'new_value': 7333.76}, {'field': 'offline_amount', 'old_value': 3042.54, 'new_value': 10242.91}, {'field': 'total_amount', 'old_value': 6651.05, 'new_value': 17576.67}, {'field': 'order_count', 'old_value': 112, 'new_value': 414}]
2025-04-28 12:02:13,515 - INFO - 日期 2025-04 处理完成 - 更新: 235 条，插入: 0 条，错误: 0 条
2025-04-28 12:02:13,515 - INFO - 数据同步完成！更新: 235 条，插入: 0 条，错误: 0 条
2025-04-28 12:02:13,515 - INFO - =================同步完成====================
2025-04-28 15:00:01,869 - INFO - =================使用默认全量同步=============
2025-04-28 15:00:02,994 - INFO - MySQL查询成功，共获取 2639 条记录
2025-04-28 15:00:02,994 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-28 15:00:03,026 - INFO - 开始处理日期: 2025-01
2025-04-28 15:00:03,026 - INFO - Request Parameters - Page 1:
2025-04-28 15:00:03,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:03,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:03,854 - INFO - Response - Page 1:
2025-04-28 15:00:04,057 - INFO - 第 1 页获取到 100 条记录
2025-04-28 15:00:04,057 - INFO - Request Parameters - Page 2:
2025-04-28 15:00:04,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:04,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:04,697 - INFO - Response - Page 2:
2025-04-28 15:00:04,901 - INFO - 第 2 页获取到 100 条记录
2025-04-28 15:00:04,901 - INFO - Request Parameters - Page 3:
2025-04-28 15:00:04,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:04,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:05,354 - INFO - Response - Page 3:
2025-04-28 15:00:05,557 - INFO - 第 3 页获取到 100 条记录
2025-04-28 15:00:05,557 - INFO - Request Parameters - Page 4:
2025-04-28 15:00:05,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:05,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:06,010 - INFO - Response - Page 4:
2025-04-28 15:00:06,213 - INFO - 第 4 页获取到 100 条记录
2025-04-28 15:00:06,213 - INFO - Request Parameters - Page 5:
2025-04-28 15:00:06,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:06,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:06,666 - INFO - Response - Page 5:
2025-04-28 15:00:06,869 - INFO - 第 5 页获取到 100 条记录
2025-04-28 15:00:06,869 - INFO - Request Parameters - Page 6:
2025-04-28 15:00:06,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:06,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:07,385 - INFO - Response - Page 6:
2025-04-28 15:00:07,588 - INFO - 第 6 页获取到 100 条记录
2025-04-28 15:00:07,588 - INFO - Request Parameters - Page 7:
2025-04-28 15:00:07,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:07,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:08,010 - INFO - Response - Page 7:
2025-04-28 15:00:08,213 - INFO - 第 7 页获取到 82 条记录
2025-04-28 15:00:08,213 - INFO - 查询完成，共获取到 682 条记录
2025-04-28 15:00:08,213 - INFO - 获取到 682 条表单数据
2025-04-28 15:00:08,213 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-28 15:00:08,229 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 15:00:08,229 - INFO - 开始处理日期: 2025-02
2025-04-28 15:00:08,229 - INFO - Request Parameters - Page 1:
2025-04-28 15:00:08,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:08,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:08,713 - INFO - Response - Page 1:
2025-04-28 15:00:08,916 - INFO - 第 1 页获取到 100 条记录
2025-04-28 15:00:08,916 - INFO - Request Parameters - Page 2:
2025-04-28 15:00:08,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:08,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:09,416 - INFO - Response - Page 2:
2025-04-28 15:00:09,619 - INFO - 第 2 页获取到 100 条记录
2025-04-28 15:00:09,619 - INFO - Request Parameters - Page 3:
2025-04-28 15:00:09,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:09,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:10,088 - INFO - Response - Page 3:
2025-04-28 15:00:10,291 - INFO - 第 3 页获取到 100 条记录
2025-04-28 15:00:10,291 - INFO - Request Parameters - Page 4:
2025-04-28 15:00:10,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:10,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:10,729 - INFO - Response - Page 4:
2025-04-28 15:00:10,947 - INFO - 第 4 页获取到 100 条记录
2025-04-28 15:00:10,947 - INFO - Request Parameters - Page 5:
2025-04-28 15:00:10,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:10,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:11,604 - INFO - Response - Page 5:
2025-04-28 15:00:11,807 - INFO - 第 5 页获取到 100 条记录
2025-04-28 15:00:11,807 - INFO - Request Parameters - Page 6:
2025-04-28 15:00:11,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:11,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:12,291 - INFO - Response - Page 6:
2025-04-28 15:00:12,494 - INFO - 第 6 页获取到 100 条记录
2025-04-28 15:00:12,494 - INFO - Request Parameters - Page 7:
2025-04-28 15:00:12,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:12,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:12,901 - INFO - Response - Page 7:
2025-04-28 15:00:13,104 - INFO - 第 7 页获取到 70 条记录
2025-04-28 15:00:13,104 - INFO - 查询完成，共获取到 670 条记录
2025-04-28 15:00:13,104 - INFO - 获取到 670 条表单数据
2025-04-28 15:00:13,104 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-28 15:00:13,119 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 15:00:13,119 - INFO - 开始处理日期: 2025-03
2025-04-28 15:00:13,119 - INFO - Request Parameters - Page 1:
2025-04-28 15:00:13,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:13,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:13,572 - INFO - Response - Page 1:
2025-04-28 15:00:13,776 - INFO - 第 1 页获取到 100 条记录
2025-04-28 15:00:13,776 - INFO - Request Parameters - Page 2:
2025-04-28 15:00:13,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:13,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:14,291 - INFO - Response - Page 2:
2025-04-28 15:00:14,494 - INFO - 第 2 页获取到 100 条记录
2025-04-28 15:00:14,494 - INFO - Request Parameters - Page 3:
2025-04-28 15:00:14,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:14,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:14,947 - INFO - Response - Page 3:
2025-04-28 15:00:15,166 - INFO - 第 3 页获取到 100 条记录
2025-04-28 15:00:15,166 - INFO - Request Parameters - Page 4:
2025-04-28 15:00:15,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:15,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:15,651 - INFO - Response - Page 4:
2025-04-28 15:00:15,854 - INFO - 第 4 页获取到 100 条记录
2025-04-28 15:00:15,854 - INFO - Request Parameters - Page 5:
2025-04-28 15:00:15,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:15,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:16,322 - INFO - Response - Page 5:
2025-04-28 15:00:16,526 - INFO - 第 5 页获取到 100 条记录
2025-04-28 15:00:16,526 - INFO - Request Parameters - Page 6:
2025-04-28 15:00:16,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:16,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:16,994 - INFO - Response - Page 6:
2025-04-28 15:00:17,197 - INFO - 第 6 页获取到 100 条记录
2025-04-28 15:00:17,197 - INFO - Request Parameters - Page 7:
2025-04-28 15:00:17,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:17,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:17,572 - INFO - Response - Page 7:
2025-04-28 15:00:17,776 - INFO - 第 7 页获取到 61 条记录
2025-04-28 15:00:17,776 - INFO - 查询完成，共获取到 661 条记录
2025-04-28 15:00:17,776 - INFO - 获取到 661 条表单数据
2025-04-28 15:00:17,776 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-28 15:00:17,791 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 15:00:17,791 - INFO - 开始处理日期: 2025-04
2025-04-28 15:00:17,791 - INFO - Request Parameters - Page 1:
2025-04-28 15:00:17,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:17,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:18,276 - INFO - Response - Page 1:
2025-04-28 15:00:18,479 - INFO - 第 1 页获取到 100 条记录
2025-04-28 15:00:18,479 - INFO - Request Parameters - Page 2:
2025-04-28 15:00:18,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:18,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:18,979 - INFO - Response - Page 2:
2025-04-28 15:00:19,182 - INFO - 第 2 页获取到 100 条记录
2025-04-28 15:00:19,182 - INFO - Request Parameters - Page 3:
2025-04-28 15:00:19,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:19,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:19,682 - INFO - Response - Page 3:
2025-04-28 15:00:19,885 - INFO - 第 3 页获取到 100 条记录
2025-04-28 15:00:19,885 - INFO - Request Parameters - Page 4:
2025-04-28 15:00:19,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:19,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:20,338 - INFO - Response - Page 4:
2025-04-28 15:00:20,541 - INFO - 第 4 页获取到 100 条记录
2025-04-28 15:00:20,541 - INFO - Request Parameters - Page 5:
2025-04-28 15:00:20,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:20,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:21,104 - INFO - Response - Page 5:
2025-04-28 15:00:21,307 - INFO - 第 5 页获取到 100 条记录
2025-04-28 15:00:21,307 - INFO - Request Parameters - Page 6:
2025-04-28 15:00:21,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:21,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:21,791 - INFO - Response - Page 6:
2025-04-28 15:00:21,994 - INFO - 第 6 页获取到 100 条记录
2025-04-28 15:00:21,994 - INFO - Request Parameters - Page 7:
2025-04-28 15:00:21,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 15:00:21,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 15:00:22,338 - INFO - Response - Page 7:
2025-04-28 15:00:22,541 - INFO - 第 7 页获取到 26 条记录
2025-04-28 15:00:22,541 - INFO - 查询完成，共获取到 626 条记录
2025-04-28 15:00:22,541 - INFO - 获取到 626 条表单数据
2025-04-28 15:00:22,541 - INFO - 当前日期 2025-04 有 626 条MySQL数据需要处理
2025-04-28 15:00:22,557 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M7
2025-04-28 15:00:23,057 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M7
2025-04-28 15:00:23,057 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192060.58, 'new_value': 184582.06}, {'field': 'total_amount', 'old_value': 192060.58, 'new_value': 184582.06}, {'field': 'order_count', 'old_value': 778, 'new_value': 758}]
2025-04-28 15:00:23,072 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-04-28 15:00:23,072 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-04-28 15:00:23,072 - INFO - =================同步完成====================
2025-04-28 18:00:01,942 - INFO - =================使用默认全量同步=============
2025-04-28 18:00:03,067 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-28 18:00:03,067 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-28 18:00:03,082 - INFO - 开始处理日期: 2025-01
2025-04-28 18:00:03,082 - INFO - Request Parameters - Page 1:
2025-04-28 18:00:03,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:03,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:04,145 - INFO - Response - Page 1:
2025-04-28 18:00:04,348 - INFO - 第 1 页获取到 100 条记录
2025-04-28 18:00:04,348 - INFO - Request Parameters - Page 2:
2025-04-28 18:00:04,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:04,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:04,832 - INFO - Response - Page 2:
2025-04-28 18:00:05,035 - INFO - 第 2 页获取到 100 条记录
2025-04-28 18:00:05,035 - INFO - Request Parameters - Page 3:
2025-04-28 18:00:05,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:05,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:05,520 - INFO - Response - Page 3:
2025-04-28 18:00:05,723 - INFO - 第 3 页获取到 100 条记录
2025-04-28 18:00:05,723 - INFO - Request Parameters - Page 4:
2025-04-28 18:00:05,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:05,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:06,176 - INFO - Response - Page 4:
2025-04-28 18:00:06,379 - INFO - 第 4 页获取到 100 条记录
2025-04-28 18:00:06,379 - INFO - Request Parameters - Page 5:
2025-04-28 18:00:06,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:06,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:06,832 - INFO - Response - Page 5:
2025-04-28 18:00:07,035 - INFO - 第 5 页获取到 100 条记录
2025-04-28 18:00:07,035 - INFO - Request Parameters - Page 6:
2025-04-28 18:00:07,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:07,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:07,520 - INFO - Response - Page 6:
2025-04-28 18:00:07,723 - INFO - 第 6 页获取到 100 条记录
2025-04-28 18:00:07,723 - INFO - Request Parameters - Page 7:
2025-04-28 18:00:07,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:07,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:08,176 - INFO - Response - Page 7:
2025-04-28 18:00:08,379 - INFO - 第 7 页获取到 82 条记录
2025-04-28 18:00:08,379 - INFO - 查询完成，共获取到 682 条记录
2025-04-28 18:00:08,379 - INFO - 获取到 682 条表单数据
2025-04-28 18:00:08,379 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-28 18:00:08,395 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 18:00:08,395 - INFO - 开始处理日期: 2025-02
2025-04-28 18:00:08,395 - INFO - Request Parameters - Page 1:
2025-04-28 18:00:08,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:08,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:08,879 - INFO - Response - Page 1:
2025-04-28 18:00:09,082 - INFO - 第 1 页获取到 100 条记录
2025-04-28 18:00:09,082 - INFO - Request Parameters - Page 2:
2025-04-28 18:00:09,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:09,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:09,520 - INFO - Response - Page 2:
2025-04-28 18:00:09,739 - INFO - 第 2 页获取到 100 条记录
2025-04-28 18:00:09,739 - INFO - Request Parameters - Page 3:
2025-04-28 18:00:09,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:09,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:10,239 - INFO - Response - Page 3:
2025-04-28 18:00:10,442 - INFO - 第 3 页获取到 100 条记录
2025-04-28 18:00:10,442 - INFO - Request Parameters - Page 4:
2025-04-28 18:00:10,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:10,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:10,895 - INFO - Response - Page 4:
2025-04-28 18:00:11,098 - INFO - 第 4 页获取到 100 条记录
2025-04-28 18:00:11,098 - INFO - Request Parameters - Page 5:
2025-04-28 18:00:11,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:11,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:11,520 - INFO - Response - Page 5:
2025-04-28 18:00:11,723 - INFO - 第 5 页获取到 100 条记录
2025-04-28 18:00:11,723 - INFO - Request Parameters - Page 6:
2025-04-28 18:00:11,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:11,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:12,207 - INFO - Response - Page 6:
2025-04-28 18:00:12,410 - INFO - 第 6 页获取到 100 条记录
2025-04-28 18:00:12,410 - INFO - Request Parameters - Page 7:
2025-04-28 18:00:12,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:12,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:12,895 - INFO - Response - Page 7:
2025-04-28 18:00:13,098 - INFO - 第 7 页获取到 70 条记录
2025-04-28 18:00:13,098 - INFO - 查询完成，共获取到 670 条记录
2025-04-28 18:00:13,098 - INFO - 获取到 670 条表单数据
2025-04-28 18:00:13,098 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-28 18:00:13,114 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 18:00:13,114 - INFO - 开始处理日期: 2025-03
2025-04-28 18:00:13,114 - INFO - Request Parameters - Page 1:
2025-04-28 18:00:13,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:13,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:13,598 - INFO - Response - Page 1:
2025-04-28 18:00:13,801 - INFO - 第 1 页获取到 100 条记录
2025-04-28 18:00:13,801 - INFO - Request Parameters - Page 2:
2025-04-28 18:00:13,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:13,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:14,254 - INFO - Response - Page 2:
2025-04-28 18:00:14,457 - INFO - 第 2 页获取到 100 条记录
2025-04-28 18:00:14,457 - INFO - Request Parameters - Page 3:
2025-04-28 18:00:14,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:14,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:14,926 - INFO - Response - Page 3:
2025-04-28 18:00:15,129 - INFO - 第 3 页获取到 100 条记录
2025-04-28 18:00:15,129 - INFO - Request Parameters - Page 4:
2025-04-28 18:00:15,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:15,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:15,676 - INFO - Response - Page 4:
2025-04-28 18:00:15,879 - INFO - 第 4 页获取到 100 条记录
2025-04-28 18:00:15,879 - INFO - Request Parameters - Page 5:
2025-04-28 18:00:15,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:15,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:16,348 - INFO - Response - Page 5:
2025-04-28 18:00:16,551 - INFO - 第 5 页获取到 100 条记录
2025-04-28 18:00:16,551 - INFO - Request Parameters - Page 6:
2025-04-28 18:00:16,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:16,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:17,051 - INFO - Response - Page 6:
2025-04-28 18:00:17,254 - INFO - 第 6 页获取到 100 条记录
2025-04-28 18:00:17,254 - INFO - Request Parameters - Page 7:
2025-04-28 18:00:17,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:17,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:17,707 - INFO - Response - Page 7:
2025-04-28 18:00:17,910 - INFO - 第 7 页获取到 61 条记录
2025-04-28 18:00:17,910 - INFO - 查询完成，共获取到 661 条记录
2025-04-28 18:00:17,910 - INFO - 获取到 661 条表单数据
2025-04-28 18:00:17,910 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-28 18:00:17,926 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 18:00:17,926 - INFO - 开始处理日期: 2025-04
2025-04-28 18:00:17,926 - INFO - Request Parameters - Page 1:
2025-04-28 18:00:17,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:17,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:18,426 - INFO - Response - Page 1:
2025-04-28 18:00:18,629 - INFO - 第 1 页获取到 100 条记录
2025-04-28 18:00:18,629 - INFO - Request Parameters - Page 2:
2025-04-28 18:00:18,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:18,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:19,160 - INFO - Response - Page 2:
2025-04-28 18:00:19,364 - INFO - 第 2 页获取到 100 条记录
2025-04-28 18:00:19,364 - INFO - Request Parameters - Page 3:
2025-04-28 18:00:19,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:19,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:19,864 - INFO - Response - Page 3:
2025-04-28 18:00:20,067 - INFO - 第 3 页获取到 100 条记录
2025-04-28 18:00:20,067 - INFO - Request Parameters - Page 4:
2025-04-28 18:00:20,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:20,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:20,567 - INFO - Response - Page 4:
2025-04-28 18:00:20,770 - INFO - 第 4 页获取到 100 条记录
2025-04-28 18:00:20,770 - INFO - Request Parameters - Page 5:
2025-04-28 18:00:20,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:20,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:21,223 - INFO - Response - Page 5:
2025-04-28 18:00:21,426 - INFO - 第 5 页获取到 100 条记录
2025-04-28 18:00:21,426 - INFO - Request Parameters - Page 6:
2025-04-28 18:00:21,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:21,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:21,879 - INFO - Response - Page 6:
2025-04-28 18:00:22,098 - INFO - 第 6 页获取到 100 条记录
2025-04-28 18:00:22,098 - INFO - Request Parameters - Page 7:
2025-04-28 18:00:22,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 18:00:22,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 18:00:22,520 - INFO - Response - Page 7:
2025-04-28 18:00:22,723 - INFO - 第 7 页获取到 26 条记录
2025-04-28 18:00:22,723 - INFO - 查询完成，共获取到 626 条记录
2025-04-28 18:00:22,723 - INFO - 获取到 626 条表单数据
2025-04-28 18:00:22,723 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-28 18:00:22,723 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M43
2025-04-28 18:00:23,332 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M43
2025-04-28 18:00:23,332 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 136008.18, 'new_value': 141683.05}, {'field': 'total_amount', 'old_value': 136008.18, 'new_value': 141683.05}, {'field': 'order_count', 'old_value': 722, 'new_value': 750}]
2025-04-28 18:00:23,332 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MA3
2025-04-28 18:00:23,817 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MA3
2025-04-28 18:00:23,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28328.3, 'new_value': 29612.7}, {'field': 'total_amount', 'old_value': 28328.3, 'new_value': 29612.7}, {'field': 'order_count', 'old_value': 220, 'new_value': 234}]
2025-04-28 18:00:23,817 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MB3
2025-04-28 18:00:24,285 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MB3
2025-04-28 18:00:24,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3593.0, 'new_value': 3992.0}, {'field': 'total_amount', 'old_value': 12291.0, 'new_value': 12690.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-04-28 18:00:24,285 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M24
2025-04-28 18:00:24,785 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M24
2025-04-28 18:00:24,785 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6204.68, 'new_value': 6489.71}, {'field': 'offline_amount', 'old_value': 76538.83, 'new_value': 80089.07}, {'field': 'total_amount', 'old_value': 82743.51, 'new_value': 86578.78}, {'field': 'order_count', 'old_value': 3239, 'new_value': 3400}]
2025-04-28 18:00:24,785 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MD4
2025-04-28 18:00:25,301 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MD4
2025-04-28 18:00:25,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89004.08, 'new_value': 91932.29}, {'field': 'total_amount', 'old_value': 89004.08, 'new_value': 91932.29}, {'field': 'order_count', 'old_value': 4078, 'new_value': 4211}]
2025-04-28 18:00:25,301 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV4
2025-04-28 18:00:25,723 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV4
2025-04-28 18:00:25,723 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33275.6, 'new_value': 33692.0}, {'field': 'offline_amount', 'old_value': 213812.3, 'new_value': 219884.5}, {'field': 'total_amount', 'old_value': 247087.9, 'new_value': 253576.5}, {'field': 'order_count', 'old_value': 2217, 'new_value': 2276}]
2025-04-28 18:00:25,723 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M15
2025-04-28 18:00:26,270 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M15
2025-04-28 18:00:26,270 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78714.97, 'new_value': 83921.7}, {'field': 'offline_amount', 'old_value': 29435.99, 'new_value': 30282.41}, {'field': 'total_amount', 'old_value': 108150.96, 'new_value': 114204.11}, {'field': 'order_count', 'old_value': 5694, 'new_value': 6021}]
2025-04-28 18:00:26,270 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M55
2025-04-28 18:00:26,707 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M55
2025-04-28 18:00:26,707 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45459.0, 'new_value': 47887.0}, {'field': 'total_amount', 'old_value': 45459.0, 'new_value': 47887.0}, {'field': 'order_count', 'old_value': 2416, 'new_value': 2539}]
2025-04-28 18:00:26,707 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP5
2025-04-28 18:00:27,192 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP5
2025-04-28 18:00:27,192 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53018.41, 'new_value': 54930.56}, {'field': 'offline_amount', 'old_value': 205177.46, 'new_value': 210227.68}, {'field': 'total_amount', 'old_value': 258195.87, 'new_value': 265158.24}, {'field': 'order_count', 'old_value': 3822, 'new_value': 3951}]
2025-04-28 18:00:27,192 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-04-28 18:00:27,629 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-04-28 18:00:27,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88513.99, 'new_value': 112265.99}, {'field': 'total_amount', 'old_value': 700018.06, 'new_value': 723770.06}, {'field': 'order_count', 'old_value': 2320, 'new_value': 2385}]
2025-04-28 18:00:27,629 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MBA
2025-04-28 18:00:28,176 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MBA
2025-04-28 18:00:28,176 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8869.13, 'new_value': 9094.75}, {'field': 'offline_amount', 'old_value': 85053.54, 'new_value': 90414.02}, {'field': 'total_amount', 'old_value': 93922.67, 'new_value': 99508.77}, {'field': 'order_count', 'old_value': 2007, 'new_value': 2095}]
2025-04-28 18:00:28,176 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ5
2025-04-28 18:00:28,660 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ5
2025-04-28 18:00:28,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34256.0, 'new_value': 35797.0}, {'field': 'total_amount', 'old_value': 34256.0, 'new_value': 35797.0}, {'field': 'order_count', 'old_value': 190, 'new_value': 197}]
2025-04-28 18:00:28,660 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME6
2025-04-28 18:00:29,082 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME6
2025-04-28 18:00:29,082 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158795.9, 'new_value': 158895.9}, {'field': 'total_amount', 'old_value': 158795.9, 'new_value': 158895.9}, {'field': 'order_count', 'old_value': 53, 'new_value': 54}]
2025-04-28 18:00:29,082 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU6
2025-04-28 18:00:29,582 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU6
2025-04-28 18:00:29,582 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48.5, 'new_value': 115.56}, {'field': 'offline_amount', 'old_value': 20599.5, 'new_value': 21377.94}, {'field': 'total_amount', 'old_value': 20648.0, 'new_value': 21493.5}, {'field': 'order_count', 'old_value': 997, 'new_value': 1038}]
2025-04-28 18:00:29,582 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF7
2025-04-28 18:00:30,051 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF7
2025-04-28 18:00:30,051 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107226.36, 'new_value': 112464.84}, {'field': 'total_amount', 'old_value': 124727.79, 'new_value': 129966.27}, {'field': 'order_count', 'old_value': 2805, 'new_value': 2916}]
2025-04-28 18:00:30,051 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN7
2025-04-28 18:00:30,520 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN7
2025-04-28 18:00:30,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77050.0, 'new_value': 82040.0}, {'field': 'total_amount', 'old_value': 77050.0, 'new_value': 82040.0}, {'field': 'order_count', 'old_value': 558, 'new_value': 595}]
2025-04-28 18:00:30,520 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR7
2025-04-28 18:00:30,957 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR7
2025-04-28 18:00:30,957 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34778.96, 'new_value': 35994.61}, {'field': 'offline_amount', 'old_value': 193967.72, 'new_value': 200352.52}, {'field': 'total_amount', 'old_value': 228746.68, 'new_value': 236347.13}, {'field': 'order_count', 'old_value': 4267, 'new_value': 4424}]
2025-04-28 18:00:30,957 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB8
2025-04-28 18:00:31,332 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB8
2025-04-28 18:00:31,332 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18679.4, 'new_value': 19061.38}, {'field': 'offline_amount', 'old_value': 104820.36, 'new_value': 109131.26}, {'field': 'total_amount', 'old_value': 123499.76, 'new_value': 128192.64}, {'field': 'order_count', 'old_value': 3819, 'new_value': 3946}]
2025-04-28 18:00:31,332 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSH
2025-04-28 18:00:31,754 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSH
2025-04-28 18:00:31,754 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78653.02, 'new_value': 83553.25}, {'field': 'total_amount', 'old_value': 105589.29, 'new_value': 110489.52}, {'field': 'order_count', 'old_value': 5213, 'new_value': 5502}]
2025-04-28 18:00:31,754 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8I
2025-04-28 18:00:32,238 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8I
2025-04-28 18:00:32,238 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142239.14, 'new_value': 146543.03}, {'field': 'total_amount', 'old_value': 142239.14, 'new_value': 146543.03}, {'field': 'order_count', 'old_value': 5500, 'new_value': 5679}]
2025-04-28 18:00:32,238 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDJ
2025-04-28 18:00:32,723 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDJ
2025-04-28 18:00:32,723 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8723.92, 'new_value': 8973.12}, {'field': 'offline_amount', 'old_value': 131222.77, 'new_value': 136283.67}, {'field': 'total_amount', 'old_value': 139946.69, 'new_value': 145256.79}, {'field': 'order_count', 'old_value': 5541, 'new_value': 5760}]
2025-04-28 18:00:32,723 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MUJ
2025-04-28 18:00:33,270 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MUJ
2025-04-28 18:00:33,270 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 245987.17, 'new_value': 259511.47}, {'field': 'total_amount', 'old_value': 245987.17, 'new_value': 259511.47}, {'field': 'order_count', 'old_value': 2144, 'new_value': 2260}]
2025-04-28 18:00:33,270 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MK
2025-04-28 18:00:33,723 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MK
2025-04-28 18:00:33,723 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15340.0, 'new_value': 16020.0}, {'field': 'total_amount', 'old_value': 15340.0, 'new_value': 16020.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-04-28 18:00:33,723 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MR1
2025-04-28 18:00:34,254 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MR1
2025-04-28 18:00:34,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5974.5, 'new_value': 12983.4}, {'field': 'total_amount', 'old_value': 199440.23, 'new_value': 206449.13}, {'field': 'order_count', 'old_value': 8651, 'new_value': 8954}]
2025-04-28 18:00:34,254 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU1
2025-04-28 18:00:34,676 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU1
2025-04-28 18:00:34,676 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 714365.41, 'new_value': 726131.41}, {'field': 'total_amount', 'old_value': 714365.41, 'new_value': 726131.41}, {'field': 'order_count', 'old_value': 630, 'new_value': 647}]
2025-04-28 18:00:34,676 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M22
2025-04-28 18:00:35,145 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M22
2025-04-28 18:00:35,145 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42682.0, 'new_value': 43070.0}, {'field': 'offline_amount', 'old_value': 298720.0, 'new_value': 312300.0}, {'field': 'total_amount', 'old_value': 341402.0, 'new_value': 355370.0}, {'field': 'order_count', 'old_value': 254, 'new_value': 267}]
2025-04-28 18:00:35,145 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M42
2025-04-28 18:00:35,692 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M42
2025-04-28 18:00:35,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 825978.0, 'new_value': 864974.0}, {'field': 'total_amount', 'old_value': 825978.0, 'new_value': 864974.0}, {'field': 'order_count', 'old_value': 37370, 'new_value': 37446}]
2025-04-28 18:00:35,692 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M52
2025-04-28 18:00:36,285 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M52
2025-04-28 18:00:36,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 609522.0, 'new_value': 630923.0}, {'field': 'total_amount', 'old_value': 609522.0, 'new_value': 630923.0}, {'field': 'order_count', 'old_value': 2690, 'new_value': 2794}]
2025-04-28 18:00:36,285 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ME2
2025-04-28 18:00:36,707 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ME2
2025-04-28 18:00:36,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 159.0, 'new_value': 5354.0}, {'field': 'total_amount', 'old_value': 145354.0, 'new_value': 150549.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 59}]
2025-04-28 18:00:36,707 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M96
2025-04-28 18:00:37,160 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M96
2025-04-28 18:00:37,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91558.0, 'new_value': 94772.0}, {'field': 'total_amount', 'old_value': 91558.0, 'new_value': 94772.0}, {'field': 'order_count', 'old_value': 4815, 'new_value': 4978}]
2025-04-28 18:00:37,160 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV6
2025-04-28 18:00:37,723 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV6
2025-04-28 18:00:37,723 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86593.12, 'new_value': 89866.0}, {'field': 'total_amount', 'old_value': 86593.12, 'new_value': 89866.0}, {'field': 'order_count', 'old_value': 3544, 'new_value': 3674}]
2025-04-28 18:00:37,723 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M77
2025-04-28 18:00:38,160 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M77
2025-04-28 18:00:38,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29799.0, 'new_value': 31049.0}, {'field': 'total_amount', 'old_value': 29799.0, 'new_value': 31049.0}, {'field': 'order_count', 'old_value': 2788, 'new_value': 2907}]
2025-04-28 18:00:38,160 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP7
2025-04-28 18:00:38,645 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP7
2025-04-28 18:00:38,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98704.0, 'new_value': 102015.0}, {'field': 'total_amount', 'old_value': 98704.0, 'new_value': 102015.0}, {'field': 'order_count', 'old_value': 654, 'new_value': 683}]
2025-04-28 18:00:38,645 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR7
2025-04-28 18:00:39,082 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR7
2025-04-28 18:00:39,082 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37267.0, 'new_value': 37366.0}, {'field': 'total_amount', 'old_value': 37267.0, 'new_value': 37366.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-04-28 18:00:39,082 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MS7
2025-04-28 18:00:39,551 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MS7
2025-04-28 18:00:39,551 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18361.59, 'new_value': 19923.45}, {'field': 'offline_amount', 'old_value': 60262.98, 'new_value': 61883.26}, {'field': 'total_amount', 'old_value': 78624.57, 'new_value': 81806.71}, {'field': 'order_count', 'old_value': 3748, 'new_value': 3921}]
2025-04-28 18:00:39,551 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT7
2025-04-28 18:00:39,988 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT7
2025-04-28 18:00:39,988 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30500.0, 'new_value': 40000.0}, {'field': 'total_amount', 'old_value': 30500.0, 'new_value': 40000.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 12}]
2025-04-28 18:00:39,988 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU7
2025-04-28 18:00:40,520 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU7
2025-04-28 18:00:40,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 608000.0, 'new_value': 764500.0}, {'field': 'total_amount', 'old_value': 608000.0, 'new_value': 764500.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-04-28 18:00:40,520 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MX7
2025-04-28 18:00:40,910 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MX7
2025-04-28 18:00:40,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 246444.0, 'new_value': 346576.0}, {'field': 'total_amount', 'old_value': 246444.0, 'new_value': 346576.0}, {'field': 'order_count', 'old_value': 620, 'new_value': 906}]
2025-04-28 18:00:40,910 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY7
2025-04-28 18:00:41,363 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY7
2025-04-28 18:00:41,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16144.7, 'new_value': 17341.7}, {'field': 'total_amount', 'old_value': 16144.7, 'new_value': 17341.7}, {'field': 'order_count', 'old_value': 60, 'new_value': 67}]
2025-04-28 18:00:41,363 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MZ7
2025-04-28 18:00:41,848 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MZ7
2025-04-28 18:00:41,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 328340.36, 'new_value': 341647.67}, {'field': 'total_amount', 'old_value': 328340.36, 'new_value': 341647.67}, {'field': 'order_count', 'old_value': 13902, 'new_value': 14474}]
2025-04-28 18:00:41,848 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M08
2025-04-28 18:00:42,238 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M08
2025-04-28 18:00:42,238 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 428472.32, 'new_value': 433299.32}, {'field': 'total_amount', 'old_value': 428472.32, 'new_value': 433299.32}, {'field': 'order_count', 'old_value': 77, 'new_value': 79}]
2025-04-28 18:00:42,238 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M18
2025-04-28 18:00:42,692 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M18
2025-04-28 18:00:42,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140913.08, 'new_value': 146641.88}, {'field': 'total_amount', 'old_value': 140913.08, 'new_value': 146641.88}, {'field': 'order_count', 'old_value': 2302, 'new_value': 2388}]
2025-04-28 18:00:42,692 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M28
2025-04-28 18:00:43,238 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M28
2025-04-28 18:00:43,238 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31129.68, 'new_value': 32187.68}, {'field': 'total_amount', 'old_value': 31129.68, 'new_value': 32187.68}, {'field': 'order_count', 'old_value': 2717, 'new_value': 2819}]
2025-04-28 18:00:43,238 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M38
2025-04-28 18:00:43,629 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M38
2025-04-28 18:00:43,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 271357.27, 'new_value': 280954.15}, {'field': 'total_amount', 'old_value': 274381.27, 'new_value': 283978.15}, {'field': 'order_count', 'old_value': 576, 'new_value': 593}]
2025-04-28 18:00:43,629 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M58
2025-04-28 18:00:44,098 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M58
2025-04-28 18:00:44,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24508.1, 'new_value': 24678.8}, {'field': 'total_amount', 'old_value': 24508.1, 'new_value': 24678.8}, {'field': 'order_count', 'old_value': 708, 'new_value': 715}]
2025-04-28 18:00:44,098 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M78
2025-04-28 18:00:44,566 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M78
2025-04-28 18:00:44,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47080.2, 'new_value': 47913.4}, {'field': 'total_amount', 'old_value': 47080.2, 'new_value': 47913.4}, {'field': 'order_count', 'old_value': 515, 'new_value': 527}]
2025-04-28 18:00:44,566 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M88
2025-04-28 18:00:45,020 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M88
2025-04-28 18:00:45,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119609.0, 'new_value': 124767.0}, {'field': 'total_amount', 'old_value': 119609.0, 'new_value': 124767.0}, {'field': 'order_count', 'old_value': 4278, 'new_value': 4497}]
2025-04-28 18:00:45,020 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-04-28 18:00:45,441 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-04-28 18:00:45,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168728.5, 'new_value': 173677.69}, {'field': 'total_amount', 'old_value': 175123.5, 'new_value': 180072.69}, {'field': 'order_count', 'old_value': 1008, 'new_value': 1046}]
2025-04-28 18:00:45,441 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF8
2025-04-28 18:00:45,910 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF8
2025-04-28 18:00:45,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1392000.0, 'new_value': 1442000.0}, {'field': 'total_amount', 'old_value': 1392000.0, 'new_value': 1442000.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-04-28 18:00:45,910 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH8
2025-04-28 18:00:46,301 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH8
2025-04-28 18:00:46,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32412.1, 'new_value': 34216.6}, {'field': 'offline_amount', 'old_value': 34302.95, 'new_value': 34744.8}, {'field': 'total_amount', 'old_value': 66715.05, 'new_value': 68961.4}, {'field': 'order_count', 'old_value': 5284, 'new_value': 5449}]
2025-04-28 18:00:46,301 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI8
2025-04-28 18:00:46,707 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI8
2025-04-28 18:00:46,723 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6076408.0, 'new_value': 6304815.0}, {'field': 'total_amount', 'old_value': 6076408.0, 'new_value': 6304815.0}, {'field': 'order_count', 'old_value': 107763, 'new_value': 111739}]
2025-04-28 18:00:46,723 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MJ8
2025-04-28 18:00:47,191 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MJ8
2025-04-28 18:00:47,191 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15297.0, 'new_value': 15615.0}, {'field': 'offline_amount', 'old_value': 7771.0, 'new_value': 7871.0}, {'field': 'total_amount', 'old_value': 23068.0, 'new_value': 23486.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 99}]
2025-04-28 18:00:47,191 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML8
2025-04-28 18:00:47,598 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML8
2025-04-28 18:00:47,598 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154714.77, 'new_value': 160182.77}, {'field': 'total_amount', 'old_value': 154714.77, 'new_value': 160182.77}, {'field': 'order_count', 'old_value': 13489, 'new_value': 14046}]
2025-04-28 18:00:47,598 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN8
2025-04-28 18:00:48,004 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN8
2025-04-28 18:00:48,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62542.0, 'new_value': 68940.0}, {'field': 'total_amount', 'old_value': 62542.0, 'new_value': 68940.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-04-28 18:00:48,004 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO8
2025-04-28 18:00:48,473 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO8
2025-04-28 18:00:48,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55839.0, 'new_value': 58292.0}, {'field': 'total_amount', 'old_value': 55839.0, 'new_value': 58292.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 57}]
2025-04-28 18:00:48,473 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP8
2025-04-28 18:00:48,941 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP8
2025-04-28 18:00:48,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98000.0, 'new_value': 105000.0}, {'field': 'total_amount', 'old_value': 98000.0, 'new_value': 105000.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-04-28 18:00:48,941 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR8
2025-04-28 18:00:49,363 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR8
2025-04-28 18:00:49,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8784.51, 'new_value': 9322.21}, {'field': 'total_amount', 'old_value': 10224.51, 'new_value': 10762.21}, {'field': 'order_count', 'old_value': 54, 'new_value': 55}]
2025-04-28 18:00:49,363 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M42
2025-04-28 18:00:49,848 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M42
2025-04-28 18:00:49,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12215.33, 'new_value': 12636.33}, {'field': 'total_amount', 'old_value': 12215.33, 'new_value': 12636.33}, {'field': 'order_count', 'old_value': 326, 'new_value': 340}]
2025-04-28 18:00:49,848 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MC2
2025-04-28 18:00:50,363 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MC2
2025-04-28 18:00:50,363 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33551.0, 'new_value': 40441.0}, {'field': 'offline_amount', 'old_value': 82880.0, 'new_value': 89844.0}, {'field': 'total_amount', 'old_value': 116431.0, 'new_value': 130285.0}, {'field': 'order_count', 'old_value': 776, 'new_value': 890}]
2025-04-28 18:00:50,363 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9ME2
2025-04-28 18:00:50,863 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9ME2
2025-04-28 18:00:50,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39793.5, 'new_value': 42585.7}, {'field': 'total_amount', 'old_value': 39793.5, 'new_value': 42585.7}, {'field': 'order_count', 'old_value': 450, 'new_value': 483}]
2025-04-28 18:00:50,863 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381ZKVUYUYGD0EVH8PCR91L20Q91PX9MY3
2025-04-28 18:00:51,410 - INFO - 更新表单数据成功: FINST-OJ966381ZKVUYUYGD0EVH8PCR91L20Q91PX9MY3
2025-04-28 18:00:51,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1023000.0, 'new_value': 1089000.0}, {'field': 'total_amount', 'old_value': 1023000.0, 'new_value': 1089000.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-04-28 18:00:51,410 - INFO - 开始批量插入 1 条新记录
2025-04-28 18:00:51,582 - INFO - 批量插入响应状态码: 200
2025-04-28 18:00:51,582 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 28 Apr 2025 10:00:32 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DEFAD91E-66CC-71C1-B932-2BDF5BB950F3', 'x-acs-trace-id': '2262b0eaecd5dd0c8c30aea0143a606f', 'etag': '6/BIGwVsXXz5TwWmnFbzKXw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-28 18:00:51,582 - INFO - 批量插入响应体: {'result': ['FINST-2FD66I718UXUFQYW74TY65GTGPXF3UKTQW0AML7']}
2025-04-28 18:00:51,582 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-04-28 18:00:51,582 - INFO - 成功插入的数据ID: ['FINST-2FD66I718UXUFQYW74TY65GTGPXF3UKTQW0AML7']
2025-04-28 18:00:54,598 - INFO - 批量插入完成，共 1 条记录
2025-04-28 18:00:54,598 - INFO - 日期 2025-04 处理完成 - 更新: 61 条，插入: 1 条，错误: 0 条
2025-04-28 18:00:54,598 - INFO - 数据同步完成！更新: 61 条，插入: 1 条，错误: 0 条
2025-04-28 18:00:54,598 - INFO - =================同步完成====================
2025-04-28 21:00:01,976 - INFO - =================使用默认全量同步=============
2025-04-28 21:00:03,101 - INFO - MySQL查询成功，共获取 2640 条记录
2025-04-28 21:00:03,101 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-28 21:00:03,133 - INFO - 开始处理日期: 2025-01
2025-04-28 21:00:03,133 - INFO - Request Parameters - Page 1:
2025-04-28 21:00:03,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:03,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:04,023 - INFO - Response - Page 1:
2025-04-28 21:00:04,226 - INFO - 第 1 页获取到 100 条记录
2025-04-28 21:00:04,226 - INFO - Request Parameters - Page 2:
2025-04-28 21:00:04,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:04,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:04,898 - INFO - Response - Page 2:
2025-04-28 21:00:05,101 - INFO - 第 2 页获取到 100 条记录
2025-04-28 21:00:05,101 - INFO - Request Parameters - Page 3:
2025-04-28 21:00:05,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:05,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:05,679 - INFO - Response - Page 3:
2025-04-28 21:00:05,883 - INFO - 第 3 页获取到 100 条记录
2025-04-28 21:00:05,883 - INFO - Request Parameters - Page 4:
2025-04-28 21:00:05,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:05,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:06,398 - INFO - Response - Page 4:
2025-04-28 21:00:06,601 - INFO - 第 4 页获取到 100 条记录
2025-04-28 21:00:06,601 - INFO - Request Parameters - Page 5:
2025-04-28 21:00:06,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:06,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:07,086 - INFO - Response - Page 5:
2025-04-28 21:00:07,289 - INFO - 第 5 页获取到 100 条记录
2025-04-28 21:00:07,289 - INFO - Request Parameters - Page 6:
2025-04-28 21:00:07,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:07,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:07,804 - INFO - Response - Page 6:
2025-04-28 21:00:08,008 - INFO - 第 6 页获取到 100 条记录
2025-04-28 21:00:08,008 - INFO - Request Parameters - Page 7:
2025-04-28 21:00:08,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:08,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:08,476 - INFO - Response - Page 7:
2025-04-28 21:00:08,679 - INFO - 第 7 页获取到 82 条记录
2025-04-28 21:00:08,679 - INFO - 查询完成，共获取到 682 条记录
2025-04-28 21:00:08,679 - INFO - 获取到 682 条表单数据
2025-04-28 21:00:08,679 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-28 21:00:08,695 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 21:00:08,695 - INFO - 开始处理日期: 2025-02
2025-04-28 21:00:08,695 - INFO - Request Parameters - Page 1:
2025-04-28 21:00:08,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:08,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:09,242 - INFO - Response - Page 1:
2025-04-28 21:00:09,445 - INFO - 第 1 页获取到 100 条记录
2025-04-28 21:00:09,445 - INFO - Request Parameters - Page 2:
2025-04-28 21:00:09,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:09,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:09,992 - INFO - Response - Page 2:
2025-04-28 21:00:10,195 - INFO - 第 2 页获取到 100 条记录
2025-04-28 21:00:10,195 - INFO - Request Parameters - Page 3:
2025-04-28 21:00:10,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:10,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:10,711 - INFO - Response - Page 3:
2025-04-28 21:00:10,914 - INFO - 第 3 页获取到 100 条记录
2025-04-28 21:00:10,914 - INFO - Request Parameters - Page 4:
2025-04-28 21:00:10,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:10,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:11,351 - INFO - Response - Page 4:
2025-04-28 21:00:11,554 - INFO - 第 4 页获取到 100 条记录
2025-04-28 21:00:11,554 - INFO - Request Parameters - Page 5:
2025-04-28 21:00:11,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:11,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:12,054 - INFO - Response - Page 5:
2025-04-28 21:00:12,258 - INFO - 第 5 页获取到 100 条记录
2025-04-28 21:00:12,258 - INFO - Request Parameters - Page 6:
2025-04-28 21:00:12,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:12,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:12,742 - INFO - Response - Page 6:
2025-04-28 21:00:12,945 - INFO - 第 6 页获取到 100 条记录
2025-04-28 21:00:12,945 - INFO - Request Parameters - Page 7:
2025-04-28 21:00:12,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:12,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:13,414 - INFO - Response - Page 7:
2025-04-28 21:00:13,617 - INFO - 第 7 页获取到 70 条记录
2025-04-28 21:00:13,617 - INFO - 查询完成，共获取到 670 条记录
2025-04-28 21:00:13,617 - INFO - 获取到 670 条表单数据
2025-04-28 21:00:13,617 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-28 21:00:13,633 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 21:00:13,633 - INFO - 开始处理日期: 2025-03
2025-04-28 21:00:13,633 - INFO - Request Parameters - Page 1:
2025-04-28 21:00:13,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:13,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:14,164 - INFO - Response - Page 1:
2025-04-28 21:00:14,367 - INFO - 第 1 页获取到 100 条记录
2025-04-28 21:00:14,367 - INFO - Request Parameters - Page 2:
2025-04-28 21:00:14,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:14,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:14,851 - INFO - Response - Page 2:
2025-04-28 21:00:15,054 - INFO - 第 2 页获取到 100 条记录
2025-04-28 21:00:15,054 - INFO - Request Parameters - Page 3:
2025-04-28 21:00:15,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:15,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:15,523 - INFO - Response - Page 3:
2025-04-28 21:00:15,726 - INFO - 第 3 页获取到 100 条记录
2025-04-28 21:00:15,726 - INFO - Request Parameters - Page 4:
2025-04-28 21:00:15,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:15,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:16,211 - INFO - Response - Page 4:
2025-04-28 21:00:16,414 - INFO - 第 4 页获取到 100 条记录
2025-04-28 21:00:16,414 - INFO - Request Parameters - Page 5:
2025-04-28 21:00:16,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:16,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:16,851 - INFO - Response - Page 5:
2025-04-28 21:00:17,054 - INFO - 第 5 页获取到 100 条记录
2025-04-28 21:00:17,054 - INFO - Request Parameters - Page 6:
2025-04-28 21:00:17,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:17,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:17,570 - INFO - Response - Page 6:
2025-04-28 21:00:17,773 - INFO - 第 6 页获取到 100 条记录
2025-04-28 21:00:17,773 - INFO - Request Parameters - Page 7:
2025-04-28 21:00:17,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:17,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:18,195 - INFO - Response - Page 7:
2025-04-28 21:00:18,398 - INFO - 第 7 页获取到 61 条记录
2025-04-28 21:00:18,398 - INFO - 查询完成，共获取到 661 条记录
2025-04-28 21:00:18,398 - INFO - 获取到 661 条表单数据
2025-04-28 21:00:18,398 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-28 21:00:18,414 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-28 21:00:18,414 - INFO - 开始处理日期: 2025-04
2025-04-28 21:00:18,414 - INFO - Request Parameters - Page 1:
2025-04-28 21:00:18,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:18,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:18,929 - INFO - Response - Page 1:
2025-04-28 21:00:19,133 - INFO - 第 1 页获取到 100 条记录
2025-04-28 21:00:19,133 - INFO - Request Parameters - Page 2:
2025-04-28 21:00:19,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:19,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:19,617 - INFO - Response - Page 2:
2025-04-28 21:00:19,820 - INFO - 第 2 页获取到 100 条记录
2025-04-28 21:00:19,820 - INFO - Request Parameters - Page 3:
2025-04-28 21:00:19,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:19,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:20,429 - INFO - Response - Page 3:
2025-04-28 21:00:20,632 - INFO - 第 3 页获取到 100 条记录
2025-04-28 21:00:20,632 - INFO - Request Parameters - Page 4:
2025-04-28 21:00:20,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:20,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:21,117 - INFO - Response - Page 4:
2025-04-28 21:00:21,320 - INFO - 第 4 页获取到 100 条记录
2025-04-28 21:00:21,320 - INFO - Request Parameters - Page 5:
2025-04-28 21:00:21,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:21,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:21,789 - INFO - Response - Page 5:
2025-04-28 21:00:21,992 - INFO - 第 5 页获取到 100 条记录
2025-04-28 21:00:21,992 - INFO - Request Parameters - Page 6:
2025-04-28 21:00:21,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:21,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:22,507 - INFO - Response - Page 6:
2025-04-28 21:00:22,711 - INFO - 第 6 页获取到 100 条记录
2025-04-28 21:00:22,711 - INFO - Request Parameters - Page 7:
2025-04-28 21:00:22,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-28 21:00:22,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-28 21:00:23,054 - INFO - Response - Page 7:
2025-04-28 21:00:23,257 - INFO - 第 7 页获取到 27 条记录
2025-04-28 21:00:23,257 - INFO - 查询完成，共获取到 627 条记录
2025-04-28 21:00:23,257 - INFO - 获取到 627 条表单数据
2025-04-28 21:00:23,257 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-04-28 21:00:23,273 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4J
2025-04-28 21:00:23,695 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4J
2025-04-28 21:00:23,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96631.0, 'new_value': 100329.0}, {'field': 'offline_amount', 'old_value': 115461.0, 'new_value': 119043.0}, {'field': 'total_amount', 'old_value': 212092.0, 'new_value': 219372.0}, {'field': 'order_count', 'old_value': 5433, 'new_value': 5633}]
2025-04-28 21:00:23,695 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV
2025-04-28 21:00:24,211 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV
2025-04-28 21:00:24,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85540.0, 'new_value': 94228.0}, {'field': 'total_amount', 'old_value': 92220.0, 'new_value': 100908.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-04-28 21:00:24,211 - INFO - 日期 2025-04 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-04-28 21:00:24,211 - INFO - 数据同步完成！更新: 2 条，插入: 0 条，错误: 0 条
2025-04-28 21:00:24,211 - INFO - =================同步完成====================
