2025-06-14 08:00:03,451 - INFO - ==================================================
2025-06-14 08:00:03,451 - INFO - 程序启动 - 版本 v1.0.0
2025-06-14 08:00:03,451 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250614.log
2025-06-14 08:00:03,451 - INFO - ==================================================
2025-06-14 08:00:03,451 - INFO - 程序入口点: __main__
2025-06-14 08:00:03,451 - INFO - ==================================================
2025-06-14 08:00:03,451 - INFO - 程序启动 - 版本 v1.0.1
2025-06-14 08:00:03,451 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250614.log
2025-06-14 08:00:03,451 - INFO - ==================================================
2025-06-14 08:00:03,451 - INFO - MySQL数据库连接成功
2025-06-14 08:00:03,764 - INFO - MySQL数据库连接成功
2025-06-14 08:00:03,764 - INFO - sales_data表已存在，无需创建
2025-06-14 08:00:03,764 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-06-14 08:00:03,764 - INFO - DataSyncManager初始化完成
2025-06-14 08:00:03,764 - INFO - 开始更新店铺映射表...
2025-06-14 08:00:03,764 - INFO - 开始获取最近7天的店铺信息，时间范围: 2025-06-07 至 2025-06-13
2025-06-14 08:00:03,764 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:03,764 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:03,764 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8278BCCBBD6CCC44A107773398EBC781'}
2025-06-14 08:00:04,889 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:04,889 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-14 08:00:05,389 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:05,389 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:05,389 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0102A9538A67F43C6D2D2DFC234CC28B'}
2025-06-14 08:00:06,467 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:06,467 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-14 08:00:06,982 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:06,982 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:06,982 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A55122ED8E795065A19825320C5C947D'}
2025-06-14 08:00:07,889 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:07,889 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-14 08:00:08,389 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:08,389 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:08,389 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A3C404C7DA3A04D0C89B7901C66A3A86'}
2025-06-14 08:00:09,357 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:09,357 - INFO - 日期 ******** 获取到 327 条店铺记录
2025-06-14 08:00:09,857 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:09,857 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:09,857 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '85961C00618E77B6CD96F6CD2D5721F8'}
2025-06-14 08:00:10,764 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:10,764 - INFO - 日期 ******** 获取到 332 条店铺记录
2025-06-14 08:00:11,279 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:11,279 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:11,279 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '712687E8AEDED342717A921E4214EBA2'}
2025-06-14 08:00:12,154 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:12,154 - INFO - 日期 ******** 获取到 333 条店铺记录
2025-06-14 08:00:12,654 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:12,654 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:12,654 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '95509FB29D04AA59B7176B2A855E9A81'}
2025-06-14 08:00:13,529 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:13,529 - INFO - 日期 ******** 获取到 333 条店铺记录
2025-06-14 08:00:14,263 - INFO - 店铺映射表更新完成，总计: 333条，成功: 333条 (更新: 333条, 插入: 0条)
2025-06-14 08:00:14,263 - INFO - 店铺映射表更新完成
2025-06-14 08:00:14,263 - INFO - 未提供日期参数，使用默认值
2025-06-14 08:00:14,263 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-06-14 08:00:14,263 - INFO - 开始综合数据同步流程...
2025-06-14 08:00:14,263 - INFO - 当前错误日期列表为空
2025-06-14 08:00:14,263 - INFO - 正在获取数衍平台日销售数据...
2025-06-14 08:00:14,263 - INFO - 开始获取最近7天的店铺信息，时间范围: 2025-06-07 至 2025-06-13
2025-06-14 08:00:14,263 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:14,263 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:14,263 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EDBD04E1E28800D1B578D9A7C168CE4B'}
2025-06-14 08:00:15,185 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:15,185 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-14 08:00:15,701 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:15,701 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:15,701 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A2D60D15C48BD907341D4247EE104D3D'}
2025-06-14 08:00:16,623 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:16,623 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-14 08:00:17,123 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:17,123 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:17,123 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '22B9A1DDEA3C90BC45F25E636B8057F2'}
2025-06-14 08:00:18,107 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:18,107 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-14 08:00:18,607 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:18,607 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:18,607 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '01A385C30D3B723DC55A70BBF64D4A38'}
2025-06-14 08:00:19,498 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:19,498 - INFO - 日期 ******** 获取到 327 条店铺记录
2025-06-14 08:00:19,998 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:19,998 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:19,998 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0ABEC963138412B84655A9C1CDAE2AD5'}
2025-06-14 08:00:20,873 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:20,873 - INFO - 日期 ******** 获取到 332 条店铺记录
2025-06-14 08:00:21,388 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:21,388 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:21,388 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'BB08D11C9998F407FCEAF2BE43ED9F06'}
2025-06-14 08:00:22,232 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:22,232 - INFO - 日期 ******** 获取到 333 条店铺记录
2025-06-14 08:00:22,748 - INFO - 查询日期 ******** 的店铺信息
2025-06-14 08:00:22,748 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:22,748 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6F2CB8054E9C260F6842687FBF85C76F'}
2025-06-14 08:00:23,654 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:23,654 - INFO - 日期 ******** 获取到 333 条店铺记录
2025-06-14 08:00:24,373 - INFO - 店铺映射表更新完成，总计: 333条，成功: 333条 (更新: 333条, 插入: 0条)
2025-06-14 08:00:24,373 - INFO - 查询数衍平台数据，时间段为: 2025-04-14, 2025-06-13
2025-06-14 08:00:24,373 - INFO - 正在获取********至********的数据
2025-06-14 08:00:24,373 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:24,373 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AD7C656C6C2D8A1B06D041ED443B85D4'}
2025-06-14 08:00:26,342 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:26,342 - INFO - 过滤后保留 429 条记录
2025-06-14 08:00:28,357 - INFO - 正在获取********至********的数据
2025-06-14 08:00:28,357 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:28,357 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0D651A1E6115C95EC0852F920F97CA6A'}
2025-06-14 08:00:30,013 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:30,013 - INFO - 过滤后保留 429 条记录
2025-06-14 08:00:32,029 - INFO - 正在获取********至********的数据
2025-06-14 08:00:32,029 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:32,029 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0C78BCF81240C40E4859B9D4B930E349'}
2025-06-14 08:00:33,748 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:33,748 - INFO - 过滤后保留 432 条记录
2025-06-14 08:00:35,763 - INFO - 正在获取********至********的数据
2025-06-14 08:00:35,763 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:35,763 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '81ED31CB4E837A5A3DB05FCE98612FEB'}
2025-06-14 08:00:37,248 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:37,248 - INFO - 过滤后保留 429 条记录
2025-06-14 08:00:39,248 - INFO - 正在获取********至********的数据
2025-06-14 08:00:39,248 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:39,248 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '3227F9E3D338C317C87AF7BAB03F3620'}
2025-06-14 08:00:41,935 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:41,935 - INFO - 过滤后保留 419 条记录
2025-06-14 08:00:43,951 - INFO - 正在获取********至********的数据
2025-06-14 08:00:43,951 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:43,951 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AEDA5C2BE39A2F05D30D194E72FD7B1C'}
2025-06-14 08:00:46,841 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:46,841 - INFO - 过滤后保留 416 条记录
2025-06-14 08:00:48,857 - INFO - 正在获取********至********的数据
2025-06-14 08:00:48,857 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:48,857 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A4DA40A4F6322197905451F928C8534B'}
2025-06-14 08:00:50,295 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:50,310 - INFO - 过滤后保留 434 条记录
2025-06-14 08:00:52,326 - INFO - 正在获取********至********的数据
2025-06-14 08:00:52,326 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:52,326 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '43BE54C7ABE16D120B1F95D5AC195FD8'}
2025-06-14 08:00:53,857 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:53,857 - INFO - 过滤后保留 424 条记录
2025-06-14 08:00:55,857 - INFO - 正在获取********至********的数据
2025-06-14 08:00:55,857 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:00:55,857 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '49C4E01B643317CF8A14E46DF251E296'}
2025-06-14 08:00:58,404 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:00:58,404 - INFO - 过滤后保留 433 条记录
2025-06-14 08:01:00,419 - INFO - 正在获取********至********的数据
2025-06-14 08:01:00,419 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:00,419 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '3375D0441E24293551D18ECFBA671910'}
2025-06-14 08:01:02,060 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:02,076 - INFO - 过滤后保留 424 条记录
2025-06-14 08:01:04,091 - INFO - 正在获取********至********的数据
2025-06-14 08:01:04,091 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:04,091 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '10D1FCDD85D9554A79428CDAA1110F2F'}
2025-06-14 08:01:05,544 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:05,544 - INFO - 过滤后保留 428 条记录
2025-06-14 08:01:07,560 - INFO - 正在获取********至********的数据
2025-06-14 08:01:07,560 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:07,560 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7628955401BA9F5D44D13B2DFD09DBF2'}
2025-06-14 08:01:09,013 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:09,013 - INFO - 过滤后保留 411 条记录
2025-06-14 08:01:11,029 - INFO - 正在获取********至********的数据
2025-06-14 08:01:11,029 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:11,029 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '210ECFFFEDD2B11CB69EE7943B81D466'}
2025-06-14 08:01:12,951 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:12,966 - INFO - 过滤后保留 423 条记录
2025-06-14 08:01:14,966 - INFO - 正在获取********至********的数据
2025-06-14 08:01:14,966 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:14,966 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '10BCDF6ED449E4149E2E4CB47EBAB040'}
2025-06-14 08:01:16,419 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:16,419 - INFO - 过滤后保留 436 条记录
2025-06-14 08:01:18,435 - INFO - 正在获取********至********的数据
2025-06-14 08:01:18,435 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:18,435 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '57F3824BB97729017C39E00A1B1FED39'}
2025-06-14 08:01:19,904 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:19,904 - INFO - 过滤后保留 426 条记录
2025-06-14 08:01:21,919 - INFO - 正在获取********至********的数据
2025-06-14 08:01:21,919 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:21,919 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '3EF3B84CE919ACDDC0B4B1A5799046E2'}
2025-06-14 08:01:23,247 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:23,247 - INFO - 过滤后保留 417 条记录
2025-06-14 08:01:25,263 - INFO - 正在获取********至********的数据
2025-06-14 08:01:25,263 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:25,263 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '51C2E1E6B1E09E1E99A7398FA5CACD5B'}
2025-06-14 08:01:26,701 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:26,716 - INFO - 过滤后保留 425 条记录
2025-06-14 08:01:28,732 - INFO - 正在获取********至********的数据
2025-06-14 08:01:28,732 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:28,732 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-06-14 08:01:31,294 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:31,310 - INFO - 过滤后保留 426 条记录
2025-06-14 08:01:33,325 - INFO - 正在获取********至********的数据
2025-06-14 08:01:33,325 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:33,325 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7933767C8F15D2A0B741DB0E48494265'}
2025-06-14 08:01:34,763 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:34,779 - INFO - 过滤后保留 421 条记录
2025-06-14 08:01:36,794 - INFO - 正在获取********至********的数据
2025-06-14 08:01:36,794 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:36,794 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B4072645BF9F5097DE169735069C9589'}
2025-06-14 08:01:38,247 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:38,263 - INFO - 过滤后保留 416 条记录
2025-06-14 08:01:40,279 - INFO - 正在获取********至********的数据
2025-06-14 08:01:40,279 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:40,279 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D208C5DDE919A8758F27547E0E1926A5'}
2025-06-14 08:01:41,747 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:41,763 - INFO - 过滤后保留 424 条记录
2025-06-14 08:01:43,779 - INFO - 正在获取********至********的数据
2025-06-14 08:01:43,779 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:43,779 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '17ABFD3DA10F52F5A7719AB65F5C903A'}
2025-06-14 08:01:45,232 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:45,232 - INFO - 过滤后保留 410 条记录
2025-06-14 08:01:47,247 - INFO - 正在获取********至********的数据
2025-06-14 08:01:47,247 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:47,247 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D22BD390755CE49F5D8A5463A02F27E8'}
2025-06-14 08:01:48,591 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:48,607 - INFO - 过滤后保留 414 条记录
2025-06-14 08:01:50,622 - INFO - 正在获取********至********的数据
2025-06-14 08:01:50,622 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:50,622 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EEB715F5FA7D0C018138C4C30E5D9ABE'}
2025-06-14 08:01:52,247 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:52,247 - INFO - 过滤后保留 417 条记录
2025-06-14 08:01:54,247 - INFO - 正在获取********至********的数据
2025-06-14 08:01:54,247 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:54,247 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '63C0C1CF0AF98F1F9588F50FDD0FC8CD'}
2025-06-14 08:01:57,013 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:01:57,028 - INFO - 过滤后保留 407 条记录
2025-06-14 08:01:59,044 - INFO - 正在获取********至********的数据
2025-06-14 08:01:59,044 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:01:59,044 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C383B1F00CF07F2B4D16FEE700FBADCA'}
2025-06-14 08:02:00,403 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:02:00,419 - INFO - 过滤后保留 401 条记录
2025-06-14 08:02:02,435 - INFO - 正在获取********至********的数据
2025-06-14 08:02:02,435 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:02:02,435 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7A4D51C4D354262E7393175BB35A1A67'}
2025-06-14 08:02:03,872 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:02:03,872 - INFO - 过滤后保留 408 条记录
2025-06-14 08:02:05,888 - INFO - 正在获取********至********的数据
2025-06-14 08:02:05,888 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:02:05,888 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A15CAC3623C8B05D6187342295C3A1E4'}
2025-06-14 08:02:07,450 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:02:07,450 - INFO - 过滤后保留 409 条记录
2025-06-14 08:02:09,466 - INFO - 正在获取********至********的数据
2025-06-14 08:02:09,466 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:02:09,466 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B5D7250566E7DA6F2A8AE1DA54BA2F68'}
2025-06-14 08:02:10,981 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:02:10,981 - INFO - 过滤后保留 402 条记录
2025-06-14 08:02:12,997 - INFO - 正在获取********至********的数据
2025-06-14 08:02:12,997 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:02:12,997 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5823FEB972E20B781C553E5C06F8DF8A'}
2025-06-14 08:02:14,278 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:02:14,278 - INFO - 过滤后保留 414 条记录
2025-06-14 08:02:16,278 - INFO - 正在获取********至********的数据
2025-06-14 08:02:16,278 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-14 08:02:16,278 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D58D9379B9B348CC22A6A8EFC31C2BC4'}
2025-06-14 08:02:17,294 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-14 08:02:17,294 - INFO - 过滤后保留 202 条记录
2025-06-14 08:02:19,294 - INFO - 开始保存数据到MySQL数据库，共 12806 条记录待处理
2025-06-14 08:02:28,341 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-06-02
2025-06-14 08:02:28,341 - INFO - 变更字段: recommend_amount: 0.0 -> 5804.0, daily_bill_amount: 0.0 -> 5804.0
2025-06-14 08:02:28,341 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-06-01
2025-06-14 08:02:28,341 - INFO - 变更字段: recommend_amount: 0.0 -> 7757.0, daily_bill_amount: 0.0 -> 7757.0
2025-06-14 08:02:28,716 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-06-03
2025-06-14 08:02:28,716 - INFO - 变更字段: recommend_amount: 0.0 -> 2800.0, daily_bill_amount: 0.0 -> 2800.0
2025-06-14 08:02:29,075 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-06-06
2025-06-14 08:02:29,075 - INFO - 变更字段: recommend_amount: 0.0 -> 3771.0, daily_bill_amount: 0.0 -> 3771.0
2025-06-14 08:02:29,075 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-06-05
2025-06-14 08:02:29,075 - INFO - 变更字段: recommend_amount: 0.0 -> 7385.0, daily_bill_amount: 0.0 -> 7385.0
2025-06-14 08:02:29,278 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS, sale_time=2025-06-05
2025-06-14 08:02:29,278 - INFO - 变更字段: amount: 2126 -> 3214, count: 15 -> 16, instore_amount: 0.0 -> 1088.0, instore_count: 0 -> 1
2025-06-14 08:02:29,434 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-06-07
2025-06-14 08:02:29,434 - INFO - 变更字段: recommend_amount: 0.0 -> 8929.0, daily_bill_amount: 0.0 -> 8929.0
2025-06-14 08:02:29,700 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6, sale_time=2025-06-08
2025-06-14 08:02:29,700 - INFO - 变更字段: amount: 7622 -> 7417, count: 145 -> 138, instore_amount: 4996.71 -> 4792.0, instore_count: 75 -> 68
2025-06-14 08:02:29,809 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-06-10
2025-06-14 08:02:29,809 - INFO - 变更字段: recommend_amount: 0.0 -> 10915.0, daily_bill_amount: 0.0 -> 10915.0
2025-06-14 08:02:29,872 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-06-10
2025-06-14 08:02:29,872 - INFO - 变更字段: recommend_amount: 4533.65 -> 4566.55, amount: 4533 -> 4566, count: 222 -> 223, online_amount: 3269.71 -> 3302.61, online_count: 165 -> 166
2025-06-14 08:02:29,966 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDUDM4PLNS0I86N3H2U121001EB7, sale_time=2025-06-10
2025-06-14 08:02:29,966 - INFO - 变更字段: amount: 3449 -> 3316
2025-06-14 08:02:29,997 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS, sale_time=2025-06-09
2025-06-14 08:02:29,997 - INFO - 变更字段: amount: 3891 -> 4227, count: 13 -> 14, instore_amount: 0.0 -> 336.0, instore_count: 0 -> 1
2025-06-14 08:02:30,075 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6, sale_time=2025-06-10
2025-06-14 08:02:30,075 - INFO - 变更字段: amount: 5283 -> 5256, count: 106 -> 105, instore_amount: 3972.4 -> 3945.9, instore_count: 68 -> 67
2025-06-14 08:02:30,091 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-06-12
2025-06-14 08:02:30,091 - INFO - 变更字段: recommend_amount: 0.0 -> 5128.6, daily_bill_amount: 0.0 -> 5128.6
2025-06-14 08:02:30,106 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=6692283A183D432BAE322E1032539CE8, sale_time=2025-06-12
2025-06-14 08:02:30,106 - INFO - 变更字段: recommend_amount: 10713.0 -> 13071.0, daily_bill_amount: 0.0 -> 10713.0, amount: 10713 -> 13071, count: 21 -> 22, instore_amount: 10713.0 -> 13071.0, instore_count: 21 -> 22
2025-06-14 08:02:30,138 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H9P9S64J8E8R652ASKKUBQUMU0018EN, sale_time=2025-06-12
2025-06-14 08:02:30,138 - INFO - 变更字段: amount: 3343 -> 3405, count: 18 -> 19, instore_amount: 3252.56 -> 3314.66, instore_count: 16 -> 17
2025-06-14 08:02:30,153 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDTG15Q4SH7Q2OV4FVC7CO001499, sale_time=2025-06-12
2025-06-14 08:02:30,153 - INFO - 变更字段: amount: 3332 -> 3335, online_amount: 1409.0 -> 1411.5
2025-06-14 08:02:30,169 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDRLHCKFK97Q2OV4FVC7BS00148D, sale_time=2025-06-12
2025-06-14 08:02:30,169 - INFO - 变更字段: recommend_amount: 0.0 -> 9723.02, daily_bill_amount: 0.0 -> 9723.02
2025-06-14 08:02:30,169 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDMMR23CHP7Q2OV4FVC79C00145T, sale_time=2025-06-12
2025-06-14 08:02:30,169 - INFO - 变更字段: amount: 766 -> 786, count: 27 -> 28, online_amount: 529.13 -> 548.93, online_count: 22 -> 23
2025-06-14 08:02:30,184 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTE3QTM66G7Q2OVBN4IS8M001D4O, sale_time=2025-06-12
2025-06-14 08:02:30,184 - INFO - 变更字段: amount: 568 -> 596, count: 49 -> 51, instore_amount: 584.64 -> 613.02, instore_count: 49 -> 51
2025-06-14 08:02:30,184 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCIR7D5JD7Q2OVBN4IS7U001D40, sale_time=2025-06-12
2025-06-14 08:02:30,184 - INFO - 变更字段: recommend_amount: 4310.6 -> 5178.6, daily_bill_amount: 4310.6 -> 5178.6
2025-06-14 08:02:30,184 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-06-12
2025-06-14 08:02:30,184 - INFO - 变更字段: recommend_amount: 0.0 -> 5313.0, daily_bill_amount: 0.0 -> 5313.0
2025-06-14 08:02:30,200 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-06-12
2025-06-14 08:02:30,200 - INFO - 变更字段: recommend_amount: 0.0 -> 10371.0, daily_bill_amount: 0.0 -> 10371.0, amount: 411 -> 647, count: 2 -> 3, instore_amount: 411.0 -> 647.0, instore_count: 2 -> 3
2025-06-14 08:02:30,200 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S, sale_time=2025-06-12
2025-06-14 08:02:30,200 - INFO - 变更字段: recommend_amount: 20666.0 -> 19608.0, amount: 20666 -> 19608, instore_amount: 20666.0 -> 19608.0
2025-06-14 08:02:30,200 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O, sale_time=2025-06-12
2025-06-14 08:02:30,200 - INFO - 变更字段: amount: -9418 -> 2875, count: 37 -> 46, instore_amount: 22449.73 -> 34743.64, instore_count: 37 -> 46
2025-06-14 08:02:30,216 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-06-11
2025-06-14 08:02:30,216 - INFO - 变更字段: recommend_amount: 1771.32 -> 1771.57, daily_bill_amount: 1771.32 -> 1771.57
2025-06-14 08:02:30,231 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-06-12
2025-06-14 08:02:30,231 - INFO - 变更字段: count: 133 -> 134, online_amount: 1850.42 -> 1863.42, online_count: 107 -> 108
2025-06-14 08:02:30,247 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEO8LGKTTH42F6DB81RH8V001P57, sale_time=2025-06-12
2025-06-14 08:02:30,247 - INFO - 变更字段: recommend_amount: 2176.75 -> 2188.94, amount: 2176 -> 2188, instore_amount: 2223.98 -> 2236.17
2025-06-14 08:02:30,263 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-06-12
2025-06-14 08:02:30,263 - INFO - 变更字段: amount: 2073 -> 2124, count: 146 -> 148, online_amount: 1733.9 -> 1784.9, online_count: 103 -> 105
2025-06-14 08:02:30,263 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1ITH5661MFT0H03I210VQKTG1J001P6B, sale_time=2025-06-12
2025-06-14 08:02:30,263 - INFO - 变更字段: daily_bill_amount: 0.0 -> 3045.92
2025-06-14 08:02:30,278 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-06-12
2025-06-14 08:02:30,278 - INFO - 变更字段: recommend_amount: 5689.91 -> 5741.71, amount: 5689 -> 5741, count: 249 -> 254, instore_amount: 1198.09 -> 1219.59, instore_count: 47 -> 49, online_amount: 4640.12 -> 4670.42, online_count: 202 -> 205
2025-06-14 08:02:30,278 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-06-12
2025-06-14 08:02:30,278 - INFO - 变更字段: recommend_amount: 0.0 -> 693.0, daily_bill_amount: 0.0 -> 693.0
2025-06-14 08:02:30,294 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-06-12
2025-06-14 08:02:30,294 - INFO - 变更字段: recommend_amount: 0.0 -> 19622.6, daily_bill_amount: 0.0 -> 19622.6
2025-06-14 08:02:30,294 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEAIN5DMKK0I86N3H2U1VH001F8N, sale_time=2025-06-12
2025-06-14 08:02:30,294 - INFO - 变更字段: recommend_amount: 5826.2 -> 1151.2, daily_bill_amount: 5826.2 -> 1151.2
2025-06-14 08:02:30,294 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-06-12
2025-06-14 08:02:30,294 - INFO - 变更字段: amount: 25933 -> 26193, count: 218 -> 219, instore_amount: 9583.5 -> 9843.5, instore_count: 62 -> 63
2025-06-14 08:02:30,325 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-06-12
2025-06-14 08:02:30,325 - INFO - 变更字段: amount: 7526 -> 7549, count: 336 -> 339, online_amount: 5055.13 -> 5078.11, online_count: 199 -> 202
2025-06-14 08:02:30,325 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVBILA3OJ0I86N3H2U1PQ001F30, sale_time=2025-06-12
2025-06-14 08:02:30,325 - INFO - 变更字段: amount: 12674 -> 13004, count: 195 -> 197, online_amount: 4393.6 -> 4723.0, online_count: 150 -> 152
2025-06-14 08:02:30,341 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-06-12
2025-06-14 08:02:30,341 - INFO - 变更字段: amount: 3325 -> 3444, count: 13 -> 14, instore_amount: 2897.36 -> 3016.26, instore_count: 10 -> 11
2025-06-14 08:02:30,356 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-06-12
2025-06-14 08:02:30,356 - INFO - 变更字段: amount: 5510 -> 6874, count: 9 -> 11, instore_amount: 5614.2 -> 6978.2, instore_count: 9 -> 11
2025-06-14 08:02:30,372 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9D9OBM41R0I86N3H2U17K001EGQ, sale_time=2025-06-12
2025-06-14 08:02:30,372 - INFO - 变更字段: recommend_amount: 984.8 -> -295.9, daily_bill_amount: 984.8 -> -295.9
2025-06-14 08:02:30,372 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCE13J2R9CI0I86N3H2U13D001ECJ, sale_time=2025-06-12
2025-06-14 08:02:30,372 - INFO - 变更字段: recommend_amount: 0.0 -> 6658.0, daily_bill_amount: 0.0 -> 6658.0
2025-06-14 08:02:30,372 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-12
2025-06-14 08:02:30,372 - INFO - 变更字段: amount: 3218 -> 3228, count: 186 -> 189, online_amount: 3218.61 -> 3228.12, online_count: 179 -> 182
2025-06-14 08:02:30,372 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDTTV5HD0Q0I86N3H2U11P001EAV, sale_time=2025-06-12
2025-06-14 08:02:30,388 - INFO - 变更字段: recommend_amount: 0.0 -> 10413.16, daily_bill_amount: 0.0 -> 10413.16
2025-06-14 08:02:30,388 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSC7N3PHM0I86N3H2U10T001EA3, sale_time=2025-06-12
2025-06-14 08:02:30,388 - INFO - 变更字段: amount: 2703 -> 2682, count: 130 -> 131, online_amount: 2532.5 -> 2536.4, online_count: 103 -> 104
2025-06-14 08:02:30,388 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDRR6FJ7A60I86N3H2U10L001E9R, sale_time=2025-06-12
2025-06-14 08:02:30,388 - INFO - 变更字段: recommend_amount: 3187.76 -> 3207.76, amount: 3187 -> 3207, count: 151 -> 152, online_amount: 2154.0 -> 2174.0, online_count: 80 -> 81
2025-06-14 08:02:30,388 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-12
2025-06-14 08:02:30,388 - INFO - 变更字段: instore_amount: 3131.93 -> 3137.93, instore_count: 219 -> 221, online_amount: 1202.2 -> 1196.2, online_count: 73 -> 71
2025-06-14 08:02:30,403 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-11
2025-06-14 08:02:30,403 - INFO - 变更字段: instore_amount: 3632.62 -> 3648.62, instore_count: 223 -> 224, online_amount: 1343.3 -> 1327.3, online_count: 82 -> 81
2025-06-14 08:02:30,403 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDNMK1P3900I86N3H2U1UO001E7U, sale_time=2025-06-12
2025-06-14 08:02:30,403 - INFO - 变更字段: amount: 3340 -> 3375, count: 89 -> 90, online_amount: 1165.08 -> 1200.48, online_count: 26 -> 27
2025-06-14 08:02:30,419 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-06-12
2025-06-14 08:02:30,419 - INFO - 变更字段: recommend_amount: 7759.98 -> 8477.22, amount: 7759 -> 8477, count: 317 -> 353, online_amount: 7882.98 -> 8600.22, online_count: 317 -> 353
2025-06-14 08:02:30,419 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ, sale_time=2025-06-12
2025-06-14 08:02:30,419 - INFO - 变更字段: amount: 14909 -> 14903, online_amount: 1741.2 -> 1735.1
2025-06-14 08:02:30,434 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MR50JEM3SR7Q2OVAE57DM4001Q85, sale_time=2025-06-12
2025-06-14 08:02:30,434 - INFO - 变更字段: amount: 14549 -> 14489
2025-06-14 08:02:30,434 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B, sale_time=2025-06-12
2025-06-14 08:02:30,434 - INFO - 变更字段: recommend_amount: 6764.23 -> 6753.23, amount: 6764 -> 6753, count: 414 -> 415, instore_amount: 3556.8 -> 3570.1, instore_count: 214 -> 215
2025-06-14 08:02:30,434 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0NLURMV9D3IKSIOCDI7R2001G23, sale_time=2025-06-12
2025-06-14 08:02:30,450 - INFO - 变更字段: recommend_amount: 0.0 -> 2846.2, daily_bill_amount: 0.0 -> 2846.2
2025-06-14 08:02:30,450 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK, sale_time=2025-06-12
2025-06-14 08:02:30,450 - INFO - 变更字段: amount: 9712 -> 10996, count: 59 -> 64, instore_amount: 8583.8 -> 9867.8, instore_count: 43 -> 48
2025-06-14 08:02:30,466 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=E79F261889C1492982227C207062C267, sale_time=2025-06-12
2025-06-14 08:02:30,466 - INFO - 变更字段: amount: 12089 -> 12103, count: 356 -> 357, online_amount: 1473.37 -> 1486.75, online_count: 52 -> 53
2025-06-14 08:02:30,481 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-06-12
2025-06-14 08:02:30,481 - INFO - 变更字段: amount: 29945 -> 32416, count: 160 -> 164, instore_amount: 26939.1 -> 29410.1, instore_count: 133 -> 137
2025-06-14 08:02:30,481 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRBVE2CQT7AV8LHQQGID9001EJI, sale_time=2025-06-12
2025-06-14 08:02:30,497 - INFO - 变更字段: amount: 14593 -> 14867, count: 98 -> 99, instore_amount: 8779.1 -> 9052.9, instore_count: 71 -> 72
2025-06-14 08:02:30,497 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6, sale_time=2025-06-12
2025-06-14 08:02:30,497 - INFO - 变更字段: amount: 9937 -> 9857, count: 212 -> 206, instore_amount: 7190.32 -> 7110.2, instore_count: 111 -> 105
2025-06-14 08:02:30,513 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6, sale_time=2025-06-11
2025-06-14 08:02:30,513 - INFO - 变更字段: amount: 11716 -> 11675, count: 250 -> 249, instore_amount: 8379.88 -> 8338.9, instore_count: 127 -> 126
2025-06-14 08:02:30,513 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPOL2K5B16E7AERKQ83JE001UNG, sale_time=2025-06-12
2025-06-14 08:02:30,513 - INFO - 变更字段: amount: 310 -> 346, count: 12 -> 13, instore_amount: 310.66 -> 346.41, instore_count: 12 -> 13
2025-06-14 08:02:30,763 - INFO - MySQL数据保存完成，统计信息：
2025-06-14 08:02:30,763 - INFO - - 总记录数: 12806
2025-06-14 08:02:30,763 - INFO - - 成功插入: 212
2025-06-14 08:02:30,763 - INFO - - 成功更新: 60
2025-06-14 08:02:30,763 - INFO - - 无需更新: 12534
2025-06-14 08:02:30,763 - INFO - - 处理失败: 0
2025-06-14 08:02:30,763 - INFO - 成功获取数衍平台数据，共 12806 条记录
2025-06-14 08:02:30,763 - INFO - 正在更新MySQL月度汇总数据...
2025-06-14 08:02:30,809 - INFO - 月度数据表清空完成
2025-06-14 08:02:31,122 - INFO - 月度汇总数据更新完成，处理了 1413 条汇总记录
2025-06-14 08:02:31,122 - INFO - 成功更新月度汇总数据，共 1413 条记录
2025-06-14 08:02:31,122 - INFO - 正在获取宜搭日销售表单数据...
2025-06-14 08:02:31,122 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-04-14 00:00:00 至 2025-06-13 23:59:59
2025-06-14 08:02:31,122 - INFO - 查询分段 1: 2025-04-14 至 2025-04-15
2025-06-14 08:02:31,122 - INFO - 查询日期范围: 2025-04-14 至 2025-04-15，使用分页查询，每页 100 条记录
2025-06-14 08:02:31,122 - INFO - Request Parameters - Page 1:
2025-06-14 08:02:31,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:02:31,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000122, 1744646400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:02:36,856 - INFO - API请求耗时: 5734ms
2025-06-14 08:02:36,856 - INFO - Response - Page 1
2025-06-14 08:02:36,856 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:02:37,356 - INFO - Request Parameters - Page 2:
2025-06-14 08:02:37,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:02:37,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000122, 1744646400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:02:45,262 - INFO - API请求耗时: 7906ms
2025-06-14 08:02:45,262 - INFO - Response - Page 2
2025-06-14 08:02:45,262 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:02:45,762 - INFO - Request Parameters - Page 3:
2025-06-14 08:02:45,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:02:45,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000122, 1744646400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:02:46,481 - INFO - API请求耗时: 719ms
2025-06-14 08:02:46,497 - INFO - Response - Page 3
2025-06-14 08:02:46,497 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:02:47,012 - INFO - Request Parameters - Page 4:
2025-06-14 08:02:47,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:02:47,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000122, 1744646400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:02:47,841 - INFO - API请求耗时: 828ms
2025-06-14 08:02:47,841 - INFO - Response - Page 4
2025-06-14 08:02:47,841 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:02:48,341 - INFO - Request Parameters - Page 5:
2025-06-14 08:02:48,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:02:48,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000122, 1744646400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:02:49,169 - INFO - API请求耗时: 828ms
2025-06-14 08:02:49,169 - INFO - Response - Page 5
2025-06-14 08:02:49,169 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:02:49,684 - INFO - Request Parameters - Page 6:
2025-06-14 08:02:49,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:02:49,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000122, 1744646400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:02:50,466 - INFO - API请求耗时: 781ms
2025-06-14 08:02:50,466 - INFO - Response - Page 6
2025-06-14 08:02:50,466 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:02:50,966 - INFO - Request Parameters - Page 7:
2025-06-14 08:02:50,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:02:50,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000122, 1744646400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:02:51,762 - INFO - API请求耗时: 797ms
2025-06-14 08:02:51,762 - INFO - Response - Page 7
2025-06-14 08:02:51,762 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:02:52,262 - INFO - Request Parameters - Page 8:
2025-06-14 08:02:52,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:02:52,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000122, 1744646400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:02:53,059 - INFO - API请求耗时: 797ms
2025-06-14 08:02:53,059 - INFO - Response - Page 8
2025-06-14 08:02:53,059 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:02:53,559 - INFO - Request Parameters - Page 9:
2025-06-14 08:02:53,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:02:53,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000122, 1744646400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:02:54,184 - INFO - API请求耗时: 625ms
2025-06-14 08:02:54,184 - INFO - Response - Page 9
2025-06-14 08:02:54,184 - INFO - 第 9 页获取到 56 条记录
2025-06-14 08:02:54,184 - INFO - 查询完成，共获取到 856 条记录
2025-06-14 08:02:54,184 - INFO - 分段 1 查询成功，获取到 856 条记录
2025-06-14 08:02:55,200 - INFO - 查询分段 2: 2025-04-16 至 2025-04-17
2025-06-14 08:02:55,200 - INFO - 查询日期范围: 2025-04-16 至 2025-04-17，使用分页查询，每页 100 条记录
2025-06-14 08:02:55,200 - INFO - Request Parameters - Page 1:
2025-06-14 08:02:55,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:02:55,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800122, 1744819200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:02:55,950 - INFO - API请求耗时: 750ms
2025-06-14 08:02:55,950 - INFO - Response - Page 1
2025-06-14 08:02:55,950 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:02:56,466 - INFO - Request Parameters - Page 2:
2025-06-14 08:02:56,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:02:56,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800122, 1744819200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:02:57,216 - INFO - API请求耗时: 750ms
2025-06-14 08:02:57,216 - INFO - Response - Page 2
2025-06-14 08:02:57,216 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:02:57,731 - INFO - Request Parameters - Page 3:
2025-06-14 08:02:57,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:02:57,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800122, 1744819200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:02:58,450 - INFO - API请求耗时: 719ms
2025-06-14 08:02:58,450 - INFO - Response - Page 3
2025-06-14 08:02:58,450 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:02:58,950 - INFO - Request Parameters - Page 4:
2025-06-14 08:02:58,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:02:58,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800122, 1744819200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:02:59,653 - INFO - API请求耗时: 703ms
2025-06-14 08:02:59,653 - INFO - Response - Page 4
2025-06-14 08:02:59,653 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:03:00,153 - INFO - Request Parameters - Page 5:
2025-06-14 08:03:00,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:00,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800122, 1744819200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:01,044 - INFO - API请求耗时: 891ms
2025-06-14 08:03:01,044 - INFO - Response - Page 5
2025-06-14 08:03:01,044 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:03:01,544 - INFO - Request Parameters - Page 6:
2025-06-14 08:03:01,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:01,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800122, 1744819200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:02,309 - INFO - API请求耗时: 766ms
2025-06-14 08:03:02,309 - INFO - Response - Page 6
2025-06-14 08:03:02,309 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:03:02,825 - INFO - Request Parameters - Page 7:
2025-06-14 08:03:02,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:02,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800122, 1744819200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:03,528 - INFO - API请求耗时: 703ms
2025-06-14 08:03:03,528 - INFO - Response - Page 7
2025-06-14 08:03:03,544 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:03:04,044 - INFO - Request Parameters - Page 8:
2025-06-14 08:03:04,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:04,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800122, 1744819200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:04,747 - INFO - API请求耗时: 703ms
2025-06-14 08:03:04,747 - INFO - Response - Page 8
2025-06-14 08:03:04,747 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:03:05,262 - INFO - Request Parameters - Page 9:
2025-06-14 08:03:05,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:05,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744732800122, 1744819200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:05,965 - INFO - API请求耗时: 703ms
2025-06-14 08:03:05,965 - INFO - Response - Page 9
2025-06-14 08:03:05,965 - INFO - 第 9 页获取到 76 条记录
2025-06-14 08:03:05,965 - INFO - 查询完成，共获取到 876 条记录
2025-06-14 08:03:05,965 - INFO - 分段 2 查询成功，获取到 876 条记录
2025-06-14 08:03:06,981 - INFO - 查询分段 3: 2025-04-18 至 2025-04-19
2025-06-14 08:03:06,981 - INFO - 查询日期范围: 2025-04-18 至 2025-04-19，使用分页查询，每页 100 条记录
2025-06-14 08:03:06,981 - INFO - Request Parameters - Page 1:
2025-06-14 08:03:06,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:06,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600122, 1744992000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:07,778 - INFO - API请求耗时: 797ms
2025-06-14 08:03:07,778 - INFO - Response - Page 1
2025-06-14 08:03:07,778 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:03:08,278 - INFO - Request Parameters - Page 2:
2025-06-14 08:03:08,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:08,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600122, 1744992000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:09,090 - INFO - API请求耗时: 812ms
2025-06-14 08:03:09,090 - INFO - Response - Page 2
2025-06-14 08:03:09,090 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:03:09,590 - INFO - Request Parameters - Page 3:
2025-06-14 08:03:09,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:09,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600122, 1744992000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:10,356 - INFO - API请求耗时: 766ms
2025-06-14 08:03:10,356 - INFO - Response - Page 3
2025-06-14 08:03:10,356 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:03:10,872 - INFO - Request Parameters - Page 4:
2025-06-14 08:03:10,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:10,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600122, 1744992000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:11,590 - INFO - API请求耗时: 719ms
2025-06-14 08:03:11,590 - INFO - Response - Page 4
2025-06-14 08:03:11,606 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:03:12,122 - INFO - Request Parameters - Page 5:
2025-06-14 08:03:12,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:12,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600122, 1744992000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:12,887 - INFO - API请求耗时: 766ms
2025-06-14 08:03:12,887 - INFO - Response - Page 5
2025-06-14 08:03:12,887 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:03:13,387 - INFO - Request Parameters - Page 6:
2025-06-14 08:03:13,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:13,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600122, 1744992000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:14,200 - INFO - API请求耗时: 812ms
2025-06-14 08:03:14,200 - INFO - Response - Page 6
2025-06-14 08:03:14,200 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:03:14,715 - INFO - Request Parameters - Page 7:
2025-06-14 08:03:14,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:14,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600122, 1744992000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:15,528 - INFO - API请求耗时: 812ms
2025-06-14 08:03:15,528 - INFO - Response - Page 7
2025-06-14 08:03:15,528 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:03:16,028 - INFO - Request Parameters - Page 8:
2025-06-14 08:03:16,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:16,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600122, 1744992000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:16,778 - INFO - API请求耗时: 750ms
2025-06-14 08:03:16,778 - INFO - Response - Page 8
2025-06-14 08:03:16,778 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:03:17,278 - INFO - Request Parameters - Page 9:
2025-06-14 08:03:17,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:17,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600122, 1744992000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:17,887 - INFO - API请求耗时: 609ms
2025-06-14 08:03:17,887 - INFO - Response - Page 9
2025-06-14 08:03:17,887 - INFO - 第 9 页获取到 60 条记录
2025-06-14 08:03:17,887 - INFO - 查询完成，共获取到 860 条记录
2025-06-14 08:03:17,887 - INFO - 分段 3 查询成功，获取到 860 条记录
2025-06-14 08:03:18,903 - INFO - 查询分段 4: 2025-04-20 至 2025-04-21
2025-06-14 08:03:18,903 - INFO - 查询日期范围: 2025-04-20 至 2025-04-21，使用分页查询，每页 100 条记录
2025-06-14 08:03:18,903 - INFO - Request Parameters - Page 1:
2025-06-14 08:03:18,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:18,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400122, 1745164800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:19,606 - INFO - API请求耗时: 703ms
2025-06-14 08:03:19,606 - INFO - Response - Page 1
2025-06-14 08:03:19,606 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:03:20,122 - INFO - Request Parameters - Page 2:
2025-06-14 08:03:20,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:20,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400122, 1745164800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:20,809 - INFO - API请求耗时: 687ms
2025-06-14 08:03:20,809 - INFO - Response - Page 2
2025-06-14 08:03:20,809 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:03:21,325 - INFO - Request Parameters - Page 3:
2025-06-14 08:03:21,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:21,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400122, 1745164800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:22,137 - INFO - API请求耗时: 812ms
2025-06-14 08:03:22,137 - INFO - Response - Page 3
2025-06-14 08:03:22,137 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:03:22,637 - INFO - Request Parameters - Page 4:
2025-06-14 08:03:22,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:22,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400122, 1745164800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:23,387 - INFO - API请求耗时: 750ms
2025-06-14 08:03:23,387 - INFO - Response - Page 4
2025-06-14 08:03:23,387 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:03:23,903 - INFO - Request Parameters - Page 5:
2025-06-14 08:03:23,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:23,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400122, 1745164800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:24,637 - INFO - API请求耗时: 734ms
2025-06-14 08:03:24,637 - INFO - Response - Page 5
2025-06-14 08:03:24,637 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:03:25,153 - INFO - Request Parameters - Page 6:
2025-06-14 08:03:25,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:25,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400122, 1745164800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:25,887 - INFO - API请求耗时: 734ms
2025-06-14 08:03:25,887 - INFO - Response - Page 6
2025-06-14 08:03:25,887 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:03:26,387 - INFO - Request Parameters - Page 7:
2025-06-14 08:03:26,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:26,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400122, 1745164800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:27,075 - INFO - API请求耗时: 687ms
2025-06-14 08:03:27,075 - INFO - Response - Page 7
2025-06-14 08:03:27,075 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:03:27,590 - INFO - Request Parameters - Page 8:
2025-06-14 08:03:27,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:27,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400122, 1745164800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:28,340 - INFO - API请求耗时: 750ms
2025-06-14 08:03:28,340 - INFO - Response - Page 8
2025-06-14 08:03:28,340 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:03:28,856 - INFO - Request Parameters - Page 9:
2025-06-14 08:03:28,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:28,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400122, 1745164800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:29,465 - INFO - API请求耗时: 609ms
2025-06-14 08:03:29,481 - INFO - Response - Page 9
2025-06-14 08:03:29,481 - INFO - 第 9 页获取到 52 条记录
2025-06-14 08:03:29,481 - INFO - 查询完成，共获取到 852 条记录
2025-06-14 08:03:29,481 - INFO - 分段 4 查询成功，获取到 852 条记录
2025-06-14 08:03:30,497 - INFO - 查询分段 5: 2025-04-22 至 2025-04-23
2025-06-14 08:03:30,497 - INFO - 查询日期范围: 2025-04-22 至 2025-04-23，使用分页查询，每页 100 条记录
2025-06-14 08:03:30,497 - INFO - Request Parameters - Page 1:
2025-06-14 08:03:30,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:30,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200122, 1745337600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:31,231 - INFO - API请求耗时: 734ms
2025-06-14 08:03:31,231 - INFO - Response - Page 1
2025-06-14 08:03:31,231 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:03:31,747 - INFO - Request Parameters - Page 2:
2025-06-14 08:03:31,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:31,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200122, 1745337600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:32,590 - INFO - API请求耗时: 844ms
2025-06-14 08:03:32,590 - INFO - Response - Page 2
2025-06-14 08:03:32,590 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:03:33,106 - INFO - Request Parameters - Page 3:
2025-06-14 08:03:33,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:33,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200122, 1745337600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:33,997 - INFO - API请求耗时: 891ms
2025-06-14 08:03:34,012 - INFO - Response - Page 3
2025-06-14 08:03:34,012 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:03:34,512 - INFO - Request Parameters - Page 4:
2025-06-14 08:03:34,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:34,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200122, 1745337600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:35,200 - INFO - API请求耗时: 688ms
2025-06-14 08:03:35,200 - INFO - Response - Page 4
2025-06-14 08:03:35,200 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:03:35,715 - INFO - Request Parameters - Page 5:
2025-06-14 08:03:35,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:35,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200122, 1745337600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:36,497 - INFO - API请求耗时: 781ms
2025-06-14 08:03:36,497 - INFO - Response - Page 5
2025-06-14 08:03:36,497 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:03:37,012 - INFO - Request Parameters - Page 6:
2025-06-14 08:03:37,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:37,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200122, 1745337600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:37,762 - INFO - API请求耗时: 750ms
2025-06-14 08:03:37,762 - INFO - Response - Page 6
2025-06-14 08:03:37,762 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:03:38,278 - INFO - Request Parameters - Page 7:
2025-06-14 08:03:38,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:38,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200122, 1745337600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:38,997 - INFO - API请求耗时: 703ms
2025-06-14 08:03:38,997 - INFO - Response - Page 7
2025-06-14 08:03:38,997 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:03:39,512 - INFO - Request Parameters - Page 8:
2025-06-14 08:03:39,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:39,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200122, 1745337600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:40,231 - INFO - API请求耗时: 719ms
2025-06-14 08:03:40,231 - INFO - Response - Page 8
2025-06-14 08:03:40,231 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:03:40,731 - INFO - Request Parameters - Page 9:
2025-06-14 08:03:40,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:40,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200122, 1745337600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:41,262 - INFO - API请求耗时: 531ms
2025-06-14 08:03:41,278 - INFO - Response - Page 9
2025-06-14 08:03:41,278 - INFO - 第 9 页获取到 28 条记录
2025-06-14 08:03:41,278 - INFO - 查询完成，共获取到 828 条记录
2025-06-14 08:03:41,278 - INFO - 分段 5 查询成功，获取到 828 条记录
2025-06-14 08:03:42,293 - INFO - 查询分段 6: 2025-04-24 至 2025-04-25
2025-06-14 08:03:42,293 - INFO - 查询日期范围: 2025-04-24 至 2025-04-25，使用分页查询，每页 100 条记录
2025-06-14 08:03:42,293 - INFO - Request Parameters - Page 1:
2025-06-14 08:03:42,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:42,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000122, 1745510400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:42,965 - INFO - API请求耗时: 672ms
2025-06-14 08:03:42,965 - INFO - Response - Page 1
2025-06-14 08:03:42,965 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:03:43,465 - INFO - Request Parameters - Page 2:
2025-06-14 08:03:43,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:43,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000122, 1745510400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:44,153 - INFO - API请求耗时: 687ms
2025-06-14 08:03:44,153 - INFO - Response - Page 2
2025-06-14 08:03:44,153 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:03:44,668 - INFO - Request Parameters - Page 3:
2025-06-14 08:03:44,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:44,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000122, 1745510400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:45,371 - INFO - API请求耗时: 703ms
2025-06-14 08:03:45,371 - INFO - Response - Page 3
2025-06-14 08:03:45,371 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:03:45,887 - INFO - Request Parameters - Page 4:
2025-06-14 08:03:45,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:45,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000122, 1745510400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:46,590 - INFO - API请求耗时: 703ms
2025-06-14 08:03:46,590 - INFO - Response - Page 4
2025-06-14 08:03:46,590 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:03:47,106 - INFO - Request Parameters - Page 5:
2025-06-14 08:03:47,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:47,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000122, 1745510400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:47,887 - INFO - API请求耗时: 781ms
2025-06-14 08:03:47,887 - INFO - Response - Page 5
2025-06-14 08:03:47,887 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:03:48,403 - INFO - Request Parameters - Page 6:
2025-06-14 08:03:48,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:48,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000122, 1745510400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:49,168 - INFO - API请求耗时: 766ms
2025-06-14 08:03:49,168 - INFO - Response - Page 6
2025-06-14 08:03:49,168 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:03:49,668 - INFO - Request Parameters - Page 7:
2025-06-14 08:03:49,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:49,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000122, 1745510400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:50,418 - INFO - API请求耗时: 750ms
2025-06-14 08:03:50,418 - INFO - Response - Page 7
2025-06-14 08:03:50,418 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:03:50,934 - INFO - Request Parameters - Page 8:
2025-06-14 08:03:50,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:50,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000122, 1745510400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:51,606 - INFO - API请求耗时: 672ms
2025-06-14 08:03:51,606 - INFO - Response - Page 8
2025-06-14 08:03:51,606 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:03:52,121 - INFO - Request Parameters - Page 9:
2025-06-14 08:03:52,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:52,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000122, 1745510400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:52,731 - INFO - API请求耗时: 609ms
2025-06-14 08:03:52,731 - INFO - Response - Page 9
2025-06-14 08:03:52,731 - INFO - 第 9 页获取到 36 条记录
2025-06-14 08:03:52,731 - INFO - 查询完成，共获取到 836 条记录
2025-06-14 08:03:52,731 - INFO - 分段 6 查询成功，获取到 836 条记录
2025-06-14 08:03:53,746 - INFO - 查询分段 7: 2025-04-26 至 2025-04-27
2025-06-14 08:03:53,746 - INFO - 查询日期范围: 2025-04-26 至 2025-04-27，使用分页查询，每页 100 条记录
2025-06-14 08:03:53,746 - INFO - Request Parameters - Page 1:
2025-06-14 08:03:53,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:53,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800122, 1745683200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:54,543 - INFO - API请求耗时: 797ms
2025-06-14 08:03:54,543 - INFO - Response - Page 1
2025-06-14 08:03:54,543 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:03:55,043 - INFO - Request Parameters - Page 2:
2025-06-14 08:03:55,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:55,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800122, 1745683200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:55,793 - INFO - API请求耗时: 750ms
2025-06-14 08:03:55,793 - INFO - Response - Page 2
2025-06-14 08:03:55,793 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:03:56,293 - INFO - Request Parameters - Page 3:
2025-06-14 08:03:56,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:56,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800122, 1745683200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:56,981 - INFO - API请求耗时: 687ms
2025-06-14 08:03:56,981 - INFO - Response - Page 3
2025-06-14 08:03:56,981 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:03:57,496 - INFO - Request Parameters - Page 4:
2025-06-14 08:03:57,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:57,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800122, 1745683200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:58,278 - INFO - API请求耗时: 781ms
2025-06-14 08:03:58,278 - INFO - Response - Page 4
2025-06-14 08:03:58,278 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:03:58,778 - INFO - Request Parameters - Page 5:
2025-06-14 08:03:58,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:58,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800122, 1745683200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:03:59,481 - INFO - API请求耗时: 703ms
2025-06-14 08:03:59,481 - INFO - Response - Page 5
2025-06-14 08:03:59,481 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:03:59,996 - INFO - Request Parameters - Page 6:
2025-06-14 08:03:59,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:03:59,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800122, 1745683200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:00,887 - INFO - API请求耗时: 891ms
2025-06-14 08:04:00,887 - INFO - Response - Page 6
2025-06-14 08:04:00,887 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:04:01,387 - INFO - Request Parameters - Page 7:
2025-06-14 08:04:01,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:01,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800122, 1745683200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:02,153 - INFO - API请求耗时: 766ms
2025-06-14 08:04:02,153 - INFO - Response - Page 7
2025-06-14 08:04:02,153 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:04:02,668 - INFO - Request Parameters - Page 8:
2025-06-14 08:04:02,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:02,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800122, 1745683200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:03,403 - INFO - API请求耗时: 734ms
2025-06-14 08:04:03,403 - INFO - Response - Page 8
2025-06-14 08:04:03,403 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:04:03,918 - INFO - Request Parameters - Page 9:
2025-06-14 08:04:03,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:03,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745596800122, 1745683200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:04,621 - INFO - API请求耗时: 703ms
2025-06-14 08:04:04,621 - INFO - Response - Page 9
2025-06-14 08:04:04,621 - INFO - 第 9 页获取到 64 条记录
2025-06-14 08:04:04,621 - INFO - 查询完成，共获取到 864 条记录
2025-06-14 08:04:04,621 - INFO - 分段 7 查询成功，获取到 864 条记录
2025-06-14 08:04:05,637 - INFO - 查询分段 8: 2025-04-28 至 2025-04-29
2025-06-14 08:04:05,637 - INFO - 查询日期范围: 2025-04-28 至 2025-04-29，使用分页查询，每页 100 条记录
2025-06-14 08:04:05,637 - INFO - Request Parameters - Page 1:
2025-06-14 08:04:05,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:05,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600122, 1745856000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:06,356 - INFO - API请求耗时: 719ms
2025-06-14 08:04:06,356 - INFO - Response - Page 1
2025-06-14 08:04:06,356 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:04:06,856 - INFO - Request Parameters - Page 2:
2025-06-14 08:04:06,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:06,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600122, 1745856000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:07,543 - INFO - API请求耗时: 687ms
2025-06-14 08:04:07,543 - INFO - Response - Page 2
2025-06-14 08:04:07,543 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:04:08,043 - INFO - Request Parameters - Page 3:
2025-06-14 08:04:08,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:08,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600122, 1745856000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:08,809 - INFO - API请求耗时: 766ms
2025-06-14 08:04:08,809 - INFO - Response - Page 3
2025-06-14 08:04:08,809 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:04:09,309 - INFO - Request Parameters - Page 4:
2025-06-14 08:04:09,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:09,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600122, 1745856000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:10,090 - INFO - API请求耗时: 781ms
2025-06-14 08:04:10,090 - INFO - Response - Page 4
2025-06-14 08:04:10,090 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:04:10,590 - INFO - Request Parameters - Page 5:
2025-06-14 08:04:10,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:10,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600122, 1745856000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:11,324 - INFO - API请求耗时: 734ms
2025-06-14 08:04:11,324 - INFO - Response - Page 5
2025-06-14 08:04:11,324 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:04:11,824 - INFO - Request Parameters - Page 6:
2025-06-14 08:04:11,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:11,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600122, 1745856000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:12,590 - INFO - API请求耗时: 766ms
2025-06-14 08:04:12,590 - INFO - Response - Page 6
2025-06-14 08:04:12,590 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:04:13,090 - INFO - Request Parameters - Page 7:
2025-06-14 08:04:13,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:13,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600122, 1745856000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:13,762 - INFO - API请求耗时: 672ms
2025-06-14 08:04:13,762 - INFO - Response - Page 7
2025-06-14 08:04:13,762 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:04:14,262 - INFO - Request Parameters - Page 8:
2025-06-14 08:04:14,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:14,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600122, 1745856000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:14,949 - INFO - API请求耗时: 688ms
2025-06-14 08:04:14,949 - INFO - Response - Page 8
2025-06-14 08:04:14,949 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:04:15,465 - INFO - Request Parameters - Page 9:
2025-06-14 08:04:15,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:15,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600122, 1745856000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:16,059 - INFO - API请求耗时: 594ms
2025-06-14 08:04:16,059 - INFO - Response - Page 9
2025-06-14 08:04:16,059 - INFO - 第 9 页获取到 48 条记录
2025-06-14 08:04:16,059 - INFO - 查询完成，共获取到 848 条记录
2025-06-14 08:04:16,059 - INFO - 分段 8 查询成功，获取到 848 条记录
2025-06-14 08:04:17,074 - INFO - 查询分段 9: 2025-04-30 至 2025-05-01
2025-06-14 08:04:17,074 - INFO - 查询日期范围: 2025-04-30 至 2025-05-01，使用分页查询，每页 100 条记录
2025-06-14 08:04:17,074 - INFO - Request Parameters - Page 1:
2025-06-14 08:04:17,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:17,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400122, 1746028800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:17,731 - INFO - API请求耗时: 656ms
2025-06-14 08:04:17,731 - INFO - Response - Page 1
2025-06-14 08:04:17,731 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:04:18,231 - INFO - Request Parameters - Page 2:
2025-06-14 08:04:18,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:18,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400122, 1746028800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:18,996 - INFO - API请求耗时: 766ms
2025-06-14 08:04:18,996 - INFO - Response - Page 2
2025-06-14 08:04:18,996 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:04:19,496 - INFO - Request Parameters - Page 3:
2025-06-14 08:04:19,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:19,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400122, 1746028800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:20,309 - INFO - API请求耗时: 812ms
2025-06-14 08:04:20,309 - INFO - Response - Page 3
2025-06-14 08:04:20,309 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:04:20,824 - INFO - Request Parameters - Page 4:
2025-06-14 08:04:20,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:20,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400122, 1746028800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:21,543 - INFO - API请求耗时: 719ms
2025-06-14 08:04:21,543 - INFO - Response - Page 4
2025-06-14 08:04:21,543 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:04:22,043 - INFO - Request Parameters - Page 5:
2025-06-14 08:04:22,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:22,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400122, 1746028800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:22,793 - INFO - API请求耗时: 750ms
2025-06-14 08:04:22,793 - INFO - Response - Page 5
2025-06-14 08:04:22,793 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:04:23,309 - INFO - Request Parameters - Page 6:
2025-06-14 08:04:23,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:23,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400122, 1746028800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:24,027 - INFO - API请求耗时: 719ms
2025-06-14 08:04:24,027 - INFO - Response - Page 6
2025-06-14 08:04:24,027 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:04:24,543 - INFO - Request Parameters - Page 7:
2025-06-14 08:04:24,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:24,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400122, 1746028800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:25,309 - INFO - API请求耗时: 766ms
2025-06-14 08:04:25,309 - INFO - Response - Page 7
2025-06-14 08:04:25,309 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:04:25,809 - INFO - Request Parameters - Page 8:
2025-06-14 08:04:25,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:25,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400122, 1746028800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:26,606 - INFO - API请求耗时: 797ms
2025-06-14 08:04:26,606 - INFO - Response - Page 8
2025-06-14 08:04:26,606 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:04:27,121 - INFO - Request Parameters - Page 9:
2025-06-14 08:04:27,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:27,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745942400122, 1746028800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:27,809 - INFO - API请求耗时: 688ms
2025-06-14 08:04:27,809 - INFO - Response - Page 9
2025-06-14 08:04:27,809 - INFO - 第 9 页获取到 64 条记录
2025-06-14 08:04:27,809 - INFO - 查询完成，共获取到 864 条记录
2025-06-14 08:04:27,809 - INFO - 分段 9 查询成功，获取到 864 条记录
2025-06-14 08:04:28,809 - INFO - 查询分段 10: 2025-05-02 至 2025-05-03
2025-06-14 08:04:28,809 - INFO - 查询日期范围: 2025-05-02 至 2025-05-03，使用分页查询，每页 100 条记录
2025-06-14 08:04:28,809 - INFO - Request Parameters - Page 1:
2025-06-14 08:04:28,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:28,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200122, 1746201600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:29,559 - INFO - API请求耗时: 750ms
2025-06-14 08:04:29,559 - INFO - Response - Page 1
2025-06-14 08:04:29,559 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:04:30,074 - INFO - Request Parameters - Page 2:
2025-06-14 08:04:30,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:30,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200122, 1746201600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:30,777 - INFO - API请求耗时: 703ms
2025-06-14 08:04:30,777 - INFO - Response - Page 2
2025-06-14 08:04:30,777 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:04:31,277 - INFO - Request Parameters - Page 3:
2025-06-14 08:04:31,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:31,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200122, 1746201600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:31,996 - INFO - API请求耗时: 719ms
2025-06-14 08:04:31,996 - INFO - Response - Page 3
2025-06-14 08:04:31,996 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:04:32,512 - INFO - Request Parameters - Page 4:
2025-06-14 08:04:32,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:32,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200122, 1746201600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:33,262 - INFO - API请求耗时: 750ms
2025-06-14 08:04:33,262 - INFO - Response - Page 4
2025-06-14 08:04:33,262 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:04:33,762 - INFO - Request Parameters - Page 5:
2025-06-14 08:04:33,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:33,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200122, 1746201600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:34,543 - INFO - API请求耗时: 781ms
2025-06-14 08:04:34,543 - INFO - Response - Page 5
2025-06-14 08:04:34,543 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:04:35,043 - INFO - Request Parameters - Page 6:
2025-06-14 08:04:35,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:35,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200122, 1746201600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:35,762 - INFO - API请求耗时: 719ms
2025-06-14 08:04:35,762 - INFO - Response - Page 6
2025-06-14 08:04:35,762 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:04:36,262 - INFO - Request Parameters - Page 7:
2025-06-14 08:04:36,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:36,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200122, 1746201600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:36,981 - INFO - API请求耗时: 719ms
2025-06-14 08:04:36,981 - INFO - Response - Page 7
2025-06-14 08:04:36,981 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:04:37,496 - INFO - Request Parameters - Page 8:
2025-06-14 08:04:37,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:37,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200122, 1746201600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:38,277 - INFO - API请求耗时: 781ms
2025-06-14 08:04:38,277 - INFO - Response - Page 8
2025-06-14 08:04:38,277 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:04:38,777 - INFO - Request Parameters - Page 9:
2025-06-14 08:04:38,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:38,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200122, 1746201600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:39,527 - INFO - API请求耗时: 750ms
2025-06-14 08:04:39,527 - INFO - Response - Page 9
2025-06-14 08:04:39,527 - INFO - 第 9 页获取到 52 条记录
2025-06-14 08:04:39,527 - INFO - 查询完成，共获取到 852 条记录
2025-06-14 08:04:39,527 - INFO - 分段 10 查询成功，获取到 852 条记录
2025-06-14 08:04:40,543 - INFO - 查询分段 11: 2025-05-04 至 2025-05-05
2025-06-14 08:04:40,543 - INFO - 查询日期范围: 2025-05-04 至 2025-05-05，使用分页查询，每页 100 条记录
2025-06-14 08:04:40,543 - INFO - Request Parameters - Page 1:
2025-06-14 08:04:40,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:40,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000122, 1746374400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:41,262 - INFO - API请求耗时: 719ms
2025-06-14 08:04:41,262 - INFO - Response - Page 1
2025-06-14 08:04:41,262 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:04:41,777 - INFO - Request Parameters - Page 2:
2025-06-14 08:04:41,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:41,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000122, 1746374400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:42,496 - INFO - API请求耗时: 719ms
2025-06-14 08:04:42,496 - INFO - Response - Page 2
2025-06-14 08:04:42,496 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:04:42,996 - INFO - Request Parameters - Page 3:
2025-06-14 08:04:42,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:42,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000122, 1746374400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:43,777 - INFO - API请求耗时: 781ms
2025-06-14 08:04:43,777 - INFO - Response - Page 3
2025-06-14 08:04:43,777 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:04:44,277 - INFO - Request Parameters - Page 4:
2025-06-14 08:04:44,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:44,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000122, 1746374400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:45,059 - INFO - API请求耗时: 781ms
2025-06-14 08:04:45,059 - INFO - Response - Page 4
2025-06-14 08:04:45,059 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:04:45,559 - INFO - Request Parameters - Page 5:
2025-06-14 08:04:45,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:45,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000122, 1746374400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:46,340 - INFO - API请求耗时: 781ms
2025-06-14 08:04:46,340 - INFO - Response - Page 5
2025-06-14 08:04:46,340 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:04:46,840 - INFO - Request Parameters - Page 6:
2025-06-14 08:04:46,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:46,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000122, 1746374400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:47,746 - INFO - API请求耗时: 906ms
2025-06-14 08:04:47,746 - INFO - Response - Page 6
2025-06-14 08:04:47,746 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:04:48,246 - INFO - Request Parameters - Page 7:
2025-06-14 08:04:48,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:48,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000122, 1746374400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:49,012 - INFO - API请求耗时: 766ms
2025-06-14 08:04:49,012 - INFO - Response - Page 7
2025-06-14 08:04:49,012 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:04:49,512 - INFO - Request Parameters - Page 8:
2025-06-14 08:04:49,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:49,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000122, 1746374400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:50,215 - INFO - API请求耗时: 703ms
2025-06-14 08:04:50,215 - INFO - Response - Page 8
2025-06-14 08:04:50,215 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:04:50,730 - INFO - Request Parameters - Page 9:
2025-06-14 08:04:50,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:50,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000122, 1746374400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:51,324 - INFO - API请求耗时: 594ms
2025-06-14 08:04:51,324 - INFO - Response - Page 9
2025-06-14 08:04:51,324 - INFO - 第 9 页获取到 52 条记录
2025-06-14 08:04:51,324 - INFO - 查询完成，共获取到 852 条记录
2025-06-14 08:04:51,324 - INFO - 分段 11 查询成功，获取到 852 条记录
2025-06-14 08:04:52,340 - INFO - 查询分段 12: 2025-05-06 至 2025-05-07
2025-06-14 08:04:52,340 - INFO - 查询日期范围: 2025-05-06 至 2025-05-07，使用分页查询，每页 100 条记录
2025-06-14 08:04:52,340 - INFO - Request Parameters - Page 1:
2025-06-14 08:04:52,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:52,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800122, 1746547200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:53,074 - INFO - API请求耗时: 734ms
2025-06-14 08:04:53,074 - INFO - Response - Page 1
2025-06-14 08:04:53,074 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:04:53,590 - INFO - Request Parameters - Page 2:
2025-06-14 08:04:53,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:53,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800122, 1746547200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:54,309 - INFO - API请求耗时: 719ms
2025-06-14 08:04:54,309 - INFO - Response - Page 2
2025-06-14 08:04:54,309 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:04:54,824 - INFO - Request Parameters - Page 3:
2025-06-14 08:04:54,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:54,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800122, 1746547200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:55,559 - INFO - API请求耗时: 734ms
2025-06-14 08:04:55,559 - INFO - Response - Page 3
2025-06-14 08:04:55,559 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:04:56,074 - INFO - Request Parameters - Page 4:
2025-06-14 08:04:56,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:56,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800122, 1746547200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:56,746 - INFO - API请求耗时: 672ms
2025-06-14 08:04:56,746 - INFO - Response - Page 4
2025-06-14 08:04:56,762 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:04:57,262 - INFO - Request Parameters - Page 5:
2025-06-14 08:04:57,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:57,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800122, 1746547200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:58,027 - INFO - API请求耗时: 766ms
2025-06-14 08:04:58,027 - INFO - Response - Page 5
2025-06-14 08:04:58,027 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:04:58,527 - INFO - Request Parameters - Page 6:
2025-06-14 08:04:58,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:58,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800122, 1746547200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:04:59,308 - INFO - API请求耗时: 781ms
2025-06-14 08:04:59,308 - INFO - Response - Page 6
2025-06-14 08:04:59,308 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:04:59,808 - INFO - Request Parameters - Page 7:
2025-06-14 08:04:59,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:04:59,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800122, 1746547200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:00,574 - INFO - API请求耗时: 766ms
2025-06-14 08:05:00,574 - INFO - Response - Page 7
2025-06-14 08:05:00,574 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:05:01,074 - INFO - Request Parameters - Page 8:
2025-06-14 08:05:01,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:01,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800122, 1746547200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:02,246 - INFO - API请求耗时: 1172ms
2025-06-14 08:05:02,246 - INFO - Response - Page 8
2025-06-14 08:05:02,246 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:05:02,762 - INFO - Request Parameters - Page 9:
2025-06-14 08:05:02,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:02,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800122, 1746547200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:03,387 - INFO - API请求耗时: 625ms
2025-06-14 08:05:03,387 - INFO - Response - Page 9
2025-06-14 08:05:03,387 - INFO - 第 9 页获取到 28 条记录
2025-06-14 08:05:03,387 - INFO - 查询完成，共获取到 828 条记录
2025-06-14 08:05:03,387 - INFO - 分段 12 查询成功，获取到 828 条记录
2025-06-14 08:05:04,402 - INFO - 查询分段 13: 2025-05-08 至 2025-05-09
2025-06-14 08:05:04,402 - INFO - 查询日期范围: 2025-05-08 至 2025-05-09，使用分页查询，每页 100 条记录
2025-06-14 08:05:04,402 - INFO - Request Parameters - Page 1:
2025-06-14 08:05:04,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:04,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600122, 1746720000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:05,183 - INFO - API请求耗时: 781ms
2025-06-14 08:05:05,183 - INFO - Response - Page 1
2025-06-14 08:05:05,183 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:05:05,683 - INFO - Request Parameters - Page 2:
2025-06-14 08:05:05,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:05,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600122, 1746720000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:06,465 - INFO - API请求耗时: 781ms
2025-06-14 08:05:06,465 - INFO - Response - Page 2
2025-06-14 08:05:06,465 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:05:06,980 - INFO - Request Parameters - Page 3:
2025-06-14 08:05:06,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:06,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600122, 1746720000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:07,746 - INFO - API请求耗时: 766ms
2025-06-14 08:05:07,746 - INFO - Response - Page 3
2025-06-14 08:05:07,746 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:05:08,246 - INFO - Request Parameters - Page 4:
2025-06-14 08:05:08,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:08,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600122, 1746720000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:08,996 - INFO - API请求耗时: 750ms
2025-06-14 08:05:08,996 - INFO - Response - Page 4
2025-06-14 08:05:09,012 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:05:09,527 - INFO - Request Parameters - Page 5:
2025-06-14 08:05:09,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:09,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600122, 1746720000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:10,277 - INFO - API请求耗时: 750ms
2025-06-14 08:05:10,277 - INFO - Response - Page 5
2025-06-14 08:05:10,277 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:05:10,793 - INFO - Request Parameters - Page 6:
2025-06-14 08:05:10,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:10,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600122, 1746720000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:11,496 - INFO - API请求耗时: 703ms
2025-06-14 08:05:11,496 - INFO - Response - Page 6
2025-06-14 08:05:11,496 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:05:12,012 - INFO - Request Parameters - Page 7:
2025-06-14 08:05:12,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:12,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600122, 1746720000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:12,871 - INFO - API请求耗时: 859ms
2025-06-14 08:05:12,871 - INFO - Response - Page 7
2025-06-14 08:05:12,871 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:05:13,371 - INFO - Request Parameters - Page 8:
2025-06-14 08:05:13,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:13,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600122, 1746720000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:14,058 - INFO - API请求耗时: 687ms
2025-06-14 08:05:14,058 - INFO - Response - Page 8
2025-06-14 08:05:14,058 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:05:14,574 - INFO - Request Parameters - Page 9:
2025-06-14 08:05:14,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:14,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600122, 1746720000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:15,168 - INFO - API请求耗时: 594ms
2025-06-14 08:05:15,168 - INFO - Response - Page 9
2025-06-14 08:05:15,168 - INFO - 第 9 页获取到 60 条记录
2025-06-14 08:05:15,168 - INFO - 查询完成，共获取到 860 条记录
2025-06-14 08:05:15,168 - INFO - 分段 13 查询成功，获取到 860 条记录
2025-06-14 08:05:16,168 - INFO - 查询分段 14: 2025-05-10 至 2025-05-11
2025-06-14 08:05:16,168 - INFO - 查询日期范围: 2025-05-10 至 2025-05-11，使用分页查询，每页 100 条记录
2025-06-14 08:05:16,168 - INFO - Request Parameters - Page 1:
2025-06-14 08:05:16,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:16,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400122, 1746892800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:16,840 - INFO - API请求耗时: 672ms
2025-06-14 08:05:16,840 - INFO - Response - Page 1
2025-06-14 08:05:16,855 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:05:17,371 - INFO - Request Parameters - Page 2:
2025-06-14 08:05:17,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:17,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400122, 1746892800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:18,074 - INFO - API请求耗时: 703ms
2025-06-14 08:05:18,074 - INFO - Response - Page 2
2025-06-14 08:05:18,074 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:05:18,590 - INFO - Request Parameters - Page 3:
2025-06-14 08:05:18,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:18,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400122, 1746892800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:19,340 - INFO - API请求耗时: 750ms
2025-06-14 08:05:19,340 - INFO - Response - Page 3
2025-06-14 08:05:19,340 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:05:19,855 - INFO - Request Parameters - Page 4:
2025-06-14 08:05:19,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:19,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400122, 1746892800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:20,558 - INFO - API请求耗时: 703ms
2025-06-14 08:05:20,558 - INFO - Response - Page 4
2025-06-14 08:05:20,558 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:05:21,058 - INFO - Request Parameters - Page 5:
2025-06-14 08:05:21,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:21,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400122, 1746892800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:21,824 - INFO - API请求耗时: 766ms
2025-06-14 08:05:21,824 - INFO - Response - Page 5
2025-06-14 08:05:21,824 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:05:22,324 - INFO - Request Parameters - Page 6:
2025-06-14 08:05:22,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:22,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400122, 1746892800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:23,027 - INFO - API请求耗时: 703ms
2025-06-14 08:05:23,027 - INFO - Response - Page 6
2025-06-14 08:05:23,027 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:05:23,527 - INFO - Request Parameters - Page 7:
2025-06-14 08:05:23,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:23,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400122, 1746892800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:24,199 - INFO - API请求耗时: 672ms
2025-06-14 08:05:24,199 - INFO - Response - Page 7
2025-06-14 08:05:24,199 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:05:24,715 - INFO - Request Parameters - Page 8:
2025-06-14 08:05:24,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:24,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400122, 1746892800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:25,449 - INFO - API请求耗时: 734ms
2025-06-14 08:05:25,449 - INFO - Response - Page 8
2025-06-14 08:05:25,449 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:05:25,949 - INFO - Request Parameters - Page 9:
2025-06-14 08:05:25,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:25,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746806400122, 1746892800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:26,652 - INFO - API请求耗时: 703ms
2025-06-14 08:05:26,652 - INFO - Response - Page 9
2025-06-14 08:05:26,652 - INFO - 第 9 页获取到 72 条记录
2025-06-14 08:05:26,652 - INFO - 查询完成，共获取到 872 条记录
2025-06-14 08:05:26,652 - INFO - 分段 14 查询成功，获取到 872 条记录
2025-06-14 08:05:27,668 - INFO - 查询分段 15: 2025-05-12 至 2025-05-13
2025-06-14 08:05:27,668 - INFO - 查询日期范围: 2025-05-12 至 2025-05-13，使用分页查询，每页 100 条记录
2025-06-14 08:05:27,668 - INFO - Request Parameters - Page 1:
2025-06-14 08:05:27,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:27,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200122, 1747065600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:28,418 - INFO - API请求耗时: 750ms
2025-06-14 08:05:28,418 - INFO - Response - Page 1
2025-06-14 08:05:28,418 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:05:28,933 - INFO - Request Parameters - Page 2:
2025-06-14 08:05:28,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:28,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200122, 1747065600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:29,652 - INFO - API请求耗时: 719ms
2025-06-14 08:05:29,652 - INFO - Response - Page 2
2025-06-14 08:05:29,652 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:05:30,152 - INFO - Request Parameters - Page 3:
2025-06-14 08:05:30,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:30,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200122, 1747065600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:30,933 - INFO - API请求耗时: 781ms
2025-06-14 08:05:30,933 - INFO - Response - Page 3
2025-06-14 08:05:30,933 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:05:31,433 - INFO - Request Parameters - Page 4:
2025-06-14 08:05:31,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:31,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200122, 1747065600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:32,261 - INFO - API请求耗时: 828ms
2025-06-14 08:05:32,261 - INFO - Response - Page 4
2025-06-14 08:05:32,277 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:05:32,777 - INFO - Request Parameters - Page 5:
2025-06-14 08:05:32,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:32,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200122, 1747065600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:33,652 - INFO - API请求耗时: 875ms
2025-06-14 08:05:33,652 - INFO - Response - Page 5
2025-06-14 08:05:33,652 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:05:34,152 - INFO - Request Parameters - Page 6:
2025-06-14 08:05:34,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:34,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200122, 1747065600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:34,949 - INFO - API请求耗时: 797ms
2025-06-14 08:05:34,964 - INFO - Response - Page 6
2025-06-14 08:05:34,964 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:05:35,465 - INFO - Request Parameters - Page 7:
2025-06-14 08:05:35,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:35,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200122, 1747065600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:36,261 - INFO - API请求耗时: 797ms
2025-06-14 08:05:36,261 - INFO - Response - Page 7
2025-06-14 08:05:36,261 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:05:36,777 - INFO - Request Parameters - Page 8:
2025-06-14 08:05:36,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:36,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200122, 1747065600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:37,636 - INFO - API请求耗时: 859ms
2025-06-14 08:05:37,636 - INFO - Response - Page 8
2025-06-14 08:05:37,636 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:05:38,152 - INFO - Request Parameters - Page 9:
2025-06-14 08:05:38,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:38,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200122, 1747065600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:38,714 - INFO - API请求耗时: 562ms
2025-06-14 08:05:38,714 - INFO - Response - Page 9
2025-06-14 08:05:38,714 - INFO - 第 9 页获取到 40 条记录
2025-06-14 08:05:38,714 - INFO - 查询完成，共获取到 840 条记录
2025-06-14 08:05:38,714 - INFO - 分段 15 查询成功，获取到 840 条记录
2025-06-14 08:05:39,730 - INFO - 查询分段 16: 2025-05-14 至 2025-05-15
2025-06-14 08:05:39,730 - INFO - 查询日期范围: 2025-05-14 至 2025-05-15，使用分页查询，每页 100 条记录
2025-06-14 08:05:39,730 - INFO - Request Parameters - Page 1:
2025-06-14 08:05:39,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:39,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000122, 1747238400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:40,527 - INFO - API请求耗时: 797ms
2025-06-14 08:05:40,527 - INFO - Response - Page 1
2025-06-14 08:05:40,527 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:05:41,043 - INFO - Request Parameters - Page 2:
2025-06-14 08:05:41,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:41,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000122, 1747238400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:41,777 - INFO - API请求耗时: 734ms
2025-06-14 08:05:41,777 - INFO - Response - Page 2
2025-06-14 08:05:41,777 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:05:42,277 - INFO - Request Parameters - Page 3:
2025-06-14 08:05:42,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:42,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000122, 1747238400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:43,011 - INFO - API请求耗时: 734ms
2025-06-14 08:05:43,011 - INFO - Response - Page 3
2025-06-14 08:05:43,011 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:05:43,511 - INFO - Request Parameters - Page 4:
2025-06-14 08:05:43,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:43,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000122, 1747238400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:44,324 - INFO - API请求耗时: 812ms
2025-06-14 08:05:44,324 - INFO - Response - Page 4
2025-06-14 08:05:44,324 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:05:44,824 - INFO - Request Parameters - Page 5:
2025-06-14 08:05:44,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:44,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000122, 1747238400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:45,543 - INFO - API请求耗时: 719ms
2025-06-14 08:05:45,543 - INFO - Response - Page 5
2025-06-14 08:05:45,543 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:05:46,043 - INFO - Request Parameters - Page 6:
2025-06-14 08:05:46,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:46,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000122, 1747238400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:46,730 - INFO - API请求耗时: 687ms
2025-06-14 08:05:46,730 - INFO - Response - Page 6
2025-06-14 08:05:46,730 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:05:47,246 - INFO - Request Parameters - Page 7:
2025-06-14 08:05:47,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:47,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000122, 1747238400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:47,980 - INFO - API请求耗时: 734ms
2025-06-14 08:05:47,980 - INFO - Response - Page 7
2025-06-14 08:05:47,980 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:05:48,480 - INFO - Request Parameters - Page 8:
2025-06-14 08:05:48,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:48,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000122, 1747238400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:49,168 - INFO - API请求耗时: 687ms
2025-06-14 08:05:49,168 - INFO - Response - Page 8
2025-06-14 08:05:49,168 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:05:49,668 - INFO - Request Parameters - Page 9:
2025-06-14 08:05:49,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:49,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747152000122, 1747238400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:50,199 - INFO - API请求耗时: 531ms
2025-06-14 08:05:50,199 - INFO - Response - Page 9
2025-06-14 08:05:50,199 - INFO - 第 9 页获取到 40 条记录
2025-06-14 08:05:50,199 - INFO - 查询完成，共获取到 840 条记录
2025-06-14 08:05:50,199 - INFO - 分段 16 查询成功，获取到 840 条记录
2025-06-14 08:05:51,199 - INFO - 查询分段 17: 2025-05-16 至 2025-05-17
2025-06-14 08:05:51,199 - INFO - 查询日期范围: 2025-05-16 至 2025-05-17，使用分页查询，每页 100 条记录
2025-06-14 08:05:51,199 - INFO - Request Parameters - Page 1:
2025-06-14 08:05:51,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:51,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800122, 1747411200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:51,902 - INFO - API请求耗时: 703ms
2025-06-14 08:05:51,902 - INFO - Response - Page 1
2025-06-14 08:05:51,902 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:05:52,418 - INFO - Request Parameters - Page 2:
2025-06-14 08:05:52,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:52,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800122, 1747411200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:53,136 - INFO - API请求耗时: 719ms
2025-06-14 08:05:53,152 - INFO - Response - Page 2
2025-06-14 08:05:53,152 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:05:53,652 - INFO - Request Parameters - Page 3:
2025-06-14 08:05:53,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:53,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800122, 1747411200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:54,402 - INFO - API请求耗时: 750ms
2025-06-14 08:05:54,402 - INFO - Response - Page 3
2025-06-14 08:05:54,402 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:05:54,917 - INFO - Request Parameters - Page 4:
2025-06-14 08:05:54,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:54,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800122, 1747411200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:55,683 - INFO - API请求耗时: 766ms
2025-06-14 08:05:55,683 - INFO - Response - Page 4
2025-06-14 08:05:55,683 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:05:56,183 - INFO - Request Parameters - Page 5:
2025-06-14 08:05:56,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:56,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800122, 1747411200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:56,917 - INFO - API请求耗时: 719ms
2025-06-14 08:05:56,917 - INFO - Response - Page 5
2025-06-14 08:05:56,917 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:05:57,417 - INFO - Request Parameters - Page 6:
2025-06-14 08:05:57,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:57,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800122, 1747411200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:58,136 - INFO - API请求耗时: 719ms
2025-06-14 08:05:58,136 - INFO - Response - Page 6
2025-06-14 08:05:58,136 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:05:58,636 - INFO - Request Parameters - Page 7:
2025-06-14 08:05:58,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:58,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800122, 1747411200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:05:59,355 - INFO - API请求耗时: 719ms
2025-06-14 08:05:59,355 - INFO - Response - Page 7
2025-06-14 08:05:59,355 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:05:59,871 - INFO - Request Parameters - Page 8:
2025-06-14 08:05:59,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:05:59,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800122, 1747411200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:00,636 - INFO - API请求耗时: 766ms
2025-06-14 08:06:00,636 - INFO - Response - Page 8
2025-06-14 08:06:00,636 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:06:01,136 - INFO - Request Parameters - Page 9:
2025-06-14 08:06:01,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:01,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800122, 1747411200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:01,855 - INFO - API请求耗时: 719ms
2025-06-14 08:06:01,855 - INFO - Response - Page 9
2025-06-14 08:06:01,855 - INFO - 第 9 页获取到 60 条记录
2025-06-14 08:06:01,855 - INFO - 查询完成，共获取到 860 条记录
2025-06-14 08:06:01,855 - INFO - 分段 17 查询成功，获取到 860 条记录
2025-06-14 08:06:02,855 - INFO - 查询分段 18: 2025-05-18 至 2025-05-19
2025-06-14 08:06:02,855 - INFO - 查询日期范围: 2025-05-18 至 2025-05-19，使用分页查询，每页 100 条记录
2025-06-14 08:06:02,855 - INFO - Request Parameters - Page 1:
2025-06-14 08:06:02,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:02,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600122, 1747584000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:03,574 - INFO - API请求耗时: 719ms
2025-06-14 08:06:03,574 - INFO - Response - Page 1
2025-06-14 08:06:03,574 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:06:04,074 - INFO - Request Parameters - Page 2:
2025-06-14 08:06:04,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:04,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600122, 1747584000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:04,746 - INFO - API请求耗时: 672ms
2025-06-14 08:06:04,746 - INFO - Response - Page 2
2025-06-14 08:06:04,746 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:06:05,246 - INFO - Request Parameters - Page 3:
2025-06-14 08:06:05,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:05,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600122, 1747584000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:06,011 - INFO - API请求耗时: 766ms
2025-06-14 08:06:06,011 - INFO - Response - Page 3
2025-06-14 08:06:06,011 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:06:06,527 - INFO - Request Parameters - Page 4:
2025-06-14 08:06:06,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:06,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600122, 1747584000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:07,308 - INFO - API请求耗时: 781ms
2025-06-14 08:06:07,308 - INFO - Response - Page 4
2025-06-14 08:06:07,308 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:06:07,824 - INFO - Request Parameters - Page 5:
2025-06-14 08:06:07,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:07,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600122, 1747584000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:08,558 - INFO - API请求耗时: 734ms
2025-06-14 08:06:08,558 - INFO - Response - Page 5
2025-06-14 08:06:08,558 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:06:09,074 - INFO - Request Parameters - Page 6:
2025-06-14 08:06:09,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:09,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600122, 1747584000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:09,808 - INFO - API请求耗时: 734ms
2025-06-14 08:06:09,808 - INFO - Response - Page 6
2025-06-14 08:06:09,808 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:06:10,324 - INFO - Request Parameters - Page 7:
2025-06-14 08:06:10,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:10,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600122, 1747584000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:11,042 - INFO - API请求耗时: 719ms
2025-06-14 08:06:11,042 - INFO - Response - Page 7
2025-06-14 08:06:11,042 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:06:11,558 - INFO - Request Parameters - Page 8:
2025-06-14 08:06:11,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:11,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600122, 1747584000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:12,370 - INFO - API请求耗时: 812ms
2025-06-14 08:06:12,370 - INFO - Response - Page 8
2025-06-14 08:06:12,370 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:06:12,871 - INFO - Request Parameters - Page 9:
2025-06-14 08:06:12,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:12,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600122, 1747584000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:13,433 - INFO - API请求耗时: 562ms
2025-06-14 08:06:13,433 - INFO - Response - Page 9
2025-06-14 08:06:13,433 - INFO - 第 9 页获取到 40 条记录
2025-06-14 08:06:13,433 - INFO - 查询完成，共获取到 840 条记录
2025-06-14 08:06:13,433 - INFO - 分段 18 查询成功，获取到 840 条记录
2025-06-14 08:06:14,449 - INFO - 查询分段 19: 2025-05-20 至 2025-05-21
2025-06-14 08:06:14,449 - INFO - 查询日期范围: 2025-05-20 至 2025-05-21，使用分页查询，每页 100 条记录
2025-06-14 08:06:14,449 - INFO - Request Parameters - Page 1:
2025-06-14 08:06:14,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:14,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400122, 1747756800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:15,120 - INFO - API请求耗时: 672ms
2025-06-14 08:06:15,120 - INFO - Response - Page 1
2025-06-14 08:06:15,120 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:06:15,636 - INFO - Request Parameters - Page 2:
2025-06-14 08:06:15,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:15,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400122, 1747756800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:16,355 - INFO - API请求耗时: 719ms
2025-06-14 08:06:16,355 - INFO - Response - Page 2
2025-06-14 08:06:16,355 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:06:16,855 - INFO - Request Parameters - Page 3:
2025-06-14 08:06:16,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:16,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400122, 1747756800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:17,605 - INFO - API请求耗时: 750ms
2025-06-14 08:06:17,605 - INFO - Response - Page 3
2025-06-14 08:06:17,605 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:06:18,105 - INFO - Request Parameters - Page 4:
2025-06-14 08:06:18,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:18,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400122, 1747756800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:18,839 - INFO - API请求耗时: 734ms
2025-06-14 08:06:18,839 - INFO - Response - Page 4
2025-06-14 08:06:18,839 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:06:19,339 - INFO - Request Parameters - Page 5:
2025-06-14 08:06:19,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:19,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400122, 1747756800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:20,089 - INFO - API请求耗时: 750ms
2025-06-14 08:06:20,089 - INFO - Response - Page 5
2025-06-14 08:06:20,089 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:06:20,605 - INFO - Request Parameters - Page 6:
2025-06-14 08:06:20,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:20,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400122, 1747756800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:21,339 - INFO - API请求耗时: 734ms
2025-06-14 08:06:21,339 - INFO - Response - Page 6
2025-06-14 08:06:21,339 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:06:21,839 - INFO - Request Parameters - Page 7:
2025-06-14 08:06:21,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:21,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400122, 1747756800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:22,574 - INFO - API请求耗时: 734ms
2025-06-14 08:06:22,574 - INFO - Response - Page 7
2025-06-14 08:06:22,574 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:06:23,089 - INFO - Request Parameters - Page 8:
2025-06-14 08:06:23,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:23,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400122, 1747756800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:23,839 - INFO - API请求耗时: 750ms
2025-06-14 08:06:23,839 - INFO - Response - Page 8
2025-06-14 08:06:23,839 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:06:24,339 - INFO - Request Parameters - Page 9:
2025-06-14 08:06:24,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:24,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400122, 1747756800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:24,886 - INFO - API请求耗时: 547ms
2025-06-14 08:06:24,886 - INFO - Response - Page 9
2025-06-14 08:06:24,886 - INFO - 第 9 页获取到 32 条记录
2025-06-14 08:06:24,886 - INFO - 查询完成，共获取到 832 条记录
2025-06-14 08:06:24,886 - INFO - 分段 19 查询成功，获取到 832 条记录
2025-06-14 08:06:25,902 - INFO - 查询分段 20: 2025-05-22 至 2025-05-23
2025-06-14 08:06:25,902 - INFO - 查询日期范围: 2025-05-22 至 2025-05-23，使用分页查询，每页 100 条记录
2025-06-14 08:06:25,902 - INFO - Request Parameters - Page 1:
2025-06-14 08:06:25,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:25,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200122, 1747929600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:26,620 - INFO - API请求耗时: 719ms
2025-06-14 08:06:26,620 - INFO - Response - Page 1
2025-06-14 08:06:26,620 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:06:27,136 - INFO - Request Parameters - Page 2:
2025-06-14 08:06:27,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:27,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200122, 1747929600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:27,933 - INFO - API请求耗时: 797ms
2025-06-14 08:06:27,933 - INFO - Response - Page 2
2025-06-14 08:06:27,933 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:06:28,433 - INFO - Request Parameters - Page 3:
2025-06-14 08:06:28,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:28,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200122, 1747929600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:29,214 - INFO - API请求耗时: 781ms
2025-06-14 08:06:29,214 - INFO - Response - Page 3
2025-06-14 08:06:29,214 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:06:29,730 - INFO - Request Parameters - Page 4:
2025-06-14 08:06:29,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:29,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200122, 1747929600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:30,402 - INFO - API请求耗时: 672ms
2025-06-14 08:06:30,402 - INFO - Response - Page 4
2025-06-14 08:06:30,402 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:06:30,902 - INFO - Request Parameters - Page 5:
2025-06-14 08:06:30,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:30,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200122, 1747929600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:31,605 - INFO - API请求耗时: 703ms
2025-06-14 08:06:31,605 - INFO - Response - Page 5
2025-06-14 08:06:31,605 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:06:32,105 - INFO - Request Parameters - Page 6:
2025-06-14 08:06:32,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:32,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200122, 1747929600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:32,839 - INFO - API请求耗时: 734ms
2025-06-14 08:06:32,855 - INFO - Response - Page 6
2025-06-14 08:06:32,855 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:06:33,370 - INFO - Request Parameters - Page 7:
2025-06-14 08:06:33,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:33,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200122, 1747929600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:34,120 - INFO - API请求耗时: 750ms
2025-06-14 08:06:34,120 - INFO - Response - Page 7
2025-06-14 08:06:34,120 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:06:34,636 - INFO - Request Parameters - Page 8:
2025-06-14 08:06:34,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:34,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200122, 1747929600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:35,339 - INFO - API请求耗时: 703ms
2025-06-14 08:06:35,339 - INFO - Response - Page 8
2025-06-14 08:06:35,339 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:06:35,855 - INFO - Request Parameters - Page 9:
2025-06-14 08:06:35,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:35,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747843200122, 1747929600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:36,480 - INFO - API请求耗时: 625ms
2025-06-14 08:06:36,480 - INFO - Response - Page 9
2025-06-14 08:06:36,480 - INFO - 第 9 页获取到 32 条记录
2025-06-14 08:06:36,480 - INFO - 查询完成，共获取到 832 条记录
2025-06-14 08:06:36,480 - INFO - 分段 20 查询成功，获取到 832 条记录
2025-06-14 08:06:37,495 - INFO - 查询分段 21: 2025-05-24 至 2025-05-25
2025-06-14 08:06:37,495 - INFO - 查询日期范围: 2025-05-24 至 2025-05-25，使用分页查询，每页 100 条记录
2025-06-14 08:06:37,495 - INFO - Request Parameters - Page 1:
2025-06-14 08:06:37,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:37,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000122, 1748102400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:38,183 - INFO - API请求耗时: 688ms
2025-06-14 08:06:38,183 - INFO - Response - Page 1
2025-06-14 08:06:38,183 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:06:38,698 - INFO - Request Parameters - Page 2:
2025-06-14 08:06:38,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:38,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000122, 1748102400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:39,386 - INFO - API请求耗时: 687ms
2025-06-14 08:06:39,386 - INFO - Response - Page 2
2025-06-14 08:06:39,386 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:06:39,902 - INFO - Request Parameters - Page 3:
2025-06-14 08:06:39,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:39,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000122, 1748102400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:40,573 - INFO - API请求耗时: 672ms
2025-06-14 08:06:40,573 - INFO - Response - Page 3
2025-06-14 08:06:40,573 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:06:41,089 - INFO - Request Parameters - Page 4:
2025-06-14 08:06:41,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:41,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000122, 1748102400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:41,870 - INFO - API请求耗时: 781ms
2025-06-14 08:06:41,870 - INFO - Response - Page 4
2025-06-14 08:06:41,870 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:06:42,370 - INFO - Request Parameters - Page 5:
2025-06-14 08:06:42,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:42,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000122, 1748102400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:43,042 - INFO - API请求耗时: 672ms
2025-06-14 08:06:43,042 - INFO - Response - Page 5
2025-06-14 08:06:43,042 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:06:43,558 - INFO - Request Parameters - Page 6:
2025-06-14 08:06:43,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:43,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000122, 1748102400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:44,261 - INFO - API请求耗时: 703ms
2025-06-14 08:06:44,261 - INFO - Response - Page 6
2025-06-14 08:06:44,261 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:06:44,777 - INFO - Request Parameters - Page 7:
2025-06-14 08:06:44,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:44,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000122, 1748102400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:45,448 - INFO - API请求耗时: 672ms
2025-06-14 08:06:45,448 - INFO - Response - Page 7
2025-06-14 08:06:45,448 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:06:45,964 - INFO - Request Parameters - Page 8:
2025-06-14 08:06:45,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:45,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000122, 1748102400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:46,714 - INFO - API请求耗时: 750ms
2025-06-14 08:06:46,714 - INFO - Response - Page 8
2025-06-14 08:06:46,714 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:06:47,230 - INFO - Request Parameters - Page 9:
2025-06-14 08:06:47,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:47,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748016000122, 1748102400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:47,870 - INFO - API请求耗时: 641ms
2025-06-14 08:06:47,870 - INFO - Response - Page 9
2025-06-14 08:06:47,870 - INFO - 第 9 页获取到 36 条记录
2025-06-14 08:06:47,870 - INFO - 查询完成，共获取到 836 条记录
2025-06-14 08:06:47,870 - INFO - 分段 21 查询成功，获取到 836 条记录
2025-06-14 08:06:48,870 - INFO - 查询分段 22: 2025-05-26 至 2025-05-27
2025-06-14 08:06:48,870 - INFO - 查询日期范围: 2025-05-26 至 2025-05-27，使用分页查询，每页 100 条记录
2025-06-14 08:06:48,870 - INFO - Request Parameters - Page 1:
2025-06-14 08:06:48,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:48,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800122, 1748275200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:49,636 - INFO - API请求耗时: 766ms
2025-06-14 08:06:49,636 - INFO - Response - Page 1
2025-06-14 08:06:49,636 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:06:50,136 - INFO - Request Parameters - Page 2:
2025-06-14 08:06:50,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:50,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800122, 1748275200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:50,839 - INFO - API请求耗时: 703ms
2025-06-14 08:06:50,839 - INFO - Response - Page 2
2025-06-14 08:06:50,839 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:06:51,355 - INFO - Request Parameters - Page 3:
2025-06-14 08:06:51,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:51,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800122, 1748275200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:52,058 - INFO - API请求耗时: 703ms
2025-06-14 08:06:52,058 - INFO - Response - Page 3
2025-06-14 08:06:52,058 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:06:52,573 - INFO - Request Parameters - Page 4:
2025-06-14 08:06:52,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:52,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800122, 1748275200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:53,261 - INFO - API请求耗时: 688ms
2025-06-14 08:06:53,261 - INFO - Response - Page 4
2025-06-14 08:06:53,261 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:06:53,776 - INFO - Request Parameters - Page 5:
2025-06-14 08:06:53,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:53,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800122, 1748275200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:54,511 - INFO - API请求耗时: 734ms
2025-06-14 08:06:54,511 - INFO - Response - Page 5
2025-06-14 08:06:54,511 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:06:55,011 - INFO - Request Parameters - Page 6:
2025-06-14 08:06:55,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:55,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800122, 1748275200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:55,745 - INFO - API请求耗时: 734ms
2025-06-14 08:06:55,745 - INFO - Response - Page 6
2025-06-14 08:06:55,745 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:06:56,245 - INFO - Request Parameters - Page 7:
2025-06-14 08:06:56,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:56,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800122, 1748275200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:56,917 - INFO - API请求耗时: 672ms
2025-06-14 08:06:56,917 - INFO - Response - Page 7
2025-06-14 08:06:56,917 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:06:57,417 - INFO - Request Parameters - Page 8:
2025-06-14 08:06:57,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:57,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800122, 1748275200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:58,120 - INFO - API请求耗时: 703ms
2025-06-14 08:06:58,120 - INFO - Response - Page 8
2025-06-14 08:06:58,120 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:06:58,620 - INFO - Request Parameters - Page 9:
2025-06-14 08:06:58,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:06:58,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748188800122, 1748275200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:06:59,151 - INFO - API请求耗时: 531ms
2025-06-14 08:06:59,151 - INFO - Response - Page 9
2025-06-14 08:06:59,151 - INFO - 第 9 页获取到 20 条记录
2025-06-14 08:06:59,151 - INFO - 查询完成，共获取到 820 条记录
2025-06-14 08:06:59,151 - INFO - 分段 22 查询成功，获取到 820 条记录
2025-06-14 08:07:00,151 - INFO - 查询分段 23: 2025-05-28 至 2025-05-29
2025-06-14 08:07:00,151 - INFO - 查询日期范围: 2025-05-28 至 2025-05-29，使用分页查询，每页 100 条记录
2025-06-14 08:07:00,151 - INFO - Request Parameters - Page 1:
2025-06-14 08:07:00,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:00,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600122, 1748448000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:00,870 - INFO - API请求耗时: 719ms
2025-06-14 08:07:00,870 - INFO - Response - Page 1
2025-06-14 08:07:00,870 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:07:01,386 - INFO - Request Parameters - Page 2:
2025-06-14 08:07:01,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:01,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600122, 1748448000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:02,105 - INFO - API请求耗时: 719ms
2025-06-14 08:07:02,105 - INFO - Response - Page 2
2025-06-14 08:07:02,105 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:07:02,620 - INFO - Request Parameters - Page 3:
2025-06-14 08:07:02,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:02,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600122, 1748448000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:03,448 - INFO - API请求耗时: 828ms
2025-06-14 08:07:03,448 - INFO - Response - Page 3
2025-06-14 08:07:03,448 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:07:03,948 - INFO - Request Parameters - Page 4:
2025-06-14 08:07:03,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:03,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600122, 1748448000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:04,651 - INFO - API请求耗时: 703ms
2025-06-14 08:07:04,651 - INFO - Response - Page 4
2025-06-14 08:07:04,651 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:07:05,151 - INFO - Request Parameters - Page 5:
2025-06-14 08:07:05,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:05,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600122, 1748448000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:05,870 - INFO - API请求耗时: 719ms
2025-06-14 08:07:05,870 - INFO - Response - Page 5
2025-06-14 08:07:05,870 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:07:06,386 - INFO - Request Parameters - Page 6:
2025-06-14 08:07:06,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:06,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600122, 1748448000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:07,120 - INFO - API请求耗时: 734ms
2025-06-14 08:07:07,120 - INFO - Response - Page 6
2025-06-14 08:07:07,120 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:07:07,620 - INFO - Request Parameters - Page 7:
2025-06-14 08:07:07,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:07,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600122, 1748448000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:08,292 - INFO - API请求耗时: 672ms
2025-06-14 08:07:08,292 - INFO - Response - Page 7
2025-06-14 08:07:08,292 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:07:08,792 - INFO - Request Parameters - Page 8:
2025-06-14 08:07:08,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:08,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600122, 1748448000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:09,526 - INFO - API请求耗时: 734ms
2025-06-14 08:07:09,526 - INFO - Response - Page 8
2025-06-14 08:07:09,542 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:07:10,042 - INFO - Request Parameters - Page 9:
2025-06-14 08:07:10,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:10,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748361600122, 1748448000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:10,573 - INFO - API请求耗时: 531ms
2025-06-14 08:07:10,573 - INFO - Response - Page 9
2025-06-14 08:07:10,573 - INFO - 第 9 页获取到 24 条记录
2025-06-14 08:07:10,573 - INFO - 查询完成，共获取到 824 条记录
2025-06-14 08:07:10,573 - INFO - 分段 23 查询成功，获取到 824 条记录
2025-06-14 08:07:11,589 - INFO - 查询分段 24: 2025-05-30 至 2025-05-31
2025-06-14 08:07:11,589 - INFO - 查询日期范围: 2025-05-30 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-14 08:07:11,589 - INFO - Request Parameters - Page 1:
2025-06-14 08:07:11,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:11,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400122, 1748620800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:12,308 - INFO - API请求耗时: 719ms
2025-06-14 08:07:12,308 - INFO - Response - Page 1
2025-06-14 08:07:12,308 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:07:12,823 - INFO - Request Parameters - Page 2:
2025-06-14 08:07:12,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:12,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400122, 1748620800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:13,526 - INFO - API请求耗时: 703ms
2025-06-14 08:07:13,526 - INFO - Response - Page 2
2025-06-14 08:07:13,526 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:07:14,026 - INFO - Request Parameters - Page 3:
2025-06-14 08:07:14,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:14,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400122, 1748620800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:14,808 - INFO - API请求耗时: 781ms
2025-06-14 08:07:14,823 - INFO - Response - Page 3
2025-06-14 08:07:14,823 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:07:15,339 - INFO - Request Parameters - Page 4:
2025-06-14 08:07:15,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:15,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400122, 1748620800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:16,136 - INFO - API请求耗时: 797ms
2025-06-14 08:07:16,136 - INFO - Response - Page 4
2025-06-14 08:07:16,136 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:07:16,651 - INFO - Request Parameters - Page 5:
2025-06-14 08:07:16,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:16,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400122, 1748620800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:17,386 - INFO - API请求耗时: 734ms
2025-06-14 08:07:17,386 - INFO - Response - Page 5
2025-06-14 08:07:17,386 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:07:17,901 - INFO - Request Parameters - Page 6:
2025-06-14 08:07:17,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:17,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400122, 1748620800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:18,667 - INFO - API请求耗时: 766ms
2025-06-14 08:07:18,667 - INFO - Response - Page 6
2025-06-14 08:07:18,667 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:07:19,183 - INFO - Request Parameters - Page 7:
2025-06-14 08:07:19,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:19,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400122, 1748620800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:19,886 - INFO - API请求耗时: 703ms
2025-06-14 08:07:19,886 - INFO - Response - Page 7
2025-06-14 08:07:19,901 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:07:20,401 - INFO - Request Parameters - Page 8:
2025-06-14 08:07:20,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:20,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400122, 1748620800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:21,073 - INFO - API请求耗时: 672ms
2025-06-14 08:07:21,073 - INFO - Response - Page 8
2025-06-14 08:07:21,073 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:07:21,589 - INFO - Request Parameters - Page 9:
2025-06-14 08:07:21,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:21,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748534400122, 1748620800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:22,198 - INFO - API请求耗时: 609ms
2025-06-14 08:07:22,198 - INFO - Response - Page 9
2025-06-14 08:07:22,198 - INFO - 第 9 页获取到 36 条记录
2025-06-14 08:07:22,198 - INFO - 查询完成，共获取到 836 条记录
2025-06-14 08:07:22,198 - INFO - 分段 24 查询成功，获取到 836 条记录
2025-06-14 08:07:23,198 - INFO - 查询分段 25: 2025-06-01 至 2025-06-02
2025-06-14 08:07:23,198 - INFO - 查询日期范围: 2025-06-01 至 2025-06-02，使用分页查询，每页 100 条记录
2025-06-14 08:07:23,198 - INFO - Request Parameters - Page 1:
2025-06-14 08:07:23,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:23,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200122, 1748793600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:23,870 - INFO - API请求耗时: 672ms
2025-06-14 08:07:23,870 - INFO - Response - Page 1
2025-06-14 08:07:23,886 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:07:24,386 - INFO - Request Parameters - Page 2:
2025-06-14 08:07:24,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:24,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200122, 1748793600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:25,089 - INFO - API请求耗时: 703ms
2025-06-14 08:07:25,089 - INFO - Response - Page 2
2025-06-14 08:07:25,104 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:07:25,604 - INFO - Request Parameters - Page 3:
2025-06-14 08:07:25,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:25,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200122, 1748793600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:26,292 - INFO - API请求耗时: 687ms
2025-06-14 08:07:26,292 - INFO - Response - Page 3
2025-06-14 08:07:26,292 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:07:26,808 - INFO - Request Parameters - Page 4:
2025-06-14 08:07:26,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:26,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200122, 1748793600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:27,542 - INFO - API请求耗时: 734ms
2025-06-14 08:07:27,542 - INFO - Response - Page 4
2025-06-14 08:07:27,542 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:07:28,042 - INFO - Request Parameters - Page 5:
2025-06-14 08:07:28,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:28,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200122, 1748793600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:28,745 - INFO - API请求耗时: 703ms
2025-06-14 08:07:28,745 - INFO - Response - Page 5
2025-06-14 08:07:28,745 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:07:29,276 - INFO - Request Parameters - Page 6:
2025-06-14 08:07:29,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:29,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200122, 1748793600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:29,979 - INFO - API请求耗时: 703ms
2025-06-14 08:07:29,979 - INFO - Response - Page 6
2025-06-14 08:07:29,979 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:07:30,495 - INFO - Request Parameters - Page 7:
2025-06-14 08:07:30,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:30,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200122, 1748793600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:31,276 - INFO - API请求耗时: 781ms
2025-06-14 08:07:31,276 - INFO - Response - Page 7
2025-06-14 08:07:31,276 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:07:31,776 - INFO - Request Parameters - Page 8:
2025-06-14 08:07:31,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:31,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200122, 1748793600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:32,542 - INFO - API请求耗时: 766ms
2025-06-14 08:07:32,542 - INFO - Response - Page 8
2025-06-14 08:07:32,542 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:07:33,057 - INFO - Request Parameters - Page 9:
2025-06-14 08:07:33,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:33,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200122, 1748793600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:33,479 - INFO - API请求耗时: 422ms
2025-06-14 08:07:33,479 - INFO - Response - Page 9
2025-06-14 08:07:33,479 - INFO - 第 9 页获取到 4 条记录
2025-06-14 08:07:33,479 - INFO - 查询完成，共获取到 804 条记录
2025-06-14 08:07:33,479 - INFO - 分段 25 查询成功，获取到 804 条记录
2025-06-14 08:07:34,479 - INFO - 查询分段 26: 2025-06-03 至 2025-06-04
2025-06-14 08:07:34,479 - INFO - 查询日期范围: 2025-06-03 至 2025-06-04，使用分页查询，每页 100 条记录
2025-06-14 08:07:34,479 - INFO - Request Parameters - Page 1:
2025-06-14 08:07:34,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:34,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000122, 1748966400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:35,136 - INFO - API请求耗时: 656ms
2025-06-14 08:07:35,136 - INFO - Response - Page 1
2025-06-14 08:07:35,136 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:07:35,651 - INFO - Request Parameters - Page 2:
2025-06-14 08:07:35,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:35,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000122, 1748966400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:36,386 - INFO - API请求耗时: 734ms
2025-06-14 08:07:36,386 - INFO - Response - Page 2
2025-06-14 08:07:36,386 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:07:36,901 - INFO - Request Parameters - Page 3:
2025-06-14 08:07:36,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:36,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000122, 1748966400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:37,651 - INFO - API请求耗时: 750ms
2025-06-14 08:07:37,651 - INFO - Response - Page 3
2025-06-14 08:07:37,651 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:07:38,151 - INFO - Request Parameters - Page 4:
2025-06-14 08:07:38,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:38,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000122, 1748966400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:38,854 - INFO - API请求耗时: 703ms
2025-06-14 08:07:38,854 - INFO - Response - Page 4
2025-06-14 08:07:38,854 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:07:39,370 - INFO - Request Parameters - Page 5:
2025-06-14 08:07:39,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:39,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000122, 1748966400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:40,089 - INFO - API请求耗时: 719ms
2025-06-14 08:07:40,104 - INFO - Response - Page 5
2025-06-14 08:07:40,104 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:07:40,604 - INFO - Request Parameters - Page 6:
2025-06-14 08:07:40,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:40,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000122, 1748966400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:41,354 - INFO - API请求耗时: 750ms
2025-06-14 08:07:41,354 - INFO - Response - Page 6
2025-06-14 08:07:41,354 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:07:41,854 - INFO - Request Parameters - Page 7:
2025-06-14 08:07:41,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:41,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000122, 1748966400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:42,542 - INFO - API请求耗时: 687ms
2025-06-14 08:07:42,542 - INFO - Response - Page 7
2025-06-14 08:07:42,542 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:07:43,042 - INFO - Request Parameters - Page 8:
2025-06-14 08:07:43,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:43,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748880000122, 1748966400122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:43,792 - INFO - API请求耗时: 750ms
2025-06-14 08:07:43,792 - INFO - Response - Page 8
2025-06-14 08:07:43,792 - INFO - 第 8 页获取到 96 条记录
2025-06-14 08:07:43,792 - INFO - 查询完成，共获取到 796 条记录
2025-06-14 08:07:43,792 - INFO - 分段 26 查询成功，获取到 796 条记录
2025-06-14 08:07:44,792 - INFO - 查询分段 27: 2025-06-05 至 2025-06-06
2025-06-14 08:07:44,792 - INFO - 查询日期范围: 2025-06-05 至 2025-06-06，使用分页查询，每页 100 条记录
2025-06-14 08:07:44,792 - INFO - Request Parameters - Page 1:
2025-06-14 08:07:44,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:44,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800122, 1749139200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:45,557 - INFO - API请求耗时: 766ms
2025-06-14 08:07:45,557 - INFO - Response - Page 1
2025-06-14 08:07:45,557 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:07:46,057 - INFO - Request Parameters - Page 2:
2025-06-14 08:07:46,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:46,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800122, 1749139200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:46,745 - INFO - API请求耗时: 687ms
2025-06-14 08:07:46,745 - INFO - Response - Page 2
2025-06-14 08:07:46,745 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:07:47,245 - INFO - Request Parameters - Page 3:
2025-06-14 08:07:47,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:47,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800122, 1749139200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:47,917 - INFO - API请求耗时: 672ms
2025-06-14 08:07:47,917 - INFO - Response - Page 3
2025-06-14 08:07:47,917 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:07:48,417 - INFO - Request Parameters - Page 4:
2025-06-14 08:07:48,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:48,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800122, 1749139200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:49,229 - INFO - API请求耗时: 812ms
2025-06-14 08:07:49,229 - INFO - Response - Page 4
2025-06-14 08:07:49,229 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:07:49,745 - INFO - Request Parameters - Page 5:
2025-06-14 08:07:49,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:49,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800122, 1749139200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:50,620 - INFO - API请求耗时: 875ms
2025-06-14 08:07:50,620 - INFO - Response - Page 5
2025-06-14 08:07:50,620 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:07:51,120 - INFO - Request Parameters - Page 6:
2025-06-14 08:07:51,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:51,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800122, 1749139200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:51,776 - INFO - API请求耗时: 656ms
2025-06-14 08:07:51,776 - INFO - Response - Page 6
2025-06-14 08:07:51,776 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:07:52,276 - INFO - Request Parameters - Page 7:
2025-06-14 08:07:52,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:52,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800122, 1749139200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:52,979 - INFO - API请求耗时: 703ms
2025-06-14 08:07:52,979 - INFO - Response - Page 7
2025-06-14 08:07:52,979 - INFO - 第 7 页获取到 100 条记录
2025-06-14 08:07:53,495 - INFO - Request Parameters - Page 8:
2025-06-14 08:07:53,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:53,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800122, 1749139200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:54,276 - INFO - API请求耗时: 781ms
2025-06-14 08:07:54,276 - INFO - Response - Page 8
2025-06-14 08:07:54,276 - INFO - 第 8 页获取到 100 条记录
2025-06-14 08:07:54,792 - INFO - Request Parameters - Page 9:
2025-06-14 08:07:54,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:54,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749052800122, 1749139200122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:55,307 - INFO - API请求耗时: 516ms
2025-06-14 08:07:55,307 - INFO - Response - Page 9
2025-06-14 08:07:55,307 - INFO - 第 9 页获取到 18 条记录
2025-06-14 08:07:55,307 - INFO - 查询完成，共获取到 818 条记录
2025-06-14 08:07:55,307 - INFO - 分段 27 查询成功，获取到 818 条记录
2025-06-14 08:07:56,323 - INFO - 查询分段 28: 2025-06-07 至 2025-06-08
2025-06-14 08:07:56,323 - INFO - 查询日期范围: 2025-06-07 至 2025-06-08，使用分页查询，每页 100 条记录
2025-06-14 08:07:56,323 - INFO - Request Parameters - Page 1:
2025-06-14 08:07:56,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:56,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600122, 1749312000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:57,073 - INFO - API请求耗时: 750ms
2025-06-14 08:07:57,073 - INFO - Response - Page 1
2025-06-14 08:07:57,073 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:07:57,589 - INFO - Request Parameters - Page 2:
2025-06-14 08:07:57,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:57,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600122, 1749312000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:58,260 - INFO - API请求耗时: 672ms
2025-06-14 08:07:58,260 - INFO - Response - Page 2
2025-06-14 08:07:58,260 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:07:58,760 - INFO - Request Parameters - Page 3:
2025-06-14 08:07:58,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:07:58,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600122, 1749312000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:07:59,589 - INFO - API请求耗时: 828ms
2025-06-14 08:07:59,589 - INFO - Response - Page 3
2025-06-14 08:07:59,589 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:08:00,089 - INFO - Request Parameters - Page 4:
2025-06-14 08:08:00,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:08:00,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600122, 1749312000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:08:00,839 - INFO - API请求耗时: 750ms
2025-06-14 08:08:00,839 - INFO - Response - Page 4
2025-06-14 08:08:00,839 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:08:01,354 - INFO - Request Parameters - Page 5:
2025-06-14 08:08:01,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:08:01,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600122, 1749312000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:08:02,073 - INFO - API请求耗时: 719ms
2025-06-14 08:08:02,073 - INFO - Response - Page 5
2025-06-14 08:08:02,073 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:08:02,589 - INFO - Request Parameters - Page 6:
2025-06-14 08:08:02,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:08:02,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600122, 1749312000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:08:03,339 - INFO - API请求耗时: 750ms
2025-06-14 08:08:03,339 - INFO - Response - Page 6
2025-06-14 08:08:03,339 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:08:03,839 - INFO - Request Parameters - Page 7:
2025-06-14 08:08:03,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:08:03,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749225600122, 1749312000122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:08:04,338 - INFO - API请求耗时: 500ms
2025-06-14 08:08:04,338 - INFO - Response - Page 7
2025-06-14 08:08:04,338 - INFO - 第 7 页获取到 10 条记录
2025-06-14 08:08:04,338 - INFO - 查询完成，共获取到 610 条记录
2025-06-14 08:08:04,338 - INFO - 分段 28 查询成功，获取到 610 条记录
2025-06-14 08:08:05,339 - INFO - 查询分段 29: 2025-06-09 至 2025-06-10
2025-06-14 08:08:05,339 - INFO - 查询日期范围: 2025-06-09 至 2025-06-10，使用分页查询，每页 100 条记录
2025-06-14 08:08:05,339 - INFO - Request Parameters - Page 1:
2025-06-14 08:08:05,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:08:05,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749398400122, 1749484800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:08:06,057 - INFO - API请求耗时: 719ms
2025-06-14 08:08:06,057 - INFO - Response - Page 1
2025-06-14 08:08:06,057 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:08:06,557 - INFO - Request Parameters - Page 2:
2025-06-14 08:08:06,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:08:06,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749398400122, 1749484800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:08:07,276 - INFO - API请求耗时: 719ms
2025-06-14 08:08:07,276 - INFO - Response - Page 2
2025-06-14 08:08:07,276 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:08:07,792 - INFO - Request Parameters - Page 3:
2025-06-14 08:08:07,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:08:07,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749398400122, 1749484800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:08:08,495 - INFO - API请求耗时: 703ms
2025-06-14 08:08:08,495 - INFO - Response - Page 3
2025-06-14 08:08:08,495 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:08:08,995 - INFO - Request Parameters - Page 4:
2025-06-14 08:08:08,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:08:08,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749398400122, 1749484800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:08:09,682 - INFO - API请求耗时: 687ms
2025-06-14 08:08:09,682 - INFO - Response - Page 4
2025-06-14 08:08:09,682 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:08:10,182 - INFO - Request Parameters - Page 5:
2025-06-14 08:08:10,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:08:10,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749398400122, 1749484800122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:08:10,620 - INFO - API请求耗时: 437ms
2025-06-14 08:08:10,620 - INFO - Response - Page 5
2025-06-14 08:08:10,620 - INFO - 第 5 页获取到 8 条记录
2025-06-14 08:08:10,620 - INFO - 查询完成，共获取到 408 条记录
2025-06-14 08:08:10,620 - INFO - 分段 29 查询成功，获取到 408 条记录
2025-06-14 08:08:11,635 - INFO - 查询分段 30: 2025-06-11 至 2025-06-12
2025-06-14 08:08:11,635 - INFO - 查询日期范围: 2025-06-11 至 2025-06-12，使用分页查询，每页 100 条记录
2025-06-14 08:08:11,635 - INFO - Request Parameters - Page 1:
2025-06-14 08:08:11,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:08:11,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749571200122, 1749657600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:08:12,323 - INFO - API请求耗时: 688ms
2025-06-14 08:08:12,323 - INFO - Response - Page 1
2025-06-14 08:08:12,323 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:08:12,838 - INFO - Request Parameters - Page 2:
2025-06-14 08:08:12,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:08:12,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749571200122, 1749657600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:08:13,542 - INFO - API请求耗时: 703ms
2025-06-14 08:08:13,542 - INFO - Response - Page 2
2025-06-14 08:08:13,542 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:08:14,042 - INFO - Request Parameters - Page 3:
2025-06-14 08:08:14,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:08:14,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749571200122, 1749657600122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:08:14,495 - INFO - API请求耗时: 453ms
2025-06-14 08:08:14,495 - INFO - Response - Page 3
2025-06-14 08:08:14,495 - INFO - 第 3 页获取到 4 条记录
2025-06-14 08:08:14,495 - INFO - 查询完成，共获取到 204 条记录
2025-06-14 08:08:14,495 - INFO - 分段 30 查询成功，获取到 204 条记录
2025-06-14 08:08:15,510 - INFO - 查询分段 31: 2025-06-13 至 2025-06-13
2025-06-14 08:08:15,510 - INFO - 查询日期范围: 2025-06-13 至 2025-06-13，使用分页查询，每页 100 条记录
2025-06-14 08:08:15,510 - INFO - Request Parameters - Page 1:
2025-06-14 08:08:15,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:08:15,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749744000122, 1749830399122], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:08:15,917 - INFO - API请求耗时: 406ms
2025-06-14 08:08:15,917 - INFO - Response - Page 1
2025-06-14 08:08:15,917 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-14 08:08:15,917 - INFO - 查询完成，共获取到 0 条记录
2025-06-14 08:08:15,917 - WARNING - 分段 31 查询返回空数据
2025-06-14 08:08:16,932 - INFO - 宜搭每日表单数据查询完成，共 31 个分段，成功获取 23948 条记录，失败 0 次
2025-06-14 08:08:16,932 - INFO - 成功获取宜搭日销售表单数据，共 23948 条记录
2025-06-14 08:08:16,932 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-14 08:08:16,932 - INFO - 开始对比和同步日销售数据...
2025-06-14 08:08:17,588 - INFO - 成功创建宜搭日销售数据索引，共 6295 条记录
2025-06-14 08:08:17,588 - INFO - 开始处理数衍数据，共 12806 条记录
2025-06-14 08:08:18,167 - INFO - 更新表单数据成功: FINST-AJF66F719G4WFVO0ENZ5J5VJKZXB2PJ3HONBME7
2025-06-14 08:08:18,167 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:18,776 - INFO - 更新表单数据成功: FINST-XMC66R91F82WBFRKBBK425YUDFIT3E2EHONBMA8
2025-06-14 08:08:18,776 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:19,385 - INFO - 更新表单数据成功: FINST-AEF66BC1YG3WDYXNBUDMCB495HIA2SERHONBMFK
2025-06-14 08:08:19,385 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:20,026 - INFO - 更新表单数据成功: FINST-3PF66O71GD2W5NAE9ZU0DB6OFGWZ1H62IONBMMU
2025-06-14 08:08:20,026 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:20,682 - INFO - 更新表单数据成功: FINST-RNA66D71TK4WR4MPDBPTLALDK3972ASCIONBMJ4
2025-06-14 08:08:20,682 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:21,245 - INFO - 更新表单数据成功: FINST-CPC66T91ES2WWWNFBY0V34LOOG8S2T9NIONBM7V
2025-06-14 08:08:21,245 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:21,932 - INFO - 更新表单数据成功: FINST-MLF669B1V72WB3UPDP9GGDYXA5HK35K0JONBM0T
2025-06-14 08:08:21,932 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:22,417 - INFO - 更新表单数据成功: FINST-8PF66V71AO2W6IZ0B7C6N6SZQ64K2X63JONBMUC
2025-06-14 08:08:22,417 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_********, 变更字段: [{'field': 'amount', 'old_value': 376.37, 'new_value': 2849.5499999999997}, {'field': 'count', 'old_value': 17, 'new_value': 95}, {'field': 'onlineAmount', 'old_value': 376.37, 'new_value': 2888.64}, {'field': 'onlineCount', 'old_value': 17, 'new_value': 95}]
2025-06-14 08:08:23,073 - INFO - 更新表单数据成功: FINST-68E66TC13N4W2RZNFZIRV7O8JOTM2V8BJONBMX6
2025-06-14 08:08:23,073 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:23,526 - INFO - 更新表单数据成功: FINST-2PF662C1CD2W71HPD54FXCZ5RU4Z2KWDJONBMB01
2025-06-14 08:08:23,526 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_********, 变更字段: [{'field': 'amount', 'old_value': 315.16, 'new_value': 9810.79}, {'field': 'count', 'old_value': 14, 'new_value': 212}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 8146.0}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 146}, {'field': 'onlineAmount', 'old_value': 315.16, 'new_value': 1664.79}, {'field': 'onlineCount', 'old_value': 14, 'new_value': 66}]
2025-06-14 08:08:24,104 - INFO - 更新表单数据成功: FINST-80B66291BN4W41PVEO13U73FHO3N2A3MJONBMI4
2025-06-14 08:08:24,104 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:24,573 - INFO - 更新表单数据成功: FINST-LI666MB11Q4WFQWEDVLA2BCEAILK26QOJONBMX5
2025-06-14 08:08:24,573 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_********, 变更字段: [{'field': 'amount', 'old_value': 531.51, 'new_value': 2396.96}, {'field': 'count', 'old_value': 16, 'new_value': 63}, {'field': 'onlineAmount', 'old_value': 531.51, 'new_value': 2396.96}, {'field': 'onlineCount', 'old_value': 16, 'new_value': 63}]
2025-06-14 08:08:25,166 - INFO - 更新表单数据成功: FINST-MLF669B1CC2WOCXW72HLTDMKOM1M3GNZJONBMX6
2025-06-14 08:08:25,166 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:25,776 - INFO - 更新表单数据成功: FINST-LLF66F714T2W4MRADVHVXAL5N8H52EDAKONBM731
2025-06-14 08:08:25,776 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:26,370 - INFO - 更新表单数据成功: FINST-LLF668810P4WMXJW807ZN90Z7UL62A2LKONBMW2
2025-06-14 08:08:26,370 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:27,088 - INFO - 更新表单数据成功: FINST-F3G66Q61BP4WCYL86ZYRNA7T5YWT3FOVKONBM14
2025-06-14 08:08:27,088 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:27,713 - INFO - 更新表单数据成功: FINST-AEF66BC1MQ2WLWP1CCKHHCZR5AQU33X8LONBMJ01
2025-06-14 08:08:27,713 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:28,370 - INFO - 更新表单数据成功: FINST-90E66JD16A2W33GN6R0KS5N43LIF33SJLONBMET
2025-06-14 08:08:28,370 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:28,963 - INFO - 更新表单数据成功: FINST-BCC66FB1UP4WTFEX74XLFBV5NF0T36KULONBMQ3
2025-06-14 08:08:28,963 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:29,557 - INFO - 更新表单数据成功: FINST-2HF66O615A2WVEHJAD4YR7HFDH1H2ID5MONBMTW
2025-06-14 08:08:29,557 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:30,213 - INFO - 更新表单数据成功: FINST-1T666B91BT1WLX9QDJTQLAST92PC2CPIMONBM1A1
2025-06-14 08:08:30,213 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:30,854 - INFO - 更新表单数据成功: FINST-OLC66Z61HD2W9CBS99BTP5OKDIR72JDTMONBMD51
2025-06-14 08:08:30,854 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:31,448 - INFO - 更新表单数据成功: FINST-XRF66A81YK4W29989SZTAD6FS81F3A34NONBMT4
2025-06-14 08:08:31,448 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:32,088 - INFO - 更新表单数据成功: FINST-RI766091YT2WHLYIDD4CED8TS17K2MRENONBM391
2025-06-14 08:08:32,088 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:32,682 - INFO - 更新表单数据成功: FINST-8LC66GC1HN4WE69K7XLO7CVFE4WO3JKPNONBMX3
2025-06-14 08:08:32,682 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:33,245 - INFO - 更新表单数据成功: FINST-I3F66991OA2WA02SCFXQ48LINMPN2EASNONBMQ4
2025-06-14 08:08:33,245 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10105.68}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10105.68}, {'field': 'amount', 'old_value': 275.99, 'new_value': 9994.77}, {'field': 'count', 'old_value': 11, 'new_value': 205}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 7914.9}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 129}, {'field': 'onlineAmount', 'old_value': 275.99, 'new_value': 2112.75}, {'field': 'onlineCount', 'old_value': 11, 'new_value': 76}]
2025-06-14 08:08:33,807 - INFO - 更新表单数据成功: FINST-DIC66I91SB2WOQNF8NDQSDMMU2R239C0OONBM8U
2025-06-14 08:08:33,807 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:34,307 - INFO - 更新表单数据成功: FINST-OPC666D1BD2WAFQSD0AB47CWOSJZ1O23OONBMY41
2025-06-14 08:08:34,307 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10929.37}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10929.37}, {'field': 'amount', 'old_value': 307.58, 'new_value': 10393.98}, {'field': 'count', 'old_value': 13, 'new_value': 235}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 7702.4}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 141}, {'field': 'onlineAmount', 'old_value': 307.58, 'new_value': 2834.02}, {'field': 'onlineCount', 'old_value': 13, 'new_value': 94}]
2025-06-14 08:08:34,916 - INFO - 更新表单数据成功: FINST-YPE66RB1A92WXO35CW6BYC1G3YDL2KPDOONBMS7
2025-06-14 08:08:34,916 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:35,338 - INFO - 更新表单数据成功: FINST-YPE66RB1A92WXO35CW6BYC1G3YDL2LPDOONBMEA
2025-06-14 08:08:35,338 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8799.24}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8799.24}, {'field': 'amount', 'old_value': 249.63, 'new_value': 8777.15}, {'field': 'count', 'old_value': 7, 'new_value': 129}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 7580.7}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 92}, {'field': 'onlineAmount', 'old_value': 249.63, 'new_value': 1219.43}, {'field': 'onlineCount', 'old_value': 7, 'new_value': 37}]
2025-06-14 08:08:35,916 - INFO - 更新表单数据成功: FINST-3Z966E91GO2WX57X8ONLB6JOXPVE223JOONBMGJ
2025-06-14 08:08:35,916 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5804.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5804.0}]
2025-06-14 08:08:36,495 - INFO - 更新表单数据成功: FINST-OJ666W71WN4WKGLPD8UWVC1P0OT13OEOOONBM01
2025-06-14 08:08:36,495 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:37,057 - INFO - 更新表单数据成功: FINST-8PF66V710E4WU1QB8TOECCDV1HM2342ZOONBMV2
2025-06-14 08:08:37,057 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:37,541 - INFO - 更新表单数据成功: FINST-8PF66V710E4WU1QB8TOECCDV1HM2352ZOONBMH5
2025-06-14 08:08:37,541 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10784.57}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10784.57}, {'field': 'amount', 'old_value': 261.97, 'new_value': 9711.9}, {'field': 'count', 'old_value': 13, 'new_value': 212}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 7325.0}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 114}, {'field': 'onlineAmount', 'old_value': 261.97, 'new_value': 2687.92}, {'field': 'onlineCount', 'old_value': 13, 'new_value': 98}]
2025-06-14 08:08:38,041 - INFO - 更新表单数据成功: FINST-2FD66I715C2W6DAT9TWWM5SF8E9M3PE4PONBM3D
2025-06-14 08:08:38,041 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3771.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3771.0}]
2025-06-14 08:08:38,541 - INFO - 更新表单数据成功: FINST-W3B66L71M82WFNUFB1B5M90MNLYJ3LP9PONBMES
2025-06-14 08:08:38,541 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:39,120 - INFO - 更新表单数据成功: FINST-W3B66L71M82WFNUFB1B5M90MNLYJ3MP9PONBM0V
2025-06-14 08:08:39,120 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_********, 变更字段: [{'field': 'amount', 'old_value': 218.09, 'new_value': 10979.029999999999}, {'field': 'count', 'old_value': 10, 'new_value': 228}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 8819.1}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 144}, {'field': 'onlineAmount', 'old_value': 218.09, 'new_value': 2221.05}, {'field': 'onlineCount', 'old_value': 10, 'new_value': 84}]
2025-06-14 08:08:39,682 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12MTP2COBM9E
2025-06-14 08:08:39,682 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:40,151 - INFO - 更新表单数据成功: FINST-QVA66B81F15WKLLAAQF2079EJPC12MTP2COBMLE
2025-06-14 08:08:40,151 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_********, 变更字段: [{'field': 'amount', 'old_value': 11108.3, 'new_value': 11292.3}, {'field': 'count', 'old_value': 96, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 5696.6, 'new_value': 5880.6}, {'field': 'instoreCount', 'old_value': 46, 'new_value': 47}]
2025-06-14 08:08:40,760 - INFO - 更新表单数据成功: FINST-MLF662B18S2W1CWT724D07P2DLNQ3NAS2COBMSU
2025-06-14 08:08:40,760 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6750.42}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6750.42}, {'field': 'amount', 'old_value': 131.48, 'new_value': 7417.65}, {'field': 'count', 'old_value': 6, 'new_value': 138}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 4792.0}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 68}, {'field': 'onlineAmount', 'old_value': 131.48, 'new_value': 2628.4}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 70}]
2025-06-14 08:08:41,198 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA25H117RBMZE
2025-06-14 08:08:41,198 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10915.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10915.0}]
2025-06-14 08:08:41,682 - INFO - 更新表单数据成功: FINST-QVA66B816P5WC1GWAZ2MS8GYVGMA26H117RBM2G
2025-06-14 08:08:41,698 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4533.65, 'new_value': 4566.55}, {'field': 'amount', 'old_value': 4533.650000000001, 'new_value': 4566.55}, {'field': 'count', 'old_value': 222, 'new_value': 223}, {'field': 'onlineAmount', 'old_value': 3269.71, 'new_value': 3302.61}, {'field': 'onlineCount', 'old_value': 165, 'new_value': 166}]
2025-06-14 08:08:42,135 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMYG
2025-06-14 08:08:42,135 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_********, 变更字段: [{'field': 'amount', 'old_value': 3449.21, 'new_value': 3316.21}]
2025-06-14 08:08:42,557 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBM9H
2025-06-14 08:08:42,557 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'shopEntityName', 'old_value': 'KKv', 'new_value': 'KKV'}]
2025-06-14 08:08:43,057 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3D6417RBMFH
2025-06-14 08:08:43,057 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 10001.63, 'new_value': 10010.53}, {'field': 'amount', 'old_value': 10001.630000000001, 'new_value': 10010.53}, {'field': 'count', 'old_value': 405, 'new_value': 407}, {'field': 'onlineAmount', 'old_value': 10196.84, 'new_value': 10205.74}, {'field': 'onlineCount', 'old_value': 405, 'new_value': 407}]
2025-06-14 08:08:43,479 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3E6417RBMJH
2025-06-14 08:08:43,479 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_********, 变更字段: [{'field': 'amount', 'old_value': 21937.1, 'new_value': 22457.1}, {'field': 'count', 'old_value': 96, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 21809.0, 'new_value': 22329.0}, {'field': 'instoreCount', 'old_value': 94, 'new_value': 95}]
2025-06-14 08:08:43,979 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3E6417RBM1I
2025-06-14 08:08:43,979 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_********, 变更字段: [{'field': 'amount', 'old_value': 24311.63, 'new_value': 24336.53}, {'field': 'count', 'old_value': 96, 'new_value': 97}, {'field': 'onlineAmount', 'old_value': 1248.4, 'new_value': 1273.3}, {'field': 'onlineCount', 'old_value': 46, 'new_value': 47}]
2025-06-14 08:08:44,416 - INFO - 更新表单数据成功: FINST-74766M71IM5WSVYADVZ6B650DRKE3E6417RBMLI
2025-06-14 08:08:44,416 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 13320.43}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 13320.43}, {'field': 'amount', 'old_value': 286.39, 'new_value': 5256.98}, {'field': 'count', 'old_value': 9, 'new_value': 105}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 3945.9}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 67}, {'field': 'onlineAmount', 'old_value': 286.39, 'new_value': 1335.25}, {'field': 'onlineCount', 'old_value': 9, 'new_value': 38}]
2025-06-14 08:08:44,776 - INFO - 更新表单数据成功: FINST-3PF66271QZ8WS6V1EXJW7DU5DL1A2GIPY1UBMG5
2025-06-14 08:08:44,776 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5128.6}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5128.6}]
2025-06-14 08:08:45,276 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3O5SY1UBMQ4
2025-06-14 08:08:45,276 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 10713.0, 'new_value': 13071.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10713.0}, {'field': 'amount', 'old_value': 10713.0, 'new_value': 13071.0}, {'field': 'count', 'old_value': 21, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 10713.0, 'new_value': 13071.0}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 22}]
2025-06-14 08:08:45,666 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3O5SY1UBM35
2025-06-14 08:08:45,666 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_********, 变更字段: [{'field': 'amount', 'old_value': 3343.12, 'new_value': 3405.22}, {'field': 'count', 'old_value': 18, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 3252.56, 'new_value': 3314.66}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 17}]
2025-06-14 08:08:46,166 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3O5SY1UBMD5
2025-06-14 08:08:46,166 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_********, 变更字段: [{'field': 'amount', 'old_value': 3332.5099999999998, 'new_value': 3335.0099999999998}, {'field': 'onlineAmount', 'old_value': 1409.0, 'new_value': 1411.5}]
2025-06-14 08:08:46,604 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3O5SY1UBMI5
2025-06-14 08:08:46,604 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9723.02}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9723.02}]
2025-06-14 08:08:47,041 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3O5SY1UBMM5
2025-06-14 08:08:47,041 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_********, 变更字段: [{'field': 'amount', 'old_value': 766.73, 'new_value': 786.5300000000001}, {'field': 'count', 'old_value': 27, 'new_value': 28}, {'field': 'onlineAmount', 'old_value': 529.13, 'new_value': 548.93}, {'field': 'onlineCount', 'old_value': 22, 'new_value': 23}]
2025-06-14 08:08:47,557 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3O5SY1UBMR5
2025-06-14 08:08:47,557 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_********, 变更字段: [{'field': 'amount', 'old_value': 568.04, 'new_value': 596.42}, {'field': 'count', 'old_value': 49, 'new_value': 51}, {'field': 'instoreAmount', 'old_value': 584.64, 'new_value': 613.02}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 51}]
2025-06-14 08:08:47,963 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3O5SY1UBMU5
2025-06-14 08:08:47,963 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4310.6, 'new_value': 5178.6}, {'field': 'dailyBillAmount', 'old_value': 4310.6, 'new_value': 5178.6}]
2025-06-14 08:08:48,338 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3O5SY1UBMV5
2025-06-14 08:08:48,338 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5313.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5313.0}]
2025-06-14 08:08:48,854 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3O5SY1UBMZ5
2025-06-14 08:08:48,854 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10371.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10371.0}, {'field': 'amount', 'old_value': 411.0, 'new_value': 647.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 411.0, 'new_value': 647.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-06-14 08:08:49,291 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3O5SY1UBM16
2025-06-14 08:08:49,291 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 20666.0, 'new_value': 19608.0}, {'field': 'amount', 'old_value': 20666.0, 'new_value': 19608.0}, {'field': 'instoreAmount', 'old_value': 20666.0, 'new_value': 19608.0}]
2025-06-14 08:08:49,713 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3O5SY1UBM26
2025-06-14 08:08:49,713 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_********, 变更字段: [{'field': 'amount', 'old_value': -9418.560000000001, 'new_value': 2875.3499999999985}, {'field': 'count', 'old_value': 37, 'new_value': 46}, {'field': 'instoreAmount', 'old_value': 22449.73, 'new_value': 34743.64}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 46}]
2025-06-14 08:08:50,166 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3P5SY1UBME6
2025-06-14 08:08:50,166 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'count', 'old_value': 133, 'new_value': 134}, {'field': 'onlineAmount', 'old_value': 1850.42, 'new_value': 1863.42}, {'field': 'onlineCount', 'old_value': 107, 'new_value': 108}]
2025-06-14 08:08:50,666 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3P5SY1UBMI6
2025-06-14 08:08:50,666 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2176.75, 'new_value': 2188.94}, {'field': 'amount', 'old_value': 2176.75, 'new_value': 2188.94}, {'field': 'instoreAmount', 'old_value': 2223.98, 'new_value': 2236.17}]
2025-06-14 08:08:51,135 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3P5SY1UBMQ6
2025-06-14 08:08:51,135 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'amount', 'old_value': 2073.67, 'new_value': 2124.67}, {'field': 'count', 'old_value': 146, 'new_value': 148}, {'field': 'onlineAmount', 'old_value': 1733.9, 'new_value': 1784.9}, {'field': 'onlineCount', 'old_value': 103, 'new_value': 105}]
2025-06-14 08:08:51,619 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3P5SY1UBMW6
2025-06-14 08:08:51,619 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1ITH5661MFT0H03I210VQKTG1J001P6B_********, 变更字段: [{'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3045.92}]
2025-06-14 08:08:52,088 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3P5SY1UBM47
2025-06-14 08:08:52,088 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5689.91, 'new_value': 5741.71}, {'field': 'amount', 'old_value': 5689.91, 'new_value': 5741.71}, {'field': 'count', 'old_value': 249, 'new_value': 254}, {'field': 'instoreAmount', 'old_value': 1198.09, 'new_value': 1219.59}, {'field': 'instoreCount', 'old_value': 47, 'new_value': 49}, {'field': 'onlineAmount', 'old_value': 4640.12, 'new_value': 4670.42}, {'field': 'onlineCount', 'old_value': 202, 'new_value': 205}]
2025-06-14 08:08:52,510 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3P5SY1UBM57
2025-06-14 08:08:52,510 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 693.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 693.0}]
2025-06-14 08:08:52,948 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3P5SY1UBM97
2025-06-14 08:08:52,948 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 19622.6}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 19622.6}]
2025-06-14 08:08:53,463 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3P5SY1UBMA7
2025-06-14 08:08:53,463 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5826.2, 'new_value': 1151.2}, {'field': 'dailyBillAmount', 'old_value': 5826.2, 'new_value': 1151.2}]
2025-06-14 08:08:53,963 - INFO - 更新表单数据成功: FINST-SWC66P91PZ8WUC476OJE34K8QSVU3P5SY1UBMD7
2025-06-14 08:08:53,963 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_********, 变更字段: [{'field': 'amount', 'old_value': 25933.72, 'new_value': 26193.72}, {'field': 'count', 'old_value': 218, 'new_value': 219}, {'field': 'instoreAmount', 'old_value': 9583.5, 'new_value': 9843.5}, {'field': 'instoreCount', 'old_value': 62, 'new_value': 63}]
2025-06-14 08:08:54,432 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2XSUY1UBMG
2025-06-14 08:08:54,432 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'amount', 'old_value': 7526.98, 'new_value': 7549.96}, {'field': 'count', 'old_value': 336, 'new_value': 339}, {'field': 'onlineAmount', 'old_value': 5055.13, 'new_value': 5078.11}, {'field': 'onlineCount', 'old_value': 199, 'new_value': 202}]
2025-06-14 08:08:54,963 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2XSUY1UBMI
2025-06-14 08:08:54,963 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_********, 变更字段: [{'field': 'amount', 'old_value': 12674.869999999999, 'new_value': 13004.27}, {'field': 'count', 'old_value': 195, 'new_value': 197}, {'field': 'onlineAmount', 'old_value': 4393.6, 'new_value': 4723.0}, {'field': 'onlineCount', 'old_value': 150, 'new_value': 152}]
2025-06-14 08:08:55,416 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2XSUY1UBMR
2025-06-14 08:08:55,416 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_********, 变更字段: [{'field': 'amount', 'old_value': 3325.24, 'new_value': 3444.14}, {'field': 'count', 'old_value': 13, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 2897.36, 'new_value': 3016.26}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 11}]
2025-06-14 08:08:55,916 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBMW
2025-06-14 08:08:55,916 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_********, 变更字段: [{'field': 'amount', 'old_value': 5510.7, 'new_value': 6874.7}, {'field': 'count', 'old_value': 9, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 5614.2, 'new_value': 6978.2}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 11}]
2025-06-14 08:08:56,354 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBM11
2025-06-14 08:08:56,354 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 984.8, 'new_value': -295.9}, {'field': 'dailyBillAmount', 'old_value': 984.8, 'new_value': -295.9}]
2025-06-14 08:08:56,823 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBM21
2025-06-14 08:08:56,838 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6658.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6658.0}]
2025-06-14 08:08:57,291 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBM71
2025-06-14 08:08:57,291 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_********, 变更字段: [{'field': 'amount', 'old_value': 3218.59, 'new_value': 3228.1}, {'field': 'count', 'old_value': 186, 'new_value': 189}, {'field': 'onlineAmount', 'old_value': 3218.61, 'new_value': 3228.12}, {'field': 'onlineCount', 'old_value': 179, 'new_value': 182}]
2025-06-14 08:08:57,744 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBM81
2025-06-14 08:08:57,744 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10413.16}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10413.16}]
2025-06-14 08:08:58,229 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBMA1
2025-06-14 08:08:58,229 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_********, 变更字段: [{'field': 'amount', 'old_value': 2703.44, 'new_value': 2682.8399999999997}, {'field': 'count', 'old_value': 130, 'new_value': 131}, {'field': 'onlineAmount', 'old_value': 2532.5, 'new_value': 2536.4}, {'field': 'onlineCount', 'old_value': 103, 'new_value': 104}]
2025-06-14 08:08:58,651 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBMC1
2025-06-14 08:08:58,651 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 3187.76, 'new_value': 3207.76}, {'field': 'amount', 'old_value': 3187.76, 'new_value': 3207.76}, {'field': 'count', 'old_value': 151, 'new_value': 152}, {'field': 'onlineAmount', 'old_value': 2154.0, 'new_value': 2174.0}, {'field': 'onlineCount', 'old_value': 80, 'new_value': 81}]
2025-06-14 08:08:59,073 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBME1
2025-06-14 08:08:59,073 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_********, 变更字段: [{'field': 'instoreAmount', 'old_value': 3131.93, 'new_value': 3137.93}, {'field': 'instoreCount', 'old_value': 219, 'new_value': 221}, {'field': 'onlineAmount', 'old_value': 1202.2, 'new_value': 1196.2}, {'field': 'onlineCount', 'old_value': 73, 'new_value': 71}]
2025-06-14 08:08:59,526 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBMJ1
2025-06-14 08:08:59,526 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_********, 变更字段: [{'field': 'amount', 'old_value': 3340.0699999999997, 'new_value': 3375.47}, {'field': 'count', 'old_value': 89, 'new_value': 90}, {'field': 'onlineAmount', 'old_value': 1165.08, 'new_value': 1200.48}, {'field': 'onlineCount', 'old_value': 26, 'new_value': 27}]
2025-06-14 08:09:00,026 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBMN1
2025-06-14 08:09:00,026 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7759.98, 'new_value': 8477.22}, {'field': 'amount', 'old_value': 7759.98, 'new_value': 8477.22}, {'field': 'count', 'old_value': 317, 'new_value': 353}, {'field': 'onlineAmount', 'old_value': 7882.98, 'new_value': 8600.22}, {'field': 'onlineCount', 'old_value': 317, 'new_value': 353}]
2025-06-14 08:09:00,494 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBMO1
2025-06-14 08:09:00,494 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_********, 变更字段: [{'field': 'amount', 'old_value': 14909.5, 'new_value': 14903.400000000001}, {'field': 'onlineAmount', 'old_value': 1741.2, 'new_value': 1735.1}]
2025-06-14 08:09:01,057 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBMT1
2025-06-14 08:09:01,057 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_********, 变更字段: [{'field': 'amount', 'old_value': 14549.5, 'new_value': 14489.2}]
2025-06-14 08:09:01,494 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBMY1
2025-06-14 08:09:01,494 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6764.23, 'new_value': 6753.23}, {'field': 'amount', 'old_value': 6764.2300000000005, 'new_value': 6753.23}, {'field': 'count', 'old_value': 414, 'new_value': 415}, {'field': 'instoreAmount', 'old_value': 3556.8, 'new_value': 3570.1}, {'field': 'instoreCount', 'old_value': 214, 'new_value': 215}]
2025-06-14 08:09:01,932 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBMZ1
2025-06-14 08:09:01,932 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2846.2}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2846.2}]
2025-06-14 08:09:02,401 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBM52
2025-06-14 08:09:02,401 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_********, 变更字段: [{'field': 'amount', 'old_value': 9712.41, 'new_value': 10996.41}, {'field': 'count', 'old_value': 59, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 8583.8, 'new_value': 9867.8}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 48}]
2025-06-14 08:09:02,823 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBMA2
2025-06-14 08:09:02,823 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_********, 变更字段: [{'field': 'amount', 'old_value': 12089.8, 'new_value': 12103.18}, {'field': 'count', 'old_value': 356, 'new_value': 357}, {'field': 'onlineAmount', 'old_value': 1473.37, 'new_value': 1486.75}, {'field': 'onlineCount', 'old_value': 52, 'new_value': 53}]
2025-06-14 08:09:03,276 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBMG2
2025-06-14 08:09:03,276 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_********, 变更字段: [{'field': 'amount', 'old_value': 29945.2, 'new_value': 32416.2}, {'field': 'count', 'old_value': 160, 'new_value': 164}, {'field': 'instoreAmount', 'old_value': 26939.1, 'new_value': 29410.1}, {'field': 'instoreCount', 'old_value': 133, 'new_value': 137}]
2025-06-14 08:09:03,744 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBML2
2025-06-14 08:09:03,744 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_********, 变更字段: [{'field': 'amount', 'old_value': 14593.9, 'new_value': 14867.7}, {'field': 'count', 'old_value': 98, 'new_value': 99}, {'field': 'instoreAmount', 'old_value': 8779.1, 'new_value': 9052.9}, {'field': 'instoreCount', 'old_value': 71, 'new_value': 72}]
2025-06-14 08:09:04,197 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBMS2
2025-06-14 08:09:04,197 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_********, 变更字段: [{'field': 'amount', 'old_value': 9937.77, 'new_value': 9857.65}, {'field': 'count', 'old_value': 212, 'new_value': 206}, {'field': 'instoreAmount', 'old_value': 7190.32, 'new_value': 7110.2}, {'field': 'instoreCount', 'old_value': 111, 'new_value': 105}]
2025-06-14 08:09:04,776 - INFO - 更新表单数据成功: FINST-MUC66Q81YX8W4UYL9DOKH451TVXL2YSUY1UBMT2
2025-06-14 08:09:04,776 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_********, 变更字段: [{'field': 'amount', 'old_value': 310.66, 'new_value': 346.41}, {'field': 'count', 'old_value': 12, 'new_value': 13}, {'field': 'instoreAmount', 'old_value': 310.66, 'new_value': 346.41}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 13}]
2025-06-14 08:09:04,994 - INFO - 正在批量插入每日数据，批次 1/66，共 100 条记录
2025-06-14 08:09:05,494 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-06-14 08:09:08,510 - INFO - 正在批量插入每日数据，批次 2/66，共 100 条记录
2025-06-14 08:09:08,947 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-06-14 08:09:11,963 - INFO - 正在批量插入每日数据，批次 3/66，共 100 条记录
2025-06-14 08:09:12,416 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-06-14 08:09:15,432 - INFO - 正在批量插入每日数据，批次 4/66，共 100 条记录
2025-06-14 08:09:15,854 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-06-14 08:09:18,869 - INFO - 正在批量插入每日数据，批次 5/66，共 100 条记录
2025-06-14 08:09:19,354 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-06-14 08:09:22,369 - INFO - 正在批量插入每日数据，批次 6/66，共 100 条记录
2025-06-14 08:09:22,822 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-06-14 08:09:25,822 - INFO - 正在批量插入每日数据，批次 7/66，共 100 条记录
2025-06-14 08:09:26,354 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-06-14 08:09:29,369 - INFO - 正在批量插入每日数据，批次 8/66，共 100 条记录
2025-06-14 08:09:29,854 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-06-14 08:09:32,869 - INFO - 正在批量插入每日数据，批次 9/66，共 100 条记录
2025-06-14 08:09:33,322 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-06-14 08:09:36,338 - INFO - 正在批量插入每日数据，批次 10/66，共 100 条记录
2025-06-14 08:09:36,838 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-06-14 08:09:39,838 - INFO - 正在批量插入每日数据，批次 11/66，共 100 条记录
2025-06-14 08:09:40,322 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-06-14 08:09:43,338 - INFO - 正在批量插入每日数据，批次 12/66，共 100 条记录
2025-06-14 08:09:43,807 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-06-14 08:09:46,822 - INFO - 正在批量插入每日数据，批次 13/66，共 100 条记录
2025-06-14 08:09:47,322 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-06-14 08:09:50,338 - INFO - 正在批量插入每日数据，批次 14/66，共 100 条记录
2025-06-14 08:09:50,760 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-06-14 08:09:53,775 - INFO - 正在批量插入每日数据，批次 15/66，共 100 条记录
2025-06-14 08:09:54,260 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-06-14 08:09:57,275 - INFO - 正在批量插入每日数据，批次 16/66，共 100 条记录
2025-06-14 08:09:57,760 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-06-14 08:10:00,775 - INFO - 正在批量插入每日数据，批次 17/66，共 100 条记录
2025-06-14 08:10:01,228 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-06-14 08:10:04,244 - INFO - 正在批量插入每日数据，批次 18/66，共 100 条记录
2025-06-14 08:10:04,728 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-06-14 08:10:07,744 - INFO - 正在批量插入每日数据，批次 19/66，共 100 条记录
2025-06-14 08:10:08,244 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-06-14 08:10:11,260 - INFO - 正在批量插入每日数据，批次 20/66，共 100 条记录
2025-06-14 08:10:11,744 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-06-14 08:10:14,760 - INFO - 正在批量插入每日数据，批次 21/66，共 100 条记录
2025-06-14 08:10:15,181 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-06-14 08:10:18,197 - INFO - 正在批量插入每日数据，批次 22/66，共 100 条记录
2025-06-14 08:10:18,541 - INFO - 批量插入每日数据成功，批次 22，100 条记录
2025-06-14 08:10:21,556 - INFO - 正在批量插入每日数据，批次 23/66，共 100 条记录
2025-06-14 08:10:21,978 - INFO - 批量插入每日数据成功，批次 23，100 条记录
2025-06-14 08:10:24,994 - INFO - 正在批量插入每日数据，批次 24/66，共 100 条记录
2025-06-14 08:10:25,447 - INFO - 批量插入每日数据成功，批次 24，100 条记录
2025-06-14 08:10:28,463 - INFO - 正在批量插入每日数据，批次 25/66，共 100 条记录
2025-06-14 08:10:28,931 - INFO - 批量插入每日数据成功，批次 25，100 条记录
2025-06-14 08:10:31,947 - INFO - 正在批量插入每日数据，批次 26/66，共 100 条记录
2025-06-14 08:10:32,400 - INFO - 批量插入每日数据成功，批次 26，100 条记录
2025-06-14 08:10:35,416 - INFO - 正在批量插入每日数据，批次 27/66，共 100 条记录
2025-06-14 08:10:35,916 - INFO - 批量插入每日数据成功，批次 27，100 条记录
2025-06-14 08:10:38,931 - INFO - 正在批量插入每日数据，批次 28/66，共 100 条记录
2025-06-14 08:10:39,447 - INFO - 批量插入每日数据成功，批次 28，100 条记录
2025-06-14 08:10:42,478 - INFO - 正在批量插入每日数据，批次 29/66，共 100 条记录
2025-06-14 08:10:42,916 - INFO - 批量插入每日数据成功，批次 29，100 条记录
2025-06-14 08:10:45,931 - INFO - 正在批量插入每日数据，批次 30/66，共 100 条记录
2025-06-14 08:10:46,322 - INFO - 批量插入每日数据成功，批次 30，100 条记录
2025-06-14 08:10:49,337 - INFO - 正在批量插入每日数据，批次 31/66，共 100 条记录
2025-06-14 08:10:49,759 - INFO - 批量插入每日数据成功，批次 31，100 条记录
2025-06-14 08:10:52,775 - INFO - 正在批量插入每日数据，批次 32/66，共 100 条记录
2025-06-14 08:10:53,291 - INFO - 批量插入每日数据成功，批次 32，100 条记录
2025-06-14 08:10:56,306 - INFO - 正在批量插入每日数据，批次 33/66，共 100 条记录
2025-06-14 08:10:56,744 - INFO - 批量插入每日数据成功，批次 33，100 条记录
2025-06-14 08:10:59,759 - INFO - 正在批量插入每日数据，批次 34/66，共 100 条记录
2025-06-14 08:11:00,181 - INFO - 批量插入每日数据成功，批次 34，100 条记录
2025-06-14 08:11:03,197 - INFO - 正在批量插入每日数据，批次 35/66，共 100 条记录
2025-06-14 08:11:03,665 - INFO - 批量插入每日数据成功，批次 35，100 条记录
2025-06-14 08:11:06,681 - INFO - 正在批量插入每日数据，批次 36/66，共 100 条记录
2025-06-14 08:11:07,103 - INFO - 批量插入每日数据成功，批次 36，100 条记录
2025-06-14 08:11:10,119 - INFO - 正在批量插入每日数据，批次 37/66，共 100 条记录
2025-06-14 08:11:10,572 - INFO - 批量插入每日数据成功，批次 37，100 条记录
2025-06-14 08:11:13,587 - INFO - 正在批量插入每日数据，批次 38/66，共 100 条记录
2025-06-14 08:11:14,025 - INFO - 批量插入每日数据成功，批次 38，100 条记录
2025-06-14 08:11:17,040 - INFO - 正在批量插入每日数据，批次 39/66，共 100 条记录
2025-06-14 08:11:17,556 - INFO - 批量插入每日数据成功，批次 39，100 条记录
2025-06-14 08:11:20,572 - INFO - 正在批量插入每日数据，批次 40/66，共 100 条记录
2025-06-14 08:11:21,118 - INFO - 批量插入每日数据成功，批次 40，100 条记录
2025-06-14 08:11:24,134 - INFO - 正在批量插入每日数据，批次 41/66，共 100 条记录
2025-06-14 08:11:24,509 - INFO - 批量插入每日数据成功，批次 41，100 条记录
2025-06-14 08:11:27,525 - INFO - 正在批量插入每日数据，批次 42/66，共 100 条记录
2025-06-14 08:11:27,868 - INFO - 批量插入每日数据成功，批次 42，100 条记录
2025-06-14 08:11:30,884 - INFO - 正在批量插入每日数据，批次 43/66，共 100 条记录
2025-06-14 08:11:31,290 - INFO - 批量插入每日数据成功，批次 43，100 条记录
2025-06-14 08:11:34,306 - INFO - 正在批量插入每日数据，批次 44/66，共 100 条记录
2025-06-14 08:11:34,790 - INFO - 批量插入每日数据成功，批次 44，100 条记录
2025-06-14 08:11:37,806 - INFO - 正在批量插入每日数据，批次 45/66，共 100 条记录
2025-06-14 08:11:38,212 - INFO - 批量插入每日数据成功，批次 45，100 条记录
2025-06-14 08:11:41,228 - INFO - 正在批量插入每日数据，批次 46/66，共 100 条记录
2025-06-14 08:11:41,650 - INFO - 批量插入每日数据成功，批次 46，100 条记录
2025-06-14 08:11:44,665 - INFO - 正在批量插入每日数据，批次 47/66，共 100 条记录
2025-06-14 08:11:45,087 - INFO - 批量插入每日数据成功，批次 47，100 条记录
2025-06-14 08:11:48,103 - INFO - 正在批量插入每日数据，批次 48/66，共 100 条记录
2025-06-14 08:11:48,525 - INFO - 批量插入每日数据成功，批次 48，100 条记录
2025-06-14 08:11:51,540 - INFO - 正在批量插入每日数据，批次 49/66，共 100 条记录
2025-06-14 08:11:51,962 - INFO - 批量插入每日数据成功，批次 49，100 条记录
2025-06-14 08:11:54,978 - INFO - 正在批量插入每日数据，批次 50/66，共 100 条记录
2025-06-14 08:11:55,384 - INFO - 批量插入每日数据成功，批次 50，100 条记录
2025-06-14 08:11:58,399 - INFO - 正在批量插入每日数据，批次 51/66，共 100 条记录
2025-06-14 08:11:58,853 - INFO - 批量插入每日数据成功，批次 51，100 条记录
2025-06-14 08:12:01,868 - INFO - 正在批量插入每日数据，批次 52/66，共 100 条记录
2025-06-14 08:12:02,259 - INFO - 批量插入每日数据成功，批次 52，100 条记录
2025-06-14 08:12:05,274 - INFO - 正在批量插入每日数据，批次 53/66，共 100 条记录
2025-06-14 08:12:05,665 - INFO - 批量插入每日数据成功，批次 53，100 条记录
2025-06-14 08:12:08,681 - INFO - 正在批量插入每日数据，批次 54/66，共 100 条记录
2025-06-14 08:12:09,149 - INFO - 批量插入每日数据成功，批次 54，100 条记录
2025-06-14 08:12:12,165 - INFO - 正在批量插入每日数据，批次 55/66，共 100 条记录
2025-06-14 08:12:12,602 - INFO - 批量插入每日数据成功，批次 55，100 条记录
2025-06-14 08:12:15,618 - INFO - 正在批量插入每日数据，批次 56/66，共 100 条记录
2025-06-14 08:12:16,056 - INFO - 批量插入每日数据成功，批次 56，100 条记录
2025-06-14 08:12:19,071 - INFO - 正在批量插入每日数据，批次 57/66，共 100 条记录
2025-06-14 08:12:19,540 - INFO - 批量插入每日数据成功，批次 57，100 条记录
2025-06-14 08:12:22,556 - INFO - 正在批量插入每日数据，批次 58/66，共 100 条记录
2025-06-14 08:12:22,946 - INFO - 批量插入每日数据成功，批次 58，100 条记录
2025-06-14 08:12:25,962 - INFO - 正在批量插入每日数据，批次 59/66，共 100 条记录
2025-06-14 08:12:26,477 - INFO - 批量插入每日数据成功，批次 59，100 条记录
2025-06-14 08:12:29,493 - INFO - 正在批量插入每日数据，批次 60/66，共 100 条记录
2025-06-14 08:12:29,852 - INFO - 批量插入每日数据成功，批次 60，100 条记录
2025-06-14 08:12:32,868 - INFO - 正在批量插入每日数据，批次 61/66，共 100 条记录
2025-06-14 08:12:33,321 - INFO - 批量插入每日数据成功，批次 61，100 条记录
2025-06-14 08:12:36,337 - INFO - 正在批量插入每日数据，批次 62/66，共 100 条记录
2025-06-14 08:12:36,727 - INFO - 批量插入每日数据成功，批次 62，100 条记录
2025-06-14 08:12:39,743 - INFO - 正在批量插入每日数据，批次 63/66，共 100 条记录
2025-06-14 08:12:40,180 - INFO - 批量插入每日数据成功，批次 63，100 条记录
2025-06-14 08:12:43,212 - INFO - 正在批量插入每日数据，批次 64/66，共 100 条记录
2025-06-14 08:12:43,665 - INFO - 批量插入每日数据成功，批次 64，100 条记录
2025-06-14 08:12:46,680 - INFO - 正在批量插入每日数据，批次 65/66，共 100 条记录
2025-06-14 08:12:47,102 - INFO - 批量插入每日数据成功，批次 65，100 条记录
2025-06-14 08:12:50,118 - INFO - 正在批量插入每日数据，批次 66/66，共 11 条记录
2025-06-14 08:12:50,305 - INFO - 批量插入每日数据成功，批次 66，11 条记录
2025-06-14 08:12:53,321 - INFO - 批量插入每日数据完成: 总计 6511 条，成功 6511 条，失败 0 条
2025-06-14 08:12:53,321 - INFO - 批量插入日销售数据完成，共 6511 条记录
2025-06-14 08:12:53,321 - INFO - 日销售数据同步完成！更新: 92 条，插入: 6511 条，错误: 0 条，跳过: 6203 条
2025-06-14 08:12:53,321 - INFO - 正在获取宜搭月销售表单数据...
2025-06-14 08:12:53,321 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-06-01 至 2025-06-30
2025-06-14 08:12:53,321 - INFO - 查询月度分段 1: 2024-06-01 至 2024-08-31
2025-06-14 08:12:53,321 - INFO - 查询日期范围: 2024-06-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-14 08:12:53,321 - INFO - Request Parameters - Page 1:
2025-06-14 08:12:53,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:12:53,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:12:53,774 - INFO - API请求耗时: 453ms
2025-06-14 08:12:53,774 - INFO - Response - Page 1
2025-06-14 08:12:53,774 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-14 08:12:53,774 - INFO - 查询完成，共获取到 0 条记录
2025-06-14 08:12:53,774 - WARNING - 月度分段 1 查询返回空数据
2025-06-14 08:12:53,774 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-06-14 08:12:53,774 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-06-14 08:12:53,774 - INFO - Request Parameters - Page 1:
2025-06-14 08:12:53,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:12:53,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:12:54,290 - INFO - API请求耗时: 516ms
2025-06-14 08:12:54,290 - INFO - Response - Page 1
2025-06-14 08:12:54,290 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-14 08:12:54,290 - INFO - 查询完成，共获取到 0 条记录
2025-06-14 08:12:54,290 - WARNING - 单月查询返回空数据: 2024-06
2025-06-14 08:12:54,790 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-06-14 08:12:54,790 - INFO - Request Parameters - Page 1:
2025-06-14 08:12:54,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:12:54,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:12:55,008 - INFO - API请求耗时: 219ms
2025-06-14 08:12:55,008 - INFO - Response - Page 1
2025-06-14 08:12:55,008 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-14 08:12:55,008 - INFO - 查询完成，共获取到 0 条记录
2025-06-14 08:12:55,008 - WARNING - 单月查询返回空数据: 2024-07
2025-06-14 08:12:55,508 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-14 08:12:55,508 - INFO - Request Parameters - Page 1:
2025-06-14 08:12:55,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:12:55,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:12:55,712 - INFO - API请求耗时: 203ms
2025-06-14 08:12:55,712 - INFO - Response - Page 1
2025-06-14 08:12:55,712 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-14 08:12:55,712 - INFO - 查询完成，共获取到 0 条记录
2025-06-14 08:12:55,712 - WARNING - 单月查询返回空数据: 2024-08
2025-06-14 08:12:57,243 - INFO - 查询月度分段 2: 2024-09-01 至 2024-11-30
2025-06-14 08:12:57,243 - INFO - 查询日期范围: 2024-09-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-14 08:12:57,243 - INFO - Request Parameters - Page 1:
2025-06-14 08:12:57,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:12:57,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:12:57,446 - INFO - API请求耗时: 203ms
2025-06-14 08:12:57,446 - INFO - Response - Page 1
2025-06-14 08:12:57,446 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-14 08:12:57,446 - INFO - 查询完成，共获取到 0 条记录
2025-06-14 08:12:57,446 - WARNING - 月度分段 2 查询返回空数据
2025-06-14 08:12:57,446 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-06-14 08:12:57,446 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-06-14 08:12:57,446 - INFO - Request Parameters - Page 1:
2025-06-14 08:12:57,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:12:57,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:12:57,649 - INFO - API请求耗时: 203ms
2025-06-14 08:12:57,649 - INFO - Response - Page 1
2025-06-14 08:12:57,649 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-14 08:12:57,649 - INFO - 查询完成，共获取到 0 条记录
2025-06-14 08:12:57,649 - WARNING - 单月查询返回空数据: 2024-09
2025-06-14 08:12:58,165 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-06-14 08:12:58,165 - INFO - Request Parameters - Page 1:
2025-06-14 08:12:58,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:12:58,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:12:58,368 - INFO - API请求耗时: 203ms
2025-06-14 08:12:58,368 - INFO - Response - Page 1
2025-06-14 08:12:58,368 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-14 08:12:58,368 - INFO - 查询完成，共获取到 0 条记录
2025-06-14 08:12:58,368 - WARNING - 单月查询返回空数据: 2024-10
2025-06-14 08:12:58,883 - INFO - 查询日期范围: 2024-11-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-14 08:12:58,883 - INFO - Request Parameters - Page 1:
2025-06-14 08:12:58,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:12:58,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:12:59,087 - INFO - API请求耗时: 203ms
2025-06-14 08:12:59,087 - INFO - Response - Page 1
2025-06-14 08:12:59,087 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-14 08:12:59,087 - INFO - 查询完成，共获取到 0 条记录
2025-06-14 08:12:59,087 - WARNING - 单月查询返回空数据: 2024-11
2025-06-14 08:13:00,602 - INFO - 查询月度分段 3: 2024-12-01 至 2025-02-28
2025-06-14 08:13:00,602 - INFO - 查询日期范围: 2024-12-01 至 2025-02-28，使用分页查询，每页 100 条记录
2025-06-14 08:13:00,602 - INFO - Request Parameters - Page 1:
2025-06-14 08:13:00,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:00,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:01,165 - INFO - API请求耗时: 562ms
2025-06-14 08:13:01,165 - INFO - Response - Page 1
2025-06-14 08:13:01,165 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:13:01,665 - INFO - Request Parameters - Page 2:
2025-06-14 08:13:01,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:01,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:02,165 - INFO - API请求耗时: 500ms
2025-06-14 08:13:02,165 - INFO - Response - Page 2
2025-06-14 08:13:02,165 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:13:02,680 - INFO - Request Parameters - Page 3:
2025-06-14 08:13:02,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:02,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:03,227 - INFO - API请求耗时: 547ms
2025-06-14 08:13:03,227 - INFO - Response - Page 3
2025-06-14 08:13:03,227 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:13:03,727 - INFO - Request Parameters - Page 4:
2025-06-14 08:13:03,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:03,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:04,196 - INFO - API请求耗时: 469ms
2025-06-14 08:13:04,196 - INFO - Response - Page 4
2025-06-14 08:13:04,196 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:13:04,712 - INFO - Request Parameters - Page 5:
2025-06-14 08:13:04,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:04,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:05,211 - INFO - API请求耗时: 500ms
2025-06-14 08:13:05,211 - INFO - Response - Page 5
2025-06-14 08:13:05,227 - INFO - 第 5 页获取到 94 条记录
2025-06-14 08:13:05,227 - INFO - 查询完成，共获取到 494 条记录
2025-06-14 08:13:05,227 - INFO - 月度分段 3 查询成功，获取到 494 条记录
2025-06-14 08:13:06,243 - INFO - 查询月度分段 4: 2025-03-01 至 2025-05-31
2025-06-14 08:13:06,243 - INFO - 查询日期范围: 2025-03-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-14 08:13:06,243 - INFO - Request Parameters - Page 1:
2025-06-14 08:13:06,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:06,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:06,758 - INFO - API请求耗时: 516ms
2025-06-14 08:13:06,758 - INFO - Response - Page 1
2025-06-14 08:13:06,758 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:13:07,274 - INFO - Request Parameters - Page 2:
2025-06-14 08:13:07,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:07,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:07,836 - INFO - API请求耗时: 563ms
2025-06-14 08:13:07,836 - INFO - Response - Page 2
2025-06-14 08:13:07,836 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:13:08,352 - INFO - Request Parameters - Page 3:
2025-06-14 08:13:08,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:08,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:08,930 - INFO - API请求耗时: 578ms
2025-06-14 08:13:08,930 - INFO - Response - Page 3
2025-06-14 08:13:08,930 - INFO - 第 3 页获取到 100 条记录
2025-06-14 08:13:09,446 - INFO - Request Parameters - Page 4:
2025-06-14 08:13:09,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:09,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:09,961 - INFO - API请求耗时: 516ms
2025-06-14 08:13:09,961 - INFO - Response - Page 4
2025-06-14 08:13:09,961 - INFO - 第 4 页获取到 100 条记录
2025-06-14 08:13:10,477 - INFO - Request Parameters - Page 5:
2025-06-14 08:13:10,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:10,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:11,040 - INFO - API请求耗时: 563ms
2025-06-14 08:13:11,040 - INFO - Response - Page 5
2025-06-14 08:13:11,040 - INFO - 第 5 页获取到 100 条记录
2025-06-14 08:13:11,540 - INFO - Request Parameters - Page 6:
2025-06-14 08:13:11,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:11,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:12,133 - INFO - API请求耗时: 594ms
2025-06-14 08:13:12,133 - INFO - Response - Page 6
2025-06-14 08:13:12,133 - INFO - 第 6 页获取到 100 条记录
2025-06-14 08:13:12,633 - INFO - Request Parameters - Page 7:
2025-06-14 08:13:12,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:12,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:13,211 - INFO - API请求耗时: 578ms
2025-06-14 08:13:13,211 - INFO - Response - Page 7
2025-06-14 08:13:13,211 - INFO - 第 7 页获取到 98 条记录
2025-06-14 08:13:13,211 - INFO - 查询完成，共获取到 698 条记录
2025-06-14 08:13:13,211 - INFO - 月度分段 4 查询成功，获取到 698 条记录
2025-06-14 08:13:14,227 - INFO - 查询月度分段 5: 2025-06-01 至 2025-06-30
2025-06-14 08:13:14,227 - INFO - 查询日期范围: 2025-06-01 至 2025-06-30，使用分页查询，每页 100 条记录
2025-06-14 08:13:14,227 - INFO - Request Parameters - Page 1:
2025-06-14 08:13:14,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:14,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:14,805 - INFO - API请求耗时: 578ms
2025-06-14 08:13:14,805 - INFO - Response - Page 1
2025-06-14 08:13:14,805 - INFO - 第 1 页获取到 100 条记录
2025-06-14 08:13:15,321 - INFO - Request Parameters - Page 2:
2025-06-14 08:13:15,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:15,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:15,868 - INFO - API请求耗时: 547ms
2025-06-14 08:13:15,868 - INFO - Response - Page 2
2025-06-14 08:13:15,868 - INFO - 第 2 页获取到 100 条记录
2025-06-14 08:13:16,383 - INFO - Request Parameters - Page 3:
2025-06-14 08:13:16,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 08:13:16,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 08:13:16,696 - INFO - API请求耗时: 312ms
2025-06-14 08:13:16,696 - INFO - Response - Page 3
2025-06-14 08:13:16,696 - INFO - 第 3 页获取到 11 条记录
2025-06-14 08:13:16,696 - INFO - 查询完成，共获取到 211 条记录
2025-06-14 08:13:16,696 - INFO - 月度分段 5 查询成功，获取到 211 条记录
2025-06-14 08:13:17,711 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1403 条记录，失败 0 次
2025-06-14 08:13:17,711 - INFO - 成功获取宜搭月销售表单数据，共 1403 条记录
2025-06-14 08:13:17,711 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-14 08:13:17,711 - INFO - 正在从MySQL获取月度汇总数据...
2025-06-14 08:13:17,758 - INFO - 成功获取MySQL月度汇总数据，共 1413 条记录
2025-06-14 08:13:18,493 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250614.xlsx
2025-06-14 08:13:18,571 - INFO - 成功创建宜搭月销售数据索引，共 1403 条记录
2025-06-14 08:13:18,586 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:18,586 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:19,118 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4I
2025-06-14 08:13:19,118 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-06, 变更字段: [{'field': 'amount', 'old_value': 222187.36, 'new_value': 260775.06}, {'field': 'count', 'old_value': 1083, 'new_value': 1257}, {'field': 'instoreAmount', 'old_value': 222187.36, 'new_value': 260775.06}, {'field': 'instoreCount', 'old_value': 1083, 'new_value': 1257}]
2025-06-14 08:13:19,118 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:19,540 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5I
2025-06-14 08:13:19,540 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 213308.66, 'new_value': 228601.23}, {'field': 'dailyBillAmount', 'old_value': 213308.66, 'new_value': 228601.23}, {'field': 'amount', 'old_value': 335886.9, 'new_value': 362557.85}, {'field': 'count', 'old_value': 1263, 'new_value': 1389}, {'field': 'instoreAmount', 'old_value': 334563.9, 'new_value': 361145.15}, {'field': 'instoreCount', 'old_value': 1254, 'new_value': 1377}, {'field': 'onlineAmount', 'old_value': 1418.1, 'new_value': 1574.3}, {'field': 'onlineCount', 'old_value': 9, 'new_value': 12}]
2025-06-14 08:13:19,540 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:19,961 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6I
2025-06-14 08:13:19,961 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24967.0, 'new_value': 25336.6}, {'field': 'dailyBillAmount', 'old_value': 24967.0, 'new_value': 25336.6}, {'field': 'amount', 'old_value': 30291.7, 'new_value': 32447.9}, {'field': 'count', 'old_value': 142, 'new_value': 155}, {'field': 'instoreAmount', 'old_value': 11228.3, 'new_value': 12652.3}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 15}, {'field': 'onlineAmount', 'old_value': 21034.8, 'new_value': 21768.0}, {'field': 'onlineCount', 'old_value': 129, 'new_value': 140}]
2025-06-14 08:13:19,977 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:20,461 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7I
2025-06-14 08:13:20,461 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 42792.2, 'new_value': 48020.2}, {'field': 'amount', 'old_value': 42792.2, 'new_value': 48020.2}, {'field': 'count', 'old_value': 49, 'new_value': 52}, {'field': 'instoreAmount', 'old_value': 42792.2, 'new_value': 48020.2}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 52}]
2025-06-14 08:13:20,461 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:20,961 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9I
2025-06-14 08:13:20,961 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 300581.19, 'new_value': 317476.94}, {'field': 'dailyBillAmount', 'old_value': 300581.19, 'new_value': 317476.94}, {'field': 'amount', 'old_value': 60224.87, 'new_value': 63295.01}, {'field': 'count', 'old_value': 311, 'new_value': 328}, {'field': 'instoreAmount', 'old_value': 60224.87, 'new_value': 63295.01}, {'field': 'instoreCount', 'old_value': 311, 'new_value': 328}]
2025-06-14 08:13:20,977 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:21,430 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8I
2025-06-14 08:13:21,430 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 340058.1, 'new_value': 367703.49}, {'field': 'dailyBillAmount', 'old_value': 340058.1, 'new_value': 367703.49}, {'field': 'amount', 'old_value': 196178.67, 'new_value': 210150.41}, {'field': 'count', 'old_value': 1442, 'new_value': 1536}, {'field': 'instoreAmount', 'old_value': 175718.83, 'new_value': 188827.01}, {'field': 'instoreCount', 'old_value': 761, 'new_value': 811}, {'field': 'onlineAmount', 'old_value': 22900.15, 'new_value': 24322.45}, {'field': 'onlineCount', 'old_value': 681, 'new_value': 725}]
2025-06-14 08:13:21,430 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:21,899 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1I
2025-06-14 08:13:21,899 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 92425.19, 'new_value': 100191.2}, {'field': 'dailyBillAmount', 'old_value': 92425.19, 'new_value': 100191.2}, {'field': 'amount', 'old_value': 2855.2, 'new_value': 3234.3}, {'field': 'count', 'old_value': 34, 'new_value': 39}, {'field': 'onlineAmount', 'old_value': 2855.2, 'new_value': 3234.3}, {'field': 'onlineCount', 'old_value': 34, 'new_value': 39}]
2025-06-14 08:13:21,899 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:22,383 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2I
2025-06-14 08:13:22,383 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 227156.69, 'new_value': 246644.4}, {'field': 'dailyBillAmount', 'old_value': 227156.69, 'new_value': 246644.4}, {'field': 'amount', 'old_value': 137042.2, 'new_value': 150357.7}, {'field': 'count', 'old_value': 1315, 'new_value': 1459}, {'field': 'instoreAmount', 'old_value': 61139.6, 'new_value': 64943.8}, {'field': 'instoreCount', 'old_value': 514, 'new_value': 556}, {'field': 'onlineAmount', 'old_value': 76025.0, 'new_value': 85596.8}, {'field': 'onlineCount', 'old_value': 801, 'new_value': 903}]
2025-06-14 08:13:22,383 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:22,836 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3I
2025-06-14 08:13:22,836 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 133350.41, 'new_value': 144273.19}, {'field': 'dailyBillAmount', 'old_value': 133350.41, 'new_value': 144273.19}, {'field': 'amount', 'old_value': 135231.94, 'new_value': 146283.84}, {'field': 'count', 'old_value': 889, 'new_value': 964}, {'field': 'instoreAmount', 'old_value': 124444.53, 'new_value': 134806.03}, {'field': 'instoreCount', 'old_value': 738, 'new_value': 804}, {'field': 'onlineAmount', 'old_value': 11006.31, 'new_value': 11696.71}, {'field': 'onlineCount', 'old_value': 151, 'new_value': 160}]
2025-06-14 08:13:22,977 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:23,461 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUJ
2025-06-14 08:13:23,461 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 387605.37, 'new_value': 420633.1}, {'field': 'count', 'old_value': 527, 'new_value': 574}, {'field': 'instoreAmount', 'old_value': 387605.37, 'new_value': 420633.1}, {'field': 'instoreCount', 'old_value': 527, 'new_value': 574}]
2025-06-14 08:13:23,477 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:23,946 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTJ
2025-06-14 08:13:23,946 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 169987.84, 'new_value': 187410.44}, {'field': 'dailyBillAmount', 'old_value': 169987.84, 'new_value': 187410.44}, {'field': 'amount', 'old_value': 181963.84, 'new_value': 199386.44}, {'field': 'count', 'old_value': 583, 'new_value': 635}, {'field': 'instoreAmount', 'old_value': 181963.84, 'new_value': 199386.44}, {'field': 'instoreCount', 'old_value': 583, 'new_value': 635}]
2025-06-14 08:13:23,946 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:24,414 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSJ
2025-06-14 08:13:24,414 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14467.83, 'new_value': 14866.83}, {'field': 'dailyBillAmount', 'old_value': 14467.83, 'new_value': 14866.83}, {'field': 'amount', 'old_value': 14467.83, 'new_value': 14866.83}, {'field': 'count', 'old_value': 21, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 15193.46, 'new_value': 15592.46}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 22}]
2025-06-14 08:13:24,414 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:24,915 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRJ
2025-06-14 08:13:24,915 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-06, 变更字段: [{'field': 'amount', 'old_value': 40650.62, 'new_value': 41948.73}, {'field': 'count', 'old_value': 237, 'new_value': 252}, {'field': 'instoreAmount', 'old_value': 40650.62, 'new_value': 41948.73}, {'field': 'instoreCount', 'old_value': 237, 'new_value': 252}]
2025-06-14 08:13:24,915 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:25,383 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQJ
2025-06-14 08:13:25,383 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78C3CF1DDB8443E8A4A4D425F7CA9756_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 94372.28, 'new_value': 105285.68}, {'field': 'dailyBillAmount', 'old_value': 88170.28, 'new_value': 99083.68}, {'field': 'amount', 'old_value': 94372.28, 'new_value': 105285.68}, {'field': 'count', 'old_value': 480, 'new_value': 539}, {'field': 'instoreAmount', 'old_value': 94372.28, 'new_value': 105285.68}, {'field': 'instoreCount', 'old_value': 480, 'new_value': 539}]
2025-06-14 08:13:25,399 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:25,821 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPJ
2025-06-14 08:13:25,821 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 132756.0, 'new_value': 138654.0}, {'field': 'amount', 'old_value': 132756.0, 'new_value': 138654.0}, {'field': 'count', 'old_value': 29, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 132756.0, 'new_value': 138654.0}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 31}]
2025-06-14 08:13:25,821 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:26,289 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOJ
2025-06-14 08:13:26,289 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17496.46, 'new_value': 19033.79}, {'field': 'amount', 'old_value': 17496.46, 'new_value': 19033.79}, {'field': 'count', 'old_value': 160, 'new_value': 174}, {'field': 'instoreAmount', 'old_value': 17496.46, 'new_value': 19033.79}, {'field': 'instoreCount', 'old_value': 160, 'new_value': 174}]
2025-06-14 08:13:26,289 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:26,758 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNJ
2025-06-14 08:13:26,758 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 145054.8, 'new_value': 156233.8}, {'field': 'dailyBillAmount', 'old_value': 107568.35, 'new_value': 118281.35}, {'field': 'amount', 'old_value': 145054.8, 'new_value': 156233.8}, {'field': 'count', 'old_value': 243, 'new_value': 262}, {'field': 'instoreAmount', 'old_value': 145054.8, 'new_value': 156233.8}, {'field': 'instoreCount', 'old_value': 243, 'new_value': 262}]
2025-06-14 08:13:26,758 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:27,258 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMJ
2025-06-14 08:13:27,258 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17875.41, 'new_value': 19799.56}, {'field': 'dailyBillAmount', 'old_value': 17875.41, 'new_value': 19799.56}, {'field': 'amount', 'old_value': 20063.5, 'new_value': 22719.24}, {'field': 'count', 'old_value': 579, 'new_value': 648}, {'field': 'instoreAmount', 'old_value': 20063.5, 'new_value': 22719.24}, {'field': 'instoreCount', 'old_value': 579, 'new_value': 648}]
2025-06-14 08:13:27,258 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:27,664 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLJ
2025-06-14 08:13:27,664 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 91636.25, 'new_value': 100461.52}, {'field': 'dailyBillAmount', 'old_value': 82088.25, 'new_value': 90913.52}, {'field': 'amount', 'old_value': 91636.25, 'new_value': 100461.52}, {'field': 'count', 'old_value': 401, 'new_value': 439}, {'field': 'instoreAmount', 'old_value': 91636.25, 'new_value': 100461.52}, {'field': 'instoreCount', 'old_value': 401, 'new_value': 439}]
2025-06-14 08:13:27,664 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:28,211 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKJ
2025-06-14 08:13:28,211 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-06, 变更字段: [{'field': 'amount', 'old_value': 2042.1, 'new_value': 2052.0}, {'field': 'count', 'old_value': 23, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 2043.65, 'new_value': 2053.55}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 24}]
2025-06-14 08:13:28,211 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:28,696 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJJ
2025-06-14 08:13:28,696 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 72869.33, 'new_value': 80159.55}, {'field': 'dailyBillAmount', 'old_value': 72869.33, 'new_value': 80159.55}, {'field': 'amount', 'old_value': 37837.8, 'new_value': 41141.47}, {'field': 'count', 'old_value': 402, 'new_value': 438}, {'field': 'instoreAmount', 'old_value': 36340.96, 'new_value': 39454.63}, {'field': 'instoreCount', 'old_value': 339, 'new_value': 370}, {'field': 'onlineAmount', 'old_value': 2131.69, 'new_value': 2321.69}, {'field': 'onlineCount', 'old_value': 63, 'new_value': 68}]
2025-06-14 08:13:28,696 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:29,164 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHJ
2025-06-14 08:13:29,164 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 90291.7, 'new_value': 99190.15}, {'field': 'dailyBillAmount', 'old_value': 90291.7, 'new_value': 99190.15}, {'field': 'amount', 'old_value': 106640.5, 'new_value': 116434.4}, {'field': 'count', 'old_value': 600, 'new_value': 653}, {'field': 'instoreAmount', 'old_value': 106640.5, 'new_value': 116434.4}, {'field': 'instoreCount', 'old_value': 600, 'new_value': 653}]
2025-06-14 08:13:29,180 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:29,664 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWI
2025-06-14 08:13:29,664 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 140317.78, 'new_value': 162590.57}, {'field': 'dailyBillAmount', 'old_value': 140317.78, 'new_value': 162590.57}, {'field': 'amount', 'old_value': 82657.34, 'new_value': 89359.34}, {'field': 'count', 'old_value': 348, 'new_value': 382}, {'field': 'instoreAmount', 'old_value': 84325.2, 'new_value': 91236.7}, {'field': 'instoreCount', 'old_value': 348, 'new_value': 382}]
2025-06-14 08:13:29,664 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:30,102 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGJ
2025-06-14 08:13:30,102 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 164030.64, 'new_value': 178347.93}, {'field': 'dailyBillAmount', 'old_value': 164030.64, 'new_value': 178347.93}, {'field': 'amount', 'old_value': 74605.37, 'new_value': 80585.57}, {'field': 'count', 'old_value': 769, 'new_value': 829}, {'field': 'instoreAmount', 'old_value': 36181.62, 'new_value': 39168.52}, {'field': 'instoreCount', 'old_value': 271, 'new_value': 288}, {'field': 'onlineAmount', 'old_value': 38423.75, 'new_value': 41417.05}, {'field': 'onlineCount', 'old_value': 498, 'new_value': 541}]
2025-06-14 08:13:30,102 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:30,649 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFJ
2025-06-14 08:13:30,649 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 55045.09, 'new_value': 59806.93}, {'field': 'dailyBillAmount', 'old_value': 55045.09, 'new_value': 59806.93}, {'field': 'amount', 'old_value': 43632.98, 'new_value': 47710.98}, {'field': 'count', 'old_value': 186, 'new_value': 200}, {'field': 'instoreAmount', 'old_value': 43437.3, 'new_value': 47492.3}, {'field': 'instoreCount', 'old_value': 182, 'new_value': 195}, {'field': 'onlineAmount', 'old_value': 196.1, 'new_value': 219.1}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 5}]
2025-06-14 08:13:30,649 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:31,149 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEJ
2025-06-14 08:13:31,149 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32530.38, 'new_value': 35619.63}, {'field': 'amount', 'old_value': 32530.38, 'new_value': 35619.63}, {'field': 'count', 'old_value': 1485, 'new_value': 1623}, {'field': 'instoreAmount', 'old_value': 34429.91, 'new_value': 37716.12}, {'field': 'instoreCount', 'old_value': 1485, 'new_value': 1623}]
2025-06-14 08:13:31,149 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:31,571 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAI
2025-06-14 08:13:31,571 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 94964.17, 'new_value': 127135.95}, {'field': 'dailyBillAmount', 'old_value': 94964.17, 'new_value': 127135.95}]
2025-06-14 08:13:31,571 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:32,024 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBI
2025-06-14 08:13:32,024 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-06, 变更字段: [{'field': 'amount', 'old_value': 65545.9, 'new_value': 71486.9}, {'field': 'count', 'old_value': 88, 'new_value': 93}, {'field': 'instoreAmount', 'old_value': 65545.9, 'new_value': 71486.9}, {'field': 'instoreCount', 'old_value': 88, 'new_value': 93}]
2025-06-14 08:13:32,024 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:32,461 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCI
2025-06-14 08:13:32,461 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 490254.61, 'new_value': 526136.93}, {'field': 'dailyBillAmount', 'old_value': 490254.61, 'new_value': 526136.93}, {'field': 'amount', 'old_value': -218666.2, 'new_value': -226140.23}, {'field': 'count', 'old_value': 494, 'new_value': 533}, {'field': 'instoreAmount', 'old_value': 276458.07, 'new_value': 303786.3}, {'field': 'instoreCount', 'old_value': 494, 'new_value': 533}]
2025-06-14 08:13:32,461 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:32,914 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDI
2025-06-14 08:13:32,914 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 179294.0, 'new_value': 214340.0}, {'field': 'amount', 'old_value': 179294.0, 'new_value': 214340.0}, {'field': 'count', 'old_value': 621, 'new_value': 716}, {'field': 'instoreAmount', 'old_value': 179294.0, 'new_value': 214340.0}, {'field': 'instoreCount', 'old_value': 621, 'new_value': 716}]
2025-06-14 08:13:32,914 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:33,368 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEI
2025-06-14 08:13:33,368 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 176233.74, 'new_value': 192287.39}, {'field': 'dailyBillAmount', 'old_value': 162042.34, 'new_value': 177213.99}, {'field': 'amount', 'old_value': 176233.74, 'new_value': 192287.39}, {'field': 'count', 'old_value': 568, 'new_value': 618}, {'field': 'instoreAmount', 'old_value': 176233.74, 'new_value': 192287.39}, {'field': 'instoreCount', 'old_value': 568, 'new_value': 618}]
2025-06-14 08:13:33,368 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:33,930 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMG1
2025-06-14 08:13:33,930 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 75830.45, 'new_value': 86201.45}, {'field': 'dailyBillAmount', 'old_value': 75830.45, 'new_value': 86201.45}, {'field': 'amount', 'old_value': 6337.7, 'new_value': 7013.7}, {'field': 'count', 'old_value': 22, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 6660.83, 'new_value': 7336.83}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 25}]
2025-06-14 08:13:33,930 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:34,336 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVJ
2025-06-14 08:13:34,336 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 53240.6, 'new_value': 58369.2}, {'field': 'dailyBillAmount', 'old_value': 53240.6, 'new_value': 58369.2}, {'field': 'amount', 'old_value': 15978.1, 'new_value': 17060.0}, {'field': 'count', 'old_value': 74, 'new_value': 82}, {'field': 'instoreAmount', 'old_value': 15978.1, 'new_value': 17060.0}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 82}]
2025-06-14 08:13:34,336 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:34,774 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGI
2025-06-14 08:13:34,774 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 70824.29, 'new_value': 77749.85}, {'field': 'dailyBillAmount', 'old_value': 30890.37, 'new_value': 37521.15}, {'field': 'amount', 'old_value': 70823.94, 'new_value': 77749.5}, {'field': 'count', 'old_value': 2339, 'new_value': 2560}, {'field': 'instoreAmount', 'old_value': 63272.51, 'new_value': 69298.25}, {'field': 'instoreCount', 'old_value': 2152, 'new_value': 2352}, {'field': 'onlineAmount', 'old_value': 7551.78, 'new_value': 8451.6}, {'field': 'onlineCount', 'old_value': 187, 'new_value': 208}]
2025-06-14 08:13:34,774 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:35,243 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHI
2025-06-14 08:13:35,243 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 116463.83, 'new_value': 130015.83}, {'field': 'dailyBillAmount', 'old_value': 116422.0, 'new_value': 129974.0}, {'field': 'amount', 'old_value': 92561.57, 'new_value': 103215.57}, {'field': 'count', 'old_value': 107, 'new_value': 120}, {'field': 'instoreAmount', 'old_value': 91637.0, 'new_value': 102291.0}, {'field': 'instoreCount', 'old_value': 100, 'new_value': 113}]
2025-06-14 08:13:35,243 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:35,727 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMII
2025-06-14 08:13:35,727 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 211550.07, 'new_value': 235250.59}, {'field': 'dailyBillAmount', 'old_value': 211550.07, 'new_value': 235250.59}, {'field': 'amount', 'old_value': 211550.07, 'new_value': 235250.59}, {'field': 'count', 'old_value': 209, 'new_value': 236}, {'field': 'instoreAmount', 'old_value': 211550.07, 'new_value': 235250.59}, {'field': 'instoreCount', 'old_value': 209, 'new_value': 236}]
2025-06-14 08:13:35,727 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:36,133 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJI
2025-06-14 08:13:36,133 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 79718.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 79718.0}, {'field': 'amount', 'old_value': 18429.0, 'new_value': 20797.0}, {'field': 'count', 'old_value': 23, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 18429.0, 'new_value': 20797.0}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 27}]
2025-06-14 08:13:36,133 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:36,555 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKI
2025-06-14 08:13:36,555 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 43165.1, 'new_value': 48920.7}, {'field': 'dailyBillAmount', 'old_value': 43165.1, 'new_value': 48920.7}, {'field': 'amount', 'old_value': 49149.2, 'new_value': 53921.8}, {'field': 'count', 'old_value': 144, 'new_value': 158}, {'field': 'instoreAmount', 'old_value': 49149.2, 'new_value': 53921.8}, {'field': 'instoreCount', 'old_value': 144, 'new_value': 158}]
2025-06-14 08:13:36,555 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:36,977 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMI
2025-06-14 08:13:36,977 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 93235.85, 'new_value': 99367.85}, {'field': 'dailyBillAmount', 'old_value': 93235.85, 'new_value': 99367.85}, {'field': 'amount', 'old_value': 120912.5, 'new_value': 128695.5}, {'field': 'count', 'old_value': 648, 'new_value': 694}, {'field': 'instoreAmount', 'old_value': 121477.5, 'new_value': 129260.5}, {'field': 'instoreCount', 'old_value': 648, 'new_value': 694}]
2025-06-14 08:13:36,977 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:37,477 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNI
2025-06-14 08:13:37,477 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 84208.95, 'new_value': 94109.8}, {'field': 'dailyBillAmount', 'old_value': 84208.95, 'new_value': 94109.8}, {'field': 'amount', 'old_value': 10641.75, 'new_value': 11565.64}, {'field': 'count', 'old_value': 788, 'new_value': 870}, {'field': 'instoreAmount', 'old_value': 11910.05, 'new_value': 13016.06}, {'field': 'instoreCount', 'old_value': 788, 'new_value': 870}]
2025-06-14 08:13:37,477 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:37,914 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPI
2025-06-14 08:13:37,914 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 114637.73, 'new_value': 122303.13}, {'field': 'dailyBillAmount', 'old_value': 114637.73, 'new_value': 122303.13}, {'field': 'amount', 'old_value': 114906.0, 'new_value': 122571.4}, {'field': 'count', 'old_value': 312, 'new_value': 334}, {'field': 'instoreAmount', 'old_value': 114906.0, 'new_value': 122571.4}, {'field': 'instoreCount', 'old_value': 312, 'new_value': 334}]
2025-06-14 08:13:37,914 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:38,461 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQI
2025-06-14 08:13:38,461 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 110949.3, 'new_value': 117640.46}, {'field': 'dailyBillAmount', 'old_value': 110949.3, 'new_value': 117640.46}, {'field': 'amount', 'old_value': 31949.7, 'new_value': 33293.7}, {'field': 'count', 'old_value': 79, 'new_value': 83}, {'field': 'instoreAmount', 'old_value': 31949.7, 'new_value': 33293.7}, {'field': 'instoreCount', 'old_value': 79, 'new_value': 83}]
2025-06-14 08:13:38,461 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:38,914 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRI
2025-06-14 08:13:38,914 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 163800.14, 'new_value': 179031.5}, {'field': 'dailyBillAmount', 'old_value': 163800.14, 'new_value': 179031.5}, {'field': 'amount', 'old_value': 66081.0, 'new_value': 72176.1}, {'field': 'count', 'old_value': 265, 'new_value': 292}, {'field': 'instoreAmount', 'old_value': 66081.0, 'new_value': 72176.1}, {'field': 'instoreCount', 'old_value': 265, 'new_value': 292}]
2025-06-14 08:13:38,914 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:39,336 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSI
2025-06-14 08:13:39,352 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 47693.09, 'new_value': 51703.59}, {'field': 'dailyBillAmount', 'old_value': 47693.09, 'new_value': 51703.59}, {'field': 'amount', 'old_value': 9782.1, 'new_value': 10462.08}, {'field': 'count', 'old_value': 369, 'new_value': 400}, {'field': 'instoreAmount', 'old_value': 2933.6, 'new_value': 3150.9}, {'field': 'instoreCount', 'old_value': 72, 'new_value': 78}, {'field': 'onlineAmount', 'old_value': 7034.34, 'new_value': 7497.55}, {'field': 'onlineCount', 'old_value': 297, 'new_value': 322}]
2025-06-14 08:13:39,352 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:39,821 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTI
2025-06-14 08:13:39,821 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 75886.71, 'new_value': 82198.04}, {'field': 'dailyBillAmount', 'old_value': 75886.71, 'new_value': 82198.04}, {'field': 'amount', 'old_value': 13845.37, 'new_value': 14778.11}, {'field': 'count', 'old_value': 365, 'new_value': 393}, {'field': 'instoreAmount', 'old_value': 11176.02, 'new_value': 11880.55}, {'field': 'instoreCount', 'old_value': 296, 'new_value': 318}, {'field': 'onlineAmount', 'old_value': 2731.6, 'new_value': 2959.81}, {'field': 'onlineCount', 'old_value': 69, 'new_value': 75}]
2025-06-14 08:13:39,821 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:40,274 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUI
2025-06-14 08:13:40,274 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13333.69, 'new_value': 13899.49}, {'field': 'dailyBillAmount', 'old_value': 13333.69, 'new_value': 13899.49}, {'field': 'amount', 'old_value': 10003.6, 'new_value': 10569.4}, {'field': 'count', 'old_value': 386, 'new_value': 406}, {'field': 'instoreAmount', 'old_value': 10103.6, 'new_value': 10669.4}, {'field': 'instoreCount', 'old_value': 386, 'new_value': 406}]
2025-06-14 08:13:40,274 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:40,743 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVI
2025-06-14 08:13:40,743 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22694.5, 'new_value': 24884.1}, {'field': 'dailyBillAmount', 'old_value': 22694.5, 'new_value': 24884.1}, {'field': 'amount', 'old_value': 13496.72, 'new_value': 14778.43}, {'field': 'count', 'old_value': 704, 'new_value': 780}, {'field': 'instoreAmount', 'old_value': 4486.37, 'new_value': 4897.36}, {'field': 'instoreCount', 'old_value': 176, 'new_value': 192}, {'field': 'onlineAmount', 'old_value': 9284.85, 'new_value': 10166.67}, {'field': 'onlineCount', 'old_value': 528, 'new_value': 588}]
2025-06-14 08:13:40,743 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:41,243 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCJ
2025-06-14 08:13:41,243 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 76434.5, 'new_value': 82007.9}, {'field': 'dailyBillAmount', 'old_value': 76434.5, 'new_value': 82007.9}, {'field': 'amount', 'old_value': 91172.5, 'new_value': 98581.4}, {'field': 'count', 'old_value': 336, 'new_value': 362}, {'field': 'instoreAmount', 'old_value': 92398.5, 'new_value': 99807.4}, {'field': 'instoreCount', 'old_value': 336, 'new_value': 362}]
2025-06-14 08:13:41,243 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:41,633 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBJ
2025-06-14 08:13:41,633 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 43596.57, 'new_value': 47733.84}, {'field': 'dailyBillAmount', 'old_value': 43596.57, 'new_value': 47733.84}, {'field': 'amount', 'old_value': 45014.06, 'new_value': 49310.39}, {'field': 'count', 'old_value': 223, 'new_value': 240}, {'field': 'instoreAmount', 'old_value': 42796.08, 'new_value': 47045.53}, {'field': 'instoreCount', 'old_value': 197, 'new_value': 213}, {'field': 'onlineAmount', 'old_value': 2217.98, 'new_value': 2265.08}, {'field': 'onlineCount', 'old_value': 26, 'new_value': 27}]
2025-06-14 08:13:41,633 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:42,071 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAJ
2025-06-14 08:13:42,071 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 75722.09, 'new_value': 82730.09}, {'field': 'dailyBillAmount', 'old_value': 74330.25, 'new_value': 81284.38}, {'field': 'amount', 'old_value': 75722.09, 'new_value': 82730.09}, {'field': 'count', 'old_value': 969, 'new_value': 1061}, {'field': 'instoreAmount', 'old_value': 72326.12, 'new_value': 79090.12}, {'field': 'instoreCount', 'old_value': 922, 'new_value': 1011}, {'field': 'onlineAmount', 'old_value': 3449.97, 'new_value': 3693.97}, {'field': 'onlineCount', 'old_value': 47, 'new_value': 50}]
2025-06-14 08:13:42,071 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:42,524 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLI
2025-06-14 08:13:42,524 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 39787.94, 'new_value': 44508.94}, {'field': 'amount', 'old_value': 39787.94, 'new_value': 44508.94}, {'field': 'count', 'old_value': 59, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 39787.94, 'new_value': 44508.94}, {'field': 'instoreCount', 'old_value': 59, 'new_value': 64}]
2025-06-14 08:13:42,524 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:42,993 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9J
2025-06-14 08:13:42,993 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 64926.7, 'new_value': 69432.56}, {'field': 'dailyBillAmount', 'old_value': 64926.7, 'new_value': 69432.56}, {'field': 'amount', 'old_value': 39972.99, 'new_value': 42753.8}, {'field': 'count', 'old_value': 1002, 'new_value': 1078}, {'field': 'instoreAmount', 'old_value': 33614.99, 'new_value': 35899.53}, {'field': 'instoreCount', 'old_value': 837, 'new_value': 897}, {'field': 'onlineAmount', 'old_value': 7302.0, 'new_value': 7798.27}, {'field': 'onlineCount', 'old_value': 165, 'new_value': 181}]
2025-06-14 08:13:42,993 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:43,508 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8J
2025-06-14 08:13:43,508 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36244.9, 'new_value': 39235.0}, {'field': 'dailyBillAmount', 'old_value': 31201.1, 'new_value': 34191.2}, {'field': 'amount', 'old_value': 36244.3, 'new_value': 39234.4}, {'field': 'count', 'old_value': 124, 'new_value': 138}, {'field': 'instoreAmount', 'old_value': 39188.3, 'new_value': 42823.7}, {'field': 'instoreCount', 'old_value': 124, 'new_value': 138}]
2025-06-14 08:13:43,508 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:43,977 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7J
2025-06-14 08:13:43,977 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24733.0, 'new_value': 25733.0}, {'field': 'dailyBillAmount', 'old_value': 24733.0, 'new_value': 25733.0}, {'field': 'amount', 'old_value': 28027.0, 'new_value': 28924.0}, {'field': 'count', 'old_value': 53, 'new_value': 54}, {'field': 'instoreAmount', 'old_value': 29111.0, 'new_value': 30008.0}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 54}]
2025-06-14 08:13:43,992 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:44,399 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6J
2025-06-14 08:13:44,399 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40971.74, 'new_value': 44209.54}, {'field': 'dailyBillAmount', 'old_value': 40971.74, 'new_value': 44209.54}, {'field': 'amount', 'old_value': 38423.74, 'new_value': 41661.54}, {'field': 'count', 'old_value': 149, 'new_value': 160}, {'field': 'instoreAmount', 'old_value': 38662.74, 'new_value': 41900.54}, {'field': 'instoreCount', 'old_value': 149, 'new_value': 160}]
2025-06-14 08:13:44,414 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:44,883 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5J
2025-06-14 08:13:44,883 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 125234.0, 'new_value': 132357.0}, {'field': 'dailyBillAmount', 'old_value': 125234.0, 'new_value': 132357.0}, {'field': 'amount', 'old_value': 51218.0, 'new_value': 53991.0}, {'field': 'count', 'old_value': 150, 'new_value': 160}, {'field': 'instoreAmount', 'old_value': 51218.0, 'new_value': 53991.0}, {'field': 'instoreCount', 'old_value': 150, 'new_value': 160}]
2025-06-14 08:13:44,883 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:45,336 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4J
2025-06-14 08:13:45,336 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37668.9, 'new_value': 40718.2}, {'field': 'amount', 'old_value': 37668.9, 'new_value': 40718.2}, {'field': 'count', 'old_value': 894, 'new_value': 963}, {'field': 'instoreAmount', 'old_value': 38132.2, 'new_value': 41181.5}, {'field': 'instoreCount', 'old_value': 894, 'new_value': 963}]
2025-06-14 08:13:45,336 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:45,727 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3J
2025-06-14 08:13:45,727 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-06, 变更字段: [{'field': 'amount', 'old_value': 24546.11, 'new_value': 27262.81}, {'field': 'count', 'old_value': 306, 'new_value': 340}, {'field': 'instoreAmount', 'old_value': 24546.11, 'new_value': 27262.81}, {'field': 'instoreCount', 'old_value': 306, 'new_value': 340}]
2025-06-14 08:13:45,727 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:46,164 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2J
2025-06-14 08:13:46,164 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-06, 变更字段: [{'field': 'amount', 'old_value': 15980.91, 'new_value': 16509.88}, {'field': 'count', 'old_value': 665, 'new_value': 691}, {'field': 'onlineAmount', 'old_value': 10554.5, 'new_value': 11083.47}, {'field': 'onlineCount', 'old_value': 482, 'new_value': 508}]
2025-06-14 08:13:46,164 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:46,617 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1J
2025-06-14 08:13:46,633 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 39878.39, 'new_value': 44139.27}, {'field': 'dailyBillAmount', 'old_value': 39878.39, 'new_value': 44139.27}, {'field': 'amount', 'old_value': 41140.19, 'new_value': 45601.47}, {'field': 'count', 'old_value': 2309, 'new_value': 2555}, {'field': 'instoreAmount', 'old_value': 22886.4, 'new_value': 25194.39}, {'field': 'instoreCount', 'old_value': 1196, 'new_value': 1311}, {'field': 'onlineAmount', 'old_value': 18982.58, 'new_value': 21207.78}, {'field': 'onlineCount', 'old_value': 1113, 'new_value': 1244}]
2025-06-14 08:13:46,633 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:47,071 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0J
2025-06-14 08:13:47,071 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28571.0, 'new_value': 31423.0}, {'field': 'dailyBillAmount', 'old_value': 28571.0, 'new_value': 31423.0}, {'field': 'amount', 'old_value': 28192.0, 'new_value': 31044.0}, {'field': 'count', 'old_value': 551, 'new_value': 606}, {'field': 'instoreAmount', 'old_value': 28192.0, 'new_value': 31044.0}, {'field': 'instoreCount', 'old_value': 551, 'new_value': 606}]
2025-06-14 08:13:47,071 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:47,508 - INFO - 更新表单数据成功: FINST-L8D665C1C82WOKI6BGFCGBTDWGJP22VFUWMBM9U
2025-06-14 08:13:47,508 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSCOR6MVCC7Q2OV392411100148T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1532.0, 'new_value': 2882.0}, {'field': 'amount', 'old_value': 1532.0, 'new_value': 2882.0}, {'field': 'count', 'old_value': 3, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 1532.0, 'new_value': 2882.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 4}]
2025-06-14 08:13:47,508 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:47,946 - INFO - 更新表单数据成功: FINST-90D66XA12PZVBSKCD49BM87RNO2X30KE17HBMG6
2025-06-14 08:13:47,946 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-06, 变更字段: [{'field': 'amount', 'old_value': 4690.72, 'new_value': 5939.84}, {'field': 'count', 'old_value': 9, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 4690.72, 'new_value': 5939.84}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 11}]
2025-06-14 08:13:47,946 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:48,477 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXI
2025-06-14 08:13:48,477 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 84525.25, 'new_value': 94197.71}, {'field': 'dailyBillAmount', 'old_value': 84525.25, 'new_value': 94197.71}, {'field': 'amount', 'old_value': 42553.45, 'new_value': 46806.39}, {'field': 'count', 'old_value': 1898, 'new_value': 2092}, {'field': 'instoreAmount', 'old_value': 43863.6, 'new_value': 48210.74}, {'field': 'instoreCount', 'old_value': 1898, 'new_value': 2092}]
2025-06-14 08:13:48,477 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:49,024 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYI
2025-06-14 08:13:49,024 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 239754.3, 'new_value': 255127.9}, {'field': 'dailyBillAmount', 'old_value': 239754.3, 'new_value': 255127.9}, {'field': 'amount', 'old_value': 239754.3, 'new_value': 255127.9}, {'field': 'count', 'old_value': 299, 'new_value': 317}, {'field': 'instoreAmount', 'old_value': 239754.3, 'new_value': 255127.9}, {'field': 'instoreCount', 'old_value': 299, 'new_value': 317}]
2025-06-14 08:13:49,149 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-14 08:13:49,180 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:49,180 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:49,180 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:49,180 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:49,196 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:49,196 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:49,196 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:49,196 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:49,617 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMNK
2025-06-14 08:13:49,617 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15270.0, 'new_value': 16374.0}, {'field': 'amount', 'old_value': 15270.0, 'new_value': 16374.0}, {'field': 'count', 'old_value': 26, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 15270.0, 'new_value': 16374.0}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 28}]
2025-06-14 08:13:49,617 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:50,055 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMMK
2025-06-14 08:13:50,071 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 39885.0, 'new_value': 46468.0}, {'field': 'amount', 'old_value': 39885.0, 'new_value': 46468.0}, {'field': 'count', 'old_value': 14, 'new_value': 15}, {'field': 'instoreAmount', 'old_value': 39885.0, 'new_value': 46468.0}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 15}]
2025-06-14 08:13:50,071 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:50,617 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMKK
2025-06-14 08:13:50,617 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29305.0, 'new_value': 31435.0}, {'field': 'amount', 'old_value': 29305.0, 'new_value': 31435.0}, {'field': 'count', 'old_value': 69, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 29305.0, 'new_value': 31435.0}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 74}]
2025-06-14 08:13:50,617 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:51,180 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMFK
2025-06-14 08:13:51,180 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 85972.0, 'new_value': 97580.0}, {'field': 'dailyBillAmount', 'old_value': 85972.0, 'new_value': 97580.0}, {'field': 'amount', 'old_value': 108547.0, 'new_value': 113697.0}, {'field': 'count', 'old_value': 98, 'new_value': 103}, {'field': 'instoreAmount', 'old_value': 115984.0, 'new_value': 121134.0}, {'field': 'instoreCount', 'old_value': 98, 'new_value': 103}]
2025-06-14 08:13:51,180 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:51,696 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMJK
2025-06-14 08:13:51,696 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22720.0, 'new_value': 24602.0}, {'field': 'dailyBillAmount', 'old_value': 11124.0, 'new_value': 13006.0}, {'field': 'amount', 'old_value': 21132.0, 'new_value': 22575.0}, {'field': 'count', 'old_value': 30, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 21132.0, 'new_value': 22575.0}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 32}]
2025-06-14 08:13:51,696 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:52,133 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMIK
2025-06-14 08:13:52,133 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23037.0, 'new_value': 25583.0}, {'field': 'dailyBillAmount', 'old_value': 23037.0, 'new_value': 25583.0}, {'field': 'amount', 'old_value': 8725.0, 'new_value': 9952.0}, {'field': 'count', 'old_value': 25, 'new_value': 26}, {'field': 'instoreAmount', 'old_value': 8725.0, 'new_value': 9952.0}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 26}]
2025-06-14 08:13:52,133 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:52,586 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMGK
2025-06-14 08:13:52,586 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 67105.7, 'new_value': 65825.0}, {'field': 'dailyBillAmount', 'old_value': 67105.7, 'new_value': 65825.0}, {'field': 'amount', 'old_value': 67179.81, 'new_value': 69324.61}, {'field': 'count', 'old_value': 164, 'new_value': 170}, {'field': 'instoreAmount', 'old_value': 69394.51, 'new_value': 71539.31}, {'field': 'instoreCount', 'old_value': 164, 'new_value': 170}]
2025-06-14 08:13:52,586 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:53,024 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMEK
2025-06-14 08:13:53,024 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45477.56, 'new_value': 47739.35}, {'field': 'dailyBillAmount', 'old_value': 45477.56, 'new_value': 47739.35}, {'field': 'amount', 'old_value': 48312.73, 'new_value': 50822.96}, {'field': 'count', 'old_value': 1692, 'new_value': 1798}, {'field': 'instoreAmount', 'old_value': 48237.12, 'new_value': 50747.35}, {'field': 'instoreCount', 'old_value': 1687, 'new_value': 1793}]
2025-06-14 08:13:53,024 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:53,446 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLS
2025-06-14 08:13:53,446 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 211721.3, 'new_value': 231343.9}, {'field': 'dailyBillAmount', 'old_value': 211721.3, 'new_value': 231343.9}, {'field': 'amount', 'old_value': 176365.7, 'new_value': 189588.48}, {'field': 'count', 'old_value': 794, 'new_value': 861}, {'field': 'instoreAmount', 'old_value': 176365.7, 'new_value': 189588.48}, {'field': 'instoreCount', 'old_value': 794, 'new_value': 861}]
2025-06-14 08:13:53,446 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:53,914 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMDK
2025-06-14 08:13:53,914 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30843.96, 'new_value': 32988.07}, {'field': 'dailyBillAmount', 'old_value': 30843.96, 'new_value': 32988.07}, {'field': 'amount', 'old_value': 36382.54, 'new_value': 39058.92}, {'field': 'count', 'old_value': 1305, 'new_value': 1400}, {'field': 'instoreAmount', 'old_value': 12278.23, 'new_value': 12837.6}, {'field': 'instoreCount', 'old_value': 420, 'new_value': 442}, {'field': 'onlineAmount', 'old_value': 24586.91, 'new_value': 26703.92}, {'field': 'onlineCount', 'old_value': 885, 'new_value': 958}]
2025-06-14 08:13:53,914 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:54,399 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMBK
2025-06-14 08:13:54,399 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 83929.59, 'new_value': 88382.69}, {'field': 'dailyBillAmount', 'old_value': 83929.59, 'new_value': 88382.69}, {'field': 'amount', 'old_value': 71655.43, 'new_value': 75984.97}, {'field': 'count', 'old_value': 2170, 'new_value': 2291}, {'field': 'instoreAmount', 'old_value': 37760.79, 'new_value': 40036.95}, {'field': 'instoreCount', 'old_value': 1530, 'new_value': 1606}, {'field': 'onlineAmount', 'old_value': 38086.9, 'new_value': 40578.1}, {'field': 'onlineCount', 'old_value': 640, 'new_value': 685}]
2025-06-14 08:13:54,399 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:54,852 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMAK
2025-06-14 08:13:54,852 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-06, 变更字段: [{'field': 'amount', 'old_value': 47472.77, 'new_value': 51053.62}, {'field': 'count', 'old_value': 3337, 'new_value': 3568}, {'field': 'instoreAmount', 'old_value': 2827.5, 'new_value': 3175.2}, {'field': 'instoreCount', 'old_value': 188, 'new_value': 203}, {'field': 'onlineAmount', 'old_value': 46545.0, 'new_value': 49829.25}, {'field': 'onlineCount', 'old_value': 3149, 'new_value': 3365}]
2025-06-14 08:13:54,852 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:55,305 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM9K
2025-06-14 08:13:55,305 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 23344.99}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 23344.99}, {'field': 'amount', 'old_value': 77385.11, 'new_value': 85141.86}, {'field': 'count', 'old_value': 3843, 'new_value': 4114}, {'field': 'instoreAmount', 'old_value': 80710.99, 'new_value': 88729.77}, {'field': 'instoreCount', 'old_value': 3830, 'new_value': 4101}]
2025-06-14 08:13:55,305 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:55,774 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM8K
2025-06-14 08:13:55,774 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 52898.13, 'new_value': 54992.61}, {'field': 'amount', 'old_value': 52896.91, 'new_value': 54991.39}, {'field': 'count', 'old_value': 1374, 'new_value': 1433}, {'field': 'instoreAmount', 'old_value': 50114.26, 'new_value': 52030.94}, {'field': 'instoreCount', 'old_value': 1338, 'new_value': 1395}, {'field': 'onlineAmount', 'old_value': 2791.17, 'new_value': 2968.97}, {'field': 'onlineCount', 'old_value': 36, 'new_value': 38}]
2025-06-14 08:13:55,774 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:56,164 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7K
2025-06-14 08:13:56,164 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 66084.56, 'new_value': 70831.92}, {'field': 'dailyBillAmount', 'old_value': 66084.56, 'new_value': 70831.92}, {'field': 'amount', 'old_value': 48152.13, 'new_value': 51616.44}, {'field': 'count', 'old_value': 2256, 'new_value': 2401}, {'field': 'instoreAmount', 'old_value': 7609.61, 'new_value': 8227.26}, {'field': 'instoreCount', 'old_value': 511, 'new_value': 544}, {'field': 'onlineAmount', 'old_value': 41585.22, 'new_value': 44488.42}, {'field': 'onlineCount', 'old_value': 1745, 'new_value': 1857}]
2025-06-14 08:13:56,164 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:56,586 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6K
2025-06-14 08:13:56,586 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 67364.48, 'new_value': 71749.18}, {'field': 'dailyBillAmount', 'old_value': 67364.48, 'new_value': 71749.18}, {'field': 'amount', 'old_value': 13926.12, 'new_value': 14883.28}, {'field': 'count', 'old_value': 466, 'new_value': 509}, {'field': 'instoreAmount', 'old_value': 14188.46, 'new_value': 15200.94}, {'field': 'instoreCount', 'old_value': 466, 'new_value': 509}]
2025-06-14 08:13:56,586 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:57,102 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5K
2025-06-14 08:13:57,102 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50226.02, 'new_value': 53428.2}, {'field': 'amount', 'old_value': 50225.62, 'new_value': 53427.04}, {'field': 'count', 'old_value': 2854, 'new_value': 3039}, {'field': 'instoreAmount', 'old_value': 22438.28, 'new_value': 23352.82}, {'field': 'instoreCount', 'old_value': 1545, 'new_value': 1616}, {'field': 'onlineAmount', 'old_value': 27787.74, 'new_value': 30075.38}, {'field': 'onlineCount', 'old_value': 1309, 'new_value': 1423}]
2025-06-14 08:13:57,102 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:57,571 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4K
2025-06-14 08:13:57,571 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 111886.11, 'new_value': 118519.81}, {'field': 'dailyBillAmount', 'old_value': 111886.11, 'new_value': 118519.81}, {'field': 'amount', 'old_value': 102506.43, 'new_value': 109518.03}, {'field': 'count', 'old_value': 3140, 'new_value': 3354}, {'field': 'instoreAmount', 'old_value': 102971.87, 'new_value': 109983.47}, {'field': 'instoreCount', 'old_value': 3140, 'new_value': 3354}]
2025-06-14 08:13:57,571 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:58,055 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3K
2025-06-14 08:13:58,055 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 62040.69, 'new_value': 68271.06}, {'field': 'dailyBillAmount', 'old_value': 62040.69, 'new_value': 68271.06}, {'field': 'amount', 'old_value': 68731.68, 'new_value': 74166.7}, {'field': 'count', 'old_value': 4485, 'new_value': 4833}, {'field': 'instoreAmount', 'old_value': 50878.54, 'new_value': 54696.6}, {'field': 'instoreCount', 'old_value': 3074, 'new_value': 3294}, {'field': 'onlineAmount', 'old_value': 19318.55, 'new_value': 21114.21}, {'field': 'onlineCount', 'old_value': 1411, 'new_value': 1539}]
2025-06-14 08:13:58,071 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:58,430 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2K
2025-06-14 08:13:58,430 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-06, 变更字段: [{'field': 'amount', 'old_value': -5112.01, 'new_value': -5564.01}, {'field': 'count', 'old_value': 52, 'new_value': 54}, {'field': 'instoreAmount', 'old_value': 1085.0, 'new_value': 1111.0}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 14}, {'field': 'onlineAmount', 'old_value': 886.0, 'new_value': 909.0}, {'field': 'onlineCount', 'old_value': 39, 'new_value': 40}]
2025-06-14 08:13:58,430 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:58,946 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1K
2025-06-14 08:13:58,946 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 246943.51, 'new_value': 272717.46}, {'field': 'amount', 'old_value': 246943.51, 'new_value': 272717.46}, {'field': 'count', 'old_value': 5650, 'new_value': 6251}, {'field': 'instoreAmount', 'old_value': 177237.86, 'new_value': 196818.26}, {'field': 'instoreCount', 'old_value': 3775, 'new_value': 4224}, {'field': 'onlineAmount', 'old_value': 69705.65, 'new_value': 75899.2}, {'field': 'onlineCount', 'old_value': 1875, 'new_value': 2027}]
2025-06-14 08:13:58,946 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:59,367 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0K
2025-06-14 08:13:59,367 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 327027.51, 'new_value': 342127.19}, {'field': 'dailyBillAmount', 'old_value': 327027.51, 'new_value': 342127.19}, {'field': 'amount', 'old_value': 422759.13, 'new_value': 442735.04}, {'field': 'count', 'old_value': 7893, 'new_value': 8326}, {'field': 'instoreAmount', 'old_value': 406572.87, 'new_value': 425036.19}, {'field': 'instoreCount', 'old_value': 7557, 'new_value': 7955}, {'field': 'onlineAmount', 'old_value': 17304.23, 'new_value': 18885.82}, {'field': 'onlineCount', 'old_value': 336, 'new_value': 371}]
2025-06-14 08:13:59,367 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:13:59,836 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3S
2025-06-14 08:13:59,836 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 256415.02, 'new_value': 276898.03}, {'field': 'dailyBillAmount', 'old_value': 256415.02, 'new_value': 276898.03}, {'field': 'amount', 'old_value': 251826.49, 'new_value': 270997.44}, {'field': 'count', 'old_value': 2764, 'new_value': 2999}, {'field': 'instoreAmount', 'old_value': 194328.98, 'new_value': 205859.72}, {'field': 'instoreCount', 'old_value': 973, 'new_value': 1030}, {'field': 'onlineAmount', 'old_value': 58753.07, 'new_value': 66589.85}, {'field': 'onlineCount', 'old_value': 1791, 'new_value': 1969}]
2025-06-14 08:13:59,836 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:00,305 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYJ
2025-06-14 08:14:00,305 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 136646.19, 'new_value': 147483.42}, {'field': 'dailyBillAmount', 'old_value': 136646.19, 'new_value': 147483.42}, {'field': 'amount', 'old_value': 69718.83, 'new_value': 74725.49}, {'field': 'count', 'old_value': 1504, 'new_value': 1610}, {'field': 'instoreAmount', 'old_value': 58663.72, 'new_value': 63011.89}, {'field': 'instoreCount', 'old_value': 1275, 'new_value': 1365}, {'field': 'onlineAmount', 'old_value': 11954.08, 'new_value': 12619.94}, {'field': 'onlineCount', 'old_value': 229, 'new_value': 245}]
2025-06-14 08:14:00,305 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:00,789 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWJ
2025-06-14 08:14:00,789 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 78577.49, 'new_value': 84120.58}, {'field': 'amount', 'old_value': 78577.31, 'new_value': 84120.4}, {'field': 'count', 'old_value': 900, 'new_value': 964}, {'field': 'instoreAmount', 'old_value': 52581.57, 'new_value': 54670.05}, {'field': 'instoreCount', 'old_value': 515, 'new_value': 538}, {'field': 'onlineAmount', 'old_value': 27725.5, 'new_value': 31267.21}, {'field': 'onlineCount', 'old_value': 385, 'new_value': 426}]
2025-06-14 08:14:00,805 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:01,242 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZJ
2025-06-14 08:14:01,242 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 130476.4, 'new_value': 135821.1}, {'field': 'dailyBillAmount', 'old_value': 179343.1, 'new_value': 188171.0}, {'field': 'amount', 'old_value': 130476.4, 'new_value': 135821.1}, {'field': 'count', 'old_value': 474, 'new_value': 499}, {'field': 'instoreAmount', 'old_value': 132790.0, 'new_value': 138134.7}, {'field': 'instoreCount', 'old_value': 474, 'new_value': 499}]
2025-06-14 08:14:01,242 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:01,742 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWR
2025-06-14 08:14:01,742 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12579.6, 'new_value': 13053.2}, {'field': 'amount', 'old_value': 12579.6, 'new_value': 13053.2}, {'field': 'count', 'old_value': 43, 'new_value': 46}, {'field': 'instoreAmount', 'old_value': 12579.6, 'new_value': 13053.2}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 46}]
2025-06-14 08:14:01,758 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:02,196 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUR
2025-06-14 08:14:02,196 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32064.0, 'new_value': 33633.0}, {'field': 'amount', 'old_value': 32064.0, 'new_value': 33633.0}, {'field': 'count', 'old_value': 48, 'new_value': 50}, {'field': 'instoreAmount', 'old_value': 34032.0, 'new_value': 35601.0}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 50}]
2025-06-14 08:14:02,211 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:02,664 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTR
2025-06-14 08:14:02,664 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 408109.14, 'new_value': 413601.08}, {'field': 'dailyBillAmount', 'old_value': 408109.14, 'new_value': 413601.08}, {'field': 'amount', 'old_value': 32840.37, 'new_value': 34017.7}, {'field': 'count', 'old_value': 329, 'new_value': 345}, {'field': 'instoreAmount', 'old_value': 26566.19, 'new_value': 27505.14}, {'field': 'instoreCount', 'old_value': 242, 'new_value': 254}, {'field': 'onlineAmount', 'old_value': 6394.14, 'new_value': 6751.56}, {'field': 'onlineCount', 'old_value': 87, 'new_value': 91}]
2025-06-14 08:14:02,664 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:03,164 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSR
2025-06-14 08:14:03,164 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4178.0, 'new_value': 4357.0}, {'field': 'dailyBillAmount', 'old_value': 4178.0, 'new_value': 4357.0}, {'field': 'amount', 'old_value': 23705.0, 'new_value': 24242.0}, {'field': 'count', 'old_value': 62, 'new_value': 65}, {'field': 'instoreAmount', 'old_value': 23903.0, 'new_value': 24440.0}, {'field': 'instoreCount', 'old_value': 62, 'new_value': 65}]
2025-06-14 08:14:03,164 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:03,664 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRR
2025-06-14 08:14:03,664 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14706.9, 'new_value': 15250.9}, {'field': 'amount', 'old_value': 14706.9, 'new_value': 15250.9}, {'field': 'count', 'old_value': 96, 'new_value': 102}, {'field': 'instoreAmount', 'old_value': 14814.9, 'new_value': 15358.9}, {'field': 'instoreCount', 'old_value': 96, 'new_value': 102}]
2025-06-14 08:14:03,664 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:04,164 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMSK
2025-06-14 08:14:04,164 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12105.0, 'new_value': 12995.0}, {'field': 'dailyBillAmount', 'old_value': 12105.0, 'new_value': 12995.0}, {'field': 'amount', 'old_value': 12100.0, 'new_value': 12990.0}, {'field': 'count', 'old_value': 48, 'new_value': 52}, {'field': 'instoreAmount', 'old_value': 12630.0, 'new_value': 13520.0}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 52}]
2025-06-14 08:14:04,164 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:04,602 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMQK
2025-06-14 08:14:04,602 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28595.7, 'new_value': 30087.6}, {'field': 'dailyBillAmount', 'old_value': 28595.7, 'new_value': 30087.6}, {'field': 'amount', 'old_value': 27230.73, 'new_value': 28846.02}, {'field': 'count', 'old_value': 255, 'new_value': 276}, {'field': 'instoreAmount', 'old_value': 25310.3, 'new_value': 26875.29}, {'field': 'instoreCount', 'old_value': 224, 'new_value': 244}, {'field': 'onlineAmount', 'old_value': 2329.83, 'new_value': 2380.13}, {'field': 'onlineCount', 'old_value': 31, 'new_value': 32}]
2025-06-14 08:14:04,602 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:05,024 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMPK
2025-06-14 08:14:05,024 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 109322.5, 'new_value': 114685.8}, {'field': 'dailyBillAmount', 'old_value': 109322.5, 'new_value': 114685.8}, {'field': 'amount', 'old_value': 104417.8, 'new_value': 111144.4}, {'field': 'count', 'old_value': 141, 'new_value': 153}, {'field': 'instoreAmount', 'old_value': 106343.9, 'new_value': 113750.3}, {'field': 'instoreCount', 'old_value': 141, 'new_value': 153}]
2025-06-14 08:14:05,024 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:05,430 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXR
2025-06-14 08:14:05,430 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24057.0, 'new_value': 25299.0}, {'field': 'dailyBillAmount', 'old_value': 24057.0, 'new_value': 25299.0}, {'field': 'amount', 'old_value': 24201.0, 'new_value': 25443.0}, {'field': 'count', 'old_value': 40, 'new_value': 43}, {'field': 'instoreAmount', 'old_value': 25836.0, 'new_value': 27078.0}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 43}]
2025-06-14 08:14:05,430 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:05,899 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYR
2025-06-14 08:14:05,899 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 106886.51, 'new_value': 111324.95}, {'field': 'dailyBillAmount', 'old_value': 94911.39, 'new_value': 98329.89}, {'field': 'amount', 'old_value': 103572.74, 'new_value': 108011.18}, {'field': 'count', 'old_value': 510, 'new_value': 520}, {'field': 'instoreAmount', 'old_value': 104571.0, 'new_value': 109009.44}, {'field': 'instoreCount', 'old_value': 510, 'new_value': 520}]
2025-06-14 08:14:05,899 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:06,367 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0S
2025-06-14 08:14:06,367 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDQR0OPQVI0I86N3H2U1NE001F0K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 33516.0, 'new_value': 36387.0}, {'field': 'amount', 'old_value': 33516.0, 'new_value': 36387.0}, {'field': 'count', 'old_value': 8, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 33516.0, 'new_value': 36387.0}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 9}]
2025-06-14 08:14:06,367 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:06,852 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1S
2025-06-14 08:14:06,852 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 106909.37, 'new_value': 113106.13}, {'field': 'dailyBillAmount', 'old_value': 106909.37, 'new_value': 113106.13}, {'field': 'amount', 'old_value': 106314.37, 'new_value': 112511.13}, {'field': 'count', 'old_value': 627, 'new_value': 665}, {'field': 'instoreAmount', 'old_value': 106314.37, 'new_value': 112511.13}, {'field': 'instoreCount', 'old_value': 627, 'new_value': 665}]
2025-06-14 08:14:06,852 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:07,305 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2S
2025-06-14 08:14:07,305 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40420.38, 'new_value': 43222.04}, {'field': 'dailyBillAmount', 'old_value': 40420.38, 'new_value': 43222.04}, {'field': 'amount', 'old_value': 19002.57, 'new_value': 20160.27}, {'field': 'count', 'old_value': 1793, 'new_value': 1929}, {'field': 'instoreAmount', 'old_value': 19523.16, 'new_value': 20680.86}, {'field': 'instoreCount', 'old_value': 1793, 'new_value': 1929}]
2025-06-14 08:14:07,305 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:07,805 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4S
2025-06-14 08:14:07,805 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 81160.6, 'new_value': 83143.4}, {'field': 'amount', 'old_value': 81160.6, 'new_value': 83143.4}, {'field': 'count', 'old_value': 523, 'new_value': 541}, {'field': 'instoreAmount', 'old_value': 81160.6, 'new_value': 83143.4}, {'field': 'instoreCount', 'old_value': 523, 'new_value': 541}]
2025-06-14 08:14:07,805 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:08,258 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5S
2025-06-14 08:14:08,258 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-06, 变更字段: [{'field': 'amount', 'old_value': 78176.74, 'new_value': 85118.14}, {'field': 'count', 'old_value': 3306, 'new_value': 3627}, {'field': 'instoreAmount', 'old_value': 25763.46, 'new_value': 28440.66}, {'field': 'instoreCount', 'old_value': 1143, 'new_value': 1274}, {'field': 'onlineAmount', 'old_value': 53743.21, 'new_value': 58173.97}, {'field': 'onlineCount', 'old_value': 2163, 'new_value': 2353}]
2025-06-14 08:14:08,258 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:08,695 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6S
2025-06-14 08:14:08,695 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35049.21, 'new_value': 37660.12}, {'field': 'dailyBillAmount', 'old_value': 35049.21, 'new_value': 37660.12}, {'field': 'amount', 'old_value': 53877.08, 'new_value': 58335.55}, {'field': 'count', 'old_value': 2645, 'new_value': 2823}, {'field': 'instoreAmount', 'old_value': 27608.42, 'new_value': 29718.79}, {'field': 'instoreCount', 'old_value': 1565, 'new_value': 1658}, {'field': 'onlineAmount', 'old_value': 27166.97, 'new_value': 29540.07}, {'field': 'onlineCount', 'old_value': 1080, 'new_value': 1165}]
2025-06-14 08:14:08,711 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:09,180 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7S
2025-06-14 08:14:09,180 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1325.0, 'new_value': 1411.0}, {'field': 'amount', 'old_value': 1325.0, 'new_value': 1411.0}, {'field': 'count', 'old_value': 47, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 1325.0, 'new_value': 1411.0}, {'field': 'instoreCount', 'old_value': 47, 'new_value': 48}]
2025-06-14 08:14:09,180 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:09,617 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8S
2025-06-14 08:14:09,617 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22932.0, 'new_value': 24331.0}, {'field': 'amount', 'old_value': 22932.0, 'new_value': 24331.0}, {'field': 'count', 'old_value': 21, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 22932.0, 'new_value': 24331.0}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 22}]
2025-06-14 08:14:09,617 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:10,039 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9S
2025-06-14 08:14:10,039 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50524.61, 'new_value': 54109.04}, {'field': 'dailyBillAmount', 'old_value': 50524.61, 'new_value': 54109.04}, {'field': 'amount', 'old_value': 28132.5, 'new_value': 30377.27}, {'field': 'count', 'old_value': 2015, 'new_value': 2167}, {'field': 'instoreAmount', 'old_value': 4055.68, 'new_value': 4096.78}, {'field': 'instoreCount', 'old_value': 182, 'new_value': 188}, {'field': 'onlineAmount', 'old_value': 24076.82, 'new_value': 26280.49}, {'field': 'onlineCount', 'old_value': 1833, 'new_value': 1979}]
2025-06-14 08:14:10,039 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:10,445 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAS
2025-06-14 08:14:10,445 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 142526.32, 'new_value': 154053.37}, {'field': 'dailyBillAmount', 'old_value': 142526.32, 'new_value': 154053.37}, {'field': 'amount', 'old_value': 134874.5, 'new_value': 147045.85}, {'field': 'count', 'old_value': 1290, 'new_value': 1432}, {'field': 'instoreAmount', 'old_value': 108816.19, 'new_value': 118163.13}, {'field': 'instoreCount', 'old_value': 783, 'new_value': 864}, {'field': 'onlineAmount', 'old_value': 26060.11, 'new_value': 28884.52}, {'field': 'onlineCount', 'old_value': 507, 'new_value': 568}]
2025-06-14 08:14:10,445 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:10,883 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBS
2025-06-14 08:14:10,883 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 173388.38, 'new_value': 186230.91}, {'field': 'dailyBillAmount', 'old_value': 173388.38, 'new_value': 186230.91}, {'field': 'amount', 'old_value': 149492.5, 'new_value': 160367.9}, {'field': 'count', 'old_value': 871, 'new_value': 928}, {'field': 'instoreAmount', 'old_value': 153472.4, 'new_value': 164499.8}, {'field': 'instoreCount', 'old_value': 871, 'new_value': 928}]
2025-06-14 08:14:10,883 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:11,289 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCS
2025-06-14 08:14:11,289 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 441282.31, 'new_value': 470359.94}, {'field': 'dailyBillAmount', 'old_value': 441282.31, 'new_value': 470359.94}, {'field': 'amount', 'old_value': 486198.49, 'new_value': 517496.18}, {'field': 'count', 'old_value': 2865, 'new_value': 3087}, {'field': 'instoreAmount', 'old_value': 355794.16, 'new_value': 374917.05}, {'field': 'instoreCount', 'old_value': 1440, 'new_value': 1528}, {'field': 'onlineAmount', 'old_value': 133097.36, 'new_value': 145551.86}, {'field': 'onlineCount', 'old_value': 1425, 'new_value': 1559}]
2025-06-14 08:14:11,289 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:11,774 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDS
2025-06-14 08:14:11,774 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 134819.54, 'new_value': 142949.6}, {'field': 'dailyBillAmount', 'old_value': 134819.54, 'new_value': 142949.6}, {'field': 'amount', 'old_value': 191283.04, 'new_value': 201967.82}, {'field': 'count', 'old_value': 971, 'new_value': 1037}, {'field': 'instoreAmount', 'old_value': 177047.65, 'new_value': 186695.85}, {'field': 'instoreCount', 'old_value': 712, 'new_value': 750}, {'field': 'onlineAmount', 'old_value': 14529.24, 'new_value': 15632.42}, {'field': 'onlineCount', 'old_value': 259, 'new_value': 287}]
2025-06-14 08:14:11,774 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:12,195 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMES
2025-06-14 08:14:12,195 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 165398.88, 'new_value': 174605.12}, {'field': 'dailyBillAmount', 'old_value': 165398.88, 'new_value': 174605.12}, {'field': 'amount', 'old_value': 157539.3, 'new_value': 165975.9}, {'field': 'count', 'old_value': 707, 'new_value': 750}, {'field': 'instoreAmount', 'old_value': 159409.1, 'new_value': 167845.7}, {'field': 'instoreCount', 'old_value': 707, 'new_value': 750}]
2025-06-14 08:14:12,195 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:12,602 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFS
2025-06-14 08:14:12,602 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 357307.68, 'new_value': 378817.26}, {'field': 'amount', 'old_value': 357307.68, 'new_value': 378817.26}, {'field': 'count', 'old_value': 2933, 'new_value': 3129}, {'field': 'instoreAmount', 'old_value': 357307.68, 'new_value': 378817.26}, {'field': 'instoreCount', 'old_value': 2933, 'new_value': 3129}]
2025-06-14 08:14:12,602 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:13,024 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGS
2025-06-14 08:14:13,024 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 346934.96, 'new_value': 374281.39}, {'field': 'dailyBillAmount', 'old_value': 346934.96, 'new_value': 374281.39}, {'field': 'amount', 'old_value': 398291.94, 'new_value': 430368.14}, {'field': 'count', 'old_value': 2876, 'new_value': 3142}, {'field': 'instoreAmount', 'old_value': 218044.0, 'new_value': 232667.2}, {'field': 'instoreCount', 'old_value': 1218, 'new_value': 1314}, {'field': 'onlineAmount', 'old_value': 187072.4, 'new_value': 205067.4}, {'field': 'onlineCount', 'old_value': 1658, 'new_value': 1828}]
2025-06-14 08:14:13,024 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:13,430 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHS
2025-06-14 08:14:13,430 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 207650.61, 'new_value': 220513.98}, {'field': 'dailyBillAmount', 'old_value': 207650.61, 'new_value': 220513.98}, {'field': 'amount', 'old_value': 204254.28, 'new_value': 216357.89}, {'field': 'count', 'old_value': 2172, 'new_value': 2329}, {'field': 'instoreAmount', 'old_value': 144086.86, 'new_value': 150754.36}, {'field': 'instoreCount', 'old_value': 1008, 'new_value': 1062}, {'field': 'onlineAmount', 'old_value': 61127.56, 'new_value': 66564.18}, {'field': 'onlineCount', 'old_value': 1164, 'new_value': 1267}]
2025-06-14 08:14:13,430 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:13,883 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIS
2025-06-14 08:14:13,883 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 245080.49, 'new_value': 265379.29}, {'field': 'dailyBillAmount', 'old_value': 245080.49, 'new_value': 265379.29}, {'field': 'amount', 'old_value': 246293.28, 'new_value': 266251.61}, {'field': 'count', 'old_value': 2038, 'new_value': 2189}, {'field': 'instoreAmount', 'old_value': 221392.7, 'new_value': 238870.71}, {'field': 'instoreCount', 'old_value': 1185, 'new_value': 1273}, {'field': 'onlineAmount', 'old_value': 25120.08, 'new_value': 27630.8}, {'field': 'onlineCount', 'old_value': 853, 'new_value': 916}]
2025-06-14 08:14:13,883 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:14,336 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJS
2025-06-14 08:14:14,336 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 58961.4, 'new_value': 63193.4}, {'field': 'amount', 'old_value': 58961.4, 'new_value': 63193.4}, {'field': 'count', 'old_value': 246, 'new_value': 271}, {'field': 'instoreAmount', 'old_value': 58961.4, 'new_value': 63193.4}, {'field': 'instoreCount', 'old_value': 246, 'new_value': 271}]
2025-06-14 08:14:14,336 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:14,742 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKS
2025-06-14 08:14:14,742 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 167234.82, 'new_value': 171639.97}, {'field': 'dailyBillAmount', 'old_value': 167234.82, 'new_value': 171639.97}, {'field': 'amount', 'old_value': -115881.3, 'new_value': -124263.03}, {'field': 'count', 'old_value': 390, 'new_value': 421}, {'field': 'instoreAmount', 'old_value': 2899.9, 'new_value': 3160.7}, {'field': 'instoreCount', 'old_value': 126, 'new_value': 135}, {'field': 'onlineAmount', 'old_value': 6577.52, 'new_value': 6938.99}, {'field': 'onlineCount', 'old_value': 264, 'new_value': 286}]
2025-06-14 08:14:14,742 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:15,149 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMS
2025-06-14 08:14:15,149 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 223109.83, 'new_value': 238786.15}, {'field': 'dailyBillAmount', 'old_value': 223109.83, 'new_value': 238786.15}, {'field': 'amount', 'old_value': 60848.2, 'new_value': 64973.8}, {'field': 'count', 'old_value': 261, 'new_value': 282}, {'field': 'instoreAmount', 'old_value': 61375.0, 'new_value': 65475.3}, {'field': 'instoreCount', 'old_value': 251, 'new_value': 271}, {'field': 'onlineAmount', 'old_value': 1113.3, 'new_value': 1138.6}, {'field': 'onlineCount', 'old_value': 10, 'new_value': 11}]
2025-06-14 08:14:15,149 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:15,711 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNS
2025-06-14 08:14:15,711 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 149914.54, 'new_value': 159264.85}, {'field': 'dailyBillAmount', 'old_value': 149914.54, 'new_value': 159264.85}, {'field': 'amount', 'old_value': 146266.23, 'new_value': 155404.72}, {'field': 'count', 'old_value': 916, 'new_value': 1001}, {'field': 'instoreAmount', 'old_value': 138952.29, 'new_value': 147096.02}, {'field': 'instoreCount', 'old_value': 716, 'new_value': 769}, {'field': 'onlineAmount', 'old_value': 7398.23, 'new_value': 8392.99}, {'field': 'onlineCount', 'old_value': 200, 'new_value': 232}]
2025-06-14 08:14:15,711 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:16,086 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOS
2025-06-14 08:14:16,086 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 152688.06, 'new_value': 162759.44}, {'field': 'dailyBillAmount', 'old_value': 152688.06, 'new_value': 162759.44}, {'field': 'amount', 'old_value': 65223.51, 'new_value': 70018.23}, {'field': 'count', 'old_value': 919, 'new_value': 995}, {'field': 'instoreAmount', 'old_value': 42731.28, 'new_value': 45156.78}, {'field': 'instoreCount', 'old_value': 232, 'new_value': 243}, {'field': 'onlineAmount', 'old_value': 22493.16, 'new_value': 24862.38}, {'field': 'onlineCount', 'old_value': 687, 'new_value': 752}]
2025-06-14 08:14:16,086 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:16,539 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMK1
2025-06-14 08:14:16,539 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12287.0, 'new_value': 12980.0}, {'field': 'dailyBillAmount', 'old_value': 12287.0, 'new_value': 12980.0}, {'field': 'amount', 'old_value': 19216.0, 'new_value': 26457.0}, {'field': 'count', 'old_value': 13, 'new_value': 17}, {'field': 'instoreAmount', 'old_value': 19216.0, 'new_value': 26457.0}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 17}]
2025-06-14 08:14:16,539 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:17,039 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPS
2025-06-14 08:14:17,039 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 67487.63, 'new_value': 73984.14}, {'field': 'amount', 'old_value': 67486.19, 'new_value': 73981.44}, {'field': 'count', 'old_value': 3152, 'new_value': 3466}, {'field': 'instoreAmount', 'old_value': 17615.35, 'new_value': 19191.44}, {'field': 'instoreCount', 'old_value': 728, 'new_value': 799}, {'field': 'onlineAmount', 'old_value': 51301.15, 'new_value': 56486.37}, {'field': 'onlineCount', 'old_value': 2424, 'new_value': 2667}]
2025-06-14 08:14:17,039 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:17,492 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQS
2025-06-14 08:14:17,492 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19184.0, 'new_value': 19864.0}, {'field': 'amount', 'old_value': 19184.0, 'new_value': 19864.0}, {'field': 'count', 'old_value': 76, 'new_value': 78}, {'field': 'instoreAmount', 'old_value': 19184.0, 'new_value': 19864.0}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 78}]
2025-06-14 08:14:17,492 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:17,945 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRS
2025-06-14 08:14:17,945 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 171520.1, 'new_value': 184157.8}, {'field': 'dailyBillAmount', 'old_value': 171520.1, 'new_value': 184157.8}, {'field': 'amount', 'old_value': 67759.19, 'new_value': 72280.39}, {'field': 'count', 'old_value': 1341, 'new_value': 1429}, {'field': 'instoreAmount', 'old_value': 68179.9, 'new_value': 72701.1}, {'field': 'instoreCount', 'old_value': 1341, 'new_value': 1429}]
2025-06-14 08:14:17,945 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:18,399 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSS
2025-06-14 08:14:18,399 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 77450.66, 'new_value': 82078.18}, {'field': 'amount', 'old_value': 77450.66, 'new_value': 82078.18}, {'field': 'count', 'old_value': 1818, 'new_value': 1922}, {'field': 'instoreAmount', 'old_value': 77450.66, 'new_value': 82078.18}, {'field': 'instoreCount', 'old_value': 1818, 'new_value': 1922}]
2025-06-14 08:14:18,399 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:18,820 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTS
2025-06-14 08:14:18,820 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18425.46, 'new_value': 19289.54}, {'field': 'amount', 'old_value': 18423.91, 'new_value': 19287.99}, {'field': 'count', 'old_value': 1135, 'new_value': 1206}, {'field': 'instoreAmount', 'old_value': 7285.88, 'new_value': 7583.38}, {'field': 'instoreCount', 'old_value': 378, 'new_value': 405}, {'field': 'onlineAmount', 'old_value': 11404.96, 'new_value': 11971.54}, {'field': 'onlineCount', 'old_value': 757, 'new_value': 801}]
2025-06-14 08:14:18,820 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:19,258 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUS
2025-06-14 08:14:19,258 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22062.8, 'new_value': 22390.8}, {'field': 'amount', 'old_value': 22062.8, 'new_value': 22390.8}, {'field': 'count', 'old_value': 64, 'new_value': 66}, {'field': 'instoreAmount', 'old_value': 22062.8, 'new_value': 22390.8}, {'field': 'instoreCount', 'old_value': 64, 'new_value': 66}]
2025-06-14 08:14:19,258 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:19,711 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZR
2025-06-14 08:14:19,711 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 51189.0, 'new_value': 51595.0}, {'field': 'amount', 'old_value': 51189.0, 'new_value': 51595.0}, {'field': 'count', 'old_value': 287, 'new_value': 291}, {'field': 'instoreAmount', 'old_value': 55249.0, 'new_value': 55655.0}, {'field': 'instoreCount', 'old_value': 287, 'new_value': 291}]
2025-06-14 08:14:19,742 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:20,133 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZS
2025-06-14 08:14:20,133 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 67852.86, 'new_value': 74661.27}, {'field': 'dailyBillAmount', 'old_value': 67854.38, 'new_value': 74697.66}, {'field': 'amount', 'old_value': 67851.04, 'new_value': 74659.22}, {'field': 'count', 'old_value': 3820, 'new_value': 4223}, {'field': 'instoreAmount', 'old_value': 33527.34, 'new_value': 37434.16}, {'field': 'instoreCount', 'old_value': 1835, 'new_value': 2062}, {'field': 'onlineAmount', 'old_value': 34814.03, 'new_value': 37799.53}, {'field': 'onlineCount', 'old_value': 1985, 'new_value': 2161}]
2025-06-14 08:14:20,149 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:20,633 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0T
2025-06-14 08:14:20,633 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 32939.62, 'new_value': 35967.75}, {'field': 'amount', 'old_value': 32939.62, 'new_value': 35967.75}, {'field': 'count', 'old_value': 2097, 'new_value': 2307}, {'field': 'instoreAmount', 'old_value': 17182.87, 'new_value': 18639.33}, {'field': 'instoreCount', 'old_value': 985, 'new_value': 1078}, {'field': 'onlineAmount', 'old_value': 17249.71, 'new_value': 18889.41}, {'field': 'onlineCount', 'old_value': 1112, 'new_value': 1229}]
2025-06-14 08:14:20,633 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:21,102 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1T
2025-06-14 08:14:21,102 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-06, 变更字段: [{'field': 'amount', 'old_value': 82371.4, 'new_value': 86526.7}, {'field': 'count', 'old_value': 865, 'new_value': 930}, {'field': 'instoreAmount', 'old_value': 82413.3, 'new_value': 86620.1}, {'field': 'instoreCount', 'old_value': 865, 'new_value': 930}]
2025-06-14 08:14:21,102 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:21,523 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2T
2025-06-14 08:14:21,523 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 48871.34, 'new_value': 53228.59}, {'field': 'dailyBillAmount', 'old_value': 51203.75, 'new_value': 56035.68}, {'field': 'amount', 'old_value': 48871.34, 'new_value': 53228.59}, {'field': 'count', 'old_value': 1298, 'new_value': 1413}, {'field': 'instoreAmount', 'old_value': 44860.4, 'new_value': 48926.7}, {'field': 'instoreCount', 'old_value': 973, 'new_value': 1066}, {'field': 'onlineAmount', 'old_value': 4057.24, 'new_value': 4348.19}, {'field': 'onlineCount', 'old_value': 325, 'new_value': 347}]
2025-06-14 08:14:21,523 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:22,008 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVS
2025-06-14 08:14:22,008 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 76879.0, 'new_value': 81805.45}, {'field': 'dailyBillAmount', 'old_value': 61641.0, 'new_value': 65692.3}, {'field': 'amount', 'old_value': 76879.0, 'new_value': 81805.45}, {'field': 'count', 'old_value': 1184, 'new_value': 1274}, {'field': 'instoreAmount', 'old_value': 72163.1, 'new_value': 76630.3}, {'field': 'instoreCount', 'old_value': 965, 'new_value': 1035}, {'field': 'onlineAmount', 'old_value': 4865.9, 'new_value': 5325.15}, {'field': 'onlineCount', 'old_value': 219, 'new_value': 239}]
2025-06-14 08:14:22,008 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:22,555 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWS
2025-06-14 08:14:22,555 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14205.9, 'new_value': 15258.7}, {'field': 'amount', 'old_value': 14205.9, 'new_value': 15258.7}, {'field': 'count', 'old_value': 627, 'new_value': 672}, {'field': 'instoreAmount', 'old_value': 12528.9, 'new_value': 13357.9}, {'field': 'instoreCount', 'old_value': 571, 'new_value': 607}, {'field': 'onlineAmount', 'old_value': 1711.4, 'new_value': 1935.2}, {'field': 'onlineCount', 'old_value': 56, 'new_value': 65}]
2025-06-14 08:14:22,555 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:23,039 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXS
2025-06-14 08:14:23,039 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 169620.17, 'new_value': 183189.39}, {'field': 'dailyBillAmount', 'old_value': 169620.17, 'new_value': 183189.39}, {'field': 'amount', 'old_value': 227121.51, 'new_value': 248759.22}, {'field': 'count', 'old_value': 2206, 'new_value': 2398}, {'field': 'instoreAmount', 'old_value': 212637.72, 'new_value': 232708.28}, {'field': 'instoreCount', 'old_value': 1443, 'new_value': 1559}, {'field': 'onlineAmount', 'old_value': 18822.8, 'new_value': 20676.95}, {'field': 'onlineCount', 'old_value': 763, 'new_value': 839}]
2025-06-14 08:14:23,039 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:23,477 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYS
2025-06-14 08:14:23,477 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56089.89, 'new_value': 58936.09}, {'field': 'dailyBillAmount', 'old_value': 56089.89, 'new_value': 58936.09}, {'field': 'amount', 'old_value': 18226.65, 'new_value': 21671.17}, {'field': 'count', 'old_value': 313, 'new_value': 360}, {'field': 'instoreAmount', 'old_value': 12753.57, 'new_value': 15010.01}, {'field': 'instoreCount', 'old_value': 198, 'new_value': 219}, {'field': 'onlineAmount', 'old_value': 5606.13, 'new_value': 6860.81}, {'field': 'onlineCount', 'old_value': 115, 'new_value': 141}]
2025-06-14 08:14:23,523 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:23,523 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:24,023 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGT
2025-06-14 08:14:24,023 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 349040.8, 'new_value': 380017.86}, {'field': 'dailyBillAmount', 'old_value': 349040.8, 'new_value': 380017.86}, {'field': 'amount', 'old_value': 252479.3, 'new_value': 274615.6}, {'field': 'count', 'old_value': 1730, 'new_value': 1876}, {'field': 'instoreAmount', 'old_value': 173868.6, 'new_value': 186646.1}, {'field': 'instoreCount', 'old_value': 1360, 'new_value': 1465}, {'field': 'onlineAmount', 'old_value': 78610.7, 'new_value': 87970.2}, {'field': 'onlineCount', 'old_value': 370, 'new_value': 411}]
2025-06-14 08:14:24,023 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:24,477 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHT
2025-06-14 08:14:24,477 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 486194.7, 'new_value': 526777.65}, {'field': 'amount', 'old_value': 486194.7, 'new_value': 526777.65}, {'field': 'count', 'old_value': 1661, 'new_value': 1801}, {'field': 'instoreAmount', 'old_value': 486051.7, 'new_value': 526634.65}, {'field': 'instoreCount', 'old_value': 1660, 'new_value': 1800}]
2025-06-14 08:14:24,477 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:24,867 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIT
2025-06-14 08:14:24,883 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 247406.63, 'new_value': 270158.68}, {'field': 'dailyBillAmount', 'old_value': 217312.85, 'new_value': 238257.8}, {'field': 'amount', 'old_value': 247406.63, 'new_value': 270158.68}, {'field': 'count', 'old_value': 1688, 'new_value': 1855}, {'field': 'instoreAmount', 'old_value': 222773.57, 'new_value': 243589.36}, {'field': 'instoreCount', 'old_value': 960, 'new_value': 1061}, {'field': 'onlineAmount', 'old_value': 24877.93, 'new_value': 26864.98}, {'field': 'onlineCount', 'old_value': 728, 'new_value': 794}]
2025-06-14 08:14:24,883 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:25,320 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJT
2025-06-14 08:14:25,320 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 180674.27, 'new_value': 198412.07}, {'field': 'dailyBillAmount', 'old_value': 166486.21, 'new_value': 184021.02}, {'field': 'amount', 'old_value': 180674.27, 'new_value': 198412.07}, {'field': 'count', 'old_value': 574, 'new_value': 634}, {'field': 'instoreAmount', 'old_value': 165527.7, 'new_value': 182043.0}, {'field': 'instoreCount', 'old_value': 438, 'new_value': 484}, {'field': 'onlineAmount', 'old_value': 15313.4, 'new_value': 16535.9}, {'field': 'onlineCount', 'old_value': 136, 'new_value': 150}]
2025-06-14 08:14:25,320 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:25,773 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3T
2025-06-14 08:14:25,773 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 108595.0, 'new_value': 119979.98}, {'field': 'dailyBillAmount', 'old_value': 108595.0, 'new_value': 119979.98}, {'field': 'amount', 'old_value': 13117.3, 'new_value': 13915.27}, {'field': 'count', 'old_value': 503, 'new_value': 538}, {'field': 'instoreAmount', 'old_value': 14976.0, 'new_value': 16111.76}, {'field': 'instoreCount', 'old_value': 503, 'new_value': 538}]
2025-06-14 08:14:25,773 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:26,195 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAT
2025-06-14 08:14:26,195 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 47198.42, 'new_value': 50564.98}, {'field': 'dailyBillAmount', 'old_value': 22575.0, 'new_value': 24101.9}, {'field': 'amount', 'old_value': 47198.42, 'new_value': 50564.98}, {'field': 'count', 'old_value': 1235, 'new_value': 1323}, {'field': 'instoreAmount', 'old_value': 24617.1, 'new_value': 26373.8}, {'field': 'instoreCount', 'old_value': 651, 'new_value': 697}, {'field': 'onlineAmount', 'old_value': 23417.54, 'new_value': 25305.4}, {'field': 'onlineCount', 'old_value': 584, 'new_value': 626}]
2025-06-14 08:14:26,195 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:26,617 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9T
2025-06-14 08:14:26,617 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-06, 变更字段: [{'field': 'amount', 'old_value': 89791.72, 'new_value': 102260.96}, {'field': 'count', 'old_value': 1835, 'new_value': 2086}, {'field': 'instoreAmount', 'old_value': 66999.61, 'new_value': 76123.01}, {'field': 'instoreCount', 'old_value': 1037, 'new_value': 1174}, {'field': 'onlineAmount', 'old_value': 23419.47, 'new_value': 26766.77}, {'field': 'onlineCount', 'old_value': 798, 'new_value': 912}]
2025-06-14 08:14:26,617 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:27,039 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8T
2025-06-14 08:14:27,039 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 59451.96, 'new_value': 66596.84}, {'field': 'dailyBillAmount', 'old_value': 59451.96, 'new_value': 66596.84}, {'field': 'amount', 'old_value': 3309.95, 'new_value': 3698.87}, {'field': 'count', 'old_value': 129, 'new_value': 143}, {'field': 'instoreAmount', 'old_value': 3309.95, 'new_value': 3699.28}, {'field': 'instoreCount', 'old_value': 129, 'new_value': 143}]
2025-06-14 08:14:27,039 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:27,445 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMT
2025-06-14 08:14:27,445 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 77340.8, 'new_value': 82993.8}, {'field': 'dailyBillAmount', 'old_value': 77340.8, 'new_value': 82993.8}, {'field': 'amount', 'old_value': 75912.0, 'new_value': 81394.0}, {'field': 'count', 'old_value': 126, 'new_value': 137}, {'field': 'instoreAmount', 'old_value': 75912.0, 'new_value': 81394.0}, {'field': 'instoreCount', 'old_value': 126, 'new_value': 137}]
2025-06-14 08:14:27,445 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:27,883 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7T
2025-06-14 08:14:27,883 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 88045.0, 'new_value': 96544.11}, {'field': 'dailyBillAmount', 'old_value': 84427.8, 'new_value': 92926.91}, {'field': 'amount', 'old_value': 60620.83, 'new_value': 66965.45}, {'field': 'count', 'old_value': 1757, 'new_value': 1880}, {'field': 'instoreAmount', 'old_value': 14454.66, 'new_value': 17180.27}, {'field': 'instoreCount', 'old_value': 303, 'new_value': 340}, {'field': 'onlineAmount', 'old_value': 46578.9, 'new_value': 50406.37}, {'field': 'onlineCount', 'old_value': 1454, 'new_value': 1540}]
2025-06-14 08:14:27,883 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:28,383 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6T
2025-06-14 08:14:28,383 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 175027.74, 'new_value': 193054.12}, {'field': 'amount', 'old_value': 175027.74, 'new_value': 193054.12}, {'field': 'count', 'old_value': 1949, 'new_value': 2170}, {'field': 'instoreAmount', 'old_value': 163287.6, 'new_value': 180150.1}, {'field': 'instoreCount', 'old_value': 1558, 'new_value': 1730}, {'field': 'onlineAmount', 'old_value': 13394.07, 'new_value': 14786.35}, {'field': 'onlineCount', 'old_value': 391, 'new_value': 440}]
2025-06-14 08:14:28,383 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:28,727 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5T
2025-06-14 08:14:28,727 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 9835.09, 'new_value': 10450.14}, {'field': 'count', 'old_value': 485, 'new_value': 520}, {'field': 'onlineAmount', 'old_value': 9893.72, 'new_value': 10508.77}, {'field': 'onlineCount', 'old_value': 485, 'new_value': 520}]
2025-06-14 08:14:28,727 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:29,148 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQT
2025-06-14 08:14:29,148 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 161961.18, 'new_value': 177815.53}, {'field': 'dailyBillAmount', 'old_value': 161961.18, 'new_value': 177815.53}, {'field': 'amount', 'old_value': 164338.79, 'new_value': 179669.43}, {'field': 'count', 'old_value': 4702, 'new_value': 5170}, {'field': 'instoreAmount', 'old_value': 152718.7, 'new_value': 166866.7}, {'field': 'instoreCount', 'old_value': 4037, 'new_value': 4433}, {'field': 'onlineAmount', 'old_value': 14571.01, 'new_value': 16194.93}, {'field': 'onlineCount', 'old_value': 665, 'new_value': 737}]
2025-06-14 08:14:29,148 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:29,602 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPT
2025-06-14 08:14:29,602 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36042.66, 'new_value': 40266.25}, {'field': 'amount', 'old_value': 36042.66, 'new_value': 40266.25}, {'field': 'count', 'old_value': 349, 'new_value': 381}, {'field': 'instoreAmount', 'old_value': 36358.82, 'new_value': 40582.41}, {'field': 'instoreCount', 'old_value': 349, 'new_value': 381}]
2025-06-14 08:14:29,602 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:30,039 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOT
2025-06-14 08:14:30,039 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8859.16, 'new_value': 9486.26}, {'field': 'amount', 'old_value': 8859.16, 'new_value': 9486.26}, {'field': 'count', 'old_value': 200, 'new_value': 214}, {'field': 'instoreAmount', 'old_value': 8868.94, 'new_value': 9525.11}, {'field': 'instoreCount', 'old_value': 200, 'new_value': 214}]
2025-06-14 08:14:30,039 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:30,523 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNT
2025-06-14 08:14:30,523 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 61775.53, 'new_value': 65423.86}, {'field': 'dailyBillAmount', 'old_value': 61775.53, 'new_value': 65423.86}, {'field': 'amount', 'old_value': 63579.08, 'new_value': 69169.08}, {'field': 'count', 'old_value': 73, 'new_value': 81}, {'field': 'instoreAmount', 'old_value': 65972.95, 'new_value': 71234.95}, {'field': 'instoreCount', 'old_value': 64, 'new_value': 70}, {'field': 'onlineAmount', 'old_value': 1253.75, 'new_value': 1581.75}, {'field': 'onlineCount', 'old_value': 9, 'new_value': 11}]
2025-06-14 08:14:30,539 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:30,930 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKT
2025-06-14 08:14:30,930 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 351778.97, 'new_value': 393947.82}, {'field': 'amount', 'old_value': 351778.97, 'new_value': 393947.82}, {'field': 'count', 'old_value': 2246, 'new_value': 2495}, {'field': 'instoreAmount', 'old_value': 317049.38, 'new_value': 355805.69}, {'field': 'instoreCount', 'old_value': 1151, 'new_value': 1281}, {'field': 'onlineAmount', 'old_value': 34765.62, 'new_value': 38249.63}, {'field': 'onlineCount', 'old_value': 1095, 'new_value': 1214}]
2025-06-14 08:14:30,930 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:31,336 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLT
2025-06-14 08:14:31,336 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 356216.57, 'new_value': 390573.04}, {'field': 'dailyBillAmount', 'old_value': 356216.57, 'new_value': 390573.04}, {'field': 'amount', 'old_value': 344505.22, 'new_value': 378422.29}, {'field': 'count', 'old_value': 1760, 'new_value': 1929}, {'field': 'instoreAmount', 'old_value': 315444.69, 'new_value': 346429.19}, {'field': 'instoreCount', 'old_value': 1436, 'new_value': 1574}, {'field': 'onlineAmount', 'old_value': 29593.05, 'new_value': 32557.32}, {'field': 'onlineCount', 'old_value': 324, 'new_value': 355}]
2025-06-14 08:14:31,336 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:31,758 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4T
2025-06-14 08:14:31,758 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 247473.87, 'new_value': 273695.57}, {'field': 'dailyBillAmount', 'old_value': 247473.87, 'new_value': 273695.57}, {'field': 'amount', 'old_value': 24992.1, 'new_value': 27161.0}, {'field': 'count', 'old_value': 129, 'new_value': 141}, {'field': 'instoreAmount', 'old_value': 24992.1, 'new_value': 27161.0}, {'field': 'instoreCount', 'old_value': 129, 'new_value': 141}]
2025-06-14 08:14:31,758 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:32,211 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFT
2025-06-14 08:14:32,211 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 108041.7, 'new_value': 119150.05}, {'field': 'dailyBillAmount', 'old_value': 108041.7, 'new_value': 119150.05}, {'field': 'amount', 'old_value': 68694.92, 'new_value': 75385.18}, {'field': 'count', 'old_value': 1810, 'new_value': 1977}, {'field': 'instoreAmount', 'old_value': 44933.62, 'new_value': 49140.12}, {'field': 'instoreCount', 'old_value': 904, 'new_value': 984}, {'field': 'onlineAmount', 'old_value': 33436.4, 'new_value': 36743.61}, {'field': 'onlineCount', 'old_value': 906, 'new_value': 993}]
2025-06-14 08:14:32,211 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:32,648 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBT
2025-06-14 08:14:32,648 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 76687.74, 'new_value': 85206.43}, {'field': 'amount', 'old_value': 76687.74, 'new_value': 85206.43}, {'field': 'count', 'old_value': 3700, 'new_value': 4132}, {'field': 'instoreAmount', 'old_value': 77839.33, 'new_value': 86526.22}, {'field': 'instoreCount', 'old_value': 3700, 'new_value': 4132}]
2025-06-14 08:14:32,648 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:33,164 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCT
2025-06-14 08:14:33,164 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29284.99, 'new_value': 32090.37}, {'field': 'dailyBillAmount', 'old_value': 29284.99, 'new_value': 32090.37}, {'field': 'amount', 'old_value': 19075.07, 'new_value': 21231.11}, {'field': 'count', 'old_value': 896, 'new_value': 1001}, {'field': 'instoreAmount', 'old_value': 7672.23, 'new_value': 8339.44}, {'field': 'instoreCount', 'old_value': 239, 'new_value': 260}, {'field': 'onlineAmount', 'old_value': 11454.03, 'new_value': 13017.12}, {'field': 'onlineCount', 'old_value': 657, 'new_value': 741}]
2025-06-14 08:14:33,164 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:33,711 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDT
2025-06-14 08:14:33,711 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31436.98, 'new_value': 34448.3}, {'field': 'amount', 'old_value': 31436.98, 'new_value': 34448.3}, {'field': 'count', 'old_value': 1044, 'new_value': 1140}, {'field': 'instoreAmount', 'old_value': 15573.28, 'new_value': 17058.98}, {'field': 'instoreCount', 'old_value': 671, 'new_value': 728}, {'field': 'onlineAmount', 'old_value': 15971.75, 'new_value': 17497.37}, {'field': 'onlineCount', 'old_value': 373, 'new_value': 412}]
2025-06-14 08:14:33,711 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:34,117 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMET
2025-06-14 08:14:34,117 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20489.71, 'new_value': 22899.68}, {'field': 'amount', 'old_value': 20489.71, 'new_value': 22899.68}, {'field': 'count', 'old_value': 527, 'new_value': 580}, {'field': 'instoreAmount', 'old_value': 15662.3, 'new_value': 17668.8}, {'field': 'instoreCount', 'old_value': 430, 'new_value': 471}, {'field': 'onlineAmount', 'old_value': 5175.61, 'new_value': 5597.08}, {'field': 'onlineCount', 'old_value': 97, 'new_value': 109}]
2025-06-14 08:14:34,148 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:34,570 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMYT
2025-06-14 08:14:34,570 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 113650.9, 'new_value': 121963.15}, {'field': 'dailyBillAmount', 'old_value': 113650.9, 'new_value': 121963.15}, {'field': 'amount', 'old_value': 120915.6, 'new_value': 129308.6}, {'field': 'count', 'old_value': 938, 'new_value': 1009}, {'field': 'instoreAmount', 'old_value': 121589.6, 'new_value': 129982.6}, {'field': 'instoreCount', 'old_value': 938, 'new_value': 1009}]
2025-06-14 08:14:34,586 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:35,055 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMXT
2025-06-14 08:14:35,055 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 177025.37, 'new_value': 191735.28}, {'field': 'dailyBillAmount', 'old_value': 177025.37, 'new_value': 191735.28}, {'field': 'amount', 'old_value': 286312.31, 'new_value': 314860.03}, {'field': 'count', 'old_value': 510, 'new_value': 577}, {'field': 'instoreAmount', 'old_value': 282878.11, 'new_value': 311105.23}, {'field': 'instoreCount', 'old_value': 491, 'new_value': 557}, {'field': 'onlineAmount', 'old_value': 3434.2, 'new_value': 3754.8}, {'field': 'onlineCount', 'old_value': 19, 'new_value': 20}]
2025-06-14 08:14:35,055 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:35,523 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMWT
2025-06-14 08:14:35,523 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 392486.4, 'new_value': 434743.36}, {'field': 'dailyBillAmount', 'old_value': 392486.4, 'new_value': 434743.36}, {'field': 'amount', 'old_value': 429090.48, 'new_value': 471870.44}, {'field': 'count', 'old_value': 1901, 'new_value': 2087}, {'field': 'instoreAmount', 'old_value': 429090.48, 'new_value': 471870.44}, {'field': 'instoreCount', 'old_value': 1901, 'new_value': 2087}]
2025-06-14 08:14:35,539 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:35,898 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMVT
2025-06-14 08:14:35,898 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 131258.64, 'new_value': 147736.65}, {'field': 'dailyBillAmount', 'old_value': 131258.64, 'new_value': 147736.65}, {'field': 'amount', 'old_value': 128875.39, 'new_value': 145464.99}, {'field': 'count', 'old_value': 687, 'new_value': 774}, {'field': 'instoreAmount', 'old_value': 122291.7, 'new_value': 138645.5}, {'field': 'instoreCount', 'old_value': 574, 'new_value': 652}, {'field': 'onlineAmount', 'old_value': 7851.67, 'new_value': 8402.88}, {'field': 'onlineCount', 'old_value': 113, 'new_value': 122}]
2025-06-14 08:14:35,898 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:36,320 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMUT
2025-06-14 08:14:36,320 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 863006.82, 'new_value': 938341.64}, {'field': 'dailyBillAmount', 'old_value': 863006.82, 'new_value': 938341.64}, {'field': 'amount', 'old_value': 731349.0, 'new_value': 783856.0}, {'field': 'count', 'old_value': 1738, 'new_value': 1863}, {'field': 'instoreAmount', 'old_value': 765214.0, 'new_value': 821577.0}, {'field': 'instoreCount', 'old_value': 1738, 'new_value': 1863}]
2025-06-14 08:14:36,320 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:36,742 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMST
2025-06-14 08:14:36,742 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 523715.1, 'new_value': 572315.72}, {'field': 'amount', 'old_value': 523715.1, 'new_value': 572315.72}, {'field': 'count', 'old_value': 1685, 'new_value': 1829}, {'field': 'instoreAmount', 'old_value': 525660.1, 'new_value': 574260.72}, {'field': 'instoreCount', 'old_value': 1685, 'new_value': 1829}]
2025-06-14 08:14:36,742 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:37,227 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTT
2025-06-14 08:14:37,227 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-06, 变更字段: [{'field': 'amount', 'old_value': 312457.19, 'new_value': 337800.43}, {'field': 'count', 'old_value': 1215, 'new_value': 1325}, {'field': 'instoreAmount', 'old_value': 303952.97, 'new_value': 328297.91}, {'field': 'instoreCount', 'old_value': 703, 'new_value': 771}, {'field': 'onlineAmount', 'old_value': 15422.6, 'new_value': 16599.4}, {'field': 'onlineCount', 'old_value': 512, 'new_value': 554}]
2025-06-14 08:14:37,227 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:37,680 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRT
2025-06-14 08:14:37,680 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 326814.67, 'new_value': 348934.67}, {'field': 'dailyBillAmount', 'old_value': 326814.67, 'new_value': 348934.67}, {'field': 'amount', 'old_value': 305303.4, 'new_value': 327656.7}, {'field': 'count', 'old_value': 908, 'new_value': 973}, {'field': 'instoreAmount', 'old_value': 313075.35, 'new_value': 335195.35}, {'field': 'instoreCount', 'old_value': 734, 'new_value': 786}, {'field': 'onlineAmount', 'old_value': 4826.27, 'new_value': 5069.43}, {'field': 'onlineCount', 'old_value': 174, 'new_value': 187}]
2025-06-14 08:14:37,727 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:38,180 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMPW
2025-06-14 08:14:38,180 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 86533.95, 'new_value': 95669.41}, {'field': 'dailyBillAmount', 'old_value': 86533.95, 'new_value': 95669.41}, {'field': 'amount', 'old_value': 90770.75, 'new_value': 100081.75}, {'field': 'count', 'old_value': 605, 'new_value': 651}, {'field': 'instoreAmount', 'old_value': 86085.0, 'new_value': 95150.0}, {'field': 'instoreCount', 'old_value': 523, 'new_value': 566}, {'field': 'onlineAmount', 'old_value': 5243.75, 'new_value': 5489.75}, {'field': 'onlineCount', 'old_value': 82, 'new_value': 85}]
2025-06-14 08:14:38,195 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:38,633 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMFU
2025-06-14 08:14:38,633 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 59314.8, 'new_value': 64204.8}, {'field': 'dailyBillAmount', 'old_value': 59314.8, 'new_value': 64204.8}, {'field': 'amount', 'old_value': 71225.0, 'new_value': 77250.0}, {'field': 'count', 'old_value': 291, 'new_value': 317}, {'field': 'instoreAmount', 'old_value': 71225.0, 'new_value': 77250.0}, {'field': 'instoreCount', 'old_value': 291, 'new_value': 317}]
2025-06-14 08:14:38,633 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:39,086 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMGU
2025-06-14 08:14:39,086 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-06, 变更字段: [{'field': 'amount', 'old_value': 24483.0, 'new_value': 27434.0}, {'field': 'count', 'old_value': 127, 'new_value': 141}, {'field': 'instoreAmount', 'old_value': 24483.0, 'new_value': 27434.0}, {'field': 'instoreCount', 'old_value': 127, 'new_value': 141}]
2025-06-14 08:14:39,086 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:39,523 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMHU
2025-06-14 08:14:39,523 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 47573.0, 'new_value': 52766.0}, {'field': 'amount', 'old_value': 47573.0, 'new_value': 52766.0}, {'field': 'count', 'old_value': 541, 'new_value': 595}, {'field': 'instoreAmount', 'old_value': 47573.0, 'new_value': 52766.0}, {'field': 'instoreCount', 'old_value': 541, 'new_value': 595}]
2025-06-14 08:14:39,523 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:40,023 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMIU
2025-06-14 08:14:40,023 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14936.28, 'new_value': 16982.7}, {'field': 'dailyBillAmount', 'old_value': 14936.28, 'new_value': 16982.7}, {'field': 'amount', 'old_value': 1840.46, 'new_value': 1782.16}]
2025-06-14 08:14:40,023 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:40,445 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMMW
2025-06-14 08:14:40,445 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-06, 变更字段: [{'field': 'amount', 'old_value': 18847.0, 'new_value': 19918.0}, {'field': 'count', 'old_value': 92, 'new_value': 99}, {'field': 'instoreAmount', 'old_value': 18847.0, 'new_value': 19918.0}, {'field': 'instoreCount', 'old_value': 92, 'new_value': 99}]
2025-06-14 08:14:40,445 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:40,883 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMNW
2025-06-14 08:14:40,883 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34428.49, 'new_value': 37044.64}, {'field': 'dailyBillAmount', 'old_value': 34428.49, 'new_value': 37044.64}, {'field': 'amount', 'old_value': 27181.86, 'new_value': 29549.71}, {'field': 'count', 'old_value': 859, 'new_value': 945}, {'field': 'instoreAmount', 'old_value': 26362.96, 'new_value': 28377.41}, {'field': 'instoreCount', 'old_value': 819, 'new_value': 887}, {'field': 'onlineAmount', 'old_value': 818.9, 'new_value': 1172.3}, {'field': 'onlineCount', 'old_value': 40, 'new_value': 58}]
2025-06-14 08:14:40,883 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:41,320 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMOW
2025-06-14 08:14:41,320 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 43509.54, 'new_value': 46419.95}, {'field': 'dailyBillAmount', 'old_value': 43509.54, 'new_value': 46419.95}, {'field': 'amount', 'old_value': 42882.2, 'new_value': 45715.68}, {'field': 'count', 'old_value': 196, 'new_value': 209}, {'field': 'instoreAmount', 'old_value': 42486.5, 'new_value': 45319.98}, {'field': 'instoreCount', 'old_value': 182, 'new_value': 195}]
2025-06-14 08:14:41,320 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:41,742 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMZT
2025-06-14 08:14:41,742 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 112709.33, 'new_value': 120832.39}, {'field': 'dailyBillAmount', 'old_value': 112709.33, 'new_value': 120832.39}, {'field': 'amount', 'old_value': 90065.7, 'new_value': 96294.9}, {'field': 'count', 'old_value': 580, 'new_value': 636}, {'field': 'instoreAmount', 'old_value': 91292.0, 'new_value': 97539.0}, {'field': 'instoreCount', 'old_value': 560, 'new_value': 610}, {'field': 'onlineAmount', 'old_value': 861.7, 'new_value': 974.9}, {'field': 'onlineCount', 'old_value': 20, 'new_value': 26}]
2025-06-14 08:14:41,742 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:42,180 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM0U
2025-06-14 08:14:42,180 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16489.7, 'new_value': 17259.1}, {'field': 'dailyBillAmount', 'old_value': 16489.7, 'new_value': 17259.1}]
2025-06-14 08:14:42,180 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:42,586 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM1U
2025-06-14 08:14:42,586 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-06, 变更字段: [{'field': 'amount', 'old_value': 39784.11, 'new_value': 42746.98}, {'field': 'count', 'old_value': 2208, 'new_value': 2373}, {'field': 'instoreAmount', 'old_value': 6172.14, 'new_value': 6447.87}, {'field': 'instoreCount', 'old_value': 519, 'new_value': 556}, {'field': 'onlineAmount', 'old_value': 35156.8, 'new_value': 38008.81}, {'field': 'onlineCount', 'old_value': 1689, 'new_value': 1817}]
2025-06-14 08:14:42,586 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:43,086 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM2U
2025-06-14 08:14:43,086 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 100287.78, 'new_value': 107401.52}, {'field': 'amount', 'old_value': 100285.73, 'new_value': 107399.47}, {'field': 'count', 'old_value': 1889, 'new_value': 2080}, {'field': 'instoreAmount', 'old_value': 83170.41, 'new_value': 89507.04}, {'field': 'instoreCount', 'old_value': 1586, 'new_value': 1756}, {'field': 'onlineAmount', 'old_value': 17117.37, 'new_value': 17894.48}, {'field': 'onlineCount', 'old_value': 303, 'new_value': 324}]
2025-06-14 08:14:43,086 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:43,539 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM3U
2025-06-14 08:14:43,539 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16387.0, 'new_value': 17250.8}, {'field': 'amount', 'old_value': 16387.0, 'new_value': 17250.8}, {'field': 'count', 'old_value': 109, 'new_value': 117}, {'field': 'instoreAmount', 'old_value': 16387.0, 'new_value': 17250.8}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 117}]
2025-06-14 08:14:43,539 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:44,039 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM4U
2025-06-14 08:14:44,039 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-06, 变更字段: [{'field': 'amount', 'old_value': 10868.3, 'new_value': 11391.5}, {'field': 'count', 'old_value': 111, 'new_value': 115}, {'field': 'instoreAmount', 'old_value': 11204.3, 'new_value': 11727.5}, {'field': 'instoreCount', 'old_value': 111, 'new_value': 115}]
2025-06-14 08:14:44,039 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:44,461 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMEU
2025-06-14 08:14:44,461 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 64086.82, 'new_value': 69449.05}, {'field': 'dailyBillAmount', 'old_value': 64086.82, 'new_value': 69449.05}, {'field': 'amount', 'old_value': 47998.72, 'new_value': 52300.72}, {'field': 'count', 'old_value': 427, 'new_value': 466}, {'field': 'instoreAmount', 'old_value': 47998.72, 'new_value': 52300.72}, {'field': 'instoreCount', 'old_value': 427, 'new_value': 466}]
2025-06-14 08:14:44,461 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:44,992 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM6U
2025-06-14 08:14:44,992 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 106610.0, 'new_value': 114196.3}, {'field': 'dailyBillAmount', 'old_value': 106610.0, 'new_value': 114196.3}, {'field': 'amount', 'old_value': 79319.4, 'new_value': 85804.8}, {'field': 'count', 'old_value': 2696, 'new_value': 2893}, {'field': 'instoreAmount', 'old_value': 75633.75, 'new_value': 82141.85}, {'field': 'instoreCount', 'old_value': 2572, 'new_value': 2765}, {'field': 'onlineAmount', 'old_value': 4647.85, 'new_value': 4754.55}, {'field': 'onlineCount', 'old_value': 124, 'new_value': 128}]
2025-06-14 08:14:44,992 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:45,508 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM7U
2025-06-14 08:14:45,508 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25437.8, 'new_value': 27925.4}, {'field': 'dailyBillAmount', 'old_value': 25437.8, 'new_value': 27925.4}, {'field': 'amount', 'old_value': 23803.2, 'new_value': 26290.8}, {'field': 'count', 'old_value': 138, 'new_value': 152}, {'field': 'instoreAmount', 'old_value': 24330.7, 'new_value': 26818.3}, {'field': 'instoreCount', 'old_value': 138, 'new_value': 152}]
2025-06-14 08:14:45,508 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:45,883 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM8U
2025-06-14 08:14:45,883 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31301.15, 'new_value': 34149.27}, {'field': 'dailyBillAmount', 'old_value': 31301.15, 'new_value': 34149.27}]
2025-06-14 08:14:45,883 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:46,367 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM9U
2025-06-14 08:14:46,367 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24079.72, 'new_value': 25913.94}, {'field': 'amount', 'old_value': 24077.08, 'new_value': 25910.36}, {'field': 'count', 'old_value': 1491, 'new_value': 1604}, {'field': 'instoreAmount', 'old_value': 24383.57, 'new_value': 26231.12}, {'field': 'instoreCount', 'old_value': 1491, 'new_value': 1604}]
2025-06-14 08:14:46,367 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:46,820 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMAU
2025-06-14 08:14:46,820 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36756.01, 'new_value': 39707.39}, {'field': 'dailyBillAmount', 'old_value': 36756.01, 'new_value': 39707.39}, {'field': 'amount', 'old_value': 37540.59, 'new_value': 40631.67}, {'field': 'count', 'old_value': 1895, 'new_value': 2067}, {'field': 'instoreAmount', 'old_value': 34030.6, 'new_value': 36659.2}, {'field': 'instoreCount', 'old_value': 1709, 'new_value': 1857}, {'field': 'onlineAmount', 'old_value': 3708.78, 'new_value': 4171.26}, {'field': 'onlineCount', 'old_value': 186, 'new_value': 210}]
2025-06-14 08:14:46,820 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:47,320 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMBU
2025-06-14 08:14:47,320 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 27275.13, 'new_value': 29417.33}, {'field': 'amount', 'old_value': 27275.13, 'new_value': 29417.33}, {'field': 'count', 'old_value': 1281, 'new_value': 1392}, {'field': 'instoreAmount', 'old_value': 15836.66, 'new_value': 17050.21}, {'field': 'instoreCount', 'old_value': 786, 'new_value': 860}, {'field': 'onlineAmount', 'old_value': 11476.29, 'new_value': 12404.94}, {'field': 'onlineCount', 'old_value': 495, 'new_value': 532}]
2025-06-14 08:14:47,320 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:47,820 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMCU
2025-06-14 08:14:47,820 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20737.36, 'new_value': 22408.79}, {'field': 'dailyBillAmount', 'old_value': 20737.36, 'new_value': 22408.79}, {'field': 'amount', 'old_value': 14627.51, 'new_value': 15885.25}, {'field': 'count', 'old_value': 562, 'new_value': 620}, {'field': 'instoreAmount', 'old_value': 14781.39, 'new_value': 16039.13}, {'field': 'instoreCount', 'old_value': 562, 'new_value': 620}]
2025-06-14 08:14:47,820 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:48,305 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMDU
2025-06-14 08:14:48,305 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31631.28, 'new_value': 35003.09}, {'field': 'amount', 'old_value': 31629.49, 'new_value': 35001.3}, {'field': 'count', 'old_value': 1816, 'new_value': 1987}, {'field': 'instoreAmount', 'old_value': 6926.6, 'new_value': 7541.95}, {'field': 'instoreCount', 'old_value': 320, 'new_value': 339}, {'field': 'onlineAmount', 'old_value': 25611.21, 'new_value': 28419.77}, {'field': 'onlineCount', 'old_value': 1496, 'new_value': 1648}]
2025-06-14 08:14:48,305 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-14 08:14:48,758 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM5U
2025-06-14 08:14:48,758 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26581.0, 'new_value': 28024.0}, {'field': 'dailyBillAmount', 'old_value': 26581.0, 'new_value': 28024.0}]
2025-06-14 08:14:48,758 - WARNING - 批量插入月度数据失败，将在 2 秒后重试 (尝试 1/3): Object of type Decimal is not JSON serializable
2025-06-14 08:14:50,773 - WARNING - 批量插入月度数据失败，将在 4 秒后重试 (尝试 2/3): Object of type Decimal is not JSON serializable
2025-06-14 08:14:54,789 - ERROR - 批量插入月度数据失败达到最大重试次数，跳过当前批次: Object of type Decimal is not JSON serializable
2025-06-14 08:14:57,804 - INFO - 批量插入月度数据完成: 总计 10 条，成功 0 条，失败 10 条
2025-06-14 08:14:57,804 - INFO - 批量插入月销售数据完成，共 10 条记录
2025-06-14 08:14:57,804 - INFO - 月销售数据同步完成！更新: 196 条，插入: 10 条，错误: 0 条，跳过: 1207 条
2025-06-14 08:14:57,804 - INFO - 综合数据同步流程完成！
2025-06-14 08:14:57,867 - INFO - 综合数据同步完成
2025-06-14 08:14:57,883 - INFO - MySQL数据库连接已关闭
2025-06-14 08:14:57,883 - INFO - ==================================================
2025-06-14 08:14:57,883 - INFO - 程序退出
2025-06-14 08:14:57,883 - INFO - ==================================================
