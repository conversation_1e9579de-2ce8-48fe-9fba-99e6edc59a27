2025-05-17 00:30:34,574 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 00:30:34,574 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 00:30:34,575 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 00:30:34,635 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 0 条记录
2025-05-17 00:30:34,635 - ERROR - 未获取到MySQL数据
2025-05-17 00:31:34,657 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 00:31:34,657 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 00:31:34,657 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 00:31:34,717 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 54 条记录
2025-05-17 00:31:34,717 - INFO - 获取到 1 个日期需要处理: ['2025-05-16']
2025-05-17 00:31:34,718 - INFO - 开始处理日期: 2025-05-16
2025-05-17 00:31:34,722 - INFO - Request Parameters - Page 1:
2025-05-17 00:31:34,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 00:31:34,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 00:31:42,848 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 387B617A-80D4-7130-A9C3-9132E32473D0 Response: {'code': 'ServiceUnavailable', 'requestid': '387B617A-80D4-7130-A9C3-9132E32473D0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 387B617A-80D4-7130-A9C3-9132E32473D0)
2025-05-17 00:31:42,848 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 00:31:42,848 - INFO - 同步完成
2025-05-17 01:30:34,549 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 01:30:34,550 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 01:30:34,550 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 01:30:34,610 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 0 条记录
2025-05-17 01:30:34,610 - ERROR - 未获取到MySQL数据
2025-05-17 01:31:34,632 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 01:31:34,632 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 01:31:34,632 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 01:31:34,691 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 54 条记录
2025-05-17 01:31:34,692 - INFO - 获取到 1 个日期需要处理: ['2025-05-16']
2025-05-17 01:31:34,692 - INFO - 开始处理日期: 2025-05-16
2025-05-17 01:31:34,697 - INFO - Request Parameters - Page 1:
2025-05-17 01:31:34,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 01:31:34,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 01:31:42,819 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 77D63F0E-54F8-76B8-A0EB-3C3D1A5EDE9D Response: {'code': 'ServiceUnavailable', 'requestid': '77D63F0E-54F8-76B8-A0EB-3C3D1A5EDE9D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 77D63F0E-54F8-76B8-A0EB-3C3D1A5EDE9D)
2025-05-17 01:31:42,819 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 01:31:42,819 - INFO - 同步完成
2025-05-17 02:30:35,516 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 02:30:35,516 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 02:30:35,516 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 02:30:35,577 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 0 条记录
2025-05-17 02:30:35,577 - ERROR - 未获取到MySQL数据
2025-05-17 02:31:35,599 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 02:31:35,599 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 02:31:35,599 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 02:31:35,663 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 54 条记录
2025-05-17 02:31:35,663 - INFO - 获取到 1 个日期需要处理: ['2025-05-16']
2025-05-17 02:31:35,664 - INFO - 开始处理日期: 2025-05-16
2025-05-17 02:31:35,668 - INFO - Request Parameters - Page 1:
2025-05-17 02:31:35,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 02:31:35,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 02:31:43,790 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1B632352-71CC-7FE8-8514-6DF5A781C20C Response: {'code': 'ServiceUnavailable', 'requestid': '1B632352-71CC-7FE8-8514-6DF5A781C20C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1B632352-71CC-7FE8-8514-6DF5A781C20C)
2025-05-17 02:31:43,790 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 02:31:43,790 - INFO - 同步完成
2025-05-17 03:30:34,696 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 03:30:34,696 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 03:30:34,696 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 03:30:34,758 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 0 条记录
2025-05-17 03:30:34,758 - ERROR - 未获取到MySQL数据
2025-05-17 03:31:34,780 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 03:31:34,780 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 03:31:34,780 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 03:31:34,838 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 54 条记录
2025-05-17 03:31:34,838 - INFO - 获取到 1 个日期需要处理: ['2025-05-16']
2025-05-17 03:31:34,839 - INFO - 开始处理日期: 2025-05-16
2025-05-17 03:31:34,844 - INFO - Request Parameters - Page 1:
2025-05-17 03:31:34,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 03:31:34,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 03:31:42,969 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 053A85AF-FEAD-71B4-AF4B-71E7A1013F96 Response: {'code': 'ServiceUnavailable', 'requestid': '053A85AF-FEAD-71B4-AF4B-71E7A1013F96', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 053A85AF-FEAD-71B4-AF4B-71E7A1013F96)
2025-05-17 03:31:42,969 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 03:31:42,970 - INFO - 同步完成
2025-05-17 04:30:35,166 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 04:30:35,166 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 04:30:35,166 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 04:30:35,226 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 0 条记录
2025-05-17 04:30:35,226 - ERROR - 未获取到MySQL数据
2025-05-17 04:31:35,248 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 04:31:35,248 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 04:31:35,248 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 04:31:35,307 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 54 条记录
2025-05-17 04:31:35,307 - INFO - 获取到 1 个日期需要处理: ['2025-05-16']
2025-05-17 04:31:35,308 - INFO - 开始处理日期: 2025-05-16
2025-05-17 04:31:35,312 - INFO - Request Parameters - Page 1:
2025-05-17 04:31:35,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 04:31:35,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 04:31:43,447 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2EAE5A9E-3418-7AE7-ADD3-4F507CEF8B58 Response: {'code': 'ServiceUnavailable', 'requestid': '2EAE5A9E-3418-7AE7-ADD3-4F507CEF8B58', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2EAE5A9E-3418-7AE7-ADD3-4F507CEF8B58)
2025-05-17 04:31:43,448 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 04:31:43,448 - INFO - 同步完成
2025-05-17 05:30:35,188 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 05:30:35,188 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 05:30:35,188 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 05:30:35,247 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 0 条记录
2025-05-17 05:30:35,248 - ERROR - 未获取到MySQL数据
2025-05-17 05:31:35,270 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 05:31:35,270 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 05:31:35,270 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 05:31:35,329 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 54 条记录
2025-05-17 05:31:35,330 - INFO - 获取到 1 个日期需要处理: ['2025-05-16']
2025-05-17 05:31:35,330 - INFO - 开始处理日期: 2025-05-16
2025-05-17 05:31:35,335 - INFO - Request Parameters - Page 1:
2025-05-17 05:31:35,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 05:31:35,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 05:31:43,471 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 23D1229D-E36F-7F50-B6A8-186F4C760644 Response: {'code': 'ServiceUnavailable', 'requestid': '23D1229D-E36F-7F50-B6A8-186F4C760644', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 23D1229D-E36F-7F50-B6A8-186F4C760644)
2025-05-17 05:31:43,471 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 05:31:43,471 - INFO - 同步完成
2025-05-17 06:30:34,368 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 06:30:34,368 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 06:30:34,368 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 06:30:34,432 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 0 条记录
2025-05-17 06:30:34,432 - ERROR - 未获取到MySQL数据
2025-05-17 06:31:34,454 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 06:31:34,454 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 06:31:34,454 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 06:31:34,513 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 54 条记录
2025-05-17 06:31:34,513 - INFO - 获取到 1 个日期需要处理: ['2025-05-16']
2025-05-17 06:31:34,514 - INFO - 开始处理日期: 2025-05-16
2025-05-17 06:31:34,518 - INFO - Request Parameters - Page 1:
2025-05-17 06:31:34,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 06:31:34,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 06:31:42,640 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2CF10645-F003-7F28-AB66-0F4897893345 Response: {'code': 'ServiceUnavailable', 'requestid': '2CF10645-F003-7F28-AB66-0F4897893345', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2CF10645-F003-7F28-AB66-0F4897893345)
2025-05-17 06:31:42,641 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 06:31:42,641 - INFO - 同步完成
2025-05-17 07:30:33,888 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 07:30:33,888 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 07:30:33,889 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 07:30:33,948 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 0 条记录
2025-05-17 07:30:33,948 - ERROR - 未获取到MySQL数据
2025-05-17 07:31:33,949 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 07:31:33,949 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 07:31:33,949 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 07:31:34,009 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 54 条记录
2025-05-17 07:31:34,009 - INFO - 获取到 1 个日期需要处理: ['2025-05-16']
2025-05-17 07:31:34,009 - INFO - 开始处理日期: 2025-05-16
2025-05-17 07:31:34,013 - INFO - Request Parameters - Page 1:
2025-05-17 07:31:34,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 07:31:34,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 07:31:42,137 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-2580-7809-A89F-9B25298E3CA3 Response: {'code': 'ServiceUnavailable', 'requestid': '********-2580-7809-A89F-9B25298E3CA3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-2580-7809-A89F-9B25298E3CA3)
2025-05-17 07:31:42,137 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 07:31:42,137 - INFO - 同步完成
2025-05-17 08:30:34,248 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 08:30:34,249 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 08:30:34,249 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 08:30:34,308 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 0 条记录
2025-05-17 08:30:34,308 - ERROR - 未获取到MySQL数据
2025-05-17 08:31:34,309 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 08:31:34,309 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 08:31:34,309 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 08:31:34,369 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 54 条记录
2025-05-17 08:31:34,369 - INFO - 获取到 1 个日期需要处理: ['2025-05-16']
2025-05-17 08:31:34,370 - INFO - 开始处理日期: 2025-05-16
2025-05-17 08:31:34,373 - INFO - Request Parameters - Page 1:
2025-05-17 08:31:34,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:31:34,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:31:42,476 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9994FD03-B942-70EB-95CF-7B52F3C73A90 Response: {'code': 'ServiceUnavailable', 'requestid': '9994FD03-B942-70EB-95CF-7B52F3C73A90', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9994FD03-B942-70EB-95CF-7B52F3C73A90)
2025-05-17 08:31:42,476 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 08:31:42,477 - INFO - 同步完成
2025-05-17 09:30:34,332 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 09:30:34,332 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 09:30:34,332 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 09:30:34,394 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 51 条记录
2025-05-17 09:30:34,394 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 09:30:34,395 - INFO - 开始处理日期: 2025-05-16
2025-05-17 09:30:34,399 - INFO - Request Parameters - Page 1:
2025-05-17 09:30:34,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:30:34,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:30:42,522 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 73A15781-1065-7B37-AB38-2CF5488444D8 Response: {'code': 'ServiceUnavailable', 'requestid': '73A15781-1065-7B37-AB38-2CF5488444D8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 73A15781-1065-7B37-AB38-2CF5488444D8)
2025-05-17 09:30:42,523 - INFO - 开始处理日期: 2025-05-17
2025-05-17 09:30:42,523 - INFO - Request Parameters - Page 1:
2025-05-17 09:30:42,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:30:42,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:30:44,826 - INFO - Response - Page 1:
2025-05-17 09:30:44,826 - INFO - 查询完成，共获取到 0 条记录
2025-05-17 09:30:44,826 - INFO - 获取到 0 条表单数据
2025-05-17 09:30:44,826 - INFO - 当前日期 2025-05-17 有 1 条MySQL数据需要处理
2025-05-17 09:30:44,826 - INFO - 开始批量插入 1 条新记录
2025-05-17 09:30:44,974 - INFO - 批量插入响应状态码: 200
2025-05-17 09:30:44,974 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 17 May 2025 01:30:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F00988D8-DABC-7C0F-91A6-EB3B83BD169E', 'x-acs-trace-id': '4acdc6c533c4d3e5b8c4fdcb055bbbd6', 'etag': '6Zy/eJ9sZbw1YVjX6BVd+Xw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-17 09:30:44,974 - INFO - 批量插入响应体: {'result': ['FINST-3ME66E81HLHVOMZNBCKCPBON9MCI2ZVAWJRAM51']}
2025-05-17 09:30:44,974 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-17 09:30:44,974 - INFO - 成功插入的数据ID: ['FINST-3ME66E81HLHVOMZNBCKCPBON9MCI2ZVAWJRAM51']
2025-05-17 09:30:49,975 - INFO - 批量插入完成，共 1 条记录
2025-05-17 09:30:49,975 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-17 09:30:49,975 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-05-17 09:31:49,976 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 09:31:49,976 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 09:31:49,976 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 09:31:50,045 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 295 条记录
2025-05-17 09:31:50,045 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 09:31:50,048 - INFO - 开始处理日期: 2025-05-16
2025-05-17 09:31:50,048 - INFO - Request Parameters - Page 1:
2025-05-17 09:31:50,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:31:50,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:31:50,901 - INFO - Response - Page 1:
2025-05-17 09:31:50,901 - INFO - 第 1 页获取到 54 条记录
2025-05-17 09:31:51,101 - INFO - 查询完成，共获取到 54 条记录
2025-05-17 09:31:51,102 - INFO - 获取到 54 条表单数据
2025-05-17 09:31:51,103 - INFO - 当前日期 2025-05-16 有 294 条MySQL数据需要处理
2025-05-17 09:31:51,106 - INFO - 开始批量插入 240 条新记录
2025-05-17 09:31:51,405 - INFO - 批量插入响应状态码: 200
2025-05-17 09:31:51,405 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 17 May 2025 01:31:46 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AA687DE2-4769-79A6-B2A5-1D516149FEB6', 'x-acs-trace-id': '5c6013eed56e80b919b97999e1ed7eb6', 'etag': '4la6//VSf9M6y3SMiWmalNA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-17 09:31:51,405 - INFO - 批量插入响应体: {'result': ['FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMS5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMT5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMU5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMV5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMW5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMX5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMY5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMZ5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM06', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM16', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM26', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM36', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM46', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM56', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM66', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM76', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM86', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM96', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMA6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMB6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMC6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMD6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAME6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMF6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMG6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMH6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMI6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMJ6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMK6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAML6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMM6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMN6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMO6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMP6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMQ6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMR6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMS6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMT6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMU6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMV6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMW6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMX6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMY6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMZ6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM07', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM17', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM27', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM37', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM47', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM57', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM67', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM77', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM87', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM97', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMA7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMB7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMC7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMD7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAME7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMF7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMG7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMH7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMI7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMJ7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMK7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAML7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMM7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMN7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMO7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMP7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMQ7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMR7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMS7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMT7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMU7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMV7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMW7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMX7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMY7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMZ7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM08', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM18', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM28', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM38', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM48', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM58', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM68', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM78', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM88', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM98', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMA8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMB8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMC8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMD8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAME8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMF8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMG8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMH8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMI8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMJ8']}
2025-05-17 09:31:51,406 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-17 09:31:51,406 - INFO - 成功插入的数据ID: ['FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMS5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMT5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMU5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMV5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMW5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMX5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMY5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMZ5', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM06', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM16', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM26', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM36', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM46', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM56', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM66', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM76', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM86', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM96', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMA6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMB6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMC6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMD6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAME6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMF6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMG6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMH6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMI6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMJ6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMK6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAML6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMM6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMN6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMO6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMP6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMQ6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMR6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMS6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMT6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMU6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMV6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMW6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMX6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMY6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAMZ6', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM07', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX3Z4QXJRAM17', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM27', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM37', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM47', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM57', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM67', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM77', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM87', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM97', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMA7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMB7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMC7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMD7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAME7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMF7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMG7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMH7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMI7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMJ7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMK7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAML7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMM7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMN7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMO7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMP7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMQ7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMR7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMS7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMT7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMU7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMV7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMW7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMX7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMY7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMZ7', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM08', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM18', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM28', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM38', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM48', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM58', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM68', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM78', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM88', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAM98', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMA8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMB8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMC8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMD8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAME8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMF8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMG8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMH8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMI8', 'FINST-PPA66671ZEHVRS877SFWVD8ICTHX305QXJRAMJ8']
2025-05-17 09:31:56,677 - INFO - 批量插入响应状态码: 200
2025-05-17 09:31:56,678 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 17 May 2025 01:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '387176BA-5787-79A1-81B6-A702719D5F04', 'x-acs-trace-id': 'ba64ffd09a6fab72ea608eff9746b1ef', 'etag': '4+jY9rxNWn3q+JY5R92dI6A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-17 09:31:56,678 - INFO - 批量插入响应体: {'result': ['FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAME5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMF5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMG5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMH5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMI5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMJ5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMK5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAML5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMM5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMN5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMO5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMP5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMQ5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMR5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMS5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMT5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMU5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMV5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMW5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMX5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMY5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMZ5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM06', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM16', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM26', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM36', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM46', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM56', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM66', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM76', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM86', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM96', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMA6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMB6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMC6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMD6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAME6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMF6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMG6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMH6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMI6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMJ6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMK6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAML6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMM6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMN6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMO6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMP6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMQ6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMR6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMS6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMT6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMU6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMV6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMW6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMX6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMY6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMZ6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM07', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM17', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM27', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM37', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM47', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM57', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM67', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM77', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM87', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM97', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMA7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMB7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMC7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMD7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAME7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMF7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMG7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMH7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMI7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMJ7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMK7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAML7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMM7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMN7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMO7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMP7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMQ7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMR7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMS7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMT7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMU7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMV7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMW7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMX7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMY7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMZ7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAM08', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAM18', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAM28', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAM38', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAM48', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAM58']}
2025-05-17 09:31:56,678 - INFO - 批量插入表单数据成功，批次 2，共 100 条记录
2025-05-17 09:31:56,678 - INFO - 成功插入的数据ID: ['FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAME5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMF5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMG5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMH5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMI5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMJ5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMK5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAML5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMM5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMN5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMO5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMP5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMQ5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMR5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMS5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMT5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMU5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMV5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMW5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMX5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMY5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMZ5', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM06', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM16', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM26', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM36', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM46', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM56', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM66', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM76', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM86', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM96', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMA6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMB6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMC6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMD6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAME6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMF6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMG6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMH6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMI6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMJ6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMK6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAML6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMM6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMN6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMO6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMP6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMQ6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMR6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMS6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMT6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMU6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMV6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMW6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMX6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMY6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMZ6', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM07', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM17', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM27', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM37', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM47', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM57', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM67', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM77', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM87', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAM97', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMA7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMB7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMC7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMD7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAME7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMF7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMG7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMH7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMI7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMJ7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMK7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAML7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMM7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMN7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572M7UXJRAMO7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMP7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMQ7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMR7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMS7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMT7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMU7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMV7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMW7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMX7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMY7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAMZ7', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAM08', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAM18', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAM28', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAM38', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAM48', 'FINST-2FD66I711ZGVRIXY5N3EP4WJ60572N7UXJRAM58']
2025-05-17 09:32:01,875 - INFO - 批量插入响应状态码: 200
2025-05-17 09:32:01,875 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 17 May 2025 01:31:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1932', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '88581E29-791F-7E4A-AEB1-FEED3DC091C7', 'x-acs-trace-id': '010256357f3241913f4c52d313f54166', 'etag': '136Ebgke0Nnok0vy8N9BMoQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-17 09:32:01,875 - INFO - 批量插入响应体: {'result': ['FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAM8B', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAM9B', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMAB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMBB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMCB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMDB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMEB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMFB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMGB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMHB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMIB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMJB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMKB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMLB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMMB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMNB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMOB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMPB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMQB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMRB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMSB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMTB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMUB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMVB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMWB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMXB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMYB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMZB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM0C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM1C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM2C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM3C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM4C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM5C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM6C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM7C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM8C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM9C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMAC', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMBC']}
2025-05-17 09:32:01,875 - INFO - 批量插入表单数据成功，批次 3，共 40 条记录
2025-05-17 09:32:01,875 - INFO - 成功插入的数据ID: ['FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAM8B', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAM9B', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMAB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMBB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMCB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMDB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMEB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMFB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMGB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMHB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMIB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMJB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMKB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMLB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMMB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMNB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMOB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMPB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMQB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMRB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX338YXJRAMSB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMTB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMUB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMVB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMWB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMXB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMYB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMZB', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM0C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM1C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM2C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM3C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM4C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM5C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM6C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM7C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM8C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAM9C', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMAC', 'FINST-N3G66S81ZEHVXEVBCX0DY8A0VIBX348YXJRAMBC']
2025-05-17 09:32:06,876 - INFO - 批量插入完成，共 240 条记录
2025-05-17 09:32:06,876 - INFO - 日期 2025-05-16 处理完成 - 更新: 0 条，插入: 240 条，错误: 0 条
2025-05-17 09:32:06,876 - INFO - 开始处理日期: 2025-05-17
2025-05-17 09:32:06,876 - INFO - Request Parameters - Page 1:
2025-05-17 09:32:06,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 09:32:06,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 09:32:14,994 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 58A61D62-8EFA-7EB1-854B-D345C94A8A1D Response: {'code': 'ServiceUnavailable', 'requestid': '58A61D62-8EFA-7EB1-854B-D345C94A8A1D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 58A61D62-8EFA-7EB1-854B-D345C94A8A1D)
2025-05-17 09:32:14,994 - INFO - 数据同步完成！更新: 0 条，插入: 240 条，错误: 1 条
2025-05-17 09:32:14,994 - INFO - 同步完成
2025-05-17 10:30:34,350 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 10:30:34,350 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 10:30:34,350 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 10:30:34,417 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 151 条记录
2025-05-17 10:30:34,417 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 10:30:34,419 - INFO - 开始处理日期: 2025-05-16
2025-05-17 10:30:34,423 - INFO - Request Parameters - Page 1:
2025-05-17 10:30:34,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 10:30:34,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 10:30:42,547 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EE668CD1-**************-BDBFD26322A4 Response: {'code': 'ServiceUnavailable', 'requestid': 'EE668CD1-**************-BDBFD26322A4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EE668CD1-**************-BDBFD26322A4)
2025-05-17 10:30:42,547 - INFO - 开始处理日期: 2025-05-17
2025-05-17 10:30:42,548 - INFO - Request Parameters - Page 1:
2025-05-17 10:30:42,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 10:30:42,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 10:30:42,693 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: EE296DE0-70E1-72E4-A33E-DD258FC52E7C Response: {'requestid': 'EE296DE0-70E1-72E4-A33E-DD258FC52E7C', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: EE296DE0-70E1-72E4-A33E-DD258FC52E7C)
2025-05-17 10:30:42,693 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-17 10:31:42,694 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 10:31:42,694 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 10:31:42,694 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 10:31:42,769 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 453 条记录
2025-05-17 10:31:42,770 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 10:31:42,772 - INFO - 开始处理日期: 2025-05-16
2025-05-17 10:31:42,773 - INFO - Request Parameters - Page 1:
2025-05-17 10:31:42,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 10:31:42,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 10:31:50,890 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9D4535BB-B73A-7EB4-8F6D-EDC8FD197E3B Response: {'code': 'ServiceUnavailable', 'requestid': '9D4535BB-B73A-7EB4-8F6D-EDC8FD197E3B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9D4535BB-B73A-7EB4-8F6D-EDC8FD197E3B)
2025-05-17 10:31:50,890 - INFO - 开始处理日期: 2025-05-17
2025-05-17 10:31:50,890 - INFO - Request Parameters - Page 1:
2025-05-17 10:31:50,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 10:31:50,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 10:31:51,037 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: B7E2F12A-F00D-7B34-B58E-C0311BEB869E Response: {'requestid': 'B7E2F12A-F00D-7B34-B58E-C0311BEB869E', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: B7E2F12A-F00D-7B34-B58E-C0311BEB869E)
2025-05-17 10:31:51,037 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-17 10:31:51,037 - INFO - 同步完成
2025-05-17 11:30:34,062 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 11:30:34,062 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 11:30:34,062 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 11:30:34,131 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 174 条记录
2025-05-17 11:30:34,131 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 11:30:34,133 - INFO - 开始处理日期: 2025-05-16
2025-05-17 11:30:34,136 - INFO - Request Parameters - Page 1:
2025-05-17 11:30:34,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 11:30:34,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 11:30:42,242 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 374D4653-4D2F-7B86-96BA-891B83A7917C Response: {'code': 'ServiceUnavailable', 'requestid': '374D4653-4D2F-7B86-96BA-891B83A7917C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 374D4653-4D2F-7B86-96BA-891B83A7917C)
2025-05-17 11:30:42,243 - INFO - 开始处理日期: 2025-05-17
2025-05-17 11:30:42,243 - INFO - Request Parameters - Page 1:
2025-05-17 11:30:42,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 11:30:42,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 11:30:50,345 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F1A424B6-CC2B-7FAE-880E-B39BF36B8793 Response: {'code': 'ServiceUnavailable', 'requestid': 'F1A424B6-CC2B-7FAE-880E-B39BF36B8793', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F1A424B6-CC2B-7FAE-880E-B39BF36B8793)
2025-05-17 11:30:50,345 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-17 11:31:50,345 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 11:31:50,345 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 11:31:50,345 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 11:31:50,423 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 510 条记录
2025-05-17 11:31:50,423 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 11:31:50,427 - INFO - 开始处理日期: 2025-05-16
2025-05-17 11:31:50,427 - INFO - Request Parameters - Page 1:
2025-05-17 11:31:50,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 11:31:50,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 11:31:51,293 - INFO - Response - Page 1:
2025-05-17 11:31:51,293 - INFO - 第 1 页获取到 100 条记录
2025-05-17 11:31:51,493 - INFO - Request Parameters - Page 2:
2025-05-17 11:31:51,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 11:31:51,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 11:31:52,298 - INFO - Response - Page 2:
2025-05-17 11:31:52,299 - INFO - 第 2 页获取到 100 条记录
2025-05-17 11:31:52,499 - INFO - Request Parameters - Page 3:
2025-05-17 11:31:52,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 11:31:52,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 11:31:53,302 - INFO - Response - Page 3:
2025-05-17 11:31:53,303 - INFO - 第 3 页获取到 94 条记录
2025-05-17 11:31:53,504 - INFO - 查询完成，共获取到 294 条记录
2025-05-17 11:31:53,504 - INFO - 获取到 294 条表单数据
2025-05-17 11:31:53,509 - INFO - 当前日期 2025-05-16 有 460 条MySQL数据需要处理
2025-05-17 11:31:53,514 - INFO - 开始批量插入 166 条新记录
2025-05-17 11:31:53,786 - INFO - 批量插入响应状态码: 200
2025-05-17 11:31:53,787 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 17 May 2025 03:31:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8B976B3D-001F-702F-B17A-7D62170B5DF3', 'x-acs-trace-id': '965dbdebcb77041a5507ddcb38c79378', 'etag': '4vQvUiq3A/wTOUUN2Dtx2sg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-17 11:31:53,787 - INFO - 批量插入响应体: {'result': ['FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM66', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM76', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM86', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM96', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMA6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMB6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMC6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMD6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAME6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMF6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMG6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMH6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMI6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMJ6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMK6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAML6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMM6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMN6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMO6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMP6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMQ6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMR6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMS6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMT6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMU6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMV6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMW6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMX6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMY6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMZ6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM07', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM17', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM27', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM37', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM47', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM57', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM67', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM77', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM87', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM97', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMA7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMB7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMC7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMD7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAME7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMF7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMG7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMH7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMI7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMJ7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMK7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAML7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMM7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMN7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMO7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMP7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMQ7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMR7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMS7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMT7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMU7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMV7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMW7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMX7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMY7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMZ7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM08', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM18', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM28', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM38', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM48', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM58', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM68', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM78', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM88', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM98', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMA8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMB8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMC8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMD8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAME8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMF8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMG8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMH8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMI8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMJ8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMK8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAML8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMM8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMN8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMO8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMP8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMQ8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMR8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMS8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMT8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMU8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMV8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMW8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMX8']}
2025-05-17 11:31:53,787 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-17 11:31:53,787 - INFO - 成功插入的数据ID: ['FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM66', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM76', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM86', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM96', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMA6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMB6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMC6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMD6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAME6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMF6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMG6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMH6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMI6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMJ6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMK6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAML6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMM6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMN6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMO6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMP6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMQ6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMR6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMS6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMT6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMU6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMV6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMW6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMX6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMY6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMZ6', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM07', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM17', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM27', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM37', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM47', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM57', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM67', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM77', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM87', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM97', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMA7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMB7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMC7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMD7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAME7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMF7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMG7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMH7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMI7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMJ7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMK7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAML7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMM7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMN7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMO7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMP7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMQ7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMR7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMS7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMT7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMU7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMV7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMW7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMX7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMY7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMZ7', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM08', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM18', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM28', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM38', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM48', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM58', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM68', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM78', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM88', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAM98', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMA8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMB8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMC8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMD8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAME8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMF8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMG8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMH8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMI8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMJ8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMK8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAML8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMM8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMN8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMO8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMP8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMQ8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMR8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMS8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMT8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMU8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMV8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMW8', 'FINST-3PF66V71UBHV5TV0EJAH1A1NHAS32NK38ORAMX8']
2025-05-17 11:31:59,004 - INFO - 批量插入响应状态码: 200
2025-05-17 11:31:59,004 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 17 May 2025 03:31:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3180', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0B43B8F9-46FB-766C-829D-C7DBDD79A458', 'x-acs-trace-id': '925ff88f86e737b88d730df29c224360', 'etag': '3QLlAosCvBwVHUtQska0Cyg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-17 11:31:59,004 - INFO - 批量插入响应体: {'result': ['FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM0P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM1P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM2P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM3P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM4P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM5P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM6P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM7P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM8P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM9P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMAP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMBP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMCP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMDP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMEP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMFP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMGP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMHP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMIP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMJP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMKP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMLP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMMP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMNP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMOP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMPP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMQP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMRP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMSP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMTP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMUP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMVP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMWP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMXP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMYP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMZP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM0Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM1Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM2Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM3Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM4Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM5Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM6Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM7Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM8Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM9Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMAQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMBQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMCQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMDQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMEQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMFQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMGQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMHQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMIQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMJQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMKQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMLQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMMQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMNQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMOQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMPQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMQQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMRQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMSQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMTQ']}
2025-05-17 11:31:59,004 - INFO - 批量插入表单数据成功，批次 2，共 66 条记录
2025-05-17 11:31:59,004 - INFO - 成功插入的数据ID: ['FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM0P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM1P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM2P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM3P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM4P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM5P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM6P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM7P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM8P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM9P', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMAP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMBP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMCP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMDP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMEP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMFP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMGP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMHP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMIP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMJP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMKP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMLP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMMP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMNP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMOP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMPP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMQP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMRP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMSP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMTP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMUP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMVP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMWP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMXP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMYP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMZP', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM0Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM1Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM2Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM3Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM4Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM5Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM6Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM7Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM8Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAM9Q', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMAQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMBQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMCQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMDQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMEQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMFQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMGQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMHQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMIQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMJQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMKQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMLQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMMQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMNQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMOQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMPQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMQQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMRQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMSQ', 'FINST-UW966371UFGVFE1FFSDKI6I0XJ7D2ML78ORAMTQ']
2025-05-17 11:32:04,005 - INFO - 批量插入完成，共 166 条记录
2025-05-17 11:32:04,005 - INFO - 日期 2025-05-16 处理完成 - 更新: 0 条，插入: 166 条，错误: 0 条
2025-05-17 11:32:04,005 - INFO - 开始处理日期: 2025-05-17
2025-05-17 11:32:04,005 - INFO - Request Parameters - Page 1:
2025-05-17 11:32:04,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 11:32:04,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 11:32:04,528 - INFO - Response - Page 1:
2025-05-17 11:32:04,528 - INFO - 第 1 页获取到 1 条记录
2025-05-17 11:32:04,728 - INFO - 查询完成，共获取到 1 条记录
2025-05-17 11:32:04,728 - INFO - 获取到 1 条表单数据
2025-05-17 11:32:04,728 - INFO - 当前日期 2025-05-17 有 50 条MySQL数据需要处理
2025-05-17 11:32:04,729 - INFO - 开始批量插入 49 条新记录
2025-05-17 11:32:04,939 - INFO - 批量插入响应状态码: 200
2025-05-17 11:32:04,939 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 17 May 2025 03:32:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2364', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B6289BD0-10DA-7A66-9881-B076303E19FB', 'x-acs-trace-id': '67953ad5cf849688f8582c7cee95c1e6', 'etag': '2cuSQojW8ONU0EaunREW7hA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-17 11:32:04,940 - INFO - 批量插入响应体: {'result': ['FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMZ7', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM08', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM18', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM28', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM38', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM48', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM58', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM68', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM78', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM88', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM98', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMA8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMB8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMC8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMD8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAME8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMF8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMG8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMH8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMI8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMJ8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMK8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAML8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMM8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMN8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMO8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMP8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMQ8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMR8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMS8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMT8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMU8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMV8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMW8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMX8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMY8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMZ8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM09', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM19', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM29', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM39', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM49', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM59', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM69', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM79', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM89', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM99', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMA9', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMB9']}
2025-05-17 11:32:04,940 - INFO - 批量插入表单数据成功，批次 1，共 49 条记录
2025-05-17 11:32:04,940 - INFO - 成功插入的数据ID: ['FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMZ7', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM08', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM18', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM28', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM38', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM48', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM58', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM68', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM78', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM88', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM98', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMA8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMB8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMC8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMD8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAME8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMF8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMG8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMH8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMI8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMJ8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMK8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAML8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMM8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMN8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMO8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMP8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMQ8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMR8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMS8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMT8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMU8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMV8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMW8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMX8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMY8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMZ8', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM09', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM19', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM29', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM39', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM49', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM59', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM69', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM79', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM89', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAM99', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMA9', 'FINST-I6E66WA1RBHV961L61FDF5KRBMHP3J6C8ORAMB9']
2025-05-17 11:32:09,941 - INFO - 批量插入完成，共 49 条记录
2025-05-17 11:32:09,941 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 49 条，错误: 0 条
2025-05-17 11:32:09,941 - INFO - 数据同步完成！更新: 0 条，插入: 215 条，错误: 0 条
2025-05-17 11:32:09,941 - INFO - 同步完成
2025-05-17 12:30:33,784 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 12:30:33,785 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 12:30:33,785 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 12:30:33,853 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 178 条记录
2025-05-17 12:30:33,853 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 12:30:33,855 - INFO - 开始处理日期: 2025-05-16
2025-05-17 12:30:33,857 - INFO - Request Parameters - Page 1:
2025-05-17 12:30:33,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:30:33,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:30:41,971 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7AC1E4EB-6B74-7EF2-BE2F-13655DF52DF7 Response: {'code': 'ServiceUnavailable', 'requestid': '7AC1E4EB-6B74-7EF2-BE2F-13655DF52DF7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7AC1E4EB-6B74-7EF2-BE2F-13655DF52DF7)
2025-05-17 12:30:41,971 - INFO - 开始处理日期: 2025-05-17
2025-05-17 12:30:41,971 - INFO - Request Parameters - Page 1:
2025-05-17 12:30:41,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:30:41,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:30:49,392 - INFO - Response - Page 1:
2025-05-17 12:30:49,392 - INFO - 第 1 页获取到 50 条记录
2025-05-17 12:30:49,592 - INFO - 查询完成，共获取到 50 条记录
2025-05-17 12:30:49,592 - INFO - 获取到 50 条表单数据
2025-05-17 12:30:49,593 - INFO - 当前日期 2025-05-17 有 50 条MySQL数据需要处理
2025-05-17 12:30:49,594 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 12:30:49,594 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 12:31:49,595 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 12:31:49,595 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 12:31:49,595 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 12:31:49,682 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 515 条记录
2025-05-17 12:31:49,682 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 12:31:49,686 - INFO - 开始处理日期: 2025-05-16
2025-05-17 12:31:49,687 - INFO - Request Parameters - Page 1:
2025-05-17 12:31:49,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:31:49,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:31:57,807 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F5D8FC6B-FA67-786A-B833-1448ACC81E35 Response: {'code': 'ServiceUnavailable', 'requestid': 'F5D8FC6B-FA67-786A-B833-1448ACC81E35', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F5D8FC6B-FA67-786A-B833-1448ACC81E35)
2025-05-17 12:31:57,808 - INFO - 开始处理日期: 2025-05-17
2025-05-17 12:31:57,808 - INFO - Request Parameters - Page 1:
2025-05-17 12:31:57,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 12:31:57,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 12:31:58,500 - INFO - Response - Page 1:
2025-05-17 12:31:58,500 - INFO - 第 1 页获取到 50 条记录
2025-05-17 12:31:58,700 - INFO - 查询完成，共获取到 50 条记录
2025-05-17 12:31:58,700 - INFO - 获取到 50 条表单数据
2025-05-17 12:31:58,701 - INFO - 当前日期 2025-05-17 有 50 条MySQL数据需要处理
2025-05-17 12:31:58,702 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 12:31:58,702 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 12:31:58,702 - INFO - 同步完成
2025-05-17 13:30:33,764 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 13:30:33,765 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 13:30:33,765 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 13:30:33,833 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 179 条记录
2025-05-17 13:30:33,833 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 13:30:33,835 - INFO - 开始处理日期: 2025-05-16
2025-05-17 13:30:33,837 - INFO - Request Parameters - Page 1:
2025-05-17 13:30:33,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 13:30:33,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 13:30:41,970 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 373844D7-6DE1-7CF6-B1D8-6EDCBCC039C6 Response: {'code': 'ServiceUnavailable', 'requestid': '373844D7-6DE1-7CF6-B1D8-6EDCBCC039C6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 373844D7-6DE1-7CF6-B1D8-6EDCBCC039C6)
2025-05-17 13:30:41,971 - INFO - 开始处理日期: 2025-05-17
2025-05-17 13:30:41,971 - INFO - Request Parameters - Page 1:
2025-05-17 13:30:41,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 13:30:41,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 13:30:50,084 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D40D36FB-24D9-7AD4-A4F0-E04879D82114 Response: {'code': 'ServiceUnavailable', 'requestid': 'D40D36FB-24D9-7AD4-A4F0-E04879D82114', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D40D36FB-24D9-7AD4-A4F0-E04879D82114)
2025-05-17 13:30:50,084 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-17 13:31:50,084 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 13:31:50,084 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 13:31:50,084 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 13:31:50,163 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 515 条记录
2025-05-17 13:31:50,163 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 13:31:50,167 - INFO - 开始处理日期: 2025-05-16
2025-05-17 13:31:50,167 - INFO - Request Parameters - Page 1:
2025-05-17 13:31:50,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 13:31:50,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 13:31:50,986 - INFO - Response - Page 1:
2025-05-17 13:31:50,987 - INFO - 第 1 页获取到 100 条记录
2025-05-17 13:31:51,188 - INFO - Request Parameters - Page 2:
2025-05-17 13:31:51,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 13:31:51,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 13:31:52,014 - INFO - Response - Page 2:
2025-05-17 13:31:52,014 - INFO - 第 2 页获取到 100 条记录
2025-05-17 13:31:52,214 - INFO - Request Parameters - Page 3:
2025-05-17 13:31:52,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 13:31:52,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 13:31:53,004 - INFO - Response - Page 3:
2025-05-17 13:31:53,004 - INFO - 第 3 页获取到 100 条记录
2025-05-17 13:31:53,204 - INFO - Request Parameters - Page 4:
2025-05-17 13:31:53,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 13:31:53,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 13:31:53,940 - INFO - Response - Page 4:
2025-05-17 13:31:53,940 - INFO - 第 4 页获取到 100 条记录
2025-05-17 13:31:54,140 - INFO - Request Parameters - Page 5:
2025-05-17 13:31:54,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 13:31:54,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 13:31:54,898 - INFO - Response - Page 5:
2025-05-17 13:31:54,898 - INFO - 第 5 页获取到 60 条记录
2025-05-17 13:31:55,098 - INFO - 查询完成，共获取到 460 条记录
2025-05-17 13:31:55,098 - INFO - 获取到 460 条表单数据
2025-05-17 13:31:55,106 - INFO - 当前日期 2025-05-16 有 465 条MySQL数据需要处理
2025-05-17 13:31:55,113 - INFO - 开始批量插入 5 条新记录
2025-05-17 13:31:55,267 - INFO - 批量插入响应状态码: 200
2025-05-17 13:31:55,267 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 17 May 2025 05:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '252', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FA5BBC7A-DB33-71A6-A43C-F85E814F3595', 'x-acs-trace-id': '948ea62cabaa33c47747751718d48190', 'etag': '2xpMtA5CRfz3fAfv3zjskRA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-17 13:31:55,267 - INFO - 批量插入响应体: {'result': ['FINST-R1A66H91AFHVCWCJEOHHK4VPXQ4033BGISRAM14', 'FINST-R1A66H91AFHVCWCJEOHHK4VPXQ4033BGISRAM24', 'FINST-R1A66H91AFHVCWCJEOHHK4VPXQ4033BGISRAM34', 'FINST-R1A66H91AFHVCWCJEOHHK4VPXQ4033BGISRAM44', 'FINST-R1A66H91AFHVCWCJEOHHK4VPXQ4033BGISRAM54']}
2025-05-17 13:31:55,267 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-05-17 13:31:55,267 - INFO - 成功插入的数据ID: ['FINST-R1A66H91AFHVCWCJEOHHK4VPXQ4033BGISRAM14', 'FINST-R1A66H91AFHVCWCJEOHHK4VPXQ4033BGISRAM24', 'FINST-R1A66H91AFHVCWCJEOHHK4VPXQ4033BGISRAM34', 'FINST-R1A66H91AFHVCWCJEOHHK4VPXQ4033BGISRAM44', 'FINST-R1A66H91AFHVCWCJEOHHK4VPXQ4033BGISRAM54']
2025-05-17 13:32:00,268 - INFO - 批量插入完成，共 5 条记录
2025-05-17 13:32:00,268 - INFO - 日期 2025-05-16 处理完成 - 更新: 0 条，插入: 5 条，错误: 0 条
2025-05-17 13:32:00,268 - INFO - 开始处理日期: 2025-05-17
2025-05-17 13:32:00,269 - INFO - Request Parameters - Page 1:
2025-05-17 13:32:00,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 13:32:00,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 13:32:01,002 - INFO - Response - Page 1:
2025-05-17 13:32:01,003 - INFO - 第 1 页获取到 50 条记录
2025-05-17 13:32:01,203 - INFO - 查询完成，共获取到 50 条记录
2025-05-17 13:32:01,203 - INFO - 获取到 50 条表单数据
2025-05-17 13:32:01,205 - INFO - 当前日期 2025-05-17 有 50 条MySQL数据需要处理
2025-05-17 13:32:01,206 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 13:32:01,207 - INFO - 数据同步完成！更新: 0 条，插入: 5 条，错误: 0 条
2025-05-17 13:32:01,207 - INFO - 同步完成
2025-05-17 14:30:33,761 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 14:30:33,761 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 14:30:33,761 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 14:30:33,830 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 179 条记录
2025-05-17 14:30:33,830 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 14:30:33,832 - INFO - 开始处理日期: 2025-05-16
2025-05-17 14:30:33,835 - INFO - Request Parameters - Page 1:
2025-05-17 14:30:33,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 14:30:33,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 14:30:41,941 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0F71431E-4478-762D-9A37-5583C5E46880 Response: {'code': 'ServiceUnavailable', 'requestid': '0F71431E-4478-762D-9A37-5583C5E46880', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0F71431E-4478-762D-9A37-5583C5E46880)
2025-05-17 14:30:41,942 - INFO - 开始处理日期: 2025-05-17
2025-05-17 14:30:41,942 - INFO - Request Parameters - Page 1:
2025-05-17 14:30:41,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 14:30:41,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 14:30:50,059 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D8F7422B-5CFD-75DF-8123-39EFEBBA418C Response: {'code': 'ServiceUnavailable', 'requestid': 'D8F7422B-5CFD-75DF-8123-39EFEBBA418C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D8F7422B-5CFD-75DF-8123-39EFEBBA418C)
2025-05-17 14:30:50,059 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-17 14:31:50,059 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 14:31:50,059 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 14:31:50,059 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 14:31:50,140 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 515 条记录
2025-05-17 14:31:50,140 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 14:31:50,145 - INFO - 开始处理日期: 2025-05-16
2025-05-17 14:31:50,145 - INFO - Request Parameters - Page 1:
2025-05-17 14:31:50,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 14:31:50,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 14:31:51,045 - INFO - Response - Page 1:
2025-05-17 14:31:51,045 - INFO - 第 1 页获取到 100 条记录
2025-05-17 14:31:51,245 - INFO - Request Parameters - Page 2:
2025-05-17 14:31:51,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 14:31:51,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 14:31:52,042 - INFO - Response - Page 2:
2025-05-17 14:31:52,043 - INFO - 第 2 页获取到 100 条记录
2025-05-17 14:31:52,243 - INFO - Request Parameters - Page 3:
2025-05-17 14:31:52,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 14:31:52,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 14:31:53,068 - INFO - Response - Page 3:
2025-05-17 14:31:53,068 - INFO - 第 3 页获取到 100 条记录
2025-05-17 14:31:53,268 - INFO - Request Parameters - Page 4:
2025-05-17 14:31:53,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 14:31:53,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 14:31:54,017 - INFO - Response - Page 4:
2025-05-17 14:31:54,017 - INFO - 第 4 页获取到 100 条记录
2025-05-17 14:31:54,218 - INFO - Request Parameters - Page 5:
2025-05-17 14:31:54,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 14:31:54,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 14:31:54,979 - INFO - Response - Page 5:
2025-05-17 14:31:54,979 - INFO - 第 5 页获取到 65 条记录
2025-05-17 14:31:55,180 - INFO - 查询完成，共获取到 465 条记录
2025-05-17 14:31:55,180 - INFO - 获取到 465 条表单数据
2025-05-17 14:31:55,190 - INFO - 当前日期 2025-05-16 有 465 条MySQL数据需要处理
2025-05-17 14:31:55,201 - INFO - 日期 2025-05-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 14:31:55,201 - INFO - 开始处理日期: 2025-05-17
2025-05-17 14:31:55,201 - INFO - Request Parameters - Page 1:
2025-05-17 14:31:55,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 14:31:55,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 14:31:56,375 - INFO - Response - Page 1:
2025-05-17 14:31:56,375 - INFO - 第 1 页获取到 50 条记录
2025-05-17 14:31:56,575 - INFO - 查询完成，共获取到 50 条记录
2025-05-17 14:31:56,575 - INFO - 获取到 50 条表单数据
2025-05-17 14:31:56,577 - INFO - 当前日期 2025-05-17 有 50 条MySQL数据需要处理
2025-05-17 14:31:56,578 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 14:31:56,578 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 14:31:56,578 - INFO - 同步完成
2025-05-17 15:30:34,061 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 15:30:34,062 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 15:30:34,062 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 15:30:34,130 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 179 条记录
2025-05-17 15:30:34,130 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 15:30:34,132 - INFO - 开始处理日期: 2025-05-16
2025-05-17 15:30:34,135 - INFO - Request Parameters - Page 1:
2025-05-17 15:30:34,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:30:34,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:30:42,259 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EFD7555D-0ADF-7F01-9034-21A9B230830A Response: {'code': 'ServiceUnavailable', 'requestid': 'EFD7555D-0ADF-7F01-9034-21A9B230830A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EFD7555D-0ADF-7F01-9034-21A9B230830A)
2025-05-17 15:30:42,259 - INFO - 开始处理日期: 2025-05-17
2025-05-17 15:30:42,259 - INFO - Request Parameters - Page 1:
2025-05-17 15:30:42,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:30:42,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:30:50,368 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4B941FBF-F13F-78BA-BFF5-42D574E16901 Response: {'code': 'ServiceUnavailable', 'requestid': '4B941FBF-F13F-78BA-BFF5-42D574E16901', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4B941FBF-F13F-78BA-BFF5-42D574E16901)
2025-05-17 15:30:50,369 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-17 15:31:50,369 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 15:31:50,369 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 15:31:50,369 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 15:31:50,450 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 515 条记录
2025-05-17 15:31:50,450 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 15:31:50,455 - INFO - 开始处理日期: 2025-05-16
2025-05-17 15:31:50,455 - INFO - Request Parameters - Page 1:
2025-05-17 15:31:50,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:31:50,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:31:51,277 - INFO - Response - Page 1:
2025-05-17 15:31:51,278 - INFO - 第 1 页获取到 100 条记录
2025-05-17 15:31:51,479 - INFO - Request Parameters - Page 2:
2025-05-17 15:31:51,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:31:51,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:31:52,313 - INFO - Response - Page 2:
2025-05-17 15:31:52,313 - INFO - 第 2 页获取到 100 条记录
2025-05-17 15:31:52,514 - INFO - Request Parameters - Page 3:
2025-05-17 15:31:52,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:31:52,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:31:53,342 - INFO - Response - Page 3:
2025-05-17 15:31:53,342 - INFO - 第 3 页获取到 100 条记录
2025-05-17 15:31:53,542 - INFO - Request Parameters - Page 4:
2025-05-17 15:31:53,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:31:53,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:31:54,312 - INFO - Response - Page 4:
2025-05-17 15:31:54,312 - INFO - 第 4 页获取到 100 条记录
2025-05-17 15:31:54,513 - INFO - Request Parameters - Page 5:
2025-05-17 15:31:54,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:31:54,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:31:55,357 - INFO - Response - Page 5:
2025-05-17 15:31:55,358 - INFO - 第 5 页获取到 65 条记录
2025-05-17 15:31:55,559 - INFO - 查询完成，共获取到 465 条记录
2025-05-17 15:31:55,559 - INFO - 获取到 465 条表单数据
2025-05-17 15:31:55,566 - INFO - 当前日期 2025-05-16 有 465 条MySQL数据需要处理
2025-05-17 15:31:55,574 - INFO - 日期 2025-05-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 15:31:55,574 - INFO - 开始处理日期: 2025-05-17
2025-05-17 15:31:55,574 - INFO - Request Parameters - Page 1:
2025-05-17 15:31:55,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 15:31:55,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 15:31:56,291 - INFO - Response - Page 1:
2025-05-17 15:31:56,291 - INFO - 第 1 页获取到 50 条记录
2025-05-17 15:31:56,491 - INFO - 查询完成，共获取到 50 条记录
2025-05-17 15:31:56,491 - INFO - 获取到 50 条表单数据
2025-05-17 15:31:56,493 - INFO - 当前日期 2025-05-17 有 50 条MySQL数据需要处理
2025-05-17 15:31:56,493 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 15:31:56,494 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 15:31:56,494 - INFO - 同步完成
2025-05-17 16:30:34,234 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 16:30:34,235 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 16:30:34,235 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 16:30:34,305 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 222 条记录
2025-05-17 16:30:34,305 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 16:30:34,307 - INFO - 开始处理日期: 2025-05-16
2025-05-17 16:30:34,311 - INFO - Request Parameters - Page 1:
2025-05-17 16:30:34,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 16:30:34,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 16:30:42,426 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B764BB87-AFE7-767E-8632-AEA604935B5E Response: {'code': 'ServiceUnavailable', 'requestid': 'B764BB87-AFE7-767E-8632-AEA604935B5E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B764BB87-AFE7-767E-8632-AEA604935B5E)
2025-05-17 16:30:42,426 - INFO - 开始处理日期: 2025-05-17
2025-05-17 16:30:42,426 - INFO - Request Parameters - Page 1:
2025-05-17 16:30:42,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 16:30:42,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 16:30:50,541 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E8B3F54B-3605-7BD1-A2C3-11A6AABB390C Response: {'code': 'ServiceUnavailable', 'requestid': 'E8B3F54B-3605-7BD1-A2C3-11A6AABB390C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E8B3F54B-3605-7BD1-A2C3-11A6AABB390C)
2025-05-17 16:30:50,541 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-17 16:31:50,541 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 16:31:50,541 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 16:31:50,541 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 16:31:50,621 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 558 条记录
2025-05-17 16:31:50,621 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 16:31:50,626 - INFO - 开始处理日期: 2025-05-16
2025-05-17 16:31:50,626 - INFO - Request Parameters - Page 1:
2025-05-17 16:31:50,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 16:31:50,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 16:31:58,747 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 592A1466-24C2-71C3-818C-870098D73CEB Response: {'code': 'ServiceUnavailable', 'requestid': '592A1466-24C2-71C3-818C-870098D73CEB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 592A1466-24C2-71C3-818C-870098D73CEB)
2025-05-17 16:31:58,747 - INFO - 开始处理日期: 2025-05-17
2025-05-17 16:31:58,747 - INFO - Request Parameters - Page 1:
2025-05-17 16:31:58,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 16:31:58,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 16:32:00,554 - INFO - Response - Page 1:
2025-05-17 16:32:00,554 - INFO - 第 1 页获取到 50 条记录
2025-05-17 16:32:00,754 - INFO - 查询完成，共获取到 50 条记录
2025-05-17 16:32:00,754 - INFO - 获取到 50 条表单数据
2025-05-17 16:32:00,755 - INFO - 当前日期 2025-05-17 有 50 条MySQL数据需要处理
2025-05-17 16:32:00,756 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 16:32:00,756 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 16:32:00,756 - INFO - 同步完成
2025-05-17 17:30:33,901 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 17:30:33,901 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 17:30:33,902 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 17:30:33,973 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 222 条记录
2025-05-17 17:30:33,973 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 17:30:33,975 - INFO - 开始处理日期: 2025-05-16
2025-05-17 17:30:33,978 - INFO - Request Parameters - Page 1:
2025-05-17 17:30:33,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 17:30:33,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 17:30:42,111 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5C3BB72F-F2D2-7D4D-97DA-C4F4106F249D Response: {'code': 'ServiceUnavailable', 'requestid': '5C3BB72F-F2D2-7D4D-97DA-C4F4106F249D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5C3BB72F-F2D2-7D4D-97DA-C4F4106F249D)
2025-05-17 17:30:42,111 - INFO - 开始处理日期: 2025-05-17
2025-05-17 17:30:42,111 - INFO - Request Parameters - Page 1:
2025-05-17 17:30:42,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 17:30:42,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 17:30:50,210 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3AE5BB02-114C-7889-8E03-D08B2BABE3EE Response: {'code': 'ServiceUnavailable', 'requestid': '3AE5BB02-114C-7889-8E03-D08B2BABE3EE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3AE5BB02-114C-7889-8E03-D08B2BABE3EE)
2025-05-17 17:30:50,211 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-17 17:31:50,211 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 17:31:50,211 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 17:31:50,211 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 17:31:50,289 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 558 条记录
2025-05-17 17:31:50,290 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 17:31:50,294 - INFO - 开始处理日期: 2025-05-16
2025-05-17 17:31:50,294 - INFO - Request Parameters - Page 1:
2025-05-17 17:31:50,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 17:31:50,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 17:31:51,164 - INFO - Response - Page 1:
2025-05-17 17:31:51,164 - INFO - 第 1 页获取到 100 条记录
2025-05-17 17:31:51,364 - INFO - Request Parameters - Page 2:
2025-05-17 17:31:51,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 17:31:51,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 17:31:52,160 - INFO - Response - Page 2:
2025-05-17 17:31:52,160 - INFO - 第 2 页获取到 100 条记录
2025-05-17 17:31:52,360 - INFO - Request Parameters - Page 3:
2025-05-17 17:31:52,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 17:31:52,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 17:31:53,180 - INFO - Response - Page 3:
2025-05-17 17:31:53,181 - INFO - 第 3 页获取到 100 条记录
2025-05-17 17:31:53,381 - INFO - Request Parameters - Page 4:
2025-05-17 17:31:53,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 17:31:53,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 17:31:54,190 - INFO - Response - Page 4:
2025-05-17 17:31:54,190 - INFO - 第 4 页获取到 100 条记录
2025-05-17 17:31:54,390 - INFO - Request Parameters - Page 5:
2025-05-17 17:31:54,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 17:31:54,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 17:31:55,174 - INFO - Response - Page 5:
2025-05-17 17:31:55,175 - INFO - 第 5 页获取到 65 条记录
2025-05-17 17:31:55,375 - INFO - 查询完成，共获取到 465 条记录
2025-05-17 17:31:55,375 - INFO - 获取到 465 条表单数据
2025-05-17 17:31:55,383 - INFO - 当前日期 2025-05-16 有 508 条MySQL数据需要处理
2025-05-17 17:31:55,391 - INFO - 开始批量插入 43 条新记录
2025-05-17 17:31:55,620 - INFO - 批量插入响应状态码: 200
2025-05-17 17:31:55,620 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 17 May 2025 09:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2064', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CA22816D-7A57-7E0B-B783-FEB43BC169F2', 'x-acs-trace-id': '07e14ddef11d8075b722806012ba3a86', 'etag': '2SAcG8FK2hpnxPWrc7PgX0w4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-17 17:31:55,620 - INFO - 批量插入响应体: {'result': ['FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMO', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMP', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMQ', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMR', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMS', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMT', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMU', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMV', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMW', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMX', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMY', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMZ', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM01', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM11', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM21', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM31', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM41', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM51', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM61', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM71', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM81', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM91', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMA1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMB1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMC1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMD1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAME1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMF1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMG1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMH1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMI1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMJ1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMK1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAML1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMM1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMN1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMO1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMP1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMQ1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMR1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMS1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMT1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMU1']}
2025-05-17 17:31:55,620 - INFO - 批量插入表单数据成功，批次 1，共 43 条记录
2025-05-17 17:31:55,620 - INFO - 成功插入的数据ID: ['FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMO', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMP', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMQ', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMR', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMS', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMT', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMU', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMV', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMW', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMX', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMY', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMZ', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM01', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM11', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM21', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM31', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM41', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM51', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM61', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM71', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM81', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAM91', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMA1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMB1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMC1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMD1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAME1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMF1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMG1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMH1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMI1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMJ1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882IR331SAMK1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAML1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMM1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMN1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMO1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMP1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMQ1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMR1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMS1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMT1', 'FINST-7PF66BA1R8IVDYIBDZL5M99D4P882JR331SAMU1']
2025-05-17 17:32:00,621 - INFO - 批量插入完成，共 43 条记录
2025-05-17 17:32:00,621 - INFO - 日期 2025-05-16 处理完成 - 更新: 0 条，插入: 43 条，错误: 0 条
2025-05-17 17:32:00,621 - INFO - 开始处理日期: 2025-05-17
2025-05-17 17:32:00,622 - INFO - Request Parameters - Page 1:
2025-05-17 17:32:00,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 17:32:00,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 17:32:01,364 - INFO - Response - Page 1:
2025-05-17 17:32:01,364 - INFO - 第 1 页获取到 50 条记录
2025-05-17 17:32:01,565 - INFO - 查询完成，共获取到 50 条记录
2025-05-17 17:32:01,565 - INFO - 获取到 50 条表单数据
2025-05-17 17:32:01,567 - INFO - 当前日期 2025-05-17 有 50 条MySQL数据需要处理
2025-05-17 17:32:01,568 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 17:32:01,568 - INFO - 数据同步完成！更新: 0 条，插入: 43 条，错误: 0 条
2025-05-17 17:32:01,568 - INFO - 同步完成
2025-05-17 18:30:34,359 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 18:30:34,360 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 18:30:34,360 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 18:30:34,431 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 222 条记录
2025-05-17 18:30:34,431 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 18:30:34,433 - INFO - 开始处理日期: 2025-05-16
2025-05-17 18:30:34,436 - INFO - Request Parameters - Page 1:
2025-05-17 18:30:34,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:30:34,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:30:42,559 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5B08FB9D-9F9E-7F92-9202-3E5A0063C3B8 Response: {'code': 'ServiceUnavailable', 'requestid': '5B08FB9D-9F9E-7F92-9202-3E5A0063C3B8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5B08FB9D-9F9E-7F92-9202-3E5A0063C3B8)
2025-05-17 18:30:42,559 - INFO - 开始处理日期: 2025-05-17
2025-05-17 18:30:42,559 - INFO - Request Parameters - Page 1:
2025-05-17 18:30:42,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:30:42,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:30:50,663 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9B96B9A4-06FB-7C1C-BA7A-8415E87AA88D Response: {'code': 'ServiceUnavailable', 'requestid': '9B96B9A4-06FB-7C1C-BA7A-8415E87AA88D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9B96B9A4-06FB-7C1C-BA7A-8415E87AA88D)
2025-05-17 18:30:50,663 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-17 18:31:50,664 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 18:31:50,664 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 18:31:50,664 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 18:31:50,743 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 558 条记录
2025-05-17 18:31:50,744 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 18:31:50,748 - INFO - 开始处理日期: 2025-05-16
2025-05-17 18:31:50,748 - INFO - Request Parameters - Page 1:
2025-05-17 18:31:50,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:31:50,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:31:51,573 - INFO - Response - Page 1:
2025-05-17 18:31:51,573 - INFO - 第 1 页获取到 100 条记录
2025-05-17 18:31:51,773 - INFO - Request Parameters - Page 2:
2025-05-17 18:31:51,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:31:51,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:31:52,579 - INFO - Response - Page 2:
2025-05-17 18:31:52,579 - INFO - 第 2 页获取到 100 条记录
2025-05-17 18:31:52,779 - INFO - Request Parameters - Page 3:
2025-05-17 18:31:52,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:31:52,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:31:53,587 - INFO - Response - Page 3:
2025-05-17 18:31:53,587 - INFO - 第 3 页获取到 100 条记录
2025-05-17 18:31:53,787 - INFO - Request Parameters - Page 4:
2025-05-17 18:31:53,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:31:53,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:31:54,637 - INFO - Response - Page 4:
2025-05-17 18:31:54,637 - INFO - 第 4 页获取到 100 条记录
2025-05-17 18:31:54,837 - INFO - Request Parameters - Page 5:
2025-05-17 18:31:54,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:31:54,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:31:55,609 - INFO - Response - Page 5:
2025-05-17 18:31:55,609 - INFO - 第 5 页获取到 100 条记录
2025-05-17 18:31:55,809 - INFO - Request Parameters - Page 6:
2025-05-17 18:31:55,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:31:55,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:31:56,342 - INFO - Response - Page 6:
2025-05-17 18:31:56,342 - INFO - 第 6 页获取到 8 条记录
2025-05-17 18:31:56,543 - INFO - 查询完成，共获取到 508 条记录
2025-05-17 18:31:56,543 - INFO - 获取到 508 条表单数据
2025-05-17 18:31:56,550 - INFO - 当前日期 2025-05-16 有 508 条MySQL数据需要处理
2025-05-17 18:31:56,559 - INFO - 日期 2025-05-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 18:31:56,559 - INFO - 开始处理日期: 2025-05-17
2025-05-17 18:31:56,559 - INFO - Request Parameters - Page 1:
2025-05-17 18:31:56,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 18:31:56,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 18:31:57,257 - INFO - Response - Page 1:
2025-05-17 18:31:57,257 - INFO - 第 1 页获取到 50 条记录
2025-05-17 18:31:57,458 - INFO - 查询完成，共获取到 50 条记录
2025-05-17 18:31:57,458 - INFO - 获取到 50 条表单数据
2025-05-17 18:31:57,460 - INFO - 当前日期 2025-05-17 有 50 条MySQL数据需要处理
2025-05-17 18:31:57,461 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 18:31:57,461 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 18:31:57,461 - INFO - 同步完成
2025-05-17 19:30:34,175 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 19:30:34,175 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 19:30:34,175 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 19:30:34,245 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 222 条记录
2025-05-17 19:30:34,245 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 19:30:34,247 - INFO - 开始处理日期: 2025-05-16
2025-05-17 19:30:34,251 - INFO - Request Parameters - Page 1:
2025-05-17 19:30:34,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 19:30:34,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 19:30:42,365 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F31B28A4-A334-78F0-B367-AF149B44560D Response: {'code': 'ServiceUnavailable', 'requestid': 'F31B28A4-A334-78F0-B367-AF149B44560D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F31B28A4-A334-78F0-B367-AF149B44560D)
2025-05-17 19:30:42,365 - INFO - 开始处理日期: 2025-05-17
2025-05-17 19:30:42,366 - INFO - Request Parameters - Page 1:
2025-05-17 19:30:42,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 19:30:42,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 19:30:49,334 - INFO - Response - Page 1:
2025-05-17 19:30:49,334 - INFO - 第 1 页获取到 50 条记录
2025-05-17 19:30:49,534 - INFO - 查询完成，共获取到 50 条记录
2025-05-17 19:30:49,534 - INFO - 获取到 50 条表单数据
2025-05-17 19:30:49,535 - INFO - 当前日期 2025-05-17 有 50 条MySQL数据需要处理
2025-05-17 19:30:49,536 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 19:30:49,536 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 19:31:49,548 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 19:31:49,548 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 19:31:49,548 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 19:31:49,626 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 558 条记录
2025-05-17 19:31:49,627 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 19:31:49,631 - INFO - 开始处理日期: 2025-05-16
2025-05-17 19:31:49,631 - INFO - Request Parameters - Page 1:
2025-05-17 19:31:49,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 19:31:49,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 19:31:57,743 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CF63437B-6449-7F92-84BF-3C20DF8CA16D Response: {'code': 'ServiceUnavailable', 'requestid': 'CF63437B-6449-7F92-84BF-3C20DF8CA16D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CF63437B-6449-7F92-84BF-3C20DF8CA16D)
2025-05-17 19:31:57,743 - INFO - 开始处理日期: 2025-05-17
2025-05-17 19:31:57,743 - INFO - Request Parameters - Page 1:
2025-05-17 19:31:57,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 19:31:57,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 19:31:58,496 - INFO - Response - Page 1:
2025-05-17 19:31:58,496 - INFO - 第 1 页获取到 50 条记录
2025-05-17 19:31:58,696 - INFO - 查询完成，共获取到 50 条记录
2025-05-17 19:31:58,696 - INFO - 获取到 50 条表单数据
2025-05-17 19:31:58,698 - INFO - 当前日期 2025-05-17 有 50 条MySQL数据需要处理
2025-05-17 19:31:58,700 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 19:31:58,700 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-17 19:31:58,700 - INFO - 同步完成
2025-05-17 20:30:34,552 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 20:30:34,552 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 20:30:34,552 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 20:30:34,623 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 226 条记录
2025-05-17 20:30:34,624 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 20:30:34,626 - INFO - 开始处理日期: 2025-05-16
2025-05-17 20:30:34,629 - INFO - Request Parameters - Page 1:
2025-05-17 20:30:34,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 20:30:34,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 20:30:42,746 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 14AA0C9C-5744-7142-ABF0-7796126B1C9F Response: {'code': 'ServiceUnavailable', 'requestid': '14AA0C9C-5744-7142-ABF0-7796126B1C9F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 14AA0C9C-5744-7142-ABF0-7796126B1C9F)
2025-05-17 20:30:42,746 - INFO - 开始处理日期: 2025-05-17
2025-05-17 20:30:42,746 - INFO - Request Parameters - Page 1:
2025-05-17 20:30:42,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 20:30:42,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 20:30:50,865 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CB0F49DA-61E2-74C2-A5F8-B12AAE884EB3 Response: {'code': 'ServiceUnavailable', 'requestid': 'CB0F49DA-61E2-74C2-A5F8-B12AAE884EB3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CB0F49DA-61E2-74C2-A5F8-B12AAE884EB3)
2025-05-17 20:30:50,865 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-17 20:31:50,876 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 20:31:50,876 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 20:31:50,876 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 20:31:50,959 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 562 条记录
2025-05-17 20:31:50,959 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 20:31:50,964 - INFO - 开始处理日期: 2025-05-16
2025-05-17 20:31:50,964 - INFO - Request Parameters - Page 1:
2025-05-17 20:31:50,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 20:31:50,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 20:31:51,826 - INFO - Response - Page 1:
2025-05-17 20:31:51,827 - INFO - 第 1 页获取到 100 条记录
2025-05-17 20:31:52,027 - INFO - Request Parameters - Page 2:
2025-05-17 20:31:52,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 20:31:52,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 20:31:52,829 - INFO - Response - Page 2:
2025-05-17 20:31:52,829 - INFO - 第 2 页获取到 100 条记录
2025-05-17 20:31:53,029 - INFO - Request Parameters - Page 3:
2025-05-17 20:31:53,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 20:31:53,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 20:31:53,868 - INFO - Response - Page 3:
2025-05-17 20:31:53,868 - INFO - 第 3 页获取到 100 条记录
2025-05-17 20:31:54,068 - INFO - Request Parameters - Page 4:
2025-05-17 20:31:54,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 20:31:54,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 20:31:54,854 - INFO - Response - Page 4:
2025-05-17 20:31:54,854 - INFO - 第 4 页获取到 100 条记录
2025-05-17 20:31:55,055 - INFO - Request Parameters - Page 5:
2025-05-17 20:31:55,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 20:31:55,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 20:31:55,848 - INFO - Response - Page 5:
2025-05-17 20:31:55,849 - INFO - 第 5 页获取到 100 条记录
2025-05-17 20:31:56,049 - INFO - Request Parameters - Page 6:
2025-05-17 20:31:56,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 20:31:56,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 20:31:56,595 - INFO - Response - Page 6:
2025-05-17 20:31:56,596 - INFO - 第 6 页获取到 8 条记录
2025-05-17 20:31:56,796 - INFO - 查询完成，共获取到 508 条记录
2025-05-17 20:31:56,796 - INFO - 获取到 508 条表单数据
2025-05-17 20:31:56,804 - INFO - 当前日期 2025-05-16 有 512 条MySQL数据需要处理
2025-05-17 20:31:56,813 - INFO - 开始批量插入 4 条新记录
2025-05-17 20:31:56,999 - INFO - 批量插入响应状态码: 200
2025-05-17 20:31:56,999 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 17 May 2025 12:31:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2E0F9A4B-06B0-7416-88C1-F4B52897C000', 'x-acs-trace-id': 'cf5d77fa585cec861d580ad3052760d2', 'etag': '2WxyQquqjIQ2bujJB36IdUA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-17 20:31:56,999 - INFO - 批量插入响应体: {'result': ['FINST-HJ966H81CFHVO2PY537LH8IGP8S3278PI7SAM1A', 'FINST-HJ966H81CFHVO2PY537LH8IGP8S3278PI7SAM2A', 'FINST-HJ966H81CFHVO2PY537LH8IGP8S3278PI7SAM3A', 'FINST-HJ966H81CFHVO2PY537LH8IGP8S3278PI7SAM4A']}
2025-05-17 20:31:56,999 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-05-17 20:31:56,999 - INFO - 成功插入的数据ID: ['FINST-HJ966H81CFHVO2PY537LH8IGP8S3278PI7SAM1A', 'FINST-HJ966H81CFHVO2PY537LH8IGP8S3278PI7SAM2A', 'FINST-HJ966H81CFHVO2PY537LH8IGP8S3278PI7SAM3A', 'FINST-HJ966H81CFHVO2PY537LH8IGP8S3278PI7SAM4A']
2025-05-17 20:32:02,002 - INFO - 批量插入完成，共 4 条记录
2025-05-17 20:32:02,002 - INFO - 日期 2025-05-16 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-05-17 20:32:02,002 - INFO - 开始处理日期: 2025-05-17
2025-05-17 20:32:02,002 - INFO - Request Parameters - Page 1:
2025-05-17 20:32:02,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 20:32:02,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 20:32:02,710 - INFO - Response - Page 1:
2025-05-17 20:32:02,710 - INFO - 第 1 页获取到 50 条记录
2025-05-17 20:32:02,911 - INFO - 查询完成，共获取到 50 条记录
2025-05-17 20:32:02,911 - INFO - 获取到 50 条表单数据
2025-05-17 20:32:02,912 - INFO - 当前日期 2025-05-17 有 50 条MySQL数据需要处理
2025-05-17 20:32:02,913 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 20:32:02,913 - INFO - 数据同步完成！更新: 0 条，插入: 4 条，错误: 0 条
2025-05-17 20:32:02,913 - INFO - 同步完成
2025-05-17 21:30:34,227 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 21:30:34,227 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 21:30:34,227 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 21:30:34,299 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 230 条记录
2025-05-17 21:30:34,299 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 21:30:34,301 - INFO - 开始处理日期: 2025-05-16
2025-05-17 21:30:34,302 - INFO - Request Parameters - Page 1:
2025-05-17 21:30:34,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:30:34,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:30:42,418 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E6116DDC-4F50-7680-9520-68589B96A233 Response: {'code': 'ServiceUnavailable', 'requestid': 'E6116DDC-4F50-7680-9520-68589B96A233', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E6116DDC-4F50-7680-9520-68589B96A233)
2025-05-17 21:30:42,418 - INFO - 开始处理日期: 2025-05-17
2025-05-17 21:30:42,418 - INFO - Request Parameters - Page 1:
2025-05-17 21:30:42,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:30:42,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:30:50,549 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-57DF-77B0-B72A-D25571264B63 Response: {'code': 'ServiceUnavailable', 'requestid': '********-57DF-77B0-B72A-D25571264B63', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-57DF-77B0-B72A-D25571264B63)
2025-05-17 21:30:50,549 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-17 21:31:50,561 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 21:31:50,561 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 21:31:50,561 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 21:31:50,639 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 566 条记录
2025-05-17 21:31:50,640 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 21:31:50,644 - INFO - 开始处理日期: 2025-05-16
2025-05-17 21:31:50,645 - INFO - Request Parameters - Page 1:
2025-05-17 21:31:50,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:31:50,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:31:51,501 - INFO - Response - Page 1:
2025-05-17 21:31:51,501 - INFO - 第 1 页获取到 100 条记录
2025-05-17 21:31:51,702 - INFO - Request Parameters - Page 2:
2025-05-17 21:31:51,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:31:51,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:31:52,515 - INFO - Response - Page 2:
2025-05-17 21:31:52,515 - INFO - 第 2 页获取到 100 条记录
2025-05-17 21:31:52,715 - INFO - Request Parameters - Page 3:
2025-05-17 21:31:52,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:31:52,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:31:53,532 - INFO - Response - Page 3:
2025-05-17 21:31:53,532 - INFO - 第 3 页获取到 100 条记录
2025-05-17 21:31:53,733 - INFO - Request Parameters - Page 4:
2025-05-17 21:31:53,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:31:53,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:31:54,530 - INFO - Response - Page 4:
2025-05-17 21:31:54,530 - INFO - 第 4 页获取到 100 条记录
2025-05-17 21:31:54,730 - INFO - Request Parameters - Page 5:
2025-05-17 21:31:54,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:31:54,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:31:55,492 - INFO - Response - Page 5:
2025-05-17 21:31:55,492 - INFO - 第 5 页获取到 100 条记录
2025-05-17 21:31:55,692 - INFO - Request Parameters - Page 6:
2025-05-17 21:31:55,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:31:55,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:31:56,281 - INFO - Response - Page 6:
2025-05-17 21:31:56,281 - INFO - 第 6 页获取到 12 条记录
2025-05-17 21:31:56,481 - INFO - 查询完成，共获取到 512 条记录
2025-05-17 21:31:56,481 - INFO - 获取到 512 条表单数据
2025-05-17 21:31:56,489 - INFO - 当前日期 2025-05-16 有 512 条MySQL数据需要处理
2025-05-17 21:31:56,498 - INFO - 日期 2025-05-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 21:31:56,498 - INFO - 开始处理日期: 2025-05-17
2025-05-17 21:31:56,498 - INFO - Request Parameters - Page 1:
2025-05-17 21:31:56,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 21:31:56,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 21:31:57,199 - INFO - Response - Page 1:
2025-05-17 21:31:57,200 - INFO - 第 1 页获取到 50 条记录
2025-05-17 21:31:57,400 - INFO - 查询完成，共获取到 50 条记录
2025-05-17 21:31:57,400 - INFO - 获取到 50 条表单数据
2025-05-17 21:31:57,402 - INFO - 当前日期 2025-05-17 有 54 条MySQL数据需要处理
2025-05-17 21:31:57,403 - INFO - 开始批量插入 4 条新记录
2025-05-17 21:31:57,564 - INFO - 批量插入响应状态码: 200
2025-05-17 21:31:57,564 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 17 May 2025 13:31:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5E0AD8ED-3A62-726C-BD6F-4BEB1A9AE5E1', 'x-acs-trace-id': '143f709b42b0a99e08e2c3c3d837e3ba', 'etag': '2mCImsGbGbjSd5ISNs8EoNw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-17 21:31:57,565 - INFO - 批量插入响应体: {'result': ['FINST-KLF66RD1SBHVICC7E9YOX7IIG6VR21YUN9SAMYG', 'FINST-KLF66RD1SBHVICC7E9YOX7IIG6VR21YUN9SAMZG', 'FINST-KLF66RD1SBHVICC7E9YOX7IIG6VR21YUN9SAM0H', 'FINST-KLF66RD1SBHVICC7E9YOX7IIG6VR21YUN9SAM1H']}
2025-05-17 21:31:57,565 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-05-17 21:31:57,565 - INFO - 成功插入的数据ID: ['FINST-KLF66RD1SBHVICC7E9YOX7IIG6VR21YUN9SAMYG', 'FINST-KLF66RD1SBHVICC7E9YOX7IIG6VR21YUN9SAMZG', 'FINST-KLF66RD1SBHVICC7E9YOX7IIG6VR21YUN9SAM0H', 'FINST-KLF66RD1SBHVICC7E9YOX7IIG6VR21YUN9SAM1H']
2025-05-17 21:32:02,567 - INFO - 批量插入完成，共 4 条记录
2025-05-17 21:32:02,567 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-05-17 21:32:02,567 - INFO - 数据同步完成！更新: 0 条，插入: 4 条，错误: 0 条
2025-05-17 21:32:02,567 - INFO - 同步完成
2025-05-17 22:30:34,601 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 22:30:34,601 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 22:30:34,601 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 22:30:34,674 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 306 条记录
2025-05-17 22:30:34,674 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 22:30:34,677 - INFO - 开始处理日期: 2025-05-16
2025-05-17 22:30:34,680 - INFO - Request Parameters - Page 1:
2025-05-17 22:30:34,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 22:30:34,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 22:30:42,804 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3A01D230-A529-797B-B1F4-7072F0CEB391 Response: {'code': 'ServiceUnavailable', 'requestid': '3A01D230-A529-797B-B1F4-7072F0CEB391', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3A01D230-A529-797B-B1F4-7072F0CEB391)
2025-05-17 22:30:42,804 - INFO - 开始处理日期: 2025-05-17
2025-05-17 22:30:42,804 - INFO - Request Parameters - Page 1:
2025-05-17 22:30:42,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 22:30:42,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 22:30:50,924 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E7F0CEBC-7BAE-7CE6-9EDB-2A49E19BEEB9 Response: {'code': 'ServiceUnavailable', 'requestid': 'E7F0CEBC-7BAE-7CE6-9EDB-2A49E19BEEB9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E7F0CEBC-7BAE-7CE6-9EDB-2A49E19BEEB9)
2025-05-17 22:30:50,924 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-17 22:31:50,936 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 22:31:50,936 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 22:31:50,936 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 22:31:51,018 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 642 条记录
2025-05-17 22:31:51,018 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 22:31:51,023 - INFO - 开始处理日期: 2025-05-16
2025-05-17 22:31:51,024 - INFO - Request Parameters - Page 1:
2025-05-17 22:31:51,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 22:31:51,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 22:31:51,839 - INFO - Response - Page 1:
2025-05-17 22:31:51,839 - INFO - 第 1 页获取到 100 条记录
2025-05-17 22:31:52,040 - INFO - Request Parameters - Page 2:
2025-05-17 22:31:52,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 22:31:52,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 22:31:52,821 - INFO - Response - Page 2:
2025-05-17 22:31:52,822 - INFO - 第 2 页获取到 100 条记录
2025-05-17 22:31:53,022 - INFO - Request Parameters - Page 3:
2025-05-17 22:31:53,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 22:31:53,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 22:31:53,833 - INFO - Response - Page 3:
2025-05-17 22:31:53,834 - INFO - 第 3 页获取到 100 条记录
2025-05-17 22:31:54,034 - INFO - Request Parameters - Page 4:
2025-05-17 22:31:54,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 22:31:54,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 22:31:54,934 - INFO - Response - Page 4:
2025-05-17 22:31:54,934 - INFO - 第 4 页获取到 100 条记录
2025-05-17 22:31:55,134 - INFO - Request Parameters - Page 5:
2025-05-17 22:31:55,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 22:31:55,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 22:31:55,933 - INFO - Response - Page 5:
2025-05-17 22:31:55,933 - INFO - 第 5 页获取到 100 条记录
2025-05-17 22:31:56,133 - INFO - Request Parameters - Page 6:
2025-05-17 22:31:56,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 22:31:56,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 22:31:56,744 - INFO - Response - Page 6:
2025-05-17 22:31:56,745 - INFO - 第 6 页获取到 12 条记录
2025-05-17 22:31:56,946 - INFO - 查询完成，共获取到 512 条记录
2025-05-17 22:31:56,946 - INFO - 获取到 512 条表单数据
2025-05-17 22:31:56,954 - INFO - 当前日期 2025-05-16 有 512 条MySQL数据需要处理
2025-05-17 22:31:56,961 - INFO - 日期 2025-05-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 22:31:56,962 - INFO - 开始处理日期: 2025-05-17
2025-05-17 22:31:56,962 - INFO - Request Parameters - Page 1:
2025-05-17 22:31:56,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 22:31:56,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 22:31:57,749 - INFO - Response - Page 1:
2025-05-17 22:31:57,749 - INFO - 第 1 页获取到 54 条记录
2025-05-17 22:31:57,949 - INFO - 查询完成，共获取到 54 条记录
2025-05-17 22:31:57,949 - INFO - 获取到 54 条表单数据
2025-05-17 22:31:57,951 - INFO - 当前日期 2025-05-17 有 130 条MySQL数据需要处理
2025-05-17 22:31:57,952 - INFO - 开始批量插入 76 条新记录
2025-05-17 22:31:58,210 - INFO - 批量插入响应状态码: 200
2025-05-17 22:31:58,210 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 17 May 2025 14:31:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3660', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A4A19669-F484-7C57-B9A2-A78758A1781E', 'x-acs-trace-id': 'db0635514eb7c5b1166ba6de5c48906b', 'etag': '3XiOFqqUQbm9YGY2s5aOvHQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-17 22:31:58,210 - INFO - 批量插入响应体: {'result': ['FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMGE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMHE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMIE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMJE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMKE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMLE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMME', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMNE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMOE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMPE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMQE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMRE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMSE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMTE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMUE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMVE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMWE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMXE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMYE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMZE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM0F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM1F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM2F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM3F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM4F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM5F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM6F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM7F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM8F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM9F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMAF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMBF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMCF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMDF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMEF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMFF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMGF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMHF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMIF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMJF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMKF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMLF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMMF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMNF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMOF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMPF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMQF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMRF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMSF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMTF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMUF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMVF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMWF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMXF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMYF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMZF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM0G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM1G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM2G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM3G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM4G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM5G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM6G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM7G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM8G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM9G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMAG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMBG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMCG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMDG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMEG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMFG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMGG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMHG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMIG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMJG']}
2025-05-17 22:31:58,210 - INFO - 批量插入表单数据成功，批次 1，共 76 条记录
2025-05-17 22:31:58,210 - INFO - 成功插入的数据ID: ['FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMGE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMHE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMIE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMJE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMKE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMLE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMME', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMNE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMOE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMPE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMQE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMRE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMSE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMTE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMUE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMVE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMWE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMXE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMYE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMZE', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM0F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM1F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM2F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM3F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM4F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM5F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM6F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM7F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM8F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM9F', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMAF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMBF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMCF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMDF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMEF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMFF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMGF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMHF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMIF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMJF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMKF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMLF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMMF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMNF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMOF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMPF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMQF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMRF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMSF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMTF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMUF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMVF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMWF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMXF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMYF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMZF', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM0G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM1G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM2G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM3G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM4G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM5G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM6G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM7G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM8G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAM9G', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMAG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMBG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMCG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMDG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMEG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMFG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMGG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMHG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMIG', 'FINST-JAC66MB12FHVSG419WSTPD6ESABN3WP0TBSAMJG']
2025-05-17 22:32:03,211 - INFO - 批量插入完成，共 76 条记录
2025-05-17 22:32:03,211 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 76 条，错误: 0 条
2025-05-17 22:32:03,211 - INFO - 数据同步完成！更新: 0 条，插入: 76 条，错误: 0 条
2025-05-17 22:32:03,211 - INFO - 同步完成
2025-05-17 23:30:33,920 - INFO - 使用默认增量同步（当天更新数据）
2025-05-17 23:30:33,921 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 23:30:33,921 - INFO - 查询参数: ('2025-05-17',)
2025-05-17 23:30:33,995 - INFO - MySQL查询成功，增量数据（日期: 2025-05-17），共获取 309 条记录
2025-05-17 23:30:33,995 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 23:30:33,998 - INFO - 开始处理日期: 2025-05-16
2025-05-17 23:30:34,001 - INFO - Request Parameters - Page 1:
2025-05-17 23:30:34,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 23:30:34,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 23:30:42,126 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AC288CE8-E0B3-7FE2-A0A4-D4BCEB2AF88D Response: {'code': 'ServiceUnavailable', 'requestid': 'AC288CE8-E0B3-7FE2-A0A4-D4BCEB2AF88D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AC288CE8-E0B3-7FE2-A0A4-D4BCEB2AF88D)
2025-05-17 23:30:42,126 - INFO - 开始处理日期: 2025-05-17
2025-05-17 23:30:42,126 - INFO - Request Parameters - Page 1:
2025-05-17 23:30:42,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 23:30:42,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 23:30:49,490 - INFO - Response - Page 1:
2025-05-17 23:30:49,490 - INFO - 第 1 页获取到 100 条记录
2025-05-17 23:30:49,690 - INFO - Request Parameters - Page 2:
2025-05-17 23:30:49,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 23:30:49,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 23:30:57,805 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DEC0B9EC-DF4F-7CD8-9CAC-BC159BD19DD2 Response: {'code': 'ServiceUnavailable', 'requestid': 'DEC0B9EC-DF4F-7CD8-9CAC-BC159BD19DD2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DEC0B9EC-DF4F-7CD8-9CAC-BC159BD19DD2)
2025-05-17 23:30:57,805 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-17 23:31:57,807 - INFO - 开始同步昨天与今天的销售数据: 2025-05-16 至 2025-05-17
2025-05-17 23:31:57,807 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-17 23:31:57,807 - INFO - 查询参数: ('2025-05-16', '2025-05-17')
2025-05-17 23:31:57,889 - INFO - MySQL查询成功，时间段: 2025-05-16 至 2025-05-17，共获取 645 条记录
2025-05-17 23:31:57,889 - INFO - 获取到 2 个日期需要处理: ['2025-05-16', '2025-05-17']
2025-05-17 23:31:57,894 - INFO - 开始处理日期: 2025-05-16
2025-05-17 23:31:57,895 - INFO - Request Parameters - Page 1:
2025-05-17 23:31:57,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 23:31:57,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 23:31:58,767 - INFO - Response - Page 1:
2025-05-17 23:31:58,768 - INFO - 第 1 页获取到 100 条记录
2025-05-17 23:31:58,968 - INFO - Request Parameters - Page 2:
2025-05-17 23:31:58,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 23:31:58,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 23:31:59,729 - INFO - Response - Page 2:
2025-05-17 23:31:59,729 - INFO - 第 2 页获取到 100 条记录
2025-05-17 23:31:59,929 - INFO - Request Parameters - Page 3:
2025-05-17 23:31:59,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 23:31:59,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 23:32:00,708 - INFO - Response - Page 3:
2025-05-17 23:32:00,708 - INFO - 第 3 页获取到 100 条记录
2025-05-17 23:32:00,909 - INFO - Request Parameters - Page 4:
2025-05-17 23:32:00,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 23:32:00,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 23:32:01,699 - INFO - Response - Page 4:
2025-05-17 23:32:01,699 - INFO - 第 4 页获取到 100 条记录
2025-05-17 23:32:01,900 - INFO - Request Parameters - Page 5:
2025-05-17 23:32:01,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 23:32:01,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 23:32:02,949 - INFO - Response - Page 5:
2025-05-17 23:32:02,949 - INFO - 第 5 页获取到 100 条记录
2025-05-17 23:32:03,150 - INFO - Request Parameters - Page 6:
2025-05-17 23:32:03,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 23:32:03,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 23:32:03,762 - INFO - Response - Page 6:
2025-05-17 23:32:03,762 - INFO - 第 6 页获取到 12 条记录
2025-05-17 23:32:03,962 - INFO - 查询完成，共获取到 512 条记录
2025-05-17 23:32:03,962 - INFO - 获取到 512 条表单数据
2025-05-17 23:32:03,971 - INFO - 当前日期 2025-05-16 有 512 条MySQL数据需要处理
2025-05-17 23:32:03,979 - INFO - 日期 2025-05-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-17 23:32:03,979 - INFO - 开始处理日期: 2025-05-17
2025-05-17 23:32:03,979 - INFO - Request Parameters - Page 1:
2025-05-17 23:32:03,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 23:32:03,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 23:32:04,780 - INFO - Response - Page 1:
2025-05-17 23:32:04,780 - INFO - 第 1 页获取到 100 条记录
2025-05-17 23:32:04,982 - INFO - Request Parameters - Page 2:
2025-05-17 23:32:04,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 23:32:04,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 23:32:05,618 - INFO - Response - Page 2:
2025-05-17 23:32:05,618 - INFO - 第 2 页获取到 30 条记录
2025-05-17 23:32:05,819 - INFO - 查询完成，共获取到 130 条记录
2025-05-17 23:32:05,819 - INFO - 获取到 130 条表单数据
2025-05-17 23:32:05,821 - INFO - 当前日期 2025-05-17 有 133 条MySQL数据需要处理
2025-05-17 23:32:05,824 - INFO - 开始批量插入 3 条新记录
2025-05-17 23:32:05,995 - INFO - 批量插入响应状态码: 200
2025-05-17 23:32:05,995 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 17 May 2025 15:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B2E0824D-A061-7083-9B70-3FA13D49D3C1', 'x-acs-trace-id': 'eb4e4dfead69d5697be5ceb34c539fee', 'etag': '1nUIZE0S1s1Hn4QJCmg6zDA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-17 23:32:05,995 - INFO - 批量插入响应体: {'result': ['FINST-68E66TC13GGVAH1DBWY9Z39R81Y33A3EYDSAM0U', 'FINST-68E66TC13GGVAH1DBWY9Z39R81Y33A3EYDSAM1U', 'FINST-68E66TC13GGVAH1DBWY9Z39R81Y33A3EYDSAM2U']}
2025-05-17 23:32:05,995 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-05-17 23:32:05,995 - INFO - 成功插入的数据ID: ['FINST-68E66TC13GGVAH1DBWY9Z39R81Y33A3EYDSAM0U', 'FINST-68E66TC13GGVAH1DBWY9Z39R81Y33A3EYDSAM1U', 'FINST-68E66TC13GGVAH1DBWY9Z39R81Y33A3EYDSAM2U']
2025-05-17 23:32:10,996 - INFO - 批量插入完成，共 3 条记录
2025-05-17 23:32:10,996 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-05-17 23:32:10,996 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 0 条
2025-05-17 23:32:10,997 - INFO - 同步完成
