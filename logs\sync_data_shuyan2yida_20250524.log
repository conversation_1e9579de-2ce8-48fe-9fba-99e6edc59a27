2025-05-24 08:00:03,656 - INFO - ==================================================
2025-05-24 08:00:03,656 - INFO - 程序启动 - 版本 v1.0.0
2025-05-24 08:00:03,656 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250524.log
2025-05-24 08:00:03,656 - INFO - ==================================================
2025-05-24 08:00:03,656 - INFO - 程序入口点: __main__
2025-05-24 08:00:03,656 - INFO - ==================================================
2025-05-24 08:00:03,656 - INFO - 程序启动 - 版本 v1.0.1
2025-05-24 08:00:03,656 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250524.log
2025-05-24 08:00:03,656 - INFO - ==================================================
2025-05-24 08:00:03,984 - INFO - 数据库文件已存在: data\sales_data.db
2025-05-24 08:00:03,984 - INFO - sales_data表已存在，无需创建
2025-05-24 08:00:03,984 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-05-24 08:00:03,984 - INFO - DataSyncManager初始化完成
2025-05-24 08:00:03,984 - INFO - 未提供日期参数，使用默认值
2025-05-24 08:00:03,984 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-05-24 08:00:03,984 - INFO - 开始综合数据同步流程...
2025-05-24 08:00:03,984 - INFO - 正在获取数衍平台日销售数据...
2025-05-24 08:00:03,984 - INFO - 查询数衍平台数据，时间段为: 2025-03-24, 2025-05-23
2025-05-24 08:00:03,984 - INFO - 正在获取********至********的数据
2025-05-24 08:00:03,984 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-24 08:00:03,984 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2D477AD3074A04EAF99941F7FB727714'}
2025-05-24 08:00:08,500 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-24 08:00:08,516 - INFO - 过滤后保留 1559 条记录
2025-05-24 08:00:10,531 - INFO - 正在获取********至********的数据
2025-05-24 08:00:10,531 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-24 08:00:10,531 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5D42F20F39876FD119F4D61202A5513C'}
2025-05-24 08:00:13,844 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-24 08:00:13,859 - INFO - 过滤后保留 1515 条记录
2025-05-24 08:00:15,859 - INFO - 正在获取********至********的数据
2025-05-24 08:00:15,859 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-24 08:00:15,859 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '63D798A9801A8FECCA4759EB37B1D974'}
2025-05-24 08:00:18,625 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-24 08:00:18,641 - INFO - 过滤后保留 1500 条记录
2025-05-24 08:00:20,656 - INFO - 正在获取********至********的数据
2025-05-24 08:00:20,656 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-24 08:00:20,656 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F48F5A38B6F8015BB422B67E4A51A6F2'}
2025-05-24 08:00:22,953 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-24 08:00:22,969 - INFO - 过滤后保留 1506 条记录
2025-05-24 08:00:24,984 - INFO - 正在获取********至********的数据
2025-05-24 08:00:24,984 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-24 08:00:24,984 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '116476ADC325955CC200DD3176CB238B'}
2025-05-24 08:00:28,359 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-24 08:00:28,375 - INFO - 过滤后保留 1482 条记录
2025-05-24 08:00:30,390 - INFO - 正在获取********至********的数据
2025-05-24 08:00:30,390 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-24 08:00:30,390 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '67638657EC24FA9CFC1986AA8224F772'}
2025-05-24 08:00:33,750 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-24 08:00:33,765 - INFO - 过滤后保留 1490 条记录
2025-05-24 08:00:35,781 - INFO - 正在获取********至********的数据
2025-05-24 08:00:35,781 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-24 08:00:35,781 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '04A91975476AB46C8D73F0F9FD1554B4'}
2025-05-24 08:00:38,187 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-24 08:00:38,203 - INFO - 过滤后保留 1473 条记录
2025-05-24 08:00:40,218 - INFO - 正在获取********至********的数据
2025-05-24 08:00:40,218 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-24 08:00:40,218 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1F91EC616553B91BE28CF6550C23467E'}
2025-05-24 08:00:42,390 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-24 08:00:42,406 - INFO - 过滤后保留 1478 条记录
2025-05-24 08:00:44,422 - INFO - 正在获取********至********的数据
2025-05-24 08:00:44,422 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-24 08:00:44,422 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1CD74AB790409DC5C637BA7D7D94B737'}
2025-05-24 08:00:45,906 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-24 08:00:45,922 - INFO - 过滤后保留 1035 条记录
2025-05-24 08:00:47,937 - INFO - 开始保存数据到SQLite数据库，共 13038 条记录待处理
2025-05-24 08:00:48,500 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HOE1A3UTAESD606LODAUCEHAF001M2A, sale_time=2025-05-01
2025-05-24 08:00:48,500 - INFO - 变更字段: amount: 13369 -> 13575, count: 132 -> 133, instore_amount: 3849.77 -> 4055.97, instore_count: 25 -> 26
2025-05-24 08:00:48,500 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE0PKSKM647Q2OV4FVC7EC0014AT, sale_time=2025-05-01
2025-05-24 08:00:48,500 - INFO - 变更字段: recommend_amount: 4351.6 -> 4429.6, daily_bill_amount: 4351.6 -> 4429.6
2025-05-24 08:00:48,531 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEBHLLHVNF0I86N3H2U102001F98, sale_time=2025-05-03
2025-05-24 08:00:48,531 - INFO - 变更字段: amount: 10595 -> 10728, count: 41 -> 42, instore_amount: 10848.8 -> 10981.9, instore_count: 40 -> 41
2025-05-24 08:00:48,593 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEBHLLHVNF0I86N3H2U102001F98, sale_time=2025-05-07
2025-05-24 08:00:48,593 - INFO - 变更字段: amount: 1567 -> 1784, count: 12 -> 13, instore_amount: 1525.6 -> 1743.4, instore_count: 9 -> 10
2025-05-24 08:00:48,609 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDP34FLR400I86N3H2U1MG001EVM, sale_time=2025-05-11
2025-05-24 08:00:48,609 - INFO - 变更字段: recommend_amount: 30290.26 -> 31109.26, amount: 30290 -> 31109, count: 60 -> 61, instore_amount: 30290.26 -> 31109.26, instore_count: 60 -> 61
2025-05-24 08:00:48,640 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HOE1A3UTAESD606LODAUCEHAF001M2A, sale_time=2025-05-18
2025-05-24 08:00:48,640 - INFO - 变更字段: amount: 4773 -> 4778, count: 66 -> 67, instore_amount: 1987.96 -> 1992.96, instore_count: 35 -> 36
2025-05-24 08:00:48,687 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FHI5VTHC3RHRI7Q2OVAE57DT4001C39, sale_time=2025-05-17
2025-05-24 08:00:48,687 - INFO - 变更字段: amount: 46011 -> 46548, count: 161 -> 162, instore_amount: 46011.0 -> 46548.0, instore_count: 161 -> 162
2025-05-24 08:00:48,687 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FHI5VTHC3RHRI7Q2OVAE57DT4001C39, sale_time=2025-05-15
2025-05-24 08:00:48,687 - INFO - 变更字段: amount: 28888 -> 28996, count: 102 -> 103, instore_amount: 28888.0 -> 28996.0, instore_count: 102 -> 103
2025-05-24 08:00:48,687 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-18
2025-05-24 08:00:48,687 - INFO - 变更字段: recommend_amount: 4371.91 -> 4365.88, amount: 4371 -> 4365
2025-05-24 08:00:48,687 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE38FF3LOL6AJB6QM8HA820011R0, sale_time=2025-05-18
2025-05-24 08:00:48,687 - INFO - 变更字段: recommend_amount: 20956.0 -> 21304.08, daily_bill_amount: 20956.0 -> 21304.08
2025-05-24 08:00:48,687 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE38FF3LOL6AJB6QM8HA820011R0, sale_time=2025-05-17
2025-05-24 08:00:48,687 - INFO - 变更字段: recommend_amount: 15257.9 -> 15335.9, daily_bill_amount: 15257.9 -> 15335.9
2025-05-24 08:00:48,687 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE38FF3LOL6AJB6QM8HA820011R0, sale_time=2025-05-16
2025-05-24 08:00:48,687 - INFO - 变更字段: recommend_amount: 16901.8 -> 17013.66, daily_bill_amount: 16901.8 -> 17013.66
2025-05-24 08:00:48,703 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-05-18
2025-05-24 08:00:48,703 - INFO - 变更字段: recommend_amount: 35906.27 -> 28314.08, daily_bill_amount: 35906.27 -> 28314.08
2025-05-24 08:00:48,703 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-05-17
2025-05-24 08:00:48,703 - INFO - 变更字段: recommend_amount: 40861.75 -> 35906.27, daily_bill_amount: 40861.75 -> 35906.27
2025-05-24 08:00:48,703 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-22
2025-05-24 08:00:48,703 - INFO - 变更字段: recommend_amount: 0.0 -> 5461.6, daily_bill_amount: 0.0 -> 5461.6
2025-05-24 08:00:48,703 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HINIR4GO8E5NM5U25UDHUFEGO001L3K, sale_time=2025-05-22
2025-05-24 08:00:48,703 - INFO - 变更字段: recommend_amount: 2944.37 -> 2896.17, amount: 2944 -> 2896
2025-05-24 08:00:48,718 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDV0D9P6J27Q2OV4FVC7DG0014A1, sale_time=2025-05-22
2025-05-24 08:00:48,718 - INFO - 变更字段: recommend_amount: 3536.0 -> 3515.0, daily_bill_amount: 3536.0 -> 3515.0
2025-05-24 08:00:48,718 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDSD911K3E7Q2OV4FVC7C800148P, sale_time=2025-05-22
2025-05-24 08:00:48,718 - INFO - 变更字段: amount: 3105 -> 3158, count: 140 -> 142, instore_amount: 3198.71 -> 3251.21, instore_count: 140 -> 142
2025-05-24 08:00:48,718 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDMMR23CHP7Q2OV4FVC79C00145T, sale_time=2025-05-22
2025-05-24 08:00:48,718 - INFO - 变更字段: amount: 1409 -> 1430, count: 47 -> 48, online_amount: 1046.07 -> 1066.37, online_count: 35 -> 36
2025-05-24 08:00:48,718 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-05-22
2025-05-24 08:00:48,718 - INFO - 变更字段: recommend_amount: 0.0 -> 9202.7, daily_bill_amount: 0.0 -> 9202.7, count: 5 -> 6, instore_count: 5 -> 6
2025-05-24 08:00:48,718 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S, sale_time=2025-05-22
2025-05-24 08:00:48,718 - INFO - 变更字段: recommend_amount: 13070.0 -> 13356.0, amount: 13070 -> 13356, instore_amount: 13070.0 -> 13356.0
2025-05-24 08:00:48,718 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-05-22
2025-05-24 08:00:48,718 - INFO - 变更字段: amount: 50 -> 100, count: 4 -> 5, instore_amount: 63.1 -> 113.65, instore_count: 4 -> 5
2025-05-24 08:00:48,734 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-22
2025-05-24 08:00:48,734 - INFO - 变更字段: count: 99 -> 100, online_count: 83 -> 84
2025-05-24 08:00:48,734 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-22
2025-05-24 08:00:48,734 - INFO - 变更字段: recommend_amount: 9565.34 -> 9636.74, amount: 9565 -> 9636, count: 154 -> 156, instore_amount: 8443.15 -> 8514.55, instore_count: 130 -> 132
2025-05-24 08:00:48,734 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-21
2025-05-24 08:00:48,734 - INFO - 变更字段: recommend_amount: 8173.03 -> 8220.63, amount: 8173 -> 8220, count: 167 -> 168, instore_amount: 7158.3 -> 7205.9, instore_count: 141 -> 142
2025-05-24 08:00:48,734 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-05-22
2025-05-24 08:00:48,734 - INFO - 变更字段: recommend_amount: 1100.17 -> 1103.87, amount: 1100 -> 1103, count: 70 -> 71, online_amount: 791.67 -> 795.37, online_count: 53 -> 54
2025-05-24 08:00:48,734 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-22
2025-05-24 08:00:48,734 - INFO - 变更字段: recommend_amount: 5040.61 -> 5062.41, amount: 5040 -> 5062, count: 244 -> 247, online_amount: 3784.12 -> 3805.92, online_count: 187 -> 190
2025-05-24 08:00:48,734 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-05-22
2025-05-24 08:00:48,734 - INFO - 变更字段: recommend_amount: 0.0 -> 4774.0, daily_bill_amount: 0.0 -> 4774.0
2025-05-24 08:00:48,734 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-05-22
2025-05-24 08:00:48,734 - INFO - 变更字段: recommend_amount: 0.0 -> 17306.93, daily_bill_amount: 0.0 -> 17306.93
2025-05-24 08:00:48,734 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEAIN5DMKK0I86N3H2U1VH001F8N, sale_time=2025-05-22
2025-05-24 08:00:48,734 - INFO - 变更字段: amount: -6840 -> -6842, online_amount: 565.65 -> 563.45
2025-05-24 08:00:48,734 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-05-22
2025-05-24 08:00:48,734 - INFO - 变更字段: amount: 20322 -> 20527, count: 155 -> 156, instore_amount: 8965.1 -> 9170.1, instore_count: 53 -> 54
2025-05-24 08:00:48,750 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-05-22
2025-05-24 08:00:48,750 - INFO - 变更字段: amount: 28086 -> 28367, count: 208 -> 209, instore_amount: 20131.43 -> 20412.43, instore_count: 92 -> 93
2025-05-24 08:00:48,750 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE2CVHLBFV0I86N3H2U1RH001F4N, sale_time=2025-05-22
2025-05-24 08:00:48,750 - INFO - 变更字段: amount: 7302 -> 9832, count: 43 -> 67, online_amount: 0.0 -> 2530.1, online_count: 0 -> 24
2025-05-24 08:00:48,750 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0A2N9KG60I86N3H2U1QB001F3H, sale_time=2025-05-22
2025-05-24 08:00:48,750 - INFO - 变更字段: amount: 4822 -> 4814
2025-05-24 08:00:48,750 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVBILA3OJ0I86N3H2U1PQ001F30, sale_time=2025-05-22
2025-05-24 08:00:48,750 - INFO - 变更字段: amount: 11835 -> 11825
2025-05-24 08:00:48,750 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-05-22
2025-05-24 08:00:48,750 - INFO - 变更字段: recommend_amount: 0.0 -> 2055.72, daily_bill_amount: 0.0 -> 2055.72, amount: 60 -> 684, count: 4 -> 73, instore_amount: 60.4 -> 713.2, instore_count: 4 -> 73
2025-05-24 08:00:48,750 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEQ2M9E710I86N3H2U1H1001EQ7, sale_time=2025-05-22
2025-05-24 08:00:48,750 - INFO - 变更字段: amount: 2931 -> 3093, count: 28 -> 29, online_amount: 49.4 -> 212.0, online_count: 1 -> 2
2025-05-24 08:00:48,750 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-22
2025-05-24 08:00:48,750 - INFO - 变更字段: amount: 10967 -> 11573, count: 11 -> 12, instore_amount: 10967.5 -> 11573.7, instore_count: 11 -> 12
2025-05-24 08:00:48,750 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9G31FV3GL0I86N3H2U190001EI6, sale_time=2025-05-22
2025-05-24 08:00:48,750 - INFO - 变更字段: amount: 20597 -> 23996, count: 3 -> 4, instore_amount: 20597.0 -> 23996.0, instore_count: 3 -> 4
2025-05-24 08:00:48,750 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9D9OBM41R0I86N3H2U17K001EGQ, sale_time=2025-05-22
2025-05-24 08:00:48,750 - INFO - 变更字段: recommend_amount: 0.0 -> 6697.92, daily_bill_amount: 0.0 -> 6697.92
2025-05-24 08:00:48,750 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-05-22
2025-05-24 08:00:48,750 - INFO - 变更字段: amount: 3718 -> 3792, count: 212 -> 219, online_amount: 3595.29 -> 3669.29, online_count: 201 -> 208
2025-05-24 08:00:48,765 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDTTV5HD0Q0I86N3H2U11P001EAV, sale_time=2025-05-22
2025-05-24 08:00:48,765 - INFO - 变更字段: amount: 6638 -> 6607
2025-05-24 08:00:48,765 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-22
2025-05-24 08:00:48,765 - INFO - 变更字段: amount: 5450 -> 5470, count: 344 -> 346, instore_amount: 3925.78 -> 3934.43, instore_count: 246 -> 247, online_amount: 1529.29 -> 1541.29, online_count: 98 -> 99
2025-05-24 08:00:48,765 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-21
2025-05-24 08:00:48,765 - INFO - 变更字段: instore_amount: 5227.92 -> 5250.92, instore_count: 332 -> 333, online_amount: 1406.7 -> 1383.7, online_count: 108 -> 107
2025-05-24 08:00:48,765 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-20
2025-05-24 08:00:48,765 - INFO - 变更字段: amount: 7700 -> 7687
2025-05-24 08:00:48,765 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-22
2025-05-24 08:00:48,765 - INFO - 变更字段: amount: 2774 -> 2820, count: 43 -> 44, online_amount: 938.37 -> 984.65, online_count: 17 -> 18
2025-05-24 08:00:48,765 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDNMK1P3900I86N3H2U1UO001E7U, sale_time=2025-05-22
2025-05-24 08:00:48,765 - INFO - 变更字段: amount: 3013 -> 3056, count: 74 -> 75, instore_amount: 2240.46 -> 2283.79, instore_count: 53 -> 54
2025-05-24 08:00:48,765 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-05-22
2025-05-24 08:00:48,765 - INFO - 变更字段: recommend_amount: 8232.43 -> 8272.44, amount: 8232 -> 8272, count: 324 -> 328, online_amount: 8862.53 -> 8921.24, online_count: 324 -> 328
2025-05-24 08:00:48,765 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-05-21
2025-05-24 08:00:48,765 - INFO - 变更字段: recommend_amount: 13895.53 -> 14615.53, amount: 13895 -> 14615, count: 462 -> 463, online_amount: 14126.79 -> 14846.79, online_count: 462 -> 463
2025-05-24 08:00:48,765 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0NLURMV9D3IKSIOCDI7R2001G23, sale_time=2025-05-22
2025-05-24 08:00:48,765 - INFO - 变更字段: amount: 1130 -> 1172, count: 17 -> 19, instore_amount: 933.37 -> 975.07, instore_count: 9 -> 11
2025-05-24 08:00:48,765 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-05-22
2025-05-24 08:00:48,765 - INFO - 变更字段: amount: 13850 -> 14457, count: 200 -> 202, instore_amount: 12025.68 -> 12633.18, instore_count: 121 -> 123
2025-05-24 08:00:48,765 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE38FF3LOL6AJB6QM8HA820011R0, sale_time=2025-05-22
2025-05-24 08:00:48,765 - INFO - 变更字段: recommend_amount: 0.0 -> 14039.7, daily_bill_amount: 0.0 -> 14039.7
2025-05-24 08:00:48,781 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE38FF3LOL6AJB6QM8HA820011R0, sale_time=2025-05-19
2025-05-24 08:00:48,781 - INFO - 变更字段: recommend_amount: 11819.0 -> 12182.68, daily_bill_amount: 11819.0 -> 12182.68
2025-05-24 08:00:48,781 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-05-22
2025-05-24 08:00:48,781 - INFO - 变更字段: amount: 32404 -> 37599, count: 169 -> 174, instore_amount: 30104.6 -> 35299.2, instore_count: 146 -> 151
2025-05-24 08:00:48,781 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-05-19
2025-05-24 08:00:48,781 - INFO - 变更字段: recommend_amount: 28314.08 -> 22126.58, daily_bill_amount: 28314.08 -> 22126.58
2025-05-24 08:00:48,781 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPK0KE3MN6E7AERKQ83JB001UND, sale_time=2025-05-22
2025-05-24 08:00:48,781 - INFO - 变更字段: amount: 5440 -> 5450, count: 190 -> 191, instore_amount: 1826.68 -> 1836.73, instore_count: 57 -> 58
2025-05-24 08:00:49,031 - INFO - SQLite数据保存完成，统计信息：
2025-05-24 08:00:49,031 - INFO - - 总记录数: 13038
2025-05-24 08:00:49,031 - INFO - - 成功插入: 205
2025-05-24 08:00:49,031 - INFO - - 成功更新: 56
2025-05-24 08:00:49,031 - INFO - - 无需更新: 12777
2025-05-24 08:00:49,031 - INFO - - 处理失败: 0
2025-05-24 08:00:54,578 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_20250524.xlsx
2025-05-24 08:00:54,593 - INFO - 成功获取数衍平台数据，共 13038 条记录
2025-05-24 08:00:54,593 - INFO - 正在更新SQLite月度汇总数据...
2025-05-24 08:00:54,609 - INFO - 月度数据sqllite清空完成
2025-05-24 08:00:54,875 - INFO - 月度汇总数据更新完成，处理了 1192 条汇总记录
2025-05-24 08:00:54,875 - INFO - 成功更新月度汇总数据，共 1192 条记录
2025-05-24 08:00:54,875 - INFO - 正在获取宜搭日销售表单数据...
2025-05-24 08:00:54,875 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-24 00:00:00 至 2025-05-23 23:59:59
2025-05-24 08:00:54,875 - INFO - 查询分段 1: 2025-03-24 至 2025-03-30
2025-05-24 08:00:54,875 - INFO - 查询日期范围: 2025-03-24 至 2025-03-30，使用分页查询，每页 100 条记录
2025-05-24 08:00:54,875 - INFO - Request Parameters - Page 1:
2025-05-24 08:00:54,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:00:54,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:01,015 - INFO - API请求耗时: 6141ms
2025-05-24 08:01:01,015 - INFO - Response - Page 1
2025-05-24 08:01:01,015 - INFO - 第 1 页获取到 100 条记录
2025-05-24 08:01:01,515 - INFO - Request Parameters - Page 2:
2025-05-24 08:01:01,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:01,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:02,250 - INFO - API请求耗时: 734ms
2025-05-24 08:01:02,250 - INFO - Response - Page 2
2025-05-24 08:01:02,250 - INFO - 第 2 页获取到 100 条记录
2025-05-24 08:01:02,750 - INFO - Request Parameters - Page 3:
2025-05-24 08:01:02,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:02,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:06,515 - INFO - API请求耗时: 3766ms
2025-05-24 08:01:06,515 - INFO - Response - Page 3
2025-05-24 08:01:06,515 - INFO - 第 3 页获取到 100 条记录
2025-05-24 08:01:07,031 - INFO - Request Parameters - Page 4:
2025-05-24 08:01:07,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:07,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:07,781 - INFO - API请求耗时: 750ms
2025-05-24 08:01:07,781 - INFO - Response - Page 4
2025-05-24 08:01:07,781 - INFO - 第 4 页获取到 100 条记录
2025-05-24 08:01:08,296 - INFO - Request Parameters - Page 5:
2025-05-24 08:01:08,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:08,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:08,906 - INFO - API请求耗时: 609ms
2025-05-24 08:01:08,906 - INFO - Response - Page 5
2025-05-24 08:01:08,906 - INFO - 第 5 页获取到 100 条记录
2025-05-24 08:01:09,421 - INFO - Request Parameters - Page 6:
2025-05-24 08:01:09,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:09,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:10,140 - INFO - API请求耗时: 719ms
2025-05-24 08:01:10,140 - INFO - Response - Page 6
2025-05-24 08:01:10,140 - INFO - 第 6 页获取到 100 条记录
2025-05-24 08:01:10,656 - INFO - Request Parameters - Page 7:
2025-05-24 08:01:10,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:10,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:11,359 - INFO - API请求耗时: 703ms
2025-05-24 08:01:11,359 - INFO - Response - Page 7
2025-05-24 08:01:11,359 - INFO - 第 7 页获取到 100 条记录
2025-05-24 08:01:11,875 - INFO - Request Parameters - Page 8:
2025-05-24 08:01:11,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:11,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:12,546 - INFO - API请求耗时: 672ms
2025-05-24 08:01:12,546 - INFO - Response - Page 8
2025-05-24 08:01:12,546 - INFO - 第 8 页获取到 100 条记录
2025-05-24 08:01:13,046 - INFO - Request Parameters - Page 9:
2025-05-24 08:01:13,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:13,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:13,812 - INFO - API请求耗时: 766ms
2025-05-24 08:01:13,812 - INFO - Response - Page 9
2025-05-24 08:01:13,812 - INFO - 第 9 页获取到 100 条记录
2025-05-24 08:01:14,312 - INFO - Request Parameters - Page 10:
2025-05-24 08:01:14,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:14,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:15,000 - INFO - API请求耗时: 687ms
2025-05-24 08:01:15,000 - INFO - Response - Page 10
2025-05-24 08:01:15,000 - INFO - 第 10 页获取到 100 条记录
2025-05-24 08:01:15,515 - INFO - Request Parameters - Page 11:
2025-05-24 08:01:15,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:15,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:16,265 - INFO - API请求耗时: 750ms
2025-05-24 08:01:16,265 - INFO - Response - Page 11
2025-05-24 08:01:16,265 - INFO - 第 11 页获取到 100 条记录
2025-05-24 08:01:16,765 - INFO - Request Parameters - Page 12:
2025-05-24 08:01:16,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:16,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:17,499 - INFO - API请求耗时: 734ms
2025-05-24 08:01:17,499 - INFO - Response - Page 12
2025-05-24 08:01:17,499 - INFO - 第 12 页获取到 100 条记录
2025-05-24 08:01:18,000 - INFO - Request Parameters - Page 13:
2025-05-24 08:01:18,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:18,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:18,671 - INFO - API请求耗时: 672ms
2025-05-24 08:01:18,671 - INFO - Response - Page 13
2025-05-24 08:01:18,671 - INFO - 第 13 页获取到 100 条记录
2025-05-24 08:01:19,187 - INFO - Request Parameters - Page 14:
2025-05-24 08:01:19,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:19,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:19,890 - INFO - API请求耗时: 703ms
2025-05-24 08:01:19,890 - INFO - Response - Page 14
2025-05-24 08:01:19,890 - INFO - 第 14 页获取到 100 条记录
2025-05-24 08:01:20,390 - INFO - Request Parameters - Page 15:
2025-05-24 08:01:20,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:20,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:21,015 - INFO - API请求耗时: 625ms
2025-05-24 08:01:21,015 - INFO - Response - Page 15
2025-05-24 08:01:21,031 - INFO - 第 15 页获取到 100 条记录
2025-05-24 08:01:21,546 - INFO - Request Parameters - Page 16:
2025-05-24 08:01:21,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:21,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:22,265 - INFO - API请求耗时: 719ms
2025-05-24 08:01:22,265 - INFO - Response - Page 16
2025-05-24 08:01:22,265 - INFO - 第 16 页获取到 100 条记录
2025-05-24 08:01:22,781 - INFO - Request Parameters - Page 17:
2025-05-24 08:01:22,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:22,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:23,484 - INFO - API请求耗时: 703ms
2025-05-24 08:01:23,484 - INFO - Response - Page 17
2025-05-24 08:01:23,484 - INFO - 第 17 页获取到 100 条记录
2025-05-24 08:01:23,984 - INFO - Request Parameters - Page 18:
2025-05-24 08:01:23,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:23,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:24,687 - INFO - API请求耗时: 703ms
2025-05-24 08:01:24,687 - INFO - Response - Page 18
2025-05-24 08:01:24,687 - INFO - 第 18 页获取到 100 条记录
2025-05-24 08:01:25,187 - INFO - Request Parameters - Page 19:
2025-05-24 08:01:25,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:25,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:25,874 - INFO - API请求耗时: 687ms
2025-05-24 08:01:25,874 - INFO - Response - Page 19
2025-05-24 08:01:25,874 - INFO - 第 19 页获取到 100 条记录
2025-05-24 08:01:26,390 - INFO - Request Parameters - Page 20:
2025-05-24 08:01:26,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:26,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:27,046 - INFO - API请求耗时: 656ms
2025-05-24 08:01:27,046 - INFO - Response - Page 20
2025-05-24 08:01:27,046 - INFO - 第 20 页获取到 100 条记录
2025-05-24 08:01:27,546 - INFO - Request Parameters - Page 21:
2025-05-24 08:01:27,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:27,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:28,265 - INFO - API请求耗时: 719ms
2025-05-24 08:01:28,265 - INFO - Response - Page 21
2025-05-24 08:01:28,265 - INFO - 第 21 页获取到 100 条记录
2025-05-24 08:01:28,765 - INFO - Request Parameters - Page 22:
2025-05-24 08:01:28,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:28,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:29,484 - INFO - API请求耗时: 719ms
2025-05-24 08:01:29,484 - INFO - Response - Page 22
2025-05-24 08:01:29,484 - INFO - 第 22 页获取到 100 条记录
2025-05-24 08:01:29,999 - INFO - Request Parameters - Page 23:
2025-05-24 08:01:29,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:29,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:30,765 - INFO - API请求耗时: 766ms
2025-05-24 08:01:30,765 - INFO - Response - Page 23
2025-05-24 08:01:30,765 - INFO - 第 23 页获取到 100 条记录
2025-05-24 08:01:31,265 - INFO - Request Parameters - Page 24:
2025-05-24 08:01:31,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:31,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:31,890 - INFO - API请求耗时: 625ms
2025-05-24 08:01:31,890 - INFO - Response - Page 24
2025-05-24 08:01:31,890 - INFO - 第 24 页获取到 100 条记录
2025-05-24 08:01:32,390 - INFO - Request Parameters - Page 25:
2025-05-24 08:01:32,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:32,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:33,218 - INFO - API请求耗时: 828ms
2025-05-24 08:01:33,218 - INFO - Response - Page 25
2025-05-24 08:01:33,218 - INFO - 第 25 页获取到 100 条记录
2025-05-24 08:01:33,718 - INFO - Request Parameters - Page 26:
2025-05-24 08:01:33,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:33,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:34,421 - INFO - API请求耗时: 703ms
2025-05-24 08:01:34,421 - INFO - Response - Page 26
2025-05-24 08:01:34,421 - INFO - 第 26 页获取到 100 条记录
2025-05-24 08:01:34,921 - INFO - Request Parameters - Page 27:
2025-05-24 08:01:34,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:34,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:35,609 - INFO - API请求耗时: 687ms
2025-05-24 08:01:35,609 - INFO - Response - Page 27
2025-05-24 08:01:35,609 - INFO - 第 27 页获取到 100 条记录
2025-05-24 08:01:36,109 - INFO - Request Parameters - Page 28:
2025-05-24 08:01:36,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:36,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:36,765 - INFO - API请求耗时: 656ms
2025-05-24 08:01:36,781 - INFO - Response - Page 28
2025-05-24 08:01:36,781 - INFO - 第 28 页获取到 100 条记录
2025-05-24 08:01:37,281 - INFO - Request Parameters - Page 29:
2025-05-24 08:01:37,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:37,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600875, 1743264000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:38,124 - INFO - API请求耗时: 844ms
2025-05-24 08:01:38,124 - INFO - Response - Page 29
2025-05-24 08:01:38,124 - INFO - 第 29 页获取到 99 条记录
2025-05-24 08:01:38,124 - INFO - 查询完成，共获取到 2899 条记录
2025-05-24 08:01:38,124 - INFO - 分段 1 查询成功，获取到 2899 条记录
2025-05-24 08:01:39,124 - INFO - 查询分段 2: 2025-03-31 至 2025-04-06
2025-05-24 08:01:39,124 - INFO - 查询日期范围: 2025-03-31 至 2025-04-06，使用分页查询，每页 100 条记录
2025-05-24 08:01:39,124 - INFO - Request Parameters - Page 1:
2025-05-24 08:01:39,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:39,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:39,749 - INFO - API请求耗时: 625ms
2025-05-24 08:01:39,749 - INFO - Response - Page 1
2025-05-24 08:01:39,749 - INFO - 第 1 页获取到 100 条记录
2025-05-24 08:01:40,249 - INFO - Request Parameters - Page 2:
2025-05-24 08:01:40,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:40,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:40,984 - INFO - API请求耗时: 734ms
2025-05-24 08:01:40,984 - INFO - Response - Page 2
2025-05-24 08:01:40,984 - INFO - 第 2 页获取到 100 条记录
2025-05-24 08:01:41,499 - INFO - Request Parameters - Page 3:
2025-05-24 08:01:41,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:41,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:42,202 - INFO - API请求耗时: 703ms
2025-05-24 08:01:42,202 - INFO - Response - Page 3
2025-05-24 08:01:42,202 - INFO - 第 3 页获取到 100 条记录
2025-05-24 08:01:42,718 - INFO - Request Parameters - Page 4:
2025-05-24 08:01:42,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:42,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:43,406 - INFO - API请求耗时: 688ms
2025-05-24 08:01:43,406 - INFO - Response - Page 4
2025-05-24 08:01:43,406 - INFO - 第 4 页获取到 100 条记录
2025-05-24 08:01:43,921 - INFO - Request Parameters - Page 5:
2025-05-24 08:01:43,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:43,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:44,562 - INFO - API请求耗时: 641ms
2025-05-24 08:01:44,562 - INFO - Response - Page 5
2025-05-24 08:01:44,562 - INFO - 第 5 页获取到 100 条记录
2025-05-24 08:01:45,062 - INFO - Request Parameters - Page 6:
2025-05-24 08:01:45,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:45,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:45,749 - INFO - API请求耗时: 687ms
2025-05-24 08:01:45,749 - INFO - Response - Page 6
2025-05-24 08:01:45,765 - INFO - 第 6 页获取到 100 条记录
2025-05-24 08:01:46,265 - INFO - Request Parameters - Page 7:
2025-05-24 08:01:46,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:46,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:46,952 - INFO - API请求耗时: 687ms
2025-05-24 08:01:46,952 - INFO - Response - Page 7
2025-05-24 08:01:46,952 - INFO - 第 7 页获取到 100 条记录
2025-05-24 08:01:47,468 - INFO - Request Parameters - Page 8:
2025-05-24 08:01:47,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:47,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:48,140 - INFO - API请求耗时: 672ms
2025-05-24 08:01:48,140 - INFO - Response - Page 8
2025-05-24 08:01:48,140 - INFO - 第 8 页获取到 100 条记录
2025-05-24 08:01:48,640 - INFO - Request Parameters - Page 9:
2025-05-24 08:01:48,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:48,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:49,296 - INFO - API请求耗时: 656ms
2025-05-24 08:01:49,296 - INFO - Response - Page 9
2025-05-24 08:01:49,296 - INFO - 第 9 页获取到 100 条记录
2025-05-24 08:01:49,812 - INFO - Request Parameters - Page 10:
2025-05-24 08:01:49,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:49,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:50,499 - INFO - API请求耗时: 687ms
2025-05-24 08:01:50,499 - INFO - Response - Page 10
2025-05-24 08:01:50,499 - INFO - 第 10 页获取到 100 条记录
2025-05-24 08:01:50,999 - INFO - Request Parameters - Page 11:
2025-05-24 08:01:50,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:50,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:51,749 - INFO - API请求耗时: 750ms
2025-05-24 08:01:51,749 - INFO - Response - Page 11
2025-05-24 08:01:51,749 - INFO - 第 11 页获取到 100 条记录
2025-05-24 08:01:52,249 - INFO - Request Parameters - Page 12:
2025-05-24 08:01:52,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:52,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:52,890 - INFO - API请求耗时: 641ms
2025-05-24 08:01:52,890 - INFO - Response - Page 12
2025-05-24 08:01:52,890 - INFO - 第 12 页获取到 100 条记录
2025-05-24 08:01:53,390 - INFO - Request Parameters - Page 13:
2025-05-24 08:01:53,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:53,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:54,109 - INFO - API请求耗时: 719ms
2025-05-24 08:01:54,109 - INFO - Response - Page 13
2025-05-24 08:01:54,109 - INFO - 第 13 页获取到 100 条记录
2025-05-24 08:01:54,624 - INFO - Request Parameters - Page 14:
2025-05-24 08:01:54,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:54,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:55,390 - INFO - API请求耗时: 766ms
2025-05-24 08:01:55,390 - INFO - Response - Page 14
2025-05-24 08:01:55,390 - INFO - 第 14 页获取到 100 条记录
2025-05-24 08:01:55,905 - INFO - Request Parameters - Page 15:
2025-05-24 08:01:55,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:55,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:56,624 - INFO - API请求耗时: 719ms
2025-05-24 08:01:56,624 - INFO - Response - Page 15
2025-05-24 08:01:56,624 - INFO - 第 15 页获取到 100 条记录
2025-05-24 08:01:57,124 - INFO - Request Parameters - Page 16:
2025-05-24 08:01:57,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:57,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:57,890 - INFO - API请求耗时: 766ms
2025-05-24 08:01:57,890 - INFO - Response - Page 16
2025-05-24 08:01:57,890 - INFO - 第 16 页获取到 100 条记录
2025-05-24 08:01:58,390 - INFO - Request Parameters - Page 17:
2025-05-24 08:01:58,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:58,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:01:59,093 - INFO - API请求耗时: 703ms
2025-05-24 08:01:59,093 - INFO - Response - Page 17
2025-05-24 08:01:59,093 - INFO - 第 17 页获取到 100 条记录
2025-05-24 08:01:59,593 - INFO - Request Parameters - Page 18:
2025-05-24 08:01:59,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:01:59,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:00,343 - INFO - API请求耗时: 750ms
2025-05-24 08:02:00,343 - INFO - Response - Page 18
2025-05-24 08:02:00,343 - INFO - 第 18 页获取到 100 条记录
2025-05-24 08:02:00,843 - INFO - Request Parameters - Page 19:
2025-05-24 08:02:00,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:00,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:01,655 - INFO - API请求耗时: 812ms
2025-05-24 08:02:01,655 - INFO - Response - Page 19
2025-05-24 08:02:01,655 - INFO - 第 19 页获取到 100 条记录
2025-05-24 08:02:02,155 - INFO - Request Parameters - Page 20:
2025-05-24 08:02:02,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:02,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:02,905 - INFO - API请求耗时: 750ms
2025-05-24 08:02:02,905 - INFO - Response - Page 20
2025-05-24 08:02:02,905 - INFO - 第 20 页获取到 100 条记录
2025-05-24 08:02:03,421 - INFO - Request Parameters - Page 21:
2025-05-24 08:02:03,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:03,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:04,249 - INFO - API请求耗时: 828ms
2025-05-24 08:02:04,249 - INFO - Response - Page 21
2025-05-24 08:02:04,249 - INFO - 第 21 页获取到 100 条记录
2025-05-24 08:02:04,749 - INFO - Request Parameters - Page 22:
2025-05-24 08:02:04,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:04,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:05,421 - INFO - API请求耗时: 672ms
2025-05-24 08:02:05,421 - INFO - Response - Page 22
2025-05-24 08:02:05,421 - INFO - 第 22 页获取到 100 条记录
2025-05-24 08:02:05,921 - INFO - Request Parameters - Page 23:
2025-05-24 08:02:05,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:05,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:06,624 - INFO - API请求耗时: 703ms
2025-05-24 08:02:06,624 - INFO - Response - Page 23
2025-05-24 08:02:06,624 - INFO - 第 23 页获取到 100 条记录
2025-05-24 08:02:07,124 - INFO - Request Parameters - Page 24:
2025-05-24 08:02:07,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:07,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:07,796 - INFO - API请求耗时: 672ms
2025-05-24 08:02:07,796 - INFO - Response - Page 24
2025-05-24 08:02:07,796 - INFO - 第 24 页获取到 100 条记录
2025-05-24 08:02:08,296 - INFO - Request Parameters - Page 25:
2025-05-24 08:02:08,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:08,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:09,109 - INFO - API请求耗时: 812ms
2025-05-24 08:02:09,109 - INFO - Response - Page 25
2025-05-24 08:02:09,109 - INFO - 第 25 页获取到 100 条记录
2025-05-24 08:02:09,609 - INFO - Request Parameters - Page 26:
2025-05-24 08:02:09,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:09,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:10,265 - INFO - API请求耗时: 656ms
2025-05-24 08:02:10,265 - INFO - Response - Page 26
2025-05-24 08:02:10,280 - INFO - 第 26 页获取到 100 条记录
2025-05-24 08:02:10,796 - INFO - Request Parameters - Page 27:
2025-05-24 08:02:10,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:10,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:11,624 - INFO - API请求耗时: 828ms
2025-05-24 08:02:11,624 - INFO - Response - Page 27
2025-05-24 08:02:11,624 - INFO - 第 27 页获取到 100 条记录
2025-05-24 08:02:12,140 - INFO - Request Parameters - Page 28:
2025-05-24 08:02:12,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:12,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:12,874 - INFO - API请求耗时: 734ms
2025-05-24 08:02:12,874 - INFO - Response - Page 28
2025-05-24 08:02:12,874 - INFO - 第 28 页获取到 100 条记录
2025-05-24 08:02:13,374 - INFO - Request Parameters - Page 29:
2025-05-24 08:02:13,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:13,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:14,093 - INFO - API请求耗时: 719ms
2025-05-24 08:02:14,093 - INFO - Response - Page 29
2025-05-24 08:02:14,093 - INFO - 第 29 页获取到 100 条记录
2025-05-24 08:02:14,593 - INFO - Request Parameters - Page 30:
2025-05-24 08:02:14,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:14,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:15,265 - INFO - API请求耗时: 672ms
2025-05-24 08:02:15,265 - INFO - Response - Page 30
2025-05-24 08:02:15,265 - INFO - 第 30 页获取到 100 条记录
2025-05-24 08:02:15,765 - INFO - Request Parameters - Page 31:
2025-05-24 08:02:15,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:15,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400875, 1743868800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:16,296 - INFO - API请求耗时: 531ms
2025-05-24 08:02:16,296 - INFO - Response - Page 31
2025-05-24 08:02:16,296 - INFO - 第 31 页获取到 21 条记录
2025-05-24 08:02:16,296 - INFO - 查询完成，共获取到 3021 条记录
2025-05-24 08:02:16,296 - INFO - 分段 2 查询成功，获取到 3021 条记录
2025-05-24 08:02:17,296 - INFO - 查询分段 3: 2025-04-07 至 2025-04-13
2025-05-24 08:02:17,296 - INFO - 查询日期范围: 2025-04-07 至 2025-04-13，使用分页查询，每页 100 条记录
2025-05-24 08:02:17,296 - INFO - Request Parameters - Page 1:
2025-05-24 08:02:17,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:17,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:17,968 - INFO - API请求耗时: 672ms
2025-05-24 08:02:17,968 - INFO - Response - Page 1
2025-05-24 08:02:17,968 - INFO - 第 1 页获取到 100 条记录
2025-05-24 08:02:18,483 - INFO - Request Parameters - Page 2:
2025-05-24 08:02:18,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:18,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:19,171 - INFO - API请求耗时: 687ms
2025-05-24 08:02:19,171 - INFO - Response - Page 2
2025-05-24 08:02:19,171 - INFO - 第 2 页获取到 100 条记录
2025-05-24 08:02:19,671 - INFO - Request Parameters - Page 3:
2025-05-24 08:02:19,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:19,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:20,546 - INFO - API请求耗时: 875ms
2025-05-24 08:02:20,546 - INFO - Response - Page 3
2025-05-24 08:02:20,546 - INFO - 第 3 页获取到 100 条记录
2025-05-24 08:02:21,062 - INFO - Request Parameters - Page 4:
2025-05-24 08:02:21,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:21,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:21,687 - INFO - API请求耗时: 625ms
2025-05-24 08:02:21,687 - INFO - Response - Page 4
2025-05-24 08:02:21,687 - INFO - 第 4 页获取到 100 条记录
2025-05-24 08:02:22,202 - INFO - Request Parameters - Page 5:
2025-05-24 08:02:22,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:22,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:23,093 - INFO - API请求耗时: 891ms
2025-05-24 08:02:23,093 - INFO - Response - Page 5
2025-05-24 08:02:23,108 - INFO - 第 5 页获取到 100 条记录
2025-05-24 08:02:23,608 - INFO - Request Parameters - Page 6:
2025-05-24 08:02:23,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:23,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:24,249 - INFO - API请求耗时: 641ms
2025-05-24 08:02:24,249 - INFO - Response - Page 6
2025-05-24 08:02:24,249 - INFO - 第 6 页获取到 100 条记录
2025-05-24 08:02:24,765 - INFO - Request Parameters - Page 7:
2025-05-24 08:02:24,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:24,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:25,452 - INFO - API请求耗时: 687ms
2025-05-24 08:02:25,452 - INFO - Response - Page 7
2025-05-24 08:02:25,452 - INFO - 第 7 页获取到 100 条记录
2025-05-24 08:02:25,952 - INFO - Request Parameters - Page 8:
2025-05-24 08:02:25,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:25,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:26,577 - INFO - API请求耗时: 625ms
2025-05-24 08:02:26,577 - INFO - Response - Page 8
2025-05-24 08:02:26,593 - INFO - 第 8 页获取到 100 条记录
2025-05-24 08:02:27,093 - INFO - Request Parameters - Page 9:
2025-05-24 08:02:27,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:27,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:27,812 - INFO - API请求耗时: 719ms
2025-05-24 08:02:27,812 - INFO - Response - Page 9
2025-05-24 08:02:27,812 - INFO - 第 9 页获取到 100 条记录
2025-05-24 08:02:28,312 - INFO - Request Parameters - Page 10:
2025-05-24 08:02:28,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:28,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:29,030 - INFO - API请求耗时: 719ms
2025-05-24 08:02:29,030 - INFO - Response - Page 10
2025-05-24 08:02:29,030 - INFO - 第 10 页获取到 100 条记录
2025-05-24 08:02:29,530 - INFO - Request Parameters - Page 11:
2025-05-24 08:02:29,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:29,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:30,233 - INFO - API请求耗时: 703ms
2025-05-24 08:02:30,233 - INFO - Response - Page 11
2025-05-24 08:02:30,233 - INFO - 第 11 页获取到 100 条记录
2025-05-24 08:02:30,733 - INFO - Request Parameters - Page 12:
2025-05-24 08:02:30,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:30,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:31,499 - INFO - API请求耗时: 766ms
2025-05-24 08:02:31,499 - INFO - Response - Page 12
2025-05-24 08:02:31,499 - INFO - 第 12 页获取到 100 条记录
2025-05-24 08:02:32,015 - INFO - Request Parameters - Page 13:
2025-05-24 08:02:32,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:32,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:32,687 - INFO - API请求耗时: 672ms
2025-05-24 08:02:32,687 - INFO - Response - Page 13
2025-05-24 08:02:32,687 - INFO - 第 13 页获取到 100 条记录
2025-05-24 08:02:33,187 - INFO - Request Parameters - Page 14:
2025-05-24 08:02:33,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:33,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:34,062 - INFO - API请求耗时: 875ms
2025-05-24 08:02:34,062 - INFO - Response - Page 14
2025-05-24 08:02:34,062 - INFO - 第 14 页获取到 100 条记录
2025-05-24 08:02:34,577 - INFO - Request Parameters - Page 15:
2025-05-24 08:02:34,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:34,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:35,296 - INFO - API请求耗时: 719ms
2025-05-24 08:02:35,296 - INFO - Response - Page 15
2025-05-24 08:02:35,296 - INFO - 第 15 页获取到 100 条记录
2025-05-24 08:02:35,796 - INFO - Request Parameters - Page 16:
2025-05-24 08:02:35,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:35,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:36,483 - INFO - API请求耗时: 687ms
2025-05-24 08:02:36,483 - INFO - Response - Page 16
2025-05-24 08:02:36,483 - INFO - 第 16 页获取到 100 条记录
2025-05-24 08:02:36,999 - INFO - Request Parameters - Page 17:
2025-05-24 08:02:36,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:36,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:37,608 - INFO - API请求耗时: 609ms
2025-05-24 08:02:37,608 - INFO - Response - Page 17
2025-05-24 08:02:37,624 - INFO - 第 17 页获取到 100 条记录
2025-05-24 08:02:38,140 - INFO - Request Parameters - Page 18:
2025-05-24 08:02:38,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:38,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:38,780 - INFO - API请求耗时: 641ms
2025-05-24 08:02:38,780 - INFO - Response - Page 18
2025-05-24 08:02:38,780 - INFO - 第 18 页获取到 100 条记录
2025-05-24 08:02:39,280 - INFO - Request Parameters - Page 19:
2025-05-24 08:02:39,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:39,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:39,858 - INFO - API请求耗时: 578ms
2025-05-24 08:02:39,858 - INFO - Response - Page 19
2025-05-24 08:02:39,858 - INFO - 第 19 页获取到 100 条记录
2025-05-24 08:02:40,358 - INFO - Request Parameters - Page 20:
2025-05-24 08:02:40,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:40,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:40,983 - INFO - API请求耗时: 625ms
2025-05-24 08:02:40,983 - INFO - Response - Page 20
2025-05-24 08:02:40,983 - INFO - 第 20 页获取到 100 条记录
2025-05-24 08:02:41,483 - INFO - Request Parameters - Page 21:
2025-05-24 08:02:41,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:41,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:42,296 - INFO - API请求耗时: 812ms
2025-05-24 08:02:42,296 - INFO - Response - Page 21
2025-05-24 08:02:42,296 - INFO - 第 21 页获取到 100 条记录
2025-05-24 08:02:42,796 - INFO - Request Parameters - Page 22:
2025-05-24 08:02:42,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:42,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:43,468 - INFO - API请求耗时: 672ms
2025-05-24 08:02:43,468 - INFO - Response - Page 22
2025-05-24 08:02:43,468 - INFO - 第 22 页获取到 100 条记录
2025-05-24 08:02:43,968 - INFO - Request Parameters - Page 23:
2025-05-24 08:02:43,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:43,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:44,702 - INFO - API请求耗时: 734ms
2025-05-24 08:02:44,702 - INFO - Response - Page 23
2025-05-24 08:02:44,702 - INFO - 第 23 页获取到 100 条记录
2025-05-24 08:02:45,202 - INFO - Request Parameters - Page 24:
2025-05-24 08:02:45,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:45,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:45,890 - INFO - API请求耗时: 687ms
2025-05-24 08:02:45,890 - INFO - Response - Page 24
2025-05-24 08:02:45,890 - INFO - 第 24 页获取到 100 条记录
2025-05-24 08:02:46,390 - INFO - Request Parameters - Page 25:
2025-05-24 08:02:46,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:46,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:47,249 - INFO - API请求耗时: 859ms
2025-05-24 08:02:47,249 - INFO - Response - Page 25
2025-05-24 08:02:47,249 - INFO - 第 25 页获取到 100 条记录
2025-05-24 08:02:47,765 - INFO - Request Parameters - Page 26:
2025-05-24 08:02:47,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:47,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:48,436 - INFO - API请求耗时: 672ms
2025-05-24 08:02:48,436 - INFO - Response - Page 26
2025-05-24 08:02:48,436 - INFO - 第 26 页获取到 100 条记录
2025-05-24 08:02:48,936 - INFO - Request Parameters - Page 27:
2025-05-24 08:02:48,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:48,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:49,671 - INFO - API请求耗时: 734ms
2025-05-24 08:02:49,671 - INFO - Response - Page 27
2025-05-24 08:02:49,671 - INFO - 第 27 页获取到 100 条记录
2025-05-24 08:02:50,171 - INFO - Request Parameters - Page 28:
2025-05-24 08:02:50,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:50,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:50,796 - INFO - API请求耗时: 625ms
2025-05-24 08:02:50,796 - INFO - Response - Page 28
2025-05-24 08:02:50,796 - INFO - 第 28 页获取到 100 条记录
2025-05-24 08:02:51,296 - INFO - Request Parameters - Page 29:
2025-05-24 08:02:51,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:51,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:51,968 - INFO - API请求耗时: 672ms
2025-05-24 08:02:51,983 - INFO - Response - Page 29
2025-05-24 08:02:51,983 - INFO - 第 29 页获取到 100 条记录
2025-05-24 08:02:52,483 - INFO - Request Parameters - Page 30:
2025-05-24 08:02:52,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:52,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:53,249 - INFO - API请求耗时: 766ms
2025-05-24 08:02:53,249 - INFO - Response - Page 30
2025-05-24 08:02:53,249 - INFO - 第 30 页获取到 100 条记录
2025-05-24 08:02:53,749 - INFO - Request Parameters - Page 31:
2025-05-24 08:02:53,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:53,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200875, 1744473600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:54,202 - INFO - API请求耗时: 453ms
2025-05-24 08:02:54,202 - INFO - Response - Page 31
2025-05-24 08:02:54,202 - INFO - 第 31 页获取到 11 条记录
2025-05-24 08:02:54,202 - INFO - 查询完成，共获取到 3011 条记录
2025-05-24 08:02:54,202 - INFO - 分段 3 查询成功，获取到 3011 条记录
2025-05-24 08:02:55,202 - INFO - 查询分段 4: 2025-04-14 至 2025-04-20
2025-05-24 08:02:55,202 - INFO - 查询日期范围: 2025-04-14 至 2025-04-20，使用分页查询，每页 100 条记录
2025-05-24 08:02:55,202 - INFO - Request Parameters - Page 1:
2025-05-24 08:02:55,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:55,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:55,827 - INFO - API请求耗时: 625ms
2025-05-24 08:02:55,827 - INFO - Response - Page 1
2025-05-24 08:02:55,827 - INFO - 第 1 页获取到 100 条记录
2025-05-24 08:02:56,343 - INFO - Request Parameters - Page 2:
2025-05-24 08:02:56,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:56,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:57,061 - INFO - API请求耗时: 719ms
2025-05-24 08:02:57,061 - INFO - Response - Page 2
2025-05-24 08:02:57,061 - INFO - 第 2 页获取到 100 条记录
2025-05-24 08:02:57,577 - INFO - Request Parameters - Page 3:
2025-05-24 08:02:57,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:57,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:58,249 - INFO - API请求耗时: 672ms
2025-05-24 08:02:58,249 - INFO - Response - Page 3
2025-05-24 08:02:58,249 - INFO - 第 3 页获取到 100 条记录
2025-05-24 08:02:58,749 - INFO - Request Parameters - Page 4:
2025-05-24 08:02:58,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:58,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:02:59,421 - INFO - API请求耗时: 672ms
2025-05-24 08:02:59,421 - INFO - Response - Page 4
2025-05-24 08:02:59,421 - INFO - 第 4 页获取到 100 条记录
2025-05-24 08:02:59,921 - INFO - Request Parameters - Page 5:
2025-05-24 08:02:59,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:02:59,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:00,608 - INFO - API请求耗时: 687ms
2025-05-24 08:03:00,608 - INFO - Response - Page 5
2025-05-24 08:03:00,608 - INFO - 第 5 页获取到 100 条记录
2025-05-24 08:03:01,108 - INFO - Request Parameters - Page 6:
2025-05-24 08:03:01,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:01,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:01,811 - INFO - API请求耗时: 703ms
2025-05-24 08:03:01,811 - INFO - Response - Page 6
2025-05-24 08:03:01,811 - INFO - 第 6 页获取到 100 条记录
2025-05-24 08:03:02,327 - INFO - Request Parameters - Page 7:
2025-05-24 08:03:02,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:02,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:02,999 - INFO - API请求耗时: 672ms
2025-05-24 08:03:02,999 - INFO - Response - Page 7
2025-05-24 08:03:02,999 - INFO - 第 7 页获取到 100 条记录
2025-05-24 08:03:03,514 - INFO - Request Parameters - Page 8:
2025-05-24 08:03:03,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:03,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:04,218 - INFO - API请求耗时: 703ms
2025-05-24 08:03:04,218 - INFO - Response - Page 8
2025-05-24 08:03:04,218 - INFO - 第 8 页获取到 100 条记录
2025-05-24 08:03:04,733 - INFO - Request Parameters - Page 9:
2025-05-24 08:03:04,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:04,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:05,327 - INFO - API请求耗时: 594ms
2025-05-24 08:03:05,327 - INFO - Response - Page 9
2025-05-24 08:03:05,327 - INFO - 第 9 页获取到 100 条记录
2025-05-24 08:03:05,827 - INFO - Request Parameters - Page 10:
2025-05-24 08:03:05,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:05,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:06,499 - INFO - API请求耗时: 672ms
2025-05-24 08:03:06,499 - INFO - Response - Page 10
2025-05-24 08:03:06,499 - INFO - 第 10 页获取到 100 条记录
2025-05-24 08:03:07,014 - INFO - Request Parameters - Page 11:
2025-05-24 08:03:07,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:07,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:07,764 - INFO - API请求耗时: 750ms
2025-05-24 08:03:07,780 - INFO - Response - Page 11
2025-05-24 08:03:07,780 - INFO - 第 11 页获取到 100 条记录
2025-05-24 08:03:08,296 - INFO - Request Parameters - Page 12:
2025-05-24 08:03:08,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:08,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:08,968 - INFO - API请求耗时: 672ms
2025-05-24 08:03:08,968 - INFO - Response - Page 12
2025-05-24 08:03:08,968 - INFO - 第 12 页获取到 100 条记录
2025-05-24 08:03:09,468 - INFO - Request Parameters - Page 13:
2025-05-24 08:03:09,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:09,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:10,077 - INFO - API请求耗时: 609ms
2025-05-24 08:03:10,077 - INFO - Response - Page 13
2025-05-24 08:03:10,077 - INFO - 第 13 页获取到 100 条记录
2025-05-24 08:03:10,593 - INFO - Request Parameters - Page 14:
2025-05-24 08:03:10,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:10,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:11,264 - INFO - API请求耗时: 672ms
2025-05-24 08:03:11,264 - INFO - Response - Page 14
2025-05-24 08:03:11,264 - INFO - 第 14 页获取到 100 条记录
2025-05-24 08:03:11,780 - INFO - Request Parameters - Page 15:
2025-05-24 08:03:11,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:11,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:12,452 - INFO - API请求耗时: 672ms
2025-05-24 08:03:12,452 - INFO - Response - Page 15
2025-05-24 08:03:12,452 - INFO - 第 15 页获取到 100 条记录
2025-05-24 08:03:12,952 - INFO - Request Parameters - Page 16:
2025-05-24 08:03:12,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:12,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:13,718 - INFO - API请求耗时: 766ms
2025-05-24 08:03:13,718 - INFO - Response - Page 16
2025-05-24 08:03:13,733 - INFO - 第 16 页获取到 100 条记录
2025-05-24 08:03:14,249 - INFO - Request Parameters - Page 17:
2025-05-24 08:03:14,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:14,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:14,999 - INFO - API请求耗时: 750ms
2025-05-24 08:03:14,999 - INFO - Response - Page 17
2025-05-24 08:03:14,999 - INFO - 第 17 页获取到 100 条记录
2025-05-24 08:03:15,514 - INFO - Request Parameters - Page 18:
2025-05-24 08:03:15,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:15,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:16,249 - INFO - API请求耗时: 734ms
2025-05-24 08:03:16,249 - INFO - Response - Page 18
2025-05-24 08:03:16,249 - INFO - 第 18 页获取到 100 条记录
2025-05-24 08:03:16,749 - INFO - Request Parameters - Page 19:
2025-05-24 08:03:16,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:16,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:17,405 - INFO - API请求耗时: 656ms
2025-05-24 08:03:17,405 - INFO - Response - Page 19
2025-05-24 08:03:17,405 - INFO - 第 19 页获取到 100 条记录
2025-05-24 08:03:17,905 - INFO - Request Parameters - Page 20:
2025-05-24 08:03:17,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:17,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:18,577 - INFO - API请求耗时: 672ms
2025-05-24 08:03:18,577 - INFO - Response - Page 20
2025-05-24 08:03:18,577 - INFO - 第 20 页获取到 100 条记录
2025-05-24 08:03:19,077 - INFO - Request Parameters - Page 21:
2025-05-24 08:03:19,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:19,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:19,842 - INFO - API请求耗时: 766ms
2025-05-24 08:03:19,842 - INFO - Response - Page 21
2025-05-24 08:03:19,842 - INFO - 第 21 页获取到 100 条记录
2025-05-24 08:03:20,358 - INFO - Request Parameters - Page 22:
2025-05-24 08:03:20,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:20,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:20,983 - INFO - API请求耗时: 625ms
2025-05-24 08:03:20,983 - INFO - Response - Page 22
2025-05-24 08:03:20,999 - INFO - 第 22 页获取到 100 条记录
2025-05-24 08:03:21,514 - INFO - Request Parameters - Page 23:
2025-05-24 08:03:21,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:21,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:22,202 - INFO - API请求耗时: 687ms
2025-05-24 08:03:22,202 - INFO - Response - Page 23
2025-05-24 08:03:22,202 - INFO - 第 23 页获取到 100 条记录
2025-05-24 08:03:22,702 - INFO - Request Parameters - Page 24:
2025-05-24 08:03:22,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:22,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:23,436 - INFO - API请求耗时: 734ms
2025-05-24 08:03:23,436 - INFO - Response - Page 24
2025-05-24 08:03:23,436 - INFO - 第 24 页获取到 100 条记录
2025-05-24 08:03:23,936 - INFO - Request Parameters - Page 25:
2025-05-24 08:03:23,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:23,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:24,624 - INFO - API请求耗时: 687ms
2025-05-24 08:03:24,624 - INFO - Response - Page 25
2025-05-24 08:03:24,624 - INFO - 第 25 页获取到 100 条记录
2025-05-24 08:03:25,139 - INFO - Request Parameters - Page 26:
2025-05-24 08:03:25,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:25,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:25,811 - INFO - API请求耗时: 672ms
2025-05-24 08:03:25,811 - INFO - Response - Page 26
2025-05-24 08:03:25,811 - INFO - 第 26 页获取到 100 条记录
2025-05-24 08:03:26,311 - INFO - Request Parameters - Page 27:
2025-05-24 08:03:26,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:26,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:26,999 - INFO - API请求耗时: 687ms
2025-05-24 08:03:26,999 - INFO - Response - Page 27
2025-05-24 08:03:26,999 - INFO - 第 27 页获取到 100 条记录
2025-05-24 08:03:27,514 - INFO - Request Parameters - Page 28:
2025-05-24 08:03:27,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:27,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:28,296 - INFO - API请求耗时: 781ms
2025-05-24 08:03:28,296 - INFO - Response - Page 28
2025-05-24 08:03:28,296 - INFO - 第 28 页获取到 100 条记录
2025-05-24 08:03:28,796 - INFO - Request Parameters - Page 29:
2025-05-24 08:03:28,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:28,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:29,608 - INFO - API请求耗时: 812ms
2025-05-24 08:03:29,608 - INFO - Response - Page 29
2025-05-24 08:03:29,608 - INFO - 第 29 页获取到 100 条记录
2025-05-24 08:03:30,108 - INFO - Request Parameters - Page 30:
2025-05-24 08:03:30,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:30,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:30,811 - INFO - API请求耗时: 703ms
2025-05-24 08:03:30,811 - INFO - Response - Page 30
2025-05-24 08:03:30,811 - INFO - 第 30 页获取到 100 条记录
2025-05-24 08:03:31,311 - INFO - Request Parameters - Page 31:
2025-05-24 08:03:31,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:31,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000875, 1745078400875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:31,749 - INFO - API请求耗时: 437ms
2025-05-24 08:03:31,749 - INFO - Response - Page 31
2025-05-24 08:03:31,749 - INFO - 第 31 页获取到 10 条记录
2025-05-24 08:03:31,749 - INFO - 查询完成，共获取到 3010 条记录
2025-05-24 08:03:31,749 - INFO - 分段 4 查询成功，获取到 3010 条记录
2025-05-24 08:03:32,749 - INFO - 查询分段 5: 2025-04-21 至 2025-04-27
2025-05-24 08:03:32,749 - INFO - 查询日期范围: 2025-04-21 至 2025-04-27，使用分页查询，每页 100 条记录
2025-05-24 08:03:32,749 - INFO - Request Parameters - Page 1:
2025-05-24 08:03:32,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:32,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:33,436 - INFO - API请求耗时: 687ms
2025-05-24 08:03:33,436 - INFO - Response - Page 1
2025-05-24 08:03:33,436 - INFO - 第 1 页获取到 100 条记录
2025-05-24 08:03:33,936 - INFO - Request Parameters - Page 2:
2025-05-24 08:03:33,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:33,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:34,686 - INFO - API请求耗时: 750ms
2025-05-24 08:03:34,686 - INFO - Response - Page 2
2025-05-24 08:03:34,686 - INFO - 第 2 页获取到 100 条记录
2025-05-24 08:03:35,186 - INFO - Request Parameters - Page 3:
2025-05-24 08:03:35,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:35,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:36,045 - INFO - API请求耗时: 859ms
2025-05-24 08:03:36,045 - INFO - Response - Page 3
2025-05-24 08:03:36,045 - INFO - 第 3 页获取到 100 条记录
2025-05-24 08:03:36,561 - INFO - Request Parameters - Page 4:
2025-05-24 08:03:36,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:36,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:37,264 - INFO - API请求耗时: 703ms
2025-05-24 08:03:37,264 - INFO - Response - Page 4
2025-05-24 08:03:37,264 - INFO - 第 4 页获取到 100 条记录
2025-05-24 08:03:37,764 - INFO - Request Parameters - Page 5:
2025-05-24 08:03:37,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:37,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:38,405 - INFO - API请求耗时: 641ms
2025-05-24 08:03:38,405 - INFO - Response - Page 5
2025-05-24 08:03:38,405 - INFO - 第 5 页获取到 100 条记录
2025-05-24 08:03:38,920 - INFO - Request Parameters - Page 6:
2025-05-24 08:03:38,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:38,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:39,639 - INFO - API请求耗时: 719ms
2025-05-24 08:03:39,639 - INFO - Response - Page 6
2025-05-24 08:03:39,639 - INFO - 第 6 页获取到 100 条记录
2025-05-24 08:03:40,139 - INFO - Request Parameters - Page 7:
2025-05-24 08:03:40,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:40,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:40,842 - INFO - API请求耗时: 703ms
2025-05-24 08:03:40,842 - INFO - Response - Page 7
2025-05-24 08:03:40,842 - INFO - 第 7 页获取到 100 条记录
2025-05-24 08:03:41,358 - INFO - Request Parameters - Page 8:
2025-05-24 08:03:41,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:41,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:42,030 - INFO - API请求耗时: 672ms
2025-05-24 08:03:42,030 - INFO - Response - Page 8
2025-05-24 08:03:42,030 - INFO - 第 8 页获取到 100 条记录
2025-05-24 08:03:42,545 - INFO - Request Parameters - Page 9:
2025-05-24 08:03:42,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:42,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:43,202 - INFO - API请求耗时: 656ms
2025-05-24 08:03:43,202 - INFO - Response - Page 9
2025-05-24 08:03:43,202 - INFO - 第 9 页获取到 100 条记录
2025-05-24 08:03:43,717 - INFO - Request Parameters - Page 10:
2025-05-24 08:03:43,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:43,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:44,389 - INFO - API请求耗时: 672ms
2025-05-24 08:03:44,389 - INFO - Response - Page 10
2025-05-24 08:03:44,389 - INFO - 第 10 页获取到 100 条记录
2025-05-24 08:03:44,905 - INFO - Request Parameters - Page 11:
2025-05-24 08:03:44,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:44,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:45,592 - INFO - API请求耗时: 687ms
2025-05-24 08:03:45,592 - INFO - Response - Page 11
2025-05-24 08:03:45,592 - INFO - 第 11 页获取到 100 条记录
2025-05-24 08:03:46,092 - INFO - Request Parameters - Page 12:
2025-05-24 08:03:46,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:46,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:46,764 - INFO - API请求耗时: 672ms
2025-05-24 08:03:46,764 - INFO - Response - Page 12
2025-05-24 08:03:46,764 - INFO - 第 12 页获取到 100 条记录
2025-05-24 08:03:47,264 - INFO - Request Parameters - Page 13:
2025-05-24 08:03:47,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:47,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:47,967 - INFO - API请求耗时: 703ms
2025-05-24 08:03:47,967 - INFO - Response - Page 13
2025-05-24 08:03:47,967 - INFO - 第 13 页获取到 100 条记录
2025-05-24 08:03:48,483 - INFO - Request Parameters - Page 14:
2025-05-24 08:03:48,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:48,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:49,108 - INFO - API请求耗时: 625ms
2025-05-24 08:03:49,108 - INFO - Response - Page 14
2025-05-24 08:03:49,108 - INFO - 第 14 页获取到 100 条记录
2025-05-24 08:03:49,608 - INFO - Request Parameters - Page 15:
2025-05-24 08:03:49,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:49,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:50,264 - INFO - API请求耗时: 656ms
2025-05-24 08:03:50,264 - INFO - Response - Page 15
2025-05-24 08:03:50,264 - INFO - 第 15 页获取到 100 条记录
2025-05-24 08:03:50,764 - INFO - Request Parameters - Page 16:
2025-05-24 08:03:50,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:50,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:51,717 - INFO - API请求耗时: 953ms
2025-05-24 08:03:51,717 - INFO - Response - Page 16
2025-05-24 08:03:51,717 - INFO - 第 16 页获取到 100 条记录
2025-05-24 08:03:52,233 - INFO - Request Parameters - Page 17:
2025-05-24 08:03:52,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:52,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:52,920 - INFO - API请求耗时: 687ms
2025-05-24 08:03:52,920 - INFO - Response - Page 17
2025-05-24 08:03:52,920 - INFO - 第 17 页获取到 100 条记录
2025-05-24 08:03:53,420 - INFO - Request Parameters - Page 18:
2025-05-24 08:03:53,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:53,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:54,092 - INFO - API请求耗时: 672ms
2025-05-24 08:03:54,092 - INFO - Response - Page 18
2025-05-24 08:03:54,092 - INFO - 第 18 页获取到 100 条记录
2025-05-24 08:03:54,592 - INFO - Request Parameters - Page 19:
2025-05-24 08:03:54,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:54,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:55,217 - INFO - API请求耗时: 625ms
2025-05-24 08:03:55,217 - INFO - Response - Page 19
2025-05-24 08:03:55,217 - INFO - 第 19 页获取到 100 条记录
2025-05-24 08:03:55,717 - INFO - Request Parameters - Page 20:
2025-05-24 08:03:55,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:55,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:56,436 - INFO - API请求耗时: 719ms
2025-05-24 08:03:56,452 - INFO - Response - Page 20
2025-05-24 08:03:56,452 - INFO - 第 20 页获取到 100 条记录
2025-05-24 08:03:56,952 - INFO - Request Parameters - Page 21:
2025-05-24 08:03:56,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:56,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:57,686 - INFO - API请求耗时: 734ms
2025-05-24 08:03:57,686 - INFO - Response - Page 21
2025-05-24 08:03:57,686 - INFO - 第 21 页获取到 100 条记录
2025-05-24 08:03:58,202 - INFO - Request Parameters - Page 22:
2025-05-24 08:03:58,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:58,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:03:58,842 - INFO - API请求耗时: 641ms
2025-05-24 08:03:58,842 - INFO - Response - Page 22
2025-05-24 08:03:58,842 - INFO - 第 22 页获取到 100 条记录
2025-05-24 08:03:59,373 - INFO - Request Parameters - Page 23:
2025-05-24 08:03:59,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:03:59,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:00,030 - INFO - API请求耗时: 656ms
2025-05-24 08:04:00,030 - INFO - Response - Page 23
2025-05-24 08:04:00,030 - INFO - 第 23 页获取到 100 条记录
2025-05-24 08:04:00,545 - INFO - Request Parameters - Page 24:
2025-05-24 08:04:00,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:00,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:01,280 - INFO - API请求耗时: 734ms
2025-05-24 08:04:01,280 - INFO - Response - Page 24
2025-05-24 08:04:01,280 - INFO - 第 24 页获取到 100 条记录
2025-05-24 08:04:01,780 - INFO - Request Parameters - Page 25:
2025-05-24 08:04:01,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:01,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:02,420 - INFO - API请求耗时: 641ms
2025-05-24 08:04:02,420 - INFO - Response - Page 25
2025-05-24 08:04:02,420 - INFO - 第 25 页获取到 100 条记录
2025-05-24 08:04:02,920 - INFO - Request Parameters - Page 26:
2025-05-24 08:04:02,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:02,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:03,577 - INFO - API请求耗时: 656ms
2025-05-24 08:04:03,577 - INFO - Response - Page 26
2025-05-24 08:04:03,577 - INFO - 第 26 页获取到 100 条记录
2025-05-24 08:04:04,077 - INFO - Request Parameters - Page 27:
2025-05-24 08:04:04,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:04,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:04,998 - INFO - API请求耗时: 922ms
2025-05-24 08:04:04,998 - INFO - Response - Page 27
2025-05-24 08:04:04,998 - INFO - 第 27 页获取到 100 条记录
2025-05-24 08:04:05,514 - INFO - Request Parameters - Page 28:
2025-05-24 08:04:05,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:05,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:06,155 - INFO - API请求耗时: 641ms
2025-05-24 08:04:06,155 - INFO - Response - Page 28
2025-05-24 08:04:06,155 - INFO - 第 28 页获取到 100 条记录
2025-05-24 08:04:06,670 - INFO - Request Parameters - Page 29:
2025-05-24 08:04:06,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:06,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:07,295 - INFO - API请求耗时: 625ms
2025-05-24 08:04:07,295 - INFO - Response - Page 29
2025-05-24 08:04:07,295 - INFO - 第 29 页获取到 100 条记录
2025-05-24 08:04:07,811 - INFO - Request Parameters - Page 30:
2025-05-24 08:04:07,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:07,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800875, 1745683200875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:08,498 - INFO - API请求耗时: 687ms
2025-05-24 08:04:08,498 - INFO - Response - Page 30
2025-05-24 08:04:08,498 - INFO - 第 30 页获取到 62 条记录
2025-05-24 08:04:08,498 - INFO - 查询完成，共获取到 2962 条记录
2025-05-24 08:04:08,498 - INFO - 分段 5 查询成功，获取到 2962 条记录
2025-05-24 08:04:09,498 - INFO - 查询分段 6: 2025-04-28 至 2025-05-04
2025-05-24 08:04:09,498 - INFO - 查询日期范围: 2025-04-28 至 2025-05-04，使用分页查询，每页 100 条记录
2025-05-24 08:04:09,498 - INFO - Request Parameters - Page 1:
2025-05-24 08:04:09,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:09,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:10,217 - INFO - API请求耗时: 719ms
2025-05-24 08:04:10,217 - INFO - Response - Page 1
2025-05-24 08:04:10,217 - INFO - 第 1 页获取到 100 条记录
2025-05-24 08:04:10,733 - INFO - Request Parameters - Page 2:
2025-05-24 08:04:10,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:10,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:11,451 - INFO - API请求耗时: 719ms
2025-05-24 08:04:11,451 - INFO - Response - Page 2
2025-05-24 08:04:11,451 - INFO - 第 2 页获取到 100 条记录
2025-05-24 08:04:11,951 - INFO - Request Parameters - Page 3:
2025-05-24 08:04:11,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:11,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:12,858 - INFO - API请求耗时: 906ms
2025-05-24 08:04:12,858 - INFO - Response - Page 3
2025-05-24 08:04:12,873 - INFO - 第 3 页获取到 100 条记录
2025-05-24 08:04:13,389 - INFO - Request Parameters - Page 4:
2025-05-24 08:04:13,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:13,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:14,092 - INFO - API请求耗时: 703ms
2025-05-24 08:04:14,092 - INFO - Response - Page 4
2025-05-24 08:04:14,092 - INFO - 第 4 页获取到 100 条记录
2025-05-24 08:04:14,608 - INFO - Request Parameters - Page 5:
2025-05-24 08:04:14,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:14,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:15,280 - INFO - API请求耗时: 672ms
2025-05-24 08:04:15,295 - INFO - Response - Page 5
2025-05-24 08:04:15,295 - INFO - 第 5 页获取到 100 条记录
2025-05-24 08:04:15,811 - INFO - Request Parameters - Page 6:
2025-05-24 08:04:15,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:15,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:16,467 - INFO - API请求耗时: 656ms
2025-05-24 08:04:16,467 - INFO - Response - Page 6
2025-05-24 08:04:16,467 - INFO - 第 6 页获取到 100 条记录
2025-05-24 08:04:16,967 - INFO - Request Parameters - Page 7:
2025-05-24 08:04:16,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:16,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:17,701 - INFO - API请求耗时: 734ms
2025-05-24 08:04:17,701 - INFO - Response - Page 7
2025-05-24 08:04:17,701 - INFO - 第 7 页获取到 100 条记录
2025-05-24 08:04:18,201 - INFO - Request Parameters - Page 8:
2025-05-24 08:04:18,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:18,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:18,811 - INFO - API请求耗时: 609ms
2025-05-24 08:04:18,811 - INFO - Response - Page 8
2025-05-24 08:04:18,826 - INFO - 第 8 页获取到 100 条记录
2025-05-24 08:04:19,326 - INFO - Request Parameters - Page 9:
2025-05-24 08:04:19,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:19,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:20,061 - INFO - API请求耗时: 734ms
2025-05-24 08:04:20,061 - INFO - Response - Page 9
2025-05-24 08:04:20,061 - INFO - 第 9 页获取到 100 条记录
2025-05-24 08:04:20,576 - INFO - Request Parameters - Page 10:
2025-05-24 08:04:20,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:20,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:21,311 - INFO - API请求耗时: 734ms
2025-05-24 08:04:21,311 - INFO - Response - Page 10
2025-05-24 08:04:21,311 - INFO - 第 10 页获取到 100 条记录
2025-05-24 08:04:21,826 - INFO - Request Parameters - Page 11:
2025-05-24 08:04:21,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:21,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:22,436 - INFO - API请求耗时: 609ms
2025-05-24 08:04:22,436 - INFO - Response - Page 11
2025-05-24 08:04:22,436 - INFO - 第 11 页获取到 100 条记录
2025-05-24 08:04:22,936 - INFO - Request Parameters - Page 12:
2025-05-24 08:04:22,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:22,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:23,608 - INFO - API请求耗时: 672ms
2025-05-24 08:04:23,608 - INFO - Response - Page 12
2025-05-24 08:04:23,608 - INFO - 第 12 页获取到 100 条记录
2025-05-24 08:04:24,108 - INFO - Request Parameters - Page 13:
2025-05-24 08:04:24,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:24,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:24,811 - INFO - API请求耗时: 703ms
2025-05-24 08:04:24,811 - INFO - Response - Page 13
2025-05-24 08:04:24,811 - INFO - 第 13 页获取到 100 条记录
2025-05-24 08:04:25,326 - INFO - Request Parameters - Page 14:
2025-05-24 08:04:25,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:25,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:26,045 - INFO - API请求耗时: 719ms
2025-05-24 08:04:26,045 - INFO - Response - Page 14
2025-05-24 08:04:26,045 - INFO - 第 14 页获取到 100 条记录
2025-05-24 08:04:26,561 - INFO - Request Parameters - Page 15:
2025-05-24 08:04:26,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:26,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:27,264 - INFO - API请求耗时: 703ms
2025-05-24 08:04:27,264 - INFO - Response - Page 15
2025-05-24 08:04:27,264 - INFO - 第 15 页获取到 100 条记录
2025-05-24 08:04:27,780 - INFO - Request Parameters - Page 16:
2025-05-24 08:04:27,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:27,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:28,436 - INFO - API请求耗时: 656ms
2025-05-24 08:04:28,436 - INFO - Response - Page 16
2025-05-24 08:04:28,436 - INFO - 第 16 页获取到 100 条记录
2025-05-24 08:04:28,936 - INFO - Request Parameters - Page 17:
2025-05-24 08:04:28,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:28,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:29,639 - INFO - API请求耗时: 703ms
2025-05-24 08:04:29,639 - INFO - Response - Page 17
2025-05-24 08:04:29,639 - INFO - 第 17 页获取到 100 条记录
2025-05-24 08:04:30,139 - INFO - Request Parameters - Page 18:
2025-05-24 08:04:30,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:30,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:30,748 - INFO - API请求耗时: 609ms
2025-05-24 08:04:30,748 - INFO - Response - Page 18
2025-05-24 08:04:30,764 - INFO - 第 18 页获取到 100 条记录
2025-05-24 08:04:31,279 - INFO - Request Parameters - Page 19:
2025-05-24 08:04:31,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:31,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:32,108 - INFO - API请求耗时: 828ms
2025-05-24 08:04:32,108 - INFO - Response - Page 19
2025-05-24 08:04:32,108 - INFO - 第 19 页获取到 100 条记录
2025-05-24 08:04:32,623 - INFO - Request Parameters - Page 20:
2025-05-24 08:04:32,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:32,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:33,311 - INFO - API请求耗时: 687ms
2025-05-24 08:04:33,326 - INFO - Response - Page 20
2025-05-24 08:04:33,326 - INFO - 第 20 页获取到 100 条记录
2025-05-24 08:04:33,826 - INFO - Request Parameters - Page 21:
2025-05-24 08:04:33,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:33,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:34,529 - INFO - API请求耗时: 703ms
2025-05-24 08:04:34,529 - INFO - Response - Page 21
2025-05-24 08:04:34,529 - INFO - 第 21 页获取到 100 条记录
2025-05-24 08:04:35,029 - INFO - Request Parameters - Page 22:
2025-05-24 08:04:35,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:35,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:35,733 - INFO - API请求耗时: 703ms
2025-05-24 08:04:35,733 - INFO - Response - Page 22
2025-05-24 08:04:35,733 - INFO - 第 22 页获取到 100 条记录
2025-05-24 08:04:36,248 - INFO - Request Parameters - Page 23:
2025-05-24 08:04:36,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:36,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:36,951 - INFO - API请求耗时: 703ms
2025-05-24 08:04:36,951 - INFO - Response - Page 23
2025-05-24 08:04:36,951 - INFO - 第 23 页获取到 100 条记录
2025-05-24 08:04:37,467 - INFO - Request Parameters - Page 24:
2025-05-24 08:04:37,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:37,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:38,311 - INFO - API请求耗时: 844ms
2025-05-24 08:04:38,311 - INFO - Response - Page 24
2025-05-24 08:04:38,311 - INFO - 第 24 页获取到 100 条记录
2025-05-24 08:04:38,811 - INFO - Request Parameters - Page 25:
2025-05-24 08:04:38,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:38,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:39,436 - INFO - API请求耗时: 625ms
2025-05-24 08:04:39,436 - INFO - Response - Page 25
2025-05-24 08:04:39,436 - INFO - 第 25 页获取到 100 条记录
2025-05-24 08:04:39,951 - INFO - Request Parameters - Page 26:
2025-05-24 08:04:39,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:39,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:40,686 - INFO - API请求耗时: 734ms
2025-05-24 08:04:40,686 - INFO - Response - Page 26
2025-05-24 08:04:40,686 - INFO - 第 26 页获取到 100 条记录
2025-05-24 08:04:41,201 - INFO - Request Parameters - Page 27:
2025-05-24 08:04:41,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:41,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:41,873 - INFO - API请求耗时: 672ms
2025-05-24 08:04:41,873 - INFO - Response - Page 27
2025-05-24 08:04:41,873 - INFO - 第 27 页获取到 100 条记录
2025-05-24 08:04:42,373 - INFO - Request Parameters - Page 28:
2025-05-24 08:04:42,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:42,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:43,061 - INFO - API请求耗时: 688ms
2025-05-24 08:04:43,061 - INFO - Response - Page 28
2025-05-24 08:04:43,061 - INFO - 第 28 页获取到 100 条记录
2025-05-24 08:04:43,576 - INFO - Request Parameters - Page 29:
2025-05-24 08:04:43,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:43,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:44,233 - INFO - API请求耗时: 656ms
2025-05-24 08:04:44,233 - INFO - Response - Page 29
2025-05-24 08:04:44,233 - INFO - 第 29 页获取到 100 条记录
2025-05-24 08:04:44,733 - INFO - Request Parameters - Page 30:
2025-05-24 08:04:44,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:44,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:45,436 - INFO - API请求耗时: 703ms
2025-05-24 08:04:45,436 - INFO - Response - Page 30
2025-05-24 08:04:45,436 - INFO - 第 30 页获取到 100 条记录
2025-05-24 08:04:45,951 - INFO - Request Parameters - Page 31:
2025-05-24 08:04:45,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:45,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:46,623 - INFO - API请求耗时: 672ms
2025-05-24 08:04:46,623 - INFO - Response - Page 31
2025-05-24 08:04:46,623 - INFO - 第 31 页获取到 100 条记录
2025-05-24 08:04:47,123 - INFO - Request Parameters - Page 32:
2025-05-24 08:04:47,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:47,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:47,779 - INFO - API请求耗时: 656ms
2025-05-24 08:04:47,779 - INFO - Response - Page 32
2025-05-24 08:04:47,779 - INFO - 第 32 页获取到 100 条记录
2025-05-24 08:04:48,279 - INFO - Request Parameters - Page 33:
2025-05-24 08:04:48,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:48,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:49,076 - INFO - API请求耗时: 797ms
2025-05-24 08:04:49,076 - INFO - Response - Page 33
2025-05-24 08:04:49,076 - INFO - 第 33 页获取到 100 条记录
2025-05-24 08:04:49,576 - INFO - Request Parameters - Page 34:
2025-05-24 08:04:49,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:49,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:50,311 - INFO - API请求耗时: 734ms
2025-05-24 08:04:50,311 - INFO - Response - Page 34
2025-05-24 08:04:50,311 - INFO - 第 34 页获取到 100 条记录
2025-05-24 08:04:50,826 - INFO - Request Parameters - Page 35:
2025-05-24 08:04:50,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:50,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:51,482 - INFO - API请求耗时: 656ms
2025-05-24 08:04:51,482 - INFO - Response - Page 35
2025-05-24 08:04:51,482 - INFO - 第 35 页获取到 100 条记录
2025-05-24 08:04:51,998 - INFO - Request Parameters - Page 36:
2025-05-24 08:04:51,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:51,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600875, 1746288000875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:52,607 - INFO - API请求耗时: 609ms
2025-05-24 08:04:52,607 - INFO - Response - Page 36
2025-05-24 08:04:52,607 - INFO - 第 36 页获取到 57 条记录
2025-05-24 08:04:52,607 - INFO - 查询完成，共获取到 3557 条记录
2025-05-24 08:04:52,607 - INFO - 分段 6 查询成功，获取到 3557 条记录
2025-05-24 08:04:53,623 - INFO - 查询分段 7: 2025-05-05 至 2025-05-11
2025-05-24 08:04:53,623 - INFO - 查询日期范围: 2025-05-05 至 2025-05-11，使用分页查询，每页 100 条记录
2025-05-24 08:04:53,623 - INFO - Request Parameters - Page 1:
2025-05-24 08:04:53,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:53,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:54,451 - INFO - API请求耗时: 828ms
2025-05-24 08:04:54,451 - INFO - Response - Page 1
2025-05-24 08:04:54,451 - INFO - 第 1 页获取到 100 条记录
2025-05-24 08:04:54,967 - INFO - Request Parameters - Page 2:
2025-05-24 08:04:54,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:54,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:55,826 - INFO - API请求耗时: 859ms
2025-05-24 08:04:55,826 - INFO - Response - Page 2
2025-05-24 08:04:55,826 - INFO - 第 2 页获取到 100 条记录
2025-05-24 08:04:56,326 - INFO - Request Parameters - Page 3:
2025-05-24 08:04:56,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:56,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:56,967 - INFO - API请求耗时: 641ms
2025-05-24 08:04:56,967 - INFO - Response - Page 3
2025-05-24 08:04:56,967 - INFO - 第 3 页获取到 100 条记录
2025-05-24 08:04:57,467 - INFO - Request Parameters - Page 4:
2025-05-24 08:04:57,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:57,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:58,170 - INFO - API请求耗时: 703ms
2025-05-24 08:04:58,170 - INFO - Response - Page 4
2025-05-24 08:04:58,170 - INFO - 第 4 页获取到 100 条记录
2025-05-24 08:04:58,686 - INFO - Request Parameters - Page 5:
2025-05-24 08:04:58,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:58,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:04:59,389 - INFO - API请求耗时: 703ms
2025-05-24 08:04:59,389 - INFO - Response - Page 5
2025-05-24 08:04:59,389 - INFO - 第 5 页获取到 100 条记录
2025-05-24 08:04:59,904 - INFO - Request Parameters - Page 6:
2025-05-24 08:04:59,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:04:59,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:00,545 - INFO - API请求耗时: 641ms
2025-05-24 08:05:00,545 - INFO - Response - Page 6
2025-05-24 08:05:00,545 - INFO - 第 6 页获取到 100 条记录
2025-05-24 08:05:01,045 - INFO - Request Parameters - Page 7:
2025-05-24 08:05:01,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:01,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:01,779 - INFO - API请求耗时: 734ms
2025-05-24 08:05:01,779 - INFO - Response - Page 7
2025-05-24 08:05:01,779 - INFO - 第 7 页获取到 100 条记录
2025-05-24 08:05:02,279 - INFO - Request Parameters - Page 8:
2025-05-24 08:05:02,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:02,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:02,982 - INFO - API请求耗时: 703ms
2025-05-24 08:05:02,982 - INFO - Response - Page 8
2025-05-24 08:05:02,982 - INFO - 第 8 页获取到 100 条记录
2025-05-24 08:05:03,498 - INFO - Request Parameters - Page 9:
2025-05-24 08:05:03,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:03,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:04,186 - INFO - API请求耗时: 687ms
2025-05-24 08:05:04,186 - INFO - Response - Page 9
2025-05-24 08:05:04,186 - INFO - 第 9 页获取到 100 条记录
2025-05-24 08:05:04,686 - INFO - Request Parameters - Page 10:
2025-05-24 08:05:04,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:04,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:05,326 - INFO - API请求耗时: 641ms
2025-05-24 08:05:05,326 - INFO - Response - Page 10
2025-05-24 08:05:05,326 - INFO - 第 10 页获取到 100 条记录
2025-05-24 08:05:05,842 - INFO - Request Parameters - Page 11:
2025-05-24 08:05:05,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:05,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:06,701 - INFO - API请求耗时: 859ms
2025-05-24 08:05:06,717 - INFO - Response - Page 11
2025-05-24 08:05:06,717 - INFO - 第 11 页获取到 100 条记录
2025-05-24 08:05:07,217 - INFO - Request Parameters - Page 12:
2025-05-24 08:05:07,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:07,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:07,904 - INFO - API请求耗时: 687ms
2025-05-24 08:05:07,904 - INFO - Response - Page 12
2025-05-24 08:05:07,904 - INFO - 第 12 页获取到 100 条记录
2025-05-24 08:05:08,420 - INFO - Request Parameters - Page 13:
2025-05-24 08:05:08,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:08,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:09,123 - INFO - API请求耗时: 703ms
2025-05-24 08:05:09,123 - INFO - Response - Page 13
2025-05-24 08:05:09,123 - INFO - 第 13 页获取到 100 条记录
2025-05-24 08:05:09,623 - INFO - Request Parameters - Page 14:
2025-05-24 08:05:09,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:09,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:10,295 - INFO - API请求耗时: 672ms
2025-05-24 08:05:10,295 - INFO - Response - Page 14
2025-05-24 08:05:10,295 - INFO - 第 14 页获取到 100 条记录
2025-05-24 08:05:10,811 - INFO - Request Parameters - Page 15:
2025-05-24 08:05:10,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:10,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:11,482 - INFO - API请求耗时: 672ms
2025-05-24 08:05:11,482 - INFO - Response - Page 15
2025-05-24 08:05:11,482 - INFO - 第 15 页获取到 100 条记录
2025-05-24 08:05:11,998 - INFO - Request Parameters - Page 16:
2025-05-24 08:05:11,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:11,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:12,670 - INFO - API请求耗时: 672ms
2025-05-24 08:05:12,670 - INFO - Response - Page 16
2025-05-24 08:05:12,670 - INFO - 第 16 页获取到 100 条记录
2025-05-24 08:05:13,170 - INFO - Request Parameters - Page 17:
2025-05-24 08:05:13,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:13,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:13,951 - INFO - API请求耗时: 781ms
2025-05-24 08:05:13,951 - INFO - Response - Page 17
2025-05-24 08:05:13,951 - INFO - 第 17 页获取到 100 条记录
2025-05-24 08:05:14,467 - INFO - Request Parameters - Page 18:
2025-05-24 08:05:14,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:14,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:15,232 - INFO - API请求耗时: 766ms
2025-05-24 08:05:15,232 - INFO - Response - Page 18
2025-05-24 08:05:15,232 - INFO - 第 18 页获取到 100 条记录
2025-05-24 08:05:15,748 - INFO - Request Parameters - Page 19:
2025-05-24 08:05:15,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:15,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:16,420 - INFO - API请求耗时: 672ms
2025-05-24 08:05:16,420 - INFO - Response - Page 19
2025-05-24 08:05:16,420 - INFO - 第 19 页获取到 100 条记录
2025-05-24 08:05:16,935 - INFO - Request Parameters - Page 20:
2025-05-24 08:05:16,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:16,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:17,685 - INFO - API请求耗时: 750ms
2025-05-24 08:05:17,685 - INFO - Response - Page 20
2025-05-24 08:05:17,685 - INFO - 第 20 页获取到 100 条记录
2025-05-24 08:05:18,185 - INFO - Request Parameters - Page 21:
2025-05-24 08:05:18,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:18,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:18,967 - INFO - API请求耗时: 781ms
2025-05-24 08:05:18,967 - INFO - Response - Page 21
2025-05-24 08:05:18,967 - INFO - 第 21 页获取到 100 条记录
2025-05-24 08:05:19,482 - INFO - Request Parameters - Page 22:
2025-05-24 08:05:19,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:19,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:20,232 - INFO - API请求耗时: 750ms
2025-05-24 08:05:20,232 - INFO - Response - Page 22
2025-05-24 08:05:20,232 - INFO - 第 22 页获取到 100 条记录
2025-05-24 08:05:20,732 - INFO - Request Parameters - Page 23:
2025-05-24 08:05:20,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:20,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:21,404 - INFO - API请求耗时: 672ms
2025-05-24 08:05:21,404 - INFO - Response - Page 23
2025-05-24 08:05:21,404 - INFO - 第 23 页获取到 100 条记录
2025-05-24 08:05:21,904 - INFO - Request Parameters - Page 24:
2025-05-24 08:05:21,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:21,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:22,639 - INFO - API请求耗时: 734ms
2025-05-24 08:05:22,639 - INFO - Response - Page 24
2025-05-24 08:05:22,639 - INFO - 第 24 页获取到 100 条记录
2025-05-24 08:05:23,170 - INFO - Request Parameters - Page 25:
2025-05-24 08:05:23,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:23,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:23,889 - INFO - API请求耗时: 703ms
2025-05-24 08:05:23,889 - INFO - Response - Page 25
2025-05-24 08:05:23,889 - INFO - 第 25 页获取到 100 条记录
2025-05-24 08:05:24,404 - INFO - Request Parameters - Page 26:
2025-05-24 08:05:24,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:24,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:25,107 - INFO - API请求耗时: 703ms
2025-05-24 08:05:25,107 - INFO - Response - Page 26
2025-05-24 08:05:25,107 - INFO - 第 26 页获取到 100 条记录
2025-05-24 08:05:25,607 - INFO - Request Parameters - Page 27:
2025-05-24 08:05:25,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:25,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:26,248 - INFO - API请求耗时: 641ms
2025-05-24 08:05:26,248 - INFO - Response - Page 27
2025-05-24 08:05:26,248 - INFO - 第 27 页获取到 100 条记录
2025-05-24 08:05:26,748 - INFO - Request Parameters - Page 28:
2025-05-24 08:05:26,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:26,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:27,451 - INFO - API请求耗时: 703ms
2025-05-24 08:05:27,451 - INFO - Response - Page 28
2025-05-24 08:05:27,451 - INFO - 第 28 页获取到 100 条记录
2025-05-24 08:05:27,951 - INFO - Request Parameters - Page 29:
2025-05-24 08:05:27,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:27,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:28,654 - INFO - API请求耗时: 703ms
2025-05-24 08:05:28,654 - INFO - Response - Page 29
2025-05-24 08:05:28,654 - INFO - 第 29 页获取到 100 条记录
2025-05-24 08:05:29,154 - INFO - Request Parameters - Page 30:
2025-05-24 08:05:29,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:29,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400875, 1746892800875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:29,732 - INFO - API请求耗时: 578ms
2025-05-24 08:05:29,748 - INFO - Response - Page 30
2025-05-24 08:05:29,748 - INFO - 第 30 页获取到 43 条记录
2025-05-24 08:05:29,748 - INFO - 查询完成，共获取到 2943 条记录
2025-05-24 08:05:29,748 - INFO - 分段 7 查询成功，获取到 2943 条记录
2025-05-24 08:05:30,763 - INFO - 查询分段 8: 2025-05-12 至 2025-05-18
2025-05-24 08:05:30,763 - INFO - 查询日期范围: 2025-05-12 至 2025-05-18，使用分页查询，每页 100 条记录
2025-05-24 08:05:30,763 - INFO - Request Parameters - Page 1:
2025-05-24 08:05:30,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:30,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:31,435 - INFO - API请求耗时: 672ms
2025-05-24 08:05:31,435 - INFO - Response - Page 1
2025-05-24 08:05:31,435 - INFO - 第 1 页获取到 100 条记录
2025-05-24 08:05:31,951 - INFO - Request Parameters - Page 2:
2025-05-24 08:05:31,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:31,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:32,592 - INFO - API请求耗时: 641ms
2025-05-24 08:05:32,592 - INFO - Response - Page 2
2025-05-24 08:05:32,592 - INFO - 第 2 页获取到 100 条记录
2025-05-24 08:05:33,092 - INFO - Request Parameters - Page 3:
2025-05-24 08:05:33,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:33,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:33,826 - INFO - API请求耗时: 734ms
2025-05-24 08:05:33,826 - INFO - Response - Page 3
2025-05-24 08:05:33,842 - INFO - 第 3 页获取到 100 条记录
2025-05-24 08:05:34,342 - INFO - Request Parameters - Page 4:
2025-05-24 08:05:34,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:34,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:34,982 - INFO - API请求耗时: 641ms
2025-05-24 08:05:34,982 - INFO - Response - Page 4
2025-05-24 08:05:34,982 - INFO - 第 4 页获取到 100 条记录
2025-05-24 08:05:35,498 - INFO - Request Parameters - Page 5:
2025-05-24 08:05:35,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:35,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:36,217 - INFO - API请求耗时: 719ms
2025-05-24 08:05:36,217 - INFO - Response - Page 5
2025-05-24 08:05:36,217 - INFO - 第 5 页获取到 100 条记录
2025-05-24 08:05:36,732 - INFO - Request Parameters - Page 6:
2025-05-24 08:05:36,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:36,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:37,435 - INFO - API请求耗时: 703ms
2025-05-24 08:05:37,451 - INFO - Response - Page 6
2025-05-24 08:05:37,451 - INFO - 第 6 页获取到 100 条记录
2025-05-24 08:05:37,951 - INFO - Request Parameters - Page 7:
2025-05-24 08:05:37,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:37,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:38,654 - INFO - API请求耗时: 703ms
2025-05-24 08:05:38,654 - INFO - Response - Page 7
2025-05-24 08:05:38,654 - INFO - 第 7 页获取到 100 条记录
2025-05-24 08:05:39,170 - INFO - Request Parameters - Page 8:
2025-05-24 08:05:39,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:39,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:39,873 - INFO - API请求耗时: 703ms
2025-05-24 08:05:39,873 - INFO - Response - Page 8
2025-05-24 08:05:39,873 - INFO - 第 8 页获取到 100 条记录
2025-05-24 08:05:40,373 - INFO - Request Parameters - Page 9:
2025-05-24 08:05:40,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:40,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:41,013 - INFO - API请求耗时: 641ms
2025-05-24 08:05:41,013 - INFO - Response - Page 9
2025-05-24 08:05:41,013 - INFO - 第 9 页获取到 100 条记录
2025-05-24 08:05:41,513 - INFO - Request Parameters - Page 10:
2025-05-24 08:05:41,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:41,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:42,310 - INFO - API请求耗时: 797ms
2025-05-24 08:05:42,310 - INFO - Response - Page 10
2025-05-24 08:05:42,310 - INFO - 第 10 页获取到 100 条记录
2025-05-24 08:05:42,826 - INFO - Request Parameters - Page 11:
2025-05-24 08:05:42,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:42,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:43,498 - INFO - API请求耗时: 672ms
2025-05-24 08:05:43,498 - INFO - Response - Page 11
2025-05-24 08:05:43,498 - INFO - 第 11 页获取到 100 条记录
2025-05-24 08:05:44,013 - INFO - Request Parameters - Page 12:
2025-05-24 08:05:44,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:44,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:44,685 - INFO - API请求耗时: 672ms
2025-05-24 08:05:44,685 - INFO - Response - Page 12
2025-05-24 08:05:44,685 - INFO - 第 12 页获取到 100 条记录
2025-05-24 08:05:45,185 - INFO - Request Parameters - Page 13:
2025-05-24 08:05:45,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:45,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:45,857 - INFO - API请求耗时: 672ms
2025-05-24 08:05:45,857 - INFO - Response - Page 13
2025-05-24 08:05:45,857 - INFO - 第 13 页获取到 100 条记录
2025-05-24 08:05:46,357 - INFO - Request Parameters - Page 14:
2025-05-24 08:05:46,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:46,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:47,013 - INFO - API请求耗时: 656ms
2025-05-24 08:05:47,013 - INFO - Response - Page 14
2025-05-24 08:05:47,013 - INFO - 第 14 页获取到 100 条记录
2025-05-24 08:05:47,513 - INFO - Request Parameters - Page 15:
2025-05-24 08:05:47,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:47,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:48,201 - INFO - API请求耗时: 688ms
2025-05-24 08:05:48,201 - INFO - Response - Page 15
2025-05-24 08:05:48,201 - INFO - 第 15 页获取到 100 条记录
2025-05-24 08:05:48,716 - INFO - Request Parameters - Page 16:
2025-05-24 08:05:48,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:48,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:49,357 - INFO - API请求耗时: 641ms
2025-05-24 08:05:49,357 - INFO - Response - Page 16
2025-05-24 08:05:49,357 - INFO - 第 16 页获取到 100 条记录
2025-05-24 08:05:49,857 - INFO - Request Parameters - Page 17:
2025-05-24 08:05:49,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:49,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:50,591 - INFO - API请求耗时: 734ms
2025-05-24 08:05:50,591 - INFO - Response - Page 17
2025-05-24 08:05:50,591 - INFO - 第 17 页获取到 100 条记录
2025-05-24 08:05:51,107 - INFO - Request Parameters - Page 18:
2025-05-24 08:05:51,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:51,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:51,795 - INFO - API请求耗时: 687ms
2025-05-24 08:05:51,795 - INFO - Response - Page 18
2025-05-24 08:05:51,795 - INFO - 第 18 页获取到 100 条记录
2025-05-24 08:05:52,295 - INFO - Request Parameters - Page 19:
2025-05-24 08:05:52,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:52,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:52,982 - INFO - API请求耗时: 687ms
2025-05-24 08:05:52,982 - INFO - Response - Page 19
2025-05-24 08:05:52,982 - INFO - 第 19 页获取到 100 条记录
2025-05-24 08:05:53,482 - INFO - Request Parameters - Page 20:
2025-05-24 08:05:53,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:53,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:54,232 - INFO - API请求耗时: 750ms
2025-05-24 08:05:54,248 - INFO - Response - Page 20
2025-05-24 08:05:54,248 - INFO - 第 20 页获取到 100 条记录
2025-05-24 08:05:54,763 - INFO - Request Parameters - Page 21:
2025-05-24 08:05:54,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:54,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:55,435 - INFO - API请求耗时: 672ms
2025-05-24 08:05:55,435 - INFO - Response - Page 21
2025-05-24 08:05:55,451 - INFO - 第 21 页获取到 100 条记录
2025-05-24 08:05:55,951 - INFO - Request Parameters - Page 22:
2025-05-24 08:05:55,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:55,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:56,763 - INFO - API请求耗时: 812ms
2025-05-24 08:05:56,763 - INFO - Response - Page 22
2025-05-24 08:05:56,763 - INFO - 第 22 页获取到 100 条记录
2025-05-24 08:05:57,263 - INFO - Request Parameters - Page 23:
2025-05-24 08:05:57,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:57,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:57,951 - INFO - API请求耗时: 688ms
2025-05-24 08:05:57,951 - INFO - Response - Page 23
2025-05-24 08:05:57,951 - INFO - 第 23 页获取到 100 条记录
2025-05-24 08:05:58,466 - INFO - Request Parameters - Page 24:
2025-05-24 08:05:58,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:58,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:05:59,138 - INFO - API请求耗时: 672ms
2025-05-24 08:05:59,138 - INFO - Response - Page 24
2025-05-24 08:05:59,138 - INFO - 第 24 页获取到 100 条记录
2025-05-24 08:05:59,638 - INFO - Request Parameters - Page 25:
2025-05-24 08:05:59,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:05:59,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:06:00,341 - INFO - API请求耗时: 703ms
2025-05-24 08:06:00,341 - INFO - Response - Page 25
2025-05-24 08:06:00,341 - INFO - 第 25 页获取到 100 条记录
2025-05-24 08:06:00,841 - INFO - Request Parameters - Page 26:
2025-05-24 08:06:00,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:06:00,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:06:01,466 - INFO - API请求耗时: 625ms
2025-05-24 08:06:01,466 - INFO - Response - Page 26
2025-05-24 08:06:01,466 - INFO - 第 26 页获取到 100 条记录
2025-05-24 08:06:01,966 - INFO - Request Parameters - Page 27:
2025-05-24 08:06:01,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:06:01,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:06:02,623 - INFO - API请求耗时: 656ms
2025-05-24 08:06:02,623 - INFO - Response - Page 27
2025-05-24 08:06:02,623 - INFO - 第 27 页获取到 100 条记录
2025-05-24 08:06:03,123 - INFO - Request Parameters - Page 28:
2025-05-24 08:06:03,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:06:03,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200875, 1747497600875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:06:03,560 - INFO - API请求耗时: 437ms
2025-05-24 08:06:03,560 - INFO - Response - Page 28
2025-05-24 08:06:03,560 - INFO - 第 28 页获取到 17 条记录
2025-05-24 08:06:03,560 - INFO - 查询完成，共获取到 2717 条记录
2025-05-24 08:06:03,560 - INFO - 分段 8 查询成功，获取到 2717 条记录
2025-05-24 08:06:04,560 - INFO - 查询分段 9: 2025-05-19 至 2025-05-23
2025-05-24 08:06:04,560 - INFO - 查询日期范围: 2025-05-19 至 2025-05-23，使用分页查询，每页 100 条记录
2025-05-24 08:06:04,560 - INFO - Request Parameters - Page 1:
2025-05-24 08:06:04,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:06:04,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000875, 1748015999875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:06:05,216 - INFO - API请求耗时: 656ms
2025-05-24 08:06:05,216 - INFO - Response - Page 1
2025-05-24 08:06:05,216 - INFO - 第 1 页获取到 100 条记录
2025-05-24 08:06:05,732 - INFO - Request Parameters - Page 2:
2025-05-24 08:06:05,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:06:05,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000875, 1748015999875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:06:06,420 - INFO - API请求耗时: 687ms
2025-05-24 08:06:06,420 - INFO - Response - Page 2
2025-05-24 08:06:06,420 - INFO - 第 2 页获取到 100 条记录
2025-05-24 08:06:06,920 - INFO - Request Parameters - Page 3:
2025-05-24 08:06:06,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:06:06,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000875, 1748015999875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:06:07,576 - INFO - API请求耗时: 656ms
2025-05-24 08:06:07,576 - INFO - Response - Page 3
2025-05-24 08:06:07,576 - INFO - 第 3 页获取到 100 条记录
2025-05-24 08:06:08,091 - INFO - Request Parameters - Page 4:
2025-05-24 08:06:08,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:06:08,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000875, 1748015999875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:06:08,810 - INFO - API请求耗时: 719ms
2025-05-24 08:06:08,810 - INFO - Response - Page 4
2025-05-24 08:06:08,810 - INFO - 第 4 页获取到 100 条记录
2025-05-24 08:06:09,326 - INFO - Request Parameters - Page 5:
2025-05-24 08:06:09,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:06:09,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000875, 1748015999875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:06:10,029 - INFO - API请求耗时: 703ms
2025-05-24 08:06:10,029 - INFO - Response - Page 5
2025-05-24 08:06:10,029 - INFO - 第 5 页获取到 100 条记录
2025-05-24 08:06:10,544 - INFO - Request Parameters - Page 6:
2025-05-24 08:06:10,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:06:10,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000875, 1748015999875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:06:11,201 - INFO - API请求耗时: 656ms
2025-05-24 08:06:11,201 - INFO - Response - Page 6
2025-05-24 08:06:11,201 - INFO - 第 6 页获取到 100 条记录
2025-05-24 08:06:11,716 - INFO - Request Parameters - Page 7:
2025-05-24 08:06:11,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:06:11,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000875, 1748015999875], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:06:12,185 - INFO - API请求耗时: 469ms
2025-05-24 08:06:12,185 - INFO - Response - Page 7
2025-05-24 08:06:12,185 - INFO - 第 7 页获取到 12 条记录
2025-05-24 08:06:12,185 - INFO - 查询完成，共获取到 612 条记录
2025-05-24 08:06:12,185 - INFO - 分段 9 查询成功，获取到 612 条记录
2025-05-24 08:06:13,185 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 24732 条记录，失败 0 次
2025-05-24 08:06:13,185 - INFO - 成功获取宜搭日销售表单数据，共 24732 条记录
2025-05-24 08:06:13,185 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-24 08:06:13,185 - INFO - 开始对比和同步日销售数据...
2025-05-24 08:06:13,904 - INFO - 成功创建宜搭日销售数据索引，共 10896 条记录
2025-05-24 08:06:13,904 - INFO - 开始处理数衍数据，共 13038 条记录
2025-05-24 08:06:14,404 - INFO - 更新表单数据成功: FINST-LR5668B1A8MV019U7PKD88YMKPPL3W113MYAMN9
2025-05-24 08:06:14,404 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250405, 变更字段: [{'field': 'recommendAmount', 'old_value': 27815.44, 'new_value': 27569.44}, {'field': 'amount', 'old_value': 27815.44, 'new_value': 27569.44}, {'field': 'count', 'old_value': 75, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 25737.2, 'new_value': 25491.2}, {'field': 'instoreCount', 'old_value': 58, 'new_value': 57}]
2025-05-24 08:06:14,857 - INFO - 更新表单数据成功: FINST-2S666NA1CFHVEXMF8FYP7AG2VAXZ2WA1QBUAM7R
2025-05-24 08:06:14,857 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250402, 变更字段: [{'field': 'recommendAmount', 'old_value': 13481.01, 'new_value': 13143.01}, {'field': 'amount', 'old_value': 13481.01, 'new_value': 13143.01}, {'field': 'count', 'old_value': 38, 'new_value': 37}, {'field': 'instoreAmount', 'old_value': 12696.0, 'new_value': 12358.0}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 30}]
2025-05-24 08:06:15,326 - INFO - 更新表单数据成功: FINST-DIC66I91SAMVST9DCSCBM6903HNN3OB63MYAM3C
2025-05-24 08:06:15,326 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250412, 变更字段: [{'field': 'recommendAmount', 'old_value': 26559.69, 'new_value': 25183.69}, {'field': 'amount', 'old_value': 26559.69, 'new_value': 25183.69}, {'field': 'count', 'old_value': 87, 'new_value': 83}, {'field': 'instoreAmount', 'old_value': 24000.0, 'new_value': 22624.0}, {'field': 'instoreCount', 'old_value': 66, 'new_value': 62}]
2025-05-24 08:06:15,748 - INFO - 更新表单数据成功: FINST-COC668A17ALVSCIB9B1EB43MUAB52CPYM6XAM8H
2025-05-24 08:06:15,748 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250411, 变更字段: [{'field': 'recommendAmount', 'old_value': 18881.27, 'new_value': 18418.67}, {'field': 'amount', 'old_value': 18881.27, 'new_value': 18418.67}, {'field': 'count', 'old_value': 58, 'new_value': 57}, {'field': 'instoreAmount', 'old_value': 17778.2, 'new_value': 17315.6}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 48}]
2025-05-24 08:06:16,216 - INFO - 更新表单数据成功: FINST-2FD66I714CHVHH31B9T1B8JDL0CA2BHW9WSAMTG
2025-05-24 08:06:16,216 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250408, 变更字段: [{'field': 'recommendAmount', 'old_value': 5801.88, 'new_value': 5534.88}, {'field': 'amount', 'old_value': 5801.88, 'new_value': 5534.88}, {'field': 'count', 'old_value': 32, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 4461.2, 'new_value': 4194.2}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 17}]
2025-05-24 08:06:16,607 - INFO - 更新表单数据成功: FINST-W4G66DA18DMVLYCT8J33MB6KPHOB2AMB3MYAMG9
2025-05-24 08:06:16,607 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250419, 变更字段: [{'field': 'recommendAmount', 'old_value': 42497.8, 'new_value': 42139.8}, {'field': 'amount', 'old_value': 42497.8, 'new_value': 42139.8}, {'field': 'count', 'old_value': 98, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 39379.8, 'new_value': 39021.8}, {'field': 'instoreCount', 'old_value': 77, 'new_value': 76}]
2025-05-24 08:06:17,076 - INFO - 更新表单数据成功: FINST-XL866HB18ZJV67U76A57O5FQKQB43CLP6RVAMSB
2025-05-24 08:06:17,091 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250417, 变更字段: [{'field': 'recommendAmount', 'old_value': 32698.32, 'new_value': 31462.32}, {'field': 'amount', 'old_value': 32698.32, 'new_value': 31462.32}, {'field': 'count', 'old_value': 64, 'new_value': 62}, {'field': 'instoreAmount', 'old_value': 32787.6, 'new_value': 31551.6}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 55}]
2025-05-24 08:06:17,576 - INFO - 更新表单数据成功: FINST-EEC66XC1YEJV5AF6B7V4P5PW0C753ZYBQBUAMK9
2025-05-24 08:06:17,576 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250416, 变更字段: [{'field': 'recommendAmount', 'old_value': 35630.72, 'new_value': 34264.72}, {'field': 'amount', 'old_value': 35630.72, 'new_value': 34264.72}, {'field': 'count', 'old_value': 64, 'new_value': 63}, {'field': 'instoreAmount', 'old_value': 35110.1, 'new_value': 33744.1}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 56}]
2025-05-24 08:06:18,076 - INFO - 更新表单数据成功: FINST-1OC66A912FMVEZOXB2AU96KHWKFM37OJ3MYAMP
2025-05-24 08:06:18,076 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250426, 变更字段: [{'field': 'recommendAmount', 'old_value': 55972.28, 'new_value': 55674.28}, {'field': 'amount', 'old_value': 55972.28, 'new_value': 55674.28}, {'field': 'count', 'old_value': 87, 'new_value': 86}, {'field': 'instoreAmount', 'old_value': 53162.6, 'new_value': 52864.6}, {'field': 'instoreCount', 'old_value': 68, 'new_value': 67}]
2025-05-24 08:06:18,498 - INFO - 更新表单数据成功: FINST-1MD668B17BHVW87290IGOAUA2PXO3P57AWSAMVP
2025-05-24 08:06:18,498 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250422, 变更字段: [{'field': 'recommendAmount', 'old_value': 27966.15, 'new_value': 27468.15}, {'field': 'amount', 'old_value': 27966.15, 'new_value': 27468.15}, {'field': 'count', 'old_value': 63, 'new_value': 62}, {'field': 'instoreAmount', 'old_value': 26436.2, 'new_value': 25938.2}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 50}]
2025-05-24 08:06:19,076 - INFO - 更新表单数据成功: FINST-OPC666D1A9ZUDDZ7BP7MP6WG7ZZM3GKUB16AMKK
2025-05-24 08:06:19,076 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_20250501, 变更字段: [{'field': 'amount', 'old_value': 13369.29, 'new_value': 13575.49}, {'field': 'count', 'old_value': 132, 'new_value': 133}, {'field': 'instoreAmount', 'old_value': 3849.77, 'new_value': 4055.97}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 26}]
2025-05-24 08:06:19,498 - INFO - 更新表单数据成功: FINST-V7966QC1LOZUPG4Y7TA0F4DVNM7Y3BXSRG7AMGR
2025-05-24 08:06:19,498 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 4351.6, 'new_value': 4429.6}, {'field': 'dailyBillAmount', 'old_value': 4351.6, 'new_value': 4429.6}]
2025-05-24 08:06:19,998 - INFO - 更新表单数据成功: FINST-FQD66YB1YCKVXZJE9RX6JCSC5BEW2KJX6RVAMA8
2025-05-24 08:06:19,998 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 15127.1, 'new_value': 15095.06}, {'field': 'amount', 'old_value': 15127.1, 'new_value': 15095.06}]
2025-05-24 08:06:20,435 - INFO - 更新表单数据成功: FINST-AAG66KB1G9GV8UW1BTUM15QTB46I290789PAML1
2025-05-24 08:06:20,435 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_20250503, 变更字段: [{'field': 'amount', 'old_value': 10595.7, 'new_value': 10728.8}, {'field': 'count', 'old_value': 41, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 10848.8, 'new_value': 10981.9}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 41}]
2025-05-24 08:06:20,919 - INFO - 更新表单数据成功: FINST-OLC66Z61NMZUP7N7EV2BF72NA4CI2SD0OBAAM1B1
2025-05-24 08:06:20,919 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_20250501, 变更字段: [{'field': 'amount', 'old_value': 12908.0, 'new_value': 12441.0}, {'field': 'count', 'old_value': 55, 'new_value': 54}, {'field': 'instoreAmount', 'old_value': 12908.0, 'new_value': 12441.0}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 54}]
2025-05-24 08:06:21,373 - INFO - 更新表单数据成功: FINST-KLF66WC106MVED2LBCIGS96FFKMH280P3MYAMCC
2025-05-24 08:06:21,373 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250503, 变更字段: [{'field': 'recommendAmount', 'old_value': 53839.98, 'new_value': 53501.98}, {'field': 'amount', 'old_value': 53839.98, 'new_value': 53501.98}, {'field': 'count', 'old_value': 90, 'new_value': 89}, {'field': 'instoreAmount', 'old_value': 51383.0, 'new_value': 51045.0}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 68}]
2025-05-24 08:06:21,748 - INFO - 更新表单数据成功: FINST-AJF66F71WBHV9KQKA9FXRBZU5O0C2CNMQBUAMHT
2025-05-24 08:06:21,748 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250430, 变更字段: [{'field': 'recommendAmount', 'old_value': 30706.46, 'new_value': 29250.46}, {'field': 'amount', 'old_value': 30706.46, 'new_value': 29250.46}, {'field': 'count', 'old_value': 67, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 28282.4, 'new_value': 26826.4}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 48}]
2025-05-24 08:06:22,185 - INFO - 更新表单数据成功: FINST-1OC66A91MWFVNQ10EE1LFCM14YOP3JGX89PAMV3
2025-05-24 08:06:22,185 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10966.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10966.0}]
2025-05-24 08:06:22,654 - INFO - 更新表单数据成功: FINST-KLF66WC106MVED2LBCIGS96FFKMH290P3MYAMBE
2025-05-24 08:06:22,654 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250510, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8105.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8105.0}]
2025-05-24 08:06:23,123 - INFO - 更新表单数据成功: FINST-AAG66KB1G9GV8UW1BTUM15QTB46I290789PAMH1
2025-05-24 08:06:23,123 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_20250507, 变更字段: [{'field': 'amount', 'old_value': 1567.1, 'new_value': 1784.9}, {'field': 'count', 'old_value': 12, 'new_value': 13}, {'field': 'instoreAmount', 'old_value': 1525.6, 'new_value': 1743.4}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 10}]
2025-05-24 08:06:23,607 - INFO - 更新表单数据成功: FINST-LFA66G91HBGVRHCZ5R1A79KXRZ9U181899PAM31
2025-05-24 08:06:23,607 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 30290.26, 'new_value': 31109.26}, {'field': 'amount', 'old_value': 30290.26, 'new_value': 31109.26}, {'field': 'count', 'old_value': 60, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 30290.26, 'new_value': 31109.26}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 61}]
2025-05-24 08:06:24,076 - INFO - 更新表单数据成功: FINST-XL866HB1NAGVD1TYEI9H79REHI3F3TWF99PAM16
2025-05-24 08:06:24,076 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 34350.76, 'new_value': 32944.76}, {'field': 'amount', 'old_value': 34350.76, 'new_value': 32944.76}, {'field': 'count', 'old_value': 94, 'new_value': 92}, {'field': 'instoreAmount', 'old_value': 31333.0, 'new_value': 29927.0}, {'field': 'instoreCount', 'old_value': 71, 'new_value': 69}]
2025-05-24 08:06:24,560 - INFO - 更新表单数据成功: FINST-3Z966E9168LVW3LBAYAOH823QYRK26EMN6XAM4J
2025-05-24 08:06:24,560 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250509, 变更字段: [{'field': 'recommendAmount', 'old_value': 16070.84, 'new_value': 15732.84}, {'field': 'amount', 'old_value': 16070.84, 'new_value': 15732.84}, {'field': 'count', 'old_value': 49, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 15034.1, 'new_value': 14696.1}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 36}]
2025-05-24 08:06:25,029 - INFO - 更新表单数据成功: FINST-SED66Q617FHV83DJALKM26VLZ7WU21PHAWSAMTI
2025-05-24 08:06:25,029 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250506, 变更字段: [{'field': 'recommendAmount', 'old_value': 17684.98, 'new_value': 14376.98}, {'field': 'amount', 'old_value': 17684.98, 'new_value': 14376.98}, {'field': 'count', 'old_value': 39, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 16501.0, 'new_value': 13193.0}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 25}]
2025-05-24 08:06:25,404 - INFO - 更新表单数据成功: FINST-6PF66691UBHVRYF3EOUJHC43R95U2MLUQBUAM9T
2025-05-24 08:06:25,404 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_********, 变更字段: [{'field': 'amount', 'old_value': 4773.36, 'new_value': 4778.36}, {'field': 'count', 'old_value': 66, 'new_value': 67}, {'field': 'instoreAmount', 'old_value': 1987.96, 'new_value': 1992.96}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 36}]
2025-05-24 08:06:25,873 - INFO - 更新表单数据成功: FINST-B2766R81LBLVS8FVCIXSE88YWGI5360PN6XAMMF
2025-05-24 08:06:25,873 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 12846.0, 'new_value': 14697.5}, {'field': 'amount', 'old_value': 12846.0, 'new_value': 14697.5}, {'field': 'count', 'old_value': 334, 'new_value': 335}, {'field': 'instoreAmount', 'old_value': 12208.7, 'new_value': 14060.2}, {'field': 'instoreCount', 'old_value': 314, 'new_value': 315}]
2025-05-24 08:06:26,404 - INFO - 更新表单数据成功: FINST-XBF66071SBMVMRJKFB1RH6AFE9QZ1P2X3MYAM8A
2025-05-24 08:06:26,404 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9350.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9350.0}]
2025-05-24 08:06:26,779 - INFO - 更新表单数据成功: FINST-B2766R81LBLVS8FVCIXSE88YWGI5360PN6XAMZF
2025-05-24 08:06:26,779 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8178.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8178.0}]
2025-05-24 08:06:27,279 - INFO - 更新表单数据成功: FINST-8LC66GC10EKVH1QRFQ06H980BXSM2ITA7RVAML1
2025-05-24 08:06:27,279 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1772.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1772.0}]
2025-05-24 08:06:27,779 - INFO - 更新表单数据成功: FINST-OIF66RB166JVHL8L82N5R9Q6PWVG3N8XQBUAM4I
2025-05-24 08:06:27,779 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250514, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4971.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4971.0}]
2025-05-24 08:06:28,216 - INFO - 更新表单数据成功: FINST-OIF66RB1WBHVN0LD9Q95I9QQL12W3LZMAWSAM2L
2025-05-24 08:06:28,216 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250513, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 12302.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 12302.0}]
2025-05-24 08:06:28,669 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP35ASAWSAMO2
2025-05-24 08:06:28,669 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_20250517, 变更字段: [{'field': 'amount', 'old_value': 46011.0, 'new_value': 46548.0}, {'field': 'count', 'old_value': 161, 'new_value': 162}, {'field': 'instoreAmount', 'old_value': 46011.0, 'new_value': 46548.0}, {'field': 'instoreCount', 'old_value': 161, 'new_value': 162}]
2025-05-24 08:06:29,154 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3TYFD1QAM67
2025-05-24 08:06:29,154 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_20250515, 变更字段: [{'field': 'amount', 'old_value': 28888.0, 'new_value': 28996.0}, {'field': 'count', 'old_value': 102, 'new_value': 103}, {'field': 'instoreAmount', 'old_value': 28888.0, 'new_value': 28996.0}, {'field': 'instoreCount', 'old_value': 102, 'new_value': 103}]
2025-05-24 08:06:29,591 - INFO - 更新表单数据成功: FINST-NS866I913EKVN0TFBYL9WAYSIM8N3QXWN6XAMY7
2025-05-24 08:06:29,591 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4371.91, 'new_value': 4365.88}, {'field': 'amount', 'old_value': 4371.91, 'new_value': 4365.88}]
2025-05-24 08:06:30,060 - INFO - 更新表单数据成功: FINST-NS866I913EKVN0TFBYL9WAYSIM8N3QXWN6XAMG8
2025-05-24 08:06:30,060 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 20956.0, 'new_value': 21304.08}, {'field': 'dailyBillAmount', 'old_value': 20956.0, 'new_value': 21304.08}]
2025-05-24 08:06:30,466 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP35ASAWSAML3
2025-05-24 08:06:30,466 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 15257.9, 'new_value': 15335.9}, {'field': 'dailyBillAmount', 'old_value': 15257.9, 'new_value': 15335.9}]
2025-05-24 08:06:30,951 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733UJAUGRAMYH
2025-05-24 08:06:30,951 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 16901.8, 'new_value': 17013.66}, {'field': 'dailyBillAmount', 'old_value': 16901.8, 'new_value': 17013.66}]
2025-05-24 08:06:31,404 - INFO - 更新表单数据成功: FINST-NS866I913EKVN0TFBYL9WAYSIM8N3QXWN6XAM89
2025-05-24 08:06:31,404 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 35906.27, 'new_value': 28314.08}, {'field': 'dailyBillAmount', 'old_value': 35906.27, 'new_value': 28314.08}]
2025-05-24 08:06:31,904 - INFO - 更新表单数据成功: FINST-RNA66D71GYHVG1PY7014I7W5X12W3ZWUAWSAMX9
2025-05-24 08:06:31,904 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 40861.75, 'new_value': 35906.27}, {'field': 'dailyBillAmount', 'old_value': 40861.75, 'new_value': 35906.27}]
2025-05-24 08:06:32,341 - INFO - 更新表单数据成功: FINST-VFF66XA1J7JVUBIND0A48ARTGTEK2U95RBUAMAD
2025-05-24 08:06:32,341 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 27042.34, 'new_value': 26672.34}, {'field': 'amount', 'old_value': 27042.34, 'new_value': 26672.34}, {'field': 'count', 'old_value': 74, 'new_value': 73}, {'field': 'instoreAmount', 'old_value': 24790.6, 'new_value': 24420.6}, {'field': 'instoreCount', 'old_value': 54, 'new_value': 53}]
2025-05-24 08:06:32,747 - INFO - 更新表单数据成功: FINST-NS866I913EKVN0TFBYL9WAYSIM8N3QXWN6XAMD9
2025-05-24 08:06:32,747 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 16559.83, 'new_value': 16061.83}, {'field': 'amount', 'old_value': 16559.83, 'new_value': 16061.83}, {'field': 'count', 'old_value': 56, 'new_value': 55}, {'field': 'instoreAmount', 'old_value': 14448.0, 'new_value': 13950.0}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 40}]
2025-05-24 08:06:33,185 - INFO - 更新表单数据成功: FINST-V7966QC1CCLVBMVHB6JLKDAS4E863OICK10BMTX
2025-05-24 08:06:33,185 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5461.6}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5461.6}]
2025-05-24 08:06:33,732 - INFO - 更新表单数据成功: FINST-V7966QC1CCLVBMVHB6JLKDAS4E863OICK10BMRY
2025-05-24 08:06:33,732 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 2944.37, 'new_value': 2896.17}, {'field': 'amount', 'old_value': 2944.37, 'new_value': 2896.17}]
2025-05-24 08:06:34,201 - INFO - 更新表单数据成功: FINST-COC668A1OALVH3UC900GTAGU68TS3B5FK10BMMI
2025-05-24 08:06:34,201 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 3536.0, 'new_value': 3515.0}, {'field': 'dailyBillAmount', 'old_value': 3536.0, 'new_value': 3515.0}]
2025-05-24 08:06:34,654 - INFO - 更新表单数据成功: FINST-COC668A1OALVH3UC900GTAGU68TS3B5FK10BMYI
2025-05-24 08:06:34,654 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_20250522, 变更字段: [{'field': 'amount', 'old_value': 3105.61, 'new_value': 3158.11}, {'field': 'count', 'old_value': 140, 'new_value': 142}, {'field': 'instoreAmount', 'old_value': 3198.71, 'new_value': 3251.21}, {'field': 'instoreCount', 'old_value': 140, 'new_value': 142}]
2025-05-24 08:06:35,107 - INFO - 更新表单数据成功: FINST-COC668A1OALVH3UC900GTAGU68TS3C5FK10BMAJ
2025-05-24 08:06:35,107 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_20250522, 变更字段: [{'field': 'amount', 'old_value': 1409.77, 'new_value': 1430.07}, {'field': 'count', 'old_value': 47, 'new_value': 48}, {'field': 'onlineAmount', 'old_value': 1046.07, 'new_value': 1066.37}, {'field': 'onlineCount', 'old_value': 35, 'new_value': 36}]
2025-05-24 08:06:35,513 - INFO - 更新表单数据成功: FINST-COC668A1OALVH3UC900GTAGU68TS3C5FK10BM2K
2025-05-24 08:06:35,513 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9202.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9202.7}, {'field': 'count', 'old_value': 5, 'new_value': 6}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 6}]
2025-05-24 08:06:35,935 - INFO - 更新表单数据成功: FINST-COC668A1OALVH3UC900GTAGU68TS3C5FK10BM7K
2025-05-24 08:06:35,935 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 13070.0, 'new_value': 13356.0}, {'field': 'amount', 'old_value': 13070.0, 'new_value': 13356.0}, {'field': 'instoreAmount', 'old_value': 13070.0, 'new_value': 13356.0}]
2025-05-24 08:06:36,388 - INFO - 更新表单数据成功: FINST-COC668A1OALVH3UC900GTAGU68TS3C5FK10BMRK
2025-05-24 08:06:36,404 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_20250522, 变更字段: [{'field': 'amount', 'old_value': 50.3, 'new_value': 100.85000000000001}, {'field': 'count', 'old_value': 4, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 63.1, 'new_value': 113.65}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 5}]
2025-05-24 08:06:36,841 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62RRHK10BM0
2025-05-24 08:06:36,841 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_20250522, 变更字段: [{'field': 'count', 'old_value': 99, 'new_value': 100}, {'field': 'onlineCount', 'old_value': 83, 'new_value': 84}]
2025-05-24 08:06:37,310 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62RRHK10BMN
2025-05-24 08:06:37,310 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 9565.34, 'new_value': 9636.74}, {'field': 'amount', 'old_value': 9565.34, 'new_value': 9636.74}, {'field': 'count', 'old_value': 154, 'new_value': 156}, {'field': 'instoreAmount', 'old_value': 8443.15, 'new_value': 8514.55}, {'field': 'instoreCount', 'old_value': 130, 'new_value': 132}]
2025-05-24 08:06:37,857 - INFO - 更新表单数据成功: FINST-X0G66U81YEHVDNKYEIQ55DT3P0S92ORZ3MYAM1S
2025-05-24 08:06:37,857 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250521, 变更字段: [{'field': 'recommendAmount', 'old_value': 8173.03, 'new_value': 8220.63}, {'field': 'amount', 'old_value': 8173.03, 'new_value': 8220.63}, {'field': 'count', 'old_value': 167, 'new_value': 168}, {'field': 'instoreAmount', 'old_value': 7158.3, 'new_value': 7205.9}, {'field': 'instoreCount', 'old_value': 141, 'new_value': 142}]
2025-05-24 08:06:38,326 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62SRHK10BMY
2025-05-24 08:06:38,326 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 1100.17, 'new_value': 1103.87}, {'field': 'amount', 'old_value': 1100.17, 'new_value': 1103.87}, {'field': 'count', 'old_value': 70, 'new_value': 71}, {'field': 'onlineAmount', 'old_value': 791.67, 'new_value': 795.37}, {'field': 'onlineCount', 'old_value': 53, 'new_value': 54}]
2025-05-24 08:06:38,779 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62SRHK10BM81
2025-05-24 08:06:38,779 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 5040.61, 'new_value': 5062.41}, {'field': 'amount', 'old_value': 5040.610000000001, 'new_value': 5062.41}, {'field': 'count', 'old_value': 244, 'new_value': 247}, {'field': 'onlineAmount', 'old_value': 3784.12, 'new_value': 3805.92}, {'field': 'onlineCount', 'old_value': 187, 'new_value': 190}]
2025-05-24 08:06:39,169 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62SRHK10BMB1
2025-05-24 08:06:39,169 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4774.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4774.0}]
2025-05-24 08:06:39,638 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62SRHK10BMK1
2025-05-24 08:06:39,638 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 17306.93}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 17306.93}]
2025-05-24 08:06:40,091 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62SRHK10BMM1
2025-05-24 08:06:40,091 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_20250522, 变更字段: [{'field': 'amount', 'old_value': -6840.55, 'new_value': -6842.75}, {'field': 'onlineAmount', 'old_value': 565.65, 'new_value': 563.45}]
2025-05-24 08:06:40,544 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62SRHK10BMV1
2025-05-24 08:06:40,544 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_20250522, 变更字段: [{'field': 'amount', 'old_value': 20322.600000000002, 'new_value': 20527.600000000002}, {'field': 'count', 'old_value': 155, 'new_value': 156}, {'field': 'instoreAmount', 'old_value': 8965.1, 'new_value': 9170.1}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 54}]
2025-05-24 08:06:40,951 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62SRHK10BM42
2025-05-24 08:06:40,951 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_20250522, 变更字段: [{'field': 'amount', 'old_value': 28086.02, 'new_value': 28367.02}, {'field': 'count', 'old_value': 208, 'new_value': 209}, {'field': 'instoreAmount', 'old_value': 20131.43, 'new_value': 20412.43}, {'field': 'instoreCount', 'old_value': 92, 'new_value': 93}]
2025-05-24 08:06:41,451 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62SRHK10BM72
2025-05-24 08:06:41,451 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_20250522, 变更字段: [{'field': 'amount', 'old_value': 7302.4, 'new_value': 9832.5}, {'field': 'count', 'old_value': 43, 'new_value': 67}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 2530.1}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 24}]
2025-05-24 08:06:41,982 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62SRHK10BMG2
2025-05-24 08:06:41,982 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_20250522, 变更字段: [{'field': 'amount', 'old_value': 4822.110000000001, 'new_value': 4814.41}]
2025-05-24 08:06:42,466 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62SRHK10BMK2
2025-05-24 08:06:42,466 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_20250522, 变更字段: [{'field': 'amount', 'old_value': 11835.91, 'new_value': 11825.09}]
2025-05-24 08:06:42,888 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62SRHK10BMM2
2025-05-24 08:06:42,888 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2055.72}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2055.72}, {'field': 'amount', 'old_value': 60.4, 'new_value': 684.3000000000001}, {'field': 'count', 'old_value': 4, 'new_value': 73}, {'field': 'instoreAmount', 'old_value': 60.4, 'new_value': 713.2}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 73}]
2025-05-24 08:06:43,294 - INFO - 更新表单数据成功: FINST-W3B66L71F5MVC2899KR2I6CRI6863AGKK10BMIJ
2025-05-24 08:06:43,294 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_20250522, 变更字段: [{'field': 'amount', 'old_value': 2931.2, 'new_value': 3093.8}, {'field': 'count', 'old_value': 28, 'new_value': 29}, {'field': 'onlineAmount', 'old_value': 49.4, 'new_value': 212.0}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 2}]
2025-05-24 08:06:43,732 - INFO - 更新表单数据成功: FINST-W3B66L71F5MVC2899KR2I6CRI6863AGKK10BMKJ
2025-05-24 08:06:43,732 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_20250522, 变更字段: [{'field': 'amount', 'old_value': 10967.5, 'new_value': 11573.7}, {'field': 'count', 'old_value': 11, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 10967.5, 'new_value': 11573.7}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 12}]
2025-05-24 08:06:44,201 - INFO - 更新表单数据成功: FINST-W3B66L71F5MVC2899KR2I6CRI6863BGKK10BMQJ
2025-05-24 08:06:44,201 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_20250522, 变更字段: [{'field': 'amount', 'old_value': 20597.0, 'new_value': 23996.0}, {'field': 'count', 'old_value': 3, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 20597.0, 'new_value': 23996.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 4}]
2025-05-24 08:06:44,701 - INFO - 更新表单数据成功: FINST-W3B66L71F5MVC2899KR2I6CRI6863BGKK10BM0K
2025-05-24 08:06:44,701 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6697.92}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6697.92}]
2025-05-24 08:06:45,169 - INFO - 更新表单数据成功: FINST-W3B66L71F5MVC2899KR2I6CRI6863BGKK10BMEK
2025-05-24 08:06:45,169 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_20250522, 变更字段: [{'field': 'amount', 'old_value': 3718.99, 'new_value': 3792.99}, {'field': 'count', 'old_value': 212, 'new_value': 219}, {'field': 'onlineAmount', 'old_value': 3595.29, 'new_value': 3669.29}, {'field': 'onlineCount', 'old_value': 201, 'new_value': 208}]
2025-05-24 08:06:45,622 - INFO - 更新表单数据成功: FINST-W3B66L71F5MVC2899KR2I6CRI6863BGKK10BMHK
2025-05-24 08:06:45,622 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_20250522, 变更字段: [{'field': 'amount', 'old_value': 6638.79, 'new_value': 6607.29}]
2025-05-24 08:06:46,154 - INFO - 更新表单数据成功: FINST-W3B66L71F5MVC2899KR2I6CRI6863BGKK10BMVK
2025-05-24 08:06:46,154 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_20250522, 变更字段: [{'field': 'amount', 'old_value': 5450.09, 'new_value': 5470.740000000001}, {'field': 'count', 'old_value': 344, 'new_value': 346}, {'field': 'instoreAmount', 'old_value': 3925.78, 'new_value': 3934.43}, {'field': 'instoreCount', 'old_value': 246, 'new_value': 247}, {'field': 'onlineAmount', 'old_value': 1529.29, 'new_value': 1541.29}, {'field': 'onlineCount', 'old_value': 98, 'new_value': 99}]
2025-05-24 08:06:46,576 - INFO - 更新表单数据成功: FINST-W3B66L71F5MVC2899KR2I6CRI6863BGKK10BMWK
2025-05-24 08:06:46,576 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_20250521, 变更字段: [{'field': 'instoreAmount', 'old_value': 5227.92, 'new_value': 5250.92}, {'field': 'instoreCount', 'old_value': 332, 'new_value': 333}, {'field': 'onlineAmount', 'old_value': 1406.7, 'new_value': 1383.7}, {'field': 'onlineCount', 'old_value': 108, 'new_value': 107}]
2025-05-24 08:06:46,951 - INFO - 更新表单数据成功: FINST-W3B66L71F5MVC2899KR2I6CRI6863BGKK10BM1L
2025-05-24 08:06:46,951 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_20250522, 变更字段: [{'field': 'amount', 'old_value': 2774.5099999999998, 'new_value': 2820.79}, {'field': 'count', 'old_value': 43, 'new_value': 44}, {'field': 'onlineAmount', 'old_value': 938.37, 'new_value': 984.65}, {'field': 'onlineCount', 'old_value': 17, 'new_value': 18}]
2025-05-24 08:06:47,388 - INFO - 更新表单数据成功: FINST-W3B66L71F5MVC2899KR2I6CRI6863BGKK10BM6L
2025-05-24 08:06:47,388 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_20250522, 变更字段: [{'field': 'amount', 'old_value': 3013.59, 'new_value': 3056.92}, {'field': 'count', 'old_value': 74, 'new_value': 75}, {'field': 'instoreAmount', 'old_value': 2240.46, 'new_value': 2283.79}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 54}]
2025-05-24 08:06:47,872 - INFO - 更新表单数据成功: FINST-W3B66L71F5MVC2899KR2I6CRI6863BGKK10BMGL
2025-05-24 08:06:47,872 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 8232.43, 'new_value': 8272.44}, {'field': 'amount', 'old_value': 8232.43, 'new_value': 8272.44}, {'field': 'count', 'old_value': 324, 'new_value': 328}, {'field': 'onlineAmount', 'old_value': 8862.53, 'new_value': 8921.24}, {'field': 'onlineCount', 'old_value': 324, 'new_value': 328}]
2025-05-24 08:06:48,372 - INFO - 更新表单数据成功: FINST-W3B66L71F5MVC2899KR2I6CRI6863BGKK10BMHL
2025-05-24 08:06:48,372 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_20250521, 变更字段: [{'field': 'recommendAmount', 'old_value': 13895.53, 'new_value': 14615.53}, {'field': 'amount', 'old_value': 13895.53, 'new_value': 14615.53}, {'field': 'count', 'old_value': 462, 'new_value': 463}, {'field': 'onlineAmount', 'old_value': 14126.79, 'new_value': 14846.79}, {'field': 'onlineCount', 'old_value': 462, 'new_value': 463}]
2025-05-24 08:06:48,732 - INFO - 更新表单数据成功: FINST-IQE66ZC1FKNVW4QR8MX848SEDAWK384NK10BM0A
2025-05-24 08:06:48,732 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_20250522, 变更字段: [{'field': 'amount', 'old_value': 1130.38, 'new_value': 1172.08}, {'field': 'count', 'old_value': 17, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 933.37, 'new_value': 975.07}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 11}]
2025-05-24 08:06:49,138 - INFO - 更新表单数据成功: FINST-IQE66ZC1FKNVW4QR8MX848SEDAWK384NK10BM2A
2025-05-24 08:06:49,138 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_20250522, 变更字段: [{'field': 'amount', 'old_value': 13850.01, 'new_value': 14457.51}, {'field': 'count', 'old_value': 200, 'new_value': 202}, {'field': 'instoreAmount', 'old_value': 12025.68, 'new_value': 12633.18}, {'field': 'instoreCount', 'old_value': 121, 'new_value': 123}]
2025-05-24 08:06:49,654 - INFO - 更新表单数据成功: FINST-IQE66ZC1FKNVW4QR8MX848SEDAWK384NK10BMCA
2025-05-24 08:06:49,654 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 14039.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 14039.7}]
2025-05-24 08:06:50,107 - INFO - 更新表单数据成功: FINST-IQE66ZC1FKNVW4QR8MX848SEDAWK394NK10BM2B
2025-05-24 08:06:50,107 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_20250522, 变更字段: [{'field': 'amount', 'old_value': 32404.54, 'new_value': 37599.14}, {'field': 'count', 'old_value': 169, 'new_value': 174}, {'field': 'instoreAmount', 'old_value': 30104.6, 'new_value': 35299.2}, {'field': 'instoreCount', 'old_value': 146, 'new_value': 151}]
2025-05-24 08:06:50,529 - INFO - 更新表单数据成功: FINST-IQE66ZC1FKNVW4QR8MX848SEDAWK394NK10BMVB
2025-05-24 08:06:50,529 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_20250522, 变更字段: [{'field': 'amount', 'old_value': 5440.65, 'new_value': 5450.7}, {'field': 'count', 'old_value': 190, 'new_value': 191}, {'field': 'instoreAmount', 'old_value': 1826.68, 'new_value': 1836.73}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 58}]
2025-05-24 08:06:50,607 - INFO - 正在批量插入每日数据，批次 1/22，共 100 条记录
2025-05-24 08:06:51,169 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-05-24 08:06:54,185 - INFO - 正在批量插入每日数据，批次 2/22，共 100 条记录
2025-05-24 08:06:54,700 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-05-24 08:06:57,716 - INFO - 正在批量插入每日数据，批次 3/22，共 100 条记录
2025-05-24 08:06:58,138 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-05-24 08:07:01,154 - INFO - 正在批量插入每日数据，批次 4/22，共 100 条记录
2025-05-24 08:07:01,669 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-05-24 08:07:04,685 - INFO - 正在批量插入每日数据，批次 5/22，共 100 条记录
2025-05-24 08:07:05,075 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-05-24 08:07:08,091 - INFO - 正在批量插入每日数据，批次 6/22，共 100 条记录
2025-05-24 08:07:08,669 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-05-24 08:07:11,685 - INFO - 正在批量插入每日数据，批次 7/22，共 100 条记录
2025-05-24 08:07:12,075 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-05-24 08:07:15,091 - INFO - 正在批量插入每日数据，批次 8/22，共 100 条记录
2025-05-24 08:07:15,513 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-05-24 08:07:18,528 - INFO - 正在批量插入每日数据，批次 9/22，共 100 条记录
2025-05-24 08:07:18,950 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-05-24 08:07:21,966 - INFO - 正在批量插入每日数据，批次 10/22，共 100 条记录
2025-05-24 08:07:22,403 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-05-24 08:07:25,419 - INFO - 正在批量插入每日数据，批次 11/22，共 100 条记录
2025-05-24 08:07:25,794 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-05-24 08:07:28,794 - INFO - 正在批量插入每日数据，批次 12/22，共 100 条记录
2025-05-24 08:07:29,200 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-05-24 08:07:32,216 - INFO - 正在批量插入每日数据，批次 13/22，共 100 条记录
2025-05-24 08:07:32,685 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-05-24 08:07:35,700 - INFO - 正在批量插入每日数据，批次 14/22，共 100 条记录
2025-05-24 08:07:36,138 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-05-24 08:07:39,169 - INFO - 正在批量插入每日数据，批次 15/22，共 100 条记录
2025-05-24 08:07:39,544 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-05-24 08:07:42,560 - INFO - 正在批量插入每日数据，批次 16/22，共 100 条记录
2025-05-24 08:07:43,013 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-05-24 08:07:46,013 - INFO - 正在批量插入每日数据，批次 17/22，共 100 条记录
2025-05-24 08:07:46,450 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-05-24 08:07:49,466 - INFO - 正在批量插入每日数据，批次 18/22，共 100 条记录
2025-05-24 08:07:49,841 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-05-24 08:07:52,856 - INFO - 正在批量插入每日数据，批次 19/22，共 100 条记录
2025-05-24 08:07:53,247 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-05-24 08:07:56,263 - INFO - 正在批量插入每日数据，批次 20/22，共 100 条记录
2025-05-24 08:07:56,731 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-05-24 08:07:59,747 - INFO - 正在批量插入每日数据，批次 21/22，共 100 条记录
2025-05-24 08:08:00,184 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-05-24 08:08:03,200 - INFO - 正在批量插入每日数据，批次 22/22，共 42 条记录
2025-05-24 08:08:03,450 - INFO - 批量插入每日数据成功，批次 22，42 条记录
2025-05-24 08:08:06,466 - INFO - 批量插入每日数据完成: 总计 2142 条，成功 2142 条，失败 0 条
2025-05-24 08:08:06,466 - INFO - 批量插入日销售数据完成，共 2142 条记录
2025-05-24 08:08:06,466 - INFO - 日销售数据同步完成！更新: 80 条，插入: 2142 条，错误: 0 条，跳过: 10816 条
2025-05-24 08:08:06,466 - INFO - 正在获取宜搭月销售表单数据...
2025-05-24 08:08:06,466 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-05-24 08:08:06,466 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-05-24 08:08:06,466 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-24 08:08:06,466 - INFO - Request Parameters - Page 1:
2025-05-24 08:08:06,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:06,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:06,934 - INFO - API请求耗时: 469ms
2025-05-24 08:08:06,934 - INFO - Response - Page 1
2025-05-24 08:08:06,934 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-24 08:08:06,934 - INFO - 查询完成，共获取到 0 条记录
2025-05-24 08:08:06,934 - WARNING - 月度分段 1 查询返回空数据
2025-05-24 08:08:06,934 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-05-24 08:08:06,934 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-05-24 08:08:06,934 - INFO - Request Parameters - Page 1:
2025-05-24 08:08:06,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:06,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:07,450 - INFO - API请求耗时: 516ms
2025-05-24 08:08:07,450 - INFO - Response - Page 1
2025-05-24 08:08:07,450 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-24 08:08:07,450 - INFO - 查询完成，共获取到 0 条记录
2025-05-24 08:08:07,450 - WARNING - 单月查询返回空数据: 2024-05
2025-05-24 08:08:07,950 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-05-24 08:08:07,950 - INFO - Request Parameters - Page 1:
2025-05-24 08:08:07,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:07,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:08,153 - INFO - API请求耗时: 203ms
2025-05-24 08:08:08,153 - INFO - Response - Page 1
2025-05-24 08:08:08,153 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-24 08:08:08,153 - INFO - 查询完成，共获取到 0 条记录
2025-05-24 08:08:08,153 - WARNING - 单月查询返回空数据: 2024-06
2025-05-24 08:08:08,669 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-24 08:08:08,669 - INFO - Request Parameters - Page 1:
2025-05-24 08:08:08,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:08,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:08,872 - INFO - API请求耗时: 203ms
2025-05-24 08:08:08,872 - INFO - Response - Page 1
2025-05-24 08:08:08,872 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-24 08:08:08,872 - INFO - 查询完成，共获取到 0 条记录
2025-05-24 08:08:08,872 - WARNING - 单月查询返回空数据: 2024-07
2025-05-24 08:08:10,403 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-05-24 08:08:10,403 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-24 08:08:10,403 - INFO - Request Parameters - Page 1:
2025-05-24 08:08:10,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:10,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:10,622 - INFO - API请求耗时: 219ms
2025-05-24 08:08:10,622 - INFO - Response - Page 1
2025-05-24 08:08:10,622 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-24 08:08:10,622 - INFO - 查询完成，共获取到 0 条记录
2025-05-24 08:08:10,622 - WARNING - 月度分段 2 查询返回空数据
2025-05-24 08:08:10,622 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-05-24 08:08:10,622 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-05-24 08:08:10,622 - INFO - Request Parameters - Page 1:
2025-05-24 08:08:10,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:10,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:10,856 - INFO - API请求耗时: 234ms
2025-05-24 08:08:10,856 - INFO - Response - Page 1
2025-05-24 08:08:10,872 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-24 08:08:10,872 - INFO - 查询完成，共获取到 0 条记录
2025-05-24 08:08:10,872 - WARNING - 单月查询返回空数据: 2024-08
2025-05-24 08:08:11,372 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-05-24 08:08:11,372 - INFO - Request Parameters - Page 1:
2025-05-24 08:08:11,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:11,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:11,559 - INFO - API请求耗时: 188ms
2025-05-24 08:08:11,559 - INFO - Response - Page 1
2025-05-24 08:08:11,575 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-24 08:08:11,575 - INFO - 查询完成，共获取到 0 条记录
2025-05-24 08:08:11,575 - WARNING - 单月查询返回空数据: 2024-09
2025-05-24 08:08:12,091 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-24 08:08:12,091 - INFO - Request Parameters - Page 1:
2025-05-24 08:08:12,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:12,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:12,309 - INFO - API请求耗时: 219ms
2025-05-24 08:08:12,309 - INFO - Response - Page 1
2025-05-24 08:08:12,309 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-24 08:08:12,309 - INFO - 查询完成，共获取到 0 条记录
2025-05-24 08:08:12,309 - WARNING - 单月查询返回空数据: 2024-10
2025-05-24 08:08:13,825 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-05-24 08:08:13,825 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-05-24 08:08:13,825 - INFO - Request Parameters - Page 1:
2025-05-24 08:08:13,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:13,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:14,466 - INFO - API请求耗时: 641ms
2025-05-24 08:08:14,466 - INFO - Response - Page 1
2025-05-24 08:08:14,466 - INFO - 第 1 页获取到 100 条记录
2025-05-24 08:08:14,981 - INFO - Request Parameters - Page 2:
2025-05-24 08:08:14,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:14,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:15,591 - INFO - API请求耗时: 609ms
2025-05-24 08:08:15,606 - INFO - Response - Page 2
2025-05-24 08:08:15,606 - INFO - 第 2 页获取到 100 条记录
2025-05-24 08:08:16,106 - INFO - Request Parameters - Page 3:
2025-05-24 08:08:16,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:16,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:16,591 - INFO - API请求耗时: 484ms
2025-05-24 08:08:16,591 - INFO - Response - Page 3
2025-05-24 08:08:16,591 - INFO - 第 3 页获取到 48 条记录
2025-05-24 08:08:16,591 - INFO - 查询完成，共获取到 248 条记录
2025-05-24 08:08:16,591 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-05-24 08:08:17,606 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-05-24 08:08:17,606 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-24 08:08:17,606 - INFO - Request Parameters - Page 1:
2025-05-24 08:08:17,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:17,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:18,091 - INFO - API请求耗时: 484ms
2025-05-24 08:08:18,091 - INFO - Response - Page 1
2025-05-24 08:08:18,091 - INFO - 第 1 页获取到 100 条记录
2025-05-24 08:08:18,606 - INFO - Request Parameters - Page 2:
2025-05-24 08:08:18,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:18,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:19,200 - INFO - API请求耗时: 594ms
2025-05-24 08:08:19,200 - INFO - Response - Page 2
2025-05-24 08:08:19,200 - INFO - 第 2 页获取到 100 条记录
2025-05-24 08:08:19,716 - INFO - Request Parameters - Page 3:
2025-05-24 08:08:19,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:19,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:20,294 - INFO - API请求耗时: 578ms
2025-05-24 08:08:20,294 - INFO - Response - Page 3
2025-05-24 08:08:20,294 - INFO - 第 3 页获取到 100 条记录
2025-05-24 08:08:20,794 - INFO - Request Parameters - Page 4:
2025-05-24 08:08:20,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:20,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:21,434 - INFO - API请求耗时: 641ms
2025-05-24 08:08:21,434 - INFO - Response - Page 4
2025-05-24 08:08:21,434 - INFO - 第 4 页获取到 100 条记录
2025-05-24 08:08:21,934 - INFO - Request Parameters - Page 5:
2025-05-24 08:08:21,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:21,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:22,434 - INFO - API请求耗时: 500ms
2025-05-24 08:08:22,434 - INFO - Response - Page 5
2025-05-24 08:08:22,434 - INFO - 第 5 页获取到 100 条记录
2025-05-24 08:08:22,934 - INFO - Request Parameters - Page 6:
2025-05-24 08:08:22,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:22,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:23,528 - INFO - API请求耗时: 594ms
2025-05-24 08:08:23,528 - INFO - Response - Page 6
2025-05-24 08:08:23,528 - INFO - 第 6 页获取到 100 条记录
2025-05-24 08:08:24,028 - INFO - Request Parameters - Page 7:
2025-05-24 08:08:24,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:24,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:24,606 - INFO - API请求耗时: 578ms
2025-05-24 08:08:24,606 - INFO - Response - Page 7
2025-05-24 08:08:24,606 - INFO - 第 7 页获取到 100 条记录
2025-05-24 08:08:25,122 - INFO - Request Parameters - Page 8:
2025-05-24 08:08:25,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:25,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:25,434 - INFO - API请求耗时: 312ms
2025-05-24 08:08:25,434 - INFO - Response - Page 8
2025-05-24 08:08:25,434 - INFO - 第 8 页获取到 16 条记录
2025-05-24 08:08:25,434 - INFO - 查询完成，共获取到 716 条记录
2025-05-24 08:08:25,434 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-05-24 08:08:26,434 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-05-24 08:08:26,434 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-05-24 08:08:26,434 - INFO - Request Parameters - Page 1:
2025-05-24 08:08:26,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:26,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:27,012 - INFO - API请求耗时: 578ms
2025-05-24 08:08:27,012 - INFO - Response - Page 1
2025-05-24 08:08:27,012 - INFO - 第 1 页获取到 100 条记录
2025-05-24 08:08:27,528 - INFO - Request Parameters - Page 2:
2025-05-24 08:08:27,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:27,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:28,075 - INFO - API请求耗时: 547ms
2025-05-24 08:08:28,075 - INFO - Response - Page 2
2025-05-24 08:08:28,075 - INFO - 第 2 页获取到 100 条记录
2025-05-24 08:08:28,590 - INFO - Request Parameters - Page 3:
2025-05-24 08:08:28,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:08:28,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:08:28,950 - INFO - API请求耗时: 359ms
2025-05-24 08:08:28,950 - INFO - Response - Page 3
2025-05-24 08:08:28,950 - INFO - 第 3 页获取到 24 条记录
2025-05-24 08:08:28,950 - INFO - 查询完成，共获取到 224 条记录
2025-05-24 08:08:28,950 - INFO - 月度分段 5 查询成功，获取到 224 条记录
2025-05-24 08:08:29,965 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1188 条记录，失败 0 次
2025-05-24 08:08:29,965 - INFO - 成功获取宜搭月销售表单数据，共 1188 条记录
2025-05-24 08:08:29,965 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-24 08:08:29,965 - INFO - 正在从SQLite获取月度汇总数据...
2025-05-24 08:08:29,965 - INFO - 成功获取SQLite月度汇总数据，共 1192 条记录
2025-05-24 08:08:30,028 - INFO - 成功创建宜搭月销售数据索引，共 1188 条记录
2025-05-24 08:08:30,559 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-05-24 08:08:30,559 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 151357.69, 'new_value': 160088.44}, {'field': 'dailyBillAmount', 'old_value': 151357.69, 'new_value': 160088.44}, {'field': 'amount', 'old_value': 4548.3, 'new_value': 4641.1}, {'field': 'count', 'old_value': 64, 'new_value': 65}, {'field': 'onlineAmount', 'old_value': 4624.3, 'new_value': 4717.1}, {'field': 'onlineCount', 'old_value': 64, 'new_value': 65}]
2025-05-24 08:08:31,059 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-05-24 08:08:31,059 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 396995.25, 'new_value': 413110.95}, {'field': 'dailyBillAmount', 'old_value': 396995.25, 'new_value': 413110.95}, {'field': 'amount', 'old_value': 219034.6, 'new_value': 226730.1}, {'field': 'count', 'old_value': 2056, 'new_value': 2128}, {'field': 'instoreAmount', 'old_value': 89809.2, 'new_value': 93015.4}, {'field': 'instoreCount', 'old_value': 688, 'new_value': 714}, {'field': 'onlineAmount', 'old_value': 129577.8, 'new_value': 134067.1}, {'field': 'onlineCount', 'old_value': 1368, 'new_value': 1414}]
2025-05-24 08:08:31,481 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-05-24 08:08:31,481 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 592810.84, 'new_value': 618056.19}, {'field': 'dailyBillAmount', 'old_value': 592810.84, 'new_value': 618056.19}, {'field': 'amount', 'old_value': 430425.25, 'new_value': 446386.58}, {'field': 'count', 'old_value': 2067, 'new_value': 2146}, {'field': 'instoreAmount', 'old_value': 430425.25, 'new_value': 446386.58}, {'field': 'instoreCount', 'old_value': 2067, 'new_value': 2146}]
2025-05-24 08:08:31,887 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-05-24 08:08:31,887 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 463316.18, 'new_value': 478651.2}, {'field': 'dailyBillAmount', 'old_value': 463316.18, 'new_value': 478651.2}, {'field': 'amount', 'old_value': 768151.0, 'new_value': 799281.0}, {'field': 'count', 'old_value': 2632, 'new_value': 2750}, {'field': 'instoreAmount', 'old_value': 769401.0, 'new_value': 800531.0}, {'field': 'instoreCount', 'old_value': 2632, 'new_value': 2750}]
2025-05-24 08:08:32,294 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-05-24 08:08:32,309 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 57385.4, 'new_value': 59891.3}, {'field': 'dailyBillAmount', 'old_value': 57385.4, 'new_value': 59891.3}, {'field': 'amount', 'old_value': 75880.71, 'new_value': 79648.41}, {'field': 'count', 'old_value': 267, 'new_value': 287}, {'field': 'instoreAmount', 'old_value': 42254.9, 'new_value': 43412.9}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 42}, {'field': 'onlineAmount', 'old_value': 37722.72, 'new_value': 40332.42}, {'field': 'onlineCount', 'old_value': 227, 'new_value': 245}]
2025-05-24 08:08:32,840 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-05-24 08:08:32,840 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'count', 'old_value': 118, 'new_value': 120}, {'field': 'instoreCount', 'old_value': 118, 'new_value': 120}]
2025-05-24 08:08:33,325 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-05-24 08:08:33,325 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 640755.34, 'new_value': 672186.3}, {'field': 'dailyBillAmount', 'old_value': 640755.34, 'new_value': 672186.3}, {'field': 'amount', 'old_value': 584377.65, 'new_value': 610225.95}, {'field': 'count', 'old_value': 4204, 'new_value': 4361}, {'field': 'instoreAmount', 'old_value': 475374.41000000003, 'new_value': 498573.31}, {'field': 'instoreCount', 'old_value': 2057, 'new_value': 2144}, {'field': 'onlineAmount', 'old_value': 112757.17, 'new_value': 115577.57}, {'field': 'onlineCount', 'old_value': 2147, 'new_value': 2217}]
2025-05-24 08:08:33,762 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMTJ
2025-05-24 08:08:33,762 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 228325.2, 'new_value': 229085.21}, {'field': 'amount', 'old_value': 228316.03, 'new_value': 229075.6}, {'field': 'count', 'old_value': 9428, 'new_value': 9433}, {'field': 'onlineAmount', 'old_value': 233715.28, 'new_value': 234493.99}, {'field': 'onlineCount', 'old_value': 9428, 'new_value': 9433}]
2025-05-24 08:08:34,294 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-05-24 08:08:34,294 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 647689.51, 'new_value': 674095.94}, {'field': 'dailyBillAmount', 'old_value': 647689.51, 'new_value': 674095.94}, {'field': 'amount', 'old_value': 168948.43, 'new_value': 185194.21}, {'field': 'count', 'old_value': 966, 'new_value': 1057}, {'field': 'instoreAmount', 'old_value': 168948.43, 'new_value': 185194.21}, {'field': 'instoreCount', 'old_value': 966, 'new_value': 1057}]
2025-05-24 08:08:34,747 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMVJ
2025-05-24 08:08:34,747 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-05, 变更字段: [{'field': 'amount', 'old_value': 25614.0, 'new_value': 27726.0}, {'field': 'count', 'old_value': 34, 'new_value': 40}, {'field': 'instoreAmount', 'old_value': 25614.0, 'new_value': 27726.0}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 40}]
2025-05-24 08:08:35,215 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-05-24 08:08:35,215 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 93957.3, 'new_value': 97095.3}, {'field': 'count', 'old_value': 266, 'new_value': 273}, {'field': 'instoreAmount', 'old_value': 93959.0, 'new_value': 97097.0}, {'field': 'instoreCount', 'old_value': 266, 'new_value': 273}]
2025-05-24 08:08:35,653 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXJ
2025-05-24 08:08:35,653 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSCOR6MVCC7Q2OV392411100148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27997.9, 'new_value': 32478.9}, {'field': 'amount', 'old_value': 27997.9, 'new_value': 32478.9}, {'field': 'count', 'old_value': 24, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 29393.9, 'new_value': 33874.9}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 28}]
2025-05-24 08:08:36,106 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-05-24 08:08:36,106 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 878332.13, 'new_value': 916609.52}, {'field': 'dailyBillAmount', 'old_value': 878332.13, 'new_value': 916609.52}, {'field': 'amount', 'old_value': -336567.21, 'new_value': -360793.4}, {'field': 'count', 'old_value': 923, 'new_value': 947}, {'field': 'instoreAmount', 'old_value': 563563.91, 'new_value': 576025.5}, {'field': 'instoreCount', 'old_value': 923, 'new_value': 947}]
2025-05-24 08:08:36,590 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-05-24 08:08:36,590 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 345415.0, 'new_value': 368664.0}, {'field': 'amount', 'old_value': 345415.0, 'new_value': 368664.0}, {'field': 'count', 'old_value': 1201, 'new_value': 1253}, {'field': 'instoreAmount', 'old_value': 345415.0, 'new_value': 368664.0}, {'field': 'instoreCount', 'old_value': 1201, 'new_value': 1253}]
2025-05-24 08:08:37,028 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-05-24 08:08:37,028 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 353674.52, 'new_value': 367399.94}, {'field': 'dailyBillAmount', 'old_value': 264343.62, 'new_value': 277168.64}, {'field': 'amount', 'old_value': 353674.52, 'new_value': 367399.94}, {'field': 'count', 'old_value': 1222, 'new_value': 1259}, {'field': 'instoreAmount', 'old_value': 353674.52, 'new_value': 367399.94}, {'field': 'instoreCount', 'old_value': 1222, 'new_value': 1259}]
2025-05-24 08:08:37,559 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-05-24 08:08:37,559 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 170713.15, 'new_value': 179915.85}, {'field': 'dailyBillAmount', 'old_value': 170713.15, 'new_value': 179915.85}, {'field': 'amount', 'old_value': 11966.5, 'new_value': 12826.4}, {'field': 'count', 'old_value': 91, 'new_value': 96}, {'field': 'instoreAmount', 'old_value': 14221.5, 'new_value': 15239.9}, {'field': 'instoreCount', 'old_value': 91, 'new_value': 96}]
2025-05-24 08:08:38,044 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-05-24 08:08:38,044 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 89815.56, 'new_value': 93638.24}, {'field': 'dailyBillAmount', 'old_value': 89815.56, 'new_value': 93638.24}, {'field': 'amount', 'old_value': 55030.13, 'new_value': 56586.73}, {'field': 'count', 'old_value': 811, 'new_value': 833}, {'field': 'instoreAmount', 'old_value': 56981.03, 'new_value': 58537.63}, {'field': 'instoreCount', 'old_value': 811, 'new_value': 833}]
2025-05-24 08:08:38,481 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-05-24 08:08:38,481 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 131485.55, 'new_value': 137463.77}, {'field': 'dailyBillAmount', 'old_value': 76726.92, 'new_value': 82358.29000000001}, {'field': 'amount', 'old_value': 131484.69, 'new_value': 137462.91}, {'field': 'count', 'old_value': 4510, 'new_value': 4701}, {'field': 'instoreAmount', 'old_value': 114450.02, 'new_value': 119422.1}, {'field': 'instoreCount', 'old_value': 4087, 'new_value': 4251}, {'field': 'onlineAmount', 'old_value': 17035.53, 'new_value': 18041.67}, {'field': 'onlineCount', 'old_value': 423, 'new_value': 450}]
2025-05-24 08:08:38,965 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-05-24 08:08:38,965 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 291157.22, 'new_value': 292381.22}, {'field': 'dailyBillAmount', 'old_value': 285767.0, 'new_value': 286991.0}, {'field': 'amount', 'old_value': 241441.91, 'new_value': 242665.91}, {'field': 'count', 'old_value': 220, 'new_value': 223}, {'field': 'instoreAmount', 'old_value': 241271.0, 'new_value': 242495.0}, {'field': 'instoreCount', 'old_value': 218, 'new_value': 221}]
2025-05-24 08:08:39,356 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-05-24 08:08:39,356 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 498193.22, 'new_value': 524844.3}, {'field': 'dailyBillAmount', 'old_value': 497688.67, 'new_value': 524339.75}, {'field': 'amount', 'old_value': 498193.22, 'new_value': 524844.3}, {'field': 'count', 'old_value': 444, 'new_value': 469}, {'field': 'instoreAmount', 'old_value': 498194.22, 'new_value': 524845.3}, {'field': 'instoreCount', 'old_value': 444, 'new_value': 469}]
2025-05-24 08:08:39,825 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-05-24 08:08:39,825 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92675.6, 'new_value': 95938.4}, {'field': 'dailyBillAmount', 'old_value': 92675.6, 'new_value': 95938.4}, {'field': 'amount', 'old_value': 102753.3, 'new_value': 105950.6}, {'field': 'count', 'old_value': 273, 'new_value': 284}, {'field': 'instoreAmount', 'old_value': 102759.2, 'new_value': 105956.5}, {'field': 'instoreCount', 'old_value': 273, 'new_value': 284}]
2025-05-24 08:08:40,294 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-05-24 08:08:40,294 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 147096.7, 'new_value': 149814.7}, {'field': 'amount', 'old_value': 147096.7, 'new_value': 149814.7}, {'field': 'count', 'old_value': 175, 'new_value': 180}, {'field': 'instoreAmount', 'old_value': 147223.7, 'new_value': 149941.7}, {'field': 'instoreCount', 'old_value': 175, 'new_value': 180}]
2025-05-24 08:08:40,794 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-05-24 08:08:40,794 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 197698.72, 'new_value': 207695.26}, {'field': 'dailyBillAmount', 'old_value': 197698.72, 'new_value': 207695.26}, {'field': 'amount', 'old_value': 208042.85, 'new_value': 218697.65}, {'field': 'count', 'old_value': 1399, 'new_value': 1465}, {'field': 'instoreAmount', 'old_value': 209251.85, 'new_value': 219906.65}, {'field': 'instoreCount', 'old_value': 1399, 'new_value': 1465}]
2025-05-24 08:08:41,340 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-05-24 08:08:41,340 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 137966.18, 'new_value': 145383.2}, {'field': 'dailyBillAmount', 'old_value': 137966.18, 'new_value': 145383.2}, {'field': 'amount', 'old_value': 13696.75, 'new_value': 14547.24}, {'field': 'count', 'old_value': 1237, 'new_value': 1333}, {'field': 'instoreAmount', 'old_value': 18197.76, 'new_value': 19338.95}, {'field': 'instoreCount', 'old_value': 1237, 'new_value': 1333}]
2025-05-24 08:08:41,840 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-05-24 08:08:41,840 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 245568.51, 'new_value': 258564.01}, {'field': 'amount', 'old_value': 245564.36000000002, 'new_value': 258559.86000000002}, {'field': 'count', 'old_value': 5703, 'new_value': 6008}, {'field': 'instoreAmount', 'old_value': 239147.16, 'new_value': 251940.26}, {'field': 'instoreCount', 'old_value': 5492, 'new_value': 5789}, {'field': 'onlineAmount', 'old_value': 10068.93, 'new_value': 10464.53}, {'field': 'onlineCount', 'old_value': 211, 'new_value': 219}]
2025-05-24 08:08:42,278 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-05-24 08:08:42,278 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 213544.6, 'new_value': 219251.8}, {'field': 'dailyBillAmount', 'old_value': 213544.6, 'new_value': 219251.8}, {'field': 'amount', 'old_value': 213544.6, 'new_value': 219251.8}, {'field': 'count', 'old_value': 647, 'new_value': 662}, {'field': 'instoreAmount', 'old_value': 213544.6, 'new_value': 219251.8}, {'field': 'instoreCount', 'old_value': 647, 'new_value': 662}]
2025-05-24 08:08:42,762 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-05-24 08:08:42,762 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 180620.51, 'new_value': 190644.21}, {'field': 'dailyBillAmount', 'old_value': 180620.51, 'new_value': 190644.21}, {'field': 'amount', 'old_value': 62733.2, 'new_value': 65975.2}, {'field': 'count', 'old_value': 148, 'new_value': 156}, {'field': 'instoreAmount', 'old_value': 62733.2, 'new_value': 65975.2}, {'field': 'instoreCount', 'old_value': 148, 'new_value': 156}]
2025-05-24 08:08:43,137 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-05-24 08:08:43,137 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 371996.84, 'new_value': 385436.03}, {'field': 'dailyBillAmount', 'old_value': 371996.84, 'new_value': 385436.03}, {'field': 'amount', 'old_value': 153677.4, 'new_value': 159597.3}, {'field': 'count', 'old_value': 571, 'new_value': 590}, {'field': 'instoreAmount', 'old_value': 153677.66, 'new_value': 159597.56}, {'field': 'instoreCount', 'old_value': 571, 'new_value': 590}]
2025-05-24 08:08:43,637 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-05-24 08:08:43,637 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 80383.68, 'new_value': 84209.63}, {'field': 'dailyBillAmount', 'old_value': 80383.68, 'new_value': 84209.63}, {'field': 'amount', 'old_value': 24418.0, 'new_value': 25518.63}, {'field': 'count', 'old_value': 896, 'new_value': 937}, {'field': 'instoreAmount', 'old_value': 5801.53, 'new_value': 6051.83}, {'field': 'instoreCount', 'old_value': 152, 'new_value': 161}, {'field': 'onlineAmount', 'old_value': 18870.99, 'new_value': 19736.89}, {'field': 'onlineCount', 'old_value': 744, 'new_value': 776}]
2025-05-24 08:08:44,137 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-05-24 08:08:44,137 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 128109.88, 'new_value': 134425.71}, {'field': 'dailyBillAmount', 'old_value': 128109.88, 'new_value': 134425.71}, {'field': 'amount', 'old_value': 21515.11, 'new_value': 22376.05}, {'field': 'count', 'old_value': 518, 'new_value': 537}, {'field': 'instoreAmount', 'old_value': 18557.01, 'new_value': 19417.95}, {'field': 'instoreCount', 'old_value': 460, 'new_value': 479}]
2025-05-24 08:08:44,684 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-05-24 08:08:44,684 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 18559.03, 'new_value': 18828.43}, {'field': 'dailyBillAmount', 'old_value': 18559.03, 'new_value': 18828.43}, {'field': 'amount', 'old_value': 14893.18, 'new_value': 14962.58}, {'field': 'count', 'old_value': 519, 'new_value': 527}, {'field': 'instoreAmount', 'old_value': 15260.78, 'new_value': 15330.18}, {'field': 'instoreCount', 'old_value': 519, 'new_value': 527}]
2025-05-24 08:08:45,122 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-05-24 08:08:45,122 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41102.9, 'new_value': 42672.13}, {'field': 'dailyBillAmount', 'old_value': 41102.9, 'new_value': 42672.13}, {'field': 'amount', 'old_value': 26482.18, 'new_value': 27495.72}, {'field': 'count', 'old_value': 1410, 'new_value': 1463}, {'field': 'instoreAmount', 'old_value': 13493.13, 'new_value': 13853.73}, {'field': 'instoreCount', 'old_value': 570, 'new_value': 588}, {'field': 'onlineAmount', 'old_value': 13629.63, 'new_value': 14298.57}, {'field': 'onlineCount', 'old_value': 840, 'new_value': 875}]
2025-05-24 08:08:45,575 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-05-24 08:08:45,575 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 291114.02999999997, 'new_value': 302923.3}, {'field': 'dailyBillAmount', 'old_value': 291114.02999999997, 'new_value': 302923.3}, {'field': 'amount', 'old_value': 131316.06, 'new_value': 137398.26}, {'field': 'count', 'old_value': 548, 'new_value': 574}, {'field': 'instoreAmount', 'old_value': 135815.52, 'new_value': 141941.92}, {'field': 'instoreCount', 'old_value': 548, 'new_value': 574}]
2025-05-24 08:08:45,965 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-05-24 08:08:45,965 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 15458.84, 'new_value': 16142.74}, {'field': 'count', 'old_value': 137, 'new_value': 145}, {'field': 'instoreAmount', 'old_value': 15533.08, 'new_value': 16216.98}, {'field': 'instoreCount', 'old_value': 137, 'new_value': 145}]
2025-05-24 08:08:46,403 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-05-24 08:08:46,403 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 179537.85, 'new_value': 187241.26}, {'field': 'dailyBillAmount', 'old_value': 179537.85, 'new_value': 187241.26}, {'field': 'amount', 'old_value': 87000.53, 'new_value': 91357.5}, {'field': 'count', 'old_value': 3677, 'new_value': 3851}, {'field': 'instoreAmount', 'old_value': 88818.78, 'new_value': 93204.46}, {'field': 'instoreCount', 'old_value': 3677, 'new_value': 3851}]
2025-05-24 08:08:46,825 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-05-24 08:08:46,825 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 415384.1, 'new_value': 430121.4}, {'field': 'dailyBillAmount', 'old_value': 415384.1, 'new_value': 430121.4}, {'field': 'amount', 'old_value': 415384.1, 'new_value': 430121.4}, {'field': 'count', 'old_value': 522, 'new_value': 543}, {'field': 'instoreAmount', 'old_value': 415384.1, 'new_value': 430121.4}, {'field': 'instoreCount', 'old_value': 522, 'new_value': 543}]
2025-05-24 08:08:47,278 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-05-24 08:08:47,278 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 187626.49, 'new_value': 192319.89}, {'field': 'dailyBillAmount', 'old_value': 187626.49, 'new_value': 192319.89}, {'field': 'amount', 'old_value': 106994.95, 'new_value': 108074.65}, {'field': 'count', 'old_value': 279, 'new_value': 282}, {'field': 'instoreAmount', 'old_value': 108411.55, 'new_value': 109491.25}, {'field': 'instoreCount', 'old_value': 279, 'new_value': 282}]
2025-05-24 08:08:47,731 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-05-24 08:08:47,731 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45749.0, 'new_value': 48182.0}, {'field': 'dailyBillAmount', 'old_value': 45749.0, 'new_value': 48182.0}, {'field': 'amount', 'old_value': 45749.0, 'new_value': 48182.0}, {'field': 'count', 'old_value': 908, 'new_value': 956}, {'field': 'instoreAmount', 'old_value': 45788.0, 'new_value': 48221.0}, {'field': 'instoreCount', 'old_value': 908, 'new_value': 956}]
2025-05-24 08:08:48,200 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-05-24 08:08:48,200 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 80076.18, 'new_value': 83245.87}, {'field': 'dailyBillAmount', 'old_value': 80076.18, 'new_value': 83245.87}, {'field': 'amount', 'old_value': 82818.23, 'new_value': 86199.06}, {'field': 'count', 'old_value': 4365, 'new_value': 4554}, {'field': 'instoreAmount', 'old_value': 39601.03, 'new_value': 41395.24}, {'field': 'instoreCount', 'old_value': 1983, 'new_value': 2075}, {'field': 'onlineAmount', 'old_value': 44362.74, 'new_value': 45998.26}, {'field': 'onlineCount', 'old_value': 2382, 'new_value': 2479}]
2025-05-24 08:08:48,668 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-05-24 08:08:48,668 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28918.37, 'new_value': 30020.78}, {'field': 'dailyBillAmount', 'old_value': 28918.37, 'new_value': 30020.78}, {'field': 'amount', 'old_value': 39828.12, 'new_value': 41249.93}, {'field': 'count', 'old_value': 1167, 'new_value': 1211}, {'field': 'instoreAmount', 'old_value': 36068.47, 'new_value': 37490.28}, {'field': 'instoreCount', 'old_value': 1016, 'new_value': 1060}]
2025-05-24 08:08:49,106 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-05-24 08:08:49,106 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 58194.3, 'new_value': 60705.090000000004}, {'field': 'dailyBillAmount', 'old_value': 58194.3, 'new_value': 60705.090000000004}, {'field': 'amount', 'old_value': 58043.76, 'new_value': 60597.01}, {'field': 'count', 'old_value': 2268, 'new_value': 2377}, {'field': 'instoreAmount', 'old_value': 37827.26, 'new_value': 39449.16}, {'field': 'instoreCount', 'old_value': 1352, 'new_value': 1418}, {'field': 'onlineAmount', 'old_value': 20517.13, 'new_value': 21448.48}, {'field': 'onlineCount', 'old_value': 916, 'new_value': 959}]
2025-05-24 08:08:49,512 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-05-24 08:08:49,512 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 58591.37, 'new_value': 60493.77}, {'field': 'count', 'old_value': 715, 'new_value': 740}, {'field': 'instoreAmount', 'old_value': 59018.27, 'new_value': 60920.67}, {'field': 'instoreCount', 'old_value': 715, 'new_value': 740}]
2025-05-24 08:08:49,950 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-05-24 08:08:49,950 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 62532.0, 'new_value': 64935.8}, {'field': 'amount', 'old_value': 62531.5, 'new_value': 64935.3}, {'field': 'count', 'old_value': 1580, 'new_value': 1642}, {'field': 'instoreAmount', 'old_value': 63581.0, 'new_value': 65984.8}, {'field': 'instoreCount', 'old_value': 1580, 'new_value': 1642}]
2025-05-24 08:08:50,403 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-05-24 08:08:50,403 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 292232.42, 'new_value': 302245.42}, {'field': 'dailyBillAmount', 'old_value': 292232.42, 'new_value': 302245.42}, {'field': 'amount', 'old_value': 90204.82, 'new_value': 94804.82}, {'field': 'count', 'old_value': 323, 'new_value': 338}, {'field': 'instoreAmount', 'old_value': 90204.82, 'new_value': 94804.82}, {'field': 'instoreCount', 'old_value': 323, 'new_value': 338}]
2025-05-24 08:08:50,872 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-05-24 08:08:50,872 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81874.06, 'new_value': 81952.06}, {'field': 'dailyBillAmount', 'old_value': 81874.06, 'new_value': 81952.06}, {'field': 'amount', 'old_value': 79176.26, 'new_value': 83253.86}, {'field': 'count', 'old_value': 284, 'new_value': 302}, {'field': 'instoreAmount', 'old_value': 81310.89, 'new_value': 85388.49}, {'field': 'instoreCount', 'old_value': 284, 'new_value': 302}]
2025-05-24 08:08:51,340 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-05-24 08:08:51,340 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44841.0, 'new_value': 47504.0}, {'field': 'dailyBillAmount', 'old_value': 44841.0, 'new_value': 47504.0}, {'field': 'amount', 'old_value': 55988.0, 'new_value': 58651.0}, {'field': 'count', 'old_value': 106, 'new_value': 111}, {'field': 'instoreAmount', 'old_value': 60086.0, 'new_value': 62749.0}, {'field': 'instoreCount', 'old_value': 106, 'new_value': 111}]
2025-05-24 08:08:51,793 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-05-24 08:08:51,793 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 78067.45, 'new_value': 83353.75}, {'field': 'dailyBillAmount', 'old_value': 75574.25, 'new_value': 80860.55}, {'field': 'amount', 'old_value': 78065.15, 'new_value': 81641.45}, {'field': 'count', 'old_value': 236, 'new_value': 251}, {'field': 'instoreAmount', 'old_value': 86857.65, 'new_value': 92533.85}, {'field': 'instoreCount', 'old_value': 236, 'new_value': 251}]
2025-05-24 08:08:52,200 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-05-24 08:08:52,200 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 103755.74, 'new_value': 106914.16}, {'field': 'dailyBillAmount', 'old_value': 103755.74, 'new_value': 106914.16}, {'field': 'amount', 'old_value': 58126.33, 'new_value': 60177.3}, {'field': 'count', 'old_value': 1598, 'new_value': 1660}, {'field': 'instoreAmount', 'old_value': 50546.17, 'new_value': 52153.07}, {'field': 'instoreCount', 'old_value': 1349, 'new_value': 1396}, {'field': 'onlineAmount', 'old_value': 8618.94, 'new_value': 9102.91}, {'field': 'onlineCount', 'old_value': 249, 'new_value': 264}]
2025-05-24 08:08:52,637 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-05-24 08:08:52,637 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 153716.82, 'new_value': 158429.95}, {'field': 'dailyBillAmount', 'old_value': 148622.86, 'new_value': 153315.58}, {'field': 'amount', 'old_value': 153716.82, 'new_value': 158429.95}, {'field': 'count', 'old_value': 1897, 'new_value': 1955}, {'field': 'instoreAmount', 'old_value': 145758.25, 'new_value': 150390.85}, {'field': 'instoreCount', 'old_value': 1806, 'new_value': 1862}, {'field': 'onlineAmount', 'old_value': 8017.8099999999995, 'new_value': 8098.34}, {'field': 'onlineCount', 'old_value': 91, 'new_value': 93}]
2025-05-24 08:08:53,075 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-05-24 08:08:53,075 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 70478.26, 'new_value': 73600.02}, {'field': 'dailyBillAmount', 'old_value': 70478.26, 'new_value': 73600.02}, {'field': 'amount', 'old_value': 92865.8, 'new_value': 96101.56}, {'field': 'count', 'old_value': 399, 'new_value': 416}, {'field': 'instoreAmount', 'old_value': 89515.67, 'new_value': 92655.72}, {'field': 'instoreCount', 'old_value': 357, 'new_value': 372}, {'field': 'onlineAmount', 'old_value': 3350.13, 'new_value': 3445.84}, {'field': 'onlineCount', 'old_value': 42, 'new_value': 44}]
2025-05-24 08:08:53,450 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-05-24 08:08:53,450 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 181667.5, 'new_value': 185037.3}, {'field': 'dailyBillAmount', 'old_value': 181667.5, 'new_value': 185037.3}, {'field': 'amount', 'old_value': 183087.0, 'new_value': 186942.8}, {'field': 'count', 'old_value': 673, 'new_value': 688}, {'field': 'instoreAmount', 'old_value': 185971.9, 'new_value': 189827.7}, {'field': 'instoreCount', 'old_value': 673, 'new_value': 688}]
2025-05-24 08:08:53,887 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3L
2025-05-24 08:08:53,887 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41302.0, 'new_value': 43373.0}, {'field': 'dailyBillAmount', 'old_value': 41302.0, 'new_value': 43373.0}, {'field': 'amount', 'old_value': 38500.0, 'new_value': 40571.0}, {'field': 'count', 'old_value': 93, 'new_value': 99}, {'field': 'instoreAmount', 'old_value': 39093.0, 'new_value': 41164.0}, {'field': 'instoreCount', 'old_value': 93, 'new_value': 99}]
2025-05-24 08:08:54,309 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6L
2025-05-24 08:08:54,309 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HI85875BG16NA6U1G19QP117C001UB5_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21580.3, 'new_value': 23191.4}, {'field': 'dailyBillAmount', 'old_value': 21580.3, 'new_value': 23191.4}, {'field': 'amount', 'old_value': 16624.81, 'new_value': 17596.31}, {'field': 'count', 'old_value': 751, 'new_value': 790}, {'field': 'instoreAmount', 'old_value': 16817.86, 'new_value': 17789.36}, {'field': 'instoreCount', 'old_value': 751, 'new_value': 790}]
2025-05-24 08:08:54,715 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-05-24 08:08:54,715 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41466.909999999996, 'new_value': 44641.42}, {'field': 'amount', 'old_value': 41465.44, 'new_value': 44639.78}, {'field': 'count', 'old_value': 2114, 'new_value': 2217}, {'field': 'instoreAmount', 'old_value': 48010.95, 'new_value': 51457.53}, {'field': 'instoreCount', 'old_value': 2114, 'new_value': 2217}]
2025-05-24 08:08:55,403 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-05-24 08:08:55,403 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 114684.7, 'new_value': 118379.07}, {'field': 'dailyBillAmount', 'old_value': 114684.7, 'new_value': 118379.07}, {'field': 'amount', 'old_value': 92176.6, 'new_value': 95070.3}, {'field': 'count', 'old_value': 380, 'new_value': 390}, {'field': 'instoreAmount', 'old_value': 92176.6, 'new_value': 95070.3}, {'field': 'instoreCount', 'old_value': 379, 'new_value': 389}]
2025-05-24 08:08:55,840 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-05-24 08:08:55,840 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 336124.68, 'new_value': 348192.74}, {'field': 'dailyBillAmount', 'old_value': 336124.68, 'new_value': 348192.74}, {'field': 'amount', 'old_value': 196173.37, 'new_value': 203587.36}, {'field': 'count', 'old_value': 2280, 'new_value': 2353}, {'field': 'instoreAmount', 'old_value': 84081.86, 'new_value': 88910.42}, {'field': 'instoreCount', 'old_value': 974, 'new_value': 1013}, {'field': 'onlineAmount', 'old_value': 112092.42, 'new_value': 114678.7}, {'field': 'onlineCount', 'old_value': 1306, 'new_value': 1340}]
2025-05-24 08:08:56,262 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-05-24 08:08:56,262 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 188614.62, 'new_value': 197933.33}, {'field': 'dailyBillAmount', 'old_value': 188614.62, 'new_value': 197933.33}, {'field': 'amount', 'old_value': 200915.4, 'new_value': 210446.19999999998}, {'field': 'count', 'old_value': 1209, 'new_value': 1274}, {'field': 'instoreAmount', 'old_value': 201695.3, 'new_value': 211226.1}, {'field': 'instoreCount', 'old_value': 1209, 'new_value': 1274}]
2025-05-24 08:08:56,715 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBL
2025-05-24 08:08:56,715 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 62223.89, 'new_value': 67448.89}, {'field': 'amount', 'old_value': 62223.89, 'new_value': 67448.89}, {'field': 'count', 'old_value': 28, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 62223.89, 'new_value': 67448.89}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 31}]
2025-05-24 08:08:57,184 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-05-24 08:08:57,184 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 144133.91, 'new_value': 150517.27}, {'field': 'dailyBillAmount', 'old_value': 144133.91, 'new_value': 150517.27}, {'field': 'amount', 'old_value': 93902.82, 'new_value': 96522.28}, {'field': 'count', 'old_value': 1057, 'new_value': 1088}, {'field': 'instoreAmount', 'old_value': 85464.78, 'new_value': 87977.34}, {'field': 'instoreCount', 'old_value': 746, 'new_value': 774}, {'field': 'onlineAmount', 'old_value': 10229.21, 'new_value': 10336.11}, {'field': 'onlineCount', 'old_value': 311, 'new_value': 314}]
2025-05-24 08:08:57,637 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDL
2025-05-24 08:08:57,637 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-05, 变更字段: [{'field': 'amount', 'old_value': 2052.5, 'new_value': 2101.5}, {'field': 'count', 'old_value': 25, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 2052.5, 'new_value': 2101.5}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 27}]
2025-05-24 08:08:58,153 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-05-24 08:08:58,153 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 171059.3, 'new_value': 178457.5}, {'field': 'dailyBillAmount', 'old_value': 166253.55, 'new_value': 173651.75}, {'field': 'amount', 'old_value': 171059.3, 'new_value': 178457.5}, {'field': 'count', 'old_value': 722, 'new_value': 755}, {'field': 'instoreAmount', 'old_value': 171059.3, 'new_value': 178457.5}, {'field': 'instoreCount', 'old_value': 722, 'new_value': 755}]
2025-05-24 08:08:58,606 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-05-24 08:08:58,606 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20322.24, 'new_value': 21184.82}, {'field': 'dailyBillAmount', 'old_value': 20322.24, 'new_value': 21184.82}, {'field': 'amount', 'old_value': 24441.24, 'new_value': 25444.82}, {'field': 'count', 'old_value': 719, 'new_value': 750}, {'field': 'instoreAmount', 'old_value': 24461.04, 'new_value': 25464.62}, {'field': 'instoreCount', 'old_value': 719, 'new_value': 750}]
2025-05-24 08:08:59,184 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-05-24 08:08:59,184 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 262469.2, 'new_value': 278373.1}, {'field': 'amount', 'old_value': 262469.2, 'new_value': 278373.1}, {'field': 'count', 'old_value': 403, 'new_value': 430}, {'field': 'instoreAmount', 'old_value': 262469.2, 'new_value': 278373.1}, {'field': 'instoreCount', 'old_value': 403, 'new_value': 430}]
2025-05-24 08:08:59,684 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-05-24 08:08:59,684 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41675.82, 'new_value': 42310.68}, {'field': 'amount', 'old_value': 41675.82, 'new_value': 42310.68}, {'field': 'count', 'old_value': 334, 'new_value': 341}, {'field': 'instoreAmount', 'old_value': 41675.82, 'new_value': 42310.68}, {'field': 'instoreCount', 'old_value': 334, 'new_value': 341}]
2025-05-24 08:09:00,090 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIL
2025-05-24 08:09:00,090 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 275442.0, 'new_value': 297226.0}, {'field': 'amount', 'old_value': 275442.0, 'new_value': 297226.0}, {'field': 'count', 'old_value': 61, 'new_value': 66}, {'field': 'instoreAmount', 'old_value': 275442.0, 'new_value': 297226.0}, {'field': 'instoreCount', 'old_value': 61, 'new_value': 66}]
2025-05-24 08:09:00,512 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-05-24 08:09:00,512 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'amount', 'old_value': 35154.05, 'new_value': 36691.95}, {'field': 'count', 'old_value': 457, 'new_value': 478}, {'field': 'instoreAmount', 'old_value': 35154.05, 'new_value': 36691.95}, {'field': 'instoreCount', 'old_value': 457, 'new_value': 478}]
2025-05-24 08:09:01,012 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKL
2025-05-24 08:09:01,012 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38117.0, 'new_value': 39541.6}, {'field': 'dailyBillAmount', 'old_value': 38117.0, 'new_value': 39541.6}, {'field': 'amount', 'old_value': 39580.3, 'new_value': 41004.9}, {'field': 'count', 'old_value': 48, 'new_value': 50}, {'field': 'instoreAmount', 'old_value': 40478.3, 'new_value': 41902.9}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 50}]
2025-05-24 08:09:01,418 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-05-24 08:09:01,418 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 379532.88, 'new_value': 392573.08}, {'field': 'dailyBillAmount', 'old_value': 379532.88, 'new_value': 392573.08}, {'field': 'amount', 'old_value': 386703.88, 'new_value': 399744.08}, {'field': 'count', 'old_value': 1218, 'new_value': 1265}, {'field': 'instoreAmount', 'old_value': 386703.88, 'new_value': 399744.08}, {'field': 'instoreCount', 'old_value': 1218, 'new_value': 1265}]
2025-05-24 08:09:01,903 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-05-24 08:09:01,903 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 881783.39, 'new_value': 936489.67}, {'field': 'count', 'old_value': 1141, 'new_value': 1183}, {'field': 'instoreAmount', 'old_value': 881783.56, 'new_value': 936489.84}, {'field': 'instoreCount', 'old_value': 1141, 'new_value': 1183}]
2025-05-24 08:09:02,372 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-05-24 08:09:02,372 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 131887.9, 'new_value': 137349.5}, {'field': 'dailyBillAmount', 'old_value': 131887.9, 'new_value': 137349.5}, {'field': 'amount', 'old_value': 27812.9, 'new_value': 28520.8}, {'field': 'count', 'old_value': 106, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 27814.4, 'new_value': 28522.3}, {'field': 'instoreCount', 'old_value': 106, 'new_value': 110}]
2025-05-24 08:09:02,840 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-05-24 08:09:02,840 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 174431.88999999998, 'new_value': 178169.81}, {'field': 'amount', 'old_value': 174429.37, 'new_value': 178167.29}, {'field': 'count', 'old_value': 1806, 'new_value': 1851}, {'field': 'instoreAmount', 'old_value': 112853.53, 'new_value': 114443.45999999999}, {'field': 'instoreCount', 'old_value': 1024, 'new_value': 1040}, {'field': 'onlineAmount', 'old_value': 66022.98, 'new_value': 68286.07}, {'field': 'onlineCount', 'old_value': 782, 'new_value': 811}]
2025-05-24 08:09:03,247 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-05-24 08:09:03,247 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 287472.13, 'new_value': 300700.61}, {'field': 'dailyBillAmount', 'old_value': 287472.13, 'new_value': 300700.61}, {'field': 'amount', 'old_value': 26432.0, 'new_value': 27395.22}, {'field': 'count', 'old_value': 818, 'new_value': 842}, {'field': 'instoreAmount', 'old_value': 30457.14, 'new_value': 31662.66}, {'field': 'instoreCount', 'old_value': 818, 'new_value': 842}]
2025-05-24 08:09:03,684 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-05-24 08:09:03,684 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 265620.35, 'new_value': 273735.54}, {'field': 'dailyBillAmount', 'old_value': 265620.35, 'new_value': 273735.54}, {'field': 'amount', 'old_value': 137884.05, 'new_value': 141733.39}, {'field': 'count', 'old_value': 3090, 'new_value': 3187}, {'field': 'instoreAmount', 'old_value': 115644.65000000001, 'new_value': 118295.99}, {'field': 'instoreCount', 'old_value': 2585, 'new_value': 2656}, {'field': 'onlineAmount', 'old_value': 24369.34, 'new_value': 25568.260000000002}, {'field': 'onlineCount', 'old_value': 505, 'new_value': 531}]
2025-05-24 08:09:04,121 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-05-24 08:09:04,121 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 250717.4, 'new_value': 254461.9}, {'field': 'amount', 'old_value': 250715.8, 'new_value': 254460.3}, {'field': 'count', 'old_value': 996, 'new_value': 1013}, {'field': 'instoreAmount', 'old_value': 253807.3, 'new_value': 257551.8}, {'field': 'instoreCount', 'old_value': 996, 'new_value': 1013}]
2025-05-24 08:09:04,621 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-05-24 08:09:04,621 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'amount', 'old_value': 400942.05, 'new_value': 409616.74}, {'field': 'count', 'old_value': 7581, 'new_value': 7695}, {'field': 'instoreAmount', 'old_value': 374378.1, 'new_value': 381827.02}, {'field': 'instoreCount', 'old_value': 7067, 'new_value': 7154}, {'field': 'onlineAmount', 'old_value': 28140.3, 'new_value': 29366.86}, {'field': 'onlineCount', 'old_value': 514, 'new_value': 541}]
2025-05-24 08:09:05,090 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-05-24 08:09:05,090 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 291633.17, 'new_value': 311455.17}, {'field': 'amount', 'old_value': 259347.9, 'new_value': 279169.9}, {'field': 'count', 'old_value': 6255, 'new_value': 6719}, {'field': 'instoreAmount', 'old_value': 212952.4, 'new_value': 223135.0}, {'field': 'instoreCount', 'old_value': 4698, 'new_value': 4904}, {'field': 'onlineAmount', 'old_value': 46555.3, 'new_value': 56194.7}, {'field': 'onlineCount', 'old_value': 1557, 'new_value': 1815}]
2025-05-24 08:09:05,637 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUL
2025-05-24 08:09:05,637 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-05, 变更字段: [{'field': 'amount', 'old_value': 31924.579999999998, 'new_value': 31729.579999999998}, {'field': 'count', 'old_value': 828, 'new_value': 832}, {'field': 'instoreAmount', 'old_value': 1943.0, 'new_value': 2428.0}, {'field': 'instoreCount', 'old_value': 38, 'new_value': 40}, {'field': 'onlineAmount', 'old_value': 44927.729999999996, 'new_value': 44960.729999999996}, {'field': 'onlineCount', 'old_value': 790, 'new_value': 792}]
2025-05-24 08:09:06,090 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-05-24 08:09:06,090 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 73151.15, 'new_value': 81992.45}, {'field': 'dailyBillAmount', 'old_value': 73151.15, 'new_value': 81992.45}, {'field': 'amount', 'old_value': 144254.46, 'new_value': 151756.5}, {'field': 'count', 'old_value': 9826, 'new_value': 10285}, {'field': 'instoreAmount', 'old_value': 118588.75, 'new_value': 124138.86}, {'field': 'instoreCount', 'old_value': 7865, 'new_value': 8184}, {'field': 'onlineAmount', 'old_value': 29061.25, 'new_value': 31223.78}, {'field': 'onlineCount', 'old_value': 1961, 'new_value': 2101}]
2025-05-24 08:09:06,528 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-05-24 08:09:06,528 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 235047.61, 'new_value': 242246.36}, {'field': 'dailyBillAmount', 'old_value': 235047.61, 'new_value': 242246.36}, {'field': 'amount', 'old_value': 226197.78, 'new_value': 232824.56}, {'field': 'count', 'old_value': 6605, 'new_value': 6787}, {'field': 'instoreAmount', 'old_value': 227726.69, 'new_value': 234389.07}, {'field': 'instoreCount', 'old_value': 6605, 'new_value': 6787}]
2025-05-24 08:09:06,996 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-05-24 08:09:06,996 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 69831.47, 'new_value': 72302.75}, {'field': 'amount', 'old_value': 69828.22, 'new_value': 72299.5}, {'field': 'count', 'old_value': 3744, 'new_value': 3883}, {'field': 'instoreAmount', 'old_value': 40311.54, 'new_value': 41204.72}, {'field': 'instoreCount', 'old_value': 2327, 'new_value': 2388}, {'field': 'onlineAmount', 'old_value': 29519.93, 'new_value': 31098.03}, {'field': 'onlineCount', 'old_value': 1417, 'new_value': 1495}]
2025-05-24 08:09:07,496 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-05-24 08:09:07,496 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 121166.89, 'new_value': 125467.63}, {'field': 'dailyBillAmount', 'old_value': 121166.89, 'new_value': 125467.63}, {'field': 'amount', 'old_value': 25602.170000000002, 'new_value': 26466.850000000002}, {'field': 'count', 'old_value': 903, 'new_value': 940}, {'field': 'instoreAmount', 'old_value': 26454.48, 'new_value': 27428.68}, {'field': 'instoreCount', 'old_value': 903, 'new_value': 940}]
2025-05-24 08:09:07,934 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-05-24 08:09:07,934 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 98021.73999999999, 'new_value': 102562.97}, {'field': 'dailyBillAmount', 'old_value': 98021.73999999999, 'new_value': 102562.97}, {'field': 'amount', 'old_value': 84162.0, 'new_value': 87351.09}, {'field': 'count', 'old_value': 4177, 'new_value': 4341}, {'field': 'instoreAmount', 'old_value': 18513.940000000002, 'new_value': 19017.510000000002}, {'field': 'instoreCount', 'old_value': 1322, 'new_value': 1365}, {'field': 'onlineAmount', 'old_value': 67118.99, 'new_value': 69813.41}, {'field': 'onlineCount', 'old_value': 2855, 'new_value': 2976}]
2025-05-24 08:09:08,340 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-05-24 08:09:08,340 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 89168.73, 'new_value': 92993.06}, {'field': 'amount', 'old_value': 89167.85, 'new_value': 92992.18}, {'field': 'count', 'old_value': 2354, 'new_value': 2450}, {'field': 'instoreAmount', 'old_value': 85932.43000000001, 'new_value': 89413.27}, {'field': 'instoreCount', 'old_value': 2293, 'new_value': 2384}, {'field': 'onlineAmount', 'old_value': 4192.71, 'new_value': 4638.31}, {'field': 'onlineCount', 'old_value': 61, 'new_value': 66}]
2025-05-24 08:09:08,746 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-05-24 08:09:08,746 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'amount', 'old_value': 142993.54, 'new_value': 150786.82}, {'field': 'count', 'old_value': 5838, 'new_value': 6086}, {'field': 'instoreAmount', 'old_value': 145976.51, 'new_value': 153814.46}, {'field': 'instoreCount', 'old_value': 5784, 'new_value': 6031}, {'field': 'onlineAmount', 'old_value': 1977.01, 'new_value': 2031.51}, {'field': 'onlineCount', 'old_value': 54, 'new_value': 55}]
2025-05-24 08:09:09,200 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-05-24 08:09:09,200 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 105081.83, 'new_value': 109516.83}, {'field': 'count', 'old_value': 8718, 'new_value': 8989}, {'field': 'instoreAmount', 'old_value': 7229.71, 'new_value': 7747.16}, {'field': 'instoreCount', 'old_value': 403, 'new_value': 425}, {'field': 'onlineAmount', 'old_value': 102814.12, 'new_value': 106803.36}, {'field': 'onlineCount', 'old_value': 8315, 'new_value': 8564}]
2025-05-24 08:09:09,715 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-05-24 08:09:09,715 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 143740.61, 'new_value': 148125.45}, {'field': 'dailyBillAmount', 'old_value': 143740.61, 'new_value': 148125.45}, {'field': 'amount', 'old_value': 123089.6, 'new_value': 126465.15}, {'field': 'count', 'old_value': 4028, 'new_value': 4137}, {'field': 'instoreAmount', 'old_value': 67325.36, 'new_value': 68942.8}, {'field': 'instoreCount', 'old_value': 2898, 'new_value': 2965}, {'field': 'onlineAmount', 'old_value': 64033.41, 'new_value': 66159.52}, {'field': 'onlineCount', 'old_value': 1130, 'new_value': 1172}]
2025-05-24 08:09:10,231 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3M
2025-05-24 08:09:10,246 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 105953.53, 'new_value': 117247.45}, {'field': 'amount', 'old_value': 105953.0, 'new_value': 117246.92}, {'field': 'count', 'old_value': 64, 'new_value': 77}, {'field': 'instoreAmount', 'old_value': 105953.53, 'new_value': 117247.45}, {'field': 'instoreCount', 'old_value': 64, 'new_value': 77}]
2025-05-24 08:09:10,700 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-05-24 08:09:10,700 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 50929.47, 'new_value': 52881.87}, {'field': 'dailyBillAmount', 'old_value': 50929.47, 'new_value': 52881.87}, {'field': 'amount', 'old_value': 68822.07, 'new_value': 71212.56}, {'field': 'count', 'old_value': 2652, 'new_value': 2756}, {'field': 'instoreAmount', 'old_value': 22082.45, 'new_value': 22655.29}, {'field': 'instoreCount', 'old_value': 937, 'new_value': 965}, {'field': 'onlineAmount', 'old_value': 47679.78, 'new_value': 49557.83}, {'field': 'onlineCount', 'old_value': 1715, 'new_value': 1791}]
2025-05-24 08:09:11,121 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-05-24 08:09:11,121 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 83475.57, 'new_value': 86371.79}, {'field': 'dailyBillAmount', 'old_value': 83475.57, 'new_value': 86371.79}, {'field': 'amount', 'old_value': 85900.9, 'new_value': 88904.24}, {'field': 'count', 'old_value': 3059, 'new_value': 3158}, {'field': 'instoreAmount', 'old_value': 85900.9, 'new_value': 88904.24}, {'field': 'instoreCount', 'old_value': 3059, 'new_value': 3158}]
2025-05-24 08:09:11,543 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-05-24 08:09:11,543 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 292085.0, 'new_value': 297176.0}, {'field': 'dailyBillAmount', 'old_value': 292085.0, 'new_value': 297176.0}, {'field': 'amount', 'old_value': 316606.0, 'new_value': 322097.0}, {'field': 'count', 'old_value': 255, 'new_value': 259}, {'field': 'instoreAmount', 'old_value': 344792.0, 'new_value': 352276.0}, {'field': 'instoreCount', 'old_value': 255, 'new_value': 259}]
2025-05-24 08:09:12,075 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-05-24 08:09:12,075 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 205160.09, 'new_value': 211858.01}, {'field': 'dailyBillAmount', 'old_value': 205160.09, 'new_value': 211858.01}, {'field': 'amount', 'old_value': 216127.96, 'new_value': 221900.96}, {'field': 'count', 'old_value': 416, 'new_value': 427}, {'field': 'instoreAmount', 'old_value': 218801.46, 'new_value': 224574.46}, {'field': 'instoreCount', 'old_value': 416, 'new_value': 427}]
2025-05-24 08:09:12,496 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8M
2025-05-24 08:09:12,496 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32852.0, 'new_value': 41186.0}, {'field': 'amount', 'old_value': 32852.0, 'new_value': 41186.0}, {'field': 'count', 'old_value': 66, 'new_value': 87}, {'field': 'instoreAmount', 'old_value': 32852.0, 'new_value': 41186.0}, {'field': 'instoreCount', 'old_value': 66, 'new_value': 87}]
2025-05-24 08:09:13,012 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-05-24 08:09:13,012 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68290.0, 'new_value': 69020.0}, {'field': 'dailyBillAmount', 'old_value': 68290.0, 'new_value': 69020.0}, {'field': 'amount', 'old_value': 33521.0, 'new_value': 33900.0}, {'field': 'count', 'old_value': 90, 'new_value': 91}, {'field': 'instoreAmount', 'old_value': 34983.0, 'new_value': 35362.0}, {'field': 'instoreCount', 'old_value': 90, 'new_value': 91}]
2025-05-24 08:09:13,496 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-05-24 08:09:13,496 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 58611.0, 'new_value': 61315.0}, {'field': 'dailyBillAmount', 'old_value': 42292.0, 'new_value': 44996.0}, {'field': 'amount', 'old_value': 54369.0, 'new_value': 57073.0}, {'field': 'count', 'old_value': 72, 'new_value': 76}, {'field': 'instoreAmount', 'old_value': 54369.0, 'new_value': 57073.0}, {'field': 'instoreCount', 'old_value': 72, 'new_value': 76}]
2025-05-24 08:09:13,950 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-05-24 08:09:13,950 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60999.299999999996, 'new_value': 61695.299999999996}, {'field': 'amount', 'old_value': 60997.1, 'new_value': 61693.1}, {'field': 'count', 'old_value': 164, 'new_value': 167}, {'field': 'instoreAmount', 'old_value': 61488.0, 'new_value': 62184.0}, {'field': 'instoreCount', 'old_value': 164, 'new_value': 167}]
2025-05-24 08:09:14,387 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM571
2025-05-24 08:09:14,387 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 507906.0, 'new_value': 543000.0}, {'field': 'dailyBillAmount', 'old_value': 507906.0, 'new_value': 543000.0}, {'field': 'amount', 'old_value': 579533.0, 'new_value': 621124.0}, {'field': 'count', 'old_value': 70, 'new_value': 76}, {'field': 'instoreAmount', 'old_value': 579533.0, 'new_value': 621124.0}, {'field': 'instoreCount', 'old_value': 70, 'new_value': 76}]
2025-05-24 08:09:14,856 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM671
2025-05-24 08:09:14,856 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 76387.0, 'new_value': 84122.0}, {'field': 'amount', 'old_value': 76387.0, 'new_value': 84122.0}, {'field': 'count', 'old_value': 20, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 76387.0, 'new_value': 84122.0}, {'field': 'instoreCount', 'old_value': 20, 'new_value': 22}]
2025-05-24 08:09:15,309 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-05-24 08:09:15,309 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 213516.0, 'new_value': 220973.8}, {'field': 'dailyBillAmount', 'old_value': 213516.0, 'new_value': 220973.8}, {'field': 'amount', 'old_value': 304774.9, 'new_value': 312838.2}, {'field': 'count', 'old_value': 377, 'new_value': 390}, {'field': 'instoreAmount', 'old_value': 316270.36, 'new_value': 325835.26}, {'field': 'instoreCount', 'old_value': 377, 'new_value': 390}]
2025-05-24 08:09:15,746 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-05-24 08:09:15,746 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 97046.15, 'new_value': 99661.45}, {'field': 'dailyBillAmount', 'old_value': 97046.15, 'new_value': 99661.45}, {'field': 'amount', 'old_value': 34047.48, 'new_value': 37771.1}, {'field': 'count', 'old_value': 325, 'new_value': 366}, {'field': 'instoreAmount', 'old_value': 33830.78, 'new_value': 37304.68}, {'field': 'instoreCount', 'old_value': 277, 'new_value': 315}, {'field': 'onlineAmount', 'old_value': 2778.0, 'new_value': 3028.52}, {'field': 'onlineCount', 'old_value': 48, 'new_value': 51}]
2025-05-24 08:09:16,215 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMA71
2025-05-24 08:09:16,215 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 12019.0, 'new_value': 12757.0}, {'field': 'amount', 'old_value': 12019.0, 'new_value': 12757.0}, {'field': 'count', 'old_value': 33, 'new_value': 35}, {'field': 'instoreAmount', 'old_value': 12019.0, 'new_value': 12757.0}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 35}]
2025-05-24 08:09:16,621 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-05-24 08:09:16,621 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31987.0, 'new_value': 32542.0}, {'field': 'dailyBillAmount', 'old_value': 31987.0, 'new_value': 32542.0}, {'field': 'amount', 'old_value': 37274.0, 'new_value': 37829.0}, {'field': 'count', 'old_value': 121, 'new_value': 123}, {'field': 'instoreAmount', 'old_value': 37274.0, 'new_value': 37829.0}, {'field': 'instoreCount', 'old_value': 121, 'new_value': 123}]
2025-05-24 08:09:17,012 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMC71
2025-05-24 08:09:17,012 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31343.7, 'new_value': 32065.7}, {'field': 'amount', 'old_value': 31343.7, 'new_value': 32065.7}, {'field': 'count', 'old_value': 190, 'new_value': 194}, {'field': 'instoreAmount', 'old_value': 31681.7, 'new_value': 32403.7}, {'field': 'instoreCount', 'old_value': 190, 'new_value': 194}]
2025-05-24 08:09:17,403 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-05-24 08:09:17,403 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 7493.0, 'new_value': 7722.0}, {'field': 'dailyBillAmount', 'old_value': 7493.0, 'new_value': 7722.0}, {'field': 'amount', 'old_value': 35929.0, 'new_value': 36844.0}, {'field': 'count', 'old_value': 109, 'new_value': 113}, {'field': 'instoreAmount', 'old_value': 36704.0, 'new_value': 37619.0}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 113}]
2025-05-24 08:09:17,887 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-05-24 08:09:17,887 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 44882.89, 'new_value': 45334.24}, {'field': 'count', 'old_value': 430, 'new_value': 437}, {'field': 'instoreAmount', 'old_value': 36663.12, 'new_value': 36806.64}, {'field': 'instoreCount', 'old_value': 305, 'new_value': 307}, {'field': 'onlineAmount', 'old_value': 9242.8, 'new_value': 9550.63}, {'field': 'onlineCount', 'old_value': 125, 'new_value': 130}]
2025-05-24 08:09:18,325 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-05-24 08:09:18,325 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68192.0, 'new_value': 68410.0}, {'field': 'amount', 'old_value': 67992.0, 'new_value': 68210.0}, {'field': 'count', 'old_value': 87, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 68999.0, 'new_value': 69217.0}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 88}]
2025-05-24 08:09:18,731 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMG71
2025-05-24 08:09:18,731 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15541.0, 'new_value': 15910.0}, {'field': 'amount', 'old_value': 15541.0, 'new_value': 15910.0}, {'field': 'count', 'old_value': 26, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 15541.0, 'new_value': 15910.0}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 27}]
2025-05-24 08:09:19,137 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-05-24 08:09:19,137 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21201.47, 'new_value': 21495.07}, {'field': 'amount', 'old_value': 21200.77, 'new_value': 21494.37}, {'field': 'count', 'old_value': 84, 'new_value': 87}, {'field': 'instoreAmount', 'old_value': 21201.47, 'new_value': 21495.07}, {'field': 'instoreCount', 'old_value': 84, 'new_value': 87}]
2025-05-24 08:09:19,559 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMI71
2025-05-24 08:09:19,559 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37576.0, 'new_value': 38327.0}, {'field': 'dailyBillAmount', 'old_value': 37576.0, 'new_value': 38327.0}, {'field': 'amount', 'old_value': 37775.0, 'new_value': 38526.0}, {'field': 'count', 'old_value': 92, 'new_value': 94}, {'field': 'instoreAmount', 'old_value': 39021.0, 'new_value': 39772.0}, {'field': 'instoreCount', 'old_value': 92, 'new_value': 94}]
2025-05-24 08:09:20,012 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-05-24 08:09:20,012 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 313310.91, 'new_value': 320418.67}, {'field': 'dailyBillAmount', 'old_value': 293046.98, 'new_value': 298987.88}, {'field': 'amount', 'old_value': 311331.14, 'new_value': 318438.64}, {'field': 'count', 'old_value': 617, 'new_value': 675}, {'field': 'instoreAmount', 'old_value': 314818.94, 'new_value': 321926.7}, {'field': 'instoreCount', 'old_value': 617, 'new_value': 675}]
2025-05-24 08:09:20,465 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMK71
2025-05-24 08:09:20,465 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56741.0, 'new_value': 57611.0}, {'field': 'amount', 'old_value': 56741.0, 'new_value': 57611.0}, {'field': 'count', 'old_value': 258, 'new_value': 263}, {'field': 'instoreAmount', 'old_value': 57495.0, 'new_value': 58365.0}, {'field': 'instoreCount', 'old_value': 258, 'new_value': 263}]
2025-05-24 08:09:20,887 - INFO - 更新表单数据成功: FINST-VRA66VA1RMZU72KJ6T3JJ8YBFM4G3QAM6RBAM032
2025-05-24 08:09:20,887 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDQR0OPQVI0I86N3H2U1NE001F0K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33192.0, 'new_value': 36603.0}, {'field': 'amount', 'old_value': 33192.0, 'new_value': 36603.0}, {'field': 'count', 'old_value': 9, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 33192.0, 'new_value': 36603.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 11}]
2025-05-24 08:09:21,325 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-05-24 08:09:21,325 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68849.08, 'new_value': 74124.08}, {'field': 'dailyBillAmount', 'old_value': 68849.08, 'new_value': 74124.08}, {'field': 'amount', 'old_value': 72111.59999999999, 'new_value': 77386.59999999999}, {'field': 'count', 'old_value': 439, 'new_value': 474}, {'field': 'instoreAmount', 'old_value': 72111.59999999999, 'new_value': 77386.59999999999}, {'field': 'instoreCount', 'old_value': 439, 'new_value': 474}]
2025-05-24 08:09:21,809 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-05-24 08:09:21,809 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 90563.47, 'new_value': 95263.31}, {'field': 'dailyBillAmount', 'old_value': 90563.47, 'new_value': 95263.31}, {'field': 'amount', 'old_value': 31633.07, 'new_value': 33315.77}, {'field': 'count', 'old_value': 3091, 'new_value': 3283}, {'field': 'instoreAmount', 'old_value': 33754.2, 'new_value': 35482.5}, {'field': 'instoreCount', 'old_value': 3091, 'new_value': 3283}]
2025-05-24 08:09:22,278 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-05-24 08:09:22,293 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 538088.75, 'new_value': 559966.46}, {'field': 'dailyBillAmount', 'old_value': 538088.75, 'new_value': 559966.46}, {'field': 'amount', 'old_value': 552634.18, 'new_value': 574260.71}, {'field': 'count', 'old_value': 5398, 'new_value': 5672}, {'field': 'instoreAmount', 'old_value': 418810.59, 'new_value': 433926.09}, {'field': 'instoreCount', 'old_value': 2079, 'new_value': 2171}, {'field': 'onlineAmount', 'old_value': 138606.15, 'new_value': 145273.3}, {'field': 'onlineCount', 'old_value': 3319, 'new_value': 3501}]
2025-05-24 08:09:22,699 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-05-24 08:09:22,699 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 166996.78, 'new_value': 170180.28}, {'field': 'amount', 'old_value': 166996.78, 'new_value': 170180.28}, {'field': 'count', 'old_value': 1127, 'new_value': 1150}, {'field': 'instoreAmount', 'old_value': 167431.78, 'new_value': 170615.28}, {'field': 'instoreCount', 'old_value': 1127, 'new_value': 1150}]
2025-05-24 08:09:23,184 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-05-24 08:09:23,184 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 84642.71, 'new_value': 87183.48}, {'field': 'dailyBillAmount', 'old_value': 84642.71, 'new_value': 87183.48}, {'field': 'amount', 'old_value': 101964.34, 'new_value': 105802.82}, {'field': 'count', 'old_value': 4664, 'new_value': 4873}, {'field': 'instoreAmount', 'old_value': 51998.58, 'new_value': 53679.75}, {'field': 'instoreCount', 'old_value': 2705, 'new_value': 2814}, {'field': 'onlineAmount', 'old_value': 51045.08, 'new_value': 53264.5}, {'field': 'onlineCount', 'old_value': 1959, 'new_value': 2059}]
2025-05-24 08:09:23,637 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS71
2025-05-24 08:09:23,637 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45682.0, 'new_value': 45961.0}, {'field': 'amount', 'old_value': 45682.0, 'new_value': 45961.0}, {'field': 'count', 'old_value': 26, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 45682.0, 'new_value': 45961.0}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 28}]
2025-05-24 08:09:24,090 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-05-24 08:09:24,090 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 108296.05, 'new_value': 111402.33}, {'field': 'dailyBillAmount', 'old_value': 108296.05, 'new_value': 111402.33}, {'field': 'amount', 'old_value': 52386.63, 'new_value': 54194.58}, {'field': 'count', 'old_value': 3557, 'new_value': 3775}, {'field': 'instoreAmount', 'old_value': 7372.4, 'new_value': 7416.4}, {'field': 'instoreCount', 'old_value': 314, 'new_value': 319}, {'field': 'onlineAmount', 'old_value': 45014.23, 'new_value': 46778.18}, {'field': 'onlineCount', 'old_value': 3243, 'new_value': 3456}]
2025-05-24 08:09:24,528 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-05-24 08:09:24,528 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 304064.41, 'new_value': 315705.66}, {'field': 'dailyBillAmount', 'old_value': 304064.41, 'new_value': 315705.66}, {'field': 'amount', 'old_value': 283536.23, 'new_value': 295571.88}, {'field': 'count', 'old_value': 2488, 'new_value': 2613}, {'field': 'instoreAmount', 'old_value': 205762.09, 'new_value': 214251.69}, {'field': 'instoreCount', 'old_value': 1014, 'new_value': 1070}, {'field': 'onlineAmount', 'old_value': 77775.36, 'new_value': 81321.41}, {'field': 'onlineCount', 'old_value': 1474, 'new_value': 1543}]
2025-05-24 08:09:25,012 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-05-24 08:09:25,012 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 340098.96, 'new_value': 349515.69}, {'field': 'dailyBillAmount', 'old_value': 340098.96, 'new_value': 349515.69}, {'field': 'amount', 'old_value': 346824.06, 'new_value': 358756.36}, {'field': 'count', 'old_value': 2097, 'new_value': 2186}, {'field': 'instoreAmount', 'old_value': 316436.16, 'new_value': 324564.06}, {'field': 'instoreCount', 'old_value': 1769, 'new_value': 1821}, {'field': 'onlineAmount', 'old_value': 36312.0, 'new_value': 40116.9}, {'field': 'onlineCount', 'old_value': 328, 'new_value': 365}]
2025-05-24 08:09:25,465 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-05-24 08:09:25,465 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 873015.66, 'new_value': 900782.55}, {'field': 'dailyBillAmount', 'old_value': 873015.66, 'new_value': 900782.55}, {'field': 'amount', 'old_value': 969536.1, 'new_value': 999437.66}, {'field': 'count', 'old_value': 5358, 'new_value': 5561}, {'field': 'instoreAmount', 'old_value': 730187.41, 'new_value': 749735.58}, {'field': 'instoreCount', 'old_value': 2931, 'new_value': 3020}, {'field': 'onlineAmount', 'old_value': 247367.26, 'new_value': 257720.68}, {'field': 'onlineCount', 'old_value': 2427, 'new_value': 2541}]
2025-05-24 08:09:25,949 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-05-24 08:09:25,949 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 288095.05, 'new_value': 293243.36}, {'field': 'dailyBillAmount', 'old_value': 288095.05, 'new_value': 293243.36}, {'field': 'amount', 'old_value': 408007.22, 'new_value': 415534.35}, {'field': 'count', 'old_value': 1904, 'new_value': 1948}, {'field': 'instoreAmount', 'old_value': 383031.72, 'new_value': 389878.72}, {'field': 'instoreCount', 'old_value': 1525, 'new_value': 1554}, {'field': 'onlineAmount', 'old_value': 25565.7, 'new_value': 26245.83}, {'field': 'onlineCount', 'old_value': 379, 'new_value': 394}]
2025-05-24 08:09:26,371 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-05-24 08:09:26,371 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 333585.46, 'new_value': 347855.81}, {'field': 'dailyBillAmount', 'old_value': 333585.46, 'new_value': 347855.81}, {'field': 'amount', 'old_value': 314474.9, 'new_value': 326924.9}, {'field': 'count', 'old_value': 1408, 'new_value': 1461}, {'field': 'instoreAmount', 'old_value': 319550.5, 'new_value': 332867.5}, {'field': 'instoreCount', 'old_value': 1408, 'new_value': 1461}]
2025-05-24 08:09:26,856 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-05-24 08:09:26,856 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 688927.68, 'new_value': 712183.28}, {'field': 'amount', 'old_value': 688926.98, 'new_value': 712182.58}, {'field': 'count', 'old_value': 5499, 'new_value': 5724}, {'field': 'instoreAmount', 'old_value': 688927.68, 'new_value': 712183.28}, {'field': 'instoreCount', 'old_value': 5499, 'new_value': 5724}]
2025-05-24 08:09:27,262 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-05-24 08:09:27,262 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 553324.78, 'new_value': 580377.72}, {'field': 'dailyBillAmount', 'old_value': 553324.78, 'new_value': 580377.72}, {'field': 'amount', 'old_value': 692213.81, 'new_value': 722499.75}, {'field': 'count', 'old_value': 4810, 'new_value': 5026}, {'field': 'instoreAmount', 'old_value': 384009.7, 'new_value': 398825.4}, {'field': 'instoreCount', 'old_value': 2023, 'new_value': 2106}, {'field': 'onlineAmount', 'old_value': 317776.6, 'new_value': 333300.8}, {'field': 'onlineCount', 'old_value': 2787, 'new_value': 2920}]
2025-05-24 08:09:27,746 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-05-24 08:09:27,746 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 349799.51, 'new_value': 363115.61}, {'field': 'dailyBillAmount', 'old_value': 349799.51, 'new_value': 363115.61}, {'field': 'amount', 'old_value': 420189.85, 'new_value': 432607.0}, {'field': 'count', 'old_value': 4620, 'new_value': 4802}, {'field': 'instoreAmount', 'old_value': 289610.02, 'new_value': 295104.22000000003}, {'field': 'instoreCount', 'old_value': 1976, 'new_value': 2029}, {'field': 'onlineAmount', 'old_value': 132288.72, 'new_value': 139229.57}, {'field': 'onlineCount', 'old_value': 2644, 'new_value': 2773}]
2025-05-24 08:09:28,184 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-05-24 08:09:28,184 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 448541.23, 'new_value': 465199.74}, {'field': 'dailyBillAmount', 'old_value': 448541.23, 'new_value': 465199.74}, {'field': 'amount', 'old_value': 455123.09, 'new_value': 471551.01}, {'field': 'count', 'old_value': 4307, 'new_value': 4483}, {'field': 'instoreAmount', 'old_value': 396845.05, 'new_value': 410023.11}, {'field': 'instoreCount', 'old_value': 2256, 'new_value': 2346}, {'field': 'onlineAmount', 'old_value': 59303.08, 'new_value': 62573.04}, {'field': 'onlineCount', 'old_value': 2051, 'new_value': 2137}]
2025-05-24 08:09:28,715 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-05-24 08:09:28,715 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 114520.8, 'new_value': 115680.8}, {'field': 'amount', 'old_value': 114520.3, 'new_value': 115680.3}, {'field': 'count', 'old_value': 513, 'new_value': 526}, {'field': 'instoreAmount', 'old_value': 114520.8, 'new_value': 115680.8}, {'field': 'instoreCount', 'old_value': 513, 'new_value': 526}]
2025-05-24 08:09:29,184 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-05-24 08:09:29,184 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 312421.14, 'new_value': 328859.75}, {'field': 'dailyBillAmount', 'old_value': 312421.14, 'new_value': 328859.75}, {'field': 'amount', 'old_value': -240613.03, 'new_value': -249134.48}, {'field': 'count', 'old_value': 869, 'new_value': 898}, {'field': 'instoreAmount', 'old_value': 6023.5, 'new_value': 6241.3}, {'field': 'instoreCount', 'old_value': 287, 'new_value': 298}, {'field': 'onlineAmount', 'old_value': 18351.67, 'new_value': 18868.67}, {'field': 'onlineCount', 'old_value': 582, 'new_value': 600}]
2025-05-24 08:09:29,637 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-05-24 08:09:29,637 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 530172.25, 'new_value': 547479.1799999999}, {'field': 'dailyBillAmount', 'old_value': 530172.25, 'new_value': 547479.1799999999}, {'field': 'amount', 'old_value': 402147.31, 'new_value': 412096.15}, {'field': 'count', 'old_value': 1685, 'new_value': 1725}, {'field': 'instoreAmount', 'old_value': 402147.31, 'new_value': 412096.15}, {'field': 'instoreCount', 'old_value': 1685, 'new_value': 1725}]
2025-05-24 08:09:30,231 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-05-24 08:09:30,231 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 335684.52, 'new_value': 350540.85}, {'field': 'dailyBillAmount', 'old_value': 335684.52, 'new_value': 350540.85}, {'field': 'amount', 'old_value': 141639.0, 'new_value': 147173.2}, {'field': 'count', 'old_value': 587, 'new_value': 607}, {'field': 'instoreAmount', 'old_value': 147629.4, 'new_value': 153165.2}, {'field': 'instoreCount', 'old_value': 568, 'new_value': 588}]
2025-05-24 08:09:30,684 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-05-24 08:09:30,684 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 274056.05, 'new_value': 280069.96}, {'field': 'dailyBillAmount', 'old_value': 274056.05, 'new_value': 280069.96}, {'field': 'amount', 'old_value': 265274.69, 'new_value': 271201.23}, {'field': 'count', 'old_value': 1751, 'new_value': 1797}, {'field': 'instoreAmount', 'old_value': 250118.28, 'new_value': 255428.79}, {'field': 'instoreCount', 'old_value': 1353, 'new_value': 1380}, {'field': 'onlineAmount', 'old_value': 15320.56, 'new_value': 15936.59}, {'field': 'onlineCount', 'old_value': 398, 'new_value': 417}]
2025-05-24 08:09:31,168 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-05-24 08:09:31,168 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 294893.42, 'new_value': 303948.11}, {'field': 'dailyBillAmount', 'old_value': 294893.42, 'new_value': 303948.11}, {'field': 'amount', 'old_value': 125588.83, 'new_value': 128844.61}, {'field': 'count', 'old_value': 2091, 'new_value': 2188}, {'field': 'instoreAmount', 'old_value': 72677.91, 'new_value': 74203.24}, {'field': 'instoreCount', 'old_value': 541, 'new_value': 565}, {'field': 'onlineAmount', 'old_value': 52914.17, 'new_value': 54644.62}, {'field': 'onlineCount', 'old_value': 1550, 'new_value': 1623}]
2025-05-24 08:09:31,606 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM881
2025-05-24 08:09:31,606 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21129.0, 'new_value': 25903.0}, {'field': 'dailyBillAmount', 'old_value': 21129.0, 'new_value': 25903.0}, {'field': 'amount', 'old_value': 56166.0, 'new_value': 56445.0}, {'field': 'count', 'old_value': 30, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 56166.0, 'new_value': 56445.0}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 31}]
2025-05-24 08:09:32,043 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-05-24 08:09:32,043 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 123262.56999999999, 'new_value': 127666.19}, {'field': 'amount', 'old_value': 123252.58, 'new_value': 127655.79000000001}, {'field': 'count', 'old_value': 5594, 'new_value': 5834}, {'field': 'instoreAmount', 'old_value': 43902.28, 'new_value': 46302.36}, {'field': 'instoreCount', 'old_value': 1777, 'new_value': 1818}, {'field': 'onlineAmount', 'old_value': 84235.65, 'new_value': 88258.89}, {'field': 'onlineCount', 'old_value': 3817, 'new_value': 4016}]
2025-05-24 08:09:32,387 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-05-24 08:09:32,387 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38441.9, 'new_value': 40571.9}, {'field': 'amount', 'old_value': 38441.9, 'new_value': 40571.9}, {'field': 'count', 'old_value': 174, 'new_value': 182}, {'field': 'instoreAmount', 'old_value': 38441.9, 'new_value': 40571.9}, {'field': 'instoreCount', 'old_value': 174, 'new_value': 182}]
2025-05-24 08:09:32,840 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-05-24 08:09:32,840 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 343423.07, 'new_value': 352224.21}, {'field': 'dailyBillAmount', 'old_value': 343423.07, 'new_value': 352224.21}, {'field': 'amount', 'old_value': 137566.3, 'new_value': 140663.2}, {'field': 'count', 'old_value': 2567, 'new_value': 2631}, {'field': 'instoreAmount', 'old_value': 138735.2, 'new_value': 141863.3}, {'field': 'instoreCount', 'old_value': 2567, 'new_value': 2631}]
2025-05-24 08:09:33,309 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-05-24 08:09:33,309 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 141046.7, 'new_value': 145143.0}, {'field': 'amount', 'old_value': 141045.5, 'new_value': 145141.8}, {'field': 'count', 'old_value': 3366, 'new_value': 3479}, {'field': 'instoreAmount', 'old_value': 141304.18, 'new_value': 145400.48}, {'field': 'instoreCount', 'old_value': 3366, 'new_value': 3479}]
2025-05-24 08:09:33,715 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-05-24 08:09:33,715 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 26639.18, 'new_value': 27719.75}, {'field': 'amount', 'old_value': 26635.829999999998, 'new_value': 27715.53}, {'field': 'count', 'old_value': 1564, 'new_value': 1651}, {'field': 'instoreAmount', 'old_value': 13985.84, 'new_value': 14350.84}, {'field': 'instoreCount', 'old_value': 692, 'new_value': 717}, {'field': 'onlineAmount', 'old_value': 13126.85, 'new_value': 13848.84}, {'field': 'onlineCount', 'old_value': 872, 'new_value': 934}]
2025-05-24 08:09:34,168 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-05-24 08:09:34,168 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 43384.9, 'new_value': 45183.1}, {'field': 'amount', 'old_value': 43384.9, 'new_value': 45183.1}, {'field': 'count', 'old_value': 110, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 43384.9, 'new_value': 45183.1}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 114}]
2025-05-24 08:09:34,621 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-05-24 08:09:34,621 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 167969.94, 'new_value': 173328.36}, {'field': 'dailyBillAmount', 'old_value': 139667.3, 'new_value': 143523.4}, {'field': 'amount', 'old_value': 167969.26, 'new_value': 173327.68}, {'field': 'count', 'old_value': 2348, 'new_value': 2451}, {'field': 'instoreAmount', 'old_value': 160873.5, 'new_value': 165648.1}, {'field': 'instoreCount', 'old_value': 2042, 'new_value': 2127}, {'field': 'onlineAmount', 'old_value': 7323.56, 'new_value': 7907.38}, {'field': 'onlineCount', 'old_value': 306, 'new_value': 324}]
2025-05-24 08:09:35,043 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-05-24 08:09:35,043 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 24754.74, 'new_value': 25574.44}, {'field': 'amount', 'old_value': 24753.94, 'new_value': 25573.64}, {'field': 'count', 'old_value': 1052, 'new_value': 1091}, {'field': 'instoreAmount', 'old_value': 20561.94, 'new_value': 21230.94}, {'field': 'instoreCount', 'old_value': 937, 'new_value': 970}, {'field': 'onlineAmount', 'old_value': 4233.0, 'new_value': 4429.2}, {'field': 'onlineCount', 'old_value': 115, 'new_value': 121}]
2025-05-24 08:09:35,481 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-05-24 08:09:35,481 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 343926.05, 'new_value': 355107.29}, {'field': 'dailyBillAmount', 'old_value': 343926.05, 'new_value': 355107.29}, {'field': 'amount', 'old_value': 442713.67, 'new_value': 459728.53}, {'field': 'count', 'old_value': 4632, 'new_value': 4833}, {'field': 'instoreAmount', 'old_value': 417329.83, 'new_value': 432681.63}, {'field': 'instoreCount', 'old_value': 3213, 'new_value': 3335}, {'field': 'onlineAmount', 'old_value': 34761.43, 'new_value': 36696.61}, {'field': 'onlineCount', 'old_value': 1419, 'new_value': 1498}]
2025-05-24 08:09:35,965 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-05-24 08:09:35,965 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 132329.92, 'new_value': 137850.73}, {'field': 'dailyBillAmount', 'old_value': 132329.92, 'new_value': 137850.73}, {'field': 'amount', 'old_value': 33817.25, 'new_value': 34762.340000000004}, {'field': 'count', 'old_value': 543, 'new_value': 562}, {'field': 'instoreAmount', 'old_value': 21673.61, 'new_value': 22188.28}, {'field': 'instoreCount', 'old_value': 279, 'new_value': 293}, {'field': 'onlineAmount', 'old_value': 13007.65, 'new_value': 13438.15}, {'field': 'onlineCount', 'old_value': 264, 'new_value': 269}]
2025-05-24 08:09:36,356 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-05-24 08:09:36,356 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 131448.19, 'new_value': 138404.68}, {'field': 'dailyBillAmount', 'old_value': 116490.89, 'new_value': 123442.6}, {'field': 'amount', 'old_value': 131446.31, 'new_value': 138402.8}, {'field': 'count', 'old_value': 7424, 'new_value': 7802}, {'field': 'instoreAmount', 'old_value': 80398.68, 'new_value': 84611.39}, {'field': 'instoreCount', 'old_value': 4456, 'new_value': 4674}, {'field': 'onlineAmount', 'old_value': 52762.95, 'new_value': 55556.07}, {'field': 'onlineCount', 'old_value': 2968, 'new_value': 3128}]
2025-05-24 08:09:36,840 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-05-24 08:09:36,840 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 69996.91, 'new_value': 73476.3}, {'field': 'amount', 'old_value': 69987.44, 'new_value': 73466.86}, {'field': 'count', 'old_value': 4457, 'new_value': 4712}, {'field': 'instoreAmount', 'old_value': 31077.04, 'new_value': 32526.83}, {'field': 'instoreCount', 'old_value': 1798, 'new_value': 1884}, {'field': 'onlineAmount', 'old_value': 40914.03, 'new_value': 43187.18}, {'field': 'onlineCount', 'old_value': 2659, 'new_value': 2828}]
2025-05-24 08:09:37,184 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-05-24 08:09:37,184 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'amount', 'old_value': 137094.2, 'new_value': 142961.5}, {'field': 'count', 'old_value': 1368, 'new_value': 1442}, {'field': 'instoreAmount', 'old_value': 137293.58, 'new_value': 143161.41}, {'field': 'instoreCount', 'old_value': 1368, 'new_value': 1442}]
2025-05-24 08:09:37,653 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-05-24 08:09:37,653 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 113652.91, 'new_value': 118465.18}, {'field': 'dailyBillAmount', 'old_value': 117495.45, 'new_value': 122502.43}, {'field': 'amount', 'old_value': 113647.1, 'new_value': 118459.37}, {'field': 'count', 'old_value': 2283, 'new_value': 2401}, {'field': 'instoreAmount', 'old_value': 108884.82, 'new_value': 113457.0}, {'field': 'instoreCount', 'old_value': 1913, 'new_value': 2012}, {'field': 'onlineAmount', 'old_value': 4864.33, 'new_value': 5116.5}, {'field': 'onlineCount', 'old_value': 370, 'new_value': 389}]
2025-05-24 08:09:38,137 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-05-24 08:09:38,137 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 179983.06, 'new_value': 190567.44}, {'field': 'dailyBillAmount', 'old_value': 179983.06, 'new_value': 190567.44}, {'field': 'amount', 'old_value': 23080.72, 'new_value': 24540.22}, {'field': 'count', 'old_value': 922, 'new_value': 962}, {'field': 'instoreAmount', 'old_value': 26610.11, 'new_value': 28091.91}, {'field': 'instoreCount', 'old_value': 922, 'new_value': 962}]
2025-05-24 08:09:38,606 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-05-24 08:09:38,606 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 458394.49, 'new_value': 482115.96}, {'field': 'dailyBillAmount', 'old_value': 458394.49, 'new_value': 482115.96}, {'field': 'amount', 'old_value': 46332.159999999996, 'new_value': 48905.86}, {'field': 'count', 'old_value': 228, 'new_value': 238}, {'field': 'instoreAmount', 'old_value': 46558.159999999996, 'new_value': 49131.86}, {'field': 'instoreCount', 'old_value': 228, 'new_value': 238}]
2025-05-24 08:09:39,090 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-05-24 08:09:39,090 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 15667.19, 'new_value': 16387.22}, {'field': 'count', 'old_value': 804, 'new_value': 844}, {'field': 'onlineAmount', 'old_value': 15799.24, 'new_value': 16519.27}, {'field': 'onlineCount', 'old_value': 804, 'new_value': 844}]
2025-05-24 08:09:39,528 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-05-24 08:09:39,528 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 278187.34, 'new_value': 294278.08}, {'field': 'amount', 'old_value': 278033.56, 'new_value': 294124.3}, {'field': 'count', 'old_value': 2866, 'new_value': 3042}, {'field': 'instoreAmount', 'old_value': 264893.2, 'new_value': 279985.7}, {'field': 'instoreCount', 'old_value': 2412, 'new_value': 2562}, {'field': 'onlineAmount', 'old_value': 18527.45, 'new_value': 19531.350000000002}, {'field': 'onlineCount', 'old_value': 454, 'new_value': 480}]
2025-05-24 08:09:40,012 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-05-24 08:09:40,012 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 159680.8, 'new_value': 168065.52}, {'field': 'dailyBillAmount', 'old_value': 155880.33, 'new_value': 164265.05}, {'field': 'amount', 'old_value': 120091.99, 'new_value': 125300.07}, {'field': 'count', 'old_value': 4319, 'new_value': 4462}, {'field': 'instoreAmount', 'old_value': 52663.26, 'new_value': 54419.49}, {'field': 'instoreCount', 'old_value': 1826, 'new_value': 1861}, {'field': 'onlineAmount', 'old_value': 69249.15, 'new_value': 72701.7}, {'field': 'onlineCount', 'old_value': 2493, 'new_value': 2601}]
2025-05-24 08:09:40,449 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-05-24 08:09:40,449 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53417.06, 'new_value': 59523.0}, {'field': 'dailyBillAmount', 'old_value': 53417.06, 'new_value': 59523.0}, {'field': 'amount', 'old_value': 4344.15, 'new_value': 4740.33}, {'field': 'count', 'old_value': 199, 'new_value': 219}, {'field': 'instoreAmount', 'old_value': 4344.15, 'new_value': 4740.33}, {'field': 'instoreCount', 'old_value': 199, 'new_value': 219}]
2025-05-24 08:09:40,840 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-05-24 08:09:40,840 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 5382.42, 'new_value': 5718.2699999999995}, {'field': 'count', 'old_value': 237, 'new_value': 250}, {'field': 'onlineAmount', 'old_value': 5382.42, 'new_value': 5718.2699999999995}, {'field': 'onlineCount', 'old_value': 237, 'new_value': 250}]
2025-05-24 08:09:41,262 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-05-24 08:09:41,262 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 94295.39, 'new_value': 98665.43}, {'field': 'dailyBillAmount', 'old_value': 47208.74, 'new_value': 48855.74}, {'field': 'amount', 'old_value': 94294.8, 'new_value': 98664.84}, {'field': 'count', 'old_value': 2306, 'new_value': 2426}, {'field': 'instoreAmount', 'old_value': 50889.46, 'new_value': 53266.56}, {'field': 'instoreCount', 'old_value': 1234, 'new_value': 1289}, {'field': 'onlineAmount', 'old_value': 45655.36, 'new_value': 48237.3}, {'field': 'onlineCount', 'old_value': 1072, 'new_value': 1137}]
2025-05-24 08:09:41,684 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-05-24 08:09:41,684 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45479.47, 'new_value': 49346.67}, {'field': 'amount', 'old_value': 45479.47, 'new_value': 49346.67}, {'field': 'count', 'old_value': 1701, 'new_value': 1851}, {'field': 'instoreAmount', 'old_value': 46050.23, 'new_value': 49936.43}, {'field': 'instoreCount', 'old_value': 1701, 'new_value': 1851}]
2025-05-24 08:09:42,137 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-05-24 08:09:42,137 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45668.23, 'new_value': 48110.69}, {'field': 'dailyBillAmount', 'old_value': 45668.23, 'new_value': 48110.69}, {'field': 'amount', 'old_value': 36038.52, 'new_value': 37401.41}, {'field': 'count', 'old_value': 1630, 'new_value': 1715}, {'field': 'instoreAmount', 'old_value': 19934.97, 'new_value': 20381.43}, {'field': 'instoreCount', 'old_value': 683, 'new_value': 696}, {'field': 'onlineAmount', 'old_value': 16177.79, 'new_value': 17102.2}, {'field': 'onlineCount', 'old_value': 947, 'new_value': 1019}]
2025-05-24 08:09:42,668 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-05-24 08:09:42,684 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 74747.65, 'new_value': 77046.33}, {'field': 'amount', 'old_value': 74747.65, 'new_value': 77046.33}, {'field': 'count', 'old_value': 2294, 'new_value': 2368}, {'field': 'instoreAmount', 'old_value': 29624.81, 'new_value': 30760.54}, {'field': 'instoreCount', 'old_value': 1135, 'new_value': 1176}, {'field': 'onlineAmount', 'old_value': 45224.33, 'new_value': 46387.28}, {'field': 'onlineCount', 'old_value': 1159, 'new_value': 1192}]
2025-05-24 08:09:43,090 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-05-24 08:09:43,090 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44941.29, 'new_value': 46875.25}, {'field': 'amount', 'old_value': 44940.39, 'new_value': 46874.35}, {'field': 'count', 'old_value': 1065, 'new_value': 1110}, {'field': 'instoreAmount', 'old_value': 34931.3, 'new_value': 36159.3}, {'field': 'instoreCount', 'old_value': 855, 'new_value': 888}, {'field': 'onlineAmount', 'old_value': 10460.52, 'new_value': 11191.960000000001}, {'field': 'onlineCount', 'old_value': 210, 'new_value': 222}]
2025-05-24 08:09:43,527 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-05-24 08:09:43,527 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'amount', 'old_value': 140242.98, 'new_value': 146743.51}, {'field': 'count', 'old_value': 3519, 'new_value': 3696}, {'field': 'instoreAmount', 'old_value': 88789.67, 'new_value': 92733.52}, {'field': 'instoreCount', 'old_value': 1744, 'new_value': 1827}, {'field': 'onlineAmount', 'old_value': 62931.17, 'new_value': 66307.33}, {'field': 'onlineCount', 'old_value': 1775, 'new_value': 1869}]
2025-05-24 08:09:43,949 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-05-24 08:09:43,949 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 637396.92, 'new_value': 660314.48}, {'field': 'dailyBillAmount', 'old_value': 637396.92, 'new_value': 660314.48}, {'field': 'amount', 'old_value': 579433.0, 'new_value': 598788.0}, {'field': 'count', 'old_value': 3459, 'new_value': 3576}, {'field': 'instoreAmount', 'old_value': 410923.7, 'new_value': 422015.0}, {'field': 'instoreCount', 'old_value': 2676, 'new_value': 2753}, {'field': 'onlineAmount', 'old_value': 168511.7, 'new_value': 176775.4}, {'field': 'onlineCount', 'old_value': 783, 'new_value': 823}]
2025-05-24 08:09:44,418 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-05-24 08:09:44,418 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 929948.92, 'new_value': 970525.92}, {'field': 'amount', 'old_value': 929948.42, 'new_value': 970525.42}, {'field': 'count', 'old_value': 3266, 'new_value': 3408}, {'field': 'instoreAmount', 'old_value': 929948.92, 'new_value': 970525.92}, {'field': 'instoreCount', 'old_value': 3266, 'new_value': 3408}]
2025-05-24 08:09:44,809 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-05-24 08:09:44,809 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 534490.45, 'new_value': 561512.55}, {'field': 'dailyBillAmount', 'old_value': 474231.45999999996, 'new_value': 497743.0}, {'field': 'amount', 'old_value': 534490.45, 'new_value': 561512.55}, {'field': 'count', 'old_value': 3318, 'new_value': 3488}, {'field': 'instoreAmount', 'old_value': 487227.98, 'new_value': 512356.02}, {'field': 'instoreCount', 'old_value': 2089, 'new_value': 2198}, {'field': 'onlineAmount', 'old_value': 47573.6, 'new_value': 49528.51}, {'field': 'onlineCount', 'old_value': 1229, 'new_value': 1290}]
2025-05-24 08:09:45,262 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-05-24 08:09:45,262 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 532584.5700000001, 'new_value': 552101.64}, {'field': 'dailyBillAmount', 'old_value': 515813.08, 'new_value': 535290.15}, {'field': 'amount', 'old_value': 532577.98, 'new_value': 552095.05}, {'field': 'count', 'old_value': 1273, 'new_value': 1337}, {'field': 'instoreAmount', 'old_value': 496289.8, 'new_value': 514421.8}, {'field': 'instoreCount', 'old_value': 983, 'new_value': 1034}, {'field': 'onlineAmount', 'old_value': 36422.05, 'new_value': 37807.12}, {'field': 'onlineCount', 'old_value': 290, 'new_value': 303}]
2025-05-24 08:09:45,762 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-05-24 08:09:45,762 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 633640.03, 'new_value': 668389.05}, {'field': 'amount', 'old_value': 633639.35, 'new_value': 668388.37}, {'field': 'count', 'old_value': 3330, 'new_value': 3535}, {'field': 'instoreAmount', 'old_value': 596817.46, 'new_value': 628648.46}, {'field': 'instoreCount', 'old_value': 2224, 'new_value': 2342}, {'field': 'onlineAmount', 'old_value': 36938.72, 'new_value': 39856.74}, {'field': 'onlineCount', 'old_value': 1106, 'new_value': 1193}]
2025-05-24 08:09:46,199 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-05-24 08:09:46,199 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 749582.89, 'new_value': 762968.14}, {'field': 'dailyBillAmount', 'old_value': 749582.89, 'new_value': 762968.14}, {'field': 'amount', 'old_value': 675368.61, 'new_value': 687366.35}, {'field': 'count', 'old_value': 3379, 'new_value': 3430}, {'field': 'instoreAmount', 'old_value': 619346.27, 'new_value': 630821.97}, {'field': 'instoreCount', 'old_value': 2799, 'new_value': 2841}, {'field': 'onlineAmount', 'old_value': 56751.07, 'new_value': 57273.25}, {'field': 'onlineCount', 'old_value': 580, 'new_value': 589}]
2025-05-24 08:09:46,699 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-05-24 08:09:46,699 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 174174.84, 'new_value': 185992.84}, {'field': 'dailyBillAmount', 'old_value': 172768.29, 'new_value': 184586.29}, {'field': 'amount', 'old_value': 170908.66, 'new_value': 182427.66}, {'field': 'count', 'old_value': 249, 'new_value': 267}, {'field': 'instoreAmount', 'old_value': 170908.66, 'new_value': 182427.66}, {'field': 'instoreCount', 'old_value': 249, 'new_value': 267}]
2025-05-24 08:09:47,137 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-05-24 08:09:47,137 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 147454.38, 'new_value': 156890.23}, {'field': 'dailyBillAmount', 'old_value': 147454.38, 'new_value': 156890.23}, {'field': 'amount', 'old_value': 128743.84, 'new_value': 138257.64}, {'field': 'count', 'old_value': 219, 'new_value': 232}, {'field': 'instoreAmount', 'old_value': 126731.5, 'new_value': 135852.2}, {'field': 'instoreCount', 'old_value': 202, 'new_value': 213}, {'field': 'onlineAmount', 'old_value': 2979.26, 'new_value': 3372.36}, {'field': 'onlineCount', 'old_value': 17, 'new_value': 19}]
2025-05-24 08:09:47,621 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-05-24 08:09:47,621 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20115.6, 'new_value': 20293.37}, {'field': 'amount', 'old_value': 20115.6, 'new_value': 20293.37}, {'field': 'count', 'old_value': 420, 'new_value': 427}, {'field': 'instoreAmount', 'old_value': 20115.6, 'new_value': 20293.37}, {'field': 'instoreCount', 'old_value': 420, 'new_value': 427}]
2025-05-24 08:09:48,090 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-05-24 08:09:48,090 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 84139.17, 'new_value': 86649.95}, {'field': 'amount', 'old_value': 84139.17, 'new_value': 86649.95}, {'field': 'count', 'old_value': 709, 'new_value': 735}, {'field': 'instoreAmount', 'old_value': 84690.01, 'new_value': 87200.79}, {'field': 'instoreCount', 'old_value': 709, 'new_value': 735}]
2025-05-24 08:09:48,527 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-05-24 08:09:48,527 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 271346.01, 'new_value': 287743.3}, {'field': 'dailyBillAmount', 'old_value': 271346.01, 'new_value': 287743.3}, {'field': 'amount', 'old_value': 290124.23, 'new_value': 305937.72000000003}, {'field': 'count', 'old_value': 7868, 'new_value': 8335}, {'field': 'instoreAmount', 'old_value': 275071.59, 'new_value': 289866.3}, {'field': 'instoreCount', 'old_value': 7108, 'new_value': 7527}, {'field': 'onlineAmount', 'old_value': 19558.23, 'new_value': 20679.51}, {'field': 'onlineCount', 'old_value': 760, 'new_value': 808}]
2025-05-24 08:09:48,949 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC91
2025-05-24 08:09:48,949 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 89805.54, 'new_value': 95591.54}, {'field': 'dailyBillAmount', 'old_value': 89805.54, 'new_value': 95591.54}, {'field': 'amount', 'old_value': 91833.54, 'new_value': 97895.54}, {'field': 'count', 'old_value': 74, 'new_value': 79}, {'field': 'instoreAmount', 'old_value': 91833.54, 'new_value': 97895.54}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 79}]
2025-05-24 08:09:49,481 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-05-24 08:09:49,481 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 744812.8, 'new_value': 773392.61}, {'field': 'dailyBillAmount', 'old_value': 744812.8, 'new_value': 773392.61}, {'field': 'amount', 'old_value': 679216.12, 'new_value': 708519.12}, {'field': 'count', 'old_value': 1686, 'new_value': 1780}, {'field': 'instoreAmount', 'old_value': 706635.7, 'new_value': 735215.51}, {'field': 'instoreCount', 'old_value': 1402, 'new_value': 1476}, {'field': 'onlineAmount', 'old_value': 6274.69, 'new_value': 6997.88}, {'field': 'onlineCount', 'old_value': 284, 'new_value': 304}]
2025-05-24 08:09:49,887 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-05-24 08:09:49,887 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1100280.96, 'new_value': 1147770.77}, {'field': 'amount', 'old_value': 1100280.96, 'new_value': 1147770.77}, {'field': 'count', 'old_value': 3516, 'new_value': 3674}, {'field': 'instoreAmount', 'old_value': 1101491.96, 'new_value': 1148981.77}, {'field': 'instoreCount', 'old_value': 3516, 'new_value': 3674}]
2025-05-24 08:09:50,293 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-05-24 08:09:50,293 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 716122.71, 'new_value': 769275.25}, {'field': 'dailyBillAmount', 'old_value': 716122.71, 'new_value': 769275.25}, {'field': 'amount', 'old_value': 677857.46, 'new_value': 713681.53}, {'field': 'count', 'old_value': 2434, 'new_value': 2569}, {'field': 'instoreAmount', 'old_value': 660525.71, 'new_value': 695035.08}, {'field': 'instoreCount', 'old_value': 1475, 'new_value': 1567}, {'field': 'onlineAmount', 'old_value': 29107.52, 'new_value': 30661.32}, {'field': 'onlineCount', 'old_value': 959, 'new_value': 1002}]
2025-05-24 08:09:50,809 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-05-24 08:09:50,809 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1617300.29, 'new_value': 1683737.39}, {'field': 'dailyBillAmount', 'old_value': 1617300.29, 'new_value': 1683737.39}, {'field': 'amount', 'old_value': 1667274.0, 'new_value': 1735222.0}, {'field': 'count', 'old_value': 4467, 'new_value': 4622}, {'field': 'instoreAmount', 'old_value': 1667274.0, 'new_value': 1735222.0}, {'field': 'instoreCount', 'old_value': 4467, 'new_value': 4622}]
2025-05-24 08:09:51,215 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-05-24 08:09:51,215 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 247914.43, 'new_value': 263213.44}, {'field': 'dailyBillAmount', 'old_value': 247914.43, 'new_value': 263213.44}, {'field': 'amount', 'old_value': 254003.21, 'new_value': 269155.22000000003}, {'field': 'count', 'old_value': 1376, 'new_value': 1453}, {'field': 'instoreAmount', 'old_value': 245736.0, 'new_value': 260553.2}, {'field': 'instoreCount', 'old_value': 1159, 'new_value': 1226}, {'field': 'onlineAmount', 'old_value': 13201.09, 'new_value': 13718.9}, {'field': 'onlineCount', 'old_value': 217, 'new_value': 227}]
2025-05-24 08:09:51,731 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-05-24 08:09:51,731 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 853463.3200000001, 'new_value': 904745.26}, {'field': 'dailyBillAmount', 'old_value': 853463.3200000001, 'new_value': 904745.26}, {'field': 'amount', 'old_value': 910516.81, 'new_value': 961798.75}, {'field': 'count', 'old_value': 3782, 'new_value': 3997}, {'field': 'instoreAmount', 'old_value': 910517.26, 'new_value': 961799.2000000001}, {'field': 'instoreCount', 'old_value': 3782, 'new_value': 3997}]
2025-05-24 08:09:52,215 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-05-24 08:09:52,215 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 365750.68, 'new_value': 399008.13}, {'field': 'dailyBillAmount', 'old_value': 365750.68, 'new_value': 399008.13}, {'field': 'amount', 'old_value': 622277.0, 'new_value': 648671.6}, {'field': 'count', 'old_value': 1042, 'new_value': 1093}, {'field': 'instoreAmount', 'old_value': 617540.48, 'new_value': 643818.48}, {'field': 'instoreCount', 'old_value': 1010, 'new_value': 1060}, {'field': 'onlineAmount', 'old_value': 5002.0, 'new_value': 5118.6}, {'field': 'onlineCount', 'old_value': 32, 'new_value': 33}]
2025-05-24 08:09:52,652 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-05-24 08:09:52,652 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 216143.74, 'new_value': 228787.89}, {'field': 'dailyBillAmount', 'old_value': 216143.74, 'new_value': 228787.89}, {'field': 'amount', 'old_value': 250583.3, 'new_value': 264112.3}, {'field': 'count', 'old_value': 1773, 'new_value': 1852}, {'field': 'instoreAmount', 'old_value': 254483.3, 'new_value': 268012.3}, {'field': 'instoreCount', 'old_value': 1773, 'new_value': 1852}]
2025-05-24 08:09:52,996 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-05-24 08:09:52,996 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 144790.04, 'new_value': 151484.5}, {'field': 'dailyBillAmount', 'old_value': 144790.04, 'new_value': 151484.5}, {'field': 'amount', 'old_value': 115499.47, 'new_value': 121902.87}, {'field': 'count', 'old_value': 763, 'new_value': 814}, {'field': 'instoreAmount', 'old_value': 115242.0, 'new_value': 121674.0}, {'field': 'instoreCount', 'old_value': 713, 'new_value': 762}, {'field': 'onlineAmount', 'old_value': 2494.47, 'new_value': 2596.87}, {'field': 'onlineCount', 'old_value': 50, 'new_value': 52}]
2025-05-24 08:09:53,449 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-05-24 08:09:53,449 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 136360.14, 'new_value': 140450.99}, {'field': 'count', 'old_value': 6588, 'new_value': 6801}, {'field': 'instoreAmount', 'old_value': 72847.2, 'new_value': 75065.01}, {'field': 'instoreCount', 'old_value': 3713, 'new_value': 3831}, {'field': 'onlineAmount', 'old_value': 67428.18000000001, 'new_value': 69431.98}, {'field': 'onlineCount', 'old_value': 2875, 'new_value': 2970}]
2025-05-24 08:09:53,902 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-05-24 08:09:53,902 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 192841.67, 'new_value': 199881.29}, {'field': 'amount', 'old_value': 192833.53, 'new_value': 199871.78}, {'field': 'count', 'old_value': 3572, 'new_value': 3762}, {'field': 'instoreAmount', 'old_value': 177740.3, 'new_value': 183603.91999999998}, {'field': 'instoreCount', 'old_value': 3286, 'new_value': 3445}, {'field': 'onlineAmount', 'old_value': 15101.37, 'new_value': 16277.37}, {'field': 'onlineCount', 'old_value': 286, 'new_value': 317}]
2025-05-24 08:09:54,371 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-05-24 08:09:54,371 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27330.7, 'new_value': 30472.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3126.4}, {'field': 'amount', 'old_value': 27330.7, 'new_value': 30472.7}, {'field': 'count', 'old_value': 191, 'new_value': 198}, {'field': 'instoreAmount', 'old_value': 27330.7, 'new_value': 30472.7}, {'field': 'instoreCount', 'old_value': 191, 'new_value': 198}]
2025-05-24 08:09:54,809 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-05-24 08:09:54,809 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'amount', 'old_value': 45277.6, 'new_value': 45993.6}, {'field': 'count', 'old_value': 415, 'new_value': 423}, {'field': 'instoreAmount', 'old_value': 45498.0, 'new_value': 46214.0}, {'field': 'instoreCount', 'old_value': 415, 'new_value': 423}]
2025-05-24 08:09:55,356 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-05-24 08:09:55,356 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 193191.9, 'new_value': 203973.7}, {'field': 'dailyBillAmount', 'old_value': 193191.9, 'new_value': 203973.7}, {'field': 'amount', 'old_value': 159491.51, 'new_value': 168014.73}, {'field': 'count', 'old_value': 4434, 'new_value': 4685}, {'field': 'instoreAmount', 'old_value': 155560.92, 'new_value': 163656.74}, {'field': 'instoreCount', 'old_value': 4267, 'new_value': 4511}, {'field': 'onlineAmount', 'old_value': 6365.38, 'new_value': 6822.18}, {'field': 'onlineCount', 'old_value': 167, 'new_value': 174}]
2025-05-24 08:09:55,871 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-05-24 08:09:55,871 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48108.0, 'new_value': 49699.9}, {'field': 'dailyBillAmount', 'old_value': 48108.0, 'new_value': 49699.9}, {'field': 'amount', 'old_value': 48203.5, 'new_value': 49745.4}, {'field': 'count', 'old_value': 272, 'new_value': 286}, {'field': 'instoreAmount', 'old_value': 50719.7, 'new_value': 52261.6}, {'field': 'instoreCount', 'old_value': 269, 'new_value': 283}]
2025-05-24 08:09:56,340 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-05-24 08:09:56,340 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 63630.72, 'new_value': 67038.66}, {'field': 'dailyBillAmount', 'old_value': 63630.72, 'new_value': 67038.66}]
2025-05-24 08:09:56,699 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-05-24 08:09:56,699 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42960.81, 'new_value': 44833.76}, {'field': 'amount', 'old_value': 42960.57, 'new_value': 44833.520000000004}, {'field': 'count', 'old_value': 2505, 'new_value': 2622}, {'field': 'instoreAmount', 'old_value': 43723.88, 'new_value': 45612.11}, {'field': 'instoreCount', 'old_value': 2505, 'new_value': 2622}]
2025-05-24 08:09:57,168 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-05-24 08:09:57,168 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68706.20999999999, 'new_value': 71998.83}, {'field': 'dailyBillAmount', 'old_value': 68706.20999999999, 'new_value': 71998.83}, {'field': 'amount', 'old_value': 70658.09, 'new_value': 74130.39}, {'field': 'count', 'old_value': 3453, 'new_value': 3644}, {'field': 'instoreAmount', 'old_value': 65668.1, 'new_value': 69020.6}, {'field': 'instoreCount', 'old_value': 3239, 'new_value': 3423}, {'field': 'onlineAmount', 'old_value': 5055.24, 'new_value': 5175.04}, {'field': 'onlineCount', 'old_value': 214, 'new_value': 221}]
2025-05-24 08:09:57,574 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-05-24 08:09:57,574 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46296.52, 'new_value': 48855.86}, {'field': 'amount', 'old_value': 46296.52, 'new_value': 48855.86}, {'field': 'count', 'old_value': 2267, 'new_value': 2391}, {'field': 'instoreAmount', 'old_value': 28777.62, 'new_value': 30339.93}, {'field': 'instoreCount', 'old_value': 1493, 'new_value': 1575}, {'field': 'onlineAmount', 'old_value': 17618.55, 'new_value': 18615.579999999998}, {'field': 'onlineCount', 'old_value': 774, 'new_value': 816}]
2025-05-24 08:09:58,074 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-05-24 08:09:58,074 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33443.03, 'new_value': 35197.43}, {'field': 'dailyBillAmount', 'old_value': 33443.03, 'new_value': 35197.43}, {'field': 'amount', 'old_value': 23837.71, 'new_value': 25089.13}, {'field': 'count', 'old_value': 959, 'new_value': 1011}, {'field': 'instoreAmount', 'old_value': 24092.75, 'new_value': 25344.17}, {'field': 'instoreCount', 'old_value': 959, 'new_value': 1011}]
2025-05-24 08:09:58,527 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-05-24 08:09:58,527 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 62877.21, 'new_value': 64896.14}, {'field': 'amount', 'old_value': 62870.51, 'new_value': 64889.44}, {'field': 'count', 'old_value': 3773, 'new_value': 3885}, {'field': 'instoreAmount', 'old_value': 16606.03, 'new_value': 17086.53}, {'field': 'instoreCount', 'old_value': 986, 'new_value': 1004}, {'field': 'onlineAmount', 'old_value': 48128.51, 'new_value': 49696.94}, {'field': 'onlineCount', 'old_value': 2787, 'new_value': 2881}]
2025-05-24 08:09:59,059 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-05-24 08:09:59,059 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 118711.27, 'new_value': 123529.77}, {'field': 'dailyBillAmount', 'old_value': 118711.27, 'new_value': 123529.77}, {'field': 'amount', 'old_value': 98782.86, 'new_value': 102852.86}, {'field': 'count', 'old_value': 957, 'new_value': 994}, {'field': 'instoreAmount', 'old_value': 98782.86, 'new_value': 102852.86}, {'field': 'instoreCount', 'old_value': 957, 'new_value': 994}]
2025-05-24 08:09:59,496 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-05-24 08:09:59,496 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 89148.15, 'new_value': 94361.15}, {'field': 'dailyBillAmount', 'old_value': 89148.15, 'new_value': 94361.15}, {'field': 'amount', 'old_value': 100134.8, 'new_value': 106489.8}, {'field': 'count', 'old_value': 434, 'new_value': 461}, {'field': 'instoreAmount', 'old_value': 100134.8, 'new_value': 106489.8}, {'field': 'instoreCount', 'old_value': 434, 'new_value': 461}]
2025-05-24 08:09:59,981 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-05-24 08:09:59,981 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56886.7, 'new_value': 59926.6}, {'field': 'dailyBillAmount', 'old_value': 56886.7, 'new_value': 59926.6}, {'field': 'amount', 'old_value': 47736.65, 'new_value': 50776.55}, {'field': 'count', 'old_value': 256, 'new_value': 273}, {'field': 'instoreAmount', 'old_value': 49173.65, 'new_value': 52213.55}, {'field': 'instoreCount', 'old_value': 256, 'new_value': 273}]
2025-05-24 08:10:00,402 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-05-24 08:10:00,402 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 116194.0, 'new_value': 121302.0}, {'field': 'amount', 'old_value': 116194.0, 'new_value': 121302.0}, {'field': 'count', 'old_value': 1193, 'new_value': 1250}, {'field': 'instoreAmount', 'old_value': 116194.0, 'new_value': 121302.0}, {'field': 'instoreCount', 'old_value': 1193, 'new_value': 1250}]
2025-05-24 08:10:00,856 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-05-24 08:10:00,856 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29269.68, 'new_value': 30631.82}, {'field': 'dailyBillAmount', 'old_value': 29269.68, 'new_value': 30631.82}, {'field': 'amount', 'old_value': 4095.44, 'new_value': 4317.29}, {'field': 'count', 'old_value': 174, 'new_value': 184}, {'field': 'instoreAmount', 'old_value': 4559.32, 'new_value': 4782.0199999999995}, {'field': 'instoreCount', 'old_value': 174, 'new_value': 184}]
2025-05-24 08:10:01,371 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMZ7
2025-05-24 08:10:01,371 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16HSQFKC90AM6QNN0HOT12RK0013M7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20251.66, 'new_value': 21630.7}, {'field': 'dailyBillAmount', 'old_value': 20251.66, 'new_value': 21630.7}, {'field': 'amount', 'old_value': 20916.43, 'new_value': 22323.85}, {'field': 'count', 'old_value': 566, 'new_value': 603}, {'field': 'instoreAmount', 'old_value': 20929.79, 'new_value': 22308.83}, {'field': 'instoreCount', 'old_value': 564, 'new_value': 600}, {'field': 'onlineAmount', 'old_value': 55.16, 'new_value': 83.53999999999999}, {'field': 'onlineCount', 'old_value': 2, 'new_value': 3}]
2025-05-24 08:10:01,840 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-05-24 08:10:01,840 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 39334.6, 'new_value': 41357.1}, {'field': 'dailyBillAmount', 'old_value': 39334.6, 'new_value': 41357.1}, {'field': 'amount', 'old_value': 59674.6, 'new_value': 62671.6}, {'field': 'count', 'old_value': 237, 'new_value': 252}, {'field': 'instoreAmount', 'old_value': 59863.6, 'new_value': 62860.6}, {'field': 'instoreCount', 'old_value': 236, 'new_value': 251}]
2025-05-24 08:10:02,309 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-05-24 08:10:02,309 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'amount', 'old_value': 41277.0, 'new_value': 42708.0}, {'field': 'count', 'old_value': 224, 'new_value': 230}, {'field': 'instoreAmount', 'old_value': 41291.0, 'new_value': 42722.0}, {'field': 'instoreCount', 'old_value': 224, 'new_value': 230}]
2025-05-24 08:10:02,777 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-05-24 08:10:02,777 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 67715.29, 'new_value': 70738.84}, {'field': 'dailyBillAmount', 'old_value': 67715.29, 'new_value': 70738.84}, {'field': 'amount', 'old_value': 59980.28, 'new_value': 62936.04}, {'field': 'count', 'old_value': 2027, 'new_value': 2124}, {'field': 'instoreAmount', 'old_value': 54648.92, 'new_value': 57409.479999999996}, {'field': 'instoreCount', 'old_value': 1778, 'new_value': 1865}, {'field': 'onlineAmount', 'old_value': 5367.8, 'new_value': 5563.0}, {'field': 'onlineCount', 'old_value': 249, 'new_value': 259}]
2025-05-24 08:10:03,246 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-05-24 08:10:03,246 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 36877.78, 'new_value': 40080.03}, {'field': 'dailyBillAmount', 'old_value': 36877.78, 'new_value': 40080.03}, {'field': 'amount', 'old_value': 41280.3, 'new_value': 44525.1}, {'field': 'count', 'old_value': 265, 'new_value': 286}, {'field': 'instoreAmount', 'old_value': 40549.01, 'new_value': 43642.21}, {'field': 'instoreCount', 'old_value': 229, 'new_value': 245}, {'field': 'onlineAmount', 'old_value': 881.89, 'new_value': 1033.49}, {'field': 'onlineCount', 'old_value': 36, 'new_value': 41}]
2025-05-24 08:10:03,730 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-05-24 08:10:03,730 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 186611.74, 'new_value': 198127.58}, {'field': 'dailyBillAmount', 'old_value': 186611.74, 'new_value': 198127.58}, {'field': 'amount', 'old_value': 193802.6, 'new_value': 205443.3}, {'field': 'count', 'old_value': 1279, 'new_value': 1335}, {'field': 'instoreAmount', 'old_value': 187343.7, 'new_value': 198623.7}, {'field': 'instoreCount', 'old_value': 1142, 'new_value': 1191}, {'field': 'onlineAmount', 'old_value': 9043.9, 'new_value': 9686.6}, {'field': 'onlineCount', 'old_value': 137, 'new_value': 144}]
2025-05-24 08:10:03,730 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-24 08:10:03,730 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-24 08:10:03,730 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-24 08:10:03,730 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-24 08:10:03,730 - INFO - 正在批量插入月度数据，批次 1/1，共 4 条记录
2025-05-24 08:10:03,887 - INFO - 批量插入月度数据成功，批次 1，4 条记录
2025-05-24 08:10:06,902 - INFO - 批量插入月度数据完成: 总计 4 条，成功 4 条，失败 0 条
2025-05-24 08:10:06,902 - INFO - 批量插入月销售数据完成，共 4 条记录
2025-05-24 08:10:06,902 - INFO - 月销售数据同步完成！更新: 205 条，插入: 4 条，错误: 0 条，跳过: 983 条
2025-05-24 08:10:06,902 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-6 至 2025-5
2025-05-24 08:10:07,418 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250524.xlsx
2025-05-24 08:10:07,418 - INFO - 综合数据同步流程完成！
2025-05-24 08:10:07,480 - INFO - 综合数据同步完成
2025-05-24 08:10:07,480 - INFO - ==================================================
2025-05-24 08:10:07,480 - INFO - 程序退出
2025-05-24 08:10:07,480 - INFO - ==================================================
