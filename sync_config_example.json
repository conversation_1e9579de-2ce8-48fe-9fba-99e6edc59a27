{"db_config": {"host": "**************", "port": 3306, "user": "c_hxp_ro_prod", "password": "xm9P06O7ezGi6PZt", "database": "yx_business", "charset": "utf8mb4", "cursorclass": "pymysql.cursors.DictCursor"}, "yida_config": {"APP_TYPE": "APP_D7E6ZB94ZUL5Q1GUAOLD", "SYSTEM_TOKEN": "BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2", "USER_ID": "hex<PERSON><PERSON>g", "LANGUAGE": "zh_CN", "FORM_UUID": "FORM-FB065F60E7C04670B0D6A745813391DAMRO5"}, "sql_query": "SELECT a.project_code, b.name as project_name, a.store_code, c.name as store_name, DATE_FORMAT(a.sales_time, '%Y-%m') as sales_date, SUM(a.online_amount) as online_amount, SUM(a.offline_amount) as offline_amount, SUM(a.total_amount) as total_amount, SUM(order_count) as order_count FROM yx_b_sales_record a JOIN yx_b_projects b ON a.project_code = b.code JOIN yx_b_tenants c ON a.store_code = c.code WHERE a.status = 2 AND a.deleted = 0 AND c.deleted = 0 and sales_time>='2025-01-01' GROUP BY a.project_code, b.name, a.store_code, c.name, DATE_FORMAT(a.sales_time, '%Y-%m');", "field_mapping": {"project_code": "textField_m9tojheo", "project_name": "textField_m9tojhep", "store_code": "textField_m9tojheq", "store_name": "textField_m9tojher", "sales_date": "dateField_m9tojheu", "online_amount": "numberField_m9tojhev", "offline_amount": "numberField_m9tojhew", "total_amount": "numberField_m9tojhex", "order_count": "numberField_m9tojhey"}, "key_fields": ["project_code", "store_code", "sales_date"], "compare_fields": ["online_amount", "offline_amount", "total_amount", "order_count"], "log_config": {"level": 20, "format": "%(asctime)s - %(levelname)s - %(message)s", "encoding": "utf-8", "filename_prefix": "mysql2yida_sync"}, "batch_config": {"batch_size": 50, "delay_seconds": 1}}