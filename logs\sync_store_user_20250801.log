2025-08-01 09:00:02,913 - INFO - 数据库连接成功
2025-08-01 09:00:03,147 - INFO - 获取钉钉access_token成功
2025-08-01 09:00:03,147 - INFO - 宜搭客户端初始化成功
2025-08-01 09:00:03,147 - INFO - 正在获取数据库店铺信息...
2025-08-01 09:00:03,147 - INFO - 第一步：获取基础店铺信息...
2025-08-01 09:00:03,179 - INFO - 获取基础店铺信息成功，共 1288 条记录
2025-08-01 09:00:03,179 - INFO - 第二步：获取上个月或本月有销售记录的店铺...
2025-08-01 09:00:03,272 - INFO - 上个月或本月有销售记录的店铺数量: 618
2025-08-01 09:00:03,272 - INFO - 第三步：获取上个月以前有销售记录的店铺...
2025-08-01 09:00:03,475 - INFO - 上个月以前有销售记录的店铺数量: 609
2025-08-01 09:00:03,475 - INFO - 检测到新店铺数量: 9
2025-08-01 09:00:03,475 - INFO - 新店铺编码列表: ['330470732', '100101310', '100101318', '100101311', '100101426', '100101278', '100101169', '100101301', '100101434']
2025-08-01 09:00:03,491 - INFO - 成功获取数据库店铺信息，共 1288 条记录
2025-08-01 09:00:03,491 - INFO - 正在获取宜搭店铺信息...
2025-08-01 09:00:04,757 - INFO - 店铺 100101437 的userid值为空
2025-08-01 09:00:04,757 - INFO - 店铺 100101438 的userid值为空
2025-08-01 09:00:04,757 - INFO - 店铺 100101436 的userid值为空
2025-08-01 09:00:04,757 - INFO - 店铺 100101434 的userid值为空
2025-08-01 09:00:04,757 - INFO - 店铺 330470732 的userid值为空
2025-08-01 09:00:05,741 - INFO - 店铺 100098598 的userid值为空
2025-08-01 09:00:05,741 - INFO - 店铺 330470728 的userid值为空
2025-08-01 09:00:05,741 - INFO - 店铺 330470726 的userid值为空
2025-08-01 09:00:05,741 - INFO - 店铺 330470737 的userid值为空
2025-08-01 09:00:05,741 - INFO - 店铺 330470730 的userid值为空
2025-08-01 09:00:05,741 - INFO - 店铺 330470731 的userid值为空
2025-08-01 09:00:05,741 - INFO - 店铺 330470729 的userid值为空
2025-08-01 09:00:05,741 - INFO - 店铺 100101275 的userid值为空
2025-08-01 09:00:06,491 - INFO - 店铺 100101431 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101314 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101309 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100100926 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101288 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101303 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101316 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101319 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101317 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101417 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101425 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101429 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101427 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101428 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101285 的userid值为空
2025-08-01 09:00:10,428 - INFO - 店铺 100101430 的userid值为空
2025-08-01 09:00:10,444 - INFO - 店铺 100101162 的userid值为空
2025-08-01 09:00:10,444 - INFO - 店铺 100101312 的userid值为空
2025-08-01 09:00:10,444 - INFO - 店铺 ********* 的userid值为空
2025-08-01 09:00:10,444 - INFO - 店铺 100101426 的userid值为空
2025-08-01 09:00:10,444 - INFO - 店铺 100100657 的userid值为空
2025-08-01 09:00:10,444 - INFO - 店铺 100100832 的userid值为空
2025-08-01 09:00:10,444 - INFO - 店铺 100098453 的userid值为空
2025-08-01 09:00:10,444 - INFO - 店铺 100099214 的userid值为空
2025-08-01 09:00:10,444 - INFO - 店铺 100098428 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100099748 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098437 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100085 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100110 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100135 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100854 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100842 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098300 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100048 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098427 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100071 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098274 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098440 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100091 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100113 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098303 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100140 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100851 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098289 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100839 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098260 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100070 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100089 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100112 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098273 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100852 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098486 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100840 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100101 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098296 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100130 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098355 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100849 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100129 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100845 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098477 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100033 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098295 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098250 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100850 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100050 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098237 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100083 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098276 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100108 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100094 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100117 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098474 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100134 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100858 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098343 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100846 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100829 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098398 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098284 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100049 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100106 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100133 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098249 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100092 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098297 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100099109 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100114 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098275 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098342 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100141 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100855 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098597 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100843 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098385 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100037 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100371 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100053 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100075 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098447 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100096 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098461 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100856 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100844 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100098384 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100035 的userid值为空
2025-08-01 09:00:11,132 - INFO - 店铺 100100052 的userid值为空
2025-08-01 09:00:11,819 - INFO - 店铺 100100074 的userid值为空
2025-08-01 09:00:11,819 - INFO - 店铺 100098445 的userid值为空
2025-08-01 09:00:11,819 - INFO - 店铺 100100095 的userid值为空
2025-08-01 09:00:11,819 - INFO - 店铺 100100121 的userid值为空
2025-08-01 09:00:11,819 - INFO - 店铺 100100067 的userid值为空
2025-08-01 09:00:11,819 - INFO - 店铺 100100088 的userid值为空
2025-08-01 09:00:11,819 - INFO - 店铺 100100111 的userid值为空
2025-08-01 09:00:11,819 - INFO - 店铺 100100136 的userid值为空
2025-08-01 09:00:11,819 - INFO - 店铺 100100853 的userid值为空
2025-08-01 09:00:11,819 - INFO - 店铺 100098287 的userid值为空
2025-08-01 09:00:11,819 - INFO - 店铺 100098485 的userid值为空
2025-08-01 09:00:11,819 - INFO - 店铺 100100841 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099117 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099272 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100101121 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099265 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099243 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100369 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099231 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099216 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100361 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100328 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099189 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099931 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100278 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099304 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099305 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100101122 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099289 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100101265 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100438 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099266 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100101304 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099212 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099188 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099150 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100224 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099332 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100881 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099299 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100456 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099284 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100880 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100101119 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099829 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099263 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099153 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100236 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099936 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100101120 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100447 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100865 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099264 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100875 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099186 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099824 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100264 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099147 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100217 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099930 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099346 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099323 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099297 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099259 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100964 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099187 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099984 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100265 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099928 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099148 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100894 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099983 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099796 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099298 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100101118 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099261 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100435 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099870 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099199 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099985 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100262 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099317 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100099978 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100100436 的userid值为空
2025-08-01 09:00:11,835 - INFO - 店铺 100101181 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100321 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099185 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100233 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099345 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099980 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099322 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100893 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100437 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099282 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100892 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100433 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099979 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100665 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099923 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100101183 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099197 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100241 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099176 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100098583 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100101173 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100944 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099803 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100098579 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100101153 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099804 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100359 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100344 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100818 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099863 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100347 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100667 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100891 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099853 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100890 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100342 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099861 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100670 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100341 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100887 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100481 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100886 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100099911 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100646 的userid值为空
2025-08-01 09:00:12,616 - INFO - 店铺 100100889 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100100888 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100100883 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100100922 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100100882 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100100016 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100099835 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100100885 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100099910 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100100884 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100099956 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100099303 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100100006 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100100900 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100099903 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100099283 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100100024 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100100293 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100100434 的userid值为空
2025-08-01 09:00:12,632 - INFO - 店铺 100098391 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100044 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099198 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100314 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100061 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099177 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100249 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100098436 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100848 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100833 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099294 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100453 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100199 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100098358 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099280 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100101129 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100379 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100229 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100663 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099256 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099237 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100374 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100339 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100042 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099192 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100098405 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100060 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100082 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099295 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099281 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100664 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099101 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100380 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100375 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100101243 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099226 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100101241 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099193 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100101224 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099313 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100084 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099292 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100101242 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100101256 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099277 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100918 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100101125 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100101154 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100100377 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100101156 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100099271 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100101132 的userid值为空
2025-08-01 09:00:13,335 - INFO - 店铺 100101155 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100099217 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100101283 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100099190 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100101284 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100101282 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100101287 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100101296 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100101295 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100099293 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100101308 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100100378 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100099249 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100100373 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100099218 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100100335 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100099191 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100100291 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100099171 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100100238 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100099338 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100099307 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100099290 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100099274 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100101123 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100100466 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100100368 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100100327 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100100269 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100101124 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100099291 的userid值为空
2025-08-01 09:00:13,350 - INFO - 店铺 100099275 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100376 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100099270 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100098351 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100156 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100099615 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100099905 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100098488 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100098350 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100099503 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100027 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100099613 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100004 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100154 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100099500 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100101180 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100046 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100099899 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100098424 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100098246 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100128 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100182 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100917 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100201 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100098360 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100098572 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100099995 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100098377 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100026 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100300 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100279 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100098596 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100099681 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100045 的userid值为空
2025-08-01 09:00:13,960 - INFO - 店铺 100100870 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100099588 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100098423 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100099675 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100099988 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100098258 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100177 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100162 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100198 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100099793 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100187 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100099104 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100099816 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100904 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100210 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100099499 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100148 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100175 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100099913 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100196 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100303 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100280 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100029 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100098317 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100098397 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100860 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100255 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100661 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100098589 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100152 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100099525 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100160 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100180 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100098594 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100099119 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100099987 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100185 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100098561 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100098348 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100207 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100861 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100098559 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100098362 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100150 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100258 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100099906 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100178 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100099811 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100028 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100098588 的userid值为空
2025-08-01 09:00:13,975 - INFO - 店铺 100100143 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100320 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099585 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100098247 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100166 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100098396 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099797 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100047 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100810 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100192 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100460 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100101190 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100834 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099831 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100214 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100459 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099650 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100098367 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100478 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100261 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100101200 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100020 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100465 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100101236 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100041 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100859 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100142 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100101254 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100101246 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100056 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099794 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100189 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099629 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100101292 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100266 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100101306 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100081 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100212 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100098449 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100098268 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099636 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100838 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100260 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100030 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100480 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100462 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100461 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100146 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099553 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100172 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100365 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100040 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100195 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099541 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100251 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099847 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100216 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100055 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100098369 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099555 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100098433 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099992 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100366 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100076 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100144 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099507 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100098253 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100169 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100098448 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099806 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100097 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100194 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099506 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099587 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100847 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100100215 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 100099539 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 ********* 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 ********* 的userid值为空
2025-08-01 09:00:14,553 - INFO - 店铺 ********* 的userid值为空
2025-08-01 09:00:14,569 - INFO - 店铺 ********* 的userid值为空
2025-08-01 09:00:14,569 - INFO - 获取宜搭店铺信息成功，共 1288 条记录
2025-08-01 09:00:14,569 - INFO - 成功获取宜搭店铺信息，共 1288 条记录
2025-08-01 09:00:14,569 - INFO - 正在获取用户信息并处理oa_account...
2025-08-01 09:00:14,569 - INFO - 获取用户信息成功，共 76 条记录
2025-08-01 09:00:14,850 - INFO - 需要查询的手机号数量: 41
2025-08-01 09:00:21,257 - INFO - 处理oa_account完成，缓存大小: 41
2025-08-01 09:00:21,257 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid', 'is_new_store']
2025-08-01 09:00:21,257 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id']
2025-08-01 09:00:21,257 - INFO - 开始对比数据库和宜搭数据...
2025-08-01 09:00:21,257 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid', 'is_new_store']
2025-08-01 09:00:21,257 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id']
2025-08-01 09:00:21,257 - INFO - 仅在数据库存在的记录数: 0
2025-08-01 09:00:21,257 - INFO - 仅在宜搭存在的记录数: 0
2025-08-01 09:00:21,257 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:21,319 - INFO - 店铺 ********* 存在字段差异: ['fz_store_code_diff']
2025-08-01 09:00:21,382 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:21,397 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:21,397 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:21,397 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:21,397 - INFO - 店铺 100101312 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:21,397 - INFO - 店铺 100101319 存在字段差异: ['userid_diff']
2025-08-01 09:00:21,397 - INFO - 店铺 100101319 userid差异 - 数据库: {'18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d', '197e4171b5a83afebf4e3184f3fa9e6f', '16340e95afb25e94fcd338840d78edb8'}, 宜搭: set()
2025-08-01 09:00:21,397 - INFO - 店铺 100101319 - 数据库有userid但宜搭为空
2025-08-01 09:00:21,397 - INFO - 店铺 100101436 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,397 - INFO - 店铺 100101437 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,397 - INFO - 店铺 100101438 存在字段差异: ['status_diff', 'jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,460 - INFO - 店铺 100101272 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:21,460 - INFO - 店铺 100101202 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,460 - INFO - 店铺 100099440 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,460 - INFO - 店铺 100099439 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,460 - INFO - 店铺 100099488 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,460 - INFO - 店铺 100099433 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,460 - INFO - 店铺 100099457 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,460 - INFO - 店铺 100099483 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,460 - INFO - 店铺 100099487 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,460 - INFO - 店铺 100099486 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,460 - INFO - 店铺 100099965 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,475 - INFO - 店铺 100100907 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,475 - INFO - 店铺 100099472 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,475 - INFO - 店铺 100099445 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,475 - INFO - 店铺 100099444 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,475 - INFO - 店铺 100099468 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,475 - INFO - 店铺 100101230 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,475 - INFO - 店铺 100101227 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100101269 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100101278 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100101291 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff', 'is_new_store_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100099167 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100099303 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100099272 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100099265 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100099243 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100099231 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100100361 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100099931 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100099335 存在字段差异: ['jde_customer_code_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100099304 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100101122 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100099289 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100100438 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100100924 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100099266 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100099212 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100099150 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,491 - INFO - 店铺 100100224 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099332 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100101170 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099299 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099263 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100100356 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099153 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099333 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099300 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099287 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100101176 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099264 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100100264 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099147 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099323 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100101148 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099297 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099259 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100100265 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099298 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100101149 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099261 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099181 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100100262 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099317 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100100321 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,507 - INFO - 店铺 100099146 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099345 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099322 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100101130 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099257 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100437 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100101108 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099282 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100665 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099176 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099283 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100666 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100434 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100355 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100314 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099177 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100249 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099294 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100953 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099280 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099256 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099237 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100374 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099220 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100339 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100297 存在字段差异: ['jde_customer_code_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099174 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099997 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099295 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099281 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100664 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100101107 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100375 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099226 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100100003 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,522 - INFO - 店铺 100099313 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099292 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100927 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099277 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100377 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100476 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099271 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100101128 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099293 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100949 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100378 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099249 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100373 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099218 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100335 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100291 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100238 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099338 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099307 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099290 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100925 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099274 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100368 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100327 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100269 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099339 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099312 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099275 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100467 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100926 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099270 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099247 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099234 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100369 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100099216 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100328 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,538 - INFO - 店铺 100100278 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,553 - INFO - 店铺 100101216 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,553 - INFO - 店铺 100101262 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,553 - INFO - 店铺 100101277 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,553 - INFO - 店铺 100101288 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,553 - INFO - 店铺 100101303 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,553 - INFO - 店铺 100099393 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,553 - INFO - 店铺 100101141 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,553 - INFO - 店铺 100099418 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,553 - INFO - 店铺 100099943 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,553 - INFO - 店铺 100101247 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,553 - INFO - 店铺 100101289 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:21,585 - INFO - 店铺 100101299 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:21,585 - INFO - 数据对比完成：
2025-08-01 09:00:21,585 - INFO - - 需要插入的记录数: 0
2025-08-01 09:00:21,585 - INFO - - 需要更新状态为禁用的记录数: 0
2025-08-01 09:00:21,585 - INFO - - 需要更新的记录数: 159
2025-08-01 09:00:21,585 - INFO - - 店铺名称变更数: 0
2025-08-01 09:00:21,585 - INFO - 生成差异报告...
2025-08-01 09:00:22,007 - INFO - 差异报告已保存到文件: data/sync_store/store_info_diff_report_20250801.xlsx
2025-08-01 09:00:22,007 - INFO - 开始更新宜搭表单...
2025-08-01 09:00:22,007 - INFO - 开始更新宜搭表单数据...
2025-08-01 09:00:22,007 - INFO - 数据库记录数: 1288
2025-08-01 09:00:22,007 - INFO - 宜搭记录数: 1288
2025-08-01 09:00:22,007 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid', 'is_new_store']
2025-08-01 09:00:22,007 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id']
2025-08-01 09:00:22,007 - INFO - 仅在数据库存在的记录数: 0
2025-08-01 09:00:22,007 - INFO - 仅在宜搭存在的记录数: 0
2025-08-01 09:00:22,007 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:22,038 - INFO - 店铺 ********* 存在字段差异: ['fz_store_code_diff']
2025-08-01 09:00:22,116 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:22,116 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:22,116 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:22,116 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:22,116 - INFO - 店铺 100101312 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:22,116 - INFO - 店铺 100101319 存在字段差异: ['userid_diff']
2025-08-01 09:00:22,116 - INFO - 店铺 100101319 userid差异 - 数据库: {'18063f3df478239384b92a7416a8207f', '1823db918644667677cfbe44476b7b9d', '197e4171b5a83afebf4e3184f3fa9e6f', '16340e95afb25e94fcd338840d78edb8'}, 宜搭: set()
2025-08-01 09:00:22,116 - INFO - 店铺 100101319 - 数据库有userid但宜搭为空
2025-08-01 09:00:22,116 - INFO - 店铺 100101436 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,116 - INFO - 店铺 100101437 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,116 - INFO - 店铺 100101438 存在字段差异: ['status_diff', 'jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,178 - INFO - 店铺 100101272 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:22,178 - INFO - 店铺 100101202 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,178 - INFO - 店铺 100099440 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,178 - INFO - 店铺 100099439 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,178 - INFO - 店铺 100099488 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,178 - INFO - 店铺 100099433 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,178 - INFO - 店铺 100099457 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,178 - INFO - 店铺 100099483 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,178 - INFO - 店铺 100099487 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,178 - INFO - 店铺 100099486 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,178 - INFO - 店铺 100099965 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,194 - INFO - 店铺 100100907 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,194 - INFO - 店铺 100099472 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,194 - INFO - 店铺 100099445 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,194 - INFO - 店铺 100099444 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,194 - INFO - 店铺 100099468 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,194 - INFO - 店铺 100101230 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,194 - INFO - 店铺 100101227 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100101269 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100101278 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100101291 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff', 'is_new_store_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100099167 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100099303 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100099272 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100099265 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100099243 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100099231 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100100361 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100099931 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100099335 存在字段差异: ['jde_customer_code_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100099304 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100101122 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100099289 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100100438 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,210 - INFO - 店铺 100100924 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099266 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099212 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099150 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100100224 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099332 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100101170 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099299 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099263 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100100356 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099153 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099333 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099300 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099287 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100101176 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099264 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100100264 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099147 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099323 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100101148 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099297 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099259 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100100265 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099298 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100101149 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099261 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099181 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100100262 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,225 - INFO - 店铺 100099317 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100321 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099146 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099345 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099322 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100101130 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099257 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100437 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100101108 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099282 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100665 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099176 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099283 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100666 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100434 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100355 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100314 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099177 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100249 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099294 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100953 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099280 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099256 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099237 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100374 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099220 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100339 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100297 存在字段差异: ['jde_customer_code_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099174 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099997 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099295 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099281 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100664 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100101107 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100100375 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,241 - INFO - 店铺 100099226 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100003 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099313 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099292 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100927 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099277 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100377 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100476 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099271 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100101128 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099293 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100949 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100378 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099249 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100373 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099218 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100335 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100291 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100238 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099338 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099307 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099290 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100925 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099274 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100368 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100327 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100269 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099339 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099312 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099275 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100467 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100100926 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099270 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,256 - INFO - 店铺 100099247 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100099234 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100100369 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100099216 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100100328 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100100278 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100101216 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100101262 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100101277 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100101288 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100101303 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100099393 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100101141 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100099418 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100099943 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100101247 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,272 - INFO - 店铺 100101289 存在字段差异: ['jde_customer_code_diff', 'jde_customer_name_diff']
2025-08-01 09:00:22,303 - INFO - 店铺 100101299 存在字段差异: ['is_new_store_diff']
2025-08-01 09:00:22,303 - INFO - 数据对比完成：
2025-08-01 09:00:22,303 - INFO - - 需要插入的记录数: 0
2025-08-01 09:00:22,303 - INFO - - 需要更新状态为禁用的记录数: 0
2025-08-01 09:00:22,303 - INFO - - 需要更新的记录数: 159
2025-08-01 09:00:22,303 - INFO - - 店铺名称变更数: 0
2025-08-01 09:00:22,303 - INFO - 开始处理需要更新的记录，共 159 条
2025-08-01 09:00:22,303 - INFO - 正在处理第 1 条更新记录 - store_code: *********
2025-08-01 09:00:22,303 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "港龙舞蹈", "employeeField_m8e8g3lw": ["15e62a6607d73f5229798af46ed94466", "17af00da28f0df4d6ae7f0b42838e2d6", "18f13290b6949adbfcd0691437491a5a", "1953c39472ad2995a15b085419b99165"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0049", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,303 - INFO - 正在处理第 2 条更新记录 - store_code: *********
2025-08-01 09:00:22,319 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "卡朋", "employeeField_m8e8g3lw": ["18f13290b6949adbfcd0691437491a5a", "178911c91380e3f9961bcc84847a615f", "18b0e1e4b78fd2c55dc90c74e3b9f3cc", "189f858c407c7b3d480039a4090b4c13"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103715", "textField_mb7rs39i": "广州市正茂餐饮有限公司", "textField_mbc1lbzm": "P0299L25CO0099", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,319 - INFO - 正在处理第 3 条更新记录 - store_code: *********
2025-08-01 09:00:22,319 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "宠胖胖", "employeeField_m8e8g3lw": ["195cbfaa191990374b814d747ed92fe3", "189f858c407c7b3d480039a4090b4c13", "16340e95afb25e94fcd338840d78edb8", "18b0e1e4b78fd2c55dc90c74e3b9f3cc", "178911c91380e3f9961bcc84847a615f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0042", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,319 - INFO - 正在处理第 4 条更新记录 - store_code: *********
2025-08-01 09:00:22,319 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "赵记传承", "employeeField_m8e8g3lw": ["195cbfaa191990374b814d747ed92fe3", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0048", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,319 - INFO - 正在处理第 5 条更新记录 - store_code: *********
2025-08-01 09:00:22,319 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "地道厨子", "employeeField_m8e8g3lw": ["18063f3df478239384b92a7416a8207f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d", "195cbfaa191990374b814d747ed92fe3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0051", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,319 - INFO - 正在处理第 6 条更新记录 - store_code: *********
2025-08-01 09:00:22,319 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "多经-江博士", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0062", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,319 - INFO - 正在处理第 7 条更新记录 - store_code: 100101312
2025-08-01 09:00:22,319 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101312", "textField_m8e8g3lu": "多经-中庭活动", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0061", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,319 - INFO - 正在处理第 8 条更新记录 - store_code: 100101319
2025-08-01 09:00:22,319 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101319", "textField_m8e8g3lu": "潮弹社", "employeeField_m8e8g3lw": ["197e4171b5a83afebf4e3184f3fa9e6f", "16340e95afb25e94fcd338840d78edb8", "1823db918644667677cfbe44476b7b9d", "18063f3df478239384b92a7416a8207f"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0074", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,319 - INFO - 正在处理第 9 条更新记录 - store_code: 100101436
2025-08-01 09:00:22,319 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101436", "textField_m8e8g3lu": "张仔纪", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0097", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,319 - INFO - 正在处理第 10 条更新记录 - store_code: 100101437
2025-08-01 09:00:22,319 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101437", "textField_m8e8g3lu": "潮玩弹珠社", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0098", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,319 - INFO - 正在处理第 11 条更新记录 - store_code: 100101438
2025-08-01 09:00:22,319 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "100101438", "textField_m8e8g3lu": "卡朋西餐", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0099", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,319 - INFO - 正在处理第 12 条更新记录 - store_code: 100101272
2025-08-01 09:00:22,319 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉国金天地", "textField_m911r3pn": "100101272", "textField_m8e8g3lu": "满江红教育", "employeeField_m8e8g3lw": ["196e80b2a411363583cdcd84553878d3", "189fd3054a18e03a44b872b495795896", "18acf80e2a353b81652ebdc4329bdcc4", "1835384a550d8f5bfad8b704c9e963f7", "18353820b37845b95ed69864fd58b885", "1847a0e687fd986b4b870904dea8430d", "16d2416ce06396ed3f55a964f9fa0c71", "18b6ef624129a1e2c9b51774b6e8ce8c"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0003L25CO0013", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,319 - INFO - 正在处理第 13 条更新记录 - store_code: 100101202
2025-08-01 09:00:22,319 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100101202", "textField_m8e8g3lu": "乐苏", "employeeField_m8e8g3lw": ["1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3", "17f90b6e5dd13dcb6e88a1f4ff1951b5"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "128016", "textField_mb7rs39i": "广州市乐苏餐饮有限公司", "textField_mbc1lbzm": "10701L24CO0028", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,319 - INFO - 正在处理第 14 条更新记录 - store_code: 100099440
2025-08-01 09:00:22,335 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100099440", "textField_m8e8g3lu": "芭曲发廊", "employeeField_m8e8g3lw": ["1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3", "17f90b6e5dd13dcb6e88a1f4ff1951b5"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "124362", "textField_mb7rs39i": "林达琼", "textField_mbc1lbzm": "10701L21CO0009", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,335 - INFO - 正在处理第 15 条更新记录 - store_code: 100099439
2025-08-01 09:00:22,335 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100099439", "textField_m8e8g3lu": "湘颂", "employeeField_m8e8g3lw": ["1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3", "17f90b6e5dd13dcb6e88a1f4ff1951b5"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "124631", "textField_mb7rs39i": "广州市湘颂五号餐饮有限公司", "textField_mbc1lbzm": "10701L24CO0018", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,335 - INFO - 正在处理第 16 条更新记录 - store_code: 100099488
2025-08-01 09:00:22,335 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100099488", "textField_m8e8g3lu": "资生堂", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "125781", "textField_mb7rs39i": "广州市信诺企业管理服务有限责任公司", "textField_mbc1lbzm": "10701L22CO0009", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,335 - INFO - 正在处理第 17 条更新记录 - store_code: 100099433
2025-08-01 09:00:22,335 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100099433", "textField_m8e8g3lu": "暖森", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "125745", "textField_mb7rs39i": "广州市天河区暖森轻疗养发馆", "textField_mbc1lbzm": "10701L22CO0005", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,335 - INFO - 正在处理第 18 条更新记录 - store_code: 100099457
2025-08-01 09:00:22,335 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100099457", "textField_m8e8g3lu": "悦碧施", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "127795", "textField_mb7rs39i": "广州瑞紫美容有限公司", "textField_mbc1lbzm": "10701L24CO0002", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,335 - INFO - 正在处理第 19 条更新记录 - store_code: 100099483
2025-08-01 09:00:22,335 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100099483", "textField_m8e8g3lu": "LAVAZZA", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "124644", "textField_mb7rs39i": "意百咖啡（上海）有限公司广东分公司", "textField_mbc1lbzm": "10701L21CO0008", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,335 - INFO - 正在处理第 20 条更新记录 - store_code: 100099487
2025-08-01 09:00:22,335 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100099487", "textField_m8e8g3lu": "ABC首创分段式石烧牛扒", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "125339", "textField_mb7rs39i": "颐潋朵餐饮管理（广州）有限公司", "textField_mbc1lbzm": "10701L21CO0041", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,335 - INFO - 正在处理第 21 条更新记录 - store_code: 100099486
2025-08-01 09:00:22,335 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100099486", "textField_m8e8g3lu": "威尔仕健身", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "120696", "textField_mb7rs39i": "威康健身管理咨询（广州）有限公司", "textField_mbc1lbzm": "10701L21CO0078", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,335 - INFO - 正在处理第 22 条更新记录 - store_code: 100099965
2025-08-01 09:00:22,335 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100099965", "textField_m8e8g3lu": "曼谷5巷", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "126877", "textField_mb7rs39i": "广州市五巷餐饮有限公司", "textField_mbc1lbzm": "10701L23CO0018", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,335 - INFO - 正在处理第 23 条更新记录 - store_code: 100100907
2025-08-01 09:00:22,335 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100100907", "textField_m8e8g3lu": "新氧·新氧优享", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "127964", "textField_mb7rs39i": "广州市心漾悦己医疗美容诊所有限公司", "textField_mbc1lbzm": "10701L24CO0017", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,335 - INFO - 正在处理第 24 条更新记录 - store_code: 100099472
2025-08-01 09:00:22,335 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100099472", "textField_m8e8g3lu": "清心堂草本茶", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "124699", "textField_mb7rs39i": "广东清心堂草本餐饮管理有限公司", "textField_mbc1lbzm": "10701L21CO0004", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,335 - INFO - 正在处理第 25 条更新记录 - store_code: 100099445
2025-08-01 09:00:22,335 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100099445", "textField_m8e8g3lu": "极氪ZEEKR", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "128176", "textField_mb7rs39i": "广州极氪汽车销售服务有限公司", "textField_mbc1lbzm": "10701L25CO0011", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,335 - INFO - 正在处理第 26 条更新记录 - store_code: 100099444
2025-08-01 09:00:22,335 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100099444", "textField_m8e8g3lu": "梅赛德斯-AMG", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "124194", "textField_mb7rs39i": "广东鸿粤汽车销售集团有限公司", "textField_mbc1lbzm": "10701L22CO0019", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,350 - INFO - 正在处理第 27 条更新记录 - store_code: 100099468
2025-08-01 09:00:22,350 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100099468", "textField_m8e8g3lu": "MARTELL", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "124547", "textField_mb7rs39i": "创享欢聚（上海）食品销售有限公司", "textField_mbc1lbzm": "10701L22CO0022", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,350 - INFO - 正在处理第 28 条更新记录 - store_code: 100101230
2025-08-01 09:00:22,350 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100101230", "textField_m8e8g3lu": "星聚会", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "128305", "textField_mb7rs39i": "广州天河星会文化娱乐有限公司", "textField_mbc1lbzm": "10701L25CO0012", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,350 - INFO - 正在处理第 29 条更新记录 - store_code: 100101227
2025-08-01 09:00:22,350 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州环贸中心", "textField_m911r3pn": "100101227", "textField_m8e8g3lu": "超级猩猩", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "128132", "textField_mb7rs39i": "广州猩火健身管理有限公司", "textField_mbc1lbzm": "10701L25CO0007", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,350 - INFO - 正在处理第 30 条更新记录 - store_code: 100101269
2025-08-01 09:00:22,350 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "100101269", "textField_m8e8g3lu": "怀鮨·SUSHI", "employeeField_m8e8g3lw": ["192ae1b7a30d7d7319ab66d444aa66f7"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108712", "textField_mb7rs39i": "翁明怀", "textField_mbc1lbzm": "10164L25CO0016", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,350 - INFO - 正在处理第 31 条更新记录 - store_code: 100101278
2025-08-01 09:00:22,350 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "100101278", "textField_m8e8g3lu": "一年四季24小时智能健身", "employeeField_m8e8g3lw": ["16d2416c64282711fb71a184c10997bb"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108843", "textField_mb7rs39i": "逸年四季国金(广州)运动管理有限公司", "textField_mbc1lbzm": "10164L25CO0023", "textField_m9jkl9nx": "是"}
2025-08-01 09:00:22,350 - INFO - 正在处理第 32 条更新记录 - store_code: 100101291
2025-08-01 09:00:22,350 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "100101291", "textField_m8e8g3lu": "常乐对症推拿", "employeeField_m8e8g3lw": ["16d2416c64282711fb71a184c10997bb"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108791", "textField_mb7rs39i": "广州常知金乐健康管理有限公司", "textField_mbc1lbzm": "10164L25CO0021", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,350 - INFO - 正在处理第 33 条更新记录 - store_code: 100099167
2025-08-01 09:00:22,350 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099167", "textField_m8e8g3lu": "阿迪达斯", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110603", "textField_mb7rs39i": "阿迪达斯体育用品（上海）有限公司", "textField_mbc1lbzm": "10295L24CO0001", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,350 - INFO - 正在处理第 34 条更新记录 - store_code: 100099303
2025-08-01 09:00:22,350 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099303", "textField_m8e8g3lu": "有间拾味", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103920", "textField_mb7rs39i": "丁祥", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,350 - INFO - 正在处理第 35 条更新记录 - store_code: 100099272
2025-08-01 09:00:22,350 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099272", "textField_m8e8g3lu": "荷叶", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109273", "textField_mb7rs39i": "林栩生", "textField_mbc1lbzm": "10295L22CO0063", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,350 - INFO - 正在处理第 36 条更新记录 - store_code: 100099265
2025-08-01 09:00:22,350 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099265", "textField_m8e8g3lu": "孔一凡饺子", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109401", "textField_mb7rs39i": "韩坤伟", "textField_mbc1lbzm": "10295L22CO0070", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,350 - INFO - 正在处理第 37 条更新记录 - store_code: 100099243
2025-08-01 09:00:22,350 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099243", "textField_m8e8g3lu": "新东方", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103960", "textField_mb7rs39i": "佛山市新东方培训学校", "textField_mbc1lbzm": "10295L20CO0023", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,350 - INFO - 正在处理第 38 条更新记录 - store_code: 100099231
2025-08-01 09:00:22,350 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099231", "textField_m8e8g3lu": "琴言筝语·音乐窝", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103927", "textField_mb7rs39i": "佛山市琴言筝语文化传播有限公司", "textField_mbc1lbzm": "10295L22CO0074", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,366 - INFO - 正在处理第 39 条更新记录 - store_code: 100100361
2025-08-01 09:00:22,366 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100361", "textField_m8e8g3lu": "格莱迈", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110925", "textField_mb7rs39i": "胡伟舜", "textField_mbc1lbzm": "10295L23CO0074", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,366 - INFO - 正在处理第 40 条更新记录 - store_code: 100099931
2025-08-01 09:00:22,366 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099931", "textField_m8e8g3lu": "本来不该有咖啡", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110255", "textField_mb7rs39i": "樊志萍", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,366 - INFO - 正在处理第 41 条更新记录 - store_code: 100099335
2025-08-01 09:00:22,366 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099335", "textField_m8e8g3lu": "浓心爷爷烘焙培训", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109293", "textField_mb7rs39i": "佛山市南海区浓泰盈面包店", "textField_mbc1lbzm": "10295L23CO0019", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,366 - INFO - 正在处理第 42 条更新记录 - store_code: 100099304
2025-08-01 09:00:22,366 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099304", "textField_m8e8g3lu": "聚膳康餐饮", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109887", "textField_mb7rs39i": "佛山市聚膳康餐饮管理有限公司", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,366 - INFO - 正在处理第 43 条更新记录 - store_code: 100101122
2025-08-01 09:00:22,366 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101122", "textField_m8e8g3lu": "美宜佳", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111310", "textField_mb7rs39i": "黄连城", "textField_mbc1lbzm": "10295L24CO0092", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,366 - INFO - 正在处理第 44 条更新记录 - store_code: 100099289
2025-08-01 09:00:22,366 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099289", "textField_m8e8g3lu": "漂移车主题餐厅", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108932", "textField_mb7rs39i": "李恩平", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,366 - INFO - 正在处理第 45 条更新记录 - store_code: 100100438
2025-08-01 09:00:22,366 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100438", "textField_m8e8g3lu": "米格斯", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111002", "textField_mb7rs39i": "广州一半文化创意有限公司", "textField_mbc1lbzm": "10295L23CO0096", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,366 - INFO - 正在处理第 46 条更新记录 - store_code: 100100924
2025-08-01 09:00:22,366 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100924", "textField_m8e8g3lu": "鹤·健身工作室", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111326", "textField_mb7rs39i": "谌壮", "textField_mbc1lbzm": "10295L24CO0029", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,366 - INFO - 正在处理第 47 条更新记录 - store_code: 100099266
2025-08-01 09:00:22,366 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099266", "textField_m8e8g3lu": "利港城茶餐厅", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103916", "textField_mb7rs39i": "佛山市港港餐饮管理有限公司", "textField_mbc1lbzm": "10295L20CO0133", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,366 - INFO - 正在处理第 48 条更新记录 - store_code: 100099212
2025-08-01 09:00:22,366 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099212", "textField_m8e8g3lu": "喇沙王", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104004", "textField_mb7rs39i": "佛山市南海区喇沙王美食店", "textField_mbc1lbzm": "10295L20CO0052", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,366 - INFO - 正在处理第 49 条更新记录 - store_code: 100099150
2025-08-01 09:00:22,366 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099150", "textField_m8e8g3lu": "乐摩吧", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109925", "textField_mb7rs39i": "乐摩物联科技（广州）有限公司", "textField_mbc1lbzm": "10295L20CO0119", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,366 - INFO - 正在处理第 50 条更新记录 - store_code: 100100224
2025-08-01 09:00:22,366 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100224", "textField_m8e8g3lu": "都市丽人", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110578", "textField_mb7rs39i": "洪栋梁", "textField_mbc1lbzm": "10295L23CO0026", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:22,366 - INFO - 达到批量处理大小，开始批量更新 50 条记录
2025-08-01 09:00:22,944 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:23,756 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:24,319 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:24,897 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:25,460 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:26,116 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:26,710 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:27,381 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:27,991 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:28,631 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:29,194 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:29,835 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:30,647 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:31,241 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:31,866 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:32,444 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:33,053 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:33,647 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:34,272 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:34,897 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:35,616 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:36,256 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:36,913 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:37,585 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:38,210 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:38,975 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:39,631 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:40,225 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:40,819 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:41,444 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:42,053 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:42,600 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:43,288 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:43,850 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:44,522 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:45,210 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:45,772 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:46,397 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:46,913 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:47,600 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:48,225 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:48,850 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:49,397 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:49,866 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:50,444 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:51,022 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:51,600 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:52,225 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:52,881 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:53,459 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:53,459 - INFO - 批量更新成功，form_instance_ids: ['FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMXA', 'FINST-3PF66V71E5VVUFXD6URWS5AZCEAX2MKHADCBM28', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBM1K', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBM8K', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBM9K', 'FINST-SED66Q616Q5WC73DCNCVYBW8L49W3N7NTZPBMK1', 'FINST-SED66Q616Q5WC73DCNCVYBW8L49W3N7NTZPBML1', 'FINST-W4G66DA1OPRWWVJLEASLBC1RPUO23ULNMTJCM6', 'FINST-VOC66Y91AKJXP32KDFQMT4FRX4R63HEIYTNDMVA', 'FINST-VOC66Y91AKJXP32KDFQMT4FRX4R63HEIYTNDMWA', 'FINST-BTF66DB1C7GXDK8N8PW6RDR2DBGA25X7UOQDMYP', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573G0LADCBM44', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBMBK', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBMGK', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBMHK', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBMKK', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBMNK', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBMPK', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBMTK', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBMUK', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBMWK', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBM0L', 'FINST-1PF66KA1V5SVCUW5F832R5V6WFYJ211JADCBMP11', 'FINST-1PF66KA1V5SVCUW5F832R5V6WFYJ211JADCBM321', 'FINST-1PF66KA1V5SVCUW5F832R5V6WFYJ211JADCBM421', 'FINST-1PF66KA1V5SVCUW5F832R5V6WFYJ211JADCBM621', 'FINST-1PF66KA1V5SVCUW5F832R5V6WFYJ211JADCBMB21', 'FINST-1PF66KA1V5SVCUW5F832R5V6WFYJ211JADCBMI21', 'FINST-1PF66KA1V5SVCUW5F832R5V6WFYJ211JADCBMJ21', 'FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMKA', 'FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMLA', 'FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMMA', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMB3', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBME3', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2GDJADCBMM6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2GDJADCBMO6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2GDJADCBMP6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2GDJADCBMQ6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2GDJADCBMR6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2GDJADCBMT6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2GDJADCBMU6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMV6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMW6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMX6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMZ6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBM07', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBM17', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBM57', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBM87', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBM97']
2025-08-01 09:00:53,459 - INFO - 正在处理第 1 条更新记录 - store_code: 100099332
2025-08-01 09:00:53,459 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099332", "textField_m8e8g3lu": "小肥羊", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103956", "textField_mb7rs39i": "卢观音", "textField_mbc1lbzm": "10295L20CO0096", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,459 - INFO - 正在处理第 2 条更新记录 - store_code: 100101170
2025-08-01 09:00:53,459 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101170", "textField_m8e8g3lu": "陈家生煎", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110086", "textField_mb7rs39i": "吴剑华", "textField_mbc1lbzm": "10295L24CO0113", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,459 - INFO - 正在处理第 3 条更新记录 - store_code: 100099299
2025-08-01 09:00:53,459 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099299", "textField_m8e8g3lu": "京东之家悦舍", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103792", "textField_mb7rs39i": "广州悦舍商业发展有限公司", "textField_mbc1lbzm": "10295L23CO0083", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,475 - INFO - 正在处理第 4 条更新记录 - store_code: 100099263
2025-08-01 09:00:53,475 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099263", "textField_m8e8g3lu": "三人行书法", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108983", "textField_mb7rs39i": "佛山华唐书法艺术有限公司", "textField_mbc1lbzm": "10295L22CO0094", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,475 - INFO - 正在处理第 5 条更新记录 - store_code: 100100356
2025-08-01 09:00:53,475 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100356", "textField_m8e8g3lu": "NUMERO TEA", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103912", "textField_mb7rs39i": "高敏", "textField_mbc1lbzm": "10295L23CO0070", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,475 - INFO - 正在处理第 6 条更新记录 - store_code: 100099153
2025-08-01 09:00:53,475 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099153", "textField_m8e8g3lu": "嘻爪", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109132", "textField_mb7rs39i": "温贵平", "textField_mbc1lbzm": "10295L22CO0068", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,475 - INFO - 正在处理第 7 条更新记录 - store_code: 100099333
2025-08-01 09:00:53,475 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099333", "textField_m8e8g3lu": "挺惠买", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111469", "textField_mb7rs39i": "徐俊辽", "textField_mbc1lbzm": "10295L24CO0111", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,475 - INFO - 正在处理第 8 条更新记录 - store_code: 100099300
2025-08-01 09:00:53,475 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099300", "textField_m8e8g3lu": "学乐英语", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111689", "textField_mb7rs39i": "佛山市南海区利和学艺术培训有限公司", "textField_mbc1lbzm": "10295L25CO0019", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,475 - INFO - 正在处理第 9 条更新记录 - store_code: 100099287
2025-08-01 09:00:53,475 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099287", "textField_m8e8g3lu": "一面馆", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110845", "textField_mb7rs39i": "蔡贤纪", "textField_mbc1lbzm": "10295L24CO0114", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,475 - INFO - 正在处理第 10 条更新记录 - store_code: 100101176
2025-08-01 09:00:53,475 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101176", "textField_m8e8g3lu": "小米", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111487", "textField_mb7rs39i": "广州智仕玛电子科技有限公司", "textField_mbc1lbzm": "10295L24CO0115", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,475 - INFO - 正在处理第 11 条更新记录 - store_code: 100099264
2025-08-01 09:00:53,475 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099264", "textField_m8e8g3lu": "迪如芳菲", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103984", "textField_mb7rs39i": "李婷婷", "textField_mbc1lbzm": "10295L20CO0206", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,475 - INFO - 正在处理第 12 条更新记录 - store_code: 100100264
2025-08-01 09:00:53,475 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100264", "textField_m8e8g3lu": "宠贝贝萌宠乐园", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110558", "textField_mb7rs39i": "广州宠贝贝科技有限公司", "textField_mbc1lbzm": "10295L23CO0038", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,475 - INFO - 正在处理第 13 条更新记录 - store_code: 100099147
2025-08-01 09:00:53,475 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099147", "textField_m8e8g3lu": "公仔机", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104020", "textField_mb7rs39i": "刘丽芳", "textField_mbc1lbzm": "10295L20CO0055", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,475 - INFO - 正在处理第 14 条更新记录 - store_code: 100099323
2025-08-01 09:00:53,475 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099323", "textField_m8e8g3lu": "东方证券", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103981", "textField_mb7rs39i": "东方证券股份有限公司佛山南海大道北证券营业部", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,475 - INFO - 正在处理第 15 条更新记录 - store_code: 100101148
2025-08-01 09:00:53,475 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101148", "textField_m8e8g3lu": "郑汉叉烧", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111464", "textField_mb7rs39i": "梁志鹏", "textField_mbc1lbzm": "10295L24CO0109", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,491 - INFO - 正在处理第 16 条更新记录 - store_code: 100099297
2025-08-01 09:00:53,491 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099297", "textField_m8e8g3lu": "天行", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103992", "textField_mb7rs39i": "许建芳", "textField_mbc1lbzm": "10295L20CO0213", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,491 - INFO - 正在处理第 17 条更新记录 - store_code: 100099259
2025-08-01 09:00:53,491 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099259", "textField_m8e8g3lu": "唯她女装", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103989", "textField_mb7rs39i": "肖英英", "textField_mbc1lbzm": "10295L20CO0208", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,491 - INFO - 正在处理第 18 条更新记录 - store_code: 100100265
2025-08-01 09:00:53,491 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100265", "textField_m8e8g3lu": "潮流龙卷风", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110414", "textField_mb7rs39i": "李齐磊", "textField_mbc1lbzm": "10295L23CO0039", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,491 - INFO - 正在处理第 19 条更新记录 - store_code: 100099298
2025-08-01 09:00:53,491 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099298", "textField_m8e8g3lu": "Elf-land 艾芙岚 Minisiam·精致暹罗", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109121", "textField_mb7rs39i": "佛山市艾芙岚文化传播有限公司", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,491 - INFO - 正在处理第 20 条更新记录 - store_code: 100101149
2025-08-01 09:00:53,491 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101149", "textField_m8e8g3lu": "泓洋西服高级定制", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111479", "textField_mb7rs39i": "佛山市黑鹰服饰有限公司", "textField_mbc1lbzm": "10295L24CO0110", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,491 - INFO - 正在处理第 21 条更新记录 - store_code: 100099261
2025-08-01 09:00:53,491 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099261", "textField_m8e8g3lu": "乐鲸灵", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103925", "textField_mb7rs39i": "佛山市澳辰教育软件科技有限公司", "textField_mbc1lbzm": "10295L20CO0079", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,491 - INFO - 正在处理第 22 条更新记录 - store_code: 100099181
2025-08-01 09:00:53,491 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099181", "textField_m8e8g3lu": "好佰年口腔", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103942", "textField_mb7rs39i": "佛山市南海好佰年粤秀口腔门诊部有限公司", "textField_mbc1lbzm": "10295L21CO0045", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,491 - INFO - 正在处理第 23 条更新记录 - store_code: 100100262
2025-08-01 09:00:53,491 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100262", "textField_m8e8g3lu": "酷豆家族", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110696", "textField_mb7rs39i": "李志勇", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,491 - INFO - 正在处理第 24 条更新记录 - store_code: 100099317
2025-08-01 09:00:53,491 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099317", "textField_m8e8g3lu": "H2密室逃脱", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103943", "textField_mb7rs39i": "何诗敏", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,491 - INFO - 正在处理第 25 条更新记录 - store_code: 100100321
2025-08-01 09:00:53,491 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100321", "textField_m8e8g3lu": "周大福", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110733", "textField_mb7rs39i": "佛山市顺德区周大福珠宝首饰有限公司", "textField_mbc1lbzm": "10295L23CO0056", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,491 - INFO - 正在处理第 26 条更新记录 - store_code: 100099146
2025-08-01 09:00:53,491 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099146", "textField_m8e8g3lu": "天音科技", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103919", "textField_mb7rs39i": "深圳市天音科技发展有限公司佛山南海分公司", "textField_mbc1lbzm": "10295L24CO0118", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,491 - INFO - 正在处理第 27 条更新记录 - store_code: 100099345
2025-08-01 09:00:53,506 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099345", "textField_m8e8g3lu": "佰思薇美甲", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103953", "textField_mb7rs39i": "罗秀珍", "textField_mbc1lbzm": "10295L20CO0026", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,506 - INFO - 正在处理第 28 条更新记录 - store_code: 100099322
2025-08-01 09:00:53,506 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099322", "textField_m8e8g3lu": "卅子", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103945", "textField_mb7rs39i": "佛山市川萃餐饮管理有限公司", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,506 - INFO - 正在处理第 29 条更新记录 - store_code: 100101130
2025-08-01 09:00:53,506 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101130", "textField_m8e8g3lu": "大可以", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111535", "textField_mb7rs39i": "佛山市南海区粤汇大可以餐饮店(个体工商户)", "textField_mbc1lbzm": "10295L24CO0121", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,506 - INFO - 正在处理第 30 条更新记录 - store_code: 100099257
2025-08-01 09:00:53,506 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099257", "textField_m8e8g3lu": "媄研社", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111381", "textField_mb7rs39i": "王辉侠", "textField_mbc1lbzm": "10295L25CO0003", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,506 - INFO - 正在处理第 31 条更新记录 - store_code: 100100437
2025-08-01 09:00:53,506 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100437", "textField_m8e8g3lu": "MLBUSA", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110755", "textField_mb7rs39i": "刘红梅", "textField_mbc1lbzm": "10295L23CO0095", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,506 - INFO - 正在处理第 32 条更新记录 - store_code: 100101108
2025-08-01 09:00:53,506 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101108", "textField_m8e8g3lu": "OPPO", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111428", "textField_mb7rs39i": "范乃昌", "textField_mbc1lbzm": "10295L24CO0042", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,506 - INFO - 正在处理第 33 条更新记录 - store_code: 100099282
2025-08-01 09:00:53,506 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099282", "textField_m8e8g3lu": "熊享居独食火锅", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109417", "textField_mb7rs39i": "劳华辉", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,506 - INFO - 正在处理第 34 条更新记录 - store_code: 100100665
2025-08-01 09:00:53,506 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100665", "textField_m8e8g3lu": "卡趣儿", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111233", "textField_mb7rs39i": "马森森", "textField_mbc1lbzm": "10295L24CO0020", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,506 - INFO - 正在处理第 35 条更新记录 - store_code: 100099176
2025-08-01 09:00:53,506 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099176", "textField_m8e8g3lu": "斯凯奇", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110353", "textField_mb7rs39i": "丰宏（广州）商贸有限公司", "textField_mbc1lbzm": "10295L20CO0194", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,506 - INFO - 正在处理第 36 条更新记录 - store_code: 100099283
2025-08-01 09:00:53,506 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099283", "textField_m8e8g3lu": "六福珠宝", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110695", "textField_mb7rs39i": "黄骏颖", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,506 - INFO - 正在处理第 37 条更新记录 - store_code: 100100666
2025-08-01 09:00:53,506 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100666", "textField_m8e8g3lu": "骆驼", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111193", "textField_mb7rs39i": "广州黑林服饰有限公司", "textField_mbc1lbzm": "10295L25CO0023", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,506 - INFO - 正在处理第 38 条更新记录 - store_code: 100100434
2025-08-01 09:00:53,506 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100434", "textField_m8e8g3lu": "无", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103893", "textField_mb7rs39i": "广州播了么传媒科技有限公司", "textField_mbc1lbzm": "10295L23CO0090", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,522 - INFO - 正在处理第 39 条更新记录 - store_code: 100100355
2025-08-01 09:00:53,522 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100355", "textField_m8e8g3lu": "春天舞蹈", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110605", "textField_mb7rs39i": "陈廷东", "textField_mbc1lbzm": "10295L24CO0030", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,522 - INFO - 正在处理第 40 条更新记录 - store_code: 100100314
2025-08-01 09:00:53,522 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100314", "textField_m8e8g3lu": "阪孖氏", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110799", "textField_mb7rs39i": "靓淘（广州）智能科技有限公司", "textField_mbc1lbzm": "10295L23CO0053", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,522 - INFO - 正在处理第 41 条更新记录 - store_code: 100099177
2025-08-01 09:00:53,522 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099177", "textField_m8e8g3lu": "宗狮打柠", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109744", "textField_mb7rs39i": "觅觉科技（广州）有限公司", "textField_mbc1lbzm": "10295L23CO0097", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,522 - INFO - 正在处理第 42 条更新记录 - store_code: 100100249
2025-08-01 09:00:53,522 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100249", "textField_m8e8g3lu": "小猪班纳", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110615", "textField_mb7rs39i": "王健清", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,522 - INFO - 正在处理第 43 条更新记录 - store_code: 100099294
2025-08-01 09:00:53,522 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099294", "textField_m8e8g3lu": "萌宝亲子乐园儿童用品", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108629", "textField_mb7rs39i": "王亚州", "textField_mbc1lbzm": "10295L22CO0101", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,522 - INFO - 正在处理第 44 条更新记录 - store_code: 100100953
2025-08-01 09:00:53,522 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100953", "textField_m8e8g3lu": "华丰超市", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111572", "textField_mb7rs39i": "佛山市华之丰超市有限公司", "textField_mbc1lbzm": "10295L25CO0005", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,522 - INFO - 正在处理第 45 条更新记录 - store_code: 100099280
2025-08-01 09:00:53,522 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099280", "textField_m8e8g3lu": "宝莱公子", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103988", "textField_mb7rs39i": "黄考", "textField_mbc1lbzm": "10295L20CO0207", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,522 - INFO - 正在处理第 46 条更新记录 - store_code: 100099256
2025-08-01 09:00:53,522 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099256", "textField_m8e8g3lu": "泡泡兔共享童车", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104007", "textField_mb7rs39i": "李胜龙", "textField_mbc1lbzm": "10295L22CO0040", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,522 - INFO - 正在处理第 47 条更新记录 - store_code: 100099237
2025-08-01 09:00:53,522 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099237", "textField_m8e8g3lu": "明星宝贝", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104000", "textField_mb7rs39i": "赵宇昊", "textField_mbc1lbzm": "10295L22CO0075", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,522 - INFO - 正在处理第 48 条更新记录 - store_code: 100100374
2025-08-01 09:00:53,522 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100374", "textField_m8e8g3lu": "漫游舱", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110931", "textField_mb7rs39i": "广州乐为文化科技有限公司", "textField_mbc1lbzm": "10295L23CO0088", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,522 - INFO - 正在处理第 49 条更新记录 - store_code: 100099220
2025-08-01 09:00:53,522 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099220", "textField_m8e8g3lu": "多经-回收宝", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "110879", "textField_mb7rs39i": "刘子成", "textField_mbc1lbzm": "10295L24CO0104", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,538 - INFO - 正在处理第 50 条更新记录 - store_code: 100100339
2025-08-01 09:00:53,538 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100339", "textField_m8e8g3lu": "周六福", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110524", "textField_mb7rs39i": "吴海明", "textField_mbc1lbzm": "10295L23CO0065", "textField_m9jkl9nx": "否"}
2025-08-01 09:00:53,538 - INFO - 达到批量处理大小，开始批量更新 50 条记录
2025-08-01 09:00:54,116 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:54,709 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:55,350 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:55,959 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:56,616 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:57,053 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:57,772 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:58,256 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:58,819 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:59,381 - INFO - 批量更新表单数据成功: 
2025-08-01 09:00:59,819 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:00,413 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:01,069 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:01,709 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:02,334 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:02,959 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:03,631 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:04,225 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:04,834 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:05,491 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:05,975 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:06,678 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:07,178 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:07,787 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:08,412 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:08,959 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:09,553 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:10,256 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:11,069 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:11,694 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:12,241 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:12,975 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:13,678 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:14,241 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:14,725 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:15,272 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:15,803 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:16,412 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:17,037 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:17,678 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:18,225 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:18,819 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:19,319 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:20,006 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:20,725 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:21,272 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:21,959 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:22,600 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:23,131 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:23,834 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:23,834 - INFO - 批量更新成功，form_instance_ids: ['FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMB7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMC7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMD7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMG7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMI7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMJ7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMM7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMN7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMP7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMQ7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMS7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMY7', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMM3', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMP3', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMQ3', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMR3', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMS3', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMU3', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMZ3', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBM14', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBM24', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBM94', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMA4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBME4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMH4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMK4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMM4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMN4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMO4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMP4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMQ4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMR4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMS4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMU4', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBME6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMI6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMJ6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMK6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMM6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMO6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMP6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMQ6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMT6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMU6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMV6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMZ6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBM07', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBM17', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBM27', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBM37']
2025-08-01 09:01:23,834 - INFO - 正在处理第 1 条更新记录 - store_code: 100100297
2025-08-01 09:01:23,834 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100297", "textField_m8e8g3lu": "魅KTV", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111486", "textField_mb7rs39i": "佛山朵咖文化娱乐有限公司", "textField_mbc1lbzm": "10295L24CO0122", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,834 - INFO - 正在处理第 2 条更新记录 - store_code: 100099174
2025-08-01 09:01:23,834 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099174", "textField_m8e8g3lu": "多经-coco都可茶饮", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "103958", "textField_mb7rs39i": "佛山市馥和煦餐饮服务有限责任公司", "textField_mbc1lbzm": "10295L24CO0108", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,834 - INFO - 正在处理第 3 条更新记录 - store_code: 100099997
2025-08-01 09:01:23,834 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099997", "textField_m8e8g3lu": "黑马艺术", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109460", "textField_mb7rs39i": "李彬", "textField_mbc1lbzm": "10295L24CO0116", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,834 - INFO - 正在处理第 4 条更新记录 - store_code: 100099295
2025-08-01 09:01:23,834 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099295", "textField_m8e8g3lu": "醒目眼镜", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109472", "textField_mb7rs39i": "谢泽明", "textField_mbc1lbzm": "10295L22CO0081", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,834 - INFO - 正在处理第 5 条更新记录 - store_code: 100099281
2025-08-01 09:01:23,834 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099281", "textField_m8e8g3lu": "喝福", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109127", "textField_mb7rs39i": "罗毅华", "textField_mbc1lbzm": "10295L22CO0038", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,834 - INFO - 正在处理第 6 条更新记录 - store_code: 100100664
2025-08-01 09:01:23,834 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100664", "textField_m8e8g3lu": "悦享时光", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111159", "textField_mb7rs39i": "吴东鸿", "textField_mbc1lbzm": "10295L24CO0017", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,834 - INFO - 正在处理第 7 条更新记录 - store_code: 100101107
2025-08-01 09:01:23,834 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101107", "textField_m8e8g3lu": "哈克斯", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111447", "textField_mb7rs39i": "符军", "textField_mbc1lbzm": "10295L24CO0043", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,834 - INFO - 正在处理第 8 条更新记录 - store_code: 100100375
2025-08-01 09:01:23,834 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100375", "textField_m8e8g3lu": "中国黄金", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110896", "textField_mb7rs39i": "郑伟光", "textField_mbc1lbzm": "10295L23CO0087", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,834 - INFO - 正在处理第 9 条更新记录 - store_code: 100099226
2025-08-01 09:01:23,834 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099226", "textField_m8e8g3lu": "去K书", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109428", "textField_mb7rs39i": "智学有成（佛山）科技有限公司", "textField_mbc1lbzm": "10295L22CO0077", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,834 - INFO - 正在处理第 10 条更新记录 - store_code: 100100003
2025-08-01 09:01:23,834 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100003", "textField_m8e8g3lu": "蒙自源过桥米线", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111657", "textField_mb7rs39i": "李良中", "textField_mbc1lbzm": "10295L25CO0018", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,834 - INFO - 正在处理第 11 条更新记录 - store_code: 100099313
2025-08-01 09:01:23,834 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099313", "textField_m8e8g3lu": "牛More石烧专门店", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103935", "textField_mb7rs39i": "陈国怡", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,834 - INFO - 正在处理第 12 条更新记录 - store_code: 100099292
2025-08-01 09:01:23,850 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099292", "textField_m8e8g3lu": "画大师", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108603", "textField_mb7rs39i": "佛山画大狮教育文化有限公司", "textField_mbc1lbzm": "10295L22CO0085", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,850 - INFO - 正在处理第 13 条更新记录 - store_code: 100100927
2025-08-01 09:01:23,850 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100927", "textField_m8e8g3lu": "龍入疆域面面俱到", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111309", "textField_mb7rs39i": "丁国鹏", "textField_mbc1lbzm": "10295L24CO0028", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,850 - INFO - 正在处理第 14 条更新记录 - store_code: 100099277
2025-08-01 09:01:23,850 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099277", "textField_m8e8g3lu": "小腰侠", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103952", "textField_mb7rs39i": "佛山市南海区小腰侠星汇餐饮管理有限公司", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,850 - INFO - 正在处理第 15 条更新记录 - store_code: 100100377
2025-08-01 09:01:23,850 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100377", "textField_m8e8g3lu": "西尚情郎", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110887", "textField_mb7rs39i": "程松林", "textField_mbc1lbzm": "10295L23CO0086", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,850 - INFO - 正在处理第 16 条更新记录 - store_code: 100100476
2025-08-01 09:01:23,850 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100476", "textField_m8e8g3lu": "广东尚尧律师事务所", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110958", "textField_mb7rs39i": "广东尚尧律师事务所", "textField_mbc1lbzm": "10295L24CO0010", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,850 - INFO - 正在处理第 17 条更新记录 - store_code: 100099271
2025-08-01 09:01:23,850 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099271", "textField_m8e8g3lu": "星火教育", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103954", "textField_mb7rs39i": "佛山市南海区吉晓教育培训中心有限公司", "textField_mbc1lbzm": "10295L20CO0105", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,850 - INFO - 正在处理第 18 条更新记录 - store_code: 100101128
2025-08-01 09:01:23,850 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101128", "textField_m8e8g3lu": "艾力斯特", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111446", "textField_mb7rs39i": "杨敏", "textField_mbc1lbzm": "10295L24CO0106", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,850 - INFO - 正在处理第 19 条更新记录 - store_code: 100099293
2025-08-01 09:01:23,850 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099293", "textField_m8e8g3lu": "保罗威特", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109778", "textField_mb7rs39i": "曹建冬", "textField_mbc1lbzm": "10295L22CO0102", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,850 - INFO - 正在处理第 20 条更新记录 - store_code: 100100949
2025-08-01 09:01:23,850 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100949", "textField_m8e8g3lu": "讯飞英语通", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111496", "textField_mb7rs39i": "悦韵讯飞（佛山市）科技有限公司", "textField_mbc1lbzm": "10295L24CO0117", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,850 - INFO - 正在处理第 21 条更新记录 - store_code: 100100378
2025-08-01 09:01:23,850 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100378", "textField_m8e8g3lu": "SMADDY", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110981", "textField_mb7rs39i": "刘红梅", "textField_mbc1lbzm": "10295L23CO0089", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,850 - INFO - 正在处理第 22 条更新记录 - store_code: 100099249
2025-08-01 09:01:23,850 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099249", "textField_m8e8g3lu": "崀山人家", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104066", "textField_mb7rs39i": "陈美", "textField_mbc1lbzm": "10295L21CO0089", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,850 - INFO - 正在处理第 23 条更新记录 - store_code: 100100373
2025-08-01 09:01:23,850 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100373", "textField_m8e8g3lu": "YES", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110899", "textField_mb7rs39i": "饶雪梅", "textField_mbc1lbzm": "10295L23CO0081", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,850 - INFO - 正在处理第 24 条更新记录 - store_code: 100099218
2025-08-01 09:01:23,850 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099218", "textField_m8e8g3lu": "易站便利店", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104008", "textField_mb7rs39i": "苏文强", "textField_mbc1lbzm": "10295L21CO0003", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,865 - INFO - 正在处理第 25 条更新记录 - store_code: 100100335
2025-08-01 09:01:23,865 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100335", "textField_m8e8g3lu": "爱客", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110843", "textField_mb7rs39i": "林金华", "textField_mbc1lbzm": "10295L23CO0063", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,865 - INFO - 正在处理第 26 条更新记录 - store_code: 100100291
2025-08-01 09:01:23,865 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100291", "textField_m8e8g3lu": "乐酷漂移碰碰车", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110769", "textField_mb7rs39i": "何舜娟", "textField_mbc1lbzm": "10295L23CO0048", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,865 - INFO - 正在处理第 27 条更新记录 - store_code: 100100238
2025-08-01 09:01:23,865 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100238", "textField_m8e8g3lu": "Ttsp.X", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110579", "textField_mb7rs39i": "陈秀荣", "textField_mbc1lbzm": "10295L23CO0032", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,865 - INFO - 正在处理第 28 条更新记录 - store_code: 100099338
2025-08-01 09:01:23,865 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099338", "textField_m8e8g3lu": "杨国福麻辣烫", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109767", "textField_mb7rs39i": "华永勇", "textField_mbc1lbzm": "10295L22CO0097", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,865 - INFO - 正在处理第 29 条更新记录 - store_code: 100099307
2025-08-01 09:01:23,865 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099307", "textField_m8e8g3lu": "酷乐鸭童装", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109791", "textField_mb7rs39i": "邓小钊", "textField_mbc1lbzm": "10295L22CO0103", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,865 - INFO - 正在处理第 30 条更新记录 - store_code: 100099290
2025-08-01 09:01:23,865 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099290", "textField_m8e8g3lu": "爱咪哚", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111457", "textField_mb7rs39i": "佛山市波亚米特服饰有限公司", "textField_mbc1lbzm": "10295L20CO0212", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,865 - INFO - 正在处理第 31 条更新记录 - store_code: 100100925
2025-08-01 09:01:23,865 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100925", "textField_m8e8g3lu": "范莎英语", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111270", "textField_mb7rs39i": "佛山市范莎教育科技有限公司", "textField_mbc1lbzm": "10295L24CO0026", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,865 - INFO - 正在处理第 32 条更新记录 - store_code: 100099274
2025-08-01 09:01:23,865 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099274", "textField_m8e8g3lu": "悦旺花店", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109656", "textField_mb7rs39i": "杨少勤", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,865 - INFO - 正在处理第 33 条更新记录 - store_code: 100100368
2025-08-01 09:01:23,865 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100368", "textField_m8e8g3lu": "法桐树下", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110750", "textField_mb7rs39i": "任嘉钰", "textField_mbc1lbzm": "10295L23CO0080", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,865 - INFO - 正在处理第 34 条更新记录 - store_code: 100100327
2025-08-01 09:01:23,865 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100327", "textField_m8e8g3lu": "开心穷味", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111094", "textField_mb7rs39i": "佛山市开心紫新餐饮管理有限公司", "textField_mbc1lbzm": "10295L24CO0004", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,865 - INFO - 正在处理第 35 条更新记录 - store_code: 100100269
2025-08-01 09:01:23,865 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100269", "textField_m8e8g3lu": "栖那", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110732", "textField_mb7rs39i": "金雯", "textField_mbc1lbzm": "10295L23CO0043", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,865 - INFO - 正在处理第 36 条更新记录 - store_code: 100099339
2025-08-01 09:01:23,865 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099339", "textField_m8e8g3lu": "乖乖专业儿童摄影", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103936", "textField_mb7rs39i": "陈德强", "textField_mbc1lbzm": "10295L24CO0036", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,865 - INFO - 正在处理第 37 条更新记录 - store_code: 100099312
2025-08-01 09:01:23,881 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099312", "textField_m8e8g3lu": "和本国际医疗", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "106246", "textField_mb7rs39i": "陈大欣", "textField_mbc1lbzm": "10295L24CO0038", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,881 - INFO - 正在处理第 38 条更新记录 - store_code: 100099275
2025-08-01 09:01:23,881 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099275", "textField_m8e8g3lu": "秦汉唐", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109106", "textField_mb7rs39i": "徐强", "textField_mbc1lbzm": "10295L22CO0016", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,881 - INFO - 正在处理第 39 条更新记录 - store_code: 100100467
2025-08-01 09:01:23,881 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100467", "textField_m8e8g3lu": "彼芯托管", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111012", "textField_mb7rs39i": "佛山市南海区静思默想教育科技有限公司", "textField_mbc1lbzm": "10295L24CO0003", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,881 - INFO - 正在处理第 40 条更新记录 - store_code: 100100926
2025-08-01 09:01:23,881 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100926", "textField_m8e8g3lu": "鸣星学府", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111556", "textField_mb7rs39i": "白浩文", "textField_mbc1lbzm": "10295L25CO0011", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,881 - INFO - 正在处理第 41 条更新记录 - store_code: 100099270
2025-08-01 09:01:23,881 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099270", "textField_m8e8g3lu": "老爷车男装", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103985", "textField_mb7rs39i": "符海花", "textField_mbc1lbzm": "10295L20CO0211", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,881 - INFO - 正在处理第 42 条更新记录 - store_code: 100099247
2025-08-01 09:01:23,881 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099247", "textField_m8e8g3lu": "多经-国王攀岩", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "111414", "textField_mb7rs39i": "佛山市星攀教育科技有限公司", "textField_mbc1lbzm": "10295L22CO0017", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,881 - INFO - 正在处理第 43 条更新记录 - store_code: 100099234
2025-08-01 09:01:23,881 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099234", "textField_m8e8g3lu": "木喵喵木", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103965", "textField_mb7rs39i": "佛山市繁星六月文化艺术有限公司", "textField_mbc1lbzm": "10295L24CO0039", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,881 - INFO - 正在处理第 44 条更新记录 - store_code: 100100369
2025-08-01 09:01:23,881 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100369", "textField_m8e8g3lu": "力果童装", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110587", "textField_mb7rs39i": "林小霞", "textField_mbc1lbzm": "10295L23CO0079", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,881 - INFO - 正在处理第 45 条更新记录 - store_code: 100099216
2025-08-01 09:01:23,881 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099216", "textField_m8e8g3lu": "零食屋", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104018", "textField_mb7rs39i": "杜钦隆", "textField_mbc1lbzm": "10295L20CO0231", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,881 - INFO - 正在处理第 46 条更新记录 - store_code: 100100328
2025-08-01 09:01:23,881 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100328", "textField_m8e8g3lu": "四季椰林", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110468", "textField_mb7rs39i": "四季椰林（广州）餐饮管理有限公司", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,881 - INFO - 正在处理第 47 条更新记录 - store_code: 100100278
2025-08-01 09:01:23,881 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100278", "textField_m8e8g3lu": "美丽衣橱", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110684", "textField_mb7rs39i": "邢有龙", "textField_mbc1lbzm": "10295L23CO0044", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,881 - INFO - 正在处理第 48 条更新记录 - store_code: 100101216
2025-08-01 09:01:23,881 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101216", "textField_m8e8g3lu": "星脊康健康中心", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111565", "textField_mb7rs39i": "佛山市星脊康健康管理有限公司", "textField_mbc1lbzm": "10295L25CO0002", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,881 - INFO - 正在处理第 49 条更新记录 - store_code: 100101262
2025-08-01 09:01:23,897 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101262", "textField_m8e8g3lu": "极客玩家", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111557", "textField_mb7rs39i": "翟文晓", "textField_mbc1lbzm": "10295L25CO0006", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,897 - INFO - 正在处理第 50 条更新记录 - store_code: 100101277
2025-08-01 09:01:23,897 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101277", "textField_m8e8g3lu": "京东电器", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111615", "textField_mb7rs39i": "京东五星电器（佛山）有限公司", "textField_mbc1lbzm": "10295L25CO0012", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:23,897 - INFO - 达到批量处理大小，开始批量更新 50 条记录
2025-08-01 09:01:24,756 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:25,569 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:26,240 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:26,803 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:27,444 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:28,069 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:28,772 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:29,256 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:29,803 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:30,412 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:31,037 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:31,678 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:32,162 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:32,678 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:33,272 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:33,975 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:34,459 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:35,037 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:35,537 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:36,084 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:36,725 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:37,397 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:38,037 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:38,615 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:39,178 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:39,631 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:40,115 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:40,600 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:41,115 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:41,740 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:42,381 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:43,006 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:43,600 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:44,537 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:45,240 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:45,897 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:46,490 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:47,100 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:47,678 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:48,178 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:48,756 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:49,381 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:50,053 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:50,615 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:51,178 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:51,709 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:52,225 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:52,818 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:53,475 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:54,100 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:54,100 - INFO - 批量更新成功，form_instance_ids: ['FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBM57', 'FINST-7PF66CC1RKVV7DYI8X2R977VF92123AIJPDBMSB', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBM87', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBM97', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMA7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMB7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMC7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMF7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMG7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMM7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMN7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMO7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMP7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMQ7', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBMN9', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBMO9', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBMP9', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBMZ9', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBM0A', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBM3A', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBM4A', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBM5A', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBM7A', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBM8A', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBM9A', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMBA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMDA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMEA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMFA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMGA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMHA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMIA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMOA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMQA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMRA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMSA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMTA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMWA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMXA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMYA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBM0B', 'FINST-7PF66CC1RKVV7DYI8X2R977VF92123AIJPDBMTB', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0374KADCBMGI', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0374KADCBMHI', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0374KADCBMII', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0374KADCBMJI', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0384KADCBMLI', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0384KADCBMNI', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0384KADCBMOI', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0384KADCBMRI']
2025-08-01 09:01:54,100 - INFO - 正在处理第 1 条更新记录 - store_code: 100101288
2025-08-01 09:01:54,115 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101288", "textField_m8e8g3lu": "玛瑞莎", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111608", "textField_mb7rs39i": "彭士松", "textField_mbc1lbzm": "10295L25CO0013", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:54,115 - INFO - 正在处理第 2 条更新记录 - store_code: 100101303
2025-08-01 09:01:54,115 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101303", "textField_m8e8g3lu": "源小晓", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111639", "textField_mb7rs39i": "陈孔文", "textField_mbc1lbzm": "10295L25CO0017", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:54,115 - INFO - 正在处理第 3 条更新记录 - store_code: 100099393
2025-08-01 09:01:54,115 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099393", "textField_m8e8g3lu": "诚寿司", "employeeField_m8e8g3lw": ["16d2416d58bbecd312653de411ab0b7b", "18ea2e5066661f3b4e114024bd290c2d", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101023", "textField_mb7rs39i": "广州市利宝餐饮管理有限公司", "textField_mbc1lbzm": "10311L20CO0082", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:54,115 - INFO - 正在处理第 4 条更新记录 - store_code: 100101141
2025-08-01 09:01:54,115 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100101141", "textField_m8e8g3lu": "德图", "employeeField_m8e8g3lw": ["16d2416d58bbecd312653de411ab0b7b", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108505", "textField_mb7rs39i": "兰海宁", "textField_mbc1lbzm": "", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:54,115 - INFO - 正在处理第 5 条更新记录 - store_code: 100099418
2025-08-01 09:01:54,115 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099418", "textField_m8e8g3lu": "SUPA FAMA", "employeeField_m8e8g3lw": ["16d2416d58bbecd312653de411ab0b7b", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "107294", "textField_mb7rs39i": "广州骏兴西餐厅有限公司", "textField_mbc1lbzm": "10311L25CO0003", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:54,115 - INFO - 正在处理第 6 条更新记录 - store_code: 100099943
2025-08-01 09:01:54,115 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100099943", "textField_m8e8g3lu": "皮氏咖啡", "employeeField_m8e8g3lw": ["16d2416d58bbecd312653de411ab0b7b", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108880", "textField_mb7rs39i": "皮氏咖啡（深圳）有限公司广州体育西路分公司", "textField_mbc1lbzm": "10311L23CO0006", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:54,115 - INFO - 正在处理第 7 条更新记录 - store_code: 100101247
2025-08-01 09:01:54,115 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100101247", "textField_m8e8g3lu": "瑞幸咖啡", "employeeField_m8e8g3lw": ["16d2416d58bbecd312653de411ab0b7b", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108644", "textField_mb7rs39i": "瑞幸咖啡（广东）有限公司", "textField_mbc1lbzm": "10311L25CO0001", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:54,115 - INFO - 正在处理第 8 条更新记录 - store_code: 100101289
2025-08-01 09:01:54,115 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "100101289", "textField_m8e8g3lu": "虾宴", "employeeField_m8e8g3lw": ["16d2416d58bbecd312653de411ab0b7b", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "107294", "textField_mb7rs39i": "广州骏兴西餐厅有限公司", "textField_mbc1lbzm": "10311L25CO0002", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:54,115 - INFO - 正在处理第 9 条更新记录 - store_code: 100101299
2025-08-01 09:01:54,115 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉星汇维港", "textField_m911r3pn": "100101299", "textField_m8e8g3lu": "黄蜀郎鸡公煲", "employeeField_m8e8g3lw": ["16d2416e996837cef3e7d79485192fc3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10630L25CO0014", "textField_m9jkl9nx": "否"}
2025-08-01 09:01:54,115 - INFO - 处理剩余 9 条更新记录
2025-08-01 09:01:54,834 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:55,397 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:56,053 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:56,678 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:57,303 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:57,912 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:58,506 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:59,100 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:59,693 - INFO - 批量更新表单数据成功: 
2025-08-01 09:01:59,693 - INFO - 批量更新成功，form_instance_ids: ['FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0384KADCBMSI', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0384KADCBMUI', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM82', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMM2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMS2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM83', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBM93', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMA3', 'FINST-EEC66XC1CISVWZ5HCG6LJ4YFOO0S3AGLADCBMAZ']
2025-08-01 09:01:59,693 - INFO - 宜搭表单更新完成
2025-08-01 09:01:59,693 - INFO - 数据处理完成
2025-08-01 09:01:59,693 - INFO - 数据库连接已关闭
