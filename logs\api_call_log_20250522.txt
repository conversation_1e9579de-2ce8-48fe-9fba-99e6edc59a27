2025-05-22 11:59:01,331 - INFO - 开始获取********至********的销售数据
2025-05-22 11:59:01,331 - INFO - Business data: {'fromDate': '********', 'toDate': '********', 'shopIds': ['1ETDLFB9DIMQME7Q2OVD93ISAI00189O', '1GD9P85P7PPH4L7Q2OV3OBNS49001KPE', '1HFLOR99TBR11L6UBHOUTGCK1C001A3F', '1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV', '1HRIS0CDPQR2GM6E7AERKQ83MS0014QU', '1HRIS7255PESAA7AV8LHQQGIH8001KNH', '1HSJMRFR3MBRV37AV8LHQQGIT4001C3D']}
2025-05-22 11:59:01,331 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-22 11:59:01,331 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CE8D4DF8083883F4CE7A9CA2979052C2'}
2025-05-22 11:59:05,586 - INFO - 开始获取********至********的销售数据
2025-05-22 11:59:05,586 - INFO - Business data: {'fromDate': '********', 'toDate': '********', 'shopIds': ['1ETDLFB9DIMQME7Q2OVD93ISAI00189O', '1GD9P85P7PPH4L7Q2OV3OBNS49001KPE', '1HFLOR99TBR11L6UBHOUTGCK1C001A3F', '1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV', '1HRIS0CDPQR2GM6E7AERKQ83MS0014QU', '1HRIS7255PESAA7AV8LHQQGIH8001KNH', '1HSJMRFR3MBRV37AV8LHQQGIT4001C3D']}
2025-05-22 11:59:05,586 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-22 11:59:05,586 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5E4A206C52886EDBBC2F390A93541AAF'}
2025-05-22 11:59:11,843 - INFO - 开始获取********至********的销售数据
2025-05-22 11:59:11,843 - INFO - Business data: {'fromDate': '********', 'toDate': '********', 'shopIds': ['1ETDLFB9DIMQME7Q2OVD93ISAI00189O', '1GD9P85P7PPH4L7Q2OV3OBNS49001KPE', '1HFLOR99TBR11L6UBHOUTGCK1C001A3F', '1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV', '1HRIS0CDPQR2GM6E7AERKQ83MS0014QU', '1HRIS7255PESAA7AV8LHQQGIH8001KNH', '1HSJMRFR3MBRV37AV8LHQQGIT4001C3D']}
2025-05-22 11:59:11,843 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-22 11:59:11,843 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A116A2EC992F72D66B425EA0B4E996E2'}
2025-05-22 11:59:16,565 - INFO - 开始获取********至********的销售数据
2025-05-22 11:59:16,565 - INFO - Business data: {'fromDate': '********', 'toDate': '********', 'shopIds': ['1ETDLFB9DIMQME7Q2OVD93ISAI00189O', '1GD9P85P7PPH4L7Q2OV3OBNS49001KPE', '1HFLOR99TBR11L6UBHOUTGCK1C001A3F', '1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV', '1HRIS0CDPQR2GM6E7AERKQ83MS0014QU', '1HRIS7255PESAA7AV8LHQQGIH8001KNH', '1HSJMRFR3MBRV37AV8LHQQGIT4001C3D']}
2025-05-22 11:59:16,565 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-22 11:59:16,565 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '63782811E55A5A3496BC9BF569980CB7'}
2025-05-22 11:59:20,982 - INFO - 开始获取********至********的销售数据
2025-05-22 11:59:20,982 - INFO - Business data: {'fromDate': '********', 'toDate': '********', 'shopIds': ['1ETDLFB9DIMQME7Q2OVD93ISAI00189O', '1GD9P85P7PPH4L7Q2OV3OBNS49001KPE', '1HFLOR99TBR11L6UBHOUTGCK1C001A3F', '1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV', '1HRIS0CDPQR2GM6E7AERKQ83MS0014QU', '1HRIS7255PESAA7AV8LHQQGIH8001KNH', '1HSJMRFR3MBRV37AV8LHQQGIT4001C3D']}
2025-05-22 11:59:20,982 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-22 11:59:20,982 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2FE71F68955D4BEAE65CFF94267D00D5'}
2025-05-22 11:59:29,756 - INFO - 数据已保存到Excel文件: sales_data_30days_20250522.xlsx
