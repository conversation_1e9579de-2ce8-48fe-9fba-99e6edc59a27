2025-05-27 00:00:01,948 - INFO - =================使用默认全量同步=============
2025-05-27 00:00:03,448 - INFO - MySQL查询成功，共获取 3302 条记录
2025-05-27 00:00:03,448 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-27 00:00:03,479 - INFO - 开始处理日期: 2025-01
2025-05-27 00:00:03,479 - INFO - Request Parameters - Page 1:
2025-05-27 00:00:03,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:03,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:04,604 - INFO - Response - Page 1:
2025-05-27 00:00:04,807 - INFO - 第 1 页获取到 100 条记录
2025-05-27 00:00:04,807 - INFO - Request Parameters - Page 2:
2025-05-27 00:00:04,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:04,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:05,338 - INFO - Response - Page 2:
2025-05-27 00:00:05,541 - INFO - 第 2 页获取到 100 条记录
2025-05-27 00:00:05,541 - INFO - Request Parameters - Page 3:
2025-05-27 00:00:05,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:05,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:06,026 - INFO - Response - Page 3:
2025-05-27 00:00:06,229 - INFO - 第 3 页获取到 100 条记录
2025-05-27 00:00:06,229 - INFO - Request Parameters - Page 4:
2025-05-27 00:00:06,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:06,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:06,760 - INFO - Response - Page 4:
2025-05-27 00:00:06,963 - INFO - 第 4 页获取到 100 条记录
2025-05-27 00:00:06,963 - INFO - Request Parameters - Page 5:
2025-05-27 00:00:06,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:06,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:07,432 - INFO - Response - Page 5:
2025-05-27 00:00:07,635 - INFO - 第 5 页获取到 100 条记录
2025-05-27 00:00:07,635 - INFO - Request Parameters - Page 6:
2025-05-27 00:00:07,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:07,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:08,166 - INFO - Response - Page 6:
2025-05-27 00:00:08,369 - INFO - 第 6 页获取到 100 条记录
2025-05-27 00:00:08,369 - INFO - Request Parameters - Page 7:
2025-05-27 00:00:08,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:08,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:08,854 - INFO - Response - Page 7:
2025-05-27 00:00:09,057 - INFO - 第 7 页获取到 82 条记录
2025-05-27 00:00:09,057 - INFO - 查询完成，共获取到 682 条记录
2025-05-27 00:00:09,057 - INFO - 获取到 682 条表单数据
2025-05-27 00:00:09,057 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-27 00:00:09,072 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 00:00:09,072 - INFO - 开始处理日期: 2025-02
2025-05-27 00:00:09,072 - INFO - Request Parameters - Page 1:
2025-05-27 00:00:09,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:09,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:09,572 - INFO - Response - Page 1:
2025-05-27 00:00:09,776 - INFO - 第 1 页获取到 100 条记录
2025-05-27 00:00:09,776 - INFO - Request Parameters - Page 2:
2025-05-27 00:00:09,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:09,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:10,244 - INFO - Response - Page 2:
2025-05-27 00:00:10,447 - INFO - 第 2 页获取到 100 条记录
2025-05-27 00:00:10,447 - INFO - Request Parameters - Page 3:
2025-05-27 00:00:10,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:10,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:10,932 - INFO - Response - Page 3:
2025-05-27 00:00:11,135 - INFO - 第 3 页获取到 100 条记录
2025-05-27 00:00:11,135 - INFO - Request Parameters - Page 4:
2025-05-27 00:00:11,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:11,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:11,697 - INFO - Response - Page 4:
2025-05-27 00:00:11,901 - INFO - 第 4 页获取到 100 条记录
2025-05-27 00:00:11,901 - INFO - Request Parameters - Page 5:
2025-05-27 00:00:11,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:11,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:12,401 - INFO - Response - Page 5:
2025-05-27 00:00:12,604 - INFO - 第 5 页获取到 100 条记录
2025-05-27 00:00:12,604 - INFO - Request Parameters - Page 6:
2025-05-27 00:00:12,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:12,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:13,072 - INFO - Response - Page 6:
2025-05-27 00:00:13,276 - INFO - 第 6 页获取到 100 条记录
2025-05-27 00:00:13,276 - INFO - Request Parameters - Page 7:
2025-05-27 00:00:13,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:13,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:13,854 - INFO - Response - Page 7:
2025-05-27 00:00:14,057 - INFO - 第 7 页获取到 70 条记录
2025-05-27 00:00:14,057 - INFO - 查询完成，共获取到 670 条记录
2025-05-27 00:00:14,057 - INFO - 获取到 670 条表单数据
2025-05-27 00:00:14,057 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-27 00:00:14,072 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 00:00:14,072 - INFO - 开始处理日期: 2025-03
2025-05-27 00:00:14,072 - INFO - Request Parameters - Page 1:
2025-05-27 00:00:14,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:14,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:14,526 - INFO - Response - Page 1:
2025-05-27 00:00:14,729 - INFO - 第 1 页获取到 100 条记录
2025-05-27 00:00:14,729 - INFO - Request Parameters - Page 2:
2025-05-27 00:00:14,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:14,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:15,260 - INFO - Response - Page 2:
2025-05-27 00:00:15,463 - INFO - 第 2 页获取到 100 条记录
2025-05-27 00:00:15,463 - INFO - Request Parameters - Page 3:
2025-05-27 00:00:15,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:15,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:16,213 - INFO - Response - Page 3:
2025-05-27 00:00:16,416 - INFO - 第 3 页获取到 100 条记录
2025-05-27 00:00:16,416 - INFO - Request Parameters - Page 4:
2025-05-27 00:00:16,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:16,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:16,916 - INFO - Response - Page 4:
2025-05-27 00:00:17,119 - INFO - 第 4 页获取到 100 条记录
2025-05-27 00:00:17,119 - INFO - Request Parameters - Page 5:
2025-05-27 00:00:17,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:17,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:17,588 - INFO - Response - Page 5:
2025-05-27 00:00:17,791 - INFO - 第 5 页获取到 100 条记录
2025-05-27 00:00:17,791 - INFO - Request Parameters - Page 6:
2025-05-27 00:00:17,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:17,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:18,322 - INFO - Response - Page 6:
2025-05-27 00:00:18,526 - INFO - 第 6 页获取到 100 条记录
2025-05-27 00:00:18,526 - INFO - Request Parameters - Page 7:
2025-05-27 00:00:18,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:18,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:18,979 - INFO - Response - Page 7:
2025-05-27 00:00:19,182 - INFO - 第 7 页获取到 61 条记录
2025-05-27 00:00:19,182 - INFO - 查询完成，共获取到 661 条记录
2025-05-27 00:00:19,182 - INFO - 获取到 661 条表单数据
2025-05-27 00:00:19,197 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-27 00:00:19,197 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 00:00:19,197 - INFO - 开始处理日期: 2025-04
2025-05-27 00:00:19,197 - INFO - Request Parameters - Page 1:
2025-05-27 00:00:19,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:19,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:19,729 - INFO - Response - Page 1:
2025-05-27 00:00:19,932 - INFO - 第 1 页获取到 100 条记录
2025-05-27 00:00:19,932 - INFO - Request Parameters - Page 2:
2025-05-27 00:00:19,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:19,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:20,494 - INFO - Response - Page 2:
2025-05-27 00:00:20,697 - INFO - 第 2 页获取到 100 条记录
2025-05-27 00:00:20,697 - INFO - Request Parameters - Page 3:
2025-05-27 00:00:20,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:20,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:21,276 - INFO - Response - Page 3:
2025-05-27 00:00:21,479 - INFO - 第 3 页获取到 100 条记录
2025-05-27 00:00:21,479 - INFO - Request Parameters - Page 4:
2025-05-27 00:00:21,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:21,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:22,010 - INFO - Response - Page 4:
2025-05-27 00:00:22,213 - INFO - 第 4 页获取到 100 条记录
2025-05-27 00:00:22,213 - INFO - Request Parameters - Page 5:
2025-05-27 00:00:22,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:22,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:22,744 - INFO - Response - Page 5:
2025-05-27 00:00:22,947 - INFO - 第 5 页获取到 100 条记录
2025-05-27 00:00:22,947 - INFO - Request Parameters - Page 6:
2025-05-27 00:00:22,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:22,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:23,385 - INFO - Response - Page 6:
2025-05-27 00:00:23,588 - INFO - 第 6 页获取到 100 条记录
2025-05-27 00:00:23,588 - INFO - Request Parameters - Page 7:
2025-05-27 00:00:23,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:23,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:24,057 - INFO - Response - Page 7:
2025-05-27 00:00:24,260 - INFO - 第 7 页获取到 56 条记录
2025-05-27 00:00:24,260 - INFO - 查询完成，共获取到 656 条记录
2025-05-27 00:00:24,260 - INFO - 获取到 656 条表单数据
2025-05-27 00:00:24,260 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-27 00:00:24,276 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 00:00:24,276 - INFO - 开始处理日期: 2025-05
2025-05-27 00:00:24,276 - INFO - Request Parameters - Page 1:
2025-05-27 00:00:24,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:24,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:24,744 - INFO - Response - Page 1:
2025-05-27 00:00:24,947 - INFO - 第 1 页获取到 100 条记录
2025-05-27 00:00:24,947 - INFO - Request Parameters - Page 2:
2025-05-27 00:00:24,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:24,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:25,416 - INFO - Response - Page 2:
2025-05-27 00:00:25,619 - INFO - 第 2 页获取到 100 条记录
2025-05-27 00:00:25,619 - INFO - Request Parameters - Page 3:
2025-05-27 00:00:25,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:25,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:26,057 - INFO - Response - Page 3:
2025-05-27 00:00:26,260 - INFO - 第 3 页获取到 100 条记录
2025-05-27 00:00:26,260 - INFO - Request Parameters - Page 4:
2025-05-27 00:00:26,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:26,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:26,744 - INFO - Response - Page 4:
2025-05-27 00:00:26,947 - INFO - 第 4 页获取到 100 条记录
2025-05-27 00:00:26,947 - INFO - Request Parameters - Page 5:
2025-05-27 00:00:26,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:26,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:27,432 - INFO - Response - Page 5:
2025-05-27 00:00:27,635 - INFO - 第 5 页获取到 100 条记录
2025-05-27 00:00:27,635 - INFO - Request Parameters - Page 6:
2025-05-27 00:00:27,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:27,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:28,104 - INFO - Response - Page 6:
2025-05-27 00:00:28,307 - INFO - 第 6 页获取到 100 条记录
2025-05-27 00:00:28,307 - INFO - Request Parameters - Page 7:
2025-05-27 00:00:28,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 00:00:28,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 00:00:28,666 - INFO - Response - Page 7:
2025-05-27 00:00:28,869 - INFO - 第 7 页获取到 33 条记录
2025-05-27 00:00:28,869 - INFO - 查询完成，共获取到 633 条记录
2025-05-27 00:00:28,869 - INFO - 获取到 633 条表单数据
2025-05-27 00:00:28,869 - INFO - 当前日期 2025-05 有 633 条MySQL数据需要处理
2025-05-27 00:00:28,869 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-27 00:00:29,432 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-27 00:00:29,432 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10100080.0, 'new_value': 10500080.0}, {'field': 'total_amount', 'old_value': 10200080.0, 'new_value': 10600080.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 52}]
2025-05-27 00:00:29,432 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-27 00:00:29,885 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-27 00:00:29,885 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 114906.0, 'new_value': 121359.0}, {'field': 'offline_amount', 'old_value': 136083.28, 'new_value': 140641.28}, {'field': 'total_amount', 'old_value': 250989.28, 'new_value': 262000.28}, {'field': 'order_count', 'old_value': 5397, 'new_value': 5614}]
2025-05-27 00:00:29,885 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-27 00:00:30,322 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-27 00:00:30,322 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2508.6, 'new_value': 2580.6}, {'field': 'total_amount', 'old_value': 34190.4, 'new_value': 34262.4}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-05-27 00:00:30,322 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-27 00:00:30,713 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-27 00:00:30,713 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18487.0, 'new_value': 21487.0}, {'field': 'total_amount', 'old_value': 18487.0, 'new_value': 21487.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-27 00:00:30,713 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-27 00:00:31,088 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-27 00:00:31,088 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1072445.0, 'new_value': 1113368.0}, {'field': 'offline_amount', 'old_value': 314895.0, 'new_value': 325885.0}, {'field': 'total_amount', 'old_value': 1387340.0, 'new_value': 1439253.0}, {'field': 'order_count', 'old_value': 1661, 'new_value': 1729}]
2025-05-27 00:00:31,088 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-27 00:00:31,494 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-27 00:00:31,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50036.0, 'new_value': 52335.0}, {'field': 'total_amount', 'old_value': 87636.0, 'new_value': 89935.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-27 00:00:31,494 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-27 00:00:31,916 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-27 00:00:31,916 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81687.0, 'new_value': 85176.0}, {'field': 'offline_amount', 'old_value': 105881.0, 'new_value': 110014.0}, {'field': 'total_amount', 'old_value': 187568.0, 'new_value': 195190.0}, {'field': 'order_count', 'old_value': 4284, 'new_value': 4442}]
2025-05-27 00:00:31,916 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-27 00:00:32,322 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-27 00:00:32,322 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 259405.0, 'new_value': 267205.0}, {'field': 'total_amount', 'old_value': 289405.0, 'new_value': 297205.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-05-27 00:00:32,322 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-27 00:00:32,760 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-27 00:00:32,760 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 317221.89, 'new_value': 327021.89}, {'field': 'total_amount', 'old_value': 317221.89, 'new_value': 327021.89}, {'field': 'order_count', 'old_value': 54, 'new_value': 55}]
2025-05-27 00:00:32,760 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-27 00:00:33,182 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-27 00:00:33,182 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285180.13, 'new_value': 289725.13}, {'field': 'total_amount', 'old_value': 324540.13, 'new_value': 329085.13}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-05-27 00:00:33,182 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-27 00:00:33,588 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-27 00:00:33,588 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31846.2, 'new_value': 32184.2}, {'field': 'offline_amount', 'old_value': 167762.22, 'new_value': 173658.02}, {'field': 'total_amount', 'old_value': 199608.42, 'new_value': 205842.22}, {'field': 'order_count', 'old_value': 258, 'new_value': 268}]
2025-05-27 00:00:33,588 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-27 00:00:34,041 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-27 00:00:34,041 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81102.0, 'new_value': 83919.0}, {'field': 'total_amount', 'old_value': 81102.0, 'new_value': 83919.0}, {'field': 'order_count', 'old_value': 695, 'new_value': 717}]
2025-05-27 00:00:34,041 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-27 00:00:34,510 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-27 00:00:34,510 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41495.0, 'new_value': 42835.0}, {'field': 'total_amount', 'old_value': 46871.0, 'new_value': 48211.0}, {'field': 'order_count', 'old_value': 213, 'new_value': 221}]
2025-05-27 00:00:34,510 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-27 00:00:34,916 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-27 00:00:34,916 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71409.44, 'new_value': 71686.86}, {'field': 'total_amount', 'old_value': 71409.44, 'new_value': 71686.86}, {'field': 'order_count', 'old_value': 125, 'new_value': 126}]
2025-05-27 00:00:34,916 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-27 00:00:35,338 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-27 00:00:35,338 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10253.0, 'new_value': 10433.0}, {'field': 'total_amount', 'old_value': 10253.0, 'new_value': 10433.0}, {'field': 'order_count', 'old_value': 305, 'new_value': 306}]
2025-05-27 00:00:35,338 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-27 00:00:35,729 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-27 00:00:35,729 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50928.3, 'new_value': 52008.8}, {'field': 'total_amount', 'old_value': 50940.2, 'new_value': 52020.7}, {'field': 'order_count', 'old_value': 297, 'new_value': 311}]
2025-05-27 00:00:35,729 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-27 00:00:36,135 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-27 00:00:36,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32074.0, 'new_value': 33414.0}, {'field': 'total_amount', 'old_value': 33774.0, 'new_value': 35114.0}, {'field': 'order_count', 'old_value': 123, 'new_value': 128}]
2025-05-27 00:00:36,135 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-27 00:00:36,510 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-27 00:00:36,510 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7321.1, 'new_value': 7795.77}, {'field': 'offline_amount', 'old_value': 109222.66, 'new_value': 111342.12}, {'field': 'total_amount', 'old_value': 116543.76, 'new_value': 119137.89}, {'field': 'order_count', 'old_value': 2819, 'new_value': 2879}]
2025-05-27 00:00:36,510 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-27 00:00:36,947 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-27 00:00:36,947 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52679.93, 'new_value': 54298.78}, {'field': 'offline_amount', 'old_value': 692229.76, 'new_value': 710253.26}, {'field': 'total_amount', 'old_value': 744909.69, 'new_value': 764552.04}, {'field': 'order_count', 'old_value': 3098, 'new_value': 3169}]
2025-05-27 00:00:36,947 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-27 00:00:37,385 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-27 00:00:37,385 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82412.55, 'new_value': 86988.23}, {'field': 'offline_amount', 'old_value': 41026.13, 'new_value': 42694.53}, {'field': 'total_amount', 'old_value': 123438.68, 'new_value': 129682.76}, {'field': 'order_count', 'old_value': 4266, 'new_value': 4481}]
2025-05-27 00:00:37,385 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-27 00:00:37,791 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-27 00:00:37,791 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60615.56, 'new_value': 62483.74}, {'field': 'offline_amount', 'old_value': 102000.16, 'new_value': 105784.81}, {'field': 'total_amount', 'old_value': 162615.72, 'new_value': 168268.55}, {'field': 'order_count', 'old_value': 5605, 'new_value': 5813}]
2025-05-27 00:00:37,791 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-27 00:00:38,213 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-27 00:00:38,213 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 124457.0, 'new_value': 125856.0}, {'field': 'total_amount', 'old_value': 200628.0, 'new_value': 202027.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 76}]
2025-05-27 00:00:38,213 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-27 00:00:38,635 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-27 00:00:38,635 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26385.79, 'new_value': 31545.79}, {'field': 'total_amount', 'old_value': 59954.01, 'new_value': 65114.01}, {'field': 'order_count', 'old_value': 61, 'new_value': 64}]
2025-05-27 00:00:38,635 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-27 00:00:39,072 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-27 00:00:39,072 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 324366.0, 'new_value': 336098.0}, {'field': 'total_amount', 'old_value': 324366.0, 'new_value': 336098.0}, {'field': 'order_count', 'old_value': 197, 'new_value': 207}]
2025-05-27 00:00:39,072 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-27 00:00:39,510 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-27 00:00:39,510 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4537.87, 'new_value': 4389.18}, {'field': 'offline_amount', 'old_value': 131372.49, 'new_value': 137212.99}, {'field': 'total_amount', 'old_value': 135910.36, 'new_value': 141602.17}, {'field': 'order_count', 'old_value': 628, 'new_value': 651}]
2025-05-27 00:00:39,510 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-27 00:00:39,885 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-27 00:00:39,900 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6826.46, 'new_value': 7069.58}, {'field': 'offline_amount', 'old_value': 204327.78, 'new_value': 212193.77}, {'field': 'total_amount', 'old_value': 211154.24, 'new_value': 219263.35}, {'field': 'order_count', 'old_value': 1333, 'new_value': 1366}]
2025-05-27 00:00:39,900 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-27 00:00:40,275 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-27 00:00:40,291 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24589.5, 'new_value': 24688.5}, {'field': 'total_amount', 'old_value': 24655.05, 'new_value': 24754.05}, {'field': 'order_count', 'old_value': 226, 'new_value': 227}]
2025-05-27 00:00:40,291 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-27 00:00:40,682 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-27 00:00:40,682 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156211.0, 'new_value': 164490.0}, {'field': 'total_amount', 'old_value': 189957.15, 'new_value': 198236.15}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-27 00:00:40,682 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-27 00:00:41,057 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-27 00:00:41,057 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55620.0, 'new_value': 58543.0}, {'field': 'total_amount', 'old_value': 55968.0, 'new_value': 58891.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 120}]
2025-05-27 00:00:41,057 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-27 00:00:41,432 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-27 00:00:41,432 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 841814.0, 'new_value': 880154.0}, {'field': 'total_amount', 'old_value': 841814.0, 'new_value': 880154.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 159}]
2025-05-27 00:00:41,432 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-27 00:00:41,838 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-27 00:00:41,838 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 246581.3, 'new_value': 252939.31}, {'field': 'offline_amount', 'old_value': 108408.55, 'new_value': 110306.74}, {'field': 'total_amount', 'old_value': 354989.85, 'new_value': 363246.05}, {'field': 'order_count', 'old_value': 1406, 'new_value': 1449}]
2025-05-27 00:00:41,838 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-27 00:00:42,244 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-27 00:00:42,244 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22203.1, 'new_value': 23521.19}, {'field': 'offline_amount', 'old_value': 307600.05, 'new_value': 312288.56}, {'field': 'total_amount', 'old_value': 329803.15, 'new_value': 335809.75}, {'field': 'order_count', 'old_value': 1559, 'new_value': 1585}]
2025-05-27 00:00:42,244 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-27 00:00:42,635 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-27 00:00:42,635 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37797.78, 'new_value': 39483.52}, {'field': 'offline_amount', 'old_value': 564687.59, 'new_value': 576655.59}, {'field': 'total_amount', 'old_value': 602485.37, 'new_value': 616139.11}, {'field': 'order_count', 'old_value': 3238, 'new_value': 3294}]
2025-05-27 00:00:42,635 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-27 00:00:43,057 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-27 00:00:43,057 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93949.52, 'new_value': 97173.12}, {'field': 'offline_amount', 'old_value': 1158573.85, 'new_value': 1205075.89}, {'field': 'total_amount', 'old_value': 1252523.37, 'new_value': 1302249.01}, {'field': 'order_count', 'old_value': 10113, 'new_value': 10536}]
2025-05-27 00:00:43,057 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-27 00:00:43,572 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-27 00:00:43,572 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83045.65, 'new_value': 84763.59}, {'field': 'total_amount', 'old_value': 83045.65, 'new_value': 84763.59}, {'field': 'order_count', 'old_value': 474, 'new_value': 487}]
2025-05-27 00:00:43,572 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-27 00:00:43,979 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-27 00:00:43,979 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28924.4, 'new_value': 29543.0}, {'field': 'offline_amount', 'old_value': 22121.5, 'new_value': 22684.5}, {'field': 'total_amount', 'old_value': 51045.9, 'new_value': 52227.5}, {'field': 'order_count', 'old_value': 277, 'new_value': 282}]
2025-05-27 00:00:43,979 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-27 00:00:44,400 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-27 00:00:44,400 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 316847.8, 'new_value': 331802.4}, {'field': 'total_amount', 'old_value': 316847.8, 'new_value': 331802.4}, {'field': 'order_count', 'old_value': 3367, 'new_value': 3424}]
2025-05-27 00:00:44,400 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-27 00:00:44,838 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-27 00:00:44,838 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1764408.13, 'new_value': 1809597.06}, {'field': 'total_amount', 'old_value': 1764408.13, 'new_value': 1809597.06}, {'field': 'order_count', 'old_value': 14938, 'new_value': 15581}]
2025-05-27 00:00:44,838 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-27 00:00:45,260 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-27 00:00:45,260 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50673.5, 'new_value': 52508.15}, {'field': 'offline_amount', 'old_value': 35021.0, 'new_value': 36276.0}, {'field': 'total_amount', 'old_value': 85694.5, 'new_value': 88784.15}, {'field': 'order_count', 'old_value': 1066, 'new_value': 1106}]
2025-05-27 00:00:45,260 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-27 00:00:45,666 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-27 00:00:45,666 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235258.36, 'new_value': 238098.26}, {'field': 'total_amount', 'old_value': 235258.36, 'new_value': 238098.26}, {'field': 'order_count', 'old_value': 430, 'new_value': 438}]
2025-05-27 00:00:45,666 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-27 00:00:46,072 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-27 00:00:46,072 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108744.68, 'new_value': 121917.1}, {'field': 'total_amount', 'old_value': 623883.52, 'new_value': 637055.94}, {'field': 'order_count', 'old_value': 2508, 'new_value': 2545}]
2025-05-27 00:00:46,072 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-27 00:00:46,494 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-27 00:00:46,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126724.0, 'new_value': 141258.0}, {'field': 'total_amount', 'old_value': 126724.0, 'new_value': 141258.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-05-27 00:00:46,494 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-27 00:00:46,932 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-27 00:00:46,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 181756.0, 'new_value': 183111.0}, {'field': 'total_amount', 'old_value': 181889.0, 'new_value': 183244.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 142}]
2025-05-27 00:00:46,932 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-27 00:00:47,400 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-27 00:00:47,400 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95753.12, 'new_value': 98760.47}, {'field': 'offline_amount', 'old_value': 69465.11, 'new_value': 72548.66}, {'field': 'total_amount', 'old_value': 165218.23, 'new_value': 171309.13}, {'field': 'order_count', 'old_value': 7047, 'new_value': 7282}]
2025-05-27 00:00:47,400 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-27 00:00:47,822 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-27 00:00:47,822 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107471.58, 'new_value': 109372.58}, {'field': 'total_amount', 'old_value': 113083.1, 'new_value': 114984.1}, {'field': 'order_count', 'old_value': 10397, 'new_value': 10766}]
2025-05-27 00:00:47,822 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-27 00:00:48,228 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-27 00:00:48,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107220.0, 'new_value': 111210.0}, {'field': 'total_amount', 'old_value': 107220.0, 'new_value': 111210.0}, {'field': 'order_count', 'old_value': 5269, 'new_value': 5397}]
2025-05-27 00:00:48,228 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-27 00:00:48,650 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-27 00:00:48,650 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71005.0, 'new_value': 76090.0}, {'field': 'total_amount', 'old_value': 71005.0, 'new_value': 76090.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 111}]
2025-05-27 00:00:48,650 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-27 00:00:49,041 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-27 00:00:49,041 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 388321.58, 'new_value': 393554.84}, {'field': 'total_amount', 'old_value': 388321.58, 'new_value': 393554.84}, {'field': 'order_count', 'old_value': 1916, 'new_value': 1952}]
2025-05-27 00:00:49,057 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-27 00:00:49,478 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-27 00:00:49,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68029.3, 'new_value': 69410.3}, {'field': 'total_amount', 'old_value': 68029.3, 'new_value': 69410.3}, {'field': 'order_count', 'old_value': 1635, 'new_value': 1640}]
2025-05-27 00:00:49,478 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-27 00:00:49,947 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-27 00:00:49,947 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 877259.0, 'new_value': 892594.0}, {'field': 'total_amount', 'old_value': 877259.0, 'new_value': 892594.0}, {'field': 'order_count', 'old_value': 123, 'new_value': 125}]
2025-05-27 00:00:49,947 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-27 00:00:50,353 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-27 00:00:50,353 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11865.0, 'new_value': 12083.0}, {'field': 'total_amount', 'old_value': 11865.0, 'new_value': 12083.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 87}]
2025-05-27 00:00:50,353 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-27 00:00:50,838 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-27 00:00:50,838 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2229.0, 'new_value': 2264.0}, {'field': 'offline_amount', 'old_value': 608776.0, 'new_value': 614884.0}, {'field': 'total_amount', 'old_value': 611005.0, 'new_value': 617148.0}, {'field': 'order_count', 'old_value': 277, 'new_value': 281}]
2025-05-27 00:00:50,838 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-27 00:00:51,275 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-27 00:00:51,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196912.0, 'new_value': 200392.0}, {'field': 'total_amount', 'old_value': 196912.0, 'new_value': 200392.0}, {'field': 'order_count', 'old_value': 3356, 'new_value': 3362}]
2025-05-27 00:00:51,291 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-27 00:00:51,697 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-27 00:00:51,697 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285823.0, 'new_value': 307984.0}, {'field': 'total_amount', 'old_value': 285823.0, 'new_value': 307984.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 81}]
2025-05-27 00:00:51,697 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-27 00:00:52,119 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-27 00:00:52,119 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86826.64, 'new_value': 90339.38}, {'field': 'offline_amount', 'old_value': 220949.38, 'new_value': 229666.77}, {'field': 'total_amount', 'old_value': 307776.02, 'new_value': 320006.15}, {'field': 'order_count', 'old_value': 15121, 'new_value': 15804}]
2025-05-27 00:00:52,135 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-27 00:00:52,603 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-27 00:00:52,603 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 744723.25, 'new_value': 764357.14}, {'field': 'total_amount', 'old_value': 744723.25, 'new_value': 764357.14}, {'field': 'order_count', 'old_value': 5207, 'new_value': 5356}]
2025-05-27 00:00:52,603 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-27 00:00:52,994 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-27 00:00:52,994 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76924.0, 'new_value': 79977.0}, {'field': 'total_amount', 'old_value': 76924.0, 'new_value': 79977.0}, {'field': 'order_count', 'old_value': 197, 'new_value': 214}]
2025-05-27 00:00:52,994 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-27 00:00:53,369 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-27 00:00:53,369 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130722.7, 'new_value': 135013.8}, {'field': 'total_amount', 'old_value': 130722.7, 'new_value': 135013.8}, {'field': 'order_count', 'old_value': 255, 'new_value': 263}]
2025-05-27 00:00:53,369 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-27 00:00:53,838 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-27 00:00:53,838 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 120888.7, 'new_value': 122706.7}, {'field': 'offline_amount', 'old_value': 78646.98, 'new_value': 79429.88}, {'field': 'total_amount', 'old_value': 199535.68, 'new_value': 202136.58}, {'field': 'order_count', 'old_value': 1335, 'new_value': 1359}]
2025-05-27 00:00:53,838 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-27 00:00:54,275 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-27 00:00:54,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 918388.0, 'new_value': 955559.0}, {'field': 'total_amount', 'old_value': 918388.0, 'new_value': 955559.0}, {'field': 'order_count', 'old_value': 51010, 'new_value': 51046}]
2025-05-27 00:00:54,275 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-27 00:00:54,713 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-27 00:00:54,713 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 254883.74, 'new_value': 259952.74}, {'field': 'total_amount', 'old_value': 254883.74, 'new_value': 259952.74}, {'field': 'order_count', 'old_value': 1557, 'new_value': 1591}]
2025-05-27 00:00:54,713 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-27 00:00:55,103 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-27 00:00:55,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 296989.0, 'new_value': 299259.0}, {'field': 'total_amount', 'old_value': 301289.0, 'new_value': 303559.0}, {'field': 'order_count', 'old_value': 210, 'new_value': 214}]
2025-05-27 00:00:55,103 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-27 00:00:55,525 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-27 00:00:55,525 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 240034.7, 'new_value': 247641.84}, {'field': 'offline_amount', 'old_value': 750092.57, 'new_value': 760557.36}, {'field': 'total_amount', 'old_value': 990127.27, 'new_value': 1008199.2}, {'field': 'order_count', 'old_value': 5904, 'new_value': 6040}]
2025-05-27 00:00:55,525 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-27 00:00:55,947 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-27 00:00:55,947 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32062.66, 'new_value': 33627.84}, {'field': 'offline_amount', 'old_value': 359652.84, 'new_value': 366411.64}, {'field': 'total_amount', 'old_value': 391715.5, 'new_value': 400039.48}, {'field': 'order_count', 'old_value': 9882, 'new_value': 9944}]
2025-05-27 00:00:55,947 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-27 00:00:56,275 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-27 00:00:56,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25520.0, 'new_value': 27478.0}, {'field': 'total_amount', 'old_value': 25520.0, 'new_value': 27478.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-27 00:00:56,275 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-27 00:00:56,666 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-27 00:00:56,666 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57480.9, 'new_value': 58950.74}, {'field': 'offline_amount', 'old_value': 45793.95, 'new_value': 46420.2}, {'field': 'total_amount', 'old_value': 103274.85, 'new_value': 105370.94}, {'field': 'order_count', 'old_value': 8644, 'new_value': 8840}]
2025-05-27 00:00:56,666 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-27 00:00:57,088 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-27 00:00:57,088 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 30000.0}, {'field': 'offline_amount', 'old_value': 569166.0, 'new_value': 583943.0}, {'field': 'total_amount', 'old_value': 569166.0, 'new_value': 613943.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 95}]
2025-05-27 00:00:57,088 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-27 00:00:57,463 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-27 00:00:57,463 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 614779.0, 'new_value': 627005.0}, {'field': 'total_amount', 'old_value': 614779.0, 'new_value': 627005.0}, {'field': 'order_count', 'old_value': 487, 'new_value': 495}]
2025-05-27 00:00:57,463 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-27 00:00:57,885 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-27 00:00:57,885 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23171.3, 'new_value': 23374.5}, {'field': 'offline_amount', 'old_value': 183024.4, 'new_value': 188227.1}, {'field': 'total_amount', 'old_value': 206195.7, 'new_value': 211601.6}, {'field': 'order_count', 'old_value': 6441, 'new_value': 6672}]
2025-05-27 00:00:57,885 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-27 00:00:58,307 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-27 00:00:58,307 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4724.0, 'new_value': 5023.0}, {'field': 'offline_amount', 'old_value': 426973.0, 'new_value': 473883.0}, {'field': 'total_amount', 'old_value': 431697.0, 'new_value': 478906.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 96}]
2025-05-27 00:00:58,307 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-27 00:00:58,713 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-27 00:00:58,713 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4857.88, 'new_value': 5634.63}, {'field': 'offline_amount', 'old_value': 48543.03, 'new_value': 51709.34}, {'field': 'total_amount', 'old_value': 53400.91, 'new_value': 57343.97}, {'field': 'order_count', 'old_value': 2110, 'new_value': 2299}]
2025-05-27 00:00:58,713 - INFO - 日期 2025-05 处理完成 - 更新: 71 条，插入: 0 条，错误: 0 条
2025-05-27 00:00:58,713 - INFO - 数据同步完成！更新: 71 条，插入: 0 条，错误: 0 条
2025-05-27 00:00:58,713 - INFO - =================同步完成====================
2025-05-27 03:00:02,050 - INFO - =================使用默认全量同步=============
2025-05-27 03:00:03,550 - INFO - MySQL查询成功，共获取 3302 条记录
2025-05-27 03:00:03,550 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-27 03:00:03,566 - INFO - 开始处理日期: 2025-01
2025-05-27 03:00:03,581 - INFO - Request Parameters - Page 1:
2025-05-27 03:00:03,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:03,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:04,691 - INFO - Response - Page 1:
2025-05-27 03:00:04,894 - INFO - 第 1 页获取到 100 条记录
2025-05-27 03:00:04,894 - INFO - Request Parameters - Page 2:
2025-05-27 03:00:04,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:04,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:05,894 - INFO - Response - Page 2:
2025-05-27 03:00:06,097 - INFO - 第 2 页获取到 100 条记录
2025-05-27 03:00:06,097 - INFO - Request Parameters - Page 3:
2025-05-27 03:00:06,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:06,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:06,612 - INFO - Response - Page 3:
2025-05-27 03:00:06,816 - INFO - 第 3 页获取到 100 条记录
2025-05-27 03:00:06,816 - INFO - Request Parameters - Page 4:
2025-05-27 03:00:06,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:06,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:07,316 - INFO - Response - Page 4:
2025-05-27 03:00:07,519 - INFO - 第 4 页获取到 100 条记录
2025-05-27 03:00:07,519 - INFO - Request Parameters - Page 5:
2025-05-27 03:00:07,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:07,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:07,987 - INFO - Response - Page 5:
2025-05-27 03:00:08,191 - INFO - 第 5 页获取到 100 条记录
2025-05-27 03:00:08,191 - INFO - Request Parameters - Page 6:
2025-05-27 03:00:08,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:08,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:08,644 - INFO - Response - Page 6:
2025-05-27 03:00:08,847 - INFO - 第 6 页获取到 100 条记录
2025-05-27 03:00:08,847 - INFO - Request Parameters - Page 7:
2025-05-27 03:00:08,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:08,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:09,253 - INFO - Response - Page 7:
2025-05-27 03:00:09,456 - INFO - 第 7 页获取到 82 条记录
2025-05-27 03:00:09,456 - INFO - 查询完成，共获取到 682 条记录
2025-05-27 03:00:09,456 - INFO - 获取到 682 条表单数据
2025-05-27 03:00:09,456 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-27 03:00:09,472 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 03:00:09,472 - INFO - 开始处理日期: 2025-02
2025-05-27 03:00:09,472 - INFO - Request Parameters - Page 1:
2025-05-27 03:00:09,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:09,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:09,972 - INFO - Response - Page 1:
2025-05-27 03:00:10,175 - INFO - 第 1 页获取到 100 条记录
2025-05-27 03:00:10,175 - INFO - Request Parameters - Page 2:
2025-05-27 03:00:10,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:10,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:10,659 - INFO - Response - Page 2:
2025-05-27 03:00:10,862 - INFO - 第 2 页获取到 100 条记录
2025-05-27 03:00:10,862 - INFO - Request Parameters - Page 3:
2025-05-27 03:00:10,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:10,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:11,394 - INFO - Response - Page 3:
2025-05-27 03:00:11,597 - INFO - 第 3 页获取到 100 条记录
2025-05-27 03:00:11,597 - INFO - Request Parameters - Page 4:
2025-05-27 03:00:11,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:11,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:12,253 - INFO - Response - Page 4:
2025-05-27 03:00:12,456 - INFO - 第 4 页获取到 100 条记录
2025-05-27 03:00:12,456 - INFO - Request Parameters - Page 5:
2025-05-27 03:00:12,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:12,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:13,019 - INFO - Response - Page 5:
2025-05-27 03:00:13,222 - INFO - 第 5 页获取到 100 条记录
2025-05-27 03:00:13,222 - INFO - Request Parameters - Page 6:
2025-05-27 03:00:13,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:13,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:13,800 - INFO - Response - Page 6:
2025-05-27 03:00:14,003 - INFO - 第 6 页获取到 100 条记录
2025-05-27 03:00:14,003 - INFO - Request Parameters - Page 7:
2025-05-27 03:00:14,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:14,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:14,441 - INFO - Response - Page 7:
2025-05-27 03:00:14,644 - INFO - 第 7 页获取到 70 条记录
2025-05-27 03:00:14,644 - INFO - 查询完成，共获取到 670 条记录
2025-05-27 03:00:14,644 - INFO - 获取到 670 条表单数据
2025-05-27 03:00:14,644 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-27 03:00:14,659 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 03:00:14,659 - INFO - 开始处理日期: 2025-03
2025-05-27 03:00:14,659 - INFO - Request Parameters - Page 1:
2025-05-27 03:00:14,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:14,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:15,144 - INFO - Response - Page 1:
2025-05-27 03:00:15,347 - INFO - 第 1 页获取到 100 条记录
2025-05-27 03:00:15,347 - INFO - Request Parameters - Page 2:
2025-05-27 03:00:15,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:15,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:15,784 - INFO - Response - Page 2:
2025-05-27 03:00:15,987 - INFO - 第 2 页获取到 100 条记录
2025-05-27 03:00:15,987 - INFO - Request Parameters - Page 3:
2025-05-27 03:00:15,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:15,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:16,441 - INFO - Response - Page 3:
2025-05-27 03:00:16,644 - INFO - 第 3 页获取到 100 条记录
2025-05-27 03:00:16,644 - INFO - Request Parameters - Page 4:
2025-05-27 03:00:16,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:16,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:17,112 - INFO - Response - Page 4:
2025-05-27 03:00:17,316 - INFO - 第 4 页获取到 100 条记录
2025-05-27 03:00:17,316 - INFO - Request Parameters - Page 5:
2025-05-27 03:00:17,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:17,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:17,816 - INFO - Response - Page 5:
2025-05-27 03:00:18,019 - INFO - 第 5 页获取到 100 条记录
2025-05-27 03:00:18,019 - INFO - Request Parameters - Page 6:
2025-05-27 03:00:18,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:18,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:18,503 - INFO - Response - Page 6:
2025-05-27 03:00:18,706 - INFO - 第 6 页获取到 100 条记录
2025-05-27 03:00:18,706 - INFO - Request Parameters - Page 7:
2025-05-27 03:00:18,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:18,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:19,159 - INFO - Response - Page 7:
2025-05-27 03:00:19,362 - INFO - 第 7 页获取到 61 条记录
2025-05-27 03:00:19,362 - INFO - 查询完成，共获取到 661 条记录
2025-05-27 03:00:19,362 - INFO - 获取到 661 条表单数据
2025-05-27 03:00:19,362 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-27 03:00:19,378 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 03:00:19,378 - INFO - 开始处理日期: 2025-04
2025-05-27 03:00:19,378 - INFO - Request Parameters - Page 1:
2025-05-27 03:00:19,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:19,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:19,925 - INFO - Response - Page 1:
2025-05-27 03:00:20,128 - INFO - 第 1 页获取到 100 条记录
2025-05-27 03:00:20,128 - INFO - Request Parameters - Page 2:
2025-05-27 03:00:20,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:20,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:20,566 - INFO - Response - Page 2:
2025-05-27 03:00:20,769 - INFO - 第 2 页获取到 100 条记录
2025-05-27 03:00:20,769 - INFO - Request Parameters - Page 3:
2025-05-27 03:00:20,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:20,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:21,331 - INFO - Response - Page 3:
2025-05-27 03:00:21,534 - INFO - 第 3 页获取到 100 条记录
2025-05-27 03:00:21,534 - INFO - Request Parameters - Page 4:
2025-05-27 03:00:21,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:21,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:22,112 - INFO - Response - Page 4:
2025-05-27 03:00:22,316 - INFO - 第 4 页获取到 100 条记录
2025-05-27 03:00:22,316 - INFO - Request Parameters - Page 5:
2025-05-27 03:00:22,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:22,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:22,800 - INFO - Response - Page 5:
2025-05-27 03:00:23,003 - INFO - 第 5 页获取到 100 条记录
2025-05-27 03:00:23,003 - INFO - Request Parameters - Page 6:
2025-05-27 03:00:23,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:23,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:23,519 - INFO - Response - Page 6:
2025-05-27 03:00:23,722 - INFO - 第 6 页获取到 100 条记录
2025-05-27 03:00:23,722 - INFO - Request Parameters - Page 7:
2025-05-27 03:00:23,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:23,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:24,112 - INFO - Response - Page 7:
2025-05-27 03:00:24,315 - INFO - 第 7 页获取到 56 条记录
2025-05-27 03:00:24,315 - INFO - 查询完成，共获取到 656 条记录
2025-05-27 03:00:24,315 - INFO - 获取到 656 条表单数据
2025-05-27 03:00:24,315 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-27 03:00:24,331 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 03:00:24,331 - INFO - 开始处理日期: 2025-05
2025-05-27 03:00:24,331 - INFO - Request Parameters - Page 1:
2025-05-27 03:00:24,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:24,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:24,800 - INFO - Response - Page 1:
2025-05-27 03:00:25,003 - INFO - 第 1 页获取到 100 条记录
2025-05-27 03:00:25,003 - INFO - Request Parameters - Page 2:
2025-05-27 03:00:25,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:25,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:25,675 - INFO - Response - Page 2:
2025-05-27 03:00:25,878 - INFO - 第 2 页获取到 100 条记录
2025-05-27 03:00:25,878 - INFO - Request Parameters - Page 3:
2025-05-27 03:00:25,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:25,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:26,425 - INFO - Response - Page 3:
2025-05-27 03:00:26,628 - INFO - 第 3 页获取到 100 条记录
2025-05-27 03:00:26,628 - INFO - Request Parameters - Page 4:
2025-05-27 03:00:26,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:26,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:27,128 - INFO - Response - Page 4:
2025-05-27 03:00:27,331 - INFO - 第 4 页获取到 100 条记录
2025-05-27 03:00:27,331 - INFO - Request Parameters - Page 5:
2025-05-27 03:00:27,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:27,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:27,784 - INFO - Response - Page 5:
2025-05-27 03:00:27,987 - INFO - 第 5 页获取到 100 条记录
2025-05-27 03:00:27,987 - INFO - Request Parameters - Page 6:
2025-05-27 03:00:27,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:27,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:28,503 - INFO - Response - Page 6:
2025-05-27 03:00:28,706 - INFO - 第 6 页获取到 100 条记录
2025-05-27 03:00:28,706 - INFO - Request Parameters - Page 7:
2025-05-27 03:00:28,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 03:00:28,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 03:00:29,034 - INFO - Response - Page 7:
2025-05-27 03:00:29,237 - INFO - 第 7 页获取到 33 条记录
2025-05-27 03:00:29,237 - INFO - 查询完成，共获取到 633 条记录
2025-05-27 03:00:29,237 - INFO - 获取到 633 条表单数据
2025-05-27 03:00:29,237 - INFO - 当前日期 2025-05 有 633 条MySQL数据需要处理
2025-05-27 03:00:29,253 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-27 03:00:29,784 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-27 03:00:29,784 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 688970.9, 'new_value': 699551.9}, {'field': 'total_amount', 'old_value': 688970.9, 'new_value': 699551.9}, {'field': 'order_count', 'old_value': 1953, 'new_value': 1999}]
2025-05-27 03:00:29,784 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-27 03:00:30,284 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-27 03:00:30,284 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 740647.0, 'new_value': 741854.0}, {'field': 'total_amount', 'old_value': 740647.0, 'new_value': 741854.0}, {'field': 'order_count', 'old_value': 179, 'new_value': 180}]
2025-05-27 03:00:30,284 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-27 03:00:30,628 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-27 03:00:30,628 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10200.01, 'new_value': 10309.91}, {'field': 'total_amount', 'old_value': 27500.01, 'new_value': 27609.91}, {'field': 'order_count', 'old_value': 158, 'new_value': 159}]
2025-05-27 03:00:30,628 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-27 03:00:31,081 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-27 03:00:31,081 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 328182.81, 'new_value': 332653.74}, {'field': 'offline_amount', 'old_value': 1292865.64, 'new_value': 1321468.68}, {'field': 'total_amount', 'old_value': 1621048.45, 'new_value': 1654122.42}, {'field': 'order_count', 'old_value': 8057, 'new_value': 8233}]
2025-05-27 03:00:31,081 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-27 03:00:31,487 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-27 03:00:31,487 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1913159.0, 'new_value': 1949114.0}, {'field': 'total_amount', 'old_value': 1913159.0, 'new_value': 1949114.0}, {'field': 'order_count', 'old_value': 7632, 'new_value': 7782}]
2025-05-27 03:00:31,487 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-27 03:00:31,925 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-27 03:00:31,925 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 282087.58, 'new_value': 283329.58}, {'field': 'total_amount', 'old_value': 282087.58, 'new_value': 283329.58}, {'field': 'order_count', 'old_value': 1606, 'new_value': 1617}]
2025-05-27 03:00:31,925 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-27 03:00:32,425 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-27 03:00:32,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 371049.9, 'new_value': 381734.9}, {'field': 'total_amount', 'old_value': 371049.9, 'new_value': 381734.9}, {'field': 'order_count', 'old_value': 0, 'new_value': 8}]
2025-05-27 03:00:32,425 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-27 03:00:32,847 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-27 03:00:32,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94738.56, 'new_value': 96407.56}, {'field': 'total_amount', 'old_value': 94738.56, 'new_value': 96407.56}, {'field': 'order_count', 'old_value': 4882, 'new_value': 4976}]
2025-05-27 03:00:32,862 - INFO - 日期 2025-05 处理完成 - 更新: 8 条，插入: 0 条，错误: 0 条
2025-05-27 03:00:32,862 - INFO - 数据同步完成！更新: 8 条，插入: 0 条，错误: 0 条
2025-05-27 03:00:32,862 - INFO - =================同步完成====================
2025-05-27 06:00:02,012 - INFO - =================使用默认全量同步=============
2025-05-27 06:00:03,481 - INFO - MySQL查询成功，共获取 3302 条记录
2025-05-27 06:00:03,481 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-27 06:00:03,512 - INFO - 开始处理日期: 2025-01
2025-05-27 06:00:03,512 - INFO - Request Parameters - Page 1:
2025-05-27 06:00:03,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:03,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:04,653 - INFO - Response - Page 1:
2025-05-27 06:00:04,856 - INFO - 第 1 页获取到 100 条记录
2025-05-27 06:00:04,856 - INFO - Request Parameters - Page 2:
2025-05-27 06:00:04,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:04,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:05,418 - INFO - Response - Page 2:
2025-05-27 06:00:05,621 - INFO - 第 2 页获取到 100 条记录
2025-05-27 06:00:05,621 - INFO - Request Parameters - Page 3:
2025-05-27 06:00:05,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:05,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:06,293 - INFO - Response - Page 3:
2025-05-27 06:00:06,496 - INFO - 第 3 页获取到 100 条记录
2025-05-27 06:00:06,496 - INFO - Request Parameters - Page 4:
2025-05-27 06:00:06,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:06,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:07,262 - INFO - Response - Page 4:
2025-05-27 06:00:07,465 - INFO - 第 4 页获取到 100 条记录
2025-05-27 06:00:07,465 - INFO - Request Parameters - Page 5:
2025-05-27 06:00:07,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:07,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:08,012 - INFO - Response - Page 5:
2025-05-27 06:00:08,215 - INFO - 第 5 页获取到 100 条记录
2025-05-27 06:00:08,215 - INFO - Request Parameters - Page 6:
2025-05-27 06:00:08,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:08,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:08,746 - INFO - Response - Page 6:
2025-05-27 06:00:08,949 - INFO - 第 6 页获取到 100 条记录
2025-05-27 06:00:08,949 - INFO - Request Parameters - Page 7:
2025-05-27 06:00:08,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:08,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:09,481 - INFO - Response - Page 7:
2025-05-27 06:00:09,684 - INFO - 第 7 页获取到 82 条记录
2025-05-27 06:00:09,684 - INFO - 查询完成，共获取到 682 条记录
2025-05-27 06:00:09,684 - INFO - 获取到 682 条表单数据
2025-05-27 06:00:09,684 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-27 06:00:09,699 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 06:00:09,699 - INFO - 开始处理日期: 2025-02
2025-05-27 06:00:09,699 - INFO - Request Parameters - Page 1:
2025-05-27 06:00:09,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:09,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:10,246 - INFO - Response - Page 1:
2025-05-27 06:00:10,449 - INFO - 第 1 页获取到 100 条记录
2025-05-27 06:00:10,449 - INFO - Request Parameters - Page 2:
2025-05-27 06:00:10,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:10,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:10,871 - INFO - Response - Page 2:
2025-05-27 06:00:11,074 - INFO - 第 2 页获取到 100 条记录
2025-05-27 06:00:11,074 - INFO - Request Parameters - Page 3:
2025-05-27 06:00:11,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:11,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:11,574 - INFO - Response - Page 3:
2025-05-27 06:00:11,778 - INFO - 第 3 页获取到 100 条记录
2025-05-27 06:00:11,778 - INFO - Request Parameters - Page 4:
2025-05-27 06:00:11,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:11,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:12,262 - INFO - Response - Page 4:
2025-05-27 06:00:12,465 - INFO - 第 4 页获取到 100 条记录
2025-05-27 06:00:12,465 - INFO - Request Parameters - Page 5:
2025-05-27 06:00:12,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:12,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:12,903 - INFO - Response - Page 5:
2025-05-27 06:00:13,106 - INFO - 第 5 页获取到 100 条记录
2025-05-27 06:00:13,106 - INFO - Request Parameters - Page 6:
2025-05-27 06:00:13,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:13,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:13,590 - INFO - Response - Page 6:
2025-05-27 06:00:13,793 - INFO - 第 6 页获取到 100 条记录
2025-05-27 06:00:13,793 - INFO - Request Parameters - Page 7:
2025-05-27 06:00:13,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:13,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:14,231 - INFO - Response - Page 7:
2025-05-27 06:00:14,434 - INFO - 第 7 页获取到 70 条记录
2025-05-27 06:00:14,434 - INFO - 查询完成，共获取到 670 条记录
2025-05-27 06:00:14,434 - INFO - 获取到 670 条表单数据
2025-05-27 06:00:14,434 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-27 06:00:14,449 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 06:00:14,449 - INFO - 开始处理日期: 2025-03
2025-05-27 06:00:14,449 - INFO - Request Parameters - Page 1:
2025-05-27 06:00:14,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:14,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:14,981 - INFO - Response - Page 1:
2025-05-27 06:00:15,184 - INFO - 第 1 页获取到 100 条记录
2025-05-27 06:00:15,184 - INFO - Request Parameters - Page 2:
2025-05-27 06:00:15,184 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:15,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:15,699 - INFO - Response - Page 2:
2025-05-27 06:00:15,903 - INFO - 第 2 页获取到 100 条记录
2025-05-27 06:00:15,903 - INFO - Request Parameters - Page 3:
2025-05-27 06:00:15,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:15,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:16,481 - INFO - Response - Page 3:
2025-05-27 06:00:16,684 - INFO - 第 3 页获取到 100 条记录
2025-05-27 06:00:16,684 - INFO - Request Parameters - Page 4:
2025-05-27 06:00:16,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:16,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:17,184 - INFO - Response - Page 4:
2025-05-27 06:00:17,387 - INFO - 第 4 页获取到 100 条记录
2025-05-27 06:00:17,387 - INFO - Request Parameters - Page 5:
2025-05-27 06:00:17,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:17,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:17,981 - INFO - Response - Page 5:
2025-05-27 06:00:18,184 - INFO - 第 5 页获取到 100 条记录
2025-05-27 06:00:18,184 - INFO - Request Parameters - Page 6:
2025-05-27 06:00:18,184 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:18,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:18,637 - INFO - Response - Page 6:
2025-05-27 06:00:18,840 - INFO - 第 6 页获取到 100 条记录
2025-05-27 06:00:18,840 - INFO - Request Parameters - Page 7:
2025-05-27 06:00:18,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:18,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:19,246 - INFO - Response - Page 7:
2025-05-27 06:00:19,449 - INFO - 第 7 页获取到 61 条记录
2025-05-27 06:00:19,449 - INFO - 查询完成，共获取到 661 条记录
2025-05-27 06:00:19,449 - INFO - 获取到 661 条表单数据
2025-05-27 06:00:19,449 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-27 06:00:19,465 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 06:00:19,465 - INFO - 开始处理日期: 2025-04
2025-05-27 06:00:19,465 - INFO - Request Parameters - Page 1:
2025-05-27 06:00:19,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:19,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:20,028 - INFO - Response - Page 1:
2025-05-27 06:00:20,231 - INFO - 第 1 页获取到 100 条记录
2025-05-27 06:00:20,231 - INFO - Request Parameters - Page 2:
2025-05-27 06:00:20,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:20,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:20,746 - INFO - Response - Page 2:
2025-05-27 06:00:20,949 - INFO - 第 2 页获取到 100 条记录
2025-05-27 06:00:20,949 - INFO - Request Parameters - Page 3:
2025-05-27 06:00:20,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:20,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:21,418 - INFO - Response - Page 3:
2025-05-27 06:00:21,621 - INFO - 第 3 页获取到 100 条记录
2025-05-27 06:00:21,621 - INFO - Request Parameters - Page 4:
2025-05-27 06:00:21,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:21,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:22,121 - INFO - Response - Page 4:
2025-05-27 06:00:22,324 - INFO - 第 4 页获取到 100 条记录
2025-05-27 06:00:22,324 - INFO - Request Parameters - Page 5:
2025-05-27 06:00:22,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:22,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:22,809 - INFO - Response - Page 5:
2025-05-27 06:00:23,012 - INFO - 第 5 页获取到 100 条记录
2025-05-27 06:00:23,012 - INFO - Request Parameters - Page 6:
2025-05-27 06:00:23,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:23,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:23,496 - INFO - Response - Page 6:
2025-05-27 06:00:23,699 - INFO - 第 6 页获取到 100 条记录
2025-05-27 06:00:23,699 - INFO - Request Parameters - Page 7:
2025-05-27 06:00:23,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:23,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:24,121 - INFO - Response - Page 7:
2025-05-27 06:00:24,324 - INFO - 第 7 页获取到 56 条记录
2025-05-27 06:00:24,324 - INFO - 查询完成，共获取到 656 条记录
2025-05-27 06:00:24,324 - INFO - 获取到 656 条表单数据
2025-05-27 06:00:24,324 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-27 06:00:24,340 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 06:00:24,340 - INFO - 开始处理日期: 2025-05
2025-05-27 06:00:24,340 - INFO - Request Parameters - Page 1:
2025-05-27 06:00:24,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:24,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:24,840 - INFO - Response - Page 1:
2025-05-27 06:00:25,043 - INFO - 第 1 页获取到 100 条记录
2025-05-27 06:00:25,043 - INFO - Request Parameters - Page 2:
2025-05-27 06:00:25,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:25,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:25,668 - INFO - Response - Page 2:
2025-05-27 06:00:25,871 - INFO - 第 2 页获取到 100 条记录
2025-05-27 06:00:25,871 - INFO - Request Parameters - Page 3:
2025-05-27 06:00:25,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:25,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:26,356 - INFO - Response - Page 3:
2025-05-27 06:00:26,559 - INFO - 第 3 页获取到 100 条记录
2025-05-27 06:00:26,559 - INFO - Request Parameters - Page 4:
2025-05-27 06:00:26,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:26,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:27,184 - INFO - Response - Page 4:
2025-05-27 06:00:27,387 - INFO - 第 4 页获取到 100 条记录
2025-05-27 06:00:27,387 - INFO - Request Parameters - Page 5:
2025-05-27 06:00:27,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:27,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:27,902 - INFO - Response - Page 5:
2025-05-27 06:00:28,106 - INFO - 第 5 页获取到 100 条记录
2025-05-27 06:00:28,106 - INFO - Request Parameters - Page 6:
2025-05-27 06:00:28,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:28,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:28,574 - INFO - Response - Page 6:
2025-05-27 06:00:28,777 - INFO - 第 6 页获取到 100 条记录
2025-05-27 06:00:28,777 - INFO - Request Parameters - Page 7:
2025-05-27 06:00:28,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 06:00:28,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 06:00:29,137 - INFO - Response - Page 7:
2025-05-27 06:00:29,340 - INFO - 第 7 页获取到 33 条记录
2025-05-27 06:00:29,340 - INFO - 查询完成，共获取到 633 条记录
2025-05-27 06:00:29,340 - INFO - 获取到 633 条表单数据
2025-05-27 06:00:29,340 - INFO - 当前日期 2025-05 有 633 条MySQL数据需要处理
2025-05-27 06:00:29,356 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-27 06:00:29,871 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-27 06:00:29,871 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82284.0, 'new_value': 85827.0}, {'field': 'total_amount', 'old_value': 84134.0, 'new_value': 87677.0}, {'field': 'order_count', 'old_value': 473, 'new_value': 490}]
2025-05-27 06:00:29,871 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-27 06:00:29,871 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-27 06:00:29,887 - INFO - =================同步完成====================
2025-05-27 09:00:02,051 - INFO - =================使用默认全量同步=============
2025-05-27 09:00:03,505 - INFO - MySQL查询成功，共获取 3302 条记录
2025-05-27 09:00:03,505 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-27 09:00:03,536 - INFO - 开始处理日期: 2025-01
2025-05-27 09:00:03,536 - INFO - Request Parameters - Page 1:
2025-05-27 09:00:03,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:03,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:04,551 - INFO - Response - Page 1:
2025-05-27 09:00:04,755 - INFO - 第 1 页获取到 100 条记录
2025-05-27 09:00:04,755 - INFO - Request Parameters - Page 2:
2025-05-27 09:00:04,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:04,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:05,708 - INFO - Response - Page 2:
2025-05-27 09:00:05,911 - INFO - 第 2 页获取到 100 条记录
2025-05-27 09:00:05,911 - INFO - Request Parameters - Page 3:
2025-05-27 09:00:05,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:05,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:06,395 - INFO - Response - Page 3:
2025-05-27 09:00:06,598 - INFO - 第 3 页获取到 100 条记录
2025-05-27 09:00:06,598 - INFO - Request Parameters - Page 4:
2025-05-27 09:00:06,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:06,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:07,176 - INFO - Response - Page 4:
2025-05-27 09:00:07,379 - INFO - 第 4 页获取到 100 条记录
2025-05-27 09:00:07,379 - INFO - Request Parameters - Page 5:
2025-05-27 09:00:07,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:07,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:07,879 - INFO - Response - Page 5:
2025-05-27 09:00:08,083 - INFO - 第 5 页获取到 100 条记录
2025-05-27 09:00:08,083 - INFO - Request Parameters - Page 6:
2025-05-27 09:00:08,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:08,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:08,614 - INFO - Response - Page 6:
2025-05-27 09:00:08,817 - INFO - 第 6 页获取到 100 条记录
2025-05-27 09:00:08,817 - INFO - Request Parameters - Page 7:
2025-05-27 09:00:08,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:08,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:09,333 - INFO - Response - Page 7:
2025-05-27 09:00:09,536 - INFO - 第 7 页获取到 82 条记录
2025-05-27 09:00:09,536 - INFO - 查询完成，共获取到 682 条记录
2025-05-27 09:00:09,536 - INFO - 获取到 682 条表单数据
2025-05-27 09:00:09,536 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-27 09:00:09,551 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 09:00:09,551 - INFO - 开始处理日期: 2025-02
2025-05-27 09:00:09,551 - INFO - Request Parameters - Page 1:
2025-05-27 09:00:09,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:09,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:10,036 - INFO - Response - Page 1:
2025-05-27 09:00:10,239 - INFO - 第 1 页获取到 100 条记录
2025-05-27 09:00:10,239 - INFO - Request Parameters - Page 2:
2025-05-27 09:00:10,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:10,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:10,770 - INFO - Response - Page 2:
2025-05-27 09:00:10,973 - INFO - 第 2 页获取到 100 条记录
2025-05-27 09:00:10,973 - INFO - Request Parameters - Page 3:
2025-05-27 09:00:10,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:10,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:11,473 - INFO - Response - Page 3:
2025-05-27 09:00:11,676 - INFO - 第 3 页获取到 100 条记录
2025-05-27 09:00:11,676 - INFO - Request Parameters - Page 4:
2025-05-27 09:00:11,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:11,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:12,161 - INFO - Response - Page 4:
2025-05-27 09:00:12,364 - INFO - 第 4 页获取到 100 条记录
2025-05-27 09:00:12,364 - INFO - Request Parameters - Page 5:
2025-05-27 09:00:12,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:12,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:12,879 - INFO - Response - Page 5:
2025-05-27 09:00:13,083 - INFO - 第 5 页获取到 100 条记录
2025-05-27 09:00:13,083 - INFO - Request Parameters - Page 6:
2025-05-27 09:00:13,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:13,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:13,583 - INFO - Response - Page 6:
2025-05-27 09:00:13,786 - INFO - 第 6 页获取到 100 条记录
2025-05-27 09:00:13,786 - INFO - Request Parameters - Page 7:
2025-05-27 09:00:13,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:13,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:14,239 - INFO - Response - Page 7:
2025-05-27 09:00:14,442 - INFO - 第 7 页获取到 70 条记录
2025-05-27 09:00:14,442 - INFO - 查询完成，共获取到 670 条记录
2025-05-27 09:00:14,442 - INFO - 获取到 670 条表单数据
2025-05-27 09:00:14,442 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-27 09:00:14,458 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 09:00:14,458 - INFO - 开始处理日期: 2025-03
2025-05-27 09:00:14,458 - INFO - Request Parameters - Page 1:
2025-05-27 09:00:14,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:14,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:14,973 - INFO - Response - Page 1:
2025-05-27 09:00:15,176 - INFO - 第 1 页获取到 100 条记录
2025-05-27 09:00:15,176 - INFO - Request Parameters - Page 2:
2025-05-27 09:00:15,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:15,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:15,708 - INFO - Response - Page 2:
2025-05-27 09:00:15,911 - INFO - 第 2 页获取到 100 条记录
2025-05-27 09:00:15,911 - INFO - Request Parameters - Page 3:
2025-05-27 09:00:15,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:15,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:16,426 - INFO - Response - Page 3:
2025-05-27 09:00:16,629 - INFO - 第 3 页获取到 100 条记录
2025-05-27 09:00:16,629 - INFO - Request Parameters - Page 4:
2025-05-27 09:00:16,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:16,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:17,239 - INFO - Response - Page 4:
2025-05-27 09:00:17,442 - INFO - 第 4 页获取到 100 条记录
2025-05-27 09:00:17,442 - INFO - Request Parameters - Page 5:
2025-05-27 09:00:17,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:17,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:18,067 - INFO - Response - Page 5:
2025-05-27 09:00:18,270 - INFO - 第 5 页获取到 100 条记录
2025-05-27 09:00:18,270 - INFO - Request Parameters - Page 6:
2025-05-27 09:00:18,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:18,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:18,786 - INFO - Response - Page 6:
2025-05-27 09:00:18,989 - INFO - 第 6 页获取到 100 条记录
2025-05-27 09:00:18,989 - INFO - Request Parameters - Page 7:
2025-05-27 09:00:18,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:18,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:19,364 - INFO - Response - Page 7:
2025-05-27 09:00:19,583 - INFO - 第 7 页获取到 61 条记录
2025-05-27 09:00:19,583 - INFO - 查询完成，共获取到 661 条记录
2025-05-27 09:00:19,583 - INFO - 获取到 661 条表单数据
2025-05-27 09:00:19,583 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-27 09:00:19,598 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 09:00:19,598 - INFO - 开始处理日期: 2025-04
2025-05-27 09:00:19,598 - INFO - Request Parameters - Page 1:
2025-05-27 09:00:19,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:19,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:20,223 - INFO - Response - Page 1:
2025-05-27 09:00:20,426 - INFO - 第 1 页获取到 100 条记录
2025-05-27 09:00:20,426 - INFO - Request Parameters - Page 2:
2025-05-27 09:00:20,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:20,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:20,926 - INFO - Response - Page 2:
2025-05-27 09:00:21,129 - INFO - 第 2 页获取到 100 条记录
2025-05-27 09:00:21,129 - INFO - Request Parameters - Page 3:
2025-05-27 09:00:21,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:21,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:21,614 - INFO - Response - Page 3:
2025-05-27 09:00:21,817 - INFO - 第 3 页获取到 100 条记录
2025-05-27 09:00:21,817 - INFO - Request Parameters - Page 4:
2025-05-27 09:00:21,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:21,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:22,364 - INFO - Response - Page 4:
2025-05-27 09:00:22,567 - INFO - 第 4 页获取到 100 条记录
2025-05-27 09:00:22,567 - INFO - Request Parameters - Page 5:
2025-05-27 09:00:22,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:22,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:23,098 - INFO - Response - Page 5:
2025-05-27 09:00:23,301 - INFO - 第 5 页获取到 100 条记录
2025-05-27 09:00:23,301 - INFO - Request Parameters - Page 6:
2025-05-27 09:00:23,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:23,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:23,801 - INFO - Response - Page 6:
2025-05-27 09:00:24,020 - INFO - 第 6 页获取到 100 条记录
2025-05-27 09:00:24,020 - INFO - Request Parameters - Page 7:
2025-05-27 09:00:24,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:24,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:24,473 - INFO - Response - Page 7:
2025-05-27 09:00:24,676 - INFO - 第 7 页获取到 56 条记录
2025-05-27 09:00:24,676 - INFO - 查询完成，共获取到 656 条记录
2025-05-27 09:00:24,676 - INFO - 获取到 656 条表单数据
2025-05-27 09:00:24,676 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-27 09:00:24,692 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 09:00:24,692 - INFO - 开始处理日期: 2025-05
2025-05-27 09:00:24,692 - INFO - Request Parameters - Page 1:
2025-05-27 09:00:24,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:24,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:25,286 - INFO - Response - Page 1:
2025-05-27 09:00:25,489 - INFO - 第 1 页获取到 100 条记录
2025-05-27 09:00:25,489 - INFO - Request Parameters - Page 2:
2025-05-27 09:00:25,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:25,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:25,957 - INFO - Response - Page 2:
2025-05-27 09:00:26,161 - INFO - 第 2 页获取到 100 条记录
2025-05-27 09:00:26,161 - INFO - Request Parameters - Page 3:
2025-05-27 09:00:26,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:26,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:26,614 - INFO - Response - Page 3:
2025-05-27 09:00:26,817 - INFO - 第 3 页获取到 100 条记录
2025-05-27 09:00:26,817 - INFO - Request Parameters - Page 4:
2025-05-27 09:00:26,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:26,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:27,270 - INFO - Response - Page 4:
2025-05-27 09:00:27,473 - INFO - 第 4 页获取到 100 条记录
2025-05-27 09:00:27,473 - INFO - Request Parameters - Page 5:
2025-05-27 09:00:27,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:27,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:27,958 - INFO - Response - Page 5:
2025-05-27 09:00:28,161 - INFO - 第 5 页获取到 100 条记录
2025-05-27 09:00:28,161 - INFO - Request Parameters - Page 6:
2025-05-27 09:00:28,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:28,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:28,614 - INFO - Response - Page 6:
2025-05-27 09:00:28,817 - INFO - 第 6 页获取到 100 条记录
2025-05-27 09:00:28,817 - INFO - Request Parameters - Page 7:
2025-05-27 09:00:28,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 09:00:28,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 09:00:29,176 - INFO - Response - Page 7:
2025-05-27 09:00:29,379 - INFO - 第 7 页获取到 33 条记录
2025-05-27 09:00:29,379 - INFO - 查询完成，共获取到 633 条记录
2025-05-27 09:00:29,379 - INFO - 获取到 633 条表单数据
2025-05-27 09:00:29,379 - INFO - 当前日期 2025-05 有 633 条MySQL数据需要处理
2025-05-27 09:00:29,379 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-27 09:00:29,770 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-27 09:00:29,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71046.0, 'new_value': 81564.0}, {'field': 'total_amount', 'old_value': 73943.0, 'new_value': 84461.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 40}]
2025-05-27 09:00:29,770 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-27 09:00:30,176 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-27 09:00:30,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53344.78, 'new_value': 54653.78}, {'field': 'total_amount', 'old_value': 53344.78, 'new_value': 54653.78}, {'field': 'order_count', 'old_value': 126, 'new_value': 130}]
2025-05-27 09:00:30,176 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-27 09:00:30,676 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-27 09:00:30,676 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112577.0, 'new_value': 118225.0}, {'field': 'total_amount', 'old_value': 112577.0, 'new_value': 118225.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 132}]
2025-05-27 09:00:30,676 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-27 09:00:31,114 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-27 09:00:31,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51740.0, 'new_value': 53380.0}, {'field': 'total_amount', 'old_value': 55860.0, 'new_value': 57500.0}, {'field': 'order_count', 'old_value': 536, 'new_value': 554}]
2025-05-27 09:00:31,114 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-27 09:00:31,629 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-27 09:00:31,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60332.0, 'new_value': 67057.0}, {'field': 'total_amount', 'old_value': 60332.0, 'new_value': 67057.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-27 09:00:31,629 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-27 09:00:32,098 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-27 09:00:32,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 289250.0, 'new_value': 293050.0}, {'field': 'total_amount', 'old_value': 289250.0, 'new_value': 293050.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 57}]
2025-05-27 09:00:32,098 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-27 09:00:32,661 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-27 09:00:32,661 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 299.92}, {'field': 'offline_amount', 'old_value': 64908.0, 'new_value': 68908.0}, {'field': 'total_amount', 'old_value': 64908.0, 'new_value': 69207.92}, {'field': 'order_count', 'old_value': 108, 'new_value': 111}]
2025-05-27 09:00:32,661 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-27 09:00:33,145 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-27 09:00:33,145 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20490.0, 'new_value': 33183.0}, {'field': 'total_amount', 'old_value': 469872.71, 'new_value': 482565.71}, {'field': 'order_count', 'old_value': 439, 'new_value': 458}]
2025-05-27 09:00:33,145 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-27 09:00:33,598 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-27 09:00:33,598 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84531.7, 'new_value': 86021.7}, {'field': 'total_amount', 'old_value': 92096.6, 'new_value': 93586.6}, {'field': 'order_count', 'old_value': 2110, 'new_value': 2122}]
2025-05-27 09:00:33,598 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-27 09:00:34,067 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-27 09:00:34,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 967711.2, 'new_value': 980331.0}, {'field': 'total_amount', 'old_value': 1010347.4, 'new_value': 1022967.2}, {'field': 'order_count', 'old_value': 92, 'new_value': 94}]
2025-05-27 09:00:34,067 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-27 09:00:34,582 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-27 09:00:34,582 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15746.7, 'new_value': 15804.7}, {'field': 'offline_amount', 'old_value': 59237.6, 'new_value': 59797.6}, {'field': 'total_amount', 'old_value': 74984.3, 'new_value': 75602.3}, {'field': 'order_count', 'old_value': 740, 'new_value': 743}]
2025-05-27 09:00:34,582 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-27 09:00:35,004 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-27 09:00:35,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9887.0, 'new_value': 10045.0}, {'field': 'total_amount', 'old_value': 11380.0, 'new_value': 11538.0}, {'field': 'order_count', 'old_value': 227, 'new_value': 231}]
2025-05-27 09:00:35,004 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-27 09:00:35,442 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-27 09:00:35,442 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 142504.0, 'new_value': 149569.0}, {'field': 'offline_amount', 'old_value': 80865.0, 'new_value': 88855.0}, {'field': 'total_amount', 'old_value': 223369.0, 'new_value': 238424.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 74}]
2025-05-27 09:00:35,442 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-27 09:00:35,911 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-27 09:00:35,911 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 226055.0, 'new_value': 230570.0}, {'field': 'total_amount', 'old_value': 226055.0, 'new_value': 230570.0}, {'field': 'order_count', 'old_value': 1173, 'new_value': 1208}]
2025-05-27 09:00:35,911 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-27 09:00:36,457 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-27 09:00:36,457 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 705182.14, 'new_value': 726740.93}, {'field': 'total_amount', 'old_value': 705182.14, 'new_value': 726740.93}, {'field': 'order_count', 'old_value': 3874, 'new_value': 4093}]
2025-05-27 09:00:36,457 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-27 09:00:36,957 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-27 09:00:36,957 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90154.0, 'new_value': 96154.0}, {'field': 'total_amount', 'old_value': 90154.0, 'new_value': 96154.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-27 09:00:36,957 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-27 09:00:37,442 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-27 09:00:37,442 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186422.31, 'new_value': 191043.37}, {'field': 'total_amount', 'old_value': 186422.31, 'new_value': 191043.37}, {'field': 'order_count', 'old_value': 1067, 'new_value': 1101}]
2025-05-27 09:00:37,442 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-27 09:00:37,911 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-27 09:00:37,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8518.0, 'new_value': 8817.0}, {'field': 'total_amount', 'old_value': 8518.0, 'new_value': 8817.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-27 09:00:37,911 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-27 09:00:38,379 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-27 09:00:38,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38985.9, 'new_value': 39486.9}, {'field': 'total_amount', 'old_value': 38985.9, 'new_value': 39486.9}, {'field': 'order_count', 'old_value': 173, 'new_value': 176}]
2025-05-27 09:00:38,379 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-27 09:00:38,848 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-27 09:00:38,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117971.0, 'new_value': 121243.0}, {'field': 'total_amount', 'old_value': 117974.0, 'new_value': 121246.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 50}]
2025-05-27 09:00:38,848 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-27 09:00:39,270 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-27 09:00:39,270 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81040.03, 'new_value': 81306.98}, {'field': 'total_amount', 'old_value': 81063.13, 'new_value': 81330.08}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-05-27 09:00:39,270 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-27 09:00:39,645 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-27 09:00:39,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79361.69, 'new_value': 79391.59}, {'field': 'total_amount', 'old_value': 83130.79, 'new_value': 83160.69}, {'field': 'order_count', 'old_value': 414, 'new_value': 415}]
2025-05-27 09:00:39,645 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-27 09:00:40,067 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-27 09:00:40,067 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59153.0, 'new_value': 63408.0}, {'field': 'offline_amount', 'old_value': 174280.0, 'new_value': 175935.0}, {'field': 'total_amount', 'old_value': 233433.0, 'new_value': 239343.0}, {'field': 'order_count', 'old_value': 5176, 'new_value': 5317}]
2025-05-27 09:00:40,067 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-27 09:00:40,536 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-27 09:00:40,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14330.95, 'new_value': 14513.45}, {'field': 'offline_amount', 'old_value': 304514.68, 'new_value': 309484.48}, {'field': 'total_amount', 'old_value': 318845.63, 'new_value': 323997.93}, {'field': 'order_count', 'old_value': 2179, 'new_value': 2219}]
2025-05-27 09:00:40,536 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-27 09:00:41,004 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-27 09:00:41,004 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 31, 'new_value': 776}]
2025-05-27 09:00:41,004 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-27 09:00:41,504 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-27 09:00:41,504 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 264072.0, 'new_value': 297270.8}, {'field': 'total_amount', 'old_value': 290456.0, 'new_value': 323654.8}, {'field': 'order_count', 'old_value': 83, 'new_value': 88}]
2025-05-27 09:00:41,504 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-27 09:00:41,957 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-27 09:00:41,957 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 532248.04, 'new_value': 533317.04}, {'field': 'offline_amount', 'old_value': 245379.9, 'new_value': 246699.9}, {'field': 'total_amount', 'old_value': 777627.94, 'new_value': 780016.94}, {'field': 'order_count', 'old_value': 6783, 'new_value': 6800}]
2025-05-27 09:00:41,957 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-27 09:00:42,442 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-27 09:00:42,442 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42029.6, 'new_value': 42414.6}, {'field': 'offline_amount', 'old_value': 573.0, 'new_value': 574.0}, {'field': 'total_amount', 'old_value': 42602.6, 'new_value': 42988.6}, {'field': 'order_count', 'old_value': 176, 'new_value': 177}]
2025-05-27 09:00:42,442 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-27 09:00:42,864 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-27 09:00:42,864 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79709.4, 'new_value': 84081.2}, {'field': 'offline_amount', 'old_value': 7147.65, 'new_value': 8405.45}, {'field': 'total_amount', 'old_value': 86857.05, 'new_value': 92486.65}, {'field': 'order_count', 'old_value': 265, 'new_value': 285}]
2025-05-27 09:00:42,879 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-27 09:00:43,286 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-27 09:00:43,286 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9005.3, 'new_value': 9568.3}, {'field': 'offline_amount', 'old_value': 49745.1, 'new_value': 54843.1}, {'field': 'total_amount', 'old_value': 58750.4, 'new_value': 64411.4}, {'field': 'order_count', 'old_value': 74, 'new_value': 79}]
2025-05-27 09:00:43,286 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-27 09:00:43,739 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-27 09:00:43,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 636057.0, 'new_value': 640757.0}, {'field': 'total_amount', 'old_value': 636057.0, 'new_value': 640757.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 79}]
2025-05-27 09:00:43,739 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-27 09:00:44,176 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-27 09:00:44,176 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 133373.12, 'new_value': 139195.1}, {'field': 'offline_amount', 'old_value': 437825.31, 'new_value': 450733.2}, {'field': 'total_amount', 'old_value': 571198.43, 'new_value': 589928.3}, {'field': 'order_count', 'old_value': 2887, 'new_value': 2937}]
2025-05-27 09:00:44,176 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-27 09:00:44,598 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-27 09:00:44,598 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43583.0, 'new_value': 44429.0}, {'field': 'total_amount', 'old_value': 43583.0, 'new_value': 44429.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 92}]
2025-05-27 09:00:44,598 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-27 09:00:45,004 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-27 09:00:45,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7669.0, 'new_value': 7879.0}, {'field': 'total_amount', 'old_value': 7669.0, 'new_value': 7879.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 45}]
2025-05-27 09:00:45,004 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-27 09:00:45,489 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-27 09:00:45,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91585.0, 'new_value': 96484.0}, {'field': 'total_amount', 'old_value': 91585.0, 'new_value': 96484.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-27 09:00:45,489 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-27 09:00:45,989 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-27 09:00:45,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54296.0, 'new_value': 55581.0}, {'field': 'total_amount', 'old_value': 57450.0, 'new_value': 58735.0}, {'field': 'order_count', 'old_value': 213, 'new_value': 218}]
2025-05-27 09:00:45,989 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-27 09:00:46,426 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-27 09:00:46,426 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17234.28, 'new_value': 17856.72}, {'field': 'offline_amount', 'old_value': 275168.04, 'new_value': 283387.54}, {'field': 'total_amount', 'old_value': 292402.32, 'new_value': 301244.26}, {'field': 'order_count', 'old_value': 16125, 'new_value': 16690}]
2025-05-27 09:00:46,426 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-27 09:00:47,036 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-27 09:00:47,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1786.9, 'new_value': 31874.9}, {'field': 'total_amount', 'old_value': 34634.37, 'new_value': 64722.37}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-27 09:00:47,036 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-27 09:00:47,457 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-27 09:00:47,457 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51811.0, 'new_value': 53362.0}, {'field': 'total_amount', 'old_value': 51811.0, 'new_value': 53362.0}, {'field': 'order_count', 'old_value': 379, 'new_value': 385}]
2025-05-27 09:00:47,457 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-27 09:00:47,879 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-27 09:00:47,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 487466.5, 'new_value': 497267.5}, {'field': 'total_amount', 'old_value': 534222.48, 'new_value': 544023.48}, {'field': 'order_count', 'old_value': 4165, 'new_value': 4265}]
2025-05-27 09:00:47,879 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-27 09:00:48,317 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-27 09:00:48,317 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36230.2, 'new_value': 36588.2}, {'field': 'total_amount', 'old_value': 36424.2, 'new_value': 36782.2}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-27 09:00:48,317 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8M
2025-05-27 09:00:48,754 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8M
2025-05-27 09:00:48,754 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44384.0, 'new_value': 47504.0}, {'field': 'total_amount', 'old_value': 44384.0, 'new_value': 47504.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-27 09:00:48,754 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-27 09:00:49,192 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-27 09:00:49,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51159.3, 'new_value': 51887.3}, {'field': 'total_amount', 'old_value': 53459.6, 'new_value': 54187.6}, {'field': 'order_count', 'old_value': 165, 'new_value': 168}]
2025-05-27 09:00:49,192 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-27 09:00:49,801 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-27 09:00:49,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 307488.0, 'new_value': 314300.8}, {'field': 'total_amount', 'old_value': 307488.0, 'new_value': 314300.8}, {'field': 'order_count', 'old_value': 133, 'new_value': 138}]
2025-05-27 09:00:49,801 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-27 09:00:50,223 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-27 09:00:50,223 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30131.0, 'new_value': 31068.0}, {'field': 'total_amount', 'old_value': 30131.0, 'new_value': 31068.0}, {'field': 'order_count', 'old_value': 293, 'new_value': 301}]
2025-05-27 09:00:50,223 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-27 09:00:50,692 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-27 09:00:50,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44226.32, 'new_value': 45930.75}, {'field': 'total_amount', 'old_value': 44226.32, 'new_value': 45930.75}, {'field': 'order_count', 'old_value': 180, 'new_value': 187}]
2025-05-27 09:00:50,692 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-27 09:00:51,160 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-27 09:00:51,160 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5210.0, 'new_value': 9911.0}, {'field': 'offline_amount', 'old_value': 82791.0, 'new_value': 83679.0}, {'field': 'total_amount', 'old_value': 88001.0, 'new_value': 93590.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 55}]
2025-05-27 09:00:51,160 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-27 09:00:51,645 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-27 09:00:51,645 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29671.93, 'new_value': 31707.3}, {'field': 'total_amount', 'old_value': 140828.41, 'new_value': 142863.78}, {'field': 'order_count', 'old_value': 111, 'new_value': 113}]
2025-05-27 09:00:51,645 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-27 09:00:52,176 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-27 09:00:52,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21559.58, 'new_value': 21759.58}, {'field': 'total_amount', 'old_value': 21559.58, 'new_value': 21759.58}, {'field': 'order_count', 'old_value': 180, 'new_value': 181}]
2025-05-27 09:00:52,176 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-27 09:00:52,692 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-27 09:00:52,692 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10375.1, 'new_value': 10674.1}, {'field': 'offline_amount', 'old_value': 27347.9, 'new_value': 28252.9}, {'field': 'total_amount', 'old_value': 37723.0, 'new_value': 38927.0}, {'field': 'order_count', 'old_value': 101, 'new_value': 104}]
2025-05-27 09:00:52,692 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-27 09:00:53,176 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-27 09:00:53,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 334709.67, 'new_value': 342485.62}, {'field': 'total_amount', 'old_value': 334709.67, 'new_value': 342485.62}, {'field': 'order_count', 'old_value': 3171, 'new_value': 3282}]
2025-05-27 09:00:53,176 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-27 09:00:53,629 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-27 09:00:53,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12274.1, 'new_value': 12358.9}, {'field': 'total_amount', 'old_value': 12703.1, 'new_value': 12787.9}, {'field': 'order_count', 'old_value': 167, 'new_value': 169}]
2025-05-27 09:00:53,629 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-27 09:00:54,145 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-27 09:00:54,145 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7304.0, 'new_value': 7634.0}, {'field': 'offline_amount', 'old_value': 35270.0, 'new_value': 35318.0}, {'field': 'total_amount', 'old_value': 42574.0, 'new_value': 42952.0}, {'field': 'order_count', 'old_value': 322, 'new_value': 325}]
2025-05-27 09:00:54,145 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-27 09:00:54,629 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-27 09:00:54,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 737272.69, 'new_value': 757619.32}, {'field': 'total_amount', 'old_value': 737272.69, 'new_value': 757619.32}, {'field': 'order_count', 'old_value': 5487, 'new_value': 5669}]
2025-05-27 09:00:54,629 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-27 09:00:55,067 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-27 09:00:55,067 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61995.29, 'new_value': 63710.78}, {'field': 'offline_amount', 'old_value': 458106.02, 'new_value': 468922.47}, {'field': 'total_amount', 'old_value': 520101.31, 'new_value': 532633.25}, {'field': 'order_count', 'old_value': 2524, 'new_value': 2598}]
2025-05-27 09:00:55,067 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-27 09:00:55,660 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-27 09:00:55,660 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51228.73, 'new_value': 52906.23}, {'field': 'offline_amount', 'old_value': 355639.61, 'new_value': 361612.59}, {'field': 'total_amount', 'old_value': 406868.34, 'new_value': 414518.82}, {'field': 'order_count', 'old_value': 2587, 'new_value': 2641}]
2025-05-27 09:00:55,660 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-27 09:00:56,160 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-27 09:00:56,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 217080.7, 'new_value': 225321.7}, {'field': 'total_amount', 'old_value': 217080.7, 'new_value': 225321.7}, {'field': 'order_count', 'old_value': 1202, 'new_value': 1251}]
2025-05-27 09:00:56,160 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-27 09:00:56,676 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-27 09:00:56,676 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74754.0, 'new_value': 75420.0}, {'field': 'total_amount', 'old_value': 74754.0, 'new_value': 75420.0}, {'field': 'order_count', 'old_value': 2201, 'new_value': 2220}]
2025-05-27 09:00:56,676 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-27 09:00:57,129 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-27 09:00:57,129 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147737.0, 'new_value': 149655.0}, {'field': 'total_amount', 'old_value': 147737.0, 'new_value': 149655.0}, {'field': 'order_count', 'old_value': 4727, 'new_value': 4787}]
2025-05-27 09:00:57,129 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-27 09:00:57,567 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-27 09:00:57,567 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 219486.42, 'new_value': 221961.92}, {'field': 'offline_amount', 'old_value': 115091.9, 'new_value': 115591.9}, {'field': 'total_amount', 'old_value': 334578.32, 'new_value': 337553.82}, {'field': 'order_count', 'old_value': 611, 'new_value': 621}]
2025-05-27 09:00:57,567 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-27 09:00:58,051 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-27 09:00:58,051 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 706069.72, 'new_value': 725629.72}, {'field': 'total_amount', 'old_value': 706069.72, 'new_value': 725629.72}, {'field': 'order_count', 'old_value': 5337, 'new_value': 5528}]
2025-05-27 09:00:58,051 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-27 09:00:58,473 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-27 09:00:58,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83800.0, 'new_value': 103800.0}, {'field': 'total_amount', 'old_value': 83800.0, 'new_value': 103800.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-27 09:00:58,473 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-27 09:00:58,957 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-27 09:00:58,957 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 136542.3, 'new_value': 139516.2}, {'field': 'offline_amount', 'old_value': 110865.9, 'new_value': 115538.4}, {'field': 'total_amount', 'old_value': 247408.2, 'new_value': 255054.6}, {'field': 'order_count', 'old_value': 5814, 'new_value': 5997}]
2025-05-27 09:00:58,957 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-27 09:00:59,426 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-27 09:00:59,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2230000.0, 'new_value': 2280000.0}, {'field': 'total_amount', 'old_value': 2230000.0, 'new_value': 2280000.0}, {'field': 'order_count', 'old_value': 282, 'new_value': 283}]
2025-05-27 09:00:59,426 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-27 09:00:59,864 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-27 09:00:59,864 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189932.9, 'new_value': 193539.9}, {'field': 'total_amount', 'old_value': 189932.9, 'new_value': 193539.9}, {'field': 'order_count', 'old_value': 2433, 'new_value': 2482}]
2025-05-27 09:00:59,864 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-27 09:01:00,317 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-27 09:01:00,317 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43210.05, 'new_value': 44703.55}, {'field': 'offline_amount', 'old_value': 1115318.92, 'new_value': 1153629.73}, {'field': 'total_amount', 'old_value': 1158528.97, 'new_value': 1198333.28}, {'field': 'order_count', 'old_value': 5688, 'new_value': 5876}]
2025-05-27 09:01:00,317 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-27 09:01:00,770 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-27 09:01:00,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 716351.82, 'new_value': 729567.37}, {'field': 'total_amount', 'old_value': 716351.82, 'new_value': 729567.37}, {'field': 'order_count', 'old_value': 8270, 'new_value': 8475}]
2025-05-27 09:01:00,770 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-27 09:01:01,317 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-27 09:01:01,317 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 199342.8, 'new_value': 203630.0}, {'field': 'offline_amount', 'old_value': 474027.2, 'new_value': 475027.2}, {'field': 'total_amount', 'old_value': 673370.0, 'new_value': 678657.2}, {'field': 'order_count', 'old_value': 4711, 'new_value': 4766}]
2025-05-27 09:01:01,317 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-27 09:01:01,723 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-27 09:01:01,723 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87006.4, 'new_value': 87499.3}, {'field': 'total_amount', 'old_value': 89162.1, 'new_value': 89655.0}, {'field': 'order_count', 'old_value': 564, 'new_value': 569}]
2025-05-27 09:01:01,723 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-27 09:01:02,207 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-27 09:01:02,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51292.0, 'new_value': 51580.0}, {'field': 'total_amount', 'old_value': 51292.0, 'new_value': 51580.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 124}]
2025-05-27 09:01:02,207 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-27 09:01:02,910 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-27 09:01:02,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 520000.0, 'new_value': 525000.0}, {'field': 'total_amount', 'old_value': 520000.0, 'new_value': 525000.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 150}]
2025-05-27 09:01:02,910 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-27 09:01:03,348 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-27 09:01:03,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 500000.0, 'new_value': 505000.0}, {'field': 'total_amount', 'old_value': 500000.0, 'new_value': 505000.0}, {'field': 'order_count', 'old_value': 148, 'new_value': 149}]
2025-05-27 09:01:03,348 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-27 09:01:03,910 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-27 09:01:03,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3348674.0, 'new_value': 3398674.0}, {'field': 'total_amount', 'old_value': 3348674.0, 'new_value': 3398674.0}, {'field': 'order_count', 'old_value': 302, 'new_value': 303}]
2025-05-27 09:01:03,910 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-27 09:01:04,395 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-27 09:01:04,395 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23058.34, 'new_value': 23917.43}, {'field': 'offline_amount', 'old_value': 16113.36, 'new_value': 16482.94}, {'field': 'total_amount', 'old_value': 39171.7, 'new_value': 40400.37}, {'field': 'order_count', 'old_value': 1669, 'new_value': 1722}]
2025-05-27 09:01:04,395 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-27 09:01:04,895 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-27 09:01:04,895 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17473.5, 'new_value': 17980.5}, {'field': 'total_amount', 'old_value': 70026.3, 'new_value': 70533.3}, {'field': 'order_count', 'old_value': 640, 'new_value': 647}]
2025-05-27 09:01:04,895 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-27 09:01:05,379 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-27 09:01:05,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108482.0, 'new_value': 111832.0}, {'field': 'total_amount', 'old_value': 108482.0, 'new_value': 111832.0}, {'field': 'order_count', 'old_value': 458, 'new_value': 473}]
2025-05-27 09:01:05,379 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-27 09:01:05,817 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-27 09:01:05,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121919.0, 'new_value': 134319.0}, {'field': 'total_amount', 'old_value': 121919.0, 'new_value': 134319.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-27 09:01:05,817 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-27 09:01:06,301 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-27 09:01:06,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 277651.03, 'new_value': 282298.46}, {'field': 'total_amount', 'old_value': 300138.43, 'new_value': 304785.86}, {'field': 'order_count', 'old_value': 1660, 'new_value': 1699}]
2025-05-27 09:01:06,301 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-27 09:01:06,817 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-27 09:01:06,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 210634.68, 'new_value': 216537.53}, {'field': 'offline_amount', 'old_value': 374946.92, 'new_value': 379946.92}, {'field': 'total_amount', 'old_value': 585581.6, 'new_value': 596484.45}, {'field': 'order_count', 'old_value': 1475, 'new_value': 1508}]
2025-05-27 09:01:06,817 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-27 09:01:07,254 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-27 09:01:07,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43736.25, 'new_value': 45113.15}, {'field': 'offline_amount', 'old_value': 128299.0, 'new_value': 131515.0}, {'field': 'total_amount', 'old_value': 172035.25, 'new_value': 176628.15}, {'field': 'order_count', 'old_value': 1836, 'new_value': 1917}]
2025-05-27 09:01:07,254 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-27 09:01:07,770 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-27 09:01:07,770 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 116752.49, 'new_value': 121168.84}, {'field': 'total_amount', 'old_value': 123981.56, 'new_value': 128397.91}, {'field': 'order_count', 'old_value': 662, 'new_value': 693}]
2025-05-27 09:01:07,770 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-27 09:01:08,192 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-27 09:01:08,192 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7154.0, 'new_value': 7273.0}, {'field': 'offline_amount', 'old_value': 55722.0, 'new_value': 56447.0}, {'field': 'total_amount', 'old_value': 62876.0, 'new_value': 63720.0}, {'field': 'order_count', 'old_value': 485, 'new_value': 496}]
2025-05-27 09:01:08,192 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-27 09:01:08,645 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-27 09:01:08,645 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66506.0, 'new_value': 67964.0}, {'field': 'offline_amount', 'old_value': 310665.0, 'new_value': 321443.0}, {'field': 'total_amount', 'old_value': 377171.0, 'new_value': 389407.0}, {'field': 'order_count', 'old_value': 1516, 'new_value': 1561}]
2025-05-27 09:01:08,645 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-27 09:01:09,113 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-27 09:01:09,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1057589.0, 'new_value': 1075900.0}, {'field': 'total_amount', 'old_value': 1057589.0, 'new_value': 1075900.0}, {'field': 'order_count', 'old_value': 4661, 'new_value': 4751}]
2025-05-27 09:01:09,113 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-27 09:01:09,551 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-27 09:01:09,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12904559.0, 'new_value': 13175779.0}, {'field': 'total_amount', 'old_value': 12904559.0, 'new_value': 13175779.0}, {'field': 'order_count', 'old_value': 40817, 'new_value': 41807}]
2025-05-27 09:01:09,551 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-27 09:01:10,223 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-27 09:01:10,223 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 291717.91, 'new_value': 299868.73}, {'field': 'offline_amount', 'old_value': 208632.61, 'new_value': 213500.09}, {'field': 'total_amount', 'old_value': 500350.52, 'new_value': 513368.82}, {'field': 'order_count', 'old_value': 20221, 'new_value': 20785}]
2025-05-27 09:01:10,223 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-27 09:01:10,660 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-27 09:01:10,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 291852.0, 'new_value': 302595.0}, {'field': 'total_amount', 'old_value': 291852.0, 'new_value': 302595.0}, {'field': 'order_count', 'old_value': 362, 'new_value': 373}]
2025-05-27 09:01:10,660 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-27 09:01:11,113 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-27 09:01:11,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 324166.3, 'new_value': 326764.7}, {'field': 'total_amount', 'old_value': 324166.3, 'new_value': 326764.7}, {'field': 'order_count', 'old_value': 7075, 'new_value': 7131}]
2025-05-27 09:01:11,113 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-27 09:01:11,535 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-27 09:01:11,535 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53551.8, 'new_value': 55365.3}, {'field': 'total_amount', 'old_value': 53551.8, 'new_value': 55365.3}, {'field': 'order_count', 'old_value': 292, 'new_value': 302}]
2025-05-27 09:01:11,535 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-27 09:01:12,035 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-27 09:01:12,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 766736.59, 'new_value': 798938.13}, {'field': 'total_amount', 'old_value': 766736.59, 'new_value': 798938.13}, {'field': 'order_count', 'old_value': 4187, 'new_value': 4295}]
2025-05-27 09:01:12,035 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-27 09:01:12,473 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-27 09:01:12,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141899.3, 'new_value': 145197.47}, {'field': 'total_amount', 'old_value': 141899.3, 'new_value': 145197.47}, {'field': 'order_count', 'old_value': 9855, 'new_value': 10099}]
2025-05-27 09:01:12,473 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-27 09:01:12,879 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-27 09:01:12,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 452581.0, 'new_value': 456253.0}, {'field': 'total_amount', 'old_value': 452581.0, 'new_value': 456253.0}, {'field': 'order_count', 'old_value': 10280, 'new_value': 10367}]
2025-05-27 09:01:12,879 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-27 09:01:13,348 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-27 09:01:13,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102129.0, 'new_value': 104109.0}, {'field': 'total_amount', 'old_value': 102129.0, 'new_value': 104109.0}, {'field': 'order_count', 'old_value': 6846, 'new_value': 7023}]
2025-05-27 09:01:13,348 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-27 09:01:13,832 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-27 09:01:13,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77742.2, 'new_value': 78068.2}, {'field': 'total_amount', 'old_value': 78022.0, 'new_value': 78348.0}, {'field': 'order_count', 'old_value': 1153, 'new_value': 1158}]
2025-05-27 09:01:13,832 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-27 09:01:14,301 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-27 09:01:14,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67065.18, 'new_value': 69746.18}, {'field': 'total_amount', 'old_value': 72425.15, 'new_value': 75106.15}, {'field': 'order_count', 'old_value': 1265, 'new_value': 1355}]
2025-05-27 09:01:14,301 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-27 09:01:14,770 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-27 09:01:14,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 544334.39, 'new_value': 556993.39}, {'field': 'total_amount', 'old_value': 549880.75, 'new_value': 562539.75}, {'field': 'order_count', 'old_value': 5614, 'new_value': 5739}]
2025-05-27 09:01:14,770 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-27 09:01:15,176 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-27 09:01:15,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 486950.0, 'new_value': 493088.0}, {'field': 'total_amount', 'old_value': 486950.0, 'new_value': 493088.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 87}]
2025-05-27 09:01:15,176 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-27 09:01:15,567 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-27 09:01:15,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35685.0, 'new_value': 37012.0}, {'field': 'total_amount', 'old_value': 35685.0, 'new_value': 37012.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 116}]
2025-05-27 09:01:15,567 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-27 09:01:16,082 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-27 09:01:16,082 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6.4, 'new_value': 79.7}, {'field': 'total_amount', 'old_value': 6.4, 'new_value': 79.7}, {'field': 'order_count', 'old_value': 20, 'new_value': 23}]
2025-05-27 09:01:16,082 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-27 09:01:16,535 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-27 09:01:16,535 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190979.0, 'new_value': 201325.0}, {'field': 'total_amount', 'old_value': 190979.0, 'new_value': 201325.0}, {'field': 'order_count', 'old_value': 20154, 'new_value': 21291}]
2025-05-27 09:01:16,535 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-27 09:01:16,895 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-27 09:01:16,895 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71720.6, 'new_value': 73504.6}, {'field': 'total_amount', 'old_value': 71720.6, 'new_value': 73504.6}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-05-27 09:01:16,895 - INFO - 开始更新记录 - 表单实例ID: FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC
2025-05-27 09:01:17,348 - INFO - 更新表单数据成功: FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC
2025-05-27 09:01:17,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20000.0, 'new_value': 25000.0}, {'field': 'total_amount', 'old_value': 20000.0, 'new_value': 25000.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-27 09:01:17,348 - INFO - 日期 2025-05 处理完成 - 更新: 102 条，插入: 0 条，错误: 0 条
2025-05-27 09:01:17,348 - INFO - 数据同步完成！更新: 102 条，插入: 0 条，错误: 0 条
2025-05-27 09:01:17,348 - INFO - =================同步完成====================
2025-05-27 12:00:02,045 - INFO - =================使用默认全量同步=============
2025-05-27 12:00:03,545 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-27 12:00:03,545 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-27 12:00:03,576 - INFO - 开始处理日期: 2025-01
2025-05-27 12:00:03,576 - INFO - Request Parameters - Page 1:
2025-05-27 12:00:03,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:03,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:04,467 - INFO - Response - Page 1:
2025-05-27 12:00:04,670 - INFO - 第 1 页获取到 100 条记录
2025-05-27 12:00:04,670 - INFO - Request Parameters - Page 2:
2025-05-27 12:00:04,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:04,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:05,654 - INFO - Response - Page 2:
2025-05-27 12:00:05,857 - INFO - 第 2 页获取到 100 条记录
2025-05-27 12:00:05,857 - INFO - Request Parameters - Page 3:
2025-05-27 12:00:05,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:05,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:06,451 - INFO - Response - Page 3:
2025-05-27 12:00:06,654 - INFO - 第 3 页获取到 100 条记录
2025-05-27 12:00:06,654 - INFO - Request Parameters - Page 4:
2025-05-27 12:00:06,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:06,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:07,186 - INFO - Response - Page 4:
2025-05-27 12:00:07,389 - INFO - 第 4 页获取到 100 条记录
2025-05-27 12:00:07,389 - INFO - Request Parameters - Page 5:
2025-05-27 12:00:07,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:07,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:07,857 - INFO - Response - Page 5:
2025-05-27 12:00:08,061 - INFO - 第 5 页获取到 100 条记录
2025-05-27 12:00:08,061 - INFO - Request Parameters - Page 6:
2025-05-27 12:00:08,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:08,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:08,529 - INFO - Response - Page 6:
2025-05-27 12:00:08,732 - INFO - 第 6 页获取到 100 条记录
2025-05-27 12:00:08,732 - INFO - Request Parameters - Page 7:
2025-05-27 12:00:08,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:08,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:09,264 - INFO - Response - Page 7:
2025-05-27 12:00:09,467 - INFO - 第 7 页获取到 82 条记录
2025-05-27 12:00:09,467 - INFO - 查询完成，共获取到 682 条记录
2025-05-27 12:00:09,467 - INFO - 获取到 682 条表单数据
2025-05-27 12:00:09,467 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-27 12:00:09,482 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 12:00:09,482 - INFO - 开始处理日期: 2025-02
2025-05-27 12:00:09,482 - INFO - Request Parameters - Page 1:
2025-05-27 12:00:09,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:09,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:09,982 - INFO - Response - Page 1:
2025-05-27 12:00:10,186 - INFO - 第 1 页获取到 100 条记录
2025-05-27 12:00:10,186 - INFO - Request Parameters - Page 2:
2025-05-27 12:00:10,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:10,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:10,654 - INFO - Response - Page 2:
2025-05-27 12:00:10,857 - INFO - 第 2 页获取到 100 条记录
2025-05-27 12:00:10,857 - INFO - Request Parameters - Page 3:
2025-05-27 12:00:10,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:10,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:11,451 - INFO - Response - Page 3:
2025-05-27 12:00:11,654 - INFO - 第 3 页获取到 100 条记录
2025-05-27 12:00:11,654 - INFO - Request Parameters - Page 4:
2025-05-27 12:00:11,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:11,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:12,217 - INFO - Response - Page 4:
2025-05-27 12:00:12,420 - INFO - 第 4 页获取到 100 条记录
2025-05-27 12:00:12,420 - INFO - Request Parameters - Page 5:
2025-05-27 12:00:12,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:12,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:12,967 - INFO - Response - Page 5:
2025-05-27 12:00:13,170 - INFO - 第 5 页获取到 100 条记录
2025-05-27 12:00:13,170 - INFO - Request Parameters - Page 6:
2025-05-27 12:00:13,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:13,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:13,732 - INFO - Response - Page 6:
2025-05-27 12:00:13,936 - INFO - 第 6 页获取到 100 条记录
2025-05-27 12:00:13,936 - INFO - Request Parameters - Page 7:
2025-05-27 12:00:13,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:13,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:14,451 - INFO - Response - Page 7:
2025-05-27 12:00:14,654 - INFO - 第 7 页获取到 70 条记录
2025-05-27 12:00:14,654 - INFO - 查询完成，共获取到 670 条记录
2025-05-27 12:00:14,654 - INFO - 获取到 670 条表单数据
2025-05-27 12:00:14,654 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-27 12:00:14,670 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 12:00:14,670 - INFO - 开始处理日期: 2025-03
2025-05-27 12:00:14,670 - INFO - Request Parameters - Page 1:
2025-05-27 12:00:14,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:14,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:15,170 - INFO - Response - Page 1:
2025-05-27 12:00:15,373 - INFO - 第 1 页获取到 100 条记录
2025-05-27 12:00:15,373 - INFO - Request Parameters - Page 2:
2025-05-27 12:00:15,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:15,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:15,811 - INFO - Response - Page 2:
2025-05-27 12:00:16,014 - INFO - 第 2 页获取到 100 条记录
2025-05-27 12:00:16,014 - INFO - Request Parameters - Page 3:
2025-05-27 12:00:16,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:16,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:16,498 - INFO - Response - Page 3:
2025-05-27 12:00:16,701 - INFO - 第 3 页获取到 100 条记录
2025-05-27 12:00:16,701 - INFO - Request Parameters - Page 4:
2025-05-27 12:00:16,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:16,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:17,232 - INFO - Response - Page 4:
2025-05-27 12:00:17,436 - INFO - 第 4 页获取到 100 条记录
2025-05-27 12:00:17,436 - INFO - Request Parameters - Page 5:
2025-05-27 12:00:17,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:17,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:17,982 - INFO - Response - Page 5:
2025-05-27 12:00:18,186 - INFO - 第 5 页获取到 100 条记录
2025-05-27 12:00:18,186 - INFO - Request Parameters - Page 6:
2025-05-27 12:00:18,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:18,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:18,654 - INFO - Response - Page 6:
2025-05-27 12:00:18,857 - INFO - 第 6 页获取到 100 条记录
2025-05-27 12:00:18,857 - INFO - Request Parameters - Page 7:
2025-05-27 12:00:18,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:18,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:19,295 - INFO - Response - Page 7:
2025-05-27 12:00:19,498 - INFO - 第 7 页获取到 61 条记录
2025-05-27 12:00:19,498 - INFO - 查询完成，共获取到 661 条记录
2025-05-27 12:00:19,498 - INFO - 获取到 661 条表单数据
2025-05-27 12:00:19,498 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-27 12:00:19,514 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 12:00:19,514 - INFO - 开始处理日期: 2025-04
2025-05-27 12:00:19,514 - INFO - Request Parameters - Page 1:
2025-05-27 12:00:19,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:19,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:20,060 - INFO - Response - Page 1:
2025-05-27 12:00:20,264 - INFO - 第 1 页获取到 100 条记录
2025-05-27 12:00:20,264 - INFO - Request Parameters - Page 2:
2025-05-27 12:00:20,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:20,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:20,764 - INFO - Response - Page 2:
2025-05-27 12:00:20,967 - INFO - 第 2 页获取到 100 条记录
2025-05-27 12:00:20,967 - INFO - Request Parameters - Page 3:
2025-05-27 12:00:20,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:20,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:21,389 - INFO - Response - Page 3:
2025-05-27 12:00:21,592 - INFO - 第 3 页获取到 100 条记录
2025-05-27 12:00:21,592 - INFO - Request Parameters - Page 4:
2025-05-27 12:00:21,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:21,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:22,139 - INFO - Response - Page 4:
2025-05-27 12:00:22,342 - INFO - 第 4 页获取到 100 条记录
2025-05-27 12:00:22,342 - INFO - Request Parameters - Page 5:
2025-05-27 12:00:22,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:22,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:22,826 - INFO - Response - Page 5:
2025-05-27 12:00:23,029 - INFO - 第 5 页获取到 100 条记录
2025-05-27 12:00:23,029 - INFO - Request Parameters - Page 6:
2025-05-27 12:00:23,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:23,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:23,514 - INFO - Response - Page 6:
2025-05-27 12:00:23,717 - INFO - 第 6 页获取到 100 条记录
2025-05-27 12:00:23,717 - INFO - Request Parameters - Page 7:
2025-05-27 12:00:23,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:23,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:24,092 - INFO - Response - Page 7:
2025-05-27 12:00:24,295 - INFO - 第 7 页获取到 56 条记录
2025-05-27 12:00:24,295 - INFO - 查询完成，共获取到 656 条记录
2025-05-27 12:00:24,295 - INFO - 获取到 656 条表单数据
2025-05-27 12:00:24,295 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-27 12:00:24,310 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 12:00:24,310 - INFO - 开始处理日期: 2025-05
2025-05-27 12:00:24,310 - INFO - Request Parameters - Page 1:
2025-05-27 12:00:24,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:24,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:24,889 - INFO - Response - Page 1:
2025-05-27 12:00:25,092 - INFO - 第 1 页获取到 100 条记录
2025-05-27 12:00:25,092 - INFO - Request Parameters - Page 2:
2025-05-27 12:00:25,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:25,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:25,623 - INFO - Response - Page 2:
2025-05-27 12:00:25,826 - INFO - 第 2 页获取到 100 条记录
2025-05-27 12:00:25,826 - INFO - Request Parameters - Page 3:
2025-05-27 12:00:25,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:25,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:26,373 - INFO - Response - Page 3:
2025-05-27 12:00:26,576 - INFO - 第 3 页获取到 100 条记录
2025-05-27 12:00:26,576 - INFO - Request Parameters - Page 4:
2025-05-27 12:00:26,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:26,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:27,107 - INFO - Response - Page 4:
2025-05-27 12:00:27,310 - INFO - 第 4 页获取到 100 条记录
2025-05-27 12:00:27,310 - INFO - Request Parameters - Page 5:
2025-05-27 12:00:27,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:27,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:27,810 - INFO - Response - Page 5:
2025-05-27 12:00:28,014 - INFO - 第 5 页获取到 100 条记录
2025-05-27 12:00:28,014 - INFO - Request Parameters - Page 6:
2025-05-27 12:00:28,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:28,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:28,498 - INFO - Response - Page 6:
2025-05-27 12:00:28,701 - INFO - 第 6 页获取到 100 条记录
2025-05-27 12:00:28,701 - INFO - Request Parameters - Page 7:
2025-05-27 12:00:28,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 12:00:28,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 12:00:29,107 - INFO - Response - Page 7:
2025-05-27 12:00:29,310 - INFO - 第 7 页获取到 33 条记录
2025-05-27 12:00:29,310 - INFO - 查询完成，共获取到 633 条记录
2025-05-27 12:00:29,310 - INFO - 获取到 633 条表单数据
2025-05-27 12:00:29,310 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-27 12:00:29,310 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-27 12:00:29,779 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-27 12:00:29,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2022.0, 'new_value': 2067.0}, {'field': 'offline_amount', 'old_value': 43418.0, 'new_value': 43698.0}, {'field': 'total_amount', 'old_value': 45440.0, 'new_value': 45765.0}, {'field': 'order_count', 'old_value': 623, 'new_value': 634}]
2025-05-27 12:00:29,779 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-27 12:00:30,217 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-27 12:00:30,217 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 383219.0, 'new_value': 392991.0}, {'field': 'total_amount', 'old_value': 383219.0, 'new_value': 392991.0}, {'field': 'order_count', 'old_value': 285, 'new_value': 294}]
2025-05-27 12:00:30,217 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-27 12:00:30,623 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-27 12:00:30,623 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48760.0, 'new_value': 51940.0}, {'field': 'total_amount', 'old_value': 50350.0, 'new_value': 53530.0}, {'field': 'order_count', 'old_value': 190, 'new_value': 202}]
2025-05-27 12:00:30,623 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-27 12:00:31,170 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-27 12:00:31,170 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 181754.57, 'new_value': 190649.36}, {'field': 'total_amount', 'old_value': 181754.57, 'new_value': 190649.36}, {'field': 'order_count', 'old_value': 6826, 'new_value': 7113}]
2025-05-27 12:00:31,170 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-27 12:00:31,639 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-27 12:00:31,639 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43691.64, 'new_value': 45889.48}, {'field': 'total_amount', 'old_value': 43691.64, 'new_value': 45889.48}, {'field': 'order_count', 'old_value': 8544, 'new_value': 9002}]
2025-05-27 12:00:31,639 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-27 12:00:32,060 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-27 12:00:32,060 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32874.87, 'new_value': 34592.87}, {'field': 'offline_amount', 'old_value': 15974.57, 'new_value': 16483.57}, {'field': 'total_amount', 'old_value': 48849.44, 'new_value': 51076.44}, {'field': 'order_count', 'old_value': 2450, 'new_value': 2560}]
2025-05-27 12:00:32,060 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-27 12:00:32,498 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-27 12:00:32,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 380300.0, 'new_value': 401980.0}, {'field': 'total_amount', 'old_value': 380300.0, 'new_value': 401980.0}, {'field': 'order_count', 'old_value': 229, 'new_value': 244}]
2025-05-27 12:00:32,498 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-27 12:00:33,139 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-27 12:00:33,139 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86001.0, 'new_value': 89001.0}, {'field': 'total_amount', 'old_value': 94200.0, 'new_value': 97200.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-05-27 12:00:33,139 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-27 12:00:33,576 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-27 12:00:33,576 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93313.15, 'new_value': 96713.15}, {'field': 'total_amount', 'old_value': 124893.03, 'new_value': 128293.03}, {'field': 'order_count', 'old_value': 2899, 'new_value': 2900}]
2025-05-27 12:00:33,576 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-27 12:00:34,076 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-27 12:00:34,076 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49026.5, 'new_value': 51604.5}, {'field': 'total_amount', 'old_value': 52986.5, 'new_value': 55564.5}, {'field': 'order_count', 'old_value': 407, 'new_value': 421}]
2025-05-27 12:00:34,076 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-27 12:00:34,498 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-27 12:00:34,498 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56025.38, 'new_value': 58602.38}, {'field': 'offline_amount', 'old_value': 110844.85, 'new_value': 117059.85}, {'field': 'total_amount', 'old_value': 166870.23, 'new_value': 175662.23}, {'field': 'order_count', 'old_value': 1916, 'new_value': 2017}]
2025-05-27 12:00:34,498 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-27 12:00:34,935 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-27 12:00:34,935 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21182.94, 'new_value': 21910.31}, {'field': 'offline_amount', 'old_value': 27029.46, 'new_value': 27428.76}, {'field': 'total_amount', 'old_value': 48212.4, 'new_value': 49339.07}, {'field': 'order_count', 'old_value': 2365, 'new_value': 2421}]
2025-05-27 12:00:34,935 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-27 12:00:35,342 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-27 12:00:35,342 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 309713.0, 'new_value': 311658.9}, {'field': 'total_amount', 'old_value': 424732.7, 'new_value': 426678.6}, {'field': 'order_count', 'old_value': 3426, 'new_value': 3488}]
2025-05-27 12:00:35,342 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-27 12:00:35,857 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-27 12:00:35,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104961.0, 'new_value': 108677.0}, {'field': 'total_amount', 'old_value': 104961.0, 'new_value': 108677.0}, {'field': 'order_count', 'old_value': 5718, 'new_value': 5955}]
2025-05-27 12:00:35,857 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-27 12:00:36,342 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-27 12:00:36,342 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4011.48, 'new_value': 4137.24}, {'field': 'offline_amount', 'old_value': 83889.94, 'new_value': 86488.52}, {'field': 'total_amount', 'old_value': 87901.42, 'new_value': 90625.76}, {'field': 'order_count', 'old_value': 3430, 'new_value': 3545}]
2025-05-27 12:00:36,342 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-27 12:00:36,810 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-27 12:00:36,810 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141826.14, 'new_value': 146881.02}, {'field': 'total_amount', 'old_value': 141826.14, 'new_value': 146881.02}, {'field': 'order_count', 'old_value': 1682, 'new_value': 1743}]
2025-05-27 12:00:36,810 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-27 12:00:37,279 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-27 12:00:37,279 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1113368.0, 'new_value': 1143368.0}, {'field': 'total_amount', 'old_value': 1439253.0, 'new_value': 1469253.0}, {'field': 'order_count', 'old_value': 1729, 'new_value': 1733}]
2025-05-27 12:00:37,279 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-27 12:00:37,717 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-27 12:00:37,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52335.0, 'new_value': 72335.0}, {'field': 'total_amount', 'old_value': 89935.0, 'new_value': 109935.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 22}]
2025-05-27 12:00:37,717 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-27 12:00:38,185 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-27 12:00:38,185 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 40000.0}, {'field': 'total_amount', 'old_value': 327021.89, 'new_value': 367021.89}, {'field': 'order_count', 'old_value': 55, 'new_value': 62}]
2025-05-27 12:00:38,185 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-27 12:00:38,638 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-27 12:00:38,638 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39360.0, 'new_value': 69360.0}, {'field': 'total_amount', 'old_value': 329085.13, 'new_value': 359085.13}, {'field': 'order_count', 'old_value': 47, 'new_value': 53}]
2025-05-27 12:00:38,638 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-27 12:00:39,076 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-27 12:00:39,076 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82211.0, 'new_value': 85978.0}, {'field': 'total_amount', 'old_value': 159256.0, 'new_value': 163023.0}, {'field': 'order_count', 'old_value': 2158, 'new_value': 2217}]
2025-05-27 12:00:39,076 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-27 12:00:39,654 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-27 12:00:39,654 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20181.0, 'new_value': 26702.0}, {'field': 'total_amount', 'old_value': 26753.0, 'new_value': 33274.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 78}]
2025-05-27 12:00:39,654 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-27 12:00:40,060 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-27 12:00:40,060 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180970.0, 'new_value': 183932.0}, {'field': 'total_amount', 'old_value': 180970.0, 'new_value': 183932.0}, {'field': 'order_count', 'old_value': 369, 'new_value': 378}]
2025-05-27 12:00:40,060 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-27 12:00:40,513 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-27 12:00:40,513 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163741.38, 'new_value': 175365.24}, {'field': 'total_amount', 'old_value': 163741.38, 'new_value': 175365.24}, {'field': 'order_count', 'old_value': 263, 'new_value': 275}]
2025-05-27 12:00:40,513 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-27 12:00:40,951 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-27 12:00:40,951 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95484.23, 'new_value': 98684.23}, {'field': 'total_amount', 'old_value': 95484.23, 'new_value': 98684.23}, {'field': 'order_count', 'old_value': 3670, 'new_value': 3671}]
2025-05-27 12:00:40,951 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-27 12:00:41,420 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-27 12:00:41,420 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14499.0, 'new_value': 15127.0}, {'field': 'total_amount', 'old_value': 14499.0, 'new_value': 15127.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-05-27 12:00:41,420 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-27 12:00:41,904 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-27 12:00:41,904 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98259.86, 'new_value': 102860.67}, {'field': 'offline_amount', 'old_value': 57410.81, 'new_value': 57960.15}, {'field': 'total_amount', 'old_value': 155670.67, 'new_value': 160820.82}, {'field': 'order_count', 'old_value': 8738, 'new_value': 9044}]
2025-05-27 12:00:41,904 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-27 12:00:42,342 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-27 12:00:42,342 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 179723.56, 'new_value': 183479.88}, {'field': 'offline_amount', 'old_value': 30247.25, 'new_value': 30768.66}, {'field': 'total_amount', 'old_value': 209970.81, 'new_value': 214248.54}, {'field': 'order_count', 'old_value': 772, 'new_value': 789}]
2025-05-27 12:00:42,342 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-27 12:00:42,826 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-27 12:00:42,826 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 171709.0, 'new_value': 174619.0}, {'field': 'offline_amount', 'old_value': 63878.43, 'new_value': 65674.01}, {'field': 'total_amount', 'old_value': 235587.43, 'new_value': 240293.01}, {'field': 'order_count', 'old_value': 1525, 'new_value': 1560}]
2025-05-27 12:00:42,826 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-27 12:00:43,295 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-27 12:00:43,295 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49450.37, 'new_value': 50834.78}, {'field': 'offline_amount', 'old_value': 51821.29, 'new_value': 52760.08}, {'field': 'total_amount', 'old_value': 101271.66, 'new_value': 103594.86}, {'field': 'order_count', 'old_value': 5343, 'new_value': 5464}]
2025-05-27 12:00:43,295 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-27 12:00:43,732 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-27 12:00:43,732 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91394.83, 'new_value': 95123.42}, {'field': 'total_amount', 'old_value': 91394.83, 'new_value': 95123.42}, {'field': 'order_count', 'old_value': 2456, 'new_value': 2558}]
2025-05-27 12:00:43,732 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-27 12:00:44,185 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-27 12:00:44,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199121.1, 'new_value': 199613.1}, {'field': 'total_amount', 'old_value': 199121.1, 'new_value': 199613.1}, {'field': 'order_count', 'old_value': 63, 'new_value': 64}]
2025-05-27 12:00:44,185 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-27 12:00:44,654 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-27 12:00:44,654 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 164179.0, 'new_value': 168026.0}, {'field': 'offline_amount', 'old_value': 72057.38, 'new_value': 73925.38}, {'field': 'total_amount', 'old_value': 236236.38, 'new_value': 241951.38}, {'field': 'order_count', 'old_value': 1653, 'new_value': 1693}]
2025-05-27 12:00:44,654 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-27 12:00:45,076 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-27 12:00:45,076 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52448.16, 'new_value': 55518.16}, {'field': 'offline_amount', 'old_value': 536113.76, 'new_value': 550436.38}, {'field': 'total_amount', 'old_value': 588561.92, 'new_value': 605954.54}, {'field': 'order_count', 'old_value': 1908, 'new_value': 1969}]
2025-05-27 12:00:45,076 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-27 12:00:45,607 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-27 12:00:45,607 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10185.94, 'new_value': 10759.61}, {'field': 'offline_amount', 'old_value': 124068.27, 'new_value': 127271.66}, {'field': 'total_amount', 'old_value': 134254.21, 'new_value': 138031.27}, {'field': 'order_count', 'old_value': 2243, 'new_value': 2312}]
2025-05-27 12:00:45,607 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-27 12:00:46,107 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-27 12:00:46,107 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10729.7, 'new_value': 11344.62}, {'field': 'offline_amount', 'old_value': 174815.26, 'new_value': 181653.01}, {'field': 'total_amount', 'old_value': 185544.96, 'new_value': 192997.63}, {'field': 'order_count', 'old_value': 2025, 'new_value': 2109}]
2025-05-27 12:00:46,107 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-27 12:00:46,670 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-27 12:00:46,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214732.2, 'new_value': 220450.83}, {'field': 'total_amount', 'old_value': 214732.2, 'new_value': 220450.83}, {'field': 'order_count', 'old_value': 725, 'new_value': 747}]
2025-05-27 12:00:46,670 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-27 12:00:47,217 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-27 12:00:47,217 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79076.78, 'new_value': 80920.92}, {'field': 'total_amount', 'old_value': 83885.73, 'new_value': 85729.87}, {'field': 'order_count', 'old_value': 4951, 'new_value': 5086}]
2025-05-27 12:00:47,217 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-27 12:00:47,685 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-27 12:00:47,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146418.0, 'new_value': 151341.0}, {'field': 'total_amount', 'old_value': 146418.0, 'new_value': 151341.0}, {'field': 'order_count', 'old_value': 3656, 'new_value': 3796}]
2025-05-27 12:00:47,685 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-27 12:00:48,248 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-27 12:00:48,248 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86335.32, 'new_value': 89825.0}, {'field': 'offline_amount', 'old_value': 877761.16, 'new_value': 902867.36}, {'field': 'total_amount', 'old_value': 964096.48, 'new_value': 992692.36}, {'field': 'order_count', 'old_value': 3047, 'new_value': 3134}]
2025-05-27 12:00:48,248 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-27 12:00:48,685 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-27 12:00:48,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32347.8, 'new_value': 32513.8}, {'field': 'total_amount', 'old_value': 32347.8, 'new_value': 32513.8}, {'field': 'order_count', 'old_value': 56, 'new_value': 58}]
2025-05-27 12:00:48,685 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-27 12:00:49,185 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-27 12:00:49,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24349.15, 'new_value': 24724.65}, {'field': 'total_amount', 'old_value': 24349.15, 'new_value': 24724.65}, {'field': 'order_count', 'old_value': 145, 'new_value': 148}]
2025-05-27 12:00:49,185 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-27 12:00:49,670 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-27 12:00:49,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168220.0, 'new_value': 175396.0}, {'field': 'total_amount', 'old_value': 168220.0, 'new_value': 175396.0}, {'field': 'order_count', 'old_value': 6324, 'new_value': 6605}]
2025-05-27 12:00:49,670 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-27 12:00:50,154 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-27 12:00:50,154 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33596.0, 'new_value': 34346.0}, {'field': 'offline_amount', 'old_value': 480226.2, 'new_value': 502380.2}, {'field': 'total_amount', 'old_value': 513822.2, 'new_value': 536726.2}, {'field': 'order_count', 'old_value': 91, 'new_value': 95}]
2025-05-27 12:00:50,154 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-27 12:00:50,638 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-27 12:00:50,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139382.14, 'new_value': 145156.3}, {'field': 'total_amount', 'old_value': 139382.14, 'new_value': 145156.3}, {'field': 'order_count', 'old_value': 5052, 'new_value': 5279}]
2025-05-27 12:00:50,638 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-27 12:00:51,170 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-27 12:00:51,170 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18329.14, 'new_value': 18800.04}, {'field': 'total_amount', 'old_value': 18329.14, 'new_value': 18800.04}, {'field': 'order_count', 'old_value': 32, 'new_value': 34}]
2025-05-27 12:00:51,170 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-27 12:00:51,685 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-27 12:00:51,685 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4678.0, 'new_value': 4698.0}, {'field': 'offline_amount', 'old_value': 19730.0, 'new_value': 21000.0}, {'field': 'total_amount', 'old_value': 24408.0, 'new_value': 25698.0}, {'field': 'order_count', 'old_value': 147, 'new_value': 153}]
2025-05-27 12:00:51,685 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-27 12:00:52,217 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-27 12:00:52,217 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 309042.54, 'new_value': 320109.58}, {'field': 'total_amount', 'old_value': 309851.54, 'new_value': 320918.58}, {'field': 'order_count', 'old_value': 3516, 'new_value': 3657}]
2025-05-27 12:00:52,217 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-27 12:00:52,670 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-27 12:00:52,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52012.0, 'new_value': 52350.0}, {'field': 'total_amount', 'old_value': 52012.0, 'new_value': 52350.0}, {'field': 'order_count', 'old_value': 108, 'new_value': 110}]
2025-05-27 12:00:52,670 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-27 12:00:53,092 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-27 12:00:53,092 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129962.26, 'new_value': 133280.52}, {'field': 'offline_amount', 'old_value': 527942.3, 'new_value': 542564.3}, {'field': 'total_amount', 'old_value': 657904.56, 'new_value': 675844.82}, {'field': 'order_count', 'old_value': 858, 'new_value': 888}]
2025-05-27 12:00:53,092 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-27 12:00:53,576 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-27 12:00:53,576 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 194663.79, 'new_value': 203512.99}, {'field': 'offline_amount', 'old_value': 439371.85, 'new_value': 446262.26}, {'field': 'total_amount', 'old_value': 634035.64, 'new_value': 649775.25}, {'field': 'order_count', 'old_value': 4642, 'new_value': 4794}]
2025-05-27 12:00:53,576 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-27 12:00:54,076 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-27 12:00:54,076 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28472.57, 'new_value': 29331.57}, {'field': 'total_amount', 'old_value': 28472.57, 'new_value': 29331.57}, {'field': 'order_count', 'old_value': 164, 'new_value': 172}]
2025-05-27 12:00:54,076 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-27 12:00:54,498 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-27 12:00:54,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177293.0, 'new_value': 196578.0}, {'field': 'total_amount', 'old_value': 177293.0, 'new_value': 196578.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 49}]
2025-05-27 12:00:54,498 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-27 12:00:54,967 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-27 12:00:54,967 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73233.47, 'new_value': 75748.11}, {'field': 'total_amount', 'old_value': 73233.47, 'new_value': 75748.11}, {'field': 'order_count', 'old_value': 3301, 'new_value': 3406}]
2025-05-27 12:00:54,967 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-27 12:00:55,373 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-27 12:00:55,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1750157.28, 'new_value': 1784644.98}, {'field': 'total_amount', 'old_value': 1803602.38, 'new_value': 1838090.08}, {'field': 'order_count', 'old_value': 3262, 'new_value': 3348}]
2025-05-27 12:00:55,373 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-27 12:00:55,826 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-27 12:00:55,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193298.0, 'new_value': 201093.62}, {'field': 'total_amount', 'old_value': 193298.0, 'new_value': 201093.62}, {'field': 'order_count', 'old_value': 11433, 'new_value': 11644}]
2025-05-27 12:00:55,826 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-27 12:00:56,295 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-27 12:00:56,295 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64129.0, 'new_value': 65299.0}, {'field': 'total_amount', 'old_value': 92775.0, 'new_value': 93945.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 42}]
2025-05-27 12:00:56,295 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-27 12:00:56,763 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-27 12:00:56,763 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7911.56, 'new_value': 8223.96}, {'field': 'offline_amount', 'old_value': 17321.67, 'new_value': 17822.67}, {'field': 'total_amount', 'old_value': 25233.23, 'new_value': 26046.63}, {'field': 'order_count', 'old_value': 847, 'new_value': 879}]
2025-05-27 12:00:56,763 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-27 12:00:57,170 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-27 12:00:57,170 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 181222.54, 'new_value': 188833.07}, {'field': 'offline_amount', 'old_value': 145296.75, 'new_value': 150783.25}, {'field': 'total_amount', 'old_value': 326519.29, 'new_value': 339616.32}, {'field': 'order_count', 'old_value': 2964, 'new_value': 3088}]
2025-05-27 12:00:57,170 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-27 12:00:57,591 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-27 12:00:57,591 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 382076.5, 'new_value': 400545.9}, {'field': 'offline_amount', 'old_value': 93478.3, 'new_value': 95266.3}, {'field': 'total_amount', 'old_value': 475554.8, 'new_value': 495812.2}, {'field': 'order_count', 'old_value': 609, 'new_value': 630}]
2025-05-27 12:00:57,591 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-27 12:00:58,029 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-27 12:00:58,029 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27916.0, 'new_value': 28882.0}, {'field': 'total_amount', 'old_value': 27916.0, 'new_value': 28882.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 81}]
2025-05-27 12:00:58,029 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-27 12:00:58,482 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-27 12:00:58,482 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67171.0, 'new_value': 68341.0}, {'field': 'total_amount', 'old_value': 67920.0, 'new_value': 69090.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 57}]
2025-05-27 12:00:58,482 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-27 12:00:58,982 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-27 12:00:58,982 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165677.8, 'new_value': 173755.8}, {'field': 'total_amount', 'old_value': 173253.6, 'new_value': 181331.6}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-27 12:00:58,982 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-27 12:00:59,373 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-27 12:00:59,373 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47306.32, 'new_value': 48817.23}, {'field': 'offline_amount', 'old_value': 95022.76, 'new_value': 96999.56}, {'field': 'total_amount', 'old_value': 142329.08, 'new_value': 145816.79}, {'field': 'order_count', 'old_value': 5190, 'new_value': 5337}]
2025-05-27 12:00:59,373 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-27 12:00:59,826 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-27 12:00:59,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120023.64, 'new_value': 120768.64}, {'field': 'total_amount', 'old_value': 125363.64, 'new_value': 126108.64}, {'field': 'order_count', 'old_value': 776, 'new_value': 32}]
2025-05-27 12:00:59,826 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-27 12:01:00,295 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-27 12:01:00,295 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 156769.53, 'new_value': 159013.74}, {'field': 'offline_amount', 'old_value': 120290.45, 'new_value': 121977.45}, {'field': 'total_amount', 'old_value': 277059.98, 'new_value': 280991.19}, {'field': 'order_count', 'old_value': 2746, 'new_value': 2791}]
2025-05-27 12:01:00,295 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-27 12:01:00,779 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-27 12:01:00,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71282.73, 'new_value': 73577.55}, {'field': 'total_amount', 'old_value': 71282.73, 'new_value': 73577.55}, {'field': 'order_count', 'old_value': 2047, 'new_value': 2109}]
2025-05-27 12:01:00,779 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-27 12:01:01,263 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-27 12:01:01,263 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 421640.77, 'new_value': 440355.51}, {'field': 'total_amount', 'old_value': 421640.77, 'new_value': 440355.51}, {'field': 'order_count', 'old_value': 538, 'new_value': 560}]
2025-05-27 12:01:01,263 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-27 12:01:01,748 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-27 12:01:01,748 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16997.29, 'new_value': 17749.97}, {'field': 'offline_amount', 'old_value': 419685.91, 'new_value': 465553.34}, {'field': 'total_amount', 'old_value': 436683.2, 'new_value': 483303.31}, {'field': 'order_count', 'old_value': 1854, 'new_value': 1912}]
2025-05-27 12:01:01,748 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-27 12:01:02,326 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-27 12:01:02,326 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81045.0, 'new_value': 88505.0}, {'field': 'offline_amount', 'old_value': 80583.76, 'new_value': 85956.76}, {'field': 'total_amount', 'old_value': 161628.76, 'new_value': 174461.76}, {'field': 'order_count', 'old_value': 195, 'new_value': 202}]
2025-05-27 12:01:02,326 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-27 12:01:02,779 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-27 12:01:02,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169779.0, 'new_value': 172687.9}, {'field': 'total_amount', 'old_value': 169779.0, 'new_value': 172687.9}, {'field': 'order_count', 'old_value': 395, 'new_value': 405}]
2025-05-27 12:01:02,779 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-27 12:01:03,248 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-27 12:01:03,248 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 201781.59, 'new_value': 202677.67}, {'field': 'offline_amount', 'old_value': 115427.04, 'new_value': 118400.74}, {'field': 'total_amount', 'old_value': 317208.63, 'new_value': 321078.41}, {'field': 'order_count', 'old_value': 3268, 'new_value': 3316}]
2025-05-27 12:01:03,248 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-27 12:01:03,732 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-27 12:01:03,732 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13389.0, 'new_value': 14927.0}, {'field': 'total_amount', 'old_value': 13389.0, 'new_value': 14927.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 33}]
2025-05-27 12:01:03,732 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-27 12:01:04,216 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-27 12:01:04,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98960.48, 'new_value': 100784.41}, {'field': 'total_amount', 'old_value': 98960.48, 'new_value': 100784.41}, {'field': 'order_count', 'old_value': 3766, 'new_value': 3843}]
2025-05-27 12:01:04,216 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-27 12:01:04,701 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-27 12:01:04,701 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14067.28, 'new_value': 15576.6}, {'field': 'total_amount', 'old_value': 24765.61, 'new_value': 26274.93}, {'field': 'order_count', 'old_value': 106, 'new_value': 110}]
2025-05-27 12:01:04,701 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-27 12:01:05,216 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-27 12:01:05,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111190.18, 'new_value': 112397.88}, {'field': 'total_amount', 'old_value': 111190.18, 'new_value': 112397.88}, {'field': 'order_count', 'old_value': 809, 'new_value': 819}]
2025-05-27 12:01:05,216 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-27 12:01:05,670 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-27 12:01:05,670 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2885.5, 'new_value': 2995.13}, {'field': 'offline_amount', 'old_value': 25200.92, 'new_value': 25723.84}, {'field': 'total_amount', 'old_value': 28086.42, 'new_value': 28718.97}, {'field': 'order_count', 'old_value': 1290, 'new_value': 1321}]
2025-05-27 12:01:05,670 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-27 12:01:06,170 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-27 12:01:06,170 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-27 12:01:06,170 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-27 12:01:06,576 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-27 12:01:06,576 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105259.5, 'new_value': 106964.3}, {'field': 'total_amount', 'old_value': 105259.5, 'new_value': 106964.3}, {'field': 'order_count', 'old_value': 320, 'new_value': 329}]
2025-05-27 12:01:06,576 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-27 12:01:07,201 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-27 12:01:07,201 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12992.81, 'new_value': 13344.01}, {'field': 'offline_amount', 'old_value': 126495.94, 'new_value': 130466.48}, {'field': 'total_amount', 'old_value': 139488.75, 'new_value': 143810.49}, {'field': 'order_count', 'old_value': 3633, 'new_value': 3767}]
2025-05-27 12:01:07,201 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-27 12:01:07,670 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-27 12:01:07,670 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 204070.68, 'new_value': 213690.33}, {'field': 'offline_amount', 'old_value': 355658.82, 'new_value': 363104.13}, {'field': 'total_amount', 'old_value': 559729.5, 'new_value': 576794.46}, {'field': 'order_count', 'old_value': 15813, 'new_value': 16434}]
2025-05-27 12:01:07,670 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-27 12:01:08,060 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-27 12:01:08,060 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45340.0, 'new_value': 45986.0}, {'field': 'total_amount', 'old_value': 45340.0, 'new_value': 45986.0}, {'field': 'order_count', 'old_value': 124, 'new_value': 128}]
2025-05-27 12:01:08,060 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-27 12:01:08,545 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-27 12:01:08,545 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7677.59, 'new_value': 8107.42}, {'field': 'offline_amount', 'old_value': 31085.0, 'new_value': 32571.0}, {'field': 'total_amount', 'old_value': 38762.59, 'new_value': 40678.42}, {'field': 'order_count', 'old_value': 203, 'new_value': 211}]
2025-05-27 12:01:08,545 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-27 12:01:08,998 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-27 12:01:08,998 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35932.0, 'new_value': 38388.0}, {'field': 'total_amount', 'old_value': 35932.0, 'new_value': 38388.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 115}]
2025-05-27 12:01:08,998 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-27 12:01:09,404 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-27 12:01:09,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45849.9, 'new_value': 46845.16}, {'field': 'total_amount', 'old_value': 46371.5, 'new_value': 47366.76}, {'field': 'order_count', 'old_value': 407, 'new_value': 416}]
2025-05-27 12:01:09,404 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-27 12:01:09,888 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-27 12:01:09,888 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5736.0, 'new_value': 6066.0}, {'field': 'offline_amount', 'old_value': 25792.3, 'new_value': 27116.8}, {'field': 'total_amount', 'old_value': 31528.3, 'new_value': 33182.8}, {'field': 'order_count', 'old_value': 1235, 'new_value': 1283}]
2025-05-27 12:01:09,888 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-27 12:01:10,373 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-27 12:01:10,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90229.56, 'new_value': 92720.66}, {'field': 'total_amount', 'old_value': 90229.56, 'new_value': 92720.66}, {'field': 'order_count', 'old_value': 337, 'new_value': 344}]
2025-05-27 12:01:10,373 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-27 12:01:10,810 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-27 12:01:10,810 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1286000.0, 'new_value': 1321000.0}, {'field': 'total_amount', 'old_value': 1286000.0, 'new_value': 1321000.0}, {'field': 'order_count', 'old_value': 344, 'new_value': 345}]
2025-05-27 12:01:10,810 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-27 12:01:11,373 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-27 12:01:11,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62722.0, 'new_value': 69785.0}, {'field': 'total_amount', 'old_value': 62722.0, 'new_value': 69785.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 29}]
2025-05-27 12:01:11,373 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-27 12:01:11,810 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-27 12:01:11,810 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19369.05, 'new_value': 20188.01}, {'field': 'offline_amount', 'old_value': 31676.28, 'new_value': 32670.38}, {'field': 'total_amount', 'old_value': 51045.33, 'new_value': 52858.39}, {'field': 'order_count', 'old_value': 2330, 'new_value': 2446}]
2025-05-27 12:01:11,810 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-27 12:01:12,357 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-27 12:01:12,357 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193141.3, 'new_value': 195129.6}, {'field': 'total_amount', 'old_value': 193141.3, 'new_value': 195129.6}, {'field': 'order_count', 'old_value': 722, 'new_value': 733}]
2025-05-27 12:01:12,357 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-27 12:01:12,873 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-27 12:01:12,873 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 909790.16, 'new_value': 936410.16}, {'field': 'total_amount', 'old_value': 909790.16, 'new_value': 936410.16}, {'field': 'order_count', 'old_value': 3541, 'new_value': 3677}]
2025-05-27 12:01:12,873 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-27 12:01:13,357 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-27 12:01:13,357 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 304435.0, 'new_value': 312732.0}, {'field': 'total_amount', 'old_value': 304435.0, 'new_value': 312732.0}, {'field': 'order_count', 'old_value': 256, 'new_value': 261}]
2025-05-27 12:01:13,357 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-27 12:01:13,857 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-27 12:01:13,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244299.4, 'new_value': 247299.4}, {'field': 'total_amount', 'old_value': 282933.3, 'new_value': 285933.3}, {'field': 'order_count', 'old_value': 2283, 'new_value': 2284}]
2025-05-27 12:01:13,857 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-27 12:01:14,373 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-27 12:01:14,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 294888.81, 'new_value': 305508.97}, {'field': 'total_amount', 'old_value': 294888.81, 'new_value': 305508.97}, {'field': 'order_count', 'old_value': 8149, 'new_value': 8467}]
2025-05-27 12:01:14,373 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-27 12:01:14,779 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-27 12:01:14,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150068.07, 'new_value': 154164.25}, {'field': 'total_amount', 'old_value': 150068.07, 'new_value': 154164.25}, {'field': 'order_count', 'old_value': 742, 'new_value': 763}]
2025-05-27 12:01:14,779 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-27 12:01:15,248 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-27 12:01:15,248 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23353.7, 'new_value': 24086.17}, {'field': 'offline_amount', 'old_value': 42758.44, 'new_value': 44728.34}, {'field': 'total_amount', 'old_value': 66112.14, 'new_value': 68814.51}, {'field': 'order_count', 'old_value': 2377, 'new_value': 2478}]
2025-05-27 12:01:15,248 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-27 12:01:15,716 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-27 12:01:15,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73996.0, 'new_value': 76592.0}, {'field': 'total_amount', 'old_value': 76404.0, 'new_value': 79000.0}, {'field': 'order_count', 'old_value': 311, 'new_value': 327}]
2025-05-27 12:01:15,716 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-27 12:01:16,201 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-27 12:01:16,201 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144896.38, 'new_value': 150896.38}, {'field': 'total_amount', 'old_value': 144896.38, 'new_value': 150896.38}, {'field': 'order_count', 'old_value': 13065, 'new_value': 13066}]
2025-05-27 12:01:16,201 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-27 12:01:16,623 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-27 12:01:16,623 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20662.2, 'new_value': 21049.2}, {'field': 'offline_amount', 'old_value': 63182.36, 'new_value': 64604.96}, {'field': 'total_amount', 'old_value': 83844.56, 'new_value': 85654.16}, {'field': 'order_count', 'old_value': 928, 'new_value': 953}]
2025-05-27 12:01:16,623 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-27 12:01:17,091 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-27 12:01:17,091 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100774.9, 'new_value': 104012.8}, {'field': 'offline_amount', 'old_value': 140382.3, 'new_value': 146320.3}, {'field': 'total_amount', 'old_value': 241157.2, 'new_value': 250333.1}, {'field': 'order_count', 'old_value': 1561, 'new_value': 1579}]
2025-05-27 12:01:17,091 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-27 12:01:17,544 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-27 12:01:17,544 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13856.26, 'new_value': 14643.76}, {'field': 'offline_amount', 'old_value': 92398.96, 'new_value': 94712.46}, {'field': 'total_amount', 'old_value': 106255.22, 'new_value': 109356.22}, {'field': 'order_count', 'old_value': 2858, 'new_value': 2929}]
2025-05-27 12:01:17,544 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-27 12:01:17,966 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-27 12:01:17,966 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22779.69, 'new_value': 23790.07}, {'field': 'offline_amount', 'old_value': 42052.48, 'new_value': 43943.87}, {'field': 'total_amount', 'old_value': 64832.17, 'new_value': 67733.94}, {'field': 'order_count', 'old_value': 3409, 'new_value': 3564}]
2025-05-27 12:01:17,966 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-27 12:01:18,404 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-27 12:01:18,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116392.1, 'new_value': 117744.0}, {'field': 'total_amount', 'old_value': 116392.1, 'new_value': 117744.0}, {'field': 'order_count', 'old_value': 1161, 'new_value': 1174}]
2025-05-27 12:01:18,404 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-27 12:01:18,873 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-27 12:01:18,873 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11106.95, 'new_value': 11572.61}, {'field': 'offline_amount', 'old_value': 12585.63, 'new_value': 12850.11}, {'field': 'total_amount', 'old_value': 23692.58, 'new_value': 24422.72}, {'field': 'order_count', 'old_value': 1918, 'new_value': 1988}]
2025-05-27 12:01:18,873 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-27 12:01:19,326 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-27 12:01:19,326 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50870.0, 'new_value': 51747.0}, {'field': 'total_amount', 'old_value': 51219.0, 'new_value': 52096.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 97}]
2025-05-27 12:01:19,326 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-27 12:01:19,763 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-27 12:01:19,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6438601.0, 'new_value': 6654713.0}, {'field': 'total_amount', 'old_value': 6438601.0, 'new_value': 6654713.0}, {'field': 'order_count', 'old_value': 108694, 'new_value': 112621}]
2025-05-27 12:01:19,763 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-27 12:01:20,294 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-27 12:01:20,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29908.0, 'new_value': 32152.0}, {'field': 'total_amount', 'old_value': 29908.0, 'new_value': 32152.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-05-27 12:01:20,294 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-27 12:01:20,794 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-27 12:01:20,794 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57460.8, 'new_value': 59376.91}, {'field': 'offline_amount', 'old_value': 407839.81, 'new_value': 413114.82}, {'field': 'total_amount', 'old_value': 465300.61, 'new_value': 472491.73}, {'field': 'order_count', 'old_value': 3875, 'new_value': 3939}]
2025-05-27 12:01:20,794 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-27 12:01:21,294 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-27 12:01:21,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122029.31, 'new_value': 124339.53}, {'field': 'total_amount', 'old_value': 122029.31, 'new_value': 124339.53}, {'field': 'order_count', 'old_value': 3546, 'new_value': 3623}]
2025-05-27 12:01:21,294 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-27 12:01:21,826 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-27 12:01:21,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 266001.82, 'new_value': 278597.68}, {'field': 'total_amount', 'old_value': 394232.69, 'new_value': 406828.55}, {'field': 'order_count', 'old_value': 4623, 'new_value': 4786}]
2025-05-27 12:01:21,826 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-27 12:01:22,326 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-27 12:01:22,326 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31920.88, 'new_value': 34058.48}, {'field': 'total_amount', 'old_value': 31920.88, 'new_value': 34058.48}, {'field': 'order_count', 'old_value': 30, 'new_value': 34}]
2025-05-27 12:01:22,326 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-27 12:01:22,748 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-27 12:01:22,748 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122384.0, 'new_value': 126584.0}, {'field': 'total_amount', 'old_value': 122384.0, 'new_value': 126584.0}, {'field': 'order_count', 'old_value': 4384, 'new_value': 4385}]
2025-05-27 12:01:22,748 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-27 12:01:23,201 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-27 12:01:23,201 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87959.43, 'new_value': 90320.21}, {'field': 'offline_amount', 'old_value': 39929.07, 'new_value': 40234.71}, {'field': 'total_amount', 'old_value': 127888.5, 'new_value': 130554.92}, {'field': 'order_count', 'old_value': 7843, 'new_value': 8015}]
2025-05-27 12:01:23,201 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-27 12:01:23,638 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-27 12:01:23,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31109.07, 'new_value': 32209.07}, {'field': 'total_amount', 'old_value': 31109.07, 'new_value': 32209.07}, {'field': 'order_count', 'old_value': 2976, 'new_value': 2977}]
2025-05-27 12:01:23,638 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-27 12:01:24,060 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-27 12:01:24,060 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171553.73, 'new_value': 173957.03}, {'field': 'total_amount', 'old_value': 171553.73, 'new_value': 173957.03}, {'field': 'order_count', 'old_value': 288, 'new_value': 293}]
2025-05-27 12:01:24,060 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-27 12:01:24,498 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-27 12:01:24,498 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 224736.0, 'new_value': 234420.0}, {'field': 'total_amount', 'old_value': 224736.0, 'new_value': 234420.0}, {'field': 'order_count', 'old_value': 18728, 'new_value': 19535}]
2025-05-27 12:01:24,498 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-27 12:01:24,966 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-27 12:01:24,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33151.64, 'new_value': 38851.64}, {'field': 'total_amount', 'old_value': 93929.34, 'new_value': 99629.34}, {'field': 'order_count', 'old_value': 11518, 'new_value': 11519}]
2025-05-27 12:01:24,966 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-27 12:01:25,498 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-27 12:01:25,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112144.98, 'new_value': 113906.98}, {'field': 'total_amount', 'old_value': 112144.98, 'new_value': 113906.98}, {'field': 'order_count', 'old_value': 1004, 'new_value': 1026}]
2025-05-27 12:01:25,498 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-27 12:01:26,013 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-27 12:01:26,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98252.03, 'new_value': 101518.73}, {'field': 'offline_amount', 'old_value': 257526.59, 'new_value': 263711.88}, {'field': 'total_amount', 'old_value': 355778.62, 'new_value': 365230.61}, {'field': 'order_count', 'old_value': 11887, 'new_value': 12268}]
2025-05-27 12:01:26,013 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-27 12:01:26,435 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-27 12:01:26,435 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102967.28, 'new_value': 104990.95}, {'field': 'offline_amount', 'old_value': 323648.28, 'new_value': 329354.28}, {'field': 'total_amount', 'old_value': 426615.56, 'new_value': 434345.23}, {'field': 'order_count', 'old_value': 5286, 'new_value': 5432}]
2025-05-27 12:01:26,435 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-27 12:01:26,857 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-27 12:01:26,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13966.2, 'new_value': 14564.2}, {'field': 'total_amount', 'old_value': 13966.2, 'new_value': 14564.2}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-27 12:01:26,857 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-27 12:01:27,326 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-27 12:01:27,326 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7273200.0, 'new_value': 7323200.0}, {'field': 'total_amount', 'old_value': 7273200.0, 'new_value': 7323200.0}, {'field': 'order_count', 'old_value': 221, 'new_value': 222}]
2025-05-27 12:01:27,326 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-27 12:01:27,748 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-27 12:01:27,748 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42980.2, 'new_value': 46701.5}, {'field': 'total_amount', 'old_value': 42980.2, 'new_value': 46701.5}, {'field': 'order_count', 'old_value': 63, 'new_value': 67}]
2025-05-27 12:01:27,748 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-27 12:01:28,154 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-27 12:01:28,154 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49549.5, 'new_value': 51608.5}, {'field': 'total_amount', 'old_value': 49549.5, 'new_value': 51608.5}, {'field': 'order_count', 'old_value': 2496, 'new_value': 2590}]
2025-05-27 12:01:28,154 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-27 12:01:28,654 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-27 12:01:28,654 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 312129.8, 'new_value': 321593.74}, {'field': 'offline_amount', 'old_value': 19145.82, 'new_value': 19354.62}, {'field': 'total_amount', 'old_value': 331275.62, 'new_value': 340948.36}, {'field': 'order_count', 'old_value': 13221, 'new_value': 13599}]
2025-05-27 12:01:28,654 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-27 12:01:29,076 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-27 12:01:29,076 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117510.9, 'new_value': 120148.9}, {'field': 'total_amount', 'old_value': 139164.8, 'new_value': 141802.8}, {'field': 'order_count', 'old_value': 190, 'new_value': 195}]
2025-05-27 12:01:29,076 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-27 12:01:29,560 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-27 12:01:29,560 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45390.45, 'new_value': 47038.35}, {'field': 'total_amount', 'old_value': 45390.45, 'new_value': 47038.35}, {'field': 'order_count', 'old_value': 2034, 'new_value': 2096}]
2025-05-27 12:01:29,560 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-27 12:01:29,998 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-27 12:01:29,998 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47541.87, 'new_value': 51214.29}, {'field': 'offline_amount', 'old_value': 250457.62, 'new_value': 254230.84}, {'field': 'total_amount', 'old_value': 297999.49, 'new_value': 305445.13}, {'field': 'order_count', 'old_value': 5474, 'new_value': 5770}]
2025-05-27 12:01:29,998 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-27 12:01:30,419 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-27 12:01:30,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43885.0, 'new_value': 44632.0}, {'field': 'total_amount', 'old_value': 44262.0, 'new_value': 45009.0}, {'field': 'order_count', 'old_value': 233, 'new_value': 240}]
2025-05-27 12:01:30,419 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-27 12:01:30,888 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-27 12:01:30,888 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 416643.0, 'new_value': 419643.0}, {'field': 'total_amount', 'old_value': 425461.99, 'new_value': 428461.99}, {'field': 'order_count', 'old_value': 78, 'new_value': 79}]
2025-05-27 12:01:30,888 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-27 12:01:31,326 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-27 12:01:31,326 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134514.95, 'new_value': 138483.82}, {'field': 'offline_amount', 'old_value': 276841.21, 'new_value': 281742.35}, {'field': 'total_amount', 'old_value': 411356.16, 'new_value': 420226.17}, {'field': 'order_count', 'old_value': 5116, 'new_value': 5214}]
2025-05-27 12:01:31,326 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-27 12:01:31,748 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-27 12:01:31,748 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37078.0, 'new_value': 37266.0}, {'field': 'offline_amount', 'old_value': 301217.0, 'new_value': 312567.0}, {'field': 'total_amount', 'old_value': 338295.0, 'new_value': 349833.0}, {'field': 'order_count', 'old_value': 309, 'new_value': 322}]
2025-05-27 12:01:31,748 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-27 12:01:32,201 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-27 12:01:32,201 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121434.6, 'new_value': 127627.6}, {'field': 'total_amount', 'old_value': 252898.95, 'new_value': 259091.95}, {'field': 'order_count', 'old_value': 6649, 'new_value': 6852}]
2025-05-27 12:01:32,201 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-27 12:01:32,638 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-27 12:01:32,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79973.05, 'new_value': 81710.35}, {'field': 'total_amount', 'old_value': 79973.05, 'new_value': 81710.35}, {'field': 'order_count', 'old_value': 580, 'new_value': 591}]
2025-05-27 12:01:32,638 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-27 12:01:33,060 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-27 12:01:33,060 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 315627.53, 'new_value': 326220.94}, {'field': 'total_amount', 'old_value': 348707.74, 'new_value': 359301.15}, {'field': 'order_count', 'old_value': 14713, 'new_value': 15139}]
2025-05-27 12:01:33,060 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-27 12:01:33,482 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-27 12:01:33,482 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 421737.0, 'new_value': 432591.0}, {'field': 'total_amount', 'old_value': 421737.0, 'new_value': 432591.0}, {'field': 'order_count', 'old_value': 11426, 'new_value': 11795}]
2025-05-27 12:01:33,482 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-27 12:01:33,935 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-27 12:01:33,935 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 436530.45, 'new_value': 456530.45}, {'field': 'total_amount', 'old_value': 436530.45, 'new_value': 456530.45}, {'field': 'order_count', 'old_value': 817, 'new_value': 818}]
2025-05-27 12:01:33,935 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-27 12:01:34,388 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-27 12:01:34,388 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76829.77, 'new_value': 81052.57}, {'field': 'offline_amount', 'old_value': 259980.58, 'new_value': 262709.61}, {'field': 'total_amount', 'old_value': 336810.35, 'new_value': 343762.18}, {'field': 'order_count', 'old_value': 4073, 'new_value': 4115}]
2025-05-27 12:01:34,388 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-27 12:01:34,888 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-27 12:01:34,888 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57153.8, 'new_value': 59442.97}, {'field': 'offline_amount', 'old_value': 34066.05, 'new_value': 35032.87}, {'field': 'total_amount', 'old_value': 91219.85, 'new_value': 94475.84}, {'field': 'order_count', 'old_value': 4964, 'new_value': 5125}]
2025-05-27 12:01:34,888 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-27 12:01:35,372 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-27 12:01:35,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95547.0, 'new_value': 95847.0}, {'field': 'total_amount', 'old_value': 95547.0, 'new_value': 95847.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-27 12:01:35,372 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-27 12:01:35,826 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-27 12:01:35,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125480.6, 'new_value': 129779.6}, {'field': 'total_amount', 'old_value': 181410.65, 'new_value': 185709.65}, {'field': 'order_count', 'old_value': 9958, 'new_value': 10203}]
2025-05-27 12:01:35,826 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-27 12:01:36,388 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-27 12:01:36,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28482.0, 'new_value': 28876.0}, {'field': 'total_amount', 'old_value': 28482.0, 'new_value': 28876.0}, {'field': 'order_count', 'old_value': 291, 'new_value': 296}]
2025-05-27 12:01:36,388 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-27 12:01:36,810 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-27 12:01:36,810 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 424052.34, 'new_value': 434260.18}, {'field': 'total_amount', 'old_value': 424052.34, 'new_value': 434260.18}, {'field': 'order_count', 'old_value': 1512, 'new_value': 1550}]
2025-05-27 12:01:36,810 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-27 12:01:37,263 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-27 12:01:37,263 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 11276.2}, {'field': 'total_amount', 'old_value': 316371.4, 'new_value': 327647.6}, {'field': 'order_count', 'old_value': 7926, 'new_value': 8225}]
2025-05-27 12:01:37,263 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-27 12:01:37,732 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-27 12:01:37,732 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39333.24, 'new_value': 40835.44}, {'field': 'total_amount', 'old_value': 39333.24, 'new_value': 40835.44}, {'field': 'order_count', 'old_value': 5083, 'new_value': 5272}]
2025-05-27 12:01:37,732 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-27 12:01:38,294 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-27 12:01:38,294 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25744.08, 'new_value': 26898.5}, {'field': 'offline_amount', 'old_value': 33411.32, 'new_value': 34547.33}, {'field': 'total_amount', 'old_value': 59155.4, 'new_value': 61445.83}, {'field': 'order_count', 'old_value': 2682, 'new_value': 2785}]
2025-05-27 12:01:38,294 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-27 12:01:38,685 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-27 12:01:38,685 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81699.48, 'new_value': 83550.97}, {'field': 'offline_amount', 'old_value': 120657.29, 'new_value': 122064.69}, {'field': 'total_amount', 'old_value': 202356.77, 'new_value': 205615.66}, {'field': 'order_count', 'old_value': 2104, 'new_value': 2146}]
2025-05-27 12:01:38,685 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-27 12:01:39,185 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-27 12:01:39,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86018.0, 'new_value': 89666.0}, {'field': 'total_amount', 'old_value': 91219.0, 'new_value': 94867.0}, {'field': 'order_count', 'old_value': 260, 'new_value': 279}]
2025-05-27 12:01:39,185 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-27 12:01:39,591 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-27 12:01:39,607 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1734232.24, 'new_value': 1787053.02}, {'field': 'offline_amount', 'old_value': 157357.3, 'new_value': 162440.3}, {'field': 'total_amount', 'old_value': 1891589.54, 'new_value': 1949493.32}, {'field': 'order_count', 'old_value': 6494, 'new_value': 6732}]
2025-05-27 12:01:39,607 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-27 12:01:40,107 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-27 12:01:40,107 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33360.0, 'new_value': 68360.0}, {'field': 'total_amount', 'old_value': 34736.0, 'new_value': 69736.0}, {'field': 'order_count', 'old_value': 3522, 'new_value': 3523}]
2025-05-27 12:01:40,107 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-27 12:01:40,513 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-27 12:01:40,513 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17682.8, 'new_value': 17815.0}, {'field': 'offline_amount', 'old_value': 43540.9, 'new_value': 46677.9}, {'field': 'total_amount', 'old_value': 61223.7, 'new_value': 64492.9}, {'field': 'order_count', 'old_value': 191, 'new_value': 199}]
2025-05-27 12:01:40,513 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-27 12:01:40,904 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-27 12:01:40,904 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 454914.08, 'new_value': 470991.32}, {'field': 'total_amount', 'old_value': 454914.08, 'new_value': 470991.32}, {'field': 'order_count', 'old_value': 2308, 'new_value': 2388}]
2025-05-27 12:01:40,904 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-27 12:01:41,326 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-27 12:01:41,326 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10776.0, 'new_value': 11128.0}, {'field': 'total_amount', 'old_value': 12782.0, 'new_value': 13134.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 127}]
2025-05-27 12:01:41,326 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-27 12:01:41,794 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-27 12:01:41,794 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28831.0, 'new_value': 29649.0}, {'field': 'total_amount', 'old_value': 28831.0, 'new_value': 29649.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 136}]
2025-05-27 12:01:41,794 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-27 12:01:42,404 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-27 12:01:42,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142676.0, 'new_value': 142745.0}, {'field': 'total_amount', 'old_value': 142676.0, 'new_value': 142745.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 83}]
2025-05-27 12:01:42,404 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-27 12:01:42,826 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-27 12:01:42,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81415.0, 'new_value': 83335.0}, {'field': 'total_amount', 'old_value': 81415.0, 'new_value': 83335.0}, {'field': 'order_count', 'old_value': 565, 'new_value': 581}]
2025-05-27 12:01:42,826 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-27 12:01:43,279 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-27 12:01:43,279 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95678.6, 'new_value': 106270.2}, {'field': 'offline_amount', 'old_value': 152459.3, 'new_value': 152796.1}, {'field': 'total_amount', 'old_value': 248137.9, 'new_value': 259066.3}, {'field': 'order_count', 'old_value': 5011, 'new_value': 5229}]
2025-05-27 12:01:43,279 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-27 12:01:43,669 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-27 12:01:43,669 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 480864.2, 'new_value': 491214.52}, {'field': 'total_amount', 'old_value': 480864.2, 'new_value': 491214.52}, {'field': 'order_count', 'old_value': 6599, 'new_value': 6738}]
2025-05-27 12:01:43,669 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-27 12:01:44,107 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-27 12:01:44,107 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 610529.42, 'new_value': 628596.72}, {'field': 'total_amount', 'old_value': 612442.47, 'new_value': 630509.77}, {'field': 'order_count', 'old_value': 1459, 'new_value': 1504}]
2025-05-27 12:01:44,107 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-27 12:01:44,529 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-27 12:01:44,529 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178290.0, 'new_value': 185150.0}, {'field': 'total_amount', 'old_value': 178291.0, 'new_value': 185151.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-27 12:01:44,529 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-27 12:01:45,138 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-27 12:01:45,138 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90308.16, 'new_value': 92504.83}, {'field': 'total_amount', 'old_value': 90308.16, 'new_value': 92504.83}, {'field': 'order_count', 'old_value': 2812, 'new_value': 2888}]
2025-05-27 12:01:45,138 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-27 12:01:45,560 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-27 12:01:45,560 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9604.55, 'new_value': 9938.45}, {'field': 'offline_amount', 'old_value': 32481.36, 'new_value': 33213.67}, {'field': 'total_amount', 'old_value': 42085.91, 'new_value': 43152.12}, {'field': 'order_count', 'old_value': 1484, 'new_value': 1522}]
2025-05-27 12:01:45,560 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-27 12:01:46,044 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-27 12:01:46,044 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1069355.0, 'new_value': 1088852.0}, {'field': 'total_amount', 'old_value': 1069355.0, 'new_value': 1088852.0}, {'field': 'order_count', 'old_value': 130, 'new_value': 133}]
2025-05-27 12:01:46,044 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-27 12:01:46,560 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-27 12:01:46,560 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54508.55, 'new_value': 55769.67}, {'field': 'total_amount', 'old_value': 100081.73, 'new_value': 101342.85}, {'field': 'order_count', 'old_value': 347, 'new_value': 353}]
2025-05-27 12:01:46,560 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-27 12:01:47,060 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-27 12:01:47,060 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151213.49, 'new_value': 153054.4}, {'field': 'total_amount', 'old_value': 151213.49, 'new_value': 153054.4}, {'field': 'order_count', 'old_value': 3905, 'new_value': 3963}]
2025-05-27 12:01:47,060 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-27 12:01:47,497 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-27 12:01:47,497 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35286.18, 'new_value': 36904.39}, {'field': 'offline_amount', 'old_value': 320460.27, 'new_value': 325686.72}, {'field': 'total_amount', 'old_value': 355746.45, 'new_value': 362591.11}, {'field': 'order_count', 'old_value': 8308, 'new_value': 8460}]
2025-05-27 12:01:47,497 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-27 12:01:48,044 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-27 12:01:48,044 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8557.67, 'new_value': 8802.13}, {'field': 'offline_amount', 'old_value': 129405.6, 'new_value': 133337.7}, {'field': 'total_amount', 'old_value': 137963.27, 'new_value': 142139.83}, {'field': 'order_count', 'old_value': 6968, 'new_value': 7140}]
2025-05-27 12:01:48,044 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-27 12:01:48,497 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-27 12:01:48,497 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 323379.92, 'new_value': 331495.92}, {'field': 'offline_amount', 'old_value': 9507.5, 'new_value': 11576.5}, {'field': 'total_amount', 'old_value': 332887.42, 'new_value': 343072.42}, {'field': 'order_count', 'old_value': 2893, 'new_value': 2983}]
2025-05-27 12:01:48,497 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-27 12:01:48,997 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-27 12:01:48,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21641.0, 'new_value': 22120.0}, {'field': 'total_amount', 'old_value': 21641.0, 'new_value': 22120.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 121}]
2025-05-27 12:01:48,997 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-27 12:01:49,513 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-27 12:01:49,513 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 420883.8, 'new_value': 431919.0}, {'field': 'total_amount', 'old_value': 420883.8, 'new_value': 431919.0}, {'field': 'order_count', 'old_value': 2081, 'new_value': 2123}]
2025-05-27 12:01:49,513 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-27 12:01:50,013 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-27 12:01:50,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28383.6, 'new_value': 28838.8}, {'field': 'total_amount', 'old_value': 28383.6, 'new_value': 28838.8}, {'field': 'order_count', 'old_value': 755, 'new_value': 790}]
2025-05-27 12:01:50,013 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-27 12:01:50,357 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-27 12:01:50,357 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134781.41, 'new_value': 138042.23}, {'field': 'offline_amount', 'old_value': 55162.73, 'new_value': 56304.6}, {'field': 'total_amount', 'old_value': 189944.14, 'new_value': 194346.83}, {'field': 'order_count', 'old_value': 11608, 'new_value': 11874}]
2025-05-27 12:01:50,357 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-27 12:01:50,857 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-27 12:01:50,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134580.87, 'new_value': 136580.87}, {'field': 'total_amount', 'old_value': 153106.6, 'new_value': 155106.6}, {'field': 'order_count', 'old_value': 4579, 'new_value': 4580}]
2025-05-27 12:01:50,857 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-27 12:01:51,357 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-27 12:01:51,357 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105201.4, 'new_value': 107852.3}, {'field': 'total_amount', 'old_value': 105201.4, 'new_value': 107852.3}, {'field': 'order_count', 'old_value': 5236, 'new_value': 5372}]
2025-05-27 12:01:51,357 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-27 12:01:51,779 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-27 12:01:51,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7034.1, 'new_value': 7044.0}, {'field': 'offline_amount', 'old_value': 49353.69, 'new_value': 51020.79}, {'field': 'total_amount', 'old_value': 56387.79, 'new_value': 58064.79}, {'field': 'order_count', 'old_value': 533, 'new_value': 555}]
2025-05-27 12:01:51,779 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-27 12:01:52,201 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-27 12:01:52,201 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84594.47, 'new_value': 90761.55}, {'field': 'total_amount', 'old_value': 223647.97, 'new_value': 229815.05}, {'field': 'order_count', 'old_value': 9614, 'new_value': 9918}]
2025-05-27 12:01:52,201 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-27 12:01:52,716 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-27 12:01:52,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34006.8, 'new_value': 39332.8}, {'field': 'total_amount', 'old_value': 50748.4, 'new_value': 56074.4}, {'field': 'order_count', 'old_value': 98, 'new_value': 103}]
2025-05-27 12:01:52,716 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-27 12:01:53,200 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-27 12:01:53,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 208312.0, 'new_value': 214544.0}, {'field': 'total_amount', 'old_value': 208312.0, 'new_value': 214544.0}, {'field': 'order_count', 'old_value': 255, 'new_value': 261}]
2025-05-27 12:01:53,200 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-27 12:01:53,654 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-27 12:01:53,654 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 720855.36, 'new_value': 732702.84}, {'field': 'total_amount', 'old_value': 720855.36, 'new_value': 732702.84}, {'field': 'order_count', 'old_value': 13697, 'new_value': 13905}]
2025-05-27 12:01:53,654 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-27 12:01:54,122 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-27 12:01:54,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 270267.32, 'new_value': 277566.28}, {'field': 'total_amount', 'old_value': 282318.29, 'new_value': 289617.25}, {'field': 'order_count', 'old_value': 12110, 'new_value': 12486}]
2025-05-27 12:01:54,122 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-27 12:01:54,591 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-27 12:01:54,591 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 949524.0, 'new_value': 989505.0}, {'field': 'total_amount', 'old_value': 949524.0, 'new_value': 989505.0}, {'field': 'order_count', 'old_value': 2023, 'new_value': 2127}]
2025-05-27 12:01:54,591 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-27 12:01:55,122 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-27 12:01:55,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12269.0, 'new_value': 14069.0}, {'field': 'total_amount', 'old_value': 12269.0, 'new_value': 14069.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 22}]
2025-05-27 12:01:55,122 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-27 12:01:55,622 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-27 12:01:55,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215333.0, 'new_value': 218323.0}, {'field': 'total_amount', 'old_value': 215333.0, 'new_value': 218323.0}, {'field': 'order_count', 'old_value': 669, 'new_value': 683}]
2025-05-27 12:01:55,622 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-27 12:01:56,044 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-27 12:01:56,044 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1815.06, 'new_value': 1855.2}, {'field': 'offline_amount', 'old_value': 23020.42, 'new_value': 23130.02}, {'field': 'total_amount', 'old_value': 24835.48, 'new_value': 24985.22}, {'field': 'order_count', 'old_value': 891, 'new_value': 897}]
2025-05-27 12:01:56,044 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-27 12:01:56,497 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-27 12:01:56,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 843715.0, 'new_value': 862154.0}, {'field': 'total_amount', 'old_value': 843715.0, 'new_value': 862154.0}, {'field': 'order_count', 'old_value': 3800, 'new_value': 3888}]
2025-05-27 12:01:56,497 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-27 12:01:56,950 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-27 12:01:56,950 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36805.0, 'new_value': 37103.0}, {'field': 'total_amount', 'old_value': 36805.0, 'new_value': 37103.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-27 12:01:56,950 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-27 12:01:57,419 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-27 12:01:57,419 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6995.1, 'new_value': 7381.83}, {'field': 'offline_amount', 'old_value': 376888.64, 'new_value': 386975.04}, {'field': 'total_amount', 'old_value': 383883.74, 'new_value': 394356.87}, {'field': 'order_count', 'old_value': 18463, 'new_value': 18948}]
2025-05-27 12:01:57,419 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-27 12:01:57,904 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-27 12:01:57,904 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65657.34, 'new_value': 67758.12}, {'field': 'offline_amount', 'old_value': 86299.17, 'new_value': 87196.17}, {'field': 'total_amount', 'old_value': 151956.51, 'new_value': 154954.29}, {'field': 'order_count', 'old_value': 7012, 'new_value': 7160}]
2025-05-27 12:01:57,904 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-27 12:01:58,310 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-27 12:01:58,310 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 380282.29, 'new_value': 389670.7}, {'field': 'total_amount', 'old_value': 402445.41, 'new_value': 411833.82}, {'field': 'order_count', 'old_value': 17066, 'new_value': 17506}]
2025-05-27 12:01:58,310 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-27 12:01:58,779 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-27 12:01:58,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29111.14, 'new_value': 30047.56}, {'field': 'offline_amount', 'old_value': 241169.94, 'new_value': 246353.44}, {'field': 'total_amount', 'old_value': 270281.08, 'new_value': 276401.0}, {'field': 'order_count', 'old_value': 8596, 'new_value': 8814}]
2025-05-27 12:01:58,779 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-27 12:01:59,310 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-27 12:01:59,310 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 392597.18, 'new_value': 397097.18}, {'field': 'total_amount', 'old_value': 392597.18, 'new_value': 397097.18}, {'field': 'order_count', 'old_value': 2864, 'new_value': 2865}]
2025-05-27 12:01:59,310 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-27 12:01:59,763 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-27 12:01:59,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92970.0, 'new_value': 94592.0}, {'field': 'total_amount', 'old_value': 108174.0, 'new_value': 109796.0}, {'field': 'order_count', 'old_value': 2496, 'new_value': 2541}]
2025-05-27 12:01:59,763 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM9E
2025-05-27 12:02:00,200 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM9E
2025-05-27 12:02:00,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 282568.0, 'new_value': 349450.0}, {'field': 'total_amount', 'old_value': 282568.0, 'new_value': 349450.0}, {'field': 'order_count', 'old_value': 658, 'new_value': 815}]
2025-05-27 12:02:00,200 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-27 12:02:00,685 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-27 12:02:00,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101436.0, 'new_value': 105136.0}, {'field': 'total_amount', 'old_value': 101436.0, 'new_value': 105136.0}, {'field': 'order_count', 'old_value': 684, 'new_value': 685}]
2025-05-27 12:02:00,685 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-27 12:02:01,138 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-27 12:02:01,138 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189166.16, 'new_value': 193818.25}, {'field': 'total_amount', 'old_value': 189166.16, 'new_value': 193818.25}, {'field': 'order_count', 'old_value': 2452, 'new_value': 2518}]
2025-05-27 12:02:01,138 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-27 12:02:01,529 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-27 12:02:01,529 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 123587.79, 'new_value': 129510.07}, {'field': 'offline_amount', 'old_value': 370069.95, 'new_value': 373561.75}, {'field': 'total_amount', 'old_value': 493657.74, 'new_value': 503071.82}, {'field': 'order_count', 'old_value': 4204, 'new_value': 4339}]
2025-05-27 12:02:01,529 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-27 12:02:02,029 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-27 12:02:02,029 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 266766.46, 'new_value': 307485.46}, {'field': 'total_amount', 'old_value': 268068.46, 'new_value': 308787.46}, {'field': 'order_count', 'old_value': 51, 'new_value': 56}]
2025-05-27 12:02:02,029 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-27 12:02:02,482 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-27 12:02:02,482 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94104.48, 'new_value': 97687.36}, {'field': 'offline_amount', 'old_value': 108187.38, 'new_value': 111063.95}, {'field': 'total_amount', 'old_value': 202291.86, 'new_value': 208751.31}, {'field': 'order_count', 'old_value': 8255, 'new_value': 8539}]
2025-05-27 12:02:02,482 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-27 12:02:02,997 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-27 12:02:02,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 242895.87, 'new_value': 247003.38}, {'field': 'total_amount', 'old_value': 262069.3, 'new_value': 266176.81}, {'field': 'order_count', 'old_value': 5441, 'new_value': 5516}]
2025-05-27 12:02:02,997 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-27 12:02:03,419 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-27 12:02:03,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54154.0, 'new_value': 56478.0}, {'field': 'total_amount', 'old_value': 54154.0, 'new_value': 56478.0}, {'field': 'order_count', 'old_value': 120, 'new_value': 127}]
2025-05-27 12:02:03,419 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-27 12:02:03,810 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-27 12:02:03,810 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 352463.4, 'new_value': 367324.1}, {'field': 'total_amount', 'old_value': 352463.4, 'new_value': 367324.1}, {'field': 'order_count', 'old_value': 435, 'new_value': 453}]
2025-05-27 12:02:03,825 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-27 12:02:04,279 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-27 12:02:04,279 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 674278.0, 'new_value': 689275.0}, {'field': 'total_amount', 'old_value': 674278.0, 'new_value': 689275.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 80}]
2025-05-27 12:02:04,279 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-27 12:02:04,716 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-27 12:02:04,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79657.82, 'new_value': 81237.86}, {'field': 'total_amount', 'old_value': 79657.82, 'new_value': 81237.86}, {'field': 'order_count', 'old_value': 4588, 'new_value': 4706}]
2025-05-27 12:02:04,716 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-27 12:02:05,216 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-27 12:02:05,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36570.0, 'new_value': 38887.0}, {'field': 'total_amount', 'old_value': 36570.0, 'new_value': 38887.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 57}]
2025-05-27 12:02:05,216 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-27 12:02:05,669 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-27 12:02:05,669 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58594.55, 'new_value': 60170.55}, {'field': 'offline_amount', 'old_value': 46301.4, 'new_value': 46963.4}, {'field': 'total_amount', 'old_value': 104895.95, 'new_value': 107133.95}, {'field': 'order_count', 'old_value': 2086, 'new_value': 2146}]
2025-05-27 12:02:05,669 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-27 12:02:06,185 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-27 12:02:06,185 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15150.45, 'new_value': 15781.45}, {'field': 'offline_amount', 'old_value': 290753.0, 'new_value': 296027.0}, {'field': 'total_amount', 'old_value': 305903.45, 'new_value': 311808.45}, {'field': 'order_count', 'old_value': 1609, 'new_value': 1647}]
2025-05-27 12:02:06,185 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-27 12:02:06,622 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-27 12:02:06,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184200.0, 'new_value': 196400.0}, {'field': 'total_amount', 'old_value': 184200.0, 'new_value': 196400.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-05-27 12:02:06,622 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-27 12:02:07,060 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-27 12:02:07,060 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33422.7, 'new_value': 34383.7}, {'field': 'total_amount', 'old_value': 33422.7, 'new_value': 34383.7}, {'field': 'order_count', 'old_value': 195, 'new_value': 201}]
2025-05-27 12:02:07,060 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-27 12:02:07,497 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-27 12:02:07,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 342928.5, 'new_value': 347928.5}, {'field': 'total_amount', 'old_value': 342928.5, 'new_value': 347928.5}, {'field': 'order_count', 'old_value': 59, 'new_value': 60}]
2025-05-27 12:02:07,497 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-27 12:02:07,935 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-27 12:02:07,935 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13370.0, 'new_value': 13408.0}, {'field': 'offline_amount', 'old_value': 8646.0, 'new_value': 8684.0}, {'field': 'total_amount', 'old_value': 22016.0, 'new_value': 22092.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 97}]
2025-05-27 12:02:07,935 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-27 12:02:08,435 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-27 12:02:08,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 316115.0, 'new_value': 328875.0}, {'field': 'total_amount', 'old_value': 343205.0, 'new_value': 355965.0}, {'field': 'order_count', 'old_value': 7242, 'new_value': 7562}]
2025-05-27 12:02:08,435 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-27 12:02:08,857 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-27 12:02:08,857 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78845.3, 'new_value': 80680.23}, {'field': 'offline_amount', 'old_value': 202858.82, 'new_value': 207749.88}, {'field': 'total_amount', 'old_value': 281704.12, 'new_value': 288430.11}, {'field': 'order_count', 'old_value': 5328, 'new_value': 5502}]
2025-05-27 12:02:08,857 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-27 12:02:09,325 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-27 12:02:09,325 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1200.0, 'new_value': 3440.8}, {'field': 'total_amount', 'old_value': 54063.45, 'new_value': 56304.25}, {'field': 'order_count', 'old_value': 211, 'new_value': 213}]
2025-05-27 12:02:09,325 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-27 12:02:09,732 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-27 12:02:09,732 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185859.0, 'new_value': 187833.0}, {'field': 'total_amount', 'old_value': 185859.0, 'new_value': 187833.0}, {'field': 'order_count', 'old_value': 3118, 'new_value': 3150}]
2025-05-27 12:02:09,732 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-27 12:02:10,138 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-27 12:02:10,138 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185760.62, 'new_value': 189874.45}, {'field': 'total_amount', 'old_value': 185760.62, 'new_value': 189874.45}, {'field': 'order_count', 'old_value': 7892, 'new_value': 8091}]
2025-05-27 12:02:10,138 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-27 12:02:10,794 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-27 12:02:10,794 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 591762.0, 'new_value': 600009.0}, {'field': 'total_amount', 'old_value': 591762.0, 'new_value': 600009.0}, {'field': 'order_count', 'old_value': 526, 'new_value': 541}]
2025-05-27 12:02:10,794 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-27 12:02:11,216 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-27 12:02:11,216 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 269432.04, 'new_value': 277307.06}, {'field': 'offline_amount', 'old_value': 148642.6, 'new_value': 152166.02}, {'field': 'total_amount', 'old_value': 418074.64, 'new_value': 429473.08}, {'field': 'order_count', 'old_value': 3234, 'new_value': 3275}]
2025-05-27 12:02:11,216 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-27 12:02:11,622 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-27 12:02:11,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238316.61, 'new_value': 244706.16}, {'field': 'total_amount', 'old_value': 238316.61, 'new_value': 244706.16}, {'field': 'order_count', 'old_value': 1835, 'new_value': 1892}]
2025-05-27 12:02:11,622 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-27 12:02:12,091 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-27 12:02:12,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93002.28, 'new_value': 95790.73}, {'field': 'total_amount', 'old_value': 162503.88, 'new_value': 165292.33}, {'field': 'order_count', 'old_value': 4517, 'new_value': 4612}]
2025-05-27 12:02:12,091 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-27 12:02:12,638 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-27 12:02:12,638 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99448.0, 'new_value': 103468.0}, {'field': 'offline_amount', 'old_value': 1099749.0, 'new_value': 1138829.0}, {'field': 'total_amount', 'old_value': 1199197.0, 'new_value': 1242297.0}, {'field': 'order_count', 'old_value': 30274, 'new_value': 31549}]
2025-05-27 12:02:12,638 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-27 12:02:13,122 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-27 12:02:13,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 387786.43, 'new_value': 397484.12}, {'field': 'total_amount', 'old_value': 401296.91, 'new_value': 410994.6}, {'field': 'order_count', 'old_value': 1289, 'new_value': 1319}]
2025-05-27 12:02:13,122 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-27 12:02:13,544 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-27 12:02:13,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 249149.0, 'new_value': 256244.0}, {'field': 'total_amount', 'old_value': 302858.0, 'new_value': 309953.0}, {'field': 'order_count', 'old_value': 275, 'new_value': 281}]
2025-05-27 12:02:13,544 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-27 12:02:14,013 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-27 12:02:14,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37003.68, 'new_value': 38211.03}, {'field': 'offline_amount', 'old_value': 48116.44, 'new_value': 49116.44}, {'field': 'total_amount', 'old_value': 85120.12, 'new_value': 87327.47}, {'field': 'order_count', 'old_value': 4172, 'new_value': 4280}]
2025-05-27 12:02:14,013 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-27 12:02:14,482 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-27 12:02:14,482 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 295028.0, 'new_value': 304644.0}, {'field': 'total_amount', 'old_value': 295028.0, 'new_value': 304644.0}, {'field': 'order_count', 'old_value': 455, 'new_value': 467}]
2025-05-27 12:02:14,482 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-27 12:02:14,919 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-27 12:02:14,919 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 119852.18, 'new_value': 125885.26}, {'field': 'offline_amount', 'old_value': 215015.81, 'new_value': 220772.82}, {'field': 'total_amount', 'old_value': 334867.99, 'new_value': 346658.08}, {'field': 'order_count', 'old_value': 10235, 'new_value': 10699}]
2025-05-27 12:02:14,919 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-27 12:02:15,450 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-27 12:02:15,450 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95175.1, 'new_value': 95274.1}, {'field': 'total_amount', 'old_value': 95980.1, 'new_value': 96079.1}, {'field': 'order_count', 'old_value': 16307, 'new_value': 16308}]
2025-05-27 12:02:15,450 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-27 12:02:15,982 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-27 12:02:15,982 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 170724.5, 'new_value': 176405.57}, {'field': 'offline_amount', 'old_value': 54088.62, 'new_value': 54831.99}, {'field': 'total_amount', 'old_value': 224813.12, 'new_value': 231237.56}, {'field': 'order_count', 'old_value': 12770, 'new_value': 13177}]
2025-05-27 12:02:15,982 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-27 12:02:16,341 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-27 12:02:16,341 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 123851.8, 'new_value': 128446.8}, {'field': 'offline_amount', 'old_value': 32105.9, 'new_value': 33036.9}, {'field': 'total_amount', 'old_value': 155957.7, 'new_value': 161483.7}, {'field': 'order_count', 'old_value': 12783, 'new_value': 13249}]
2025-05-27 12:02:16,341 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-27 12:02:16,794 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-27 12:02:16,810 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171121.98, 'new_value': 176638.86}, {'field': 'total_amount', 'old_value': 171121.98, 'new_value': 176638.86}, {'field': 'order_count', 'old_value': 8630, 'new_value': 8928}]
2025-05-27 12:02:16,810 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-27 12:02:17,247 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-27 12:02:17,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163942.4, 'new_value': 167948.2}, {'field': 'total_amount', 'old_value': 163942.4, 'new_value': 167948.2}, {'field': 'order_count', 'old_value': 740, 'new_value': 757}]
2025-05-27 12:02:17,247 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-27 12:02:17,763 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-27 12:02:17,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137850.0, 'new_value': 139112.4}, {'field': 'total_amount', 'old_value': 137850.0, 'new_value': 139112.4}, {'field': 'order_count', 'old_value': 3808, 'new_value': 3843}]
2025-05-27 12:02:17,763 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-27 12:02:18,200 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-27 12:02:18,200 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151691.45, 'new_value': 155251.47}, {'field': 'offline_amount', 'old_value': 290416.03, 'new_value': 295482.91}, {'field': 'total_amount', 'old_value': 442107.48, 'new_value': 450734.38}, {'field': 'order_count', 'old_value': 3664, 'new_value': 3761}]
2025-05-27 12:02:18,200 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-27 12:02:18,622 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-27 12:02:18,622 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104760.4, 'new_value': 108219.0}, {'field': 'total_amount', 'old_value': 104760.4, 'new_value': 108219.0}, {'field': 'order_count', 'old_value': 504, 'new_value': 520}]
2025-05-27 12:02:18,622 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-27 12:02:19,122 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-27 12:02:19,122 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42349.54, 'new_value': 43600.47}, {'field': 'offline_amount', 'old_value': 25317.06, 'new_value': 25800.41}, {'field': 'total_amount', 'old_value': 67666.6, 'new_value': 69400.88}, {'field': 'order_count', 'old_value': 2972, 'new_value': 3062}]
2025-05-27 12:02:19,122 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-27 12:02:19,544 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-27 12:02:19,544 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14969.86, 'new_value': 15310.72}, {'field': 'offline_amount', 'old_value': 35518.9, 'new_value': 35901.6}, {'field': 'total_amount', 'old_value': 50488.76, 'new_value': 51212.32}, {'field': 'order_count', 'old_value': 2003, 'new_value': 2038}]
2025-05-27 12:02:19,544 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-27 12:02:19,950 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-27 12:02:19,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110011.0, 'new_value': 110399.0}, {'field': 'total_amount', 'old_value': 110011.0, 'new_value': 110399.0}, {'field': 'order_count', 'old_value': 1695, 'new_value': 1696}]
2025-05-27 12:02:19,966 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-27 12:02:20,435 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-27 12:02:20,435 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2281.11, 'new_value': 2413.31}, {'field': 'offline_amount', 'old_value': 84415.58, 'new_value': 87495.78}, {'field': 'total_amount', 'old_value': 86696.69, 'new_value': 89909.09}, {'field': 'order_count', 'old_value': 405, 'new_value': 427}]
2025-05-27 12:02:20,435 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-27 12:02:20,950 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-27 12:02:20,950 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 789804.44, 'new_value': 803885.24}, {'field': 'total_amount', 'old_value': 789804.44, 'new_value': 803885.24}, {'field': 'order_count', 'old_value': 6311, 'new_value': 6458}]
2025-05-27 12:02:20,950 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-27 12:02:21,435 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-27 12:02:21,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 520834.0, 'new_value': 529691.0}, {'field': 'total_amount', 'old_value': 520834.0, 'new_value': 529691.0}, {'field': 'order_count', 'old_value': 3703, 'new_value': 3803}]
2025-05-27 12:02:21,435 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-27 12:02:22,013 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-27 12:02:22,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7673.0, 'new_value': 7763.0}, {'field': 'total_amount', 'old_value': 13188.0, 'new_value': 13278.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 142}]
2025-05-27 12:02:22,013 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-27 12:02:22,450 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-27 12:02:22,450 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54283.72, 'new_value': 56720.8}, {'field': 'offline_amount', 'old_value': 54206.19, 'new_value': 57024.74}, {'field': 'total_amount', 'old_value': 108489.91, 'new_value': 113745.54}, {'field': 'order_count', 'old_value': 5425, 'new_value': 5665}]
2025-05-27 12:02:22,450 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-27 12:02:22,966 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-27 12:02:22,966 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26011.54, 'new_value': 26695.44}, {'field': 'offline_amount', 'old_value': 28817.88, 'new_value': 29641.08}, {'field': 'total_amount', 'old_value': 54829.42, 'new_value': 56336.52}, {'field': 'order_count', 'old_value': 2647, 'new_value': 2732}]
2025-05-27 12:02:22,966 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-27 12:02:23,528 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-27 12:02:23,528 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3718.0, 'new_value': 3821.0}, {'field': 'offline_amount', 'old_value': 29698.8, 'new_value': 29890.8}, {'field': 'total_amount', 'old_value': 33416.8, 'new_value': 33711.8}, {'field': 'order_count', 'old_value': 1214, 'new_value': 1223}]
2025-05-27 12:02:23,528 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-27 12:02:24,013 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-27 12:02:24,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104180.37, 'new_value': 108153.18}, {'field': 'offline_amount', 'old_value': 116351.31, 'new_value': 121567.87}, {'field': 'total_amount', 'old_value': 220531.68, 'new_value': 229721.05}, {'field': 'order_count', 'old_value': 5575, 'new_value': 5801}]
2025-05-27 12:02:24,013 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-27 12:02:24,497 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-27 12:02:24,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79069.0, 'new_value': 85169.0}, {'field': 'total_amount', 'old_value': 79069.0, 'new_value': 85169.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-27 12:02:24,497 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-27 12:02:24,935 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-27 12:02:24,935 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 987020.0, 'new_value': 1011076.0}, {'field': 'total_amount', 'old_value': 987020.0, 'new_value': 1011076.0}, {'field': 'order_count', 'old_value': 1128, 'new_value': 1160}]
2025-05-27 12:02:24,935 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-27 12:02:25,388 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-27 12:02:25,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201565.9, 'new_value': 212678.5}, {'field': 'total_amount', 'old_value': 207516.2, 'new_value': 218628.8}, {'field': 'order_count', 'old_value': 409, 'new_value': 428}]
2025-05-27 12:02:25,388 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-27 12:02:25,794 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-27 12:02:25,794 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140849.0, 'new_value': 144272.0}, {'field': 'offline_amount', 'old_value': 98256.0, 'new_value': 100443.0}, {'field': 'total_amount', 'old_value': 239105.0, 'new_value': 244715.0}, {'field': 'order_count', 'old_value': 3126, 'new_value': 3215}]
2025-05-27 12:02:25,794 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-27 12:02:26,153 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-27 12:02:26,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24322.64, 'new_value': 24441.64}, {'field': 'total_amount', 'old_value': 32056.94, 'new_value': 32175.94}, {'field': 'order_count', 'old_value': 315, 'new_value': 316}]
2025-05-27 12:02:26,153 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-27 12:02:26,653 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-27 12:02:26,653 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177578.0, 'new_value': 181478.0}, {'field': 'total_amount', 'old_value': 189494.3, 'new_value': 193394.3}, {'field': 'order_count', 'old_value': 83, 'new_value': 84}]
2025-05-27 12:02:26,653 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-27 12:02:27,091 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-27 12:02:27,107 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33209.66, 'new_value': 33359.66}, {'field': 'offline_amount', 'old_value': 32227.82, 'new_value': 32792.82}, {'field': 'total_amount', 'old_value': 65437.48, 'new_value': 66152.48}, {'field': 'order_count', 'old_value': 292, 'new_value': 296}]
2025-05-27 12:02:27,107 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-27 12:02:27,669 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-27 12:02:27,669 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 222696.5, 'new_value': 230369.5}, {'field': 'total_amount', 'old_value': 222696.5, 'new_value': 230369.5}, {'field': 'order_count', 'old_value': 1099, 'new_value': 1130}]
2025-05-27 12:02:27,669 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-27 12:02:28,091 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-27 12:02:28,091 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4844.0, 'new_value': 5212.0}, {'field': 'offline_amount', 'old_value': 20844.0, 'new_value': 21704.0}, {'field': 'total_amount', 'old_value': 25688.0, 'new_value': 26916.0}, {'field': 'order_count', 'old_value': 201, 'new_value': 209}]
2025-05-27 12:02:28,091 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-27 12:02:28,575 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-27 12:02:28,575 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53677.77, 'new_value': 54951.77}, {'field': 'total_amount', 'old_value': 60343.81, 'new_value': 61617.81}, {'field': 'order_count', 'old_value': 554, 'new_value': 574}]
2025-05-27 12:02:28,575 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-27 12:02:29,028 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-27 12:02:29,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199509.04, 'new_value': 206349.44}, {'field': 'total_amount', 'old_value': 199509.04, 'new_value': 206349.44}, {'field': 'order_count', 'old_value': 758, 'new_value': 786}]
2025-05-27 12:02:29,028 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-27 12:02:29,482 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-27 12:02:29,482 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3739462.18, 'new_value': 3828105.82}, {'field': 'total_amount', 'old_value': 3739462.18, 'new_value': 3828105.82}, {'field': 'order_count', 'old_value': 6446, 'new_value': 6604}]
2025-05-27 12:02:29,482 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-27 12:02:29,935 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-27 12:02:29,935 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162607.43, 'new_value': 169442.75}, {'field': 'total_amount', 'old_value': 170047.07, 'new_value': 176882.39}, {'field': 'order_count', 'old_value': 11863, 'new_value': 12373}]
2025-05-27 12:02:29,935 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-27 12:02:30,372 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-27 12:02:30,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 320664.0, 'new_value': 334398.0}, {'field': 'total_amount', 'old_value': 320664.0, 'new_value': 334398.0}, {'field': 'order_count', 'old_value': 6272, 'new_value': 6572}]
2025-05-27 12:02:30,372 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-27 12:02:30,903 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-27 12:02:30,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204871.0, 'new_value': 204929.0}, {'field': 'total_amount', 'old_value': 204871.0, 'new_value': 204929.0}, {'field': 'order_count', 'old_value': 463, 'new_value': 464}]
2025-05-27 12:02:30,903 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-27 12:02:31,372 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-27 12:02:31,372 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47479.29, 'new_value': 49237.59}, {'field': 'offline_amount', 'old_value': 68673.11, 'new_value': 69395.11}, {'field': 'total_amount', 'old_value': 116152.4, 'new_value': 118632.7}, {'field': 'order_count', 'old_value': 2509, 'new_value': 2582}]
2025-05-27 12:02:31,372 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-27 12:02:31,903 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-27 12:02:31,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49742.0, 'new_value': 77334.0}, {'field': 'total_amount', 'old_value': 57722.0, 'new_value': 85314.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 22}]
2025-05-27 12:02:31,903 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-27 12:02:32,466 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-27 12:02:32,466 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 169099.6, 'new_value': 175625.6}, {'field': 'total_amount', 'old_value': 329315.38, 'new_value': 335841.38}]
2025-05-27 12:02:32,466 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-27 12:02:32,950 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-27 12:02:32,950 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 200030.0, 'new_value': 204182.0}, {'field': 'offline_amount', 'old_value': 182944.0, 'new_value': 187532.0}, {'field': 'total_amount', 'old_value': 382974.0, 'new_value': 391714.0}, {'field': 'order_count', 'old_value': 1058, 'new_value': 1086}]
2025-05-27 12:02:32,950 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-27 12:02:33,388 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-27 12:02:33,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142272.0, 'new_value': 149113.0}, {'field': 'total_amount', 'old_value': 147011.0, 'new_value': 153852.0}, {'field': 'order_count', 'old_value': 10868, 'new_value': 11400}]
2025-05-27 12:02:33,388 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-27 12:02:33,856 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-27 12:02:33,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23144.0, 'new_value': 24441.0}, {'field': 'total_amount', 'old_value': 27797.6, 'new_value': 29094.6}, {'field': 'order_count', 'old_value': 696, 'new_value': 729}]
2025-05-27 12:02:33,856 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-27 12:02:34,356 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-27 12:02:34,356 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193280.96, 'new_value': 200796.27}, {'field': 'total_amount', 'old_value': 193280.96, 'new_value': 200796.27}, {'field': 'order_count', 'old_value': 13788, 'new_value': 14335}]
2025-05-27 12:02:34,356 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-27 12:02:35,028 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-27 12:02:35,028 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27237.7, 'new_value': 28160.5}, {'field': 'offline_amount', 'old_value': 61892.3, 'new_value': 64974.4}, {'field': 'total_amount', 'old_value': 89130.0, 'new_value': 93134.9}, {'field': 'order_count', 'old_value': 3350, 'new_value': 3514}]
2025-05-27 12:02:35,028 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-27 12:02:35,466 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-27 12:02:35,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37575.37, 'new_value': 39239.41}, {'field': 'total_amount', 'old_value': 37575.37, 'new_value': 39239.41}, {'field': 'order_count', 'old_value': 1764, 'new_value': 1842}]
2025-05-27 12:02:35,466 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-27 12:02:35,919 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-27 12:02:35,919 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5133715.67, 'new_value': 5315313.67}, {'field': 'total_amount', 'old_value': 5133715.67, 'new_value': 5315313.67}, {'field': 'order_count', 'old_value': 105903, 'new_value': 109492}]
2025-05-27 12:02:35,919 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-27 12:02:36,341 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-27 12:02:36,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25175.88, 'new_value': 25465.88}, {'field': 'total_amount', 'old_value': 25175.88, 'new_value': 25465.88}, {'field': 'order_count', 'old_value': 110, 'new_value': 112}]
2025-05-27 12:02:36,341 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-27 12:02:36,778 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-27 12:02:36,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202711.86, 'new_value': 212145.44}, {'field': 'total_amount', 'old_value': 202711.86, 'new_value': 212145.44}, {'field': 'order_count', 'old_value': 3735, 'new_value': 3884}]
2025-05-27 12:02:36,794 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-27 12:02:37,263 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-27 12:02:37,263 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 326434.0, 'new_value': 342510.0}, {'field': 'total_amount', 'old_value': 326434.0, 'new_value': 342510.0}, {'field': 'order_count', 'old_value': 7175, 'new_value': 7491}]
2025-05-27 12:02:37,263 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-27 12:02:37,700 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-27 12:02:37,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79791.31, 'new_value': 82889.23}, {'field': 'total_amount', 'old_value': 79791.31, 'new_value': 82889.23}, {'field': 'order_count', 'old_value': 8280, 'new_value': 8646}]
2025-05-27 12:02:37,700 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-27 12:02:38,122 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-27 12:02:38,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128442.03, 'new_value': 133778.03}, {'field': 'total_amount', 'old_value': 133410.23, 'new_value': 138746.23}, {'field': 'order_count', 'old_value': 3363, 'new_value': 3510}]
2025-05-27 12:02:38,122 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-27 12:02:38,591 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-27 12:02:38,591 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103240.24, 'new_value': 106722.04}, {'field': 'offline_amount', 'old_value': 415130.0, 'new_value': 419266.0}, {'field': 'total_amount', 'old_value': 518370.24, 'new_value': 525988.04}, {'field': 'order_count', 'old_value': 3719, 'new_value': 3813}]
2025-05-27 12:02:38,591 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-27 12:02:39,060 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-27 12:02:39,060 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57546.2, 'new_value': 61290.0}, {'field': 'total_amount', 'old_value': 91898.7, 'new_value': 95642.5}, {'field': 'order_count', 'old_value': 6011, 'new_value': 6264}]
2025-05-27 12:02:39,060 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-27 12:02:39,544 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-27 12:02:39,544 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99288.44, 'new_value': 106512.36}, {'field': 'total_amount', 'old_value': 159142.83, 'new_value': 166366.75}, {'field': 'order_count', 'old_value': 10517, 'new_value': 10969}]
2025-05-27 12:02:39,544 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-27 12:02:39,997 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-27 12:02:39,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1139452.14, 'new_value': 1175768.34}, {'field': 'total_amount', 'old_value': 1139452.14, 'new_value': 1175768.34}, {'field': 'order_count', 'old_value': 3350, 'new_value': 3448}]
2025-05-27 12:02:39,997 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-27 12:02:40,466 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-27 12:02:40,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 183010.8, 'new_value': 192886.8}, {'field': 'total_amount', 'old_value': 183010.8, 'new_value': 192886.8}, {'field': 'order_count', 'old_value': 6422, 'new_value': 6754}]
2025-05-27 12:02:40,466 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-27 12:02:40,903 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-27 12:02:40,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 739831.8, 'new_value': 757036.25}, {'field': 'total_amount', 'old_value': 739831.8, 'new_value': 757036.25}, {'field': 'order_count', 'old_value': 3898, 'new_value': 4047}]
2025-05-27 12:02:40,903 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-27 12:02:41,372 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-27 12:02:41,372 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1083542.62, 'new_value': 1111690.82}, {'field': 'total_amount', 'old_value': 1083542.62, 'new_value': 1111690.82}, {'field': 'order_count', 'old_value': 3837, 'new_value': 3939}]
2025-05-27 12:02:41,372 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-27 12:02:41,825 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-27 12:02:41,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 792126.11, 'new_value': 829916.51}, {'field': 'total_amount', 'old_value': 792126.11, 'new_value': 829916.51}, {'field': 'order_count', 'old_value': 2254, 'new_value': 2340}]
2025-05-27 12:02:41,825 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-27 12:02:42,294 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-27 12:02:42,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41185.0, 'new_value': 44765.0}, {'field': 'total_amount', 'old_value': 41185.0, 'new_value': 44765.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-27 12:02:42,294 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-27 12:02:42,794 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-27 12:02:42,794 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23624.0, 'new_value': 24318.0}, {'field': 'offline_amount', 'old_value': 26024.0, 'new_value': 26718.0}, {'field': 'total_amount', 'old_value': 49648.0, 'new_value': 51036.0}, {'field': 'order_count', 'old_value': 23662, 'new_value': 24356}]
2025-05-27 12:02:42,794 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-27 12:02:43,231 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-27 12:02:43,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46349.9, 'new_value': 48601.4}, {'field': 'total_amount', 'old_value': 49193.9, 'new_value': 51445.4}, {'field': 'order_count', 'old_value': 361, 'new_value': 380}]
2025-05-27 12:02:43,231 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-27 12:02:43,638 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-27 12:02:43,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 310344.18, 'new_value': 315263.81}, {'field': 'total_amount', 'old_value': 310344.18, 'new_value': 315263.81}, {'field': 'order_count', 'old_value': 858, 'new_value': 873}]
2025-05-27 12:02:43,638 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-27 12:02:44,138 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-27 12:02:44,138 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64826.0, 'new_value': 65878.0}, {'field': 'total_amount', 'old_value': 80331.0, 'new_value': 81383.0}, {'field': 'order_count', 'old_value': 124, 'new_value': 129}]
2025-05-27 12:02:44,138 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-27 12:02:44,606 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-27 12:02:44,606 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 136230.0, 'new_value': 141432.0}, {'field': 'offline_amount', 'old_value': 91670.0, 'new_value': 96666.0}, {'field': 'total_amount', 'old_value': 227900.0, 'new_value': 238098.0}, {'field': 'order_count', 'old_value': 9300, 'new_value': 9746}]
2025-05-27 12:02:44,606 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-27 12:02:45,060 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-27 12:02:45,060 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122790.0, 'new_value': 128727.0}, {'field': 'total_amount', 'old_value': 122790.0, 'new_value': 128727.0}, {'field': 'order_count', 'old_value': 602, 'new_value': 631}]
2025-05-27 12:02:45,060 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-27 12:02:45,466 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-27 12:02:45,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139173.0, 'new_value': 148072.0}, {'field': 'total_amount', 'old_value': 139173.0, 'new_value': 148072.0}, {'field': 'order_count', 'old_value': 581, 'new_value': 601}]
2025-05-27 12:02:45,466 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-27 12:02:45,856 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-27 12:02:45,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 273185.0, 'new_value': 287885.0}, {'field': 'total_amount', 'old_value': 273185.0, 'new_value': 287885.0}, {'field': 'order_count', 'old_value': 643, 'new_value': 677}]
2025-05-27 12:02:45,856 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-27 12:02:46,356 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-27 12:02:46,356 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52145.0, 'new_value': 54216.0}, {'field': 'total_amount', 'old_value': 52145.0, 'new_value': 54216.0}, {'field': 'order_count', 'old_value': 1005, 'new_value': 1045}]
2025-05-27 12:02:46,356 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-27 12:02:46,747 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-27 12:02:46,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201325.0, 'new_value': 203849.0}, {'field': 'total_amount', 'old_value': 201325.0, 'new_value': 203849.0}, {'field': 'order_count', 'old_value': 21291, 'new_value': 21539}]
2025-05-27 12:02:46,747 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-27 12:02:47,247 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-27 12:02:47,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154673.82, 'new_value': 156673.82}, {'field': 'total_amount', 'old_value': 154673.82, 'new_value': 156673.82}, {'field': 'order_count', 'old_value': 1330, 'new_value': 1331}]
2025-05-27 12:02:47,247 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-27 12:02:47,685 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-27 12:02:47,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4677.0, 'new_value': 5257.0}, {'field': 'total_amount', 'old_value': 4677.0, 'new_value': 5257.0}, {'field': 'order_count', 'old_value': 588, 'new_value': 589}]
2025-05-27 12:02:47,685 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-27 12:02:48,138 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-27 12:02:48,138 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4967.6, 'new_value': 5079.6}, {'field': 'total_amount', 'old_value': 4967.6, 'new_value': 5079.6}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-05-27 12:02:48,138 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-27 12:02:48,528 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-27 12:02:48,528 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36569.0, 'new_value': 36769.0}, {'field': 'total_amount', 'old_value': 76694.4, 'new_value': 76894.4}, {'field': 'order_count', 'old_value': 2800, 'new_value': 2801}]
2025-05-27 12:02:48,528 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-27 12:02:48,997 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-27 12:02:48,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47534.04, 'new_value': 49032.04}, {'field': 'total_amount', 'old_value': 47534.04, 'new_value': 49032.04}, {'field': 'order_count', 'old_value': 818, 'new_value': 840}]
2025-05-27 12:02:48,997 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-27 12:02:49,435 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-27 12:02:49,435 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 124111.85, 'new_value': 126547.77}, {'field': 'offline_amount', 'old_value': 682753.28, 'new_value': 701093.02}, {'field': 'total_amount', 'old_value': 806865.13, 'new_value': 827640.79}, {'field': 'order_count', 'old_value': 1831, 'new_value': 1885}]
2025-05-27 12:02:49,435 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-27 12:02:49,888 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-27 12:02:49,888 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73504.6, 'new_value': 79124.6}, {'field': 'total_amount', 'old_value': 73504.6, 'new_value': 79124.6}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-05-27 12:02:49,888 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-27 12:02:50,310 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-27 12:02:50,310 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88733.97, 'new_value': 92054.37}, {'field': 'offline_amount', 'old_value': 882360.01, 'new_value': 920053.53}, {'field': 'total_amount', 'old_value': 969219.65, 'new_value': 1010233.57}, {'field': 'order_count', 'old_value': 4593, 'new_value': 4773}]
2025-05-27 12:02:50,310 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-27 12:02:50,716 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-27 12:02:50,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116820.0, 'new_value': 121565.0}, {'field': 'total_amount', 'old_value': 116820.0, 'new_value': 121565.0}, {'field': 'order_count', 'old_value': 364, 'new_value': 378}]
2025-05-27 12:02:50,716 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-27 12:02:51,169 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-27 12:02:51,169 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78451.0, 'new_value': 79257.0}, {'field': 'total_amount', 'old_value': 83769.0, 'new_value': 84575.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 45}]
2025-05-27 12:02:51,169 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-27 12:02:51,638 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-27 12:02:51,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17418958.06, 'new_value': 18084105.92}, {'field': 'total_amount', 'old_value': 17418958.06, 'new_value': 18084105.92}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-27 12:02:51,638 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-27 12:02:52,091 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-27 12:02:52,091 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6315.0, 'new_value': 8598.0}, {'field': 'offline_amount', 'old_value': 40096.36, 'new_value': 40861.36}, {'field': 'total_amount', 'old_value': 46411.36, 'new_value': 49459.36}, {'field': 'order_count', 'old_value': 2900, 'new_value': 3060}]
2025-05-27 12:02:52,091 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-27 12:02:52,497 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-27 12:02:52,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182565.02, 'new_value': 188383.58}, {'field': 'total_amount', 'old_value': 182565.02, 'new_value': 188383.58}, {'field': 'order_count', 'old_value': 19272, 'new_value': 19948}]
2025-05-27 12:02:52,497 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-27 12:02:53,106 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-27 12:02:53,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17013.0, 'new_value': 17280.0}, {'field': 'total_amount', 'old_value': 17013.0, 'new_value': 17280.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 112}]
2025-05-27 12:02:53,106 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-27 12:02:53,606 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-27 12:02:53,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54532.9, 'new_value': 58018.09}, {'field': 'total_amount', 'old_value': 60356.28, 'new_value': 63841.47}, {'field': 'order_count', 'old_value': 3378, 'new_value': 4044}]
2025-05-27 12:02:53,606 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-27 12:02:54,044 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-27 12:02:54,044 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42723.17, 'new_value': 47799.22}, {'field': 'offline_amount', 'old_value': 57107.77, 'new_value': 59883.77}, {'field': 'total_amount', 'old_value': 99830.94, 'new_value': 107682.99}, {'field': 'order_count', 'old_value': 455, 'new_value': 483}]
2025-05-27 12:02:54,044 - INFO - 开始更新记录 - 表单实例ID: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-27 12:02:54,450 - INFO - 更新表单数据成功: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-27 12:02:54,450 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99660.0, 'new_value': 113811.38}, {'field': 'total_amount', 'old_value': 99660.0, 'new_value': 113811.38}, {'field': 'order_count', 'old_value': 4591, 'new_value': 5224}]
2025-05-27 12:02:54,450 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-05-27 12:02:54,981 - INFO - 更新表单数据成功: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-05-27 12:02:54,981 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2731.27, 'new_value': 3375.43}, {'field': 'total_amount', 'old_value': 2731.27, 'new_value': 3375.43}, {'field': 'order_count', 'old_value': 102, 'new_value': 150}]
2025-05-27 12:02:54,981 - INFO - 开始更新记录 - 表单实例ID: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-05-27 12:02:55,481 - INFO - 更新表单数据成功: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-05-27 12:02:55,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1493.48, 'new_value': 2593.48}, {'field': 'total_amount', 'old_value': 1493.48, 'new_value': 2593.48}, {'field': 'order_count', 'old_value': 67, 'new_value': 68}]
2025-05-27 12:02:55,481 - INFO - 开始批量插入 1 条新记录
2025-05-27 12:02:55,638 - INFO - 批量插入响应状态码: 200
2025-05-27 12:02:55,638 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 27 May 2025 04:02:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '932E65AF-D3B7-7098-956E-8948D8D66BD6', 'x-acs-trace-id': '2b9d5712242ca7a5f25d207aee09bbd6', 'etag': '5OfkuW3L7JjlC0TxWn94TTQ9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-27 12:02:55,638 - INFO - 批量插入响应体: {'result': ['FINST-S0E660A167SV3ROVC9GYH5S58C6T3TUMQZ5BMN']}
2025-05-27 12:02:55,638 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-27 12:02:55,638 - INFO - 成功插入的数据ID: ['FINST-S0E660A167SV3ROVC9GYH5S58C6T3TUMQZ5BMN']
2025-05-27 12:02:58,653 - INFO - 批量插入完成，共 1 条记录
2025-05-27 12:02:58,653 - INFO - 日期 2025-05 处理完成 - 更新: 313 条，插入: 1 条，错误: 0 条
2025-05-27 12:02:58,653 - INFO - 数据同步完成！更新: 313 条，插入: 1 条，错误: 0 条
2025-05-27 12:02:58,653 - INFO - =================同步完成====================
2025-05-27 15:00:02,107 - INFO - =================使用默认全量同步=============
2025-05-27 15:00:03,591 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-27 15:00:03,591 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-27 15:00:03,622 - INFO - 开始处理日期: 2025-01
2025-05-27 15:00:03,622 - INFO - Request Parameters - Page 1:
2025-05-27 15:00:03,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:03,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:04,435 - INFO - Response - Page 1:
2025-05-27 15:00:04,638 - INFO - 第 1 页获取到 100 条记录
2025-05-27 15:00:04,638 - INFO - Request Parameters - Page 2:
2025-05-27 15:00:04,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:04,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:05,544 - INFO - Response - Page 2:
2025-05-27 15:00:05,747 - INFO - 第 2 页获取到 100 条记录
2025-05-27 15:00:05,747 - INFO - Request Parameters - Page 3:
2025-05-27 15:00:05,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:05,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:06,294 - INFO - Response - Page 3:
2025-05-27 15:00:06,497 - INFO - 第 3 页获取到 100 条记录
2025-05-27 15:00:06,497 - INFO - Request Parameters - Page 4:
2025-05-27 15:00:06,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:06,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:06,982 - INFO - Response - Page 4:
2025-05-27 15:00:07,185 - INFO - 第 4 页获取到 100 条记录
2025-05-27 15:00:07,185 - INFO - Request Parameters - Page 5:
2025-05-27 15:00:07,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:07,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:07,747 - INFO - Response - Page 5:
2025-05-27 15:00:07,950 - INFO - 第 5 页获取到 100 条记录
2025-05-27 15:00:07,950 - INFO - Request Parameters - Page 6:
2025-05-27 15:00:07,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:07,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:08,575 - INFO - Response - Page 6:
2025-05-27 15:00:08,778 - INFO - 第 6 页获取到 100 条记录
2025-05-27 15:00:08,778 - INFO - Request Parameters - Page 7:
2025-05-27 15:00:08,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:08,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:09,200 - INFO - Response - Page 7:
2025-05-27 15:00:09,403 - INFO - 第 7 页获取到 82 条记录
2025-05-27 15:00:09,403 - INFO - 查询完成，共获取到 682 条记录
2025-05-27 15:00:09,403 - INFO - 获取到 682 条表单数据
2025-05-27 15:00:09,419 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-27 15:00:09,435 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 15:00:09,435 - INFO - 开始处理日期: 2025-02
2025-05-27 15:00:09,435 - INFO - Request Parameters - Page 1:
2025-05-27 15:00:09,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:09,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:09,872 - INFO - Response - Page 1:
2025-05-27 15:00:10,075 - INFO - 第 1 页获取到 100 条记录
2025-05-27 15:00:10,075 - INFO - Request Parameters - Page 2:
2025-05-27 15:00:10,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:10,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:10,591 - INFO - Response - Page 2:
2025-05-27 15:00:10,794 - INFO - 第 2 页获取到 100 条记录
2025-05-27 15:00:10,794 - INFO - Request Parameters - Page 3:
2025-05-27 15:00:10,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:10,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:11,325 - INFO - Response - Page 3:
2025-05-27 15:00:11,528 - INFO - 第 3 页获取到 100 条记录
2025-05-27 15:00:11,528 - INFO - Request Parameters - Page 4:
2025-05-27 15:00:11,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:11,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:12,060 - INFO - Response - Page 4:
2025-05-27 15:00:12,263 - INFO - 第 4 页获取到 100 条记录
2025-05-27 15:00:12,263 - INFO - Request Parameters - Page 5:
2025-05-27 15:00:12,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:12,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:12,732 - INFO - Response - Page 5:
2025-05-27 15:00:12,935 - INFO - 第 5 页获取到 100 条记录
2025-05-27 15:00:12,935 - INFO - Request Parameters - Page 6:
2025-05-27 15:00:12,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:12,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:13,497 - INFO - Response - Page 6:
2025-05-27 15:00:13,700 - INFO - 第 6 页获取到 100 条记录
2025-05-27 15:00:13,700 - INFO - Request Parameters - Page 7:
2025-05-27 15:00:13,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:13,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:14,138 - INFO - Response - Page 7:
2025-05-27 15:00:14,357 - INFO - 第 7 页获取到 70 条记录
2025-05-27 15:00:14,357 - INFO - 查询完成，共获取到 670 条记录
2025-05-27 15:00:14,357 - INFO - 获取到 670 条表单数据
2025-05-27 15:00:14,357 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-27 15:00:14,372 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 15:00:14,372 - INFO - 开始处理日期: 2025-03
2025-05-27 15:00:14,372 - INFO - Request Parameters - Page 1:
2025-05-27 15:00:14,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:14,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:14,888 - INFO - Response - Page 1:
2025-05-27 15:00:15,091 - INFO - 第 1 页获取到 100 条记录
2025-05-27 15:00:15,091 - INFO - Request Parameters - Page 2:
2025-05-27 15:00:15,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:15,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:15,560 - INFO - Response - Page 2:
2025-05-27 15:00:15,763 - INFO - 第 2 页获取到 100 条记录
2025-05-27 15:00:15,763 - INFO - Request Parameters - Page 3:
2025-05-27 15:00:15,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:15,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:16,294 - INFO - Response - Page 3:
2025-05-27 15:00:16,497 - INFO - 第 3 页获取到 100 条记录
2025-05-27 15:00:16,497 - INFO - Request Parameters - Page 4:
2025-05-27 15:00:16,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:16,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:16,966 - INFO - Response - Page 4:
2025-05-27 15:00:17,169 - INFO - 第 4 页获取到 100 条记录
2025-05-27 15:00:17,169 - INFO - Request Parameters - Page 5:
2025-05-27 15:00:17,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:17,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:17,606 - INFO - Response - Page 5:
2025-05-27 15:00:17,810 - INFO - 第 5 页获取到 100 条记录
2025-05-27 15:00:17,810 - INFO - Request Parameters - Page 6:
2025-05-27 15:00:17,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:17,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:18,528 - INFO - Response - Page 6:
2025-05-27 15:00:18,731 - INFO - 第 6 页获取到 100 条记录
2025-05-27 15:00:18,731 - INFO - Request Parameters - Page 7:
2025-05-27 15:00:18,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:18,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:19,231 - INFO - Response - Page 7:
2025-05-27 15:00:19,435 - INFO - 第 7 页获取到 61 条记录
2025-05-27 15:00:19,435 - INFO - 查询完成，共获取到 661 条记录
2025-05-27 15:00:19,435 - INFO - 获取到 661 条表单数据
2025-05-27 15:00:19,435 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-27 15:00:19,450 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 15:00:19,450 - INFO - 开始处理日期: 2025-04
2025-05-27 15:00:19,450 - INFO - Request Parameters - Page 1:
2025-05-27 15:00:19,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:19,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:20,122 - INFO - Response - Page 1:
2025-05-27 15:00:20,325 - INFO - 第 1 页获取到 100 条记录
2025-05-27 15:00:20,325 - INFO - Request Parameters - Page 2:
2025-05-27 15:00:20,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:20,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:20,981 - INFO - Response - Page 2:
2025-05-27 15:00:21,185 - INFO - 第 2 页获取到 100 条记录
2025-05-27 15:00:21,185 - INFO - Request Parameters - Page 3:
2025-05-27 15:00:21,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:21,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:21,622 - INFO - Response - Page 3:
2025-05-27 15:00:21,825 - INFO - 第 3 页获取到 100 条记录
2025-05-27 15:00:21,825 - INFO - Request Parameters - Page 4:
2025-05-27 15:00:21,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:21,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:22,294 - INFO - Response - Page 4:
2025-05-27 15:00:22,497 - INFO - 第 4 页获取到 100 条记录
2025-05-27 15:00:22,497 - INFO - Request Parameters - Page 5:
2025-05-27 15:00:22,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:22,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:22,935 - INFO - Response - Page 5:
2025-05-27 15:00:23,138 - INFO - 第 5 页获取到 100 条记录
2025-05-27 15:00:23,138 - INFO - Request Parameters - Page 6:
2025-05-27 15:00:23,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:23,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:23,575 - INFO - Response - Page 6:
2025-05-27 15:00:23,778 - INFO - 第 6 页获取到 100 条记录
2025-05-27 15:00:23,778 - INFO - Request Parameters - Page 7:
2025-05-27 15:00:23,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:23,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:24,169 - INFO - Response - Page 7:
2025-05-27 15:00:24,372 - INFO - 第 7 页获取到 56 条记录
2025-05-27 15:00:24,372 - INFO - 查询完成，共获取到 656 条记录
2025-05-27 15:00:24,372 - INFO - 获取到 656 条表单数据
2025-05-27 15:00:24,372 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-27 15:00:24,388 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 15:00:24,388 - INFO - 开始处理日期: 2025-05
2025-05-27 15:00:24,388 - INFO - Request Parameters - Page 1:
2025-05-27 15:00:24,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:24,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:24,856 - INFO - Response - Page 1:
2025-05-27 15:00:25,060 - INFO - 第 1 页获取到 100 条记录
2025-05-27 15:00:25,060 - INFO - Request Parameters - Page 2:
2025-05-27 15:00:25,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:25,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:25,544 - INFO - Response - Page 2:
2025-05-27 15:00:25,747 - INFO - 第 2 页获取到 100 条记录
2025-05-27 15:00:25,747 - INFO - Request Parameters - Page 3:
2025-05-27 15:00:25,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:25,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:26,216 - INFO - Response - Page 3:
2025-05-27 15:00:26,419 - INFO - 第 3 页获取到 100 条记录
2025-05-27 15:00:26,419 - INFO - Request Parameters - Page 4:
2025-05-27 15:00:26,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:26,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:27,013 - INFO - Response - Page 4:
2025-05-27 15:00:27,216 - INFO - 第 4 页获取到 100 条记录
2025-05-27 15:00:27,216 - INFO - Request Parameters - Page 5:
2025-05-27 15:00:27,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:27,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:27,950 - INFO - Response - Page 5:
2025-05-27 15:00:28,153 - INFO - 第 5 页获取到 100 条记录
2025-05-27 15:00:28,153 - INFO - Request Parameters - Page 6:
2025-05-27 15:00:28,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:28,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:28,653 - INFO - Response - Page 6:
2025-05-27 15:00:28,856 - INFO - 第 6 页获取到 100 条记录
2025-05-27 15:00:28,856 - INFO - Request Parameters - Page 7:
2025-05-27 15:00:28,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 15:00:28,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 15:00:29,341 - INFO - Response - Page 7:
2025-05-27 15:00:29,544 - INFO - 第 7 页获取到 34 条记录
2025-05-27 15:00:29,544 - INFO - 查询完成，共获取到 634 条记录
2025-05-27 15:00:29,544 - INFO - 获取到 634 条表单数据
2025-05-27 15:00:29,544 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-27 15:00:29,544 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-27 15:00:29,966 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-27 15:00:29,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27791.7, 'new_value': 28936.7}, {'field': 'total_amount', 'old_value': 27791.7, 'new_value': 28936.7}, {'field': 'order_count', 'old_value': 190, 'new_value': 195}]
2025-05-27 15:00:29,966 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-27 15:00:30,403 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-27 15:00:30,403 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 400545.9, 'new_value': 419015.3}, {'field': 'offline_amount', 'old_value': 95266.3, 'new_value': 97054.3}, {'field': 'total_amount', 'old_value': 495812.2, 'new_value': 516069.6}, {'field': 'order_count', 'old_value': 630, 'new_value': 651}]
2025-05-27 15:00:30,419 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-27 15:00:30,872 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-27 15:00:30,872 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32280.69, 'new_value': 33759.2}, {'field': 'total_amount', 'old_value': 32280.69, 'new_value': 33759.2}, {'field': 'order_count', 'old_value': 1199, 'new_value': 1250}]
2025-05-27 15:00:30,872 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-27 15:00:31,341 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-27 15:00:31,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 397097.18, 'new_value': 383885.87}, {'field': 'total_amount', 'old_value': 397097.18, 'new_value': 383885.87}, {'field': 'order_count', 'old_value': 2865, 'new_value': 2806}]
2025-05-27 15:00:31,356 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-27 15:00:31,794 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-27 15:00:31,810 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1175768.34, 'new_value': 1227778.39}, {'field': 'total_amount', 'old_value': 1175768.34, 'new_value': 1227778.39}, {'field': 'order_count', 'old_value': 3448, 'new_value': 3607}]
2025-05-27 15:00:31,810 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-27 15:00:32,669 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-27 15:00:32,669 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 829916.51, 'new_value': 857796.41}, {'field': 'total_amount', 'old_value': 829916.51, 'new_value': 857796.41}, {'field': 'order_count', 'old_value': 2340, 'new_value': 2418}]
2025-05-27 15:00:32,669 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-27 15:00:33,138 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-27 15:00:33,138 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117622.0, 'new_value': 121049.0}, {'field': 'total_amount', 'old_value': 117622.0, 'new_value': 121049.0}, {'field': 'order_count', 'old_value': 1179, 'new_value': 1233}]
2025-05-27 15:00:33,138 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-27 15:00:33,544 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-27 15:00:33,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49032.04, 'new_value': 50417.97}, {'field': 'total_amount', 'old_value': 49032.04, 'new_value': 50417.97}, {'field': 'order_count', 'old_value': 840, 'new_value': 865}]
2025-05-27 15:00:33,544 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-27 15:00:33,997 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-27 15:00:33,997 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126547.77, 'new_value': 129450.87}, {'field': 'offline_amount', 'old_value': 701093.02, 'new_value': 718169.02}, {'field': 'total_amount', 'old_value': 827640.79, 'new_value': 847619.89}, {'field': 'order_count', 'old_value': 1885, 'new_value': 1945}]
2025-05-27 15:00:33,997 - INFO - 日期 2025-05 处理完成 - 更新: 9 条，插入: 0 条，错误: 0 条
2025-05-27 15:00:33,997 - INFO - 数据同步完成！更新: 9 条，插入: 0 条，错误: 0 条
2025-05-27 15:00:33,997 - INFO - =================同步完成====================
2025-05-27 18:00:02,055 - INFO - =================使用默认全量同步=============
2025-05-27 18:00:03,649 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-27 18:00:03,649 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-27 18:00:03,680 - INFO - 开始处理日期: 2025-01
2025-05-27 18:00:03,680 - INFO - Request Parameters - Page 1:
2025-05-27 18:00:03,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:03,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:04,914 - INFO - Response - Page 1:
2025-05-27 18:00:05,117 - INFO - 第 1 页获取到 100 条记录
2025-05-27 18:00:05,117 - INFO - Request Parameters - Page 2:
2025-05-27 18:00:05,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:05,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:05,820 - INFO - Response - Page 2:
2025-05-27 18:00:06,024 - INFO - 第 2 页获取到 100 条记录
2025-05-27 18:00:06,024 - INFO - Request Parameters - Page 3:
2025-05-27 18:00:06,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:06,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:06,633 - INFO - Response - Page 3:
2025-05-27 18:00:06,836 - INFO - 第 3 页获取到 100 条记录
2025-05-27 18:00:06,836 - INFO - Request Parameters - Page 4:
2025-05-27 18:00:06,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:06,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:07,352 - INFO - Response - Page 4:
2025-05-27 18:00:07,555 - INFO - 第 4 页获取到 100 条记录
2025-05-27 18:00:07,555 - INFO - Request Parameters - Page 5:
2025-05-27 18:00:07,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:07,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:08,070 - INFO - Response - Page 5:
2025-05-27 18:00:08,274 - INFO - 第 5 页获取到 100 条记录
2025-05-27 18:00:08,274 - INFO - Request Parameters - Page 6:
2025-05-27 18:00:08,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:08,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:08,758 - INFO - Response - Page 6:
2025-05-27 18:00:08,961 - INFO - 第 6 页获取到 100 条记录
2025-05-27 18:00:08,961 - INFO - Request Parameters - Page 7:
2025-05-27 18:00:08,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:08,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:09,383 - INFO - Response - Page 7:
2025-05-27 18:00:09,586 - INFO - 第 7 页获取到 82 条记录
2025-05-27 18:00:09,586 - INFO - 查询完成，共获取到 682 条记录
2025-05-27 18:00:09,586 - INFO - 获取到 682 条表单数据
2025-05-27 18:00:09,586 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-27 18:00:09,602 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 18:00:09,602 - INFO - 开始处理日期: 2025-02
2025-05-27 18:00:09,602 - INFO - Request Parameters - Page 1:
2025-05-27 18:00:09,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:09,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:10,149 - INFO - Response - Page 1:
2025-05-27 18:00:10,352 - INFO - 第 1 页获取到 100 条记录
2025-05-27 18:00:10,352 - INFO - Request Parameters - Page 2:
2025-05-27 18:00:10,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:10,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:10,852 - INFO - Response - Page 2:
2025-05-27 18:00:11,055 - INFO - 第 2 页获取到 100 条记录
2025-05-27 18:00:11,055 - INFO - Request Parameters - Page 3:
2025-05-27 18:00:11,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:11,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:11,664 - INFO - Response - Page 3:
2025-05-27 18:00:11,867 - INFO - 第 3 页获取到 100 条记录
2025-05-27 18:00:11,867 - INFO - Request Parameters - Page 4:
2025-05-27 18:00:11,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:11,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:12,789 - INFO - Response - Page 4:
2025-05-27 18:00:12,992 - INFO - 第 4 页获取到 100 条记录
2025-05-27 18:00:12,992 - INFO - Request Parameters - Page 5:
2025-05-27 18:00:12,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:12,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:13,445 - INFO - Response - Page 5:
2025-05-27 18:00:13,649 - INFO - 第 5 页获取到 100 条记录
2025-05-27 18:00:13,649 - INFO - Request Parameters - Page 6:
2025-05-27 18:00:13,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:13,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:14,117 - INFO - Response - Page 6:
2025-05-27 18:00:14,320 - INFO - 第 6 页获取到 100 条记录
2025-05-27 18:00:14,320 - INFO - Request Parameters - Page 7:
2025-05-27 18:00:14,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:14,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:14,852 - INFO - Response - Page 7:
2025-05-27 18:00:15,055 - INFO - 第 7 页获取到 70 条记录
2025-05-27 18:00:15,055 - INFO - 查询完成，共获取到 670 条记录
2025-05-27 18:00:15,055 - INFO - 获取到 670 条表单数据
2025-05-27 18:00:15,055 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-27 18:00:15,070 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 18:00:15,070 - INFO - 开始处理日期: 2025-03
2025-05-27 18:00:15,070 - INFO - Request Parameters - Page 1:
2025-05-27 18:00:15,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:15,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:15,602 - INFO - Response - Page 1:
2025-05-27 18:00:15,805 - INFO - 第 1 页获取到 100 条记录
2025-05-27 18:00:15,805 - INFO - Request Parameters - Page 2:
2025-05-27 18:00:15,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:15,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:16,274 - INFO - Response - Page 2:
2025-05-27 18:00:16,492 - INFO - 第 2 页获取到 100 条记录
2025-05-27 18:00:16,492 - INFO - Request Parameters - Page 3:
2025-05-27 18:00:16,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:16,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:16,961 - INFO - Response - Page 3:
2025-05-27 18:00:17,164 - INFO - 第 3 页获取到 100 条记录
2025-05-27 18:00:17,164 - INFO - Request Parameters - Page 4:
2025-05-27 18:00:17,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:17,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:17,695 - INFO - Response - Page 4:
2025-05-27 18:00:17,899 - INFO - 第 4 页获取到 100 条记录
2025-05-27 18:00:17,899 - INFO - Request Parameters - Page 5:
2025-05-27 18:00:17,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:17,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:18,367 - INFO - Response - Page 5:
2025-05-27 18:00:18,570 - INFO - 第 5 页获取到 100 条记录
2025-05-27 18:00:18,570 - INFO - Request Parameters - Page 6:
2025-05-27 18:00:18,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:18,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:19,102 - INFO - Response - Page 6:
2025-05-27 18:00:19,305 - INFO - 第 6 页获取到 100 条记录
2025-05-27 18:00:19,305 - INFO - Request Parameters - Page 7:
2025-05-27 18:00:19,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:19,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:19,789 - INFO - Response - Page 7:
2025-05-27 18:00:19,992 - INFO - 第 7 页获取到 61 条记录
2025-05-27 18:00:19,992 - INFO - 查询完成，共获取到 661 条记录
2025-05-27 18:00:19,992 - INFO - 获取到 661 条表单数据
2025-05-27 18:00:19,992 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-27 18:00:20,008 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 18:00:20,008 - INFO - 开始处理日期: 2025-04
2025-05-27 18:00:20,008 - INFO - Request Parameters - Page 1:
2025-05-27 18:00:20,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:20,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:20,648 - INFO - Response - Page 1:
2025-05-27 18:00:20,852 - INFO - 第 1 页获取到 100 条记录
2025-05-27 18:00:20,852 - INFO - Request Parameters - Page 2:
2025-05-27 18:00:20,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:20,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:21,461 - INFO - Response - Page 2:
2025-05-27 18:00:21,664 - INFO - 第 2 页获取到 100 条记录
2025-05-27 18:00:21,664 - INFO - Request Parameters - Page 3:
2025-05-27 18:00:21,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:21,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:22,273 - INFO - Response - Page 3:
2025-05-27 18:00:22,477 - INFO - 第 3 页获取到 100 条记录
2025-05-27 18:00:22,477 - INFO - Request Parameters - Page 4:
2025-05-27 18:00:22,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:22,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:22,961 - INFO - Response - Page 4:
2025-05-27 18:00:23,164 - INFO - 第 4 页获取到 100 条记录
2025-05-27 18:00:23,164 - INFO - Request Parameters - Page 5:
2025-05-27 18:00:23,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:23,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:23,711 - INFO - Response - Page 5:
2025-05-27 18:00:23,914 - INFO - 第 5 页获取到 100 条记录
2025-05-27 18:00:23,914 - INFO - Request Parameters - Page 6:
2025-05-27 18:00:23,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:23,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:24,367 - INFO - Response - Page 6:
2025-05-27 18:00:24,570 - INFO - 第 6 页获取到 100 条记录
2025-05-27 18:00:24,570 - INFO - Request Parameters - Page 7:
2025-05-27 18:00:24,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:24,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:25,008 - INFO - Response - Page 7:
2025-05-27 18:00:25,211 - INFO - 第 7 页获取到 56 条记录
2025-05-27 18:00:25,211 - INFO - 查询完成，共获取到 656 条记录
2025-05-27 18:00:25,211 - INFO - 获取到 656 条表单数据
2025-05-27 18:00:25,211 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-27 18:00:25,227 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 18:00:25,227 - INFO - 开始处理日期: 2025-05
2025-05-27 18:00:25,227 - INFO - Request Parameters - Page 1:
2025-05-27 18:00:25,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:25,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:25,695 - INFO - Response - Page 1:
2025-05-27 18:00:25,898 - INFO - 第 1 页获取到 100 条记录
2025-05-27 18:00:25,898 - INFO - Request Parameters - Page 2:
2025-05-27 18:00:25,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:25,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:26,414 - INFO - Response - Page 2:
2025-05-27 18:00:26,617 - INFO - 第 2 页获取到 100 条记录
2025-05-27 18:00:26,617 - INFO - Request Parameters - Page 3:
2025-05-27 18:00:26,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:26,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:27,289 - INFO - Response - Page 3:
2025-05-27 18:00:27,492 - INFO - 第 3 页获取到 100 条记录
2025-05-27 18:00:27,492 - INFO - Request Parameters - Page 4:
2025-05-27 18:00:27,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:27,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:28,180 - INFO - Response - Page 4:
2025-05-27 18:00:28,383 - INFO - 第 4 页获取到 100 条记录
2025-05-27 18:00:28,383 - INFO - Request Parameters - Page 5:
2025-05-27 18:00:28,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:28,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:28,852 - INFO - Response - Page 5:
2025-05-27 18:00:29,055 - INFO - 第 5 页获取到 100 条记录
2025-05-27 18:00:29,055 - INFO - Request Parameters - Page 6:
2025-05-27 18:00:29,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:29,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:29,633 - INFO - Response - Page 6:
2025-05-27 18:00:29,836 - INFO - 第 6 页获取到 100 条记录
2025-05-27 18:00:29,836 - INFO - Request Parameters - Page 7:
2025-05-27 18:00:29,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 18:00:29,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 18:00:30,430 - INFO - Response - Page 7:
2025-05-27 18:00:30,633 - INFO - 第 7 页获取到 34 条记录
2025-05-27 18:00:30,633 - INFO - 查询完成，共获取到 634 条记录
2025-05-27 18:00:30,633 - INFO - 获取到 634 条表单数据
2025-05-27 18:00:30,633 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-27 18:00:30,633 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-27 18:00:31,086 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-27 18:00:31,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 739291.98, 'new_value': 763707.98}, {'field': 'total_amount', 'old_value': 739291.98, 'new_value': 763707.98}, {'field': 'order_count', 'old_value': 2303, 'new_value': 2397}]
2025-05-27 18:00:31,086 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-27 18:00:31,523 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-27 18:00:31,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96713.15, 'new_value': 98229.15}, {'field': 'total_amount', 'old_value': 128293.03, 'new_value': 129809.03}, {'field': 'order_count', 'old_value': 2900, 'new_value': 2999}]
2025-05-27 18:00:31,523 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-27 18:00:32,008 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-27 18:00:32,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98684.23, 'new_value': 96947.95}, {'field': 'total_amount', 'old_value': 98684.23, 'new_value': 96947.95}, {'field': 'order_count', 'old_value': 3671, 'new_value': 3738}]
2025-05-27 18:00:32,008 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-27 18:00:32,461 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-27 18:00:32,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32513.8, 'new_value': 34388.6}, {'field': 'total_amount', 'old_value': 32513.8, 'new_value': 34388.6}, {'field': 'order_count', 'old_value': 58, 'new_value': 64}]
2025-05-27 18:00:32,461 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-27 18:00:33,008 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-27 18:00:33,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109307.18, 'new_value': 118823.9}, {'field': 'total_amount', 'old_value': 109307.18, 'new_value': 118823.9}, {'field': 'order_count', 'old_value': 86, 'new_value': 90}]
2025-05-27 18:00:33,008 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-27 18:00:33,461 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-27 18:00:33,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1321000.0, 'new_value': 1326000.0}, {'field': 'total_amount', 'old_value': 1321000.0, 'new_value': 1326000.0}]
2025-05-27 18:00:33,461 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-27 18:00:34,070 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-27 18:00:34,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 247299.4, 'new_value': 248689.6}, {'field': 'total_amount', 'old_value': 285933.3, 'new_value': 287323.5}, {'field': 'order_count', 'old_value': 2284, 'new_value': 2340}]
2025-05-27 18:00:34,070 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-27 18:00:34,555 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-27 18:00:34,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49240.0, 'new_value': 50920.0}, {'field': 'total_amount', 'old_value': 49240.0, 'new_value': 50920.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-27 18:00:34,555 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-27 18:00:35,023 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-27 18:00:35,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150896.38, 'new_value': 151483.54}, {'field': 'total_amount', 'old_value': 150896.38, 'new_value': 151483.54}, {'field': 'order_count', 'old_value': 13066, 'new_value': 13608}]
2025-05-27 18:00:35,023 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-27 18:00:35,430 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-27 18:00:35,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126584.0, 'new_value': 126300.0}, {'field': 'total_amount', 'old_value': 126584.0, 'new_value': 126300.0}, {'field': 'order_count', 'old_value': 4385, 'new_value': 4543}]
2025-05-27 18:00:35,430 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-27 18:00:35,898 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-27 18:00:35,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32209.07, 'new_value': 32427.07}, {'field': 'total_amount', 'old_value': 32209.07, 'new_value': 32427.07}, {'field': 'order_count', 'old_value': 2977, 'new_value': 3100}]
2025-05-27 18:00:35,898 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-27 18:00:36,305 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-27 18:00:36,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38851.64, 'new_value': 35987.14}, {'field': 'total_amount', 'old_value': 99629.34, 'new_value': 96764.84}, {'field': 'order_count', 'old_value': 11519, 'new_value': 11710}]
2025-05-27 18:00:36,305 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-27 18:00:36,789 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-27 18:00:36,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7323200.0, 'new_value': 7346800.0}, {'field': 'total_amount', 'old_value': 7323200.0, 'new_value': 7346800.0}]
2025-05-27 18:00:36,789 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-27 18:00:37,242 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-27 18:00:37,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 419643.0, 'new_value': 437311.0}, {'field': 'total_amount', 'old_value': 428461.99, 'new_value': 446129.99}]
2025-05-27 18:00:37,242 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-27 18:00:37,648 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-27 18:00:37,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 456530.45, 'new_value': 443927.86}, {'field': 'total_amount', 'old_value': 456530.45, 'new_value': 443927.86}, {'field': 'order_count', 'old_value': 818, 'new_value': 833}]
2025-05-27 18:00:37,648 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-27 18:00:38,086 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-27 18:00:38,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95847.0, 'new_value': 95597.0}, {'field': 'total_amount', 'old_value': 95847.0, 'new_value': 95597.0}]
2025-05-27 18:00:38,086 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-27 18:00:38,570 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-27 18:00:38,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68360.0, 'new_value': 34675.0}, {'field': 'total_amount', 'old_value': 69736.0, 'new_value': 36051.0}, {'field': 'order_count', 'old_value': 3523, 'new_value': 3638}]
2025-05-27 18:00:38,570 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-27 18:00:38,976 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-27 18:00:38,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136580.87, 'new_value': 137497.03}, {'field': 'total_amount', 'old_value': 155106.6, 'new_value': 156022.76}, {'field': 'order_count', 'old_value': 4580, 'new_value': 4683}]
2025-05-27 18:00:38,976 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-27 18:00:39,430 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-27 18:00:39,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 383885.87, 'new_value': 396259.87}, {'field': 'total_amount', 'old_value': 383885.87, 'new_value': 396259.87}, {'field': 'order_count', 'old_value': 2806, 'new_value': 2842}]
2025-05-27 18:00:39,430 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-27 18:00:39,867 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-27 18:00:39,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105136.0, 'new_value': 106652.0}, {'field': 'total_amount', 'old_value': 105136.0, 'new_value': 106652.0}, {'field': 'order_count', 'old_value': 685, 'new_value': 716}]
2025-05-27 18:00:39,867 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-27 18:00:40,273 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-27 18:00:40,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1069900.0, 'new_value': 1129900.0}, {'field': 'total_amount', 'old_value': 1069900.0, 'new_value': 1129900.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 75}]
2025-05-27 18:00:40,273 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-27 18:00:40,773 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-27 18:00:40,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12290.67, 'new_value': 13018.67}, {'field': 'total_amount', 'old_value': 12290.67, 'new_value': 13018.67}, {'field': 'order_count', 'old_value': 353, 'new_value': 371}]
2025-05-27 18:00:40,773 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-27 18:00:41,258 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-27 18:00:41,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 347928.5, 'new_value': 343028.5}, {'field': 'total_amount', 'old_value': 347928.5, 'new_value': 343028.5}]
2025-05-27 18:00:41,258 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-27 18:00:41,664 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-27 18:00:41,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156673.82, 'new_value': 160151.82}, {'field': 'total_amount', 'old_value': 156673.82, 'new_value': 160151.82}, {'field': 'order_count', 'old_value': 1331, 'new_value': 1371}]
2025-05-27 18:00:41,664 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-27 18:00:42,117 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-27 18:00:42,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17280.0, 'new_value': 19035.0}, {'field': 'total_amount', 'old_value': 17280.0, 'new_value': 19035.0}, {'field': 'order_count', 'old_value': 112, 'new_value': 124}]
2025-05-27 18:00:42,117 - INFO - 开始更新记录 - 表单实例ID: FINST-QZE668D186RVL112BFPNG4ULXOMU20RL9K4BM5
2025-05-27 18:00:42,617 - INFO - 更新表单数据成功: FINST-QZE668D186RVL112BFPNG4ULXOMU20RL9K4BM5
2025-05-27 18:00:42,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 198562.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 198562.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 169}]
2025-05-27 18:00:42,617 - INFO - 开始更新记录 - 表单实例ID: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-05-27 18:00:43,039 - INFO - 更新表单数据成功: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-05-27 18:00:43,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2593.48, 'new_value': 18663.61}, {'field': 'total_amount', 'old_value': 2593.48, 'new_value': 18663.61}, {'field': 'order_count', 'old_value': 68, 'new_value': 808}]
2025-05-27 18:00:43,055 - INFO - 日期 2025-05 处理完成 - 更新: 27 条，插入: 0 条，错误: 0 条
2025-05-27 18:00:43,055 - INFO - 数据同步完成！更新: 27 条，插入: 0 条，错误: 0 条
2025-05-27 18:00:43,055 - INFO - =================同步完成====================
2025-05-27 21:00:02,103 - INFO - =================使用默认全量同步=============
2025-05-27 21:00:03,572 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-27 21:00:03,572 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-27 21:00:03,604 - INFO - 开始处理日期: 2025-01
2025-05-27 21:00:03,604 - INFO - Request Parameters - Page 1:
2025-05-27 21:00:03,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:03,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:04,526 - INFO - Response - Page 1:
2025-05-27 21:00:04,729 - INFO - 第 1 页获取到 100 条记录
2025-05-27 21:00:04,729 - INFO - Request Parameters - Page 2:
2025-05-27 21:00:04,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:04,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:05,401 - INFO - Response - Page 2:
2025-05-27 21:00:05,604 - INFO - 第 2 页获取到 100 条记录
2025-05-27 21:00:05,604 - INFO - Request Parameters - Page 3:
2025-05-27 21:00:05,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:05,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:06,104 - INFO - Response - Page 3:
2025-05-27 21:00:06,307 - INFO - 第 3 页获取到 100 条记录
2025-05-27 21:00:06,307 - INFO - Request Parameters - Page 4:
2025-05-27 21:00:06,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:06,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:06,823 - INFO - Response - Page 4:
2025-05-27 21:00:07,026 - INFO - 第 4 页获取到 100 条记录
2025-05-27 21:00:07,026 - INFO - Request Parameters - Page 5:
2025-05-27 21:00:07,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:07,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:07,479 - INFO - Response - Page 5:
2025-05-27 21:00:07,682 - INFO - 第 5 页获取到 100 条记录
2025-05-27 21:00:07,682 - INFO - Request Parameters - Page 6:
2025-05-27 21:00:07,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:07,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:08,589 - INFO - Response - Page 6:
2025-05-27 21:00:08,792 - INFO - 第 6 页获取到 100 条记录
2025-05-27 21:00:08,792 - INFO - Request Parameters - Page 7:
2025-05-27 21:00:08,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:08,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:09,198 - INFO - Response - Page 7:
2025-05-27 21:00:09,401 - INFO - 第 7 页获取到 82 条记录
2025-05-27 21:00:09,401 - INFO - 查询完成，共获取到 682 条记录
2025-05-27 21:00:09,401 - INFO - 获取到 682 条表单数据
2025-05-27 21:00:09,401 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-27 21:00:09,417 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 21:00:09,417 - INFO - 开始处理日期: 2025-02
2025-05-27 21:00:09,417 - INFO - Request Parameters - Page 1:
2025-05-27 21:00:09,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:09,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:09,964 - INFO - Response - Page 1:
2025-05-27 21:00:10,167 - INFO - 第 1 页获取到 100 条记录
2025-05-27 21:00:10,167 - INFO - Request Parameters - Page 2:
2025-05-27 21:00:10,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:10,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:10,651 - INFO - Response - Page 2:
2025-05-27 21:00:10,854 - INFO - 第 2 页获取到 100 条记录
2025-05-27 21:00:10,854 - INFO - Request Parameters - Page 3:
2025-05-27 21:00:10,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:10,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:11,339 - INFO - Response - Page 3:
2025-05-27 21:00:11,542 - INFO - 第 3 页获取到 100 条记录
2025-05-27 21:00:11,542 - INFO - Request Parameters - Page 4:
2025-05-27 21:00:11,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:11,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:12,058 - INFO - Response - Page 4:
2025-05-27 21:00:12,261 - INFO - 第 4 页获取到 100 条记录
2025-05-27 21:00:12,261 - INFO - Request Parameters - Page 5:
2025-05-27 21:00:12,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:12,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:12,792 - INFO - Response - Page 5:
2025-05-27 21:00:12,995 - INFO - 第 5 页获取到 100 条记录
2025-05-27 21:00:12,995 - INFO - Request Parameters - Page 6:
2025-05-27 21:00:12,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:12,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:13,464 - INFO - Response - Page 6:
2025-05-27 21:00:13,667 - INFO - 第 6 页获取到 100 条记录
2025-05-27 21:00:13,667 - INFO - Request Parameters - Page 7:
2025-05-27 21:00:13,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:13,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:14,120 - INFO - Response - Page 7:
2025-05-27 21:00:14,324 - INFO - 第 7 页获取到 70 条记录
2025-05-27 21:00:14,324 - INFO - 查询完成，共获取到 670 条记录
2025-05-27 21:00:14,324 - INFO - 获取到 670 条表单数据
2025-05-27 21:00:14,324 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-27 21:00:14,339 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 21:00:14,339 - INFO - 开始处理日期: 2025-03
2025-05-27 21:00:14,339 - INFO - Request Parameters - Page 1:
2025-05-27 21:00:14,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:14,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:14,855 - INFO - Response - Page 1:
2025-05-27 21:00:15,058 - INFO - 第 1 页获取到 100 条记录
2025-05-27 21:00:15,058 - INFO - Request Parameters - Page 2:
2025-05-27 21:00:15,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:15,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:15,543 - INFO - Response - Page 2:
2025-05-27 21:00:15,746 - INFO - 第 2 页获取到 100 条记录
2025-05-27 21:00:15,746 - INFO - Request Parameters - Page 3:
2025-05-27 21:00:15,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:15,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:16,230 - INFO - Response - Page 3:
2025-05-27 21:00:16,433 - INFO - 第 3 页获取到 100 条记录
2025-05-27 21:00:16,433 - INFO - Request Parameters - Page 4:
2025-05-27 21:00:16,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:16,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:16,933 - INFO - Response - Page 4:
2025-05-27 21:00:17,136 - INFO - 第 4 页获取到 100 条记录
2025-05-27 21:00:17,136 - INFO - Request Parameters - Page 5:
2025-05-27 21:00:17,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:17,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:17,668 - INFO - Response - Page 5:
2025-05-27 21:00:17,871 - INFO - 第 5 页获取到 100 条记录
2025-05-27 21:00:17,871 - INFO - Request Parameters - Page 6:
2025-05-27 21:00:17,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:17,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:18,371 - INFO - Response - Page 6:
2025-05-27 21:00:18,574 - INFO - 第 6 页获取到 100 条记录
2025-05-27 21:00:18,574 - INFO - Request Parameters - Page 7:
2025-05-27 21:00:18,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:18,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:19,043 - INFO - Response - Page 7:
2025-05-27 21:00:19,246 - INFO - 第 7 页获取到 61 条记录
2025-05-27 21:00:19,246 - INFO - 查询完成，共获取到 661 条记录
2025-05-27 21:00:19,246 - INFO - 获取到 661 条表单数据
2025-05-27 21:00:19,246 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-27 21:00:19,262 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 21:00:19,262 - INFO - 开始处理日期: 2025-04
2025-05-27 21:00:19,262 - INFO - Request Parameters - Page 1:
2025-05-27 21:00:19,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:19,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:19,793 - INFO - Response - Page 1:
2025-05-27 21:00:19,996 - INFO - 第 1 页获取到 100 条记录
2025-05-27 21:00:19,996 - INFO - Request Parameters - Page 2:
2025-05-27 21:00:19,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:19,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:20,465 - INFO - Response - Page 2:
2025-05-27 21:00:20,668 - INFO - 第 2 页获取到 100 条记录
2025-05-27 21:00:20,668 - INFO - Request Parameters - Page 3:
2025-05-27 21:00:20,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:20,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:21,137 - INFO - Response - Page 3:
2025-05-27 21:00:21,340 - INFO - 第 3 页获取到 100 条记录
2025-05-27 21:00:21,340 - INFO - Request Parameters - Page 4:
2025-05-27 21:00:21,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:21,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:21,840 - INFO - Response - Page 4:
2025-05-27 21:00:22,043 - INFO - 第 4 页获取到 100 条记录
2025-05-27 21:00:22,043 - INFO - Request Parameters - Page 5:
2025-05-27 21:00:22,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:22,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:22,575 - INFO - Response - Page 5:
2025-05-27 21:00:22,778 - INFO - 第 5 页获取到 100 条记录
2025-05-27 21:00:22,778 - INFO - Request Parameters - Page 6:
2025-05-27 21:00:22,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:22,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:23,231 - INFO - Response - Page 6:
2025-05-27 21:00:23,434 - INFO - 第 6 页获取到 100 条记录
2025-05-27 21:00:23,434 - INFO - Request Parameters - Page 7:
2025-05-27 21:00:23,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:23,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:23,825 - INFO - Response - Page 7:
2025-05-27 21:00:24,028 - INFO - 第 7 页获取到 56 条记录
2025-05-27 21:00:24,028 - INFO - 查询完成，共获取到 656 条记录
2025-05-27 21:00:24,028 - INFO - 获取到 656 条表单数据
2025-05-27 21:00:24,028 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-27 21:00:24,044 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-27 21:00:24,044 - INFO - 开始处理日期: 2025-05
2025-05-27 21:00:24,044 - INFO - Request Parameters - Page 1:
2025-05-27 21:00:24,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:24,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:24,591 - INFO - Response - Page 1:
2025-05-27 21:00:24,794 - INFO - 第 1 页获取到 100 条记录
2025-05-27 21:00:24,794 - INFO - Request Parameters - Page 2:
2025-05-27 21:00:24,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:24,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:25,294 - INFO - Response - Page 2:
2025-05-27 21:00:25,497 - INFO - 第 2 页获取到 100 条记录
2025-05-27 21:00:25,497 - INFO - Request Parameters - Page 3:
2025-05-27 21:00:25,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:25,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:26,044 - INFO - Response - Page 3:
2025-05-27 21:00:26,247 - INFO - 第 3 页获取到 100 条记录
2025-05-27 21:00:26,247 - INFO - Request Parameters - Page 4:
2025-05-27 21:00:26,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:26,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:26,747 - INFO - Response - Page 4:
2025-05-27 21:00:26,950 - INFO - 第 4 页获取到 100 条记录
2025-05-27 21:00:26,950 - INFO - Request Parameters - Page 5:
2025-05-27 21:00:26,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:26,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:27,450 - INFO - Response - Page 5:
2025-05-27 21:00:27,653 - INFO - 第 5 页获取到 100 条记录
2025-05-27 21:00:27,653 - INFO - Request Parameters - Page 6:
2025-05-27 21:00:27,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:27,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:28,185 - INFO - Response - Page 6:
2025-05-27 21:00:28,388 - INFO - 第 6 页获取到 100 条记录
2025-05-27 21:00:28,388 - INFO - Request Parameters - Page 7:
2025-05-27 21:00:28,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-27 21:00:28,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-27 21:00:28,779 - INFO - Response - Page 7:
2025-05-27 21:00:28,982 - INFO - 第 7 页获取到 34 条记录
2025-05-27 21:00:28,982 - INFO - 查询完成，共获取到 634 条记录
2025-05-27 21:00:28,982 - INFO - 获取到 634 条表单数据
2025-05-27 21:00:28,982 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-27 21:00:28,982 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-27 21:00:29,466 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-27 21:00:29,466 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 423167.0, 'new_value': 446856.0}, {'field': 'offline_amount', 'old_value': 328414.0, 'new_value': 339314.0}, {'field': 'total_amount', 'old_value': 751581.0, 'new_value': 786170.0}, {'field': 'order_count', 'old_value': 830, 'new_value': 863}]
2025-05-27 21:00:29,482 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-27 21:00:30,013 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-27 21:00:30,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32184.2, 'new_value': 32710.2}, {'field': 'offline_amount', 'old_value': 173658.02, 'new_value': 176559.02}, {'field': 'total_amount', 'old_value': 205842.22, 'new_value': 209269.22}, {'field': 'order_count', 'old_value': 268, 'new_value': 277}]
2025-05-27 21:00:30,013 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-27 21:00:30,419 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-27 21:00:30,419 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 254720.65, 'new_value': 268780.65}, {'field': 'offline_amount', 'old_value': 125313.0, 'new_value': 130381.0}, {'field': 'total_amount', 'old_value': 380033.65, 'new_value': 399161.65}, {'field': 'order_count', 'old_value': 1961, 'new_value': 2022}]
2025-05-27 21:00:30,419 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-27 21:00:30,873 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-27 21:00:30,873 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27764.0, 'new_value': 28663.0}, {'field': 'total_amount', 'old_value': 27764.0, 'new_value': 28663.0}, {'field': 'order_count', 'old_value': 160, 'new_value': 166}]
2025-05-27 21:00:30,873 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-27 21:00:31,373 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-27 21:00:31,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52863.45, 'new_value': 54453.2}, {'field': 'total_amount', 'old_value': 56304.25, 'new_value': 57894.0}, {'field': 'order_count', 'old_value': 213, 'new_value': 218}]
2025-05-27 21:00:31,388 - INFO - 日期 2025-05 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-05-27 21:00:31,388 - INFO - 数据同步完成！更新: 5 条，插入: 0 条，错误: 0 条
2025-05-27 21:00:31,388 - INFO - =================同步完成====================
