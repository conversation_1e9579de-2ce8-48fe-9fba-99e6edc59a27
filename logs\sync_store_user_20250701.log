2025-07-01 09:00:02,705 - INFO - 数据库连接成功
2025-07-01 09:00:02,893 - INFO - 获取钉钉access_token成功
2025-07-01 09:00:02,893 - INFO - 宜搭客户端初始化成功
2025-07-01 09:00:02,893 - INFO - 正在获取数据库店铺信息...
2025-07-01 09:00:02,893 - INFO - 第一步：获取基础店铺信息...
2025-07-01 09:00:02,939 - INFO - 获取基础店铺信息成功，共 1267 条记录
2025-07-01 09:00:02,939 - INFO - 第二步：获取上个月或本月有销售记录的店铺...
2025-07-01 09:00:03,018 - INFO - 上个月或本月有销售记录的店铺数量: 629
2025-07-01 09:00:03,033 - INFO - 第三步：获取上个月以前有销售记录的店铺...
2025-07-01 09:00:03,205 - INFO - 上个月以前有销售记录的店铺数量: 622
2025-07-01 09:00:03,205 - INFO - 检测到新店铺数量: 7
2025-07-01 09:00:03,205 - INFO - 新店铺编码列表: ['100101302', '100101291', '100101272', '100100073', '100101266', '100101297', '100101299']
2025-07-01 09:00:03,205 - INFO - 成功获取数据库店铺信息，共 1267 条记录
2025-07-01 09:00:03,205 - INFO - 正在获取宜搭店铺信息...
2025-07-01 09:00:05,971 - INFO - 店铺 100101285 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100101288 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 ********* 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100098437 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100100085 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100100110 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100100135 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100100854 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100100842 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100098300 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100100048 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100098427 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100100071 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100098274 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100098440 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100100091 的userid值为空
2025-07-01 09:00:05,971 - INFO - 店铺 100100113 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100098303 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100100140 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100100851 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100098289 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100100839 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100098260 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100100070 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100100089 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100100112 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100098273 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100100852 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100098486 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100100840 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100100101 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100098296 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100100130 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100098355 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100100849 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 100101278 的userid值为空
2025-07-01 09:00:07,096 - INFO - 店铺 ********* 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100129 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100845 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100098477 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100033 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100098295 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100098250 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100850 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100050 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100098237 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100083 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100098276 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100108 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100094 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100117 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100098474 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100134 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100858 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100098343 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100846 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100829 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100098398 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100098284 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100049 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100106 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100133 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100098249 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100092 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100098297 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100099109 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100100114 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100098275 的userid值为空
2025-07-01 09:00:07,111 - INFO - 店铺 100098342 的userid值为空
2025-07-01 09:00:07,830 - INFO - 店铺 100100141 的userid值为空
2025-07-01 09:00:07,830 - INFO - 店铺 100100855 的userid值为空
2025-07-01 09:00:07,830 - INFO - 店铺 100098597 的userid值为空
2025-07-01 09:00:07,830 - INFO - 店铺 100100843 的userid值为空
2025-07-01 09:00:07,830 - INFO - 店铺 100098385 的userid值为空
2025-07-01 09:00:07,830 - INFO - 店铺 100100037 的userid值为空
2025-07-01 09:00:07,830 - INFO - 店铺 100100371 的userid值为空
2025-07-01 09:00:07,830 - INFO - 店铺 100100053 的userid值为空
2025-07-01 09:00:07,830 - INFO - 店铺 100100075 的userid值为空
2025-07-01 09:00:07,830 - INFO - 店铺 100098447 的userid值为空
2025-07-01 09:00:07,830 - INFO - 店铺 100100096 的userid值为空
2025-07-01 09:00:07,830 - INFO - 店铺 100098461 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100100856 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100100844 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100098384 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100100035 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100100052 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100100074 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100098445 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100100095 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100100121 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100100067 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100100088 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100100111 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100100136 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100100853 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100098287 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100098485 的userid值为空
2025-07-01 09:00:07,846 - INFO - 店铺 100100841 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099117 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099272 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100101121 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099265 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099243 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100369 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099231 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099216 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100361 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100328 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099189 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099931 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100278 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099304 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099305 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100101122 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099289 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100101265 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100438 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100101275 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099266 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100101304 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099212 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099188 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099150 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100224 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099332 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100881 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099299 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100456 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099284 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100880 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100101119 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099829 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099263 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099153 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100236 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099936 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100101120 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100447 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100865 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099264 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100875 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099186 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099824 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100264 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099147 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100100217 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099930 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099346 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099323 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099297 的userid值为空
2025-07-01 09:00:08,502 - INFO - 店铺 100099259 的userid值为空
2025-07-01 09:00:08,517 - INFO - 店铺 100100964 的userid值为空
2025-07-01 09:00:08,517 - INFO - 店铺 100099187 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099984 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100265 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099928 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099148 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100894 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099983 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099796 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099298 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100101118 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099261 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100435 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099870 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099199 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099985 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100262 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099317 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099978 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100436 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100101181 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100321 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099185 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100233 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099345 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099980 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099322 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100893 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100437 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099282 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100892 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100433 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099979 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100665 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099923 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100101183 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099197 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100241 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100099176 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100101162 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100098583 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100101173 的userid值为空
2025-07-01 09:00:09,221 - INFO - 店铺 100100944 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100099803 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100098579 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100101153 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100099804 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100100359 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100100344 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100100818 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100099863 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100100347 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100100667 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100100891 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100099853 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100100890 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100100342 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100099861 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100100670 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100100341 的userid值为空
2025-07-01 09:00:09,924 - INFO - 店铺 100100887 的userid值为空
2025-07-01 09:00:09,939 - INFO - 店铺 100100481 的userid值为空
2025-07-01 09:00:09,939 - INFO - 店铺 100100886 的userid值为空
2025-07-01 09:00:09,939 - INFO - 店铺 100099911 的userid值为空
2025-07-01 09:00:09,939 - INFO - 店铺 100100646 的userid值为空
2025-07-01 09:00:09,939 - INFO - 店铺 100100889 的userid值为空
2025-07-01 09:00:09,939 - INFO - 店铺 100100888 的userid值为空
2025-07-01 09:00:10,642 - INFO - 店铺 100100883 的userid值为空
2025-07-01 09:00:10,642 - INFO - 店铺 100100922 的userid值为空
2025-07-01 09:00:10,642 - INFO - 店铺 100100882 的userid值为空
2025-07-01 09:00:10,642 - INFO - 店铺 100100016 的userid值为空
2025-07-01 09:00:10,642 - INFO - 店铺 100099835 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100885 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100099910 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100884 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100099956 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100099303 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100006 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100900 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100099903 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100099283 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100024 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100293 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100434 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100098391 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100044 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100099198 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100314 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100061 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100099177 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100249 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100098436 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100848 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100833 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100099294 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100453 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100199 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100098358 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100099280 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100101129 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100379 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100229 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100663 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100099256 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100099237 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100374 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100339 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100042 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100099192 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100098405 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100060 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100100082 的userid值为空
2025-07-01 09:00:10,658 - INFO - 店铺 100099295 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100099281 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100100664 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100099101 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100100380 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100100375 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100101243 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100099226 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100101241 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100099193 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100101224 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100099313 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100100084 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100099292 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100101242 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100101256 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100099277 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100100918 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100101125 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100101154 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100100377 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100101156 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100099271 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100101132 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100101155 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100099217 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100101283 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100099190 的userid值为空
2025-07-01 09:00:11,299 - INFO - 店铺 100101284 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100101282 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100101287 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100101296 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100101295 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100099293 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100101308 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100101310 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100100378 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100101313 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100099249 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100101312 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100101316 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100100373 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100099218 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100100335 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100099191 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100100291 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100099171 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100100238 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100099338 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100099307 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100099290 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100099274 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100101123 的userid值为空
2025-07-01 09:00:11,314 - INFO - 店铺 100100466 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100368 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100327 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100269 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100101124 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100099291 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100099275 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100926 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100376 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100099270 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100098351 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100156 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100099615 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100099905 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100098488 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100098350 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100099503 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100027 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100099613 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100004 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100154 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100099500 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100101180 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100046 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100099899 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100098424 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100098246 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100128 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100182 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100917 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100201 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100098360 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100098572 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100099995 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100098377 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100026 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100098598 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100300 的userid值为空
2025-07-01 09:00:12,017 - INFO - 店铺 100100279 的userid值为空
2025-07-01 09:00:12,767 - INFO - 店铺 100098596 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099681 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100045 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100870 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099588 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100098423 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099675 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099988 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100098258 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100177 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100162 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100198 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099793 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100187 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099104 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099816 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100904 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100210 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099499 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100148 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100175 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099913 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100196 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100303 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100280 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100029 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100098317 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100098397 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100860 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100255 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100661 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100098589 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100152 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099525 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100160 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100180 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100098594 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099119 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099987 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100185 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100098561 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100098348 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100207 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100861 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100098559 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100098362 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100150 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100258 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099906 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100100178 的userid值为空
2025-07-01 09:00:12,783 - INFO - 店铺 100099811 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100028 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100098588 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100143 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100320 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100099585 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100098247 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100166 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100098396 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100099797 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100047 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100810 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100192 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100460 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100101190 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100834 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100099831 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100214 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100459 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100099650 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100098367 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100478 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100261 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100101200 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100020 的userid值为空
2025-07-01 09:00:13,486 - INFO - 店铺 100100465 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100101236 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100041 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100859 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100142 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100101254 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100101246 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100056 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100099794 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100189 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100099629 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100101292 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100266 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100101306 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100081 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100212 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100101309 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100098449 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100098268 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100101311 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100099636 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100838 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100101314 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100260 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100030 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100480 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100462 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100461 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100146 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100099553 的userid值为空
2025-07-01 09:00:13,502 - INFO - 店铺 100100172 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100365 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100040 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100195 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100099541 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100251 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100099847 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100216 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100055 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100098369 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100099555 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100098433 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100099992 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100366 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100076 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100144 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100099507 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100098253 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100169 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100098448 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100099806 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100097 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100194 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100099506 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100099587 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100847 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100215 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100832 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100099539 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100100200 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 100099522 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 ********* 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 ********* 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 ********* 的userid值为空
2025-07-01 09:00:14,080 - INFO - 店铺 ********* 的userid值为空
2025-07-01 09:00:14,080 - INFO - 获取宜搭店铺信息成功，共 1264 条记录
2025-07-01 09:00:14,096 - INFO - 成功获取宜搭店铺信息，共 1264 条记录
2025-07-01 09:00:14,096 - INFO - 正在获取用户信息并处理oa_account...
2025-07-01 09:00:14,096 - INFO - 获取用户信息成功，共 70 条记录
2025-07-01 09:00:14,392 - INFO - 需要查询的手机号数量: 40
2025-07-01 09:00:20,283 - INFO - 处理oa_account完成，缓存大小: 40
2025-07-01 09:00:20,283 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid', 'is_new_store']
2025-07-01 09:00:20,283 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id']
2025-07-01 09:00:20,283 - INFO - 开始对比数据库和宜搭数据...
2025-07-01 09:00:20,283 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid', 'is_new_store']
2025-07-01 09:00:20,283 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id']
2025-07-01 09:00:20,299 - INFO - 仅在数据库存在的记录数: 3
2025-07-01 09:00:20,299 - INFO - 需要插入的记录: ['*********', '*********', '*********']
2025-07-01 09:00:20,299 - INFO - 仅在宜搭存在的记录数: 0
2025-07-01 09:00:20,314 - INFO - 店铺 ********* 存在字段差异: ['status_diff']
2025-07-01 09:00:20,314 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:20,455 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:20,471 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:20,486 - INFO - 店铺 ********* 存在字段差异: ['status_diff']
2025-07-01 09:00:20,486 - INFO - 店铺 100099167 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,486 - INFO - 店铺 100099167 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,486 - INFO - 店铺 100099167 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,486 - INFO - 店铺 100099858 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,486 - INFO - 店铺 100099858 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,486 - INFO - 店铺 100099858 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,486 - INFO - 店铺 100099334 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,486 - INFO - 店铺 100099334 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,486 - INFO - 店铺 100099334 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,486 - INFO - 店铺 100099288 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,486 - INFO - 店铺 100099288 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,486 - INFO - 店铺 100099288 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,486 - INFO - 店铺 100100322 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,486 - INFO - 店铺 100100322 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,486 - INFO - 店铺 100100322 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,486 - INFO - 店铺 100099335 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,486 - INFO - 店铺 100099335 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,486 - INFO - 店铺 100099335 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,486 - INFO - 店铺 100099273 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,486 - INFO - 店铺 100099273 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,486 - INFO - 店铺 100099273 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,486 - INFO - 店铺 100100924 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,486 - INFO - 店铺 100100924 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,486 - INFO - 店铺 100100924 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099244 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100099244 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099244 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099232 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100099232 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099232 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100100362 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100100362 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,502 - INFO - 店铺 100100362 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100100323 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100100323 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,502 - INFO - 店铺 100100323 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099825 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100099825 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099825 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100101170 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100101170 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,502 - INFO - 店铺 100101170 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099241 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100099241 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099241 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100100356 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100100356 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,502 - INFO - 店铺 100100356 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099333 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100099333 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099333 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099300 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100099300 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099300 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099287 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100099287 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099287 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100101176 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100101176 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,502 - INFO - 店铺 100101176 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099242 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100099242 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099242 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099230 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100099230 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099230 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100100360 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100100360 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,502 - INFO - 店铺 100100360 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099209 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100099209 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099209 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100101148 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100101148 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,502 - INFO - 店铺 100101148 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100100219 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100100219 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,502 - INFO - 店铺 100100219 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,502 - INFO - 店铺 100099326 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,502 - INFO - 店铺 100099326 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099326 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100101149 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100101149 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,517 - INFO - 店铺 100101149 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099240 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100099240 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099240 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100811 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100100811 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100811 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099229 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100099229 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099229 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100316 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100100316 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100316 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099181 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100099181 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099181 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099140 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100099140 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099140 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100014 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100100014 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100014 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099344 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100099344 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099344 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099206 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100099206 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099206 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100263 存在字段差异: ['userid_diff', 'status_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100100263 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100263 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099146 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100099146 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099146 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100015 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100100015 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100015 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100101130 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100101130 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,517 - INFO - 店铺 100101130 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099257 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100099257 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099257 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100101108 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100101108 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,517 - INFO - 店铺 100101108 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099239 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100099239 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099239 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099227 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100099227 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,517 - INFO - 店铺 100099227 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100354 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100100354 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100354 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100312 存在字段差异: ['userid_diff', 'status_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100100312 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100312 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,517 - INFO - 店铺 100100248 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,517 - INFO - 店铺 100100248 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100248 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100012 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100100012 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100012 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099340 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100099340 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099340 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100666 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100100666 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100666 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099228 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100099228 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099228 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100355 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100100355 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100355 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100013 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100100013 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100013 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099341 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100099341 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099341 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100953 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100100953 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100953 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099220 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100099220 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099220 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100297 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100100297 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100297 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099174 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100099174 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099174 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100239 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100100239 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100100239 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099997 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100099997 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099997 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099238 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,533 - INFO - 店铺 100099238 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,533 - INFO - 店铺 100099238 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100340 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100100340 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100340 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100298 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100100298 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100298 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100099175 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100099175 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,549 - INFO - 店铺 100099175 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100247 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100100247 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100247 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100003 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100100003 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100003 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100927 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100100927 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100927 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100476 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100100476 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100476 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100099248 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100099248 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,549 - INFO - 店铺 100099248 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100099235 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100099235 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,549 - INFO - 店铺 100099235 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100372 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100100372 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100372 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100329 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100100329 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100329 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100290 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100100290 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100290 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100237 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100100237 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100237 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100099316 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100099316 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,549 - INFO - 店铺 100099316 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100101128 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100101128 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,549 - INFO - 店铺 100101128 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100099279 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100099279 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,549 - INFO - 店铺 100099279 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100484 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100100484 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100484 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100949 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100100949 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100949 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100099236 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100099236 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100099236 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100925 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100100925 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,549 - INFO - 店铺 100100925 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,549 - INFO - 店铺 100099268 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,549 - INFO - 店铺 100099268 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099268 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099246 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,564 - INFO - 店铺 100099246 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099246 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099233 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,564 - INFO - 店铺 100099233 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099233 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099214 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,564 - INFO - 店铺 100099214 userid差异 - 数据库: set(), 宜搭: {'191456278313b6c12b11b654db593290'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099214 - 宜搭有userid但数据库为空
2025-07-01 09:00:20,564 - INFO - 店铺 100099339 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,564 - INFO - 店铺 100099339 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099339 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099312 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,564 - INFO - 店铺 100099312 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099312 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100100467 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,564 - INFO - 店铺 100100467 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100100467 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099247 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,564 - INFO - 店铺 100099247 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099247 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099234 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,564 - INFO - 店铺 100099234 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100099234 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100101216 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,564 - INFO - 店铺 100101216 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:20,564 - INFO - 店铺 100101216 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100101262 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,564 - INFO - 店铺 100101262 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100101262 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100101277 存在字段差异: ['userid_diff']
2025-07-01 09:00:20,564 - INFO - 店铺 100101277 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:20,564 - INFO - 店铺 100101277 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:20,564 - INFO - 店铺 100101288 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:20,564 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:20,596 - INFO - 店铺 100100352 存在字段差异: ['fz_store_code_diff']
2025-07-01 09:00:20,627 - INFO - 店铺 100101273 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:20,627 - INFO - 店铺 100101279 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:20,627 - INFO - 店铺 100101281 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:20,658 - INFO - 店铺 100101280 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:20,658 - INFO - 店铺 100101286 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:20,658 - INFO - 店铺 100101298 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:20,658 - INFO - 数据对比完成：
2025-07-01 09:00:20,658 - INFO - - 需要插入的记录数: 3
2025-07-01 09:00:20,658 - INFO - - 需要更新状态为禁用的记录数: 0
2025-07-01 09:00:20,658 - INFO - - 需要更新的记录数: 108
2025-07-01 09:00:20,658 - INFO - - 店铺名称变更数: 0
2025-07-01 09:00:20,658 - INFO - 生成差异报告...
2025-07-01 09:00:21,080 - INFO - 差异报告已保存到文件: data/sync_store/store_info_diff_report_20250701.xlsx
2025-07-01 09:00:21,080 - INFO - 开始更新宜搭表单...
2025-07-01 09:00:21,080 - INFO - 开始更新宜搭表单数据...
2025-07-01 09:00:21,080 - INFO - 数据库记录数: 1267
2025-07-01 09:00:21,080 - INFO - 宜搭记录数: 1264
2025-07-01 09:00:21,080 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid', 'is_new_store']
2025-07-01 09:00:21,080 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id']
2025-07-01 09:00:21,080 - INFO - 仅在数据库存在的记录数: 3
2025-07-01 09:00:21,080 - INFO - 需要插入的记录: ['*********', '*********', '*********']
2025-07-01 09:00:21,080 - INFO - 仅在宜搭存在的记录数: 0
2025-07-01 09:00:21,096 - INFO - 店铺 ********* 存在字段差异: ['status_diff']
2025-07-01 09:00:21,096 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:21,189 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:21,221 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:21,221 - INFO - 店铺 ********* 存在字段差异: ['status_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100099167 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100099167 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099167 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099858 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100099858 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099858 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099334 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100099334 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099334 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099288 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100099288 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099288 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100100322 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100100322 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,236 - INFO - 店铺 100100322 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099335 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100099335 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099335 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099273 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100099273 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099273 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100100924 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100100924 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,236 - INFO - 店铺 100100924 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099244 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100099244 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099244 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099232 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100099232 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099232 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100100362 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100100362 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,236 - INFO - 店铺 100100362 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100100323 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100100323 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,236 - INFO - 店铺 100100323 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099825 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100099825 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099825 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100101170 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100101170 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,236 - INFO - 店铺 100101170 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099241 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100099241 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,236 - INFO - 店铺 100099241 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,236 - INFO - 店铺 100100356 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,236 - INFO - 店铺 100100356 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,236 - INFO - 店铺 100100356 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099333 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100099333 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099333 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099300 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100099300 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099300 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099287 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100099287 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099287 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100101176 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100101176 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,252 - INFO - 店铺 100101176 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099242 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100099242 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099242 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099230 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100099230 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099230 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100100360 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100100360 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,252 - INFO - 店铺 100100360 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099209 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100099209 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099209 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100101148 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100101148 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,252 - INFO - 店铺 100101148 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100100219 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100100219 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,252 - INFO - 店铺 100100219 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099326 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100099326 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099326 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100101149 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100101149 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,252 - INFO - 店铺 100101149 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099240 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100099240 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099240 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100100811 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100100811 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100100811 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099229 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100099229 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099229 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100100316 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100100316 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,252 - INFO - 店铺 100100316 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099181 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100099181 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099181 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099140 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100099140 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099140 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100100014 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100100014 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,252 - INFO - 店铺 100100014 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099344 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,252 - INFO - 店铺 100099344 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,252 - INFO - 店铺 100099344 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099206 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100099206 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099206 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100263 存在字段差异: ['userid_diff', 'status_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100100263 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100263 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099146 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100099146 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099146 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100015 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100100015 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100015 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100101130 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100101130 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,267 - INFO - 店铺 100101130 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099257 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100099257 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099257 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100101108 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100101108 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,267 - INFO - 店铺 100101108 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099239 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100099239 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099239 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099227 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100099227 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099227 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100354 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100100354 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100354 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100312 存在字段差异: ['userid_diff', 'status_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100100312 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100312 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100248 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100100248 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100248 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100012 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100100012 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100012 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099340 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100099340 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099340 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100666 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100100666 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100666 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099228 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100099228 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099228 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100355 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100100355 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100355 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100013 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100100013 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100013 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099341 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100099341 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,267 - INFO - 店铺 100099341 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100953 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,267 - INFO - 店铺 100100953 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,267 - INFO - 店铺 100100953 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099220 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100099220 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099220 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100297 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100100297 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100297 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099174 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100099174 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099174 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100239 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100100239 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100239 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099997 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100099997 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099997 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099238 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100099238 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099238 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100340 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100100340 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100340 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100298 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100100298 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100298 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099175 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100099175 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099175 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100247 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100100247 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100247 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100003 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100100003 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100003 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100927 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100100927 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100927 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100476 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100100476 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100476 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099248 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100099248 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099248 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099235 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100099235 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099235 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100372 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100100372 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100372 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100329 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100100329 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100329 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100290 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100100290 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100290 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100237 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100100237 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100237 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099316 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100099316 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099316 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100101128 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100101128 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,283 - INFO - 店铺 100101128 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099279 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100099279 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,283 - INFO - 店铺 100099279 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100484 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,283 - INFO - 店铺 100100484 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,283 - INFO - 店铺 100100484 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100100949 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100100949 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100100949 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099236 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100099236 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099236 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100100925 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100100925 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,299 - INFO - 店铺 100100925 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099268 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100099268 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099268 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099246 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100099246 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099246 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099233 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100099233 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099233 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099214 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100099214 userid差异 - 数据库: set(), 宜搭: {'191456278313b6c12b11b654db593290'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099214 - 宜搭有userid但数据库为空
2025-07-01 09:00:21,299 - INFO - 店铺 100099339 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100099339 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099339 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099312 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100099312 userid差异 - 数据库: {'18ee095369c74d07bd5e9364f0bbfe21'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '18ee095369c74d07bd5e9364f0bbfe21'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099312 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100100467 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100100467 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100100467 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099247 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100099247 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099247 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099234 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100099234 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100099234 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100101216 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100101216 userid差异 - 数据库: {'1971b2e69fe08b1dcd93ff64cc28d918'}, 宜搭: {'17b9f98a4d9b9ad2bb3a745401180c78', '1971b2e69fe08b1dcd93ff64cc28d918'}
2025-07-01 09:00:21,299 - INFO - 店铺 100101216 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100101262 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100101262 userid差异 - 数据库: {'1911111840fdac3db9f89204e6eb4322'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100101262 - 仅在宜搭存在的userid: {'17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100101277 存在字段差异: ['userid_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 100101277 userid差异 - 数据库: {'174c4fe5c5853e3c68a4e0347338a136'}, 宜搭: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78', '174c4fe5c5853e3c68a4e0347338a136'}
2025-07-01 09:00:21,299 - INFO - 店铺 100101277 - 仅在宜搭存在的userid: {'191456278313b6c12b11b654db593290', '17b9f98a4d9b9ad2bb3a745401180c78'}
2025-07-01 09:00:21,299 - INFO - 店铺 100101288 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:21,299 - INFO - 店铺 ********* 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:21,330 - INFO - 店铺 100100352 存在字段差异: ['fz_store_code_diff']
2025-07-01 09:00:21,361 - INFO - 店铺 100101273 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:21,361 - INFO - 店铺 100101279 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:21,361 - INFO - 店铺 100101281 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:21,392 - INFO - 店铺 100101280 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:21,392 - INFO - 店铺 100101286 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:21,392 - INFO - 店铺 100101298 存在字段差异: ['is_new_store_diff']
2025-07-01 09:00:21,392 - INFO - 数据对比完成：
2025-07-01 09:00:21,392 - INFO - - 需要插入的记录数: 3
2025-07-01 09:00:21,392 - INFO - - 需要更新状态为禁用的记录数: 0
2025-07-01 09:00:21,392 - INFO - - 需要更新的记录数: 108
2025-07-01 09:00:21,392 - INFO - - 店铺名称变更数: 0
2025-07-01 09:00:21,392 - INFO - 开始处理需要插入的记录，共 3 条
2025-07-01 09:00:21,392 - INFO - 正在处理第 1 条插入记录 - store_code: *********
2025-07-01 09:00:21,392 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "潮弹社", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0074", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:21,392 - INFO - 正在处理第 2 条插入记录 - store_code: *********
2025-07-01 09:00:21,392 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "JOYMARK玩偶遇到爱", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0073", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:21,392 - INFO - 正在处理第 3 条插入记录 - store_code: *********
2025-07-01 09:00:21,408 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "Cupping Zakka", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0072", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:21,408 - INFO - 处理剩余 3 条插入记录
2025-07-01 09:00:21,564 - INFO - 批量创建表单数据成功: 3 条记录
2025-07-01 09:00:21,564 - INFO - 批量插入成功，form_instance_ids: ['FINST-W4G66DA1OPRWWVJLEASLBC1RPUO23ULNMTJCM6', 'FINST-W4G66DA1OPRWWVJLEASLBC1RPUO23ULNMTJCM7', 'FINST-W4G66DA1OPRWWVJLEASLBC1RPUO23ULNMTJCM8']
2025-07-01 09:00:21,564 - INFO - 发送新店铺插入邮件通知
2025-07-01 09:00:22,549 - INFO - 邮件发送成功
2025-07-01 09:00:22,549 - INFO - 新店铺插入邮件发送成功，共 3 条记录
2025-07-01 09:00:22,549 - INFO - 开始处理需要更新的记录，共 108 条
2025-07-01 09:00:22,549 - INFO - 正在处理第 1 条更新记录 - store_code: *********
2025-07-01 09:00:22,549 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "半岛豪苑", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10164L25CO0024", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,564 - INFO - 正在处理第 2 条更新记录 - store_code: *********
2025-07-01 09:00:22,564 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "Lenovo", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L21CO0110", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,564 - INFO - 正在处理第 3 条更新记录 - store_code: *********
2025-07-01 09:00:22,564 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "必胜客", "employeeField_m8e8g3lw": ["195cbfaa191990374b814d747ed92fe3", "178911c91380e3f9961bcc84847a615f", "18b0e1e4b78fd2c55dc90c74e3b9f3cc", "16340e95afb25e94fcd338840d78edb8", "189f858c407c7b3d480039a4090b4c13"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0011", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,564 - INFO - 正在处理第 4 条更新记录 - store_code: *********
2025-07-01 09:00:22,564 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州ICC环贸天地", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "星聚会", "employeeField_m8e8g3lw": ["17f90b6e5dd13dcb6e88a1f4ff1951b5", "1866c87b7d8cb136f51df5d45fcaee10", "16dd75cc46f7e9497f12b134a59a1015", "16d2416d0e08855198ac3134905914f3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "128042", "textField_mb7rs39i": "任春宇", "textField_mbc1lbzm": "10701L25CO0001", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,564 - INFO - 正在处理第 5 条更新记录 - store_code: *********
2025-07-01 09:00:22,564 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州VT101维多利广场", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "西贝莜面村", "employeeField_m8e8g3lw": ["16d2416d58bbecd312653de411ab0b7b", "181d652cd41d8f441bfe3bd4596a79e8", "16d2416db03efb5beff93cb4f59aab6e", "18f99e6481178d51b06ff0b4410ad323", "18ea2e5066661f3b4e114024bd290c2d"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101909", "textField_mb7rs39i": "深圳西贝喜悦餐饮有限公司广州维多利广场分公司", "textField_mbc1lbzm": "10311L24CO0006", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,564 - INFO - 正在处理第 6 条更新记录 - store_code: 100099167
2025-07-01 09:00:22,564 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099167", "textField_m8e8g3lu": "阿迪达斯", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0001", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,564 - INFO - 正在处理第 7 条更新记录 - store_code: 100099858
2025-07-01 09:00:22,564 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099858", "textField_m8e8g3lu": "华唐书法", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108983", "textField_mb7rs39i": "佛山华唐书法艺术有限公司", "textField_mbc1lbzm": "10295L23CO0023", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,564 - INFO - 正在处理第 8 条更新记录 - store_code: 100099334
2025-07-01 09:00:22,564 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099334", "textField_m8e8g3lu": "创兴银行", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103924", "textField_mb7rs39i": "创兴银行有限公司佛山支行", "textField_mbc1lbzm": "10295L23CO0046", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,564 - INFO - 正在处理第 9 条更新记录 - store_code: 100099288
2025-07-01 09:00:22,564 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099288", "textField_m8e8g3lu": "木棉花", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110591", "textField_mb7rs39i": "佛山南海木棉花普通专科门诊部有限公司", "textField_mbc1lbzm": "10295L23CO0028", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,564 - INFO - 正在处理第 10 条更新记录 - store_code: 100100322
2025-07-01 09:00:22,564 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100322", "textField_m8e8g3lu": "汉堡王", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110666", "textField_mb7rs39i": "汉堡王食品（深圳）有限公司", "textField_mbc1lbzm": "10295L23CO0058", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,564 - INFO - 正在处理第 11 条更新记录 - store_code: 100099335
2025-07-01 09:00:22,564 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099335", "textField_m8e8g3lu": "浓心爷爷烘焙培训", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103948", "textField_mb7rs39i": "佛山市南海区浓泰盈面包店", "textField_mbc1lbzm": "10295L23CO0019", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,564 - INFO - 正在处理第 12 条更新记录 - store_code: 100099273
2025-07-01 09:00:22,564 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099273", "textField_m8e8g3lu": "金麟鳍寿司", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103955", "textField_mb7rs39i": "佛山市金鲔鳍餐饮服务有限公司", "textField_mbc1lbzm": "10295L23CO0029", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,580 - INFO - 正在处理第 13 条更新记录 - store_code: 100100924
2025-07-01 09:00:22,580 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100924", "textField_m8e8g3lu": "鹤·健身工作室", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0029", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,580 - INFO - 正在处理第 14 条更新记录 - store_code: 100099244
2025-07-01 09:00:22,580 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099244", "textField_m8e8g3lu": "星巴克", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103512", "textField_mb7rs39i": "广东星巴克咖啡有限公司", "textField_mbc1lbzm": "10295L20CO0018", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,580 - INFO - 正在处理第 15 条更新记录 - store_code: 100099232
2025-07-01 09:00:22,580 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099232", "textField_m8e8g3lu": "博览书店", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103998", "textField_mb7rs39i": "广东省博览图书有限公司", "textField_mbc1lbzm": "10295L23CO0010", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,580 - INFO - 正在处理第 16 条更新记录 - store_code: 100100362
2025-07-01 09:00:22,580 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100362", "textField_m8e8g3lu": "拉茶王子", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111097", "textField_mb7rs39i": "佛山市拉茶王子餐饮有限公司", "textField_mbc1lbzm": "10295L24CO0005", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,580 - INFO - 正在处理第 17 条更新记录 - store_code: 100100323
2025-07-01 09:00:22,580 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100323", "textField_m8e8g3lu": "奈雪の茶", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104055", "textField_mb7rs39i": "广州市奈雪餐饮管理有限公司", "textField_mbc1lbzm": "10295L23CO0057", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,580 - INFO - 正在处理第 18 条更新记录 - store_code: 100099825
2025-07-01 09:00:22,580 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099825", "textField_m8e8g3lu": "童星荟", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104000", "textField_mb7rs39i": "赵宇昊", "textField_mbc1lbzm": "10295L23CO0077", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,580 - INFO - 正在处理第 19 条更新记录 - store_code: 100101170
2025-07-01 09:00:22,580 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101170", "textField_m8e8g3lu": "陈家生煎", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0113", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,580 - INFO - 正在处理第 20 条更新记录 - store_code: 100099241
2025-07-01 09:00:22,580 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099241", "textField_m8e8g3lu": "一号教室", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104065", "textField_mb7rs39i": "陈胜广", "textField_mbc1lbzm": "10295L23CO0066", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,580 - INFO - 正在处理第 21 条更新记录 - store_code: 100100356
2025-07-01 09:00:22,580 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100356", "textField_m8e8g3lu": "NUMERO TEA", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L23CO0070", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,580 - INFO - 正在处理第 22 条更新记录 - store_code: 100099333
2025-07-01 09:00:22,580 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099333", "textField_m8e8g3lu": "挺惠买", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0111", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,580 - INFO - 正在处理第 23 条更新记录 - store_code: 100099300
2025-07-01 09:00:22,580 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099300", "textField_m8e8g3lu": "学乐英语", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103993", "textField_mb7rs39i": "佛山市利和文化传播有限公司", "textField_mbc1lbzm": "10295L25CO0019", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,580 - INFO - 正在处理第 24 条更新记录 - store_code: 100099287
2025-07-01 09:00:22,596 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099287", "textField_m8e8g3lu": "一面馆", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0114", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,596 - INFO - 正在处理第 25 条更新记录 - store_code: 100101176
2025-07-01 09:00:22,596 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101176", "textField_m8e8g3lu": "小米", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0115", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,596 - INFO - 正在处理第 26 条更新记录 - store_code: 100099242
2025-07-01 09:00:22,596 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099242", "textField_m8e8g3lu": "一诺教育", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109073", "textField_mb7rs39i": "广东省一诺教育集团有限公司", "textField_mbc1lbzm": "10295L22CO0019", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,596 - INFO - 正在处理第 27 条更新记录 - store_code: 100099230
2025-07-01 09:00:22,596 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099230", "textField_m8e8g3lu": "慧鱼创新学院", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103967", "textField_mb7rs39i": "佛山市瓦力科技有限公司", "textField_mbc1lbzm": "10295L22CO0083", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,596 - INFO - 正在处理第 28 条更新记录 - store_code: 100100360
2025-07-01 09:00:22,596 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100360", "textField_m8e8g3lu": "优剪", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110904", "textField_mb7rs39i": "何志勇", "textField_mbc1lbzm": "10295L25CO0008", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,596 - INFO - 正在处理第 29 条更新记录 - store_code: 100099209
2025-07-01 09:00:22,596 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099209", "textField_m8e8g3lu": "华为", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110164", "textField_mb7rs39i": "佛山市顺德区龙沃通讯器材有限公司", "textField_mbc1lbzm": "10295L23CO0011", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,596 - INFO - 正在处理第 30 条更新记录 - store_code: 100101148
2025-07-01 09:00:22,596 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101148", "textField_m8e8g3lu": "郑汉叉烧", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0109", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,596 - INFO - 正在处理第 31 条更新记录 - store_code: 100100219
2025-07-01 09:00:22,596 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100219", "textField_m8e8g3lu": "点蹄", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110574", "textField_mb7rs39i": "周剑锋", "textField_mbc1lbzm": "10295L23CO0025", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,596 - INFO - 正在处理第 32 条更新记录 - store_code: 100099326
2025-07-01 09:00:22,596 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099326", "textField_m8e8g3lu": "伊丽汇", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103929", "textField_mb7rs39i": "佛山市南海区伊齐美美容店", "textField_mbc1lbzm": "10295L20CO0122", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,596 - INFO - 正在处理第 33 条更新记录 - store_code: 100101149
2025-07-01 09:00:22,596 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101149", "textField_m8e8g3lu": "泓洋西服高级定制", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0110", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,596 - INFO - 正在处理第 34 条更新记录 - store_code: 100099240
2025-07-01 09:00:22,596 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099240", "textField_m8e8g3lu": "小画狮美术教育", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104001", "textField_mb7rs39i": "周晶", "textField_mbc1lbzm": "10295L23CO0012", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,596 - INFO - 正在处理第 35 条更新记录 - store_code: 100100811
2025-07-01 09:00:22,611 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100811", "textField_m8e8g3lu": "西关楼", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111331", "textField_mb7rs39i": "佛山市南海区盛元餐饮文化管理有限公司", "textField_mbc1lbzm": "10295L24CO0025", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,611 - INFO - 正在处理第 36 条更新记录 - store_code: 100099229
2025-07-01 09:00:22,611 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099229", "textField_m8e8g3lu": "喜事汇", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103914", "textField_mb7rs39i": "佛山市喜事汇餐饮管理有限公司", "textField_mbc1lbzm": "10295L20CO0094", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,611 - INFO - 正在处理第 37 条更新记录 - store_code: 100100316
2025-07-01 09:00:22,611 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100316", "textField_m8e8g3lu": "名品乐淘", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110828", "textField_mb7rs39i": "吴志辉", "textField_mbc1lbzm": "10295L25CO0015", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,611 - INFO - 正在处理第 38 条更新记录 - store_code: 100099181
2025-07-01 09:00:22,611 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099181", "textField_m8e8g3lu": "好佰年口腔", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L21CO0045", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,611 - INFO - 正在处理第 39 条更新记录 - store_code: 100099140
2025-07-01 09:00:22,611 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099140", "textField_m8e8g3lu": "卓越教育", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110580", "textField_mb7rs39i": "佛山市南海区卓明教育培训中心", "textField_mbc1lbzm": "10295L23CO0072", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,611 - INFO - 正在处理第 40 条更新记录 - store_code: 100100014
2025-07-01 09:00:22,611 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100014", "textField_m8e8g3lu": "多经-回力", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "110436", "textField_mb7rs39i": "李红", "textField_mbc1lbzm": "10295L23CO0018", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,611 - INFO - 正在处理第 41 条更新记录 - store_code: 100099344
2025-07-01 09:00:22,611 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099344", "textField_m8e8g3lu": "惠儿堂", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111023", "textField_mb7rs39i": "惠儿堂（佛山南海）健康科技有限公司", "textField_mbc1lbzm": "10295L24CO0011", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,611 - INFO - 正在处理第 42 条更新记录 - store_code: 100099206
2025-07-01 09:00:22,611 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099206", "textField_m8e8g3lu": "肯德基", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103397", "textField_mb7rs39i": "百胜餐饮（广东）有限公司", "textField_mbc1lbzm": "10295L20CO0017", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,611 - INFO - 正在处理第 43 条更新记录 - store_code: 100100263
2025-07-01 09:00:22,611 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100263", "textField_m8e8g3lu": "探鱼", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110708", "textField_mb7rs39i": "金德艳", "textField_mbc1lbzm": "10295L23CO0041", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,611 - INFO - 正在处理第 44 条更新记录 - store_code: 100099146
2025-07-01 09:00:22,611 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099146", "textField_m8e8g3lu": "天音科技", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0118", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,611 - INFO - 正在处理第 45 条更新记录 - store_code: 100100015
2025-07-01 09:00:22,611 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100015", "textField_m8e8g3lu": "星豪桌球", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110323", "textField_mb7rs39i": "何政洋", "textField_mbc1lbzm": "10295L23CO0022", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,627 - INFO - 正在处理第 46 条更新记录 - store_code: 100101130
2025-07-01 09:00:22,627 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101130", "textField_m8e8g3lu": "大可以", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0121", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,627 - INFO - 正在处理第 47 条更新记录 - store_code: 100099257
2025-07-01 09:00:22,627 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099257", "textField_m8e8g3lu": "媄研社", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L25CO0003", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,627 - INFO - 正在处理第 48 条更新记录 - store_code: 100101108
2025-07-01 09:00:22,627 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101108", "textField_m8e8g3lu": "OPPO", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0042", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,627 - INFO - 正在处理第 49 条更新记录 - store_code: 100099239
2025-07-01 09:00:22,627 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099239", "textField_m8e8g3lu": "凯茵斯咖啡•轻西厨", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103979", "textField_mb7rs39i": "吕健樑", "textField_mbc1lbzm": "10295L21CO0051", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,627 - INFO - 正在处理第 50 条更新记录 - store_code: 100099227
2025-07-01 09:00:22,627 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099227", "textField_m8e8g3lu": "VIVA声学教室", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103968", "textField_mb7rs39i": "张勰", "textField_mbc1lbzm": "10295L23CO0016", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:22,627 - INFO - 达到批量处理大小，开始批量更新 50 条记录
2025-07-01 09:00:23,158 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:23,533 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:23,986 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:24,533 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:24,971 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:25,377 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:25,830 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:26,283 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:26,752 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:27,189 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:27,580 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:28,096 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:28,549 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:29,142 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:29,564 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:30,002 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:30,502 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:31,017 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:31,502 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:32,002 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:32,424 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:32,877 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:33,314 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:33,845 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:34,314 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:34,705 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:35,189 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:35,642 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:36,080 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:36,502 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:36,955 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:37,392 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:37,861 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:38,252 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:38,705 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:39,158 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:39,611 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:40,064 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:40,486 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:40,892 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:41,345 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:41,799 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:42,189 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:42,658 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:43,127 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:43,595 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:44,033 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:44,486 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:44,939 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:45,392 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:45,392 - INFO - 批量更新成功，form_instance_ids: ['FINST-IQG66AD1U4OWZZJFCMCFTA907VIL2HJGBJFCMF6', 'FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMDB', 'FINST-LLF66J719JUVO23HDEWKOA369T6V3JUIADCBMZJ', 'FINST-1PF66KA1V5SVCUW5F832R5V6WFYJ211JADCBMI21', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMY2', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMB3', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMC3', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMD3', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573E7JADCBMF3', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2GDJADCBMS6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2GDJADCBMU6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMY6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBM07', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBM27', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBM37', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBM47', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBM67', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMA7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMC7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMH7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMI7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMM7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMN7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMP7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMQ7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMT7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMU7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMV7', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMW7', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMQ3', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMW3', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMY3', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBM14', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBM34', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBM54', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBM64', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBM84', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBM94', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMB4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMC4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMD4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMG4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMJ4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMK4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBML4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMO4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMP4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMR4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMV4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMW4']
2025-07-01 09:00:45,408 - INFO - 正在处理第 1 条更新记录 - store_code: 100100354
2025-07-01 09:00:45,408 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100354", "textField_m8e8g3lu": "今鸿五洞牛肉火锅", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110820", "textField_mb7rs39i": "费吉祥", "textField_mbc1lbzm": "10295L23CO0069", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,408 - INFO - 正在处理第 2 条更新记录 - store_code: 100100312
2025-07-01 09:00:45,408 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100312", "textField_m8e8g3lu": "卡滋贝诺烘焙", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110839", "textField_mb7rs39i": "陈清风", "textField_mbc1lbzm": "10295L23CO0052", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,408 - INFO - 正在处理第 3 条更新记录 - store_code: 100100248
2025-07-01 09:00:45,408 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100248", "textField_m8e8g3lu": "普莱斯眼镜", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110606", "textField_mb7rs39i": "查岚", "textField_mbc1lbzm": "10295L25CO0014", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,408 - INFO - 正在处理第 4 条更新记录 - store_code: 100100012
2025-07-01 09:00:45,408 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100012", "textField_m8e8g3lu": "霸王茶姬", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110476", "textField_mb7rs39i": "杨群", "textField_mbc1lbzm": "10295L23CO0020", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,408 - INFO - 正在处理第 5 条更新记录 - store_code: 100099340
2025-07-01 09:00:45,408 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099340", "textField_m8e8g3lu": "暨博卓越口腔", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108340", "textField_mb7rs39i": "佛山南海暨博卓越口腔门诊部有限公司", "textField_mbc1lbzm": "10295L22CO0059", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,408 - INFO - 正在处理第 6 条更新记录 - store_code: 100100666
2025-07-01 09:00:45,408 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100666", "textField_m8e8g3lu": "骆驼", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0035", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,408 - INFO - 正在处理第 7 条更新记录 - store_code: 100099228
2025-07-01 09:00:45,408 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099228", "textField_m8e8g3lu": "新精武体育培训", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103930", "textField_mb7rs39i": "佛山新精武极一体育咨询服务有限公司", "textField_mbc1lbzm": "10295L20CO0063", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,408 - INFO - 正在处理第 8 条更新记录 - store_code: 100100355
2025-07-01 09:00:45,408 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100355", "textField_m8e8g3lu": "春天舞蹈", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0030", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,408 - INFO - 正在处理第 9 条更新记录 - store_code: 100100013
2025-07-01 09:00:45,408 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100013", "textField_m8e8g3lu": "辰事记乐园", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110562", "textField_mb7rs39i": "佛山市南海区小辰大事文化娱乐有限公司", "textField_mbc1lbzm": "10295L23CO0027", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,408 - INFO - 正在处理第 10 条更新记录 - store_code: 100099341
2025-07-01 09:00:45,408 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099341", "textField_m8e8g3lu": "片仔癀", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109755", "textField_mb7rs39i": "黄鸿铭", "textField_mbc1lbzm": "10295L23CO0009", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,408 - INFO - 正在处理第 11 条更新记录 - store_code: 100100953
2025-07-01 09:00:45,424 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100953", "textField_m8e8g3lu": "华丰超市", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L25CO0005", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,424 - INFO - 正在处理第 12 条更新记录 - store_code: 100099220
2025-07-01 09:00:45,424 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099220", "textField_m8e8g3lu": "多经-回收宝", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0104", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,424 - INFO - 正在处理第 13 条更新记录 - store_code: 100100297
2025-07-01 09:00:45,424 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100297", "textField_m8e8g3lu": "魅KTV", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111383", "textField_mb7rs39i": "佛山朵咖文化娱乐有限公司", "textField_mbc1lbzm": "10295L24CO0122", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,424 - INFO - 正在处理第 14 条更新记录 - store_code: 100099174
2025-07-01 09:00:45,424 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099174", "textField_m8e8g3lu": "多经-coco都可茶饮", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0108", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,424 - INFO - 正在处理第 15 条更新记录 - store_code: 100100239
2025-07-01 09:00:45,424 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100239", "textField_m8e8g3lu": "卜吉熊", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110612", "textField_mb7rs39i": "广州市新珑源供应链管理有限公司", "textField_mbc1lbzm": "10295L23CO0033", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,424 - INFO - 正在处理第 16 条更新记录 - store_code: 100099997
2025-07-01 09:00:45,424 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099997", "textField_m8e8g3lu": "黑马艺术", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0116", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,424 - INFO - 正在处理第 17 条更新记录 - store_code: 100099238
2025-07-01 09:00:45,424 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099238", "textField_m8e8g3lu": "数泰一口清珠心算", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109461", "textField_mb7rs39i": "刘红英", "textField_mbc1lbzm": "10295L23CO0045", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,424 - INFO - 正在处理第 18 条更新记录 - store_code: 100100340
2025-07-01 09:00:45,424 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100340", "textField_m8e8g3lu": "名创优品", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110395", "textField_mb7rs39i": "吴永生", "textField_mbc1lbzm": "10295L23CO0064", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,424 - INFO - 正在处理第 19 条更新记录 - store_code: 100100298
2025-07-01 09:00:45,424 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100298", "textField_m8e8g3lu": "苹果数码", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110969", "textField_mb7rs39i": "佛山尚派正品科技有限公司", "textField_mbc1lbzm": "10295L23CO0076", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,424 - INFO - 正在处理第 20 条更新记录 - store_code: 100099175
2025-07-01 09:00:45,424 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099175", "textField_m8e8g3lu": "瑞幸咖啡", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110482", "textField_mb7rs39i": "王刚", "textField_mbc1lbzm": "10295L23CO0021", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,424 - INFO - 正在处理第 21 条更新记录 - store_code: 100100247
2025-07-01 09:00:45,424 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100247", "textField_m8e8g3lu": "杯子红西餐", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111118", "textField_mb7rs39i": "佛山市南海区怡立餐饮店（个体工商户）", "textField_mbc1lbzm": "10295L24CO0006", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,424 - INFO - 正在处理第 22 条更新记录 - store_code: 100100003
2025-07-01 09:00:45,439 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100003", "textField_m8e8g3lu": "蒙自源过桥米线", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110447", "textField_mb7rs39i": "段添", "textField_mbc1lbzm": "10295L25CO0018", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,439 - INFO - 正在处理第 23 条更新记录 - store_code: 100100927
2025-07-01 09:00:45,439 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100927", "textField_m8e8g3lu": "龍入疆域面面俱到", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0028", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,439 - INFO - 正在处理第 24 条更新记录 - store_code: 100100476
2025-07-01 09:00:45,439 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100476", "textField_m8e8g3lu": "广东尚尧律师事务所", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110991", "textField_mb7rs39i": "佛山市裕和悦汇商贸有限公司", "textField_mbc1lbzm": "10295L24CO0010", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,439 - INFO - 正在处理第 25 条更新记录 - store_code: 100099248
2025-07-01 09:00:45,439 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099248", "textField_m8e8g3lu": "婆婆那辈", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108959", "textField_mb7rs39i": "佛山市南海区婆婆那辈餐饮店", "textField_mbc1lbzm": "10295L21CO0138", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,439 - INFO - 正在处理第 26 条更新记录 - store_code: 100099235
2025-07-01 09:00:45,439 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099235", "textField_m8e8g3lu": "飞鹰轮滑", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "106613", "textField_mb7rs39i": "张益康", "textField_mbc1lbzm": "10295L23CO0101", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,439 - INFO - 正在处理第 27 条更新记录 - store_code: 100100372
2025-07-01 09:00:45,439 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100372", "textField_m8e8g3lu": "恿", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110932", "textField_mb7rs39i": "陈建平", "textField_mbc1lbzm": "10295L23CO0082", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,439 - INFO - 正在处理第 28 条更新记录 - store_code: 100100329
2025-07-01 09:00:45,439 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100329", "textField_m8e8g3lu": "沸吃火锅", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111228", "textField_mb7rs39i": "佛山沸嘻餐饮管理有限公司", "textField_mbc1lbzm": "10295L24CO0018", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,439 - INFO - 正在处理第 29 条更新记录 - store_code: 100100290
2025-07-01 09:00:45,439 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100290", "textField_m8e8g3lu": "霸碗盖码饭", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110690", "textField_mb7rs39i": "胡进飞", "textField_mbc1lbzm": "10295L23CO0047", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,439 - INFO - 正在处理第 30 条更新记录 - store_code: 100100237
2025-07-01 09:00:45,439 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100237", "textField_m8e8g3lu": "花悦厨", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110529", "textField_mb7rs39i": "蔡振华", "textField_mbc1lbzm": "10295L23CO0031", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,439 - INFO - 正在处理第 31 条更新记录 - store_code: 100099316
2025-07-01 09:00:45,439 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099316", "textField_m8e8g3lu": "蒲公英青少年宫", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103917", "textField_mb7rs39i": "广东蒲公英青少年宫教育发展有限公司", "textField_mbc1lbzm": "10295L20CO0014", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,439 - INFO - 正在处理第 32 条更新记录 - store_code: 100101128
2025-07-01 09:00:45,439 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101128", "textField_m8e8g3lu": "艾力斯特", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0106", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,439 - INFO - 正在处理第 33 条更新记录 - store_code: 100099279
2025-07-01 09:00:45,439 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099279", "textField_m8e8g3lu": "多经-华蒙星", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "103946", "textField_mb7rs39i": "佛山市南海区源星体育有限公司", "textField_mbc1lbzm": "10295L20CO0234", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,455 - INFO - 正在处理第 34 条更新记录 - store_code: 100100484
2025-07-01 09:00:45,455 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100484", "textField_m8e8g3lu": "卓正医疗", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111385", "textField_mb7rs39i": "佛山南海卓正综合门诊有限公司", "textField_mbc1lbzm": "10295L24CO0124", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,455 - INFO - 正在处理第 35 条更新记录 - store_code: 100100949
2025-07-01 09:00:45,455 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100949", "textField_m8e8g3lu": "讯飞英语通", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0117", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,455 - INFO - 正在处理第 36 条更新记录 - store_code: 100099236
2025-07-01 09:00:45,455 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099236", "textField_m8e8g3lu": "万达影城", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103915", "textField_mb7rs39i": "广州万达国际电影城有限公司", "textField_mbc1lbzm": "10295L20CO0173", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,455 - INFO - 正在处理第 37 条更新记录 - store_code: 100100925
2025-07-01 09:00:45,455 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100925", "textField_m8e8g3lu": "范莎英语", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0026", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,455 - INFO - 正在处理第 38 条更新记录 - store_code: 100099268
2025-07-01 09:00:45,455 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099268", "textField_m8e8g3lu": "大湾教育", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "109129", "textField_mb7rs39i": "佛山市南海区大湾教育培训中心", "textField_mbc1lbzm": "10295L22CO0039", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,455 - INFO - 正在处理第 39 条更新记录 - store_code: 100099246
2025-07-01 09:00:45,455 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099246", "textField_m8e8g3lu": "7-11便利店", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104006", "textField_mb7rs39i": "广东赛壹便利店有限公司", "textField_mbc1lbzm": "10295L23CO0030", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,455 - INFO - 正在处理第 40 条更新记录 - store_code: 100099233
2025-07-01 09:00:45,455 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099233", "textField_m8e8g3lu": "尚礼跆拳道", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103926", "textField_mb7rs39i": "佛山市百哲体育有限公司", "textField_mbc1lbzm": "10295L22CO0087", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,455 - INFO - 正在处理第 41 条更新记录 - store_code: 100099214
2025-07-01 09:00:45,455 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099214", "textField_m8e8g3lu": "哥伦比亚", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111006", "textField_mb7rs39i": "佛山市南海区悦桂企业管理有限公司", "textField_mbc1lbzm": "10295L20CO0220", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,455 - INFO - 正在处理第 42 条更新记录 - store_code: 100099339
2025-07-01 09:00:45,455 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099339", "textField_m8e8g3lu": "乖乖专业儿童摄影", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0036", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,455 - INFO - 正在处理第 43 条更新记录 - store_code: 100099312
2025-07-01 09:00:45,455 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099312", "textField_m8e8g3lu": "和本国际医疗", "employeeField_m8e8g3lw": ["18ee095369c74d07bd5e9364f0bbfe21"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0038", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,455 - INFO - 正在处理第 44 条更新记录 - store_code: 100100467
2025-07-01 09:00:45,455 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100467", "textField_m8e8g3lu": "彼芯托管", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0003", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,455 - INFO - 正在处理第 45 条更新记录 - store_code: 100099247
2025-07-01 09:00:45,470 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099247", "textField_m8e8g3lu": "多经-国王攀岩", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "109064", "textField_mb7rs39i": "广州市国王攀岩俱乐部有限公司", "textField_mbc1lbzm": "10295L22CO0017", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,470 - INFO - 正在处理第 46 条更新记录 - store_code: 100099234
2025-07-01 09:00:45,470 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100099234", "textField_m8e8g3lu": "木喵喵木", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L24CO0039", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,470 - INFO - 正在处理第 47 条更新记录 - store_code: 100101216
2025-07-01 09:00:45,470 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101216", "textField_m8e8g3lu": "星脊康健康中心", "employeeField_m8e8g3lw": ["1971b2e69fe08b1dcd93ff64cc28d918"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L25CO0002", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,470 - INFO - 正在处理第 48 条更新记录 - store_code: 100101262
2025-07-01 09:00:45,470 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101262", "textField_m8e8g3lu": "极客玩家", "employeeField_m8e8g3lw": ["1911111840fdac3db9f89204e6eb4322"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L25CO0006", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,470 - INFO - 正在处理第 49 条更新记录 - store_code: 100101277
2025-07-01 09:00:45,470 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101277", "textField_m8e8g3lu": "京东电器", "employeeField_m8e8g3lw": ["174c4fe5c5853e3c68a4e0347338a136"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L25CO0012", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,470 - INFO - 正在处理第 50 条更新记录 - store_code: 100101288
2025-07-01 09:00:45,470 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101288", "textField_m8e8g3lu": "玛瑞莎", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L25CO0013", "textField_m9jkl9nx": "否"}
2025-07-01 09:00:45,470 - INFO - 达到批量处理大小，开始批量更新 50 条记录
2025-07-01 09:00:45,908 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:46,424 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:46,861 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:47,314 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:47,767 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:48,174 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:48,705 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:49,127 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:49,564 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:50,002 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:50,470 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:50,877 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:51,361 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:51,814 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:52,314 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:52,767 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:53,236 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:53,658 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:54,080 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:54,533 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:54,970 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:55,439 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:55,908 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:56,392 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:56,877 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:57,283 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:57,783 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:58,205 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:58,705 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:59,189 - INFO - 批量更新表单数据成功: 
2025-07-01 09:00:59,627 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:00,095 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:00,564 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:00,955 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:01,486 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:01,923 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:02,439 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:02,845 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:03,283 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:03,752 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:04,205 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:04,720 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:05,158 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:05,627 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:06,048 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:06,533 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:06,955 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:07,423 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:07,970 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:08,470 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:08,470 - INFO - 批量更新成功，form_instance_ids: ['FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMX4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMZ4', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMF6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMG6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMH6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMJ6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBML6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMM6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMR6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMS6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMU6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBM27', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBM57', 'FINST-7PF66CC1RKVV7DYI8X2R977VF92123AIJPDBMSB', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBM77', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBM87', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBME7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMH7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMJ7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMK7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBML7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMM7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMP7', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBMO9', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBMQ9', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBMR9', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBMS9', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBMU9', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBMW9', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBMX9', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBMY9', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBMZ9', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBM1A', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBM2A', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBM3A', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2KXJADCBM6A', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMHA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMLA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMMA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMNA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMPA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMSA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMTA', 'FINST-E3G66QA1N7VVW18ABFYT9AYO99NG2LXJADCBMXA', 'FINST-7PF66CC1RKVV7DYI8X2R977VF92123AIJPDBMTB', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0374KADCBMGI', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0384KADCBMNI', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0384KADCBMOI', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0384KADCBMRI', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0384KADCBMSI']
2025-07-01 09:01:08,470 - INFO - 正在处理第 1 条更新记录 - store_code: *********
2025-07-01 09:01:08,470 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "源小晓", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10295L25CO0017", "textField_m9jkl9nx": "否"}
2025-07-01 09:01:08,470 - INFO - 正在处理第 2 条更新记录 - store_code: 100100352
2025-07-01 09:01:08,470 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉国金天地", "textField_m911r3pn": "100100352", "textField_m8e8g3lu": "康悦诗", "employeeField_m8e8g3lw": ["196e80b2a411363583cdcd84553878d3", "189fd3054a18e03a44b872b495795896", "18acf80e2a353b81652ebdc4329bdcc4", "1835384a550d8f5bfad8b704c9e963f7", "18353820b37845b95ed69864fd58b885", "1847a0e687fd986b4b870904dea8430d", "16d2416ce06396ed3f55a964f9fa0c71", "18b6ef624129a1e2c9b51774b6e8ce8c"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110906", "textField_mb7rs39i": "王玉洁", "textField_mbc1lbzm": "P0003L25CO0037", "textField_m9jkl9nx": "否"}
2025-07-01 09:01:08,470 - INFO - 正在处理第 3 条更新记录 - store_code: 100101273
2025-07-01 09:01:08,470 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉国金天地", "textField_m911r3pn": "100101273", "textField_m8e8g3lu": "斐悦", "employeeField_m8e8g3lw": ["196e80b2a411363583cdcd84553878d3", "189fd3054a18e03a44b872b495795896", "18acf80e2a353b81652ebdc4329bdcc4", "1835384a550d8f5bfad8b704c9e963f7", "18353820b37845b95ed69864fd58b885", "1847a0e687fd986b4b870904dea8430d", "16d2416ce06396ed3f55a964f9fa0c71", "18b6ef624129a1e2c9b51774b6e8ce8c"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0003L25CO0012", "textField_m9jkl9nx": "否"}
2025-07-01 09:01:08,486 - INFO - 正在处理第 4 条更新记录 - store_code: 100101279
2025-07-01 09:01:08,486 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉国金天地", "textField_m911r3pn": "100101279", "textField_m8e8g3lu": "半空杯bistro", "employeeField_m8e8g3lw": ["196e80b2a411363583cdcd84553878d3", "189fd3054a18e03a44b872b495795896", "18acf80e2a353b81652ebdc4329bdcc4", "1847a0e687fd986b4b870904dea8430d", "18353820b37845b95ed69864fd58b885", "1835384a550d8f5bfad8b704c9e963f7", "16d2416ce06396ed3f55a964f9fa0c71", "18b6ef624129a1e2c9b51774b6e8ce8c"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0003L25CO0020", "textField_m9jkl9nx": "否"}
2025-07-01 09:01:08,486 - INFO - 正在处理第 5 条更新记录 - store_code: 100101281
2025-07-01 09:01:08,486 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉国金天地", "textField_m911r3pn": "100101281", "textField_m8e8g3lu": "久吕寿司", "employeeField_m8e8g3lw": ["196e80b2a411363583cdcd84553878d3", "189fd3054a18e03a44b872b495795896", "18acf80e2a353b81652ebdc4329bdcc4", "1847a0e687fd986b4b870904dea8430d", "18353820b37845b95ed69864fd58b885", "1835384a550d8f5bfad8b704c9e963f7", "16d2416ce06396ed3f55a964f9fa0c71", "18b6ef624129a1e2c9b51774b6e8ce8c"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0003L25CO0021", "textField_m9jkl9nx": "否"}
2025-07-01 09:01:08,486 - INFO - 正在处理第 6 条更新记录 - store_code: 100101280
2025-07-01 09:01:08,486 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉星汇维港", "textField_m911r3pn": "100101280", "textField_m8e8g3lu": "一点点台式奶茶", "employeeField_m8e8g3lw": ["16d2416e996837cef3e7d79485192fc3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10630L25CO0007", "textField_m9jkl9nx": "否"}
2025-07-01 09:01:08,486 - INFO - 正在处理第 7 条更新记录 - store_code: 100101286
2025-07-01 09:01:08,486 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉星汇维港", "textField_m911r3pn": "100101286", "textField_m8e8g3lu": "大米先生", "employeeField_m8e8g3lw": ["16d2416e996837cef3e7d79485192fc3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10630L25CO0017", "textField_m9jkl9nx": "否"}
2025-07-01 09:01:08,486 - INFO - 正在处理第 8 条更新记录 - store_code: 100101298
2025-07-01 09:01:08,486 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉星汇维港", "textField_m911r3pn": "100101298", "textField_m8e8g3lu": "和善园手工鲜包", "employeeField_m8e8g3lw": ["188227e479184e0b0a1d7234cc0830b0"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10630L25CO0012", "textField_m9jkl9nx": "否"}
2025-07-01 09:01:08,486 - INFO - 处理剩余 8 条更新记录
2025-07-01 09:01:09,017 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:09,439 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:09,892 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:10,345 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:10,861 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:11,236 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:11,673 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:12,142 - INFO - 批量更新表单数据成功: 
2025-07-01 09:01:12,142 - INFO - 批量更新成功，form_instance_ids: ['FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0384KADCBMUI', 'FINST-9EA669D1BGVVNOZG7TCCP8XTN9SE25HKADCBMP4', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573G0LADCBM34', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573G0LADCBM54', 'FINST-LLF66FD1ZCWV99RA9A68MC0YHF573G0LADCBM64', 'FINST-DIC66I912HUVER3UCWV957HJ6FWK3WOQLDCBMKD', 'FINST-EEC66XC1CISVWZ5HCG6LJ4YFOO0S3AGLADCBM9Z', 'FINST-EEC66XC1CISVWZ5HCG6LJ4YFOO0S3AGLADCBMBZ']
2025-07-01 09:01:12,142 - INFO - 宜搭表单更新完成
2025-07-01 09:01:12,142 - INFO - 数据处理完成
2025-07-01 09:01:12,142 - INFO - 数据库连接已关闭
