# com.gooagoo.exportbill 账单导入接口测试工具

## 📋 文件说明

### 主要文件
- **`simple_api_test.py`** - 主测试文件
- **`com.gooagoo.exportbill-账单导入.html`** - 接口文档
- **`README_exportbill_test.md`** - 详细说明文档
- **`使用说明.md`** - 本文件（快速使用指南）

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install requests
```

### 2. 运行测试

#### 方式一：交互式菜单（推荐）
```bash
python simple_api_test.py
```
然后按提示选择：
- `1` - 基础账单导入测试
- `2` - 多种账单类型测试  
- `3` - 自定义参数测试
- `4` - 查看接口文档说明
- `0` - 退出

#### 方式二：命令行直接运行
```bash
# 基础测试
python simple_api_test.py basic

# 多种账单类型测试
python simple_api_test.py multi

# 查看接口信息
python simple_api_test.py info
```

## ✅ 测试结果示例

### 成功响应
```
2025-07-06 18:40:39,192 - INFO - ✅ 账单导入成功!
2025-07-06 18:40:39,192 - INFO - 📄 响应消息: 账单导入成功
2025-07-06 18:40:39,192 - INFO - 📊 业务数据: 成功
```

### 测试参数
```json
{
  "exactBillType": "10102",
  "billSerialNumber": "TEST1751798438",
  "terminalNumber": "6A53BB2D7CDE",
  "saleTime": "2025-07-06 18:40:38",
  "thirdPartyOrderNo": "ORDER1751798438",
  "receivableAmount": 55.22,
  "totalNum": 10.0,
  "totalFee": 60.0,
  "paidAmount": 55.22,
  "billType": "1"
}
```

## 🔧 配置说明

### 测试环境（默认使用）
- **URL**: `http://api.test.goago.cn/oapi/rest`
- **AppId**: `d1667ebbaa3e4935a7e09be1a50f0af5`
- **设备编号**: `6A53BB2D7CDE`

### 生产环境
- **URL**: `http://api.gooagoo.com/oapi/rest`
- **AppId**: `a5274b7e5d9a41939346c33c2c3443db`

## 📊 支持的账单类型

### 基础类型
- `1` - 结账单
- `6` - 退款单
- `3` - 日结单

### 细分类型
- `10102` - 美团外卖单
- `10103` - 饿了么外卖单
- `10104` - 抖音外卖单
- `10105` - 京东外卖单
- `10602` - 美团外卖退款单
- `10603` - 饿了么外卖退款单
- `10604` - 抖音外卖退款单
- `10605` - 京东外卖退款单

## 🎯 特点

- ✅ **仅必填参数** - 简化测试，专注核心功能
- 🧪 **测试环境** - 默认使用安全的测试环境
- 📝 **详细日志** - 完整的请求响应记录
- 🔄 **多场景测试** - 支持不同类型账单测试
- 🎯 **交互友好** - 菜单式操作界面

## 📝 必填参数说明

| 参数名 | 说明 | 示例 |
|--------|------|------|
| exactBillType | 细分账单类型 | "10102" |
| billSerialNumber | 票据流水号 | "TEST123456" |
| terminalNumber | 设备编号(12位) | "6A53BB2D7CDE" |
| saleTime | 销售时间 | "2025-01-06 15:30:00" |
| thirdPartyOrderNo | 第三方订单号 | "ORDER123456" |
| receivableAmount | 实收金额 | 55.22 |
| totalNum | 商品数量 | 10.0 |
| totalFee | 应收金额 | 60.00 |
| paidAmount | 实付金额 | 55.22 |
| billType | 账单类型 | "1" |

## 🔍 自定义测试

如需修改测试参数，编辑 `simple_api_test.py` 文件中的 `business_data` 部分：

```python
business_data = {
    "exactBillType": "10103",  # 改为饿了么外卖单
    "billSerialNumber": f"CUSTOM{int(time.time())}",
    "terminalNumber": TEST_CONFIG['terminalNumber'],
    "saleTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    "thirdPartyOrderNo": f"MYORDER{int(time.time())}",
    "receivableAmount": 88.88,  # 修改金额
    "totalNum": 5.0,
    "totalFee": 90.00,
    "paidAmount": 88.88,
    "billType": "1"
}
```

## 📋 日志文件

测试过程会生成日志文件：
- **文件名**: `exportbill_test_YYYYMMDD.log`
- **位置**: 脚本运行目录
- **内容**: 详细的请求参数、响应结果、错误信息

## ⚠️ 注意事项

1. **设备编号**: 必须在数衍平台存在，否则导入失败
2. **订单号唯一性**: `thirdPartyOrderNo` 要求唯一，重复会覆盖
3. **金额格式**: 保留两位小数
4. **时间格式**: 使用 `yyyy-MM-dd HH:mm:ss` 格式
5. **测试频率**: 避免过于频繁的请求

## 🆘 常见问题

### Q: 如何切换到生产环境？
A: 修改 `simple_api_test.py` 中的 `use_test_env = False`

### Q: 如何添加非必填参数？
A: 在 `business_data` 中添加需要的参数，参考接口文档

### Q: 测试失败怎么办？
A: 查看日志文件，检查参数格式和网络连接

### Q: 如何测试退款单？
A: 设置 `billType = "6"` 和对应的退款类型 `exactBillType`

## 📞 技术支持

如有问题，请：
1. 查看生成的日志文件
2. 确认参数配置正确  
3. 检查网络连接状态
4. 参考接口文档说明
