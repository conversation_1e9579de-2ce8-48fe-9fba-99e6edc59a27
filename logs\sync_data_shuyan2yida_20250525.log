2025-05-25 08:00:03,579 - INFO - ==================================================
2025-05-25 08:00:03,579 - INFO - 程序启动 - 版本 v1.0.0
2025-05-25 08:00:03,579 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250525.log
2025-05-25 08:00:03,579 - INFO - ==================================================
2025-05-25 08:00:03,579 - INFO - 程序入口点: __main__
2025-05-25 08:00:03,579 - INFO - ==================================================
2025-05-25 08:00:03,579 - INFO - 程序启动 - 版本 v1.0.1
2025-05-25 08:00:03,579 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250525.log
2025-05-25 08:00:03,579 - INFO - ==================================================
2025-05-25 08:00:03,892 - INFO - 数据库文件已存在: data\sales_data.db
2025-05-25 08:00:03,892 - INFO - sales_data表已存在，无需创建
2025-05-25 08:00:03,892 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-05-25 08:00:03,892 - INFO - DataSyncManager初始化完成
2025-05-25 08:00:03,892 - INFO - 未提供日期参数，使用默认值
2025-05-25 08:00:03,892 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-05-25 08:00:03,892 - INFO - 开始综合数据同步流程...
2025-05-25 08:00:03,892 - INFO - 正在获取数衍平台日销售数据...
2025-05-25 08:00:03,892 - INFO - 查询数衍平台数据，时间段为: 2025-03-25, 2025-05-24
2025-05-25 08:00:03,892 - INFO - 正在获取********至********的数据
2025-05-25 08:00:03,892 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-25 08:00:03,892 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9662333ECB2864B25DCA5269CD086D6E'}
2025-05-25 08:00:08,798 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-25 08:00:08,814 - INFO - 过滤后保留 1556 条记录
2025-05-25 08:00:10,829 - INFO - 正在获取********至********的数据
2025-05-25 08:00:10,829 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-25 08:00:10,829 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7303BAED0219D0CD30A8B530842894A5'}
2025-05-25 08:00:14,376 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-25 08:00:14,392 - INFO - 过滤后保留 1508 条记录
2025-05-25 08:00:16,407 - INFO - 正在获取********至********的数据
2025-05-25 08:00:16,407 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-25 08:00:16,407 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C052B0D32E3AE5F0BA0F2B00C7823FAB'}
2025-05-25 08:00:18,954 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-25 08:00:18,970 - INFO - 过滤后保留 1503 条记录
2025-05-25 08:00:20,985 - INFO - 正在获取********至********的数据
2025-05-25 08:00:20,985 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-25 08:00:20,985 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C4851712B02E32F18837FDA7A7DC7BD2'}
2025-05-25 08:00:23,642 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-25 08:00:23,657 - INFO - 过滤后保留 1504 条记录
2025-05-25 08:00:25,673 - INFO - 正在获取********至********的数据
2025-05-25 08:00:25,673 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-25 08:00:25,673 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '07A3290A08D38E8193D38FD782D09659'}
2025-05-25 08:00:28,173 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-25 08:00:28,189 - INFO - 过滤后保留 1481 条记录
2025-05-25 08:00:30,204 - INFO - 正在获取********至********的数据
2025-05-25 08:00:30,204 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-25 08:00:30,204 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1858C908BAA6083E613C0A26B71151A7'}
2025-05-25 08:00:32,985 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-25 08:00:33,001 - INFO - 过滤后保留 1488 条记录
2025-05-25 08:00:35,017 - INFO - 正在获取********至********的数据
2025-05-25 08:00:35,017 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-25 08:00:35,017 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8197B865F131F91560CDE7869BC2BB90'}
2025-05-25 08:00:37,360 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-25 08:00:37,376 - INFO - 过滤后保留 1477 条记录
2025-05-25 08:00:39,392 - INFO - 正在获取********至********的数据
2025-05-25 08:00:39,392 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-25 08:00:39,392 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '99A2CE88AAC023A612596C235636FF3F'}
2025-05-25 08:00:41,689 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-25 08:00:41,704 - INFO - 过滤后保留 1473 条记录
2025-05-25 08:00:43,720 - INFO - 正在获取********至********的数据
2025-05-25 08:00:43,720 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-25 08:00:43,720 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2114D62FC0C7227D8FAD85110A62AE3B'}
2025-05-25 08:00:45,438 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-25 08:00:45,438 - INFO - 过滤后保留 1046 条记录
2025-05-25 08:00:47,454 - INFO - 开始保存数据到SQLite数据库，共 13036 条记录待处理
2025-05-25 08:00:48,329 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-23
2025-05-25 08:00:48,329 - INFO - 变更字段: recommend_amount: 0.0 -> 7382.3, daily_bill_amount: 0.0 -> 7382.3
2025-05-25 08:00:48,329 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HOE1A3UTAESD606LODAUCEHAF001M2A, sale_time=2025-05-23
2025-05-25 08:00:48,329 - INFO - 变更字段: amount: 7203 -> 7352, count: 71 -> 72, instore_amount: 4617.36 -> 4766.03, instore_count: 37 -> 38
2025-05-25 08:00:48,329 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE2TORA7KI7Q2OV4FVC7FC0014BT, sale_time=2025-05-23
2025-05-25 08:00:48,329 - INFO - 变更字段: amount: 2050 -> 2129, count: 62 -> 63, online_amount: 483.97 -> 562.28, online_count: 15 -> 16
2025-05-25 08:00:48,329 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE29HIJ7QK7Q2OV4FVC7F40014BL, sale_time=2025-05-23
2025-05-25 08:00:48,329 - INFO - 变更字段: amount: 3576 -> 5286, count: 15 -> 16, instore_amount: 5676.2 -> 7386.2, instore_count: 15 -> 16
2025-05-25 08:00:48,329 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDSD911K3E7Q2OV4FVC7C800148P, sale_time=2025-05-23
2025-05-25 08:00:48,329 - INFO - 变更字段: amount: 4304 -> 4314, count: 172 -> 173, instore_amount: 4333.18 -> 4343.08, instore_count: 172 -> 173
2025-05-25 08:00:48,345 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDQDVL1EG67Q2OV4FVC7B800147P, sale_time=2025-05-23
2025-05-25 08:00:48,345 - INFO - 变更字段: amount: 1013 -> 929, count: 53 -> 54, instore_amount: 360.6 -> 402.6, instore_count: 18 -> 19
2025-05-25 08:00:48,345 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDMMR23CHP7Q2OV4FVC79C00145T, sale_time=2025-05-23
2025-05-25 08:00:48,345 - INFO - 变更字段: amount: 1080 -> 1111, count: 40 -> 42, online_amount: 845.6 -> 877.01, online_count: 31 -> 33
2025-05-25 08:00:48,345 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S, sale_time=2025-05-23
2025-05-25 08:00:48,345 - INFO - 变更字段: instore_amount: 12793.1 -> 12731.8, instore_count: 297 -> 296, online_amount: 395.6 -> 456.9, online_count: 8 -> 9
2025-05-25 08:00:48,345 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-05-23
2025-05-25 08:00:48,345 - INFO - 变更字段: recommend_amount: 0.0 -> 13036.68, daily_bill_amount: 0.0 -> 13036.68
2025-05-25 08:00:48,345 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINSC23H1J3M7Q2OV392410L00148H, sale_time=2025-05-23
2025-05-25 08:00:48,345 - INFO - 变更字段: amount: 3138 -> 3743, count: 7 -> 11, instore_amount: 3138.0 -> 3743.0, instore_count: 7 -> 11
2025-05-25 08:00:48,345 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=6D1F9FC749FA44C6B70CA818C3E7FB77, sale_time=2025-05-23
2025-05-25 08:00:48,345 - INFO - 变更字段: recommend_amount: 0.0 -> 1521.0, daily_bill_amount: 0.0 -> 1521.0
2025-05-25 08:00:48,345 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-23
2025-05-25 08:00:48,345 - INFO - 变更字段: recommend_amount: 2018.93 -> 2044.92, amount: 2018 -> 2044, count: 111 -> 113, instore_amount: 480.5 -> 499.49, instore_count: 18 -> 19, online_amount: 1568.43 -> 1575.43, online_count: 93 -> 94
2025-05-25 08:00:48,345 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-23
2025-05-25 08:00:48,360 - INFO - 变更字段: amount: 8523 -> 8583, count: 251 -> 252, instore_amount: 8095.82 -> 8155.82, instore_count: 244 -> 245
2025-05-25 08:00:48,360 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-22
2025-05-25 08:00:48,360 - INFO - 变更字段: amount: 5171 -> 5321, count: 194 -> 195, instore_amount: 5039.1 -> 5189.1, instore_count: 187 -> 188
2025-05-25 08:00:48,360 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-23
2025-05-25 08:00:48,360 - INFO - 变更字段: recommend_amount: 0.0 -> 1563.0, daily_bill_amount: 0.0 -> 1563.0
2025-05-25 08:00:48,360 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-23
2025-05-25 08:00:48,360 - INFO - 变更字段: recommend_amount: 6920.62 -> 7082.02, amount: 6920 -> 7082, count: 187 -> 190, instore_amount: 5744.62 -> 5906.02, instore_count: 156 -> 159
2025-05-25 08:00:48,360 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-23
2025-05-25 08:00:48,360 - INFO - 变更字段: amount: 4090 -> 4113, count: 213 -> 215, online_amount: 2003.8 -> 2026.2, online_count: 95 -> 97
2025-05-25 08:00:48,360 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-05-23
2025-05-25 08:00:48,360 - INFO - 变更字段: recommend_amount: 1076.87 -> 1081.37, amount: 1076 -> 1081, count: 86 -> 87, online_amount: 718.29 -> 722.79, online_count: 61 -> 62
2025-05-25 08:00:48,360 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-23
2025-05-25 08:00:48,360 - INFO - 变更字段: recommend_amount: 4381.82 -> 4393.42, amount: 4381 -> 4393, count: 237 -> 238, online_amount: 4001.44 -> 4013.04, online_count: 196 -> 197
2025-05-25 08:00:48,360 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-22
2025-05-25 08:00:48,360 - INFO - 变更字段: recommend_amount: 5062.41 -> 5065.31, amount: 5062 -> 5065, count: 247 -> 248, online_amount: 3805.92 -> 3808.82, online_count: 190 -> 191
2025-05-25 08:00:48,360 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-05-23
2025-05-25 08:00:48,360 - INFO - 变更字段: recommend_amount: 0.0 -> 36372.98, daily_bill_amount: 0.0 -> 36372.98
2025-05-25 08:00:48,360 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEA4041D6F0I86N3H2U1V9001F8F, sale_time=2025-05-23
2025-05-25 08:00:48,360 - INFO - 变更字段: amount: 16427 -> 16762, count: 176 -> 178, instore_amount: 13178.06 -> 13369.86, instore_count: 90 -> 91, online_amount: 3269.96 -> 3412.29, online_count: 86 -> 87
2025-05-25 08:00:48,360 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-05-23
2025-05-25 08:00:48,360 - INFO - 变更字段: amount: 30081 -> 30976, count: 215 -> 218, instore_amount: 14610.7 -> 15505.7, instore_count: 82 -> 85
2025-05-25 08:00:48,360 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-05-23
2025-05-25 08:00:48,360 - INFO - 变更字段: amount: 29620 -> 30616, count: 202 -> 208, instore_amount: 19267.17 -> 20262.97, instore_count: 88 -> 94
2025-05-25 08:00:48,360 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE2CVHLBFV0I86N3H2U1RH001F4N, sale_time=2025-05-23
2025-05-25 08:00:48,360 - INFO - 变更字段: amount: 9402 -> 9804, count: 65 -> 66, instore_amount: 8127.9 -> 8529.9, instore_count: 52 -> 53
2025-05-25 08:00:48,376 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9D9OBM41R0I86N3H2U17K001EGQ, sale_time=2025-05-23
2025-05-25 08:00:48,376 - INFO - 变更字段: recommend_amount: 0.0 -> 6052.88, daily_bill_amount: 0.0 -> 6052.88
2025-05-25 08:00:48,376 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDVSS4V3460I86N3H2U12P001EBV, sale_time=2025-05-23
2025-05-25 08:00:48,376 - INFO - 变更字段: count: 104 -> 105, online_amount: 1878.05 -> 1912.65, online_count: 76 -> 77
2025-05-25 08:00:48,376 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-05-23
2025-05-25 08:00:48,376 - INFO - 变更字段: amount: 4361 -> 4417, count: 264 -> 270, online_amount: 3915.24 -> 3970.44, online_count: 242 -> 248
2025-05-25 08:00:48,376 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSK489TE20I86N3H2U114001EAA, sale_time=2025-05-23
2025-05-25 08:00:48,376 - INFO - 变更字段: recommend_amount: 3824.33 -> 4021.53, amount: 3824 -> 4021, count: 96 -> 97, online_amount: 445.6 -> 642.8, online_count: 5 -> 6
2025-05-25 08:00:48,376 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-23
2025-05-25 08:00:48,376 - INFO - 变更字段: amount: 7495 -> 7521, count: 457 -> 460, instore_amount: 5518.46 -> 5548.68, instore_count: 317 -> 320, online_amount: 2173.53 -> 2176.21
2025-05-25 08:00:48,376 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-22
2025-05-25 08:00:48,376 - INFO - 变更字段: instore_amount: 3934.43 -> 3936.93, instore_count: 247 -> 248, online_amount: 1541.29 -> 1538.79, online_count: 99 -> 98
2025-05-25 08:00:48,376 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-23
2025-05-25 08:00:48,376 - INFO - 变更字段: amount: 8629 -> 8802, count: 113 -> 114, online_amount: 1180.28 -> 1353.28, online_count: 26 -> 27
2025-05-25 08:00:48,392 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-23
2025-05-25 08:00:48,392 - INFO - 变更字段: recommend_amount: 3485.42 -> 3446.8, amount: 3485 -> 3446
2025-05-25 08:00:48,392 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B, sale_time=2025-05-23
2025-05-25 08:00:48,392 - INFO - 变更字段: recommend_amount: 6956.49 -> 6940.69, amount: 6956 -> 6940
2025-05-25 08:00:48,392 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-05-23
2025-05-25 08:00:48,392 - INFO - 变更字段: amount: 16407 -> 17015, count: 199 -> 201, instore_amount: 14744.3 -> 15352.3, instore_count: 120 -> 122
2025-05-25 08:00:48,392 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-05-23
2025-05-25 08:00:48,392 - INFO - 变更字段: amount: 35824 -> 42683, count: 135 -> 137, instore_amount: 34509.37 -> 41368.74, instore_count: 92 -> 94
2025-05-25 08:00:48,392 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUISOVAPU1P7AV8LHQQGIDU001EK7, sale_time=2025-05-23
2025-05-25 08:00:48,392 - INFO - 变更字段: amount: 9513 -> 10327, count: 13 -> 14, instore_amount: 9120.7 -> 9934.3, instore_count: 11 -> 12
2025-05-25 08:00:48,392 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-05-23
2025-05-25 08:00:48,392 - INFO - 变更字段: amount: 6803 -> 8516, count: 46 -> 48, instore_amount: 6281.1 -> 7994.1, instore_count: 37 -> 39
2025-05-25 08:00:48,626 - INFO - SQLite数据保存完成，统计信息：
2025-05-25 08:00:48,626 - INFO - - 总记录数: 13036
2025-05-25 08:00:48,626 - INFO - - 成功插入: 220
2025-05-25 08:00:48,626 - INFO - - 成功更新: 38
2025-05-25 08:00:48,626 - INFO - - 无需更新: 12778
2025-05-25 08:00:48,626 - INFO - - 处理失败: 0
2025-05-25 08:00:54,017 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_20250525.xlsx
2025-05-25 08:00:54,017 - INFO - 成功获取数衍平台数据，共 13036 条记录
2025-05-25 08:00:54,017 - INFO - 正在更新SQLite月度汇总数据...
2025-05-25 08:00:54,032 - INFO - 月度数据sqllite清空完成
2025-05-25 08:00:54,267 - INFO - 月度汇总数据更新完成，处理了 1192 条汇总记录
2025-05-25 08:00:54,267 - INFO - 成功更新月度汇总数据，共 1192 条记录
2025-05-25 08:00:54,267 - INFO - 正在获取宜搭日销售表单数据...
2025-05-25 08:00:54,267 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-25 00:00:00 至 2025-05-24 23:59:59
2025-05-25 08:00:54,267 - INFO - 查询分段 1: 2025-03-25 至 2025-03-31
2025-05-25 08:00:54,267 - INFO - 查询日期范围: 2025-03-25 至 2025-03-31，使用分页查询，每页 100 条记录
2025-05-25 08:00:54,267 - INFO - Request Parameters - Page 1:
2025-05-25 08:00:54,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:00:54,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:00,298 - INFO - API请求耗时: 6031ms
2025-05-25 08:01:00,298 - INFO - Response - Page 1
2025-05-25 08:01:00,298 - INFO - 第 1 页获取到 100 条记录
2025-05-25 08:01:00,798 - INFO - Request Parameters - Page 2:
2025-05-25 08:01:00,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:00,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:01,532 - INFO - API请求耗时: 734ms
2025-05-25 08:01:01,532 - INFO - Response - Page 2
2025-05-25 08:01:01,532 - INFO - 第 2 页获取到 100 条记录
2025-05-25 08:01:02,048 - INFO - Request Parameters - Page 3:
2025-05-25 08:01:02,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:02,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:02,813 - INFO - API请求耗时: 766ms
2025-05-25 08:01:02,813 - INFO - Response - Page 3
2025-05-25 08:01:02,813 - INFO - 第 3 页获取到 100 条记录
2025-05-25 08:01:03,313 - INFO - Request Parameters - Page 4:
2025-05-25 08:01:03,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:03,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:07,266 - INFO - API请求耗时: 3953ms
2025-05-25 08:01:07,266 - INFO - Response - Page 4
2025-05-25 08:01:07,266 - INFO - 第 4 页获取到 100 条记录
2025-05-25 08:01:07,782 - INFO - Request Parameters - Page 5:
2025-05-25 08:01:07,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:07,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:08,641 - INFO - API请求耗时: 859ms
2025-05-25 08:01:08,641 - INFO - Response - Page 5
2025-05-25 08:01:08,641 - INFO - 第 5 页获取到 100 条记录
2025-05-25 08:01:09,157 - INFO - Request Parameters - Page 6:
2025-05-25 08:01:09,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:09,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:09,845 - INFO - API请求耗时: 688ms
2025-05-25 08:01:09,845 - INFO - Response - Page 6
2025-05-25 08:01:09,845 - INFO - 第 6 页获取到 100 条记录
2025-05-25 08:01:10,360 - INFO - Request Parameters - Page 7:
2025-05-25 08:01:10,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:10,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:11,110 - INFO - API请求耗时: 750ms
2025-05-25 08:01:11,110 - INFO - Response - Page 7
2025-05-25 08:01:11,110 - INFO - 第 7 页获取到 100 条记录
2025-05-25 08:01:11,610 - INFO - Request Parameters - Page 8:
2025-05-25 08:01:11,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:11,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:12,298 - INFO - API请求耗时: 687ms
2025-05-25 08:01:12,298 - INFO - Response - Page 8
2025-05-25 08:01:12,298 - INFO - 第 8 页获取到 100 条记录
2025-05-25 08:01:12,813 - INFO - Request Parameters - Page 9:
2025-05-25 08:01:12,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:12,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:13,563 - INFO - API请求耗时: 750ms
2025-05-25 08:01:13,563 - INFO - Response - Page 9
2025-05-25 08:01:13,563 - INFO - 第 9 页获取到 100 条记录
2025-05-25 08:01:14,079 - INFO - Request Parameters - Page 10:
2025-05-25 08:01:14,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:14,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:14,813 - INFO - API请求耗时: 734ms
2025-05-25 08:01:14,813 - INFO - Response - Page 10
2025-05-25 08:01:14,813 - INFO - 第 10 页获取到 100 条记录
2025-05-25 08:01:15,329 - INFO - Request Parameters - Page 11:
2025-05-25 08:01:15,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:15,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:16,016 - INFO - API请求耗时: 688ms
2025-05-25 08:01:16,032 - INFO - Response - Page 11
2025-05-25 08:01:16,032 - INFO - 第 11 页获取到 100 条记录
2025-05-25 08:01:16,548 - INFO - Request Parameters - Page 12:
2025-05-25 08:01:16,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:16,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:17,188 - INFO - API请求耗时: 641ms
2025-05-25 08:01:17,188 - INFO - Response - Page 12
2025-05-25 08:01:17,188 - INFO - 第 12 页获取到 100 条记录
2025-05-25 08:01:17,704 - INFO - Request Parameters - Page 13:
2025-05-25 08:01:17,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:17,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:18,454 - INFO - API请求耗时: 750ms
2025-05-25 08:01:18,454 - INFO - Response - Page 13
2025-05-25 08:01:18,454 - INFO - 第 13 页获取到 100 条记录
2025-05-25 08:01:18,954 - INFO - Request Parameters - Page 14:
2025-05-25 08:01:18,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:18,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:19,610 - INFO - API请求耗时: 656ms
2025-05-25 08:01:19,610 - INFO - Response - Page 14
2025-05-25 08:01:19,610 - INFO - 第 14 页获取到 100 条记录
2025-05-25 08:01:20,126 - INFO - Request Parameters - Page 15:
2025-05-25 08:01:20,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:20,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:20,860 - INFO - API请求耗时: 734ms
2025-05-25 08:01:20,860 - INFO - Response - Page 15
2025-05-25 08:01:20,860 - INFO - 第 15 页获取到 100 条记录
2025-05-25 08:01:21,376 - INFO - Request Parameters - Page 16:
2025-05-25 08:01:21,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:21,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:22,126 - INFO - API请求耗时: 750ms
2025-05-25 08:01:22,126 - INFO - Response - Page 16
2025-05-25 08:01:22,126 - INFO - 第 16 页获取到 100 条记录
2025-05-25 08:01:22,641 - INFO - Request Parameters - Page 17:
2025-05-25 08:01:22,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:22,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:23,344 - INFO - API请求耗时: 703ms
2025-05-25 08:01:23,344 - INFO - Response - Page 17
2025-05-25 08:01:23,344 - INFO - 第 17 页获取到 100 条记录
2025-05-25 08:01:23,844 - INFO - Request Parameters - Page 18:
2025-05-25 08:01:23,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:23,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:24,532 - INFO - API请求耗时: 687ms
2025-05-25 08:01:24,532 - INFO - Response - Page 18
2025-05-25 08:01:24,532 - INFO - 第 18 页获取到 100 条记录
2025-05-25 08:01:25,032 - INFO - Request Parameters - Page 19:
2025-05-25 08:01:25,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:25,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:25,735 - INFO - API请求耗时: 703ms
2025-05-25 08:01:25,735 - INFO - Response - Page 19
2025-05-25 08:01:25,751 - INFO - 第 19 页获取到 100 条记录
2025-05-25 08:01:26,266 - INFO - Request Parameters - Page 20:
2025-05-25 08:01:26,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:26,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:26,938 - INFO - API请求耗时: 672ms
2025-05-25 08:01:26,938 - INFO - Response - Page 20
2025-05-25 08:01:26,938 - INFO - 第 20 页获取到 100 条记录
2025-05-25 08:01:27,438 - INFO - Request Parameters - Page 21:
2025-05-25 08:01:27,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:27,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:28,219 - INFO - API请求耗时: 781ms
2025-05-25 08:01:28,219 - INFO - Response - Page 21
2025-05-25 08:01:28,219 - INFO - 第 21 页获取到 100 条记录
2025-05-25 08:01:28,735 - INFO - Request Parameters - Page 22:
2025-05-25 08:01:28,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:28,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:29,548 - INFO - API请求耗时: 812ms
2025-05-25 08:01:29,548 - INFO - Response - Page 22
2025-05-25 08:01:29,548 - INFO - 第 22 页获取到 100 条记录
2025-05-25 08:01:30,048 - INFO - Request Parameters - Page 23:
2025-05-25 08:01:30,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:30,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:30,860 - INFO - API请求耗时: 813ms
2025-05-25 08:01:30,860 - INFO - Response - Page 23
2025-05-25 08:01:30,860 - INFO - 第 23 页获取到 100 条记录
2025-05-25 08:01:31,376 - INFO - Request Parameters - Page 24:
2025-05-25 08:01:31,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:31,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:32,094 - INFO - API请求耗时: 719ms
2025-05-25 08:01:32,094 - INFO - Response - Page 24
2025-05-25 08:01:32,094 - INFO - 第 24 页获取到 100 条记录
2025-05-25 08:01:32,610 - INFO - Request Parameters - Page 25:
2025-05-25 08:01:32,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:32,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:33,251 - INFO - API请求耗时: 641ms
2025-05-25 08:01:33,251 - INFO - Response - Page 25
2025-05-25 08:01:33,251 - INFO - 第 25 页获取到 100 条记录
2025-05-25 08:01:33,766 - INFO - Request Parameters - Page 26:
2025-05-25 08:01:33,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:33,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:34,454 - INFO - API请求耗时: 687ms
2025-05-25 08:01:34,454 - INFO - Response - Page 26
2025-05-25 08:01:34,454 - INFO - 第 26 页获取到 100 条记录
2025-05-25 08:01:34,954 - INFO - Request Parameters - Page 27:
2025-05-25 08:01:34,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:34,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:35,641 - INFO - API请求耗时: 687ms
2025-05-25 08:01:35,641 - INFO - Response - Page 27
2025-05-25 08:01:35,657 - INFO - 第 27 页获取到 100 条记录
2025-05-25 08:01:36,157 - INFO - Request Parameters - Page 28:
2025-05-25 08:01:36,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:36,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:36,860 - INFO - API请求耗时: 703ms
2025-05-25 08:01:36,860 - INFO - Response - Page 28
2025-05-25 08:01:36,860 - INFO - 第 28 页获取到 100 条记录
2025-05-25 08:01:37,376 - INFO - Request Parameters - Page 29:
2025-05-25 08:01:37,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:37,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:38,079 - INFO - API请求耗时: 703ms
2025-05-25 08:01:38,079 - INFO - Response - Page 29
2025-05-25 08:01:38,079 - INFO - 第 29 页获取到 100 条记录
2025-05-25 08:01:38,594 - INFO - Request Parameters - Page 30:
2025-05-25 08:01:38,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:38,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:39,266 - INFO - API请求耗时: 672ms
2025-05-25 08:01:39,266 - INFO - Response - Page 30
2025-05-25 08:01:39,266 - INFO - 第 30 页获取到 100 条记录
2025-05-25 08:01:39,782 - INFO - Request Parameters - Page 31:
2025-05-25 08:01:39,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:39,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:40,469 - INFO - API请求耗时: 688ms
2025-05-25 08:01:40,469 - INFO - Response - Page 31
2025-05-25 08:01:40,469 - INFO - 第 31 页获取到 100 条记录
2025-05-25 08:01:40,985 - INFO - Request Parameters - Page 32:
2025-05-25 08:01:40,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:40,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742832000267, 1743350400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:41,469 - INFO - API请求耗时: 484ms
2025-05-25 08:01:41,469 - INFO - Response - Page 32
2025-05-25 08:01:41,469 - INFO - 第 32 页获取到 16 条记录
2025-05-25 08:01:41,469 - INFO - 查询完成，共获取到 3116 条记录
2025-05-25 08:01:41,469 - INFO - 分段 1 查询成功，获取到 3116 条记录
2025-05-25 08:01:42,485 - INFO - 查询分段 2: 2025-04-01 至 2025-04-07
2025-05-25 08:01:42,485 - INFO - 查询日期范围: 2025-04-01 至 2025-04-07，使用分页查询，每页 100 条记录
2025-05-25 08:01:42,485 - INFO - Request Parameters - Page 1:
2025-05-25 08:01:42,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:42,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:43,297 - INFO - API请求耗时: 812ms
2025-05-25 08:01:43,297 - INFO - Response - Page 1
2025-05-25 08:01:43,297 - INFO - 第 1 页获取到 100 条记录
2025-05-25 08:01:43,797 - INFO - Request Parameters - Page 2:
2025-05-25 08:01:43,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:43,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:44,501 - INFO - API请求耗时: 703ms
2025-05-25 08:01:44,501 - INFO - Response - Page 2
2025-05-25 08:01:44,501 - INFO - 第 2 页获取到 100 条记录
2025-05-25 08:01:45,001 - INFO - Request Parameters - Page 3:
2025-05-25 08:01:45,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:45,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:45,704 - INFO - API请求耗时: 703ms
2025-05-25 08:01:45,704 - INFO - Response - Page 3
2025-05-25 08:01:45,704 - INFO - 第 3 页获取到 100 条记录
2025-05-25 08:01:46,219 - INFO - Request Parameters - Page 4:
2025-05-25 08:01:46,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:46,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:46,938 - INFO - API请求耗时: 719ms
2025-05-25 08:01:46,938 - INFO - Response - Page 4
2025-05-25 08:01:46,938 - INFO - 第 4 页获取到 100 条记录
2025-05-25 08:01:47,438 - INFO - Request Parameters - Page 5:
2025-05-25 08:01:47,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:47,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:48,063 - INFO - API请求耗时: 625ms
2025-05-25 08:01:48,063 - INFO - Response - Page 5
2025-05-25 08:01:48,063 - INFO - 第 5 页获取到 100 条记录
2025-05-25 08:01:48,579 - INFO - Request Parameters - Page 6:
2025-05-25 08:01:48,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:48,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:49,251 - INFO - API请求耗时: 672ms
2025-05-25 08:01:49,251 - INFO - Response - Page 6
2025-05-25 08:01:49,251 - INFO - 第 6 页获取到 100 条记录
2025-05-25 08:01:49,751 - INFO - Request Parameters - Page 7:
2025-05-25 08:01:49,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:49,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:50,469 - INFO - API请求耗时: 719ms
2025-05-25 08:01:50,469 - INFO - Response - Page 7
2025-05-25 08:01:50,469 - INFO - 第 7 页获取到 100 条记录
2025-05-25 08:01:50,985 - INFO - Request Parameters - Page 8:
2025-05-25 08:01:50,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:50,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:51,641 - INFO - API请求耗时: 656ms
2025-05-25 08:01:51,641 - INFO - Response - Page 8
2025-05-25 08:01:51,641 - INFO - 第 8 页获取到 100 条记录
2025-05-25 08:01:52,141 - INFO - Request Parameters - Page 9:
2025-05-25 08:01:52,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:52,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:52,860 - INFO - API请求耗时: 719ms
2025-05-25 08:01:52,860 - INFO - Response - Page 9
2025-05-25 08:01:52,860 - INFO - 第 9 页获取到 100 条记录
2025-05-25 08:01:53,360 - INFO - Request Parameters - Page 10:
2025-05-25 08:01:53,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:53,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:54,016 - INFO - API请求耗时: 656ms
2025-05-25 08:01:54,016 - INFO - Response - Page 10
2025-05-25 08:01:54,016 - INFO - 第 10 页获取到 100 条记录
2025-05-25 08:01:54,516 - INFO - Request Parameters - Page 11:
2025-05-25 08:01:54,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:54,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:55,219 - INFO - API请求耗时: 703ms
2025-05-25 08:01:55,219 - INFO - Response - Page 11
2025-05-25 08:01:55,219 - INFO - 第 11 页获取到 100 条记录
2025-05-25 08:01:55,735 - INFO - Request Parameters - Page 12:
2025-05-25 08:01:55,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:55,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:56,657 - INFO - API请求耗时: 922ms
2025-05-25 08:01:56,657 - INFO - Response - Page 12
2025-05-25 08:01:56,657 - INFO - 第 12 页获取到 100 条记录
2025-05-25 08:01:57,157 - INFO - Request Parameters - Page 13:
2025-05-25 08:01:57,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:57,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:57,829 - INFO - API请求耗时: 672ms
2025-05-25 08:01:57,829 - INFO - Response - Page 13
2025-05-25 08:01:57,829 - INFO - 第 13 页获取到 100 条记录
2025-05-25 08:01:58,344 - INFO - Request Parameters - Page 14:
2025-05-25 08:01:58,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:58,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:01:59,047 - INFO - API请求耗时: 703ms
2025-05-25 08:01:59,047 - INFO - Response - Page 14
2025-05-25 08:01:59,047 - INFO - 第 14 页获取到 100 条记录
2025-05-25 08:01:59,547 - INFO - Request Parameters - Page 15:
2025-05-25 08:01:59,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:01:59,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:00,219 - INFO - API请求耗时: 672ms
2025-05-25 08:02:00,219 - INFO - Response - Page 15
2025-05-25 08:02:00,219 - INFO - 第 15 页获取到 100 条记录
2025-05-25 08:02:00,735 - INFO - Request Parameters - Page 16:
2025-05-25 08:02:00,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:00,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:01,500 - INFO - API请求耗时: 766ms
2025-05-25 08:02:01,500 - INFO - Response - Page 16
2025-05-25 08:02:01,500 - INFO - 第 16 页获取到 100 条记录
2025-05-25 08:02:02,000 - INFO - Request Parameters - Page 17:
2025-05-25 08:02:02,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:02,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:02,704 - INFO - API请求耗时: 703ms
2025-05-25 08:02:02,719 - INFO - Response - Page 17
2025-05-25 08:02:02,719 - INFO - 第 17 页获取到 100 条记录
2025-05-25 08:02:03,219 - INFO - Request Parameters - Page 18:
2025-05-25 08:02:03,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:03,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:03,907 - INFO - API请求耗时: 687ms
2025-05-25 08:02:03,907 - INFO - Response - Page 18
2025-05-25 08:02:03,907 - INFO - 第 18 页获取到 100 条记录
2025-05-25 08:02:04,422 - INFO - Request Parameters - Page 19:
2025-05-25 08:02:04,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:04,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:05,125 - INFO - API请求耗时: 703ms
2025-05-25 08:02:05,125 - INFO - Response - Page 19
2025-05-25 08:02:05,125 - INFO - 第 19 页获取到 100 条记录
2025-05-25 08:02:05,625 - INFO - Request Parameters - Page 20:
2025-05-25 08:02:05,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:05,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:06,282 - INFO - API请求耗时: 656ms
2025-05-25 08:02:06,282 - INFO - Response - Page 20
2025-05-25 08:02:06,282 - INFO - 第 20 页获取到 100 条记录
2025-05-25 08:02:06,782 - INFO - Request Parameters - Page 21:
2025-05-25 08:02:06,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:06,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:07,454 - INFO - API请求耗时: 672ms
2025-05-25 08:02:07,454 - INFO - Response - Page 21
2025-05-25 08:02:07,454 - INFO - 第 21 页获取到 100 条记录
2025-05-25 08:02:07,954 - INFO - Request Parameters - Page 22:
2025-05-25 08:02:07,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:07,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:08,766 - INFO - API请求耗时: 812ms
2025-05-25 08:02:08,766 - INFO - Response - Page 22
2025-05-25 08:02:08,766 - INFO - 第 22 页获取到 100 条记录
2025-05-25 08:02:09,282 - INFO - Request Parameters - Page 23:
2025-05-25 08:02:09,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:09,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:09,938 - INFO - API请求耗时: 656ms
2025-05-25 08:02:09,938 - INFO - Response - Page 23
2025-05-25 08:02:09,938 - INFO - 第 23 页获取到 100 条记录
2025-05-25 08:02:10,454 - INFO - Request Parameters - Page 24:
2025-05-25 08:02:10,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:10,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:11,157 - INFO - API请求耗时: 703ms
2025-05-25 08:02:11,157 - INFO - Response - Page 24
2025-05-25 08:02:11,157 - INFO - 第 24 页获取到 100 条记录
2025-05-25 08:02:11,672 - INFO - Request Parameters - Page 25:
2025-05-25 08:02:11,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:11,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:12,532 - INFO - API请求耗时: 859ms
2025-05-25 08:02:12,532 - INFO - Response - Page 25
2025-05-25 08:02:12,532 - INFO - 第 25 页获取到 100 条记录
2025-05-25 08:02:13,032 - INFO - Request Parameters - Page 26:
2025-05-25 08:02:13,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:13,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:13,954 - INFO - API请求耗时: 922ms
2025-05-25 08:02:13,954 - INFO - Response - Page 26
2025-05-25 08:02:13,954 - INFO - 第 26 页获取到 100 条记录
2025-05-25 08:02:14,469 - INFO - Request Parameters - Page 27:
2025-05-25 08:02:14,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:14,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:15,360 - INFO - API请求耗时: 891ms
2025-05-25 08:02:15,360 - INFO - Response - Page 27
2025-05-25 08:02:15,360 - INFO - 第 27 页获取到 100 条记录
2025-05-25 08:02:15,860 - INFO - Request Parameters - Page 28:
2025-05-25 08:02:15,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:15,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:16,485 - INFO - API请求耗时: 625ms
2025-05-25 08:02:16,485 - INFO - Response - Page 28
2025-05-25 08:02:16,485 - INFO - 第 28 页获取到 100 条记录
2025-05-25 08:02:16,985 - INFO - Request Parameters - Page 29:
2025-05-25 08:02:16,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:16,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:17,672 - INFO - API请求耗时: 688ms
2025-05-25 08:02:17,672 - INFO - Response - Page 29
2025-05-25 08:02:17,672 - INFO - 第 29 页获取到 100 条记录
2025-05-25 08:02:18,188 - INFO - Request Parameters - Page 30:
2025-05-25 08:02:18,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:18,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:18,797 - INFO - API请求耗时: 609ms
2025-05-25 08:02:18,797 - INFO - Response - Page 30
2025-05-25 08:02:18,797 - INFO - 第 30 页获取到 100 条记录
2025-05-25 08:02:19,313 - INFO - Request Parameters - Page 31:
2025-05-25 08:02:19,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:19,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:20,047 - INFO - API请求耗时: 734ms
2025-05-25 08:02:20,047 - INFO - Response - Page 31
2025-05-25 08:02:20,047 - INFO - 第 31 页获取到 100 条记录
2025-05-25 08:02:20,563 - INFO - Request Parameters - Page 32:
2025-05-25 08:02:20,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:20,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:21,328 - INFO - API请求耗时: 766ms
2025-05-25 08:02:21,328 - INFO - Response - Page 32
2025-05-25 08:02:21,328 - INFO - 第 32 页获取到 100 条记录
2025-05-25 08:02:21,844 - INFO - Request Parameters - Page 33:
2025-05-25 08:02:21,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:21,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800267, 1743955200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:22,297 - INFO - API请求耗时: 453ms
2025-05-25 08:02:22,297 - INFO - Response - Page 33
2025-05-25 08:02:22,297 - INFO - 第 33 页获取到 17 条记录
2025-05-25 08:02:22,297 - INFO - 查询完成，共获取到 3217 条记录
2025-05-25 08:02:22,297 - INFO - 分段 2 查询成功，获取到 3217 条记录
2025-05-25 08:02:23,313 - INFO - 查询分段 3: 2025-04-08 至 2025-04-14
2025-05-25 08:02:23,313 - INFO - 查询日期范围: 2025-04-08 至 2025-04-14，使用分页查询，每页 100 条记录
2025-05-25 08:02:23,313 - INFO - Request Parameters - Page 1:
2025-05-25 08:02:23,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:23,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:24,016 - INFO - API请求耗时: 703ms
2025-05-25 08:02:24,016 - INFO - Response - Page 1
2025-05-25 08:02:24,016 - INFO - 第 1 页获取到 100 条记录
2025-05-25 08:02:24,516 - INFO - Request Parameters - Page 2:
2025-05-25 08:02:24,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:24,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:25,250 - INFO - API请求耗时: 734ms
2025-05-25 08:02:25,250 - INFO - Response - Page 2
2025-05-25 08:02:25,250 - INFO - 第 2 页获取到 100 条记录
2025-05-25 08:02:25,766 - INFO - Request Parameters - Page 3:
2025-05-25 08:02:25,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:25,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:26,657 - INFO - API请求耗时: 891ms
2025-05-25 08:02:26,657 - INFO - Response - Page 3
2025-05-25 08:02:26,672 - INFO - 第 3 页获取到 100 条记录
2025-05-25 08:02:27,172 - INFO - Request Parameters - Page 4:
2025-05-25 08:02:27,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:27,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:27,860 - INFO - API请求耗时: 687ms
2025-05-25 08:02:27,860 - INFO - Response - Page 4
2025-05-25 08:02:27,860 - INFO - 第 4 页获取到 100 条记录
2025-05-25 08:02:28,360 - INFO - Request Parameters - Page 5:
2025-05-25 08:02:28,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:28,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:29,094 - INFO - API请求耗时: 734ms
2025-05-25 08:02:29,094 - INFO - Response - Page 5
2025-05-25 08:02:29,094 - INFO - 第 5 页获取到 100 条记录
2025-05-25 08:02:29,594 - INFO - Request Parameters - Page 6:
2025-05-25 08:02:29,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:29,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:30,313 - INFO - API请求耗时: 719ms
2025-05-25 08:02:30,313 - INFO - Response - Page 6
2025-05-25 08:02:30,313 - INFO - 第 6 页获取到 100 条记录
2025-05-25 08:02:30,828 - INFO - Request Parameters - Page 7:
2025-05-25 08:02:30,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:30,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:31,547 - INFO - API请求耗时: 719ms
2025-05-25 08:02:31,547 - INFO - Response - Page 7
2025-05-25 08:02:31,547 - INFO - 第 7 页获取到 100 条记录
2025-05-25 08:02:32,047 - INFO - Request Parameters - Page 8:
2025-05-25 08:02:32,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:32,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:32,750 - INFO - API请求耗时: 703ms
2025-05-25 08:02:32,750 - INFO - Response - Page 8
2025-05-25 08:02:32,750 - INFO - 第 8 页获取到 100 条记录
2025-05-25 08:02:33,250 - INFO - Request Parameters - Page 9:
2025-05-25 08:02:33,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:33,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:34,016 - INFO - API请求耗时: 766ms
2025-05-25 08:02:34,016 - INFO - Response - Page 9
2025-05-25 08:02:34,016 - INFO - 第 9 页获取到 100 条记录
2025-05-25 08:02:34,516 - INFO - Request Parameters - Page 10:
2025-05-25 08:02:34,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:34,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:35,203 - INFO - API请求耗时: 687ms
2025-05-25 08:02:35,203 - INFO - Response - Page 10
2025-05-25 08:02:35,203 - INFO - 第 10 页获取到 100 条记录
2025-05-25 08:02:35,703 - INFO - Request Parameters - Page 11:
2025-05-25 08:02:35,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:35,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:36,422 - INFO - API请求耗时: 719ms
2025-05-25 08:02:36,422 - INFO - Response - Page 11
2025-05-25 08:02:36,422 - INFO - 第 11 页获取到 100 条记录
2025-05-25 08:02:36,938 - INFO - Request Parameters - Page 12:
2025-05-25 08:02:36,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:36,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:37,578 - INFO - API请求耗时: 641ms
2025-05-25 08:02:37,578 - INFO - Response - Page 12
2025-05-25 08:02:37,578 - INFO - 第 12 页获取到 100 条记录
2025-05-25 08:02:38,078 - INFO - Request Parameters - Page 13:
2025-05-25 08:02:38,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:38,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:38,750 - INFO - API请求耗时: 672ms
2025-05-25 08:02:38,750 - INFO - Response - Page 13
2025-05-25 08:02:38,750 - INFO - 第 13 页获取到 100 条记录
2025-05-25 08:02:39,250 - INFO - Request Parameters - Page 14:
2025-05-25 08:02:39,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:39,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:40,016 - INFO - API请求耗时: 766ms
2025-05-25 08:02:40,016 - INFO - Response - Page 14
2025-05-25 08:02:40,016 - INFO - 第 14 页获取到 100 条记录
2025-05-25 08:02:40,516 - INFO - Request Parameters - Page 15:
2025-05-25 08:02:40,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:40,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:41,281 - INFO - API请求耗时: 766ms
2025-05-25 08:02:41,281 - INFO - Response - Page 15
2025-05-25 08:02:41,281 - INFO - 第 15 页获取到 100 条记录
2025-05-25 08:02:41,797 - INFO - Request Parameters - Page 16:
2025-05-25 08:02:41,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:41,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:42,469 - INFO - API请求耗时: 672ms
2025-05-25 08:02:42,469 - INFO - Response - Page 16
2025-05-25 08:02:42,469 - INFO - 第 16 页获取到 100 条记录
2025-05-25 08:02:42,985 - INFO - Request Parameters - Page 17:
2025-05-25 08:02:42,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:42,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:43,703 - INFO - API请求耗时: 719ms
2025-05-25 08:02:43,703 - INFO - Response - Page 17
2025-05-25 08:02:43,703 - INFO - 第 17 页获取到 100 条记录
2025-05-25 08:02:44,219 - INFO - Request Parameters - Page 18:
2025-05-25 08:02:44,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:44,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:44,891 - INFO - API请求耗时: 672ms
2025-05-25 08:02:44,891 - INFO - Response - Page 18
2025-05-25 08:02:44,906 - INFO - 第 18 页获取到 100 条记录
2025-05-25 08:02:45,422 - INFO - Request Parameters - Page 19:
2025-05-25 08:02:45,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:45,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:46,110 - INFO - API请求耗时: 672ms
2025-05-25 08:02:46,110 - INFO - Response - Page 19
2025-05-25 08:02:46,110 - INFO - 第 19 页获取到 100 条记录
2025-05-25 08:02:46,625 - INFO - Request Parameters - Page 20:
2025-05-25 08:02:46,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:46,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:47,344 - INFO - API请求耗时: 719ms
2025-05-25 08:02:47,344 - INFO - Response - Page 20
2025-05-25 08:02:47,344 - INFO - 第 20 页获取到 100 条记录
2025-05-25 08:02:47,860 - INFO - Request Parameters - Page 21:
2025-05-25 08:02:47,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:47,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:48,469 - INFO - API请求耗时: 609ms
2025-05-25 08:02:48,469 - INFO - Response - Page 21
2025-05-25 08:02:48,469 - INFO - 第 21 页获取到 100 条记录
2025-05-25 08:02:48,969 - INFO - Request Parameters - Page 22:
2025-05-25 08:02:48,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:48,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:49,766 - INFO - API请求耗时: 797ms
2025-05-25 08:02:49,766 - INFO - Response - Page 22
2025-05-25 08:02:49,766 - INFO - 第 22 页获取到 100 条记录
2025-05-25 08:02:50,281 - INFO - Request Parameters - Page 23:
2025-05-25 08:02:50,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:50,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:50,969 - INFO - API请求耗时: 687ms
2025-05-25 08:02:50,969 - INFO - Response - Page 23
2025-05-25 08:02:50,969 - INFO - 第 23 页获取到 100 条记录
2025-05-25 08:02:51,485 - INFO - Request Parameters - Page 24:
2025-05-25 08:02:51,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:51,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:52,203 - INFO - API请求耗时: 719ms
2025-05-25 08:02:52,203 - INFO - Response - Page 24
2025-05-25 08:02:52,203 - INFO - 第 24 页获取到 100 条记录
2025-05-25 08:02:52,719 - INFO - Request Parameters - Page 25:
2025-05-25 08:02:52,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:52,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:53,344 - INFO - API请求耗时: 625ms
2025-05-25 08:02:53,344 - INFO - Response - Page 25
2025-05-25 08:02:53,344 - INFO - 第 25 页获取到 100 条记录
2025-05-25 08:02:53,844 - INFO - Request Parameters - Page 26:
2025-05-25 08:02:53,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:53,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:54,484 - INFO - API请求耗时: 641ms
2025-05-25 08:02:54,500 - INFO - Response - Page 26
2025-05-25 08:02:54,500 - INFO - 第 26 页获取到 100 条记录
2025-05-25 08:02:55,000 - INFO - Request Parameters - Page 27:
2025-05-25 08:02:55,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:55,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:55,672 - INFO - API请求耗时: 672ms
2025-05-25 08:02:55,672 - INFO - Response - Page 27
2025-05-25 08:02:55,672 - INFO - 第 27 页获取到 100 条记录
2025-05-25 08:02:56,172 - INFO - Request Parameters - Page 28:
2025-05-25 08:02:56,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:56,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:56,922 - INFO - API请求耗时: 750ms
2025-05-25 08:02:56,922 - INFO - Response - Page 28
2025-05-25 08:02:56,922 - INFO - 第 28 页获取到 100 条记录
2025-05-25 08:02:57,438 - INFO - Request Parameters - Page 29:
2025-05-25 08:02:57,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:57,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:58,359 - INFO - API请求耗时: 922ms
2025-05-25 08:02:58,359 - INFO - Response - Page 29
2025-05-25 08:02:58,359 - INFO - 第 29 页获取到 100 条记录
2025-05-25 08:02:58,875 - INFO - Request Parameters - Page 30:
2025-05-25 08:02:58,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:02:58,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:02:59,547 - INFO - API请求耗时: 672ms
2025-05-25 08:02:59,547 - INFO - Response - Page 30
2025-05-25 08:02:59,547 - INFO - 第 30 页获取到 100 条记录
2025-05-25 08:03:00,047 - INFO - Request Parameters - Page 31:
2025-05-25 08:03:00,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:00,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:00,844 - INFO - API请求耗时: 797ms
2025-05-25 08:03:00,844 - INFO - Response - Page 31
2025-05-25 08:03:00,844 - INFO - 第 31 页获取到 100 条记录
2025-05-25 08:03:01,359 - INFO - Request Parameters - Page 32:
2025-05-25 08:03:01,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:01,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:02,125 - INFO - API请求耗时: 766ms
2025-05-25 08:03:02,125 - INFO - Response - Page 32
2025-05-25 08:03:02,125 - INFO - 第 32 页获取到 100 条记录
2025-05-25 08:03:02,641 - INFO - Request Parameters - Page 33:
2025-05-25 08:03:02,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:02,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600267, 1744560000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:03,188 - INFO - API请求耗时: 547ms
2025-05-25 08:03:03,188 - INFO - Response - Page 33
2025-05-25 08:03:03,188 - INFO - 第 33 页获取到 27 条记录
2025-05-25 08:03:03,188 - INFO - 查询完成，共获取到 3227 条记录
2025-05-25 08:03:03,188 - INFO - 分段 3 查询成功，获取到 3227 条记录
2025-05-25 08:03:04,188 - INFO - 查询分段 4: 2025-04-15 至 2025-04-21
2025-05-25 08:03:04,188 - INFO - 查询日期范围: 2025-04-15 至 2025-04-21，使用分页查询，每页 100 条记录
2025-05-25 08:03:04,188 - INFO - Request Parameters - Page 1:
2025-05-25 08:03:04,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:04,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:04,828 - INFO - API请求耗时: 641ms
2025-05-25 08:03:04,828 - INFO - Response - Page 1
2025-05-25 08:03:04,828 - INFO - 第 1 页获取到 100 条记录
2025-05-25 08:03:05,344 - INFO - Request Parameters - Page 2:
2025-05-25 08:03:05,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:05,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:06,047 - INFO - API请求耗时: 688ms
2025-05-25 08:03:06,047 - INFO - Response - Page 2
2025-05-25 08:03:06,047 - INFO - 第 2 页获取到 100 条记录
2025-05-25 08:03:06,563 - INFO - Request Parameters - Page 3:
2025-05-25 08:03:06,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:06,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:07,266 - INFO - API请求耗时: 703ms
2025-05-25 08:03:07,266 - INFO - Response - Page 3
2025-05-25 08:03:07,266 - INFO - 第 3 页获取到 100 条记录
2025-05-25 08:03:07,781 - INFO - Request Parameters - Page 4:
2025-05-25 08:03:07,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:07,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:08,547 - INFO - API请求耗时: 766ms
2025-05-25 08:03:08,547 - INFO - Response - Page 4
2025-05-25 08:03:08,547 - INFO - 第 4 页获取到 100 条记录
2025-05-25 08:03:09,063 - INFO - Request Parameters - Page 5:
2025-05-25 08:03:09,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:09,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:09,781 - INFO - API请求耗时: 719ms
2025-05-25 08:03:09,781 - INFO - Response - Page 5
2025-05-25 08:03:09,797 - INFO - 第 5 页获取到 100 条记录
2025-05-25 08:03:10,313 - INFO - Request Parameters - Page 6:
2025-05-25 08:03:10,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:10,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:10,969 - INFO - API请求耗时: 656ms
2025-05-25 08:03:10,969 - INFO - Response - Page 6
2025-05-25 08:03:10,969 - INFO - 第 6 页获取到 100 条记录
2025-05-25 08:03:11,484 - INFO - Request Parameters - Page 7:
2025-05-25 08:03:11,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:11,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:12,250 - INFO - API请求耗时: 766ms
2025-05-25 08:03:12,250 - INFO - Response - Page 7
2025-05-25 08:03:12,250 - INFO - 第 7 页获取到 100 条记录
2025-05-25 08:03:12,766 - INFO - Request Parameters - Page 8:
2025-05-25 08:03:12,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:12,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:13,500 - INFO - API请求耗时: 734ms
2025-05-25 08:03:13,500 - INFO - Response - Page 8
2025-05-25 08:03:13,500 - INFO - 第 8 页获取到 100 条记录
2025-05-25 08:03:14,000 - INFO - Request Parameters - Page 9:
2025-05-25 08:03:14,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:14,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:14,688 - INFO - API请求耗时: 688ms
2025-05-25 08:03:14,688 - INFO - Response - Page 9
2025-05-25 08:03:14,688 - INFO - 第 9 页获取到 100 条记录
2025-05-25 08:03:15,203 - INFO - Request Parameters - Page 10:
2025-05-25 08:03:15,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:15,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:15,984 - INFO - API请求耗时: 781ms
2025-05-25 08:03:15,984 - INFO - Response - Page 10
2025-05-25 08:03:15,984 - INFO - 第 10 页获取到 100 条记录
2025-05-25 08:03:16,500 - INFO - Request Parameters - Page 11:
2025-05-25 08:03:16,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:16,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:17,219 - INFO - API请求耗时: 719ms
2025-05-25 08:03:17,219 - INFO - Response - Page 11
2025-05-25 08:03:17,219 - INFO - 第 11 页获取到 100 条记录
2025-05-25 08:03:17,734 - INFO - Request Parameters - Page 12:
2025-05-25 08:03:17,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:17,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:18,422 - INFO - API请求耗时: 687ms
2025-05-25 08:03:18,422 - INFO - Response - Page 12
2025-05-25 08:03:18,422 - INFO - 第 12 页获取到 100 条记录
2025-05-25 08:03:18,937 - INFO - Request Parameters - Page 13:
2025-05-25 08:03:18,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:18,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:19,547 - INFO - API请求耗时: 609ms
2025-05-25 08:03:19,547 - INFO - Response - Page 13
2025-05-25 08:03:19,547 - INFO - 第 13 页获取到 100 条记录
2025-05-25 08:03:20,047 - INFO - Request Parameters - Page 14:
2025-05-25 08:03:20,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:20,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:20,812 - INFO - API请求耗时: 766ms
2025-05-25 08:03:20,812 - INFO - Response - Page 14
2025-05-25 08:03:20,812 - INFO - 第 14 页获取到 100 条记录
2025-05-25 08:03:21,328 - INFO - Request Parameters - Page 15:
2025-05-25 08:03:21,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:21,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:22,000 - INFO - API请求耗时: 672ms
2025-05-25 08:03:22,000 - INFO - Response - Page 15
2025-05-25 08:03:22,000 - INFO - 第 15 页获取到 100 条记录
2025-05-25 08:03:22,516 - INFO - Request Parameters - Page 16:
2025-05-25 08:03:22,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:22,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:23,297 - INFO - API请求耗时: 781ms
2025-05-25 08:03:23,297 - INFO - Response - Page 16
2025-05-25 08:03:23,297 - INFO - 第 16 页获取到 100 条记录
2025-05-25 08:03:23,797 - INFO - Request Parameters - Page 17:
2025-05-25 08:03:23,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:23,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:24,484 - INFO - API请求耗时: 687ms
2025-05-25 08:03:24,484 - INFO - Response - Page 17
2025-05-25 08:03:24,484 - INFO - 第 17 页获取到 100 条记录
2025-05-25 08:03:25,000 - INFO - Request Parameters - Page 18:
2025-05-25 08:03:25,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:25,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:25,641 - INFO - API请求耗时: 641ms
2025-05-25 08:03:25,641 - INFO - Response - Page 18
2025-05-25 08:03:25,641 - INFO - 第 18 页获取到 100 条记录
2025-05-25 08:03:26,141 - INFO - Request Parameters - Page 19:
2025-05-25 08:03:26,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:26,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:26,969 - INFO - API请求耗时: 828ms
2025-05-25 08:03:26,969 - INFO - Response - Page 19
2025-05-25 08:03:26,969 - INFO - 第 19 页获取到 100 条记录
2025-05-25 08:03:27,484 - INFO - Request Parameters - Page 20:
2025-05-25 08:03:27,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:27,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:28,219 - INFO - API请求耗时: 734ms
2025-05-25 08:03:28,219 - INFO - Response - Page 20
2025-05-25 08:03:28,219 - INFO - 第 20 页获取到 100 条记录
2025-05-25 08:03:28,719 - INFO - Request Parameters - Page 21:
2025-05-25 08:03:28,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:28,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:29,406 - INFO - API请求耗时: 687ms
2025-05-25 08:03:29,406 - INFO - Response - Page 21
2025-05-25 08:03:29,406 - INFO - 第 21 页获取到 100 条记录
2025-05-25 08:03:29,906 - INFO - Request Parameters - Page 22:
2025-05-25 08:03:29,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:29,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:30,547 - INFO - API请求耗时: 641ms
2025-05-25 08:03:30,547 - INFO - Response - Page 22
2025-05-25 08:03:30,547 - INFO - 第 22 页获取到 100 条记录
2025-05-25 08:03:31,062 - INFO - Request Parameters - Page 23:
2025-05-25 08:03:31,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:31,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:31,703 - INFO - API请求耗时: 641ms
2025-05-25 08:03:31,703 - INFO - Response - Page 23
2025-05-25 08:03:31,703 - INFO - 第 23 页获取到 100 条记录
2025-05-25 08:03:32,219 - INFO - Request Parameters - Page 24:
2025-05-25 08:03:32,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:32,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:32,953 - INFO - API请求耗时: 734ms
2025-05-25 08:03:32,953 - INFO - Response - Page 24
2025-05-25 08:03:32,953 - INFO - 第 24 页获取到 100 条记录
2025-05-25 08:03:33,469 - INFO - Request Parameters - Page 25:
2025-05-25 08:03:33,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:33,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:34,094 - INFO - API请求耗时: 625ms
2025-05-25 08:03:34,109 - INFO - Response - Page 25
2025-05-25 08:03:34,109 - INFO - 第 25 页获取到 100 条记录
2025-05-25 08:03:34,625 - INFO - Request Parameters - Page 26:
2025-05-25 08:03:34,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:34,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:35,531 - INFO - API请求耗时: 906ms
2025-05-25 08:03:35,531 - INFO - Response - Page 26
2025-05-25 08:03:35,531 - INFO - 第 26 页获取到 100 条记录
2025-05-25 08:03:36,031 - INFO - Request Parameters - Page 27:
2025-05-25 08:03:36,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:36,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:36,719 - INFO - API请求耗时: 687ms
2025-05-25 08:03:36,719 - INFO - Response - Page 27
2025-05-25 08:03:36,719 - INFO - 第 27 页获取到 100 条记录
2025-05-25 08:03:37,219 - INFO - Request Parameters - Page 28:
2025-05-25 08:03:37,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:37,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:37,953 - INFO - API请求耗时: 734ms
2025-05-25 08:03:37,953 - INFO - Response - Page 28
2025-05-25 08:03:37,953 - INFO - 第 28 页获取到 100 条记录
2025-05-25 08:03:38,469 - INFO - Request Parameters - Page 29:
2025-05-25 08:03:38,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:38,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:39,140 - INFO - API请求耗时: 672ms
2025-05-25 08:03:39,140 - INFO - Response - Page 29
2025-05-25 08:03:39,140 - INFO - 第 29 页获取到 100 条记录
2025-05-25 08:03:39,656 - INFO - Request Parameters - Page 30:
2025-05-25 08:03:39,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:39,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:40,375 - INFO - API请求耗时: 719ms
2025-05-25 08:03:40,375 - INFO - Response - Page 30
2025-05-25 08:03:40,375 - INFO - 第 30 页获取到 100 条记录
2025-05-25 08:03:40,890 - INFO - Request Parameters - Page 31:
2025-05-25 08:03:40,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:40,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:41,578 - INFO - API请求耗时: 687ms
2025-05-25 08:03:41,578 - INFO - Response - Page 31
2025-05-25 08:03:41,578 - INFO - 第 31 页获取到 100 条记录
2025-05-25 08:03:42,094 - INFO - Request Parameters - Page 32:
2025-05-25 08:03:42,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:42,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:42,812 - INFO - API请求耗时: 719ms
2025-05-25 08:03:42,812 - INFO - Response - Page 32
2025-05-25 08:03:42,812 - INFO - 第 32 页获取到 100 条记录
2025-05-25 08:03:43,312 - INFO - Request Parameters - Page 33:
2025-05-25 08:03:43,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:43,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400267, 1745164800267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:43,812 - INFO - API请求耗时: 500ms
2025-05-25 08:03:43,812 - INFO - Response - Page 33
2025-05-25 08:03:43,812 - INFO - 第 33 页获取到 17 条记录
2025-05-25 08:03:43,812 - INFO - 查询完成，共获取到 3217 条记录
2025-05-25 08:03:43,812 - INFO - 分段 4 查询成功，获取到 3217 条记录
2025-05-25 08:03:44,828 - INFO - 查询分段 5: 2025-04-22 至 2025-04-28
2025-05-25 08:03:44,828 - INFO - 查询日期范围: 2025-04-22 至 2025-04-28，使用分页查询，每页 100 条记录
2025-05-25 08:03:44,828 - INFO - Request Parameters - Page 1:
2025-05-25 08:03:44,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:44,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:45,500 - INFO - API请求耗时: 672ms
2025-05-25 08:03:45,500 - INFO - Response - Page 1
2025-05-25 08:03:45,500 - INFO - 第 1 页获取到 100 条记录
2025-05-25 08:03:46,000 - INFO - Request Parameters - Page 2:
2025-05-25 08:03:46,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:46,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:46,734 - INFO - API请求耗时: 734ms
2025-05-25 08:03:46,734 - INFO - Response - Page 2
2025-05-25 08:03:46,734 - INFO - 第 2 页获取到 100 条记录
2025-05-25 08:03:47,234 - INFO - Request Parameters - Page 3:
2025-05-25 08:03:47,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:47,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:48,062 - INFO - API请求耗时: 828ms
2025-05-25 08:03:48,062 - INFO - Response - Page 3
2025-05-25 08:03:48,062 - INFO - 第 3 页获取到 100 条记录
2025-05-25 08:03:48,578 - INFO - Request Parameters - Page 4:
2025-05-25 08:03:48,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:48,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:49,297 - INFO - API请求耗时: 719ms
2025-05-25 08:03:49,297 - INFO - Response - Page 4
2025-05-25 08:03:49,297 - INFO - 第 4 页获取到 100 条记录
2025-05-25 08:03:49,797 - INFO - Request Parameters - Page 5:
2025-05-25 08:03:49,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:49,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:50,531 - INFO - API请求耗时: 734ms
2025-05-25 08:03:50,531 - INFO - Response - Page 5
2025-05-25 08:03:50,531 - INFO - 第 5 页获取到 100 条记录
2025-05-25 08:03:51,031 - INFO - Request Parameters - Page 6:
2025-05-25 08:03:51,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:51,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:51,765 - INFO - API请求耗时: 734ms
2025-05-25 08:03:51,765 - INFO - Response - Page 6
2025-05-25 08:03:51,765 - INFO - 第 6 页获取到 100 条记录
2025-05-25 08:03:52,281 - INFO - Request Parameters - Page 7:
2025-05-25 08:03:52,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:52,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:53,015 - INFO - API请求耗时: 734ms
2025-05-25 08:03:53,015 - INFO - Response - Page 7
2025-05-25 08:03:53,015 - INFO - 第 7 页获取到 100 条记录
2025-05-25 08:03:53,531 - INFO - Request Parameters - Page 8:
2025-05-25 08:03:53,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:53,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:54,125 - INFO - API请求耗时: 594ms
2025-05-25 08:03:54,125 - INFO - Response - Page 8
2025-05-25 08:03:54,125 - INFO - 第 8 页获取到 100 条记录
2025-05-25 08:03:54,640 - INFO - Request Parameters - Page 9:
2025-05-25 08:03:54,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:54,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:55,297 - INFO - API请求耗时: 656ms
2025-05-25 08:03:55,297 - INFO - Response - Page 9
2025-05-25 08:03:55,297 - INFO - 第 9 页获取到 100 条记录
2025-05-25 08:03:55,812 - INFO - Request Parameters - Page 10:
2025-05-25 08:03:55,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:55,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:56,484 - INFO - API请求耗时: 672ms
2025-05-25 08:03:56,484 - INFO - Response - Page 10
2025-05-25 08:03:56,484 - INFO - 第 10 页获取到 100 条记录
2025-05-25 08:03:57,000 - INFO - Request Parameters - Page 11:
2025-05-25 08:03:57,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:57,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:57,672 - INFO - API请求耗时: 672ms
2025-05-25 08:03:57,687 - INFO - Response - Page 11
2025-05-25 08:03:57,687 - INFO - 第 11 页获取到 100 条记录
2025-05-25 08:03:58,203 - INFO - Request Parameters - Page 12:
2025-05-25 08:03:58,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:58,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:03:58,890 - INFO - API请求耗时: 687ms
2025-05-25 08:03:58,890 - INFO - Response - Page 12
2025-05-25 08:03:58,890 - INFO - 第 12 页获取到 100 条记录
2025-05-25 08:03:59,390 - INFO - Request Parameters - Page 13:
2025-05-25 08:03:59,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:03:59,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:00,062 - INFO - API请求耗时: 672ms
2025-05-25 08:04:00,062 - INFO - Response - Page 13
2025-05-25 08:04:00,062 - INFO - 第 13 页获取到 100 条记录
2025-05-25 08:04:00,578 - INFO - Request Parameters - Page 14:
2025-05-25 08:04:00,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:00,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:01,187 - INFO - API请求耗时: 609ms
2025-05-25 08:04:01,187 - INFO - Response - Page 14
2025-05-25 08:04:01,187 - INFO - 第 14 页获取到 100 条记录
2025-05-25 08:04:01,687 - INFO - Request Parameters - Page 15:
2025-05-25 08:04:01,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:01,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:02,406 - INFO - API请求耗时: 719ms
2025-05-25 08:04:02,406 - INFO - Response - Page 15
2025-05-25 08:04:02,406 - INFO - 第 15 页获取到 100 条记录
2025-05-25 08:04:02,906 - INFO - Request Parameters - Page 16:
2025-05-25 08:04:02,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:02,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:03,797 - INFO - API请求耗时: 891ms
2025-05-25 08:04:03,797 - INFO - Response - Page 16
2025-05-25 08:04:03,797 - INFO - 第 16 页获取到 100 条记录
2025-05-25 08:04:04,297 - INFO - Request Parameters - Page 17:
2025-05-25 08:04:04,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:04,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:04,953 - INFO - API请求耗时: 656ms
2025-05-25 08:04:04,953 - INFO - Response - Page 17
2025-05-25 08:04:04,953 - INFO - 第 17 页获取到 100 条记录
2025-05-25 08:04:05,468 - INFO - Request Parameters - Page 18:
2025-05-25 08:04:05,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:05,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:06,109 - INFO - API请求耗时: 641ms
2025-05-25 08:04:06,109 - INFO - Response - Page 18
2025-05-25 08:04:06,125 - INFO - 第 18 页获取到 100 条记录
2025-05-25 08:04:06,640 - INFO - Request Parameters - Page 19:
2025-05-25 08:04:06,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:06,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:07,328 - INFO - API请求耗时: 687ms
2025-05-25 08:04:07,328 - INFO - Response - Page 19
2025-05-25 08:04:07,328 - INFO - 第 19 页获取到 100 条记录
2025-05-25 08:04:07,828 - INFO - Request Parameters - Page 20:
2025-05-25 08:04:07,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:07,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:08,500 - INFO - API请求耗时: 672ms
2025-05-25 08:04:08,500 - INFO - Response - Page 20
2025-05-25 08:04:08,500 - INFO - 第 20 页获取到 100 条记录
2025-05-25 08:04:09,015 - INFO - Request Parameters - Page 21:
2025-05-25 08:04:09,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:09,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:09,703 - INFO - API请求耗时: 687ms
2025-05-25 08:04:09,703 - INFO - Response - Page 21
2025-05-25 08:04:09,703 - INFO - 第 21 页获取到 100 条记录
2025-05-25 08:04:10,203 - INFO - Request Parameters - Page 22:
2025-05-25 08:04:10,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:10,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:10,921 - INFO - API请求耗时: 719ms
2025-05-25 08:04:10,921 - INFO - Response - Page 22
2025-05-25 08:04:10,921 - INFO - 第 22 页获取到 100 条记录
2025-05-25 08:04:11,422 - INFO - Request Parameters - Page 23:
2025-05-25 08:04:11,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:11,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:12,109 - INFO - API请求耗时: 687ms
2025-05-25 08:04:12,109 - INFO - Response - Page 23
2025-05-25 08:04:12,109 - INFO - 第 23 页获取到 100 条记录
2025-05-25 08:04:12,609 - INFO - Request Parameters - Page 24:
2025-05-25 08:04:12,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:12,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:13,375 - INFO - API请求耗时: 766ms
2025-05-25 08:04:13,375 - INFO - Response - Page 24
2025-05-25 08:04:13,375 - INFO - 第 24 页获取到 100 条记录
2025-05-25 08:04:13,890 - INFO - Request Parameters - Page 25:
2025-05-25 08:04:13,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:13,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:14,625 - INFO - API请求耗时: 734ms
2025-05-25 08:04:14,625 - INFO - Response - Page 25
2025-05-25 08:04:14,625 - INFO - 第 25 页获取到 100 条记录
2025-05-25 08:04:15,125 - INFO - Request Parameters - Page 26:
2025-05-25 08:04:15,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:15,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:15,937 - INFO - API请求耗时: 813ms
2025-05-25 08:04:15,937 - INFO - Response - Page 26
2025-05-25 08:04:15,937 - INFO - 第 26 页获取到 100 条记录
2025-05-25 08:04:16,437 - INFO - Request Parameters - Page 27:
2025-05-25 08:04:16,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:16,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:17,140 - INFO - API请求耗时: 703ms
2025-05-25 08:04:17,140 - INFO - Response - Page 27
2025-05-25 08:04:17,140 - INFO - 第 27 页获取到 100 条记录
2025-05-25 08:04:17,656 - INFO - Request Parameters - Page 28:
2025-05-25 08:04:17,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:17,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:18,468 - INFO - API请求耗时: 813ms
2025-05-25 08:04:18,468 - INFO - Response - Page 28
2025-05-25 08:04:18,468 - INFO - 第 28 页获取到 100 条记录
2025-05-25 08:04:18,984 - INFO - Request Parameters - Page 29:
2025-05-25 08:04:18,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:18,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:19,921 - INFO - API请求耗时: 937ms
2025-05-25 08:04:19,921 - INFO - Response - Page 29
2025-05-25 08:04:19,921 - INFO - 第 29 页获取到 100 条记录
2025-05-25 08:04:20,437 - INFO - Request Parameters - Page 30:
2025-05-25 08:04:20,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:20,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:21,125 - INFO - API请求耗时: 687ms
2025-05-25 08:04:21,125 - INFO - Response - Page 30
2025-05-25 08:04:21,125 - INFO - 第 30 页获取到 100 条记录
2025-05-25 08:04:21,640 - INFO - Request Parameters - Page 31:
2025-05-25 08:04:21,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:21,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:22,375 - INFO - API请求耗时: 734ms
2025-05-25 08:04:22,375 - INFO - Response - Page 31
2025-05-25 08:04:22,375 - INFO - 第 31 页获取到 100 条记录
2025-05-25 08:04:22,875 - INFO - Request Parameters - Page 32:
2025-05-25 08:04:22,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:22,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200267, 1745769600267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:23,515 - INFO - API请求耗时: 641ms
2025-05-25 08:04:23,515 - INFO - Response - Page 32
2025-05-25 08:04:23,515 - INFO - 第 32 页获取到 73 条记录
2025-05-25 08:04:23,515 - INFO - 查询完成，共获取到 3173 条记录
2025-05-25 08:04:23,515 - INFO - 分段 5 查询成功，获取到 3173 条记录
2025-05-25 08:04:24,531 - INFO - 查询分段 6: 2025-04-29 至 2025-05-05
2025-05-25 08:04:24,531 - INFO - 查询日期范围: 2025-04-29 至 2025-05-05，使用分页查询，每页 100 条记录
2025-05-25 08:04:24,531 - INFO - Request Parameters - Page 1:
2025-05-25 08:04:24,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:24,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:25,265 - INFO - API请求耗时: 734ms
2025-05-25 08:04:25,265 - INFO - Response - Page 1
2025-05-25 08:04:25,265 - INFO - 第 1 页获取到 100 条记录
2025-05-25 08:04:25,781 - INFO - Request Parameters - Page 2:
2025-05-25 08:04:25,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:25,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:26,468 - INFO - API请求耗时: 687ms
2025-05-25 08:04:26,468 - INFO - Response - Page 2
2025-05-25 08:04:26,468 - INFO - 第 2 页获取到 100 条记录
2025-05-25 08:04:26,968 - INFO - Request Parameters - Page 3:
2025-05-25 08:04:26,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:26,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:27,656 - INFO - API请求耗时: 688ms
2025-05-25 08:04:27,656 - INFO - Response - Page 3
2025-05-25 08:04:27,656 - INFO - 第 3 页获取到 100 条记录
2025-05-25 08:04:28,156 - INFO - Request Parameters - Page 4:
2025-05-25 08:04:28,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:28,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:28,843 - INFO - API请求耗时: 687ms
2025-05-25 08:04:28,843 - INFO - Response - Page 4
2025-05-25 08:04:28,859 - INFO - 第 4 页获取到 100 条记录
2025-05-25 08:04:29,375 - INFO - Request Parameters - Page 5:
2025-05-25 08:04:29,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:29,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:30,015 - INFO - API请求耗时: 641ms
2025-05-25 08:04:30,015 - INFO - Response - Page 5
2025-05-25 08:04:30,015 - INFO - 第 5 页获取到 100 条记录
2025-05-25 08:04:30,515 - INFO - Request Parameters - Page 6:
2025-05-25 08:04:30,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:30,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:31,171 - INFO - API请求耗时: 656ms
2025-05-25 08:04:31,171 - INFO - Response - Page 6
2025-05-25 08:04:31,171 - INFO - 第 6 页获取到 100 条记录
2025-05-25 08:04:31,687 - INFO - Request Parameters - Page 7:
2025-05-25 08:04:31,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:31,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:32,343 - INFO - API请求耗时: 656ms
2025-05-25 08:04:32,343 - INFO - Response - Page 7
2025-05-25 08:04:32,343 - INFO - 第 7 页获取到 100 条记录
2025-05-25 08:04:32,859 - INFO - Request Parameters - Page 8:
2025-05-25 08:04:32,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:32,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:33,499 - INFO - API请求耗时: 641ms
2025-05-25 08:04:33,499 - INFO - Response - Page 8
2025-05-25 08:04:33,499 - INFO - 第 8 页获取到 100 条记录
2025-05-25 08:04:33,999 - INFO - Request Parameters - Page 9:
2025-05-25 08:04:33,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:33,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:34,656 - INFO - API请求耗时: 656ms
2025-05-25 08:04:34,656 - INFO - Response - Page 9
2025-05-25 08:04:34,656 - INFO - 第 9 页获取到 100 条记录
2025-05-25 08:04:35,171 - INFO - Request Parameters - Page 10:
2025-05-25 08:04:35,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:35,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:36,109 - INFO - API请求耗时: 937ms
2025-05-25 08:04:36,109 - INFO - Response - Page 10
2025-05-25 08:04:36,109 - INFO - 第 10 页获取到 100 条记录
2025-05-25 08:04:36,624 - INFO - Request Parameters - Page 11:
2025-05-25 08:04:36,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:36,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:37,265 - INFO - API请求耗时: 641ms
2025-05-25 08:04:37,265 - INFO - Response - Page 11
2025-05-25 08:04:37,265 - INFO - 第 11 页获取到 100 条记录
2025-05-25 08:04:37,781 - INFO - Request Parameters - Page 12:
2025-05-25 08:04:37,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:37,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:38,578 - INFO - API请求耗时: 797ms
2025-05-25 08:04:38,578 - INFO - Response - Page 12
2025-05-25 08:04:38,593 - INFO - 第 12 页获取到 100 条记录
2025-05-25 08:04:39,109 - INFO - Request Parameters - Page 13:
2025-05-25 08:04:39,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:39,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:39,749 - INFO - API请求耗时: 641ms
2025-05-25 08:04:39,749 - INFO - Response - Page 13
2025-05-25 08:04:39,749 - INFO - 第 13 页获取到 100 条记录
2025-05-25 08:04:40,249 - INFO - Request Parameters - Page 14:
2025-05-25 08:04:40,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:40,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:40,984 - INFO - API请求耗时: 734ms
2025-05-25 08:04:40,984 - INFO - Response - Page 14
2025-05-25 08:04:40,984 - INFO - 第 14 页获取到 100 条记录
2025-05-25 08:04:41,499 - INFO - Request Parameters - Page 15:
2025-05-25 08:04:41,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:41,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:42,187 - INFO - API请求耗时: 688ms
2025-05-25 08:04:42,187 - INFO - Response - Page 15
2025-05-25 08:04:42,187 - INFO - 第 15 页获取到 100 条记录
2025-05-25 08:04:42,687 - INFO - Request Parameters - Page 16:
2025-05-25 08:04:42,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:42,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:43,390 - INFO - API请求耗时: 703ms
2025-05-25 08:04:43,390 - INFO - Response - Page 16
2025-05-25 08:04:43,390 - INFO - 第 16 页获取到 100 条记录
2025-05-25 08:04:43,890 - INFO - Request Parameters - Page 17:
2025-05-25 08:04:43,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:43,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:44,578 - INFO - API请求耗时: 687ms
2025-05-25 08:04:44,578 - INFO - Response - Page 17
2025-05-25 08:04:44,578 - INFO - 第 17 页获取到 100 条记录
2025-05-25 08:04:45,093 - INFO - Request Parameters - Page 18:
2025-05-25 08:04:45,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:45,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:45,796 - INFO - API请求耗时: 703ms
2025-05-25 08:04:45,796 - INFO - Response - Page 18
2025-05-25 08:04:45,796 - INFO - 第 18 页获取到 100 条记录
2025-05-25 08:04:46,312 - INFO - Request Parameters - Page 19:
2025-05-25 08:04:46,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:46,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:47,015 - INFO - API请求耗时: 703ms
2025-05-25 08:04:47,015 - INFO - Response - Page 19
2025-05-25 08:04:47,015 - INFO - 第 19 页获取到 100 条记录
2025-05-25 08:04:47,515 - INFO - Request Parameters - Page 20:
2025-05-25 08:04:47,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:47,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:48,203 - INFO - API请求耗时: 687ms
2025-05-25 08:04:48,203 - INFO - Response - Page 20
2025-05-25 08:04:48,203 - INFO - 第 20 页获取到 100 条记录
2025-05-25 08:04:48,703 - INFO - Request Parameters - Page 21:
2025-05-25 08:04:48,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:48,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:49,406 - INFO - API请求耗时: 703ms
2025-05-25 08:04:49,406 - INFO - Response - Page 21
2025-05-25 08:04:49,406 - INFO - 第 21 页获取到 100 条记录
2025-05-25 08:04:49,906 - INFO - Request Parameters - Page 22:
2025-05-25 08:04:49,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:49,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:50,593 - INFO - API请求耗时: 687ms
2025-05-25 08:04:50,593 - INFO - Response - Page 22
2025-05-25 08:04:50,593 - INFO - 第 22 页获取到 100 条记录
2025-05-25 08:04:51,093 - INFO - Request Parameters - Page 23:
2025-05-25 08:04:51,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:51,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:51,765 - INFO - API请求耗时: 672ms
2025-05-25 08:04:51,765 - INFO - Response - Page 23
2025-05-25 08:04:51,765 - INFO - 第 23 页获取到 100 条记录
2025-05-25 08:04:52,281 - INFO - Request Parameters - Page 24:
2025-05-25 08:04:52,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:52,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:52,906 - INFO - API请求耗时: 625ms
2025-05-25 08:04:52,906 - INFO - Response - Page 24
2025-05-25 08:04:52,906 - INFO - 第 24 页获取到 100 条记录
2025-05-25 08:04:53,406 - INFO - Request Parameters - Page 25:
2025-05-25 08:04:53,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:53,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:54,124 - INFO - API请求耗时: 719ms
2025-05-25 08:04:54,124 - INFO - Response - Page 25
2025-05-25 08:04:54,124 - INFO - 第 25 页获取到 100 条记录
2025-05-25 08:04:54,640 - INFO - Request Parameters - Page 26:
2025-05-25 08:04:54,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:54,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:55,343 - INFO - API请求耗时: 703ms
2025-05-25 08:04:55,343 - INFO - Response - Page 26
2025-05-25 08:04:55,343 - INFO - 第 26 页获取到 100 条记录
2025-05-25 08:04:55,843 - INFO - Request Parameters - Page 27:
2025-05-25 08:04:55,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:55,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:56,499 - INFO - API请求耗时: 656ms
2025-05-25 08:04:56,515 - INFO - Response - Page 27
2025-05-25 08:04:56,515 - INFO - 第 27 页获取到 100 条记录
2025-05-25 08:04:57,031 - INFO - Request Parameters - Page 28:
2025-05-25 08:04:57,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:57,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:57,702 - INFO - API请求耗时: 672ms
2025-05-25 08:04:57,702 - INFO - Response - Page 28
2025-05-25 08:04:57,702 - INFO - 第 28 页获取到 100 条记录
2025-05-25 08:04:58,218 - INFO - Request Parameters - Page 29:
2025-05-25 08:04:58,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:58,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:04:59,031 - INFO - API请求耗时: 813ms
2025-05-25 08:04:59,031 - INFO - Response - Page 29
2025-05-25 08:04:59,031 - INFO - 第 29 页获取到 100 条记录
2025-05-25 08:04:59,546 - INFO - Request Parameters - Page 30:
2025-05-25 08:04:59,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:04:59,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:00,296 - INFO - API请求耗时: 750ms
2025-05-25 08:05:00,296 - INFO - Response - Page 30
2025-05-25 08:05:00,296 - INFO - 第 30 页获取到 100 条记录
2025-05-25 08:05:00,796 - INFO - Request Parameters - Page 31:
2025-05-25 08:05:00,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:00,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:01,546 - INFO - API请求耗时: 750ms
2025-05-25 08:05:01,546 - INFO - Response - Page 31
2025-05-25 08:05:01,546 - INFO - 第 31 页获取到 100 条记录
2025-05-25 08:05:02,046 - INFO - Request Parameters - Page 32:
2025-05-25 08:05:02,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:02,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:02,921 - INFO - API请求耗时: 875ms
2025-05-25 08:05:02,921 - INFO - Response - Page 32
2025-05-25 08:05:02,921 - INFO - 第 32 页获取到 100 条记录
2025-05-25 08:05:03,437 - INFO - Request Parameters - Page 33:
2025-05-25 08:05:03,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:03,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:04,171 - INFO - API请求耗时: 734ms
2025-05-25 08:05:04,171 - INFO - Response - Page 33
2025-05-25 08:05:04,171 - INFO - 第 33 页获取到 100 条记录
2025-05-25 08:05:04,687 - INFO - Request Parameters - Page 34:
2025-05-25 08:05:04,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:04,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:05,468 - INFO - API请求耗时: 781ms
2025-05-25 08:05:05,468 - INFO - Response - Page 34
2025-05-25 08:05:05,468 - INFO - 第 34 页获取到 100 条记录
2025-05-25 08:05:05,968 - INFO - Request Parameters - Page 35:
2025-05-25 08:05:05,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:05,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:06,640 - INFO - API请求耗时: 672ms
2025-05-25 08:05:06,640 - INFO - Response - Page 35
2025-05-25 08:05:06,640 - INFO - 第 35 页获取到 100 条记录
2025-05-25 08:05:07,156 - INFO - Request Parameters - Page 36:
2025-05-25 08:05:07,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:07,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:07,968 - INFO - API请求耗时: 812ms
2025-05-25 08:05:07,968 - INFO - Response - Page 36
2025-05-25 08:05:07,968 - INFO - 第 36 页获取到 100 条记录
2025-05-25 08:05:08,468 - INFO - Request Parameters - Page 37:
2025-05-25 08:05:08,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:08,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:09,156 - INFO - API请求耗时: 687ms
2025-05-25 08:05:09,156 - INFO - Response - Page 37
2025-05-25 08:05:09,156 - INFO - 第 37 页获取到 100 条记录
2025-05-25 08:05:09,671 - INFO - Request Parameters - Page 38:
2025-05-25 08:05:09,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:09,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000267, 1746374400267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:10,343 - INFO - API请求耗时: 672ms
2025-05-25 08:05:10,343 - INFO - Response - Page 38
2025-05-25 08:05:10,343 - INFO - 第 38 页获取到 57 条记录
2025-05-25 08:05:10,343 - INFO - 查询完成，共获取到 3757 条记录
2025-05-25 08:05:10,343 - INFO - 分段 6 查询成功，获取到 3757 条记录
2025-05-25 08:05:11,359 - INFO - 查询分段 7: 2025-05-06 至 2025-05-12
2025-05-25 08:05:11,359 - INFO - 查询日期范围: 2025-05-06 至 2025-05-12，使用分页查询，每页 100 条记录
2025-05-25 08:05:11,359 - INFO - Request Parameters - Page 1:
2025-05-25 08:05:11,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:11,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:12,046 - INFO - API请求耗时: 688ms
2025-05-25 08:05:12,046 - INFO - Response - Page 1
2025-05-25 08:05:12,046 - INFO - 第 1 页获取到 100 条记录
2025-05-25 08:05:12,562 - INFO - Request Parameters - Page 2:
2025-05-25 08:05:12,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:12,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:13,280 - INFO - API请求耗时: 719ms
2025-05-25 08:05:13,280 - INFO - Response - Page 2
2025-05-25 08:05:13,280 - INFO - 第 2 页获取到 100 条记录
2025-05-25 08:05:13,796 - INFO - Request Parameters - Page 3:
2025-05-25 08:05:13,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:13,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:14,624 - INFO - API请求耗时: 828ms
2025-05-25 08:05:14,624 - INFO - Response - Page 3
2025-05-25 08:05:14,624 - INFO - 第 3 页获取到 100 条记录
2025-05-25 08:05:15,124 - INFO - Request Parameters - Page 4:
2025-05-25 08:05:15,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:15,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:15,827 - INFO - API请求耗时: 703ms
2025-05-25 08:05:15,827 - INFO - Response - Page 4
2025-05-25 08:05:15,827 - INFO - 第 4 页获取到 100 条记录
2025-05-25 08:05:16,343 - INFO - Request Parameters - Page 5:
2025-05-25 08:05:16,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:16,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:17,046 - INFO - API请求耗时: 703ms
2025-05-25 08:05:17,046 - INFO - Response - Page 5
2025-05-25 08:05:17,046 - INFO - 第 5 页获取到 100 条记录
2025-05-25 08:05:17,562 - INFO - Request Parameters - Page 6:
2025-05-25 08:05:17,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:17,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:18,265 - INFO - API请求耗时: 703ms
2025-05-25 08:05:18,265 - INFO - Response - Page 6
2025-05-25 08:05:18,265 - INFO - 第 6 页获取到 100 条记录
2025-05-25 08:05:18,765 - INFO - Request Parameters - Page 7:
2025-05-25 08:05:18,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:18,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:19,499 - INFO - API请求耗时: 734ms
2025-05-25 08:05:19,499 - INFO - Response - Page 7
2025-05-25 08:05:19,499 - INFO - 第 7 页获取到 100 条记录
2025-05-25 08:05:19,999 - INFO - Request Parameters - Page 8:
2025-05-25 08:05:19,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:19,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:20,734 - INFO - API请求耗时: 734ms
2025-05-25 08:05:20,734 - INFO - Response - Page 8
2025-05-25 08:05:20,734 - INFO - 第 8 页获取到 100 条记录
2025-05-25 08:05:21,249 - INFO - Request Parameters - Page 9:
2025-05-25 08:05:21,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:21,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:21,921 - INFO - API请求耗时: 672ms
2025-05-25 08:05:21,921 - INFO - Response - Page 9
2025-05-25 08:05:21,921 - INFO - 第 9 页获取到 100 条记录
2025-05-25 08:05:22,437 - INFO - Request Parameters - Page 10:
2025-05-25 08:05:22,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:22,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:23,109 - INFO - API请求耗时: 672ms
2025-05-25 08:05:23,109 - INFO - Response - Page 10
2025-05-25 08:05:23,109 - INFO - 第 10 页获取到 100 条记录
2025-05-25 08:05:23,624 - INFO - Request Parameters - Page 11:
2025-05-25 08:05:23,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:23,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:24,280 - INFO - API请求耗时: 656ms
2025-05-25 08:05:24,280 - INFO - Response - Page 11
2025-05-25 08:05:24,280 - INFO - 第 11 页获取到 100 条记录
2025-05-25 08:05:24,796 - INFO - Request Parameters - Page 12:
2025-05-25 08:05:24,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:24,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:25,530 - INFO - API请求耗时: 734ms
2025-05-25 08:05:25,530 - INFO - Response - Page 12
2025-05-25 08:05:25,530 - INFO - 第 12 页获取到 100 条记录
2025-05-25 08:05:26,030 - INFO - Request Parameters - Page 13:
2025-05-25 08:05:26,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:26,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:26,702 - INFO - API请求耗时: 672ms
2025-05-25 08:05:26,702 - INFO - Response - Page 13
2025-05-25 08:05:26,702 - INFO - 第 13 页获取到 100 条记录
2025-05-25 08:05:27,202 - INFO - Request Parameters - Page 14:
2025-05-25 08:05:27,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:27,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:27,890 - INFO - API请求耗时: 687ms
2025-05-25 08:05:27,890 - INFO - Response - Page 14
2025-05-25 08:05:27,890 - INFO - 第 14 页获取到 100 条记录
2025-05-25 08:05:28,390 - INFO - Request Parameters - Page 15:
2025-05-25 08:05:28,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:28,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:29,062 - INFO - API请求耗时: 672ms
2025-05-25 08:05:29,062 - INFO - Response - Page 15
2025-05-25 08:05:29,062 - INFO - 第 15 页获取到 100 条记录
2025-05-25 08:05:29,577 - INFO - Request Parameters - Page 16:
2025-05-25 08:05:29,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:29,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:30,312 - INFO - API请求耗时: 734ms
2025-05-25 08:05:30,312 - INFO - Response - Page 16
2025-05-25 08:05:30,312 - INFO - 第 16 页获取到 100 条记录
2025-05-25 08:05:30,827 - INFO - Request Parameters - Page 17:
2025-05-25 08:05:30,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:30,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:31,437 - INFO - API请求耗时: 609ms
2025-05-25 08:05:31,452 - INFO - Response - Page 17
2025-05-25 08:05:31,452 - INFO - 第 17 页获取到 100 条记录
2025-05-25 08:05:31,952 - INFO - Request Parameters - Page 18:
2025-05-25 08:05:31,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:31,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:32,765 - INFO - API请求耗时: 812ms
2025-05-25 08:05:32,765 - INFO - Response - Page 18
2025-05-25 08:05:32,765 - INFO - 第 18 页获取到 100 条记录
2025-05-25 08:05:33,265 - INFO - Request Parameters - Page 19:
2025-05-25 08:05:33,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:33,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:33,905 - INFO - API请求耗时: 641ms
2025-05-25 08:05:33,905 - INFO - Response - Page 19
2025-05-25 08:05:33,905 - INFO - 第 19 页获取到 100 条记录
2025-05-25 08:05:34,405 - INFO - Request Parameters - Page 20:
2025-05-25 08:05:34,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:34,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:35,030 - INFO - API请求耗时: 625ms
2025-05-25 08:05:35,030 - INFO - Response - Page 20
2025-05-25 08:05:35,030 - INFO - 第 20 页获取到 100 条记录
2025-05-25 08:05:35,530 - INFO - Request Parameters - Page 21:
2025-05-25 08:05:35,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:35,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:36,249 - INFO - API请求耗时: 719ms
2025-05-25 08:05:36,249 - INFO - Response - Page 21
2025-05-25 08:05:36,249 - INFO - 第 21 页获取到 100 条记录
2025-05-25 08:05:36,765 - INFO - Request Parameters - Page 22:
2025-05-25 08:05:36,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:36,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:37,515 - INFO - API请求耗时: 750ms
2025-05-25 08:05:37,515 - INFO - Response - Page 22
2025-05-25 08:05:37,515 - INFO - 第 22 页获取到 100 条记录
2025-05-25 08:05:38,030 - INFO - Request Parameters - Page 23:
2025-05-25 08:05:38,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:38,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:38,687 - INFO - API请求耗时: 656ms
2025-05-25 08:05:38,687 - INFO - Response - Page 23
2025-05-25 08:05:38,687 - INFO - 第 23 页获取到 100 条记录
2025-05-25 08:05:39,187 - INFO - Request Parameters - Page 24:
2025-05-25 08:05:39,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:39,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:39,905 - INFO - API请求耗时: 719ms
2025-05-25 08:05:39,905 - INFO - Response - Page 24
2025-05-25 08:05:39,905 - INFO - 第 24 页获取到 100 条记录
2025-05-25 08:05:40,405 - INFO - Request Parameters - Page 25:
2025-05-25 08:05:40,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:40,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:41,046 - INFO - API请求耗时: 641ms
2025-05-25 08:05:41,046 - INFO - Response - Page 25
2025-05-25 08:05:41,046 - INFO - 第 25 页获取到 100 条记录
2025-05-25 08:05:41,562 - INFO - Request Parameters - Page 26:
2025-05-25 08:05:41,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:41,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:42,265 - INFO - API请求耗时: 703ms
2025-05-25 08:05:42,265 - INFO - Response - Page 26
2025-05-25 08:05:42,265 - INFO - 第 26 页获取到 100 条记录
2025-05-25 08:05:42,765 - INFO - Request Parameters - Page 27:
2025-05-25 08:05:42,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:42,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:43,468 - INFO - API请求耗时: 703ms
2025-05-25 08:05:43,468 - INFO - Response - Page 27
2025-05-25 08:05:43,468 - INFO - 第 27 页获取到 100 条记录
2025-05-25 08:05:43,983 - INFO - Request Parameters - Page 28:
2025-05-25 08:05:43,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:43,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:44,624 - INFO - API请求耗时: 641ms
2025-05-25 08:05:44,624 - INFO - Response - Page 28
2025-05-25 08:05:44,624 - INFO - 第 28 页获取到 100 条记录
2025-05-25 08:05:45,140 - INFO - Request Parameters - Page 29:
2025-05-25 08:05:45,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:45,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:45,765 - INFO - API请求耗时: 625ms
2025-05-25 08:05:45,765 - INFO - Response - Page 29
2025-05-25 08:05:45,765 - INFO - 第 29 页获取到 100 条记录
2025-05-25 08:05:46,265 - INFO - Request Parameters - Page 30:
2025-05-25 08:05:46,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:46,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:46,937 - INFO - API请求耗时: 672ms
2025-05-25 08:05:46,937 - INFO - Response - Page 30
2025-05-25 08:05:46,937 - INFO - 第 30 页获取到 100 条记录
2025-05-25 08:05:47,437 - INFO - Request Parameters - Page 31:
2025-05-25 08:05:47,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:47,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:48,140 - INFO - API请求耗时: 703ms
2025-05-25 08:05:48,140 - INFO - Response - Page 31
2025-05-25 08:05:48,140 - INFO - 第 31 页获取到 100 条记录
2025-05-25 08:05:48,640 - INFO - Request Parameters - Page 32:
2025-05-25 08:05:48,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:48,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800267, 1746979200267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:49,218 - INFO - API请求耗时: 578ms
2025-05-25 08:05:49,218 - INFO - Response - Page 32
2025-05-25 08:05:49,218 - INFO - 第 32 页获取到 78 条记录
2025-05-25 08:05:49,218 - INFO - 查询完成，共获取到 3178 条记录
2025-05-25 08:05:49,218 - INFO - 分段 7 查询成功，获取到 3178 条记录
2025-05-25 08:05:50,218 - INFO - 查询分段 8: 2025-05-13 至 2025-05-19
2025-05-25 08:05:50,218 - INFO - 查询日期范围: 2025-05-13 至 2025-05-19，使用分页查询，每页 100 条记录
2025-05-25 08:05:50,218 - INFO - Request Parameters - Page 1:
2025-05-25 08:05:50,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:50,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:50,874 - INFO - API请求耗时: 656ms
2025-05-25 08:05:50,874 - INFO - Response - Page 1
2025-05-25 08:05:50,874 - INFO - 第 1 页获取到 100 条记录
2025-05-25 08:05:51,390 - INFO - Request Parameters - Page 2:
2025-05-25 08:05:51,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:51,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:52,108 - INFO - API请求耗时: 719ms
2025-05-25 08:05:52,108 - INFO - Response - Page 2
2025-05-25 08:05:52,108 - INFO - 第 2 页获取到 100 条记录
2025-05-25 08:05:52,624 - INFO - Request Parameters - Page 3:
2025-05-25 08:05:52,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:52,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:53,343 - INFO - API请求耗时: 719ms
2025-05-25 08:05:53,343 - INFO - Response - Page 3
2025-05-25 08:05:53,343 - INFO - 第 3 页获取到 100 条记录
2025-05-25 08:05:53,858 - INFO - Request Parameters - Page 4:
2025-05-25 08:05:53,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:53,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:54,483 - INFO - API请求耗时: 625ms
2025-05-25 08:05:54,483 - INFO - Response - Page 4
2025-05-25 08:05:54,483 - INFO - 第 4 页获取到 100 条记录
2025-05-25 08:05:54,983 - INFO - Request Parameters - Page 5:
2025-05-25 08:05:54,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:54,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:55,624 - INFO - API请求耗时: 641ms
2025-05-25 08:05:55,624 - INFO - Response - Page 5
2025-05-25 08:05:55,624 - INFO - 第 5 页获取到 100 条记录
2025-05-25 08:05:56,124 - INFO - Request Parameters - Page 6:
2025-05-25 08:05:56,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:56,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:56,843 - INFO - API请求耗时: 719ms
2025-05-25 08:05:56,843 - INFO - Response - Page 6
2025-05-25 08:05:56,843 - INFO - 第 6 页获取到 100 条记录
2025-05-25 08:05:57,343 - INFO - Request Parameters - Page 7:
2025-05-25 08:05:57,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:57,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:58,015 - INFO - API请求耗时: 672ms
2025-05-25 08:05:58,015 - INFO - Response - Page 7
2025-05-25 08:05:58,015 - INFO - 第 7 页获取到 100 条记录
2025-05-25 08:05:58,530 - INFO - Request Parameters - Page 8:
2025-05-25 08:05:58,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:58,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:05:59,202 - INFO - API请求耗时: 672ms
2025-05-25 08:05:59,202 - INFO - Response - Page 8
2025-05-25 08:05:59,202 - INFO - 第 8 页获取到 100 条记录
2025-05-25 08:05:59,718 - INFO - Request Parameters - Page 9:
2025-05-25 08:05:59,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:05:59,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:00,390 - INFO - API请求耗时: 672ms
2025-05-25 08:06:00,390 - INFO - Response - Page 9
2025-05-25 08:06:00,390 - INFO - 第 9 页获取到 100 条记录
2025-05-25 08:06:00,890 - INFO - Request Parameters - Page 10:
2025-05-25 08:06:00,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:00,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:01,577 - INFO - API请求耗时: 687ms
2025-05-25 08:06:01,577 - INFO - Response - Page 10
2025-05-25 08:06:01,577 - INFO - 第 10 页获取到 100 条记录
2025-05-25 08:06:02,077 - INFO - Request Parameters - Page 11:
2025-05-25 08:06:02,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:02,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:02,749 - INFO - API请求耗时: 672ms
2025-05-25 08:06:02,749 - INFO - Response - Page 11
2025-05-25 08:06:02,749 - INFO - 第 11 页获取到 100 条记录
2025-05-25 08:06:03,249 - INFO - Request Parameters - Page 12:
2025-05-25 08:06:03,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:03,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:03,999 - INFO - API请求耗时: 750ms
2025-05-25 08:06:03,999 - INFO - Response - Page 12
2025-05-25 08:06:03,999 - INFO - 第 12 页获取到 100 条记录
2025-05-25 08:06:04,515 - INFO - Request Parameters - Page 13:
2025-05-25 08:06:04,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:04,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:05,265 - INFO - API请求耗时: 750ms
2025-05-25 08:06:05,280 - INFO - Response - Page 13
2025-05-25 08:06:05,280 - INFO - 第 13 页获取到 100 条记录
2025-05-25 08:06:05,796 - INFO - Request Parameters - Page 14:
2025-05-25 08:06:05,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:05,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:06,561 - INFO - API请求耗时: 766ms
2025-05-25 08:06:06,561 - INFO - Response - Page 14
2025-05-25 08:06:06,561 - INFO - 第 14 页获取到 100 条记录
2025-05-25 08:06:07,061 - INFO - Request Parameters - Page 15:
2025-05-25 08:06:07,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:07,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:07,796 - INFO - API请求耗时: 734ms
2025-05-25 08:06:07,796 - INFO - Response - Page 15
2025-05-25 08:06:07,796 - INFO - 第 15 页获取到 100 条记录
2025-05-25 08:06:08,296 - INFO - Request Parameters - Page 16:
2025-05-25 08:06:08,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:08,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:09,015 - INFO - API请求耗时: 719ms
2025-05-25 08:06:09,015 - INFO - Response - Page 16
2025-05-25 08:06:09,015 - INFO - 第 16 页获取到 100 条记录
2025-05-25 08:06:09,530 - INFO - Request Parameters - Page 17:
2025-05-25 08:06:09,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:09,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:10,186 - INFO - API请求耗时: 656ms
2025-05-25 08:06:10,186 - INFO - Response - Page 17
2025-05-25 08:06:10,186 - INFO - 第 17 页获取到 100 条记录
2025-05-25 08:06:10,702 - INFO - Request Parameters - Page 18:
2025-05-25 08:06:10,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:10,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:11,358 - INFO - API请求耗时: 656ms
2025-05-25 08:06:11,358 - INFO - Response - Page 18
2025-05-25 08:06:11,358 - INFO - 第 18 页获取到 100 条记录
2025-05-25 08:06:11,874 - INFO - Request Parameters - Page 19:
2025-05-25 08:06:11,874 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:11,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:12,639 - INFO - API请求耗时: 766ms
2025-05-25 08:06:12,639 - INFO - Response - Page 19
2025-05-25 08:06:12,639 - INFO - 第 19 页获取到 100 条记录
2025-05-25 08:06:13,155 - INFO - Request Parameters - Page 20:
2025-05-25 08:06:13,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:13,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:13,874 - INFO - API请求耗时: 719ms
2025-05-25 08:06:13,874 - INFO - Response - Page 20
2025-05-25 08:06:13,874 - INFO - 第 20 页获取到 100 条记录
2025-05-25 08:06:14,389 - INFO - Request Parameters - Page 21:
2025-05-25 08:06:14,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:14,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:15,046 - INFO - API请求耗时: 656ms
2025-05-25 08:06:15,046 - INFO - Response - Page 21
2025-05-25 08:06:15,046 - INFO - 第 21 页获取到 100 条记录
2025-05-25 08:06:15,561 - INFO - Request Parameters - Page 22:
2025-05-25 08:06:15,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:15,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:16,233 - INFO - API请求耗时: 672ms
2025-05-25 08:06:16,233 - INFO - Response - Page 22
2025-05-25 08:06:16,233 - INFO - 第 22 页获取到 100 条记录
2025-05-25 08:06:16,749 - INFO - Request Parameters - Page 23:
2025-05-25 08:06:16,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:16,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:17,405 - INFO - API请求耗时: 656ms
2025-05-25 08:06:17,405 - INFO - Response - Page 23
2025-05-25 08:06:17,405 - INFO - 第 23 页获取到 100 条记录
2025-05-25 08:06:17,905 - INFO - Request Parameters - Page 24:
2025-05-25 08:06:17,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:17,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:18,561 - INFO - API请求耗时: 656ms
2025-05-25 08:06:18,561 - INFO - Response - Page 24
2025-05-25 08:06:18,561 - INFO - 第 24 页获取到 100 条记录
2025-05-25 08:06:19,077 - INFO - Request Parameters - Page 25:
2025-05-25 08:06:19,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:19,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:19,764 - INFO - API请求耗时: 688ms
2025-05-25 08:06:19,764 - INFO - Response - Page 25
2025-05-25 08:06:19,764 - INFO - 第 25 页获取到 100 条记录
2025-05-25 08:06:20,280 - INFO - Request Parameters - Page 26:
2025-05-25 08:06:20,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:20,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:20,952 - INFO - API请求耗时: 672ms
2025-05-25 08:06:20,952 - INFO - Response - Page 26
2025-05-25 08:06:20,952 - INFO - 第 26 页获取到 100 条记录
2025-05-25 08:06:21,468 - INFO - Request Parameters - Page 27:
2025-05-25 08:06:21,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:21,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600267, 1747584000267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:22,171 - INFO - API请求耗时: 703ms
2025-05-25 08:06:22,171 - INFO - Response - Page 27
2025-05-25 08:06:22,171 - INFO - 第 27 页获取到 98 条记录
2025-05-25 08:06:22,171 - INFO - 查询完成，共获取到 2698 条记录
2025-05-25 08:06:22,171 - INFO - 分段 8 查询成功，获取到 2698 条记录
2025-05-25 08:06:23,186 - INFO - 查询分段 9: 2025-05-20 至 2025-05-24
2025-05-25 08:06:23,186 - INFO - 查询日期范围: 2025-05-20 至 2025-05-24，使用分页查询，每页 100 条记录
2025-05-25 08:06:23,186 - INFO - Request Parameters - Page 1:
2025-05-25 08:06:23,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:23,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400267, 1748102399267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:23,921 - INFO - API请求耗时: 734ms
2025-05-25 08:06:23,921 - INFO - Response - Page 1
2025-05-25 08:06:23,921 - INFO - 第 1 页获取到 100 条记录
2025-05-25 08:06:24,421 - INFO - Request Parameters - Page 2:
2025-05-25 08:06:24,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:24,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400267, 1748102399267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:25,124 - INFO - API请求耗时: 703ms
2025-05-25 08:06:25,124 - INFO - Response - Page 2
2025-05-25 08:06:25,124 - INFO - 第 2 页获取到 100 条记录
2025-05-25 08:06:25,639 - INFO - Request Parameters - Page 3:
2025-05-25 08:06:25,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:25,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400267, 1748102399267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:26,311 - INFO - API请求耗时: 672ms
2025-05-25 08:06:26,311 - INFO - Response - Page 3
2025-05-25 08:06:26,311 - INFO - 第 3 页获取到 100 条记录
2025-05-25 08:06:26,827 - INFO - Request Parameters - Page 4:
2025-05-25 08:06:26,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:26,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400267, 1748102399267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:27,686 - INFO - API请求耗时: 859ms
2025-05-25 08:06:27,686 - INFO - Response - Page 4
2025-05-25 08:06:27,686 - INFO - 第 4 页获取到 100 条记录
2025-05-25 08:06:28,186 - INFO - Request Parameters - Page 5:
2025-05-25 08:06:28,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:28,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400267, 1748102399267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:28,874 - INFO - API请求耗时: 687ms
2025-05-25 08:06:28,874 - INFO - Response - Page 5
2025-05-25 08:06:28,874 - INFO - 第 5 页获取到 100 条记录
2025-05-25 08:06:29,389 - INFO - Request Parameters - Page 6:
2025-05-25 08:06:29,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:06:29,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400267, 1748102399267], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:06:30,046 - INFO - API请求耗时: 656ms
2025-05-25 08:06:30,046 - INFO - Response - Page 6
2025-05-25 08:06:30,046 - INFO - 第 6 页获取到 77 条记录
2025-05-25 08:06:30,046 - INFO - 查询完成，共获取到 577 条记录
2025-05-25 08:06:30,046 - INFO - 分段 9 查询成功，获取到 577 条记录
2025-05-25 08:06:31,061 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 26160 条记录，失败 0 次
2025-05-25 08:06:31,061 - INFO - 成功获取宜搭日销售表单数据，共 26160 条记录
2025-05-25 08:06:31,061 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-25 08:06:31,061 - INFO - 开始对比和同步日销售数据...
2025-05-25 08:06:31,796 - INFO - 成功创建宜搭日销售数据索引，共 10853 条记录
2025-05-25 08:06:31,796 - INFO - 开始处理数衍数据，共 13036 条记录
2025-05-25 08:06:32,296 - INFO - 更新表单数据成功: FINST-PPA6667126NVCEF1F460B7V2LS802850QUYAMN1
2025-05-25 08:06:32,296 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250405, 变更字段: [{'field': 'recommendAmount', 'old_value': 27815.44, 'new_value': 27569.44}, {'field': 'amount', 'old_value': 27815.44, 'new_value': 27569.44}, {'field': 'count', 'old_value': 75, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 25737.2, 'new_value': 25491.2}, {'field': 'instoreCount', 'old_value': 58, 'new_value': 57}]
2025-05-25 08:06:32,717 - INFO - 更新表单数据成功: FINST-DIC66I91SDKV9RGT733QXADDENIG3PF5QUYAMJB
2025-05-25 08:06:32,717 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250412, 变更字段: [{'field': 'recommendAmount', 'old_value': 26559.69, 'new_value': 25183.69}, {'field': 'amount', 'old_value': 26559.69, 'new_value': 25183.69}, {'field': 'count', 'old_value': 87, 'new_value': 83}, {'field': 'instoreAmount', 'old_value': 24000.0, 'new_value': 22624.0}, {'field': 'instoreCount', 'old_value': 66, 'new_value': 62}]
2025-05-25 08:06:33,249 - INFO - 更新表单数据成功: FINST-OY8665C15BKVKNUK6ITIG5QS2X9Z2ARAQUYAMUX
2025-05-25 08:06:33,249 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250419, 变更字段: [{'field': 'recommendAmount', 'old_value': 42497.8, 'new_value': 42139.8}, {'field': 'amount', 'old_value': 42497.8, 'new_value': 42139.8}, {'field': 'count', 'old_value': 98, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 39379.8, 'new_value': 39021.8}, {'field': 'instoreCount', 'old_value': 77, 'new_value': 76}]
2025-05-25 08:06:33,733 - INFO - 更新表单数据成功: FINST-E3G66QA11FHVOM8V6288C50S6EGT3G5XTGRAMFA
2025-05-25 08:06:33,733 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 17465.69, 'new_value': 16629.69}, {'field': 'amount', 'old_value': 17465.69, 'new_value': 16629.69}, {'field': 'count', 'old_value': 50, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 16736.0, 'new_value': 15900.0}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 41}]
2025-05-25 08:06:34,171 - INFO - 更新表单数据成功: FINST-DOA66K91PBKVB00NBC9919DDQ68D3AQIQUYAM161
2025-05-25 08:06:34,171 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250426, 变更字段: [{'field': 'recommendAmount', 'old_value': 55972.28, 'new_value': 55674.28}, {'field': 'amount', 'old_value': 55972.28, 'new_value': 55674.28}, {'field': 'count', 'old_value': 87, 'new_value': 86}, {'field': 'instoreAmount', 'old_value': 53162.6, 'new_value': 52864.6}, {'field': 'instoreCount', 'old_value': 68, 'new_value': 67}]
2025-05-25 08:06:34,655 - INFO - 更新表单数据成功: FINST-4OD66CC1Y00VA3W37N53L7ONWBWH257QRG7AMEN
2025-05-25 08:06:34,655 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_20250501, 变更字段: [{'field': 'amount', 'old_value': 13369.29, 'new_value': 13575.49}, {'field': 'count', 'old_value': 132, 'new_value': 133}, {'field': 'instoreAmount', 'old_value': 3849.77, 'new_value': 4055.97}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 26}]
2025-05-25 08:06:35,092 - INFO - 更新表单数据成功: FINST-00D66K71MMZU3HSX6YXER9AAT6A53J2C5W8AMZ81
2025-05-25 08:06:35,092 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 4351.6, 'new_value': 4429.6}, {'field': 'dailyBillAmount', 'old_value': 4351.6, 'new_value': 4429.6}]
2025-05-25 08:06:35,561 - INFO - 更新表单数据成功: FINST-L8D665C1WBHV8H2O8YHVBBVWE1SZ21CM3MYAM2A1
2025-05-25 08:06:35,561 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_20250503, 变更字段: [{'field': 'amount', 'old_value': 10595.7, 'new_value': 10728.8}, {'field': 'count', 'old_value': 41, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 10848.8, 'new_value': 10981.9}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 41}]
2025-05-25 08:06:35,983 - INFO - 更新表单数据成功: FINST-LFA66G911DKV1OGA75U1M50XFDGD3O707RVAMZ4
2025-05-25 08:06:35,983 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_20250501, 变更字段: [{'field': 'amount', 'old_value': 12908.0, 'new_value': 12441.0}, {'field': 'count', 'old_value': 55, 'new_value': 54}, {'field': 'instoreAmount', 'old_value': 12908.0, 'new_value': 12441.0}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 54}]
2025-05-25 08:06:36,436 - INFO - 更新表单数据成功: FINST-2PF66CD1C4MVU4UD8K99ICKTEH1H251OQUYAM93
2025-05-25 08:06:36,436 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250503, 变更字段: [{'field': 'recommendAmount', 'old_value': 53839.98, 'new_value': 53501.98}, {'field': 'amount', 'old_value': 53839.98, 'new_value': 53501.98}, {'field': 'count', 'old_value': 90, 'new_value': 89}, {'field': 'instoreAmount', 'old_value': 51383.0, 'new_value': 51045.0}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 68}]
2025-05-25 08:06:36,858 - INFO - 更新表单数据成功: FINST-NU966I81DCHVHQKKAL3N2D0K20162D65UGRAMB7
2025-05-25 08:06:36,858 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5790.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5790.0}]
2025-05-25 08:06:37,358 - INFO - 更新表单数据成功: FINST-LR5668B10BGVQHUZCR3SE4OP310X20MAD1QAMM1
2025-05-25 08:06:37,358 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250511, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10966.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10966.0}]
2025-05-25 08:06:37,858 - INFO - 更新表单数据成功: FINST-2PF66CD1C4MVU4UD8K99ICKTEH1H261OQUYAM85
2025-05-25 08:06:37,858 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250510, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8105.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8105.0}]
2025-05-25 08:06:38,342 - INFO - 更新表单数据成功: FINST-FSC66G81K7JVF5EV8UGNG4KAGEIV29WRQBUAMH9
2025-05-25 08:06:38,342 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_20250507, 变更字段: [{'field': 'amount', 'old_value': 1567.1, 'new_value': 1784.9}, {'field': 'count', 'old_value': 12, 'new_value': 13}, {'field': 'instoreAmount', 'old_value': 1525.6, 'new_value': 1743.4}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 10}]
2025-05-25 08:06:38,780 - INFO - 更新表单数据成功: FINST-GNC66E91KBGV4E2R7BB6L7DSAK1K2SADD1QAMF7
2025-05-25 08:06:38,780 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_20250511, 变更字段: [{'field': 'recommendAmount', 'old_value': 30290.26, 'new_value': 31109.26}, {'field': 'amount', 'old_value': 30290.26, 'new_value': 31109.26}, {'field': 'count', 'old_value': 60, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 30290.26, 'new_value': 31109.26}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 61}]
2025-05-25 08:06:39,202 - INFO - 更新表单数据成功: FINST-A17661C1HFHVUTF77C3YSA9LPDJ02N3DUGRAMD5
2025-05-25 08:06:39,202 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 11753.34, 'new_value': 11015.34}, {'field': 'amount', 'old_value': 11753.34, 'new_value': 11015.34}, {'field': 'count', 'old_value': 39, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 11090.0, 'new_value': 10352.0}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 35}]
2025-05-25 08:06:39,671 - INFO - 更新表单数据成功: FINST-7PF66BA1J9GVZXM8E8AW6DL1G51V33IID1QAMMC
2025-05-25 08:06:39,671 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250511, 变更字段: [{'field': 'recommendAmount', 'old_value': 34350.76, 'new_value': 32944.76}, {'field': 'amount', 'old_value': 34350.76, 'new_value': 32944.76}, {'field': 'count', 'old_value': 94, 'new_value': 92}, {'field': 'instoreAmount', 'old_value': 31333.0, 'new_value': 29927.0}, {'field': 'instoreCount', 'old_value': 71, 'new_value': 69}]
2025-05-25 08:06:40,171 - INFO - 更新表单数据成功: FINST-V7966QC1CCLVBMVHB6JLKDAS4E863OICK10BMOY
2025-05-25 08:06:40,171 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_20250518, 变更字段: [{'field': 'amount', 'old_value': 4773.36, 'new_value': 4778.36}, {'field': 'count', 'old_value': 66, 'new_value': 67}, {'field': 'instoreAmount', 'old_value': 1987.96, 'new_value': 1992.96}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 36}]
2025-05-25 08:06:40,655 - INFO - 更新表单数据成功: FINST-RI766091P9MVF7P0F84PR4IN1PPP3W1WQUYAMJ1
2025-05-25 08:06:40,655 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9350.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9350.0}]
2025-05-25 08:06:41,108 - INFO - 更新表单数据成功: FINST-1OC66A91A9MVE2TH6IGV36HCBWIH2J254MYAM4B
2025-05-25 08:06:41,108 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_20250517, 变更字段: [{'field': 'amount', 'old_value': 46011.0, 'new_value': 46548.0}, {'field': 'count', 'old_value': 161, 'new_value': 162}, {'field': 'instoreAmount', 'old_value': 46011.0, 'new_value': 46548.0}, {'field': 'instoreCount', 'old_value': 161, 'new_value': 162}]
2025-05-25 08:06:41,483 - INFO - 更新表单数据成功: FINST-7PF66CC13AKVLF84EWBD0AB9MYP92I3G7RVAMY2
2025-05-25 08:06:41,483 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_20250515, 变更字段: [{'field': 'amount', 'old_value': 28888.0, 'new_value': 28996.0}, {'field': 'count', 'old_value': 102, 'new_value': 103}, {'field': 'instoreAmount', 'old_value': 28888.0, 'new_value': 28996.0}, {'field': 'instoreCount', 'old_value': 102, 'new_value': 103}]
2025-05-25 08:06:41,827 - INFO - 更新表单数据成功: FINST-IQE66ZC1FKNVW4QR8MX848SEDAWK384NK10BMX9
2025-05-25 08:06:41,827 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 4371.91, 'new_value': 4365.88}, {'field': 'amount', 'old_value': 4371.91, 'new_value': 4365.88}]
2025-05-25 08:06:42,296 - INFO - 更新表单数据成功: FINST-KLF66WC1WCKVPOSACQFQZB4VN5ZK23QI7RVAMHB
2025-05-25 08:06:42,296 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 11819.0, 'new_value': 12182.68}, {'field': 'dailyBillAmount', 'old_value': 11819.0, 'new_value': 12182.68}]
2025-05-25 08:06:42,889 - INFO - 更新表单数据成功: FINST-IQE66ZC1FKNVW4QR8MX848SEDAWK384NK10BMDA
2025-05-25 08:06:42,889 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 20956.0, 'new_value': 21304.08}, {'field': 'dailyBillAmount', 'old_value': 20956.0, 'new_value': 21304.08}]
2025-05-25 08:06:43,296 - INFO - 更新表单数据成功: FINST-1OC66A91A9MVE2TH6IGV36HCBWIH2K254MYAMYB
2025-05-25 08:06:43,296 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 15257.9, 'new_value': 15335.9}, {'field': 'dailyBillAmount', 'old_value': 15257.9, 'new_value': 15335.9}]
2025-05-25 08:06:43,780 - INFO - 更新表单数据成功: FINST-NS866I913EKVN0TFBYL9WAYSIM8N3QXWN6XAMH8
2025-05-25 08:06:43,780 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 16901.8, 'new_value': 17013.66}, {'field': 'dailyBillAmount', 'old_value': 16901.8, 'new_value': 17013.66}]
2025-05-25 08:06:44,233 - INFO - 更新表单数据成功: FINST-1OC66A91A9MVE2TH6IGV36HCBWIH2K254MYAMOC
2025-05-25 08:06:44,233 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 28314.08, 'new_value': 22126.58}, {'field': 'dailyBillAmount', 'old_value': 28314.08, 'new_value': 22126.58}]
2025-05-25 08:06:44,670 - INFO - 更新表单数据成功: FINST-IQE66ZC1FKNVW4QR8MX848SEDAWK394NK10BM4B
2025-05-25 08:06:44,670 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 35906.27, 'new_value': 28314.08}, {'field': 'dailyBillAmount', 'old_value': 35906.27, 'new_value': 28314.08}]
2025-05-25 08:06:45,139 - INFO - 更新表单数据成功: FINST-1OC66A91A9MVE2TH6IGV36HCBWIH2K254MYAMPC
2025-05-25 08:06:45,139 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 40861.75, 'new_value': 35906.27}, {'field': 'dailyBillAmount', 'old_value': 40861.75, 'new_value': 35906.27}]
2025-05-25 08:06:45,624 - INFO - 更新表单数据成功: FINST-OJ666W71ZGNVO8CUDDBLV4TY7LIB31BV0H1BMS9
2025-05-25 08:06:45,624 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7382.3}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7382.3}]
2025-05-25 08:06:46,108 - INFO - 更新表单数据成功: FINST-OJ666W71ZGNVO8CUDDBLV4TY7LIB31BV0H1BMNA
2025-05-25 08:06:46,108 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_20250523, 变更字段: [{'field': 'amount', 'old_value': 7203.64, 'new_value': 7352.31}, {'field': 'count', 'old_value': 71, 'new_value': 72}, {'field': 'instoreAmount', 'old_value': 4617.36, 'new_value': 4766.03}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 38}]
2025-05-25 08:06:46,592 - INFO - 更新表单数据成功: FINST-OJ666W71ZGNVO8CUDDBLV4TY7LIB31BV0H1BM5B
2025-05-25 08:06:46,592 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_20250523, 变更字段: [{'field': 'amount', 'old_value': 2050.97, 'new_value': 2129.2799999999997}, {'field': 'count', 'old_value': 62, 'new_value': 63}, {'field': 'onlineAmount', 'old_value': 483.97, 'new_value': 562.28}, {'field': 'onlineCount', 'old_value': 15, 'new_value': 16}]
2025-05-25 08:06:47,077 - INFO - 更新表单数据成功: FINST-OJ666W71ZGNVO8CUDDBLV4TY7LIB31BV0H1BM7B
2025-05-25 08:06:47,077 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_20250523, 变更字段: [{'field': 'amount', 'old_value': 3576.2999999999997, 'new_value': 5286.299999999999}, {'field': 'count', 'old_value': 15, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 5676.2, 'new_value': 7386.2}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 16}]
2025-05-25 08:06:47,467 - INFO - 更新表单数据成功: FINST-OJ666W71ZGNVO8CUDDBLV4TY7LIB32BV0H1BMVB
2025-05-25 08:06:47,467 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_20250523, 变更字段: [{'field': 'amount', 'old_value': 4304.58, 'new_value': 4314.48}, {'field': 'count', 'old_value': 172, 'new_value': 173}, {'field': 'instoreAmount', 'old_value': 4333.18, 'new_value': 4343.08}, {'field': 'instoreCount', 'old_value': 172, 'new_value': 173}]
2025-05-25 08:06:48,061 - INFO - 更新表单数据成功: FINST-9EA669D1CGOV8GNQCGL8VBQHXT292UXX0H1BMI7
2025-05-25 08:06:48,061 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_20250523, 变更字段: [{'field': 'amount', 'old_value': 1013.54, 'new_value': 929.54}, {'field': 'count', 'old_value': 53, 'new_value': 54}, {'field': 'instoreAmount', 'old_value': 360.6, 'new_value': 402.6}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 19}]
2025-05-25 08:06:48,420 - INFO - 更新表单数据成功: FINST-9EA669D1CGOV8GNQCGL8VBQHXT292UXX0H1BMO7
2025-05-25 08:06:48,420 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_20250523, 变更字段: [{'field': 'amount', 'old_value': 1080.4, 'new_value': 1111.81}, {'field': 'count', 'old_value': 40, 'new_value': 42}, {'field': 'onlineAmount', 'old_value': 845.6, 'new_value': 877.01}, {'field': 'onlineCount', 'old_value': 31, 'new_value': 33}]
2025-05-25 08:06:48,920 - INFO - 更新表单数据成功: FINST-9EA669D1CGOV8GNQCGL8VBQHXT292UXX0H1BMX7
2025-05-25 08:06:48,920 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_20250523, 变更字段: [{'field': 'instoreAmount', 'old_value': 12793.1, 'new_value': 12731.8}, {'field': 'instoreCount', 'old_value': 297, 'new_value': 296}, {'field': 'onlineAmount', 'old_value': 395.6, 'new_value': 456.9}, {'field': 'onlineCount', 'old_value': 8, 'new_value': 9}]
2025-05-25 08:06:49,327 - INFO - 更新表单数据成功: FINST-9EA669D1CGOV8GNQCGL8VBQHXT292UXX0H1BMI8
2025-05-25 08:06:49,327 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 13036.68}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 13036.68}]
2025-05-25 08:06:49,733 - INFO - 更新表单数据成功: FINST-9EA669D1CGOV8GNQCGL8VBQHXT292UXX0H1BMR8
2025-05-25 08:06:49,733 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_20250523, 变更字段: [{'field': 'amount', 'old_value': 3138.0, 'new_value': 3743.0}, {'field': 'count', 'old_value': 7, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 3138.0, 'new_value': 3743.0}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 11}]
2025-05-25 08:06:50,202 - INFO - 更新表单数据成功: FINST-9EA669D1CGOV8GNQCGL8VBQHXT292UXX0H1BM29
2025-05-25 08:06:50,202 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1521.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1521.0}]
2025-05-25 08:06:50,733 - INFO - 更新表单数据成功: FINST-9EA669D1CGOV8GNQCGL8VBQHXT292VXX0H1BMI9
2025-05-25 08:06:50,733 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 2018.93, 'new_value': 2044.92}, {'field': 'amount', 'old_value': 2018.9299999999998, 'new_value': 2044.92}, {'field': 'count', 'old_value': 111, 'new_value': 113}, {'field': 'instoreAmount', 'old_value': 480.5, 'new_value': 499.49}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 19}, {'field': 'onlineAmount', 'old_value': 1568.43, 'new_value': 1575.43}, {'field': 'onlineCount', 'old_value': 93, 'new_value': 94}]
2025-05-25 08:06:51,342 - INFO - 更新表单数据成功: FINST-9EA669D1CGOV8GNQCGL8VBQHXT292VXX0H1BMW9
2025-05-25 08:06:51,342 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_20250523, 变更字段: [{'field': 'amount', 'old_value': 8523.220000000001, 'new_value': 8583.220000000001}, {'field': 'count', 'old_value': 251, 'new_value': 252}, {'field': 'instoreAmount', 'old_value': 8095.82, 'new_value': 8155.82}, {'field': 'instoreCount', 'old_value': 244, 'new_value': 245}]
2025-05-25 08:06:51,842 - INFO - 更新表单数据成功: FINST-DUF66091J1OVS5YV9PAXFCDYTPE62RRHK10BMF
2025-05-25 08:06:51,842 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_20250522, 变更字段: [{'field': 'amount', 'old_value': 5171.5, 'new_value': 5321.5}, {'field': 'count', 'old_value': 194, 'new_value': 195}, {'field': 'instoreAmount', 'old_value': 5039.1, 'new_value': 5189.1}, {'field': 'instoreCount', 'old_value': 187, 'new_value': 188}]
2025-05-25 08:06:52,358 - INFO - 更新表单数据成功: FINST-9EA669D1CGOV8GNQCGL8VBQHXT292VXX0H1BMZ9
2025-05-25 08:06:52,358 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1563.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1563.0}]
2025-05-25 08:06:52,811 - INFO - 更新表单数据成功: FINST-9EA669D1CGOV8GNQCGL8VBQHXT292VXX0H1BM3A
2025-05-25 08:06:52,811 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 6920.62, 'new_value': 7082.02}, {'field': 'amount', 'old_value': 6920.62, 'new_value': 7082.02}, {'field': 'count', 'old_value': 187, 'new_value': 190}, {'field': 'instoreAmount', 'old_value': 5744.62, 'new_value': 5906.02}, {'field': 'instoreCount', 'old_value': 156, 'new_value': 159}]
2025-05-25 08:06:53,249 - INFO - 更新表单数据成功: FINST-9EA669D1CGOV8GNQCGL8VBQHXT292VXX0H1BM5A
2025-05-25 08:06:53,249 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_20250523, 变更字段: [{'field': 'amount', 'old_value': 4090.8499999999995, 'new_value': 4113.25}, {'field': 'count', 'old_value': 213, 'new_value': 215}, {'field': 'onlineAmount', 'old_value': 2003.8, 'new_value': 2026.2}, {'field': 'onlineCount', 'old_value': 95, 'new_value': 97}]
2025-05-25 08:06:53,624 - INFO - 更新表单数据成功: FINST-2K666OB11BLV28H37RVKBAELLLD43KM01H1BMZ8
2025-05-25 08:06:53,624 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 1076.87, 'new_value': 1081.37}, {'field': 'amount', 'old_value': 1076.87, 'new_value': 1081.37}, {'field': 'count', 'old_value': 86, 'new_value': 87}, {'field': 'onlineAmount', 'old_value': 718.29, 'new_value': 722.79}, {'field': 'onlineCount', 'old_value': 61, 'new_value': 62}]
2025-05-25 08:06:54,108 - INFO - 更新表单数据成功: FINST-2K666OB11BLV28H37RVKBAELLLD43KM01H1BM79
2025-05-25 08:06:54,108 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 4381.82, 'new_value': 4393.42}, {'field': 'amount', 'old_value': 4381.820000000001, 'new_value': 4393.42}, {'field': 'count', 'old_value': 237, 'new_value': 238}, {'field': 'onlineAmount', 'old_value': 4001.44, 'new_value': 4013.04}, {'field': 'onlineCount', 'old_value': 196, 'new_value': 197}]
2025-05-25 08:06:54,577 - INFO - 更新表单数据成功: FINST-2K666OB11BLV28H37RVKBAELLLD43LM01H1BMG9
2025-05-25 08:06:54,577 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 36372.98}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 36372.98}]
2025-05-25 08:06:54,999 - INFO - 更新表单数据成功: FINST-2K666OB11BLV28H37RVKBAELLLD43LM01H1BMN9
2025-05-25 08:06:54,999 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_20250523, 变更字段: [{'field': 'amount', 'old_value': 16427.920000000002, 'new_value': 16762.050000000003}, {'field': 'count', 'old_value': 176, 'new_value': 178}, {'field': 'instoreAmount', 'old_value': 13178.06, 'new_value': 13369.86}, {'field': 'instoreCount', 'old_value': 90, 'new_value': 91}, {'field': 'onlineAmount', 'old_value': 3269.96, 'new_value': 3412.29}, {'field': 'onlineCount', 'old_value': 86, 'new_value': 87}]
2025-05-25 08:06:55,467 - INFO - 更新表单数据成功: FINST-2K666OB11BLV28H37RVKBAELLLD43LM01H1BMR9
2025-05-25 08:06:55,467 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_20250523, 变更字段: [{'field': 'amount', 'old_value': 30081.54, 'new_value': 30976.54}, {'field': 'count', 'old_value': 215, 'new_value': 218}, {'field': 'instoreAmount', 'old_value': 14610.7, 'new_value': 15505.7}, {'field': 'instoreCount', 'old_value': 82, 'new_value': 85}]
2025-05-25 08:06:55,905 - INFO - 更新表单数据成功: FINST-2K666OB11BLV28H37RVKBAELLLD43LM01H1BMZ9
2025-05-25 08:06:55,905 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_20250523, 变更字段: [{'field': 'amount', 'old_value': 29620.58, 'new_value': 30616.38}, {'field': 'count', 'old_value': 202, 'new_value': 208}, {'field': 'instoreAmount', 'old_value': 19267.17, 'new_value': 20262.97}, {'field': 'instoreCount', 'old_value': 88, 'new_value': 94}]
2025-05-25 08:06:56,295 - INFO - 更新表单数据成功: FINST-2K666OB11BLV28H37RVKBAELLLD43LM01H1BM1A
2025-05-25 08:06:56,295 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_20250523, 变更字段: [{'field': 'amount', 'old_value': 9402.7, 'new_value': 9804.7}, {'field': 'count', 'old_value': 65, 'new_value': 66}, {'field': 'instoreAmount', 'old_value': 8127.9, 'new_value': 8529.9}, {'field': 'instoreCount', 'old_value': 52, 'new_value': 53}]
2025-05-25 08:06:56,717 - INFO - 更新表单数据成功: FINST-2K666OB11BLV28H37RVKBAELLLD43LM01H1BMKB
2025-05-25 08:06:56,717 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6052.88}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6052.88}]
2025-05-25 08:06:57,233 - INFO - 更新表单数据成功: FINST-NWE664C1IDOVVO3MDJLY960TZFCV2GA31H1BMMB
2025-05-25 08:06:57,233 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_20250523, 变更字段: [{'field': 'count', 'old_value': 104, 'new_value': 105}, {'field': 'onlineAmount', 'old_value': 1878.05, 'new_value': 1912.65}, {'field': 'onlineCount', 'old_value': 76, 'new_value': 77}]
2025-05-25 08:06:57,702 - INFO - 更新表单数据成功: FINST-NWE664C1IDOVVO3MDJLY960TZFCV2GA31H1BMSB
2025-05-25 08:06:57,702 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_20250523, 变更字段: [{'field': 'amount', 'old_value': 4361.99, 'new_value': 4417.1900000000005}, {'field': 'count', 'old_value': 264, 'new_value': 270}, {'field': 'onlineAmount', 'old_value': 3915.24, 'new_value': 3970.44}, {'field': 'onlineCount', 'old_value': 242, 'new_value': 248}]
2025-05-25 08:06:58,108 - INFO - 更新表单数据成功: FINST-NWE664C1IDOVVO3MDJLY960TZFCV2GA31H1BMWB
2025-05-25 08:06:58,108 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 3824.33, 'new_value': 4021.53}, {'field': 'amount', 'old_value': 3824.33, 'new_value': 4021.53}, {'field': 'count', 'old_value': 96, 'new_value': 97}, {'field': 'onlineAmount', 'old_value': 445.6, 'new_value': 642.8}, {'field': 'onlineCount', 'old_value': 5, 'new_value': 6}]
2025-05-25 08:06:58,577 - INFO - 更新表单数据成功: FINST-NWE664C1IDOVVO3MDJLY960TZFCV2HA31H1BM6C
2025-05-25 08:06:58,592 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_20250523, 变更字段: [{'field': 'amount', 'old_value': 7495.13, 'new_value': 7521.530000000001}, {'field': 'count', 'old_value': 457, 'new_value': 460}, {'field': 'instoreAmount', 'old_value': 5518.46, 'new_value': 5548.68}, {'field': 'instoreCount', 'old_value': 317, 'new_value': 320}, {'field': 'onlineAmount', 'old_value': 2173.53, 'new_value': 2176.21}]
2025-05-25 08:06:59,092 - INFO - 更新表单数据成功: FINST-NWE664C1IDOVVO3MDJLY960TZFCV2HA31H1BMDC
2025-05-25 08:06:59,092 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_20250523, 变更字段: [{'field': 'amount', 'old_value': 8629.2, 'new_value': 8802.2}, {'field': 'count', 'old_value': 113, 'new_value': 114}, {'field': 'onlineAmount', 'old_value': 1180.28, 'new_value': 1353.28}, {'field': 'onlineCount', 'old_value': 26, 'new_value': 27}]
2025-05-25 08:06:59,514 - INFO - 更新表单数据成功: FINST-NWE664C1IDOVVO3MDJLY960TZFCV2HA31H1BMAD
2025-05-25 08:06:59,514 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 3485.42, 'new_value': 3446.8}, {'field': 'amount', 'old_value': 3485.42, 'new_value': 3446.8}]
2025-05-25 08:06:59,952 - INFO - 更新表单数据成功: FINST-NWE664C1IDOVVO3MDJLY960TZFCV2HA31H1BMCD
2025-05-25 08:06:59,952 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 6956.49, 'new_value': 6940.69}, {'field': 'amount', 'old_value': 6956.49, 'new_value': 6940.69}]
2025-05-25 08:07:00,389 - INFO - 更新表单数据成功: FINST-NWE664C1IDOVVO3MDJLY960TZFCV2HA31H1BMGD
2025-05-25 08:07:00,389 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_20250523, 变更字段: [{'field': 'amount', 'old_value': 16407.87, 'new_value': 17015.87}, {'field': 'count', 'old_value': 199, 'new_value': 201}, {'field': 'instoreAmount', 'old_value': 14744.3, 'new_value': 15352.3}, {'field': 'instoreCount', 'old_value': 120, 'new_value': 122}]
2025-05-25 08:07:00,874 - INFO - 更新表单数据成功: FINST-NWE664C1IDOVVO3MDJLY960TZFCV2HA31H1BMXD
2025-05-25 08:07:00,874 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_20250523, 变更字段: [{'field': 'amount', 'old_value': 35824.07, 'new_value': 42683.44}, {'field': 'count', 'old_value': 135, 'new_value': 137}, {'field': 'instoreAmount', 'old_value': 34509.37, 'new_value': 41368.74}, {'field': 'instoreCount', 'old_value': 92, 'new_value': 94}]
2025-05-25 08:07:01,342 - INFO - 更新表单数据成功: FINST-NWE664C1IDOVVO3MDJLY960TZFCV2IA31H1BMCE
2025-05-25 08:07:01,342 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_20250523, 变更字段: [{'field': 'amount', 'old_value': 9513.8, 'new_value': 10327.4}, {'field': 'count', 'old_value': 13, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 9120.7, 'new_value': 9934.3}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 12}]
2025-05-25 08:07:01,749 - INFO - 更新表单数据成功: FINST-2PF66KD1XKOVAZZOBRAWACODS8IY2FT51H1BMV4
2025-05-25 08:07:01,749 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_20250523, 变更字段: [{'field': 'amount', 'old_value': 6803.28, 'new_value': 8516.28}, {'field': 'count', 'old_value': 46, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 6281.1, 'new_value': 7994.1}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 39}]
2025-05-25 08:07:01,827 - INFO - 正在批量插入每日数据，批次 1/22，共 100 条记录
2025-05-25 08:07:02,186 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-05-25 08:07:05,202 - INFO - 正在批量插入每日数据，批次 2/22，共 100 条记录
2025-05-25 08:07:05,655 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-05-25 08:07:08,670 - INFO - 正在批量插入每日数据，批次 3/22，共 100 条记录
2025-05-25 08:07:09,092 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-05-25 08:07:12,108 - INFO - 正在批量插入每日数据，批次 4/22，共 100 条记录
2025-05-25 08:07:12,561 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-05-25 08:07:15,577 - INFO - 正在批量插入每日数据，批次 5/22，共 100 条记录
2025-05-25 08:07:16,030 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-05-25 08:07:19,045 - INFO - 正在批量插入每日数据，批次 6/22，共 100 条记录
2025-05-25 08:07:19,514 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-05-25 08:07:22,530 - INFO - 正在批量插入每日数据，批次 7/22，共 100 条记录
2025-05-25 08:07:22,905 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-05-25 08:07:25,920 - INFO - 正在批量插入每日数据，批次 8/22，共 100 条记录
2025-05-25 08:07:26,373 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-05-25 08:07:29,389 - INFO - 正在批量插入每日数据，批次 9/22，共 100 条记录
2025-05-25 08:07:29,858 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-05-25 08:07:32,873 - INFO - 正在批量插入每日数据，批次 10/22，共 100 条记录
2025-05-25 08:07:33,311 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-05-25 08:07:36,326 - INFO - 正在批量插入每日数据，批次 11/22，共 100 条记录
2025-05-25 08:07:36,733 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-05-25 08:07:39,748 - INFO - 正在批量插入每日数据，批次 12/22，共 100 条记录
2025-05-25 08:07:40,186 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-05-25 08:07:43,201 - INFO - 正在批量插入每日数据，批次 13/22，共 100 条记录
2025-05-25 08:07:43,608 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-05-25 08:07:46,623 - INFO - 正在批量插入每日数据，批次 14/22，共 100 条记录
2025-05-25 08:07:47,045 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-05-25 08:07:50,061 - INFO - 正在批量插入每日数据，批次 15/22，共 100 条记录
2025-05-25 08:07:50,483 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-05-25 08:07:53,498 - INFO - 正在批量插入每日数据，批次 16/22，共 100 条记录
2025-05-25 08:07:53,967 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-05-25 08:07:56,983 - INFO - 正在批量插入每日数据，批次 17/22，共 100 条记录
2025-05-25 08:07:57,389 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-05-25 08:08:00,404 - INFO - 正在批量插入每日数据，批次 18/22，共 100 条记录
2025-05-25 08:08:00,842 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-05-25 08:08:03,857 - INFO - 正在批量插入每日数据，批次 19/22，共 100 条记录
2025-05-25 08:08:04,264 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-05-25 08:08:07,279 - INFO - 正在批量插入每日数据，批次 20/22，共 100 条记录
2025-05-25 08:08:07,701 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-05-25 08:08:10,701 - INFO - 正在批量插入每日数据，批次 21/22，共 100 条记录
2025-05-25 08:08:11,170 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-05-25 08:08:14,186 - INFO - 正在批量插入每日数据，批次 22/22，共 83 条记录
2025-05-25 08:08:14,529 - INFO - 批量插入每日数据成功，批次 22，83 条记录
2025-05-25 08:08:17,545 - INFO - 批量插入每日数据完成: 总计 2183 条，成功 2183 条，失败 0 条
2025-05-25 08:08:17,545 - INFO - 批量插入日销售数据完成，共 2183 条记录
2025-05-25 08:08:17,545 - INFO - 日销售数据同步完成！更新: 65 条，插入: 2183 条，错误: 0 条，跳过: 10788 条
2025-05-25 08:08:17,545 - INFO - 正在获取宜搭月销售表单数据...
2025-05-25 08:08:17,545 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-05-25 08:08:17,545 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-05-25 08:08:17,545 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-25 08:08:17,545 - INFO - Request Parameters - Page 1:
2025-05-25 08:08:17,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:17,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:18,311 - INFO - API请求耗时: 766ms
2025-05-25 08:08:18,311 - INFO - Response - Page 1
2025-05-25 08:08:18,311 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-25 08:08:18,311 - INFO - 查询完成，共获取到 0 条记录
2025-05-25 08:08:18,311 - WARNING - 月度分段 1 查询返回空数据
2025-05-25 08:08:18,311 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-05-25 08:08:18,311 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-05-25 08:08:18,311 - INFO - Request Parameters - Page 1:
2025-05-25 08:08:18,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:18,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:18,529 - INFO - API请求耗时: 219ms
2025-05-25 08:08:18,529 - INFO - Response - Page 1
2025-05-25 08:08:18,529 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-25 08:08:18,529 - INFO - 查询完成，共获取到 0 条记录
2025-05-25 08:08:18,529 - WARNING - 单月查询返回空数据: 2024-05
2025-05-25 08:08:19,045 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-05-25 08:08:19,045 - INFO - Request Parameters - Page 1:
2025-05-25 08:08:19,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:19,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:19,248 - INFO - API请求耗时: 203ms
2025-05-25 08:08:19,248 - INFO - Response - Page 1
2025-05-25 08:08:19,248 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-25 08:08:19,248 - INFO - 查询完成，共获取到 0 条记录
2025-05-25 08:08:19,248 - WARNING - 单月查询返回空数据: 2024-06
2025-05-25 08:08:19,764 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-25 08:08:19,764 - INFO - Request Parameters - Page 1:
2025-05-25 08:08:19,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:19,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:19,998 - INFO - API请求耗时: 234ms
2025-05-25 08:08:19,998 - INFO - Response - Page 1
2025-05-25 08:08:19,998 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-25 08:08:19,998 - INFO - 查询完成，共获取到 0 条记录
2025-05-25 08:08:19,998 - WARNING - 单月查询返回空数据: 2024-07
2025-05-25 08:08:21,529 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-05-25 08:08:21,529 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-25 08:08:21,529 - INFO - Request Parameters - Page 1:
2025-05-25 08:08:21,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:21,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:21,732 - INFO - API请求耗时: 203ms
2025-05-25 08:08:21,732 - INFO - Response - Page 1
2025-05-25 08:08:21,732 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-25 08:08:21,732 - INFO - 查询完成，共获取到 0 条记录
2025-05-25 08:08:21,732 - WARNING - 月度分段 2 查询返回空数据
2025-05-25 08:08:21,732 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-05-25 08:08:21,732 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-05-25 08:08:21,732 - INFO - Request Parameters - Page 1:
2025-05-25 08:08:21,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:21,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:21,951 - INFO - API请求耗时: 219ms
2025-05-25 08:08:21,951 - INFO - Response - Page 1
2025-05-25 08:08:21,951 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-25 08:08:21,951 - INFO - 查询完成，共获取到 0 条记录
2025-05-25 08:08:21,951 - WARNING - 单月查询返回空数据: 2024-08
2025-05-25 08:08:22,451 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-05-25 08:08:22,451 - INFO - Request Parameters - Page 1:
2025-05-25 08:08:22,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:22,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:22,639 - INFO - API请求耗时: 188ms
2025-05-25 08:08:22,639 - INFO - Response - Page 1
2025-05-25 08:08:22,639 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-25 08:08:22,639 - INFO - 查询完成，共获取到 0 条记录
2025-05-25 08:08:22,639 - WARNING - 单月查询返回空数据: 2024-09
2025-05-25 08:08:23,154 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-25 08:08:23,154 - INFO - Request Parameters - Page 1:
2025-05-25 08:08:23,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:23,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:23,389 - INFO - API请求耗时: 234ms
2025-05-25 08:08:23,389 - INFO - Response - Page 1
2025-05-25 08:08:23,389 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-25 08:08:23,389 - INFO - 查询完成，共获取到 0 条记录
2025-05-25 08:08:23,389 - WARNING - 单月查询返回空数据: 2024-10
2025-05-25 08:08:24,920 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-05-25 08:08:24,920 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-05-25 08:08:24,920 - INFO - Request Parameters - Page 1:
2025-05-25 08:08:24,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:24,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:25,560 - INFO - API请求耗时: 641ms
2025-05-25 08:08:25,560 - INFO - Response - Page 1
2025-05-25 08:08:25,560 - INFO - 第 1 页获取到 100 条记录
2025-05-25 08:08:26,076 - INFO - Request Parameters - Page 2:
2025-05-25 08:08:26,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:26,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:26,607 - INFO - API请求耗时: 531ms
2025-05-25 08:08:26,607 - INFO - Response - Page 2
2025-05-25 08:08:26,607 - INFO - 第 2 页获取到 100 条记录
2025-05-25 08:08:27,123 - INFO - Request Parameters - Page 3:
2025-05-25 08:08:27,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:27,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:27,592 - INFO - API请求耗时: 469ms
2025-05-25 08:08:27,592 - INFO - Response - Page 3
2025-05-25 08:08:27,592 - INFO - 第 3 页获取到 48 条记录
2025-05-25 08:08:27,592 - INFO - 查询完成，共获取到 248 条记录
2025-05-25 08:08:27,592 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-05-25 08:08:28,607 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-05-25 08:08:28,607 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-25 08:08:28,607 - INFO - Request Parameters - Page 1:
2025-05-25 08:08:28,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:28,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:29,170 - INFO - API请求耗时: 562ms
2025-05-25 08:08:29,170 - INFO - Response - Page 1
2025-05-25 08:08:29,170 - INFO - 第 1 页获取到 100 条记录
2025-05-25 08:08:29,685 - INFO - Request Parameters - Page 2:
2025-05-25 08:08:29,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:29,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:30,217 - INFO - API请求耗时: 531ms
2025-05-25 08:08:30,217 - INFO - Response - Page 2
2025-05-25 08:08:30,217 - INFO - 第 2 页获取到 100 条记录
2025-05-25 08:08:30,717 - INFO - Request Parameters - Page 3:
2025-05-25 08:08:30,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:30,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:31,342 - INFO - API请求耗时: 625ms
2025-05-25 08:08:31,342 - INFO - Response - Page 3
2025-05-25 08:08:31,342 - INFO - 第 3 页获取到 100 条记录
2025-05-25 08:08:31,857 - INFO - Request Parameters - Page 4:
2025-05-25 08:08:31,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:31,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:32,435 - INFO - API请求耗时: 578ms
2025-05-25 08:08:32,435 - INFO - Response - Page 4
2025-05-25 08:08:32,435 - INFO - 第 4 页获取到 100 条记录
2025-05-25 08:08:32,951 - INFO - Request Parameters - Page 5:
2025-05-25 08:08:32,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:32,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:33,529 - INFO - API请求耗时: 578ms
2025-05-25 08:08:33,529 - INFO - Response - Page 5
2025-05-25 08:08:33,529 - INFO - 第 5 页获取到 100 条记录
2025-05-25 08:08:34,029 - INFO - Request Parameters - Page 6:
2025-05-25 08:08:34,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:34,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:34,607 - INFO - API请求耗时: 578ms
2025-05-25 08:08:34,607 - INFO - Response - Page 6
2025-05-25 08:08:34,607 - INFO - 第 6 页获取到 100 条记录
2025-05-25 08:08:35,123 - INFO - Request Parameters - Page 7:
2025-05-25 08:08:35,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:35,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:35,654 - INFO - API请求耗时: 531ms
2025-05-25 08:08:35,654 - INFO - Response - Page 7
2025-05-25 08:08:35,654 - INFO - 第 7 页获取到 100 条记录
2025-05-25 08:08:36,154 - INFO - Request Parameters - Page 8:
2025-05-25 08:08:36,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:36,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:36,498 - INFO - API请求耗时: 344ms
2025-05-25 08:08:36,498 - INFO - Response - Page 8
2025-05-25 08:08:36,498 - INFO - 第 8 页获取到 16 条记录
2025-05-25 08:08:36,498 - INFO - 查询完成，共获取到 716 条记录
2025-05-25 08:08:36,498 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-05-25 08:08:37,514 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-05-25 08:08:37,514 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-05-25 08:08:37,514 - INFO - Request Parameters - Page 1:
2025-05-25 08:08:37,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:37,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:38,076 - INFO - API请求耗时: 562ms
2025-05-25 08:08:38,076 - INFO - Response - Page 1
2025-05-25 08:08:38,076 - INFO - 第 1 页获取到 100 条记录
2025-05-25 08:08:38,592 - INFO - Request Parameters - Page 2:
2025-05-25 08:08:38,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:38,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:39,185 - INFO - API请求耗时: 594ms
2025-05-25 08:08:39,185 - INFO - Response - Page 2
2025-05-25 08:08:39,185 - INFO - 第 2 页获取到 100 条记录
2025-05-25 08:08:39,701 - INFO - Request Parameters - Page 3:
2025-05-25 08:08:39,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 08:08:39,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 08:08:40,060 - INFO - API请求耗时: 359ms
2025-05-25 08:08:40,060 - INFO - Response - Page 3
2025-05-25 08:08:40,060 - INFO - 第 3 页获取到 24 条记录
2025-05-25 08:08:40,060 - INFO - 查询完成，共获取到 224 条记录
2025-05-25 08:08:40,060 - INFO - 月度分段 5 查询成功，获取到 224 条记录
2025-05-25 08:08:41,076 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1188 条记录，失败 0 次
2025-05-25 08:08:41,076 - INFO - 成功获取宜搭月销售表单数据，共 1188 条记录
2025-05-25 08:08:41,076 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-25 08:08:41,076 - INFO - 正在从SQLite获取月度汇总数据...
2025-05-25 08:08:41,076 - INFO - 成功获取SQLite月度汇总数据，共 1192 条记录
2025-05-25 08:08:41,138 - INFO - 成功创建宜搭月销售数据索引，共 1188 条记录
2025-05-25 08:08:41,576 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-05-25 08:08:41,576 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 160088.44, 'new_value': 170864.48}, {'field': 'dailyBillAmount', 'old_value': 160088.44, 'new_value': 170864.48}, {'field': 'amount', 'old_value': 4641.1, 'new_value': 4752.9}, {'field': 'count', 'old_value': 65, 'new_value': 67}, {'field': 'onlineAmount', 'old_value': 4717.1, 'new_value': 4828.9}, {'field': 'onlineCount', 'old_value': 65, 'new_value': 67}]
2025-05-25 08:08:42,013 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-05-25 08:08:42,013 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 413110.95, 'new_value': 441071.63}, {'field': 'dailyBillAmount', 'old_value': 413110.95, 'new_value': 441071.63}, {'field': 'amount', 'old_value': 226730.1, 'new_value': 236937.3}, {'field': 'count', 'old_value': 2128, 'new_value': 2224}, {'field': 'instoreAmount', 'old_value': 93015.4, 'new_value': 97158.4}, {'field': 'instoreCount', 'old_value': 714, 'new_value': 748}, {'field': 'onlineAmount', 'old_value': 134067.1, 'new_value': 140131.3}, {'field': 'onlineCount', 'old_value': 1414, 'new_value': 1476}]
2025-05-25 08:08:42,404 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMMJ
2025-05-25 08:08:42,404 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 219419.18, 'new_value': 302496.88}, {'field': 'dailyBillAmount', 'old_value': 219419.18, 'new_value': 302496.88}, {'field': 'amount', 'old_value': 221407.51, 'new_value': 305220.51}, {'field': 'count', 'old_value': 1464, 'new_value': 2023}, {'field': 'instoreAmount', 'old_value': 209068.3, 'new_value': 289107.08}, {'field': 'instoreCount', 'old_value': 1283, 'new_value': 1790}, {'field': 'onlineAmount', 'old_value': 12557.41, 'new_value': 16355.63}, {'field': 'onlineCount', 'old_value': 181, 'new_value': 233}]
2025-05-25 08:08:42,810 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-05-25 08:08:42,810 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 618056.19, 'new_value': 651165.1}, {'field': 'dailyBillAmount', 'old_value': 618056.19, 'new_value': 651165.1}, {'field': 'amount', 'old_value': 446386.58, 'new_value': 471200.98}, {'field': 'count', 'old_value': 2146, 'new_value': 2272}, {'field': 'instoreAmount', 'old_value': 446386.58, 'new_value': 471200.98}, {'field': 'instoreCount', 'old_value': 2146, 'new_value': 2272}]
2025-05-25 08:08:43,279 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-05-25 08:08:43,279 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 478651.2, 'new_value': 511692.0}, {'field': 'dailyBillAmount', 'old_value': 478651.2, 'new_value': 511692.0}, {'field': 'amount', 'old_value': 799281.0, 'new_value': 844680.0}, {'field': 'count', 'old_value': 2750, 'new_value': 2915}, {'field': 'instoreAmount', 'old_value': 800531.0, 'new_value': 845930.0}, {'field': 'instoreCount', 'old_value': 2750, 'new_value': 2915}]
2025-05-25 08:08:43,748 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-05-25 08:08:43,763 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59891.3, 'new_value': 60909.8}, {'field': 'dailyBillAmount', 'old_value': 59891.3, 'new_value': 60909.8}, {'field': 'amount', 'old_value': 79648.41, 'new_value': 81204.01}, {'field': 'count', 'old_value': 287, 'new_value': 303}, {'field': 'instoreAmount', 'old_value': 43412.9, 'new_value': 43776.6}, {'field': 'instoreCount', 'old_value': 42, 'new_value': 44}, {'field': 'onlineAmount', 'old_value': 40332.42, 'new_value': 41524.32}, {'field': 'onlineCount', 'old_value': 245, 'new_value': 259}]
2025-05-25 08:08:44,217 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-05-25 08:08:44,217 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'count', 'old_value': 120, 'new_value': 123}, {'field': 'instoreCount', 'old_value': 120, 'new_value': 123}]
2025-05-25 08:08:44,717 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-05-25 08:08:44,717 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 672186.3, 'new_value': 707570.42}, {'field': 'dailyBillAmount', 'old_value': 672186.3, 'new_value': 707570.42}, {'field': 'amount', 'old_value': 610225.95, 'new_value': 635882.35}, {'field': 'count', 'old_value': 4361, 'new_value': 4508}, {'field': 'instoreAmount', 'old_value': 498573.31, 'new_value': 522587.11}, {'field': 'instoreCount', 'old_value': 2144, 'new_value': 2242}, {'field': 'onlineAmount', 'old_value': 115577.57, 'new_value': 117280.07}, {'field': 'onlineCount', 'old_value': 2217, 'new_value': 2266}]
2025-05-25 08:08:45,185 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-05-25 08:08:45,185 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 674095.94, 'new_value': 707050.4299999999}, {'field': 'dailyBillAmount', 'old_value': 674095.94, 'new_value': 707050.4299999999}, {'field': 'amount', 'old_value': 185194.21, 'new_value': 200133.5}, {'field': 'count', 'old_value': 1057, 'new_value': 1136}, {'field': 'instoreAmount', 'old_value': 185194.21, 'new_value': 200133.5}, {'field': 'instoreCount', 'old_value': 1057, 'new_value': 1136}]
2025-05-25 08:08:45,685 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMVJ
2025-05-25 08:08:45,685 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-05, 变更字段: [{'field': 'amount', 'old_value': 27726.0, 'new_value': 48698.0}, {'field': 'count', 'old_value': 40, 'new_value': 70}, {'field': 'instoreAmount', 'old_value': 27726.0, 'new_value': 48698.0}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 70}]
2025-05-25 08:08:46,060 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-05-25 08:08:46,060 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 97095.3, 'new_value': 103593.2}, {'field': 'count', 'old_value': 273, 'new_value': 289}, {'field': 'instoreAmount', 'old_value': 97097.0, 'new_value': 103594.9}, {'field': 'instoreCount', 'old_value': 273, 'new_value': 289}]
2025-05-25 08:08:46,498 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXJ
2025-05-25 08:08:46,498 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSCOR6MVCC7Q2OV392411100148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32478.9, 'new_value': 32567.9}, {'field': 'amount', 'old_value': 32478.9, 'new_value': 32567.9}, {'field': 'count', 'old_value': 28, 'new_value': 29}, {'field': 'instoreAmount', 'old_value': 33874.9, 'new_value': 33963.9}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 29}]
2025-05-25 08:08:46,982 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-05-25 08:08:46,982 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 916609.52, 'new_value': 971382.79}, {'field': 'dailyBillAmount', 'old_value': 916609.52, 'new_value': 971382.79}, {'field': 'amount', 'old_value': -360793.4, 'new_value': -383117.34}, {'field': 'count', 'old_value': 947, 'new_value': 1019}, {'field': 'instoreAmount', 'old_value': 576025.5, 'new_value': 607015.87}, {'field': 'instoreCount', 'old_value': 947, 'new_value': 1019}]
2025-05-25 08:08:47,404 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-05-25 08:08:47,404 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 368664.0, 'new_value': 413260.0}, {'field': 'amount', 'old_value': 368664.0, 'new_value': 413260.0}, {'field': 'count', 'old_value': 1253, 'new_value': 1359}, {'field': 'instoreAmount', 'old_value': 368664.0, 'new_value': 413260.0}, {'field': 'instoreCount', 'old_value': 1253, 'new_value': 1359}]
2025-05-25 08:08:47,857 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-05-25 08:08:47,857 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 367399.94, 'new_value': 385560.95}, {'field': 'dailyBillAmount', 'old_value': 277168.64, 'new_value': 294362.05}, {'field': 'amount', 'old_value': 367399.94, 'new_value': 385560.95}, {'field': 'count', 'old_value': 1259, 'new_value': 1313}, {'field': 'instoreAmount', 'old_value': 367399.94, 'new_value': 385560.95}, {'field': 'instoreCount', 'old_value': 1259, 'new_value': 1313}]
2025-05-25 08:08:48,295 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-05-25 08:08:48,295 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 179915.85, 'new_value': 192952.53}, {'field': 'dailyBillAmount', 'old_value': 179915.85, 'new_value': 192952.53}, {'field': 'amount', 'old_value': 12826.4, 'new_value': 13432.4}, {'field': 'count', 'old_value': 96, 'new_value': 98}, {'field': 'instoreAmount', 'old_value': 15239.9, 'new_value': 15845.9}, {'field': 'instoreCount', 'old_value': 96, 'new_value': 98}]
2025-05-25 08:08:48,779 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-05-25 08:08:48,779 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 93638.24, 'new_value': 99646.4}, {'field': 'dailyBillAmount', 'old_value': 93638.24, 'new_value': 99646.4}, {'field': 'amount', 'old_value': 56586.73, 'new_value': 61130.95}, {'field': 'count', 'old_value': 833, 'new_value': 904}, {'field': 'instoreAmount', 'old_value': 58537.63, 'new_value': 63081.85}, {'field': 'instoreCount', 'old_value': 833, 'new_value': 904}]
2025-05-25 08:08:49,279 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-05-25 08:08:49,279 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 137463.77, 'new_value': 143720.52}, {'field': 'amount', 'old_value': 137462.91, 'new_value': 143719.66}, {'field': 'count', 'old_value': 4701, 'new_value': 4930}, {'field': 'instoreAmount', 'old_value': 119422.1, 'new_value': 125159.6}, {'field': 'instoreCount', 'old_value': 4251, 'new_value': 4467}, {'field': 'onlineAmount', 'old_value': 18041.67, 'new_value': 18560.92}, {'field': 'onlineCount', 'old_value': 450, 'new_value': 463}]
2025-05-25 08:08:49,888 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-05-25 08:08:49,888 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 292381.22, 'new_value': 299149.22}, {'field': 'dailyBillAmount', 'old_value': 286991.0, 'new_value': 293759.0}, {'field': 'amount', 'old_value': 242665.91, 'new_value': 248091.01}, {'field': 'count', 'old_value': 223, 'new_value': 231}, {'field': 'instoreAmount', 'old_value': 242495.0, 'new_value': 247858.0}, {'field': 'instoreCount', 'old_value': 221, 'new_value': 228}, {'field': 'onlineAmount', 'old_value': 170.91, 'new_value': 233.01}, {'field': 'onlineCount', 'old_value': 2, 'new_value': 3}]
2025-05-25 08:08:50,342 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-05-25 08:08:50,342 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 524844.3, 'new_value': 560882.72}, {'field': 'dailyBillAmount', 'old_value': 524339.75, 'new_value': 560378.17}, {'field': 'amount', 'old_value': 524844.3, 'new_value': 560882.72}, {'field': 'count', 'old_value': 469, 'new_value': 501}, {'field': 'instoreAmount', 'old_value': 524845.3, 'new_value': 560883.72}, {'field': 'instoreCount', 'old_value': 469, 'new_value': 501}]
2025-05-25 08:08:50,779 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6K
2025-05-25 08:08:50,779 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-05, 变更字段: [{'field': 'amount', 'old_value': 37025.0, 'new_value': 37295.0}, {'field': 'count', 'old_value': 64, 'new_value': 65}, {'field': 'instoreAmount', 'old_value': 37025.0, 'new_value': 37295.0}, {'field': 'instoreCount', 'old_value': 64, 'new_value': 65}]
2025-05-25 08:08:51,217 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-05-25 08:08:51,217 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 95938.4, 'new_value': 101506.0}, {'field': 'dailyBillAmount', 'old_value': 95938.4, 'new_value': 101506.0}, {'field': 'amount', 'old_value': 105950.6, 'new_value': 111126.2}, {'field': 'count', 'old_value': 284, 'new_value': 298}, {'field': 'instoreAmount', 'old_value': 105956.5, 'new_value': 111132.09999999999}, {'field': 'instoreCount', 'old_value': 284, 'new_value': 298}]
2025-05-25 08:08:51,638 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-05-25 08:08:51,638 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 149814.7, 'new_value': 153783.7}, {'field': 'amount', 'old_value': 149814.7, 'new_value': 153783.7}, {'field': 'count', 'old_value': 180, 'new_value': 184}, {'field': 'instoreAmount', 'old_value': 149941.7, 'new_value': 153910.7}, {'field': 'instoreCount', 'old_value': 180, 'new_value': 184}]
2025-05-25 08:08:52,154 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-05-25 08:08:52,154 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 207695.26, 'new_value': 221711.16}, {'field': 'dailyBillAmount', 'old_value': 207695.26, 'new_value': 221711.16}, {'field': 'amount', 'old_value': 218697.65, 'new_value': 233334.55}, {'field': 'count', 'old_value': 1465, 'new_value': 1558}, {'field': 'instoreAmount', 'old_value': 219906.65, 'new_value': 234543.55}, {'field': 'instoreCount', 'old_value': 1465, 'new_value': 1558}]
2025-05-25 08:08:52,545 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-05-25 08:08:52,545 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 145383.2, 'new_value': 151996.95}, {'field': 'dailyBillAmount', 'old_value': 145383.2, 'new_value': 151996.95}, {'field': 'amount', 'old_value': 14547.24, 'new_value': 15102.83}, {'field': 'count', 'old_value': 1333, 'new_value': 1367}, {'field': 'instoreAmount', 'old_value': 19338.95, 'new_value': 20016.739999999998}, {'field': 'instoreCount', 'old_value': 1333, 'new_value': 1367}]
2025-05-25 08:08:53,107 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-05-25 08:08:53,107 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 258564.01, 'new_value': 271244.11}, {'field': 'amount', 'old_value': 258559.86000000002, 'new_value': 271239.96}, {'field': 'count', 'old_value': 6008, 'new_value': 6297}, {'field': 'instoreAmount', 'old_value': 251940.26, 'new_value': 264674.56}, {'field': 'instoreCount', 'old_value': 5789, 'new_value': 6073}, {'field': 'onlineAmount', 'old_value': 10464.53, 'new_value': 10629.43}, {'field': 'onlineCount', 'old_value': 219, 'new_value': 224}]
2025-05-25 08:08:53,529 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-05-25 08:08:53,545 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 219251.8, 'new_value': 231137.7}, {'field': 'dailyBillAmount', 'old_value': 219251.8, 'new_value': 231137.7}, {'field': 'amount', 'old_value': 219251.8, 'new_value': 231137.7}, {'field': 'count', 'old_value': 662, 'new_value': 691}, {'field': 'instoreAmount', 'old_value': 219251.8, 'new_value': 231137.7}, {'field': 'instoreCount', 'old_value': 662, 'new_value': 691}]
2025-05-25 08:08:53,998 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-05-25 08:08:53,998 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 190644.21, 'new_value': 202160.65}, {'field': 'dailyBillAmount', 'old_value': 190644.21, 'new_value': 202160.65}, {'field': 'amount', 'old_value': 65975.2, 'new_value': 69531.2}, {'field': 'count', 'old_value': 156, 'new_value': 162}, {'field': 'instoreAmount', 'old_value': 65975.2, 'new_value': 69531.2}, {'field': 'instoreCount', 'old_value': 156, 'new_value': 162}]
2025-05-25 08:08:54,357 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-05-25 08:08:54,357 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 385436.03, 'new_value': 406534.57}, {'field': 'dailyBillAmount', 'old_value': 385436.03, 'new_value': 406534.57}, {'field': 'amount', 'old_value': 159597.3, 'new_value': 167727.3}, {'field': 'count', 'old_value': 590, 'new_value': 622}, {'field': 'instoreAmount', 'old_value': 159597.56, 'new_value': 167727.56}, {'field': 'instoreCount', 'old_value': 590, 'new_value': 622}]
2025-05-25 08:08:54,857 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFK
2025-05-25 08:08:54,857 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDL7K17VIP7Q2OV4FVC78K001455_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21187.0, 'new_value': 24749.0}, {'field': 'amount', 'old_value': 21187.0, 'new_value': 24749.0}, {'field': 'count', 'old_value': 17, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 21187.0, 'new_value': 24749.0}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 21}]
2025-05-25 08:08:55,263 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-05-25 08:08:55,263 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 84209.63, 'new_value': 87965.66}, {'field': 'dailyBillAmount', 'old_value': 84209.63, 'new_value': 87965.66}, {'field': 'amount', 'old_value': 25518.63, 'new_value': 26317.87}, {'field': 'count', 'old_value': 937, 'new_value': 968}, {'field': 'instoreAmount', 'old_value': 6051.83, 'new_value': 6126.83}, {'field': 'instoreCount', 'old_value': 161, 'new_value': 164}, {'field': 'onlineAmount', 'old_value': 19736.89, 'new_value': 20461.94}, {'field': 'onlineCount', 'old_value': 776, 'new_value': 804}]
2025-05-25 08:08:55,685 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-05-25 08:08:55,685 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 134425.71, 'new_value': 140393.06}, {'field': 'dailyBillAmount', 'old_value': 134425.71, 'new_value': 140393.06}, {'field': 'amount', 'old_value': 22376.05, 'new_value': 23465.76}, {'field': 'count', 'old_value': 537, 'new_value': 562}, {'field': 'instoreAmount', 'old_value': 19417.95, 'new_value': 20406.95}, {'field': 'instoreCount', 'old_value': 479, 'new_value': 501}, {'field': 'onlineAmount', 'old_value': 2958.79, 'new_value': 3059.5}, {'field': 'onlineCount', 'old_value': 58, 'new_value': 61}]
2025-05-25 08:08:56,154 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-05-25 08:08:56,154 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 18828.43, 'new_value': 19550.43}, {'field': 'dailyBillAmount', 'old_value': 18828.43, 'new_value': 19550.43}, {'field': 'amount', 'old_value': 14962.58, 'new_value': 15186.58}, {'field': 'count', 'old_value': 527, 'new_value': 558}, {'field': 'instoreAmount', 'old_value': 15330.18, 'new_value': 15554.18}, {'field': 'instoreCount', 'old_value': 527, 'new_value': 558}]
2025-05-25 08:08:56,576 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-05-25 08:08:56,576 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42672.13, 'new_value': 44683.26}, {'field': 'dailyBillAmount', 'old_value': 42672.13, 'new_value': 44683.26}, {'field': 'amount', 'old_value': 27495.72, 'new_value': 28741.67}, {'field': 'count', 'old_value': 1463, 'new_value': 1537}, {'field': 'instoreAmount', 'old_value': 13853.73, 'new_value': 14696.72}, {'field': 'instoreCount', 'old_value': 588, 'new_value': 619}, {'field': 'onlineAmount', 'old_value': 14298.57, 'new_value': 14903.07}, {'field': 'onlineCount', 'old_value': 875, 'new_value': 918}]
2025-05-25 08:08:57,013 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-05-25 08:08:57,013 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 302923.3, 'new_value': 313795.26}, {'field': 'dailyBillAmount', 'old_value': 302923.3, 'new_value': 313795.26}, {'field': 'amount', 'old_value': 137398.26, 'new_value': 142866.46}, {'field': 'count', 'old_value': 574, 'new_value': 596}, {'field': 'instoreAmount', 'old_value': 141941.92, 'new_value': 147449.92}, {'field': 'instoreCount', 'old_value': 574, 'new_value': 596}]
2025-05-25 08:08:57,467 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-05-25 08:08:57,467 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 16142.74, 'new_value': 17814.38}, {'field': 'count', 'old_value': 145, 'new_value': 152}, {'field': 'instoreAmount', 'old_value': 16216.98, 'new_value': 17888.62}, {'field': 'instoreCount', 'old_value': 145, 'new_value': 152}]
2025-05-25 08:08:57,888 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-05-25 08:08:57,888 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 187241.26, 'new_value': 194391.52}, {'field': 'dailyBillAmount', 'old_value': 187241.26, 'new_value': 194391.52}, {'field': 'amount', 'old_value': 91357.5, 'new_value': 94583.14}, {'field': 'count', 'old_value': 3851, 'new_value': 4023}, {'field': 'instoreAmount', 'old_value': 93204.46, 'new_value': 96516.18}, {'field': 'instoreCount', 'old_value': 3851, 'new_value': 4023}]
2025-05-25 08:08:58,310 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-05-25 08:08:58,310 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 430121.4, 'new_value': 453968.6}, {'field': 'dailyBillAmount', 'old_value': 430121.4, 'new_value': 453968.6}, {'field': 'amount', 'old_value': 430121.4, 'new_value': 453968.6}, {'field': 'count', 'old_value': 543, 'new_value': 579}, {'field': 'instoreAmount', 'old_value': 430121.4, 'new_value': 453968.6}, {'field': 'instoreCount', 'old_value': 543, 'new_value': 579}]
2025-05-25 08:08:58,732 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-05-25 08:08:58,732 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 192319.89, 'new_value': 202449.59}, {'field': 'dailyBillAmount', 'old_value': 192319.89, 'new_value': 202449.59}, {'field': 'amount', 'old_value': 108074.65, 'new_value': 115128.65}, {'field': 'count', 'old_value': 282, 'new_value': 301}, {'field': 'instoreAmount', 'old_value': 109491.25, 'new_value': 116545.25}, {'field': 'instoreCount', 'old_value': 282, 'new_value': 301}]
2025-05-25 08:08:59,263 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-05-25 08:08:59,263 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48182.0, 'new_value': 50451.0}, {'field': 'dailyBillAmount', 'old_value': 48182.0, 'new_value': 50451.0}, {'field': 'amount', 'old_value': 48182.0, 'new_value': 50451.0}, {'field': 'count', 'old_value': 956, 'new_value': 995}, {'field': 'instoreAmount', 'old_value': 48221.0, 'new_value': 50490.0}, {'field': 'instoreCount', 'old_value': 956, 'new_value': 995}]
2025-05-25 08:08:59,748 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-05-25 08:08:59,748 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 83245.87, 'new_value': 87366.79}, {'field': 'dailyBillAmount', 'old_value': 83245.87, 'new_value': 87366.79}, {'field': 'amount', 'old_value': 86199.06, 'new_value': 90316.21}, {'field': 'count', 'old_value': 4554, 'new_value': 4775}, {'field': 'instoreAmount', 'old_value': 41395.24, 'new_value': 43499.05}, {'field': 'instoreCount', 'old_value': 2075, 'new_value': 2177}, {'field': 'onlineAmount', 'old_value': 45998.26, 'new_value': 48075.1}, {'field': 'onlineCount', 'old_value': 2479, 'new_value': 2598}]
2025-05-25 08:09:00,232 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-05-25 08:09:00,232 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30020.78, 'new_value': 30931.19}, {'field': 'dailyBillAmount', 'old_value': 30020.78, 'new_value': 30931.19}, {'field': 'amount', 'old_value': 41249.93, 'new_value': 42381.54}, {'field': 'count', 'old_value': 1211, 'new_value': 1239}, {'field': 'instoreAmount', 'old_value': 37490.28, 'new_value': 38621.89}, {'field': 'instoreCount', 'old_value': 1060, 'new_value': 1088}]
2025-05-25 08:09:00,591 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-05-25 08:09:00,591 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60705.090000000004, 'new_value': 63511.63}, {'field': 'dailyBillAmount', 'old_value': 60705.090000000004, 'new_value': 63511.63}, {'field': 'amount', 'old_value': 60597.01, 'new_value': 63384.14}, {'field': 'count', 'old_value': 2377, 'new_value': 2465}, {'field': 'instoreAmount', 'old_value': 39449.16, 'new_value': 41368.92}, {'field': 'instoreCount', 'old_value': 1418, 'new_value': 1470}, {'field': 'onlineAmount', 'old_value': 21448.48, 'new_value': 22315.85}, {'field': 'onlineCount', 'old_value': 959, 'new_value': 995}]
2025-05-25 08:09:01,060 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-05-25 08:09:01,060 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 60493.77, 'new_value': 61951.79}, {'field': 'count', 'old_value': 740, 'new_value': 764}, {'field': 'instoreAmount', 'old_value': 60920.67, 'new_value': 62416.69}, {'field': 'instoreCount', 'old_value': 740, 'new_value': 764}]
2025-05-25 08:09:01,545 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-05-25 08:09:01,545 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64935.8, 'new_value': 68160.6}, {'field': 'amount', 'old_value': 64935.3, 'new_value': 68160.1}, {'field': 'count', 'old_value': 1642, 'new_value': 1733}, {'field': 'instoreAmount', 'old_value': 65984.8, 'new_value': 69209.6}, {'field': 'instoreCount', 'old_value': 1642, 'new_value': 1733}]
2025-05-25 08:09:01,982 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-05-25 08:09:01,982 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 302245.42, 'new_value': 316607.42}, {'field': 'dailyBillAmount', 'old_value': 302245.42, 'new_value': 316607.42}, {'field': 'amount', 'old_value': 94804.82, 'new_value': 98428.82}, {'field': 'count', 'old_value': 338, 'new_value': 351}, {'field': 'instoreAmount', 'old_value': 94804.82, 'new_value': 98428.82}, {'field': 'instoreCount', 'old_value': 338, 'new_value': 351}]
2025-05-25 08:09:02,451 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-05-25 08:09:02,451 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81952.06, 'new_value': 85162.66}, {'field': 'dailyBillAmount', 'old_value': 81952.06, 'new_value': 85162.66}, {'field': 'amount', 'old_value': 83253.86, 'new_value': 86464.46}, {'field': 'count', 'old_value': 302, 'new_value': 316}, {'field': 'instoreAmount', 'old_value': 85388.49, 'new_value': 88599.09}, {'field': 'instoreCount', 'old_value': 302, 'new_value': 316}]
2025-05-25 08:09:02,841 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-05-25 08:09:02,841 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 47504.0, 'new_value': 49120.0}, {'field': 'dailyBillAmount', 'old_value': 47504.0, 'new_value': 49120.0}, {'field': 'amount', 'old_value': 58651.0, 'new_value': 60815.0}, {'field': 'count', 'old_value': 111, 'new_value': 117}, {'field': 'instoreAmount', 'old_value': 62749.0, 'new_value': 65770.0}, {'field': 'instoreCount', 'old_value': 111, 'new_value': 117}]
2025-05-25 08:09:03,295 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-05-25 08:09:03,295 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 83353.75, 'new_value': 85736.15}, {'field': 'dailyBillAmount', 'old_value': 80860.55, 'new_value': 83242.95}, {'field': 'amount', 'old_value': 81641.45, 'new_value': 85733.55}, {'field': 'count', 'old_value': 251, 'new_value': 264}, {'field': 'instoreAmount', 'old_value': 92533.85, 'new_value': 96626.25}, {'field': 'instoreCount', 'old_value': 251, 'new_value': 264}]
2025-05-25 08:09:03,763 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-05-25 08:09:03,763 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 106914.16, 'new_value': 114629.13}, {'field': 'dailyBillAmount', 'old_value': 106914.16, 'new_value': 114629.13}, {'field': 'amount', 'old_value': 60177.3, 'new_value': 64166.0}, {'field': 'count', 'old_value': 1660, 'new_value': 1756}, {'field': 'instoreAmount', 'old_value': 52153.07, 'new_value': 55808.04}, {'field': 'instoreCount', 'old_value': 1396, 'new_value': 1480}, {'field': 'onlineAmount', 'old_value': 9102.91, 'new_value': 9461.92}, {'field': 'onlineCount', 'old_value': 264, 'new_value': 276}]
2025-05-25 08:09:04,341 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-05-25 08:09:04,341 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 158429.95, 'new_value': 164787.19}, {'field': 'dailyBillAmount', 'old_value': 153315.58, 'new_value': 159652.47}, {'field': 'amount', 'old_value': 158429.95, 'new_value': 164787.19}, {'field': 'count', 'old_value': 1955, 'new_value': 2022}, {'field': 'instoreAmount', 'old_value': 150390.85, 'new_value': 156653.85}, {'field': 'instoreCount', 'old_value': 1862, 'new_value': 1928}, {'field': 'onlineAmount', 'old_value': 8098.34, 'new_value': 8192.58}, {'field': 'onlineCount', 'old_value': 93, 'new_value': 94}]
2025-05-25 08:09:04,810 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-05-25 08:09:04,810 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'amount', 'old_value': 96101.56, 'new_value': 99948.34}, {'field': 'count', 'old_value': 416, 'new_value': 440}, {'field': 'instoreAmount', 'old_value': 92655.72, 'new_value': 96430.9}, {'field': 'instoreCount', 'old_value': 372, 'new_value': 393}, {'field': 'onlineAmount', 'old_value': 3445.84, 'new_value': 3655.44}, {'field': 'onlineCount', 'old_value': 44, 'new_value': 47}]
2025-05-25 08:09:05,263 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-05-25 08:09:05,263 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 185037.3, 'new_value': 192239.3}, {'field': 'dailyBillAmount', 'old_value': 185037.3, 'new_value': 192239.3}, {'field': 'amount', 'old_value': 186942.8, 'new_value': 195991.8}, {'field': 'count', 'old_value': 688, 'new_value': 721}, {'field': 'instoreAmount', 'old_value': 189827.7, 'new_value': 198876.7}, {'field': 'instoreCount', 'old_value': 688, 'new_value': 721}]
2025-05-25 08:09:05,716 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3L
2025-05-25 08:09:05,716 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 43373.0, 'new_value': 44028.0}, {'field': 'dailyBillAmount', 'old_value': 43373.0, 'new_value': 44028.0}, {'field': 'amount', 'old_value': 40571.0, 'new_value': 41226.0}, {'field': 'count', 'old_value': 99, 'new_value': 105}, {'field': 'instoreAmount', 'old_value': 41164.0, 'new_value': 41819.0}, {'field': 'instoreCount', 'old_value': 99, 'new_value': 105}]
2025-05-25 08:09:06,107 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5L
2025-05-25 08:09:06,107 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDS9O4DUBH2L6U1G19QP11V40018CI_2025-05, 变更字段: [{'field': 'amount', 'old_value': 37402.23, 'new_value': 39712.98}, {'field': 'count', 'old_value': 203, 'new_value': 214}, {'field': 'instoreAmount', 'old_value': 35878.0, 'new_value': 37955.4}, {'field': 'instoreCount', 'old_value': 156, 'new_value': 165}, {'field': 'onlineAmount', 'old_value': 2364.98, 'new_value': 2598.33}, {'field': 'onlineCount', 'old_value': 47, 'new_value': 49}]
2025-05-25 08:09:06,607 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6L
2025-05-25 08:09:06,607 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HI85875BG16NA6U1G19QP117C001UB5_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23191.4, 'new_value': 24329.9}, {'field': 'dailyBillAmount', 'old_value': 23191.4, 'new_value': 24329.9}, {'field': 'amount', 'old_value': 17596.31, 'new_value': 18552.31}, {'field': 'count', 'old_value': 790, 'new_value': 831}, {'field': 'instoreAmount', 'old_value': 17789.36, 'new_value': 18745.36}, {'field': 'instoreCount', 'old_value': 790, 'new_value': 831}]
2025-05-25 08:09:07,170 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-05-25 08:09:07,170 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44641.42, 'new_value': 46370.6}, {'field': 'amount', 'old_value': 44639.78, 'new_value': 46368.96}, {'field': 'count', 'old_value': 2217, 'new_value': 2310}, {'field': 'instoreAmount', 'old_value': 51457.53, 'new_value': 53360.8}, {'field': 'instoreCount', 'old_value': 2217, 'new_value': 2310}]
2025-05-25 08:09:07,763 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-05-25 08:09:07,763 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 118379.07, 'new_value': 127716.17}, {'field': 'dailyBillAmount', 'old_value': 118379.07, 'new_value': 127716.17}, {'field': 'amount', 'old_value': 95070.3, 'new_value': 102584.2}, {'field': 'count', 'old_value': 390, 'new_value': 416}, {'field': 'instoreAmount', 'old_value': 95070.3, 'new_value': 102584.2}, {'field': 'instoreCount', 'old_value': 389, 'new_value': 415}]
2025-05-25 08:09:08,216 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-05-25 08:09:08,216 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 348192.74, 'new_value': 364436.88}, {'field': 'dailyBillAmount', 'old_value': 348192.74, 'new_value': 364436.88}, {'field': 'amount', 'old_value': 203587.36, 'new_value': 210639.26}, {'field': 'count', 'old_value': 2353, 'new_value': 2427}, {'field': 'instoreAmount', 'old_value': 88910.42, 'new_value': 92747.27}, {'field': 'instoreCount', 'old_value': 1013, 'new_value': 1044}, {'field': 'onlineAmount', 'old_value': 114678.7, 'new_value': 117894.06}, {'field': 'onlineCount', 'old_value': 1340, 'new_value': 1383}]
2025-05-25 08:09:08,591 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-05-25 08:09:08,591 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 197933.33, 'new_value': 211451.33}, {'field': 'dailyBillAmount', 'old_value': 197933.33, 'new_value': 211451.33}, {'field': 'amount', 'old_value': 210446.19999999998, 'new_value': 224533.19999999998}, {'field': 'count', 'old_value': 1274, 'new_value': 1367}, {'field': 'instoreAmount', 'old_value': 211226.1, 'new_value': 225313.1}, {'field': 'instoreCount', 'old_value': 1274, 'new_value': 1367}]
2025-05-25 08:09:09,060 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBL
2025-05-25 08:09:09,060 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 67448.89, 'new_value': 74547.89}, {'field': 'amount', 'old_value': 67448.89, 'new_value': 74547.89}, {'field': 'count', 'old_value': 31, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 67448.89, 'new_value': 74547.89}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 32}]
2025-05-25 08:09:09,466 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-05-25 08:09:09,466 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'amount', 'old_value': 96522.28, 'new_value': 96537.28}, {'field': 'count', 'old_value': 1088, 'new_value': 1089}, {'field': 'onlineAmount', 'old_value': 10336.11, 'new_value': 10351.11}, {'field': 'onlineCount', 'old_value': 314, 'new_value': 315}]
2025-05-25 08:09:09,873 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDL
2025-05-25 08:09:09,873 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-05, 变更字段: [{'field': 'amount', 'old_value': 2101.5, 'new_value': 2151.4}, {'field': 'count', 'old_value': 27, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 2101.5, 'new_value': 2151.4}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 28}]
2025-05-25 08:09:10,388 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-05-25 08:09:10,388 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 178457.5, 'new_value': 190686.45}, {'field': 'dailyBillAmount', 'old_value': 173651.75, 'new_value': 185880.7}, {'field': 'amount', 'old_value': 178457.5, 'new_value': 190686.45}, {'field': 'count', 'old_value': 755, 'new_value': 803}, {'field': 'instoreAmount', 'old_value': 178457.5, 'new_value': 190686.45}, {'field': 'instoreCount', 'old_value': 755, 'new_value': 803}]
2025-05-25 08:09:10,841 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-05-25 08:09:10,841 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21184.82, 'new_value': 23033.98}, {'field': 'dailyBillAmount', 'old_value': 21184.82, 'new_value': 23033.98}, {'field': 'amount', 'old_value': 25444.82, 'new_value': 27550.88}, {'field': 'count', 'old_value': 750, 'new_value': 803}, {'field': 'instoreAmount', 'old_value': 25464.62, 'new_value': 27570.68}, {'field': 'instoreCount', 'old_value': 750, 'new_value': 803}]
2025-05-25 08:09:11,326 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-05-25 08:09:11,326 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 278373.1, 'new_value': 291437.8}, {'field': 'amount', 'old_value': 278373.1, 'new_value': 291437.8}, {'field': 'count', 'old_value': 430, 'new_value': 453}, {'field': 'instoreAmount', 'old_value': 278373.1, 'new_value': 291437.8}, {'field': 'instoreCount', 'old_value': 430, 'new_value': 453}]
2025-05-25 08:09:11,779 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-05-25 08:09:11,779 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42310.68, 'new_value': 44419.32}, {'field': 'amount', 'old_value': 42310.68, 'new_value': 44419.32}, {'field': 'count', 'old_value': 341, 'new_value': 364}, {'field': 'instoreAmount', 'old_value': 42310.68, 'new_value': 44419.32}, {'field': 'instoreCount', 'old_value': 341, 'new_value': 364}]
2025-05-25 08:09:12,279 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMG01
2025-05-25 08:09:12,279 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_8711865ACD3B4C2AADD3843CA2A204D9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 52528.0, 'new_value': 55816.0}, {'field': 'amount', 'old_value': 52528.0, 'new_value': 55816.0}, {'field': 'count', 'old_value': 10, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 52528.0, 'new_value': 55816.0}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 11}]
2025-05-25 08:09:12,748 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-05-25 08:09:12,748 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'amount', 'old_value': 36691.95, 'new_value': 42565.05}, {'field': 'count', 'old_value': 478, 'new_value': 506}, {'field': 'instoreAmount', 'old_value': 36691.95, 'new_value': 42565.05}, {'field': 'instoreCount', 'old_value': 478, 'new_value': 506}]
2025-05-25 08:09:13,248 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKL
2025-05-25 08:09:13,248 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 39541.6, 'new_value': 40359.9}, {'field': 'dailyBillAmount', 'old_value': 39541.6, 'new_value': 40359.9}, {'field': 'amount', 'old_value': 41004.9, 'new_value': 41823.2}, {'field': 'count', 'old_value': 50, 'new_value': 52}, {'field': 'instoreAmount', 'old_value': 41902.9, 'new_value': 42721.2}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 52}]
2025-05-25 08:09:13,670 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-05-25 08:09:13,670 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 392573.08, 'new_value': 416260.08}, {'field': 'dailyBillAmount', 'old_value': 392573.08, 'new_value': 416260.08}, {'field': 'amount', 'old_value': 399744.08, 'new_value': 423431.08}, {'field': 'count', 'old_value': 1265, 'new_value': 1348}, {'field': 'instoreAmount', 'old_value': 399744.08, 'new_value': 423431.08}, {'field': 'instoreCount', 'old_value': 1265, 'new_value': 1348}]
2025-05-25 08:09:14,076 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-05-25 08:09:14,076 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 936489.67, 'new_value': 978149.24}, {'field': 'count', 'old_value': 1183, 'new_value': 1229}, {'field': 'instoreAmount', 'old_value': 936489.84, 'new_value': 978149.41}, {'field': 'instoreCount', 'old_value': 1183, 'new_value': 1229}]
2025-05-25 08:09:14,420 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMH01
2025-05-25 08:09:14,420 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_D384CB5088914FB296DE32297895B8D6_2025-05, 变更字段: [{'field': 'count', 'old_value': 17, 'new_value': 19}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 19}]
2025-05-25 08:09:14,904 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-05-25 08:09:14,904 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 137349.5, 'new_value': 144731.8}, {'field': 'dailyBillAmount', 'old_value': 137349.5, 'new_value': 144731.8}, {'field': 'amount', 'old_value': 28520.8, 'new_value': 30351.8}, {'field': 'count', 'old_value': 110, 'new_value': 117}, {'field': 'instoreAmount', 'old_value': 28522.3, 'new_value': 30353.3}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 117}]
2025-05-25 08:09:15,451 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-05-25 08:09:15,466 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 178169.81, 'new_value': 189799.99}, {'field': 'amount', 'old_value': 178167.29, 'new_value': 189797.47}, {'field': 'count', 'old_value': 1851, 'new_value': 1990}, {'field': 'instoreAmount', 'old_value': 114443.45999999999, 'new_value': 122837.87}, {'field': 'instoreCount', 'old_value': 1040, 'new_value': 1131}, {'field': 'onlineAmount', 'old_value': 68286.07, 'new_value': 72007.85}, {'field': 'onlineCount', 'old_value': 811, 'new_value': 859}]
2025-05-25 08:09:15,857 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-05-25 08:09:15,857 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 300700.61, 'new_value': 318141.66}, {'field': 'dailyBillAmount', 'old_value': 300700.61, 'new_value': 318141.66}, {'field': 'amount', 'old_value': 27395.22, 'new_value': 28519.93}, {'field': 'count', 'old_value': 842, 'new_value': 877}, {'field': 'instoreAmount', 'old_value': 31662.66, 'new_value': 33304.4}, {'field': 'instoreCount', 'old_value': 842, 'new_value': 877}]
2025-05-25 08:09:16,295 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-05-25 08:09:16,295 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 273735.54, 'new_value': 294480.33}, {'field': 'dailyBillAmount', 'old_value': 273735.54, 'new_value': 294480.33}, {'field': 'amount', 'old_value': 141733.39, 'new_value': 150806.52}, {'field': 'count', 'old_value': 3187, 'new_value': 3414}, {'field': 'instoreAmount', 'old_value': 118295.99, 'new_value': 125861.40000000001}, {'field': 'instoreCount', 'old_value': 2656, 'new_value': 2848}, {'field': 'onlineAmount', 'old_value': 25568.260000000002, 'new_value': 27165.68}, {'field': 'onlineCount', 'old_value': 531, 'new_value': 566}]
2025-05-25 08:09:16,685 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-05-25 08:09:16,685 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 254461.9, 'new_value': 263523.4}, {'field': 'amount', 'old_value': 254460.3, 'new_value': 263521.8}, {'field': 'count', 'old_value': 1013, 'new_value': 1057}, {'field': 'instoreAmount', 'old_value': 257551.8, 'new_value': 266613.3}, {'field': 'instoreCount', 'old_value': 1013, 'new_value': 1057}]
2025-05-25 08:09:17,138 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-05-25 08:09:17,138 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 429150.81, 'new_value': 474657.74}, {'field': 'dailyBillAmount', 'old_value': 429150.81, 'new_value': 474657.74}, {'field': 'amount', 'old_value': 409616.74, 'new_value': 435269.41}, {'field': 'count', 'old_value': 7695, 'new_value': 8207}, {'field': 'instoreAmount', 'old_value': 381827.02, 'new_value': 406350.69}, {'field': 'instoreCount', 'old_value': 7154, 'new_value': 7645}, {'field': 'onlineAmount', 'old_value': 29366.86, 'new_value': 30690.56}, {'field': 'onlineCount', 'old_value': 541, 'new_value': 562}]
2025-05-25 08:09:17,623 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-05-25 08:09:17,623 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 311455.17, 'new_value': 340345.32}, {'field': 'amount', 'old_value': 279169.9, 'new_value': 308060.05}, {'field': 'count', 'old_value': 6719, 'new_value': 7380}, {'field': 'instoreAmount', 'old_value': 223135.0, 'new_value': 243212.5}, {'field': 'instoreCount', 'old_value': 4904, 'new_value': 5328}, {'field': 'onlineAmount', 'old_value': 56194.7, 'new_value': 65007.35}, {'field': 'onlineCount', 'old_value': 1815, 'new_value': 2052}]
2025-05-25 08:09:18,060 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUL
2025-05-25 08:09:18,060 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-05, 变更字段: [{'field': 'amount', 'old_value': 31729.579999999998, 'new_value': 31196.579999999998}, {'field': 'count', 'old_value': 832, 'new_value': 834}, {'field': 'instoreAmount', 'old_value': 2428.0, 'new_value': 2471.0}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 41}, {'field': 'onlineAmount', 'old_value': 44960.729999999996, 'new_value': 44979.729999999996}, {'field': 'onlineCount', 'old_value': 792, 'new_value': 793}]
2025-05-25 08:09:18,545 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-05-25 08:09:18,545 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81992.45, 'new_value': 92187.0}, {'field': 'dailyBillAmount', 'old_value': 81992.45, 'new_value': 92187.0}, {'field': 'amount', 'old_value': 151756.5, 'new_value': 159263.79}, {'field': 'count', 'old_value': 10285, 'new_value': 10840}, {'field': 'instoreAmount', 'old_value': 124138.86, 'new_value': 129135.04000000001}, {'field': 'instoreCount', 'old_value': 8184, 'new_value': 8540}, {'field': 'onlineAmount', 'old_value': 31223.78, 'new_value': 33897.58}, {'field': 'onlineCount', 'old_value': 2101, 'new_value': 2300}]
2025-05-25 08:09:19,013 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-05-25 08:09:19,013 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 242246.36, 'new_value': 259910.03}, {'field': 'dailyBillAmount', 'old_value': 242246.36, 'new_value': 259910.03}, {'field': 'amount', 'old_value': 232824.56, 'new_value': 251007.86000000002}, {'field': 'count', 'old_value': 6787, 'new_value': 7339}, {'field': 'instoreAmount', 'old_value': 234389.07, 'new_value': 252687.97}, {'field': 'instoreCount', 'old_value': 6787, 'new_value': 7339}]
2025-05-25 08:09:19,466 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-05-25 08:09:19,466 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 72302.75, 'new_value': 76568.49}, {'field': 'amount', 'old_value': 72299.5, 'new_value': 76565.24}, {'field': 'count', 'old_value': 3883, 'new_value': 4126}, {'field': 'instoreAmount', 'old_value': 41204.72, 'new_value': 43395.159999999996}, {'field': 'instoreCount', 'old_value': 2388, 'new_value': 2523}, {'field': 'onlineAmount', 'old_value': 31098.03, 'new_value': 33173.33}, {'field': 'onlineCount', 'old_value': 1495, 'new_value': 1603}]
2025-05-25 08:09:19,857 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-05-25 08:09:19,857 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 125467.63, 'new_value': 133910.93}, {'field': 'dailyBillAmount', 'old_value': 125467.63, 'new_value': 133910.93}, {'field': 'amount', 'old_value': 26466.850000000002, 'new_value': 27750.64}, {'field': 'count', 'old_value': 940, 'new_value': 989}, {'field': 'instoreAmount', 'old_value': 27428.68, 'new_value': 28715.68}, {'field': 'instoreCount', 'old_value': 940, 'new_value': 989}]
2025-05-25 08:09:20,263 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-05-25 08:09:20,279 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 102562.97, 'new_value': 110164.97}, {'field': 'dailyBillAmount', 'old_value': 102562.97, 'new_value': 110164.97}, {'field': 'amount', 'old_value': 87351.09, 'new_value': 91372.5}, {'field': 'count', 'old_value': 4341, 'new_value': 4544}, {'field': 'instoreAmount', 'old_value': 19017.510000000002, 'new_value': 19615.510000000002}, {'field': 'instoreCount', 'old_value': 1365, 'new_value': 1411}, {'field': 'onlineAmount', 'old_value': 69813.41, 'new_value': 73275.82}, {'field': 'onlineCount', 'old_value': 2976, 'new_value': 3133}]
2025-05-25 08:09:20,748 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-05-25 08:09:20,748 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92993.06, 'new_value': 102052.96}, {'field': 'amount', 'old_value': 92992.18, 'new_value': 102051.55}, {'field': 'count', 'old_value': 2450, 'new_value': 2649}, {'field': 'instoreAmount', 'old_value': 89413.27, 'new_value': 98147.07}, {'field': 'instoreCount', 'old_value': 2384, 'new_value': 2578}, {'field': 'onlineAmount', 'old_value': 4638.31, 'new_value': 4964.41}, {'field': 'onlineCount', 'old_value': 66, 'new_value': 71}]
2025-05-25 08:09:21,185 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-05-25 08:09:21,201 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'amount', 'old_value': 150786.82, 'new_value': 157752.05}, {'field': 'count', 'old_value': 6086, 'new_value': 6440}, {'field': 'instoreAmount', 'old_value': 153814.46, 'new_value': 160929.89}, {'field': 'instoreCount', 'old_value': 6031, 'new_value': 6383}, {'field': 'onlineAmount', 'old_value': 2031.51, 'new_value': 2094.41}, {'field': 'onlineCount', 'old_value': 55, 'new_value': 57}]
2025-05-25 08:09:21,623 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-05-25 08:09:21,623 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 109516.83, 'new_value': 115032.27}, {'field': 'count', 'old_value': 8989, 'new_value': 9392}, {'field': 'instoreAmount', 'old_value': 7747.16, 'new_value': 8292.11}, {'field': 'instoreCount', 'old_value': 425, 'new_value': 456}, {'field': 'onlineAmount', 'old_value': 106803.36, 'new_value': 111890.24}, {'field': 'onlineCount', 'old_value': 8564, 'new_value': 8936}]
2025-05-25 08:09:22,154 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-05-25 08:09:22,154 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 148125.45, 'new_value': 156378.1}, {'field': 'dailyBillAmount', 'old_value': 148125.45, 'new_value': 156378.1}, {'field': 'amount', 'old_value': 126465.15, 'new_value': 133575.01}, {'field': 'count', 'old_value': 4137, 'new_value': 4380}, {'field': 'instoreAmount', 'old_value': 68942.8, 'new_value': 72922.51}, {'field': 'instoreCount', 'old_value': 2965, 'new_value': 3146}, {'field': 'onlineAmount', 'old_value': 66159.52, 'new_value': 69346.82}, {'field': 'onlineCount', 'old_value': 1172, 'new_value': 1234}]
2025-05-25 08:09:22,576 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3M
2025-05-25 08:09:22,576 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 117247.45, 'new_value': 118927.45}, {'field': 'amount', 'old_value': 117246.92, 'new_value': 118926.92}, {'field': 'count', 'old_value': 77, 'new_value': 78}, {'field': 'instoreAmount', 'old_value': 117247.45, 'new_value': 118927.45}, {'field': 'instoreCount', 'old_value': 77, 'new_value': 78}]
2025-05-25 08:09:23,060 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-05-25 08:09:23,060 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 52881.87, 'new_value': 56056.33}, {'field': 'dailyBillAmount', 'old_value': 52881.87, 'new_value': 56056.33}, {'field': 'amount', 'old_value': 71212.56, 'new_value': 74629.14}, {'field': 'count', 'old_value': 2756, 'new_value': 2913}, {'field': 'instoreAmount', 'old_value': 22655.29, 'new_value': 23948.27}, {'field': 'instoreCount', 'old_value': 965, 'new_value': 1032}, {'field': 'onlineAmount', 'old_value': 49557.83, 'new_value': 51755.23}, {'field': 'onlineCount', 'old_value': 1791, 'new_value': 1881}]
2025-05-25 08:09:23,419 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-05-25 08:09:23,419 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 86371.79, 'new_value': 92667.36}, {'field': 'dailyBillAmount', 'old_value': 86371.79, 'new_value': 92667.36}, {'field': 'amount', 'old_value': 88904.24, 'new_value': 95407.69}, {'field': 'count', 'old_value': 3158, 'new_value': 3374}, {'field': 'instoreAmount', 'old_value': 88904.24, 'new_value': 95407.69}, {'field': 'instoreCount', 'old_value': 3158, 'new_value': 3374}]
2025-05-25 08:09:23,857 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-05-25 08:09:23,857 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 297176.0, 'new_value': 305588.0}, {'field': 'dailyBillAmount', 'old_value': 297176.0, 'new_value': 305588.0}, {'field': 'amount', 'old_value': 322097.0, 'new_value': 330709.0}, {'field': 'count', 'old_value': 259, 'new_value': 267}, {'field': 'instoreAmount', 'old_value': 352276.0, 'new_value': 360888.0}, {'field': 'instoreCount', 'old_value': 259, 'new_value': 267}]
2025-05-25 08:09:24,294 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-05-25 08:09:24,294 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 211858.01, 'new_value': 228910.88999999998}, {'field': 'dailyBillAmount', 'old_value': 211858.01, 'new_value': 228910.88999999998}, {'field': 'amount', 'old_value': 221900.96, 'new_value': 233094.96}, {'field': 'count', 'old_value': 427, 'new_value': 452}, {'field': 'instoreAmount', 'old_value': 224574.46, 'new_value': 235768.46}, {'field': 'instoreCount', 'old_value': 427, 'new_value': 452}]
2025-05-25 08:09:24,748 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8M
2025-05-25 08:09:24,748 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41186.0, 'new_value': 42192.0}, {'field': 'amount', 'old_value': 41186.0, 'new_value': 42192.0}, {'field': 'count', 'old_value': 87, 'new_value': 91}, {'field': 'instoreAmount', 'old_value': 41186.0, 'new_value': 42192.0}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 91}]
2025-05-25 08:09:25,216 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-05-25 08:09:25,216 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 69020.0, 'new_value': 72839.0}, {'field': 'dailyBillAmount', 'old_value': 69020.0, 'new_value': 72839.0}, {'field': 'amount', 'old_value': 33900.0, 'new_value': 37340.0}, {'field': 'count', 'old_value': 91, 'new_value': 102}, {'field': 'instoreAmount', 'old_value': 35362.0, 'new_value': 38802.0}, {'field': 'instoreCount', 'old_value': 91, 'new_value': 102}]
2025-05-25 08:09:25,607 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-05-25 08:09:25,607 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 61315.0, 'new_value': 63458.0}, {'field': 'dailyBillAmount', 'old_value': 44996.0, 'new_value': 46317.0}, {'field': 'amount', 'old_value': 57073.0, 'new_value': 59216.0}, {'field': 'count', 'old_value': 76, 'new_value': 79}, {'field': 'instoreAmount', 'old_value': 57073.0, 'new_value': 59216.0}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 79}]
2025-05-25 08:09:26,076 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-05-25 08:09:26,076 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 61695.299999999996, 'new_value': 66948.1}, {'field': 'amount', 'old_value': 61693.1, 'new_value': 66945.9}, {'field': 'count', 'old_value': 167, 'new_value': 180}, {'field': 'instoreAmount', 'old_value': 62184.0, 'new_value': 67436.8}, {'field': 'instoreCount', 'old_value': 167, 'new_value': 180}]
2025-05-25 08:09:26,466 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM571
2025-05-25 08:09:26,466 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 543000.0, 'new_value': 572096.0}, {'field': 'dailyBillAmount', 'old_value': 543000.0, 'new_value': 572096.0}]
2025-05-25 08:09:26,888 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM671
2025-05-25 08:09:26,888 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 84122.0, 'new_value': 88371.0}, {'field': 'amount', 'old_value': 84122.0, 'new_value': 88371.0}, {'field': 'count', 'old_value': 22, 'new_value': 23}, {'field': 'instoreAmount', 'old_value': 84122.0, 'new_value': 88371.0}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 23}]
2025-05-25 08:09:27,357 - INFO - 更新表单数据成功: FINST-KLF66WC1ZU6VH5NR8PRRSCENITCO29B1M6DAM6G
2025-05-25 08:09:27,357 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22467.0, 'new_value': 25955.0}, {'field': 'amount', 'old_value': 22467.0, 'new_value': 25955.0}, {'field': 'count', 'old_value': 35, 'new_value': 40}, {'field': 'instoreAmount', 'old_value': 22467.0, 'new_value': 25955.0}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 40}]
2025-05-25 08:09:27,841 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM771
2025-05-25 08:09:27,841 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 58606.0, 'new_value': 66770.0}, {'field': 'amount', 'old_value': 58606.0, 'new_value': 66770.0}, {'field': 'count', 'old_value': 67, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 58606.0, 'new_value': 66770.0}, {'field': 'instoreCount', 'old_value': 67, 'new_value': 74}]
2025-05-25 08:09:28,388 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-05-25 08:09:28,388 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 220973.8, 'new_value': 237728.1}, {'field': 'dailyBillAmount', 'old_value': 220973.8, 'new_value': 237728.1}, {'field': 'amount', 'old_value': 312838.2, 'new_value': 322942.1}, {'field': 'count', 'old_value': 390, 'new_value': 405}, {'field': 'instoreAmount', 'old_value': 325835.26, 'new_value': 335939.16000000003}, {'field': 'instoreCount', 'old_value': 390, 'new_value': 405}]
2025-05-25 08:09:28,873 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-05-25 08:09:28,873 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 99661.45, 'new_value': 105597.04}, {'field': 'dailyBillAmount', 'old_value': 99661.45, 'new_value': 105597.04}, {'field': 'amount', 'old_value': 37771.1, 'new_value': 44344.5}, {'field': 'count', 'old_value': 366, 'new_value': 434}, {'field': 'instoreAmount', 'old_value': 37304.68, 'new_value': 43388.58}, {'field': 'instoreCount', 'old_value': 315, 'new_value': 377}, {'field': 'onlineAmount', 'old_value': 3028.52, 'new_value': 3518.02}, {'field': 'onlineCount', 'old_value': 51, 'new_value': 57}]
2025-05-25 08:09:29,341 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMA71
2025-05-25 08:09:29,341 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 12757.0, 'new_value': 14499.0}, {'field': 'amount', 'old_value': 12757.0, 'new_value': 14499.0}, {'field': 'count', 'old_value': 35, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 12757.0, 'new_value': 14499.0}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 38}]
2025-05-25 08:09:29,794 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-05-25 08:09:29,794 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32542.0, 'new_value': 35741.0}, {'field': 'dailyBillAmount', 'old_value': 32542.0, 'new_value': 35741.0}, {'field': 'amount', 'old_value': 37829.0, 'new_value': 41028.0}, {'field': 'count', 'old_value': 123, 'new_value': 131}, {'field': 'instoreAmount', 'old_value': 37829.0, 'new_value': 41028.0}, {'field': 'instoreCount', 'old_value': 123, 'new_value': 131}]
2025-05-25 08:09:30,232 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMC71
2025-05-25 08:09:30,232 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32065.7, 'new_value': 33333.7}, {'field': 'amount', 'old_value': 32065.7, 'new_value': 33333.7}, {'field': 'count', 'old_value': 194, 'new_value': 202}, {'field': 'instoreAmount', 'old_value': 32403.7, 'new_value': 33671.7}, {'field': 'instoreCount', 'old_value': 194, 'new_value': 202}]
2025-05-25 08:09:30,576 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-05-25 08:09:30,576 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 7722.0, 'new_value': 8048.0}, {'field': 'dailyBillAmount', 'old_value': 7722.0, 'new_value': 8048.0}, {'field': 'amount', 'old_value': 36844.0, 'new_value': 39448.0}, {'field': 'count', 'old_value': 113, 'new_value': 121}, {'field': 'instoreAmount', 'old_value': 37619.0, 'new_value': 40223.0}, {'field': 'instoreCount', 'old_value': 113, 'new_value': 121}]
2025-05-25 08:09:31,013 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-05-25 08:09:31,013 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 45334.24, 'new_value': 46552.11}, {'field': 'count', 'old_value': 437, 'new_value': 449}, {'field': 'instoreAmount', 'old_value': 36806.64, 'new_value': 36981.659999999996}, {'field': 'instoreCount', 'old_value': 307, 'new_value': 310}, {'field': 'onlineAmount', 'old_value': 9550.63, 'new_value': 10593.48}, {'field': 'onlineCount', 'old_value': 130, 'new_value': 139}]
2025-05-25 08:09:31,576 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-05-25 08:09:31,576 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68410.0, 'new_value': 78998.5}, {'field': 'amount', 'old_value': 68210.0, 'new_value': 78798.5}, {'field': 'count', 'old_value': 88, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 69217.0, 'new_value': 81345.0}, {'field': 'instoreCount', 'old_value': 88, 'new_value': 97}]
2025-05-25 08:09:32,138 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMG71
2025-05-25 08:09:32,138 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15910.0, 'new_value': 16378.0}, {'field': 'amount', 'old_value': 15910.0, 'new_value': 16378.0}, {'field': 'count', 'old_value': 27, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 15910.0, 'new_value': 16378.0}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 28}]
2025-05-25 08:09:32,669 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-05-25 08:09:32,669 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21495.07, 'new_value': 22954.27}, {'field': 'amount', 'old_value': 21494.37, 'new_value': 22953.57}, {'field': 'count', 'old_value': 87, 'new_value': 92}, {'field': 'instoreAmount', 'old_value': 21495.07, 'new_value': 22954.27}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 92}]
2025-05-25 08:09:33,107 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMI71
2025-05-25 08:09:33,107 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38327.0, 'new_value': 41824.0}, {'field': 'dailyBillAmount', 'old_value': 38327.0, 'new_value': 41824.0}, {'field': 'amount', 'old_value': 38526.0, 'new_value': 42023.0}, {'field': 'count', 'old_value': 94, 'new_value': 103}, {'field': 'instoreAmount', 'old_value': 39772.0, 'new_value': 43269.0}, {'field': 'instoreCount', 'old_value': 94, 'new_value': 103}]
2025-05-25 08:09:33,623 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-05-25 08:09:33,623 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 320418.67, 'new_value': 335451.07}, {'field': 'dailyBillAmount', 'old_value': 298987.88, 'new_value': 312775.6}, {'field': 'amount', 'old_value': 318438.64, 'new_value': 333471.04}, {'field': 'count', 'old_value': 675, 'new_value': 814}, {'field': 'instoreAmount', 'old_value': 321926.7, 'new_value': 336959.1}, {'field': 'instoreCount', 'old_value': 675, 'new_value': 814}]
2025-05-25 08:09:34,060 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMK71
2025-05-25 08:09:34,060 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 57611.0, 'new_value': 64329.0}, {'field': 'amount', 'old_value': 57611.0, 'new_value': 64329.0}, {'field': 'count', 'old_value': 263, 'new_value': 286}, {'field': 'instoreAmount', 'old_value': 58365.0, 'new_value': 65257.0}, {'field': 'instoreCount', 'old_value': 263, 'new_value': 286}]
2025-05-25 08:09:34,560 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-05-25 08:09:34,560 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 74124.08, 'new_value': 88760.79}, {'field': 'dailyBillAmount', 'old_value': 74124.08, 'new_value': 88760.79}, {'field': 'amount', 'old_value': 77386.59999999999, 'new_value': 92023.31}, {'field': 'count', 'old_value': 474, 'new_value': 560}, {'field': 'instoreAmount', 'old_value': 77386.59999999999, 'new_value': 92023.31}, {'field': 'instoreCount', 'old_value': 474, 'new_value': 560}]
2025-05-25 08:09:35,013 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-05-25 08:09:35,013 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 33315.77, 'new_value': 33570.88}, {'field': 'count', 'old_value': 3283, 'new_value': 3311}, {'field': 'instoreAmount', 'old_value': 35482.5, 'new_value': 35769.0}, {'field': 'instoreCount', 'old_value': 3283, 'new_value': 3311}]
2025-05-25 08:09:35,451 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-05-25 08:09:35,451 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 559966.46, 'new_value': 590780.69}, {'field': 'dailyBillAmount', 'old_value': 559966.46, 'new_value': 590780.69}, {'field': 'amount', 'old_value': 574260.71, 'new_value': 605750.95}, {'field': 'count', 'old_value': 5672, 'new_value': 5962}, {'field': 'instoreAmount', 'old_value': 433926.09, 'new_value': 460739.26}, {'field': 'instoreCount', 'old_value': 2171, 'new_value': 2306}, {'field': 'onlineAmount', 'old_value': 145273.3, 'new_value': 150081.37}, {'field': 'onlineCount', 'old_value': 3501, 'new_value': 3656}]
2025-05-25 08:09:35,935 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-05-25 08:09:35,935 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 170180.28, 'new_value': 184540.88}, {'field': 'amount', 'old_value': 170180.28, 'new_value': 184540.88}, {'field': 'count', 'old_value': 1150, 'new_value': 1241}, {'field': 'instoreAmount', 'old_value': 170615.28, 'new_value': 184975.88}, {'field': 'instoreCount', 'old_value': 1150, 'new_value': 1241}]
2025-05-25 08:09:36,341 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-05-25 08:09:36,341 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 87183.48, 'new_value': 90846.29}, {'field': 'dailyBillAmount', 'old_value': 87183.48, 'new_value': 90846.29}, {'field': 'amount', 'old_value': 105802.82, 'new_value': 110901.69}, {'field': 'count', 'old_value': 4873, 'new_value': 5165}, {'field': 'instoreAmount', 'old_value': 53679.75, 'new_value': 56645.57}, {'field': 'instoreCount', 'old_value': 2814, 'new_value': 2974}, {'field': 'onlineAmount', 'old_value': 53264.5, 'new_value': 55496.95}, {'field': 'onlineCount', 'old_value': 2059, 'new_value': 2191}]
2025-05-25 08:09:36,763 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR71
2025-05-25 08:09:36,763 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 2169.0, 'new_value': 2520.0}, {'field': 'amount', 'old_value': 2169.0, 'new_value': 2520.0}, {'field': 'count', 'old_value': 111, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 2169.0, 'new_value': 2520.0}, {'field': 'instoreCount', 'old_value': 111, 'new_value': 114}]
2025-05-25 08:09:37,169 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS71
2025-05-25 08:09:37,169 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45961.0, 'new_value': 66193.0}, {'field': 'amount', 'old_value': 45961.0, 'new_value': 66193.0}, {'field': 'count', 'old_value': 28, 'new_value': 34}, {'field': 'instoreAmount', 'old_value': 45961.0, 'new_value': 66193.0}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 34}]
2025-05-25 08:09:37,529 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-05-25 08:09:37,529 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 111402.33, 'new_value': 116701.31}, {'field': 'dailyBillAmount', 'old_value': 111402.33, 'new_value': 116701.31}, {'field': 'amount', 'old_value': 54194.58, 'new_value': 56255.42}, {'field': 'count', 'old_value': 3775, 'new_value': 3918}, {'field': 'instoreAmount', 'old_value': 7416.4, 'new_value': 7872.98}, {'field': 'instoreCount', 'old_value': 319, 'new_value': 335}, {'field': 'onlineAmount', 'old_value': 46778.18, 'new_value': 48382.44}, {'field': 'onlineCount', 'old_value': 3456, 'new_value': 3583}]
2025-05-25 08:09:37,982 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-05-25 08:09:37,982 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 315705.66, 'new_value': 335386.9}, {'field': 'dailyBillAmount', 'old_value': 315705.66, 'new_value': 335386.9}, {'field': 'amount', 'old_value': 295571.88, 'new_value': 314652.18}, {'field': 'count', 'old_value': 2613, 'new_value': 2775}, {'field': 'instoreAmount', 'old_value': 214251.69, 'new_value': 228978.49}, {'field': 'instoreCount', 'old_value': 1070, 'new_value': 1153}, {'field': 'onlineAmount', 'old_value': 81321.41, 'new_value': 85674.91}, {'field': 'onlineCount', 'old_value': 1543, 'new_value': 1622}]
2025-05-25 08:09:38,513 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-05-25 08:09:38,513 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 349515.69, 'new_value': 374022.33999999997}, {'field': 'dailyBillAmount', 'old_value': 349515.69, 'new_value': 374022.33999999997}, {'field': 'amount', 'old_value': 358756.36, 'new_value': 383789.76}, {'field': 'count', 'old_value': 2186, 'new_value': 2333}, {'field': 'instoreAmount', 'old_value': 324564.06, 'new_value': 348346.96}, {'field': 'instoreCount', 'old_value': 1821, 'new_value': 1958}, {'field': 'onlineAmount', 'old_value': 40116.9, 'new_value': 41584.1}, {'field': 'onlineCount', 'old_value': 365, 'new_value': 375}]
2025-05-25 08:09:38,919 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-05-25 08:09:38,919 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 900782.55, 'new_value': 951521.51}, {'field': 'dailyBillAmount', 'old_value': 900782.55, 'new_value': 951521.51}, {'field': 'amount', 'old_value': 999437.66, 'new_value': 1056472.81}, {'field': 'count', 'old_value': 5561, 'new_value': 5882}, {'field': 'instoreAmount', 'old_value': 749735.58, 'new_value': 797341.08}, {'field': 'instoreCount', 'old_value': 3020, 'new_value': 3214}, {'field': 'onlineAmount', 'old_value': 257720.68, 'new_value': 267525.73}, {'field': 'onlineCount', 'old_value': 2541, 'new_value': 2668}]
2025-05-25 08:09:39,388 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-05-25 08:09:39,388 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 293243.36, 'new_value': 314009.85}, {'field': 'dailyBillAmount', 'old_value': 293243.36, 'new_value': 314009.85}, {'field': 'amount', 'old_value': 415534.35, 'new_value': 445265.01}, {'field': 'count', 'old_value': 1948, 'new_value': 2092}, {'field': 'instoreAmount', 'old_value': 389878.72, 'new_value': 418592.26}, {'field': 'instoreCount', 'old_value': 1554, 'new_value': 1678}, {'field': 'onlineAmount', 'old_value': 26245.83, 'new_value': 27432.95}, {'field': 'onlineCount', 'old_value': 394, 'new_value': 414}]
2025-05-25 08:09:39,888 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-05-25 08:09:39,888 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 347855.81, 'new_value': 367988.38}, {'field': 'dailyBillAmount', 'old_value': 347855.81, 'new_value': 367988.38}, {'field': 'amount', 'old_value': 326924.9, 'new_value': 346069.6}, {'field': 'count', 'old_value': 1461, 'new_value': 1552}, {'field': 'instoreAmount', 'old_value': 332867.5, 'new_value': 352530.2}, {'field': 'instoreCount', 'old_value': 1461, 'new_value': 1552}]
2025-05-25 08:09:40,357 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-05-25 08:09:40,357 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 712183.28, 'new_value': 758741.65}, {'field': 'amount', 'old_value': 712182.58, 'new_value': 758740.95}, {'field': 'count', 'old_value': 5724, 'new_value': 6062}, {'field': 'instoreAmount', 'old_value': 712183.28, 'new_value': 758741.65}, {'field': 'instoreCount', 'old_value': 5724, 'new_value': 6062}]
2025-05-25 08:09:40,794 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-05-25 08:09:40,794 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 580377.72, 'new_value': 617852.57}, {'field': 'dailyBillAmount', 'old_value': 580377.72, 'new_value': 617852.57}, {'field': 'amount', 'old_value': 722499.75, 'new_value': 765075.51}, {'field': 'count', 'old_value': 5026, 'new_value': 5321}, {'field': 'instoreAmount', 'old_value': 398825.4, 'new_value': 424555.2}, {'field': 'instoreCount', 'old_value': 2106, 'new_value': 2238}, {'field': 'onlineAmount', 'old_value': 333300.8, 'new_value': 350417.5}, {'field': 'onlineCount', 'old_value': 2920, 'new_value': 3083}]
2025-05-25 08:09:41,279 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-05-25 08:09:41,279 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 363115.61, 'new_value': 395431.71}, {'field': 'dailyBillAmount', 'old_value': 363115.61, 'new_value': 395431.71}, {'field': 'amount', 'old_value': 432607.0, 'new_value': 464728.23}, {'field': 'count', 'old_value': 4802, 'new_value': 5096}, {'field': 'instoreAmount', 'old_value': 295104.22000000003, 'new_value': 319329.92}, {'field': 'instoreCount', 'old_value': 2029, 'new_value': 2191}, {'field': 'onlineAmount', 'old_value': 139229.57, 'new_value': 147354.53}, {'field': 'onlineCount', 'old_value': 2773, 'new_value': 2905}]
2025-05-25 08:09:41,732 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-05-25 08:09:41,732 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 465199.74, 'new_value': 492810.35}, {'field': 'dailyBillAmount', 'old_value': 465199.74, 'new_value': 492810.35}, {'field': 'amount', 'old_value': 471551.01, 'new_value': 499560.85}, {'field': 'count', 'old_value': 4483, 'new_value': 4746}, {'field': 'instoreAmount', 'old_value': 410023.11, 'new_value': 435226.44}, {'field': 'instoreCount', 'old_value': 2346, 'new_value': 2511}, {'field': 'onlineAmount', 'old_value': 62573.04, 'new_value': 65379.6}, {'field': 'onlineCount', 'old_value': 2137, 'new_value': 2235}]
2025-05-25 08:09:42,154 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-05-25 08:09:42,154 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 115680.8, 'new_value': 120325.8}, {'field': 'amount', 'old_value': 115680.3, 'new_value': 120325.3}, {'field': 'count', 'old_value': 526, 'new_value': 558}, {'field': 'instoreAmount', 'old_value': 115680.8, 'new_value': 120325.8}, {'field': 'instoreCount', 'old_value': 526, 'new_value': 558}]
2025-05-25 08:09:42,638 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-05-25 08:09:42,638 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 328859.75, 'new_value': 344795.55}, {'field': 'dailyBillAmount', 'old_value': 328859.75, 'new_value': 344795.55}, {'field': 'amount', 'old_value': -249134.48, 'new_value': -266299.18}, {'field': 'count', 'old_value': 898, 'new_value': 933}, {'field': 'instoreAmount', 'old_value': 6241.3, 'new_value': 6512.1}, {'field': 'instoreCount', 'old_value': 298, 'new_value': 308}, {'field': 'onlineAmount', 'old_value': 18868.67, 'new_value': 19393.37}, {'field': 'onlineCount', 'old_value': 600, 'new_value': 625}]
2025-05-25 08:09:43,044 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-05-25 08:09:43,044 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 547479.1799999999, 'new_value': 583852.16}, {'field': 'dailyBillAmount', 'old_value': 547479.1799999999, 'new_value': 583852.16}, {'field': 'amount', 'old_value': 412096.15, 'new_value': 439855.15}, {'field': 'count', 'old_value': 1725, 'new_value': 1845}, {'field': 'instoreAmount', 'old_value': 412096.15, 'new_value': 439855.15}, {'field': 'instoreCount', 'old_value': 1725, 'new_value': 1845}]
2025-05-25 08:09:43,497 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-05-25 08:09:43,497 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 350540.85, 'new_value': 378022.27999999997}, {'field': 'dailyBillAmount', 'old_value': 350540.85, 'new_value': 378022.27999999997}, {'field': 'amount', 'old_value': 147173.2, 'new_value': 158899.9}, {'field': 'count', 'old_value': 607, 'new_value': 652}, {'field': 'instoreAmount', 'old_value': 153165.2, 'new_value': 165043.2}, {'field': 'instoreCount', 'old_value': 588, 'new_value': 631}, {'field': 'onlineAmount', 'old_value': 1406.2, 'new_value': 1471.8999999999999}, {'field': 'onlineCount', 'old_value': 19, 'new_value': 21}]
2025-05-25 08:09:43,966 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-05-25 08:09:43,966 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 280069.96, 'new_value': 298684.21}, {'field': 'dailyBillAmount', 'old_value': 280069.96, 'new_value': 298684.21}, {'field': 'amount', 'old_value': 271201.23, 'new_value': 289294.12}, {'field': 'count', 'old_value': 1797, 'new_value': 1908}, {'field': 'instoreAmount', 'old_value': 255428.79, 'new_value': 272720.60000000003}, {'field': 'instoreCount', 'old_value': 1380, 'new_value': 1462}, {'field': 'onlineAmount', 'old_value': 15936.59, 'new_value': 16737.67}, {'field': 'onlineCount', 'old_value': 417, 'new_value': 446}]
2025-05-25 08:09:44,372 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-05-25 08:09:44,372 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 303948.11, 'new_value': 321226.3}, {'field': 'dailyBillAmount', 'old_value': 303948.11, 'new_value': 321226.3}, {'field': 'amount', 'old_value': 128844.61, 'new_value': 135750.61}, {'field': 'count', 'old_value': 2188, 'new_value': 2299}, {'field': 'instoreAmount', 'old_value': 74203.24, 'new_value': 78654.58}, {'field': 'instoreCount', 'old_value': 565, 'new_value': 598}, {'field': 'onlineAmount', 'old_value': 54644.62, 'new_value': 57099.28}, {'field': 'onlineCount', 'old_value': 1623, 'new_value': 1701}]
2025-05-25 08:09:44,935 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM881
2025-05-25 08:09:44,935 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-05, 变更字段: [{'field': 'amount', 'old_value': 56445.0, 'new_value': 56804.0}, {'field': 'count', 'old_value': 31, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 56445.0, 'new_value': 56804.0}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 32}]
2025-05-25 08:09:45,341 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-05-25 08:09:45,341 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 127666.19, 'new_value': 134323.35}, {'field': 'amount', 'old_value': 127655.79000000001, 'new_value': 134312.63}, {'field': 'count', 'old_value': 5834, 'new_value': 6143}, {'field': 'instoreAmount', 'old_value': 46302.36, 'new_value': 48556.56}, {'field': 'instoreCount', 'old_value': 1818, 'new_value': 1916}, {'field': 'onlineAmount', 'old_value': 88258.89, 'new_value': 92950.84}, {'field': 'onlineCount', 'old_value': 4016, 'new_value': 4227}]
2025-05-25 08:09:45,794 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-05-25 08:09:45,794 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40571.9, 'new_value': 42471.9}, {'field': 'amount', 'old_value': 40571.9, 'new_value': 42471.9}, {'field': 'count', 'old_value': 182, 'new_value': 190}, {'field': 'instoreAmount', 'old_value': 40571.9, 'new_value': 42471.9}, {'field': 'instoreCount', 'old_value': 182, 'new_value': 190}]
2025-05-25 08:09:46,232 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-05-25 08:09:46,232 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 352224.21, 'new_value': 379453.1}, {'field': 'dailyBillAmount', 'old_value': 352224.21, 'new_value': 379453.1}, {'field': 'amount', 'old_value': 140663.2, 'new_value': 152515.2}, {'field': 'count', 'old_value': 2631, 'new_value': 2846}, {'field': 'instoreAmount', 'old_value': 141863.3, 'new_value': 153724.5}, {'field': 'instoreCount', 'old_value': 2631, 'new_value': 2846}]
2025-05-25 08:09:46,747 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-05-25 08:09:46,747 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 145143.0, 'new_value': 155641.44}, {'field': 'amount', 'old_value': 145141.8, 'new_value': 155640.24}, {'field': 'count', 'old_value': 3479, 'new_value': 3715}, {'field': 'instoreAmount', 'old_value': 145400.48, 'new_value': 155898.92}, {'field': 'instoreCount', 'old_value': 3479, 'new_value': 3715}]
2025-05-25 08:09:47,279 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-05-25 08:09:47,279 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27719.75, 'new_value': 29777.65}, {'field': 'amount', 'old_value': 27715.53, 'new_value': 29773.059999999998}, {'field': 'count', 'old_value': 1651, 'new_value': 1800}, {'field': 'instoreAmount', 'old_value': 14350.84, 'new_value': 15498.94}, {'field': 'instoreCount', 'old_value': 717, 'new_value': 771}, {'field': 'onlineAmount', 'old_value': 13848.84, 'new_value': 14797.210000000001}, {'field': 'onlineCount', 'old_value': 934, 'new_value': 1029}]
2025-05-25 08:09:47,701 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-05-25 08:09:47,701 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45183.1, 'new_value': 49222.4}, {'field': 'amount', 'old_value': 45183.1, 'new_value': 49222.4}, {'field': 'count', 'old_value': 114, 'new_value': 123}, {'field': 'instoreAmount', 'old_value': 45183.1, 'new_value': 49222.4}, {'field': 'instoreCount', 'old_value': 114, 'new_value': 123}]
2025-05-25 08:09:48,169 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-05-25 08:09:48,169 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 173328.36, 'new_value': 184100.62}, {'field': 'dailyBillAmount', 'old_value': 143523.4, 'new_value': 152237.8}, {'field': 'amount', 'old_value': 173327.68, 'new_value': 184099.94}, {'field': 'count', 'old_value': 2451, 'new_value': 2588}, {'field': 'instoreAmount', 'old_value': 165648.1, 'new_value': 175885.0}, {'field': 'instoreCount', 'old_value': 2127, 'new_value': 2248}, {'field': 'onlineAmount', 'old_value': 7907.38, 'new_value': 8442.74}, {'field': 'onlineCount', 'old_value': 324, 'new_value': 340}]
2025-05-25 08:09:48,591 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-05-25 08:09:48,591 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 25574.44, 'new_value': 27790.52}, {'field': 'amount', 'old_value': 25573.64, 'new_value': 27789.72}, {'field': 'count', 'old_value': 1091, 'new_value': 1187}, {'field': 'instoreAmount', 'old_value': 21230.94, 'new_value': 23171.420000000002}, {'field': 'instoreCount', 'old_value': 970, 'new_value': 1057}, {'field': 'onlineAmount', 'old_value': 4429.2, 'new_value': 4704.8}, {'field': 'onlineCount', 'old_value': 121, 'new_value': 130}]
2025-05-25 08:09:49,122 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-05-25 08:09:49,122 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 355107.29, 'new_value': 377773.77}, {'field': 'dailyBillAmount', 'old_value': 355107.29, 'new_value': 377773.77}, {'field': 'amount', 'old_value': 459728.53, 'new_value': 486872.31}, {'field': 'count', 'old_value': 4833, 'new_value': 5045}, {'field': 'instoreAmount', 'old_value': 432681.63, 'new_value': 458641.33}, {'field': 'instoreCount', 'old_value': 3335, 'new_value': 3487}, {'field': 'onlineAmount', 'old_value': 36696.61, 'new_value': 38243.56}, {'field': 'onlineCount', 'old_value': 1498, 'new_value': 1558}]
2025-05-25 08:09:49,529 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-05-25 08:09:49,529 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 137850.73, 'new_value': 146538.64}, {'field': 'dailyBillAmount', 'old_value': 137850.73, 'new_value': 146538.64}, {'field': 'amount', 'old_value': 34762.340000000004, 'new_value': 36814.29}, {'field': 'count', 'old_value': 562, 'new_value': 588}, {'field': 'instoreAmount', 'old_value': 22188.28, 'new_value': 24021.73}, {'field': 'instoreCount', 'old_value': 293, 'new_value': 310}, {'field': 'onlineAmount', 'old_value': 13438.15, 'new_value': 13679.65}, {'field': 'onlineCount', 'old_value': 269, 'new_value': 278}]
2025-05-25 08:09:49,997 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-05-25 08:09:49,997 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 138404.68, 'new_value': 146173.03}, {'field': 'dailyBillAmount', 'old_value': 123442.6, 'new_value': 131215.3}, {'field': 'amount', 'old_value': 138402.8, 'new_value': 146170.46}, {'field': 'count', 'old_value': 7802, 'new_value': 8230}, {'field': 'instoreAmount', 'old_value': 84611.39, 'new_value': 90109.59}, {'field': 'instoreCount', 'old_value': 4674, 'new_value': 4962}, {'field': 'onlineAmount', 'old_value': 55556.07, 'new_value': 57888.79}, {'field': 'onlineCount', 'old_value': 3128, 'new_value': 3268}]
2025-05-25 08:09:50,451 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-05-25 08:09:50,451 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 73476.3, 'new_value': 77790.4}, {'field': 'amount', 'old_value': 73466.86, 'new_value': 77780.16}, {'field': 'count', 'old_value': 4712, 'new_value': 5001}, {'field': 'instoreAmount', 'old_value': 32526.83, 'new_value': 34678.26}, {'field': 'instoreCount', 'old_value': 1884, 'new_value': 2021}, {'field': 'onlineAmount', 'old_value': 43187.18, 'new_value': 45504.46}, {'field': 'onlineCount', 'old_value': 2828, 'new_value': 2980}]
2025-05-25 08:09:50,951 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-05-25 08:09:50,951 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'amount', 'old_value': 142961.5, 'new_value': 153842.22}, {'field': 'count', 'old_value': 1442, 'new_value': 1534}, {'field': 'instoreAmount', 'old_value': 143161.41, 'new_value': 154067.13}, {'field': 'instoreCount', 'old_value': 1442, 'new_value': 1534}]
2025-05-25 08:09:51,372 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-05-25 08:09:51,372 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 118465.18, 'new_value': 124619.78}, {'field': 'dailyBillAmount', 'old_value': 122502.43, 'new_value': 128880.59}, {'field': 'amount', 'old_value': 118459.37, 'new_value': 124613.97}, {'field': 'count', 'old_value': 2401, 'new_value': 2562}, {'field': 'instoreAmount', 'old_value': 113457.0, 'new_value': 119258.78}, {'field': 'instoreCount', 'old_value': 2012, 'new_value': 2145}, {'field': 'onlineAmount', 'old_value': 5116.5, 'new_value': 5469.32}, {'field': 'onlineCount', 'old_value': 389, 'new_value': 417}]
2025-05-25 08:09:51,904 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-05-25 08:09:51,904 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 190567.44, 'new_value': 195691.91999999998}, {'field': 'dailyBillAmount', 'old_value': 190567.44, 'new_value': 195691.91999999998}, {'field': 'amount', 'old_value': 24540.22, 'new_value': 25293.11}, {'field': 'count', 'old_value': 962, 'new_value': 996}, {'field': 'instoreAmount', 'old_value': 28091.91, 'new_value': 29011.2}, {'field': 'instoreCount', 'old_value': 962, 'new_value': 996}]
2025-05-25 08:09:52,310 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-05-25 08:09:52,310 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 482115.96, 'new_value': 516105.86}, {'field': 'dailyBillAmount', 'old_value': 482115.96, 'new_value': 516105.86}, {'field': 'amount', 'old_value': 48905.86, 'new_value': 51489.659999999996}, {'field': 'count', 'old_value': 238, 'new_value': 249}, {'field': 'instoreAmount', 'old_value': 49131.86, 'new_value': 51715.659999999996}, {'field': 'instoreCount', 'old_value': 238, 'new_value': 249}]
2025-05-25 08:09:52,779 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-05-25 08:09:52,779 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 16387.22, 'new_value': 16927.67}, {'field': 'count', 'old_value': 844, 'new_value': 865}, {'field': 'onlineAmount', 'old_value': 16519.27, 'new_value': 17093.57}, {'field': 'onlineCount', 'old_value': 844, 'new_value': 865}]
2025-05-25 08:09:53,294 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-05-25 08:09:53,294 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 294278.08, 'new_value': 306760.25}, {'field': 'amount', 'old_value': 294124.3, 'new_value': 306606.47}, {'field': 'count', 'old_value': 3042, 'new_value': 3131}, {'field': 'instoreAmount', 'old_value': 279985.7, 'new_value': 291835.5}, {'field': 'instoreCount', 'old_value': 2562, 'new_value': 2637}, {'field': 'onlineAmount', 'old_value': 19531.350000000002, 'new_value': 20183.54}, {'field': 'onlineCount', 'old_value': 480, 'new_value': 494}]
2025-05-25 08:09:53,763 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-05-25 08:09:53,763 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 168065.52, 'new_value': 173311.26}, {'field': 'dailyBillAmount', 'old_value': 164265.05, 'new_value': 169510.79}, {'field': 'amount', 'old_value': 125300.07, 'new_value': 128862.75}, {'field': 'count', 'old_value': 4462, 'new_value': 4619}, {'field': 'instoreAmount', 'old_value': 54419.49, 'new_value': 55079.83}, {'field': 'instoreCount', 'old_value': 1861, 'new_value': 1883}, {'field': 'onlineAmount', 'old_value': 72701.7, 'new_value': 75604.04000000001}, {'field': 'onlineCount', 'old_value': 2601, 'new_value': 2736}]
2025-05-25 08:09:54,279 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-05-25 08:09:54,279 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59523.0, 'new_value': 62375.36}, {'field': 'dailyBillAmount', 'old_value': 59523.0, 'new_value': 62375.36}, {'field': 'amount', 'old_value': 4740.33, 'new_value': 4888.53}, {'field': 'count', 'old_value': 219, 'new_value': 225}, {'field': 'instoreAmount', 'old_value': 4740.33, 'new_value': 4888.53}, {'field': 'instoreCount', 'old_value': 219, 'new_value': 225}]
2025-05-25 08:09:54,701 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-05-25 08:09:54,701 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 5718.2699999999995, 'new_value': 6006.75}, {'field': 'count', 'old_value': 250, 'new_value': 260}, {'field': 'onlineAmount', 'old_value': 5718.2699999999995, 'new_value': 6006.75}, {'field': 'onlineCount', 'old_value': 250, 'new_value': 260}]
2025-05-25 08:09:55,263 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-05-25 08:09:55,263 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 98665.43, 'new_value': 102895.62}, {'field': 'dailyBillAmount', 'old_value': 48855.74, 'new_value': 51335.14}, {'field': 'amount', 'old_value': 98664.84, 'new_value': 102895.03}, {'field': 'count', 'old_value': 2426, 'new_value': 2544}, {'field': 'instoreAmount', 'old_value': 53266.56, 'new_value': 55660.26}, {'field': 'instoreCount', 'old_value': 1289, 'new_value': 1355}, {'field': 'onlineAmount', 'old_value': 48237.3, 'new_value': 50172.49}, {'field': 'onlineCount', 'old_value': 1137, 'new_value': 1189}]
2025-05-25 08:09:55,716 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-05-25 08:09:55,716 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49346.67, 'new_value': 51509.62}, {'field': 'amount', 'old_value': 49346.67, 'new_value': 51509.62}, {'field': 'count', 'old_value': 1851, 'new_value': 1944}, {'field': 'instoreAmount', 'old_value': 49936.43, 'new_value': 52112.68}, {'field': 'instoreCount', 'old_value': 1851, 'new_value': 1944}]
2025-05-25 08:09:56,200 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-05-25 08:09:56,200 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48110.69, 'new_value': 49685.53}, {'field': 'dailyBillAmount', 'old_value': 48110.69, 'new_value': 49685.53}, {'field': 'amount', 'old_value': 37401.41, 'new_value': 38268.04}, {'field': 'count', 'old_value': 1715, 'new_value': 1769}, {'field': 'instoreAmount', 'old_value': 20381.43, 'new_value': 20791.11}, {'field': 'instoreCount', 'old_value': 696, 'new_value': 711}, {'field': 'onlineAmount', 'old_value': 17102.2, 'new_value': 17559.15}, {'field': 'onlineCount', 'old_value': 1019, 'new_value': 1058}]
2025-05-25 08:09:56,654 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-05-25 08:09:56,654 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77046.33, 'new_value': 80515.55}, {'field': 'amount', 'old_value': 77046.33, 'new_value': 80515.55}, {'field': 'count', 'old_value': 2368, 'new_value': 2468}, {'field': 'instoreAmount', 'old_value': 30760.54, 'new_value': 32489.84}, {'field': 'instoreCount', 'old_value': 1176, 'new_value': 1232}, {'field': 'onlineAmount', 'old_value': 46387.28, 'new_value': 48127.2}, {'field': 'onlineCount', 'old_value': 1192, 'new_value': 1236}]
2025-05-25 08:09:57,091 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-05-25 08:09:57,091 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46875.25, 'new_value': 48172.45}, {'field': 'amount', 'old_value': 46874.35, 'new_value': 48171.55}, {'field': 'count', 'old_value': 1110, 'new_value': 1152}, {'field': 'instoreAmount', 'old_value': 36159.3, 'new_value': 37233.8}, {'field': 'instoreCount', 'old_value': 888, 'new_value': 922}, {'field': 'onlineAmount', 'old_value': 11191.960000000001, 'new_value': 11414.66}, {'field': 'onlineCount', 'old_value': 222, 'new_value': 230}]
2025-05-25 08:09:57,497 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-05-25 08:09:57,497 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 215914.69, 'new_value': 224422.93}, {'field': 'dailyBillAmount', 'old_value': 215914.69, 'new_value': 224422.93}, {'field': 'amount', 'old_value': 146743.51, 'new_value': 151692.52}, {'field': 'count', 'old_value': 3696, 'new_value': 3841}, {'field': 'instoreAmount', 'old_value': 92733.52, 'new_value': 96288.42}, {'field': 'instoreCount', 'old_value': 1827, 'new_value': 1911}, {'field': 'onlineAmount', 'old_value': 66307.33, 'new_value': 68264.44}, {'field': 'onlineCount', 'old_value': 1869, 'new_value': 1930}]
2025-05-25 08:09:57,950 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-05-25 08:09:57,950 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'amount', 'old_value': 598788.0, 'new_value': 656545.0}, {'field': 'count', 'old_value': 3576, 'new_value': 3902}, {'field': 'instoreAmount', 'old_value': 422015.0, 'new_value': 469733.2}, {'field': 'instoreCount', 'old_value': 2753, 'new_value': 3029}, {'field': 'onlineAmount', 'old_value': 176775.4, 'new_value': 186814.2}, {'field': 'onlineCount', 'old_value': 823, 'new_value': 873}]
2025-05-25 08:09:58,357 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-05-25 08:09:58,357 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 970525.92, 'new_value': 1019949.32}, {'field': 'amount', 'old_value': 970525.42, 'new_value': 1019948.82}, {'field': 'count', 'old_value': 3408, 'new_value': 3585}, {'field': 'instoreAmount', 'old_value': 970525.92, 'new_value': 1019949.32}, {'field': 'instoreCount', 'old_value': 3408, 'new_value': 3585}]
2025-05-25 08:09:58,732 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-05-25 08:09:58,732 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 561512.55, 'new_value': 587886.49}, {'field': 'dailyBillAmount', 'old_value': 497743.0, 'new_value': 521977.8}, {'field': 'amount', 'old_value': 561512.55, 'new_value': 587886.49}, {'field': 'count', 'old_value': 3488, 'new_value': 3623}, {'field': 'instoreAmount', 'old_value': 512356.02, 'new_value': 537169.41}, {'field': 'instoreCount', 'old_value': 2198, 'new_value': 2299}, {'field': 'onlineAmount', 'old_value': 49528.51, 'new_value': 51089.06}, {'field': 'onlineCount', 'old_value': 1290, 'new_value': 1324}]
2025-05-25 08:09:59,185 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-05-25 08:09:59,185 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 552101.64, 'new_value': 573645.66}, {'field': 'dailyBillAmount', 'old_value': 535290.15, 'new_value': 556824.17}, {'field': 'amount', 'old_value': 552095.05, 'new_value': 573639.07}, {'field': 'count', 'old_value': 1337, 'new_value': 1401}, {'field': 'instoreAmount', 'old_value': 514421.8, 'new_value': 533078.4}, {'field': 'instoreCount', 'old_value': 1034, 'new_value': 1083}, {'field': 'onlineAmount', 'old_value': 37807.12, 'new_value': 40694.54}, {'field': 'onlineCount', 'old_value': 303, 'new_value': 318}]
2025-05-25 08:09:59,685 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-05-25 08:09:59,685 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 668389.05, 'new_value': 709567.19}, {'field': 'amount', 'old_value': 668388.37, 'new_value': 709566.51}, {'field': 'count', 'old_value': 3535, 'new_value': 3722}, {'field': 'instoreAmount', 'old_value': 628648.46, 'new_value': 667656.46}, {'field': 'instoreCount', 'old_value': 2342, 'new_value': 2472}, {'field': 'onlineAmount', 'old_value': 39856.74, 'new_value': 42055.53}, {'field': 'onlineCount', 'old_value': 1193, 'new_value': 1250}]
2025-05-25 08:10:00,107 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-05-25 08:10:00,107 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 762968.14, 'new_value': 789624.1}, {'field': 'dailyBillAmount', 'old_value': 762968.14, 'new_value': 789624.1}, {'field': 'amount', 'old_value': 687366.35, 'new_value': 714019.63}, {'field': 'count', 'old_value': 3430, 'new_value': 3565}, {'field': 'instoreAmount', 'old_value': 630821.97, 'new_value': 655009.98}, {'field': 'instoreCount', 'old_value': 2841, 'new_value': 2952}, {'field': 'onlineAmount', 'old_value': 57273.25, 'new_value': 59802.06}, {'field': 'onlineCount', 'old_value': 589, 'new_value': 613}]
2025-05-25 08:10:00,575 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-05-25 08:10:00,575 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 185992.84, 'new_value': 197077.84}, {'field': 'dailyBillAmount', 'old_value': 184586.29, 'new_value': 195671.29}, {'field': 'amount', 'old_value': 182427.66, 'new_value': 193266.66}, {'field': 'count', 'old_value': 267, 'new_value': 285}, {'field': 'instoreAmount', 'old_value': 182427.66, 'new_value': 193266.66}, {'field': 'instoreCount', 'old_value': 267, 'new_value': 285}]
2025-05-25 08:10:01,013 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-05-25 08:10:01,013 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 156890.23, 'new_value': 160083.73}, {'field': 'dailyBillAmount', 'old_value': 156890.23, 'new_value': 160083.73}, {'field': 'amount', 'old_value': 138257.64, 'new_value': 141886.85}, {'field': 'count', 'old_value': 232, 'new_value': 238}, {'field': 'instoreAmount', 'old_value': 135852.2, 'new_value': 138944.8}, {'field': 'instoreCount', 'old_value': 213, 'new_value': 218}, {'field': 'onlineAmount', 'old_value': 3372.36, 'new_value': 3909.37}, {'field': 'onlineCount', 'old_value': 19, 'new_value': 20}]
2025-05-25 08:10:01,450 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-05-25 08:10:01,450 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20293.37, 'new_value': 21793.11}, {'field': 'amount', 'old_value': 20293.37, 'new_value': 21793.11}, {'field': 'count', 'old_value': 427, 'new_value': 452}, {'field': 'instoreAmount', 'old_value': 20293.37, 'new_value': 21793.11}, {'field': 'instoreCount', 'old_value': 427, 'new_value': 452}]
2025-05-25 08:10:01,919 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-05-25 08:10:01,919 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 86649.95, 'new_value': 91642.64}, {'field': 'amount', 'old_value': 86649.95, 'new_value': 91642.64}, {'field': 'count', 'old_value': 735, 'new_value': 771}, {'field': 'instoreAmount', 'old_value': 87200.79, 'new_value': 92193.48}, {'field': 'instoreCount', 'old_value': 735, 'new_value': 771}]
2025-05-25 08:10:02,372 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-05-25 08:10:02,372 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 287743.3, 'new_value': 303452.38}, {'field': 'dailyBillAmount', 'old_value': 287743.3, 'new_value': 303452.38}, {'field': 'amount', 'old_value': 305937.72000000003, 'new_value': 321364.93}, {'field': 'count', 'old_value': 8335, 'new_value': 8779}, {'field': 'instoreAmount', 'old_value': 289866.3, 'new_value': 303890.24}, {'field': 'instoreCount', 'old_value': 7527, 'new_value': 7906}, {'field': 'onlineAmount', 'old_value': 20679.51, 'new_value': 22272.579999999998}, {'field': 'onlineCount', 'old_value': 808, 'new_value': 873}]
2025-05-25 08:10:02,825 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC91
2025-05-25 08:10:02,825 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 95591.54, 'new_value': 97393.94}, {'field': 'dailyBillAmount', 'old_value': 95591.54, 'new_value': 97393.94}, {'field': 'amount', 'old_value': 97895.54, 'new_value': 99697.94}, {'field': 'count', 'old_value': 79, 'new_value': 81}, {'field': 'instoreAmount', 'old_value': 97895.54, 'new_value': 99697.94}, {'field': 'instoreCount', 'old_value': 79, 'new_value': 81}]
2025-05-25 08:10:03,310 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-05-25 08:10:03,310 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 773392.61, 'new_value': 806865.13}, {'field': 'dailyBillAmount', 'old_value': 773392.61, 'new_value': 806865.13}, {'field': 'amount', 'old_value': 708519.12, 'new_value': 728960.2}, {'field': 'count', 'old_value': 1780, 'new_value': 1854}, {'field': 'instoreAmount', 'old_value': 735215.51, 'new_value': 761856.45}, {'field': 'instoreCount', 'old_value': 1476, 'new_value': 1540}, {'field': 'onlineAmount', 'old_value': 6997.88, 'new_value': 7214.14}, {'field': 'onlineCount', 'old_value': 304, 'new_value': 314}]
2025-05-25 08:10:03,732 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-05-25 08:10:03,732 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1147770.77, 'new_value': 1200086.04}, {'field': 'amount', 'old_value': 1147770.77, 'new_value': 1200086.04}, {'field': 'count', 'old_value': 3674, 'new_value': 3865}, {'field': 'instoreAmount', 'old_value': 1148981.77, 'new_value': 1201297.04}, {'field': 'instoreCount', 'old_value': 3674, 'new_value': 3865}]
2025-05-25 08:10:04,138 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-05-25 08:10:04,138 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 769275.25, 'new_value': 805158.71}, {'field': 'dailyBillAmount', 'old_value': 769275.25, 'new_value': 805158.71}, {'field': 'amount', 'old_value': 713681.53, 'new_value': 747853.4}, {'field': 'count', 'old_value': 2569, 'new_value': 2671}, {'field': 'instoreAmount', 'old_value': 695035.08, 'new_value': 728851.19}, {'field': 'instoreCount', 'old_value': 1567, 'new_value': 1634}, {'field': 'onlineAmount', 'old_value': 30661.32, 'new_value': 31983.52}, {'field': 'onlineCount', 'old_value': 1002, 'new_value': 1037}]
2025-05-25 08:10:04,560 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-05-25 08:10:04,560 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1683737.39, 'new_value': 1757001.39}, {'field': 'dailyBillAmount', 'old_value': 1683737.39, 'new_value': 1757001.39}, {'field': 'amount', 'old_value': 1735222.0, 'new_value': 1807529.0}, {'field': 'count', 'old_value': 4622, 'new_value': 4773}, {'field': 'instoreAmount', 'old_value': 1735222.0, 'new_value': 1807529.0}, {'field': 'instoreCount', 'old_value': 4622, 'new_value': 4773}]
2025-05-25 08:10:05,091 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-05-25 08:10:05,091 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 263213.44, 'new_value': 280244.95}, {'field': 'dailyBillAmount', 'old_value': 263213.44, 'new_value': 280244.95}, {'field': 'amount', 'old_value': 269155.22000000003, 'new_value': 285699.73}, {'field': 'count', 'old_value': 1453, 'new_value': 1536}, {'field': 'instoreAmount', 'old_value': 260553.2, 'new_value': 276846.7}, {'field': 'instoreCount', 'old_value': 1226, 'new_value': 1300}, {'field': 'onlineAmount', 'old_value': 13718.9, 'new_value': 14494.91}, {'field': 'onlineCount', 'old_value': 227, 'new_value': 236}]
2025-05-25 08:10:05,529 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-05-25 08:10:05,529 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 904745.26, 'new_value': 955056.97}, {'field': 'dailyBillAmount', 'old_value': 904745.26, 'new_value': 955056.97}, {'field': 'amount', 'old_value': 961798.75, 'new_value': 1012114.76}, {'field': 'count', 'old_value': 3997, 'new_value': 4203}, {'field': 'instoreAmount', 'old_value': 961799.2000000001, 'new_value': 1012115.21}, {'field': 'instoreCount', 'old_value': 3997, 'new_value': 4203}]
2025-05-25 08:10:05,935 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-05-25 08:10:05,935 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 399008.13, 'new_value': 422979.76}, {'field': 'dailyBillAmount', 'old_value': 399008.13, 'new_value': 422979.76}, {'field': 'amount', 'old_value': 648671.6, 'new_value': 682402.5}, {'field': 'count', 'old_value': 1093, 'new_value': 1147}, {'field': 'instoreAmount', 'old_value': 643818.48, 'new_value': 677479.68}, {'field': 'instoreCount', 'old_value': 1060, 'new_value': 1112}, {'field': 'onlineAmount', 'old_value': 5118.6, 'new_value': 5255.6}, {'field': 'onlineCount', 'old_value': 33, 'new_value': 35}]
2025-05-25 08:10:06,419 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-05-25 08:10:06,419 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 228787.89, 'new_value': 245769.34}, {'field': 'dailyBillAmount', 'old_value': 228787.89, 'new_value': 245769.34}, {'field': 'amount', 'old_value': 264112.3, 'new_value': 283519.3}, {'field': 'count', 'old_value': 1852, 'new_value': 1968}, {'field': 'instoreAmount', 'old_value': 268012.3, 'new_value': 287419.3}, {'field': 'instoreCount', 'old_value': 1852, 'new_value': 1968}]
2025-05-25 08:10:06,935 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-05-25 08:10:06,935 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 151484.5, 'new_value': 162287.21}, {'field': 'dailyBillAmount', 'old_value': 151484.5, 'new_value': 162287.21}, {'field': 'amount', 'old_value': 121902.87, 'new_value': 131526.87}, {'field': 'count', 'old_value': 814, 'new_value': 884}, {'field': 'instoreAmount', 'old_value': 121674.0, 'new_value': 131298.0}, {'field': 'instoreCount', 'old_value': 762, 'new_value': 832}]
2025-05-25 08:10:07,435 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMI01
2025-05-25 08:10:07,435 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 47053.5, 'new_value': 50803.5}, {'field': 'dailyBillAmount', 'old_value': 47053.5, 'new_value': 50803.5}]
2025-05-25 08:10:07,919 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-05-25 08:10:07,919 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 140450.99, 'new_value': 147240.71}, {'field': 'count', 'old_value': 6801, 'new_value': 7128}, {'field': 'instoreAmount', 'old_value': 75065.01, 'new_value': 77822.25}, {'field': 'instoreCount', 'old_value': 3831, 'new_value': 3973}, {'field': 'onlineAmount', 'old_value': 69431.98, 'new_value': 73589.51}, {'field': 'onlineCount', 'old_value': 2970, 'new_value': 3155}]
2025-05-25 08:10:08,404 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-05-25 08:10:08,404 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 199881.29, 'new_value': 209304.93}, {'field': 'amount', 'old_value': 199871.78, 'new_value': 209295.4}, {'field': 'count', 'old_value': 3762, 'new_value': 3945}, {'field': 'instoreAmount', 'old_value': 183603.91999999998, 'new_value': 191763.81}, {'field': 'instoreCount', 'old_value': 3445, 'new_value': 3592}, {'field': 'onlineAmount', 'old_value': 16277.37, 'new_value': 17541.12}, {'field': 'onlineCount', 'old_value': 317, 'new_value': 353}]
2025-05-25 08:10:08,950 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-05-25 08:10:08,950 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30472.7, 'new_value': 31157.1}, {'field': 'amount', 'old_value': 30472.7, 'new_value': 31157.1}, {'field': 'count', 'old_value': 198, 'new_value': 205}, {'field': 'instoreAmount', 'old_value': 30472.7, 'new_value': 31157.1}, {'field': 'instoreCount', 'old_value': 198, 'new_value': 205}]
2025-05-25 08:10:09,372 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-05-25 08:10:09,372 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51212.7, 'new_value': 60590.7}, {'field': 'dailyBillAmount', 'old_value': 51212.7, 'new_value': 60590.7}, {'field': 'amount', 'old_value': 45993.6, 'new_value': 48086.9}, {'field': 'count', 'old_value': 423, 'new_value': 436}, {'field': 'instoreAmount', 'old_value': 46214.0, 'new_value': 48307.3}, {'field': 'instoreCount', 'old_value': 423, 'new_value': 436}]
2025-05-25 08:10:09,825 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP91
2025-05-25 08:10:09,825 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51000.0, 'new_value': 53374.0}, {'field': 'dailyBillAmount', 'old_value': 51000.0, 'new_value': 53374.0}]
2025-05-25 08:10:10,325 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-05-25 08:10:10,325 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 203973.7, 'new_value': 222839.3}, {'field': 'dailyBillAmount', 'old_value': 203973.7, 'new_value': 222839.3}, {'field': 'amount', 'old_value': 168014.73, 'new_value': 176621.5}, {'field': 'count', 'old_value': 4685, 'new_value': 4922}, {'field': 'instoreAmount', 'old_value': 163656.74, 'new_value': 172076.13}, {'field': 'instoreCount', 'old_value': 4511, 'new_value': 4738}, {'field': 'onlineAmount', 'old_value': 6822.18, 'new_value': 7283.28}, {'field': 'onlineCount', 'old_value': 174, 'new_value': 184}]
2025-05-25 08:10:10,794 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-05-25 08:10:10,794 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49699.9, 'new_value': 50940.2}, {'field': 'dailyBillAmount', 'old_value': 49699.9, 'new_value': 50940.2}, {'field': 'amount', 'old_value': 49745.4, 'new_value': 50985.7}, {'field': 'count', 'old_value': 286, 'new_value': 296}, {'field': 'instoreAmount', 'old_value': 52261.6, 'new_value': 53501.9}, {'field': 'instoreCount', 'old_value': 283, 'new_value': 293}]
2025-05-25 08:10:11,232 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-05-25 08:10:11,232 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 67038.66, 'new_value': 70015.65}, {'field': 'dailyBillAmount', 'old_value': 67038.66, 'new_value': 70015.65}]
2025-05-25 08:10:11,638 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-05-25 08:10:11,638 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44833.76, 'new_value': 46618.92}, {'field': 'amount', 'old_value': 44833.520000000004, 'new_value': 46618.68}, {'field': 'count', 'old_value': 2622, 'new_value': 2736}, {'field': 'instoreAmount', 'old_value': 45612.11, 'new_value': 47410.8}, {'field': 'instoreCount', 'old_value': 2622, 'new_value': 2736}]
2025-05-25 08:10:12,044 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-05-25 08:10:12,044 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 71998.83, 'new_value': 74246.1}, {'field': 'dailyBillAmount', 'old_value': 71998.83, 'new_value': 74246.1}, {'field': 'amount', 'old_value': 74130.39, 'new_value': 76477.54}, {'field': 'count', 'old_value': 3644, 'new_value': 3762}, {'field': 'instoreAmount', 'old_value': 69020.6, 'new_value': 71079.2}, {'field': 'instoreCount', 'old_value': 3423, 'new_value': 3526}, {'field': 'onlineAmount', 'old_value': 5175.04, 'new_value': 5480.39}, {'field': 'onlineCount', 'old_value': 221, 'new_value': 236}]
2025-05-25 08:10:12,513 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-05-25 08:10:12,513 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48855.86, 'new_value': 51265.67}, {'field': 'amount', 'old_value': 48855.86, 'new_value': 51265.67}, {'field': 'count', 'old_value': 2391, 'new_value': 2505}, {'field': 'instoreAmount', 'old_value': 30339.93, 'new_value': 31768.170000000002}, {'field': 'instoreCount', 'old_value': 1575, 'new_value': 1646}, {'field': 'onlineAmount', 'old_value': 18615.579999999998, 'new_value': 19597.91}, {'field': 'onlineCount', 'old_value': 816, 'new_value': 859}]
2025-05-25 08:10:12,997 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-05-25 08:10:13,013 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 35197.43, 'new_value': 36566.83}, {'field': 'dailyBillAmount', 'old_value': 35197.43, 'new_value': 36566.83}, {'field': 'amount', 'old_value': 25089.13, 'new_value': 25659.95}, {'field': 'count', 'old_value': 1011, 'new_value': 1035}, {'field': 'instoreAmount', 'old_value': 25344.17, 'new_value': 25932.47}, {'field': 'instoreCount', 'old_value': 1011, 'new_value': 1035}]
2025-05-25 08:10:13,435 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-05-25 08:10:13,435 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64896.14, 'new_value': 67724.94}, {'field': 'amount', 'old_value': 64889.44, 'new_value': 67717.32}, {'field': 'count', 'old_value': 3885, 'new_value': 4038}, {'field': 'instoreAmount', 'old_value': 17086.53, 'new_value': 17838.31}, {'field': 'instoreCount', 'old_value': 1004, 'new_value': 1033}, {'field': 'onlineAmount', 'old_value': 49696.94, 'new_value': 51837.96}, {'field': 'onlineCount', 'old_value': 2881, 'new_value': 3005}]
2025-05-25 08:10:13,857 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-05-25 08:10:13,857 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 123529.77, 'new_value': 128901.32}, {'field': 'dailyBillAmount', 'old_value': 123529.77, 'new_value': 128901.32}, {'field': 'amount', 'old_value': 102852.86, 'new_value': 107461.56}, {'field': 'count', 'old_value': 994, 'new_value': 1030}, {'field': 'instoreAmount', 'old_value': 102852.86, 'new_value': 107461.56}, {'field': 'instoreCount', 'old_value': 994, 'new_value': 1030}]
2025-05-25 08:10:14,325 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-05-25 08:10:14,325 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 94361.15, 'new_value': 101391.15}, {'field': 'dailyBillAmount', 'old_value': 94361.15, 'new_value': 101391.15}, {'field': 'amount', 'old_value': 106489.8, 'new_value': 115787.8}, {'field': 'count', 'old_value': 461, 'new_value': 496}, {'field': 'instoreAmount', 'old_value': 106489.8, 'new_value': 115787.8}, {'field': 'instoreCount', 'old_value': 461, 'new_value': 496}]
2025-05-25 08:10:14,763 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-05-25 08:10:14,763 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59926.6, 'new_value': 65229.6}, {'field': 'dailyBillAmount', 'old_value': 59926.6, 'new_value': 65229.6}, {'field': 'amount', 'old_value': 50776.55, 'new_value': 53264.55}, {'field': 'count', 'old_value': 273, 'new_value': 288}, {'field': 'instoreAmount', 'old_value': 52213.55, 'new_value': 54701.55}, {'field': 'instoreCount', 'old_value': 273, 'new_value': 288}]
2025-05-25 08:10:15,232 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-05-25 08:10:15,232 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 121302.0, 'new_value': 126332.0}, {'field': 'amount', 'old_value': 121302.0, 'new_value': 126332.0}, {'field': 'count', 'old_value': 1250, 'new_value': 1299}, {'field': 'instoreAmount', 'old_value': 121302.0, 'new_value': 126332.0}, {'field': 'instoreCount', 'old_value': 1250, 'new_value': 1299}]
2025-05-25 08:10:15,669 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-05-25 08:10:15,669 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30631.82, 'new_value': 31412.42}, {'field': 'dailyBillAmount', 'old_value': 30631.82, 'new_value': 31412.42}]
2025-05-25 08:10:16,060 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMZ7
2025-05-25 08:10:16,060 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16HSQFKC90AM6QNN0HOT12RK0013M7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21630.7, 'new_value': 22320.63}, {'field': 'dailyBillAmount', 'old_value': 21630.7, 'new_value': 22320.63}, {'field': 'amount', 'old_value': 22323.85, 'new_value': 23043.06}, {'field': 'count', 'old_value': 603, 'new_value': 626}, {'field': 'instoreAmount', 'old_value': 22308.83, 'new_value': 22998.76}, {'field': 'instoreCount', 'old_value': 600, 'new_value': 622}, {'field': 'onlineAmount', 'old_value': 83.53999999999999, 'new_value': 112.82}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 4}]
2025-05-25 08:10:16,528 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-05-25 08:10:16,528 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41357.1, 'new_value': 43778.1}, {'field': 'dailyBillAmount', 'old_value': 41357.1, 'new_value': 43778.1}, {'field': 'amount', 'old_value': 62671.6, 'new_value': 65842.6}, {'field': 'count', 'old_value': 252, 'new_value': 265}, {'field': 'instoreAmount', 'old_value': 62860.6, 'new_value': 66031.6}, {'field': 'instoreCount', 'old_value': 251, 'new_value': 264}]
2025-05-25 08:10:16,997 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-05-25 08:10:16,997 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 39076.0, 'new_value': 40597.0}, {'field': 'dailyBillAmount', 'old_value': 39076.0, 'new_value': 40597.0}, {'field': 'amount', 'old_value': 42708.0, 'new_value': 44154.0}, {'field': 'count', 'old_value': 230, 'new_value': 242}, {'field': 'instoreAmount', 'old_value': 42722.0, 'new_value': 44168.0}, {'field': 'instoreCount', 'old_value': 230, 'new_value': 242}]
2025-05-25 08:10:17,435 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-05-25 08:10:17,435 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 70738.84, 'new_value': 73498.95}, {'field': 'dailyBillAmount', 'old_value': 70738.84, 'new_value': 73498.95}, {'field': 'amount', 'old_value': 62936.04, 'new_value': 65360.08}, {'field': 'count', 'old_value': 2124, 'new_value': 2197}, {'field': 'instoreAmount', 'old_value': 57409.479999999996, 'new_value': 59634.62}, {'field': 'instoreCount', 'old_value': 1865, 'new_value': 1927}, {'field': 'onlineAmount', 'old_value': 5563.0, 'new_value': 5761.9}, {'field': 'onlineCount', 'old_value': 259, 'new_value': 270}]
2025-05-25 08:10:17,841 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-05-25 08:10:17,841 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40080.03, 'new_value': 42315.21}, {'field': 'dailyBillAmount', 'old_value': 40080.03, 'new_value': 42315.21}, {'field': 'amount', 'old_value': 44525.1, 'new_value': 46816.28}, {'field': 'count', 'old_value': 286, 'new_value': 308}, {'field': 'instoreAmount', 'old_value': 43642.21, 'new_value': 45558.21}, {'field': 'instoreCount', 'old_value': 245, 'new_value': 256}, {'field': 'onlineAmount', 'old_value': 1033.49, 'new_value': 1408.67}, {'field': 'onlineCount', 'old_value': 41, 'new_value': 52}]
2025-05-25 08:10:18,372 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-05-25 08:10:18,372 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 198127.58, 'new_value': 204127.94}, {'field': 'dailyBillAmount', 'old_value': 198127.58, 'new_value': 204127.94}, {'field': 'amount', 'old_value': 205443.3, 'new_value': 211763.8}, {'field': 'count', 'old_value': 1335, 'new_value': 1387}, {'field': 'instoreAmount', 'old_value': 198623.7, 'new_value': 204642.7}, {'field': 'instoreCount', 'old_value': 1191, 'new_value': 1240}, {'field': 'onlineAmount', 'old_value': 9686.6, 'new_value': 9988.1}, {'field': 'onlineCount', 'old_value': 144, 'new_value': 147}]
2025-05-25 08:10:18,372 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-25 08:10:18,372 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-25 08:10:18,372 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-25 08:10:18,372 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-25 08:10:18,372 - INFO - 正在批量插入月度数据，批次 1/1，共 4 条记录
2025-05-25 08:10:18,544 - INFO - 批量插入月度数据成功，批次 1，4 条记录
2025-05-25 08:10:21,560 - INFO - 批量插入月度数据完成: 总计 4 条，成功 4 条，失败 0 条
2025-05-25 08:10:21,560 - INFO - 批量插入月销售数据完成，共 4 条记录
2025-05-25 08:10:21,560 - INFO - 月销售数据同步完成！更新: 213 条，插入: 4 条，错误: 0 条，跳过: 975 条
2025-05-25 08:10:21,560 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-6 至 2025-5
2025-05-25 08:10:22,044 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250525.xlsx
2025-05-25 08:10:22,044 - INFO - 综合数据同步流程完成！
2025-05-25 08:10:22,107 - INFO - 综合数据同步完成
2025-05-25 08:10:22,107 - INFO - ==================================================
2025-05-25 08:10:22,107 - INFO - 程序退出
2025-05-25 08:10:22,107 - INFO - ==================================================
