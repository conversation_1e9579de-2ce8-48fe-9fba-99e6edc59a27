2025-07-30 01:30:33,802 - INFO - 使用默认增量同步（当天更新数据）
2025-07-30 01:30:33,802 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-30 01:30:33,802 - INFO - 查询参数: ('2025-07-30',)
2025-07-30 01:30:33,958 - INFO - MySQL查询成功，增量数据（日期: 2025-07-30），共获取 1 条记录
2025-07-30 01:30:33,958 - INFO - 获取到 1 个日期需要处理: ['2025-07-29']
2025-07-30 01:30:33,958 - INFO - 开始处理日期: 2025-07-29
2025-07-30 01:30:33,958 - INFO - Request Parameters - Page 1:
2025-07-30 01:30:33,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 01:30:33,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 01:30:42,083 - ERROR - 处理日期 2025-07-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2D2E1A33-4C60-7EE3-91B6-6599288EBE08 Response: {'code': 'ServiceUnavailable', 'requestid': '2D2E1A33-4C60-7EE3-91B6-6599288EBE08', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2D2E1A33-4C60-7EE3-91B6-6599288EBE08)
2025-07-30 01:30:42,083 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-30 01:31:42,099 - INFO - 开始同步昨天与今天的销售数据: 2025-07-29 至 2025-07-30
2025-07-30 01:31:42,099 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-30 01:31:42,099 - INFO - 查询参数: ('2025-07-29', '2025-07-30')
2025-07-30 01:31:42,255 - INFO - MySQL查询成功，时间段: 2025-07-29 至 2025-07-30，共获取 75 条记录
2025-07-30 01:31:42,255 - INFO - 获取到 1 个日期需要处理: ['2025-07-29']
2025-07-30 01:31:42,255 - INFO - 开始处理日期: 2025-07-29
2025-07-30 01:31:42,255 - INFO - Request Parameters - Page 1:
2025-07-30 01:31:42,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 01:31:42,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 01:31:43,021 - INFO - Response - Page 1:
2025-07-30 01:31:43,021 - INFO - 第 1 页获取到 39 条记录
2025-07-30 01:31:43,536 - INFO - 查询完成，共获取到 39 条记录
2025-07-30 01:31:43,536 - INFO - 获取到 39 条表单数据
2025-07-30 01:31:43,536 - INFO - 当前日期 2025-07-29 有 75 条MySQL数据需要处理
2025-07-30 01:31:43,536 - INFO - 开始批量插入 36 条新记录
2025-07-30 01:31:43,802 - INFO - 批量插入响应状态码: 200
2025-07-30 01:31:43,802 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 17:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1740', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5119E98A-7844-70B3-B16D-9FBF4F439712', 'x-acs-trace-id': 'a6e85a9c2a1780dc843a67f8a53f42b9', 'etag': '1fvZCmWAbXTTFla6AZCrkdA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 01:31:43,802 - INFO - 批量插入响应体: {'result': ['FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM3I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM4I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM5I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM6I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM7I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM8I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM9I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMAI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMBI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMCI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMDI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMEI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMFI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMGI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMHI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMII', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMJI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMKI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMLI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMMI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMNI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMOI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMPI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMQI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMRI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMSI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMTI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMUI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMVI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMWI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMXI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMYI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMZI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM0J', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM1J', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM2J']}
2025-07-30 01:31:43,802 - INFO - 批量插入表单数据成功，批次 1，共 36 条记录
2025-07-30 01:31:43,802 - INFO - 成功插入的数据ID: ['FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM3I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM4I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM5I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM6I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM7I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM8I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM9I', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMAI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMBI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMCI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMDI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMEI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMFI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMGI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMHI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMII', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMJI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMKI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMLI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMMI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMNI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMOI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMPI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMQI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMRI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMSI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMTI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMUI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMVI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMWI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMXI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMYI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODMZI', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM0J', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM1J', 'FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM2J']
2025-07-30 01:31:48,817 - INFO - 批量插入完成，共 36 条记录
2025-07-30 01:31:48,817 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 36 条，错误: 0 条
2025-07-30 01:31:48,817 - INFO - 数据同步完成！更新: 0 条，插入: 36 条，错误: 0 条
2025-07-30 01:31:48,817 - INFO - 同步完成
2025-07-30 04:30:33,666 - INFO - 使用默认增量同步（当天更新数据）
2025-07-30 04:30:33,666 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-30 04:30:33,666 - INFO - 查询参数: ('2025-07-30',)
2025-07-30 04:30:33,822 - INFO - MySQL查询成功，增量数据（日期: 2025-07-30），共获取 1 条记录
2025-07-30 04:30:33,822 - INFO - 获取到 1 个日期需要处理: ['2025-07-29']
2025-07-30 04:30:33,822 - INFO - 开始处理日期: 2025-07-29
2025-07-30 04:30:33,838 - INFO - Request Parameters - Page 1:
2025-07-30 04:30:33,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 04:30:33,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 04:30:41,979 - ERROR - 处理日期 2025-07-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C791E457-24FF-7E65-A68F-740BB9A7C725 Response: {'code': 'ServiceUnavailable', 'requestid': 'C791E457-24FF-7E65-A68F-740BB9A7C725', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C791E457-24FF-7E65-A68F-740BB9A7C725)
2025-07-30 04:30:41,979 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-30 04:31:41,994 - INFO - 开始同步昨天与今天的销售数据: 2025-07-29 至 2025-07-30
2025-07-30 04:31:41,994 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-30 04:31:41,994 - INFO - 查询参数: ('2025-07-29', '2025-07-30')
2025-07-30 04:31:42,134 - INFO - MySQL查询成功，时间段: 2025-07-29 至 2025-07-30，共获取 75 条记录
2025-07-30 04:31:42,134 - INFO - 获取到 1 个日期需要处理: ['2025-07-29']
2025-07-30 04:31:42,150 - INFO - 开始处理日期: 2025-07-29
2025-07-30 04:31:42,150 - INFO - Request Parameters - Page 1:
2025-07-30 04:31:42,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 04:31:42,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 04:31:50,259 - ERROR - 处理日期 2025-07-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1566A59D-74FA-7B3C-A38D-388D64B97880 Response: {'code': 'ServiceUnavailable', 'requestid': '1566A59D-74FA-7B3C-A38D-388D64B97880', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1566A59D-74FA-7B3C-A38D-388D64B97880)
2025-07-30 04:31:50,259 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-30 04:31:50,259 - INFO - 同步完成
2025-07-30 07:30:33,686 - INFO - 使用默认增量同步（当天更新数据）
2025-07-30 07:30:33,686 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-30 07:30:33,686 - INFO - 查询参数: ('2025-07-30',)
2025-07-30 07:30:33,842 - INFO - MySQL查询成功，增量数据（日期: 2025-07-30），共获取 4 条记录
2025-07-30 07:30:33,842 - INFO - 获取到 1 个日期需要处理: ['2025-07-29']
2025-07-30 07:30:33,842 - INFO - 开始处理日期: 2025-07-29
2025-07-30 07:30:33,842 - INFO - Request Parameters - Page 1:
2025-07-30 07:30:33,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 07:30:33,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 07:30:41,967 - ERROR - 处理日期 2025-07-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 603A3245-DDA1-7909-A8BA-DB65B627A7A0 Response: {'code': 'ServiceUnavailable', 'requestid': '603A3245-DDA1-7909-A8BA-DB65B627A7A0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 603A3245-DDA1-7909-A8BA-DB65B627A7A0)
2025-07-30 07:30:41,967 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-30 07:31:41,982 - INFO - 开始同步昨天与今天的销售数据: 2025-07-29 至 2025-07-30
2025-07-30 07:31:41,982 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-30 07:31:41,982 - INFO - 查询参数: ('2025-07-29', '2025-07-30')
2025-07-30 07:31:42,139 - INFO - MySQL查询成功，时间段: 2025-07-29 至 2025-07-30，共获取 95 条记录
2025-07-30 07:31:42,139 - INFO - 获取到 1 个日期需要处理: ['2025-07-29']
2025-07-30 07:31:42,139 - INFO - 开始处理日期: 2025-07-29
2025-07-30 07:31:42,139 - INFO - Request Parameters - Page 1:
2025-07-30 07:31:42,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 07:31:42,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 07:31:42,982 - INFO - Response - Page 1:
2025-07-30 07:31:42,982 - INFO - 第 1 页获取到 50 条记录
2025-07-30 07:31:43,498 - INFO - Request Parameters - Page 2:
2025-07-30 07:31:43,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 07:31:43,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 07:31:44,185 - INFO - Response - Page 2:
2025-07-30 07:31:44,185 - INFO - 第 2 页获取到 25 条记录
2025-07-30 07:31:44,685 - INFO - 查询完成，共获取到 75 条记录
2025-07-30 07:31:44,685 - INFO - 获取到 75 条表单数据
2025-07-30 07:31:44,685 - INFO - 当前日期 2025-07-29 有 93 条MySQL数据需要处理
2025-07-30 07:31:44,685 - INFO - 开始批量插入 18 条新记录
2025-07-30 07:31:44,889 - INFO - 批量插入响应状态码: 200
2025-07-30 07:31:44,889 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Jul 2025 23:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '876', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4512D5EF-5F3B-7183-9885-1228E3156836', 'x-acs-trace-id': '49297f19fee179d293b9bf6605546ddb', 'etag': '8XP0NlS+qaZJ3YvVKD/LHQQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 07:31:44,889 - INFO - 批量插入响应体: {'result': ['FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2WJE86PDM81', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2WJE86PDM91', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2WJE86PDMA1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2WJE86PDMB1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2WJE86PDMC1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMD1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDME1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMF1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMG1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMH1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMI1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMJ1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMK1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDML1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMM1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMN1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMO1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMP1']}
2025-07-30 07:31:44,889 - INFO - 批量插入表单数据成功，批次 1，共 18 条记录
2025-07-30 07:31:44,889 - INFO - 成功插入的数据ID: ['FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2WJE86PDM81', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2WJE86PDM91', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2WJE86PDMA1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2WJE86PDMB1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2WJE86PDMC1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMD1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDME1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMF1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMG1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMH1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMI1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMJ1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMK1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDML1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMM1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMN1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMO1', 'FINST-34B66L91NLKX8VHMF7O9M6IHUXKP2XJE86PDMP1']
2025-07-30 07:31:49,904 - INFO - 批量插入完成，共 18 条记录
2025-07-30 07:31:49,904 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 18 条，错误: 0 条
2025-07-30 07:31:49,904 - INFO - 数据同步完成！更新: 0 条，插入: 18 条，错误: 0 条
2025-07-30 07:31:49,904 - INFO - 同步完成
2025-07-30 10:30:33,972 - INFO - 使用默认增量同步（当天更新数据）
2025-07-30 10:30:33,972 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-30 10:30:33,972 - INFO - 查询参数: ('2025-07-30',)
2025-07-30 10:30:34,144 - INFO - MySQL查询成功，增量数据（日期: 2025-07-30），共获取 178 条记录
2025-07-30 10:30:34,144 - INFO - 获取到 1 个日期需要处理: ['2025-07-29']
2025-07-30 10:30:34,144 - INFO - 开始处理日期: 2025-07-29
2025-07-30 10:30:34,144 - INFO - Request Parameters - Page 1:
2025-07-30 10:30:34,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 10:30:34,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 10:30:42,253 - ERROR - 处理日期 2025-07-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AAD48049-E3AF-75C6-B9C1-77A86DD41064 Response: {'code': 'ServiceUnavailable', 'requestid': 'AAD48049-E3AF-75C6-B9C1-77A86DD41064', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AAD48049-E3AF-75C6-B9C1-77A86DD41064)
2025-07-30 10:30:42,253 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-30 10:31:42,268 - INFO - 开始同步昨天与今天的销售数据: 2025-07-29 至 2025-07-30
2025-07-30 10:31:42,268 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-30 10:31:42,268 - INFO - 查询参数: ('2025-07-29', '2025-07-30')
2025-07-30 10:31:42,440 - INFO - MySQL查询成功，时间段: 2025-07-29 至 2025-07-30，共获取 546 条记录
2025-07-30 10:31:42,440 - INFO - 获取到 1 个日期需要处理: ['2025-07-29']
2025-07-30 10:31:42,440 - INFO - 开始处理日期: 2025-07-29
2025-07-30 10:31:42,440 - INFO - Request Parameters - Page 1:
2025-07-30 10:31:42,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 10:31:42,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 10:31:50,565 - ERROR - 处理日期 2025-07-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 595AF633-D192-77D1-AEEF-7ACB86A429BF Response: {'code': 'ServiceUnavailable', 'requestid': '595AF633-D192-77D1-AEEF-7ACB86A429BF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 595AF633-D192-77D1-AEEF-7ACB86A429BF)
2025-07-30 10:31:50,565 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-30 10:31:50,565 - INFO - 同步完成
2025-07-30 13:30:34,148 - INFO - 使用默认增量同步（当天更新数据）
2025-07-30 13:30:34,148 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-30 13:30:34,148 - INFO - 查询参数: ('2025-07-30',)
2025-07-30 13:30:34,320 - INFO - MySQL查询成功，增量数据（日期: 2025-07-30），共获取 195 条记录
2025-07-30 13:30:34,320 - INFO - 获取到 1 个日期需要处理: ['2025-07-29']
2025-07-30 13:30:34,320 - INFO - 开始处理日期: 2025-07-29
2025-07-30 13:30:34,336 - INFO - Request Parameters - Page 1:
2025-07-30 13:30:34,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 13:30:34,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 13:30:42,445 - ERROR - 处理日期 2025-07-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F906B456-F89F-77A1-88AC-4F4428846467 Response: {'code': 'ServiceUnavailable', 'requestid': 'F906B456-F89F-77A1-88AC-4F4428846467', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F906B456-F89F-77A1-88AC-4F4428846467)
2025-07-30 13:30:42,445 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-30 13:31:42,460 - INFO - 开始同步昨天与今天的销售数据: 2025-07-29 至 2025-07-30
2025-07-30 13:31:42,460 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-30 13:31:42,460 - INFO - 查询参数: ('2025-07-29', '2025-07-30')
2025-07-30 13:31:42,632 - INFO - MySQL查询成功，时间段: 2025-07-29 至 2025-07-30，共获取 564 条记录
2025-07-30 13:31:42,632 - INFO - 获取到 1 个日期需要处理: ['2025-07-29']
2025-07-30 13:31:42,632 - INFO - 开始处理日期: 2025-07-29
2025-07-30 13:31:42,632 - INFO - Request Parameters - Page 1:
2025-07-30 13:31:42,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 13:31:42,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 13:31:50,757 - ERROR - 处理日期 2025-07-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 86BE0273-8DA2-7A61-A017-587A37B7AC3C Response: {'code': 'ServiceUnavailable', 'requestid': '86BE0273-8DA2-7A61-A017-587A37B7AC3C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 86BE0273-8DA2-7A61-A017-587A37B7AC3C)
2025-07-30 13:31:50,757 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-30 13:31:50,757 - INFO - 同步完成
2025-07-30 16:30:33,663 - INFO - 使用默认增量同步（当天更新数据）
2025-07-30 16:30:33,663 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-30 16:30:33,663 - INFO - 查询参数: ('2025-07-30',)
2025-07-30 16:30:33,819 - INFO - MySQL查询成功，增量数据（日期: 2025-07-30），共获取 196 条记录
2025-07-30 16:30:33,819 - INFO - 获取到 1 个日期需要处理: ['2025-07-29']
2025-07-30 16:30:33,834 - INFO - 开始处理日期: 2025-07-29
2025-07-30 16:30:33,834 - INFO - Request Parameters - Page 1:
2025-07-30 16:30:33,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 16:30:33,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 16:30:41,959 - ERROR - 处理日期 2025-07-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 638666BC-2D6E-7C6C-97BC-E9CE66D0F2FB Response: {'code': 'ServiceUnavailable', 'requestid': '638666BC-2D6E-7C6C-97BC-E9CE66D0F2FB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 638666BC-2D6E-7C6C-97BC-E9CE66D0F2FB)
2025-07-30 16:30:41,959 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-30 16:31:41,975 - INFO - 开始同步昨天与今天的销售数据: 2025-07-29 至 2025-07-30
2025-07-30 16:31:41,975 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-30 16:31:41,975 - INFO - 查询参数: ('2025-07-29', '2025-07-30')
2025-07-30 16:31:42,146 - INFO - MySQL查询成功，时间段: 2025-07-29 至 2025-07-30，共获取 565 条记录
2025-07-30 16:31:42,146 - INFO - 获取到 1 个日期需要处理: ['2025-07-29']
2025-07-30 16:31:42,146 - INFO - 开始处理日期: 2025-07-29
2025-07-30 16:31:42,146 - INFO - Request Parameters - Page 1:
2025-07-30 16:31:42,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 16:31:42,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 16:31:42,990 - INFO - Response - Page 1:
2025-07-30 16:31:42,990 - INFO - 第 1 页获取到 50 条记录
2025-07-30 16:31:43,506 - INFO - Request Parameters - Page 2:
2025-07-30 16:31:43,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 16:31:43,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 16:31:51,646 - INFO - Response - Page 2:
2025-07-30 16:31:51,646 - INFO - 第 2 页获取到 43 条记录
2025-07-30 16:31:52,146 - INFO - 查询完成，共获取到 93 条记录
2025-07-30 16:31:52,146 - INFO - 获取到 93 条表单数据
2025-07-30 16:31:52,146 - INFO - 当前日期 2025-07-29 有 548 条MySQL数据需要处理
2025-07-30 16:31:52,146 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM5I
2025-07-30 16:31:52,803 - INFO - 更新表单数据成功: FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM5I
2025-07-30 16:31:52,803 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36788.0, 'new_value': 76788.0}, {'field': 'total_amount', 'old_value': 43344.0, 'new_value': 83344.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/e36cbe3255b0496c88bdea9dc1215761.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=qx1fWO0I3z3WHSN%2BO%2FyZcRttGE4%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/3878a72ca62c449fb461a0105651485f.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=DOX5sPpXRpHJBUFhchnNACW6MZc%3D'}]
2025-07-30 16:31:52,803 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM6I
2025-07-30 16:31:53,318 - INFO - 更新表单数据成功: FINST-PPA66671GCKXE6I5E5RFB9TZYLL827XEDTODM6I
2025-07-30 16:31:53,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 7474.0, 'new_value': 57474.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/8fba8d4994f44c7db22917a53ced533e.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=s0GQlGYJPR2UB2pF%2Bp1AZr16b68%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/321f549287e84c5c947cf6fd0a8be958.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=nhDWNk%2FleEBMew5hUiQJioXirz8%3D'}]
2025-07-30 16:31:53,318 - INFO - 开始批量插入 455 条新记录
2025-07-30 16:31:53,693 - INFO - 批量插入响应状态码: 200
2025-07-30 16:31:53,693 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 08:31:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '11D66694-8707-77B1-936B-64E743E7E414', 'x-acs-trace-id': '4f885d677ba8c7af8b18c92acb02fb14', 'etag': '24Txqfm3Iyph6jr+DBYI9cg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 16:31:53,693 - INFO - 批量插入响应体: {'result': ['FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMRD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMSD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMTD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMUD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMVD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMWD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMXD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMYD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMZD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM0E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM1E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM2E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM3E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM4E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM5E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM6E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM7E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM8E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM9E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMAE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMBE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMCE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMDE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMEE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMFE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMGE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMHE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMIE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMJE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMKE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMLE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMME', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMNE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMOE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMPE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMQE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMRE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMSE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMTE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMUE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMVE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMWE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMXE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMYE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMZE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM0F', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM1F', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM2F', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM3F', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM4F']}
2025-07-30 16:31:53,693 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-30 16:31:53,693 - INFO - 成功插入的数据ID: ['FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMRD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMSD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMTD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMUD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMVD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMWD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMXD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMYD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMZD', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM0E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM1E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM2E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM3E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM4E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM5E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM6E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM7E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM8E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM9E', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMAE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMBE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMCE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMDE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMEE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMFE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMGE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMHE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMIE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMJE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMKE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMLE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMME', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMNE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMOE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMPE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMQE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMRE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMSE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMTE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMUE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMVE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMWE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMXE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMYE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDMZE', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM0F', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM1F', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM2F', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM3F', 'FINST-QUA66S71W9JXOMPDBGFQVCQCNCA339I1JPPDM4F']
2025-07-30 16:31:58,943 - INFO - 批量插入响应状态码: 200
2025-07-30 16:31:58,943 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 08:31:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2410', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3A751B46-2F75-7058-9FFA-C572E28742E0', 'x-acs-trace-id': 'ee348236533c43f5c89cd82aabfedbef', 'etag': '2GGm8mgSZi+WVItV04m997g0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 16:31:58,943 - INFO - 批量插入响应体: {'result': ['FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMY', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMZ', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM01', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM11', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM21', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM31', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM41', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM51', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM61', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM71', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM81', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM91', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMA1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMB1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMC1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMD1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDME1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMF1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMG1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMH1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMI1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMJ1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMK1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDML1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMM1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMN1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMO1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMP1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMQ1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMR1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMS1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMT1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMU1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMV1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMW1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMX1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMY1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMZ1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM02', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM12', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM22', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM32', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM42', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM52', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM62', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM72', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM82', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM92', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO22K5JPPDMA2', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO22K5JPPDMB2']}
2025-07-30 16:31:58,943 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-30 16:31:58,943 - INFO - 成功插入的数据ID: ['FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMY', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMZ', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM01', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM11', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM21', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM31', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM41', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM51', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM61', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM71', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM81', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM91', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMA1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMB1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMC1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMD1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDME1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMF1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMG1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMH1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMI1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMJ1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMK1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDML1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMM1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMN1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMO1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMP1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMQ1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMR1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMS1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMT1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMU1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMV1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMW1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMX1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMY1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDMZ1', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM02', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM12', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM22', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM32', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM42', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM52', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM62', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM72', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM82', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO21K5JPPDM92', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO22K5JPPDMA2', 'FINST-2K666OB1PBLXV6XQCJK9290OFHYO22K5JPPDMB2']
2025-07-30 16:32:04,209 - INFO - 批量插入响应状态码: 200
2025-07-30 16:32:04,209 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 08:32:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '086F6775-9D16-7AF2-A120-ABD70A6440C2', 'x-acs-trace-id': 'b51b188dd2d3905537491c6e4e872493', 'etag': '2Ln2UbF9DKjmvREfgAFvZXA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 16:32:04,209 - INFO - 批量插入响应体: {'result': ['FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMB3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMC3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMD3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDME3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMF3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMG3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMH3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMI3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMJ3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMK3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDML3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMM3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMN3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMO3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMP3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMQ3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMR3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMS3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMT3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMU3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMV3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMW3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMX3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMY3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMZ3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM04', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM14', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM24', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM34', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM44', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM54', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM64', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM74', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM84', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM94', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMA4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMB4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMC4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMD4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDME4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMF4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMG4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMH4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMI4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMJ4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMK4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDML4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMM4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMN4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMO4']}
2025-07-30 16:32:04,209 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-30 16:32:04,209 - INFO - 成功插入的数据ID: ['FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMB3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMC3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMD3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDME3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMF3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMG3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMH3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMI3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMJ3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMK3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDML3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMM3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMN3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMO3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMP3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMQ3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMR3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMS3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMT3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMU3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMV3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMW3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMX3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMY3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMZ3', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM04', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM14', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM24', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM34', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM44', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM54', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM64', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM74', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM84', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDM94', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMA4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMB4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMC4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMD4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDME4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMF4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMG4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMH4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMI4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMJ4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMK4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDML4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMM4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMN4', 'FINST-6PF66691DBKXXVJW6JCH65003EXH35M9JPPDMO4']
2025-07-30 16:32:09,459 - INFO - 批量插入响应状态码: 200
2025-07-30 16:32:09,459 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 08:32:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E4D51016-EFF4-7577-A155-5B77C9616E05', 'x-acs-trace-id': '34963f44deadea9a0b005cc2ab556009', 'etag': '2cuwB7naVbK22thtk5ozWwA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 16:32:09,459 - INFO - 批量插入响应体: {'result': ['FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM2A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM3A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM4A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM5A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM6A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM7A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM8A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM9A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMAA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMBA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMCA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMDA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMEA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMFA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMGA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMHA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMIA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMJA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMKA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMLA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMMA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMNA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMOA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMPA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMQA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMRA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMSA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMTA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMUA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMVA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMWA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMXA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMYA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMZA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM0B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM1B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM2B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM3B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM4B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM5B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM6B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM7B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM8B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM9B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMAB', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMBB', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMCB', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMDB', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMEB', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMFB']}
2025-07-30 16:32:09,459 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-30 16:32:09,459 - INFO - 成功插入的数据ID: ['FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM2A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM3A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM4A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM5A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM6A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM7A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM8A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDM9A', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMAA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMBA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMCA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMDA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMEA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMFA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMGA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMHA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMIA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMJA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMKA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMLA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMMA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMNA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMOA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMPA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMQA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMRA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMSA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMTA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMUA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMVA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMWA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33BODJPPDMXA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMYA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMZA', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM0B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM1B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM2B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM3B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM4B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM5B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM6B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM7B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM8B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDM9B', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMAB', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMBB', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMCB', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMDB', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMEB', 'FINST-ZNE66RC1ZCKXOWLIDZEAMB400TG33CODJPPDMFB']
2025-07-30 16:32:14,709 - INFO - 批量插入响应状态码: 200
2025-07-30 16:32:14,709 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 08:32:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '83DF1871-742D-74B0-A8EE-BEA6E7BFEBA9', 'x-acs-trace-id': '458b47bc8ec6b29ca802480dd96e0173', 'etag': '2Ao81bpys00ypx0F71hMlhA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 16:32:14,709 - INFO - 批量插入响应体: {'result': ['FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM01', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM11', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM21', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM31', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM41', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM51', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM61', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM71', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM81', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM91', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMA1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMB1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMC1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMD1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDME1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMF1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMG1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMH1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMI1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMJ1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMK1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDML1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMM1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMN1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMO1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMP1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMQ1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMR1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMS1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMT1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMU1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMV1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMW1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMX1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMY1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMZ1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM02', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM12', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM22', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM32', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM42', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM52', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM62', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM72', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM82', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM92', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMA2', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMB2', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMC2', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMD2']}
2025-07-30 16:32:14,709 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-07-30 16:32:14,709 - INFO - 成功插入的数据ID: ['FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM01', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM11', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM21', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM31', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM41', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM51', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM61', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM71', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM81', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM91', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMA1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMB1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMC1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMD1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDME1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMF1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMG1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMH1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMI1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMJ1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMK1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDML1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMM1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMN1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMO1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMP1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMQ1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMR1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMS1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMT1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMU1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMV1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMW1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMX1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMY1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMZ1', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM02', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM12', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM22', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM32', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM42', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM52', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM62', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM72', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM82', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDM92', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMA2', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMB2', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMC2', 'FINST-RN7661819CLX0A9F6MOI6DJJAOG22DQHJPPDMD2']
2025-07-30 16:32:19,959 - INFO - 批量插入响应状态码: 200
2025-07-30 16:32:19,959 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 08:32:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '076C0826-9314-7582-BF64-583FC7FC8C08', 'x-acs-trace-id': 'f7030794bc664f74214246f36b5c1a2a', 'etag': '2Qfun1JrjA2GItvFq6bs5Lw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 16:32:19,959 - INFO - 批量插入响应体: {'result': ['FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMHG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMIG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMJG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMKG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMLG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMMG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMNG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMOG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMPG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMQG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMRG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMSG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMTG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMUG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMVG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMWG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMXG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMYG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMZG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM0H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM1H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM2H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM3H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM4H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM5H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM6H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM7H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM8H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM9H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMAH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMBH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMCH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMDH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMEH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMFH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMGH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMHH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMIH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMJH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMKH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMLH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMMH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMNH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMOH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMPH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMQH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMRH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMSH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMTH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMUH']}
2025-07-30 16:32:19,959 - INFO - 批量插入表单数据成功，批次 6，共 50 条记录
2025-07-30 16:32:19,959 - INFO - 成功插入的数据ID: ['FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMHG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMIG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMJG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMKG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMLG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMMG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMNG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMOG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMPG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMQG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMRG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMSG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMTG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMUG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMVG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMWG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMXG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMYG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMZG', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM0H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM1H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM2H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM3H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM4H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM5H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM6H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM7H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM8H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDM9H', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMAH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMBH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMCH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMDH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMEH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMFH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMGH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMHH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMIH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMJH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMKH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMLH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMMH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMNH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMOH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMPH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMQH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMRH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMSH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMTH', 'FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMUH']
2025-07-30 16:32:25,224 - INFO - 批量插入响应状态码: 200
2025-07-30 16:32:25,224 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 08:32:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2390', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0231D799-B99C-705F-8328-705EBD6D05E2', 'x-acs-trace-id': '379bde5660302f33064820cff0fd963d', 'etag': '2p9JO30ymvp3mN+yNLinAzw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 16:32:25,224 - INFO - 批量插入响应体: {'result': ['FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDME', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMF', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMG', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMH', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMI', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMJ', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMK', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDML', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMM', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMN', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMO', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMP', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMQ', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMR', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMS', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMT', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMU', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMV', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMW', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMX', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMY', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMZ', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM01', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM11', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM21', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM31', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM41', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM51', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM61', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM71', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM81', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM91', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMA1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMB1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMC1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMD1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDME1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMF1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMG1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMH1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMI1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMJ1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMK1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDML1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMM1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMN1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMO1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMP1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMQ1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMR1']}
2025-07-30 16:32:25,224 - INFO - 批量插入表单数据成功，批次 7，共 50 条记录
2025-07-30 16:32:25,224 - INFO - 成功插入的数据ID: ['FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDME', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMF', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMG', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMH', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMI', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMJ', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMK', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDML', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMM', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMN', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMO', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMP', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMQ', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMR', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMS', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMT', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMU', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMV', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMW', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMX', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMY', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMZ', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM01', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM11', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM21', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM31', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM41', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM51', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM61', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM71', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM81', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDM91', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMA1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMB1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMC1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMD1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDME1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMF1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMG1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMH1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMI1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMJ1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMK1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDML1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMM1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMN1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMO1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMP1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMQ1', 'FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMR1']
2025-07-30 16:32:30,459 - INFO - 批量插入响应状态码: 200
2025-07-30 16:32:30,459 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 08:32:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '41665BC1-5600-7590-A1CE-45257B9E1B0A', 'x-acs-trace-id': '3d10a4b987a6b900dc88fbd9bfd5d5c1', 'etag': '2PPyQfaRifSC+INeptRn+BQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 16:32:30,459 - INFO - 批量插入响应体: {'result': ['FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMU8', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMV8', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMW8', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMX8', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMY8', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMZ8', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM09', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM19', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM29', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM39', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM49', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM59', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM69', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM79', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM89', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM99', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMA9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMB9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMC9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMD9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDME9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMF9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMG9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMH9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMI9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMJ9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMK9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDML9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMM9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMN9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMO9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMP9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMQ9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMR9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMS9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMT9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMU9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMV9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMW9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMX9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMY9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMZ9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM0A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM1A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM2A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM3A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM4A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM5A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM6A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM7A']}
2025-07-30 16:32:30,459 - INFO - 批量插入表单数据成功，批次 8，共 50 条记录
2025-07-30 16:32:30,459 - INFO - 成功插入的数据ID: ['FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMU8', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMV8', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMW8', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMX8', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMY8', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMZ8', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM09', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM19', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM29', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM39', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM49', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM59', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM69', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM79', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM89', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM99', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMA9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMB9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMC9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMD9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDME9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMF9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMG9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMH9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMI9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMJ9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMK9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDML9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMM9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMN9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMO9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMP9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMQ9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMR9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMS9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMT9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMU9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMV9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMW9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMX9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMY9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMZ9', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDM0A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM1A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM2A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM3A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM4A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM5A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM6A', 'FINST-FQD66YB148JX4KVQBHABM84K3LRX3NVTJPPDM7A']
2025-07-30 16:32:35,724 - INFO - 批量插入响应状态码: 200
2025-07-30 16:32:35,724 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 08:32:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EE4A30D5-52FC-7CB2-B6D1-21488D712BCA', 'x-acs-trace-id': 'afb7b8573d63b9b652463f56c8e3f4e1', 'etag': '2MEtwq8TC8vwmUafsWxWewA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 16:32:35,724 - INFO - 批量插入响应体: {'result': ['FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMXT', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMYT', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMZT', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM0U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM1U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM2U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM3U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM4U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM5U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM6U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM7U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM8U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM9U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMAU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMBU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMCU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMDU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMEU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMFU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMGU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMHU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMIU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMJU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMKU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMLU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMMU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMNU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMOU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMPU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMQU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMRU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMSU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMTU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMUU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMVU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMWU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMXU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMYU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMZU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM0V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM1V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM2V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM3V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM4V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM5V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM6V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM7V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM8V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM9V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMAV']}
2025-07-30 16:32:35,724 - INFO - 批量插入表单数据成功，批次 9，共 50 条记录
2025-07-30 16:32:35,724 - INFO - 成功插入的数据ID: ['FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMXT', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMYT', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMZT', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM0U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM1U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM2U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM3U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM4U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM5U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM6U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM7U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM8U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM9U', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMAU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMBU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMCU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMDU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMEU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMFU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMGU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMHU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMIU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMJU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMKU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMLU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMMU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMNU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMOU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMPU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMQU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMRU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMSU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMTU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMUU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMVU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMWU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMXU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMYU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMZU', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM0V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM1V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM2V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM3V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM4V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM5V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM6V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM7V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM8V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM9V', 'FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMAV']
2025-07-30 16:32:40,896 - INFO - 批量插入响应状态码: 200
2025-07-30 16:32:40,896 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 08:32:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '252', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'ABE4F85E-2190-75E7-9AE7-0AF7DEF50888', 'x-acs-trace-id': '1de71f1999de275301c57724d30a5173', 'etag': '2qpDwTR+oIUHE4cSGAwWF/w2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 16:32:40,896 - INFO - 批量插入响应体: {'result': ['FINST-EEC66XC1X9JXG51RF651CD3UPSLX3LX1KPPDMO1', 'FINST-EEC66XC1X9JXG51RF651CD3UPSLX3LX1KPPDMP1', 'FINST-EEC66XC1X9JXG51RF651CD3UPSLX3LX1KPPDMQ1', 'FINST-EEC66XC1X9JXG51RF651CD3UPSLX3LX1KPPDMR1', 'FINST-EEC66XC1X9JXG51RF651CD3UPSLX3LX1KPPDMS1']}
2025-07-30 16:32:40,896 - INFO - 批量插入表单数据成功，批次 10，共 5 条记录
2025-07-30 16:32:40,896 - INFO - 成功插入的数据ID: ['FINST-EEC66XC1X9JXG51RF651CD3UPSLX3LX1KPPDMO1', 'FINST-EEC66XC1X9JXG51RF651CD3UPSLX3LX1KPPDMP1', 'FINST-EEC66XC1X9JXG51RF651CD3UPSLX3LX1KPPDMQ1', 'FINST-EEC66XC1X9JXG51RF651CD3UPSLX3LX1KPPDMR1', 'FINST-EEC66XC1X9JXG51RF651CD3UPSLX3LX1KPPDMS1']
2025-07-30 16:32:45,912 - INFO - 批量插入完成，共 455 条记录
2025-07-30 16:32:45,912 - INFO - 日期 2025-07-29 处理完成 - 更新: 2 条，插入: 455 条，错误: 0 条
2025-07-30 16:32:45,912 - INFO - 数据同步完成！更新: 2 条，插入: 455 条，错误: 0 条
2025-07-30 16:32:45,912 - INFO - 同步完成
2025-07-30 19:30:34,096 - INFO - 使用默认增量同步（当天更新数据）
2025-07-30 19:30:34,096 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-30 19:30:34,096 - INFO - 查询参数: ('2025-07-30',)
2025-07-30 19:30:34,268 - INFO - MySQL查询成功，增量数据（日期: 2025-07-30），共获取 207 条记录
2025-07-30 19:30:34,268 - INFO - 获取到 4 个日期需要处理: ['2025-07-06', '2025-07-13', '2025-07-29', '2025-07-30']
2025-07-30 19:30:34,268 - INFO - 开始处理日期: 2025-07-06
2025-07-30 19:30:34,268 - INFO - Request Parameters - Page 1:
2025-07-30 19:30:34,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:30:34,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:30:42,286 - INFO - Response - Page 1:
2025-07-30 19:30:42,286 - INFO - 第 1 页获取到 50 条记录
2025-07-30 19:30:42,787 - INFO - Request Parameters - Page 2:
2025-07-30 19:30:42,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:30:42,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:30:43,568 - INFO - Response - Page 2:
2025-07-30 19:30:43,568 - INFO - 第 2 页获取到 50 条记录
2025-07-30 19:30:44,068 - INFO - Request Parameters - Page 3:
2025-07-30 19:30:44,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:30:44,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:30:44,756 - INFO - Response - Page 3:
2025-07-30 19:30:44,756 - INFO - 第 3 页获取到 50 条记录
2025-07-30 19:30:45,256 - INFO - Request Parameters - Page 4:
2025-07-30 19:30:45,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:30:45,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:30:45,991 - INFO - Response - Page 4:
2025-07-30 19:30:45,991 - INFO - 第 4 页获取到 50 条记录
2025-07-30 19:30:46,491 - INFO - Request Parameters - Page 5:
2025-07-30 19:30:46,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:30:46,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:30:47,210 - INFO - Response - Page 5:
2025-07-30 19:30:47,210 - INFO - 第 5 页获取到 50 条记录
2025-07-30 19:30:47,726 - INFO - Request Parameters - Page 6:
2025-07-30 19:30:47,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:30:47,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:30:55,839 - ERROR - 处理日期 2025-07-06 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2C67F2CD-CB49-7A3A-BC09-911D7875D134 Response: {'code': 'ServiceUnavailable', 'requestid': '2C67F2CD-CB49-7A3A-BC09-911D7875D134', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2C67F2CD-CB49-7A3A-BC09-911D7875D134)
2025-07-30 19:30:55,839 - INFO - 开始处理日期: 2025-07-13
2025-07-30 19:30:55,839 - INFO - Request Parameters - Page 1:
2025-07-30 19:30:55,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:30:55,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:00,497 - INFO - Response - Page 1:
2025-07-30 19:31:00,497 - INFO - 第 1 页获取到 50 条记录
2025-07-30 19:31:01,013 - INFO - Request Parameters - Page 2:
2025-07-30 19:31:01,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:01,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:01,747 - INFO - Response - Page 2:
2025-07-30 19:31:01,747 - INFO - 第 2 页获取到 50 条记录
2025-07-30 19:31:02,263 - INFO - Request Parameters - Page 3:
2025-07-30 19:31:02,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:02,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:02,998 - INFO - Response - Page 3:
2025-07-30 19:31:02,998 - INFO - 第 3 页获取到 50 条记录
2025-07-30 19:31:03,498 - INFO - Request Parameters - Page 4:
2025-07-30 19:31:03,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:03,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:04,342 - INFO - Response - Page 4:
2025-07-30 19:31:04,342 - INFO - 第 4 页获取到 50 条记录
2025-07-30 19:31:04,858 - INFO - Request Parameters - Page 5:
2025-07-30 19:31:04,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:04,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:05,624 - INFO - Response - Page 5:
2025-07-30 19:31:05,624 - INFO - 第 5 页获取到 50 条记录
2025-07-30 19:31:06,124 - INFO - Request Parameters - Page 6:
2025-07-30 19:31:06,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:06,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:06,812 - INFO - Response - Page 6:
2025-07-30 19:31:06,812 - INFO - 第 6 页获取到 50 条记录
2025-07-30 19:31:07,328 - INFO - Request Parameters - Page 7:
2025-07-30 19:31:07,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:07,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:07,968 - INFO - Response - Page 7:
2025-07-30 19:31:07,968 - INFO - 第 7 页获取到 50 条记录
2025-07-30 19:31:08,484 - INFO - Request Parameters - Page 8:
2025-07-30 19:31:08,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:08,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:09,235 - INFO - Response - Page 8:
2025-07-30 19:31:09,235 - INFO - 第 8 页获取到 50 条记录
2025-07-30 19:31:09,750 - INFO - Request Parameters - Page 9:
2025-07-30 19:31:09,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:09,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:10,485 - INFO - Response - Page 9:
2025-07-30 19:31:10,485 - INFO - 第 9 页获取到 50 条记录
2025-07-30 19:31:11,001 - INFO - Request Parameters - Page 10:
2025-07-30 19:31:11,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:11,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:11,720 - INFO - Response - Page 10:
2025-07-30 19:31:11,720 - INFO - 第 10 页获取到 50 条记录
2025-07-30 19:31:12,236 - INFO - Request Parameters - Page 11:
2025-07-30 19:31:12,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:12,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:12,861 - INFO - Response - Page 11:
2025-07-30 19:31:12,861 - INFO - 第 11 页获取到 15 条记录
2025-07-30 19:31:13,377 - INFO - 查询完成，共获取到 515 条记录
2025-07-30 19:31:13,377 - INFO - 获取到 515 条表单数据
2025-07-30 19:31:13,377 - INFO - 当前日期 2025-07-13 有 1 条MySQL数据需要处理
2025-07-30 19:31:13,377 - INFO - 开始更新记录 - 表单实例ID: FINST-AJF66F71J06X4BPN6XI0OBJ30BLL3ASUBX3DMH
2025-07-30 19:31:14,002 - INFO - 更新表单数据成功: FINST-AJF66F71J06X4BPN6XI0OBJ30BLL3ASUBX3DMH
2025-07-30 19:31:14,002 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14096.0, 'new_value': 111.0}, {'field': 'total_amount', 'old_value': 14096.0, 'new_value': 111.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 14096}]
2025-07-30 19:31:14,002 - INFO - 日期 2025-07-13 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-30 19:31:14,002 - INFO - 开始处理日期: 2025-07-29
2025-07-30 19:31:14,002 - INFO - Request Parameters - Page 1:
2025-07-30 19:31:14,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:14,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:14,737 - INFO - Response - Page 1:
2025-07-30 19:31:14,737 - INFO - 第 1 页获取到 50 条记录
2025-07-30 19:31:15,253 - INFO - Request Parameters - Page 2:
2025-07-30 19:31:15,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:15,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:15,925 - INFO - Response - Page 2:
2025-07-30 19:31:15,925 - INFO - 第 2 页获取到 50 条记录
2025-07-30 19:31:16,441 - INFO - Request Parameters - Page 3:
2025-07-30 19:31:16,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:16,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:17,144 - INFO - Response - Page 3:
2025-07-30 19:31:17,144 - INFO - 第 3 页获取到 50 条记录
2025-07-30 19:31:17,644 - INFO - Request Parameters - Page 4:
2025-07-30 19:31:17,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:17,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:18,394 - INFO - Response - Page 4:
2025-07-30 19:31:18,394 - INFO - 第 4 页获取到 50 条记录
2025-07-30 19:31:18,895 - INFO - Request Parameters - Page 5:
2025-07-30 19:31:18,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:18,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:19,754 - INFO - Response - Page 5:
2025-07-30 19:31:19,754 - INFO - 第 5 页获取到 50 条记录
2025-07-30 19:31:20,255 - INFO - Request Parameters - Page 6:
2025-07-30 19:31:20,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:20,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:21,036 - INFO - Response - Page 6:
2025-07-30 19:31:21,036 - INFO - 第 6 页获取到 50 条记录
2025-07-30 19:31:21,552 - INFO - Request Parameters - Page 7:
2025-07-30 19:31:21,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:21,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:22,255 - INFO - Response - Page 7:
2025-07-30 19:31:22,255 - INFO - 第 7 页获取到 50 条记录
2025-07-30 19:31:22,771 - INFO - Request Parameters - Page 8:
2025-07-30 19:31:22,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:22,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:23,490 - INFO - Response - Page 8:
2025-07-30 19:31:23,490 - INFO - 第 8 页获取到 50 条记录
2025-07-30 19:31:24,006 - INFO - Request Parameters - Page 9:
2025-07-30 19:31:24,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:24,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:24,725 - INFO - Response - Page 9:
2025-07-30 19:31:24,725 - INFO - 第 9 页获取到 50 条记录
2025-07-30 19:31:25,241 - INFO - Request Parameters - Page 10:
2025-07-30 19:31:25,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:25,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:25,944 - INFO - Response - Page 10:
2025-07-30 19:31:25,944 - INFO - 第 10 页获取到 50 条记录
2025-07-30 19:31:26,460 - INFO - Request Parameters - Page 11:
2025-07-30 19:31:26,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:26,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:27,226 - INFO - Response - Page 11:
2025-07-30 19:31:27,226 - INFO - 第 11 页获取到 48 条记录
2025-07-30 19:31:27,742 - INFO - 查询完成，共获取到 548 条记录
2025-07-30 19:31:27,742 - INFO - 获取到 548 条表单数据
2025-07-30 19:31:27,742 - INFO - 当前日期 2025-07-29 有 198 条MySQL数据需要处理
2025-07-30 19:31:27,742 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMNU
2025-07-30 19:31:28,320 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMNU
2025-07-30 19:31:28,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 13619.45}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 13619.45}, {'field': 'order_count', 'old_value': 0, 'new_value': 135}]
2025-07-30 19:31:28,320 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM5V
2025-07-30 19:31:28,914 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM5V
2025-07-30 19:31:28,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 30009.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 30009.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 106}]
2025-07-30 19:31:28,914 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMYU
2025-07-30 19:31:29,430 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMYU
2025-07-30 19:31:29,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3616.66}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3616.66}, {'field': 'order_count', 'old_value': 0, 'new_value': 160}]
2025-07-30 19:31:29,430 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMZU
2025-07-30 19:31:29,946 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMZU
2025-07-30 19:31:29,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 833.8}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 9088.9}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 9922.7}, {'field': 'order_count', 'old_value': 0, 'new_value': 103}]
2025-07-30 19:31:29,946 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMOU
2025-07-30 19:31:30,509 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMOU
2025-07-30 19:31:30,509 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 16351.7}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 16351.7}, {'field': 'order_count', 'old_value': 0, 'new_value': 239}]
2025-07-30 19:31:30,509 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM0V
2025-07-30 19:31:31,040 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM0V
2025-07-30 19:31:31,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 722.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 722.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 19}]
2025-07-30 19:31:31,040 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMJU
2025-07-30 19:31:31,665 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMJU
2025-07-30 19:31:31,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1815.87}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1815.87}, {'field': 'order_count', 'old_value': 0, 'new_value': 298}]
2025-07-30 19:31:31,665 - INFO - 开始更新记录 - 表单实例ID: FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMW9
2025-07-30 19:31:32,181 - INFO - 更新表单数据成功: FINST-FQD66YB148JX4KVQBHABM84K3LRX3MVTJPPDMW9
2025-07-30 19:31:32,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5756.0, 'new_value': 10760.0}, {'field': 'total_amount', 'old_value': 5756.0, 'new_value': 10760.0}]
2025-07-30 19:31:32,181 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMTU
2025-07-30 19:31:32,728 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMTU
2025-07-30 19:31:32,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10083.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 10083.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 269}]
2025-07-30 19:31:32,728 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMMU
2025-07-30 19:31:33,307 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMMU
2025-07-30 19:31:33,307 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 56000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 56000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 96}]
2025-07-30 19:31:33,307 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMUU
2025-07-30 19:31:33,885 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMUU
2025-07-30 19:31:33,885 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 50000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-30 19:31:33,885 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMXU
2025-07-30 19:31:34,448 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMXU
2025-07-30 19:31:34,448 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 599.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 599.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-30 19:31:34,448 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMQU
2025-07-30 19:31:35,010 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMQU
2025-07-30 19:31:35,026 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3232.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3232.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 33}]
2025-07-30 19:31:35,026 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM9V
2025-07-30 19:31:35,526 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM9V
2025-07-30 19:31:35,526 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 980.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 980.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 4}]
2025-07-30 19:31:35,526 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMVU
2025-07-30 19:31:36,073 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMVU
2025-07-30 19:31:36,073 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5115.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5115.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 332}]
2025-07-30 19:31:36,089 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM8V
2025-07-30 19:31:36,652 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM8V
2025-07-30 19:31:36,652 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 9000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 9000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-07-30 19:31:36,652 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM7V
2025-07-30 19:31:37,199 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM7V
2025-07-30 19:31:37,199 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 13250.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 13250.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-07-30 19:31:37,199 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM3V
2025-07-30 19:31:37,699 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDM3V
2025-07-30 19:31:37,699 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 102}]
2025-07-30 19:31:37,699 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMRU
2025-07-30 19:31:38,324 - INFO - 更新表单数据成功: FINST-SL966GD1YKGXOBY9CC86487EP0RY2SXXJPPDMRU
2025-07-30 19:31:38,324 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 60}]
2025-07-30 19:31:38,324 - INFO - 开始批量插入 6 条新记录
2025-07-30 19:31:38,481 - INFO - 批量插入响应状态码: 200
2025-07-30 19:31:38,481 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 11:31:38 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '300', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EEEE6E05-0547-7046-AF11-34A556BF16F1', 'x-acs-trace-id': 'a886ead1d298f93f19af108d577a68b4', 'etag': '3GyNTvt0EcKNnCCX+5/twdw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 19:31:38,481 - INFO - 批量插入响应体: {'result': ['FINST-AEF66BC1QBLXW77ZD6RRNDV916NU2SV6YVPDM41', 'FINST-AEF66BC1QBLXW77ZD6RRNDV916NU2SV6YVPDM51', 'FINST-AEF66BC1QBLXW77ZD6RRNDV916NU2SV6YVPDM61', 'FINST-AEF66BC1QBLXW77ZD6RRNDV916NU2SV6YVPDM71', 'FINST-AEF66BC1QBLXW77ZD6RRNDV916NU2SV6YVPDM81', 'FINST-AEF66BC1QBLXW77ZD6RRNDV916NU2SV6YVPDM91']}
2025-07-30 19:31:38,481 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-07-30 19:31:38,481 - INFO - 成功插入的数据ID: ['FINST-AEF66BC1QBLXW77ZD6RRNDV916NU2SV6YVPDM41', 'FINST-AEF66BC1QBLXW77ZD6RRNDV916NU2SV6YVPDM51', 'FINST-AEF66BC1QBLXW77ZD6RRNDV916NU2SV6YVPDM61', 'FINST-AEF66BC1QBLXW77ZD6RRNDV916NU2SV6YVPDM71', 'FINST-AEF66BC1QBLXW77ZD6RRNDV916NU2SV6YVPDM81', 'FINST-AEF66BC1QBLXW77ZD6RRNDV916NU2SV6YVPDM91']
2025-07-30 19:31:43,498 - INFO - 批量插入完成，共 6 条记录
2025-07-30 19:31:43,498 - INFO - 日期 2025-07-29 处理完成 - 更新: 19 条，插入: 6 条，错误: 0 条
2025-07-30 19:31:43,498 - INFO - 开始处理日期: 2025-07-30
2025-07-30 19:31:43,498 - INFO - Request Parameters - Page 1:
2025-07-30 19:31:43,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:31:43,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:31:43,967 - INFO - Response - Page 1:
2025-07-30 19:31:43,967 - INFO - 查询完成，共获取到 0 条记录
2025-07-30 19:31:43,967 - INFO - 获取到 0 条表单数据
2025-07-30 19:31:43,967 - INFO - 当前日期 2025-07-30 有 1 条MySQL数据需要处理
2025-07-30 19:31:43,967 - INFO - 开始批量插入 1 条新记录
2025-07-30 19:31:44,108 - INFO - 批量插入响应状态码: 200
2025-07-30 19:31:44,108 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 11:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CA93E9AB-D564-7D9C-88D6-E958D6B66DD8', 'x-acs-trace-id': '73187cd5400998031c460164f1eae1d8', 'etag': '6tVmY7qg5npdnHQ+dxM6jXQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 19:31:44,108 - INFO - 批量插入响应体: {'result': ['FINST-NWE664C128LX6KBRB7EBUCPYP7CW2G8BYVPDM71']}
2025-07-30 19:31:44,108 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-30 19:31:44,108 - INFO - 成功插入的数据ID: ['FINST-NWE664C128LX6KBRB7EBUCPYP7CW2G8BYVPDM71']
2025-07-30 19:31:49,125 - INFO - 批量插入完成，共 1 条记录
2025-07-30 19:31:49,125 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-30 19:31:49,125 - INFO - 数据同步完成！更新: 20 条，插入: 7 条，错误: 1 条
2025-07-30 19:32:49,165 - INFO - 开始同步昨天与今天的销售数据: 2025-07-29 至 2025-07-30
2025-07-30 19:32:49,165 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-30 19:32:49,165 - INFO - 查询参数: ('2025-07-29', '2025-07-30')
2025-07-30 19:32:49,337 - INFO - MySQL查询成功，时间段: 2025-07-29 至 2025-07-30，共获取 587 条记录
2025-07-30 19:32:49,337 - INFO - 获取到 2 个日期需要处理: ['2025-07-29', '2025-07-30']
2025-07-30 19:32:49,337 - INFO - 开始处理日期: 2025-07-29
2025-07-30 19:32:49,337 - INFO - Request Parameters - Page 1:
2025-07-30 19:32:49,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:32:49,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:32:50,103 - INFO - Response - Page 1:
2025-07-30 19:32:50,103 - INFO - 第 1 页获取到 50 条记录
2025-07-30 19:32:50,603 - INFO - Request Parameters - Page 2:
2025-07-30 19:32:50,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:32:50,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:32:51,306 - INFO - Response - Page 2:
2025-07-30 19:32:51,306 - INFO - 第 2 页获取到 50 条记录
2025-07-30 19:32:51,807 - INFO - Request Parameters - Page 3:
2025-07-30 19:32:51,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:32:51,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:32:52,666 - INFO - Response - Page 3:
2025-07-30 19:32:52,666 - INFO - 第 3 页获取到 50 条记录
2025-07-30 19:32:53,166 - INFO - Request Parameters - Page 4:
2025-07-30 19:32:53,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:32:53,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:32:53,839 - INFO - Response - Page 4:
2025-07-30 19:32:53,839 - INFO - 第 4 页获取到 50 条记录
2025-07-30 19:32:54,339 - INFO - Request Parameters - Page 5:
2025-07-30 19:32:54,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:32:54,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:32:55,042 - INFO - Response - Page 5:
2025-07-30 19:32:55,042 - INFO - 第 5 页获取到 50 条记录
2025-07-30 19:32:55,542 - INFO - Request Parameters - Page 6:
2025-07-30 19:32:55,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:32:55,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:32:56,293 - INFO - Response - Page 6:
2025-07-30 19:32:56,293 - INFO - 第 6 页获取到 50 条记录
2025-07-30 19:32:56,793 - INFO - Request Parameters - Page 7:
2025-07-30 19:32:56,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:32:56,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:32:57,528 - INFO - Response - Page 7:
2025-07-30 19:32:57,528 - INFO - 第 7 页获取到 50 条记录
2025-07-30 19:32:58,043 - INFO - Request Parameters - Page 8:
2025-07-30 19:32:58,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:32:58,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:32:58,778 - INFO - Response - Page 8:
2025-07-30 19:32:58,778 - INFO - 第 8 页获取到 50 条记录
2025-07-30 19:32:59,294 - INFO - Request Parameters - Page 9:
2025-07-30 19:32:59,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:32:59,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:33:00,075 - INFO - Response - Page 9:
2025-07-30 19:33:00,075 - INFO - 第 9 页获取到 50 条记录
2025-07-30 19:33:00,576 - INFO - Request Parameters - Page 10:
2025-07-30 19:33:00,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:33:00,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:33:01,279 - INFO - Response - Page 10:
2025-07-30 19:33:01,279 - INFO - 第 10 页获取到 50 条记录
2025-07-30 19:33:01,795 - INFO - Request Parameters - Page 11:
2025-07-30 19:33:01,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:33:01,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:33:02,498 - INFO - Response - Page 11:
2025-07-30 19:33:02,498 - INFO - 第 11 页获取到 50 条记录
2025-07-30 19:33:03,014 - INFO - Request Parameters - Page 12:
2025-07-30 19:33:03,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:33:03,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:33:03,608 - INFO - Response - Page 12:
2025-07-30 19:33:03,608 - INFO - 第 12 页获取到 4 条记录
2025-07-30 19:33:04,124 - INFO - 查询完成，共获取到 554 条记录
2025-07-30 19:33:04,124 - INFO - 获取到 554 条表单数据
2025-07-30 19:33:04,124 - INFO - 当前日期 2025-07-29 有 565 条MySQL数据需要处理
2025-07-30 19:33:04,140 - INFO - 开始批量插入 11 条新记录
2025-07-30 19:33:04,311 - INFO - 批量插入响应状态码: 200
2025-07-30 19:33:04,311 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 11:33:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '540', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6275A141-0880-7303-90BB-E2F979517C29', 'x-acs-trace-id': '5266aa6a52222b892470e57d2d8d30a8', 'etag': '5Yd4ND0nxqMTIwrQHZs2dwQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 19:33:04,311 - INFO - 批量插入响应体: {'result': ['FINST-3PF66271RBLXMXEWDBCNCDR08FLF27310WPDMI2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF27310WPDMJ2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF27310WPDMK2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDML2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMM2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMN2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMO2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMP2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMQ2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMR2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMS2']}
2025-07-30 19:33:04,311 - INFO - 批量插入表单数据成功，批次 1，共 11 条记录
2025-07-30 19:33:04,311 - INFO - 成功插入的数据ID: ['FINST-3PF66271RBLXMXEWDBCNCDR08FLF27310WPDMI2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF27310WPDMJ2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF27310WPDMK2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDML2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMM2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMN2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMO2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMP2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMQ2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMR2', 'FINST-3PF66271RBLXMXEWDBCNCDR08FLF28310WPDMS2']
2025-07-30 19:33:09,329 - INFO - 批量插入完成，共 11 条记录
2025-07-30 19:33:09,329 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 11 条，错误: 0 条
2025-07-30 19:33:09,329 - INFO - 开始处理日期: 2025-07-30
2025-07-30 19:33:09,329 - INFO - Request Parameters - Page 1:
2025-07-30 19:33:09,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 19:33:09,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 19:33:09,829 - INFO - Response - Page 1:
2025-07-30 19:33:09,829 - INFO - 第 1 页获取到 1 条记录
2025-07-30 19:33:10,329 - INFO - 查询完成，共获取到 1 条记录
2025-07-30 19:33:10,329 - INFO - 获取到 1 条表单数据
2025-07-30 19:33:10,329 - INFO - 当前日期 2025-07-30 有 2 条MySQL数据需要处理
2025-07-30 19:33:10,329 - INFO - 开始批量插入 1 条新记录
2025-07-30 19:33:10,470 - INFO - 批量插入响应状态码: 200
2025-07-30 19:33:10,470 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 11:33:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FDF293D5-D399-7886-9A8A-5A218FB3576D', 'x-acs-trace-id': 'c0bdbe4d2b4f513313d22bbe6d27b115', 'etag': '5OHv9iDYROnlf208xNeDvAg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 19:33:10,470 - INFO - 批量插入响应体: {'result': ['FINST-OIF66BA1DOLX7F2CDPJS7ASQPCEV26U50WPDM3']}
2025-07-30 19:33:10,470 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-30 19:33:10,470 - INFO - 成功插入的数据ID: ['FINST-OIF66BA1DOLX7F2CDPJS7ASQPCEV26U50WPDM3']
2025-07-30 19:33:15,488 - INFO - 批量插入完成，共 1 条记录
2025-07-30 19:33:15,488 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-30 19:33:15,488 - INFO - 数据同步完成！更新: 0 条，插入: 12 条，错误: 0 条
2025-07-30 19:33:15,488 - INFO - 同步完成
2025-07-30 22:30:35,439 - INFO - 使用默认增量同步（当天更新数据）
2025-07-30 22:30:35,439 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-30 22:30:35,439 - INFO - 查询参数: ('2025-07-30',)
2025-07-30 22:30:35,611 - INFO - MySQL查询成功，增量数据（日期: 2025-07-30），共获取 252 条记录
2025-07-30 22:30:35,611 - INFO - 获取到 4 个日期需要处理: ['2025-07-06', '2025-07-13', '2025-07-29', '2025-07-30']
2025-07-30 22:30:35,611 - INFO - 开始处理日期: 2025-07-06
2025-07-30 22:30:35,611 - INFO - Request Parameters - Page 1:
2025-07-30 22:30:35,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:30:35,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:30:43,739 - ERROR - 处理日期 2025-07-06 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1688A789-0758-73C7-AF7B-D39150D1D4F2 Response: {'code': 'ServiceUnavailable', 'requestid': '1688A789-0758-73C7-AF7B-D39150D1D4F2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1688A789-0758-73C7-AF7B-D39150D1D4F2)
2025-07-30 22:30:43,739 - INFO - 开始处理日期: 2025-07-13
2025-07-30 22:30:43,739 - INFO - Request Parameters - Page 1:
2025-07-30 22:30:43,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:30:43,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:30:48,944 - INFO - Response - Page 1:
2025-07-30 22:30:48,944 - INFO - 第 1 页获取到 50 条记录
2025-07-30 22:30:49,444 - INFO - Request Parameters - Page 2:
2025-07-30 22:30:49,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:30:49,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:30:50,226 - INFO - Response - Page 2:
2025-07-30 22:30:50,226 - INFO - 第 2 页获取到 50 条记录
2025-07-30 22:30:50,742 - INFO - Request Parameters - Page 3:
2025-07-30 22:30:50,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:30:50,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:30:51,429 - INFO - Response - Page 3:
2025-07-30 22:30:51,429 - INFO - 第 3 页获取到 50 条记录
2025-07-30 22:30:51,945 - INFO - Request Parameters - Page 4:
2025-07-30 22:30:51,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:30:51,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:30:52,680 - INFO - Response - Page 4:
2025-07-30 22:30:52,680 - INFO - 第 4 页获取到 50 条记录
2025-07-30 22:30:53,196 - INFO - Request Parameters - Page 5:
2025-07-30 22:30:53,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:30:53,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:30:53,915 - INFO - Response - Page 5:
2025-07-30 22:30:53,915 - INFO - 第 5 页获取到 50 条记录
2025-07-30 22:30:54,431 - INFO - Request Parameters - Page 6:
2025-07-30 22:30:54,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:30:54,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:30:55,134 - INFO - Response - Page 6:
2025-07-30 22:30:55,134 - INFO - 第 6 页获取到 50 条记录
2025-07-30 22:30:55,634 - INFO - Request Parameters - Page 7:
2025-07-30 22:30:55,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:30:55,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:03,747 - ERROR - 处理日期 2025-07-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 30AABA45-98F9-76DC-8881-6D1A2D05EB66 Response: {'code': 'ServiceUnavailable', 'requestid': '30AABA45-98F9-76DC-8881-6D1A2D05EB66', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 30AABA45-98F9-76DC-8881-6D1A2D05EB66)
2025-07-30 22:31:03,762 - INFO - 开始处理日期: 2025-07-29
2025-07-30 22:31:03,762 - INFO - Request Parameters - Page 1:
2025-07-30 22:31:03,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:31:03,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:04,482 - INFO - Response - Page 1:
2025-07-30 22:31:04,482 - INFO - 第 1 页获取到 50 条记录
2025-07-30 22:31:04,997 - INFO - Request Parameters - Page 2:
2025-07-30 22:31:04,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:31:04,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:05,732 - INFO - Response - Page 2:
2025-07-30 22:31:05,732 - INFO - 第 2 页获取到 50 条记录
2025-07-30 22:31:06,232 - INFO - Request Parameters - Page 3:
2025-07-30 22:31:06,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:31:06,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:07,045 - INFO - Response - Page 3:
2025-07-30 22:31:07,045 - INFO - 第 3 页获取到 50 条记录
2025-07-30 22:31:07,545 - INFO - Request Parameters - Page 4:
2025-07-30 22:31:07,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:31:07,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:08,327 - INFO - Response - Page 4:
2025-07-30 22:31:08,327 - INFO - 第 4 页获取到 50 条记录
2025-07-30 22:31:08,827 - INFO - Request Parameters - Page 5:
2025-07-30 22:31:08,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:31:08,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:09,546 - INFO - Response - Page 5:
2025-07-30 22:31:09,546 - INFO - 第 5 页获取到 50 条记录
2025-07-30 22:31:10,062 - INFO - Request Parameters - Page 6:
2025-07-30 22:31:10,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:31:10,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:10,765 - INFO - Response - Page 6:
2025-07-30 22:31:10,765 - INFO - 第 6 页获取到 50 条记录
2025-07-30 22:31:11,265 - INFO - Request Parameters - Page 7:
2025-07-30 22:31:11,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:31:11,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:12,016 - INFO - Response - Page 7:
2025-07-30 22:31:12,016 - INFO - 第 7 页获取到 50 条记录
2025-07-30 22:31:12,532 - INFO - Request Parameters - Page 8:
2025-07-30 22:31:12,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:31:12,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:13,360 - INFO - Response - Page 8:
2025-07-30 22:31:13,360 - INFO - 第 8 页获取到 50 条记录
2025-07-30 22:31:13,876 - INFO - Request Parameters - Page 9:
2025-07-30 22:31:13,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:31:13,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:14,548 - INFO - Response - Page 9:
2025-07-30 22:31:14,548 - INFO - 第 9 页获取到 50 条记录
2025-07-30 22:31:15,064 - INFO - Request Parameters - Page 10:
2025-07-30 22:31:15,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:31:15,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:15,814 - INFO - Response - Page 10:
2025-07-30 22:31:15,814 - INFO - 第 10 页获取到 50 条记录
2025-07-30 22:31:16,330 - INFO - Request Parameters - Page 11:
2025-07-30 22:31:16,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:31:16,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:17,096 - INFO - Response - Page 11:
2025-07-30 22:31:17,096 - INFO - 第 11 页获取到 50 条记录
2025-07-30 22:31:17,612 - INFO - Request Parameters - Page 12:
2025-07-30 22:31:17,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:31:17,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:18,221 - INFO - Response - Page 12:
2025-07-30 22:31:18,221 - INFO - 第 12 页获取到 15 条记录
2025-07-30 22:31:18,737 - INFO - 查询完成，共获取到 565 条记录
2025-07-30 22:31:18,737 - INFO - 获取到 565 条表单数据
2025-07-30 22:31:18,737 - INFO - 当前日期 2025-07-29 有 198 条MySQL数据需要处理
2025-07-30 22:31:18,737 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-30 22:31:18,737 - INFO - 开始处理日期: 2025-07-30
2025-07-30 22:31:18,737 - INFO - Request Parameters - Page 1:
2025-07-30 22:31:18,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:31:18,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:31:19,253 - INFO - Response - Page 1:
2025-07-30 22:31:19,253 - INFO - 第 1 页获取到 2 条记录
2025-07-30 22:31:19,753 - INFO - 查询完成，共获取到 2 条记录
2025-07-30 22:31:19,753 - INFO - 获取到 2 条表单数据
2025-07-30 22:31:19,753 - INFO - 当前日期 2025-07-30 有 45 条MySQL数据需要处理
2025-07-30 22:31:19,753 - INFO - 开始批量插入 43 条新记录
2025-07-30 22:31:20,003 - INFO - 批量插入响应状态码: 200
2025-07-30 22:31:20,003 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Jul 2025 14:31:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2055', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '60DDE308-0B00-7418-9216-376300FFC92F', 'x-acs-trace-id': '84cf249b478c1c03dfcf8d247d85e890', 'etag': '2IfQavz06ysfwa7g6gVZVVA5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-30 22:31:20,003 - INFO - 批量插入响应体: {'result': ['FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMF', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMG', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMH', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMI', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMJ', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMK', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDML', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMM', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMN', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMO', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMP', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMQ', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMR', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMS', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMT', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMU', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMV', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMW', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMX', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMY', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMZ', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM01', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM11', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM21', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM31', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM41', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM51', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM61', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM71', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM81', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM91', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMA1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMB1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMC1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMD1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDME1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMF1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMG1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMH1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMI1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMJ1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMK1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDML1']}
2025-07-30 22:31:20,003 - INFO - 批量插入表单数据成功，批次 1，共 43 条记录
2025-07-30 22:31:20,003 - INFO - 成功插入的数据ID: ['FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMF', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMG', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMH', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMI', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMJ', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMK', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDML', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMM', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMN', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMO', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMP', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMQ', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMR', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMS', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMT', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMU', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMV', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMW', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMX', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMY', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMZ', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM01', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM11', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM21', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM31', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM41', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM51', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM61', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM71', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM81', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDM91', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMA1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMB1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMC1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMD1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDME1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMF1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMG1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMH1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMI1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMJ1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDMK1', 'FINST-8LG66D71ENLX45GQE4SHIB9ETG7G2YN6D2QDML1']
2025-07-30 22:31:25,021 - INFO - 批量插入完成，共 43 条记录
2025-07-30 22:31:25,021 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 43 条，错误: 0 条
2025-07-30 22:31:25,021 - INFO - 数据同步完成！更新: 0 条，插入: 43 条，错误: 2 条
2025-07-30 22:32:25,060 - INFO - 开始同步昨天与今天的销售数据: 2025-07-29 至 2025-07-30
2025-07-30 22:32:25,060 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-30 22:32:25,060 - INFO - 查询参数: ('2025-07-29', '2025-07-30')
2025-07-30 22:32:25,232 - INFO - MySQL查询成功，时间段: 2025-07-29 至 2025-07-30，共获取 631 条记录
2025-07-30 22:32:25,232 - INFO - 获取到 2 个日期需要处理: ['2025-07-29', '2025-07-30']
2025-07-30 22:32:25,232 - INFO - 开始处理日期: 2025-07-29
2025-07-30 22:32:25,232 - INFO - Request Parameters - Page 1:
2025-07-30 22:32:25,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:32:25,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:32:25,967 - INFO - Response - Page 1:
2025-07-30 22:32:25,967 - INFO - 第 1 页获取到 50 条记录
2025-07-30 22:32:26,483 - INFO - Request Parameters - Page 2:
2025-07-30 22:32:26,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:32:26,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:32:27,139 - INFO - Response - Page 2:
2025-07-30 22:32:27,139 - INFO - 第 2 页获取到 50 条记录
2025-07-30 22:32:27,639 - INFO - Request Parameters - Page 3:
2025-07-30 22:32:27,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:32:27,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:32:28,327 - INFO - Response - Page 3:
2025-07-30 22:32:28,327 - INFO - 第 3 页获取到 50 条记录
2025-07-30 22:32:28,827 - INFO - Request Parameters - Page 4:
2025-07-30 22:32:28,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:32:28,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:32:29,562 - INFO - Response - Page 4:
2025-07-30 22:32:29,562 - INFO - 第 4 页获取到 50 条记录
2025-07-30 22:32:30,078 - INFO - Request Parameters - Page 5:
2025-07-30 22:32:30,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:32:30,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:32:30,766 - INFO - Response - Page 5:
2025-07-30 22:32:30,766 - INFO - 第 5 页获取到 50 条记录
2025-07-30 22:32:31,282 - INFO - Request Parameters - Page 6:
2025-07-30 22:32:31,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:32:31,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:32:31,985 - INFO - Response - Page 6:
2025-07-30 22:32:31,985 - INFO - 第 6 页获取到 50 条记录
2025-07-30 22:32:32,501 - INFO - Request Parameters - Page 7:
2025-07-30 22:32:32,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:32:32,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:32:33,220 - INFO - Response - Page 7:
2025-07-30 22:32:33,220 - INFO - 第 7 页获取到 50 条记录
2025-07-30 22:32:33,720 - INFO - Request Parameters - Page 8:
2025-07-30 22:32:33,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:32:33,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:32:34,439 - INFO - Response - Page 8:
2025-07-30 22:32:34,439 - INFO - 第 8 页获取到 50 条记录
2025-07-30 22:32:34,939 - INFO - Request Parameters - Page 9:
2025-07-30 22:32:34,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:32:34,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:32:35,690 - INFO - Response - Page 9:
2025-07-30 22:32:35,690 - INFO - 第 9 页获取到 50 条记录
2025-07-30 22:32:36,205 - INFO - Request Parameters - Page 10:
2025-07-30 22:32:36,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:32:36,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:32:36,893 - INFO - Response - Page 10:
2025-07-30 22:32:36,893 - INFO - 第 10 页获取到 50 条记录
2025-07-30 22:32:37,409 - INFO - Request Parameters - Page 11:
2025-07-30 22:32:37,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:32:37,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:32:38,112 - INFO - Response - Page 11:
2025-07-30 22:32:38,112 - INFO - 第 11 页获取到 50 条记录
2025-07-30 22:32:38,613 - INFO - Request Parameters - Page 12:
2025-07-30 22:32:38,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:32:38,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:32:39,222 - INFO - Response - Page 12:
2025-07-30 22:32:39,222 - INFO - 第 12 页获取到 15 条记录
2025-07-30 22:32:39,722 - INFO - 查询完成，共获取到 565 条记录
2025-07-30 22:32:39,722 - INFO - 获取到 565 条表单数据
2025-07-30 22:32:39,722 - INFO - 当前日期 2025-07-29 有 565 条MySQL数据需要处理
2025-07-30 22:32:39,738 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-30 22:32:39,738 - INFO - 开始处理日期: 2025-07-30
2025-07-30 22:32:39,738 - INFO - Request Parameters - Page 1:
2025-07-30 22:32:39,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-30 22:32:39,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-30 22:32:40,410 - INFO - Response - Page 1:
2025-07-30 22:32:40,410 - INFO - 第 1 页获取到 45 条记录
2025-07-30 22:32:40,910 - INFO - 查询完成，共获取到 45 条记录
2025-07-30 22:32:40,910 - INFO - 获取到 45 条表单数据
2025-07-30 22:32:40,910 - INFO - 当前日期 2025-07-30 有 45 条MySQL数据需要处理
2025-07-30 22:32:40,910 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-30 22:32:40,910 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-30 22:32:40,910 - INFO - 同步完成
