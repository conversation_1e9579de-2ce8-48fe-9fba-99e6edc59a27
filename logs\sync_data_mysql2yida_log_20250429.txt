2025-04-29 00:30:34,077 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 00:30:34,077 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 00:30:34,077 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 00:30:34,124 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 0 条记录
2025-04-29 00:30:34,124 - ERROR - 未获取到MySQL数据
2025-04-29 00:31:34,184 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 00:31:34,184 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 00:31:34,184 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 00:31:34,231 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 00:31:34,231 - ERROR - 未获取到MySQL数据
2025-04-29 00:31:34,231 - INFO - 同步完成
2025-04-29 01:30:34,224 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 01:30:34,224 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 01:30:34,224 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 01:30:34,271 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 0 条记录
2025-04-29 01:30:34,271 - ERROR - 未获取到MySQL数据
2025-04-29 01:31:34,332 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 01:31:34,332 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 01:31:34,332 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 01:31:34,379 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 01:31:34,379 - ERROR - 未获取到MySQL数据
2025-04-29 01:31:34,379 - INFO - 同步完成
2025-04-29 02:30:34,216 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 02:30:34,216 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 02:30:34,216 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 02:30:34,263 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 0 条记录
2025-04-29 02:30:34,263 - ERROR - 未获取到MySQL数据
2025-04-29 02:31:34,323 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 02:31:34,323 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 02:31:34,323 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 02:31:34,370 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 02:31:34,370 - ERROR - 未获取到MySQL数据
2025-04-29 02:31:34,370 - INFO - 同步完成
2025-04-29 03:30:34,254 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 03:30:34,254 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 03:30:34,254 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 03:30:34,301 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 0 条记录
2025-04-29 03:30:34,301 - ERROR - 未获取到MySQL数据
2025-04-29 03:31:34,362 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 03:31:34,362 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 03:31:34,362 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 03:31:34,409 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 03:31:34,409 - ERROR - 未获取到MySQL数据
2025-04-29 03:31:34,409 - INFO - 同步完成
2025-04-29 04:30:34,214 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 04:30:34,214 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 04:30:34,214 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 04:30:34,261 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 0 条记录
2025-04-29 04:30:34,261 - ERROR - 未获取到MySQL数据
2025-04-29 04:31:34,321 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 04:31:34,321 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 04:31:34,321 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 04:31:34,368 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 04:31:34,368 - ERROR - 未获取到MySQL数据
2025-04-29 04:31:34,368 - INFO - 同步完成
2025-04-29 05:30:34,211 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 05:30:34,211 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 05:30:34,211 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 05:30:34,258 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 0 条记录
2025-04-29 05:30:34,258 - ERROR - 未获取到MySQL数据
2025-04-29 05:31:34,319 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 05:31:34,319 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 05:31:34,319 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 05:31:34,366 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 05:31:34,366 - ERROR - 未获取到MySQL数据
2025-04-29 05:31:34,366 - INFO - 同步完成
2025-04-29 06:30:33,780 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 06:30:33,780 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 06:30:33,780 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 06:30:33,843 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 0 条记录
2025-04-29 06:30:33,843 - ERROR - 未获取到MySQL数据
2025-04-29 06:31:33,858 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 06:31:33,858 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 06:31:33,858 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 06:31:33,905 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 06:31:33,905 - ERROR - 未获取到MySQL数据
2025-04-29 06:31:33,905 - INFO - 同步完成
2025-04-29 07:30:33,758 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 07:30:33,758 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 07:30:33,758 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 07:30:33,820 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 3 条记录
2025-04-29 07:30:33,820 - INFO - 获取到 1 个日期需要处理: ['2025-04-28']
2025-04-29 07:30:33,820 - INFO - 开始处理日期: 2025-04-28
2025-04-29 07:30:33,820 - INFO - Request Parameters - Page 1:
2025-04-29 07:30:33,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 07:30:33,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 07:30:41,992 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0EBF2BE7-6BD7-7C81-814A-3A7AE7FADE40 Response: {'code': 'ServiceUnavailable', 'requestid': '0EBF2BE7-6BD7-7C81-814A-3A7AE7FADE40', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0EBF2BE7-6BD7-7C81-814A-3A7AE7FADE40)
2025-04-29 07:30:41,992 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-29 07:31:42,007 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 07:31:42,007 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 07:31:42,007 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 07:31:42,054 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 07:31:42,054 - ERROR - 未获取到MySQL数据
2025-04-29 07:31:42,054 - INFO - 同步完成
2025-04-29 08:30:33,766 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 08:30:33,766 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 08:30:33,766 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 08:30:33,829 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 15 条记录
2025-04-29 08:30:33,829 - INFO - 获取到 1 个日期需要处理: ['2025-04-28']
2025-04-29 08:30:33,829 - INFO - 开始处理日期: 2025-04-28
2025-04-29 08:30:33,829 - INFO - Request Parameters - Page 1:
2025-04-29 08:30:33,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 08:30:33,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 08:30:41,938 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3A9C116A-606B-70C7-B946-5C867F071664 Response: {'code': 'ServiceUnavailable', 'requestid': '3A9C116A-606B-70C7-B946-5C867F071664', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3A9C116A-606B-70C7-B946-5C867F071664)
2025-04-29 08:30:41,938 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-29 08:31:41,953 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 08:31:41,953 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 08:31:41,953 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 08:31:42,000 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 08:31:42,000 - ERROR - 未获取到MySQL数据
2025-04-29 08:31:42,000 - INFO - 同步完成
2025-04-29 09:30:33,915 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 09:30:33,915 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 09:30:33,915 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 09:30:33,978 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 101 条记录
2025-04-29 09:30:33,978 - INFO - 获取到 2 个日期需要处理: ['2025-04-27', '2025-04-28']
2025-04-29 09:30:33,978 - INFO - 开始处理日期: 2025-04-27
2025-04-29 09:30:33,978 - INFO - Request Parameters - Page 1:
2025-04-29 09:30:33,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:30:33,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:30:42,103 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FABE4BBA-BF14-75DF-9F57-0D9B18417C04 Response: {'code': 'ServiceUnavailable', 'requestid': 'FABE4BBA-BF14-75DF-9F57-0D9B18417C04', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FABE4BBA-BF14-75DF-9F57-0D9B18417C04)
2025-04-29 09:30:42,103 - INFO - 开始处理日期: 2025-04-28
2025-04-29 09:30:42,103 - INFO - Request Parameters - Page 1:
2025-04-29 09:30:42,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 09:30:42,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 09:30:50,197 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 337A0654-98E2-7E0B-85BD-BC2B10E6E5F0 Response: {'code': 'ServiceUnavailable', 'requestid': '337A0654-98E2-7E0B-85BD-BC2B10E6E5F0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 337A0654-98E2-7E0B-85BD-BC2B10E6E5F0)
2025-04-29 09:30:50,197 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-29 09:31:50,212 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 09:31:50,212 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 09:31:50,212 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 09:31:50,259 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 09:31:50,259 - ERROR - 未获取到MySQL数据
2025-04-29 09:31:50,259 - INFO - 同步完成
2025-04-29 10:30:33,752 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 10:30:33,752 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 10:30:33,752 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 10:30:33,815 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 193 条记录
2025-04-29 10:30:33,815 - INFO - 获取到 2 个日期需要处理: ['2025-04-27', '2025-04-28']
2025-04-29 10:30:33,830 - INFO - 开始处理日期: 2025-04-27
2025-04-29 10:30:33,830 - INFO - Request Parameters - Page 1:
2025-04-29 10:30:33,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 10:30:33,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 10:30:41,940 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BB4899E8-5CF7-7E3F-A2FC-51BF9A01DF0B Response: {'code': 'ServiceUnavailable', 'requestid': 'BB4899E8-5CF7-7E3F-A2FC-51BF9A01DF0B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BB4899E8-5CF7-7E3F-A2FC-51BF9A01DF0B)
2025-04-29 10:30:41,940 - INFO - 开始处理日期: 2025-04-28
2025-04-29 10:30:41,940 - INFO - Request Parameters - Page 1:
2025-04-29 10:30:41,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 10:30:41,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 10:30:50,049 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4C415BC6-C769-7A78-AF32-74F62B727A6B Response: {'code': 'ServiceUnavailable', 'requestid': '4C415BC6-C769-7A78-AF32-74F62B727A6B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4C415BC6-C769-7A78-AF32-74F62B727A6B)
2025-04-29 10:30:50,049 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-29 10:31:50,064 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 10:31:50,064 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 10:31:50,064 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 10:31:50,111 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 10:31:50,111 - ERROR - 未获取到MySQL数据
2025-04-29 10:31:50,111 - INFO - 同步完成
2025-04-29 11:30:33,839 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 11:30:33,839 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 11:30:33,839 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 11:30:33,901 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 198 条记录
2025-04-29 11:30:33,901 - INFO - 获取到 2 个日期需要处理: ['2025-04-27', '2025-04-28']
2025-04-29 11:30:33,901 - INFO - 开始处理日期: 2025-04-27
2025-04-29 11:30:33,917 - INFO - Request Parameters - Page 1:
2025-04-29 11:30:33,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 11:30:33,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 11:30:42,026 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FFABA749-3124-7E78-800A-6E57FD6F264D Response: {'code': 'ServiceUnavailable', 'requestid': 'FFABA749-3124-7E78-800A-6E57FD6F264D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FFABA749-3124-7E78-800A-6E57FD6F264D)
2025-04-29 11:30:42,026 - INFO - 开始处理日期: 2025-04-28
2025-04-29 11:30:42,026 - INFO - Request Parameters - Page 1:
2025-04-29 11:30:42,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 11:30:42,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 11:30:50,151 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F16FD964-8A85-798C-A158-C5BDE29B2663 Response: {'code': 'ServiceUnavailable', 'requestid': 'F16FD964-8A85-798C-A158-C5BDE29B2663', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F16FD964-8A85-798C-A158-C5BDE29B2663)
2025-04-29 11:30:50,151 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-29 11:31:50,167 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 11:31:50,167 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 11:31:50,167 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 11:31:50,213 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 11:31:50,213 - ERROR - 未获取到MySQL数据
2025-04-29 11:31:50,213 - INFO - 同步完成
2025-04-29 12:30:33,926 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 12:30:33,926 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 12:30:33,926 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 12:30:33,988 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 200 条记录
2025-04-29 12:30:33,988 - INFO - 获取到 2 个日期需要处理: ['2025-04-27', '2025-04-28']
2025-04-29 12:30:33,988 - INFO - 开始处理日期: 2025-04-27
2025-04-29 12:30:33,988 - INFO - Request Parameters - Page 1:
2025-04-29 12:30:33,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:30:33,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:30:42,113 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D7F223BB-BCEC-71AD-A706-FE8D977CB6BA Response: {'code': 'ServiceUnavailable', 'requestid': 'D7F223BB-BCEC-71AD-A706-FE8D977CB6BA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D7F223BB-BCEC-71AD-A706-FE8D977CB6BA)
2025-04-29 12:30:42,113 - INFO - 开始处理日期: 2025-04-28
2025-04-29 12:30:42,113 - INFO - Request Parameters - Page 1:
2025-04-29 12:30:42,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 12:30:42,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 12:30:50,223 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BFEFCD3F-DC9D-7FC6-A05D-C267D74D0385 Response: {'code': 'ServiceUnavailable', 'requestid': 'BFEFCD3F-DC9D-7FC6-A05D-C267D74D0385', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BFEFCD3F-DC9D-7FC6-A05D-C267D74D0385)
2025-04-29 12:30:50,223 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-29 12:31:50,238 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 12:31:50,238 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 12:31:50,238 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 12:31:50,285 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 12:31:50,285 - ERROR - 未获取到MySQL数据
2025-04-29 12:31:50,285 - INFO - 同步完成
2025-04-29 13:30:33,794 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 13:30:33,794 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 13:30:33,794 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 13:30:33,856 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 200 条记录
2025-04-29 13:30:33,856 - INFO - 获取到 2 个日期需要处理: ['2025-04-27', '2025-04-28']
2025-04-29 13:30:33,856 - INFO - 开始处理日期: 2025-04-27
2025-04-29 13:30:33,856 - INFO - Request Parameters - Page 1:
2025-04-29 13:30:33,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 13:30:33,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 13:30:41,981 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 14428609-**************-1F0E860F0DA2 Response: {'code': 'ServiceUnavailable', 'requestid': '14428609-**************-1F0E860F0DA2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 14428609-**************-1F0E860F0DA2)
2025-04-29 13:30:41,981 - INFO - 开始处理日期: 2025-04-28
2025-04-29 13:30:41,981 - INFO - Request Parameters - Page 1:
2025-04-29 13:30:41,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 13:30:41,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 13:30:42,700 - INFO - Response - Page 1:
2025-04-29 13:30:42,700 - INFO - 第 1 页获取到 64 条记录
2025-04-29 13:30:42,903 - INFO - 查询完成，共获取到 64 条记录
2025-04-29 13:30:42,903 - INFO - 获取到 64 条表单数据
2025-04-29 13:30:42,903 - INFO - 当前日期 2025-04-28 有 195 条MySQL数据需要处理
2025-04-29 13:30:42,903 - INFO - 开始更新记录 - 表单实例ID: FINST-WBF66B814OYUMMIQ8S43MAJBNT8N3CNRIE0AM0
2025-04-29 13:30:43,356 - INFO - 更新表单数据成功: FINST-WBF66B814OYUMMIQ8S43MAJBNT8N3CNRIE0AM0
2025-04-29 13:30:43,356 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 931.0, 'new_value': 2137.28}, {'field': 'total_amount', 'old_value': 931.0, 'new_value': 2137.28}, {'field': 'order_count', 'old_value': 18, 'new_value': 44}]
2025-04-29 13:30:43,356 - INFO - 开始批量插入 194 条新记录
2025-04-29 13:30:43,700 - INFO - 批量插入响应状态码: 200
2025-04-29 13:30:43,700 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Apr 2025 05:30:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D24BF40D-657E-7078-A89D-9566845CD33A', 'x-acs-trace-id': '93313bbdeb990339c9109266152afbf9', 'etag': '44rGh4nFubXnQXC4rHUW1JA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-29 13:30:43,700 - INFO - 批量插入响应体: {'result': ['FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM67', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM77', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM87', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM97', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMA7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMB7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMC7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMD7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AME7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMF7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMG7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMH7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMI7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMJ7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMK7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AML7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMM7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMN7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMO7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMP7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMQ7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMR7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMS7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMT7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMU7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMV7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMW7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMX7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMY7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMZ7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM08', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM18', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM28', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM38', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM48', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM58', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM68', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM78', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM88', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM98', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMA8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMB8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMC8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMD8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AME8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMF8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMG8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMH8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMI8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMJ8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMK8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AML8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMM8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMN8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMO8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMP8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMQ8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMR8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMS8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMT8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMU8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMV8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMW8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMX8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMY8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMZ8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM09', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM19', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM29', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM39', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM49', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM59', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM69', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM79', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM89', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM99', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMA9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMB9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMC9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMD9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AME9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMF9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMG9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMH9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMI9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMJ9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMK9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AML9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMM9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMN9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMO9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMP9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMQ9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMR9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMS9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMT9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMU9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMV9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMW9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMX9']}
2025-04-29 13:30:43,700 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-04-29 13:30:43,700 - INFO - 成功插入的数据ID: ['FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM67', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM77', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM87', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM97', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMA7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMB7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMC7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMD7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AME7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMF7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMG7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMH7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMI7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMJ7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMK7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AML7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMM7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMN7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMO7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMP7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMQ7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMR7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMS7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMT7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMU7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMV7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMW7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMX7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMY7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMZ7', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM08', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM18', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM28', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM38', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM48', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM58', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM68', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM78', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM88', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM98', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMA8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMB8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMC8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMD8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AME8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMF8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMG8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMH8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMI8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMJ8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMK8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AML8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMM8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMN8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMO8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMP8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMQ8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMR8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMS8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMT8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMU8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMV8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMW8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMX8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMY8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMZ8', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM09', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM19', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM29', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM39', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM49', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM59', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM69', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM79', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM89', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AM99', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMA9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMB9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMC9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMD9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AME9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMF9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMG9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMH9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMI9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMJ9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMK9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AML9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMM9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMN9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMO9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMP9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMQ9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMR9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMS9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMT9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMU9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMV9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMW9', 'FINST-I3F66991D5ZUXMGEE6YWFAVQGK8B3JF5J22AMX9']
2025-04-29 13:30:48,966 - INFO - 批量插入响应状态码: 200
2025-04-29 13:30:48,966 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Apr 2025 05:30:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4524', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '41D29647-5D89-77F9-8D92-E4863ACFB4A1', 'x-acs-trace-id': '09f67d4f7d44b7567c0d8d4c4858a61b', 'etag': '43BD1JayCrya8gxIUhmnu6g4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-29 13:30:48,966 - INFO - 批量插入响应体: {'result': ['FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMD3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AME3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMF3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMG3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMH3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMI3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMJ3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMK3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AML3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMM3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMN3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMO3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMP3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMQ3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMR3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMS3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMT3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMU3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMV3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMW3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMX3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMY3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMZ3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM04', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM14', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM24', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM34', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM44', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM54', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM64', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM74', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM84', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM94', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMA4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMB4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMC4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMD4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AME4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMF4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMG4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMH4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMI4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMJ4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMK4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AML4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMM4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMN4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMO4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMP4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMQ4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMR4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMS4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMT4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMU4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMV4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMW4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMX4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMY4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMZ4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM05', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM15', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM25', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM35', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM45', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM55', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM65', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM75', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM85', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM95', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMA5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMB5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMC5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMD5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AME5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMF5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMG5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMH5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMI5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMJ5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMK5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AML5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMM5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMN5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMO5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMP5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMQ5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMR5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMS5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMT5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMU5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMV5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMW5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMX5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMY5']}
2025-04-29 13:30:48,966 - INFO - 批量插入表单数据成功，批次 2，共 94 条记录
2025-04-29 13:30:48,966 - INFO - 成功插入的数据ID: ['FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMD3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AME3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMF3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMG3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMH3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMI3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMJ3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMK3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AML3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMM3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMN3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMO3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMP3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMQ3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMR3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMS3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMT3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMU3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMV3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMW3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMX3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMY3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMZ3', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM04', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM14', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM24', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM34', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM44', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM54', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM64', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM74', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM84', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM94', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMA4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMB4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMC4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMD4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AME4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMF4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMG4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMH4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMI4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMJ4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMK4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AML4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMM4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMN4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMO4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMP4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMQ4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMR4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMS4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMT4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMU4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMV4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMW4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMX4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMY4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMZ4', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM05', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM15', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM25', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM35', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM45', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM55', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM65', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM75', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM85', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AM95', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMA5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMB5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMC5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMD5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AME5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMF5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMG5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMH5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMI5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMJ5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMK5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AML5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMM5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMN5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMO5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMP5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMQ5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMR5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMS5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMT5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMU5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMV5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMW5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMX5', 'FINST-GRA66V61TBZUAA4569IPL9Y09LU32VH9J22AMY5']
2025-04-29 13:30:53,981 - INFO - 批量插入完成，共 194 条记录
2025-04-29 13:30:53,981 - INFO - 日期 2025-04-28 处理完成 - 更新: 1 条，插入: 194 条，错误: 0 条
2025-04-29 13:30:53,981 - INFO - 数据同步完成！更新: 1 条，插入: 194 条，错误: 1 条
2025-04-29 13:31:53,997 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 13:31:53,997 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 13:31:53,997 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 13:31:54,043 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 13:31:54,043 - ERROR - 未获取到MySQL数据
2025-04-29 13:31:54,043 - INFO - 同步完成
2025-04-29 14:30:33,740 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 14:30:33,740 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 14:30:33,740 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 14:30:33,803 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 201 条记录
2025-04-29 14:30:33,803 - INFO - 获取到 2 个日期需要处理: ['2025-04-27', '2025-04-28']
2025-04-29 14:30:33,818 - INFO - 开始处理日期: 2025-04-27
2025-04-29 14:30:33,818 - INFO - Request Parameters - Page 1:
2025-04-29 14:30:33,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 14:30:33,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 14:30:41,943 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 75553960-7D3B-7C22-AF56-A0338F7578FB Response: {'code': 'ServiceUnavailable', 'requestid': '75553960-7D3B-7C22-AF56-A0338F7578FB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 75553960-7D3B-7C22-AF56-A0338F7578FB)
2025-04-29 14:30:41,943 - INFO - 开始处理日期: 2025-04-28
2025-04-29 14:30:41,943 - INFO - Request Parameters - Page 1:
2025-04-29 14:30:41,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 14:30:41,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 14:30:42,084 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 5778CC43-3C98-7F97-86DB-DC47523FB448 Response: {'requestid': '5778CC43-3C98-7F97-86DB-DC47523FB448', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 5778CC43-3C98-7F97-86DB-DC47523FB448)
2025-04-29 14:30:42,084 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-29 14:31:42,099 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 14:31:42,099 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 14:31:42,099 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 14:31:42,146 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 14:31:42,146 - ERROR - 未获取到MySQL数据
2025-04-29 14:31:42,146 - INFO - 同步完成
2025-04-29 15:30:33,749 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 15:30:33,749 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 15:30:33,749 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 15:30:33,812 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 201 条记录
2025-04-29 15:30:33,812 - INFO - 获取到 2 个日期需要处理: ['2025-04-27', '2025-04-28']
2025-04-29 15:30:33,812 - INFO - 开始处理日期: 2025-04-27
2025-04-29 15:30:33,812 - INFO - Request Parameters - Page 1:
2025-04-29 15:30:33,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:30:33,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:30:41,952 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DA7AC64A-4CB7-7418-8689-8C729D9BFAFC Response: {'code': 'ServiceUnavailable', 'requestid': 'DA7AC64A-4CB7-7418-8689-8C729D9BFAFC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DA7AC64A-4CB7-7418-8689-8C729D9BFAFC)
2025-04-29 15:30:41,952 - INFO - 开始处理日期: 2025-04-28
2025-04-29 15:30:41,952 - INFO - Request Parameters - Page 1:
2025-04-29 15:30:41,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 15:30:41,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 15:30:42,077 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: F6A610C3-CAE3-7677-B2C2-3AF481C310A6 Response: {'requestid': 'F6A610C3-CAE3-7677-B2C2-3AF481C310A6', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: F6A610C3-CAE3-7677-B2C2-3AF481C310A6)
2025-04-29 15:30:42,077 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-29 15:31:42,108 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 15:31:42,108 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 15:31:42,108 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 15:31:42,155 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 15:31:42,155 - ERROR - 未获取到MySQL数据
2025-04-29 15:31:42,155 - INFO - 同步完成
2025-04-29 16:30:33,742 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 16:30:33,742 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 16:30:33,742 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 16:30:33,805 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 202 条记录
2025-04-29 16:30:33,805 - INFO - 获取到 2 个日期需要处理: ['2025-04-27', '2025-04-28']
2025-04-29 16:30:33,805 - INFO - 开始处理日期: 2025-04-27
2025-04-29 16:30:33,820 - INFO - Request Parameters - Page 1:
2025-04-29 16:30:33,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 16:30:33,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 16:30:41,945 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4708B4DE-CFE9-718F-A149-3996A82A8253 Response: {'code': 'ServiceUnavailable', 'requestid': '4708B4DE-CFE9-718F-A149-3996A82A8253', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4708B4DE-CFE9-718F-A149-3996A82A8253)
2025-04-29 16:30:41,945 - INFO - 开始处理日期: 2025-04-28
2025-04-29 16:30:41,945 - INFO - Request Parameters - Page 1:
2025-04-29 16:30:41,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 16:30:41,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 16:30:42,086 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 0EF2D54A-6EDC-7CEF-B52A-74152CD39CE9 Response: {'requestid': '0EF2D54A-6EDC-7CEF-B52A-74152CD39CE9', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 0EF2D54A-6EDC-7CEF-B52A-74152CD39CE9)
2025-04-29 16:30:42,086 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-29 16:31:42,101 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 16:31:42,101 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 16:31:42,101 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 16:31:42,148 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 16:31:42,148 - ERROR - 未获取到MySQL数据
2025-04-29 16:31:42,148 - INFO - 同步完成
2025-04-29 17:30:33,751 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 17:30:33,751 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 17:30:33,751 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 17:30:33,814 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 206 条记录
2025-04-29 17:30:33,814 - INFO - 获取到 3 个日期需要处理: ['2025-04-27', '2025-04-28', '2025-04-29']
2025-04-29 17:30:33,814 - INFO - 开始处理日期: 2025-04-27
2025-04-29 17:30:33,814 - INFO - Request Parameters - Page 1:
2025-04-29 17:30:33,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 17:30:33,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 17:30:41,954 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 60711407-C603-70CA-A2FA-345FB65B6F8F Response: {'code': 'ServiceUnavailable', 'requestid': '60711407-C603-70CA-A2FA-345FB65B6F8F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 60711407-C603-70CA-A2FA-345FB65B6F8F)
2025-04-29 17:30:41,954 - INFO - 开始处理日期: 2025-04-28
2025-04-29 17:30:41,954 - INFO - Request Parameters - Page 1:
2025-04-29 17:30:41,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 17:30:41,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 17:30:42,111 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: E6B4B277-30BE-7FD2-85CE-5CD73F69A5FC Response: {'requestid': 'E6B4B277-30BE-7FD2-85CE-5CD73F69A5FC', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: E6B4B277-30BE-7FD2-85CE-5CD73F69A5FC)
2025-04-29 17:30:42,111 - INFO - 开始处理日期: 2025-04-29
2025-04-29 17:30:42,111 - INFO - Request Parameters - Page 1:
2025-04-29 17:30:42,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 17:30:42,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 17:30:42,251 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: E1C5053A-C6A0-70BB-BF7F-C6EC416FB500 Response: {'requestid': 'E1C5053A-C6A0-70BB-BF7F-C6EC416FB500', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: E1C5053A-C6A0-70BB-BF7F-C6EC416FB500)
2025-04-29 17:30:42,251 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-04-29 17:31:42,266 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 17:31:42,266 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 17:31:42,266 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 17:31:42,313 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 17:31:42,313 - ERROR - 未获取到MySQL数据
2025-04-29 17:31:42,313 - INFO - 同步完成
2025-04-29 18:30:33,948 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 18:30:33,948 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 18:30:33,948 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 18:30:34,010 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 208 条记录
2025-04-29 18:30:34,010 - INFO - 获取到 3 个日期需要处理: ['2025-04-27', '2025-04-28', '2025-04-29']
2025-04-29 18:30:34,010 - INFO - 开始处理日期: 2025-04-27
2025-04-29 18:30:34,010 - INFO - Request Parameters - Page 1:
2025-04-29 18:30:34,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:30:34,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:30:42,135 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F00647BD-3620-7710-9FB7-16AF96EF7516 Response: {'code': 'ServiceUnavailable', 'requestid': 'F00647BD-3620-7710-9FB7-16AF96EF7516', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F00647BD-3620-7710-9FB7-16AF96EF7516)
2025-04-29 18:30:42,135 - INFO - 开始处理日期: 2025-04-28
2025-04-29 18:30:42,135 - INFO - Request Parameters - Page 1:
2025-04-29 18:30:42,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:30:42,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:30:42,291 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 2B8F8F26-DF7E-72E8-BDE6-1E705D5B6FFF Response: {'requestid': '2B8F8F26-DF7E-72E8-BDE6-1E705D5B6FFF', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 2B8F8F26-DF7E-72E8-BDE6-1E705D5B6FFF)
2025-04-29 18:30:42,291 - INFO - 开始处理日期: 2025-04-29
2025-04-29 18:30:42,291 - INFO - Request Parameters - Page 1:
2025-04-29 18:30:42,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 18:30:42,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 18:30:42,432 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 4250FA87-BD0B-76DF-9E2B-520E019E014C Response: {'requestid': '4250FA87-BD0B-76DF-9E2B-520E019E014C', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 4250FA87-BD0B-76DF-9E2B-520E019E014C)
2025-04-29 18:30:42,448 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-04-29 18:31:42,463 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 18:31:42,463 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 18:31:42,463 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 18:31:42,510 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 18:31:42,510 - ERROR - 未获取到MySQL数据
2025-04-29 18:31:42,510 - INFO - 同步完成
2025-04-29 19:30:33,873 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 19:30:33,873 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 19:30:33,873 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 19:30:33,936 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 209 条记录
2025-04-29 19:30:33,936 - INFO - 获取到 3 个日期需要处理: ['2025-04-27', '2025-04-28', '2025-04-29']
2025-04-29 19:30:33,936 - INFO - 开始处理日期: 2025-04-27
2025-04-29 19:30:33,951 - INFO - Request Parameters - Page 1:
2025-04-29 19:30:33,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 19:30:33,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 19:30:42,061 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DAC5119A-29A0-774B-A69F-69C3DB86676C Response: {'code': 'ServiceUnavailable', 'requestid': 'DAC5119A-29A0-774B-A69F-69C3DB86676C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DAC5119A-29A0-774B-A69F-69C3DB86676C)
2025-04-29 19:30:42,061 - INFO - 开始处理日期: 2025-04-28
2025-04-29 19:30:42,061 - INFO - Request Parameters - Page 1:
2025-04-29 19:30:42,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 19:30:42,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 19:30:42,217 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: CEECF05D-FF46-730D-98E0-04CF3A7CF901 Response: {'requestid': 'CEECF05D-FF46-730D-98E0-04CF3A7CF901', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: CEECF05D-FF46-730D-98E0-04CF3A7CF901)
2025-04-29 19:30:42,217 - INFO - 开始处理日期: 2025-04-29
2025-04-29 19:30:42,217 - INFO - Request Parameters - Page 1:
2025-04-29 19:30:42,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 19:30:42,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 19:30:42,373 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 42913522-C305-78AD-A595-6E55EF2C4D2A Response: {'requestid': '42913522-C305-78AD-A595-6E55EF2C4D2A', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 42913522-C305-78AD-A595-6E55EF2C4D2A)
2025-04-29 19:30:42,373 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-04-29 19:31:42,388 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 19:31:42,388 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 19:31:42,388 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 19:31:42,435 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 19:31:42,435 - ERROR - 未获取到MySQL数据
2025-04-29 19:31:42,435 - INFO - 同步完成
2025-04-29 20:30:33,775 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 20:30:33,775 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 20:30:33,775 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 20:30:33,837 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 209 条记录
2025-04-29 20:30:33,837 - INFO - 获取到 3 个日期需要处理: ['2025-04-27', '2025-04-28', '2025-04-29']
2025-04-29 20:30:33,837 - INFO - 开始处理日期: 2025-04-27
2025-04-29 20:30:33,837 - INFO - Request Parameters - Page 1:
2025-04-29 20:30:33,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 20:30:33,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 20:30:41,978 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4738E9A0-EA71-7A3B-88D7-927D3A41E851 Response: {'code': 'ServiceUnavailable', 'requestid': '4738E9A0-EA71-7A3B-88D7-927D3A41E851', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4738E9A0-EA71-7A3B-88D7-927D3A41E851)
2025-04-29 20:30:41,978 - INFO - 开始处理日期: 2025-04-28
2025-04-29 20:30:41,978 - INFO - Request Parameters - Page 1:
2025-04-29 20:30:41,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 20:30:41,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 20:30:50,087 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 59D854D6-2F81-70B1-BCD8-A524F02E978D Response: {'code': 'ServiceUnavailable', 'requestid': '59D854D6-2F81-70B1-BCD8-A524F02E978D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 59D854D6-2F81-70B1-BCD8-A524F02E978D)
2025-04-29 20:30:50,087 - INFO - 开始处理日期: 2025-04-29
2025-04-29 20:30:50,087 - INFO - Request Parameters - Page 1:
2025-04-29 20:30:50,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 20:30:50,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 20:30:50,524 - INFO - Response - Page 1:
2025-04-29 20:30:50,524 - INFO - 查询完成，共获取到 0 条记录
2025-04-29 20:30:50,524 - INFO - 获取到 0 条表单数据
2025-04-29 20:30:50,524 - INFO - 当前日期 2025-04-29 有 3 条MySQL数据需要处理
2025-04-29 20:30:50,524 - INFO - 开始批量插入 3 条新记录
2025-04-29 20:30:50,696 - INFO - 批量插入响应状态码: 200
2025-04-29 20:30:50,696 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Apr 2025 12:30:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '153', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '044F49CB-803A-7BA4-B7DB-977AF66FB74F', 'x-acs-trace-id': 'bf077aa4b2589d3180e235305d89d534', 'etag': '1foFavD3xCKH3qE45Nfr0uw3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-29 20:30:50,696 - INFO - 批量插入响应体: {'result': ['FINST-YPE66RB1CDZU3L8ACJKG37X77MVE3MNYJH2AMW', 'FINST-YPE66RB1CDZU3L8ACJKG37X77MVE3MNYJH2AMX', 'FINST-YPE66RB1CDZU3L8ACJKG37X77MVE3MNYJH2AMY']}
2025-04-29 20:30:50,696 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-04-29 20:30:50,696 - INFO - 成功插入的数据ID: ['FINST-YPE66RB1CDZU3L8ACJKG37X77MVE3MNYJH2AMW', 'FINST-YPE66RB1CDZU3L8ACJKG37X77MVE3MNYJH2AMX', 'FINST-YPE66RB1CDZU3L8ACJKG37X77MVE3MNYJH2AMY']
2025-04-29 20:30:55,712 - INFO - 批量插入完成，共 3 条记录
2025-04-29 20:30:55,712 - INFO - 日期 2025-04-29 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-04-29 20:30:55,712 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 2 条
2025-04-29 20:31:55,727 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 20:31:55,727 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 20:31:55,727 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 20:31:55,774 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 20:31:55,774 - ERROR - 未获取到MySQL数据
2025-04-29 20:31:55,774 - INFO - 同步完成
2025-04-29 21:30:33,705 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 21:30:33,705 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 21:30:33,705 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 21:30:33,799 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 210 条记录
2025-04-29 21:30:33,799 - INFO - 获取到 3 个日期需要处理: ['2025-04-27', '2025-04-28', '2025-04-29']
2025-04-29 21:30:33,814 - INFO - 开始处理日期: 2025-04-27
2025-04-29 21:30:33,814 - INFO - Request Parameters - Page 1:
2025-04-29 21:30:33,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:30:33,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:30:41,924 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 55B8CD35-F760-7691-8F66-7E99062FE9A9 Response: {'code': 'ServiceUnavailable', 'requestid': '55B8CD35-F760-7691-8F66-7E99062FE9A9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 55B8CD35-F760-7691-8F66-7E99062FE9A9)
2025-04-29 21:30:41,924 - INFO - 开始处理日期: 2025-04-28
2025-04-29 21:30:41,924 - INFO - Request Parameters - Page 1:
2025-04-29 21:30:41,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:30:41,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:30:42,080 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: A24B3602-DF46-733E-BBAF-52094EE5C594 Response: {'requestid': 'A24B3602-DF46-733E-BBAF-52094EE5C594', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: A24B3602-DF46-733E-BBAF-52094EE5C594)
2025-04-29 21:30:42,080 - INFO - 开始处理日期: 2025-04-29
2025-04-29 21:30:42,080 - INFO - Request Parameters - Page 1:
2025-04-29 21:30:42,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 21:30:42,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 21:30:42,221 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 4F02FAF6-BEF4-7CD6-98BF-23DA6395ABB9 Response: {'requestid': '4F02FAF6-BEF4-7CD6-98BF-23DA6395ABB9', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 4F02FAF6-BEF4-7CD6-98BF-23DA6395ABB9)
2025-04-29 21:30:42,221 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-04-29 21:31:42,236 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 21:31:42,236 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 21:31:42,236 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 21:31:42,283 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 21:31:42,283 - ERROR - 未获取到MySQL数据
2025-04-29 21:31:42,283 - INFO - 同步完成
2025-04-29 22:30:33,745 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 22:30:33,745 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 22:30:33,745 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 22:30:33,823 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 284 条记录
2025-04-29 22:30:33,823 - INFO - 获取到 3 个日期需要处理: ['2025-04-27', '2025-04-28', '2025-04-29']
2025-04-29 22:30:33,823 - INFO - 开始处理日期: 2025-04-27
2025-04-29 22:30:33,823 - INFO - Request Parameters - Page 1:
2025-04-29 22:30:33,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 22:30:33,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 22:30:41,932 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E561B6CD-E2FF-7FA1-A415-F0B652E5D5E2 Response: {'code': 'ServiceUnavailable', 'requestid': 'E561B6CD-E2FF-7FA1-A415-F0B652E5D5E2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E561B6CD-E2FF-7FA1-A415-F0B652E5D5E2)
2025-04-29 22:30:41,932 - INFO - 开始处理日期: 2025-04-28
2025-04-29 22:30:41,932 - INFO - Request Parameters - Page 1:
2025-04-29 22:30:41,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 22:30:41,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 22:30:50,057 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D1F393B6-12F2-7904-8A4C-F59FD67A6D2D Response: {'code': 'ServiceUnavailable', 'requestid': 'D1F393B6-12F2-7904-8A4C-F59FD67A6D2D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D1F393B6-12F2-7904-8A4C-F59FD67A6D2D)
2025-04-29 22:30:50,057 - INFO - 开始处理日期: 2025-04-29
2025-04-29 22:30:50,057 - INFO - Request Parameters - Page 1:
2025-04-29 22:30:50,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 22:30:50,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 22:30:50,542 - INFO - Response - Page 1:
2025-04-29 22:30:50,542 - INFO - 第 1 页获取到 3 条记录
2025-04-29 22:30:50,745 - INFO - 查询完成，共获取到 3 条记录
2025-04-29 22:30:50,745 - INFO - 获取到 3 条表单数据
2025-04-29 22:30:50,745 - INFO - 当前日期 2025-04-29 有 78 条MySQL数据需要处理
2025-04-29 22:30:50,745 - INFO - 开始批量插入 75 条新记录
2025-04-29 22:30:50,979 - INFO - 批量插入响应状态码: 200
2025-04-29 22:30:50,979 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Apr 2025 14:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3580', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '88118BAA-9565-7D5E-923F-E09966399FC8', 'x-acs-trace-id': '2dc03f5453b94ba49c5fbab1319db81b', 'etag': '3sGgAl3YqS7EwZ6azyYF/YQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-29 22:30:50,979 - INFO - 批量插入响应体: {'result': ['FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AM4', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AM5', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AM6', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AM7', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AM8', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AM9', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AMA', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AMB', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AMC', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AMD', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AME', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMF', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMG', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMH', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMI', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMJ', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMK', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AML', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMM', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMN', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMO', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMP', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMQ', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMR', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMS', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMT', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMU', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMV', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMW', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMX', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMY', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMZ', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM01', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM11', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM21', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM31', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM41', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM51', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM61', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM71', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM81', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM91', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMA1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMB1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMC1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMD1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AME1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMF1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMG1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMH1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMI1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMJ1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMK1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AML1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMM1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMN1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMO1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMP1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMQ1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMR1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMS1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMT1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMU1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMV1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMW1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMX1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMY1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMZ1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM02', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM12', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM22', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM32', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM42', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM52', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM62']}
2025-04-29 22:30:50,979 - INFO - 批量插入表单数据成功，批次 1，共 75 条记录
2025-04-29 22:30:50,979 - INFO - 成功插入的数据ID: ['FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AM4', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AM5', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AM6', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AM7', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AM8', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AM9', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AMA', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AMB', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AMC', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AMD', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3OGAUL2AME', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMF', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMG', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMH', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMI', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMJ', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMK', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AML', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMM', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMN', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMO', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMP', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMQ', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMR', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMS', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMT', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMU', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMV', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMW', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMX', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMY', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMZ', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM01', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM11', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM21', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM31', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM41', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM51', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM61', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM71', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM81', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM91', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMA1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMB1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMC1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMD1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AME1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMF1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMG1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMH1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMI1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMJ1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMK1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AML1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMM1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMN1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMO1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMP1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMQ1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMR1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMS1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMT1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMU1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMV1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMW1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMX1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMY1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AMZ1', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM02', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM12', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM22', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM32', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM42', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM52', 'FINST-6AG66W81C40V6R6N9KL588PL1B1F3PGAUL2AM62']
2025-04-29 22:30:55,995 - INFO - 批量插入完成，共 75 条记录
2025-04-29 22:30:55,995 - INFO - 日期 2025-04-29 处理完成 - 更新: 0 条，插入: 75 条，错误: 0 条
2025-04-29 22:30:55,995 - INFO - 数据同步完成！更新: 0 条，插入: 75 条，错误: 2 条
2025-04-29 22:31:56,010 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 22:31:56,010 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 22:31:56,010 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 22:31:56,057 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 22:31:56,057 - ERROR - 未获取到MySQL数据
2025-04-29 22:31:56,057 - INFO - 同步完成
2025-04-29 23:30:34,321 - INFO - 使用默认增量同步（当天更新数据）
2025-04-29 23:30:34,321 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 23:30:34,321 - INFO - 查询参数: ('2025-04-29',)
2025-04-29 23:30:34,383 - INFO - MySQL查询成功，增量数据（日期: 2025-04-29），共获取 293 条记录
2025-04-29 23:30:34,383 - INFO - 获取到 3 个日期需要处理: ['2025-04-27', '2025-04-28', '2025-04-29']
2025-04-29 23:30:34,383 - INFO - 开始处理日期: 2025-04-27
2025-04-29 23:30:34,383 - INFO - Request Parameters - Page 1:
2025-04-29 23:30:34,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 23:30:34,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745683200000, 1745769599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 23:30:42,531 - ERROR - 处理日期 2025-04-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F1E83AE3-C2AC-72AD-B4ED-D8928CB68906 Response: {'code': 'ServiceUnavailable', 'requestid': 'F1E83AE3-C2AC-72AD-B4ED-D8928CB68906', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F1E83AE3-C2AC-72AD-B4ED-D8928CB68906)
2025-04-29 23:30:42,531 - INFO - 开始处理日期: 2025-04-28
2025-04-29 23:30:42,531 - INFO - Request Parameters - Page 1:
2025-04-29 23:30:42,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 23:30:42,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745769600000, 1745855999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 23:30:50,648 - ERROR - 处理日期 2025-04-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 74E4A0AA-D189-7681-9654-98BB73488BBB Response: {'code': 'ServiceUnavailable', 'requestid': '74E4A0AA-D189-7681-9654-98BB73488BBB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 74E4A0AA-D189-7681-9654-98BB73488BBB)
2025-04-29 23:30:50,648 - INFO - 开始处理日期: 2025-04-29
2025-04-29 23:30:50,648 - INFO - Request Parameters - Page 1:
2025-04-29 23:30:50,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-29 23:30:50,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-29 23:30:51,415 - INFO - Response - Page 1:
2025-04-29 23:30:51,415 - INFO - 第 1 页获取到 78 条记录
2025-04-29 23:30:51,618 - INFO - 查询完成，共获取到 78 条记录
2025-04-29 23:30:51,618 - INFO - 获取到 78 条表单数据
2025-04-29 23:30:51,618 - INFO - 当前日期 2025-04-29 有 87 条MySQL数据需要处理
2025-04-29 23:30:51,618 - INFO - 开始批量插入 9 条新记录
2025-04-29 23:30:51,774 - INFO - 批量插入响应状态码: 200
2025-04-29 23:30:51,774 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 29 Apr 2025 15:30:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '435', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0BB80DF1-6505-7768-ABF8-20A431E5CE3E', 'x-acs-trace-id': 'b4765e67df73da2e7b48a3264fa059d9', 'etag': '4tTbQqsgLNsbWj8DrJu+KKQ5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-29 23:30:51,774 - INFO - 批量插入响应体: {'result': ['FINST-KLF66HD1260VPYYEFLZX34DIFTSM3UPFZN2AM2', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM3', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM4', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM5', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM6', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM7', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM8', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM9', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AMA']}
2025-04-29 23:30:51,774 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-04-29 23:30:51,774 - INFO - 成功插入的数据ID: ['FINST-KLF66HD1260VPYYEFLZX34DIFTSM3UPFZN2AM2', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM3', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM4', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM5', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM6', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM7', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM8', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AM9', 'FINST-KLF66HD1260VPYYEFLZX34DIFTSM3VPFZN2AMA']
2025-04-29 23:30:56,795 - INFO - 批量插入完成，共 9 条记录
2025-04-29 23:30:56,795 - INFO - 日期 2025-04-29 处理完成 - 更新: 0 条，插入: 9 条，错误: 0 条
2025-04-29 23:30:56,795 - INFO - 数据同步完成！更新: 0 条，插入: 9 条，错误: 2 条
2025-04-29 23:31:56,866 - INFO - 开始同步昨天与今天的销售数据: 20250428 至 20250429
2025-04-29 23:31:56,866 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-29 23:31:56,866 - INFO - 查询参数: ('20250428', '20250429')
2025-04-29 23:31:56,913 - INFO - MySQL查询成功，时间段: 20250428 至 20250429，共获取 0 条记录
2025-04-29 23:31:56,913 - ERROR - 未获取到MySQL数据
2025-04-29 23:31:56,913 - INFO - 同步完成
