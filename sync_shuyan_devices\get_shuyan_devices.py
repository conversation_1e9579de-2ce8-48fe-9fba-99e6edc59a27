import hashlib
import time
import requests
import json
import logging
from datetime import datetime, timedelta
import argparse
import os
import pymysql
from pymysql.cursors import DictCursor
# import schedule

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 43306,
    'user': 'root',
    'password': 'Hxp@1987!@#',
    'database': 'mydatabase',
    'charset': 'utf8mb4'
}

# 配置日志记录
current_date = datetime.now().strftime('%Y%m%d')
logging.basicConfig(
    filename=f'sync_shuyan_devices_{current_date}.txt',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'  # 添加 UTF-8 编码设置
)

# 字段值映射配置
DEFAULT_FIELD_MAPPINGS = {
    'isDel': {
        'Y': '已删除',
        'N': '未删除'
    },
    'deviceState': {
        '0': '在用',
        '1': '停用', 
        '2': '损坏',
        '3': '在库',
        '4': '出库',
        '5': '试运行'
    },
    'deviceType': {
        '0': '转发器',
        '1': '软件桥接器',
        '2': '硬件采集器',
        '3': '智能收银台',
        '4': '智能地面',
        '5': '安卓桥接器',
        '6': '凭证分发器',
        '7': '发票助手',
        '8': '凭证分发器AP',
        '9': '凭证分发器LP',
        'B':'纸电打印机XP',
        'V':'虚拟设备',
        'A':'其他'
    }
}

def convert_field_value(value, field_name, field_mappings):
    """
    转换字段值
    :param value: 原始值
    :param field_name: 字段名称
    :param field_mappings: 字段映射字典
    :return: 转换后的值，如果映射不存在则返回原值
    """
    if field_name in field_mappings and value in field_mappings[field_name]:
        return field_mappings[field_name][value]
    return value

def generate_sign(params, api_secret):
    """
    生成签名，采用MD5算法
    :param params: 参数字典
    :param api_secret: API密钥
    :return: 签名字符串
    """
    # 删除空值的参数
    params = {k: v for k, v in params.items() if v is not None and v != ""}
    # 按ASCII码从小到大排序
    sorted_keys = sorted(params)
    # 拼接成stringA
    stringA = '&'.join([f"{k}={params[k]}" for k in sorted_keys])
    # 拼接API密钥
    stringSignTemp = f"{stringA}&key={api_secret}"
    # MD5运算并转换为大写
    sign = hashlib.md5(stringSignTemp.encode('utf-8')).hexdigest().upper()
    return sign

def call_sale_query_api(app_id, app_key, api_secret, method, lower_method, url, business_data):
    try:
        timestamp = time.strftime("%Y%m%d%H%M%S", time.localtime())
        
        # 公共参数
        public_params = {
            "appId": app_id,
            "appKey": app_key,
            "method": method,
            "lowerMethod": lower_method,  # 业务接口名称
            "timestamp": timestamp,
            "messageFormat": "Json",
            "v": "1.0",
            "signMethod": "MD5"
        }
        
        # 将业务数据封装到 "data" 参数中
        data_param = json.dumps(business_data, ensure_ascii=False)
        all_params = {**public_params, "data": data_param}
        
        # 生成签名
        all_params["sign"] = generate_sign(all_params, api_secret)
        
        # 记录请求信息
        logging.info(f"Request URL: {url}")
        logging.info(f"Request Params: {all_params}")

        response = requests.post(url, data=all_params)
        result = response.json()
        
        # 记录响应信息
        logging.info(f"Response: {result}")
        
        return result
        
    except Exception as e:
        logging.error(f"API调用失败: {str(e)}")
        return {"error": str(e)}

def extract_device_info(response_data, shop_name='', field_mappings=None):
    """
    从API响应中提取设备信息
    :param response_data: API响应数据
    :param shop_name: 项目名称
    :param field_mappings: 字段映射字典
    :return: 提取的设备信息列表
    """
    if field_mappings is None:
        field_mappings = DEFAULT_FIELD_MAPPINGS
    
    extracted_data = []
    
    if response_data.get('rescode') == 'OPEN_SUCCESS':
        data = response_data.get('data', {})
        info_list = data.get('info', [])
        
        for device in info_list:
            # 获取原始值
            raw_device_type = device.get('deviceType', '')
            raw_device_state = device.get('deviceState', '')
            raw_is_del = device.get('isDel', '')
            
            # 转换字段值
            device_type_desc = convert_field_value(raw_device_type, 'deviceType', field_mappings)
            device_state_desc = convert_field_value(raw_device_state, 'deviceState', field_mappings)
            is_del_desc = convert_field_value(raw_is_del, 'isDel', field_mappings)
            
            device_info = {
                'projectName': shop_name,  # 添加项目名称作为第一列
                'shopEntityId': device.get('shopEntityId', ''),
                'device': device.get('device', ''),  # 设备标识
                'deviceType': device_type_desc,  # 转换后的设备类型
                'deviceTypeCode': raw_device_type,  # 保留原始代码
                'deviceState': device_state_desc,  # 转换后的设备状态
                'deviceStateCode': raw_device_state,  # 保留原始代码
                'isDel': is_del_desc,  # 转换后的删除状态
                'isDelCode': raw_is_del,  # 保留原始代码
                'installDate': device.get('installDate', '')
            }
            extracted_data.append(device_info)
    
    return extracted_data





def get_db_connection():
    """
    获取数据库连接
    :return: 数据库连接对象
    """
    try:
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database'],
            charset=DB_CONFIG['charset'],
            cursorclass=DictCursor
        )
        return connection
    except Exception as e:
        logging.error(f"数据库连接失败: {str(e)}")
        print(f"数据库连接失败: {str(e)}")
        return None

def create_device_table():
    """
    创建设备信息表
    :return: 是否创建成功
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS device_info (
        id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
        project_name VARCHAR(100) NOT NULL COMMENT '项目名称',
        shop_entity_id VARCHAR(50) NOT NULL COMMENT '店铺实体ID',
        device_id VARCHAR(100) NOT NULL COMMENT '设备标识',
        device_type VARCHAR(50) NOT NULL COMMENT '设备类型描述',
        device_type_code VARCHAR(10) NOT NULL COMMENT '设备类型代码',
        device_state VARCHAR(50) NOT NULL COMMENT '设备状态描述',
        device_state_code VARCHAR(10) NOT NULL COMMENT '设备状态代码',
        is_deleted VARCHAR(20) NOT NULL COMMENT '删除状态描述',
        is_deleted_code VARCHAR(10) NOT NULL COMMENT '删除状态代码',
        install_date VARCHAR(50) COMMENT '安装日期',
        sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '数据同步时间',
        INDEX idx_project_name (project_name),
        INDEX idx_shop_entity_id (shop_entity_id),
        INDEX idx_device_id (device_id),
        INDEX idx_sync_time (sync_time),
        UNIQUE KEY uk_device_shop (device_id, shop_entity_id) COMMENT '设备ID和店铺ID唯一约束'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备信息表'
    """
    
    try:
        connection = get_db_connection()
        if connection is None:
            return False
        
        with connection.cursor() as cursor:
            cursor.execute(create_table_sql)
            connection.commit()
            print("设备信息表创建成功")
            logging.info("设备信息表创建成功")
            return True
    except Exception as e:
        logging.error(f"创建表失败: {str(e)}")
        print(f"创建表失败: {str(e)}")
        return False
    finally:
        if connection:
            connection.close()

def insert_device_data_to_db(device_list):
    """
    将设备数据插入到数据库
    :param device_list: 设备信息列表
    :return: 插入的记录数
    """
    if not device_list:
        print("没有设备数据需要插入")
        return 0
    
    insert_sql = """
    INSERT INTO device_info (
        project_name, shop_entity_id, device_id, device_type, device_type_code,
        device_state, device_state_code, is_deleted, is_deleted_code, install_date
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    ON DUPLICATE KEY UPDATE
        device_type = VALUES(device_type),
        device_type_code = VALUES(device_type_code),
        device_state = VALUES(device_state),
        device_state_code = VALUES(device_state_code),
        is_deleted = VALUES(is_deleted),
        is_deleted_code = VALUES(is_deleted_code),
        install_date = VALUES(install_date),
        sync_time = CURRENT_TIMESTAMP
    """
    
    try:
        connection = get_db_connection()
        if connection is None:
            return 0
        
        inserted_count = 0
        with connection.cursor() as cursor:
            for device in device_list:
                try:
                    cursor.execute(insert_sql, (
                        device['projectName'],
                        device['shopEntityId'],
                        device['device'],
                        device['deviceType'],
                        device['deviceTypeCode'],
                        device['deviceState'],
                        device['deviceStateCode'],
                        device['isDel'],
                        device['isDelCode'],
                        device['installDate']
                    ))
                    inserted_count += 1
                except Exception as e:
                    logging.error(f"插入设备数据失败: {str(e)}, 设备: {device}")
                    print(f"插入设备数据失败: {str(e)}, 设备: {device}")
            
            connection.commit()
        
        print(f"成功插入/更新 {inserted_count} 条设备记录到数据库")
        logging.info(f"成功插入/更新 {inserted_count} 条设备记录到数据库")
        return inserted_count
        
    except Exception as e:
        logging.error(f"数据库操作失败: {str(e)}")
        print(f"数据库操作失败: {str(e)}")
        return 0
    finally:
        if connection:
            connection.close()

if __name__ == "__main__":
    # 加载字段映射配置
    field_mappings = DEFAULT_FIELD_MAPPINGS
    
    appId = "a5274b7e5d9a41939346c33c2c3443db"
    appKey = "2c9a5a628e7dab16018f5b055f3d0002"
    apiSecret = "07F77244AD915AC2BB3EECE8EF7AE4DB"
    method = "gogo.open.auto.routing"
    lowerMethod = "gag.device.info.query"
    url = "http://api.gooagoo.com/oapi/rest"
    shop_mapping = {
        "1ETDLFB9DIMQME7Q2OVD93ISAI00189O": "广州维多利广场",
        "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE": "武汉国金天地",
        "1HFLOR99TBR11L6UBHOUTGCK1C001A3F": "广州悦汇城",
        "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV": "悦汇广场·南海",
        "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU": "广州国金天地",
        "1HRIS7255PESAA7AV8LHQQGIH8001KNH": "广州环贸天地",
        "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D": "武汉星汇维港购物中心"
    }
    
    # 测试数据库连接
    print("正在测试数据库连接...")
    if get_db_connection() is None:
        print("数据库连接失败，程序退出")
        exit(1)
    else:
        print("数据库连接成功")
    
    # 收集所有店铺的设备数据
    total_shops = len(shop_mapping)
    current_shop = 0
    total_devices = 0
    
    print(f"开始获取 {total_shops} 个店铺的设备信息并保存到数据库...")
    print("使用字段映射配置进行数据转换")
    
    for shopId, shopName in shop_mapping.items():
        current_shop += 1
        print(f"\n[{current_shop}/{total_shops}] 正在获取{shopName}的设备信息...")
        
        business_data = {
            "shopId": shopId,
            "searchType": "1"
        }
        
        response_data = call_sale_query_api(appId, appKey, apiSecret, method, lowerMethod, url, business_data)
        
        if response_data.get('rescode') == 'OPEN_SUCCESS':
            device_list = extract_device_info(response_data, shopName, field_mappings)
            if device_list:
                print(f"成功获取到 {len(device_list)} 条设备信息")
                # 立即保存当前店铺的数据到数据库
                inserted_count = insert_device_data_to_db(device_list)
                total_devices += inserted_count
            else:
                print("未找到设备信息数据")
        else:
            print(f"API调用失败: {response_data.get('resmsg', '未知错误')}")
        
        # 添加延时避免API请求过于频繁
        if current_shop < total_shops:
            print("等待2秒后继续下一个店铺...")
            time.sleep(2)
    
    # 输出最终统计信息
    print(f"\n所有店铺数据获取完成，共保存 {total_devices} 条设备记录到数据库")
    print("数据同步完成！")

