2025-06-01 00:30:33,560 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 00:30:33,560 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 00:30:33,560 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 00:30:33,641 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 0 条记录
2025-06-01 00:30:33,641 - ERROR - 未获取到MySQL数据
2025-06-01 00:31:33,641 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 00:31:33,641 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 00:31:33,641 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 00:31:33,710 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 121 条记录
2025-06-01 00:31:33,710 - INFO - 获取到 1 个日期需要处理: ['2025-05-31']
2025-06-01 00:31:33,711 - INFO - 开始处理日期: 2025-05-31
2025-06-01 00:31:33,716 - INFO - Request Parameters - Page 1:
2025-06-01 00:31:33,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:31:33,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:31:41,828 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E8FAB3D1-D818-7A34-A9D8-3B458D5D26FF Response: {'code': 'ServiceUnavailable', 'requestid': 'E8FAB3D1-D818-7A34-A9D8-3B458D5D26FF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E8FAB3D1-D818-7A34-A9D8-3B458D5D26FF)
2025-06-01 00:31:41,828 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-01 00:31:41,828 - INFO - 同步完成
2025-06-01 01:30:33,570 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 01:30:33,570 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 01:30:33,570 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 01:30:33,648 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 0 条记录
2025-06-01 01:30:33,648 - ERROR - 未获取到MySQL数据
2025-06-01 01:31:33,664 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 01:31:33,664 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 01:31:33,664 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 01:31:33,726 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 121 条记录
2025-06-01 01:31:33,726 - INFO - 获取到 1 个日期需要处理: ['2025-05-31']
2025-06-01 01:31:33,726 - INFO - 开始处理日期: 2025-05-31
2025-06-01 01:31:33,726 - INFO - Request Parameters - Page 1:
2025-06-01 01:31:33,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 01:31:33,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 01:31:41,851 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1463B3F5-2D79-7E13-9165-6B74306ADDA4 Response: {'code': 'ServiceUnavailable', 'requestid': '1463B3F5-2D79-7E13-9165-6B74306ADDA4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1463B3F5-2D79-7E13-9165-6B74306ADDA4)
2025-06-01 01:31:41,851 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-01 01:31:41,851 - INFO - 同步完成
2025-06-01 02:30:33,578 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 02:30:33,578 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 02:30:33,578 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 02:30:33,640 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 0 条记录
2025-06-01 02:30:33,640 - ERROR - 未获取到MySQL数据
2025-06-01 02:31:33,656 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 02:31:33,656 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 02:31:33,656 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 02:31:33,718 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 121 条记录
2025-06-01 02:31:33,718 - INFO - 获取到 1 个日期需要处理: ['2025-05-31']
2025-06-01 02:31:33,718 - INFO - 开始处理日期: 2025-05-31
2025-06-01 02:31:33,718 - INFO - Request Parameters - Page 1:
2025-06-01 02:31:33,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 02:31:33,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 02:31:41,827 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B89949F4-51F0-7885-98AE-120479648DB4 Response: {'code': 'ServiceUnavailable', 'requestid': 'B89949F4-51F0-7885-98AE-120479648DB4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B89949F4-51F0-7885-98AE-120479648DB4)
2025-06-01 02:31:41,827 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-01 02:31:41,827 - INFO - 同步完成
2025-06-01 03:30:33,569 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 03:30:33,569 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 03:30:33,569 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 03:30:33,648 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 0 条记录
2025-06-01 03:30:33,648 - ERROR - 未获取到MySQL数据
2025-06-01 03:31:33,664 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 03:31:33,664 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 03:31:33,664 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 03:31:33,727 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 121 条记录
2025-06-01 03:31:33,727 - INFO - 获取到 1 个日期需要处理: ['2025-05-31']
2025-06-01 03:31:33,727 - INFO - 开始处理日期: 2025-05-31
2025-06-01 03:31:33,727 - INFO - Request Parameters - Page 1:
2025-06-01 03:31:33,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:31:33,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:31:41,836 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 32B76CBC-BCF1-7494-B61A-71C78ABEE846 Response: {'code': 'ServiceUnavailable', 'requestid': '32B76CBC-BCF1-7494-B61A-71C78ABEE846', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 32B76CBC-BCF1-7494-B61A-71C78ABEE846)
2025-06-01 03:31:41,836 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-01 03:31:41,836 - INFO - 同步完成
2025-06-01 04:30:33,514 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 04:30:33,514 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 04:30:33,514 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 04:30:33,577 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 0 条记录
2025-06-01 04:30:33,577 - ERROR - 未获取到MySQL数据
2025-06-01 04:31:33,590 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 04:31:33,590 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 04:31:33,590 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 04:31:33,653 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 121 条记录
2025-06-01 04:31:33,653 - INFO - 获取到 1 个日期需要处理: ['2025-05-31']
2025-06-01 04:31:33,653 - INFO - 开始处理日期: 2025-05-31
2025-06-01 04:31:33,653 - INFO - Request Parameters - Page 1:
2025-06-01 04:31:33,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 04:31:33,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 04:31:41,762 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D0B4AB58-B5B5-7B3A-8B71-AB3DF688B6CE Response: {'code': 'ServiceUnavailable', 'requestid': 'D0B4AB58-B5B5-7B3A-8B71-AB3DF688B6CE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D0B4AB58-B5B5-7B3A-8B71-AB3DF688B6CE)
2025-06-01 04:31:41,762 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-01 04:31:41,762 - INFO - 同步完成
2025-06-01 05:30:33,563 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 05:30:33,563 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 05:30:33,563 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 05:30:33,626 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 0 条记录
2025-06-01 05:30:33,626 - ERROR - 未获取到MySQL数据
2025-06-01 05:31:33,641 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 05:31:33,641 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 05:31:33,641 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 05:31:33,703 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 121 条记录
2025-06-01 05:31:33,703 - INFO - 获取到 1 个日期需要处理: ['2025-05-31']
2025-06-01 05:31:33,703 - INFO - 开始处理日期: 2025-05-31
2025-06-01 05:31:33,703 - INFO - Request Parameters - Page 1:
2025-06-01 05:31:33,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 05:31:33,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 05:31:41,844 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ED42496F-D2FC-776D-AA6E-EE139A860329 Response: {'code': 'ServiceUnavailable', 'requestid': 'ED42496F-D2FC-776D-AA6E-EE139A860329', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ED42496F-D2FC-776D-AA6E-EE139A860329)
2025-06-01 05:31:41,844 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-01 05:31:41,844 - INFO - 同步完成
2025-06-01 06:30:33,601 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 06:30:33,601 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 06:30:33,601 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 06:30:33,679 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 0 条记录
2025-06-01 06:30:33,679 - ERROR - 未获取到MySQL数据
2025-06-01 06:31:33,695 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 06:31:33,695 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 06:31:33,695 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 06:31:33,757 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 121 条记录
2025-06-01 06:31:33,757 - INFO - 获取到 1 个日期需要处理: ['2025-05-31']
2025-06-01 06:31:33,757 - INFO - 开始处理日期: 2025-05-31
2025-06-01 06:31:33,757 - INFO - Request Parameters - Page 1:
2025-06-01 06:31:33,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:31:33,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:31:41,866 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8608C3FC-A47E-76CE-9EA2-BE61E5AE92F3 Response: {'code': 'ServiceUnavailable', 'requestid': '8608C3FC-A47E-76CE-9EA2-BE61E5AE92F3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8608C3FC-A47E-76CE-9EA2-BE61E5AE92F3)
2025-06-01 06:31:41,866 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-01 06:31:41,866 - INFO - 同步完成
2025-06-01 07:30:33,577 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 07:30:33,577 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 07:30:33,577 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 07:30:33,640 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 6 条记录
2025-06-01 07:30:33,640 - INFO - 获取到 1 个日期需要处理: ['2025-05-31']
2025-06-01 07:30:33,640 - INFO - 开始处理日期: 2025-05-31
2025-06-01 07:30:33,640 - INFO - Request Parameters - Page 1:
2025-06-01 07:30:33,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 07:30:33,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 07:30:41,749 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 74548E2A-CA18-725F-A129-97AE56E3717F Response: {'code': 'ServiceUnavailable', 'requestid': '74548E2A-CA18-725F-A129-97AE56E3717F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 74548E2A-CA18-725F-A129-97AE56E3717F)
2025-06-01 07:30:41,749 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-01 07:31:41,764 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 07:31:41,764 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 07:31:41,764 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 07:31:41,827 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 139 条记录
2025-06-01 07:31:41,827 - INFO - 获取到 1 个日期需要处理: ['2025-05-31']
2025-06-01 07:31:41,827 - INFO - 开始处理日期: 2025-05-31
2025-06-01 07:31:41,827 - INFO - Request Parameters - Page 1:
2025-06-01 07:31:41,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 07:31:41,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 07:31:49,936 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9100C84B-AE98-7C80-B9F3-82575C3B8243 Response: {'code': 'ServiceUnavailable', 'requestid': '9100C84B-AE98-7C80-B9F3-82575C3B8243', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9100C84B-AE98-7C80-B9F3-82575C3B8243)
2025-06-01 07:31:49,936 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-01 07:31:49,952 - INFO - 同步完成
2025-06-01 08:30:33,569 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 08:30:33,569 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 08:30:33,569 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 08:30:33,632 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 6 条记录
2025-06-01 08:30:33,632 - INFO - 获取到 1 个日期需要处理: ['2025-05-31']
2025-06-01 08:30:33,632 - INFO - 开始处理日期: 2025-05-31
2025-06-01 08:30:33,632 - INFO - Request Parameters - Page 1:
2025-06-01 08:30:33,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:30:33,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:30:41,757 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 49B2E385-3575-7B1A-95C3-EBFEE20BCDB3 Response: {'code': 'ServiceUnavailable', 'requestid': '49B2E385-3575-7B1A-95C3-EBFEE20BCDB3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 49B2E385-3575-7B1A-95C3-EBFEE20BCDB3)
2025-06-01 08:30:41,757 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-01 08:31:41,772 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 08:31:41,772 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 08:31:41,772 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 08:31:41,834 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 139 条记录
2025-06-01 08:31:41,834 - INFO - 获取到 1 个日期需要处理: ['2025-05-31']
2025-06-01 08:31:41,834 - INFO - 开始处理日期: 2025-05-31
2025-06-01 08:31:41,834 - INFO - Request Parameters - Page 1:
2025-06-01 08:31:41,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:31:41,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:31:49,944 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F64D81EB-625C-77FD-A30C-93D8A962D2B2 Response: {'code': 'ServiceUnavailable', 'requestid': 'F64D81EB-625C-77FD-A30C-93D8A962D2B2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F64D81EB-625C-77FD-A30C-93D8A962D2B2)
2025-06-01 08:31:49,944 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-01 08:31:49,944 - INFO - 同步完成
2025-06-01 09:30:33,592 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 09:30:33,592 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 09:30:33,592 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 09:30:33,670 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 82 条记录
2025-06-01 09:30:33,670 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 09:30:33,670 - INFO - 开始处理日期: 2025-05-31
2025-06-01 09:30:33,670 - INFO - Request Parameters - Page 1:
2025-06-01 09:30:33,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:30:33,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:30:41,795 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AA8053B2-4204-7657-9DE9-57EE49E19BF5 Response: {'code': 'ServiceUnavailable', 'requestid': 'AA8053B2-4204-7657-9DE9-57EE49E19BF5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AA8053B2-4204-7657-9DE9-57EE49E19BF5)
2025-06-01 09:30:41,795 - INFO - 开始处理日期: 2025-06-01
2025-06-01 09:30:41,795 - INFO - Request Parameters - Page 1:
2025-06-01 09:30:41,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:30:41,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:30:41,951 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 03DF5CBA-B40D-732A-B8EC-7A7EAC22D9AC Response: {'requestid': '03DF5CBA-B40D-732A-B8EC-7A7EAC22D9AC', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 03DF5CBA-B40D-732A-B8EC-7A7EAC22D9AC)
2025-06-01 09:30:41,951 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-01 09:31:41,967 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 09:31:41,967 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 09:31:41,967 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 09:31:42,029 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 354 条记录
2025-06-01 09:31:42,029 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 09:31:42,045 - INFO - 开始处理日期: 2025-05-31
2025-06-01 09:31:42,045 - INFO - Request Parameters - Page 1:
2025-06-01 09:31:42,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:31:42,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:31:50,170 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A369BF96-5F3C-7F04-AA3D-137E174DCD87 Response: {'code': 'ServiceUnavailable', 'requestid': 'A369BF96-5F3C-7F04-AA3D-137E174DCD87', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A369BF96-5F3C-7F04-AA3D-137E174DCD87)
2025-06-01 09:31:50,170 - INFO - 开始处理日期: 2025-06-01
2025-06-01 09:31:50,170 - INFO - Request Parameters - Page 1:
2025-06-01 09:31:50,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:31:50,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:31:50,326 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: FE0FD458-67FB-7BF1-BC0B-990A300522E3 Response: {'requestid': 'FE0FD458-67FB-7BF1-BC0B-990A300522E3', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: FE0FD458-67FB-7BF1-BC0B-990A300522E3)
2025-06-01 09:31:50,326 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-01 09:31:50,326 - INFO - 同步完成
2025-06-01 10:30:33,646 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 10:30:33,646 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 10:30:33,646 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 10:30:33,724 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 157 条记录
2025-06-01 10:30:33,724 - INFO - 获取到 4 个日期需要处理: ['2025-05-12', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 10:30:33,724 - INFO - 开始处理日期: 2025-05-12
2025-06-01 10:30:33,724 - INFO - Request Parameters - Page 1:
2025-06-01 10:30:33,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 10:30:33,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 10:30:41,849 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EC56CE48-BCCE-7495-81E5-8362B8E7D4D3 Response: {'code': 'ServiceUnavailable', 'requestid': 'EC56CE48-BCCE-7495-81E5-8362B8E7D4D3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EC56CE48-BCCE-7495-81E5-8362B8E7D4D3)
2025-06-01 10:30:41,849 - INFO - 开始处理日期: 2025-05-30
2025-06-01 10:30:41,849 - INFO - Request Parameters - Page 1:
2025-06-01 10:30:41,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 10:30:41,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 10:30:42,021 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: BA8A1281-A74A-7C41-BA20-C76E6ECB4ECD Response: {'requestid': 'BA8A1281-A74A-7C41-BA20-C76E6ECB4ECD', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: BA8A1281-A74A-7C41-BA20-C76E6ECB4ECD)
2025-06-01 10:30:42,021 - INFO - 开始处理日期: 2025-05-31
2025-06-01 10:30:42,021 - INFO - Request Parameters - Page 1:
2025-06-01 10:30:42,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 10:30:42,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 10:30:42,162 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: CDE0B376-19F9-74C4-AB3E-8EC80B62B63B Response: {'requestid': 'CDE0B376-19F9-74C4-AB3E-8EC80B62B63B', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: CDE0B376-19F9-74C4-AB3E-8EC80B62B63B)
2025-06-01 10:30:42,162 - INFO - 开始处理日期: 2025-06-01
2025-06-01 10:30:42,162 - INFO - Request Parameters - Page 1:
2025-06-01 10:30:42,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 10:30:42,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 10:30:42,318 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 4745D739-3E56-7250-B4F1-51B93D40A8E2 Response: {'requestid': '4745D739-3E56-7250-B4F1-51B93D40A8E2', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 4745D739-3E56-7250-B4F1-51B93D40A8E2)
2025-06-01 10:30:42,318 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 4 条
2025-06-01 10:31:42,333 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 10:31:42,333 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 10:31:42,333 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 10:31:42,411 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 424 条记录
2025-06-01 10:31:42,411 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 10:31:42,411 - INFO - 开始处理日期: 2025-05-31
2025-06-01 10:31:42,411 - INFO - Request Parameters - Page 1:
2025-06-01 10:31:42,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 10:31:42,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 10:31:50,536 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1EB8978C-D1A7-7FEC-A815-649E4C9C934D Response: {'code': 'ServiceUnavailable', 'requestid': '1EB8978C-D1A7-7FEC-A815-649E4C9C934D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1EB8978C-D1A7-7FEC-A815-649E4C9C934D)
2025-06-01 10:31:50,536 - INFO - 开始处理日期: 2025-06-01
2025-06-01 10:31:50,536 - INFO - Request Parameters - Page 1:
2025-06-01 10:31:50,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 10:31:50,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 10:31:50,693 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 1D6432D5-676C-7A56-AEEE-9EE5F1EA0AC8 Response: {'requestid': '1D6432D5-676C-7A56-AEEE-9EE5F1EA0AC8', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 1D6432D5-676C-7A56-AEEE-9EE5F1EA0AC8)
2025-06-01 10:31:50,693 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-01 10:31:50,693 - INFO - 同步完成
2025-06-01 11:30:33,607 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 11:30:33,607 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 11:30:33,607 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 11:30:33,685 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 192 条记录
2025-06-01 11:30:33,685 - INFO - 获取到 4 个日期需要处理: ['2025-05-12', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 11:30:33,700 - INFO - 开始处理日期: 2025-05-12
2025-06-01 11:30:33,700 - INFO - Request Parameters - Page 1:
2025-06-01 11:30:33,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 11:30:33,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 11:30:41,810 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5239BF69-F6A7-7CC3-A069-333777403CD2 Response: {'code': 'ServiceUnavailable', 'requestid': '5239BF69-F6A7-7CC3-A069-333777403CD2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5239BF69-F6A7-7CC3-A069-333777403CD2)
2025-06-01 11:30:41,810 - INFO - 开始处理日期: 2025-05-30
2025-06-01 11:30:41,810 - INFO - Request Parameters - Page 1:
2025-06-01 11:30:41,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 11:30:41,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 11:30:41,966 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: D0C4FD11-2769-7E2C-AF72-27B88AD21C75 Response: {'requestid': 'D0C4FD11-2769-7E2C-AF72-27B88AD21C75', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: D0C4FD11-2769-7E2C-AF72-27B88AD21C75)
2025-06-01 11:30:41,966 - INFO - 开始处理日期: 2025-05-31
2025-06-01 11:30:41,966 - INFO - Request Parameters - Page 1:
2025-06-01 11:30:41,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 11:30:41,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 11:30:42,122 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 45BA817C-1B8D-71FE-B5DE-A06878D08F21 Response: {'requestid': '45BA817C-1B8D-71FE-B5DE-A06878D08F21', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 45BA817C-1B8D-71FE-B5DE-A06878D08F21)
2025-06-01 11:30:42,122 - INFO - 开始处理日期: 2025-06-01
2025-06-01 11:30:42,122 - INFO - Request Parameters - Page 1:
2025-06-01 11:30:42,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 11:30:42,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 11:30:42,263 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 45A7D51A-5924-7E77-B89D-6B0BC1FF8D60 Response: {'requestid': '45A7D51A-5924-7E77-B89D-6B0BC1FF8D60', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 45A7D51A-5924-7E77-B89D-6B0BC1FF8D60)
2025-06-01 11:30:42,263 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 4 条
2025-06-01 11:31:42,278 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 11:31:42,278 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 11:31:42,278 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 11:31:42,356 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 515 条记录
2025-06-01 11:31:42,356 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 11:31:42,356 - INFO - 开始处理日期: 2025-05-31
2025-06-01 11:31:42,356 - INFO - Request Parameters - Page 1:
2025-06-01 11:31:42,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 11:31:42,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 11:31:50,466 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 61CA9550-2C45-737F-9C7F-D7614B002AA8 Response: {'code': 'ServiceUnavailable', 'requestid': '61CA9550-2C45-737F-9C7F-D7614B002AA8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 61CA9550-2C45-737F-9C7F-D7614B002AA8)
2025-06-01 11:31:50,466 - INFO - 开始处理日期: 2025-06-01
2025-06-01 11:31:50,466 - INFO - Request Parameters - Page 1:
2025-06-01 11:31:50,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 11:31:50,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 11:31:50,622 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 5465DC9E-A1E8-7E86-9334-FD06D4651AE4 Response: {'requestid': '5465DC9E-A1E8-7E86-9334-FD06D4651AE4', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 5465DC9E-A1E8-7E86-9334-FD06D4651AE4)
2025-06-01 11:31:50,622 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-01 11:31:50,622 - INFO - 同步完成
2025-06-01 12:30:33,644 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 12:30:33,645 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 12:30:33,645 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 12:30:33,720 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 197 条记录
2025-06-01 12:30:33,720 - INFO - 获取到 4 个日期需要处理: ['2025-05-12', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 12:30:33,722 - INFO - 开始处理日期: 2025-05-12
2025-06-01 12:30:33,725 - INFO - Request Parameters - Page 1:
2025-06-01 12:30:33,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:30:33,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:30:41,842 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1E7A7660-D361-7D5B-9B60-B6A8D80CC709 Response: {'code': 'ServiceUnavailable', 'requestid': '1E7A7660-D361-7D5B-9B60-B6A8D80CC709', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1E7A7660-D361-7D5B-9B60-B6A8D80CC709)
2025-06-01 12:30:41,842 - INFO - 开始处理日期: 2025-05-30
2025-06-01 12:30:41,842 - INFO - Request Parameters - Page 1:
2025-06-01 12:30:41,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:30:41,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:30:42,009 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: F2450E7A-1A91-7B3B-9350-9B7D79FB0695 Response: {'requestid': 'F2450E7A-1A91-7B3B-9350-9B7D79FB0695', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: F2450E7A-1A91-7B3B-9350-9B7D79FB0695)
2025-06-01 12:30:42,009 - INFO - 开始处理日期: 2025-05-31
2025-06-01 12:30:42,009 - INFO - Request Parameters - Page 1:
2025-06-01 12:30:42,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:30:42,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:30:44,729 - INFO - Response - Page 1:
2025-06-01 12:30:44,730 - INFO - 第 1 页获取到 100 条记录
2025-06-01 12:30:44,930 - INFO - Request Parameters - Page 2:
2025-06-01 12:30:44,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:30:44,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:30:53,046 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 95396C6A-2548-7608-940B-CEFAD3D5D2E7 Response: {'code': 'ServiceUnavailable', 'requestid': '95396C6A-2548-7608-940B-CEFAD3D5D2E7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 95396C6A-2548-7608-940B-CEFAD3D5D2E7)
2025-06-01 12:30:53,047 - INFO - 开始处理日期: 2025-06-01
2025-06-01 12:30:53,047 - INFO - Request Parameters - Page 1:
2025-06-01 12:30:53,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:30:53,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:30:53,202 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 5B7276CE-7EAF-79EF-8E2D-7DF808A015DA Response: {'requestid': '5B7276CE-7EAF-79EF-8E2D-7DF808A015DA', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 5B7276CE-7EAF-79EF-8E2D-7DF808A015DA)
2025-06-01 12:30:53,202 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 4 条
2025-06-01 12:31:53,202 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 12:31:53,202 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 12:31:53,202 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 12:31:53,291 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 522 条记录
2025-06-01 12:31:53,291 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 12:31:53,295 - INFO - 开始处理日期: 2025-05-31
2025-06-01 12:31:53,296 - INFO - Request Parameters - Page 1:
2025-06-01 12:31:53,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:31:53,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:31:54,403 - INFO - Response - Page 1:
2025-06-01 12:31:54,404 - INFO - 第 1 页获取到 100 条记录
2025-06-01 12:31:54,605 - INFO - Request Parameters - Page 2:
2025-06-01 12:31:54,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:31:54,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:31:55,292 - INFO - Response - Page 2:
2025-06-01 12:31:55,293 - INFO - 第 2 页获取到 21 条记录
2025-06-01 12:31:55,493 - INFO - 查询完成，共获取到 121 条记录
2025-06-01 12:31:55,493 - INFO - 获取到 121 条表单数据
2025-06-01 12:31:55,496 - INFO - 当前日期 2025-05-31 有 519 条MySQL数据需要处理
2025-06-01 12:31:55,499 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM42
2025-06-01 12:31:55,980 - INFO - 更新表单数据成功: FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM42
2025-06-01 12:31:55,980 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 21859.96}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 21859.96}, {'field': 'order_count', 'old_value': 0, 'new_value': 243}]
2025-06-01 12:31:55,981 - INFO - 开始更新记录 - 表单实例ID: FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMP8
2025-06-01 12:31:56,398 - INFO - 更新表单数据成功: FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMP8
2025-06-01 12:31:56,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32266.3, 'new_value': 32636.2}, {'field': 'total_amount', 'old_value': 32266.3, 'new_value': 32636.2}, {'field': 'order_count', 'old_value': 123, 'new_value': 508}]
2025-06-01 12:31:56,400 - INFO - 开始批量插入 398 条新记录
2025-06-01 12:31:56,720 - INFO - 批量插入响应状态码: 200
2025-06-01 12:31:56,720 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 04:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '758ED2B5-561B-7031-9890-6701B1BBA73A', 'x-acs-trace-id': 'cfeb2d8a757528c763349c8a5ef32931', 'etag': '4yM9qrLJHKyFYklCwGyByXA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 12:31:56,721 - INFO - 批量插入响应体: {'result': ['FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMT8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMU8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMV8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMW8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMX8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMY8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMZ8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM09', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM19', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM29', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM39', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM49', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM59', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM69', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM79', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM89', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM99', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMA9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMB9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMC9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMD9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBME9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMF9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMG9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMH9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMI9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMJ9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMK9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBML9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMM9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMN9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMO9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMP9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMQ9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMR9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMS9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMT9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMU9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMV9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMW9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMX9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMY9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMZ9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM0A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM1A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM2A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM3A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM4A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM5A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM6A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM7A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM8A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM9A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMAA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMBA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMCA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMDA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMEA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMFA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMGA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMHA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMIA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMJA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMKA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMLA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMMA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMNA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMOA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMPA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMQA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMRA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMSA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMTA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMUA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMVA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMWA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMXA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMYA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMZA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM0B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM1B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM2B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM3B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM4B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM5B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM6B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM7B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM8B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM9B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMAB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMBB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMCB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMDB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMEB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMFB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMGB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMHB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMIB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMJB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMKB']}
2025-06-01 12:31:56,721 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-06-01 12:31:56,721 - INFO - 成功插入的数据ID: ['FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMT8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMU8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMV8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMW8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMX8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMY8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMZ8', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM09', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM19', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM29', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM39', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM49', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM59', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM69', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM79', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM89', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM99', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMA9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMB9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMC9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMD9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBME9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMF9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMG9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMH9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMI9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMJ9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMK9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBML9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMM9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMN9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMO9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMP9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMQ9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMR9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMS9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMT9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMU9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMV9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMW9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMX9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMY9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMZ9', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM0A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM1A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM2A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM3A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM4A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM5A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM6A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM7A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM8A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM9A', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMAA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMBA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMCA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMDA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMEA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMFA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMGA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMHA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMIA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMJA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMKA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMLA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMMA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMNA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMOA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMPA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMQA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMRA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMSA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMTA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMUA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMVA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMWA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMXA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMYA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMZA', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM0B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM1B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM2B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM3B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM4B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM5B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM6B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM7B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM8B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBM9B', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMAB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMBB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMCB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMDB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMEB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMFB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMGB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMHB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMIB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMJB', 'FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMKB']
2025-06-01 12:32:02,030 - INFO - 批量插入响应状态码: 200
2025-06-01 12:32:02,030 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 04:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D95F586B-AD1B-7469-A2DB-278A9A2F33D5', 'x-acs-trace-id': 'a25b5f0e9e37e79e032d6c3cc04a3fcf', 'etag': '4REa//7cyxOvJS5u8r+VGUg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 12:32:02,030 - INFO - 批量插入响应体: {'result': ['FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMH7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMI7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMJ7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMK7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBML7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMM7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMN7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMO7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMP7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMQ7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMR7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMS7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMT7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMU7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMV7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMW7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMX7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMY7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMZ7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM08', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM18', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM28', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM38', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM48', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM58', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM68', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM78', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM88', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM98', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMA8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMB8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMC8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMD8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBME8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMF8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMG8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMH8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMI8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMJ8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMK8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBML8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMM8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMN8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMO8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMP8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMQ8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMR8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMS8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMT8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMU8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMV8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMW8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMX8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMY8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMZ8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM09', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM19', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM29', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM39', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM49', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM59', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM69', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM79', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM89', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM99', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMA9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMB9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMC9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMD9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBME9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMF9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMG9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMH9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMI9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMJ9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMK9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBML9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMM9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMN9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMO9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMP9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMQ9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMR9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMS9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMT9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMU9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMV9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMW9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMX9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMY9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMZ9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM0A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM1A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM2A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM3A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM4A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM5A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM6A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM7A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM8A']}
2025-06-01 12:32:02,030 - INFO - 批量插入表单数据成功，批次 2，共 100 条记录
2025-06-01 12:32:02,031 - INFO - 成功插入的数据ID: ['FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMH7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMI7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMJ7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMK7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBML7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMM7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMN7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMO7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMP7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMQ7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMR7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMS7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMT7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMU7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMV7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMW7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMX7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMY7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMZ7', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM08', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM18', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM28', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM38', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM48', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM58', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM68', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM78', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM88', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM98', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMA8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMB8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMC8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMD8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBME8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMF8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMG8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMH8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMI8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMJ8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMK8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBML8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMM8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMN8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMO8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMP8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMQ8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMR8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMS8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMT8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMU8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMV8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMW8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMX8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMY8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMZ8', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM09', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM19', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM29', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM39', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM49', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM59', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM69', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM79', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM89', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBM99', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMA9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMB9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMC9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBMD9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33G98Z5DBME9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMF9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMG9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMH9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMI9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMJ9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMK9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBML9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMM9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMN9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMO9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMP9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMQ9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMR9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMS9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMT9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMU9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMV9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMW9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMX9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMY9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBMZ9', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM0A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM1A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM2A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM3A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM4A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM5A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM6A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM7A', 'FINST-K7666JC11NVVRN01A5KDACTZ3GH33H98Z5DBM8A']
2025-06-01 12:32:07,317 - INFO - 批量插入响应状态码: 200
2025-06-01 12:32:07,317 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 04:32:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8B8614CF-14FD-7D99-B47D-993CFD94AD2C', 'x-acs-trace-id': 'f58e21570139032520abb08b51b3dec6', 'etag': '4w3znCOyLxYPJY/NQvEJASg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 12:32:07,317 - INFO - 批量插入响应体: {'result': ['FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMFK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMGK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMHK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMIK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMJK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMKK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMLK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMMK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMNK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMOK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMPK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMQK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMRK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMSK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMTK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMUK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMVK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMWK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMXK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMYK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMZK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM0L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM1L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM2L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM3L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM4L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM5L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM6L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM7L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM8L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM9L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMAL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMBL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMCL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMDL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMEL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMFL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMGL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMHL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMIL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMJL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMKL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMLL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMML', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMNL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMOL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMPL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMQL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMRL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMSL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMTL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMUL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMVL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMWL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMXL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMYL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMZL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM0M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM1M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM2M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM3M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM4M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM5M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM6M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM7M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM8M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM9M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMAM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMBM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMCM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMDM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMEM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMFM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMGM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMHM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMIM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMJM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMKM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMLM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMMM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMNM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMOM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMPM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMQM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMRM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMSM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMTM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMUM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMVM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMWM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMXM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMYM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMZM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM0N', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM1N', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM2N', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM3N', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM4N', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM5N', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM6N']}
2025-06-01 12:32:07,317 - INFO - 批量插入表单数据成功，批次 3，共 100 条记录
2025-06-01 12:32:07,317 - INFO - 成功插入的数据ID: ['FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMFK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMGK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMHK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMIK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMJK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMKK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMLK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMMK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMNK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMOK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMPK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMQK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMRK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMSK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMTK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMUK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMVK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMWK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMXK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMYK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMZK', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM0L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM1L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM2L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM3L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM4L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM5L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM6L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM7L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM8L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBM9L', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMAL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMBL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMCL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMDL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMEL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMFL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMGL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMHL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMIL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMJL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMKL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMLL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMML', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMNL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMOL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMPL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMQL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMRL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMSL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMTL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMUL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMVL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMWL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMXL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMYL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23GCCZ5DBMZL', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM0M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM1M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM2M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM3M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM4M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM5M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM6M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM7M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM8M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM9M', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMAM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMBM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMCM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMDM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMEM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMFM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMGM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMHM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMIM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMJM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMKM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMLM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMMM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMNM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMOM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMPM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMQM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMRM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMSM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMTM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMUM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMVM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMWM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMXM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMYM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBMZM', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM0N', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM1N', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM2N', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM3N', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM4N', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM5N', 'FINST-W4G66DA1X7VVMYB57LXBO79B4IK23HCCZ5DBM6N']
2025-06-01 12:32:12,644 - INFO - 批量插入响应状态码: 200
2025-06-01 12:32:12,645 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 04:32:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4716', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B9BE762D-847C-7932-AB7C-7AF740A75196', 'x-acs-trace-id': '595cb06575b12e131aec2192f456ad06', 'etag': '4yL7fBi+uWlWJwIiG4Jy5sg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 12:32:12,645 - INFO - 批量插入响应体: {'result': ['FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM5B', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM6B', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM7B', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM8B', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM9B', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMAB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMBB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMCB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMDB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMEB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMFB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMGB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMHB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMIB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMJB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMKB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMLB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMMB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMNB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMOB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMPB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMQB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMRB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMSB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMTB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMUB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMVB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMWB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMXB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMYB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMZB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM0C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM1C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM2C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM3C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM4C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM5C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM6C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM7C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM8C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM9C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMAC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMBC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMCC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMDC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMEC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMFC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMGC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMHC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMIC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMJC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMKC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMLC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMMC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMNC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMOC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMPC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMQC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMRC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMSC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMTC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMUC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMVC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMWC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMXC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMYC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMZC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM0D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM1D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM2D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM3D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM4D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM5D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM6D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM7D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM8D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM9D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMAD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMBD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMCD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMDD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMED', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMFD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMGD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMHD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMID', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMJD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMKD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMLD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMMD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMND', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMOD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMPD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMQD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMRD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMSD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMTD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMUD']}
2025-06-01 12:32:12,645 - INFO - 批量插入表单数据成功，批次 4，共 98 条记录
2025-06-01 12:32:12,645 - INFO - 成功插入的数据ID: ['FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM5B', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM6B', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM7B', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM8B', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM9B', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMAB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMBB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMCB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMDB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMEB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMFB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMGB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMHB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMIB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMJB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMKB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMLB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMMB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMNB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMOB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMPB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMQB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMRB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMSB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMTB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMUB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMVB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMWB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMXB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMYB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMZB', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM0C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM1C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM2C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM3C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM4C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM5C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM6C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM7C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM8C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM9C', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMAC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMBC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMCC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMDC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMEC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMFC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMGC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMHC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMIC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMJC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMKC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMLC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMMC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMNC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMOC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMPC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMQC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMRC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMSC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMTC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMUC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMVC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMWC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMXC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMYC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMZC', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM0D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM1D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM2D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM3D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM4D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM5D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM6D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM7D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM8D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM9D', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMAD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMBD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMCD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMDD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMED', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMFD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMGD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMHD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMID', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMJD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMKD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMLD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMMD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMND', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMOD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMPD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMQD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMRD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMSD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMTD', 'FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMUD']
2025-06-01 12:32:17,646 - INFO - 批量插入完成，共 398 条记录
2025-06-01 12:32:17,646 - INFO - 日期 2025-05-31 处理完成 - 更新: 2 条，插入: 398 条，错误: 0 条
2025-06-01 12:32:17,646 - INFO - 开始处理日期: 2025-06-01
2025-06-01 12:32:17,646 - INFO - Request Parameters - Page 1:
2025-06-01 12:32:17,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:32:17,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:32:18,190 - INFO - Response - Page 1:
2025-06-01 12:32:18,191 - INFO - 查询完成，共获取到 0 条记录
2025-06-01 12:32:18,191 - INFO - 获取到 0 条表单数据
2025-06-01 12:32:18,191 - INFO - 当前日期 2025-06-01 有 3 条MySQL数据需要处理
2025-06-01 12:32:18,191 - INFO - 开始批量插入 3 条新记录
2025-06-01 12:32:18,340 - INFO - 批量插入响应状态码: 200
2025-06-01 12:32:18,340 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 04:32:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '88A36423-1E6A-7653-918B-AB45955EC90F', 'x-acs-trace-id': '24cd95906252269324bfe59321900a27', 'etag': '1bP1EhJZvgwEo4NDRV/j1GQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 12:32:18,340 - INFO - 批量插入响应体: {'result': ['FINST-FD966QA14HRVZ9SYBJEGNDXLTWY23QUKZ5DBMUV', 'FINST-FD966QA14HRVZ9SYBJEGNDXLTWY23QUKZ5DBMVV', 'FINST-FD966QA14HRVZ9SYBJEGNDXLTWY23QUKZ5DBMWV']}
2025-06-01 12:32:18,340 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-06-01 12:32:18,340 - INFO - 成功插入的数据ID: ['FINST-FD966QA14HRVZ9SYBJEGNDXLTWY23QUKZ5DBMUV', 'FINST-FD966QA14HRVZ9SYBJEGNDXLTWY23QUKZ5DBMVV', 'FINST-FD966QA14HRVZ9SYBJEGNDXLTWY23QUKZ5DBMWV']
2025-06-01 12:32:23,341 - INFO - 批量插入完成，共 3 条记录
2025-06-01 12:32:23,341 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-06-01 12:32:23,341 - INFO - 数据同步完成！更新: 2 条，插入: 401 条，错误: 0 条
2025-06-01 12:32:23,341 - INFO - 同步完成
2025-06-01 13:30:33,573 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 13:30:33,573 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 13:30:33,573 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 13:30:33,651 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 197 条记录
2025-06-01 13:30:33,651 - INFO - 获取到 4 个日期需要处理: ['2025-05-12', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 13:30:33,651 - INFO - 开始处理日期: 2025-05-12
2025-06-01 13:30:33,667 - INFO - Request Parameters - Page 1:
2025-06-01 13:30:33,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 13:30:33,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 13:30:41,792 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1CF51DB7-EBB9-730D-82EA-CCEF1861631B Response: {'code': 'ServiceUnavailable', 'requestid': '1CF51DB7-EBB9-730D-82EA-CCEF1861631B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1CF51DB7-EBB9-730D-82EA-CCEF1861631B)
2025-06-01 13:30:41,792 - INFO - 开始处理日期: 2025-05-30
2025-06-01 13:30:41,792 - INFO - Request Parameters - Page 1:
2025-06-01 13:30:41,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 13:30:41,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 13:30:41,933 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 12676C18-D745-7073-9FF5-09114D8C99F1 Response: {'requestid': '12676C18-D745-7073-9FF5-09114D8C99F1', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 12676C18-D745-7073-9FF5-09114D8C99F1)
2025-06-01 13:30:41,933 - INFO - 开始处理日期: 2025-05-31
2025-06-01 13:30:41,933 - INFO - Request Parameters - Page 1:
2025-06-01 13:30:41,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 13:30:41,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 13:30:42,105 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 4A02E409-BBFE-778C-ACF6-291CC57A2243 Response: {'requestid': '4A02E409-BBFE-778C-ACF6-291CC57A2243', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 4A02E409-BBFE-778C-ACF6-291CC57A2243)
2025-06-01 13:30:42,105 - INFO - 开始处理日期: 2025-06-01
2025-06-01 13:30:42,105 - INFO - Request Parameters - Page 1:
2025-06-01 13:30:42,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 13:30:42,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 13:30:42,323 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 0DC75652-669C-78B1-9367-70EFDBA992DD Response: {'requestid': '0DC75652-669C-78B1-9367-70EFDBA992DD', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 0DC75652-669C-78B1-9367-70EFDBA992DD)
2025-06-01 13:30:42,323 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 4 条
2025-06-01 13:31:42,339 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 13:31:42,339 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 13:31:42,339 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 13:31:42,417 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 522 条记录
2025-06-01 13:31:42,417 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 13:31:42,417 - INFO - 开始处理日期: 2025-05-31
2025-06-01 13:31:42,417 - INFO - Request Parameters - Page 1:
2025-06-01 13:31:42,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 13:31:42,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 13:31:43,323 - INFO - Response - Page 1:
2025-06-01 13:31:43,323 - INFO - 第 1 页获取到 100 条记录
2025-06-01 13:31:43,526 - INFO - Request Parameters - Page 2:
2025-06-01 13:31:43,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 13:31:43,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 13:31:51,620 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 90745431-F980-7F4A-BE41-61C303D4716D Response: {'code': 'ServiceUnavailable', 'requestid': '90745431-F980-7F4A-BE41-61C303D4716D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 90745431-F980-7F4A-BE41-61C303D4716D)
2025-06-01 13:31:51,620 - INFO - 开始处理日期: 2025-06-01
2025-06-01 13:31:51,620 - INFO - Request Parameters - Page 1:
2025-06-01 13:31:51,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 13:31:51,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 13:31:59,713 - INFO - Response - Page 1:
2025-06-01 13:31:59,713 - INFO - 第 1 页获取到 3 条记录
2025-06-01 13:31:59,917 - INFO - 查询完成，共获取到 3 条记录
2025-06-01 13:31:59,917 - INFO - 获取到 3 条表单数据
2025-06-01 13:31:59,917 - INFO - 当前日期 2025-06-01 有 3 条MySQL数据需要处理
2025-06-01 13:31:59,917 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 13:31:59,917 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-01 13:31:59,917 - INFO - 同步完成
2025-06-01 14:30:32,856 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 14:30:32,856 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 14:30:32,856 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 14:30:32,934 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 199 条记录
2025-06-01 14:30:32,934 - INFO - 获取到 4 个日期需要处理: ['2025-05-12', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 14:30:32,934 - INFO - 开始处理日期: 2025-05-12
2025-06-01 14:30:32,934 - INFO - Request Parameters - Page 1:
2025-06-01 14:30:32,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:30:32,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:30:41,059 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 39808924-156E-7ECF-957A-62E66FE44DED Response: {'code': 'ServiceUnavailable', 'requestid': '39808924-156E-7ECF-957A-62E66FE44DED', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 39808924-156E-7ECF-957A-62E66FE44DED)
2025-06-01 14:30:41,059 - INFO - 开始处理日期: 2025-05-30
2025-06-01 14:30:41,059 - INFO - Request Parameters - Page 1:
2025-06-01 14:30:41,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:30:41,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:30:49,169 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EE9D3834-02EF-7298-8536-1B74B08E47B1 Response: {'code': 'ServiceUnavailable', 'requestid': 'EE9D3834-02EF-7298-8536-1B74B08E47B1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EE9D3834-02EF-7298-8536-1B74B08E47B1)
2025-06-01 14:30:49,169 - INFO - 开始处理日期: 2025-05-31
2025-06-01 14:30:49,169 - INFO - Request Parameters - Page 1:
2025-06-01 14:30:49,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:30:49,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:30:57,418 - INFO - Response - Page 1:
2025-06-01 14:30:57,418 - INFO - 第 1 页获取到 100 条记录
2025-06-01 14:30:57,622 - INFO - Request Parameters - Page 2:
2025-06-01 14:30:57,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:30:57,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:30:58,465 - INFO - Response - Page 2:
2025-06-01 14:30:58,465 - INFO - 第 2 页获取到 100 条记录
2025-06-01 14:30:58,668 - INFO - Request Parameters - Page 3:
2025-06-01 14:30:58,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:30:58,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:30:59,543 - INFO - Response - Page 3:
2025-06-01 14:30:59,543 - INFO - 第 3 页获取到 100 条记录
2025-06-01 14:30:59,747 - INFO - Request Parameters - Page 4:
2025-06-01 14:30:59,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:30:59,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:31:00,512 - INFO - Response - Page 4:
2025-06-01 14:31:00,512 - INFO - 第 4 页获取到 100 条记录
2025-06-01 14:31:00,715 - INFO - Request Parameters - Page 5:
2025-06-01 14:31:00,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:31:00,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:31:01,512 - INFO - Response - Page 5:
2025-06-01 14:31:01,512 - INFO - 第 5 页获取到 100 条记录
2025-06-01 14:31:01,715 - INFO - Request Parameters - Page 6:
2025-06-01 14:31:01,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:31:01,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:31:02,356 - INFO - Response - Page 6:
2025-06-01 14:31:02,356 - INFO - 第 6 页获取到 19 条记录
2025-06-01 14:31:02,559 - INFO - 查询完成，共获取到 519 条记录
2025-06-01 14:31:02,559 - INFO - 获取到 519 条表单数据
2025-06-01 14:31:02,559 - INFO - 当前日期 2025-05-31 有 193 条MySQL数据需要处理
2025-06-01 14:31:02,559 - INFO - 开始批量插入 2 条新记录
2025-06-01 14:31:02,715 - INFO - 批量插入响应状态码: 200
2025-06-01 14:31:02,715 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 06:30:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C221F02E-4A11-766B-B8A2-64ABE1AF3D82', 'x-acs-trace-id': '1cf82385453d1a8cc347504dd631e80a', 'etag': '1ZTNV5vNxGsebi5w5wn9/zQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 14:31:02,715 - INFO - 批量插入响应体: {'result': ['FINST-ZNE66RC1KCVVKYSGFQC1Z7RX72FG3K3A8ADBMMA', 'FINST-ZNE66RC1KCVVKYSGFQC1Z7RX72FG3K3A8ADBMNA']}
2025-06-01 14:31:02,715 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-01 14:31:02,715 - INFO - 成功插入的数据ID: ['FINST-ZNE66RC1KCVVKYSGFQC1Z7RX72FG3K3A8ADBMMA', 'FINST-ZNE66RC1KCVVKYSGFQC1Z7RX72FG3K3A8ADBMNA']
2025-06-01 14:31:07,731 - INFO - 批量插入完成，共 2 条记录
2025-06-01 14:31:07,731 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-01 14:31:07,731 - INFO - 开始处理日期: 2025-06-01
2025-06-01 14:31:07,731 - INFO - Request Parameters - Page 1:
2025-06-01 14:31:07,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:31:07,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:31:08,246 - INFO - Response - Page 1:
2025-06-01 14:31:08,246 - INFO - 第 1 页获取到 3 条记录
2025-06-01 14:31:08,450 - INFO - 查询完成，共获取到 3 条记录
2025-06-01 14:31:08,450 - INFO - 获取到 3 条表单数据
2025-06-01 14:31:08,450 - INFO - 当前日期 2025-06-01 有 3 条MySQL数据需要处理
2025-06-01 14:31:08,450 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 14:31:08,450 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 2 条
2025-06-01 14:32:08,465 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 14:32:08,465 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 14:32:08,465 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 14:32:08,543 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 524 条记录
2025-06-01 14:32:08,543 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 14:32:08,559 - INFO - 开始处理日期: 2025-05-31
2025-06-01 14:32:08,559 - INFO - Request Parameters - Page 1:
2025-06-01 14:32:08,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:32:08,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:32:09,496 - INFO - Response - Page 1:
2025-06-01 14:32:09,496 - INFO - 第 1 页获取到 100 条记录
2025-06-01 14:32:09,699 - INFO - Request Parameters - Page 2:
2025-06-01 14:32:09,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:32:09,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:32:10,543 - INFO - Response - Page 2:
2025-06-01 14:32:10,543 - INFO - 第 2 页获取到 100 条记录
2025-06-01 14:32:10,746 - INFO - Request Parameters - Page 3:
2025-06-01 14:32:10,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:32:10,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:32:11,559 - INFO - Response - Page 3:
2025-06-01 14:32:11,559 - INFO - 第 3 页获取到 100 条记录
2025-06-01 14:32:11,762 - INFO - Request Parameters - Page 4:
2025-06-01 14:32:11,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:32:11,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:32:12,605 - INFO - Response - Page 4:
2025-06-01 14:32:12,605 - INFO - 第 4 页获取到 100 条记录
2025-06-01 14:32:12,809 - INFO - Request Parameters - Page 5:
2025-06-01 14:32:12,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:32:12,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:32:13,684 - INFO - Response - Page 5:
2025-06-01 14:32:13,684 - INFO - 第 5 页获取到 100 条记录
2025-06-01 14:32:13,887 - INFO - Request Parameters - Page 6:
2025-06-01 14:32:13,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:32:13,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:32:14,605 - INFO - Response - Page 6:
2025-06-01 14:32:14,605 - INFO - 第 6 页获取到 21 条记录
2025-06-01 14:32:14,809 - INFO - 查询完成，共获取到 521 条记录
2025-06-01 14:32:14,809 - INFO - 获取到 521 条表单数据
2025-06-01 14:32:14,809 - INFO - 当前日期 2025-05-31 有 521 条MySQL数据需要处理
2025-06-01 14:32:14,824 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 14:32:14,824 - INFO - 开始处理日期: 2025-06-01
2025-06-01 14:32:14,824 - INFO - Request Parameters - Page 1:
2025-06-01 14:32:14,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 14:32:14,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 14:32:15,340 - INFO - Response - Page 1:
2025-06-01 14:32:15,340 - INFO - 第 1 页获取到 3 条记录
2025-06-01 14:32:15,543 - INFO - 查询完成，共获取到 3 条记录
2025-06-01 14:32:15,543 - INFO - 获取到 3 条表单数据
2025-06-01 14:32:15,543 - INFO - 当前日期 2025-06-01 有 3 条MySQL数据需要处理
2025-06-01 14:32:15,543 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 14:32:15,543 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 14:32:15,543 - INFO - 同步完成
2025-06-01 15:30:33,573 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 15:30:33,589 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 15:30:33,589 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 15:30:33,652 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 204 条记录
2025-06-01 15:30:33,652 - INFO - 获取到 4 个日期需要处理: ['2025-05-12', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 15:30:33,667 - INFO - 开始处理日期: 2025-05-12
2025-06-01 15:30:33,667 - INFO - Request Parameters - Page 1:
2025-06-01 15:30:33,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:30:33,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:30:41,777 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 16C836C9-41E5-71BD-9AAF-6439C79A1A94 Response: {'code': 'ServiceUnavailable', 'requestid': '16C836C9-41E5-71BD-9AAF-6439C79A1A94', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 16C836C9-41E5-71BD-9AAF-6439C79A1A94)
2025-06-01 15:30:41,777 - INFO - 开始处理日期: 2025-05-30
2025-06-01 15:30:41,777 - INFO - Request Parameters - Page 1:
2025-06-01 15:30:41,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:30:41,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:30:49,901 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E625F6DA-8FC6-7413-9CFF-5E97AF95E4BC Response: {'code': 'ServiceUnavailable', 'requestid': 'E625F6DA-8FC6-7413-9CFF-5E97AF95E4BC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E625F6DA-8FC6-7413-9CFF-5E97AF95E4BC)
2025-06-01 15:30:49,901 - INFO - 开始处理日期: 2025-05-31
2025-06-01 15:30:49,901 - INFO - Request Parameters - Page 1:
2025-06-01 15:30:49,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:30:49,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:30:50,839 - INFO - Response - Page 1:
2025-06-01 15:30:50,839 - INFO - 第 1 页获取到 100 条记录
2025-06-01 15:30:51,042 - INFO - Request Parameters - Page 2:
2025-06-01 15:30:51,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:30:51,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:30:52,011 - INFO - Response - Page 2:
2025-06-01 15:30:52,011 - INFO - 第 2 页获取到 100 条记录
2025-06-01 15:30:52,214 - INFO - Request Parameters - Page 3:
2025-06-01 15:30:52,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:30:52,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:30:53,214 - INFO - Response - Page 3:
2025-06-01 15:30:53,214 - INFO - 第 3 页获取到 100 条记录
2025-06-01 15:30:53,417 - INFO - Request Parameters - Page 4:
2025-06-01 15:30:53,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:30:53,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:30:54,198 - INFO - Response - Page 4:
2025-06-01 15:30:54,198 - INFO - 第 4 页获取到 100 条记录
2025-06-01 15:30:54,401 - INFO - Request Parameters - Page 5:
2025-06-01 15:30:54,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:30:54,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:30:55,198 - INFO - Response - Page 5:
2025-06-01 15:30:55,198 - INFO - 第 5 页获取到 100 条记录
2025-06-01 15:30:55,401 - INFO - Request Parameters - Page 6:
2025-06-01 15:30:55,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:30:55,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:30:56,042 - INFO - Response - Page 6:
2025-06-01 15:30:56,042 - INFO - 第 6 页获取到 21 条记录
2025-06-01 15:30:56,245 - INFO - 查询完成，共获取到 521 条记录
2025-06-01 15:30:56,245 - INFO - 获取到 521 条表单数据
2025-06-01 15:30:56,245 - INFO - 当前日期 2025-05-31 有 196 条MySQL数据需要处理
2025-06-01 15:30:56,245 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMU8
2025-06-01 15:30:56,761 - INFO - 更新表单数据成功: FINST-T9D66B81B9VVV4EJBJKE58Q0IXF22064Z5DBMU8
2025-06-01 15:30:56,761 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1201.2, 'new_value': 1221.2}, {'field': 'total_amount', 'old_value': 1201.2, 'new_value': 1221.2}]
2025-06-01 15:30:56,776 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM1C
2025-06-01 15:30:57,230 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM1C
2025-06-01 15:30:57,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 42727.71}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 42727.71}, {'field': 'order_count', 'old_value': 0, 'new_value': 36}]
2025-06-01 15:30:57,230 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM3C
2025-06-01 15:30:57,651 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM3C
2025-06-01 15:30:57,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2505900.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2505900.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-01 15:30:57,651 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM4C
2025-06-01 15:30:58,105 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM4C
2025-06-01 15:30:58,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 24371.28}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 24371.28}, {'field': 'order_count', 'old_value': 0, 'new_value': 163}]
2025-06-01 15:30:58,105 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM7C
2025-06-01 15:30:58,651 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM7C
2025-06-01 15:30:58,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-01 15:30:58,651 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMBC
2025-06-01 15:30:59,073 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMBC
2025-06-01 15:30:59,073 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 9658.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 9658.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 56}]
2025-06-01 15:30:59,073 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMCC
2025-06-01 15:30:59,542 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMCC
2025-06-01 15:30:59,542 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-01 15:30:59,542 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMEC
2025-06-01 15:30:59,980 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMEC
2025-06-01 15:30:59,980 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 25896.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 25896.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 65}]
2025-06-01 15:30:59,980 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMFC
2025-06-01 15:31:00,480 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMFC
2025-06-01 15:31:00,480 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 9658.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 9658.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-06-01 15:31:00,480 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMGC
2025-06-01 15:31:01,011 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMGC
2025-06-01 15:31:01,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 23770.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 23770.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-06-01 15:31:01,011 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMHC
2025-06-01 15:31:01,401 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMHC
2025-06-01 15:31:01,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 19744.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 19744.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 469}]
2025-06-01 15:31:01,401 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMIC
2025-06-01 15:31:01,792 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMIC
2025-06-01 15:31:01,792 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 70000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 70000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-01 15:31:01,792 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMOC
2025-06-01 15:31:02,261 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMOC
2025-06-01 15:31:02,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1855.75}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1855.75}, {'field': 'order_count', 'old_value': 0, 'new_value': 75}]
2025-06-01 15:31:02,261 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMPC
2025-06-01 15:31:02,776 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMPC
2025-06-01 15:31:02,776 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 785.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 785.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 25}]
2025-06-01 15:31:02,776 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMVC
2025-06-01 15:31:03,229 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMVC
2025-06-01 15:31:03,229 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 31466.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 31466.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 7}]
2025-06-01 15:31:03,229 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMXC
2025-06-01 15:31:03,698 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMXC
2025-06-01 15:31:03,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 53000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 53000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-01 15:31:03,698 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMYC
2025-06-01 15:31:04,167 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMYC
2025-06-01 15:31:04,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10834.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 10834.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-06-01 15:31:04,167 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMZC
2025-06-01 15:31:04,683 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMZC
2025-06-01 15:31:04,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 50000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-01 15:31:04,683 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM1D
2025-06-01 15:31:05,183 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM1D
2025-06-01 15:31:05,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 7890.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 7890.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 8}]
2025-06-01 15:31:05,183 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM4D
2025-06-01 15:31:05,620 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM4D
2025-06-01 15:31:05,620 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2644.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2644.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-06-01 15:31:05,636 - INFO - 开始批量插入 2 条新记录
2025-06-01 15:31:05,808 - INFO - 批量插入响应状态码: 200
2025-06-01 15:31:05,808 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 07:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '67DC6277-E9BC-7FDF-B561-7E922878BA41', 'x-acs-trace-id': '67ab16967858bcfe4a0eea91a445a8cd', 'etag': '1WmrtMCJNdkqrvJWWGFq+EQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 15:31:05,808 - INFO - 批量插入响应体: {'result': ['FINST-5TD66N918FTVUY6WA0MNDBCTMNU32U9IDCDBMCL', 'FINST-5TD66N918FTVUY6WA0MNDBCTMNU32U9IDCDBMDL']}
2025-06-01 15:31:05,808 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-01 15:31:05,808 - INFO - 成功插入的数据ID: ['FINST-5TD66N918FTVUY6WA0MNDBCTMNU32U9IDCDBMCL', 'FINST-5TD66N918FTVUY6WA0MNDBCTMNU32U9IDCDBMDL']
2025-06-01 15:31:10,823 - INFO - 批量插入完成，共 2 条记录
2025-06-01 15:31:10,823 - INFO - 日期 2025-05-31 处理完成 - 更新: 20 条，插入: 2 条，错误: 0 条
2025-06-01 15:31:10,823 - INFO - 开始处理日期: 2025-06-01
2025-06-01 15:31:10,823 - INFO - Request Parameters - Page 1:
2025-06-01 15:31:10,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:31:10,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:31:11,375 - INFO - Response - Page 1:
2025-06-01 15:31:11,375 - INFO - 第 1 页获取到 3 条记录
2025-06-01 15:31:11,578 - INFO - 查询完成，共获取到 3 条记录
2025-06-01 15:31:11,578 - INFO - 获取到 3 条表单数据
2025-06-01 15:31:11,578 - INFO - 当前日期 2025-06-01 有 4 条MySQL数据需要处理
2025-06-01 15:31:11,578 - INFO - 开始批量插入 1 条新记录
2025-06-01 15:31:11,734 - INFO - 批量插入响应状态码: 200
2025-06-01 15:31:11,734 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 07:31:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '10171286-DA97-7BA7-B1B4-6705ED832217', 'x-acs-trace-id': '78f38fb2f02cfc00bec911df0865df21', 'etag': '6mwjDh1e98v+1ZTw3CEdvig0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 15:31:11,734 - INFO - 批量插入响应体: {'result': ['FINST-KLF66WC10AVV0QSBF3D0089SGVMA3KUMDCDBMFH']}
2025-06-01 15:31:11,734 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-01 15:31:11,734 - INFO - 成功插入的数据ID: ['FINST-KLF66WC10AVV0QSBF3D0089SGVMA3KUMDCDBMFH']
2025-06-01 15:31:16,750 - INFO - 批量插入完成，共 1 条记录
2025-06-01 15:31:16,750 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-01 15:31:16,750 - INFO - 数据同步完成！更新: 20 条，插入: 3 条，错误: 2 条
2025-06-01 15:32:16,765 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 15:32:16,765 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 15:32:16,765 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 15:32:16,843 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 527 条记录
2025-06-01 15:32:16,843 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 15:32:16,843 - INFO - 开始处理日期: 2025-05-31
2025-06-01 15:32:16,843 - INFO - Request Parameters - Page 1:
2025-06-01 15:32:16,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:32:16,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:32:17,765 - INFO - Response - Page 1:
2025-06-01 15:32:17,765 - INFO - 第 1 页获取到 100 条记录
2025-06-01 15:32:17,968 - INFO - Request Parameters - Page 2:
2025-06-01 15:32:17,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:32:17,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:32:18,812 - INFO - Response - Page 2:
2025-06-01 15:32:18,812 - INFO - 第 2 页获取到 100 条记录
2025-06-01 15:32:19,015 - INFO - Request Parameters - Page 3:
2025-06-01 15:32:19,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:32:19,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:32:19,859 - INFO - Response - Page 3:
2025-06-01 15:32:19,859 - INFO - 第 3 页获取到 100 条记录
2025-06-01 15:32:20,062 - INFO - Request Parameters - Page 4:
2025-06-01 15:32:20,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:32:20,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:32:20,953 - INFO - Response - Page 4:
2025-06-01 15:32:20,953 - INFO - 第 4 页获取到 100 条记录
2025-06-01 15:32:21,156 - INFO - Request Parameters - Page 5:
2025-06-01 15:32:21,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:32:21,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:32:21,968 - INFO - Response - Page 5:
2025-06-01 15:32:21,968 - INFO - 第 5 页获取到 100 条记录
2025-06-01 15:32:22,171 - INFO - Request Parameters - Page 6:
2025-06-01 15:32:22,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:32:22,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:32:22,843 - INFO - Response - Page 6:
2025-06-01 15:32:22,843 - INFO - 第 6 页获取到 23 条记录
2025-06-01 15:32:23,046 - INFO - 查询完成，共获取到 523 条记录
2025-06-01 15:32:23,046 - INFO - 获取到 523 条表单数据
2025-06-01 15:32:23,046 - INFO - 当前日期 2025-05-31 有 523 条MySQL数据需要处理
2025-06-01 15:32:23,062 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 15:32:23,062 - INFO - 开始处理日期: 2025-06-01
2025-06-01 15:32:23,062 - INFO - Request Parameters - Page 1:
2025-06-01 15:32:23,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:32:23,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:32:23,640 - INFO - Response - Page 1:
2025-06-01 15:32:23,640 - INFO - 第 1 页获取到 4 条记录
2025-06-01 15:32:23,843 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 15:32:23,843 - INFO - 获取到 4 条表单数据
2025-06-01 15:32:23,843 - INFO - 当前日期 2025-06-01 有 4 条MySQL数据需要处理
2025-06-01 15:32:23,843 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 15:32:23,843 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 15:32:23,843 - INFO - 同步完成
2025-06-01 16:30:33,558 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 16:30:33,558 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 16:30:33,558 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 16:30:33,636 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 205 条记录
2025-06-01 16:30:33,636 - INFO - 获取到 4 个日期需要处理: ['2025-05-12', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 16:30:33,636 - INFO - 开始处理日期: 2025-05-12
2025-06-01 16:30:33,652 - INFO - Request Parameters - Page 1:
2025-06-01 16:30:33,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 16:30:33,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 16:30:41,761 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F20A048B-F9D4-7CF1-8FEC-B65EEDE67615 Response: {'code': 'ServiceUnavailable', 'requestid': 'F20A048B-F9D4-7CF1-8FEC-B65EEDE67615', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F20A048B-F9D4-7CF1-8FEC-B65EEDE67615)
2025-06-01 16:30:41,761 - INFO - 开始处理日期: 2025-05-30
2025-06-01 16:30:41,761 - INFO - Request Parameters - Page 1:
2025-06-01 16:30:41,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 16:30:41,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 16:30:49,902 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 64FF61AC-BBC8-7226-95B3-0886FCEB99ED Response: {'code': 'ServiceUnavailable', 'requestid': '64FF61AC-BBC8-7226-95B3-0886FCEB99ED', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 64FF61AC-BBC8-7226-95B3-0886FCEB99ED)
2025-06-01 16:30:49,902 - INFO - 开始处理日期: 2025-05-31
2025-06-01 16:30:49,902 - INFO - Request Parameters - Page 1:
2025-06-01 16:30:49,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 16:30:49,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 16:30:58,027 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B97AE00B-02EC-7F8D-B112-A29832E670DC Response: {'code': 'ServiceUnavailable', 'requestid': 'B97AE00B-02EC-7F8D-B112-A29832E670DC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B97AE00B-02EC-7F8D-B112-A29832E670DC)
2025-06-01 16:30:58,027 - INFO - 开始处理日期: 2025-06-01
2025-06-01 16:30:58,027 - INFO - Request Parameters - Page 1:
2025-06-01 16:30:58,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 16:30:58,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 16:30:58,605 - INFO - Response - Page 1:
2025-06-01 16:30:58,605 - INFO - 第 1 页获取到 4 条记录
2025-06-01 16:30:58,808 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 16:30:58,808 - INFO - 获取到 4 条表单数据
2025-06-01 16:30:58,808 - INFO - 当前日期 2025-06-01 有 4 条MySQL数据需要处理
2025-06-01 16:30:58,808 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 16:30:58,808 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-06-01 16:31:58,823 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 16:31:58,823 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 16:31:58,823 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 16:31:58,901 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 528 条记录
2025-06-01 16:31:58,901 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 16:31:58,901 - INFO - 开始处理日期: 2025-05-31
2025-06-01 16:31:58,901 - INFO - Request Parameters - Page 1:
2025-06-01 16:31:58,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 16:31:58,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 16:31:59,792 - INFO - Response - Page 1:
2025-06-01 16:31:59,792 - INFO - 第 1 页获取到 100 条记录
2025-06-01 16:31:59,995 - INFO - Request Parameters - Page 2:
2025-06-01 16:31:59,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 16:31:59,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 16:32:00,870 - INFO - Response - Page 2:
2025-06-01 16:32:00,870 - INFO - 第 2 页获取到 100 条记录
2025-06-01 16:32:01,073 - INFO - Request Parameters - Page 3:
2025-06-01 16:32:01,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 16:32:01,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 16:32:02,058 - INFO - Response - Page 3:
2025-06-01 16:32:02,073 - INFO - 第 3 页获取到 100 条记录
2025-06-01 16:32:02,276 - INFO - Request Parameters - Page 4:
2025-06-01 16:32:02,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 16:32:02,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 16:32:03,105 - INFO - Response - Page 4:
2025-06-01 16:32:03,105 - INFO - 第 4 页获取到 100 条记录
2025-06-01 16:32:03,308 - INFO - Request Parameters - Page 5:
2025-06-01 16:32:03,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 16:32:03,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 16:32:04,183 - INFO - Response - Page 5:
2025-06-01 16:32:04,183 - INFO - 第 5 页获取到 100 条记录
2025-06-01 16:32:04,386 - INFO - Request Parameters - Page 6:
2025-06-01 16:32:04,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 16:32:04,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 16:32:05,026 - INFO - Response - Page 6:
2025-06-01 16:32:05,042 - INFO - 第 6 页获取到 23 条记录
2025-06-01 16:32:05,245 - INFO - 查询完成，共获取到 523 条记录
2025-06-01 16:32:05,245 - INFO - 获取到 523 条表单数据
2025-06-01 16:32:05,245 - INFO - 当前日期 2025-05-31 有 524 条MySQL数据需要处理
2025-06-01 16:32:05,261 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMNC
2025-06-01 16:32:05,839 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMNC
2025-06-01 16:32:05,839 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4518.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4518.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 158}]
2025-06-01 16:32:05,839 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMQC
2025-06-01 16:32:06,323 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMQC
2025-06-01 16:32:06,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 7915.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 7915.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 105}]
2025-06-01 16:32:06,323 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMRC
2025-06-01 16:32:06,808 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMRC
2025-06-01 16:32:06,808 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 12854.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 12854.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-06-01 16:32:06,808 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMSC
2025-06-01 16:32:07,276 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMSC
2025-06-01 16:32:07,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-06-01 16:32:07,276 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMTC
2025-06-01 16:32:07,698 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMTC
2025-06-01 16:32:07,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 9155.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 9155.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 618}]
2025-06-01 16:32:07,698 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMUC
2025-06-01 16:32:08,198 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBMUC
2025-06-01 16:32:08,198 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 10}]
2025-06-01 16:32:08,198 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM0D
2025-06-01 16:32:08,730 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM0D
2025-06-01 16:32:08,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1120.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1120.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-01 16:32:08,730 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM2D
2025-06-01 16:32:09,167 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM2D
2025-06-01 16:32:09,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 7800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 7800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-01 16:32:09,167 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM5D
2025-06-01 16:32:09,714 - INFO - 更新表单数据成功: FINST-90D66XA1GESVHU6D9OU1RCO0H7Q83HGGZ5DBM5D
2025-06-01 16:32:09,714 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 18000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 18000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 10}]
2025-06-01 16:32:09,714 - INFO - 开始批量插入 1 条新记录
2025-06-01 16:32:09,870 - INFO - 批量插入响应状态码: 200
2025-06-01 16:32:09,870 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 08:32:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F53883D6-93DD-7DD4-96E3-A6731B9DE0F2', 'x-acs-trace-id': '44153b6ebc43ec443b263167bc9bd134', 'etag': '6/2H2dkrkvij6ASv+lY+bQg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 16:32:09,870 - INFO - 批量插入响应体: {'result': ['FINST-UW966371C7VV8ARLB0RLD4H01QDV2EI1KEDBMPB']}
2025-06-01 16:32:09,870 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-01 16:32:09,870 - INFO - 成功插入的数据ID: ['FINST-UW966371C7VV8ARLB0RLD4H01QDV2EI1KEDBMPB']
2025-06-01 16:32:14,886 - INFO - 批量插入完成，共 1 条记录
2025-06-01 16:32:14,886 - INFO - 日期 2025-05-31 处理完成 - 更新: 9 条，插入: 1 条，错误: 0 条
2025-06-01 16:32:14,886 - INFO - 开始处理日期: 2025-06-01
2025-06-01 16:32:14,886 - INFO - Request Parameters - Page 1:
2025-06-01 16:32:14,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 16:32:14,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 16:32:15,480 - INFO - Response - Page 1:
2025-06-01 16:32:15,480 - INFO - 第 1 页获取到 4 条记录
2025-06-01 16:32:15,683 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 16:32:15,683 - INFO - 获取到 4 条表单数据
2025-06-01 16:32:15,683 - INFO - 当前日期 2025-06-01 有 4 条MySQL数据需要处理
2025-06-01 16:32:15,683 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 16:32:15,683 - INFO - 数据同步完成！更新: 9 条，插入: 1 条，错误: 0 条
2025-06-01 16:32:15,683 - INFO - 同步完成
2025-06-01 17:30:33,574 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 17:30:33,574 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 17:30:33,574 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 17:30:33,652 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 205 条记录
2025-06-01 17:30:33,652 - INFO - 获取到 4 个日期需要处理: ['2025-05-12', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 17:30:33,652 - INFO - 开始处理日期: 2025-05-12
2025-06-01 17:30:33,652 - INFO - Request Parameters - Page 1:
2025-06-01 17:30:33,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:30:33,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:30:41,761 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D3BF28EF-33CF-7906-BA0C-************ Response: {'code': 'ServiceUnavailable', 'requestid': 'D3BF28EF-33CF-7906-BA0C-************', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D3BF28EF-33CF-7906-BA0C-************)
2025-06-01 17:30:41,761 - INFO - 开始处理日期: 2025-05-30
2025-06-01 17:30:41,761 - INFO - Request Parameters - Page 1:
2025-06-01 17:30:41,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:30:41,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:30:49,886 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3932D559-9548-74AB-AB4C-BDB7C137E187 Response: {'code': 'ServiceUnavailable', 'requestid': '3932D559-9548-74AB-AB4C-BDB7C137E187', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3932D559-9548-74AB-AB4C-BDB7C137E187)
2025-06-01 17:30:49,886 - INFO - 开始处理日期: 2025-05-31
2025-06-01 17:30:49,886 - INFO - Request Parameters - Page 1:
2025-06-01 17:30:49,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:30:49,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:30:57,870 - INFO - Response - Page 1:
2025-06-01 17:30:57,870 - INFO - 第 1 页获取到 100 条记录
2025-06-01 17:30:58,073 - INFO - Request Parameters - Page 2:
2025-06-01 17:30:58,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:30:58,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:30:58,995 - INFO - Response - Page 2:
2025-06-01 17:30:58,995 - INFO - 第 2 页获取到 100 条记录
2025-06-01 17:30:59,198 - INFO - Request Parameters - Page 3:
2025-06-01 17:30:59,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:30:59,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:30:59,980 - INFO - Response - Page 3:
2025-06-01 17:30:59,980 - INFO - 第 3 页获取到 100 条记录
2025-06-01 17:31:00,183 - INFO - Request Parameters - Page 4:
2025-06-01 17:31:00,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:31:00,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:31:01,026 - INFO - Response - Page 4:
2025-06-01 17:31:01,026 - INFO - 第 4 页获取到 100 条记录
2025-06-01 17:31:01,230 - INFO - Request Parameters - Page 5:
2025-06-01 17:31:01,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:31:01,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:31:02,105 - INFO - Response - Page 5:
2025-06-01 17:31:02,105 - INFO - 第 5 页获取到 100 条记录
2025-06-01 17:31:02,308 - INFO - Request Parameters - Page 6:
2025-06-01 17:31:02,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:31:02,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:31:03,011 - INFO - Response - Page 6:
2025-06-01 17:31:03,011 - INFO - 第 6 页获取到 24 条记录
2025-06-01 17:31:03,214 - INFO - 查询完成，共获取到 524 条记录
2025-06-01 17:31:03,214 - INFO - 获取到 524 条表单数据
2025-06-01 17:31:03,214 - INFO - 当前日期 2025-05-31 有 197 条MySQL数据需要处理
2025-06-01 17:31:03,214 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 17:31:03,214 - INFO - 开始处理日期: 2025-06-01
2025-06-01 17:31:03,214 - INFO - Request Parameters - Page 1:
2025-06-01 17:31:03,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:31:03,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:31:03,823 - INFO - Response - Page 1:
2025-06-01 17:31:03,823 - INFO - 第 1 页获取到 4 条记录
2025-06-01 17:31:04,026 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 17:31:04,026 - INFO - 获取到 4 条表单数据
2025-06-01 17:31:04,026 - INFO - 当前日期 2025-06-01 有 4 条MySQL数据需要处理
2025-06-01 17:31:04,026 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 17:31:04,026 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-01 17:32:04,042 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 17:32:04,042 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 17:32:04,042 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 17:32:04,120 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 528 条记录
2025-06-01 17:32:04,120 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 17:32:04,120 - INFO - 开始处理日期: 2025-05-31
2025-06-01 17:32:04,120 - INFO - Request Parameters - Page 1:
2025-06-01 17:32:04,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:32:04,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:32:05,010 - INFO - Response - Page 1:
2025-06-01 17:32:05,010 - INFO - 第 1 页获取到 100 条记录
2025-06-01 17:32:05,214 - INFO - Request Parameters - Page 2:
2025-06-01 17:32:05,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:32:05,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:32:06,042 - INFO - Response - Page 2:
2025-06-01 17:32:06,042 - INFO - 第 2 页获取到 100 条记录
2025-06-01 17:32:06,245 - INFO - Request Parameters - Page 3:
2025-06-01 17:32:06,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:32:06,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:32:07,135 - INFO - Response - Page 3:
2025-06-01 17:32:07,135 - INFO - 第 3 页获取到 100 条记录
2025-06-01 17:32:07,339 - INFO - Request Parameters - Page 4:
2025-06-01 17:32:07,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:32:07,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:32:08,120 - INFO - Response - Page 4:
2025-06-01 17:32:08,120 - INFO - 第 4 页获取到 100 条记录
2025-06-01 17:32:08,323 - INFO - Request Parameters - Page 5:
2025-06-01 17:32:08,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:32:08,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:32:09,135 - INFO - Response - Page 5:
2025-06-01 17:32:09,135 - INFO - 第 5 页获取到 100 条记录
2025-06-01 17:32:09,339 - INFO - Request Parameters - Page 6:
2025-06-01 17:32:09,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:32:09,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:32:10,026 - INFO - Response - Page 6:
2025-06-01 17:32:10,026 - INFO - 第 6 页获取到 24 条记录
2025-06-01 17:32:10,229 - INFO - 查询完成，共获取到 524 条记录
2025-06-01 17:32:10,229 - INFO - 获取到 524 条表单数据
2025-06-01 17:32:10,229 - INFO - 当前日期 2025-05-31 有 524 条MySQL数据需要处理
2025-06-01 17:32:10,245 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 17:32:10,245 - INFO - 开始处理日期: 2025-06-01
2025-06-01 17:32:10,245 - INFO - Request Parameters - Page 1:
2025-06-01 17:32:10,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 17:32:10,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 17:32:10,792 - INFO - Response - Page 1:
2025-06-01 17:32:10,792 - INFO - 第 1 页获取到 4 条记录
2025-06-01 17:32:10,995 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 17:32:10,995 - INFO - 获取到 4 条表单数据
2025-06-01 17:32:10,995 - INFO - 当前日期 2025-06-01 有 4 条MySQL数据需要处理
2025-06-01 17:32:10,995 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 17:32:10,995 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 17:32:10,995 - INFO - 同步完成
2025-06-01 18:30:33,636 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 18:30:33,636 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 18:30:33,636 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 18:30:33,714 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 205 条记录
2025-06-01 18:30:33,714 - INFO - 获取到 4 个日期需要处理: ['2025-05-12', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 18:30:33,714 - INFO - 开始处理日期: 2025-05-12
2025-06-01 18:30:33,714 - INFO - Request Parameters - Page 1:
2025-06-01 18:30:33,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:30:33,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:30:41,854 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 964BF327-091D-72BE-919E-311D39515ACD Response: {'code': 'ServiceUnavailable', 'requestid': '964BF327-091D-72BE-919E-311D39515ACD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 964BF327-091D-72BE-919E-311D39515ACD)
2025-06-01 18:30:41,854 - INFO - 开始处理日期: 2025-05-30
2025-06-01 18:30:41,854 - INFO - Request Parameters - Page 1:
2025-06-01 18:30:41,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:30:41,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:30:44,792 - INFO - Response - Page 1:
2025-06-01 18:30:44,792 - INFO - 第 1 页获取到 100 条记录
2025-06-01 18:30:44,995 - INFO - Request Parameters - Page 2:
2025-06-01 18:30:44,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:30:44,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:30:53,089 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B7902FF6-FC2A-710E-A055-62B0B4AE7265 Response: {'code': 'ServiceUnavailable', 'requestid': 'B7902FF6-FC2A-710E-A055-62B0B4AE7265', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B7902FF6-FC2A-710E-A055-62B0B4AE7265)
2025-06-01 18:30:53,089 - INFO - 开始处理日期: 2025-05-31
2025-06-01 18:30:53,089 - INFO - Request Parameters - Page 1:
2025-06-01 18:30:53,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:30:53,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:31:00,776 - INFO - Response - Page 1:
2025-06-01 18:31:00,776 - INFO - 第 1 页获取到 100 条记录
2025-06-01 18:31:00,979 - INFO - Request Parameters - Page 2:
2025-06-01 18:31:00,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:31:00,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:31:01,807 - INFO - Response - Page 2:
2025-06-01 18:31:01,807 - INFO - 第 2 页获取到 100 条记录
2025-06-01 18:31:02,010 - INFO - Request Parameters - Page 3:
2025-06-01 18:31:02,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:31:02,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:31:02,917 - INFO - Response - Page 3:
2025-06-01 18:31:02,917 - INFO - 第 3 页获取到 100 条记录
2025-06-01 18:31:03,120 - INFO - Request Parameters - Page 4:
2025-06-01 18:31:03,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:31:03,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:31:03,964 - INFO - Response - Page 4:
2025-06-01 18:31:03,964 - INFO - 第 4 页获取到 100 条记录
2025-06-01 18:31:04,167 - INFO - Request Parameters - Page 5:
2025-06-01 18:31:04,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:31:04,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:31:05,010 - INFO - Response - Page 5:
2025-06-01 18:31:05,010 - INFO - 第 5 页获取到 100 条记录
2025-06-01 18:31:05,214 - INFO - Request Parameters - Page 6:
2025-06-01 18:31:05,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:31:05,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:31:05,964 - INFO - Response - Page 6:
2025-06-01 18:31:05,964 - INFO - 第 6 页获取到 24 条记录
2025-06-01 18:31:06,167 - INFO - 查询完成，共获取到 524 条记录
2025-06-01 18:31:06,167 - INFO - 获取到 524 条表单数据
2025-06-01 18:31:06,167 - INFO - 当前日期 2025-05-31 有 197 条MySQL数据需要处理
2025-06-01 18:31:06,167 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 18:31:06,167 - INFO - 开始处理日期: 2025-06-01
2025-06-01 18:31:06,167 - INFO - Request Parameters - Page 1:
2025-06-01 18:31:06,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:31:06,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:31:06,760 - INFO - Response - Page 1:
2025-06-01 18:31:06,760 - INFO - 第 1 页获取到 4 条记录
2025-06-01 18:31:06,964 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 18:31:06,964 - INFO - 获取到 4 条表单数据
2025-06-01 18:31:06,964 - INFO - 当前日期 2025-06-01 有 4 条MySQL数据需要处理
2025-06-01 18:31:06,964 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 18:31:06,964 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-01 18:32:06,979 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 18:32:06,979 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 18:32:06,979 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 18:32:07,072 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 528 条记录
2025-06-01 18:32:07,072 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 18:32:07,072 - INFO - 开始处理日期: 2025-05-31
2025-06-01 18:32:07,072 - INFO - Request Parameters - Page 1:
2025-06-01 18:32:07,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:32:07,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:32:07,901 - INFO - Response - Page 1:
2025-06-01 18:32:07,901 - INFO - 第 1 页获取到 100 条记录
2025-06-01 18:32:08,104 - INFO - Request Parameters - Page 2:
2025-06-01 18:32:08,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:32:08,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:32:08,916 - INFO - Response - Page 2:
2025-06-01 18:32:08,916 - INFO - 第 2 页获取到 100 条记录
2025-06-01 18:32:09,119 - INFO - Request Parameters - Page 3:
2025-06-01 18:32:09,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:32:09,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:32:09,994 - INFO - Response - Page 3:
2025-06-01 18:32:09,994 - INFO - 第 3 页获取到 100 条记录
2025-06-01 18:32:10,197 - INFO - Request Parameters - Page 4:
2025-06-01 18:32:10,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:32:10,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:32:11,057 - INFO - Response - Page 4:
2025-06-01 18:32:11,057 - INFO - 第 4 页获取到 100 条记录
2025-06-01 18:32:11,260 - INFO - Request Parameters - Page 5:
2025-06-01 18:32:11,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:32:11,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:32:12,104 - INFO - Response - Page 5:
2025-06-01 18:32:12,104 - INFO - 第 5 页获取到 100 条记录
2025-06-01 18:32:12,307 - INFO - Request Parameters - Page 6:
2025-06-01 18:32:12,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:32:12,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:32:12,963 - INFO - Response - Page 6:
2025-06-01 18:32:12,963 - INFO - 第 6 页获取到 24 条记录
2025-06-01 18:32:13,166 - INFO - 查询完成，共获取到 524 条记录
2025-06-01 18:32:13,166 - INFO - 获取到 524 条表单数据
2025-06-01 18:32:13,166 - INFO - 当前日期 2025-05-31 有 524 条MySQL数据需要处理
2025-06-01 18:32:13,182 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 18:32:13,182 - INFO - 开始处理日期: 2025-06-01
2025-06-01 18:32:13,182 - INFO - Request Parameters - Page 1:
2025-06-01 18:32:13,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:32:13,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:32:13,760 - INFO - Response - Page 1:
2025-06-01 18:32:13,760 - INFO - 第 1 页获取到 4 条记录
2025-06-01 18:32:13,963 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 18:32:13,963 - INFO - 获取到 4 条表单数据
2025-06-01 18:32:13,963 - INFO - 当前日期 2025-06-01 有 4 条MySQL数据需要处理
2025-06-01 18:32:13,963 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 18:32:13,963 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 18:32:13,963 - INFO - 同步完成
2025-06-01 19:30:33,670 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 19:30:33,670 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 19:30:33,670 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 19:30:33,749 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 206 条记录
2025-06-01 19:30:33,749 - INFO - 获取到 5 个日期需要处理: ['2025-05-02', '2025-05-12', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 19:30:33,749 - INFO - 开始处理日期: 2025-05-02
2025-06-01 19:30:33,749 - INFO - Request Parameters - Page 1:
2025-06-01 19:30:33,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:30:33,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:30:41,889 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0D9C8E7E-429E-7EB8-A34B-462A7E074E78 Response: {'code': 'ServiceUnavailable', 'requestid': '0D9C8E7E-429E-7EB8-A34B-462A7E074E78', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0D9C8E7E-429E-7EB8-A34B-462A7E074E78)
2025-06-01 19:30:41,889 - INFO - 开始处理日期: 2025-05-12
2025-06-01 19:30:41,889 - INFO - Request Parameters - Page 1:
2025-06-01 19:30:41,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:30:41,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:30:50,014 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C5AD64FB-A771-7783-A11A-5EEEDA056393 Response: {'code': 'ServiceUnavailable', 'requestid': 'C5AD64FB-A771-7783-A11A-5EEEDA056393', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C5AD64FB-A771-7783-A11A-5EEEDA056393)
2025-06-01 19:30:50,014 - INFO - 开始处理日期: 2025-05-30
2025-06-01 19:30:50,014 - INFO - Request Parameters - Page 1:
2025-06-01 19:30:50,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:30:50,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:30:57,811 - INFO - Response - Page 1:
2025-06-01 19:30:57,811 - INFO - 第 1 页获取到 100 条记录
2025-06-01 19:30:58,014 - INFO - Request Parameters - Page 2:
2025-06-01 19:30:58,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:30:58,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:30:58,889 - INFO - Response - Page 2:
2025-06-01 19:30:58,889 - INFO - 第 2 页获取到 100 条记录
2025-06-01 19:30:59,092 - INFO - Request Parameters - Page 3:
2025-06-01 19:30:59,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:30:59,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:30:59,952 - INFO - Response - Page 3:
2025-06-01 19:30:59,952 - INFO - 第 3 页获取到 100 条记录
2025-06-01 19:31:00,155 - INFO - Request Parameters - Page 4:
2025-06-01 19:31:00,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:31:00,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:31:00,967 - INFO - Response - Page 4:
2025-06-01 19:31:00,967 - INFO - 第 4 页获取到 100 条记录
2025-06-01 19:31:01,170 - INFO - Request Parameters - Page 5:
2025-06-01 19:31:01,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:31:01,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:31:02,092 - INFO - Response - Page 5:
2025-06-01 19:31:02,092 - INFO - 第 5 页获取到 100 条记录
2025-06-01 19:31:02,295 - INFO - Request Parameters - Page 6:
2025-06-01 19:31:02,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:31:02,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:31:02,905 - INFO - Response - Page 6:
2025-06-01 19:31:02,905 - INFO - 第 6 页获取到 33 条记录
2025-06-01 19:31:03,108 - INFO - 查询完成，共获取到 533 条记录
2025-06-01 19:31:03,108 - INFO - 获取到 533 条表单数据
2025-06-01 19:31:03,108 - INFO - 当前日期 2025-05-30 有 2 条MySQL数据需要处理
2025-06-01 19:31:03,108 - INFO - 开始更新记录 - 表单实例ID: FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMX9
2025-06-01 19:31:03,514 - INFO - 更新表单数据成功: FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMX9
2025-06-01 19:31:03,514 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13580.0, 'new_value': 7249.0}, {'field': 'total_amount', 'old_value': 13580.0, 'new_value': 7249.0}]
2025-06-01 19:31:03,514 - INFO - 开始批量插入 1 条新记录
2025-06-01 19:31:03,670 - INFO - 批量插入响应状态码: 200
2025-06-01 19:31:03,670 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 11:31:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '325E4BE9-CB3E-7C65-9623-003793D06A17', 'x-acs-trace-id': '0ff718a94c5c99c4dedf9628720a4312', 'etag': '6TK8PRqLwaHpeKOxYVulkdg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 19:31:03,670 - INFO - 批量插入响应体: {'result': ['FINST-F3G66Q61OCTV43HS9KWQKCLUY8PG37W6YKDBMNI']}
2025-06-01 19:31:03,670 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-01 19:31:03,670 - INFO - 成功插入的数据ID: ['FINST-F3G66Q61OCTV43HS9KWQKCLUY8PG37W6YKDBMNI']
2025-06-01 19:31:08,686 - INFO - 批量插入完成，共 1 条记录
2025-06-01 19:31:08,686 - INFO - 日期 2025-05-30 处理完成 - 更新: 1 条，插入: 1 条，错误: 0 条
2025-06-01 19:31:08,686 - INFO - 开始处理日期: 2025-05-31
2025-06-01 19:31:08,686 - INFO - Request Parameters - Page 1:
2025-06-01 19:31:08,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:31:08,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:31:09,530 - INFO - Response - Page 1:
2025-06-01 19:31:09,530 - INFO - 第 1 页获取到 100 条记录
2025-06-01 19:31:09,733 - INFO - Request Parameters - Page 2:
2025-06-01 19:31:09,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:31:09,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:31:10,592 - INFO - Response - Page 2:
2025-06-01 19:31:10,592 - INFO - 第 2 页获取到 100 条记录
2025-06-01 19:31:10,795 - INFO - Request Parameters - Page 3:
2025-06-01 19:31:10,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:31:10,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:31:11,608 - INFO - Response - Page 3:
2025-06-01 19:31:11,608 - INFO - 第 3 页获取到 100 条记录
2025-06-01 19:31:11,811 - INFO - Request Parameters - Page 4:
2025-06-01 19:31:11,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:31:11,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:31:12,628 - INFO - Response - Page 4:
2025-06-01 19:31:12,628 - INFO - 第 4 页获取到 100 条记录
2025-06-01 19:31:12,831 - INFO - Request Parameters - Page 5:
2025-06-01 19:31:12,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:31:12,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:31:13,691 - INFO - Response - Page 5:
2025-06-01 19:31:13,691 - INFO - 第 5 页获取到 100 条记录
2025-06-01 19:31:13,894 - INFO - Request Parameters - Page 6:
2025-06-01 19:31:13,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:31:13,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:31:14,597 - INFO - Response - Page 6:
2025-06-01 19:31:14,597 - INFO - 第 6 页获取到 24 条记录
2025-06-01 19:31:14,800 - INFO - 查询完成，共获取到 524 条记录
2025-06-01 19:31:14,800 - INFO - 获取到 524 条表单数据
2025-06-01 19:31:14,800 - INFO - 当前日期 2025-05-31 有 197 条MySQL数据需要处理
2025-06-01 19:31:14,800 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 19:31:14,800 - INFO - 开始处理日期: 2025-06-01
2025-06-01 19:31:14,800 - INFO - Request Parameters - Page 1:
2025-06-01 19:31:14,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:31:14,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:31:15,378 - INFO - Response - Page 1:
2025-06-01 19:31:15,394 - INFO - 第 1 页获取到 4 条记录
2025-06-01 19:31:15,597 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 19:31:15,597 - INFO - 获取到 4 条表单数据
2025-06-01 19:31:15,597 - INFO - 当前日期 2025-06-01 有 4 条MySQL数据需要处理
2025-06-01 19:31:15,597 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 19:31:15,597 - INFO - 数据同步完成！更新: 1 条，插入: 1 条，错误: 2 条
2025-06-01 19:32:15,612 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 19:32:15,612 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 19:32:15,612 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 19:32:15,690 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 528 条记录
2025-06-01 19:32:15,690 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 19:32:15,690 - INFO - 开始处理日期: 2025-05-31
2025-06-01 19:32:15,690 - INFO - Request Parameters - Page 1:
2025-06-01 19:32:15,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:32:15,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:32:16,597 - INFO - Response - Page 1:
2025-06-01 19:32:16,597 - INFO - 第 1 页获取到 100 条记录
2025-06-01 19:32:16,800 - INFO - Request Parameters - Page 2:
2025-06-01 19:32:16,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:32:16,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:32:17,675 - INFO - Response - Page 2:
2025-06-01 19:32:17,675 - INFO - 第 2 页获取到 100 条记录
2025-06-01 19:32:17,878 - INFO - Request Parameters - Page 3:
2025-06-01 19:32:17,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:32:17,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:32:18,690 - INFO - Response - Page 3:
2025-06-01 19:32:18,690 - INFO - 第 3 页获取到 100 条记录
2025-06-01 19:32:18,893 - INFO - Request Parameters - Page 4:
2025-06-01 19:32:18,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:32:18,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:32:19,675 - INFO - Response - Page 4:
2025-06-01 19:32:19,675 - INFO - 第 4 页获取到 100 条记录
2025-06-01 19:32:19,878 - INFO - Request Parameters - Page 5:
2025-06-01 19:32:19,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:32:19,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:32:20,690 - INFO - Response - Page 5:
2025-06-01 19:32:20,690 - INFO - 第 5 页获取到 100 条记录
2025-06-01 19:32:20,893 - INFO - Request Parameters - Page 6:
2025-06-01 19:32:20,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:32:20,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:32:21,565 - INFO - Response - Page 6:
2025-06-01 19:32:21,565 - INFO - 第 6 页获取到 24 条记录
2025-06-01 19:32:21,768 - INFO - 查询完成，共获取到 524 条记录
2025-06-01 19:32:21,768 - INFO - 获取到 524 条表单数据
2025-06-01 19:32:21,768 - INFO - 当前日期 2025-05-31 有 524 条MySQL数据需要处理
2025-06-01 19:32:21,784 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 19:32:21,784 - INFO - 开始处理日期: 2025-06-01
2025-06-01 19:32:21,784 - INFO - Request Parameters - Page 1:
2025-06-01 19:32:21,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 19:32:21,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 19:32:22,346 - INFO - Response - Page 1:
2025-06-01 19:32:22,346 - INFO - 第 1 页获取到 4 条记录
2025-06-01 19:32:22,550 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 19:32:22,550 - INFO - 获取到 4 条表单数据
2025-06-01 19:32:22,550 - INFO - 当前日期 2025-06-01 有 4 条MySQL数据需要处理
2025-06-01 19:32:22,550 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 19:32:22,550 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 19:32:22,550 - INFO - 同步完成
2025-06-01 20:30:33,576 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 20:30:33,576 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 20:30:33,576 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 20:30:33,654 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 211 条记录
2025-06-01 20:30:33,654 - INFO - 获取到 9 个日期需要处理: ['2025-05-02', '2025-05-12', '2025-05-14', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 20:30:33,654 - INFO - 开始处理日期: 2025-05-02
2025-06-01 20:30:33,654 - INFO - Request Parameters - Page 1:
2025-06-01 20:30:33,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:30:33,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:30:41,764 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 512BF61E-1756-76FF-BA8A-85E68619CE15 Response: {'code': 'ServiceUnavailable', 'requestid': '512BF61E-1756-76FF-BA8A-85E68619CE15', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 512BF61E-1756-76FF-BA8A-85E68619CE15)
2025-06-01 20:30:41,764 - INFO - 开始处理日期: 2025-05-12
2025-06-01 20:30:41,764 - INFO - Request Parameters - Page 1:
2025-06-01 20:30:41,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:30:41,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:30:49,873 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1ABBEB9E-3040-7267-939D-9220CF4B7AF7 Response: {'code': 'ServiceUnavailable', 'requestid': '1ABBEB9E-3040-7267-939D-9220CF4B7AF7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1ABBEB9E-3040-7267-939D-9220CF4B7AF7)
2025-06-01 20:30:49,873 - INFO - 开始处理日期: 2025-05-14
2025-06-01 20:30:49,873 - INFO - Request Parameters - Page 1:
2025-06-01 20:30:49,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:30:49,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:30:57,982 - ERROR - 处理日期 2025-05-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BB85B9BF-A1F7-7ADD-9E13-A94EC7F187D9 Response: {'code': 'ServiceUnavailable', 'requestid': 'BB85B9BF-A1F7-7ADD-9E13-A94EC7F187D9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BB85B9BF-A1F7-7ADD-9E13-A94EC7F187D9)
2025-06-01 20:30:57,982 - INFO - 开始处理日期: 2025-05-17
2025-06-01 20:30:57,982 - INFO - Request Parameters - Page 1:
2025-06-01 20:30:57,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:30:57,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:30:58,889 - INFO - Response - Page 1:
2025-06-01 20:30:58,889 - INFO - 第 1 页获取到 100 条记录
2025-06-01 20:30:59,092 - INFO - Request Parameters - Page 2:
2025-06-01 20:30:59,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:30:59,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:00,529 - INFO - Response - Page 2:
2025-06-01 20:31:00,529 - INFO - 第 2 页获取到 100 条记录
2025-06-01 20:31:00,732 - INFO - Request Parameters - Page 3:
2025-06-01 20:31:00,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:00,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:01,498 - INFO - Response - Page 3:
2025-06-01 20:31:01,498 - INFO - 第 3 页获取到 100 条记录
2025-06-01 20:31:01,701 - INFO - Request Parameters - Page 4:
2025-06-01 20:31:01,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:01,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:02,545 - INFO - Response - Page 4:
2025-06-01 20:31:02,545 - INFO - 第 4 页获取到 100 条记录
2025-06-01 20:31:02,748 - INFO - Request Parameters - Page 5:
2025-06-01 20:31:02,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:02,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:03,529 - INFO - Response - Page 5:
2025-06-01 20:31:03,529 - INFO - 第 5 页获取到 100 条记录
2025-06-01 20:31:03,732 - INFO - Request Parameters - Page 6:
2025-06-01 20:31:03,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:03,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:04,451 - INFO - Response - Page 6:
2025-06-01 20:31:04,451 - INFO - 第 6 页获取到 61 条记录
2025-06-01 20:31:04,654 - INFO - 查询完成，共获取到 561 条记录
2025-06-01 20:31:04,654 - INFO - 获取到 561 条表单数据
2025-06-01 20:31:04,654 - INFO - 当前日期 2025-05-17 有 1 条MySQL数据需要处理
2025-06-01 20:31:04,654 - INFO - 开始更新记录 - 表单实例ID: FINST-BD766BC1ESHVTE2RB2CG6DJWRBCL351UDZSAMPM
2025-06-01 20:31:05,092 - INFO - 更新表单数据成功: FINST-BD766BC1ESHVTE2RB2CG6DJWRBCL351UDZSAMPM
2025-06-01 20:31:05,092 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 3288.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3288.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-01 20:31:05,092 - INFO - 日期 2025-05-17 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-01 20:31:05,092 - INFO - 开始处理日期: 2025-05-18
2025-06-01 20:31:05,092 - INFO - Request Parameters - Page 1:
2025-06-01 20:31:05,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:05,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:05,967 - INFO - Response - Page 1:
2025-06-01 20:31:05,967 - INFO - 第 1 页获取到 100 条记录
2025-06-01 20:31:06,170 - INFO - Request Parameters - Page 2:
2025-06-01 20:31:06,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:06,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:07,029 - INFO - Response - Page 2:
2025-06-01 20:31:07,029 - INFO - 第 2 页获取到 100 条记录
2025-06-01 20:31:07,232 - INFO - Request Parameters - Page 3:
2025-06-01 20:31:07,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:07,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:08,107 - INFO - Response - Page 3:
2025-06-01 20:31:08,107 - INFO - 第 3 页获取到 100 条记录
2025-06-01 20:31:08,310 - INFO - Request Parameters - Page 4:
2025-06-01 20:31:08,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:08,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:09,217 - INFO - Response - Page 4:
2025-06-01 20:31:09,217 - INFO - 第 4 页获取到 100 条记录
2025-06-01 20:31:09,420 - INFO - Request Parameters - Page 5:
2025-06-01 20:31:09,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:09,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:10,217 - INFO - Response - Page 5:
2025-06-01 20:31:10,217 - INFO - 第 5 页获取到 100 条记录
2025-06-01 20:31:10,420 - INFO - Request Parameters - Page 6:
2025-06-01 20:31:10,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:10,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:11,248 - INFO - Response - Page 6:
2025-06-01 20:31:11,248 - INFO - 第 6 页获取到 100 条记录
2025-06-01 20:31:11,451 - INFO - Request Parameters - Page 7:
2025-06-01 20:31:11,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:11,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:12,170 - INFO - Response - Page 7:
2025-06-01 20:31:12,170 - INFO - 第 7 页获取到 15 条记录
2025-06-01 20:31:12,373 - INFO - 查询完成，共获取到 615 条记录
2025-06-01 20:31:12,373 - INFO - 获取到 615 条表单数据
2025-06-01 20:31:12,373 - INFO - 当前日期 2025-05-18 有 1 条MySQL数据需要处理
2025-06-01 20:31:12,373 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381O1KV1JBCE1V4JAY2QXIS2WCWXGUAMV1
2025-06-01 20:31:12,951 - INFO - 更新表单数据成功: FINST-OJ966381O1KV1JBCE1V4JAY2QXIS2WCWXGUAMV1
2025-06-01 20:31:12,951 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 6076.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6076.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-06-01 20:31:12,951 - INFO - 日期 2025-05-18 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-01 20:31:12,951 - INFO - 开始处理日期: 2025-05-19
2025-06-01 20:31:12,951 - INFO - Request Parameters - Page 1:
2025-06-01 20:31:12,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:12,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:13,810 - INFO - Response - Page 1:
2025-06-01 20:31:13,810 - INFO - 第 1 页获取到 100 条记录
2025-06-01 20:31:14,013 - INFO - Request Parameters - Page 2:
2025-06-01 20:31:14,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:14,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:14,904 - INFO - Response - Page 2:
2025-06-01 20:31:14,904 - INFO - 第 2 页获取到 100 条记录
2025-06-01 20:31:15,107 - INFO - Request Parameters - Page 3:
2025-06-01 20:31:15,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:15,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:15,920 - INFO - Response - Page 3:
2025-06-01 20:31:15,920 - INFO - 第 3 页获取到 100 条记录
2025-06-01 20:31:16,123 - INFO - Request Parameters - Page 4:
2025-06-01 20:31:16,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:16,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:16,951 - INFO - Response - Page 4:
2025-06-01 20:31:16,951 - INFO - 第 4 页获取到 100 条记录
2025-06-01 20:31:17,154 - INFO - Request Parameters - Page 5:
2025-06-01 20:31:17,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:17,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:17,982 - INFO - Response - Page 5:
2025-06-01 20:31:17,982 - INFO - 第 5 页获取到 100 条记录
2025-06-01 20:31:18,185 - INFO - Request Parameters - Page 6:
2025-06-01 20:31:18,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:18,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:19,013 - INFO - Response - Page 6:
2025-06-01 20:31:19,013 - INFO - 第 6 页获取到 100 条记录
2025-06-01 20:31:19,216 - INFO - Request Parameters - Page 7:
2025-06-01 20:31:19,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:19,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:19,779 - INFO - Response - Page 7:
2025-06-01 20:31:19,779 - INFO - 第 7 页获取到 5 条记录
2025-06-01 20:31:19,982 - INFO - 查询完成，共获取到 605 条记录
2025-06-01 20:31:19,982 - INFO - 获取到 605 条表单数据
2025-06-01 20:31:19,982 - INFO - 当前日期 2025-05-19 有 1 条MySQL数据需要处理
2025-06-01 20:31:19,982 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMI
2025-06-01 20:31:20,435 - INFO - 更新表单数据成功: FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMI
2025-06-01 20:31:20,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9864.0, 'new_value': 0.0}, {'field': 'total_amount', 'old_value': 9864.0, 'new_value': 0.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 0}]
2025-06-01 20:31:20,435 - INFO - 日期 2025-05-19 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-01 20:31:20,435 - INFO - 开始处理日期: 2025-05-30
2025-06-01 20:31:20,435 - INFO - Request Parameters - Page 1:
2025-06-01 20:31:20,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:20,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:21,216 - INFO - Response - Page 1:
2025-06-01 20:31:21,216 - INFO - 第 1 页获取到 100 条记录
2025-06-01 20:31:21,420 - INFO - Request Parameters - Page 2:
2025-06-01 20:31:21,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:21,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:22,248 - INFO - Response - Page 2:
2025-06-01 20:31:22,248 - INFO - 第 2 页获取到 100 条记录
2025-06-01 20:31:22,451 - INFO - Request Parameters - Page 3:
2025-06-01 20:31:22,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:22,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:23,295 - INFO - Response - Page 3:
2025-06-01 20:31:23,295 - INFO - 第 3 页获取到 100 条记录
2025-06-01 20:31:23,498 - INFO - Request Parameters - Page 4:
2025-06-01 20:31:23,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:23,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:24,310 - INFO - Response - Page 4:
2025-06-01 20:31:24,310 - INFO - 第 4 页获取到 100 条记录
2025-06-01 20:31:24,513 - INFO - Request Parameters - Page 5:
2025-06-01 20:31:24,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:24,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:25,295 - INFO - Response - Page 5:
2025-06-01 20:31:25,295 - INFO - 第 5 页获取到 100 条记录
2025-06-01 20:31:25,498 - INFO - Request Parameters - Page 6:
2025-06-01 20:31:25,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:25,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:26,201 - INFO - Response - Page 6:
2025-06-01 20:31:26,201 - INFO - 第 6 页获取到 34 条记录
2025-06-01 20:31:26,404 - INFO - 查询完成，共获取到 534 条记录
2025-06-01 20:31:26,404 - INFO - 获取到 534 条表单数据
2025-06-01 20:31:26,404 - INFO - 当前日期 2025-05-30 有 2 条MySQL数据需要处理
2025-06-01 20:31:26,404 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 20:31:26,404 - INFO - 开始处理日期: 2025-05-31
2025-06-01 20:31:26,404 - INFO - Request Parameters - Page 1:
2025-06-01 20:31:26,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:26,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:27,232 - INFO - Response - Page 1:
2025-06-01 20:31:27,232 - INFO - 第 1 页获取到 100 条记录
2025-06-01 20:31:27,435 - INFO - Request Parameters - Page 2:
2025-06-01 20:31:27,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:27,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:28,216 - INFO - Response - Page 2:
2025-06-01 20:31:28,216 - INFO - 第 2 页获取到 100 条记录
2025-06-01 20:31:28,420 - INFO - Request Parameters - Page 3:
2025-06-01 20:31:28,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:28,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:29,201 - INFO - Response - Page 3:
2025-06-01 20:31:29,201 - INFO - 第 3 页获取到 100 条记录
2025-06-01 20:31:29,404 - INFO - Request Parameters - Page 4:
2025-06-01 20:31:29,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:29,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:30,279 - INFO - Response - Page 4:
2025-06-01 20:31:30,279 - INFO - 第 4 页获取到 100 条记录
2025-06-01 20:31:30,482 - INFO - Request Parameters - Page 5:
2025-06-01 20:31:30,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:30,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:31,326 - INFO - Response - Page 5:
2025-06-01 20:31:31,326 - INFO - 第 5 页获取到 100 条记录
2025-06-01 20:31:31,529 - INFO - Request Parameters - Page 6:
2025-06-01 20:31:31,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:31,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:32,154 - INFO - Response - Page 6:
2025-06-01 20:31:32,154 - INFO - 第 6 页获取到 24 条记录
2025-06-01 20:31:32,357 - INFO - 查询完成，共获取到 524 条记录
2025-06-01 20:31:32,357 - INFO - 获取到 524 条表单数据
2025-06-01 20:31:32,357 - INFO - 当前日期 2025-05-31 有 197 条MySQL数据需要处理
2025-06-01 20:31:32,357 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 20:31:32,357 - INFO - 开始处理日期: 2025-06-01
2025-06-01 20:31:32,357 - INFO - Request Parameters - Page 1:
2025-06-01 20:31:32,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:31:32,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:31:32,951 - INFO - Response - Page 1:
2025-06-01 20:31:32,951 - INFO - 第 1 页获取到 4 条记录
2025-06-01 20:31:33,154 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 20:31:33,154 - INFO - 获取到 4 条表单数据
2025-06-01 20:31:33,154 - INFO - 当前日期 2025-06-01 有 4 条MySQL数据需要处理
2025-06-01 20:31:33,154 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 20:31:33,154 - INFO - 数据同步完成！更新: 3 条，插入: 0 条，错误: 3 条
2025-06-01 20:32:33,169 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 20:32:33,169 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 20:32:33,169 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 20:32:33,247 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 528 条记录
2025-06-01 20:32:33,247 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 20:32:33,247 - INFO - 开始处理日期: 2025-05-31
2025-06-01 20:32:33,247 - INFO - Request Parameters - Page 1:
2025-06-01 20:32:33,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:32:33,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:32:34,138 - INFO - Response - Page 1:
2025-06-01 20:32:34,138 - INFO - 第 1 页获取到 100 条记录
2025-06-01 20:32:34,341 - INFO - Request Parameters - Page 2:
2025-06-01 20:32:34,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:32:34,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:32:35,325 - INFO - Response - Page 2:
2025-06-01 20:32:35,325 - INFO - 第 2 页获取到 100 条记录
2025-06-01 20:32:35,528 - INFO - Request Parameters - Page 3:
2025-06-01 20:32:35,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:32:35,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:32:36,388 - INFO - Response - Page 3:
2025-06-01 20:32:36,388 - INFO - 第 3 页获取到 100 条记录
2025-06-01 20:32:36,591 - INFO - Request Parameters - Page 4:
2025-06-01 20:32:36,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:32:36,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:32:37,372 - INFO - Response - Page 4:
2025-06-01 20:32:37,372 - INFO - 第 4 页获取到 100 条记录
2025-06-01 20:32:37,575 - INFO - Request Parameters - Page 5:
2025-06-01 20:32:37,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:32:37,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:32:38,466 - INFO - Response - Page 5:
2025-06-01 20:32:38,466 - INFO - 第 5 页获取到 100 条记录
2025-06-01 20:32:38,669 - INFO - Request Parameters - Page 6:
2025-06-01 20:32:38,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:32:38,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:32:39,325 - INFO - Response - Page 6:
2025-06-01 20:32:39,325 - INFO - 第 6 页获取到 24 条记录
2025-06-01 20:32:39,528 - INFO - 查询完成，共获取到 524 条记录
2025-06-01 20:32:39,528 - INFO - 获取到 524 条表单数据
2025-06-01 20:32:39,528 - INFO - 当前日期 2025-05-31 有 524 条MySQL数据需要处理
2025-06-01 20:32:39,544 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 20:32:39,544 - INFO - 开始处理日期: 2025-06-01
2025-06-01 20:32:39,544 - INFO - Request Parameters - Page 1:
2025-06-01 20:32:39,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 20:32:39,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 20:32:40,091 - INFO - Response - Page 1:
2025-06-01 20:32:40,091 - INFO - 第 1 页获取到 4 条记录
2025-06-01 20:32:40,294 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 20:32:40,294 - INFO - 获取到 4 条表单数据
2025-06-01 20:32:40,294 - INFO - 当前日期 2025-06-01 有 4 条MySQL数据需要处理
2025-06-01 20:32:40,294 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 20:32:40,294 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 20:32:40,294 - INFO - 同步完成
2025-06-01 21:30:33,577 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 21:30:33,577 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 21:30:33,577 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 21:30:33,655 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 212 条记录
2025-06-01 21:30:33,655 - INFO - 获取到 9 个日期需要处理: ['2025-05-02', '2025-05-12', '2025-05-14', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 21:30:33,655 - INFO - 开始处理日期: 2025-05-02
2025-06-01 21:30:33,670 - INFO - Request Parameters - Page 1:
2025-06-01 21:30:33,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:30:33,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:30:41,780 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-F19C-7A35-976B-2E291364107A Response: {'code': 'ServiceUnavailable', 'requestid': '********-F19C-7A35-976B-2E291364107A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-F19C-7A35-976B-2E291364107A)
2025-06-01 21:30:41,780 - INFO - 开始处理日期: 2025-05-12
2025-06-01 21:30:41,780 - INFO - Request Parameters - Page 1:
2025-06-01 21:30:41,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:30:41,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:30:49,889 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 73CC3432-CD4E-73A8-9AB9-A5E475F7C69D Response: {'code': 'ServiceUnavailable', 'requestid': '73CC3432-CD4E-73A8-9AB9-A5E475F7C69D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 73CC3432-CD4E-73A8-9AB9-A5E475F7C69D)
2025-06-01 21:30:49,889 - INFO - 开始处理日期: 2025-05-14
2025-06-01 21:30:49,905 - INFO - Request Parameters - Page 1:
2025-06-01 21:30:49,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:30:49,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:30:50,826 - INFO - Response - Page 1:
2025-06-01 21:30:50,826 - INFO - 第 1 页获取到 100 条记录
2025-06-01 21:30:51,030 - INFO - Request Parameters - Page 2:
2025-06-01 21:30:51,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:30:51,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:30:52,655 - INFO - Response - Page 2:
2025-06-01 21:30:52,655 - INFO - 第 2 页获取到 100 条记录
2025-06-01 21:30:52,858 - INFO - Request Parameters - Page 3:
2025-06-01 21:30:52,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:30:52,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:30:53,670 - INFO - Response - Page 3:
2025-06-01 21:30:53,670 - INFO - 第 3 页获取到 100 条记录
2025-06-01 21:30:53,873 - INFO - Request Parameters - Page 4:
2025-06-01 21:30:53,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:30:53,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:30:54,623 - INFO - Response - Page 4:
2025-06-01 21:30:54,623 - INFO - 第 4 页获取到 100 条记录
2025-06-01 21:30:54,826 - INFO - Request Parameters - Page 5:
2025-06-01 21:30:54,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:30:54,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:30:55,748 - INFO - Response - Page 5:
2025-06-01 21:30:55,748 - INFO - 第 5 页获取到 100 条记录
2025-06-01 21:30:55,951 - INFO - Request Parameters - Page 6:
2025-06-01 21:30:55,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:30:55,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:30:56,717 - INFO - Response - Page 6:
2025-06-01 21:30:56,717 - INFO - 第 6 页获取到 100 条记录
2025-06-01 21:30:56,920 - INFO - Request Parameters - Page 7:
2025-06-01 21:30:56,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:30:56,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:30:57,451 - INFO - Response - Page 7:
2025-06-01 21:30:57,451 - INFO - 第 7 页获取到 1 条记录
2025-06-01 21:30:57,655 - INFO - 查询完成，共获取到 601 条记录
2025-06-01 21:30:57,655 - INFO - 获取到 601 条表单数据
2025-06-01 21:30:57,655 - INFO - 当前日期 2025-05-14 有 1 条MySQL数据需要处理
2025-06-01 21:30:57,655 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC11AFV7HSM9KXHRDQ1TJZS2W9N5ROAMF2
2025-06-01 21:30:58,092 - INFO - 更新表单数据成功: FINST-KLF66WC11AFV7HSM9KXHRDQ1TJZS2W9N5ROAMF2
2025-06-01 21:30:58,092 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3288.0, 'new_value': 1688.0}, {'field': 'total_amount', 'old_value': 3288.0, 'new_value': 1688.0}]
2025-06-01 21:30:58,092 - INFO - 日期 2025-05-14 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-01 21:30:58,092 - INFO - 开始处理日期: 2025-05-17
2025-06-01 21:30:58,092 - INFO - Request Parameters - Page 1:
2025-06-01 21:30:58,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:30:58,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:30:58,889 - INFO - Response - Page 1:
2025-06-01 21:30:58,889 - INFO - 第 1 页获取到 100 条记录
2025-06-01 21:30:59,092 - INFO - Request Parameters - Page 2:
2025-06-01 21:30:59,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:30:59,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:30:59,889 - INFO - Response - Page 2:
2025-06-01 21:30:59,889 - INFO - 第 2 页获取到 100 条记录
2025-06-01 21:31:00,092 - INFO - Request Parameters - Page 3:
2025-06-01 21:31:00,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:00,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:00,951 - INFO - Response - Page 3:
2025-06-01 21:31:00,951 - INFO - 第 3 页获取到 100 条记录
2025-06-01 21:31:01,155 - INFO - Request Parameters - Page 4:
2025-06-01 21:31:01,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:01,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:01,951 - INFO - Response - Page 4:
2025-06-01 21:31:01,951 - INFO - 第 4 页获取到 100 条记录
2025-06-01 21:31:02,155 - INFO - Request Parameters - Page 5:
2025-06-01 21:31:02,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:02,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:02,936 - INFO - Response - Page 5:
2025-06-01 21:31:02,936 - INFO - 第 5 页获取到 100 条记录
2025-06-01 21:31:03,139 - INFO - Request Parameters - Page 6:
2025-06-01 21:31:03,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:03,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:03,889 - INFO - Response - Page 6:
2025-06-01 21:31:03,889 - INFO - 第 6 页获取到 61 条记录
2025-06-01 21:31:04,092 - INFO - 查询完成，共获取到 561 条记录
2025-06-01 21:31:04,092 - INFO - 获取到 561 条表单数据
2025-06-01 21:31:04,092 - INFO - 当前日期 2025-05-17 有 1 条MySQL数据需要处理
2025-06-01 21:31:04,092 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 21:31:04,092 - INFO - 开始处理日期: 2025-05-18
2025-06-01 21:31:04,092 - INFO - Request Parameters - Page 1:
2025-06-01 21:31:04,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:04,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:04,951 - INFO - Response - Page 1:
2025-06-01 21:31:04,951 - INFO - 第 1 页获取到 100 条记录
2025-06-01 21:31:05,155 - INFO - Request Parameters - Page 2:
2025-06-01 21:31:05,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:05,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:05,936 - INFO - Response - Page 2:
2025-06-01 21:31:05,936 - INFO - 第 2 页获取到 100 条记录
2025-06-01 21:31:06,139 - INFO - Request Parameters - Page 3:
2025-06-01 21:31:06,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:06,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:06,967 - INFO - Response - Page 3:
2025-06-01 21:31:06,967 - INFO - 第 3 页获取到 100 条记录
2025-06-01 21:31:07,170 - INFO - Request Parameters - Page 4:
2025-06-01 21:31:07,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:07,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:08,014 - INFO - Response - Page 4:
2025-06-01 21:31:08,014 - INFO - 第 4 页获取到 100 条记录
2025-06-01 21:31:08,217 - INFO - Request Parameters - Page 5:
2025-06-01 21:31:08,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:08,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:09,076 - INFO - Response - Page 5:
2025-06-01 21:31:09,076 - INFO - 第 5 页获取到 100 条记录
2025-06-01 21:31:09,279 - INFO - Request Parameters - Page 6:
2025-06-01 21:31:09,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:09,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:10,139 - INFO - Response - Page 6:
2025-06-01 21:31:10,139 - INFO - 第 6 页获取到 100 条记录
2025-06-01 21:31:10,342 - INFO - Request Parameters - Page 7:
2025-06-01 21:31:10,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:10,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:10,967 - INFO - Response - Page 7:
2025-06-01 21:31:10,967 - INFO - 第 7 页获取到 15 条记录
2025-06-01 21:31:11,170 - INFO - 查询完成，共获取到 615 条记录
2025-06-01 21:31:11,170 - INFO - 获取到 615 条表单数据
2025-06-01 21:31:11,170 - INFO - 当前日期 2025-05-18 有 1 条MySQL数据需要处理
2025-06-01 21:31:11,170 - INFO - 日期 2025-05-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 21:31:11,170 - INFO - 开始处理日期: 2025-05-19
2025-06-01 21:31:11,170 - INFO - Request Parameters - Page 1:
2025-06-01 21:31:11,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:11,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:12,139 - INFO - Response - Page 1:
2025-06-01 21:31:12,139 - INFO - 第 1 页获取到 100 条记录
2025-06-01 21:31:12,342 - INFO - Request Parameters - Page 2:
2025-06-01 21:31:12,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:12,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:13,154 - INFO - Response - Page 2:
2025-06-01 21:31:13,154 - INFO - 第 2 页获取到 100 条记录
2025-06-01 21:31:13,358 - INFO - Request Parameters - Page 3:
2025-06-01 21:31:13,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:13,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:14,170 - INFO - Response - Page 3:
2025-06-01 21:31:14,170 - INFO - 第 3 页获取到 100 条记录
2025-06-01 21:31:14,373 - INFO - Request Parameters - Page 4:
2025-06-01 21:31:14,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:14,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:15,201 - INFO - Response - Page 4:
2025-06-01 21:31:15,201 - INFO - 第 4 页获取到 100 条记录
2025-06-01 21:31:15,404 - INFO - Request Parameters - Page 5:
2025-06-01 21:31:15,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:15,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:16,186 - INFO - Response - Page 5:
2025-06-01 21:31:16,186 - INFO - 第 5 页获取到 100 条记录
2025-06-01 21:31:16,389 - INFO - Request Parameters - Page 6:
2025-06-01 21:31:16,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:16,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:17,295 - INFO - Response - Page 6:
2025-06-01 21:31:17,295 - INFO - 第 6 页获取到 100 条记录
2025-06-01 21:31:17,498 - INFO - Request Parameters - Page 7:
2025-06-01 21:31:17,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:17,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:18,092 - INFO - Response - Page 7:
2025-06-01 21:31:18,092 - INFO - 第 7 页获取到 5 条记录
2025-06-01 21:31:18,295 - INFO - 查询完成，共获取到 605 条记录
2025-06-01 21:31:18,295 - INFO - 获取到 605 条表单数据
2025-06-01 21:31:18,295 - INFO - 当前日期 2025-05-19 有 1 条MySQL数据需要处理
2025-06-01 21:31:18,295 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 21:31:18,295 - INFO - 开始处理日期: 2025-05-30
2025-06-01 21:31:18,295 - INFO - Request Parameters - Page 1:
2025-06-01 21:31:18,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:18,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:19,139 - INFO - Response - Page 1:
2025-06-01 21:31:19,139 - INFO - 第 1 页获取到 100 条记录
2025-06-01 21:31:19,342 - INFO - Request Parameters - Page 2:
2025-06-01 21:31:19,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:19,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:20,061 - INFO - Response - Page 2:
2025-06-01 21:31:20,061 - INFO - 第 2 页获取到 100 条记录
2025-06-01 21:31:20,264 - INFO - Request Parameters - Page 3:
2025-06-01 21:31:20,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:20,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:21,092 - INFO - Response - Page 3:
2025-06-01 21:31:21,092 - INFO - 第 3 页获取到 100 条记录
2025-06-01 21:31:21,295 - INFO - Request Parameters - Page 4:
2025-06-01 21:31:21,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:21,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:22,076 - INFO - Response - Page 4:
2025-06-01 21:31:22,076 - INFO - 第 4 页获取到 100 条记录
2025-06-01 21:31:22,279 - INFO - Request Parameters - Page 5:
2025-06-01 21:31:22,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:22,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:23,108 - INFO - Response - Page 5:
2025-06-01 21:31:23,108 - INFO - 第 5 页获取到 100 条记录
2025-06-01 21:31:23,311 - INFO - Request Parameters - Page 6:
2025-06-01 21:31:23,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:23,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:23,983 - INFO - Response - Page 6:
2025-06-01 21:31:23,983 - INFO - 第 6 页获取到 34 条记录
2025-06-01 21:31:24,186 - INFO - 查询完成，共获取到 534 条记录
2025-06-01 21:31:24,186 - INFO - 获取到 534 条表单数据
2025-06-01 21:31:24,186 - INFO - 当前日期 2025-05-30 有 2 条MySQL数据需要处理
2025-06-01 21:31:24,186 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 21:31:24,186 - INFO - 开始处理日期: 2025-05-31
2025-06-01 21:31:24,186 - INFO - Request Parameters - Page 1:
2025-06-01 21:31:24,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:24,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:25,045 - INFO - Response - Page 1:
2025-06-01 21:31:25,045 - INFO - 第 1 页获取到 100 条记录
2025-06-01 21:31:25,248 - INFO - Request Parameters - Page 2:
2025-06-01 21:31:25,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:25,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:26,139 - INFO - Response - Page 2:
2025-06-01 21:31:26,139 - INFO - 第 2 页获取到 100 条记录
2025-06-01 21:31:26,342 - INFO - Request Parameters - Page 3:
2025-06-01 21:31:26,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:26,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:27,139 - INFO - Response - Page 3:
2025-06-01 21:31:27,139 - INFO - 第 3 页获取到 100 条记录
2025-06-01 21:31:27,342 - INFO - Request Parameters - Page 4:
2025-06-01 21:31:27,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:27,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:28,139 - INFO - Response - Page 4:
2025-06-01 21:31:28,139 - INFO - 第 4 页获取到 100 条记录
2025-06-01 21:31:28,342 - INFO - Request Parameters - Page 5:
2025-06-01 21:31:28,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:28,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:29,311 - INFO - Response - Page 5:
2025-06-01 21:31:29,311 - INFO - 第 5 页获取到 100 条记录
2025-06-01 21:31:29,514 - INFO - Request Parameters - Page 6:
2025-06-01 21:31:29,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:29,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:30,139 - INFO - Response - Page 6:
2025-06-01 21:31:30,139 - INFO - 第 6 页获取到 24 条记录
2025-06-01 21:31:30,342 - INFO - 查询完成，共获取到 524 条记录
2025-06-01 21:31:30,342 - INFO - 获取到 524 条表单数据
2025-06-01 21:31:30,342 - INFO - 当前日期 2025-05-31 有 197 条MySQL数据需要处理
2025-06-01 21:31:30,357 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 21:31:30,357 - INFO - 开始处理日期: 2025-06-01
2025-06-01 21:31:30,357 - INFO - Request Parameters - Page 1:
2025-06-01 21:31:30,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:31:30,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:31:30,982 - INFO - Response - Page 1:
2025-06-01 21:31:30,982 - INFO - 第 1 页获取到 4 条记录
2025-06-01 21:31:31,186 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 21:31:31,186 - INFO - 获取到 4 条表单数据
2025-06-01 21:31:31,186 - INFO - 当前日期 2025-06-01 有 5 条MySQL数据需要处理
2025-06-01 21:31:31,186 - INFO - 开始批量插入 1 条新记录
2025-06-01 21:31:31,326 - INFO - 批量插入响应状态码: 200
2025-06-01 21:31:31,326 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 13:31:31 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4249D8B1-BF84-7CFC-BAC0-79C2D3359D32', 'x-acs-trace-id': 'da44d2d3c442ec5f7bdd93f61956c279', 'etag': '6OGzFGkkvyQOapX72r/6J5Q0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 21:31:31,326 - INFO - 批量插入响应体: {'result': ['FINST-HXD667B1U5VVJAMVADQ127U51M933MT39PDBMH6']}
2025-06-01 21:31:31,326 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-01 21:31:31,326 - INFO - 成功插入的数据ID: ['FINST-HXD667B1U5VVJAMVADQ127U51M933MT39PDBMH6']
2025-06-01 21:31:36,342 - INFO - 批量插入完成，共 1 条记录
2025-06-01 21:31:36,342 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-01 21:31:36,342 - INFO - 数据同步完成！更新: 1 条，插入: 1 条，错误: 2 条
2025-06-01 21:32:36,357 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 21:32:36,357 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 21:32:36,357 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 21:32:36,435 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 529 条记录
2025-06-01 21:32:36,435 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 21:32:36,435 - INFO - 开始处理日期: 2025-05-31
2025-06-01 21:32:36,435 - INFO - Request Parameters - Page 1:
2025-06-01 21:32:36,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:32:36,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:32:37,279 - INFO - Response - Page 1:
2025-06-01 21:32:37,279 - INFO - 第 1 页获取到 100 条记录
2025-06-01 21:32:37,482 - INFO - Request Parameters - Page 2:
2025-06-01 21:32:37,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:32:37,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:32:38,263 - INFO - Response - Page 2:
2025-06-01 21:32:38,263 - INFO - 第 2 页获取到 100 条记录
2025-06-01 21:32:38,466 - INFO - Request Parameters - Page 3:
2025-06-01 21:32:38,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:32:38,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:32:39,310 - INFO - Response - Page 3:
2025-06-01 21:32:39,310 - INFO - 第 3 页获取到 100 条记录
2025-06-01 21:32:39,513 - INFO - Request Parameters - Page 4:
2025-06-01 21:32:39,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:32:39,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:32:40,295 - INFO - Response - Page 4:
2025-06-01 21:32:40,295 - INFO - 第 4 页获取到 100 条记录
2025-06-01 21:32:40,498 - INFO - Request Parameters - Page 5:
2025-06-01 21:32:40,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:32:40,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:32:41,326 - INFO - Response - Page 5:
2025-06-01 21:32:41,326 - INFO - 第 5 页获取到 100 条记录
2025-06-01 21:32:41,529 - INFO - Request Parameters - Page 6:
2025-06-01 21:32:41,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:32:41,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:32:42,123 - INFO - Response - Page 6:
2025-06-01 21:32:42,123 - INFO - 第 6 页获取到 24 条记录
2025-06-01 21:32:42,326 - INFO - 查询完成，共获取到 524 条记录
2025-06-01 21:32:42,326 - INFO - 获取到 524 条表单数据
2025-06-01 21:32:42,326 - INFO - 当前日期 2025-05-31 有 524 条MySQL数据需要处理
2025-06-01 21:32:42,341 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 21:32:42,341 - INFO - 开始处理日期: 2025-06-01
2025-06-01 21:32:42,341 - INFO - Request Parameters - Page 1:
2025-06-01 21:32:42,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:32:42,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:32:42,935 - INFO - Response - Page 1:
2025-06-01 21:32:42,935 - INFO - 第 1 页获取到 5 条记录
2025-06-01 21:32:43,138 - INFO - 查询完成，共获取到 5 条记录
2025-06-01 21:32:43,138 - INFO - 获取到 5 条表单数据
2025-06-01 21:32:43,138 - INFO - 当前日期 2025-06-01 有 5 条MySQL数据需要处理
2025-06-01 21:32:43,138 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 21:32:43,138 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 21:32:43,138 - INFO - 同步完成
2025-06-01 22:30:33,579 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 22:30:33,579 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 22:30:33,580 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 22:30:33,647 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 255 条记录
2025-06-01 22:30:33,647 - INFO - 获取到 9 个日期需要处理: ['2025-05-02', '2025-05-12', '2025-05-14', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 22:30:33,647 - INFO - 开始处理日期: 2025-05-02
2025-06-01 22:30:33,663 - INFO - Request Parameters - Page 1:
2025-06-01 22:30:33,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:30:33,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:30:41,785 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 56E3BA61-3A5C-73AD-81E8-7A9EF126368D Response: {'code': 'ServiceUnavailable', 'requestid': '56E3BA61-3A5C-73AD-81E8-7A9EF126368D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 56E3BA61-3A5C-73AD-81E8-7A9EF126368D)
2025-06-01 22:30:41,785 - INFO - 开始处理日期: 2025-05-12
2025-06-01 22:30:41,785 - INFO - Request Parameters - Page 1:
2025-06-01 22:30:41,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:30:41,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:30:49,912 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EB88543D-DF81-7466-90B2-8ED3C1F54E71 Response: {'code': 'ServiceUnavailable', 'requestid': 'EB88543D-DF81-7466-90B2-8ED3C1F54E71', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EB88543D-DF81-7466-90B2-8ED3C1F54E71)
2025-06-01 22:30:49,912 - INFO - 开始处理日期: 2025-05-14
2025-06-01 22:30:49,912 - INFO - Request Parameters - Page 1:
2025-06-01 22:30:49,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:30:49,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:30:50,860 - INFO - Response - Page 1:
2025-06-01 22:30:50,860 - INFO - 第 1 页获取到 100 条记录
2025-06-01 22:30:51,060 - INFO - Request Parameters - Page 2:
2025-06-01 22:30:51,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:30:51,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:30:57,472 - INFO - Response - Page 2:
2025-06-01 22:30:57,473 - INFO - 第 2 页获取到 100 条记录
2025-06-01 22:30:57,674 - INFO - Request Parameters - Page 3:
2025-06-01 22:30:57,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:30:57,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:30:58,507 - INFO - Response - Page 3:
2025-06-01 22:30:58,507 - INFO - 第 3 页获取到 100 条记录
2025-06-01 22:30:58,709 - INFO - Request Parameters - Page 4:
2025-06-01 22:30:58,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:30:58,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:30:59,479 - INFO - Response - Page 4:
2025-06-01 22:30:59,480 - INFO - 第 4 页获取到 100 条记录
2025-06-01 22:30:59,680 - INFO - Request Parameters - Page 5:
2025-06-01 22:30:59,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:30:59,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:00,470 - INFO - Response - Page 5:
2025-06-01 22:31:00,470 - INFO - 第 5 页获取到 100 条记录
2025-06-01 22:31:00,671 - INFO - Request Parameters - Page 6:
2025-06-01 22:31:00,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:00,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:01,507 - INFO - Response - Page 6:
2025-06-01 22:31:01,508 - INFO - 第 6 页获取到 100 条记录
2025-06-01 22:31:01,709 - INFO - Request Parameters - Page 7:
2025-06-01 22:31:01,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:01,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:02,268 - INFO - Response - Page 7:
2025-06-01 22:31:02,268 - INFO - 第 7 页获取到 1 条记录
2025-06-01 22:31:02,468 - INFO - 查询完成，共获取到 601 条记录
2025-06-01 22:31:02,468 - INFO - 获取到 601 条表单数据
2025-06-01 22:31:02,478 - INFO - 当前日期 2025-05-14 有 1 条MySQL数据需要处理
2025-06-01 22:31:02,478 - INFO - 日期 2025-05-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 22:31:02,478 - INFO - 开始处理日期: 2025-05-17
2025-06-01 22:31:02,478 - INFO - Request Parameters - Page 1:
2025-06-01 22:31:02,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:02,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:03,372 - INFO - Response - Page 1:
2025-06-01 22:31:03,372 - INFO - 第 1 页获取到 100 条记录
2025-06-01 22:31:03,572 - INFO - Request Parameters - Page 2:
2025-06-01 22:31:03,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:03,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:04,434 - INFO - Response - Page 2:
2025-06-01 22:31:04,434 - INFO - 第 2 页获取到 100 条记录
2025-06-01 22:31:04,634 - INFO - Request Parameters - Page 3:
2025-06-01 22:31:04,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:04,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:05,489 - INFO - Response - Page 3:
2025-06-01 22:31:05,489 - INFO - 第 3 页获取到 100 条记录
2025-06-01 22:31:05,689 - INFO - Request Parameters - Page 4:
2025-06-01 22:31:05,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:05,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:06,533 - INFO - Response - Page 4:
2025-06-01 22:31:06,533 - INFO - 第 4 页获取到 100 条记录
2025-06-01 22:31:06,733 - INFO - Request Parameters - Page 5:
2025-06-01 22:31:06,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:06,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:07,520 - INFO - Response - Page 5:
2025-06-01 22:31:07,520 - INFO - 第 5 页获取到 100 条记录
2025-06-01 22:31:07,723 - INFO - Request Parameters - Page 6:
2025-06-01 22:31:07,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:07,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:08,510 - INFO - Response - Page 6:
2025-06-01 22:31:08,510 - INFO - 第 6 页获取到 61 条记录
2025-06-01 22:31:08,711 - INFO - 查询完成，共获取到 561 条记录
2025-06-01 22:31:08,711 - INFO - 获取到 561 条表单数据
2025-06-01 22:31:08,720 - INFO - 当前日期 2025-05-17 有 1 条MySQL数据需要处理
2025-06-01 22:31:08,720 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 22:31:08,720 - INFO - 开始处理日期: 2025-05-18
2025-06-01 22:31:08,720 - INFO - Request Parameters - Page 1:
2025-06-01 22:31:08,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:08,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:09,561 - INFO - Response - Page 1:
2025-06-01 22:31:09,561 - INFO - 第 1 页获取到 100 条记录
2025-06-01 22:31:09,761 - INFO - Request Parameters - Page 2:
2025-06-01 22:31:09,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:09,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:10,693 - INFO - Response - Page 2:
2025-06-01 22:31:10,693 - INFO - 第 2 页获取到 100 条记录
2025-06-01 22:31:10,893 - INFO - Request Parameters - Page 3:
2025-06-01 22:31:10,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:10,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:11,659 - INFO - Response - Page 3:
2025-06-01 22:31:11,659 - INFO - 第 3 页获取到 100 条记录
2025-06-01 22:31:11,859 - INFO - Request Parameters - Page 4:
2025-06-01 22:31:11,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:11,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:12,802 - INFO - Response - Page 4:
2025-06-01 22:31:12,803 - INFO - 第 4 页获取到 100 条记录
2025-06-01 22:31:13,003 - INFO - Request Parameters - Page 5:
2025-06-01 22:31:13,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:13,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:13,792 - INFO - Response - Page 5:
2025-06-01 22:31:13,793 - INFO - 第 5 页获取到 100 条记录
2025-06-01 22:31:13,993 - INFO - Request Parameters - Page 6:
2025-06-01 22:31:13,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:13,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:14,919 - INFO - Response - Page 6:
2025-06-01 22:31:14,919 - INFO - 第 6 页获取到 100 条记录
2025-06-01 22:31:15,120 - INFO - Request Parameters - Page 7:
2025-06-01 22:31:15,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:15,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:15,716 - INFO - Response - Page 7:
2025-06-01 22:31:15,716 - INFO - 第 7 页获取到 15 条记录
2025-06-01 22:31:15,917 - INFO - 查询完成，共获取到 615 条记录
2025-06-01 22:31:15,917 - INFO - 获取到 615 条表单数据
2025-06-01 22:31:15,927 - INFO - 当前日期 2025-05-18 有 1 条MySQL数据需要处理
2025-06-01 22:31:15,927 - INFO - 日期 2025-05-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 22:31:15,927 - INFO - 开始处理日期: 2025-05-19
2025-06-01 22:31:15,927 - INFO - Request Parameters - Page 1:
2025-06-01 22:31:15,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:15,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:16,749 - INFO - Response - Page 1:
2025-06-01 22:31:16,749 - INFO - 第 1 页获取到 100 条记录
2025-06-01 22:31:16,949 - INFO - Request Parameters - Page 2:
2025-06-01 22:31:16,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:16,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:17,739 - INFO - Response - Page 2:
2025-06-01 22:31:17,739 - INFO - 第 2 页获取到 100 条记录
2025-06-01 22:31:17,939 - INFO - Request Parameters - Page 3:
2025-06-01 22:31:17,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:17,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:18,731 - INFO - Response - Page 3:
2025-06-01 22:31:18,732 - INFO - 第 3 页获取到 100 条记录
2025-06-01 22:31:18,932 - INFO - Request Parameters - Page 4:
2025-06-01 22:31:18,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:18,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:19,807 - INFO - Response - Page 4:
2025-06-01 22:31:19,807 - INFO - 第 4 页获取到 100 条记录
2025-06-01 22:31:20,007 - INFO - Request Parameters - Page 5:
2025-06-01 22:31:20,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:20,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:20,836 - INFO - Response - Page 5:
2025-06-01 22:31:20,836 - INFO - 第 5 页获取到 100 条记录
2025-06-01 22:31:21,037 - INFO - Request Parameters - Page 6:
2025-06-01 22:31:21,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:21,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:21,796 - INFO - Response - Page 6:
2025-06-01 22:31:21,797 - INFO - 第 6 页获取到 100 条记录
2025-06-01 22:31:21,998 - INFO - Request Parameters - Page 7:
2025-06-01 22:31:21,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:21,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:22,591 - INFO - Response - Page 7:
2025-06-01 22:31:22,591 - INFO - 第 7 页获取到 5 条记录
2025-06-01 22:31:22,792 - INFO - 查询完成，共获取到 605 条记录
2025-06-01 22:31:22,792 - INFO - 获取到 605 条表单数据
2025-06-01 22:31:22,802 - INFO - 当前日期 2025-05-19 有 1 条MySQL数据需要处理
2025-06-01 22:31:22,803 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 22:31:22,803 - INFO - 开始处理日期: 2025-05-30
2025-06-01 22:31:22,803 - INFO - Request Parameters - Page 1:
2025-06-01 22:31:22,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:22,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:23,615 - INFO - Response - Page 1:
2025-06-01 22:31:23,615 - INFO - 第 1 页获取到 100 条记录
2025-06-01 22:31:23,816 - INFO - Request Parameters - Page 2:
2025-06-01 22:31:23,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:23,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:24,667 - INFO - Response - Page 2:
2025-06-01 22:31:24,667 - INFO - 第 2 页获取到 100 条记录
2025-06-01 22:31:24,867 - INFO - Request Parameters - Page 3:
2025-06-01 22:31:24,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:24,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:25,870 - INFO - Response - Page 3:
2025-06-01 22:31:25,870 - INFO - 第 3 页获取到 100 条记录
2025-06-01 22:31:26,070 - INFO - Request Parameters - Page 4:
2025-06-01 22:31:26,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:26,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:26,802 - INFO - Response - Page 4:
2025-06-01 22:31:26,802 - INFO - 第 4 页获取到 100 条记录
2025-06-01 22:31:27,002 - INFO - Request Parameters - Page 5:
2025-06-01 22:31:27,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:27,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:27,950 - INFO - Response - Page 5:
2025-06-01 22:31:27,950 - INFO - 第 5 页获取到 100 条记录
2025-06-01 22:31:28,150 - INFO - Request Parameters - Page 6:
2025-06-01 22:31:28,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:28,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:28,823 - INFO - Response - Page 6:
2025-06-01 22:31:28,823 - INFO - 第 6 页获取到 34 条记录
2025-06-01 22:31:29,024 - INFO - 查询完成，共获取到 534 条记录
2025-06-01 22:31:29,024 - INFO - 获取到 534 条表单数据
2025-06-01 22:31:29,035 - INFO - 当前日期 2025-05-30 有 2 条MySQL数据需要处理
2025-06-01 22:31:29,035 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 22:31:29,035 - INFO - 开始处理日期: 2025-05-31
2025-06-01 22:31:29,035 - INFO - Request Parameters - Page 1:
2025-06-01 22:31:29,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:29,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:29,777 - INFO - Response - Page 1:
2025-06-01 22:31:29,777 - INFO - 第 1 页获取到 100 条记录
2025-06-01 22:31:29,977 - INFO - Request Parameters - Page 2:
2025-06-01 22:31:29,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:29,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:30,791 - INFO - Response - Page 2:
2025-06-01 22:31:30,791 - INFO - 第 2 页获取到 100 条记录
2025-06-01 22:31:30,992 - INFO - Request Parameters - Page 3:
2025-06-01 22:31:30,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:30,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:31,737 - INFO - Response - Page 3:
2025-06-01 22:31:31,738 - INFO - 第 3 页获取到 100 条记录
2025-06-01 22:31:31,938 - INFO - Request Parameters - Page 4:
2025-06-01 22:31:31,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:31,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:32,773 - INFO - Response - Page 4:
2025-06-01 22:31:32,774 - INFO - 第 4 页获取到 100 条记录
2025-06-01 22:31:32,974 - INFO - Request Parameters - Page 5:
2025-06-01 22:31:32,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:32,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:33,765 - INFO - Response - Page 5:
2025-06-01 22:31:33,765 - INFO - 第 5 页获取到 100 条记录
2025-06-01 22:31:33,966 - INFO - Request Parameters - Page 6:
2025-06-01 22:31:33,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:33,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:34,739 - INFO - Response - Page 6:
2025-06-01 22:31:34,739 - INFO - 第 6 页获取到 24 条记录
2025-06-01 22:31:34,939 - INFO - 查询完成，共获取到 524 条记录
2025-06-01 22:31:34,939 - INFO - 获取到 524 条表单数据
2025-06-01 22:31:34,948 - INFO - 当前日期 2025-05-31 有 197 条MySQL数据需要处理
2025-06-01 22:31:34,952 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 22:31:34,952 - INFO - 开始处理日期: 2025-06-01
2025-06-01 22:31:34,952 - INFO - Request Parameters - Page 1:
2025-06-01 22:31:34,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:31:34,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:31:35,531 - INFO - Response - Page 1:
2025-06-01 22:31:35,531 - INFO - 第 1 页获取到 5 条记录
2025-06-01 22:31:35,731 - INFO - 查询完成，共获取到 5 条记录
2025-06-01 22:31:35,731 - INFO - 获取到 5 条表单数据
2025-06-01 22:31:35,732 - INFO - 当前日期 2025-06-01 有 48 条MySQL数据需要处理
2025-06-01 22:31:35,733 - INFO - 开始批量插入 43 条新记录
2025-06-01 22:31:35,967 - INFO - 批量插入响应状态码: 200
2025-06-01 22:31:35,967 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 14:31:36 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2076', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7D0480E2-94E6-7F3B-97A9-A0E92AA7E8A6', 'x-acs-trace-id': 'cff606267ae2d7fd2fc895845a561344', 'etag': '2RxeJOPwXi1+5VHCUxhMlWQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 22:31:35,967 - INFO - 批量插入响应体: {'result': ['FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM4M', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM5M', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM6M', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM7M', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM8M', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM9M', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMAM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMBM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMCM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMDM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMEM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMFM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMGM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMHM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMIM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMJM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMKM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMLM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMMM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMNM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMOM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMPM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMQM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMRM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMSM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMTM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMUM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMVM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMWM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMXM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMYM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMZM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM0N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM1N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM2N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM3N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM4N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92Q6DERDBM5N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92Q6DERDBM6N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92Q6DERDBM7N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92Q6DERDBM8N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92Q6DERDBM9N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92Q6DERDBMAN']}
2025-06-01 22:31:35,967 - INFO - 批量插入表单数据成功，批次 1，共 43 条记录
2025-06-01 22:31:35,967 - INFO - 成功插入的数据ID: ['FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM4M', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM5M', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM6M', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM7M', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM8M', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM9M', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMAM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMBM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMCM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMDM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMEM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMFM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMGM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMHM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMIM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMJM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMKM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMLM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMMM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMNM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMOM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMPM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMQM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMRM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMSM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMTM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMUM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMVM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMWM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMXM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMYM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBMZM', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM0N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM1N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM2N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM3N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92P6DERDBM4N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92Q6DERDBM5N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92Q6DERDBM6N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92Q6DERDBM7N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92Q6DERDBM8N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92Q6DERDBM9N', 'FINST-RNA66D71N8VVRNUN9GHNVBP8JYI92Q6DERDBMAN']
2025-06-01 22:31:40,968 - INFO - 批量插入完成，共 43 条记录
2025-06-01 22:31:40,968 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 43 条，错误: 0 条
2025-06-01 22:31:40,968 - INFO - 数据同步完成！更新: 0 条，插入: 43 条，错误: 2 条
2025-06-01 22:32:40,979 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 22:32:40,979 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 22:32:40,979 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 22:32:41,068 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 572 条记录
2025-06-01 22:32:41,068 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 22:32:41,073 - INFO - 开始处理日期: 2025-05-31
2025-06-01 22:32:41,073 - INFO - Request Parameters - Page 1:
2025-06-01 22:32:41,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:32:41,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:32:41,943 - INFO - Response - Page 1:
2025-06-01 22:32:41,943 - INFO - 第 1 页获取到 100 条记录
2025-06-01 22:32:42,144 - INFO - Request Parameters - Page 2:
2025-06-01 22:32:42,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:32:42,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:32:43,107 - INFO - Response - Page 2:
2025-06-01 22:32:43,107 - INFO - 第 2 页获取到 100 条记录
2025-06-01 22:32:43,308 - INFO - Request Parameters - Page 3:
2025-06-01 22:32:43,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:32:43,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:32:44,139 - INFO - Response - Page 3:
2025-06-01 22:32:44,139 - INFO - 第 3 页获取到 100 条记录
2025-06-01 22:32:44,339 - INFO - Request Parameters - Page 4:
2025-06-01 22:32:44,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:32:44,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:32:45,076 - INFO - Response - Page 4:
2025-06-01 22:32:45,076 - INFO - 第 4 页获取到 100 条记录
2025-06-01 22:32:45,276 - INFO - Request Parameters - Page 5:
2025-06-01 22:32:45,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:32:45,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:32:46,073 - INFO - Response - Page 5:
2025-06-01 22:32:46,074 - INFO - 第 5 页获取到 100 条记录
2025-06-01 22:32:46,285 - INFO - Request Parameters - Page 6:
2025-06-01 22:32:46,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:32:46,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:32:46,920 - INFO - Response - Page 6:
2025-06-01 22:32:46,920 - INFO - 第 6 页获取到 24 条记录
2025-06-01 22:32:47,120 - INFO - 查询完成，共获取到 524 条记录
2025-06-01 22:32:47,120 - INFO - 获取到 524 条表单数据
2025-06-01 22:32:47,130 - INFO - 当前日期 2025-05-31 有 524 条MySQL数据需要处理
2025-06-01 22:32:47,139 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 22:32:47,139 - INFO - 开始处理日期: 2025-06-01
2025-06-01 22:32:47,139 - INFO - Request Parameters - Page 1:
2025-06-01 22:32:47,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 22:32:47,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 22:32:47,863 - INFO - Response - Page 1:
2025-06-01 22:32:47,863 - INFO - 第 1 页获取到 48 条记录
2025-06-01 22:32:48,063 - INFO - 查询完成，共获取到 48 条记录
2025-06-01 22:32:48,063 - INFO - 获取到 48 条表单数据
2025-06-01 22:32:48,066 - INFO - 当前日期 2025-06-01 有 48 条MySQL数据需要处理
2025-06-01 22:32:48,066 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 22:32:48,066 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 22:32:48,066 - INFO - 同步完成
2025-06-01 23:30:33,973 - INFO - 使用默认增量同步（当天更新数据）
2025-06-01 23:30:33,973 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 23:30:33,973 - INFO - 查询参数: ('2025-06-01',)
2025-06-01 23:30:34,051 - INFO - MySQL查询成功，增量数据（日期: 2025-06-01），共获取 285 条记录
2025-06-01 23:30:34,051 - INFO - 获取到 9 个日期需要处理: ['2025-05-02', '2025-05-12', '2025-05-14', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-30', '2025-05-31', '2025-06-01']
2025-06-01 23:30:34,066 - INFO - 开始处理日期: 2025-05-02
2025-06-01 23:30:34,066 - INFO - Request Parameters - Page 1:
2025-06-01 23:30:34,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:30:34,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:30:42,193 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 129260B2-EC48-7C1E-97E6-E0F1C9907CC3 Response: {'code': 'ServiceUnavailable', 'requestid': '129260B2-EC48-7C1E-97E6-E0F1C9907CC3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 129260B2-EC48-7C1E-97E6-E0F1C9907CC3)
2025-06-01 23:30:42,193 - INFO - 开始处理日期: 2025-05-12
2025-06-01 23:30:42,193 - INFO - Request Parameters - Page 1:
2025-06-01 23:30:42,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:30:42,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746979200000, 1747065599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:30:50,319 - ERROR - 处理日期 2025-05-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 868FDF87-D333-74B5-AD5F-B65C42CF9A4B Response: {'code': 'ServiceUnavailable', 'requestid': '868FDF87-D333-74B5-AD5F-B65C42CF9A4B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 868FDF87-D333-74B5-AD5F-B65C42CF9A4B)
2025-06-01 23:30:50,319 - INFO - 开始处理日期: 2025-05-14
2025-06-01 23:30:50,319 - INFO - Request Parameters - Page 1:
2025-06-01 23:30:50,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:30:50,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:30:58,070 - INFO - Response - Page 1:
2025-06-01 23:30:58,070 - INFO - 第 1 页获取到 100 条记录
2025-06-01 23:30:58,273 - INFO - Request Parameters - Page 2:
2025-06-01 23:30:58,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:30:58,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:30:59,008 - INFO - Response - Page 2:
2025-06-01 23:30:59,008 - INFO - 第 2 页获取到 100 条记录
2025-06-01 23:30:59,211 - INFO - Request Parameters - Page 3:
2025-06-01 23:30:59,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:30:59,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:30:59,945 - INFO - Response - Page 3:
2025-06-01 23:30:59,945 - INFO - 第 3 页获取到 100 条记录
2025-06-01 23:31:00,149 - INFO - Request Parameters - Page 4:
2025-06-01 23:31:00,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:00,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:00,899 - INFO - Response - Page 4:
2025-06-01 23:31:00,899 - INFO - 第 4 页获取到 100 条记录
2025-06-01 23:31:01,102 - INFO - Request Parameters - Page 5:
2025-06-01 23:31:01,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:01,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:01,821 - INFO - Response - Page 5:
2025-06-01 23:31:01,821 - INFO - 第 5 页获取到 100 条记录
2025-06-01 23:31:02,024 - INFO - Request Parameters - Page 6:
2025-06-01 23:31:02,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:02,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:02,821 - INFO - Response - Page 6:
2025-06-01 23:31:02,821 - INFO - 第 6 页获取到 100 条记录
2025-06-01 23:31:03,024 - INFO - Request Parameters - Page 7:
2025-06-01 23:31:03,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:03,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:03,555 - INFO - Response - Page 7:
2025-06-01 23:31:03,555 - INFO - 第 7 页获取到 1 条记录
2025-06-01 23:31:03,759 - INFO - 查询完成，共获取到 601 条记录
2025-06-01 23:31:03,759 - INFO - 获取到 601 条表单数据
2025-06-01 23:31:03,774 - INFO - 当前日期 2025-05-14 有 1 条MySQL数据需要处理
2025-06-01 23:31:03,774 - INFO - 日期 2025-05-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 23:31:03,774 - INFO - 开始处理日期: 2025-05-17
2025-06-01 23:31:03,774 - INFO - Request Parameters - Page 1:
2025-06-01 23:31:03,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:03,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:04,602 - INFO - Response - Page 1:
2025-06-01 23:31:04,602 - INFO - 第 1 页获取到 100 条记录
2025-06-01 23:31:04,806 - INFO - Request Parameters - Page 2:
2025-06-01 23:31:04,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:04,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:05,650 - INFO - Response - Page 2:
2025-06-01 23:31:05,650 - INFO - 第 2 页获取到 100 条记录
2025-06-01 23:31:05,853 - INFO - Request Parameters - Page 3:
2025-06-01 23:31:05,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:05,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:06,697 - INFO - Response - Page 3:
2025-06-01 23:31:06,697 - INFO - 第 3 页获取到 100 条记录
2025-06-01 23:31:06,900 - INFO - Request Parameters - Page 4:
2025-06-01 23:31:06,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:06,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:07,697 - INFO - Response - Page 4:
2025-06-01 23:31:07,697 - INFO - 第 4 页获取到 100 条记录
2025-06-01 23:31:07,900 - INFO - Request Parameters - Page 5:
2025-06-01 23:31:07,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:07,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:08,759 - INFO - Response - Page 5:
2025-06-01 23:31:08,759 - INFO - 第 5 页获取到 100 条记录
2025-06-01 23:31:08,963 - INFO - Request Parameters - Page 6:
2025-06-01 23:31:08,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:08,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:09,650 - INFO - Response - Page 6:
2025-06-01 23:31:09,650 - INFO - 第 6 页获取到 61 条记录
2025-06-01 23:31:09,853 - INFO - 查询完成，共获取到 561 条记录
2025-06-01 23:31:09,853 - INFO - 获取到 561 条表单数据
2025-06-01 23:31:09,853 - INFO - 当前日期 2025-05-17 有 1 条MySQL数据需要处理
2025-06-01 23:31:09,853 - INFO - 日期 2025-05-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 23:31:09,853 - INFO - 开始处理日期: 2025-05-18
2025-06-01 23:31:09,853 - INFO - Request Parameters - Page 1:
2025-06-01 23:31:09,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:09,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:10,682 - INFO - Response - Page 1:
2025-06-01 23:31:10,682 - INFO - 第 1 页获取到 100 条记录
2025-06-01 23:31:10,885 - INFO - Request Parameters - Page 2:
2025-06-01 23:31:10,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:10,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:11,744 - INFO - Response - Page 2:
2025-06-01 23:31:11,744 - INFO - 第 2 页获取到 100 条记录
2025-06-01 23:31:11,947 - INFO - Request Parameters - Page 3:
2025-06-01 23:31:11,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:11,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:12,791 - INFO - Response - Page 3:
2025-06-01 23:31:12,791 - INFO - 第 3 页获取到 100 条记录
2025-06-01 23:31:12,994 - INFO - Request Parameters - Page 4:
2025-06-01 23:31:12,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:12,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:13,760 - INFO - Response - Page 4:
2025-06-01 23:31:13,760 - INFO - 第 4 页获取到 100 条记录
2025-06-01 23:31:13,963 - INFO - Request Parameters - Page 5:
2025-06-01 23:31:13,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:13,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:14,807 - INFO - Response - Page 5:
2025-06-01 23:31:14,807 - INFO - 第 5 页获取到 100 条记录
2025-06-01 23:31:15,010 - INFO - Request Parameters - Page 6:
2025-06-01 23:31:15,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:15,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:15,807 - INFO - Response - Page 6:
2025-06-01 23:31:15,807 - INFO - 第 6 页获取到 100 条记录
2025-06-01 23:31:16,011 - INFO - Request Parameters - Page 7:
2025-06-01 23:31:16,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:16,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:16,573 - INFO - Response - Page 7:
2025-06-01 23:31:16,573 - INFO - 第 7 页获取到 15 条记录
2025-06-01 23:31:16,776 - INFO - 查询完成，共获取到 615 条记录
2025-06-01 23:31:16,776 - INFO - 获取到 615 条表单数据
2025-06-01 23:31:16,776 - INFO - 当前日期 2025-05-18 有 1 条MySQL数据需要处理
2025-06-01 23:31:16,776 - INFO - 日期 2025-05-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 23:31:16,776 - INFO - 开始处理日期: 2025-05-19
2025-06-01 23:31:16,776 - INFO - Request Parameters - Page 1:
2025-06-01 23:31:16,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:16,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:17,573 - INFO - Response - Page 1:
2025-06-01 23:31:17,573 - INFO - 第 1 页获取到 100 条记录
2025-06-01 23:31:17,776 - INFO - Request Parameters - Page 2:
2025-06-01 23:31:17,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:17,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:18,573 - INFO - Response - Page 2:
2025-06-01 23:31:18,573 - INFO - 第 2 页获取到 100 条记录
2025-06-01 23:31:18,777 - INFO - Request Parameters - Page 3:
2025-06-01 23:31:18,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:18,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:19,542 - INFO - Response - Page 3:
2025-06-01 23:31:19,542 - INFO - 第 3 页获取到 100 条记录
2025-06-01 23:31:19,746 - INFO - Request Parameters - Page 4:
2025-06-01 23:31:19,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:19,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:20,574 - INFO - Response - Page 4:
2025-06-01 23:31:20,574 - INFO - 第 4 页获取到 100 条记录
2025-06-01 23:31:20,777 - INFO - Request Parameters - Page 5:
2025-06-01 23:31:20,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:20,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:21,543 - INFO - Response - Page 5:
2025-06-01 23:31:21,543 - INFO - 第 5 页获取到 100 条记录
2025-06-01 23:31:21,746 - INFO - Request Parameters - Page 6:
2025-06-01 23:31:21,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:21,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:22,543 - INFO - Response - Page 6:
2025-06-01 23:31:22,543 - INFO - 第 6 页获取到 100 条记录
2025-06-01 23:31:22,746 - INFO - Request Parameters - Page 7:
2025-06-01 23:31:22,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:22,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:23,277 - INFO - Response - Page 7:
2025-06-01 23:31:23,277 - INFO - 第 7 页获取到 5 条记录
2025-06-01 23:31:23,481 - INFO - 查询完成，共获取到 605 条记录
2025-06-01 23:31:23,481 - INFO - 获取到 605 条表单数据
2025-06-01 23:31:23,481 - INFO - 当前日期 2025-05-19 有 1 条MySQL数据需要处理
2025-06-01 23:31:23,481 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 23:31:23,481 - INFO - 开始处理日期: 2025-05-30
2025-06-01 23:31:23,481 - INFO - Request Parameters - Page 1:
2025-06-01 23:31:23,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:23,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:24,403 - INFO - Response - Page 1:
2025-06-01 23:31:24,403 - INFO - 第 1 页获取到 100 条记录
2025-06-01 23:31:24,606 - INFO - Request Parameters - Page 2:
2025-06-01 23:31:24,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:24,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:25,590 - INFO - Response - Page 2:
2025-06-01 23:31:25,590 - INFO - 第 2 页获取到 100 条记录
2025-06-01 23:31:25,793 - INFO - Request Parameters - Page 3:
2025-06-01 23:31:25,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:25,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:26,637 - INFO - Response - Page 3:
2025-06-01 23:31:26,637 - INFO - 第 3 页获取到 100 条记录
2025-06-01 23:31:26,840 - INFO - Request Parameters - Page 4:
2025-06-01 23:31:26,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:26,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:27,606 - INFO - Response - Page 4:
2025-06-01 23:31:27,606 - INFO - 第 4 页获取到 100 条记录
2025-06-01 23:31:27,809 - INFO - Request Parameters - Page 5:
2025-06-01 23:31:27,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:27,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:28,684 - INFO - Response - Page 5:
2025-06-01 23:31:28,684 - INFO - 第 5 页获取到 100 条记录
2025-06-01 23:31:28,888 - INFO - Request Parameters - Page 6:
2025-06-01 23:31:28,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:28,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:29,528 - INFO - Response - Page 6:
2025-06-01 23:31:29,528 - INFO - 第 6 页获取到 34 条记录
2025-06-01 23:31:29,731 - INFO - 查询完成，共获取到 534 条记录
2025-06-01 23:31:29,731 - INFO - 获取到 534 条表单数据
2025-06-01 23:31:29,731 - INFO - 当前日期 2025-05-30 有 2 条MySQL数据需要处理
2025-06-01 23:31:29,731 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 23:31:29,731 - INFO - 开始处理日期: 2025-05-31
2025-06-01 23:31:29,731 - INFO - Request Parameters - Page 1:
2025-06-01 23:31:29,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:29,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:30,482 - INFO - Response - Page 1:
2025-06-01 23:31:30,482 - INFO - 第 1 页获取到 100 条记录
2025-06-01 23:31:30,685 - INFO - Request Parameters - Page 2:
2025-06-01 23:31:30,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:30,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:31,513 - INFO - Response - Page 2:
2025-06-01 23:31:31,513 - INFO - 第 2 页获取到 100 条记录
2025-06-01 23:31:31,716 - INFO - Request Parameters - Page 3:
2025-06-01 23:31:31,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:31,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:32,513 - INFO - Response - Page 3:
2025-06-01 23:31:32,513 - INFO - 第 3 页获取到 100 条记录
2025-06-01 23:31:32,716 - INFO - Request Parameters - Page 4:
2025-06-01 23:31:32,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:32,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:33,748 - INFO - Response - Page 4:
2025-06-01 23:31:33,748 - INFO - 第 4 页获取到 100 条记录
2025-06-01 23:31:33,951 - INFO - Request Parameters - Page 5:
2025-06-01 23:31:33,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:33,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:34,732 - INFO - Response - Page 5:
2025-06-01 23:31:34,732 - INFO - 第 5 页获取到 100 条记录
2025-06-01 23:31:34,935 - INFO - Request Parameters - Page 6:
2025-06-01 23:31:34,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:34,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:35,576 - INFO - Response - Page 6:
2025-06-01 23:31:35,576 - INFO - 第 6 页获取到 24 条记录
2025-06-01 23:31:35,779 - INFO - 查询完成，共获取到 524 条记录
2025-06-01 23:31:35,779 - INFO - 获取到 524 条表单数据
2025-06-01 23:31:35,779 - INFO - 当前日期 2025-05-31 有 198 条MySQL数据需要处理
2025-06-01 23:31:35,779 - INFO - 开始批量插入 1 条新记录
2025-06-01 23:31:35,936 - INFO - 批量插入响应状态码: 200
2025-06-01 23:31:35,936 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 15:31:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4CAE28E5-25F2-709A-83CE-C8A3BAC3904E', 'x-acs-trace-id': '0b83a6566d63f2ba4319a287555080f8', 'etag': '6WAXo7imKclKijuOFgzG5uA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 23:31:35,936 - INFO - 批量插入响应体: {'result': ['FINST-LR5668B14CWVW9HX8PDFXBUJ91AG21NIJTDBMZ5']}
2025-06-01 23:31:35,936 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-01 23:31:35,936 - INFO - 成功插入的数据ID: ['FINST-LR5668B14CWVW9HX8PDFXBUJ91AG21NIJTDBMZ5']
2025-06-01 23:31:40,952 - INFO - 批量插入完成，共 1 条记录
2025-06-01 23:31:40,952 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-01 23:31:40,952 - INFO - 开始处理日期: 2025-06-01
2025-06-01 23:31:40,952 - INFO - Request Parameters - Page 1:
2025-06-01 23:31:40,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:31:40,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:31:41,687 - INFO - Response - Page 1:
2025-06-01 23:31:41,687 - INFO - 第 1 页获取到 48 条记录
2025-06-01 23:31:41,890 - INFO - 查询完成，共获取到 48 条记录
2025-06-01 23:31:41,890 - INFO - 获取到 48 条表单数据
2025-06-01 23:31:41,890 - INFO - 当前日期 2025-06-01 有 77 条MySQL数据需要处理
2025-06-01 23:31:41,890 - INFO - 开始批量插入 29 条新记录
2025-06-01 23:31:42,093 - INFO - 批量插入响应状态码: 200
2025-06-01 23:31:42,093 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 15:31:41 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1404', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CC84349A-1099-713F-9A8B-E225B79B3352', 'x-acs-trace-id': 'aefd63a0821a657a20107468d12425e8', 'etag': '1Lc8lYycYMWPnO65LVA7jig4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 23:31:42,093 - INFO - 批量插入响应体: {'result': ['FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMEL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMFL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMGL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMHL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMIL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMJL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMKL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMLL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMML', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMNL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMOL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMPL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMQL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMRL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMSL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMTL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMUL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMVL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMWL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMXL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMYL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMZL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM0M', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM1M', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM2M', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM3M', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM4M', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM5M', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM6M']}
2025-06-01 23:31:42,093 - INFO - 批量插入表单数据成功，批次 1，共 29 条记录
2025-06-01 23:31:42,093 - INFO - 成功插入的数据ID: ['FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMEL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMFL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMGL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMHL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMIL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMJL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMKL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMLL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMML', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMNL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMOL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMPL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMQL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMRL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMSL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMTL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMUL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMVL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMWL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMXL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMYL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBMZL', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM0M', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM1M', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM2M', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM3M', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM4M', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM5M', 'FINST-QVA66B81B9TVQ2IRAXMC39GMNE2P2TDNJTDBM6M']
2025-06-01 23:31:47,109 - INFO - 批量插入完成，共 29 条记录
2025-06-01 23:31:47,109 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 29 条，错误: 0 条
2025-06-01 23:31:47,109 - INFO - 数据同步完成！更新: 0 条，插入: 30 条，错误: 2 条
2025-06-01 23:32:47,134 - INFO - 开始同步昨天与今天的销售数据: 2025-05-31 至 2025-06-01
2025-06-01 23:32:47,134 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-01 23:32:47,134 - INFO - 查询参数: ('2025-05-31', '2025-06-01')
2025-06-01 23:32:47,213 - INFO - MySQL查询成功，时间段: 2025-05-31 至 2025-06-01，共获取 602 条记录
2025-06-01 23:32:47,213 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-01 23:32:47,213 - INFO - 开始处理日期: 2025-05-31
2025-06-01 23:32:47,213 - INFO - Request Parameters - Page 1:
2025-06-01 23:32:47,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:32:47,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:32:48,057 - INFO - Response - Page 1:
2025-06-01 23:32:48,057 - INFO - 第 1 页获取到 100 条记录
2025-06-01 23:32:48,260 - INFO - Request Parameters - Page 2:
2025-06-01 23:32:48,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:32:48,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:32:49,041 - INFO - Response - Page 2:
2025-06-01 23:32:49,041 - INFO - 第 2 页获取到 100 条记录
2025-06-01 23:32:49,244 - INFO - Request Parameters - Page 3:
2025-06-01 23:32:49,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:32:49,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:32:50,338 - INFO - Response - Page 3:
2025-06-01 23:32:50,338 - INFO - 第 3 页获取到 100 条记录
2025-06-01 23:32:50,541 - INFO - Request Parameters - Page 4:
2025-06-01 23:32:50,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:32:50,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:32:51,370 - INFO - Response - Page 4:
2025-06-01 23:32:51,370 - INFO - 第 4 页获取到 100 条记录
2025-06-01 23:32:51,573 - INFO - Request Parameters - Page 5:
2025-06-01 23:32:51,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:32:51,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:32:52,354 - INFO - Response - Page 5:
2025-06-01 23:32:52,354 - INFO - 第 5 页获取到 100 条记录
2025-06-01 23:32:52,557 - INFO - Request Parameters - Page 6:
2025-06-01 23:32:52,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:32:52,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:32:53,229 - INFO - Response - Page 6:
2025-06-01 23:32:53,229 - INFO - 第 6 页获取到 25 条记录
2025-06-01 23:32:53,432 - INFO - 查询完成，共获取到 525 条记录
2025-06-01 23:32:53,432 - INFO - 获取到 525 条表单数据
2025-06-01 23:32:53,432 - INFO - 当前日期 2025-05-31 有 525 条MySQL数据需要处理
2025-06-01 23:32:53,448 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 23:32:53,448 - INFO - 开始处理日期: 2025-06-01
2025-06-01 23:32:53,448 - INFO - Request Parameters - Page 1:
2025-06-01 23:32:53,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 23:32:53,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 23:32:54,229 - INFO - Response - Page 1:
2025-06-01 23:32:54,229 - INFO - 第 1 页获取到 77 条记录
2025-06-01 23:32:54,433 - INFO - 查询完成，共获取到 77 条记录
2025-06-01 23:32:54,433 - INFO - 获取到 77 条表单数据
2025-06-01 23:32:54,433 - INFO - 当前日期 2025-06-01 有 77 条MySQL数据需要处理
2025-06-01 23:32:54,433 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 23:32:54,433 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 23:32:54,433 - INFO - 同步完成
