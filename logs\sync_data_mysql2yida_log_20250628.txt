2025-06-28 01:30:33,566 - INFO - 使用默认增量同步（当天更新数据）
2025-06-28 01:30:33,566 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-28 01:30:33,566 - INFO - 查询参数: ('2025-06-28',)
2025-06-28 01:30:33,691 - INFO - MySQL查询成功，增量数据（日期: 2025-06-28），共获取 4 条记录
2025-06-28 01:30:33,691 - INFO - 获取到 1 个日期需要处理: ['2025-06-27']
2025-06-28 01:30:33,691 - INFO - 开始处理日期: 2025-06-27
2025-06-28 01:30:33,691 - INFO - Request Parameters - Page 1:
2025-06-28 01:30:33,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 01:30:33,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 01:30:41,800 - ERROR - 处理日期 2025-06-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 07E30353-7515-7CC2-9B16-1C264B04DA1F Response: {'code': 'ServiceUnavailable', 'requestid': '07E30353-7515-7CC2-9B16-1C264B04DA1F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 07E30353-7515-7CC2-9B16-1C264B04DA1F)
2025-06-28 01:30:41,800 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-28 01:31:41,815 - INFO - 开始同步昨天与今天的销售数据: 2025-06-27 至 2025-06-28
2025-06-28 01:31:41,815 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-28 01:31:41,815 - INFO - 查询参数: ('2025-06-27', '2025-06-28')
2025-06-28 01:31:41,940 - INFO - MySQL查询成功，时间段: 2025-06-27 至 2025-06-28，共获取 130 条记录
2025-06-28 01:31:41,940 - INFO - 获取到 1 个日期需要处理: ['2025-06-27']
2025-06-28 01:31:41,940 - INFO - 开始处理日期: 2025-06-27
2025-06-28 01:31:41,940 - INFO - Request Parameters - Page 1:
2025-06-28 01:31:41,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 01:31:41,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 01:31:48,143 - INFO - Response - Page 1:
2025-06-28 01:31:48,143 - INFO - 第 1 页获取到 50 条记录
2025-06-28 01:31:48,643 - INFO - Request Parameters - Page 2:
2025-06-28 01:31:48,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 01:31:48,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 01:31:49,315 - INFO - Response - Page 2:
2025-06-28 01:31:49,315 - INFO - 第 2 页获取到 40 条记录
2025-06-28 01:31:49,831 - INFO - 查询完成，共获取到 90 条记录
2025-06-28 01:31:49,831 - INFO - 获取到 90 条表单数据
2025-06-28 01:31:49,831 - INFO - 当前日期 2025-06-27 有 126 条MySQL数据需要处理
2025-06-28 01:31:49,831 - INFO - 开始批量插入 36 条新记录
2025-06-28 01:31:50,081 - INFO - 批量插入响应状态码: 200
2025-06-28 01:31:50,081 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 17:31:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1740', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DA760CA4-EA50-7FCA-B4FE-73AD32153933', 'x-acs-trace-id': '154fdba8f2ab261f7746cc9d305cef37', 'etag': '1746KVfTrTysoHAWw+dXaHQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 01:31:50,081 - INFO - 批量插入响应体: {'result': ['FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMW1', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMX1', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMY1', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMZ1', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM02', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM12', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM22', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM32', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM42', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM52', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM62', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM72', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM82', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM92', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMA2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMB2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMC2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMD2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCME2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMF2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMG2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMH2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMI2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMJ2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMK2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCML2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMM2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMN2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMO2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMP2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMQ2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMR2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMS2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMT2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMU2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMV2']}
2025-06-28 01:31:50,081 - INFO - 批量插入表单数据成功，批次 1，共 36 条记录
2025-06-28 01:31:50,081 - INFO - 成功插入的数据ID: ['FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMW1', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMX1', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMY1', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMZ1', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM02', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM12', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM22', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM32', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM42', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM52', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM62', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM72', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM82', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCM92', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMA2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMB2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMC2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMD2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCME2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMF2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3TFAA3FCMG2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMH2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMI2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMJ2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMK2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCML2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMM2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMN2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMO2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMP2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMQ2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMR2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMS2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMT2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMU2', 'FINST-MLF662B1W6OWOA36A5VHH861BZXA3UFAA3FCMV2']
2025-06-28 01:31:55,096 - INFO - 批量插入完成，共 36 条记录
2025-06-28 01:31:55,096 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 36 条，错误: 0 条
2025-06-28 01:31:55,096 - INFO - 数据同步完成！更新: 0 条，插入: 36 条，错误: 0 条
2025-06-28 01:31:55,096 - INFO - 同步完成
2025-06-28 04:30:33,598 - INFO - 使用默认增量同步（当天更新数据）
2025-06-28 04:30:33,598 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-28 04:30:33,598 - INFO - 查询参数: ('2025-06-28',)
2025-06-28 04:30:33,723 - INFO - MySQL查询成功，增量数据（日期: 2025-06-28），共获取 11 条记录
2025-06-28 04:30:33,723 - INFO - 获取到 1 个日期需要处理: ['2025-06-27']
2025-06-28 04:30:33,723 - INFO - 开始处理日期: 2025-06-27
2025-06-28 04:30:33,738 - INFO - Request Parameters - Page 1:
2025-06-28 04:30:33,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 04:30:33,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 04:30:40,082 - INFO - Response - Page 1:
2025-06-28 04:30:40,082 - INFO - 第 1 页获取到 50 条记录
2025-06-28 04:30:40,582 - INFO - Request Parameters - Page 2:
2025-06-28 04:30:40,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 04:30:40,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 04:30:48,676 - ERROR - 处理日期 2025-06-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AC561D44-3BD7-7FD8-883D-63D460C97A2F Response: {'code': 'ServiceUnavailable', 'requestid': 'AC561D44-3BD7-7FD8-883D-63D460C97A2F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AC561D44-3BD7-7FD8-883D-63D460C97A2F)
2025-06-28 04:30:48,676 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-28 04:31:48,691 - INFO - 开始同步昨天与今天的销售数据: 2025-06-27 至 2025-06-28
2025-06-28 04:31:48,691 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-28 04:31:48,691 - INFO - 查询参数: ('2025-06-27', '2025-06-28')
2025-06-28 04:31:48,832 - INFO - MySQL查询成功，时间段: 2025-06-27 至 2025-06-28，共获取 169 条记录
2025-06-28 04:31:48,832 - INFO - 获取到 1 个日期需要处理: ['2025-06-27']
2025-06-28 04:31:48,832 - INFO - 开始处理日期: 2025-06-27
2025-06-28 04:31:48,832 - INFO - Request Parameters - Page 1:
2025-06-28 04:31:48,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 04:31:48,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 04:31:49,550 - INFO - Response - Page 1:
2025-06-28 04:31:49,550 - INFO - 第 1 页获取到 50 条记录
2025-06-28 04:31:50,050 - INFO - Request Parameters - Page 2:
2025-06-28 04:31:50,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 04:31:50,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 04:31:50,722 - INFO - Response - Page 2:
2025-06-28 04:31:50,722 - INFO - 第 2 页获取到 50 条记录
2025-06-28 04:31:51,222 - INFO - Request Parameters - Page 3:
2025-06-28 04:31:51,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 04:31:51,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 04:31:51,816 - INFO - Response - Page 3:
2025-06-28 04:31:51,816 - INFO - 第 3 页获取到 26 条记录
2025-06-28 04:31:52,332 - INFO - 查询完成，共获取到 126 条记录
2025-06-28 04:31:52,332 - INFO - 获取到 126 条表单数据
2025-06-28 04:31:52,332 - INFO - 当前日期 2025-06-27 有 164 条MySQL数据需要处理
2025-06-28 04:31:52,332 - INFO - 开始批量插入 38 条新记录
2025-06-28 04:31:52,566 - INFO - 批量插入响应状态码: 200
2025-06-28 04:31:52,566 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 20:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1836', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CD394BF0-AE6C-7296-AAA6-7AC399F2161F', 'x-acs-trace-id': '16d7bafd974f4e56c48590d7b75f2b26', 'etag': '1fzE8Lyqx2DPOMjrUs9dF8w6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 04:31:52,566 - INFO - 批量插入响应体: {'result': ['FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM04', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM14', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM24', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM34', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM44', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM54', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM64', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM74', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM84', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM94', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMA4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMB4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMC4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMD4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCME4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMF4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMG4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMH4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMI4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMJ4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMK4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCML4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMM4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMN4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMO4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMP4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMQ4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMR4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMS4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMT4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMU4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMV4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMW4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMX4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMY4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMZ4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM05', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM15']}
2025-06-28 04:31:52,566 - INFO - 批量插入表单数据成功，批次 1，共 38 条记录
2025-06-28 04:31:52,566 - INFO - 成功插入的数据ID: ['FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM04', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM14', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM24', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM34', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM44', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM54', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM64', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM74', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM84', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM94', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMA4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMB4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMC4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMD4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCME4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMF4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMG4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMH4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMI4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMJ4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMK4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCML4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMM4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMN4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMO4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMP4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMQ4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMR4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMS4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMT4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMU4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMV4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMW4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMX4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMY4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCMZ4', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM05', 'FINST-KLF66WC1K8OW1ZVEDRYO3BTC7KUT2VQTP9FCM15']
2025-06-28 04:31:57,582 - INFO - 批量插入完成，共 38 条记录
2025-06-28 04:31:57,582 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 38 条，错误: 0 条
2025-06-28 04:31:57,582 - INFO - 数据同步完成！更新: 0 条，插入: 38 条，错误: 0 条
2025-06-28 04:31:57,582 - INFO - 同步完成
2025-06-28 07:30:34,082 - INFO - 使用默认增量同步（当天更新数据）
2025-06-28 07:30:34,082 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-28 07:30:34,082 - INFO - 查询参数: ('2025-06-28',)
2025-06-28 07:30:34,207 - INFO - MySQL查询成功，增量数据（日期: 2025-06-28），共获取 11 条记录
2025-06-28 07:30:34,207 - INFO - 获取到 1 个日期需要处理: ['2025-06-27']
2025-06-28 07:30:34,207 - INFO - 开始处理日期: 2025-06-27
2025-06-28 07:30:34,223 - INFO - Request Parameters - Page 1:
2025-06-28 07:30:34,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 07:30:34,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 07:30:40,363 - INFO - Response - Page 1:
2025-06-28 07:30:40,363 - INFO - 第 1 页获取到 50 条记录
2025-06-28 07:30:40,879 - INFO - Request Parameters - Page 2:
2025-06-28 07:30:40,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 07:30:40,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 07:30:49,004 - ERROR - 处理日期 2025-06-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 02F04CBB-52BF-76D1-8474-6DA24F847684 Response: {'code': 'ServiceUnavailable', 'requestid': '02F04CBB-52BF-76D1-8474-6DA24F847684', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 02F04CBB-52BF-76D1-8474-6DA24F847684)
2025-06-28 07:30:49,004 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-28 07:31:49,019 - INFO - 开始同步昨天与今天的销售数据: 2025-06-27 至 2025-06-28
2025-06-28 07:31:49,019 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-28 07:31:49,019 - INFO - 查询参数: ('2025-06-27', '2025-06-28')
2025-06-28 07:31:49,144 - INFO - MySQL查询成功，时间段: 2025-06-27 至 2025-06-28，共获取 169 条记录
2025-06-28 07:31:49,144 - INFO - 获取到 1 个日期需要处理: ['2025-06-27']
2025-06-28 07:31:49,144 - INFO - 开始处理日期: 2025-06-27
2025-06-28 07:31:49,144 - INFO - Request Parameters - Page 1:
2025-06-28 07:31:49,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 07:31:49,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 07:31:49,831 - INFO - Response - Page 1:
2025-06-28 07:31:49,831 - INFO - 第 1 页获取到 50 条记录
2025-06-28 07:31:50,331 - INFO - Request Parameters - Page 2:
2025-06-28 07:31:50,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 07:31:50,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 07:31:50,925 - INFO - Response - Page 2:
2025-06-28 07:31:50,941 - INFO - 第 2 页获取到 50 条记录
2025-06-28 07:31:51,441 - INFO - Request Parameters - Page 3:
2025-06-28 07:31:51,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 07:31:51,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 07:31:52,160 - INFO - Response - Page 3:
2025-06-28 07:31:52,160 - INFO - 第 3 页获取到 50 条记录
2025-06-28 07:31:52,675 - INFO - Request Parameters - Page 4:
2025-06-28 07:31:52,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 07:31:52,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 07:31:53,191 - INFO - Response - Page 4:
2025-06-28 07:31:53,191 - INFO - 第 4 页获取到 14 条记录
2025-06-28 07:31:53,706 - INFO - 查询完成，共获取到 164 条记录
2025-06-28 07:31:53,706 - INFO - 获取到 164 条表单数据
2025-06-28 07:31:53,706 - INFO - 当前日期 2025-06-27 有 164 条MySQL数据需要处理
2025-06-28 07:31:53,706 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 07:31:53,706 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 07:31:53,706 - INFO - 同步完成
2025-06-28 10:30:33,893 - INFO - 使用默认增量同步（当天更新数据）
2025-06-28 10:30:33,893 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-28 10:30:33,893 - INFO - 查询参数: ('2025-06-28',)
2025-06-28 10:30:34,034 - INFO - MySQL查询成功，增量数据（日期: 2025-06-28），共获取 105 条记录
2025-06-28 10:30:34,034 - INFO - 获取到 2 个日期需要处理: ['2025-06-27', '2025-06-28']
2025-06-28 10:30:34,034 - INFO - 开始处理日期: 2025-06-27
2025-06-28 10:30:34,034 - INFO - Request Parameters - Page 1:
2025-06-28 10:30:34,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 10:30:34,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 10:30:42,143 - ERROR - 处理日期 2025-06-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6A1DFD22-8089-79A1-9D13-C6661DF0668E Response: {'code': 'ServiceUnavailable', 'requestid': '6A1DFD22-8089-79A1-9D13-C6661DF0668E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6A1DFD22-8089-79A1-9D13-C6661DF0668E)
2025-06-28 10:30:42,143 - INFO - 开始处理日期: 2025-06-28
2025-06-28 10:30:42,143 - INFO - Request Parameters - Page 1:
2025-06-28 10:30:42,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 10:30:42,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 10:30:49,393 - INFO - Response - Page 1:
2025-06-28 10:30:49,393 - INFO - 查询完成，共获取到 0 条记录
2025-06-28 10:30:49,393 - INFO - 获取到 0 条表单数据
2025-06-28 10:30:49,393 - INFO - 当前日期 2025-06-28 有 1 条MySQL数据需要处理
2025-06-28 10:30:49,393 - INFO - 开始批量插入 1 条新记录
2025-06-28 10:30:49,549 - INFO - 批量插入响应状态码: 200
2025-06-28 10:30:49,549 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 02:30:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0643F57F-5CA5-728F-96E5-978C573545D6', 'x-acs-trace-id': 'd45f3dda92da6ee4f791dca9fda2da0d', 'etag': '5/E7SUPrTH1LJT2+jeEe7UQ9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 10:30:49,549 - INFO - 批量插入响应体: {'result': ['FINST-LLF66J71JROWOSO6B0VBJBB3C1963EWFJMFCM0']}
2025-06-28 10:30:49,549 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-28 10:30:49,549 - INFO - 成功插入的数据ID: ['FINST-LLF66J71JROWOSO6B0VBJBB3C1963EWFJMFCM0']
2025-06-28 10:30:54,565 - INFO - 批量插入完成，共 1 条记录
2025-06-28 10:30:54,565 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-28 10:30:54,565 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-06-28 10:31:54,580 - INFO - 开始同步昨天与今天的销售数据: 2025-06-27 至 2025-06-28
2025-06-28 10:31:54,580 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-28 10:31:54,580 - INFO - 查询参数: ('2025-06-27', '2025-06-28')
2025-06-28 10:31:54,720 - INFO - MySQL查询成功，时间段: 2025-06-27 至 2025-06-28，共获取 382 条记录
2025-06-28 10:31:54,720 - INFO - 获取到 2 个日期需要处理: ['2025-06-27', '2025-06-28']
2025-06-28 10:31:54,720 - INFO - 开始处理日期: 2025-06-27
2025-06-28 10:31:54,720 - INFO - Request Parameters - Page 1:
2025-06-28 10:31:54,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 10:31:54,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 10:31:55,502 - INFO - Response - Page 1:
2025-06-28 10:31:55,502 - INFO - 第 1 页获取到 50 条记录
2025-06-28 10:31:56,017 - INFO - Request Parameters - Page 2:
2025-06-28 10:31:56,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 10:31:56,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 10:31:56,689 - INFO - Response - Page 2:
2025-06-28 10:31:56,689 - INFO - 第 2 页获取到 50 条记录
2025-06-28 10:31:57,205 - INFO - Request Parameters - Page 3:
2025-06-28 10:31:57,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 10:31:57,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 10:31:57,892 - INFO - Response - Page 3:
2025-06-28 10:31:57,892 - INFO - 第 3 页获取到 50 条记录
2025-06-28 10:31:58,408 - INFO - Request Parameters - Page 4:
2025-06-28 10:31:58,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 10:31:58,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 10:31:58,955 - INFO - Response - Page 4:
2025-06-28 10:31:58,955 - INFO - 第 4 页获取到 14 条记录
2025-06-28 10:31:59,470 - INFO - 查询完成，共获取到 164 条记录
2025-06-28 10:31:59,470 - INFO - 获取到 164 条表单数据
2025-06-28 10:31:59,470 - INFO - 当前日期 2025-06-27 有 371 条MySQL数据需要处理
2025-06-28 10:31:59,470 - INFO - 开始批量插入 207 条新记录
2025-06-28 10:31:59,705 - INFO - 批量插入响应状态码: 200
2025-06-28 10:31:59,705 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 02:31:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '59B78916-3214-7BCE-A6F2-2E666FC5237F', 'x-acs-trace-id': 'a7db272219cf16b6fe934b644f110c29', 'etag': '2R7RBpxEp5RlOYJO1fKPJ6w2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 10:31:59,705 - INFO - 批量插入响应体: {'result': ['FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMMF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMNF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMOF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMPF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMQF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMRF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMSF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMTF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMUF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMVF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMWF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMXF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMYF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMZF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM0G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM1G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM2G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM3G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM4G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM5G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM6G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM7G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM8G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM9G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMAG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMBG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMCG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMDG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMEG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMFG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMGG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMHG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMIG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMJG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMKG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMLG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMMG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMNG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMOG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMPG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMQG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMRG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMSG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMTG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMUG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMVG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMWG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3B1YKMFCMXG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3B1YKMFCMYG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3B1YKMFCMZG']}
2025-06-28 10:31:59,705 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-28 10:31:59,705 - INFO - 成功插入的数据ID: ['FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMMF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMNF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMOF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMPF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMQF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMRF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMSF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMTF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMUF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMVF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMWF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMXF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMYF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMZF', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM0G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM1G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM2G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM3G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM4G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM5G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM6G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM7G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM8G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM9G', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMAG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMBG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMCG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMDG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMEG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMFG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMGG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMHG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMIG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMJG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMKG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMLG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMMG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMNG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMOG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMPG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMQG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMRG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMSG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMTG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMUG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMVG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMWG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3B1YKMFCMXG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3B1YKMFCMYG', 'FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3B1YKMFCMZG']
2025-06-28 10:32:04,970 - INFO - 批量插入响应状态码: 200
2025-06-28 10:32:04,970 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 02:32:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1BDD0291-D5C6-72D9-B91B-C0C5F8565DC4', 'x-acs-trace-id': '26780227a8880876831811513de5d4c8', 'etag': '2M/DUqxYkdcj8wRmaE4GdsA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 10:32:04,970 - INFO - 批量插入响应体: {'result': ['FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMC5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMD5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCME5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMF5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMG5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMH5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMI5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMJ5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMK5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCML5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMM5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMN5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMO5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMP5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMQ5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMR5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMS5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMT5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMU5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMV5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMW5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMX5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMY5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMZ5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM06', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM16', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM26', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM36', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM46', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM56', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM66', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM76', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM86', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM96', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMA6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMB6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMC6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMD6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCME6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMF6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMG6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMH6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMI6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMJ6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMK6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCML6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMM6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMN6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMO6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMP6']}
2025-06-28 10:32:04,970 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-28 10:32:04,970 - INFO - 成功插入的数据ID: ['FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMC5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMD5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCME5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMF5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMG5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMH5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMI5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMJ5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMK5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCML5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMM5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMN5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMO5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMP5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMQ5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMR5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMS5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMT5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMU5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMV5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMW5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMX5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMY5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMZ5', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM06', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM16', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM26', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM36', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM46', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM56', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM66', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM76', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM86', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCM96', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMA6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMB6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMC6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMD6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCME6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMF6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMG6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMH6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMI6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMJ6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMK6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCML6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMM6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMN6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMO6', 'FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMP6']
2025-06-28 10:32:10,236 - INFO - 批量插入响应状态码: 200
2025-06-28 10:32:10,236 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 02:32:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0BF8BB8B-C415-7DAA-AA92-30F6ED52ACAF', 'x-acs-trace-id': '6cd6f912b94d68524105d492bb5d4664', 'etag': '2HWAVg/7OmMo2IkrFFEpEew2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 10:32:10,236 - INFO - 批量插入响应体: {'result': ['FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMH7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMI7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMJ7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMK7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCML7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMM7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMN7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMO7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMP7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMQ7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMR7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMS7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMT7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMU7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMV7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMW7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMX7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMY7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMZ7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM08', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM18', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM28', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM38', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM48', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM58', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM68', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM78', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM88', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM98', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMA8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMB8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMC8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMD8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCME8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMF8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMG8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMH8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMI8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMJ8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMK8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCML8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMM8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMN8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMO8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMP8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMQ8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMR8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMS8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMT8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMU8']}
2025-06-28 10:32:10,236 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-28 10:32:10,236 - INFO - 成功插入的数据ID: ['FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMH7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMI7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMJ7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMK7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCML7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMM7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMN7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMO7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMP7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMQ7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMR7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMS7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMT7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMU7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMV7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMW7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMX7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMY7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMZ7', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM08', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM18', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM28', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM38', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM48', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM58', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM68', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM78', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM88', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM98', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMA8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMB8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMC8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMD8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCME8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMF8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMG8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMH8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMI8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMJ8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMK8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCML8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMM8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMN8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMO8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMP8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMQ8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMR8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMS8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMT8', 'FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCMU8']
2025-06-28 10:32:15,502 - INFO - 批量插入响应状态码: 200
2025-06-28 10:32:15,502 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 02:32:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4082FFD6-BB9C-7233-AA5A-7D677A1F1390', 'x-acs-trace-id': '45632b0f151c930548195d39a97d62d1', 'etag': '2ktHnbsZRA+DHG5WThQOrqA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 10:32:15,502 - INFO - 批量插入响应体: {'result': ['FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM96', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMA6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMB6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMC6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMD6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCME6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMF6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMG6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMH6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMI6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMJ6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMK6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCML6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMM6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMN6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMO6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMP6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMQ6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMR6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMS6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMT6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMU6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMV6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMW6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMX6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMY6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMZ6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM07', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM17', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM27', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM37', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM47', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM57', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM67', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM77', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM87', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM97', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMA7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMB7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMC7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMD7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCME7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMF7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMG7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMH7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMI7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMJ7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMK7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCML7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMM7']}
2025-06-28 10:32:15,502 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-06-28 10:32:15,502 - INFO - 成功插入的数据ID: ['FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM96', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMA6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMB6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMC6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMD6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCME6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMF6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMG6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMH6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMI6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMJ6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMK6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCML6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMM6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMN6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMO6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMP6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMQ6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMR6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMS6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMT6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMU6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMV6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMW6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMX6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMY6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMZ6', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM07', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM17', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM27', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM37', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM47', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM57', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM67', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM77', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM87', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCM97', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMA7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMB7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMC7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMD7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCME7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMF7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMG7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMH7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMI7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMJ7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMK7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCML7', 'FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCMM7']
2025-06-28 10:32:20,720 - INFO - 批量插入响应状态码: 200
2025-06-28 10:32:20,720 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 02:32:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '348', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BA396A5B-C8E3-72F0-A191-E9E3D67B429D', 'x-acs-trace-id': '0351dcaa6e38464566697c1550ea3576', 'etag': '3BeBH9T0YNHmmMoHJ4CJCgQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 10:32:20,720 - INFO - 批量插入响应体: {'result': ['FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCMX9', 'FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCMY9', 'FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCMZ9', 'FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCM0A', 'FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCM1A', 'FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCM2A', 'FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCM3A']}
2025-06-28 10:32:20,720 - INFO - 批量插入表单数据成功，批次 5，共 7 条记录
2025-06-28 10:32:20,720 - INFO - 成功插入的数据ID: ['FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCMX9', 'FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCMY9', 'FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCMZ9', 'FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCM0A', 'FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCM1A', 'FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCM2A', 'FINST-DUF66091N8OWULMYCXI84837WRUJ3W8ELMFCM3A']
2025-06-28 10:32:25,736 - INFO - 批量插入完成，共 207 条记录
2025-06-28 10:32:25,736 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 207 条，错误: 0 条
2025-06-28 10:32:25,736 - INFO - 开始处理日期: 2025-06-28
2025-06-28 10:32:25,736 - INFO - Request Parameters - Page 1:
2025-06-28 10:32:25,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 10:32:25,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 10:32:26,580 - INFO - Response - Page 1:
2025-06-28 10:32:26,580 - INFO - 第 1 页获取到 1 条记录
2025-06-28 10:32:27,080 - INFO - 查询完成，共获取到 1 条记录
2025-06-28 10:32:27,080 - INFO - 获取到 1 条表单数据
2025-06-28 10:32:27,080 - INFO - 当前日期 2025-06-28 有 1 条MySQL数据需要处理
2025-06-28 10:32:27,080 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 10:32:27,080 - INFO - 数据同步完成！更新: 0 条，插入: 207 条，错误: 0 条
2025-06-28 10:32:27,080 - INFO - 同步完成
2025-06-28 13:30:33,722 - INFO - 使用默认增量同步（当天更新数据）
2025-06-28 13:30:33,722 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-28 13:30:33,722 - INFO - 查询参数: ('2025-06-28',)
2025-06-28 13:30:33,864 - INFO - MySQL查询成功，增量数据（日期: 2025-06-28），共获取 141 条记录
2025-06-28 13:30:33,864 - INFO - 获取到 2 个日期需要处理: ['2025-06-27', '2025-06-28']
2025-06-28 13:30:33,864 - INFO - 开始处理日期: 2025-06-27
2025-06-28 13:30:33,880 - INFO - Request Parameters - Page 1:
2025-06-28 13:30:33,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 13:30:33,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 13:30:41,986 - ERROR - 处理日期 2025-06-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 736189C2-7C40-7BBB-883C-F409FED846C6 Response: {'code': 'ServiceUnavailable', 'requestid': '736189C2-7C40-7BBB-883C-F409FED846C6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 736189C2-7C40-7BBB-883C-F409FED846C6)
2025-06-28 13:30:41,986 - INFO - 开始处理日期: 2025-06-28
2025-06-28 13:30:41,986 - INFO - Request Parameters - Page 1:
2025-06-28 13:30:41,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 13:30:41,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 13:30:43,131 - INFO - Response - Page 1:
2025-06-28 13:30:43,131 - INFO - 第 1 页获取到 1 条记录
2025-06-28 13:30:43,641 - INFO - 查询完成，共获取到 1 条记录
2025-06-28 13:30:43,641 - INFO - 获取到 1 条表单数据
2025-06-28 13:30:43,641 - INFO - 当前日期 2025-06-28 有 1 条MySQL数据需要处理
2025-06-28 13:30:43,641 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 13:30:43,641 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-28 13:31:43,649 - INFO - 开始同步昨天与今天的销售数据: 2025-06-27 至 2025-06-28
2025-06-28 13:31:43,649 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-28 13:31:43,649 - INFO - 查询参数: ('2025-06-27', '2025-06-28')
2025-06-28 13:31:43,791 - INFO - MySQL查询成功，时间段: 2025-06-27 至 2025-06-28，共获取 485 条记录
2025-06-28 13:31:43,791 - INFO - 获取到 2 个日期需要处理: ['2025-06-27', '2025-06-28']
2025-06-28 13:31:43,791 - INFO - 开始处理日期: 2025-06-27
2025-06-28 13:31:43,791 - INFO - Request Parameters - Page 1:
2025-06-28 13:31:43,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 13:31:43,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 13:31:44,526 - INFO - Response - Page 1:
2025-06-28 13:31:44,526 - INFO - 第 1 页获取到 50 条记录
2025-06-28 13:31:45,034 - INFO - Request Parameters - Page 2:
2025-06-28 13:31:45,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 13:31:45,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 13:31:45,649 - INFO - Response - Page 2:
2025-06-28 13:31:45,649 - INFO - 第 2 页获取到 50 条记录
2025-06-28 13:31:46,158 - INFO - Request Parameters - Page 3:
2025-06-28 13:31:46,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 13:31:46,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 13:31:46,805 - INFO - Response - Page 3:
2025-06-28 13:31:46,805 - INFO - 第 3 页获取到 50 条记录
2025-06-28 13:31:47,315 - INFO - Request Parameters - Page 4:
2025-06-28 13:31:47,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 13:31:47,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 13:31:47,899 - INFO - Response - Page 4:
2025-06-28 13:31:47,899 - INFO - 第 4 页获取到 50 条记录
2025-06-28 13:31:48,408 - INFO - Request Parameters - Page 5:
2025-06-28 13:31:48,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 13:31:48,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 13:31:49,187 - INFO - Response - Page 5:
2025-06-28 13:31:49,187 - INFO - 第 5 页获取到 50 条记录
2025-06-28 13:31:49,691 - INFO - Request Parameters - Page 6:
2025-06-28 13:31:49,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 13:31:49,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 13:31:55,930 - INFO - Response - Page 6:
2025-06-28 13:31:55,930 - INFO - 第 6 页获取到 50 条记录
2025-06-28 13:31:56,439 - INFO - Request Parameters - Page 7:
2025-06-28 13:31:56,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 13:31:56,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 13:31:57,066 - INFO - Response - Page 7:
2025-06-28 13:31:57,066 - INFO - 第 7 页获取到 50 条记录
2025-06-28 13:31:57,576 - INFO - Request Parameters - Page 8:
2025-06-28 13:31:57,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 13:31:57,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 13:31:58,154 - INFO - Response - Page 8:
2025-06-28 13:31:58,154 - INFO - 第 8 页获取到 21 条记录
2025-06-28 13:31:58,668 - INFO - 查询完成，共获取到 371 条记录
2025-06-28 13:31:58,668 - INFO - 获取到 371 条表单数据
2025-06-28 13:31:58,668 - INFO - 当前日期 2025-06-27 有 473 条MySQL数据需要处理
2025-06-28 13:31:58,684 - INFO - 开始更新记录 - 表单实例ID: FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMC5
2025-06-28 13:31:59,160 - INFO - 更新表单数据成功: FINST-MUC66Q812DOWR2WB6FNTQDXAZYJQ3132LMFCMC5
2025-06-28 13:31:59,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 26000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 26000.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-28 13:31:59,160 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM9G
2025-06-28 13:31:59,645 - INFO - 更新表单数据成功: FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCM9G
2025-06-28 13:31:59,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 127782.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 127782.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/48621a226dde4c2cbbb4b044f4b29e8d.png?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=Crh%2Fxk%2BQwZlJDvBwt3tTXLfXMZo%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/746a0912cd664e40b39969850be32c93.png?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=pr0uDOBBnO%2FQzS8fekHtkJDOkO8%3D'}]
2025-06-28 13:31:59,645 - INFO - 开始批量插入 102 条新记录
2025-06-28 13:31:59,897 - INFO - 批量插入响应状态码: 200
2025-06-28 13:31:59,897 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 05:31:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '46A582E1-93E7-76F3-9BAA-B88AA1476EC2', 'x-acs-trace-id': 'ee19dc2446c6cee6cf0b5e35c423e95c', 'etag': '2fDT7KQwXiy8Sz0uTBywVHg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 13:31:59,897 - INFO - 批量插入响应体: {'result': ['FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM34', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM44', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM54', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM64', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM74', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM84', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM94', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMA4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMB4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMC4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMD4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCME4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMF4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMG4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMH4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMI4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMJ4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMK4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCML4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMM4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMN4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMO4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMP4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMQ4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMR4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMS4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMT4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMU4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMV4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMW4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMX4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMY4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMZ4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM05', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM15', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM25', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM35', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM45', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM55', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM65', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM75', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM85', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM95', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMA5', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMB5', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMC5', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMD5', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCME5', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMF5', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMG5']}
2025-06-28 13:31:59,897 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-28 13:31:59,897 - INFO - 成功插入的数据ID: ['FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM34', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM44', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM54', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM64', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM74', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM84', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCM94', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMA4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMB4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMC4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMD4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCME4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMF4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMG4', 'FINST-AI866781VNOWA061CZFNIAMW766F3AKF0TFCMH4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMI4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMJ4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMK4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCML4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMM4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMN4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMO4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMP4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMQ4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMR4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMS4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMT4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMU4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMV4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMW4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMX4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMY4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMZ4', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM05', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM15', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM25', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM35', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM45', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM55', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM65', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM75', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM85', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCM95', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMA5', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMB5', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMC5', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMD5', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCME5', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMF5', 'FINST-AI866781VNOWA061CZFNIAMW766F3BKF0TFCMG5']
2025-06-28 13:32:05,131 - INFO - 批量插入响应状态码: 200
2025-06-28 13:32:05,131 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 05:32:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E5202FD3-533C-7601-B2C0-4C323BEECC74', 'x-acs-trace-id': '1092a36ede716f9cb1aa3db2799db022', 'etag': '2z5zRwhwhWcjM7U4w/nV5Ow2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 13:32:05,131 - INFO - 批量插入响应体: {'result': ['FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM0B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM1B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM2B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM3B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM4B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM5B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM6B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM7B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM8B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM9B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMAB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMBB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMCB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMDB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMEB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMFB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMGB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMHB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMIB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMJB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMKB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMLB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMMB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMNB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMOB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMPB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMQB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMRB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMSB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMTB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMUB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMVB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMWB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMXB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMYB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMZB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM0C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM1C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM2C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM3C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM4C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM5C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM6C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM7C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM8C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM9C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMAC', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2RLJ0TFCMBC', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2RLJ0TFCMCC', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2RLJ0TFCMDC']}
2025-06-28 13:32:05,131 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-28 13:32:05,131 - INFO - 成功插入的数据ID: ['FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM0B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM1B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM2B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM3B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM4B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM5B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM6B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM7B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM8B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM9B', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMAB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMBB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMCB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMDB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMEB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMFB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMGB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMHB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMIB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMJB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMKB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMLB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMMB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMNB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMOB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMPB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMQB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMRB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMSB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMTB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMUB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMVB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMWB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMXB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMYB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMZB', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM0C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM1C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM2C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM3C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM4C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM5C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM6C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM7C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM8C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCM9C', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2QLJ0TFCMAC', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2RLJ0TFCMBC', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2RLJ0TFCMCC', 'FINST-LLF66FD1O6OW8VAA8IVSJ8MQ9TSE2RLJ0TFCMDC']
2025-06-28 13:32:10,285 - INFO - 批量插入响应状态码: 200
2025-06-28 13:32:10,285 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 05:32:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1A9EFCA0-BB80-79FC-BEA7-64717A944268', 'x-acs-trace-id': 'ea2063d071abb6017d9bda08a606ebf4', 'etag': '11f+4NxzKWHCf7cmvgmhJsA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 13:32:10,285 - INFO - 批量插入响应体: {'result': ['FINST-3PF66X61J8OWC8J5FI2R57GZAS6C38LN0TFCM1G', 'FINST-3PF66X61J8OWC8J5FI2R57GZAS6C38LN0TFCM2G']}
2025-06-28 13:32:10,285 - INFO - 批量插入表单数据成功，批次 3，共 2 条记录
2025-06-28 13:32:10,285 - INFO - 成功插入的数据ID: ['FINST-3PF66X61J8OWC8J5FI2R57GZAS6C38LN0TFCM1G', 'FINST-3PF66X61J8OWC8J5FI2R57GZAS6C38LN0TFCM2G']
2025-06-28 13:32:15,289 - INFO - 批量插入完成，共 102 条记录
2025-06-28 13:32:15,289 - INFO - 日期 2025-06-27 处理完成 - 更新: 2 条，插入: 102 条，错误: 0 条
2025-06-28 13:32:15,289 - INFO - 开始处理日期: 2025-06-28
2025-06-28 13:32:15,289 - INFO - Request Parameters - Page 1:
2025-06-28 13:32:15,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 13:32:15,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 13:32:15,733 - INFO - Response - Page 1:
2025-06-28 13:32:15,733 - INFO - 第 1 页获取到 1 条记录
2025-06-28 13:32:16,238 - INFO - 查询完成，共获取到 1 条记录
2025-06-28 13:32:16,238 - INFO - 获取到 1 条表单数据
2025-06-28 13:32:16,238 - INFO - 当前日期 2025-06-28 有 1 条MySQL数据需要处理
2025-06-28 13:32:16,238 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 13:32:16,238 - INFO - 数据同步完成！更新: 2 条，插入: 102 条，错误: 0 条
2025-06-28 13:32:16,238 - INFO - 同步完成
2025-06-28 16:30:33,642 - INFO - 使用默认增量同步（当天更新数据）
2025-06-28 16:30:33,642 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-28 16:30:33,642 - INFO - 查询参数: ('2025-06-28',)
2025-06-28 16:30:33,768 - INFO - MySQL查询成功，增量数据（日期: 2025-06-28），共获取 143 条记录
2025-06-28 16:30:33,768 - INFO - 获取到 2 个日期需要处理: ['2025-06-27', '2025-06-28']
2025-06-28 16:30:33,785 - INFO - 开始处理日期: 2025-06-27
2025-06-28 16:30:33,785 - INFO - Request Parameters - Page 1:
2025-06-28 16:30:33,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:30:33,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:30:40,120 - INFO - Response - Page 1:
2025-06-28 16:30:40,120 - INFO - 第 1 页获取到 50 条记录
2025-06-28 16:30:40,625 - INFO - Request Parameters - Page 2:
2025-06-28 16:30:40,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:30:40,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:30:48,745 - ERROR - 处理日期 2025-06-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C6FBE116-CA90-7CE6-A234-FAEF92EA1CBA Response: {'code': 'ServiceUnavailable', 'requestid': 'C6FBE116-CA90-7CE6-A234-FAEF92EA1CBA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C6FBE116-CA90-7CE6-A234-FAEF92EA1CBA)
2025-06-28 16:30:48,745 - INFO - 开始处理日期: 2025-06-28
2025-06-28 16:30:48,745 - INFO - Request Parameters - Page 1:
2025-06-28 16:30:48,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:30:48,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:30:50,009 - INFO - Response - Page 1:
2025-06-28 16:30:50,009 - INFO - 第 1 页获取到 1 条记录
2025-06-28 16:30:50,519 - INFO - 查询完成，共获取到 1 条记录
2025-06-28 16:30:50,519 - INFO - 获取到 1 条表单数据
2025-06-28 16:30:50,519 - INFO - 当前日期 2025-06-28 有 1 条MySQL数据需要处理
2025-06-28 16:30:50,519 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 16:30:50,519 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-28 16:31:50,521 - INFO - 开始同步昨天与今天的销售数据: 2025-06-27 至 2025-06-28
2025-06-28 16:31:50,521 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-28 16:31:50,521 - INFO - 查询参数: ('2025-06-27', '2025-06-28')
2025-06-28 16:31:50,662 - INFO - MySQL查询成功，时间段: 2025-06-27 至 2025-06-28，共获取 487 条记录
2025-06-28 16:31:50,662 - INFO - 获取到 2 个日期需要处理: ['2025-06-27', '2025-06-28']
2025-06-28 16:31:50,662 - INFO - 开始处理日期: 2025-06-27
2025-06-28 16:31:50,662 - INFO - Request Parameters - Page 1:
2025-06-28 16:31:50,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:31:50,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:31:51,360 - INFO - Response - Page 1:
2025-06-28 16:31:51,360 - INFO - 第 1 页获取到 50 条记录
2025-06-28 16:31:51,864 - INFO - Request Parameters - Page 2:
2025-06-28 16:31:51,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:31:51,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:31:52,516 - INFO - Response - Page 2:
2025-06-28 16:31:52,516 - INFO - 第 2 页获取到 50 条记录
2025-06-28 16:31:53,021 - INFO - Request Parameters - Page 3:
2025-06-28 16:31:53,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:31:53,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:31:53,676 - INFO - Response - Page 3:
2025-06-28 16:31:53,676 - INFO - 第 3 页获取到 50 条记录
2025-06-28 16:31:54,180 - INFO - Request Parameters - Page 4:
2025-06-28 16:31:54,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:31:54,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:31:54,848 - INFO - Response - Page 4:
2025-06-28 16:31:54,848 - INFO - 第 4 页获取到 50 条记录
2025-06-28 16:31:55,358 - INFO - Request Parameters - Page 5:
2025-06-28 16:31:55,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:31:55,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:31:55,973 - INFO - Response - Page 5:
2025-06-28 16:31:55,973 - INFO - 第 5 页获取到 50 条记录
2025-06-28 16:31:56,487 - INFO - Request Parameters - Page 6:
2025-06-28 16:31:56,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:31:56,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:31:57,169 - INFO - Response - Page 6:
2025-06-28 16:31:57,169 - INFO - 第 6 页获取到 50 条记录
2025-06-28 16:31:57,685 - INFO - Request Parameters - Page 7:
2025-06-28 16:31:57,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:31:57,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:31:58,338 - INFO - Response - Page 7:
2025-06-28 16:31:58,338 - INFO - 第 7 页获取到 50 条记录
2025-06-28 16:31:58,842 - INFO - Request Parameters - Page 8:
2025-06-28 16:31:58,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:31:58,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:31:59,493 - INFO - Response - Page 8:
2025-06-28 16:31:59,493 - INFO - 第 8 页获取到 50 条记录
2025-06-28 16:31:59,997 - INFO - Request Parameters - Page 9:
2025-06-28 16:31:59,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:31:59,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:32:00,718 - INFO - Response - Page 9:
2025-06-28 16:32:00,718 - INFO - 第 9 页获取到 50 条记录
2025-06-28 16:32:01,229 - INFO - Request Parameters - Page 10:
2025-06-28 16:32:01,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:32:01,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:32:01,834 - INFO - Response - Page 10:
2025-06-28 16:32:01,834 - INFO - 第 10 页获取到 23 条记录
2025-06-28 16:32:02,339 - INFO - 查询完成，共获取到 473 条记录
2025-06-28 16:32:02,339 - INFO - 获取到 473 条表单数据
2025-06-28 16:32:02,339 - INFO - 当前日期 2025-06-27 有 475 条MySQL数据需要处理
2025-06-28 16:32:02,355 - INFO - 开始批量插入 2 条新记录
2025-06-28 16:32:02,509 - INFO - 批量插入响应状态码: 200
2025-06-28 16:32:02,509 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 08:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5DC2881D-193D-7C3C-AE5D-E8F728FDD9F2', 'x-acs-trace-id': '0bf4ae5436bc19e653e3677f78572c46', 'etag': '1fn709elXMUzr1piiclgR/Q8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 16:32:02,509 - INFO - 批量插入响应体: {'result': ['FINST-XXF66LA1Q8OWHW8TAI7A3CJZ7USC27ZYFZFCMFA', 'FINST-XXF66LA1Q8OWHW8TAI7A3CJZ7USC27ZYFZFCMGA']}
2025-06-28 16:32:02,509 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-28 16:32:02,509 - INFO - 成功插入的数据ID: ['FINST-XXF66LA1Q8OWHW8TAI7A3CJZ7USC27ZYFZFCMFA', 'FINST-XXF66LA1Q8OWHW8TAI7A3CJZ7USC27ZYFZFCMGA']
2025-06-28 16:32:07,510 - INFO - 批量插入完成，共 2 条记录
2025-06-28 16:32:07,510 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-28 16:32:07,510 - INFO - 开始处理日期: 2025-06-28
2025-06-28 16:32:07,510 - INFO - Request Parameters - Page 1:
2025-06-28 16:32:07,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 16:32:07,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 16:32:07,951 - INFO - Response - Page 1:
2025-06-28 16:32:07,951 - INFO - 第 1 页获取到 1 条记录
2025-06-28 16:32:08,454 - INFO - 查询完成，共获取到 1 条记录
2025-06-28 16:32:08,454 - INFO - 获取到 1 条表单数据
2025-06-28 16:32:08,454 - INFO - 当前日期 2025-06-28 有 1 条MySQL数据需要处理
2025-06-28 16:32:08,454 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 16:32:08,454 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-06-28 16:32:08,454 - INFO - 同步完成
2025-06-28 19:30:33,982 - INFO - 使用默认增量同步（当天更新数据）
2025-06-28 19:30:33,982 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-28 19:30:33,982 - INFO - 查询参数: ('2025-06-28',)
2025-06-28 19:30:34,123 - INFO - MySQL查询成功，增量数据（日期: 2025-06-28），共获取 143 条记录
2025-06-28 19:30:34,123 - INFO - 获取到 2 个日期需要处理: ['2025-06-27', '2025-06-28']
2025-06-28 19:30:34,123 - INFO - 开始处理日期: 2025-06-27
2025-06-28 19:30:34,139 - INFO - Request Parameters - Page 1:
2025-06-28 19:30:34,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:30:34,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:30:40,385 - INFO - Response - Page 1:
2025-06-28 19:30:40,385 - INFO - 第 1 页获取到 50 条记录
2025-06-28 19:30:40,896 - INFO - Request Parameters - Page 2:
2025-06-28 19:30:40,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:30:40,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:30:41,590 - INFO - Response - Page 2:
2025-06-28 19:30:41,590 - INFO - 第 2 页获取到 50 条记录
2025-06-28 19:30:42,102 - INFO - Request Parameters - Page 3:
2025-06-28 19:30:42,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:30:42,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:30:42,784 - INFO - Response - Page 3:
2025-06-28 19:30:42,784 - INFO - 第 3 页获取到 50 条记录
2025-06-28 19:30:43,288 - INFO - Request Parameters - Page 4:
2025-06-28 19:30:43,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:30:43,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:30:51,395 - ERROR - 处理日期 2025-06-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A2FE4C12-537F-77B1-AEEF-5E9717598B36 Response: {'code': 'ServiceUnavailable', 'requestid': 'A2FE4C12-537F-77B1-AEEF-5E9717598B36', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A2FE4C12-537F-77B1-AEEF-5E9717598B36)
2025-06-28 19:30:51,395 - INFO - 开始处理日期: 2025-06-28
2025-06-28 19:30:51,395 - INFO - Request Parameters - Page 1:
2025-06-28 19:30:51,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:30:51,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:30:51,844 - INFO - Response - Page 1:
2025-06-28 19:30:51,844 - INFO - 第 1 页获取到 1 条记录
2025-06-28 19:30:52,348 - INFO - 查询完成，共获取到 1 条记录
2025-06-28 19:30:52,348 - INFO - 获取到 1 条表单数据
2025-06-28 19:30:52,348 - INFO - 当前日期 2025-06-28 有 1 条MySQL数据需要处理
2025-06-28 19:30:52,348 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 19:30:52,348 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-28 19:31:52,378 - INFO - 开始同步昨天与今天的销售数据: 2025-06-27 至 2025-06-28
2025-06-28 19:31:52,378 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-28 19:31:52,378 - INFO - 查询参数: ('2025-06-27', '2025-06-28')
2025-06-28 19:31:52,519 - INFO - MySQL查询成功，时间段: 2025-06-27 至 2025-06-28，共获取 487 条记录
2025-06-28 19:31:52,519 - INFO - 获取到 2 个日期需要处理: ['2025-06-27', '2025-06-28']
2025-06-28 19:31:52,519 - INFO - 开始处理日期: 2025-06-27
2025-06-28 19:31:52,519 - INFO - Request Parameters - Page 1:
2025-06-28 19:31:52,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:31:52,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:31:53,229 - INFO - Response - Page 1:
2025-06-28 19:31:53,229 - INFO - 第 1 页获取到 50 条记录
2025-06-28 19:31:53,738 - INFO - Request Parameters - Page 2:
2025-06-28 19:31:53,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:31:53,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:31:54,425 - INFO - Response - Page 2:
2025-06-28 19:31:54,425 - INFO - 第 2 页获取到 50 条记录
2025-06-28 19:31:54,936 - INFO - Request Parameters - Page 3:
2025-06-28 19:31:54,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:31:54,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:31:55,556 - INFO - Response - Page 3:
2025-06-28 19:31:55,556 - INFO - 第 3 页获取到 50 条记录
2025-06-28 19:31:56,060 - INFO - Request Parameters - Page 4:
2025-06-28 19:31:56,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:31:56,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:31:56,713 - INFO - Response - Page 4:
2025-06-28 19:31:56,713 - INFO - 第 4 页获取到 50 条记录
2025-06-28 19:31:57,227 - INFO - Request Parameters - Page 5:
2025-06-28 19:31:57,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:31:57,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:31:57,833 - INFO - Response - Page 5:
2025-06-28 19:31:57,833 - INFO - 第 5 页获取到 50 条记录
2025-06-28 19:31:58,336 - INFO - Request Parameters - Page 6:
2025-06-28 19:31:58,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:31:58,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:31:58,985 - INFO - Response - Page 6:
2025-06-28 19:31:58,985 - INFO - 第 6 页获取到 50 条记录
2025-06-28 19:31:59,500 - INFO - Request Parameters - Page 7:
2025-06-28 19:31:59,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:31:59,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:32:00,133 - INFO - Response - Page 7:
2025-06-28 19:32:00,133 - INFO - 第 7 页获取到 50 条记录
2025-06-28 19:32:00,634 - INFO - Request Parameters - Page 8:
2025-06-28 19:32:00,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:32:00,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:32:01,264 - INFO - Response - Page 8:
2025-06-28 19:32:01,264 - INFO - 第 8 页获取到 50 条记录
2025-06-28 19:32:01,772 - INFO - Request Parameters - Page 9:
2025-06-28 19:32:01,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:32:01,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:32:02,403 - INFO - Response - Page 9:
2025-06-28 19:32:02,419 - INFO - 第 9 页获取到 50 条记录
2025-06-28 19:32:02,933 - INFO - Request Parameters - Page 10:
2025-06-28 19:32:02,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:32:02,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:32:03,521 - INFO - Response - Page 10:
2025-06-28 19:32:03,521 - INFO - 第 10 页获取到 25 条记录
2025-06-28 19:32:04,032 - INFO - 查询完成，共获取到 475 条记录
2025-06-28 19:32:04,032 - INFO - 获取到 475 条表单数据
2025-06-28 19:32:04,032 - INFO - 当前日期 2025-06-27 有 475 条MySQL数据需要处理
2025-06-28 19:32:04,049 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 19:32:04,049 - INFO - 开始处理日期: 2025-06-28
2025-06-28 19:32:04,049 - INFO - Request Parameters - Page 1:
2025-06-28 19:32:04,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 19:32:04,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 19:32:04,504 - INFO - Response - Page 1:
2025-06-28 19:32:04,504 - INFO - 第 1 页获取到 1 条记录
2025-06-28 19:32:05,008 - INFO - 查询完成，共获取到 1 条记录
2025-06-28 19:32:05,008 - INFO - 获取到 1 条表单数据
2025-06-28 19:32:05,008 - INFO - 当前日期 2025-06-28 有 1 条MySQL数据需要处理
2025-06-28 19:32:05,008 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 19:32:05,008 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 19:32:05,008 - INFO - 同步完成
2025-06-28 22:30:34,156 - INFO - 使用默认增量同步（当天更新数据）
2025-06-28 22:30:34,156 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-28 22:30:34,156 - INFO - 查询参数: ('2025-06-28',)
2025-06-28 22:30:34,296 - INFO - MySQL查询成功，增量数据（日期: 2025-06-28），共获取 216 条记录
2025-06-28 22:30:34,296 - INFO - 获取到 2 个日期需要处理: ['2025-06-27', '2025-06-28']
2025-06-28 22:30:34,296 - INFO - 开始处理日期: 2025-06-27
2025-06-28 22:30:34,296 - INFO - Request Parameters - Page 1:
2025-06-28 22:30:34,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:30:34,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:30:42,440 - ERROR - 处理日期 2025-06-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-FFE0-7D70-AC9B-2E74E66A3232 Response: {'code': 'ServiceUnavailable', 'requestid': '********-FFE0-7D70-AC9B-2E74E66A3232', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-FFE0-7D70-AC9B-2E74E66A3232)
2025-06-28 22:30:42,440 - INFO - 开始处理日期: 2025-06-28
2025-06-28 22:30:42,440 - INFO - Request Parameters - Page 1:
2025-06-28 22:30:42,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:30:42,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:30:43,566 - INFO - Response - Page 1:
2025-06-28 22:30:43,566 - INFO - 第 1 页获取到 1 条记录
2025-06-28 22:30:44,082 - INFO - 查询完成，共获取到 1 条记录
2025-06-28 22:30:44,082 - INFO - 获取到 1 条表单数据
2025-06-28 22:30:44,082 - INFO - 当前日期 2025-06-28 有 71 条MySQL数据需要处理
2025-06-28 22:30:44,082 - INFO - 开始批量插入 70 条新记录
2025-06-28 22:30:44,347 - INFO - 批量插入响应状态码: 200
2025-06-28 22:30:44,347 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 14:30:39 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B86EDDAC-5D54-78AE-B5AE-B10B8C5F59E6', 'x-acs-trace-id': '96795095e8078ba576bb6c0caaaca99d', 'etag': '2lK26ag3JMcIvS8zlsyfTMQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 22:30:44,347 - INFO - 批量插入响应体: {'result': ['FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMY4', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMZ4', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM05', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM15', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM25', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM35', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM45', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM55', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM65', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM75', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM85', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM95', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMA5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMB5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMC5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMD5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCME5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMF5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMG5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMH5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMI5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMJ5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMK5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCML5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMM5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMN5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMO5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMP5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMQ5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMR5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMS5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMT5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMU5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMV5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMW5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMX5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMY5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMZ5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM06', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM16', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM26', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM36', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM46', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM56', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM66', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM76', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM86', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM96', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMA6', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMB6']}
2025-06-28 22:30:44,347 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-28 22:30:44,347 - INFO - 成功插入的数据ID: ['FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMY4', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMZ4', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM05', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM15', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM25', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM35', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM45', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM55', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM65', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM75', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM85', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM95', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMA5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMB5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMC5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMD5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCME5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMF5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMG5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMH5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMI5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMJ5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMK5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCML5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMM5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMN5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMO5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMP5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMQ5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMR5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMS5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMT5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMU5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMV5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMW5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMX5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMY5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMZ5', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM06', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM16', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM26', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM36', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM46', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM56', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM66', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM76', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM86', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCM96', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMA6', 'FINST-RI766091TNOWNMIID93YGCIJMHPE3VT59CGCMB6']
2025-06-28 22:30:49,568 - INFO - 批量插入响应状态码: 200
2025-06-28 22:30:49,568 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 14:30:45 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '972', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D2F170E4-A25A-74F0-8827-98F4736B4FE0', 'x-acs-trace-id': '510420fc069049bc049692019b0bee6d', 'etag': '9Fmow/tTAdJrNoyK3+rcouA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 22:30:49,568 - INFO - 批量插入响应体: {'result': ['FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMNE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMOE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMPE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMQE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMRE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMSE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMTE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMUE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMVE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMWE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMXE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMYE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMZE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM0F', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM1F', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM2F', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM3F', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM4F', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM5F', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM6F']}
2025-06-28 22:30:49,568 - INFO - 批量插入表单数据成功，批次 2，共 20 条记录
2025-06-28 22:30:49,568 - INFO - 成功插入的数据ID: ['FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMNE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMOE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMPE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMQE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMRE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMSE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMTE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMUE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMVE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMWE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMXE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMYE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCMZE', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM0F', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM1F', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM2F', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM3F', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM4F', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM5F', 'FINST-XMC66R91U4OW0GP3DLF8A71W0SLS3NU99CGCM6F']
2025-06-28 22:30:54,586 - INFO - 批量插入完成，共 70 条记录
2025-06-28 22:30:54,586 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 70 条，错误: 0 条
2025-06-28 22:30:54,586 - INFO - 数据同步完成！更新: 0 条，插入: 70 条，错误: 1 条
2025-06-28 22:31:54,625 - INFO - 开始同步昨天与今天的销售数据: 2025-06-27 至 2025-06-28
2025-06-28 22:31:54,625 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-28 22:31:54,625 - INFO - 查询参数: ('2025-06-27', '2025-06-28')
2025-06-28 22:31:54,781 - INFO - MySQL查询成功，时间段: 2025-06-27 至 2025-06-28，共获取 566 条记录
2025-06-28 22:31:54,781 - INFO - 获取到 2 个日期需要处理: ['2025-06-27', '2025-06-28']
2025-06-28 22:31:54,781 - INFO - 开始处理日期: 2025-06-27
2025-06-28 22:31:54,797 - INFO - Request Parameters - Page 1:
2025-06-28 22:31:54,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:31:54,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:31:55,485 - INFO - Response - Page 1:
2025-06-28 22:31:55,485 - INFO - 第 1 页获取到 50 条记录
2025-06-28 22:31:56,001 - INFO - Request Parameters - Page 2:
2025-06-28 22:31:56,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:31:56,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:31:56,782 - INFO - Response - Page 2:
2025-06-28 22:31:56,782 - INFO - 第 2 页获取到 50 条记录
2025-06-28 22:31:57,282 - INFO - Request Parameters - Page 3:
2025-06-28 22:31:57,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:31:57,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:31:57,923 - INFO - Response - Page 3:
2025-06-28 22:31:57,923 - INFO - 第 3 页获取到 50 条记录
2025-06-28 22:31:58,439 - INFO - Request Parameters - Page 4:
2025-06-28 22:31:58,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:31:58,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:31:59,111 - INFO - Response - Page 4:
2025-06-28 22:31:59,111 - INFO - 第 4 页获取到 50 条记录
2025-06-28 22:31:59,627 - INFO - Request Parameters - Page 5:
2025-06-28 22:31:59,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:31:59,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:32:05,567 - INFO - Response - Page 5:
2025-06-28 22:32:05,567 - INFO - 第 5 页获取到 50 条记录
2025-06-28 22:32:06,067 - INFO - Request Parameters - Page 6:
2025-06-28 22:32:06,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:32:06,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:32:06,755 - INFO - Response - Page 6:
2025-06-28 22:32:06,755 - INFO - 第 6 页获取到 50 条记录
2025-06-28 22:32:07,255 - INFO - Request Parameters - Page 7:
2025-06-28 22:32:07,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:32:07,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:32:07,896 - INFO - Response - Page 7:
2025-06-28 22:32:07,896 - INFO - 第 7 页获取到 50 条记录
2025-06-28 22:32:08,412 - INFO - Request Parameters - Page 8:
2025-06-28 22:32:08,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:32:08,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:32:09,131 - INFO - Response - Page 8:
2025-06-28 22:32:09,131 - INFO - 第 8 页获取到 50 条记录
2025-06-28 22:32:09,647 - INFO - Request Parameters - Page 9:
2025-06-28 22:32:09,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:32:09,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:32:10,209 - INFO - Response - Page 9:
2025-06-28 22:32:10,209 - INFO - 第 9 页获取到 50 条记录
2025-06-28 22:32:10,725 - INFO - Request Parameters - Page 10:
2025-06-28 22:32:10,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:32:10,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:32:11,288 - INFO - Response - Page 10:
2025-06-28 22:32:11,288 - INFO - 第 10 页获取到 25 条记录
2025-06-28 22:32:11,804 - INFO - 查询完成，共获取到 475 条记录
2025-06-28 22:32:11,804 - INFO - 获取到 475 条表单数据
2025-06-28 22:32:11,804 - INFO - 当前日期 2025-06-27 有 475 条MySQL数据需要处理
2025-06-28 22:32:11,819 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 22:32:11,819 - INFO - 开始处理日期: 2025-06-28
2025-06-28 22:32:11,819 - INFO - Request Parameters - Page 1:
2025-06-28 22:32:11,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:32:11,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:32:12,460 - INFO - Response - Page 1:
2025-06-28 22:32:12,460 - INFO - 第 1 页获取到 50 条记录
2025-06-28 22:32:12,976 - INFO - Request Parameters - Page 2:
2025-06-28 22:32:12,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 22:32:12,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 22:32:13,601 - INFO - Response - Page 2:
2025-06-28 22:32:13,601 - INFO - 第 2 页获取到 21 条记录
2025-06-28 22:32:14,117 - INFO - 查询完成，共获取到 71 条记录
2025-06-28 22:32:14,117 - INFO - 获取到 71 条表单数据
2025-06-28 22:32:14,117 - INFO - 当前日期 2025-06-28 有 77 条MySQL数据需要处理
2025-06-28 22:32:14,117 - INFO - 开始批量插入 6 条新记录
2025-06-28 22:32:14,274 - INFO - 批量插入响应状态码: 200
2025-06-28 22:32:14,274 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 28 Jun 2025 14:32:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '300', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CD6FF338-9B03-7302-A1D6-CBDEAD028E81', 'x-acs-trace-id': '583764234a3ffa237c054222a958d46f', 'etag': '3a+5IT8pi8hMExDpUWUhk/Q0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-28 22:32:14,274 - INFO - 批量插入响应体: {'result': ['FINST-WBF66B81E9OW0GRCD6E8R5ZZDNOH3M63BCGCMMK', 'FINST-WBF66B81E9OW0GRCD6E8R5ZZDNOH3M63BCGCMNK', 'FINST-WBF66B81E9OW0GRCD6E8R5ZZDNOH3M63BCGCMOK', 'FINST-WBF66B81E9OW0GRCD6E8R5ZZDNOH3M63BCGCMPK', 'FINST-WBF66B81E9OW0GRCD6E8R5ZZDNOH3M63BCGCMQK', 'FINST-WBF66B81E9OW0GRCD6E8R5ZZDNOH3M63BCGCMRK']}
2025-06-28 22:32:14,274 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-06-28 22:32:14,274 - INFO - 成功插入的数据ID: ['FINST-WBF66B81E9OW0GRCD6E8R5ZZDNOH3M63BCGCMMK', 'FINST-WBF66B81E9OW0GRCD6E8R5ZZDNOH3M63BCGCMNK', 'FINST-WBF66B81E9OW0GRCD6E8R5ZZDNOH3M63BCGCMOK', 'FINST-WBF66B81E9OW0GRCD6E8R5ZZDNOH3M63BCGCMPK', 'FINST-WBF66B81E9OW0GRCD6E8R5ZZDNOH3M63BCGCMQK', 'FINST-WBF66B81E9OW0GRCD6E8R5ZZDNOH3M63BCGCMRK']
2025-06-28 22:32:19,291 - INFO - 批量插入完成，共 6 条记录
2025-06-28 22:32:19,291 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 6 条，错误: 0 条
2025-06-28 22:32:19,291 - INFO - 数据同步完成！更新: 0 条，插入: 6 条，错误: 0 条
2025-06-28 22:32:19,291 - INFO - 同步完成
