# -*- coding: utf-8 -*-
"""
MySQL与宜搭数据同步框架快速启动脚本
"""
import sys
import argparse
from mysql2yida_sync_framework import SyncConfig, MySQL2YidaSyncClient

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='MySQL与宜搭数据同步框架快速启动',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python quick_start.py --type sales                    # 使用默认销售配置同步
  python quick_start.py --type device                   # 使用默认设备配置同步
  python quick_start.py --config my_config.json         # 使用自定义配置同步
  python quick_start.py --create-config my_config.json  # 创建配置文件模板
        """
    )
    
    parser.add_argument('--type', choices=['sales', 'device'], 
                       help='同步类型: sales(销售数据) 或 device(设备数据)')
    parser.add_argument('--config', type=str, 
                       help='自定义配置文件路径')
    parser.add_argument('--create-config', type=str, 
                       help='创建配置文件模板')
    
    args = parser.parse_args()
    
    # 创建配置文件模板
    if args.create_config:
        config = SyncConfig()
        config.save_to_file(args.create_config)
        print(f"配置文件模板已创建: {args.create_config}")
        print("请根据实际需求修改配置文件后再运行同步")
        return
    
    # 确定使用的配置
    config_file = None
    if args.config:
        config_file = args.config
    elif args.type == 'sales':
        config_file = 'sync_config_example.json'
    elif args.type == 'device':
        config_file = 'sync_config_devices.json'
    else:
        print("错误: 请指定同步类型 (--type) 或配置文件 (--config)")
        parser.print_help()
        return
    
    try:
        print(f"使用配置文件: {config_file}")
        
        # 加载配置
        config = SyncConfig(config_file)
        
        # 创建同步客户端并执行同步
        sync_client = MySQL2YidaSyncClient(config)
        sync_client.sync_data()
        
        print("同步完成！")
        
    except FileNotFoundError:
        print(f"错误: 配置文件 {config_file} 不存在")
        print("请先创建配置文件或使用 --create-config 创建模板")
    except Exception as e:
        print(f"同步失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
