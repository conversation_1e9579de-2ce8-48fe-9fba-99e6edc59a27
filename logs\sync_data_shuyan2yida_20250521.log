2025-05-21 08:00:03,570 - INFO - ==================================================
2025-05-21 08:00:03,570 - INFO - 程序启动 - 版本 v1.0.0
2025-05-21 08:00:03,570 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250521.log
2025-05-21 08:00:03,571 - INFO - ==================================================
2025-05-21 08:00:03,571 - INFO - 程序入口点: __main__
2025-05-21 08:00:03,571 - INFO - ==================================================
2025-05-21 08:00:03,571 - INFO - 程序启动 - 版本 v1.0.1
2025-05-21 08:00:03,572 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250521.log
2025-05-21 08:00:03,572 - INFO - ==================================================
2025-05-21 08:00:03,892 - INFO - 数据库文件已存在: data\sales_data.db
2025-05-21 08:00:03,893 - INFO - sales_data表已存在，无需创建
2025-05-21 08:00:03,894 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-05-21 08:00:03,894 - INFO - DataSyncManager初始化完成
2025-05-21 08:00:03,894 - INFO - 未提供日期参数，使用默认值
2025-05-21 08:00:03,895 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-05-21 08:00:03,895 - INFO - 开始综合数据同步流程...
2025-05-21 08:00:03,895 - INFO - 正在获取数衍平台日销售数据...
2025-05-21 08:00:03,895 - INFO - 查询数衍平台数据，时间段为: 2025-03-21, 2025-05-20
2025-05-21 08:00:03,895 - INFO - 正在获取********至********的数据
2025-05-21 08:00:03,895 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-21 08:00:03,896 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B791454D1BEC4755EAFCDF1E66CC9178'}
2025-05-21 08:00:11,297 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-21 08:00:11,310 - INFO - 过滤后保留 1567 条记录
2025-05-21 08:00:13,311 - INFO - 正在获取********至********的数据
2025-05-21 08:00:13,311 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-21 08:00:13,312 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '3331F3E822836986DF56EEF7174C0B4D'}
2025-05-21 08:00:17,312 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-21 08:00:17,325 - INFO - 过滤后保留 1535 条记录
2025-05-21 08:00:19,327 - INFO - 正在获取********至********的数据
2025-05-21 08:00:19,327 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-21 08:00:19,328 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6B95F51D42C2E380AB0BFAFED7624249'}
2025-05-21 08:00:22,644 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-21 08:00:22,656 - INFO - 过滤后保留 1497 条记录
2025-05-21 08:00:24,658 - INFO - 正在获取********至********的数据
2025-05-21 08:00:24,658 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-21 08:00:24,659 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AC14DD2CBEF4F88F29BCD2EDDE5767BC'}
2025-05-21 08:00:27,548 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-21 08:00:27,563 - INFO - 过滤后保留 1509 条记录
2025-05-21 08:00:29,564 - INFO - 正在获取********至********的数据
2025-05-21 08:00:29,564 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-21 08:00:29,565 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '448B23FBF2D6ED9A45E64CBDFEF22933'}
2025-05-21 08:00:32,813 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-21 08:00:32,827 - INFO - 过滤后保留 1487 条记录
2025-05-21 08:00:34,828 - INFO - 正在获取********至********的数据
2025-05-21 08:00:34,828 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-21 08:00:34,829 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5E0C51F05A6EE1F2BBA304B3D1844FB2'}
2025-05-21 08:00:37,450 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-21 08:00:37,463 - INFO - 过滤后保留 1498 条记录
2025-05-21 08:00:39,464 - INFO - 正在获取********至********的数据
2025-05-21 08:00:39,464 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-21 08:00:39,464 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4F82E074263849D38021593B0D35296B'}
2025-05-21 08:00:41,806 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-21 08:00:41,819 - INFO - 过滤后保留 1459 条记录
2025-05-21 08:00:43,821 - INFO - 正在获取********至********的数据
2025-05-21 08:00:43,821 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-21 08:00:43,822 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '816A41FD24FAF037787F0CBB09B993F9'}
2025-05-21 08:00:46,488 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-21 08:00:46,501 - INFO - 过滤后保留 1489 条记录
2025-05-21 08:00:48,503 - INFO - 正在获取********至********的数据
2025-05-21 08:00:48,503 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-21 08:00:48,504 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0F3DDA0BE09550DE10C41333EA025AD4'}
2025-05-21 08:00:50,083 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-21 08:00:50,093 - INFO - 过滤后保留 1055 条记录
2025-05-21 08:00:52,094 - INFO - 开始保存数据到SQLite数据库，共 13096 条记录待处理
2025-05-21 08:00:52,585 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B358872E0DBE420182AF77D4C47644F6, sale_time=2025-04-22
2025-05-21 08:00:52,585 - INFO - 变更字段: recommend_amount: 3511.33 -> 3470.33, amount: 3511 -> 3470
2025-05-21 08:00:52,650 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B358872E0DBE420182AF77D4C47644F6, sale_time=2025-05-01
2025-05-21 08:00:52,650 - INFO - 变更字段: recommend_amount: 15127.1 -> 15095.06, amount: 15127 -> 15095
2025-05-21 08:00:52,654 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEBHLLHVNF0I86N3H2U102001F98, sale_time=2025-04-27
2025-05-21 08:00:52,654 - INFO - 变更字段: amount: 5189 -> 5249, count: 19 -> 20, instore_amount: 5156.4 -> 5216.4, instore_count: 18 -> 19
2025-05-21 08:00:52,713 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-04
2025-05-21 08:00:52,713 - INFO - 变更字段: amount: 3896 -> 3596, instore_amount: 3896.2 -> 3596.2
2025-05-21 08:00:52,716 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B358872E0DBE420182AF77D4C47644F6, sale_time=2025-05-03
2025-05-21 08:00:52,717 - INFO - 变更字段: recommend_amount: 11946.08 -> 11920.08, amount: 11946 -> 11920
2025-05-21 08:00:52,717 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B358872E0DBE420182AF77D4C47644F6, sale_time=2025-05-02
2025-05-21 08:00:52,717 - INFO - 变更字段: recommend_amount: 10570.56 -> 10487.56, amount: 10570 -> 10487
2025-05-21 08:00:52,779 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B358872E0DBE420182AF77D4C47644F6, sale_time=2025-05-09
2025-05-21 08:00:52,780 - INFO - 变更字段: recommend_amount: 4089.1 -> 4021.06, amount: 4089 -> 4021
2025-05-21 08:00:52,799 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDMFCJQF4F0I86N3H2U1UC001E7I, sale_time=2025-05-09
2025-05-21 08:00:52,800 - INFO - 变更字段: recommend_amount: 4907.59 -> 5413.39, amount: 4907 -> 5413, count: 79 -> 80, instore_amount: 2281.99 -> 2787.79, instore_count: 43 -> 44
2025-05-21 08:00:52,816 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-19
2025-05-21 08:00:52,817 - INFO - 变更字段: recommend_amount: 0.0 -> 8155.7, daily_bill_amount: 0.0 -> 8155.7
2025-05-21 08:00:52,826 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDTG15Q4SH7Q2OV4FVC7CO001499, sale_time=2025-05-19
2025-05-21 08:00:52,827 - INFO - 变更字段: amount: 3796 -> 3856, count: 229 -> 230, online_amount: 1879.71 -> 1939.71, online_count: 117 -> 118
2025-05-21 08:00:52,828 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDSD911K3E7Q2OV4FVC7C800148P, sale_time=2025-05-19
2025-05-21 08:00:52,828 - INFO - 变更字段: amount: 2893 -> 2907, count: 127 -> 128, instore_amount: 2924.24 -> 2938.34, instore_count: 127 -> 128
2025-05-21 08:00:52,833 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTAKS3N4EI7Q2OVBN4IS76001D38, sale_time=2025-05-19
2025-05-21 08:00:52,833 - INFO - 变更字段: recommend_amount: 43675.0 -> 43879.0, amount: 43675 -> 43879, count: 22 -> 23, instore_amount: 43675.0 -> 43879.0, instore_count: 22 -> 23
2025-05-21 08:00:52,835 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINSC23H1J3M7Q2OV392410L00148H, sale_time=2025-05-19
2025-05-21 08:00:52,836 - INFO - 变更字段: amount: 1532 -> 1830, count: 6 -> 7, instore_amount: 1532.0 -> 1830.0, instore_count: 6 -> 7
2025-05-21 08:00:52,840 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-19
2025-05-21 08:00:52,841 - INFO - 变更字段: recommend_amount: 1999.93 -> 2025.87, amount: 1999 -> 2025, count: 130 -> 134, instore_amount: 549.4 -> 562.34, instore_count: 36 -> 38, online_amount: 1453.11 -> 1466.11, online_count: 94 -> 96
2025-05-21 08:00:52,844 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-19
2025-05-21 08:00:52,844 - INFO - 变更字段: recommend_amount: 7465.04 -> 7557.54, amount: 7465 -> 7557, count: 173 -> 174, online_amount: 1867.13 -> 1959.63, online_count: 36 -> 37
2025-05-21 08:00:52,844 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-18
2025-05-21 08:00:52,846 - INFO - 变更字段: recommend_amount: 11439.98 -> 11658.98, amount: 11439 -> 11658, count: 193 -> 194, instore_amount: 10392.64 -> 10611.64, instore_count: 176 -> 177
2025-05-21 08:00:52,846 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-19
2025-05-21 08:00:52,846 - INFO - 变更字段: amount: 4737 -> 4753, count: 263 -> 264, online_amount: 1937.51 -> 1953.61, online_count: 116 -> 117
2025-05-21 08:00:52,848 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B358872E0DBE420182AF77D4C47644F6, sale_time=2025-05-17
2025-05-21 08:00:52,848 - INFO - 变更字段: recommend_amount: 12531.84 -> 12483.44, amount: 12531 -> 12483
2025-05-21 08:00:52,850 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-19
2025-05-21 08:00:52,850 - INFO - 变更字段: recommend_amount: 3701.08 -> 4814.76, amount: 3701 -> 4814, count: 165 -> 226, instore_amount: 919.48 -> 1316.85, instore_count: 37 -> 57, online_amount: 2805.0 -> 3639.01, online_count: 128 -> 169
2025-05-21 08:00:52,850 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-18
2025-05-21 08:00:52,850 - INFO - 变更字段: recommend_amount: 5181.97 -> 5189.97, amount: 5181 -> 5189, count: 270 -> 271, online_amount: 3573.78 -> 3581.78, online_count: 179 -> 180
2025-05-21 08:00:52,851 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-17
2025-05-21 08:00:52,851 - INFO - 变更字段: recommend_amount: 6587.65 -> 6599.15, amount: 6587 -> 6599, count: 310 -> 311, online_amount: 4428.67 -> 4440.17, online_count: 207 -> 208
2025-05-21 08:00:52,851 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEC0U8246I0I86N3H2U10A001F9G, sale_time=2025-05-19
2025-05-21 08:00:52,852 - INFO - 变更字段: amount: 3165 -> 3188, count: 85 -> 86, instore_amount: 1473.61 -> 1496.61, instore_count: 15 -> 16
2025-05-21 08:00:52,853 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-05-19
2025-05-21 08:00:52,853 - INFO - 变更字段: recommend_amount: 0.0 -> 20091.86, daily_bill_amount: 0.0 -> 20091.86
2025-05-21 08:00:52,854 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEAIN5DMKK0I86N3H2U1VH001F8N, sale_time=2025-05-19
2025-05-21 08:00:52,854 - INFO - 变更字段: amount: -6372 -> -6440
2025-05-21 08:00:52,855 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEA4041D6F0I86N3H2U1V9001F8F, sale_time=2025-05-19
2025-05-21 08:00:52,856 - INFO - 变更字段: amount: 13573 -> 13787, count: 154 -> 155, instore_amount: 11551.09 -> 11765.09, instore_count: 82 -> 83
2025-05-21 08:00:52,860 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0A2N9KG60I86N3H2U1QB001F3H, sale_time=2025-05-19
2025-05-21 08:00:52,860 - INFO - 变更字段: amount: 3789 -> 3827, count: 175 -> 176, online_amount: 2511.82 -> 2549.82, online_count: 91 -> 92
2025-05-21 08:00:52,862 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVBILA3OJ0I86N3H2U1PQ001F30, sale_time=2025-05-19
2025-05-21 08:00:52,862 - INFO - 变更字段: amount: 14137 -> 14144, count: 211 -> 212, instore_amount: 8827.19 -> 8835.1, instore_count: 60 -> 61
2025-05-21 08:00:52,862 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-05-19
2025-05-21 08:00:52,863 - INFO - 变更字段: recommend_amount: 1118.52 -> 5165.64, daily_bill_amount: 1118.52 -> 5165.64, amount: 259 -> 509, count: 32 -> 60, instore_amount: 345.9 -> 600.3, instore_count: 32 -> 60
2025-05-21 08:00:52,863 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-05-18
2025-05-21 08:00:52,863 - INFO - 变更字段: recommend_amount: 0.0 -> 6482.15, daily_bill_amount: 0.0 -> 6482.15, amount: 1743 -> 1816, count: 181 -> 188, instore_amount: 1823.1 -> 1896.1, instore_count: 181 -> 188
2025-05-21 08:00:52,863 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-05-17
2025-05-21 08:00:52,864 - INFO - 变更字段: recommend_amount: 0.0 -> 6046.42, daily_bill_amount: 0.0 -> 6046.42, amount: 1527 -> 1917, count: 146 -> 181, instore_amount: 1580.9 -> 1975.4, instore_count: 146 -> 181
2025-05-21 08:00:52,867 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDF8HFHI690I86N3H2U1H9001EQF, sale_time=2025-05-19
2025-05-21 08:00:52,868 - INFO - 变更字段: amount: 230 -> 409, count: 2 -> 3, instore_amount: 230.0 -> 409.0, instore_count: 2 -> 3
2025-05-21 08:00:52,869 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-19
2025-05-21 08:00:52,869 - INFO - 变更字段: amount: 12519 -> 13951, count: 14 -> 15, instore_amount: 14025.0 -> 15457.2, instore_count: 14 -> 15
2025-05-21 08:00:52,871 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9D9OBM41R0I86N3H2U17K001EGQ, sale_time=2025-05-19
2025-05-21 08:00:52,871 - INFO - 变更字段: recommend_amount: 0.0 -> 4491.49, daily_bill_amount: 0.0 -> 4491.49
2025-05-21 08:00:52,872 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDVBEGSM760I86N3H2U12H001EBN, sale_time=2025-05-19
2025-05-21 08:00:52,873 - INFO - 变更字段: recommend_amount: 2888.0 -> 3186.0, amount: 2888 -> 3186, count: 2 -> 3, instore_amount: 2888.0 -> 3186.0, instore_count: 2 -> 3
2025-05-21 08:00:52,873 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-05-19
2025-05-21 08:00:52,874 - INFO - 变更字段: amount: 2757 -> 2769, count: 215 -> 217, online_amount: 2519.95 -> 2531.85, online_count: 199 -> 201
2025-05-21 08:00:52,876 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDRR6FJ7A60I86N3H2U10L001E9R, sale_time=2025-05-19
2025-05-21 08:00:52,876 - INFO - 变更字段: recommend_amount: 1577.78 -> 1590.78, amount: 1577 -> 1590, count: 84 -> 85, instore_amount: 502.28 -> 515.28, instore_count: 39 -> 40
2025-05-21 08:00:52,877 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-19
2025-05-21 08:00:52,878 - INFO - 变更字段: amount: 6071 -> 6080, count: 364 -> 366, instore_amount: 4721.1 -> 4776.75, instore_count: 267 -> 272, online_amount: 1490.84 -> 1445.0, online_count: 97 -> 94
2025-05-21 08:00:52,878 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-18
2025-05-21 08:00:52,878 - INFO - 变更字段: instore_amount: 6014.89 -> 6092.9, instore_count: 409 -> 414, online_amount: 1583.51 -> 1505.5, online_count: 108 -> 103
2025-05-21 08:00:52,879 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-19
2025-05-21 08:00:52,880 - INFO - 变更字段: amount: 4231 -> 4279, online_amount: 2135.26 -> 2184.03
2025-05-21 08:00:52,882 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-05-18
2025-05-21 08:00:52,882 - INFO - 变更字段: recommend_amount: 10607.75 -> 11281.76, amount: 10607 -> 11281, count: 429 -> 450, online_amount: 10803.65 -> 11477.66, online_count: 429 -> 450
2025-05-21 08:00:52,882 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-05-17
2025-05-21 08:00:52,883 - INFO - 变更字段: recommend_amount: 699.34 -> 709.74, amount: 699 -> 709, count: 49 -> 50, online_amount: 764.88 -> 775.28, online_count: 49 -> 50
2025-05-21 08:00:52,885 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MR50JEM3SR7Q2OVAE57DM4001Q85, sale_time=2025-05-19
2025-05-21 08:00:52,885 - INFO - 变更字段: amount: 11002 -> 10942
2025-05-21 08:00:52,887 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-19
2025-05-21 08:00:52,887 - INFO - 变更字段: recommend_amount: 2626.35 -> 2608.37, amount: 2626 -> 2608
2025-05-21 08:00:52,893 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-05-19
2025-05-21 08:00:52,893 - INFO - 变更字段: amount: 22081 -> 22750, count: 124 -> 125, instore_amount: 19622.0 -> 20291.0, instore_count: 95 -> 96
2025-05-21 08:00:52,899 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPK0KE3MN6E7AERKQ83JB001UND, sale_time=2025-05-19
2025-05-21 08:00:52,899 - INFO - 变更字段: amount: 4253 -> 4302, count: 143 -> 144, instore_amount: 1079.35 -> 1128.23, instore_count: 37 -> 38
2025-05-21 08:00:52,901 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-19
2025-05-21 08:00:52,901 - INFO - 变更字段: amount: 1284 -> 1243
2025-05-21 08:00:53,119 - INFO - SQLite数据保存完成，统计信息：
2025-05-21 08:00:53,119 - INFO - - 总记录数: 13096
2025-05-21 08:00:53,120 - INFO - - 成功插入: 212
2025-05-21 08:00:53,120 - INFO - - 成功更新: 46
2025-05-21 08:00:53,120 - INFO - - 无需更新: 12838
2025-05-21 08:00:53,120 - INFO - - 处理失败: 0
2025-05-21 08:00:58,483 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250521.xlsx
2025-05-21 08:00:58,491 - INFO - 成功获取数衍平台数据，共 13096 条记录
2025-05-21 08:00:58,492 - INFO - 正在更新SQLite月度汇总数据...
2025-05-21 08:00:58,499 - INFO - 月度数据sqllite清空完成
2025-05-21 08:00:58,753 - INFO - 月度汇总数据更新完成，处理了 1190 条汇总记录
2025-05-21 08:00:58,753 - INFO - 成功更新月度汇总数据，共 1190 条记录
2025-05-21 08:00:58,753 - INFO - 正在获取宜搭日销售表单数据...
2025-05-21 08:00:58,754 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-21 00:00:00 至 2025-05-20 23:59:59
2025-05-21 08:00:58,754 - INFO - 查询分段 1: 2025-03-21 至 2025-03-27
2025-05-21 08:00:58,754 - INFO - 查询日期范围: 2025-03-21 至 2025-03-27，使用分页查询，每页 100 条记录
2025-05-21 08:00:58,754 - INFO - Request Parameters - Page 1:
2025-05-21 08:00:58,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:00:58,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:03,851 - INFO - API请求耗时: 5096ms
2025-05-21 08:01:03,851 - INFO - Response - Page 1
2025-05-21 08:01:03,852 - INFO - 第 1 页获取到 100 条记录
2025-05-21 08:01:04,352 - INFO - Request Parameters - Page 2:
2025-05-21 08:01:04,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:04,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:07,593 - INFO - API请求耗时: 3240ms
2025-05-21 08:01:07,594 - INFO - Response - Page 2
2025-05-21 08:01:07,594 - INFO - 第 2 页获取到 100 条记录
2025-05-21 08:01:08,095 - INFO - Request Parameters - Page 3:
2025-05-21 08:01:08,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:08,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:08,810 - INFO - API请求耗时: 714ms
2025-05-21 08:01:08,811 - INFO - Response - Page 3
2025-05-21 08:01:08,811 - INFO - 第 3 页获取到 100 条记录
2025-05-21 08:01:09,312 - INFO - Request Parameters - Page 4:
2025-05-21 08:01:09,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:09,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:10,018 - INFO - API请求耗时: 705ms
2025-05-21 08:01:10,019 - INFO - Response - Page 4
2025-05-21 08:01:10,019 - INFO - 第 4 页获取到 100 条记录
2025-05-21 08:01:10,521 - INFO - Request Parameters - Page 5:
2025-05-21 08:01:10,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:10,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:11,229 - INFO - API请求耗时: 707ms
2025-05-21 08:01:11,229 - INFO - Response - Page 5
2025-05-21 08:01:11,230 - INFO - 第 5 页获取到 100 条记录
2025-05-21 08:01:11,730 - INFO - Request Parameters - Page 6:
2025-05-21 08:01:11,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:11,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:12,460 - INFO - API请求耗时: 729ms
2025-05-21 08:01:12,460 - INFO - Response - Page 6
2025-05-21 08:01:12,461 - INFO - 第 6 页获取到 100 条记录
2025-05-21 08:01:12,962 - INFO - Request Parameters - Page 7:
2025-05-21 08:01:12,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:12,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:13,576 - INFO - API请求耗时: 613ms
2025-05-21 08:01:13,576 - INFO - Response - Page 7
2025-05-21 08:01:13,577 - INFO - 第 7 页获取到 100 条记录
2025-05-21 08:01:14,078 - INFO - Request Parameters - Page 8:
2025-05-21 08:01:14,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:14,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:14,767 - INFO - API请求耗时: 687ms
2025-05-21 08:01:14,767 - INFO - Response - Page 8
2025-05-21 08:01:14,768 - INFO - 第 8 页获取到 100 条记录
2025-05-21 08:01:15,269 - INFO - Request Parameters - Page 9:
2025-05-21 08:01:15,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:15,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:15,952 - INFO - API请求耗时: 682ms
2025-05-21 08:01:15,953 - INFO - Response - Page 9
2025-05-21 08:01:15,953 - INFO - 第 9 页获取到 100 条记录
2025-05-21 08:01:16,453 - INFO - Request Parameters - Page 10:
2025-05-21 08:01:16,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:16,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:17,212 - INFO - API请求耗时: 758ms
2025-05-21 08:01:17,213 - INFO - Response - Page 10
2025-05-21 08:01:17,213 - INFO - 第 10 页获取到 100 条记录
2025-05-21 08:01:17,715 - INFO - Request Parameters - Page 11:
2025-05-21 08:01:17,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:17,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:18,403 - INFO - API请求耗时: 687ms
2025-05-21 08:01:18,403 - INFO - Response - Page 11
2025-05-21 08:01:18,404 - INFO - 第 11 页获取到 100 条记录
2025-05-21 08:01:18,904 - INFO - Request Parameters - Page 12:
2025-05-21 08:01:18,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:18,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:19,644 - INFO - API请求耗时: 738ms
2025-05-21 08:01:19,644 - INFO - Response - Page 12
2025-05-21 08:01:19,645 - INFO - 第 12 页获取到 100 条记录
2025-05-21 08:01:20,146 - INFO - Request Parameters - Page 13:
2025-05-21 08:01:20,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:20,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:20,820 - INFO - API请求耗时: 672ms
2025-05-21 08:01:20,820 - INFO - Response - Page 13
2025-05-21 08:01:20,820 - INFO - 第 13 页获取到 100 条记录
2025-05-21 08:01:21,321 - INFO - Request Parameters - Page 14:
2025-05-21 08:01:21,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:21,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:22,132 - INFO - API请求耗时: 810ms
2025-05-21 08:01:22,132 - INFO - Response - Page 14
2025-05-21 08:01:22,133 - INFO - 第 14 页获取到 100 条记录
2025-05-21 08:01:22,634 - INFO - Request Parameters - Page 15:
2025-05-21 08:01:22,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:22,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:23,296 - INFO - API请求耗时: 661ms
2025-05-21 08:01:23,297 - INFO - Response - Page 15
2025-05-21 08:01:23,297 - INFO - 第 15 页获取到 100 条记录
2025-05-21 08:01:23,799 - INFO - Request Parameters - Page 16:
2025-05-21 08:01:23,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:23,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:24,461 - INFO - API请求耗时: 661ms
2025-05-21 08:01:24,462 - INFO - Response - Page 16
2025-05-21 08:01:24,462 - INFO - 第 16 页获取到 100 条记录
2025-05-21 08:01:24,964 - INFO - Request Parameters - Page 17:
2025-05-21 08:01:24,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:24,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:25,641 - INFO - API请求耗时: 676ms
2025-05-21 08:01:25,641 - INFO - Response - Page 17
2025-05-21 08:01:25,642 - INFO - 第 17 页获取到 100 条记录
2025-05-21 08:01:26,143 - INFO - Request Parameters - Page 18:
2025-05-21 08:01:26,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:26,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:27,097 - INFO - API请求耗时: 953ms
2025-05-21 08:01:27,097 - INFO - Response - Page 18
2025-05-21 08:01:27,098 - INFO - 第 18 页获取到 100 条记录
2025-05-21 08:01:27,599 - INFO - Request Parameters - Page 19:
2025-05-21 08:01:27,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:27,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:28,444 - INFO - API请求耗时: 845ms
2025-05-21 08:01:28,445 - INFO - Response - Page 19
2025-05-21 08:01:28,445 - INFO - 第 19 页获取到 100 条记录
2025-05-21 08:01:28,947 - INFO - Request Parameters - Page 20:
2025-05-21 08:01:28,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:28,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:29,632 - INFO - API请求耗时: 684ms
2025-05-21 08:01:29,632 - INFO - Response - Page 20
2025-05-21 08:01:29,633 - INFO - 第 20 页获取到 100 条记录
2025-05-21 08:01:30,133 - INFO - Request Parameters - Page 21:
2025-05-21 08:01:30,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:30,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:30,763 - INFO - API请求耗时: 629ms
2025-05-21 08:01:30,763 - INFO - Response - Page 21
2025-05-21 08:01:30,764 - INFO - 第 21 页获取到 100 条记录
2025-05-21 08:01:31,265 - INFO - Request Parameters - Page 22:
2025-05-21 08:01:31,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:31,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:31,877 - INFO - API请求耗时: 611ms
2025-05-21 08:01:31,877 - INFO - Response - Page 22
2025-05-21 08:01:31,878 - INFO - 第 22 页获取到 100 条记录
2025-05-21 08:01:32,379 - INFO - Request Parameters - Page 23:
2025-05-21 08:01:32,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:32,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:33,066 - INFO - API请求耗时: 687ms
2025-05-21 08:01:33,067 - INFO - Response - Page 23
2025-05-21 08:01:33,068 - INFO - 第 23 页获取到 100 条记录
2025-05-21 08:01:33,569 - INFO - Request Parameters - Page 24:
2025-05-21 08:01:33,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:33,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:34,425 - INFO - API请求耗时: 854ms
2025-05-21 08:01:34,425 - INFO - Response - Page 24
2025-05-21 08:01:34,426 - INFO - 第 24 页获取到 100 条记录
2025-05-21 08:01:34,926 - INFO - Request Parameters - Page 25:
2025-05-21 08:01:34,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:34,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742486400754, 1743004800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:35,501 - INFO - API请求耗时: 574ms
2025-05-21 08:01:35,502 - INFO - Response - Page 25
2025-05-21 08:01:35,502 - INFO - 第 25 页获取到 61 条记录
2025-05-21 08:01:35,502 - INFO - 查询完成，共获取到 2461 条记录
2025-05-21 08:01:35,503 - INFO - 分段 1 查询成功，获取到 2461 条记录
2025-05-21 08:01:36,504 - INFO - 查询分段 2: 2025-03-28 至 2025-04-03
2025-05-21 08:01:36,504 - INFO - 查询日期范围: 2025-03-28 至 2025-04-03，使用分页查询，每页 100 条记录
2025-05-21 08:01:36,505 - INFO - Request Parameters - Page 1:
2025-05-21 08:01:36,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:36,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:37,308 - INFO - API请求耗时: 803ms
2025-05-21 08:01:37,309 - INFO - Response - Page 1
2025-05-21 08:01:37,309 - INFO - 第 1 页获取到 100 条记录
2025-05-21 08:01:37,811 - INFO - Request Parameters - Page 2:
2025-05-21 08:01:37,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:37,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:38,408 - INFO - API请求耗时: 597ms
2025-05-21 08:01:38,409 - INFO - Response - Page 2
2025-05-21 08:01:38,409 - INFO - 第 2 页获取到 100 条记录
2025-05-21 08:01:38,909 - INFO - Request Parameters - Page 3:
2025-05-21 08:01:38,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:38,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:39,599 - INFO - API请求耗时: 688ms
2025-05-21 08:01:39,600 - INFO - Response - Page 3
2025-05-21 08:01:39,600 - INFO - 第 3 页获取到 100 条记录
2025-05-21 08:01:40,101 - INFO - Request Parameters - Page 4:
2025-05-21 08:01:40,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:40,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:40,773 - INFO - API请求耗时: 672ms
2025-05-21 08:01:40,774 - INFO - Response - Page 4
2025-05-21 08:01:40,774 - INFO - 第 4 页获取到 100 条记录
2025-05-21 08:01:41,276 - INFO - Request Parameters - Page 5:
2025-05-21 08:01:41,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:41,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:41,998 - INFO - API请求耗时: 720ms
2025-05-21 08:01:41,998 - INFO - Response - Page 5
2025-05-21 08:01:41,999 - INFO - 第 5 页获取到 100 条记录
2025-05-21 08:01:42,500 - INFO - Request Parameters - Page 6:
2025-05-21 08:01:42,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:42,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:43,141 - INFO - API请求耗时: 640ms
2025-05-21 08:01:43,141 - INFO - Response - Page 6
2025-05-21 08:01:43,142 - INFO - 第 6 页获取到 100 条记录
2025-05-21 08:01:43,643 - INFO - Request Parameters - Page 7:
2025-05-21 08:01:43,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:43,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:44,443 - INFO - API请求耗时: 799ms
2025-05-21 08:01:44,443 - INFO - Response - Page 7
2025-05-21 08:01:44,444 - INFO - 第 7 页获取到 100 条记录
2025-05-21 08:01:44,945 - INFO - Request Parameters - Page 8:
2025-05-21 08:01:44,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:44,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:45,748 - INFO - API请求耗时: 802ms
2025-05-21 08:01:45,748 - INFO - Response - Page 8
2025-05-21 08:01:45,749 - INFO - 第 8 页获取到 100 条记录
2025-05-21 08:01:46,250 - INFO - Request Parameters - Page 9:
2025-05-21 08:01:46,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:46,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:46,933 - INFO - API请求耗时: 682ms
2025-05-21 08:01:46,934 - INFO - Response - Page 9
2025-05-21 08:01:46,934 - INFO - 第 9 页获取到 100 条记录
2025-05-21 08:01:47,436 - INFO - Request Parameters - Page 10:
2025-05-21 08:01:47,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:47,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:48,254 - INFO - API请求耗时: 817ms
2025-05-21 08:01:48,255 - INFO - Response - Page 10
2025-05-21 08:01:48,255 - INFO - 第 10 页获取到 100 条记录
2025-05-21 08:01:48,756 - INFO - Request Parameters - Page 11:
2025-05-21 08:01:48,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:48,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:49,348 - INFO - API请求耗时: 591ms
2025-05-21 08:01:49,349 - INFO - Response - Page 11
2025-05-21 08:01:49,349 - INFO - 第 11 页获取到 100 条记录
2025-05-21 08:01:49,851 - INFO - Request Parameters - Page 12:
2025-05-21 08:01:49,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:49,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:50,585 - INFO - API请求耗时: 733ms
2025-05-21 08:01:50,585 - INFO - Response - Page 12
2025-05-21 08:01:50,586 - INFO - 第 12 页获取到 100 条记录
2025-05-21 08:01:51,087 - INFO - Request Parameters - Page 13:
2025-05-21 08:01:51,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:51,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:51,716 - INFO - API请求耗时: 628ms
2025-05-21 08:01:51,717 - INFO - Response - Page 13
2025-05-21 08:01:51,717 - INFO - 第 13 页获取到 100 条记录
2025-05-21 08:01:52,219 - INFO - Request Parameters - Page 14:
2025-05-21 08:01:52,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:52,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:52,872 - INFO - API请求耗时: 652ms
2025-05-21 08:01:52,873 - INFO - Response - Page 14
2025-05-21 08:01:52,874 - INFO - 第 14 页获取到 100 条记录
2025-05-21 08:01:53,375 - INFO - Request Parameters - Page 15:
2025-05-21 08:01:53,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:53,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:54,145 - INFO - API请求耗时: 770ms
2025-05-21 08:01:54,145 - INFO - Response - Page 15
2025-05-21 08:01:54,146 - INFO - 第 15 页获取到 100 条记录
2025-05-21 08:01:54,646 - INFO - Request Parameters - Page 16:
2025-05-21 08:01:54,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:54,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:55,341 - INFO - API请求耗时: 693ms
2025-05-21 08:01:55,341 - INFO - Response - Page 16
2025-05-21 08:01:55,342 - INFO - 第 16 页获取到 100 条记录
2025-05-21 08:01:55,843 - INFO - Request Parameters - Page 17:
2025-05-21 08:01:55,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:55,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:56,499 - INFO - API请求耗时: 655ms
2025-05-21 08:01:56,500 - INFO - Response - Page 17
2025-05-21 08:01:56,500 - INFO - 第 17 页获取到 100 条记录
2025-05-21 08:01:57,001 - INFO - Request Parameters - Page 18:
2025-05-21 08:01:57,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:57,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:57,594 - INFO - API请求耗时: 591ms
2025-05-21 08:01:57,594 - INFO - Response - Page 18
2025-05-21 08:01:57,595 - INFO - 第 18 页获取到 100 条记录
2025-05-21 08:01:58,095 - INFO - Request Parameters - Page 19:
2025-05-21 08:01:58,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:58,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:01:58,818 - INFO - API请求耗时: 723ms
2025-05-21 08:01:58,818 - INFO - Response - Page 19
2025-05-21 08:01:58,819 - INFO - 第 19 页获取到 100 条记录
2025-05-21 08:01:59,320 - INFO - Request Parameters - Page 20:
2025-05-21 08:01:59,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:01:59,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:00,070 - INFO - API请求耗时: 749ms
2025-05-21 08:02:00,070 - INFO - Response - Page 20
2025-05-21 08:02:00,071 - INFO - 第 20 页获取到 100 条记录
2025-05-21 08:02:00,573 - INFO - Request Parameters - Page 21:
2025-05-21 08:02:00,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:00,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:01,432 - INFO - API请求耗时: 857ms
2025-05-21 08:02:01,432 - INFO - Response - Page 21
2025-05-21 08:02:01,433 - INFO - 第 21 页获取到 100 条记录
2025-05-21 08:02:01,934 - INFO - Request Parameters - Page 22:
2025-05-21 08:02:01,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:01,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200754, 1743609600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:02,658 - INFO - API请求耗时: 724ms
2025-05-21 08:02:02,658 - INFO - Response - Page 22
2025-05-21 08:02:02,659 - INFO - 第 22 页获取到 81 条记录
2025-05-21 08:02:02,659 - INFO - 查询完成，共获取到 2181 条记录
2025-05-21 08:02:02,660 - INFO - 分段 2 查询成功，获取到 2181 条记录
2025-05-21 08:02:03,661 - INFO - 查询分段 3: 2025-04-04 至 2025-04-10
2025-05-21 08:02:03,661 - INFO - 查询日期范围: 2025-04-04 至 2025-04-10，使用分页查询，每页 100 条记录
2025-05-21 08:02:03,662 - INFO - Request Parameters - Page 1:
2025-05-21 08:02:03,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:03,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:04,450 - INFO - API请求耗时: 788ms
2025-05-21 08:02:04,451 - INFO - Response - Page 1
2025-05-21 08:02:04,451 - INFO - 第 1 页获取到 100 条记录
2025-05-21 08:02:04,952 - INFO - Request Parameters - Page 2:
2025-05-21 08:02:04,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:04,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:05,605 - INFO - API请求耗时: 652ms
2025-05-21 08:02:05,605 - INFO - Response - Page 2
2025-05-21 08:02:05,606 - INFO - 第 2 页获取到 100 条记录
2025-05-21 08:02:06,106 - INFO - Request Parameters - Page 3:
2025-05-21 08:02:06,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:06,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:06,721 - INFO - API请求耗时: 613ms
2025-05-21 08:02:06,722 - INFO - Response - Page 3
2025-05-21 08:02:06,723 - INFO - 第 3 页获取到 100 条记录
2025-05-21 08:02:07,223 - INFO - Request Parameters - Page 4:
2025-05-21 08:02:07,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:07,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:07,890 - INFO - API请求耗时: 666ms
2025-05-21 08:02:07,891 - INFO - Response - Page 4
2025-05-21 08:02:07,891 - INFO - 第 4 页获取到 100 条记录
2025-05-21 08:02:08,392 - INFO - Request Parameters - Page 5:
2025-05-21 08:02:08,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:08,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:09,213 - INFO - API请求耗时: 819ms
2025-05-21 08:02:09,213 - INFO - Response - Page 5
2025-05-21 08:02:09,214 - INFO - 第 5 页获取到 100 条记录
2025-05-21 08:02:09,715 - INFO - Request Parameters - Page 6:
2025-05-21 08:02:09,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:09,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:10,487 - INFO - API请求耗时: 771ms
2025-05-21 08:02:10,488 - INFO - Response - Page 6
2025-05-21 08:02:10,488 - INFO - 第 6 页获取到 100 条记录
2025-05-21 08:02:10,990 - INFO - Request Parameters - Page 7:
2025-05-21 08:02:10,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:10,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:11,622 - INFO - API请求耗时: 631ms
2025-05-21 08:02:11,622 - INFO - Response - Page 7
2025-05-21 08:02:11,623 - INFO - 第 7 页获取到 100 条记录
2025-05-21 08:02:12,123 - INFO - Request Parameters - Page 8:
2025-05-21 08:02:12,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:12,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:12,832 - INFO - API请求耗时: 708ms
2025-05-21 08:02:12,832 - INFO - Response - Page 8
2025-05-21 08:02:12,833 - INFO - 第 8 页获取到 100 条记录
2025-05-21 08:02:13,334 - INFO - Request Parameters - Page 9:
2025-05-21 08:02:13,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:13,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:13,932 - INFO - API请求耗时: 598ms
2025-05-21 08:02:13,933 - INFO - Response - Page 9
2025-05-21 08:02:13,933 - INFO - 第 9 页获取到 100 条记录
2025-05-21 08:02:14,435 - INFO - Request Parameters - Page 10:
2025-05-21 08:02:14,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:14,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:15,136 - INFO - API请求耗时: 699ms
2025-05-21 08:02:15,136 - INFO - Response - Page 10
2025-05-21 08:02:15,137 - INFO - 第 10 页获取到 100 条记录
2025-05-21 08:02:15,638 - INFO - Request Parameters - Page 11:
2025-05-21 08:02:15,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:15,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:16,272 - INFO - API请求耗时: 634ms
2025-05-21 08:02:16,273 - INFO - Response - Page 11
2025-05-21 08:02:16,273 - INFO - 第 11 页获取到 100 条记录
2025-05-21 08:02:16,774 - INFO - Request Parameters - Page 12:
2025-05-21 08:02:16,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:16,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:17,610 - INFO - API请求耗时: 834ms
2025-05-21 08:02:17,610 - INFO - Response - Page 12
2025-05-21 08:02:17,611 - INFO - 第 12 页获取到 100 条记录
2025-05-21 08:02:18,112 - INFO - Request Parameters - Page 13:
2025-05-21 08:02:18,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:18,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:18,753 - INFO - API请求耗时: 640ms
2025-05-21 08:02:18,754 - INFO - Response - Page 13
2025-05-21 08:02:18,755 - INFO - 第 13 页获取到 100 条记录
2025-05-21 08:02:19,255 - INFO - Request Parameters - Page 14:
2025-05-21 08:02:19,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:19,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:19,890 - INFO - API请求耗时: 635ms
2025-05-21 08:02:19,890 - INFO - Response - Page 14
2025-05-21 08:02:19,891 - INFO - 第 14 页获取到 100 条记录
2025-05-21 08:02:20,392 - INFO - Request Parameters - Page 15:
2025-05-21 08:02:20,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:20,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:21,021 - INFO - API请求耗时: 628ms
2025-05-21 08:02:21,022 - INFO - Response - Page 15
2025-05-21 08:02:21,022 - INFO - 第 15 页获取到 100 条记录
2025-05-21 08:02:21,524 - INFO - Request Parameters - Page 16:
2025-05-21 08:02:21,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:21,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:22,334 - INFO - API请求耗时: 809ms
2025-05-21 08:02:22,334 - INFO - Response - Page 16
2025-05-21 08:02:22,335 - INFO - 第 16 页获取到 100 条记录
2025-05-21 08:02:22,835 - INFO - Request Parameters - Page 17:
2025-05-21 08:02:22,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:22,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:23,670 - INFO - API请求耗时: 834ms
2025-05-21 08:02:23,670 - INFO - Response - Page 17
2025-05-21 08:02:23,671 - INFO - 第 17 页获取到 100 条记录
2025-05-21 08:02:24,172 - INFO - Request Parameters - Page 18:
2025-05-21 08:02:24,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:24,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:24,806 - INFO - API请求耗时: 633ms
2025-05-21 08:02:24,806 - INFO - Response - Page 18
2025-05-21 08:02:24,807 - INFO - 第 18 页获取到 100 条记录
2025-05-21 08:02:25,308 - INFO - Request Parameters - Page 19:
2025-05-21 08:02:25,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:25,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:25,997 - INFO - API请求耗时: 687ms
2025-05-21 08:02:25,998 - INFO - Response - Page 19
2025-05-21 08:02:25,999 - INFO - 第 19 页获取到 100 条记录
2025-05-21 08:02:26,499 - INFO - Request Parameters - Page 20:
2025-05-21 08:02:26,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:26,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:27,210 - INFO - API请求耗时: 710ms
2025-05-21 08:02:27,211 - INFO - Response - Page 20
2025-05-21 08:02:27,211 - INFO - 第 20 页获取到 100 条记录
2025-05-21 08:02:27,711 - INFO - Request Parameters - Page 21:
2025-05-21 08:02:27,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:27,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:28,396 - INFO - API请求耗时: 684ms
2025-05-21 08:02:28,396 - INFO - Response - Page 21
2025-05-21 08:02:28,397 - INFO - 第 21 页获取到 100 条记录
2025-05-21 08:02:28,898 - INFO - Request Parameters - Page 22:
2025-05-21 08:02:28,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:28,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:29,562 - INFO - API请求耗时: 663ms
2025-05-21 08:02:29,562 - INFO - Response - Page 22
2025-05-21 08:02:29,562 - INFO - 第 22 页获取到 100 条记录
2025-05-21 08:02:30,064 - INFO - Request Parameters - Page 23:
2025-05-21 08:02:30,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:30,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:30,784 - INFO - API请求耗时: 719ms
2025-05-21 08:02:30,785 - INFO - Response - Page 23
2025-05-21 08:02:30,785 - INFO - 第 23 页获取到 100 条记录
2025-05-21 08:02:31,285 - INFO - Request Parameters - Page 24:
2025-05-21 08:02:31,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:31,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000754, 1744214400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:31,876 - INFO - API请求耗时: 590ms
2025-05-21 08:02:31,876 - INFO - Response - Page 24
2025-05-21 08:02:31,877 - INFO - 第 24 页获取到 44 条记录
2025-05-21 08:02:31,877 - INFO - 查询完成，共获取到 2344 条记录
2025-05-21 08:02:31,877 - INFO - 分段 3 查询成功，获取到 2344 条记录
2025-05-21 08:02:32,879 - INFO - 查询分段 4: 2025-04-11 至 2025-04-17
2025-05-21 08:02:32,879 - INFO - 查询日期范围: 2025-04-11 至 2025-04-17，使用分页查询，每页 100 条记录
2025-05-21 08:02:32,880 - INFO - Request Parameters - Page 1:
2025-05-21 08:02:32,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:32,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:33,483 - INFO - API请求耗时: 604ms
2025-05-21 08:02:33,484 - INFO - Response - Page 1
2025-05-21 08:02:33,484 - INFO - 第 1 页获取到 100 条记录
2025-05-21 08:02:33,986 - INFO - Request Parameters - Page 2:
2025-05-21 08:02:33,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:33,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:34,602 - INFO - API请求耗时: 615ms
2025-05-21 08:02:34,603 - INFO - Response - Page 2
2025-05-21 08:02:34,603 - INFO - 第 2 页获取到 100 条记录
2025-05-21 08:02:35,105 - INFO - Request Parameters - Page 3:
2025-05-21 08:02:35,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:35,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:35,779 - INFO - API请求耗时: 673ms
2025-05-21 08:02:35,780 - INFO - Response - Page 3
2025-05-21 08:02:35,780 - INFO - 第 3 页获取到 100 条记录
2025-05-21 08:02:36,282 - INFO - Request Parameters - Page 4:
2025-05-21 08:02:36,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:36,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:37,009 - INFO - API请求耗时: 727ms
2025-05-21 08:02:37,011 - INFO - Response - Page 4
2025-05-21 08:02:37,011 - INFO - 第 4 页获取到 100 条记录
2025-05-21 08:02:37,513 - INFO - Request Parameters - Page 5:
2025-05-21 08:02:37,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:37,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:38,222 - INFO - API请求耗时: 708ms
2025-05-21 08:02:38,223 - INFO - Response - Page 5
2025-05-21 08:02:38,224 - INFO - 第 5 页获取到 100 条记录
2025-05-21 08:02:38,725 - INFO - Request Parameters - Page 6:
2025-05-21 08:02:38,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:38,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:39,333 - INFO - API请求耗时: 606ms
2025-05-21 08:02:39,333 - INFO - Response - Page 6
2025-05-21 08:02:39,335 - INFO - 第 6 页获取到 100 条记录
2025-05-21 08:02:39,836 - INFO - Request Parameters - Page 7:
2025-05-21 08:02:39,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:39,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:40,505 - INFO - API请求耗时: 668ms
2025-05-21 08:02:40,505 - INFO - Response - Page 7
2025-05-21 08:02:40,506 - INFO - 第 7 页获取到 100 条记录
2025-05-21 08:02:41,006 - INFO - Request Parameters - Page 8:
2025-05-21 08:02:41,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:41,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:41,631 - INFO - API请求耗时: 624ms
2025-05-21 08:02:41,631 - INFO - Response - Page 8
2025-05-21 08:02:41,632 - INFO - 第 8 页获取到 100 条记录
2025-05-21 08:02:42,132 - INFO - Request Parameters - Page 9:
2025-05-21 08:02:42,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:42,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:42,861 - INFO - API请求耗时: 728ms
2025-05-21 08:02:42,862 - INFO - Response - Page 9
2025-05-21 08:02:42,863 - INFO - 第 9 页获取到 100 条记录
2025-05-21 08:02:43,364 - INFO - Request Parameters - Page 10:
2025-05-21 08:02:43,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:43,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:43,956 - INFO - API请求耗时: 591ms
2025-05-21 08:02:43,957 - INFO - Response - Page 10
2025-05-21 08:02:43,957 - INFO - 第 10 页获取到 100 条记录
2025-05-21 08:02:44,458 - INFO - Request Parameters - Page 11:
2025-05-21 08:02:44,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:44,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:45,283 - INFO - API请求耗时: 824ms
2025-05-21 08:02:45,284 - INFO - Response - Page 11
2025-05-21 08:02:45,284 - INFO - 第 11 页获取到 100 条记录
2025-05-21 08:02:45,784 - INFO - Request Parameters - Page 12:
2025-05-21 08:02:45,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:45,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:46,483 - INFO - API请求耗时: 698ms
2025-05-21 08:02:46,484 - INFO - Response - Page 12
2025-05-21 08:02:46,484 - INFO - 第 12 页获取到 100 条记录
2025-05-21 08:02:46,986 - INFO - Request Parameters - Page 13:
2025-05-21 08:02:46,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:46,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:47,655 - INFO - API请求耗时: 668ms
2025-05-21 08:02:47,656 - INFO - Response - Page 13
2025-05-21 08:02:47,656 - INFO - 第 13 页获取到 100 条记录
2025-05-21 08:02:48,157 - INFO - Request Parameters - Page 14:
2025-05-21 08:02:48,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:48,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:48,832 - INFO - API请求耗时: 674ms
2025-05-21 08:02:48,833 - INFO - Response - Page 14
2025-05-21 08:02:48,833 - INFO - 第 14 页获取到 100 条记录
2025-05-21 08:02:49,335 - INFO - Request Parameters - Page 15:
2025-05-21 08:02:49,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:49,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:50,047 - INFO - API请求耗时: 711ms
2025-05-21 08:02:50,048 - INFO - Response - Page 15
2025-05-21 08:02:50,048 - INFO - 第 15 页获取到 100 条记录
2025-05-21 08:02:50,550 - INFO - Request Parameters - Page 16:
2025-05-21 08:02:50,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:50,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:51,230 - INFO - API请求耗时: 679ms
2025-05-21 08:02:51,230 - INFO - Response - Page 16
2025-05-21 08:02:51,231 - INFO - 第 16 页获取到 100 条记录
2025-05-21 08:02:51,731 - INFO - Request Parameters - Page 17:
2025-05-21 08:02:51,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:51,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:52,364 - INFO - API请求耗时: 632ms
2025-05-21 08:02:52,364 - INFO - Response - Page 17
2025-05-21 08:02:52,365 - INFO - 第 17 页获取到 100 条记录
2025-05-21 08:02:52,866 - INFO - Request Parameters - Page 18:
2025-05-21 08:02:52,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:52,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:53,602 - INFO - API请求耗时: 735ms
2025-05-21 08:02:53,602 - INFO - Response - Page 18
2025-05-21 08:02:53,602 - INFO - 第 18 页获取到 100 条记录
2025-05-21 08:02:54,103 - INFO - Request Parameters - Page 19:
2025-05-21 08:02:54,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:54,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:54,742 - INFO - API请求耗时: 638ms
2025-05-21 08:02:54,742 - INFO - Response - Page 19
2025-05-21 08:02:54,743 - INFO - 第 19 页获取到 100 条记录
2025-05-21 08:02:55,244 - INFO - Request Parameters - Page 20:
2025-05-21 08:02:55,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:55,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:55,826 - INFO - API请求耗时: 581ms
2025-05-21 08:02:55,826 - INFO - Response - Page 20
2025-05-21 08:02:55,827 - INFO - 第 20 页获取到 100 条记录
2025-05-21 08:02:56,327 - INFO - Request Parameters - Page 21:
2025-05-21 08:02:56,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:56,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:57,009 - INFO - API请求耗时: 682ms
2025-05-21 08:02:57,009 - INFO - Response - Page 21
2025-05-21 08:02:57,010 - INFO - 第 21 页获取到 100 条记录
2025-05-21 08:02:57,510 - INFO - Request Parameters - Page 22:
2025-05-21 08:02:57,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:57,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:58,284 - INFO - API请求耗时: 772ms
2025-05-21 08:02:58,285 - INFO - Response - Page 22
2025-05-21 08:02:58,285 - INFO - 第 22 页获取到 100 条记录
2025-05-21 08:02:58,786 - INFO - Request Parameters - Page 23:
2025-05-21 08:02:58,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:58,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:02:59,473 - INFO - API请求耗时: 687ms
2025-05-21 08:02:59,474 - INFO - Response - Page 23
2025-05-21 08:02:59,474 - INFO - 第 23 页获取到 100 条记录
2025-05-21 08:02:59,976 - INFO - Request Parameters - Page 24:
2025-05-21 08:02:59,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:02:59,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800754, 1744819200754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:00,565 - INFO - API请求耗时: 588ms
2025-05-21 08:03:00,565 - INFO - Response - Page 24
2025-05-21 08:03:00,566 - INFO - 第 24 页获取到 74 条记录
2025-05-21 08:03:00,566 - INFO - 查询完成，共获取到 2374 条记录
2025-05-21 08:03:00,566 - INFO - 分段 4 查询成功，获取到 2374 条记录
2025-05-21 08:03:01,567 - INFO - 查询分段 5: 2025-04-18 至 2025-04-24
2025-05-21 08:03:01,567 - INFO - 查询日期范围: 2025-04-18 至 2025-04-24，使用分页查询，每页 100 条记录
2025-05-21 08:03:01,568 - INFO - Request Parameters - Page 1:
2025-05-21 08:03:01,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:01,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:02,496 - INFO - API请求耗时: 927ms
2025-05-21 08:03:02,496 - INFO - Response - Page 1
2025-05-21 08:03:02,497 - INFO - 第 1 页获取到 100 条记录
2025-05-21 08:03:02,997 - INFO - Request Parameters - Page 2:
2025-05-21 08:03:02,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:02,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:03,634 - INFO - API请求耗时: 637ms
2025-05-21 08:03:03,634 - INFO - Response - Page 2
2025-05-21 08:03:03,635 - INFO - 第 2 页获取到 100 条记录
2025-05-21 08:03:04,135 - INFO - Request Parameters - Page 3:
2025-05-21 08:03:04,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:04,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:04,823 - INFO - API请求耗时: 686ms
2025-05-21 08:03:04,823 - INFO - Response - Page 3
2025-05-21 08:03:04,824 - INFO - 第 3 页获取到 100 条记录
2025-05-21 08:03:05,325 - INFO - Request Parameters - Page 4:
2025-05-21 08:03:05,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:05,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:06,215 - INFO - API请求耗时: 890ms
2025-05-21 08:03:06,215 - INFO - Response - Page 4
2025-05-21 08:03:06,216 - INFO - 第 4 页获取到 100 条记录
2025-05-21 08:03:06,716 - INFO - Request Parameters - Page 5:
2025-05-21 08:03:06,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:06,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:07,440 - INFO - API请求耗时: 723ms
2025-05-21 08:03:07,441 - INFO - Response - Page 5
2025-05-21 08:03:07,441 - INFO - 第 5 页获取到 100 条记录
2025-05-21 08:03:07,943 - INFO - Request Parameters - Page 6:
2025-05-21 08:03:07,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:07,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:08,608 - INFO - API请求耗时: 664ms
2025-05-21 08:03:08,608 - INFO - Response - Page 6
2025-05-21 08:03:08,609 - INFO - 第 6 页获取到 100 条记录
2025-05-21 08:03:09,109 - INFO - Request Parameters - Page 7:
2025-05-21 08:03:09,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:09,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:09,828 - INFO - API请求耗时: 718ms
2025-05-21 08:03:09,829 - INFO - Response - Page 7
2025-05-21 08:03:09,829 - INFO - 第 7 页获取到 100 条记录
2025-05-21 08:03:10,331 - INFO - Request Parameters - Page 8:
2025-05-21 08:03:10,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:10,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:10,982 - INFO - API请求耗时: 650ms
2025-05-21 08:03:10,982 - INFO - Response - Page 8
2025-05-21 08:03:10,983 - INFO - 第 8 页获取到 100 条记录
2025-05-21 08:03:11,484 - INFO - Request Parameters - Page 9:
2025-05-21 08:03:11,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:11,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:12,331 - INFO - API请求耗时: 846ms
2025-05-21 08:03:12,331 - INFO - Response - Page 9
2025-05-21 08:03:12,331 - INFO - 第 9 页获取到 100 条记录
2025-05-21 08:03:12,832 - INFO - Request Parameters - Page 10:
2025-05-21 08:03:12,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:12,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:13,523 - INFO - API请求耗时: 689ms
2025-05-21 08:03:13,523 - INFO - Response - Page 10
2025-05-21 08:03:13,524 - INFO - 第 10 页获取到 100 条记录
2025-05-21 08:03:14,025 - INFO - Request Parameters - Page 11:
2025-05-21 08:03:14,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:14,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:14,654 - INFO - API请求耗时: 628ms
2025-05-21 08:03:14,654 - INFO - Response - Page 11
2025-05-21 08:03:14,655 - INFO - 第 11 页获取到 100 条记录
2025-05-21 08:03:15,156 - INFO - Request Parameters - Page 12:
2025-05-21 08:03:15,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:15,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:15,828 - INFO - API请求耗时: 671ms
2025-05-21 08:03:15,829 - INFO - Response - Page 12
2025-05-21 08:03:15,829 - INFO - 第 12 页获取到 100 条记录
2025-05-21 08:03:16,329 - INFO - Request Parameters - Page 13:
2025-05-21 08:03:16,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:16,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:16,996 - INFO - API请求耗时: 667ms
2025-05-21 08:03:16,997 - INFO - Response - Page 13
2025-05-21 08:03:16,998 - INFO - 第 13 页获取到 100 条记录
2025-05-21 08:03:17,499 - INFO - Request Parameters - Page 14:
2025-05-21 08:03:17,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:17,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:18,201 - INFO - API请求耗时: 700ms
2025-05-21 08:03:18,201 - INFO - Response - Page 14
2025-05-21 08:03:18,202 - INFO - 第 14 页获取到 100 条记录
2025-05-21 08:03:18,703 - INFO - Request Parameters - Page 15:
2025-05-21 08:03:18,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:18,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:19,550 - INFO - API请求耗时: 847ms
2025-05-21 08:03:19,551 - INFO - Response - Page 15
2025-05-21 08:03:19,551 - INFO - 第 15 页获取到 100 条记录
2025-05-21 08:03:20,053 - INFO - Request Parameters - Page 16:
2025-05-21 08:03:20,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:20,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:20,755 - INFO - API请求耗时: 701ms
2025-05-21 08:03:20,756 - INFO - Response - Page 16
2025-05-21 08:03:20,757 - INFO - 第 16 页获取到 100 条记录
2025-05-21 08:03:21,258 - INFO - Request Parameters - Page 17:
2025-05-21 08:03:21,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:21,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:21,869 - INFO - API请求耗时: 609ms
2025-05-21 08:03:21,869 - INFO - Response - Page 17
2025-05-21 08:03:21,869 - INFO - 第 17 页获取到 100 条记录
2025-05-21 08:03:22,369 - INFO - Request Parameters - Page 18:
2025-05-21 08:03:22,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:22,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:23,036 - INFO - API请求耗时: 665ms
2025-05-21 08:03:23,036 - INFO - Response - Page 18
2025-05-21 08:03:23,037 - INFO - 第 18 页获取到 100 条记录
2025-05-21 08:03:23,538 - INFO - Request Parameters - Page 19:
2025-05-21 08:03:23,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:23,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:24,341 - INFO - API请求耗时: 802ms
2025-05-21 08:03:24,341 - INFO - Response - Page 19
2025-05-21 08:03:24,342 - INFO - 第 19 页获取到 100 条记录
2025-05-21 08:03:24,843 - INFO - Request Parameters - Page 20:
2025-05-21 08:03:24,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:24,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:25,513 - INFO - API请求耗时: 669ms
2025-05-21 08:03:25,513 - INFO - Response - Page 20
2025-05-21 08:03:25,514 - INFO - 第 20 页获取到 100 条记录
2025-05-21 08:03:26,015 - INFO - Request Parameters - Page 21:
2025-05-21 08:03:26,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:26,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:26,650 - INFO - API请求耗时: 634ms
2025-05-21 08:03:26,650 - INFO - Response - Page 21
2025-05-21 08:03:26,650 - INFO - 第 21 页获取到 100 条记录
2025-05-21 08:03:27,151 - INFO - Request Parameters - Page 22:
2025-05-21 08:03:27,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:27,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:27,786 - INFO - API请求耗时: 635ms
2025-05-21 08:03:27,787 - INFO - Response - Page 22
2025-05-21 08:03:27,787 - INFO - 第 22 页获取到 100 条记录
2025-05-21 08:03:28,289 - INFO - Request Parameters - Page 23:
2025-05-21 08:03:28,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:28,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:28,909 - INFO - API请求耗时: 619ms
2025-05-21 08:03:28,910 - INFO - Response - Page 23
2025-05-21 08:03:28,910 - INFO - 第 23 页获取到 100 条记录
2025-05-21 08:03:29,411 - INFO - Request Parameters - Page 24:
2025-05-21 08:03:29,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:29,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600754, 1745424000754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:29,856 - INFO - API请求耗时: 443ms
2025-05-21 08:03:29,856 - INFO - Response - Page 24
2025-05-21 08:03:29,857 - INFO - 第 24 页获取到 22 条记录
2025-05-21 08:03:29,857 - INFO - 查询完成，共获取到 2322 条记录
2025-05-21 08:03:29,857 - INFO - 分段 5 查询成功，获取到 2322 条记录
2025-05-21 08:03:30,857 - INFO - 查询分段 6: 2025-04-25 至 2025-05-01
2025-05-21 08:03:30,857 - INFO - 查询日期范围: 2025-04-25 至 2025-05-01，使用分页查询，每页 100 条记录
2025-05-21 08:03:30,858 - INFO - Request Parameters - Page 1:
2025-05-21 08:03:30,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:30,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:31,508 - INFO - API请求耗时: 650ms
2025-05-21 08:03:31,509 - INFO - Response - Page 1
2025-05-21 08:03:31,509 - INFO - 第 1 页获取到 100 条记录
2025-05-21 08:03:32,011 - INFO - Request Parameters - Page 2:
2025-05-21 08:03:32,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:32,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:32,783 - INFO - API请求耗时: 771ms
2025-05-21 08:03:32,783 - INFO - Response - Page 2
2025-05-21 08:03:32,783 - INFO - 第 2 页获取到 100 条记录
2025-05-21 08:03:33,284 - INFO - Request Parameters - Page 3:
2025-05-21 08:03:33,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:33,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:33,880 - INFO - API请求耗时: 595ms
2025-05-21 08:03:33,880 - INFO - Response - Page 3
2025-05-21 08:03:33,881 - INFO - 第 3 页获取到 100 条记录
2025-05-21 08:03:34,381 - INFO - Request Parameters - Page 4:
2025-05-21 08:03:34,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:34,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:35,052 - INFO - API请求耗时: 670ms
2025-05-21 08:03:35,052 - INFO - Response - Page 4
2025-05-21 08:03:35,053 - INFO - 第 4 页获取到 100 条记录
2025-05-21 08:03:35,554 - INFO - Request Parameters - Page 5:
2025-05-21 08:03:35,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:35,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:36,185 - INFO - API请求耗时: 630ms
2025-05-21 08:03:36,185 - INFO - Response - Page 5
2025-05-21 08:03:36,186 - INFO - 第 5 页获取到 100 条记录
2025-05-21 08:03:36,687 - INFO - Request Parameters - Page 6:
2025-05-21 08:03:36,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:36,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:37,271 - INFO - API请求耗时: 583ms
2025-05-21 08:03:37,272 - INFO - Response - Page 6
2025-05-21 08:03:37,272 - INFO - 第 6 页获取到 100 条记录
2025-05-21 08:03:37,773 - INFO - Request Parameters - Page 7:
2025-05-21 08:03:37,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:37,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:38,477 - INFO - API请求耗时: 703ms
2025-05-21 08:03:38,478 - INFO - Response - Page 7
2025-05-21 08:03:38,478 - INFO - 第 7 页获取到 100 条记录
2025-05-21 08:03:38,978 - INFO - Request Parameters - Page 8:
2025-05-21 08:03:38,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:38,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:39,620 - INFO - API请求耗时: 642ms
2025-05-21 08:03:39,620 - INFO - Response - Page 8
2025-05-21 08:03:39,621 - INFO - 第 8 页获取到 100 条记录
2025-05-21 08:03:40,122 - INFO - Request Parameters - Page 9:
2025-05-21 08:03:40,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:40,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:40,836 - INFO - API请求耗时: 712ms
2025-05-21 08:03:40,836 - INFO - Response - Page 9
2025-05-21 08:03:40,837 - INFO - 第 9 页获取到 100 条记录
2025-05-21 08:03:41,338 - INFO - Request Parameters - Page 10:
2025-05-21 08:03:41,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:41,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:42,061 - INFO - API请求耗时: 722ms
2025-05-21 08:03:42,062 - INFO - Response - Page 10
2025-05-21 08:03:42,063 - INFO - 第 10 页获取到 100 条记录
2025-05-21 08:03:42,563 - INFO - Request Parameters - Page 11:
2025-05-21 08:03:42,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:42,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:43,232 - INFO - API请求耗时: 668ms
2025-05-21 08:03:43,233 - INFO - Response - Page 11
2025-05-21 08:03:43,233 - INFO - 第 11 页获取到 100 条记录
2025-05-21 08:03:43,735 - INFO - Request Parameters - Page 12:
2025-05-21 08:03:43,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:43,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:44,529 - INFO - API请求耗时: 794ms
2025-05-21 08:03:44,529 - INFO - Response - Page 12
2025-05-21 08:03:44,530 - INFO - 第 12 页获取到 100 条记录
2025-05-21 08:03:45,030 - INFO - Request Parameters - Page 13:
2025-05-21 08:03:45,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:45,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:45,761 - INFO - API请求耗时: 730ms
2025-05-21 08:03:45,761 - INFO - Response - Page 13
2025-05-21 08:03:45,762 - INFO - 第 13 页获取到 100 条记录
2025-05-21 08:03:46,263 - INFO - Request Parameters - Page 14:
2025-05-21 08:03:46,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:46,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:46,938 - INFO - API请求耗时: 674ms
2025-05-21 08:03:46,938 - INFO - Response - Page 14
2025-05-21 08:03:46,939 - INFO - 第 14 页获取到 100 条记录
2025-05-21 08:03:47,440 - INFO - Request Parameters - Page 15:
2025-05-21 08:03:47,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:47,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:48,287 - INFO - API请求耗时: 847ms
2025-05-21 08:03:48,287 - INFO - Response - Page 15
2025-05-21 08:03:48,288 - INFO - 第 15 页获取到 100 条记录
2025-05-21 08:03:48,789 - INFO - Request Parameters - Page 16:
2025-05-21 08:03:48,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:48,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:49,549 - INFO - API请求耗时: 759ms
2025-05-21 08:03:49,550 - INFO - Response - Page 16
2025-05-21 08:03:49,551 - INFO - 第 16 页获取到 100 条记录
2025-05-21 08:03:50,052 - INFO - Request Parameters - Page 17:
2025-05-21 08:03:50,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:50,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:50,894 - INFO - API请求耗时: 841ms
2025-05-21 08:03:50,895 - INFO - Response - Page 17
2025-05-21 08:03:50,895 - INFO - 第 17 页获取到 100 条记录
2025-05-21 08:03:51,397 - INFO - Request Parameters - Page 18:
2025-05-21 08:03:51,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:51,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:52,079 - INFO - API请求耗时: 682ms
2025-05-21 08:03:52,079 - INFO - Response - Page 18
2025-05-21 08:03:52,080 - INFO - 第 18 页获取到 100 条记录
2025-05-21 08:03:52,580 - INFO - Request Parameters - Page 19:
2025-05-21 08:03:52,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:52,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:53,325 - INFO - API请求耗时: 744ms
2025-05-21 08:03:53,325 - INFO - Response - Page 19
2025-05-21 08:03:53,326 - INFO - 第 19 页获取到 100 条记录
2025-05-21 08:03:53,827 - INFO - Request Parameters - Page 20:
2025-05-21 08:03:53,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:53,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:54,551 - INFO - API请求耗时: 722ms
2025-05-21 08:03:54,551 - INFO - Response - Page 20
2025-05-21 08:03:54,552 - INFO - 第 20 页获取到 100 条记录
2025-05-21 08:03:55,053 - INFO - Request Parameters - Page 21:
2025-05-21 08:03:55,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:55,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:55,669 - INFO - API请求耗时: 615ms
2025-05-21 08:03:55,669 - INFO - Response - Page 21
2025-05-21 08:03:55,670 - INFO - 第 21 页获取到 100 条记录
2025-05-21 08:03:56,170 - INFO - Request Parameters - Page 22:
2025-05-21 08:03:56,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:56,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:56,829 - INFO - API请求耗时: 657ms
2025-05-21 08:03:56,829 - INFO - Response - Page 22
2025-05-21 08:03:56,829 - INFO - 第 22 页获取到 100 条记录
2025-05-21 08:03:57,331 - INFO - Request Parameters - Page 23:
2025-05-21 08:03:57,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:57,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:58,204 - INFO - API请求耗时: 872ms
2025-05-21 08:03:58,204 - INFO - Response - Page 23
2025-05-21 08:03:58,205 - INFO - 第 23 页获取到 100 条记录
2025-05-21 08:03:58,706 - INFO - Request Parameters - Page 24:
2025-05-21 08:03:58,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:58,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:03:59,389 - INFO - API请求耗时: 682ms
2025-05-21 08:03:59,390 - INFO - Response - Page 24
2025-05-21 08:03:59,390 - INFO - 第 24 页获取到 100 条记录
2025-05-21 08:03:59,891 - INFO - Request Parameters - Page 25:
2025-05-21 08:03:59,891 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:03:59,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:00,547 - INFO - API请求耗时: 655ms
2025-05-21 08:04:00,548 - INFO - Response - Page 25
2025-05-21 08:04:00,548 - INFO - 第 25 页获取到 100 条记录
2025-05-21 08:04:01,050 - INFO - Request Parameters - Page 26:
2025-05-21 08:04:01,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:01,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:01,720 - INFO - API请求耗时: 669ms
2025-05-21 08:04:01,721 - INFO - Response - Page 26
2025-05-21 08:04:01,721 - INFO - 第 26 页获取到 100 条记录
2025-05-21 08:04:02,222 - INFO - Request Parameters - Page 27:
2025-05-21 08:04:02,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:02,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:02,821 - INFO - API请求耗时: 599ms
2025-05-21 08:04:02,821 - INFO - Response - Page 27
2025-05-21 08:04:02,822 - INFO - 第 27 页获取到 100 条记录
2025-05-21 08:04:03,322 - INFO - Request Parameters - Page 28:
2025-05-21 08:04:03,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:03,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:04,297 - INFO - API请求耗时: 974ms
2025-05-21 08:04:04,297 - INFO - Response - Page 28
2025-05-21 08:04:04,298 - INFO - 第 28 页获取到 100 条记录
2025-05-21 08:04:04,799 - INFO - Request Parameters - Page 29:
2025-05-21 08:04:04,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:04,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:05,464 - INFO - API请求耗时: 664ms
2025-05-21 08:04:05,464 - INFO - Response - Page 29
2025-05-21 08:04:05,465 - INFO - 第 29 页获取到 100 条记录
2025-05-21 08:04:05,966 - INFO - Request Parameters - Page 30:
2025-05-21 08:04:05,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:05,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400754, 1746028800754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:06,561 - INFO - API请求耗时: 594ms
2025-05-21 08:04:06,562 - INFO - Response - Page 30
2025-05-21 08:04:06,563 - INFO - 第 30 页获取到 47 条记录
2025-05-21 08:04:06,563 - INFO - 查询完成，共获取到 2947 条记录
2025-05-21 08:04:06,563 - INFO - 分段 6 查询成功，获取到 2947 条记录
2025-05-21 08:04:07,565 - INFO - 查询分段 7: 2025-05-02 至 2025-05-08
2025-05-21 08:04:07,565 - INFO - 查询日期范围: 2025-05-02 至 2025-05-08，使用分页查询，每页 100 条记录
2025-05-21 08:04:07,565 - INFO - Request Parameters - Page 1:
2025-05-21 08:04:07,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:07,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:08,200 - INFO - API请求耗时: 635ms
2025-05-21 08:04:08,201 - INFO - Response - Page 1
2025-05-21 08:04:08,201 - INFO - 第 1 页获取到 100 条记录
2025-05-21 08:04:08,701 - INFO - Request Parameters - Page 2:
2025-05-21 08:04:08,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:08,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:09,370 - INFO - API请求耗时: 667ms
2025-05-21 08:04:09,370 - INFO - Response - Page 2
2025-05-21 08:04:09,371 - INFO - 第 2 页获取到 100 条记录
2025-05-21 08:04:09,871 - INFO - Request Parameters - Page 3:
2025-05-21 08:04:09,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:09,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:10,494 - INFO - API请求耗时: 622ms
2025-05-21 08:04:10,495 - INFO - Response - Page 3
2025-05-21 08:04:10,495 - INFO - 第 3 页获取到 100 条记录
2025-05-21 08:04:10,996 - INFO - Request Parameters - Page 4:
2025-05-21 08:04:10,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:10,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:11,724 - INFO - API请求耗时: 727ms
2025-05-21 08:04:11,725 - INFO - Response - Page 4
2025-05-21 08:04:11,726 - INFO - 第 4 页获取到 100 条记录
2025-05-21 08:04:12,227 - INFO - Request Parameters - Page 5:
2025-05-21 08:04:12,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:12,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:12,881 - INFO - API请求耗时: 653ms
2025-05-21 08:04:12,881 - INFO - Response - Page 5
2025-05-21 08:04:12,882 - INFO - 第 5 页获取到 100 条记录
2025-05-21 08:04:13,383 - INFO - Request Parameters - Page 6:
2025-05-21 08:04:13,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:13,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:14,015 - INFO - API请求耗时: 631ms
2025-05-21 08:04:14,015 - INFO - Response - Page 6
2025-05-21 08:04:14,016 - INFO - 第 6 页获取到 100 条记录
2025-05-21 08:04:14,517 - INFO - Request Parameters - Page 7:
2025-05-21 08:04:14,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:14,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:15,194 - INFO - API请求耗时: 677ms
2025-05-21 08:04:15,194 - INFO - Response - Page 7
2025-05-21 08:04:15,194 - INFO - 第 7 页获取到 100 条记录
2025-05-21 08:04:15,696 - INFO - Request Parameters - Page 8:
2025-05-21 08:04:15,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:15,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:16,337 - INFO - API请求耗时: 640ms
2025-05-21 08:04:16,338 - INFO - Response - Page 8
2025-05-21 08:04:16,338 - INFO - 第 8 页获取到 100 条记录
2025-05-21 08:04:16,839 - INFO - Request Parameters - Page 9:
2025-05-21 08:04:16,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:16,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:17,512 - INFO - API请求耗时: 672ms
2025-05-21 08:04:17,512 - INFO - Response - Page 9
2025-05-21 08:04:17,513 - INFO - 第 9 页获取到 100 条记录
2025-05-21 08:04:18,013 - INFO - Request Parameters - Page 10:
2025-05-21 08:04:18,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:18,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:18,769 - INFO - API请求耗时: 755ms
2025-05-21 08:04:18,769 - INFO - Response - Page 10
2025-05-21 08:04:18,770 - INFO - 第 10 页获取到 100 条记录
2025-05-21 08:04:19,270 - INFO - Request Parameters - Page 11:
2025-05-21 08:04:19,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:19,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:19,941 - INFO - API请求耗时: 671ms
2025-05-21 08:04:19,942 - INFO - Response - Page 11
2025-05-21 08:04:19,943 - INFO - 第 11 页获取到 100 条记录
2025-05-21 08:04:20,444 - INFO - Request Parameters - Page 12:
2025-05-21 08:04:20,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:20,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:21,174 - INFO - API请求耗时: 728ms
2025-05-21 08:04:21,175 - INFO - Response - Page 12
2025-05-21 08:04:21,175 - INFO - 第 12 页获取到 100 条记录
2025-05-21 08:04:21,677 - INFO - Request Parameters - Page 13:
2025-05-21 08:04:21,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:21,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:22,466 - INFO - API请求耗时: 788ms
2025-05-21 08:04:22,466 - INFO - Response - Page 13
2025-05-21 08:04:22,467 - INFO - 第 13 页获取到 100 条记录
2025-05-21 08:04:22,967 - INFO - Request Parameters - Page 14:
2025-05-21 08:04:22,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:22,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:23,580 - INFO - API请求耗时: 612ms
2025-05-21 08:04:23,580 - INFO - Response - Page 14
2025-05-21 08:04:23,580 - INFO - 第 14 页获取到 100 条记录
2025-05-21 08:04:24,082 - INFO - Request Parameters - Page 15:
2025-05-21 08:04:24,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:24,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:24,768 - INFO - API请求耗时: 686ms
2025-05-21 08:04:24,768 - INFO - Response - Page 15
2025-05-21 08:04:24,769 - INFO - 第 15 页获取到 100 条记录
2025-05-21 08:04:25,271 - INFO - Request Parameters - Page 16:
2025-05-21 08:04:25,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:25,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:26,124 - INFO - API请求耗时: 853ms
2025-05-21 08:04:26,125 - INFO - Response - Page 16
2025-05-21 08:04:26,125 - INFO - 第 16 页获取到 100 条记录
2025-05-21 08:04:26,626 - INFO - Request Parameters - Page 17:
2025-05-21 08:04:26,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:26,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:27,309 - INFO - API请求耗时: 683ms
2025-05-21 08:04:27,310 - INFO - Response - Page 17
2025-05-21 08:04:27,310 - INFO - 第 17 页获取到 100 条记录
2025-05-21 08:04:27,812 - INFO - Request Parameters - Page 18:
2025-05-21 08:04:27,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:27,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:28,464 - INFO - API请求耗时: 650ms
2025-05-21 08:04:28,464 - INFO - Response - Page 18
2025-05-21 08:04:28,465 - INFO - 第 18 页获取到 100 条记录
2025-05-21 08:04:28,965 - INFO - Request Parameters - Page 19:
2025-05-21 08:04:28,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:28,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:29,771 - INFO - API请求耗时: 806ms
2025-05-21 08:04:29,785 - INFO - Response - Page 19
2025-05-21 08:04:29,786 - INFO - 第 19 页获取到 100 条记录
2025-05-21 08:04:30,289 - INFO - Request Parameters - Page 20:
2025-05-21 08:04:30,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:30,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:30,966 - INFO - API请求耗时: 676ms
2025-05-21 08:04:30,966 - INFO - Response - Page 20
2025-05-21 08:04:30,967 - INFO - 第 20 页获取到 100 条记录
2025-05-21 08:04:31,467 - INFO - Request Parameters - Page 21:
2025-05-21 08:04:31,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:31,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:32,130 - INFO - API请求耗时: 662ms
2025-05-21 08:04:32,130 - INFO - Response - Page 21
2025-05-21 08:04:32,131 - INFO - 第 21 页获取到 100 条记录
2025-05-21 08:04:32,633 - INFO - Request Parameters - Page 22:
2025-05-21 08:04:32,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:32,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:33,346 - INFO - API请求耗时: 712ms
2025-05-21 08:04:33,346 - INFO - Response - Page 22
2025-05-21 08:04:33,347 - INFO - 第 22 页获取到 100 条记录
2025-05-21 08:04:33,847 - INFO - Request Parameters - Page 23:
2025-05-21 08:04:33,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:33,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200754, 1746633600754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:34,724 - INFO - API请求耗时: 877ms
2025-05-21 08:04:34,725 - INFO - Response - Page 23
2025-05-21 08:04:34,725 - INFO - 第 23 页获取到 75 条记录
2025-05-21 08:04:34,726 - INFO - 查询完成，共获取到 2275 条记录
2025-05-21 08:04:34,726 - INFO - 分段 7 查询成功，获取到 2275 条记录
2025-05-21 08:04:35,727 - INFO - 查询分段 8: 2025-05-09 至 2025-05-15
2025-05-21 08:04:35,727 - INFO - 查询日期范围: 2025-05-09 至 2025-05-15，使用分页查询，每页 100 条记录
2025-05-21 08:04:35,728 - INFO - Request Parameters - Page 1:
2025-05-21 08:04:35,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:35,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:36,440 - INFO - API请求耗时: 711ms
2025-05-21 08:04:36,440 - INFO - Response - Page 1
2025-05-21 08:04:36,441 - INFO - 第 1 页获取到 100 条记录
2025-05-21 08:04:36,941 - INFO - Request Parameters - Page 2:
2025-05-21 08:04:36,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:36,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:37,632 - INFO - API请求耗时: 689ms
2025-05-21 08:04:37,633 - INFO - Response - Page 2
2025-05-21 08:04:37,633 - INFO - 第 2 页获取到 100 条记录
2025-05-21 08:04:38,134 - INFO - Request Parameters - Page 3:
2025-05-21 08:04:38,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:38,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:38,821 - INFO - API请求耗时: 687ms
2025-05-21 08:04:38,821 - INFO - Response - Page 3
2025-05-21 08:04:38,822 - INFO - 第 3 页获取到 100 条记录
2025-05-21 08:04:39,323 - INFO - Request Parameters - Page 4:
2025-05-21 08:04:39,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:39,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:40,098 - INFO - API请求耗时: 774ms
2025-05-21 08:04:40,099 - INFO - Response - Page 4
2025-05-21 08:04:40,099 - INFO - 第 4 页获取到 100 条记录
2025-05-21 08:04:40,600 - INFO - Request Parameters - Page 5:
2025-05-21 08:04:40,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:40,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:41,254 - INFO - API请求耗时: 652ms
2025-05-21 08:04:41,255 - INFO - Response - Page 5
2025-05-21 08:04:41,256 - INFO - 第 5 页获取到 100 条记录
2025-05-21 08:04:41,757 - INFO - Request Parameters - Page 6:
2025-05-21 08:04:41,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:41,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:42,333 - INFO - API请求耗时: 575ms
2025-05-21 08:04:42,334 - INFO - Response - Page 6
2025-05-21 08:04:42,334 - INFO - 第 6 页获取到 100 条记录
2025-05-21 08:04:42,836 - INFO - Request Parameters - Page 7:
2025-05-21 08:04:42,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:42,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:43,476 - INFO - API请求耗时: 640ms
2025-05-21 08:04:43,477 - INFO - Response - Page 7
2025-05-21 08:04:43,477 - INFO - 第 7 页获取到 100 条记录
2025-05-21 08:04:43,979 - INFO - Request Parameters - Page 8:
2025-05-21 08:04:43,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:43,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:44,728 - INFO - API请求耗时: 748ms
2025-05-21 08:04:44,729 - INFO - Response - Page 8
2025-05-21 08:04:44,729 - INFO - 第 8 页获取到 100 条记录
2025-05-21 08:04:45,231 - INFO - Request Parameters - Page 9:
2025-05-21 08:04:45,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:45,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:45,958 - INFO - API请求耗时: 726ms
2025-05-21 08:04:45,958 - INFO - Response - Page 9
2025-05-21 08:04:45,959 - INFO - 第 9 页获取到 100 条记录
2025-05-21 08:04:46,460 - INFO - Request Parameters - Page 10:
2025-05-21 08:04:46,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:46,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:47,103 - INFO - API请求耗时: 643ms
2025-05-21 08:04:47,103 - INFO - Response - Page 10
2025-05-21 08:04:47,104 - INFO - 第 10 页获取到 100 条记录
2025-05-21 08:04:47,604 - INFO - Request Parameters - Page 11:
2025-05-21 08:04:47,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:47,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:48,229 - INFO - API请求耗时: 623ms
2025-05-21 08:04:48,229 - INFO - Response - Page 11
2025-05-21 08:04:48,229 - INFO - 第 11 页获取到 100 条记录
2025-05-21 08:04:48,731 - INFO - Request Parameters - Page 12:
2025-05-21 08:04:48,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:48,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:49,393 - INFO - API请求耗时: 661ms
2025-05-21 08:04:49,394 - INFO - Response - Page 12
2025-05-21 08:04:49,394 - INFO - 第 12 页获取到 100 条记录
2025-05-21 08:04:49,896 - INFO - Request Parameters - Page 13:
2025-05-21 08:04:49,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:49,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:50,813 - INFO - API请求耗时: 916ms
2025-05-21 08:04:50,814 - INFO - Response - Page 13
2025-05-21 08:04:50,815 - INFO - 第 13 页获取到 100 条记录
2025-05-21 08:04:51,316 - INFO - Request Parameters - Page 14:
2025-05-21 08:04:51,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:51,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:51,947 - INFO - API请求耗时: 630ms
2025-05-21 08:04:51,948 - INFO - Response - Page 14
2025-05-21 08:04:51,948 - INFO - 第 14 页获取到 100 条记录
2025-05-21 08:04:52,450 - INFO - Request Parameters - Page 15:
2025-05-21 08:04:52,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:52,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:53,093 - INFO - API请求耗时: 642ms
2025-05-21 08:04:53,094 - INFO - Response - Page 15
2025-05-21 08:04:53,094 - INFO - 第 15 页获取到 100 条记录
2025-05-21 08:04:53,594 - INFO - Request Parameters - Page 16:
2025-05-21 08:04:53,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:53,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:54,347 - INFO - API请求耗时: 752ms
2025-05-21 08:04:54,347 - INFO - Response - Page 16
2025-05-21 08:04:54,348 - INFO - 第 16 页获取到 100 条记录
2025-05-21 08:04:54,848 - INFO - Request Parameters - Page 17:
2025-05-21 08:04:54,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:54,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:55,499 - INFO - API请求耗时: 649ms
2025-05-21 08:04:55,500 - INFO - Response - Page 17
2025-05-21 08:04:55,500 - INFO - 第 17 页获取到 100 条记录
2025-05-21 08:04:56,001 - INFO - Request Parameters - Page 18:
2025-05-21 08:04:56,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:56,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:56,843 - INFO - API请求耗时: 842ms
2025-05-21 08:04:56,844 - INFO - Response - Page 18
2025-05-21 08:04:56,845 - INFO - 第 18 页获取到 100 条记录
2025-05-21 08:04:57,346 - INFO - Request Parameters - Page 19:
2025-05-21 08:04:57,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:57,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:58,009 - INFO - API请求耗时: 662ms
2025-05-21 08:04:58,009 - INFO - Response - Page 19
2025-05-21 08:04:58,010 - INFO - 第 19 页获取到 100 条记录
2025-05-21 08:04:58,511 - INFO - Request Parameters - Page 20:
2025-05-21 08:04:58,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:58,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:04:59,176 - INFO - API请求耗时: 665ms
2025-05-21 08:04:59,177 - INFO - Response - Page 20
2025-05-21 08:04:59,177 - INFO - 第 20 页获取到 100 条记录
2025-05-21 08:04:59,679 - INFO - Request Parameters - Page 21:
2025-05-21 08:04:59,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:04:59,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:05:00,293 - INFO - API请求耗时: 612ms
2025-05-21 08:05:00,294 - INFO - Response - Page 21
2025-05-21 08:05:00,295 - INFO - 第 21 页获取到 100 条记录
2025-05-21 08:05:00,796 - INFO - Request Parameters - Page 22:
2025-05-21 08:05:00,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:05:00,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:05:01,481 - INFO - API请求耗时: 685ms
2025-05-21 08:05:01,481 - INFO - Response - Page 22
2025-05-21 08:05:01,482 - INFO - 第 22 页获取到 100 条记录
2025-05-21 08:05:01,983 - INFO - Request Parameters - Page 23:
2025-05-21 08:05:01,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:05:01,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:05:02,591 - INFO - API请求耗时: 606ms
2025-05-21 08:05:02,591 - INFO - Response - Page 23
2025-05-21 08:05:02,591 - INFO - 第 23 页获取到 100 条记录
2025-05-21 08:05:03,092 - INFO - Request Parameters - Page 24:
2025-05-21 08:05:03,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:05:03,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000754, 1747238400754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:05:03,531 - INFO - API请求耗时: 438ms
2025-05-21 08:05:03,532 - INFO - Response - Page 24
2025-05-21 08:05:03,532 - INFO - 第 24 页获取到 13 条记录
2025-05-21 08:05:03,533 - INFO - 查询完成，共获取到 2313 条记录
2025-05-21 08:05:03,533 - INFO - 分段 8 查询成功，获取到 2313 条记录
2025-05-21 08:05:04,534 - INFO - 查询分段 9: 2025-05-16 至 2025-05-20
2025-05-21 08:05:04,534 - INFO - 查询日期范围: 2025-05-16 至 2025-05-20，使用分页查询，每页 100 条记录
2025-05-21 08:05:04,535 - INFO - Request Parameters - Page 1:
2025-05-21 08:05:04,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:05:04,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800754, 1747756799754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:05:05,368 - INFO - API请求耗时: 833ms
2025-05-21 08:05:05,369 - INFO - Response - Page 1
2025-05-21 08:05:05,369 - INFO - 第 1 页获取到 100 条记录
2025-05-21 08:05:05,869 - INFO - Request Parameters - Page 2:
2025-05-21 08:05:05,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:05:05,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800754, 1747756799754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:05:06,548 - INFO - API请求耗时: 678ms
2025-05-21 08:05:06,548 - INFO - Response - Page 2
2025-05-21 08:05:06,548 - INFO - 第 2 页获取到 100 条记录
2025-05-21 08:05:07,050 - INFO - Request Parameters - Page 3:
2025-05-21 08:05:07,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:05:07,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800754, 1747756799754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:05:07,829 - INFO - API请求耗时: 778ms
2025-05-21 08:05:07,829 - INFO - Response - Page 3
2025-05-21 08:05:07,830 - INFO - 第 3 页获取到 100 条记录
2025-05-21 08:05:08,331 - INFO - Request Parameters - Page 4:
2025-05-21 08:05:08,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:05:08,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800754, 1747756799754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:05:08,939 - INFO - API请求耗时: 607ms
2025-05-21 08:05:08,940 - INFO - Response - Page 4
2025-05-21 08:05:08,940 - INFO - 第 4 页获取到 100 条记录
2025-05-21 08:05:09,440 - INFO - Request Parameters - Page 5:
2025-05-21 08:05:09,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:05:09,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800754, 1747756799754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:05:10,102 - INFO - API请求耗时: 660ms
2025-05-21 08:05:10,103 - INFO - Response - Page 5
2025-05-21 08:05:10,104 - INFO - 第 5 页获取到 100 条记录
2025-05-21 08:05:10,605 - INFO - Request Parameters - Page 6:
2025-05-21 08:05:10,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:05:10,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800754, 1747756799754], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:05:11,272 - INFO - API请求耗时: 666ms
2025-05-21 08:05:11,273 - INFO - Response - Page 6
2025-05-21 08:05:11,273 - INFO - 第 6 页获取到 85 条记录
2025-05-21 08:05:11,274 - INFO - 查询完成，共获取到 585 条记录
2025-05-21 08:05:11,274 - INFO - 分段 9 查询成功，获取到 585 条记录
2025-05-21 08:05:12,274 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 19802 条记录，失败 0 次
2025-05-21 08:05:12,274 - INFO - 成功获取宜搭日销售表单数据，共 19802 条记录
2025-05-21 08:05:12,275 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-21 08:05:12,275 - INFO - 开始对比和同步日销售数据...
2025-05-21 08:05:12,815 - INFO - 成功创建宜搭日销售数据索引，共 10889 条记录
2025-05-21 08:05:12,815 - INFO - 开始处理数衍数据，共 13096 条记录
2025-05-21 08:05:13,320 - INFO - 更新表单数据成功: FINST-LLF66FD13CHVRUU1BIKA76HVA0DE3SMEQBUAMW51
2025-05-21 08:05:13,321 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_20250423, 变更字段: [{'field': 'recommendAmount', 'old_value': 10889.38, 'new_value': 11206.38}, {'field': 'amount', 'old_value': 10889.379999999997, 'new_value': 11206.379999999997}, {'field': 'count', 'old_value': 48, 'new_value': 49}, {'field': 'instoreAmount', 'old_value': 18032.92, 'new_value': 18349.92}, {'field': 'instoreCount', 'old_value': 45, 'new_value': 46}]
2025-05-21 08:05:13,759 - INFO - 更新表单数据成功: FINST-F7D66UA1LXFVW0UYED0IL5HQOJ3A30Q469PAM02
2025-05-21 08:05:13,760 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_20250422, 变更字段: [{'field': 'recommendAmount', 'old_value': 3511.33, 'new_value': 3470.33}, {'field': 'amount', 'old_value': 3511.33, 'new_value': 3470.33}]
2025-05-21 08:05:14,222 - INFO - 更新表单数据成功: FINST-K7666JC1VXEVQSBT7Y3R7AM7425O2S0A69PAM98
2025-05-21 08:05:14,222 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5173.12, 'new_value': 5642.12}, {'field': 'amount', 'old_value': 5173.12, 'new_value': 5642.12}, {'field': 'count', 'old_value': 12, 'new_value': 13}, {'field': 'instoreAmount', 'old_value': 5173.12, 'new_value': 5642.12}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 13}]
2025-05-21 08:05:14,721 - INFO - 更新表单数据成功: FINST-7PF66CC1DBHVF838ERQIUDR99G6P2OR9AWSAM6G
2025-05-21 08:05:14,721 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_20250429, 变更字段: [{'field': 'amount', 'old_value': 16402.74, 'new_value': 16729.540000000005}, {'field': 'count', 'old_value': 73, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 38094.5, 'new_value': 38421.3}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 74}]
2025-05-21 08:05:15,165 - INFO - 更新表单数据成功: FINST-1PF66KA1621VD7CFC24MQ60ZVFCH36F5C16AM5Q
2025-05-21 08:05:15,166 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 15127.1, 'new_value': 15095.06}, {'field': 'amount', 'old_value': 15127.1, 'new_value': 15095.06}]
2025-05-21 08:05:15,618 - INFO - 更新表单数据成功: FINST-OIF66BA1Y9GVVGXN8DEGDBVE8H1N3ES579PAM62
2025-05-21 08:05:15,619 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_20250427, 变更字段: [{'field': 'amount', 'old_value': 5189.3, 'new_value': 5249.3}, {'field': 'count', 'old_value': 19, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 5156.4, 'new_value': 5216.4}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 19}]
2025-05-21 08:05:16,134 - INFO - 更新表单数据成功: FINST-7PF66BA1F11VTFHR73LN29AFOIHR23WBSG7AMRL
2025-05-21 08:05:16,134 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 8619.82, 'new_value': 8612.02}, {'field': 'amount', 'old_value': 8619.82, 'new_value': 8612.02}, {'field': 'instoreAmount', 'old_value': 8264.74, 'new_value': 8256.94}]
2025-05-21 08:05:16,586 - INFO - 更新表单数据成功: FINST-7PF66BA1F11VTFHR73LN29AFOIHR24WBSG7AM1M
2025-05-21 08:05:16,586 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4718.99, 'new_value': 4638.02}, {'field': 'amount', 'old_value': 4718.99, 'new_value': 4638.02}, {'field': 'count', 'old_value': 205, 'new_value': 202}, {'field': 'instoreAmount', 'old_value': 3183.31, 'new_value': 3102.34}, {'field': 'instoreCount', 'old_value': 98, 'new_value': 95}]
2025-05-21 08:05:17,030 - INFO - 更新表单数据成功: FINST-OLC66Z61KZFVY1P064UV0CKKT4XH24C489PAMX2
2025-05-21 08:05:17,031 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_20250504, 变更字段: [{'field': 'amount', 'old_value': 3896.2, 'new_value': 3596.2}, {'field': 'instoreAmount', 'old_value': 3896.2, 'new_value': 3596.2}]
2025-05-21 08:05:17,461 - INFO - 更新表单数据成功: FINST-OLC66Z61KZFVY1P064UV0CKKT4XH24C489PAMH4
2025-05-21 08:05:17,462 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_20250503, 变更字段: [{'field': 'recommendAmount', 'old_value': 11946.08, 'new_value': 11920.08}, {'field': 'amount', 'old_value': 11946.08, 'new_value': 11920.08}]
2025-05-21 08:05:17,927 - INFO - 更新表单数据成功: FINST-LLF66F71LFFV6KOC788B149S4LOP2885D1QAMAI
2025-05-21 08:05:17,928 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_20250504, 变更字段: [{'field': 'amount', 'old_value': 3477.4, 'new_value': 3935.4}, {'field': 'count', 'old_value': 7, 'new_value': 8}, {'field': 'onlineAmount', 'old_value': 257.6, 'new_value': 715.6}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 5}]
2025-05-21 08:05:18,378 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3TYFD1QAMX6
2025-05-21 08:05:18,378 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_********, 变更字段: [{'field': 'amount', 'old_value': 5660.0, 'new_value': 5699.0}, {'field': 'count', 'old_value': 31, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 5660.0, 'new_value': 5699.0}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 32}]
2025-05-21 08:05:18,742 - INFO - 更新表单数据成功: FINST-RNA66D71GYHVG1PY7014I7W5X12W3ZWUAWSAMF9
2025-05-21 08:05:18,743 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_********, 变更字段: [{'field': 'amount', 'old_value': 34664.55, 'new_value': 35369.55}, {'field': 'count', 'old_value': 98, 'new_value': 99}, {'field': 'instoreAmount', 'old_value': 33903.35, 'new_value': 34608.35}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 70}]
2025-05-21 08:05:19,195 - INFO - 更新表单数据成功: FINST-1PF66VA1ABKVGYSAAD88Z474VOJK3D687RVAM85
2025-05-21 08:05:19,195 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8155.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8155.7}]
2025-05-21 08:05:19,632 - INFO - 更新表单数据成功: FINST-8LC66GC10EKVH1QRFQ06H980BXSM2HTA7RVAME
2025-05-21 08:05:19,632 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_20250519, 变更字段: [{'field': 'amount', 'old_value': 3796.92, 'new_value': 3856.92}, {'field': 'count', 'old_value': 229, 'new_value': 230}, {'field': 'onlineAmount', 'old_value': 1879.71, 'new_value': 1939.71}, {'field': 'onlineCount', 'old_value': 117, 'new_value': 118}]
2025-05-21 08:05:20,138 - INFO - 更新表单数据成功: FINST-8LC66GC10EKVH1QRFQ06H980BXSM2ITA7RVAMN
2025-05-21 08:05:20,139 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_20250519, 变更字段: [{'field': 'amount', 'old_value': 2893.04, 'new_value': 2907.1400000000003}, {'field': 'count', 'old_value': 127, 'new_value': 128}, {'field': 'instoreAmount', 'old_value': 2924.24, 'new_value': 2938.34}, {'field': 'instoreCount', 'old_value': 127, 'new_value': 128}]
2025-05-21 08:05:20,655 - INFO - 更新表单数据成功: FINST-8LC66GC10EKVH1QRFQ06H980BXSM2ITA7RVAMO1
2025-05-21 08:05:20,655 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 43675.0, 'new_value': 43879.0}, {'field': 'amount', 'old_value': 43675.0, 'new_value': 43879.0}, {'field': 'count', 'old_value': 22, 'new_value': 23}, {'field': 'instoreAmount', 'old_value': 43675.0, 'new_value': 43879.0}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 23}]
2025-05-21 08:05:21,093 - INFO - 更新表单数据成功: FINST-8LC66GC10EKVH1QRFQ06H980BXSM2ITA7RVAM52
2025-05-21 08:05:21,093 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_20250519, 变更字段: [{'field': 'amount', 'old_value': 1532.0, 'new_value': 1830.0}, {'field': 'count', 'old_value': 6, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 1532.0, 'new_value': 1830.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 7}]
2025-05-21 08:05:21,529 - INFO - 更新表单数据成功: FINST-8LC66GC10EKVH1QRFQ06H980BXSM2JTA7RVAMY2
2025-05-21 08:05:21,529 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 1999.93, 'new_value': 2025.87}, {'field': 'amount', 'old_value': 1999.93, 'new_value': 2025.8700000000001}, {'field': 'count', 'old_value': 130, 'new_value': 134}, {'field': 'instoreAmount', 'old_value': 549.4, 'new_value': 562.34}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 38}, {'field': 'onlineAmount', 'old_value': 1453.11, 'new_value': 1466.11}, {'field': 'onlineCount', 'old_value': 94, 'new_value': 96}]
2025-05-21 08:05:22,082 - INFO - 更新表单数据成功: FINST-FD966QA18DKVI6ZBDY9RGAQR61J939GD7RVAMA7
2025-05-21 08:05:22,082 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 7465.04, 'new_value': 7557.54}, {'field': 'amount', 'old_value': 7465.04, 'new_value': 7557.54}, {'field': 'count', 'old_value': 173, 'new_value': 174}, {'field': 'onlineAmount', 'old_value': 1867.13, 'new_value': 1959.63}, {'field': 'onlineCount', 'old_value': 36, 'new_value': 37}]
2025-05-21 08:05:22,521 - INFO - 更新表单数据成功: FINST-FD966QA18DKVI6ZBDY9RGAQR61J93AGD7RVAMD7
2025-05-21 08:05:22,522 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_20250519, 变更字段: [{'field': 'amount', 'old_value': 4737.1900000000005, 'new_value': 4753.29}, {'field': 'count', 'old_value': 263, 'new_value': 264}, {'field': 'onlineAmount', 'old_value': 1937.51, 'new_value': 1953.61}, {'field': 'onlineCount', 'old_value': 116, 'new_value': 117}]
2025-05-21 08:05:22,871 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2ENPAWSAM4R
2025-05-21 08:05:22,872 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 12531.84, 'new_value': 12483.44}, {'field': 'amount', 'old_value': 12531.84, 'new_value': 12483.44}]
2025-05-21 08:05:23,364 - INFO - 更新表单数据成功: FINST-FD966QA18DKVI6ZBDY9RGAQR61J93AGD7RVAMV7
2025-05-21 08:05:23,365 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 3701.08, 'new_value': 4814.76}, {'field': 'amount', 'old_value': 3701.08, 'new_value': 4814.759999999999}, {'field': 'count', 'old_value': 165, 'new_value': 226}, {'field': 'instoreAmount', 'old_value': 919.48, 'new_value': 1316.85}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 57}, {'field': 'onlineAmount', 'old_value': 2805.0, 'new_value': 3639.01}, {'field': 'onlineCount', 'old_value': 128, 'new_value': 169}]
2025-05-21 08:05:23,845 - INFO - 更新表单数据成功: FINST-737662B1ZBHV6ASXA3DE16QPIE3N2ENPAWSAMAR
2025-05-21 08:05:23,845 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 6587.65, 'new_value': 6599.15}, {'field': 'amount', 'old_value': 6587.650000000001, 'new_value': 6599.150000000001}, {'field': 'count', 'old_value': 310, 'new_value': 311}, {'field': 'onlineAmount', 'old_value': 4428.67, 'new_value': 4440.17}, {'field': 'onlineCount', 'old_value': 207, 'new_value': 208}]
2025-05-21 08:05:24,353 - INFO - 更新表单数据成功: FINST-FD966QA18DKVI6ZBDY9RGAQR61J93AGD7RVAMX7
2025-05-21 08:05:24,354 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_20250519, 变更字段: [{'field': 'amount', 'old_value': 3165.22, 'new_value': 3188.22}, {'field': 'count', 'old_value': 85, 'new_value': 86}, {'field': 'instoreAmount', 'old_value': 1473.61, 'new_value': 1496.61}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 16}]
2025-05-21 08:05:24,844 - INFO - 更新表单数据成功: FINST-FD966QA18DKVI6ZBDY9RGAQR61J93AGD7RVAM48
2025-05-21 08:05:24,844 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 20091.86}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 20091.86}]
2025-05-21 08:05:25,298 - INFO - 更新表单数据成功: FINST-FD966QA18DKVI6ZBDY9RGAQR61J93AGD7RVAM78
2025-05-21 08:05:25,299 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_20250519, 变更字段: [{'field': 'amount', 'old_value': -6372.8, 'new_value': -6440.8}]
2025-05-21 08:05:25,955 - INFO - 更新表单数据成功: FINST-FD966QA18DKVI6ZBDY9RGAQR61J93AGD7RVAMC8
2025-05-21 08:05:25,956 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_20250519, 变更字段: [{'field': 'amount', 'old_value': 13573.73, 'new_value': 13787.73}, {'field': 'count', 'old_value': 154, 'new_value': 155}, {'field': 'instoreAmount', 'old_value': 11551.09, 'new_value': 11765.09}, {'field': 'instoreCount', 'old_value': 82, 'new_value': 83}]
2025-05-21 08:05:26,408 - INFO - 更新表单数据成功: FINST-FD966QA18DKVI6ZBDY9RGAQR61J93AGD7RVAM09
2025-05-21 08:05:26,409 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_20250519, 变更字段: [{'field': 'amount', 'old_value': 3789.19, 'new_value': 3827.19}, {'field': 'count', 'old_value': 175, 'new_value': 176}, {'field': 'onlineAmount', 'old_value': 2511.82, 'new_value': 2549.82}, {'field': 'onlineCount', 'old_value': 91, 'new_value': 92}]
2025-05-21 08:05:26,935 - INFO - 更新表单数据成功: FINST-FD966QA18DKVI6ZBDY9RGAQR61J93AGD7RVAM49
2025-05-21 08:05:26,936 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_20250519, 变更字段: [{'field': 'amount', 'old_value': 14137.07, 'new_value': 14144.98}, {'field': 'count', 'old_value': 211, 'new_value': 212}, {'field': 'instoreAmount', 'old_value': 8827.19, 'new_value': 8835.1}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 61}]
2025-05-21 08:05:27,336 - INFO - 更新表单数据成功: FINST-FD966QA18DKVI6ZBDY9RGAQR61J93AGD7RVAM79
2025-05-21 08:05:27,336 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 1118.52, 'new_value': 5165.64}, {'field': 'dailyBillAmount', 'old_value': 1118.52, 'new_value': 5165.64}, {'field': 'amount', 'old_value': 259.27, 'new_value': 509.77}, {'field': 'count', 'old_value': 32, 'new_value': 60}, {'field': 'instoreAmount', 'old_value': 345.9, 'new_value': 600.3}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 60}]
2025-05-21 08:05:27,832 - INFO - 更新表单数据成功: FINST-FD966QA18DKVI6ZBDY9RGAQR61J93AGD7RVAM89
2025-05-21 08:05:27,832 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6046.42}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6046.42}, {'field': 'amount', 'old_value': 1527.0900000000001, 'new_value': 1917.7900000000002}, {'field': 'count', 'old_value': 146, 'new_value': 181}, {'field': 'instoreAmount', 'old_value': 1580.9, 'new_value': 1975.4}, {'field': 'instoreCount', 'old_value': 146, 'new_value': 181}]
2025-05-21 08:05:28,275 - INFO - 更新表单数据成功: FINST-7PF66CC13AKVLF84EWBD0AB9MYP92G3G7RVAMR
2025-05-21 08:05:28,276 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_20250519, 变更字段: [{'field': 'amount', 'old_value': 230.0, 'new_value': 409.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 230.0, 'new_value': 409.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-05-21 08:05:28,822 - INFO - 更新表单数据成功: FINST-7PF66CC13AKVLF84EWBD0AB9MYP92G3G7RVAMX
2025-05-21 08:05:28,822 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_20250519, 变更字段: [{'field': 'amount', 'old_value': 12519.1, 'new_value': 13951.300000000001}, {'field': 'count', 'old_value': 14, 'new_value': 15}, {'field': 'instoreAmount', 'old_value': 14025.0, 'new_value': 15457.2}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 15}]
2025-05-21 08:05:29,279 - INFO - 更新表单数据成功: FINST-7PF66CC13AKVLF84EWBD0AB9MYP92H3G7RVAME1
2025-05-21 08:05:29,280 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4491.49}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4491.49}]
2025-05-21 08:05:29,752 - INFO - 更新表单数据成功: FINST-7PF66CC13AKVLF84EWBD0AB9MYP92H3G7RVAMN1
2025-05-21 08:05:29,752 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 2888.0, 'new_value': 3186.0}, {'field': 'amount', 'old_value': 2888.0, 'new_value': 3186.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 2888.0, 'new_value': 3186.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-05-21 08:05:30,166 - INFO - 更新表单数据成功: FINST-7PF66CC13AKVLF84EWBD0AB9MYP92H3G7RVAMQ1
2025-05-21 08:05:30,179 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_20250519, 变更字段: [{'field': 'amount', 'old_value': 2757.4900000000002, 'new_value': 2769.39}, {'field': 'count', 'old_value': 215, 'new_value': 217}, {'field': 'onlineAmount', 'old_value': 2519.95, 'new_value': 2531.85}, {'field': 'onlineCount', 'old_value': 199, 'new_value': 201}]
2025-05-21 08:05:30,642 - INFO - 更新表单数据成功: FINST-7PF66CC13AKVLF84EWBD0AB9MYP92H3G7RVAM32
2025-05-21 08:05:30,643 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 1577.78, 'new_value': 1590.78}, {'field': 'amount', 'old_value': 1577.78, 'new_value': 1590.78}, {'field': 'count', 'old_value': 84, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 502.28, 'new_value': 515.28}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 40}]
2025-05-21 08:05:31,123 - INFO - 更新表单数据成功: FINST-7PF66CC13AKVLF84EWBD0AB9MYP92H3G7RVAM72
2025-05-21 08:05:31,123 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_20250519, 变更字段: [{'field': 'amount', 'old_value': 6071.139999999999, 'new_value': 6080.95}, {'field': 'count', 'old_value': 364, 'new_value': 366}, {'field': 'instoreAmount', 'old_value': 4721.1, 'new_value': 4776.75}, {'field': 'instoreCount', 'old_value': 267, 'new_value': 272}, {'field': 'onlineAmount', 'old_value': 1490.84, 'new_value': 1445.0}, {'field': 'onlineCount', 'old_value': 97, 'new_value': 94}]
2025-05-21 08:05:31,668 - INFO - 更新表单数据成功: FINST-7PF66CC13AKVLF84EWBD0AB9MYP92H3G7RVAME2
2025-05-21 08:05:31,669 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_20250519, 变更字段: [{'field': 'amount', 'old_value': 4231.13, 'new_value': 4279.9}, {'field': 'onlineAmount', 'old_value': 2135.26, 'new_value': 2184.03}]
2025-05-21 08:05:32,142 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAMD5
2025-05-21 08:05:32,142 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 10607.75, 'new_value': 11281.76}, {'field': 'amount', 'old_value': 10607.75, 'new_value': 11281.76}, {'field': 'count', 'old_value': 429, 'new_value': 450}, {'field': 'onlineAmount', 'old_value': 10803.65, 'new_value': 11477.66}, {'field': 'onlineCount', 'old_value': 429, 'new_value': 450}]
2025-05-21 08:05:32,590 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAME5
2025-05-21 08:05:32,590 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_20250517, 变更字段: [{'field': 'recommendAmount', 'old_value': 699.34, 'new_value': 709.74}, {'field': 'amount', 'old_value': 699.34, 'new_value': 709.74}, {'field': 'count', 'old_value': 49, 'new_value': 50}, {'field': 'onlineAmount', 'old_value': 764.88, 'new_value': 775.28}, {'field': 'onlineCount', 'old_value': 49, 'new_value': 50}]
2025-05-21 08:05:33,023 - INFO - 更新表单数据成功: FINST-7PF66CC13AKVLF84EWBD0AB9MYP92I3G7RVAM33
2025-05-21 08:05:33,023 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_20250519, 变更字段: [{'field': 'amount', 'old_value': 11002.5, 'new_value': 10942.2}]
2025-05-21 08:05:33,558 - INFO - 更新表单数据成功: FINST-KLF66WC1WCKVPOSACQFQZB4VN5ZK23QI7RVAMXA
2025-05-21 08:05:33,559 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 2626.35, 'new_value': 2608.37}, {'field': 'amount', 'old_value': 2626.35, 'new_value': 2608.37}]
2025-05-21 08:05:33,952 - INFO - 更新表单数据成功: FINST-KLF66WC1WCKVPOSACQFQZB4VN5ZK23QI7RVAM8C
2025-05-21 08:05:33,952 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_20250519, 变更字段: [{'field': 'amount', 'old_value': 22081.24, 'new_value': 22750.24}, {'field': 'count', 'old_value': 124, 'new_value': 125}, {'field': 'instoreAmount', 'old_value': 19622.0, 'new_value': 20291.0}, {'field': 'instoreCount', 'old_value': 95, 'new_value': 96}]
2025-05-21 08:05:34,428 - INFO - 更新表单数据成功: FINST-KLF66WC1WCKVPOSACQFQZB4VN5ZK24QI7RVAM2D
2025-05-21 08:05:34,428 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_20250519, 变更字段: [{'field': 'amount', 'old_value': 4253.59, 'new_value': 4302.47}, {'field': 'count', 'old_value': 143, 'new_value': 144}, {'field': 'instoreAmount', 'old_value': 1079.35, 'new_value': 1128.23}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 38}]
2025-05-21 08:05:34,881 - INFO - 更新表单数据成功: FINST-KLF66WC1WCKVPOSACQFQZB4VN5ZK24QI7RVAMAD
2025-05-21 08:05:34,881 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_20250519, 变更字段: [{'field': 'amount', 'old_value': 1284.55, 'new_value': 1243.05}]
2025-05-21 08:05:34,962 - INFO - 正在批量插入每日数据，批次 1/23，共 100 条记录
2025-05-21 08:05:35,481 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-05-21 08:05:38,484 - INFO - 正在批量插入每日数据，批次 2/23，共 100 条记录
2025-05-21 08:05:38,959 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-05-21 08:05:41,961 - INFO - 正在批量插入每日数据，批次 3/23，共 100 条记录
2025-05-21 08:05:42,338 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-05-21 08:05:45,342 - INFO - 正在批量插入每日数据，批次 4/23，共 100 条记录
2025-05-21 08:05:45,808 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-05-21 08:05:48,811 - INFO - 正在批量插入每日数据，批次 5/23，共 100 条记录
2025-05-21 08:05:49,193 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-05-21 08:05:52,197 - INFO - 正在批量插入每日数据，批次 6/23，共 100 条记录
2025-05-21 08:05:52,563 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-05-21 08:05:55,566 - INFO - 正在批量插入每日数据，批次 7/23，共 100 条记录
2025-05-21 08:05:56,000 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-05-21 08:05:59,003 - INFO - 正在批量插入每日数据，批次 8/23，共 100 条记录
2025-05-21 08:05:59,445 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-05-21 08:06:02,449 - INFO - 正在批量插入每日数据，批次 9/23，共 100 条记录
2025-05-21 08:06:02,910 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-05-21 08:06:05,912 - INFO - 正在批量插入每日数据，批次 10/23，共 100 条记录
2025-05-21 08:06:06,354 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-05-21 08:06:09,358 - INFO - 正在批量插入每日数据，批次 11/23，共 100 条记录
2025-05-21 08:06:09,784 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-05-21 08:06:12,787 - INFO - 正在批量插入每日数据，批次 12/23，共 100 条记录
2025-05-21 08:06:13,228 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-05-21 08:06:16,231 - INFO - 正在批量插入每日数据，批次 13/23，共 100 条记录
2025-05-21 08:06:16,659 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-05-21 08:06:19,662 - INFO - 正在批量插入每日数据，批次 14/23，共 100 条记录
2025-05-21 08:06:20,031 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-05-21 08:06:23,035 - INFO - 正在批量插入每日数据，批次 15/23，共 100 条记录
2025-05-21 08:06:23,452 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-05-21 08:06:26,456 - INFO - 正在批量插入每日数据，批次 16/23，共 100 条记录
2025-05-21 08:06:26,807 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-05-21 08:06:29,810 - INFO - 正在批量插入每日数据，批次 17/23，共 100 条记录
2025-05-21 08:06:30,185 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-05-21 08:06:33,188 - INFO - 正在批量插入每日数据，批次 18/23，共 100 条记录
2025-05-21 08:06:33,604 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-05-21 08:06:36,607 - INFO - 正在批量插入每日数据，批次 19/23，共 100 条记录
2025-05-21 08:06:36,988 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-05-21 08:06:39,991 - INFO - 正在批量插入每日数据，批次 20/23，共 100 条记录
2025-05-21 08:06:40,414 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-05-21 08:06:43,416 - INFO - 正在批量插入每日数据，批次 21/23，共 100 条记录
2025-05-21 08:06:43,859 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-05-21 08:06:46,863 - INFO - 正在批量插入每日数据，批次 22/23，共 100 条记录
2025-05-21 08:06:47,270 - INFO - 批量插入每日数据成功，批次 22，100 条记录
2025-05-21 08:06:50,270 - INFO - 正在批量插入每日数据，批次 23/23，共 7 条记录
2025-05-21 08:06:50,435 - INFO - 批量插入每日数据成功，批次 23，7 条记录
2025-05-21 08:06:53,436 - INFO - 批量插入每日数据完成: 总计 2207 条，成功 2207 条，失败 0 条
2025-05-21 08:06:53,440 - INFO - 批量插入日销售数据完成，共 2207 条记录
2025-05-21 08:06:53,440 - INFO - 日销售数据同步完成！更新: 47 条，插入: 2207 条，错误: 0 条，跳过: 10842 条
2025-05-21 08:06:53,440 - INFO - 正在获取宜搭月销售表单数据...
2025-05-21 08:06:53,441 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-05-21 08:06:53,441 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-05-21 08:06:53,441 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-21 08:06:53,441 - INFO - Request Parameters - Page 1:
2025-05-21 08:06:53,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:06:53,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:06:54,150 - INFO - API请求耗时: 708ms
2025-05-21 08:06:54,150 - INFO - Response - Page 1
2025-05-21 08:06:54,151 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-21 08:06:54,151 - INFO - 查询完成，共获取到 0 条记录
2025-05-21 08:06:54,152 - WARNING - 月度分段 1 查询返回空数据
2025-05-21 08:06:54,152 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-05-21 08:06:54,152 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-05-21 08:06:54,152 - INFO - Request Parameters - Page 1:
2025-05-21 08:06:54,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:06:54,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:06:54,360 - INFO - API请求耗时: 207ms
2025-05-21 08:06:54,360 - INFO - Response - Page 1
2025-05-21 08:06:54,361 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-21 08:06:54,361 - INFO - 查询完成，共获取到 0 条记录
2025-05-21 08:06:54,361 - WARNING - 单月查询返回空数据: 2024-05
2025-05-21 08:06:54,862 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-05-21 08:06:54,862 - INFO - Request Parameters - Page 1:
2025-05-21 08:06:54,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:06:54,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:06:55,085 - INFO - API请求耗时: 222ms
2025-05-21 08:06:55,085 - INFO - Response - Page 1
2025-05-21 08:06:55,086 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-21 08:06:55,086 - INFO - 查询完成，共获取到 0 条记录
2025-05-21 08:06:55,086 - WARNING - 单月查询返回空数据: 2024-06
2025-05-21 08:06:55,588 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-21 08:06:55,588 - INFO - Request Parameters - Page 1:
2025-05-21 08:06:55,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:06:55,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:06:55,800 - INFO - API请求耗时: 211ms
2025-05-21 08:06:55,800 - INFO - Response - Page 1
2025-05-21 08:06:55,801 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-21 08:06:55,801 - INFO - 查询完成，共获取到 0 条记录
2025-05-21 08:06:55,801 - WARNING - 单月查询返回空数据: 2024-07
2025-05-21 08:06:57,303 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-05-21 08:06:57,303 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-21 08:06:57,304 - INFO - Request Parameters - Page 1:
2025-05-21 08:06:57,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:06:57,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:06:57,542 - INFO - API请求耗时: 238ms
2025-05-21 08:06:57,542 - INFO - Response - Page 1
2025-05-21 08:06:57,543 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-21 08:06:57,543 - INFO - 查询完成，共获取到 0 条记录
2025-05-21 08:06:57,544 - WARNING - 月度分段 2 查询返回空数据
2025-05-21 08:06:57,544 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-05-21 08:06:57,544 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-05-21 08:06:57,544 - INFO - Request Parameters - Page 1:
2025-05-21 08:06:57,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:06:57,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:06:57,760 - INFO - API请求耗时: 215ms
2025-05-21 08:06:57,760 - INFO - Response - Page 1
2025-05-21 08:06:57,761 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-21 08:06:57,761 - INFO - 查询完成，共获取到 0 条记录
2025-05-21 08:06:57,761 - WARNING - 单月查询返回空数据: 2024-08
2025-05-21 08:06:58,261 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-05-21 08:06:58,261 - INFO - Request Parameters - Page 1:
2025-05-21 08:06:58,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:06:58,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:06:58,487 - INFO - API请求耗时: 225ms
2025-05-21 08:06:58,487 - INFO - Response - Page 1
2025-05-21 08:06:58,487 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-21 08:06:58,488 - INFO - 查询完成，共获取到 0 条记录
2025-05-21 08:06:58,488 - WARNING - 单月查询返回空数据: 2024-09
2025-05-21 08:06:58,989 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-21 08:06:58,989 - INFO - Request Parameters - Page 1:
2025-05-21 08:06:58,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:06:58,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:06:59,267 - INFO - API请求耗时: 277ms
2025-05-21 08:06:59,267 - INFO - Response - Page 1
2025-05-21 08:06:59,268 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-21 08:06:59,268 - INFO - 查询完成，共获取到 0 条记录
2025-05-21 08:06:59,268 - WARNING - 单月查询返回空数据: 2024-10
2025-05-21 08:07:00,770 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-05-21 08:07:00,770 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-05-21 08:07:00,771 - INFO - Request Parameters - Page 1:
2025-05-21 08:07:00,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:00,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:01,330 - INFO - API请求耗时: 559ms
2025-05-21 08:07:01,331 - INFO - Response - Page 1
2025-05-21 08:07:01,331 - INFO - 第 1 页获取到 100 条记录
2025-05-21 08:07:01,832 - INFO - Request Parameters - Page 2:
2025-05-21 08:07:01,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:01,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:02,471 - INFO - API请求耗时: 638ms
2025-05-21 08:07:02,471 - INFO - Response - Page 2
2025-05-21 08:07:02,472 - INFO - 第 2 页获取到 100 条记录
2025-05-21 08:07:02,972 - INFO - Request Parameters - Page 3:
2025-05-21 08:07:02,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:02,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:03,490 - INFO - API请求耗时: 517ms
2025-05-21 08:07:03,490 - INFO - Response - Page 3
2025-05-21 08:07:03,491 - INFO - 第 3 页获取到 48 条记录
2025-05-21 08:07:03,491 - INFO - 查询完成，共获取到 248 条记录
2025-05-21 08:07:03,491 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-05-21 08:07:04,493 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-05-21 08:07:04,493 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-21 08:07:04,494 - INFO - Request Parameters - Page 1:
2025-05-21 08:07:04,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:04,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:05,141 - INFO - API请求耗时: 646ms
2025-05-21 08:07:05,142 - INFO - Response - Page 1
2025-05-21 08:07:05,142 - INFO - 第 1 页获取到 100 条记录
2025-05-21 08:07:05,643 - INFO - Request Parameters - Page 2:
2025-05-21 08:07:05,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:05,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:06,220 - INFO - API请求耗时: 575ms
2025-05-21 08:07:06,220 - INFO - Response - Page 2
2025-05-21 08:07:06,221 - INFO - 第 2 页获取到 100 条记录
2025-05-21 08:07:06,722 - INFO - Request Parameters - Page 3:
2025-05-21 08:07:06,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:06,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:07,285 - INFO - API请求耗时: 562ms
2025-05-21 08:07:07,285 - INFO - Response - Page 3
2025-05-21 08:07:07,286 - INFO - 第 3 页获取到 100 条记录
2025-05-21 08:07:07,787 - INFO - Request Parameters - Page 4:
2025-05-21 08:07:07,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:07,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:08,447 - INFO - API请求耗时: 659ms
2025-05-21 08:07:08,447 - INFO - Response - Page 4
2025-05-21 08:07:08,448 - INFO - 第 4 页获取到 100 条记录
2025-05-21 08:07:08,948 - INFO - Request Parameters - Page 5:
2025-05-21 08:07:08,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:08,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:09,556 - INFO - API请求耗时: 606ms
2025-05-21 08:07:09,556 - INFO - Response - Page 5
2025-05-21 08:07:09,556 - INFO - 第 5 页获取到 100 条记录
2025-05-21 08:07:10,057 - INFO - Request Parameters - Page 6:
2025-05-21 08:07:10,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:10,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:10,664 - INFO - API请求耗时: 605ms
2025-05-21 08:07:10,665 - INFO - Response - Page 6
2025-05-21 08:07:10,665 - INFO - 第 6 页获取到 100 条记录
2025-05-21 08:07:11,167 - INFO - Request Parameters - Page 7:
2025-05-21 08:07:11,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:11,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:11,760 - INFO - API请求耗时: 592ms
2025-05-21 08:07:11,761 - INFO - Response - Page 7
2025-05-21 08:07:11,762 - INFO - 第 7 页获取到 100 条记录
2025-05-21 08:07:12,263 - INFO - Request Parameters - Page 8:
2025-05-21 08:07:12,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:12,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:12,586 - INFO - API请求耗时: 322ms
2025-05-21 08:07:12,586 - INFO - Response - Page 8
2025-05-21 08:07:12,587 - INFO - 第 8 页获取到 16 条记录
2025-05-21 08:07:12,587 - INFO - 查询完成，共获取到 716 条记录
2025-05-21 08:07:12,588 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-05-21 08:07:13,589 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-05-21 08:07:13,589 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-05-21 08:07:13,590 - INFO - Request Parameters - Page 1:
2025-05-21 08:07:13,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:13,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:14,081 - INFO - API请求耗时: 491ms
2025-05-21 08:07:14,081 - INFO - Response - Page 1
2025-05-21 08:07:14,082 - INFO - 第 1 页获取到 100 条记录
2025-05-21 08:07:14,583 - INFO - Request Parameters - Page 2:
2025-05-21 08:07:14,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:14,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:15,358 - INFO - API请求耗时: 774ms
2025-05-21 08:07:15,359 - INFO - Response - Page 2
2025-05-21 08:07:15,359 - INFO - 第 2 页获取到 100 条记录
2025-05-21 08:07:15,861 - INFO - Request Parameters - Page 3:
2025-05-21 08:07:15,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:07:15,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:07:16,213 - INFO - API请求耗时: 351ms
2025-05-21 08:07:16,213 - INFO - Response - Page 3
2025-05-21 08:07:16,214 - INFO - 第 3 页获取到 24 条记录
2025-05-21 08:07:16,214 - INFO - 查询完成，共获取到 224 条记录
2025-05-21 08:07:16,215 - INFO - 月度分段 5 查询成功，获取到 224 条记录
2025-05-21 08:07:17,215 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1188 条记录，失败 0 次
2025-05-21 08:07:17,215 - INFO - 成功获取宜搭月销售表单数据，共 1188 条记录
2025-05-21 08:07:17,216 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-21 08:07:17,216 - INFO - 正在从SQLite获取月度汇总数据...
2025-05-21 08:07:17,226 - INFO - 成功获取SQLite月度汇总数据，共 1190 条记录
2025-05-21 08:07:17,284 - INFO - 成功创建宜搭月销售数据索引，共 1188 条记录
2025-05-21 08:07:17,820 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-05-21 08:07:17,820 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 131746.76, 'new_value': 140212.33}, {'field': 'dailyBillAmount', 'old_value': 131746.76, 'new_value': 140212.33}, {'field': 'amount', 'old_value': 4133.0, 'new_value': 4327.7}, {'field': 'count', 'old_value': 56, 'new_value': 60}, {'field': 'onlineAmount', 'old_value': 4209.0, 'new_value': 4403.7}, {'field': 'onlineCount', 'old_value': 56, 'new_value': 60}]
2025-05-21 08:07:18,231 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-05-21 08:07:18,232 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 350741.47, 'new_value': 370600.83}, {'field': 'dailyBillAmount', 'old_value': 350741.47, 'new_value': 370600.83}, {'field': 'amount', 'old_value': 191938.0, 'new_value': 203622.5}, {'field': 'count', 'old_value': 1755, 'new_value': 1884}, {'field': 'instoreAmount', 'old_value': 77769.1, 'new_value': 84032.1}, {'field': 'instoreCount', 'old_value': 571, 'new_value': 638}, {'field': 'onlineAmount', 'old_value': 114460.8, 'new_value': 119942.8}, {'field': 'onlineCount', 'old_value': 1184, 'new_value': 1246}]
2025-05-21 08:07:18,729 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-05-21 08:07:18,729 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 516183.18, 'new_value': 545129.4}, {'field': 'dailyBillAmount', 'old_value': 516183.18, 'new_value': 545129.4}, {'field': 'amount', 'old_value': 381282.72, 'new_value': 399661.13}, {'field': 'count', 'old_value': 1840, 'new_value': 1927}, {'field': 'instoreAmount', 'old_value': 381282.72, 'new_value': 399661.13}, {'field': 'instoreCount', 'old_value': 1840, 'new_value': 1927}]
2025-05-21 08:07:19,127 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-05-21 08:07:19,127 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 405950.18, 'new_value': 436249.58}, {'field': 'dailyBillAmount', 'old_value': 405950.18, 'new_value': 436249.58}, {'field': 'amount', 'old_value': 653436.0, 'new_value': 701583.0}, {'field': 'count', 'old_value': 2236, 'new_value': 2397}, {'field': 'instoreAmount', 'old_value': 654686.0, 'new_value': 702833.0}, {'field': 'instoreCount', 'old_value': 2236, 'new_value': 2397}]
2025-05-21 08:07:19,533 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-05-21 08:07:19,534 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 50091.3, 'new_value': 51158.2}, {'field': 'dailyBillAmount', 'old_value': 50091.3, 'new_value': 51158.2}, {'field': 'amount', 'old_value': 66779.11, 'new_value': 68414.41}, {'field': 'count', 'old_value': 218, 'new_value': 234}, {'field': 'instoreAmount', 'old_value': 39080.5, 'new_value': 39230.5}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 37}, {'field': 'onlineAmount', 'old_value': 31795.52, 'new_value': 33280.82}, {'field': 'onlineCount', 'old_value': 182, 'new_value': 197}]
2025-05-21 08:07:20,024 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-05-21 08:07:20,024 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 193305.9, 'new_value': 193703.9}, {'field': 'amount', 'old_value': 193305.9, 'new_value': 193703.9}, {'field': 'count', 'old_value': 100, 'new_value': 108}, {'field': 'instoreAmount', 'old_value': 193305.9, 'new_value': 193703.9}, {'field': 'instoreCount', 'old_value': 100, 'new_value': 108}]
2025-05-21 08:07:20,444 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-05-21 08:07:20,445 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 564971.13, 'new_value': 592946.94}, {'field': 'dailyBillAmount', 'old_value': 564971.13, 'new_value': 592946.94}, {'field': 'amount', 'old_value': 530679.61, 'new_value': 550607.61}, {'field': 'count', 'old_value': 3830, 'new_value': 3956}, {'field': 'instoreAmount', 'old_value': 426993.01, 'new_value': 445564.81}, {'field': 'instoreCount', 'old_value': 1827, 'new_value': 1914}, {'field': 'onlineAmount', 'old_value': 107021.17, 'new_value': 108476.87}, {'field': 'onlineCount', 'old_value': 2003, 'new_value': 2042}]
2025-05-21 08:07:20,986 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMTJ
2025-05-21 08:07:20,987 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 190622.88999999998, 'new_value': 205078.49}, {'field': 'amount', 'old_value': 190616.69, 'new_value': 205070.79}, {'field': 'count', 'old_value': 7992, 'new_value': 8586}, {'field': 'onlineAmount', 'old_value': 194900.17, 'new_value': 209607.21}, {'field': 'onlineCount', 'old_value': 7992, 'new_value': 8586}]
2025-05-21 08:07:21,456 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-05-21 08:07:21,456 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 570476.73, 'new_value': 596138.61}, {'field': 'dailyBillAmount', 'old_value': 570476.73, 'new_value': 596138.61}, {'field': 'amount', 'old_value': 130948.76, 'new_value': 143497.76}, {'field': 'count', 'old_value': 691, 'new_value': 780}, {'field': 'instoreAmount', 'old_value': 130948.76, 'new_value': 143497.76}, {'field': 'instoreCount', 'old_value': 691, 'new_value': 780}]
2025-05-21 08:07:21,957 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMVJ
2025-05-21 08:07:21,957 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-05, 变更字段: [{'field': 'amount', 'old_value': 19100.0, 'new_value': 20403.0}, {'field': 'count', 'old_value': 26, 'new_value': 29}, {'field': 'instoreAmount', 'old_value': 19100.0, 'new_value': 20403.0}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 29}]
2025-05-21 08:07:22,374 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-05-21 08:07:22,374 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 87308.3, 'new_value': 89540.3}, {'field': 'count', 'old_value': 246, 'new_value': 251}, {'field': 'instoreAmount', 'old_value': 87309.1, 'new_value': 89541.1}, {'field': 'instoreCount', 'old_value': 246, 'new_value': 251}]
2025-05-21 08:07:22,823 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXJ
2025-05-21 08:07:22,823 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSCOR6MVCC7Q2OV392411100148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 24670.9, 'new_value': 25269.9}, {'field': 'amount', 'old_value': 24670.9, 'new_value': 25269.9}, {'field': 'count', 'old_value': 21, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 26066.9, 'new_value': 26665.9}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 22}]
2025-05-21 08:07:23,339 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-05-21 08:07:23,340 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 783136.36, 'new_value': 831395.84}, {'field': 'dailyBillAmount', 'old_value': 783136.36, 'new_value': 831395.84}, {'field': 'amount', 'old_value': -277940.96, 'new_value': -308813.34}, {'field': 'count', 'old_value': 853, 'new_value': 889}, {'field': 'instoreAmount', 'old_value': 528513.08, 'new_value': 543606.6}, {'field': 'instoreCount', 'old_value': 853, 'new_value': 889}]
2025-05-21 08:07:23,787 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-05-21 08:07:23,788 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 288045.0, 'new_value': 308730.0}, {'field': 'amount', 'old_value': 288045.0, 'new_value': 308730.0}, {'field': 'count', 'old_value': 1054, 'new_value': 1118}, {'field': 'instoreAmount', 'old_value': 288045.0, 'new_value': 308730.0}, {'field': 'instoreCount', 'old_value': 1054, 'new_value': 1118}]
2025-05-21 08:07:24,259 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-05-21 08:07:24,259 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 318871.72000000003, 'new_value': 334156.25}, {'field': 'dailyBillAmount', 'old_value': 232379.82, 'new_value': 246138.95}, {'field': 'amount', 'old_value': 318871.72000000003, 'new_value': 334156.25}, {'field': 'count', 'old_value': 1102, 'new_value': 1159}, {'field': 'instoreAmount', 'old_value': 318871.72000000003, 'new_value': 334156.25}, {'field': 'instoreCount', 'old_value': 1102, 'new_value': 1159}]
2025-05-21 08:07:24,736 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-05-21 08:07:24,736 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 149152.85, 'new_value': 159418.45}, {'field': 'dailyBillAmount', 'old_value': 149152.85, 'new_value': 159418.45}, {'field': 'amount', 'old_value': 11033.3, 'new_value': 11620.8}, {'field': 'count', 'old_value': 79, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 12927.2, 'new_value': 13669.7}, {'field': 'instoreCount', 'old_value': 79, 'new_value': 85}]
2025-05-21 08:07:25,229 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-05-21 08:07:25,229 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81721.92, 'new_value': 84815.06999999999}, {'field': 'dailyBillAmount', 'old_value': 81721.92, 'new_value': 84815.06999999999}, {'field': 'amount', 'old_value': 51966.97, 'new_value': 53089.07}, {'field': 'count', 'old_value': 758, 'new_value': 778}, {'field': 'instoreAmount', 'old_value': 53864.87, 'new_value': 54986.97}, {'field': 'instoreCount', 'old_value': 758, 'new_value': 778}]
2025-05-21 08:07:25,738 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-05-21 08:07:25,739 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 111629.83, 'new_value': 119928.04}, {'field': 'amount', 'old_value': 111628.97, 'new_value': 119927.18}, {'field': 'count', 'old_value': 3884, 'new_value': 4134}, {'field': 'instoreAmount', 'old_value': 98269.74, 'new_value': 105300.34}, {'field': 'instoreCount', 'old_value': 3521, 'new_value': 3756}, {'field': 'onlineAmount', 'old_value': 13360.09, 'new_value': 14627.699999999999}, {'field': 'onlineCount', 'old_value': 363, 'new_value': 378}]
2025-05-21 08:07:26,232 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-05-21 08:07:26,233 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 219988.22, 'new_value': 256052.22}, {'field': 'dailyBillAmount', 'old_value': 214802.0, 'new_value': 250662.0}, {'field': 'amount', 'old_value': 183415.22, 'new_value': 218317.91}, {'field': 'count', 'old_value': 174, 'new_value': 200}, {'field': 'instoreAmount', 'old_value': 183286.0, 'new_value': 218147.0}, {'field': 'instoreCount', 'old_value': 173, 'new_value': 198}, {'field': 'onlineAmount', 'old_value': 129.22, 'new_value': 170.91}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 2}]
2025-05-21 08:07:26,759 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-05-21 08:07:26,759 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 429365.68, 'new_value': 459260.66}, {'field': 'dailyBillAmount', 'old_value': 428861.13, 'new_value': 458756.11}, {'field': 'amount', 'old_value': 429365.68, 'new_value': 459260.66}, {'field': 'count', 'old_value': 376, 'new_value': 402}, {'field': 'instoreAmount', 'old_value': 429366.68, 'new_value': 459261.66}, {'field': 'instoreCount', 'old_value': 376, 'new_value': 402}]
2025-05-21 08:07:27,364 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6K
2025-05-21 08:07:27,364 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-05, 变更字段: [{'field': 'amount', 'old_value': 33655.0, 'new_value': 34461.0}, {'field': 'count', 'old_value': 53, 'new_value': 57}, {'field': 'instoreAmount', 'old_value': 33655.0, 'new_value': 34461.0}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 57}]
2025-05-21 08:07:27,751 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-05-21 08:07:27,752 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 83777.2, 'new_value': 87630.0}, {'field': 'dailyBillAmount', 'old_value': 83777.2, 'new_value': 87630.0}, {'field': 'amount', 'old_value': 93914.9, 'new_value': 97727.7}, {'field': 'count', 'old_value': 244, 'new_value': 256}, {'field': 'instoreAmount', 'old_value': 93920.8, 'new_value': 97733.59999999999}, {'field': 'instoreCount', 'old_value': 244, 'new_value': 256}]
2025-05-21 08:07:28,256 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-05-21 08:07:28,256 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 129023.87, 'new_value': 134605.87}, {'field': 'amount', 'old_value': 129023.87, 'new_value': 134605.87}, {'field': 'count', 'old_value': 152, 'new_value': 161}, {'field': 'instoreAmount', 'old_value': 129150.87, 'new_value': 134732.87}, {'field': 'instoreCount', 'old_value': 152, 'new_value': 161}]
2025-05-21 08:07:28,714 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-05-21 08:07:28,715 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'amount', 'old_value': 189982.85, 'new_value': 198551.85}, {'field': 'count', 'old_value': 1263, 'new_value': 1326}, {'field': 'instoreAmount', 'old_value': 191191.85, 'new_value': 199760.85}, {'field': 'instoreCount', 'old_value': 1263, 'new_value': 1326}]
2025-05-21 08:07:29,244 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-05-21 08:07:29,244 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 115366.87, 'new_value': 123363.83}, {'field': 'dailyBillAmount', 'old_value': 115366.87, 'new_value': 123363.83}, {'field': 'amount', 'old_value': 10857.75, 'new_value': 12273.19}, {'field': 'count', 'old_value': 995, 'new_value': 1099}, {'field': 'instoreAmount', 'old_value': 14596.21, 'new_value': 16075.4}, {'field': 'instoreCount', 'old_value': 995, 'new_value': 1099}]
2025-05-21 08:07:29,804 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-05-21 08:07:29,805 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 205489.52, 'new_value': 214722.72}, {'field': 'amount', 'old_value': 205486.13, 'new_value': 214719.33000000002}, {'field': 'count', 'old_value': 5035, 'new_value': 5287}, {'field': 'instoreAmount', 'old_value': 199927.47, 'new_value': 208562.47}, {'field': 'instoreCount', 'old_value': 4855, 'new_value': 5091}, {'field': 'onlineAmount', 'old_value': 8524.03, 'new_value': 9306.23}, {'field': 'onlineCount', 'old_value': 180, 'new_value': 196}]
2025-05-21 08:07:30,251 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-05-21 08:07:30,252 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 181810.09, 'new_value': 196867.39}, {'field': 'dailyBillAmount', 'old_value': 181810.09, 'new_value': 196867.39}, {'field': 'amount', 'old_value': 181810.09, 'new_value': 196867.39}, {'field': 'count', 'old_value': 560, 'new_value': 605}, {'field': 'instoreAmount', 'old_value': 181810.09, 'new_value': 196867.39}, {'field': 'instoreCount', 'old_value': 560, 'new_value': 605}]
2025-05-21 08:07:30,671 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-05-21 08:07:30,671 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 157925.82, 'new_value': 167568.99}, {'field': 'dailyBillAmount', 'old_value': 157925.82, 'new_value': 167568.99}, {'field': 'amount', 'old_value': 54721.2, 'new_value': 57703.2}, {'field': 'count', 'old_value': 127, 'new_value': 135}, {'field': 'instoreAmount', 'old_value': 54721.2, 'new_value': 57703.2}, {'field': 'instoreCount', 'old_value': 127, 'new_value': 135}]
2025-05-21 08:07:31,098 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-05-21 08:07:31,098 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 319452.04, 'new_value': 347703.24}, {'field': 'dailyBillAmount', 'old_value': 319452.04, 'new_value': 347703.24}, {'field': 'amount', 'old_value': 131327.4, 'new_value': 142876.8}, {'field': 'count', 'old_value': 494, 'new_value': 533}, {'field': 'instoreAmount', 'old_value': 131327.66, 'new_value': 142877.06}, {'field': 'instoreCount', 'old_value': 494, 'new_value': 533}]
2025-05-21 08:07:31,509 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-05-21 08:07:31,509 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 67877.89, 'new_value': 71663.09}, {'field': 'dailyBillAmount', 'old_value': 67877.89, 'new_value': 71663.09}, {'field': 'amount', 'old_value': 20657.94, 'new_value': 21716.28}, {'field': 'count', 'old_value': 757, 'new_value': 797}, {'field': 'instoreAmount', 'old_value': 5006.73, 'new_value': 5121.53}, {'field': 'instoreCount', 'old_value': 127, 'new_value': 132}, {'field': 'onlineAmount', 'old_value': 15905.73, 'new_value': 16849.27}, {'field': 'onlineCount', 'old_value': 630, 'new_value': 665}]
2025-05-21 08:07:31,935 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-05-21 08:07:31,936 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 110508.19, 'new_value': 116772.73}, {'field': 'dailyBillAmount', 'old_value': 110508.19, 'new_value': 116772.73}, {'field': 'amount', 'old_value': 18046.28, 'new_value': 19263.79}, {'field': 'count', 'old_value': 440, 'new_value': 469}, {'field': 'instoreAmount', 'old_value': 15348.51, 'new_value': 16374.01}, {'field': 'instoreCount', 'old_value': 387, 'new_value': 413}, {'field': 'onlineAmount', 'old_value': 2698.46, 'new_value': 2890.47}, {'field': 'onlineCount', 'old_value': 53, 'new_value': 56}]
2025-05-21 08:07:32,448 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-05-21 08:07:32,448 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17457.03, 'new_value': 17503.03}, {'field': 'dailyBillAmount', 'old_value': 17457.03, 'new_value': 17503.03}, {'field': 'amount', 'old_value': 14587.18, 'new_value': 14633.18}, {'field': 'count', 'old_value': 483, 'new_value': 498}, {'field': 'instoreAmount', 'old_value': 14954.78, 'new_value': 15000.78}, {'field': 'instoreCount', 'old_value': 483, 'new_value': 498}]
2025-05-21 08:07:32,914 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-05-21 08:07:32,915 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 35651.32, 'new_value': 38279.8}, {'field': 'dailyBillAmount', 'old_value': 35651.32, 'new_value': 38279.8}, {'field': 'amount', 'old_value': 22707.76, 'new_value': 24395.89}, {'field': 'count', 'old_value': 1236, 'new_value': 1319}, {'field': 'instoreAmount', 'old_value': 11624.02, 'new_value': 12488.94}, {'field': 'instoreCount', 'old_value': 504, 'new_value': 536}, {'field': 'onlineAmount', 'old_value': 11597.72, 'new_value': 12420.93}, {'field': 'onlineCount', 'old_value': 732, 'new_value': 783}]
2025-05-21 08:07:33,328 - INFO - 更新表单数据成功: FINST-KLF66WC1ZU6VH5NR8PRRSCENITCO29B1M6DAM5G
2025-05-21 08:07:33,329 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDR5M9BI657Q2OV4FVC7BK001485_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 799.0, 'new_value': 2888.0}, {'field': 'dailyBillAmount', 'old_value': 799.0, 'new_value': 2888.0}, {'field': 'amount', 'old_value': 1149.0, 'new_value': 3238.0}, {'field': 'count', 'old_value': 3, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 1149.0, 'new_value': 3238.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 5}]
2025-05-21 08:07:33,802 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-05-21 08:07:33,802 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 246411.63999999998, 'new_value': 264099.85}, {'field': 'dailyBillAmount', 'old_value': 246411.63999999998, 'new_value': 264099.85}, {'field': 'amount', 'old_value': 113573.56, 'new_value': 118551.26}, {'field': 'count', 'old_value': 463, 'new_value': 489}, {'field': 'instoreAmount', 'old_value': 117645.52, 'new_value': 122973.52}, {'field': 'instoreCount', 'old_value': 463, 'new_value': 489}]
2025-05-21 08:07:34,376 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-05-21 08:07:34,376 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 12707.69, 'new_value': 13403.69}, {'field': 'count', 'old_value': 110, 'new_value': 118}, {'field': 'instoreAmount', 'old_value': 12781.93, 'new_value': 13477.93}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 118}]
2025-05-21 08:07:34,788 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-05-21 08:07:34,789 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 156295.82, 'new_value': 165974.4}, {'field': 'dailyBillAmount', 'old_value': 156295.82, 'new_value': 165974.4}, {'field': 'amount', 'old_value': 74903.0, 'new_value': 79734.56999999999}, {'field': 'count', 'old_value': 3162, 'new_value': 3375}, {'field': 'instoreAmount', 'old_value': 76366.62, 'new_value': 81405.32}, {'field': 'instoreCount', 'old_value': 3162, 'new_value': 3375}]
2025-05-21 08:07:35,229 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-05-21 08:07:35,230 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 346058.0, 'new_value': 381742.0}, {'field': 'dailyBillAmount', 'old_value': 346058.0, 'new_value': 381742.0}, {'field': 'amount', 'old_value': 346058.0, 'new_value': 381742.0}, {'field': 'count', 'old_value': 431, 'new_value': 476}, {'field': 'instoreAmount', 'old_value': 346058.0, 'new_value': 381742.0}, {'field': 'instoreCount', 'old_value': 431, 'new_value': 476}]
2025-05-21 08:07:35,648 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-05-21 08:07:35,648 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 166787.26, 'new_value': 177841.69}, {'field': 'dailyBillAmount', 'old_value': 166787.26, 'new_value': 177841.69}, {'field': 'amount', 'old_value': 96297.01, 'new_value': 101560.75}, {'field': 'count', 'old_value': 250, 'new_value': 265}, {'field': 'instoreAmount', 'old_value': 97713.61, 'new_value': 102977.35}, {'field': 'instoreCount', 'old_value': 250, 'new_value': 265}]
2025-05-21 08:07:36,119 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-05-21 08:07:36,120 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38593.0, 'new_value': 40293.0}, {'field': 'dailyBillAmount', 'old_value': 38593.0, 'new_value': 40293.0}, {'field': 'amount', 'old_value': 38593.0, 'new_value': 40293.0}, {'field': 'count', 'old_value': 752, 'new_value': 791}, {'field': 'instoreAmount', 'old_value': 38632.0, 'new_value': 40332.0}, {'field': 'instoreCount', 'old_value': 752, 'new_value': 791}]
2025-05-21 08:07:36,478 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-05-21 08:07:36,478 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68006.71, 'new_value': 73080.53}, {'field': 'dailyBillAmount', 'old_value': 68006.71, 'new_value': 73080.53}, {'field': 'amount', 'old_value': 70012.8, 'new_value': 75277.25}, {'field': 'count', 'old_value': 3729, 'new_value': 3981}, {'field': 'instoreAmount', 'old_value': 33377.33, 'new_value': 35755.71}, {'field': 'instoreCount', 'old_value': 1693, 'new_value': 1800}, {'field': 'onlineAmount', 'old_value': 37606.99, 'new_value': 40509.98}, {'field': 'onlineCount', 'old_value': 2036, 'new_value': 2181}]
2025-05-21 08:07:36,888 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-05-21 08:07:36,889 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23867.98, 'new_value': 25214.72}, {'field': 'dailyBillAmount', 'old_value': 23867.98, 'new_value': 25214.72}, {'field': 'amount', 'old_value': 33004.37, 'new_value': 34923.93}, {'field': 'count', 'old_value': 961, 'new_value': 1020}, {'field': 'instoreAmount', 'old_value': 29837.68, 'new_value': 31578.02}, {'field': 'instoreCount', 'old_value': 834, 'new_value': 886}, {'field': 'onlineAmount', 'old_value': 3222.86, 'new_value': 3402.08}, {'field': 'onlineCount', 'old_value': 127, 'new_value': 134}]
2025-05-21 08:07:37,327 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-05-21 08:07:37,327 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49352.11, 'new_value': 51904.49}, {'field': 'dailyBillAmount', 'old_value': 49352.11, 'new_value': 51904.49}, {'field': 'amount', 'old_value': 49295.94, 'new_value': 51820.42}, {'field': 'count', 'old_value': 1919, 'new_value': 2025}, {'field': 'instoreAmount', 'old_value': 31718.02, 'new_value': 33492.58}, {'field': 'instoreCount', 'old_value': 1132, 'new_value': 1201}, {'field': 'onlineAmount', 'old_value': 17754.98, 'new_value': 18555.82}, {'field': 'onlineCount', 'old_value': 787, 'new_value': 824}]
2025-05-21 08:07:37,768 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-05-21 08:07:37,769 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 52947.32, 'new_value': 55240.46}, {'field': 'count', 'old_value': 628, 'new_value': 660}, {'field': 'instoreAmount', 'old_value': 53374.22, 'new_value': 55667.36}, {'field': 'instoreCount', 'old_value': 628, 'new_value': 660}]
2025-05-21 08:07:38,283 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-05-21 08:07:38,284 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56292.6, 'new_value': 59142.2}, {'field': 'amount', 'old_value': 56292.1, 'new_value': 59141.7}, {'field': 'count', 'old_value': 1396, 'new_value': 1477}, {'field': 'instoreAmount', 'old_value': 57049.6, 'new_value': 60191.2}, {'field': 'instoreCount', 'old_value': 1396, 'new_value': 1477}]
2025-05-21 08:07:38,749 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-05-21 08:07:38,750 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 258320.42, 'new_value': 275858.42}, {'field': 'dailyBillAmount', 'old_value': 258320.42, 'new_value': 275858.42}, {'field': 'amount', 'old_value': 79181.82, 'new_value': 83954.82}, {'field': 'count', 'old_value': 284, 'new_value': 300}, {'field': 'instoreAmount', 'old_value': 79181.82, 'new_value': 83954.82}, {'field': 'instoreCount', 'old_value': 284, 'new_value': 300}]
2025-05-21 08:07:39,193 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-05-21 08:07:39,194 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 78997.52, 'new_value': 80044.66}, {'field': 'dailyBillAmount', 'old_value': 78997.52, 'new_value': 80044.66}, {'field': 'amount', 'old_value': 76299.72, 'new_value': 77346.86}, {'field': 'count', 'old_value': 261, 'new_value': 273}, {'field': 'instoreAmount', 'old_value': 78434.35, 'new_value': 79481.49}, {'field': 'instoreCount', 'old_value': 261, 'new_value': 273}]
2025-05-21 08:07:39,630 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-05-21 08:07:39,630 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41108.0, 'new_value': 43096.0}, {'field': 'dailyBillAmount', 'old_value': 41108.0, 'new_value': 43096.0}, {'field': 'amount', 'old_value': 50541.0, 'new_value': 54243.0}, {'field': 'count', 'old_value': 94, 'new_value': 101}, {'field': 'instoreAmount', 'old_value': 54639.0, 'new_value': 58341.0}, {'field': 'instoreCount', 'old_value': 94, 'new_value': 101}]
2025-05-21 08:07:40,072 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-05-21 08:07:40,073 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64847.549999999996, 'new_value': 69129.55}, {'field': 'dailyBillAmount', 'old_value': 62354.35, 'new_value': 66636.35}, {'field': 'amount', 'old_value': 64845.25, 'new_value': 69127.25}, {'field': 'count', 'old_value': 201, 'new_value': 214}, {'field': 'instoreAmount', 'old_value': 73008.65, 'new_value': 77410.65}, {'field': 'instoreCount', 'old_value': 201, 'new_value': 214}]
2025-05-21 08:07:40,485 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-05-21 08:07:40,485 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 94589.69, 'new_value': 97096.79}, {'field': 'dailyBillAmount', 'old_value': 94589.69, 'new_value': 97096.79}, {'field': 'amount', 'old_value': 52683.0, 'new_value': 54188.95}, {'field': 'count', 'old_value': 1436, 'new_value': 1484}, {'field': 'instoreAmount', 'old_value': 46021.68, 'new_value': 47308.72}, {'field': 'instoreCount', 'old_value': 1219, 'new_value': 1261}, {'field': 'onlineAmount', 'old_value': 7606.6, 'new_value': 7838.41}, {'field': 'onlineCount', 'old_value': 217, 'new_value': 223}]
2025-05-21 08:07:40,957 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-05-21 08:07:40,957 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 132459.33000000002, 'new_value': 140185.24}, {'field': 'dailyBillAmount', 'old_value': 127823.94, 'new_value': 135350.76}, {'field': 'amount', 'old_value': 132459.33000000002, 'new_value': 140185.24}, {'field': 'count', 'old_value': 1623, 'new_value': 1725}, {'field': 'instoreAmount', 'old_value': 126429.45, 'new_value': 133438.45}, {'field': 'instoreCount', 'old_value': 1557, 'new_value': 1650}, {'field': 'onlineAmount', 'old_value': 6029.88, 'new_value': 6746.79}, {'field': 'onlineCount', 'old_value': 66, 'new_value': 75}]
2025-05-21 08:07:41,430 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-05-21 08:07:41,431 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59561.43, 'new_value': 65252.23}, {'field': 'dailyBillAmount', 'old_value': 59561.43, 'new_value': 65252.23}, {'field': 'amount', 'old_value': 79888.99, 'new_value': 85696.66}, {'field': 'count', 'old_value': 338, 'new_value': 366}, {'field': 'instoreAmount', 'old_value': 77020.41, 'new_value': 82678.78}, {'field': 'instoreCount', 'old_value': 304, 'new_value': 329}, {'field': 'onlineAmount', 'old_value': 2868.58, 'new_value': 3017.88}, {'field': 'onlineCount', 'old_value': 34, 'new_value': 37}]
2025-05-21 08:07:41,862 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-05-21 08:07:41,862 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 164889.6, 'new_value': 175141.7}, {'field': 'dailyBillAmount', 'old_value': 164889.6, 'new_value': 175141.7}, {'field': 'amount', 'old_value': 168333.0, 'new_value': 176658.2}, {'field': 'count', 'old_value': 616, 'new_value': 649}, {'field': 'instoreAmount', 'old_value': 171217.9, 'new_value': 179543.1}, {'field': 'instoreCount', 'old_value': 616, 'new_value': 649}]
2025-05-21 08:07:42,402 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3L
2025-05-21 08:07:42,402 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38602.0, 'new_value': 39864.0}, {'field': 'dailyBillAmount', 'old_value': 38602.0, 'new_value': 39864.0}, {'field': 'amount', 'old_value': 35800.0, 'new_value': 37062.0}, {'field': 'count', 'old_value': 85, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 36393.0, 'new_value': 37655.0}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 88}]
2025-05-21 08:07:42,857 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5L
2025-05-21 08:07:42,858 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDS9O4DUBH2L6U1G19QP11V40018CI_2025-05, 变更字段: [{'field': 'count', 'old_value': 202, 'new_value': 203}, {'field': 'instoreCount', 'old_value': 155, 'new_value': 156}]
2025-05-21 08:07:43,310 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6L
2025-05-21 08:07:43,310 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HI85875BG16NA6U1G19QP117C001UB5_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19777.2, 'new_value': 20333.8}, {'field': 'dailyBillAmount', 'old_value': 19777.2, 'new_value': 20333.8}, {'field': 'amount', 'old_value': 15155.61, 'new_value': 15487.41}, {'field': 'count', 'old_value': 677, 'new_value': 695}, {'field': 'instoreAmount', 'old_value': 15348.66, 'new_value': 15680.46}, {'field': 'instoreCount', 'old_value': 677, 'new_value': 695}]
2025-05-21 08:07:43,849 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-05-21 08:07:43,849 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33650.39, 'new_value': 35868.07}, {'field': 'amount', 'old_value': 33648.92, 'new_value': 35866.6}, {'field': 'count', 'old_value': 1744, 'new_value': 1844}, {'field': 'instoreAmount', 'old_value': 39412.8, 'new_value': 41664.88}, {'field': 'instoreCount', 'old_value': 1744, 'new_value': 1844}]
2025-05-21 08:07:44,319 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-05-21 08:07:44,320 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 101583.87, 'new_value': 109870.98}, {'field': 'dailyBillAmount', 'old_value': 101583.87, 'new_value': 109870.98}, {'field': 'amount', 'old_value': 79159.3, 'new_value': 84358.3}, {'field': 'count', 'old_value': 323, 'new_value': 346}, {'field': 'instoreAmount', 'old_value': 79159.3, 'new_value': 84358.3}, {'field': 'instoreCount', 'old_value': 323, 'new_value': 346}]
2025-05-21 08:07:44,764 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-05-21 08:07:44,765 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 291932.07, 'new_value': 307030.45}, {'field': 'dailyBillAmount', 'old_value': 291932.07, 'new_value': 307030.45}, {'field': 'amount', 'old_value': 174282.75, 'new_value': 181116.66}, {'field': 'count', 'old_value': 2039, 'new_value': 2114}, {'field': 'instoreAmount', 'old_value': 72125.01, 'new_value': 75516.11}, {'field': 'instoreCount', 'old_value': 849, 'new_value': 890}, {'field': 'onlineAmount', 'old_value': 102157.74, 'new_value': 105600.55}, {'field': 'onlineCount', 'old_value': 1190, 'new_value': 1224}]
2025-05-21 08:07:45,191 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-05-21 08:07:45,191 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 180091.82, 'new_value': 188614.62}, {'field': 'dailyBillAmount', 'old_value': 180091.82, 'new_value': 188614.62}, {'field': 'amount', 'old_value': 186067.6, 'new_value': 194489.5}, {'field': 'count', 'old_value': 1108, 'new_value': 1161}, {'field': 'instoreAmount', 'old_value': 186847.5, 'new_value': 195269.4}, {'field': 'instoreCount', 'old_value': 1108, 'new_value': 1161}]
2025-05-21 08:07:45,676 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBL
2025-05-21 08:07:45,677 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 62074.89, 'new_value': 62223.89}, {'field': 'amount', 'old_value': 62074.89, 'new_value': 62223.89}, {'field': 'count', 'old_value': 27, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 62074.89, 'new_value': 62223.89}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 28}]
2025-05-21 08:07:46,138 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-05-21 08:07:46,139 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'amount', 'old_value': 82450.5, 'new_value': 86628.84}, {'field': 'count', 'old_value': 942, 'new_value': 981}, {'field': 'instoreAmount', 'old_value': 73428.07, 'new_value': 77485.9}, {'field': 'instoreCount', 'old_value': 647, 'new_value': 680}, {'field': 'onlineAmount', 'old_value': 9813.6, 'new_value': 9934.11}, {'field': 'onlineCount', 'old_value': 295, 'new_value': 301}]
2025-05-21 08:07:46,539 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-05-21 08:07:46,540 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 144337.1, 'new_value': 157311.6}, {'field': 'dailyBillAmount', 'old_value': 139531.35, 'new_value': 152505.85}, {'field': 'amount', 'old_value': 144337.1, 'new_value': 157311.6}, {'field': 'count', 'old_value': 605, 'new_value': 661}, {'field': 'instoreAmount', 'old_value': 144337.1, 'new_value': 157311.6}, {'field': 'instoreCount', 'old_value': 605, 'new_value': 661}]
2025-05-21 08:07:46,996 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-05-21 08:07:46,996 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17474.66, 'new_value': 18443.04}, {'field': 'dailyBillAmount', 'old_value': 17474.66, 'new_value': 18443.04}, {'field': 'amount', 'old_value': 20329.66, 'new_value': 21541.24}, {'field': 'count', 'old_value': 623, 'new_value': 652}, {'field': 'instoreAmount', 'old_value': 20349.46, 'new_value': 21561.04}, {'field': 'instoreCount', 'old_value': 623, 'new_value': 652}]
2025-05-21 08:07:47,518 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-05-21 08:07:47,518 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 221035.2, 'new_value': 246343.2}, {'field': 'amount', 'old_value': 221035.2, 'new_value': 246343.2}, {'field': 'count', 'old_value': 335, 'new_value': 375}, {'field': 'instoreAmount', 'old_value': 221035.2, 'new_value': 246343.2}, {'field': 'instoreCount', 'old_value': 335, 'new_value': 375}]
2025-05-21 08:07:47,997 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-05-21 08:07:47,997 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 39103.28, 'new_value': 39464.08}, {'field': 'amount', 'old_value': 39103.28, 'new_value': 39464.08}, {'field': 'count', 'old_value': 315, 'new_value': 319}, {'field': 'instoreAmount', 'old_value': 39103.28, 'new_value': 39464.08}, {'field': 'instoreCount', 'old_value': 315, 'new_value': 319}]
2025-05-21 08:07:48,405 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIL
2025-05-21 08:07:48,405 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 261107.0, 'new_value': 263244.0}, {'field': 'amount', 'old_value': 261107.0, 'new_value': 263244.0}, {'field': 'count', 'old_value': 57, 'new_value': 59}, {'field': 'instoreAmount', 'old_value': 261107.0, 'new_value': 263244.0}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 59}]
2025-05-21 08:07:48,821 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-05-21 08:07:48,821 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'amount', 'old_value': 31929.4, 'new_value': 33183.25}, {'field': 'count', 'old_value': 420, 'new_value': 440}, {'field': 'instoreAmount', 'old_value': 31929.4, 'new_value': 33183.25}, {'field': 'instoreCount', 'old_value': 420, 'new_value': 440}]
2025-05-21 08:07:49,241 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKL
2025-05-21 08:07:49,242 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 35644.0, 'new_value': 36282.0}, {'field': 'dailyBillAmount', 'old_value': 35644.0, 'new_value': 36282.0}, {'field': 'amount', 'old_value': 35644.0, 'new_value': 36282.0}, {'field': 'count', 'old_value': 42, 'new_value': 43}, {'field': 'instoreAmount', 'old_value': 35644.0, 'new_value': 36681.0}, {'field': 'instoreCount', 'old_value': 42, 'new_value': 43}]
2025-05-21 08:07:49,722 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-05-21 08:07:49,722 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 340622.36, 'new_value': 354478.88}, {'field': 'dailyBillAmount', 'old_value': 340622.36, 'new_value': 354478.88}, {'field': 'amount', 'old_value': 353253.36, 'new_value': 361649.88}, {'field': 'count', 'old_value': 1116, 'new_value': 1141}, {'field': 'instoreAmount', 'old_value': 353253.36, 'new_value': 361649.88}, {'field': 'instoreCount', 'old_value': 1116, 'new_value': 1141}]
2025-05-21 08:07:50,159 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-05-21 08:07:50,160 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 768796.28, 'new_value': 823266.61}, {'field': 'count', 'old_value': 990, 'new_value': 1055}, {'field': 'instoreAmount', 'old_value': 768796.45, 'new_value': 823266.78}, {'field': 'instoreCount', 'old_value': 990, 'new_value': 1055}]
2025-05-21 08:07:50,620 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-05-21 08:07:50,621 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 115378.0, 'new_value': 123533.7}, {'field': 'dailyBillAmount', 'old_value': 115378.0, 'new_value': 123533.7}, {'field': 'amount', 'old_value': 24335.1, 'new_value': 25742.1}, {'field': 'count', 'old_value': 93, 'new_value': 98}, {'field': 'instoreAmount', 'old_value': 24336.6, 'new_value': 25743.6}, {'field': 'instoreCount', 'old_value': 93, 'new_value': 98}]
2025-05-21 08:07:51,065 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2CHBNXX9MOB
2025-05-21 08:07:51,065 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-04, 变更字段: [{'field': 'amount', 'old_value': 119848.5, 'new_value': 119908.5}, {'field': 'count', 'old_value': 529, 'new_value': 530}, {'field': 'instoreAmount', 'old_value': 124040.1, 'new_value': 124100.1}, {'field': 'instoreCount', 'old_value': 505, 'new_value': 506}]
2025-05-21 08:07:51,538 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2CHBNXX9MWB
2025-05-21 08:07:51,538 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 155246.8, 'new_value': 155205.8}, {'field': 'amount', 'old_value': 155246.06, 'new_value': 155204.73}]
2025-05-21 08:07:51,974 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-05-21 08:07:51,974 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 165354.36, 'new_value': 170207.71}, {'field': 'amount', 'old_value': 165353.13999999998, 'new_value': 170206.1}, {'field': 'count', 'old_value': 1678, 'new_value': 1739}, {'field': 'instoreAmount', 'old_value': 108191.98999999999, 'new_value': 111038.73999999999}, {'field': 'instoreCount', 'old_value': 974, 'new_value': 1001}, {'field': 'onlineAmount', 'old_value': 61337.19, 'new_value': 63563.19}, {'field': 'onlineCount', 'old_value': 704, 'new_value': 738}]
2025-05-21 08:07:52,441 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-05-21 08:07:52,442 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 253539.42, 'new_value': 264599.74}, {'field': 'dailyBillAmount', 'old_value': 253539.42, 'new_value': 264599.74}, {'field': 'amount', 'old_value': 24213.41, 'new_value': 24905.23}, {'field': 'count', 'old_value': 748, 'new_value': 769}, {'field': 'instoreAmount', 'old_value': 27707.53, 'new_value': 28618.76}, {'field': 'instoreCount', 'old_value': 748, 'new_value': 769}]
2025-05-21 08:07:52,914 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-05-21 08:07:52,914 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 247430.54, 'new_value': 256037.33000000002}, {'field': 'dailyBillAmount', 'old_value': 247430.54, 'new_value': 256037.33000000002}, {'field': 'amount', 'old_value': 125703.46, 'new_value': 131677.34}, {'field': 'count', 'old_value': 2837, 'new_value': 2938}, {'field': 'instoreAmount', 'old_value': 106497.46, 'new_value': 111498.74}, {'field': 'instoreCount', 'old_value': 2408, 'new_value': 2484}, {'field': 'onlineAmount', 'old_value': 21190.52, 'new_value': 22282.12}, {'field': 'onlineCount', 'old_value': 429, 'new_value': 454}]
2025-05-21 08:07:53,413 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-05-21 08:07:53,414 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 234306.3, 'new_value': 240262.8}, {'field': 'amount', 'old_value': 234304.7, 'new_value': 240261.2}, {'field': 'count', 'old_value': 931, 'new_value': 957}, {'field': 'instoreAmount', 'old_value': 237116.9, 'new_value': 243352.7}, {'field': 'instoreCount', 'old_value': 931, 'new_value': 957}]
2025-05-21 08:07:53,832 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-05-21 08:07:53,833 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 400191.23, 'new_value': 417507.72}, {'field': 'dailyBillAmount', 'old_value': 400191.23, 'new_value': 417507.72}, {'field': 'amount', 'old_value': 382587.67, 'new_value': 392182.85}, {'field': 'count', 'old_value': 7274, 'new_value': 7431}, {'field': 'instoreAmount', 'old_value': 360222.63, 'new_value': 367421.42}, {'field': 'instoreCount', 'old_value': 6841, 'new_value': 6956}, {'field': 'onlineAmount', 'old_value': 23783.88, 'new_value': 26181.170000000002}, {'field': 'onlineCount', 'old_value': 433, 'new_value': 475}]
2025-05-21 08:07:54,296 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-05-21 08:07:54,296 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 230316.67, 'new_value': 252795.52}, {'field': 'amount', 'old_value': 198031.4, 'new_value': 220510.25}, {'field': 'count', 'old_value': 4787, 'new_value': 5309}, {'field': 'instoreAmount', 'old_value': 177496.9, 'new_value': 193471.2}, {'field': 'instoreCount', 'old_value': 3898, 'new_value': 4252}, {'field': 'onlineAmount', 'old_value': 20694.3, 'new_value': 27198.85}, {'field': 'onlineCount', 'old_value': 889, 'new_value': 1057}]
2025-05-21 08:07:54,797 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUL
2025-05-21 08:07:54,797 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-05, 变更字段: [{'field': 'amount', 'old_value': 32153.309999999998, 'new_value': 32050.309999999998}, {'field': 'count', 'old_value': 824, 'new_value': 827}, {'field': 'instoreAmount', 'old_value': 1877.0, 'new_value': 1943.0}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 38}, {'field': 'onlineAmount', 'old_value': 44876.729999999996, 'new_value': 44912.729999999996}, {'field': 'onlineCount', 'old_value': 787, 'new_value': 789}]
2025-05-21 08:07:55,146 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-05-21 08:07:55,146 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 63441.99, 'new_value': 73151.15}, {'field': 'dailyBillAmount', 'old_value': 63441.99, 'new_value': 73151.15}, {'field': 'amount', 'old_value': 124568.61, 'new_value': 132273.54}, {'field': 'count', 'old_value': 8530, 'new_value': 9041}, {'field': 'instoreAmount', 'old_value': 103507.42, 'new_value': 109418.05}, {'field': 'instoreCount', 'old_value': 6901, 'new_value': 7283}, {'field': 'onlineAmount', 'old_value': 24243.71, 'new_value': 26138.260000000002}, {'field': 'onlineCount', 'old_value': 1629, 'new_value': 1758}]
2025-05-21 08:07:55,705 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-05-21 08:07:55,705 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 216532.12, 'new_value': 223021.19}, {'field': 'dailyBillAmount', 'old_value': 216532.12, 'new_value': 223021.19}, {'field': 'amount', 'old_value': 208419.34, 'new_value': 215055.54}, {'field': 'count', 'old_value': 6083, 'new_value': 6279}, {'field': 'instoreAmount', 'old_value': 209735.05, 'new_value': 216430.55}, {'field': 'instoreCount', 'old_value': 6083, 'new_value': 6279}]
2025-05-21 08:07:56,283 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-05-21 08:07:56,284 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 61867.55, 'new_value': 64972.060000000005}, {'field': 'amount', 'old_value': 61865.87, 'new_value': 64969.6}, {'field': 'count', 'old_value': 3320, 'new_value': 3473}, {'field': 'instoreAmount', 'old_value': 37570.79, 'new_value': 38883.69}, {'field': 'instoreCount', 'old_value': 2129, 'new_value': 2209}, {'field': 'onlineAmount', 'old_value': 24296.76, 'new_value': 26088.37}, {'field': 'onlineCount', 'old_value': 1191, 'new_value': 1264}]
2025-05-21 08:07:56,740 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-05-21 08:07:56,741 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 109463.39, 'new_value': 113922.53}, {'field': 'dailyBillAmount', 'old_value': 109463.39, 'new_value': 113922.53}, {'field': 'amount', 'old_value': 22753.79, 'new_value': 23634.86}, {'field': 'count', 'old_value': 793, 'new_value': 828}, {'field': 'instoreAmount', 'old_value': 23429.58, 'new_value': 24453.760000000002}, {'field': 'instoreCount', 'old_value': 793, 'new_value': 828}]
2025-05-21 08:07:57,209 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-05-21 08:07:57,209 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 87783.73, 'new_value': 92979.42}, {'field': 'dailyBillAmount', 'old_value': 87783.73, 'new_value': 92979.42}, {'field': 'amount', 'old_value': 72986.67, 'new_value': 76535.29}, {'field': 'count', 'old_value': 3657, 'new_value': 3848}, {'field': 'instoreAmount', 'old_value': 16822.510000000002, 'new_value': 17358.08}, {'field': 'instoreCount', 'old_value': 1194, 'new_value': 1243}, {'field': 'onlineAmount', 'old_value': 57198.36, 'new_value': 60316.99}, {'field': 'onlineCount', 'old_value': 2463, 'new_value': 2605}]
2025-05-21 08:07:57,618 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-05-21 08:07:57,619 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82202.81999999999, 'new_value': 85019.98}, {'field': 'amount', 'old_value': 82201.94, 'new_value': 85019.1}, {'field': 'count', 'old_value': 2161, 'new_value': 2240}, {'field': 'instoreAmount', 'old_value': 79514.82, 'new_value': 82128.08}, {'field': 'instoreCount', 'old_value': 2109, 'new_value': 2185}, {'field': 'onlineAmount', 'old_value': 3644.41, 'new_value': 3848.31}, {'field': 'onlineCount', 'old_value': 52, 'new_value': 55}]
2025-05-21 08:07:58,088 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-05-21 08:07:58,088 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'amount', 'old_value': 119187.7, 'new_value': 128831.07}, {'field': 'count', 'old_value': 4978, 'new_value': 5316}, {'field': 'instoreAmount', 'old_value': 121897.55, 'new_value': 131665.32}, {'field': 'instoreCount', 'old_value': 4934, 'new_value': 5269}, {'field': 'onlineAmount', 'old_value': 1696.31, 'new_value': 1791.01}, {'field': 'onlineCount', 'old_value': 44, 'new_value': 47}]
2025-05-21 08:07:58,781 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-05-21 08:07:58,782 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 129932.12, 'new_value': 150544.1}, {'field': 'dailyBillAmount', 'old_value': 129932.12, 'new_value': 150544.1}, {'field': 'amount', 'old_value': 92336.33, 'new_value': 97483.83}, {'field': 'count', 'old_value': 7834, 'new_value': 8219}, {'field': 'instoreAmount', 'old_value': 6642.55, 'new_value': 6965.71}, {'field': 'instoreCount', 'old_value': 360, 'new_value': 382}, {'field': 'onlineAmount', 'old_value': 90305.27, 'new_value': 95333.81}, {'field': 'onlineCount', 'old_value': 7474, 'new_value': 7837}]
2025-05-21 08:07:59,290 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-05-21 08:07:59,290 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 126771.7, 'new_value': 136814.68}, {'field': 'dailyBillAmount', 'old_value': 126771.7, 'new_value': 136814.68}, {'field': 'amount', 'old_value': 106928.44, 'new_value': 116565.3}, {'field': 'count', 'old_value': 3643, 'new_value': 3843}, {'field': 'instoreAmount', 'old_value': 61230.49, 'new_value': 64455.15}, {'field': 'instoreCount', 'old_value': 2679, 'new_value': 2791}, {'field': 'onlineAmount', 'old_value': 53220.31, 'new_value': 60284.71}, {'field': 'onlineCount', 'old_value': 964, 'new_value': 1052}]
2025-05-21 08:07:59,725 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3M
2025-05-21 08:07:59,726 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 86053.53, 'new_value': 90951.53}, {'field': 'amount', 'old_value': 86053.0, 'new_value': 90951.0}, {'field': 'count', 'old_value': 50, 'new_value': 52}, {'field': 'instoreAmount', 'old_value': 86053.53, 'new_value': 90951.53}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 52}]
2025-05-21 08:08:00,146 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-05-21 08:08:00,146 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 43741.63, 'new_value': 46640.67}, {'field': 'dailyBillAmount', 'old_value': 43741.63, 'new_value': 46640.67}, {'field': 'amount', 'old_value': 59733.65, 'new_value': 63442.88}, {'field': 'count', 'old_value': 2340, 'new_value': 2458}, {'field': 'instoreAmount', 'old_value': 20293.51, 'new_value': 20876.56}, {'field': 'instoreCount', 'old_value': 863, 'new_value': 890}, {'field': 'onlineAmount', 'old_value': 40285.77, 'new_value': 43489.75}, {'field': 'onlineCount', 'old_value': 1477, 'new_value': 1568}]
2025-05-21 08:08:00,566 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-05-21 08:08:00,567 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 78283.15, 'new_value': 80117.41}, {'field': 'dailyBillAmount', 'old_value': 78283.15, 'new_value': 80117.41}, {'field': 'amount', 'old_value': 80567.08, 'new_value': 82448.58}, {'field': 'count', 'old_value': 2858, 'new_value': 2929}, {'field': 'instoreAmount', 'old_value': 80567.08, 'new_value': 82448.58}, {'field': 'instoreCount', 'old_value': 2858, 'new_value': 2929}]
2025-05-21 08:08:00,997 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-05-21 08:08:00,998 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 272889.0, 'new_value': 281934.0}, {'field': 'dailyBillAmount', 'old_value': 272889.0, 'new_value': 281934.0}, {'field': 'amount', 'old_value': 296690.0, 'new_value': 306185.0}, {'field': 'count', 'old_value': 240, 'new_value': 244}, {'field': 'instoreAmount', 'old_value': 322989.0, 'new_value': 332484.0}, {'field': 'instoreCount', 'old_value': 240, 'new_value': 244}]
2025-05-21 08:08:01,459 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-05-21 08:08:01,460 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 195456.1, 'new_value': 202475.79}, {'field': 'dailyBillAmount', 'old_value': 195456.1, 'new_value': 202475.79}, {'field': 'amount', 'old_value': 204453.46, 'new_value': 207041.66}, {'field': 'count', 'old_value': 386, 'new_value': 394}, {'field': 'instoreAmount', 'old_value': 206369.16, 'new_value': 208957.36}, {'field': 'instoreCount', 'old_value': 386, 'new_value': 394}]
2025-05-21 08:08:01,955 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-05-21 08:08:01,955 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 63415.0, 'new_value': 64680.0}, {'field': 'dailyBillAmount', 'old_value': 63415.0, 'new_value': 64680.0}, {'field': 'amount', 'old_value': 31242.0, 'new_value': 31641.0}, {'field': 'count', 'old_value': 83, 'new_value': 84}, {'field': 'instoreAmount', 'old_value': 32405.0, 'new_value': 32804.0}, {'field': 'instoreCount', 'old_value': 83, 'new_value': 84}]
2025-05-21 08:08:02,440 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-05-21 08:08:02,440 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55307.0, 'new_value': 56624.0}, {'field': 'dailyBillAmount', 'old_value': 39457.0, 'new_value': 40774.0}, {'field': 'amount', 'old_value': 51675.0, 'new_value': 52992.0}, {'field': 'count', 'old_value': 67, 'new_value': 69}, {'field': 'instoreAmount', 'old_value': 51675.0, 'new_value': 52992.0}, {'field': 'instoreCount', 'old_value': 67, 'new_value': 69}]
2025-05-21 08:08:02,908 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-05-21 08:08:02,908 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55831.4, 'new_value': 57292.4}, {'field': 'amount', 'old_value': 55829.2, 'new_value': 57290.2}, {'field': 'count', 'old_value': 150, 'new_value': 155}, {'field': 'instoreAmount', 'old_value': 55831.4, 'new_value': 57292.4}, {'field': 'instoreCount', 'old_value': 150, 'new_value': 155}]
2025-05-21 08:08:03,362 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM571
2025-05-21 08:08:03,363 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 441717.0, 'new_value': 463213.0}, {'field': 'dailyBillAmount', 'old_value': 441717.0, 'new_value': 463213.0}, {'field': 'amount', 'old_value': 504345.0, 'new_value': 525841.0}, {'field': 'count', 'old_value': 62, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 504345.0, 'new_value': 525841.0}, {'field': 'instoreCount', 'old_value': 62, 'new_value': 64}]
2025-05-21 08:08:03,858 - INFO - 更新表单数据成功: FINST-KLF66WC1ZU6VH5NR8PRRSCENITCO29B1M6DAM6G
2025-05-21 08:08:03,859 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20274.0, 'new_value': 20553.0}, {'field': 'amount', 'old_value': 20274.0, 'new_value': 20553.0}, {'field': 'count', 'old_value': 31, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 20274.0, 'new_value': 20553.0}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 32}]
2025-05-21 08:08:04,298 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM771
2025-05-21 08:08:04,298 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56778.0, 'new_value': 57487.0}, {'field': 'amount', 'old_value': 56778.0, 'new_value': 57487.0}, {'field': 'count', 'old_value': 64, 'new_value': 65}, {'field': 'instoreAmount', 'old_value': 56778.0, 'new_value': 57487.0}, {'field': 'instoreCount', 'old_value': 64, 'new_value': 65}]
2025-05-21 08:08:04,722 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-05-21 08:08:04,722 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 185789.1, 'new_value': 191414.5}, {'field': 'dailyBillAmount', 'old_value': 185789.1, 'new_value': 191414.5}, {'field': 'amount', 'old_value': 276222.9, 'new_value': 283280.2}, {'field': 'count', 'old_value': 342, 'new_value': 352}, {'field': 'instoreAmount', 'old_value': 285877.26, 'new_value': 294775.66000000003}, {'field': 'instoreCount', 'old_value': 342, 'new_value': 352}]
2025-05-21 08:08:05,241 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-05-21 08:08:05,242 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 88581.09, 'new_value': 93105.85}, {'field': 'dailyBillAmount', 'old_value': 88581.09, 'new_value': 93105.85}, {'field': 'amount', 'old_value': 24496.74, 'new_value': 29417.98}, {'field': 'count', 'old_value': 235, 'new_value': 272}, {'field': 'instoreAmount', 'old_value': 24009.94, 'new_value': 29349.18}, {'field': 'instoreCount', 'old_value': 190, 'new_value': 227}]
2025-05-21 08:08:05,683 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMA71
2025-05-21 08:08:05,684 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 10195.0, 'new_value': 11323.0}, {'field': 'amount', 'old_value': 10195.0, 'new_value': 11323.0}, {'field': 'count', 'old_value': 28, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 10195.0, 'new_value': 11323.0}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 31}]
2025-05-21 08:08:06,143 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-05-21 08:08:06,144 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27924.0, 'new_value': 30717.0}, {'field': 'dailyBillAmount', 'old_value': 27924.0, 'new_value': 30717.0}, {'field': 'amount', 'old_value': 31858.0, 'new_value': 35018.0}, {'field': 'count', 'old_value': 104, 'new_value': 113}, {'field': 'instoreAmount', 'old_value': 31858.0, 'new_value': 35018.0}, {'field': 'instoreCount', 'old_value': 104, 'new_value': 113}]
2025-05-21 08:08:06,598 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMC71
2025-05-21 08:08:06,599 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28018.7, 'new_value': 29334.7}, {'field': 'amount', 'old_value': 28018.7, 'new_value': 29334.7}, {'field': 'count', 'old_value': 163, 'new_value': 175}, {'field': 'instoreAmount', 'old_value': 28356.7, 'new_value': 29672.7}, {'field': 'instoreCount', 'old_value': 163, 'new_value': 175}]
2025-05-21 08:08:07,038 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-05-21 08:08:07,039 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 6528.0, 'new_value': 6990.0}, {'field': 'dailyBillAmount', 'old_value': 6528.0, 'new_value': 6990.0}, {'field': 'amount', 'old_value': 32659.0, 'new_value': 34505.0}, {'field': 'count', 'old_value': 99, 'new_value': 103}, {'field': 'instoreAmount', 'old_value': 33434.0, 'new_value': 35280.0}, {'field': 'instoreCount', 'old_value': 99, 'new_value': 103}]
2025-05-21 08:08:07,472 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-05-21 08:08:07,473 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 650329.29, 'new_value': 670850.91}, {'field': 'dailyBillAmount', 'old_value': 650329.29, 'new_value': 670850.91}, {'field': 'amount', 'old_value': 40827.33, 'new_value': 42427.09}, {'field': 'count', 'old_value': 387, 'new_value': 407}, {'field': 'instoreAmount', 'old_value': 33582.0, 'new_value': 34750.88}, {'field': 'instoreCount', 'old_value': 274, 'new_value': 287}, {'field': 'onlineAmount', 'old_value': 8268.36, 'new_value': 8699.24}, {'field': 'onlineCount', 'old_value': 113, 'new_value': 120}]
2025-05-21 08:08:07,930 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-05-21 08:08:07,930 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 62660.0, 'new_value': 65238.0}, {'field': 'amount', 'old_value': 62460.0, 'new_value': 65038.0}, {'field': 'count', 'old_value': 76, 'new_value': 80}, {'field': 'instoreAmount', 'old_value': 62959.0, 'new_value': 65537.0}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 80}]
2025-05-21 08:08:08,358 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMG71
2025-05-21 08:08:08,358 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 14751.0, 'new_value': 15080.0}, {'field': 'amount', 'old_value': 14751.0, 'new_value': 15080.0}, {'field': 'count', 'old_value': 23, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 14751.0, 'new_value': 15080.0}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 24}]
2025-05-21 08:08:08,762 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-05-21 08:08:08,762 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19399.57, 'new_value': 19997.17}, {'field': 'amount', 'old_value': 19398.87, 'new_value': 19996.47}, {'field': 'count', 'old_value': 75, 'new_value': 78}, {'field': 'instoreAmount', 'old_value': 19399.57, 'new_value': 19997.17}, {'field': 'instoreCount', 'old_value': 75, 'new_value': 78}]
2025-05-21 08:08:09,229 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMI71
2025-05-21 08:08:09,229 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37162.0, 'new_value': 37548.0}, {'field': 'dailyBillAmount', 'old_value': 37162.0, 'new_value': 37548.0}, {'field': 'amount', 'old_value': 37361.0, 'new_value': 37747.0}, {'field': 'count', 'old_value': 89, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 38607.0, 'new_value': 38993.0}, {'field': 'instoreCount', 'old_value': 89, 'new_value': 90}]
2025-05-21 08:08:09,626 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-05-21 08:08:09,626 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 297594.93, 'new_value': 302915.79}, {'field': 'dailyBillAmount', 'old_value': 278053.92, 'new_value': 283374.78}, {'field': 'amount', 'old_value': 295817.42, 'new_value': 301053.2}, {'field': 'count', 'old_value': 577, 'new_value': 589}, {'field': 'instoreAmount', 'old_value': 297850.52, 'new_value': 303088.3}, {'field': 'instoreCount', 'old_value': 577, 'new_value': 589}]
2025-05-21 08:08:10,081 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMK71
2025-05-21 08:08:10,081 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55627.0, 'new_value': 55697.0}, {'field': 'amount', 'old_value': 55627.0, 'new_value': 55697.0}, {'field': 'count', 'old_value': 251, 'new_value': 253}, {'field': 'instoreAmount', 'old_value': 56381.0, 'new_value': 56451.0}, {'field': 'instoreCount', 'old_value': 251, 'new_value': 253}]
2025-05-21 08:08:10,517 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-05-21 08:08:10,517 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51732.979999999996, 'new_value': 62229.78}, {'field': 'dailyBillAmount', 'old_value': 51732.979999999996, 'new_value': 62229.78}, {'field': 'amount', 'old_value': 54995.5, 'new_value': 65492.299999999996}, {'field': 'count', 'old_value': 327, 'new_value': 389}, {'field': 'instoreAmount', 'old_value': 54995.5, 'new_value': 65492.299999999996}, {'field': 'instoreCount', 'old_value': 327, 'new_value': 389}]
2025-05-21 08:08:11,055 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-05-21 08:08:11,055 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 67062.1, 'new_value': 87422.6}, {'field': 'dailyBillAmount', 'old_value': 67062.1, 'new_value': 87422.6}, {'field': 'amount', 'old_value': 29381.94, 'new_value': 31067.14}, {'field': 'count', 'old_value': 2857, 'new_value': 3023}, {'field': 'instoreAmount', 'old_value': 31365.52, 'new_value': 33117.02}, {'field': 'instoreCount', 'old_value': 2857, 'new_value': 3023}]
2025-05-21 08:08:11,479 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-05-21 08:08:11,479 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 484732.5, 'new_value': 508163.64}, {'field': 'dailyBillAmount', 'old_value': 484732.5, 'new_value': 508163.64}, {'field': 'amount', 'old_value': 500243.11, 'new_value': 524266.92}, {'field': 'count', 'old_value': 4654, 'new_value': 4939}, {'field': 'instoreAmount', 'old_value': 382387.4, 'new_value': 399820.97000000003}, {'field': 'instoreCount', 'old_value': 1855, 'new_value': 1954}, {'field': 'onlineAmount', 'old_value': 121833.5, 'new_value': 128773.85}, {'field': 'onlineCount', 'old_value': 2799, 'new_value': 2985}]
2025-05-21 08:08:11,995 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-05-21 08:08:11,995 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 150963.36, 'new_value': 161274.76}, {'field': 'amount', 'old_value': 150963.36, 'new_value': 161274.76}, {'field': 'count', 'old_value': 1008, 'new_value': 1086}, {'field': 'instoreAmount', 'old_value': 151072.36, 'new_value': 161709.76}, {'field': 'instoreCount', 'old_value': 1008, 'new_value': 1086}]
2025-05-21 08:08:12,409 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-05-21 08:08:12,410 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 73969.93, 'new_value': 79746.01}, {'field': 'dailyBillAmount', 'old_value': 73969.93, 'new_value': 79746.01}, {'field': 'amount', 'old_value': 86153.02, 'new_value': 92467.2}, {'field': 'count', 'old_value': 3926, 'new_value': 4241}, {'field': 'instoreAmount', 'old_value': 44714.71, 'new_value': 47613.85}, {'field': 'instoreCount', 'old_value': 2318, 'new_value': 2489}, {'field': 'onlineAmount', 'old_value': 42302.48, 'new_value': 45836.41}, {'field': 'onlineCount', 'old_value': 1608, 'new_value': 1752}]
2025-05-21 08:08:12,852 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR71
2025-05-21 08:08:12,852 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_2025-05, 变更字段: [{'field': 'count', 'old_value': 110, 'new_value': 111}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 111}]
2025-05-21 08:08:13,247 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS71
2025-05-21 08:08:13,247 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37974.0, 'new_value': 38363.0}, {'field': 'amount', 'old_value': 37974.0, 'new_value': 38363.0}, {'field': 'count', 'old_value': 20, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 37974.0, 'new_value': 38363.0}, {'field': 'instoreCount', 'old_value': 20, 'new_value': 22}]
2025-05-21 08:08:13,760 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-05-21 08:08:13,760 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 98856.24, 'new_value': 102659.86}, {'field': 'dailyBillAmount', 'old_value': 98856.24, 'new_value': 102659.86}, {'field': 'amount', 'old_value': 47080.16, 'new_value': 49020.18}, {'field': 'count', 'old_value': 3037, 'new_value': 3205}, {'field': 'instoreAmount', 'old_value': 7148.8, 'new_value': 7229.0}, {'field': 'instoreCount', 'old_value': 294, 'new_value': 303}, {'field': 'onlineAmount', 'old_value': 39931.36, 'new_value': 41791.18}, {'field': 'onlineCount', 'old_value': 2743, 'new_value': 2902}]
2025-05-21 08:08:14,244 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-05-21 08:08:14,245 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 268338.67, 'new_value': 283969.08}, {'field': 'dailyBillAmount', 'old_value': 268338.67, 'new_value': 283969.08}, {'field': 'amount', 'old_value': 250982.4, 'new_value': 264809.82}, {'field': 'count', 'old_value': 2162, 'new_value': 2306}, {'field': 'instoreAmount', 'old_value': 184168.09, 'new_value': 193140.79}, {'field': 'instoreCount', 'old_value': 890, 'new_value': 939}, {'field': 'onlineAmount', 'old_value': 66815.53, 'new_value': 71670.25}, {'field': 'onlineCount', 'old_value': 1272, 'new_value': 1367}]
2025-05-21 08:08:14,679 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-05-21 08:08:14,679 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 308707.42, 'new_value': 322472.57}, {'field': 'dailyBillAmount', 'old_value': 308707.42, 'new_value': 322472.57}, {'field': 'amount', 'old_value': 316355.31, 'new_value': 330531.36}, {'field': 'count', 'old_value': 1899, 'new_value': 1990}, {'field': 'instoreAmount', 'old_value': 288868.21, 'new_value': 301247.26}, {'field': 'instoreCount', 'old_value': 1599, 'new_value': 1675}, {'field': 'onlineAmount', 'old_value': 32750.2, 'new_value': 34777.2}, {'field': 'onlineCount', 'old_value': 300, 'new_value': 315}]
2025-05-21 08:08:15,164 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-05-21 08:08:15,164 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 787516.15, 'new_value': 827808.83}, {'field': 'dailyBillAmount', 'old_value': 787516.15, 'new_value': 827808.83}, {'field': 'amount', 'old_value': 866797.08, 'new_value': 911214.2}, {'field': 'count', 'old_value': 4640, 'new_value': 4921}, {'field': 'instoreAmount', 'old_value': 654132.56, 'new_value': 686484.27}, {'field': 'instoreCount', 'old_value': 2595, 'new_value': 2734}, {'field': 'onlineAmount', 'old_value': 218991.14, 'new_value': 231585.66}, {'field': 'onlineCount', 'old_value': 2045, 'new_value': 2187}]
2025-05-21 08:08:15,590 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-05-21 08:08:15,590 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 246937.5, 'new_value': 273429.04}, {'field': 'dailyBillAmount', 'old_value': 246937.5, 'new_value': 273429.04}, {'field': 'amount', 'old_value': 350739.01, 'new_value': 386545.02}, {'field': 'count', 'old_value': 1652, 'new_value': 1780}, {'field': 'instoreAmount', 'old_value': 329561.52, 'new_value': 363077.92}, {'field': 'instoreCount', 'old_value': 1324, 'new_value': 1432}, {'field': 'onlineAmount', 'old_value': 21479.19, 'new_value': 23904.8}, {'field': 'onlineCount', 'old_value': 328, 'new_value': 348}]
2025-05-21 08:08:16,019 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-05-21 08:08:16,019 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 304312.24, 'new_value': 319106.21}, {'field': 'dailyBillAmount', 'old_value': 304312.24, 'new_value': 319106.21}, {'field': 'amount', 'old_value': 287857.3, 'new_value': 301576.2}, {'field': 'count', 'old_value': 1265, 'new_value': 1341}, {'field': 'instoreAmount', 'old_value': 292372.0, 'new_value': 306430.8}, {'field': 'instoreCount', 'old_value': 1265, 'new_value': 1341}]
2025-05-21 08:08:16,471 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-05-21 08:08:16,472 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 625345.52, 'new_value': 657256.98}, {'field': 'amount', 'old_value': 625345.52, 'new_value': 657256.98}, {'field': 'count', 'old_value': 4869, 'new_value': 5165}, {'field': 'instoreAmount', 'old_value': 625345.52, 'new_value': 657256.98}, {'field': 'instoreCount', 'old_value': 4869, 'new_value': 5165}]
2025-05-21 08:08:16,888 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-05-21 08:08:16,889 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 485053.27, 'new_value': 515487.58999999997}, {'field': 'dailyBillAmount', 'old_value': 485053.27, 'new_value': 515487.58999999997}, {'field': 'amount', 'old_value': 612850.12, 'new_value': 646489.74}, {'field': 'count', 'old_value': 4212, 'new_value': 4468}, {'field': 'instoreAmount', 'old_value': 341792.4, 'new_value': 360700.8}, {'field': 'instoreCount', 'old_value': 1778, 'new_value': 1891}, {'field': 'onlineAmount', 'old_value': 279339.4, 'new_value': 294656.7}, {'field': 'onlineCount', 'old_value': 2434, 'new_value': 2577}]
2025-05-21 08:08:17,349 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-05-21 08:08:17,350 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 315747.81, 'new_value': 315793.71}, {'field': 'dailyBillAmount', 'old_value': 315747.81, 'new_value': 315793.71}, {'field': 'amount', 'old_value': 387996.37, 'new_value': 399815.52}, {'field': 'count', 'old_value': 4129, 'new_value': 4296}, {'field': 'instoreAmount', 'old_value': 273847.62, 'new_value': 280541.12}, {'field': 'instoreCount', 'old_value': 1837, 'new_value': 1895}, {'field': 'onlineAmount', 'old_value': 115644.72, 'new_value': 120804.17}, {'field': 'onlineCount', 'old_value': 2292, 'new_value': 2401}]
2025-05-21 08:08:17,763 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-05-21 08:08:17,763 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 401495.04, 'new_value': 421376.31}, {'field': 'dailyBillAmount', 'old_value': 401495.04, 'new_value': 421376.31}, {'field': 'amount', 'old_value': 406702.39, 'new_value': 427210.25}, {'field': 'count', 'old_value': 3798, 'new_value': 3999}, {'field': 'instoreAmount', 'old_value': 356024.85, 'new_value': 373443.26}, {'field': 'instoreCount', 'old_value': 1988, 'new_value': 2102}, {'field': 'onlineAmount', 'old_value': 51622.71, 'new_value': 54741.79}, {'field': 'onlineCount', 'old_value': 1810, 'new_value': 1897}]
2025-05-21 08:08:18,255 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-05-21 08:08:18,256 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 105881.8, 'new_value': 107400.8}, {'field': 'amount', 'old_value': 105881.3, 'new_value': 107400.3}, {'field': 'count', 'old_value': 471, 'new_value': 484}, {'field': 'instoreAmount', 'old_value': 105881.8, 'new_value': 107400.8}, {'field': 'instoreCount', 'old_value': 471, 'new_value': 484}]
2025-05-21 08:08:18,731 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-05-21 08:08:18,732 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 287191.09, 'new_value': 300330.79}, {'field': 'dailyBillAmount', 'old_value': 287191.09, 'new_value': 300330.79}, {'field': 'amount', 'old_value': -215800.98, 'new_value': -228877.68}, {'field': 'count', 'old_value': 783, 'new_value': 820}, {'field': 'instoreAmount', 'old_value': 5739.6, 'new_value': 5812.6}, {'field': 'instoreCount', 'old_value': 262, 'new_value': 276}, {'field': 'onlineAmount', 'old_value': 16329.42, 'new_value': 17139.12}, {'field': 'onlineCount', 'old_value': 521, 'new_value': 544}]
2025-05-21 08:08:19,177 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-05-21 08:08:19,178 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 481479.83, 'new_value': 501571.69}, {'field': 'dailyBillAmount', 'old_value': 481479.83, 'new_value': 501571.69}, {'field': 'amount', 'old_value': 374957.91000000003, 'new_value': 387962.91000000003}, {'field': 'count', 'old_value': 1552, 'new_value': 1617}, {'field': 'instoreAmount', 'old_value': 374957.91000000003, 'new_value': 387962.91000000003}, {'field': 'instoreCount', 'old_value': 1552, 'new_value': 1617}]
2025-05-21 08:08:19,691 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-05-21 08:08:19,692 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 293195.01, 'new_value': 312351.33999999997}, {'field': 'dailyBillAmount', 'old_value': 293195.01, 'new_value': 312351.33999999997}, {'field': 'amount', 'old_value': 130903.9, 'new_value': 135147.3}, {'field': 'count', 'old_value': 540, 'new_value': 563}, {'field': 'instoreAmount', 'old_value': 135945.1, 'new_value': 141163.6}, {'field': 'instoreCount', 'old_value': 523, 'new_value': 545}, {'field': 'onlineAmount', 'old_value': 1247.3999999999999, 'new_value': 1380.3}, {'field': 'onlineCount', 'old_value': 17, 'new_value': 18}]
2025-05-21 08:08:20,286 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-05-21 08:08:20,287 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 251800.91, 'new_value': 263696.36}, {'field': 'dailyBillAmount', 'old_value': 251800.91, 'new_value': 263696.36}, {'field': 'amount', 'old_value': 244172.41, 'new_value': 255764.95}, {'field': 'count', 'old_value': 1567, 'new_value': 1651}, {'field': 'instoreAmount', 'old_value': 230996.34, 'new_value': 241820.49}, {'field': 'instoreCount', 'old_value': 1227, 'new_value': 1291}, {'field': 'onlineAmount', 'old_value': 13318.83, 'new_value': 14087.22}, {'field': 'onlineCount', 'old_value': 340, 'new_value': 360}]
2025-05-21 08:08:20,779 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-05-21 08:08:20,780 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 262210.75, 'new_value': 277812.89}, {'field': 'dailyBillAmount', 'old_value': 262210.75, 'new_value': 277812.89}, {'field': 'amount', 'old_value': 115497.79, 'new_value': 119934.92}, {'field': 'count', 'old_value': 1835, 'new_value': 1935}, {'field': 'instoreAmount', 'old_value': 67540.43000000001, 'new_value': 69529.45}, {'field': 'instoreCount', 'old_value': 493, 'new_value': 513}, {'field': 'onlineAmount', 'old_value': 47960.01, 'new_value': 50408.34}, {'field': 'onlineCount', 'old_value': 1342, 'new_value': 1422}]
2025-05-21 08:08:21,193 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM881
2025-05-21 08:08:21,193 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-05, 变更字段: [{'field': 'amount', 'old_value': 47850.0, 'new_value': 51392.0}, {'field': 'count', 'old_value': 27, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 47850.0, 'new_value': 51392.0}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 28}]
2025-05-21 08:08:21,683 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-05-21 08:08:21,683 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 104193.17, 'new_value': 112681.58}, {'field': 'amount', 'old_value': 104185.7, 'new_value': 112673.2}, {'field': 'count', 'old_value': 4699, 'new_value': 5090}, {'field': 'instoreAmount', 'old_value': 38894.06, 'new_value': 41285.92}, {'field': 'instoreCount', 'old_value': 1551, 'new_value': 1658}, {'field': 'onlineAmount', 'old_value': 69731.37, 'new_value': 76138.82}, {'field': 'onlineCount', 'old_value': 3148, 'new_value': 3432}]
2025-05-21 08:08:22,219 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-05-21 08:08:22,219 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34965.9, 'new_value': 35741.9}, {'field': 'amount', 'old_value': 34965.9, 'new_value': 35741.9}, {'field': 'count', 'old_value': 162, 'new_value': 166}, {'field': 'instoreAmount', 'old_value': 34965.9, 'new_value': 35741.9}, {'field': 'instoreCount', 'old_value': 162, 'new_value': 166}]
2025-05-21 08:08:22,661 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-05-21 08:08:22,662 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 314753.43, 'new_value': 326189.87}, {'field': 'dailyBillAmount', 'old_value': 314753.43, 'new_value': 326189.87}, {'field': 'amount', 'old_value': 124746.4, 'new_value': 129984.7}, {'field': 'count', 'old_value': 2330, 'new_value': 2406}, {'field': 'instoreAmount', 'old_value': 125878.4, 'new_value': 131138.1}, {'field': 'instoreCount', 'old_value': 2330, 'new_value': 2406}]
2025-05-21 08:08:23,121 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-05-21 08:08:23,121 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 131002.36, 'new_value': 134064.72}, {'field': 'amount', 'old_value': 131002.36, 'new_value': 134063.52}, {'field': 'count', 'old_value': 3087, 'new_value': 3178}, {'field': 'instoreAmount', 'old_value': 131002.36, 'new_value': 134322.2}, {'field': 'instoreCount', 'old_value': 3087, 'new_value': 3178}]
2025-05-21 08:08:23,573 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-05-21 08:08:23,574 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22317.2, 'new_value': 23635.82}, {'field': 'amount', 'old_value': 22314.04, 'new_value': 23632.66}, {'field': 'count', 'old_value': 1323, 'new_value': 1418}, {'field': 'instoreAmount', 'old_value': 12303.59, 'new_value': 12818.7}, {'field': 'instoreCount', 'old_value': 627, 'new_value': 654}, {'field': 'onlineAmount', 'old_value': 10465.11, 'new_value': 11282.3}, {'field': 'onlineCount', 'old_value': 696, 'new_value': 764}]
2025-05-21 08:08:24,068 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-05-21 08:08:24,068 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37887.0, 'new_value': 38741.5}, {'field': 'amount', 'old_value': 37887.0, 'new_value': 38741.5}, {'field': 'count', 'old_value': 98, 'new_value': 100}, {'field': 'instoreAmount', 'old_value': 37887.0, 'new_value': 38741.5}, {'field': 'instoreCount', 'old_value': 98, 'new_value': 100}]
2025-05-21 08:08:24,509 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG81
2025-05-21 08:08:24,510 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ08R852VK83IKSIOCDI7KE001FRF_2025-05, 变更字段: [{'field': 'amount', 'old_value': 68238.83, 'new_value': 70790.43}, {'field': 'count', 'old_value': 2638, 'new_value': 2738}, {'field': 'instoreAmount', 'old_value': 68660.63, 'new_value': 71280.23}, {'field': 'instoreCount', 'old_value': 2638, 'new_value': 2738}]
2025-05-21 08:08:24,981 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-05-21 08:08:24,981 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 144068.02, 'new_value': 156331.48}, {'field': 'dailyBillAmount', 'old_value': 120824.8, 'new_value': 130787.2}, {'field': 'amount', 'old_value': 144067.34, 'new_value': 156330.8}, {'field': 'count', 'old_value': 2011, 'new_value': 2165}, {'field': 'instoreAmount', 'old_value': 138565.6, 'new_value': 150428.6}, {'field': 'instoreCount', 'old_value': 1755, 'new_value': 1891}, {'field': 'onlineAmount', 'old_value': 5729.54, 'new_value': 6130.0}, {'field': 'onlineCount', 'old_value': 256, 'new_value': 274}]
2025-05-21 08:08:25,403 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-05-21 08:08:25,403 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22205.54, 'new_value': 23104.84}, {'field': 'amount', 'old_value': 22204.74, 'new_value': 23104.04}, {'field': 'count', 'old_value': 943, 'new_value': 979}, {'field': 'instoreAmount', 'old_value': 18687.64, 'new_value': 19412.94}, {'field': 'instoreCount', 'old_value': 846, 'new_value': 876}, {'field': 'onlineAmount', 'old_value': 3558.1, 'new_value': 3732.1}, {'field': 'onlineCount', 'old_value': 97, 'new_value': 103}]
2025-05-21 08:08:25,853 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-05-21 08:08:25,854 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 302276.18, 'new_value': 322154.66000000003}, {'field': 'dailyBillAmount', 'old_value': 302276.18, 'new_value': 322154.66000000003}, {'field': 'amount', 'old_value': 387681.66, 'new_value': 411993.48}, {'field': 'count', 'old_value': 3988, 'new_value': 4216}, {'field': 'instoreAmount', 'old_value': 365199.96, 'new_value': 389112.77}, {'field': 'instoreCount', 'old_value': 2760, 'new_value': 2935}, {'field': 'onlineAmount', 'old_value': 30386.8, 'new_value': 31698.71}, {'field': 'onlineCount', 'old_value': 1228, 'new_value': 1281}]
2025-05-21 08:08:26,282 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-05-21 08:08:26,283 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 119473.43000000001, 'new_value': 125642.25}, {'field': 'dailyBillAmount', 'old_value': 119473.43000000001, 'new_value': 125642.25}, {'field': 'amount', 'old_value': 29380.68, 'new_value': 31124.91}, {'field': 'count', 'old_value': 478, 'new_value': 502}, {'field': 'instoreAmount', 'old_value': 18528.37, 'new_value': 19615.09}, {'field': 'instoreCount', 'old_value': 241, 'new_value': 257}, {'field': 'onlineAmount', 'old_value': 11715.64, 'new_value': 12373.15}, {'field': 'onlineCount', 'old_value': 237, 'new_value': 245}]
2025-05-21 08:08:26,732 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-05-21 08:08:26,732 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 118001.68, 'new_value': 123604.44}, {'field': 'dailyBillAmount', 'old_value': 105474.06999999999, 'new_value': 111105.1}, {'field': 'amount', 'old_value': 117999.8, 'new_value': 123602.56}, {'field': 'count', 'old_value': 6670, 'new_value': 6987}, {'field': 'instoreAmount', 'old_value': 74279.67, 'new_value': 77511.74}, {'field': 'instoreCount', 'old_value': 4122, 'new_value': 4303}, {'field': 'onlineAmount', 'old_value': 45285.47, 'new_value': 47728.72}, {'field': 'onlineCount', 'old_value': 2548, 'new_value': 2684}]
2025-05-21 08:08:27,238 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-05-21 08:08:27,239 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 61998.41, 'new_value': 64565.73}, {'field': 'amount', 'old_value': 61990.12, 'new_value': 64557.07}, {'field': 'count', 'old_value': 3878, 'new_value': 4055}, {'field': 'instoreAmount', 'old_value': 28564.44, 'new_value': 29367.34}, {'field': 'instoreCount', 'old_value': 1618, 'new_value': 1674}, {'field': 'onlineAmount', 'old_value': 35101.74, 'new_value': 36970.51}, {'field': 'onlineCount', 'old_value': 2260, 'new_value': 2381}]
2025-05-21 08:08:27,787 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-05-21 08:08:27,787 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'amount', 'old_value': 118944.16, 'new_value': 127256.12}, {'field': 'count', 'old_value': 1174, 'new_value': 1249}, {'field': 'instoreAmount', 'old_value': 119065.84, 'new_value': 127403.8}, {'field': 'instoreCount', 'old_value': 1174, 'new_value': 1249}]
2025-05-21 08:08:28,380 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-05-21 08:08:28,381 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 102380.79, 'new_value': 106509.2}, {'field': 'dailyBillAmount', 'old_value': 105730.74, 'new_value': 109990.88}, {'field': 'amount', 'old_value': 102374.98, 'new_value': 106503.39}, {'field': 'count', 'old_value': 2006, 'new_value': 2103}, {'field': 'instoreAmount', 'old_value': 98344.34, 'new_value': 102200.01}, {'field': 'instoreCount', 'old_value': 1700, 'new_value': 1773}, {'field': 'onlineAmount', 'old_value': 4121.89, 'new_value': 4405.43}, {'field': 'onlineCount', 'old_value': 306, 'new_value': 330}]
2025-05-21 08:08:28,872 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-05-21 08:08:28,872 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 148315.83, 'new_value': 158964.02}, {'field': 'dailyBillAmount', 'old_value': 148315.83, 'new_value': 158964.02}, {'field': 'amount', 'old_value': 19211.2, 'new_value': 20892.8}, {'field': 'count', 'old_value': 748, 'new_value': 828}, {'field': 'instoreAmount', 'old_value': 22148.59, 'new_value': 24096.14}, {'field': 'instoreCount', 'old_value': 748, 'new_value': 828}]
2025-05-21 08:08:29,326 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-05-21 08:08:29,327 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 390182.79, 'new_value': 420589.09}, {'field': 'dailyBillAmount', 'old_value': 390182.79, 'new_value': 420589.09}, {'field': 'amount', 'old_value': 38657.86, 'new_value': 42067.56}, {'field': 'count', 'old_value': 189, 'new_value': 206}, {'field': 'instoreAmount', 'old_value': 38883.66, 'new_value': 42293.36}, {'field': 'instoreCount', 'old_value': 189, 'new_value': 206}]
2025-05-21 08:08:29,778 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-05-21 08:08:29,779 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 13315.76, 'new_value': 14175.72}, {'field': 'count', 'old_value': 683, 'new_value': 727}, {'field': 'onlineAmount', 'old_value': 13402.02, 'new_value': 14261.98}, {'field': 'onlineCount', 'old_value': 683, 'new_value': 727}]
2025-05-21 08:08:30,390 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-05-21 08:08:30,391 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 221705.49, 'new_value': 241790.13}, {'field': 'amount', 'old_value': 221551.71, 'new_value': 241636.35}, {'field': 'count', 'old_value': 2307, 'new_value': 2495}, {'field': 'instoreAmount', 'old_value': 209977.6, 'new_value': 229168.5}, {'field': 'instoreCount', 'old_value': 1961, 'new_value': 2124}, {'field': 'onlineAmount', 'old_value': 14334.33, 'new_value': 15329.87}, {'field': 'onlineCount', 'old_value': 346, 'new_value': 371}]
2025-05-21 08:08:30,914 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-05-21 08:08:30,914 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 132872.84, 'new_value': 141761.77}, {'field': 'dailyBillAmount', 'old_value': 129072.37, 'new_value': 137961.3}, {'field': 'amount', 'old_value': 104851.41, 'new_value': 110000.07}, {'field': 'count', 'old_value': 3809, 'new_value': 3980}, {'field': 'instoreAmount', 'old_value': 47757.49, 'new_value': 49141.5}, {'field': 'instoreCount', 'old_value': 1680, 'new_value': 1728}, {'field': 'onlineAmount', 'old_value': 58290.22, 'new_value': 62456.57}, {'field': 'onlineCount', 'old_value': 2129, 'new_value': 2252}]
2025-05-21 08:08:31,479 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-05-21 08:08:31,479 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'amount', 'old_value': 2889.84, 'new_value': 3511.3}, {'field': 'count', 'old_value': 130, 'new_value': 160}, {'field': 'instoreAmount', 'old_value': 2889.84, 'new_value': 3511.3}, {'field': 'instoreCount', 'old_value': 130, 'new_value': 160}]
2025-05-21 08:08:31,946 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-05-21 08:08:31,946 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 4632.78, 'new_value': 4960.7}, {'field': 'count', 'old_value': 198, 'new_value': 215}, {'field': 'onlineAmount', 'old_value': 4632.78, 'new_value': 4960.7}, {'field': 'onlineCount', 'old_value': 198, 'new_value': 215}]
2025-05-21 08:08:32,403 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-05-21 08:08:32,404 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82412.82, 'new_value': 87543.93}, {'field': 'dailyBillAmount', 'old_value': 42223.87, 'new_value': 44145.87}, {'field': 'amount', 'old_value': 82412.23, 'new_value': 87543.34}, {'field': 'count', 'old_value': 2025, 'new_value': 2138}, {'field': 'instoreAmount', 'old_value': 44750.7, 'new_value': 47274.5}, {'field': 'instoreCount', 'old_value': 1088, 'new_value': 1147}, {'field': 'onlineAmount', 'old_value': 39627.96, 'new_value': 42349.27}, {'field': 'onlineCount', 'old_value': 937, 'new_value': 991}]
2025-05-21 08:08:32,794 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-05-21 08:08:32,795 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32686.53, 'new_value': 37142.08}, {'field': 'amount', 'old_value': 32686.53, 'new_value': 37142.08}, {'field': 'count', 'old_value': 1218, 'new_value': 1390}, {'field': 'instoreAmount', 'old_value': 33063.98, 'new_value': 37622.24}, {'field': 'instoreCount', 'old_value': 1218, 'new_value': 1390}]
2025-05-21 08:08:33,261 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-05-21 08:08:33,262 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 39039.41, 'new_value': 41025.81}, {'field': 'dailyBillAmount', 'old_value': 39039.41, 'new_value': 41025.81}, {'field': 'amount', 'old_value': 31787.48, 'new_value': 33197.17}, {'field': 'count', 'old_value': 1402, 'new_value': 1481}, {'field': 'instoreAmount', 'old_value': 18626.97, 'new_value': 19161.81}, {'field': 'instoreCount', 'old_value': 634, 'new_value': 650}, {'field': 'onlineAmount', 'old_value': 13204.99, 'new_value': 14079.84}, {'field': 'onlineCount', 'old_value': 768, 'new_value': 831}]
2025-05-21 08:08:33,725 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-05-21 08:08:33,725 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 66148.15, 'new_value': 69111.74}, {'field': 'amount', 'old_value': 66148.15, 'new_value': 69111.74}, {'field': 'count', 'old_value': 1949, 'new_value': 2116}, {'field': 'instoreAmount', 'old_value': 25716.94, 'new_value': 27440.61}, {'field': 'instoreCount', 'old_value': 917, 'new_value': 1048}, {'field': 'onlineAmount', 'old_value': 40498.91, 'new_value': 41772.62}, {'field': 'onlineCount', 'old_value': 1032, 'new_value': 1068}]
2025-05-21 08:08:34,160 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-05-21 08:08:34,161 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 39687.09, 'new_value': 41979.66}, {'field': 'amount', 'old_value': 39686.19, 'new_value': 41978.76}, {'field': 'count', 'old_value': 916, 'new_value': 965}, {'field': 'instoreAmount', 'old_value': 31110.4, 'new_value': 32798.25}, {'field': 'instoreCount', 'old_value': 739, 'new_value': 780}, {'field': 'onlineAmount', 'old_value': 8909.22, 'new_value': 9513.94}, {'field': 'onlineCount', 'old_value': 177, 'new_value': 185}]
2025-05-21 08:08:34,581 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-05-21 08:08:34,581 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 183800.49, 'new_value': 194833.08}, {'field': 'dailyBillAmount', 'old_value': 183800.49, 'new_value': 194833.08}, {'field': 'amount', 'old_value': 121401.53, 'new_value': 127847.19}, {'field': 'count', 'old_value': 3020, 'new_value': 3184}, {'field': 'instoreAmount', 'old_value': 76941.97, 'new_value': 80584.97}, {'field': 'instoreCount', 'old_value': 1503, 'new_value': 1579}, {'field': 'onlineAmount', 'old_value': 54012.5, 'new_value': 57350.16}, {'field': 'onlineCount', 'old_value': 1517, 'new_value': 1605}]
2025-05-21 08:08:35,078 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-05-21 08:08:35,078 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 562043.78, 'new_value': 593824.86}, {'field': 'dailyBillAmount', 'old_value': 562043.78, 'new_value': 593824.86}, {'field': 'amount', 'old_value': 517225.9, 'new_value': 539211.2}, {'field': 'count', 'old_value': 3055, 'new_value': 3214}, {'field': 'instoreAmount', 'old_value': 369858.4, 'new_value': 382069.4}, {'field': 'instoreCount', 'old_value': 2370, 'new_value': 2486}, {'field': 'onlineAmount', 'old_value': 147369.9, 'new_value': 157144.2}, {'field': 'onlineCount', 'old_value': 685, 'new_value': 728}]
2025-05-21 08:08:35,545 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-05-21 08:08:35,545 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 811603.33, 'new_value': 849510.7}, {'field': 'amount', 'old_value': 811602.83, 'new_value': 849510.2}, {'field': 'count', 'old_value': 2837, 'new_value': 2994}, {'field': 'instoreAmount', 'old_value': 811603.33, 'new_value': 849510.7}, {'field': 'instoreCount', 'old_value': 2837, 'new_value': 2994}]
2025-05-21 08:08:35,947 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-05-21 08:08:35,947 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 465601.32, 'new_value': 489901.83}, {'field': 'dailyBillAmount', 'old_value': 413704.57, 'new_value': 435599.85}, {'field': 'amount', 'old_value': 465601.32, 'new_value': 489901.83}, {'field': 'count', 'old_value': 2863, 'new_value': 3003}, {'field': 'instoreAmount', 'old_value': 424268.04000000004, 'new_value': 446609.55}, {'field': 'instoreCount', 'old_value': 1819, 'new_value': 1907}, {'field': 'onlineAmount', 'old_value': 41580.76, 'new_value': 43577.48}, {'field': 'onlineCount', 'old_value': 1044, 'new_value': 1096}]
2025-05-21 08:08:36,380 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-05-21 08:08:36,380 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 482877.17, 'new_value': 502808.4}, {'field': 'dailyBillAmount', 'old_value': 458976.68, 'new_value': 478907.91}, {'field': 'amount', 'old_value': 482877.17, 'new_value': 502808.4}, {'field': 'count', 'old_value': 1121, 'new_value': 1176}, {'field': 'instoreAmount', 'old_value': 452869.2, 'new_value': 470537.2}, {'field': 'instoreCount', 'old_value': 868, 'new_value': 911}, {'field': 'onlineAmount', 'old_value': 30135.25, 'new_value': 32398.48}, {'field': 'onlineCount', 'old_value': 253, 'new_value': 265}]
2025-05-21 08:08:36,818 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-05-21 08:08:36,819 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 546629.3, 'new_value': 584987.33}, {'field': 'amount', 'old_value': 546628.62, 'new_value': 584986.65}, {'field': 'count', 'old_value': 2789, 'new_value': 3003}, {'field': 'instoreAmount', 'old_value': 516252.79, 'new_value': 552486.27}, {'field': 'instoreCount', 'old_value': 1909, 'new_value': 2052}, {'field': 'onlineAmount', 'old_value': 30447.690000000002, 'new_value': 32572.24}, {'field': 'onlineCount', 'old_value': 880, 'new_value': 951}]
2025-05-21 08:08:37,268 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-05-21 08:08:37,269 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 653184.51, 'new_value': 682842.72}, {'field': 'dailyBillAmount', 'old_value': 653184.51, 'new_value': 682842.72}, {'field': 'amount', 'old_value': 583857.31, 'new_value': 614399.59}, {'field': 'count', 'old_value': 2901, 'new_value': 3068}, {'field': 'instoreAmount', 'old_value': 534491.36, 'new_value': 562352.22}, {'field': 'instoreCount', 'old_value': 2408, 'new_value': 2539}, {'field': 'onlineAmount', 'old_value': 49745.26, 'new_value': 52775.36}, {'field': 'onlineCount', 'old_value': 493, 'new_value': 529}]
2025-05-21 08:08:37,746 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-05-21 08:08:37,747 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 149295.24, 'new_value': 155176.24}, {'field': 'dailyBillAmount', 'old_value': 147888.69, 'new_value': 153769.69}, {'field': 'amount', 'old_value': 146172.66, 'new_value': 151971.66}, {'field': 'count', 'old_value': 219, 'new_value': 225}, {'field': 'instoreAmount', 'old_value': 146172.66, 'new_value': 151971.66}, {'field': 'instoreCount', 'old_value': 219, 'new_value': 225}]
2025-05-21 08:08:38,240 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-05-21 08:08:38,242 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 123424.02, 'new_value': 137924.62}, {'field': 'dailyBillAmount', 'old_value': 123424.02, 'new_value': 137924.62}, {'field': 'amount', 'old_value': 105073.23, 'new_value': 114961.83}, {'field': 'count', 'old_value': 189, 'new_value': 200}, {'field': 'instoreAmount', 'old_value': 102289.0, 'new_value': 112177.6}, {'field': 'instoreCount', 'old_value': 173, 'new_value': 184}]
2025-05-21 08:08:38,694 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-05-21 08:08:38,694 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 18221.74, 'new_value': 18808.63}, {'field': 'amount', 'old_value': 18221.74, 'new_value': 18808.63}, {'field': 'count', 'old_value': 370, 'new_value': 385}, {'field': 'instoreAmount', 'old_value': 18221.74, 'new_value': 18808.63}, {'field': 'instoreCount', 'old_value': 370, 'new_value': 385}]
2025-05-21 08:08:39,224 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-05-21 08:08:39,225 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 75006.65, 'new_value': 80237.67}, {'field': 'amount', 'old_value': 75006.65, 'new_value': 80237.67}, {'field': 'count', 'old_value': 625, 'new_value': 663}, {'field': 'instoreAmount', 'old_value': 75191.21, 'new_value': 80788.51}, {'field': 'instoreCount', 'old_value': 625, 'new_value': 663}]
2025-05-21 08:08:39,715 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-05-21 08:08:39,715 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 233205.32, 'new_value': 247339.41}, {'field': 'dailyBillAmount', 'old_value': 233205.32, 'new_value': 247339.41}, {'field': 'amount', 'old_value': 252849.16, 'new_value': 266247.47000000003}, {'field': 'count', 'old_value': 6732, 'new_value': 7123}, {'field': 'instoreAmount', 'old_value': 240475.84, 'new_value': 253055.84}, {'field': 'instoreCount', 'old_value': 6109, 'new_value': 6462}, {'field': 'onlineAmount', 'old_value': 16522.51, 'new_value': 17377.82}, {'field': 'onlineCount', 'old_value': 623, 'new_value': 661}]
2025-05-21 08:08:40,218 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC91
2025-05-21 08:08:40,219 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 65707.1, 'new_value': 67143.94}, {'field': 'dailyBillAmount', 'old_value': 65707.1, 'new_value': 67143.94}, {'field': 'amount', 'old_value': 66335.1, 'new_value': 67771.94}, {'field': 'count', 'old_value': 59, 'new_value': 66}, {'field': 'instoreAmount', 'old_value': 66335.1, 'new_value': 67771.94}, {'field': 'instoreCount', 'old_value': 59, 'new_value': 66}]
2025-05-21 08:08:40,651 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-05-21 08:08:40,651 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 615591.13, 'new_value': 662105.58}, {'field': 'dailyBillAmount', 'old_value': 615591.13, 'new_value': 662105.58}, {'field': 'amount', 'old_value': 559815.79, 'new_value': 605451.85}, {'field': 'count', 'old_value': 1448, 'new_value': 1530}, {'field': 'instoreAmount', 'old_value': 582807.55, 'new_value': 628744.5}, {'field': 'instoreCount', 'old_value': 1204, 'new_value': 1273}, {'field': 'onlineAmount', 'old_value': 5459.79, 'new_value': 5736.4}, {'field': 'onlineCount', 'old_value': 244, 'new_value': 257}]
2025-05-21 08:08:41,108 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-05-21 08:08:41,108 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 954178.29, 'new_value': 998476.01}, {'field': 'amount', 'old_value': 954178.29, 'new_value': 998476.01}, {'field': 'count', 'old_value': 3060, 'new_value': 3223}, {'field': 'instoreAmount', 'old_value': 955389.29, 'new_value': 999687.01}, {'field': 'instoreCount', 'old_value': 3060, 'new_value': 3223}]
2025-05-21 08:08:41,571 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-05-21 08:08:41,571 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'amount', 'old_value': 557728.21, 'new_value': 597407.46}, {'field': 'count', 'old_value': 2050, 'new_value': 2184}, {'field': 'instoreAmount', 'old_value': 543470.05, 'new_value': 581653.6}, {'field': 'instoreCount', 'old_value': 1222, 'new_value': 1304}, {'field': 'onlineAmount', 'old_value': 25294.32, 'new_value': 26839.82}, {'field': 'onlineCount', 'old_value': 828, 'new_value': 880}]
2025-05-21 08:08:42,020 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-05-21 08:08:42,020 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1393115.29, 'new_value': 1471468.29}, {'field': 'dailyBillAmount', 'old_value': 1393115.29, 'new_value': 1471468.29}, {'field': 'amount', 'old_value': 1439272.0, 'new_value': 1519227.0}, {'field': 'count', 'old_value': 3991, 'new_value': 4170}, {'field': 'instoreAmount', 'old_value': 1439272.0, 'new_value': 1519227.0}, {'field': 'instoreCount', 'old_value': 3991, 'new_value': 4170}]
2025-05-21 08:08:42,429 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-05-21 08:08:42,429 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 223722.51, 'new_value': 228475.88}, {'field': 'dailyBillAmount', 'old_value': 223722.51, 'new_value': 228475.88}, {'field': 'amount', 'old_value': 220981.38, 'new_value': 234689.66}, {'field': 'count', 'old_value': 1190, 'new_value': 1261}, {'field': 'instoreAmount', 'old_value': 213750.4, 'new_value': 227419.8}, {'field': 'instoreCount', 'old_value': 1001, 'new_value': 1065}, {'field': 'onlineAmount', 'old_value': 11637.86, 'new_value': 12044.74}, {'field': 'onlineCount', 'old_value': 189, 'new_value': 196}]
2025-05-21 08:08:42,912 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-05-21 08:08:42,912 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 730556.53, 'new_value': 768758.8}, {'field': 'dailyBillAmount', 'old_value': 730556.53, 'new_value': 768758.8}, {'field': 'amount', 'old_value': 787578.05, 'new_value': 825849.42}, {'field': 'count', 'old_value': 3229, 'new_value': 3407}, {'field': 'instoreAmount', 'old_value': 787578.5, 'new_value': 825849.87}, {'field': 'instoreCount', 'old_value': 3229, 'new_value': 3407}]
2025-05-21 08:08:43,364 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-05-21 08:08:43,364 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'amount', 'old_value': 515191.2, 'new_value': 567620.8}, {'field': 'count', 'old_value': 864, 'new_value': 933}, {'field': 'instoreAmount', 'old_value': 511225.28, 'new_value': 563654.88}, {'field': 'instoreCount', 'old_value': 833, 'new_value': 902}]
2025-05-21 08:08:43,866 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-05-21 08:08:43,867 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 194630.85, 'new_value': 203679.31}, {'field': 'dailyBillAmount', 'old_value': 194630.85, 'new_value': 203679.31}, {'field': 'amount', 'old_value': 226453.3, 'new_value': 237073.3}, {'field': 'count', 'old_value': 1588, 'new_value': 1672}, {'field': 'instoreAmount', 'old_value': 229798.3, 'new_value': 240718.3}, {'field': 'instoreCount', 'old_value': 1588, 'new_value': 1672}]
2025-05-21 08:08:44,283 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-05-21 08:08:44,283 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 116361.88, 'new_value': 127977.27}, {'field': 'dailyBillAmount', 'old_value': 116361.88, 'new_value': 127977.27}, {'field': 'amount', 'old_value': 91974.95999999999, 'new_value': 101984.95999999999}, {'field': 'count', 'old_value': 601, 'new_value': 669}, {'field': 'instoreAmount', 'old_value': 91376.0, 'new_value': 102011.0}, {'field': 'instoreCount', 'old_value': 556, 'new_value': 623}, {'field': 'onlineAmount', 'old_value': 2136.96, 'new_value': 2210.96}, {'field': 'onlineCount', 'old_value': 45, 'new_value': 46}]
2025-05-21 08:08:44,801 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMI01
2025-05-21 08:08:44,802 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 35432.6, 'new_value': 43647.5}, {'field': 'dailyBillAmount', 'old_value': 35432.6, 'new_value': 43647.5}]
2025-05-21 08:08:45,276 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-05-21 08:08:45,277 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 119347.95, 'new_value': 125475.52}, {'field': 'count', 'old_value': 5693, 'new_value': 6019}, {'field': 'instoreAmount', 'old_value': 64561.96, 'new_value': 67182.1}, {'field': 'instoreCount', 'old_value': 3281, 'new_value': 3428}, {'field': 'onlineAmount', 'old_value': 58114.24, 'new_value': 61759.66}, {'field': 'onlineCount', 'old_value': 2412, 'new_value': 2591}]
2025-05-21 08:08:45,716 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-05-21 08:08:45,716 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 165527.86, 'new_value': 174953.9}, {'field': 'amount', 'old_value': 165521.2, 'new_value': 174946.7}, {'field': 'count', 'old_value': 3070, 'new_value': 3248}, {'field': 'instoreAmount', 'old_value': 155454.86, 'new_value': 161989.45}, {'field': 'instoreCount', 'old_value': 2877, 'new_value': 3012}, {'field': 'onlineAmount', 'old_value': 10073.0, 'new_value': 12964.45}, {'field': 'onlineCount', 'old_value': 193, 'new_value': 236}]
2025-05-21 08:08:46,160 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-05-21 08:08:46,160 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23547.2, 'new_value': 25807.8}, {'field': 'amount', 'old_value': 23547.2, 'new_value': 25807.8}, {'field': 'count', 'old_value': 166, 'new_value': 179}, {'field': 'instoreAmount', 'old_value': 23547.2, 'new_value': 25807.8}, {'field': 'instoreCount', 'old_value': 166, 'new_value': 179}]
2025-05-21 08:08:46,573 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-05-21 08:08:46,574 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34728.2, 'new_value': 40980.2}, {'field': 'dailyBillAmount', 'old_value': 34728.2, 'new_value': 40980.2}, {'field': 'amount', 'old_value': 41472.1, 'new_value': 42342.1}, {'field': 'count', 'old_value': 373, 'new_value': 388}, {'field': 'instoreAmount', 'old_value': 41692.3, 'new_value': 42562.5}, {'field': 'instoreCount', 'old_value': 373, 'new_value': 388}]
2025-05-21 08:08:47,023 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP91
2025-05-21 08:08:47,024 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42721.0, 'new_value': 44784.0}, {'field': 'dailyBillAmount', 'old_value': 42721.0, 'new_value': 44784.0}]
2025-05-21 08:08:47,428 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-05-21 08:08:47,429 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 144820.5, 'new_value': 172483.2}, {'field': 'dailyBillAmount', 'old_value': 144820.5, 'new_value': 172483.2}, {'field': 'amount', 'old_value': 134300.83, 'new_value': 147587.25}, {'field': 'count', 'old_value': 3877, 'new_value': 4069}, {'field': 'instoreAmount', 'old_value': 130482.9, 'new_value': 143884.82}, {'field': 'instoreCount', 'old_value': 3727, 'new_value': 3914}, {'field': 'onlineAmount', 'old_value': 5708.72, 'new_value': 5967.52}, {'field': 'onlineCount', 'old_value': 150, 'new_value': 155}]
2025-05-21 08:08:47,853 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-05-21 08:08:47,854 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41156.2, 'new_value': 45019.2}, {'field': 'dailyBillAmount', 'old_value': 41156.2, 'new_value': 45019.2}, {'field': 'amount', 'old_value': 40961.7, 'new_value': 44964.8}, {'field': 'count', 'old_value': 231, 'new_value': 251}, {'field': 'instoreAmount', 'old_value': 43767.9, 'new_value': 47481.0}, {'field': 'instoreCount', 'old_value': 230, 'new_value': 248}, {'field': 'onlineAmount', 'old_value': 97.9, 'new_value': 387.9}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 3}]
2025-05-21 08:08:48,388 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-05-21 08:08:48,388 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51490.11, 'new_value': 55113.32}, {'field': 'dailyBillAmount', 'old_value': 51490.11, 'new_value': 55113.32}]
2025-05-21 08:08:48,839 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-05-21 08:08:48,839 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 36842.0, 'new_value': 38924.05}, {'field': 'amount', 'old_value': 36841.76, 'new_value': 38923.81}, {'field': 'count', 'old_value': 2123, 'new_value': 2254}, {'field': 'instoreAmount', 'old_value': 37484.24, 'new_value': 39613.47}, {'field': 'instoreCount', 'old_value': 2123, 'new_value': 2254}]
2025-05-21 08:08:49,397 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-05-21 08:08:49,398 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59503.729999999996, 'new_value': 62317.15}, {'field': 'dailyBillAmount', 'old_value': 59503.729999999996, 'new_value': 62317.15}, {'field': 'amount', 'old_value': 61344.22, 'new_value': 64165.96}, {'field': 'count', 'old_value': 2967, 'new_value': 3114}, {'field': 'instoreAmount', 'old_value': 57100.4, 'new_value': 59723.6}, {'field': 'instoreCount', 'old_value': 2788, 'new_value': 2928}, {'field': 'onlineAmount', 'old_value': 4309.07, 'new_value': 4507.61}, {'field': 'onlineCount', 'old_value': 179, 'new_value': 186}]
2025-05-21 08:08:49,807 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-05-21 08:08:49,807 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41660.08, 'new_value': 42998.1}, {'field': 'amount', 'old_value': 41660.08, 'new_value': 42998.1}, {'field': 'count', 'old_value': 2014, 'new_value': 2086}, {'field': 'instoreAmount', 'old_value': 26139.64, 'new_value': 26871.62}, {'field': 'instoreCount', 'old_value': 1335, 'new_value': 1379}, {'field': 'onlineAmount', 'old_value': 15581.44, 'new_value': 16187.48}, {'field': 'onlineCount', 'old_value': 679, 'new_value': 707}]
2025-05-21 08:08:50,212 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-05-21 08:08:50,212 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29397.75, 'new_value': 30571.72}, {'field': 'dailyBillAmount', 'old_value': 29397.75, 'new_value': 30571.72}, {'field': 'amount', 'old_value': 21008.97, 'new_value': 21849.05}, {'field': 'count', 'old_value': 850, 'new_value': 880}, {'field': 'instoreAmount', 'old_value': 21198.37, 'new_value': 22038.45}, {'field': 'instoreCount', 'old_value': 850, 'new_value': 880}]
2025-05-21 08:08:50,635 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-05-21 08:08:50,635 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54682.99, 'new_value': 58028.52}, {'field': 'amount', 'old_value': 54677.35, 'new_value': 58022.01}, {'field': 'count', 'old_value': 3341, 'new_value': 3540}, {'field': 'instoreAmount', 'old_value': 14187.91, 'new_value': 15165.33}, {'field': 'instoreCount', 'old_value': 897, 'new_value': 942}, {'field': 'onlineAmount', 'old_value': 41652.46, 'new_value': 44304.17}, {'field': 'onlineCount', 'old_value': 2444, 'new_value': 2598}]
2025-05-21 08:08:51,041 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-05-21 08:08:51,041 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 100359.05, 'new_value': 105411.95}, {'field': 'dailyBillAmount', 'old_value': 100359.05, 'new_value': 105411.95}, {'field': 'amount', 'old_value': 83797.34, 'new_value': 88014.54}, {'field': 'count', 'old_value': 824, 'new_value': 861}, {'field': 'instoreAmount', 'old_value': 83797.34, 'new_value': 88014.54}, {'field': 'instoreCount', 'old_value': 824, 'new_value': 861}]
2025-05-21 08:08:51,499 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-05-21 08:08:51,499 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81481.49, 'new_value': 85146.05}, {'field': 'dailyBillAmount', 'old_value': 81481.49, 'new_value': 85146.05}, {'field': 'amount', 'old_value': 90705.8, 'new_value': 95043.8}, {'field': 'count', 'old_value': 392, 'new_value': 412}, {'field': 'instoreAmount', 'old_value': 90705.8, 'new_value': 95043.8}, {'field': 'instoreCount', 'old_value': 392, 'new_value': 412}]
2025-05-21 08:08:52,018 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-05-21 08:08:52,019 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53201.7, 'new_value': 55644.7}, {'field': 'dailyBillAmount', 'old_value': 53201.7, 'new_value': 55644.7}, {'field': 'amount', 'old_value': 45599.65, 'new_value': 47721.65}, {'field': 'count', 'old_value': 241, 'new_value': 255}, {'field': 'instoreAmount', 'old_value': 47036.65, 'new_value': 49158.65}, {'field': 'instoreCount', 'old_value': 241, 'new_value': 255}]
2025-05-21 08:08:52,509 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-05-21 08:08:52,509 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 102093.0, 'new_value': 106374.0}, {'field': 'amount', 'old_value': 102093.0, 'new_value': 106374.0}, {'field': 'count', 'old_value': 1053, 'new_value': 1098}, {'field': 'instoreAmount', 'old_value': 102093.0, 'new_value': 106374.0}, {'field': 'instoreCount', 'old_value': 1053, 'new_value': 1098}]
2025-05-21 08:08:52,956 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-05-21 08:08:52,957 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22551.52, 'new_value': 25127.95}, {'field': 'dailyBillAmount', 'old_value': 22551.52, 'new_value': 25127.95}, {'field': 'amount', 'old_value': 2452.28, 'new_value': 3880.04}, {'field': 'count', 'old_value': 134, 'new_value': 166}, {'field': 'instoreAmount', 'old_value': 2880.2599999999998, 'new_value': 4311.5199999999995}, {'field': 'instoreCount', 'old_value': 134, 'new_value': 166}]
2025-05-21 08:08:53,355 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMZ7
2025-05-21 08:08:53,356 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16HSQFKC90AM6QNN0HOT12RK0013M7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17942.16, 'new_value': 18467.31}, {'field': 'dailyBillAmount', 'old_value': 17942.16, 'new_value': 18467.31}, {'field': 'amount', 'old_value': 18606.94, 'new_value': 19132.09}, {'field': 'count', 'old_value': 495, 'new_value': 517}, {'field': 'instoreAmount', 'old_value': 18620.3, 'new_value': 19145.45}, {'field': 'instoreCount', 'old_value': 493, 'new_value': 515}]
2025-05-21 08:08:53,791 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-05-21 08:08:53,791 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'amount', 'old_value': 53119.6, 'new_value': 55528.6}, {'field': 'count', 'old_value': 210, 'new_value': 222}, {'field': 'instoreAmount', 'old_value': 53308.6, 'new_value': 55717.6}, {'field': 'instoreCount', 'old_value': 209, 'new_value': 221}]
2025-05-21 08:08:54,261 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-05-21 08:08:54,261 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'amount', 'old_value': 34369.0, 'new_value': 38571.0}, {'field': 'count', 'old_value': 180, 'new_value': 208}, {'field': 'instoreAmount', 'old_value': 34383.0, 'new_value': 38585.0}, {'field': 'instoreCount', 'old_value': 180, 'new_value': 208}]
2025-05-21 08:08:54,734 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-05-21 08:08:54,734 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59605.25, 'new_value': 61646.38}, {'field': 'dailyBillAmount', 'old_value': 59605.25, 'new_value': 61646.38}, {'field': 'amount', 'old_value': 52866.35, 'new_value': 54669.25}, {'field': 'count', 'old_value': 1775, 'new_value': 1834}, {'field': 'instoreAmount', 'old_value': 48349.19, 'new_value': 49907.09}, {'field': 'instoreCount', 'old_value': 1567, 'new_value': 1613}, {'field': 'onlineAmount', 'old_value': 4553.6, 'new_value': 4798.6}, {'field': 'onlineCount', 'old_value': 208, 'new_value': 221}]
2025-05-21 08:08:55,166 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-05-21 08:08:55,167 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 25609.74, 'new_value': 28747.3}, {'field': 'dailyBillAmount', 'old_value': 25609.74, 'new_value': 28747.3}, {'field': 'amount', 'old_value': 29318.31, 'new_value': 32547.33}, {'field': 'count', 'old_value': 175, 'new_value': 204}, {'field': 'instoreAmount', 'old_value': 29290.11, 'new_value': 32093.11}, {'field': 'instoreCount', 'old_value': 169, 'new_value': 183}, {'field': 'onlineAmount', 'old_value': 178.8, 'new_value': 604.8199999999999}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 21}]
2025-05-21 08:08:55,604 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-05-21 08:08:55,605 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 154672.1, 'new_value': 162841.48}, {'field': 'dailyBillAmount', 'old_value': 154672.1, 'new_value': 162841.48}, {'field': 'amount', 'old_value': 161771.6, 'new_value': 169635.6}, {'field': 'count', 'old_value': 1112, 'new_value': 1183}, {'field': 'instoreAmount', 'old_value': 155131.7, 'new_value': 163718.7}, {'field': 'instoreCount', 'old_value': 990, 'new_value': 1058}, {'field': 'onlineAmount', 'old_value': 8012.9, 'new_value': 8285.9}, {'field': 'onlineCount', 'old_value': 122, 'new_value': 125}]
2025-05-21 08:08:55,605 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-21 08:08:55,606 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-21 08:08:55,606 - INFO - 正在批量插入月度数据，批次 1/1，共 2 条记录
2025-05-21 08:08:55,795 - INFO - 批量插入月度数据成功，批次 1，2 条记录
2025-05-21 08:08:58,796 - INFO - 批量插入月度数据完成: 总计 2 条，成功 2 条，失败 0 条
2025-05-21 08:08:58,796 - INFO - 批量插入月销售数据完成，共 2 条记录
2025-05-21 08:08:58,797 - INFO - 月销售数据同步完成！更新: 212 条，插入: 2 条，错误: 0 条，跳过: 976 条
2025-05-21 08:08:58,797 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-6 至 2025-5
2025-05-21 08:08:59,348 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250521.xlsx
2025-05-21 08:08:59,349 - INFO - 综合数据同步流程完成！
2025-05-21 08:08:59,408 - INFO - 综合数据同步完成
2025-05-21 08:08:59,408 - INFO - ==================================================
2025-05-21 08:08:59,409 - INFO - 程序退出
2025-05-21 08:08:59,409 - INFO - ==================================================
