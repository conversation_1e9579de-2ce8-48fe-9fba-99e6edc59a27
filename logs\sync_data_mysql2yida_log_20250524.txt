2025-05-24 00:30:34,041 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 00:30:34,041 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 00:30:34,041 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 00:30:34,103 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 0 条记录
2025-05-24 00:30:34,103 - ERROR - 未获取到MySQL数据
2025-05-24 00:31:34,122 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 00:31:34,122 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 00:31:34,122 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 00:31:34,169 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 32 条记录
2025-05-24 00:31:34,169 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 00:31:34,169 - INFO - 开始处理日期: 2025-05-23
2025-05-24 00:31:34,185 - INFO - Request Parameters - Page 1:
2025-05-24 00:31:34,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 00:31:34,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 00:31:42,279 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 660B7ADE-28B8-7249-8ECD-FE4EBABFFFEB Response: {'code': 'ServiceUnavailable', 'requestid': '660B7ADE-28B8-7249-8ECD-FE4EBABFFFEB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 660B7ADE-28B8-7249-8ECD-FE4EBABFFFEB)
2025-05-24 00:31:42,279 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 00:31:42,279 - INFO - 同步完成
2025-05-24 01:30:34,178 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 01:30:34,178 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 01:30:34,178 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 01:30:34,241 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 1 条记录
2025-05-24 01:30:34,241 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 01:30:34,241 - INFO - 开始处理日期: 2025-05-23
2025-05-24 01:30:34,241 - INFO - Request Parameters - Page 1:
2025-05-24 01:30:34,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 01:30:34,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 01:30:42,382 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 32E5101B-C792-72B8-859F-EB55F57EA0BD Response: {'code': 'ServiceUnavailable', 'requestid': '32E5101B-C792-72B8-859F-EB55F57EA0BD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 32E5101B-C792-72B8-859F-EB55F57EA0BD)
2025-05-24 01:30:42,382 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 01:31:42,401 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 01:31:42,401 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 01:31:42,401 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 01:31:42,463 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 33 条记录
2025-05-24 01:31:42,463 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 01:31:42,463 - INFO - 开始处理日期: 2025-05-23
2025-05-24 01:31:42,463 - INFO - Request Parameters - Page 1:
2025-05-24 01:31:42,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 01:31:42,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 01:31:43,198 - INFO - Response - Page 1:
2025-05-24 01:31:43,213 - INFO - 第 1 页获取到 31 条记录
2025-05-24 01:31:43,416 - INFO - 查询完成，共获取到 31 条记录
2025-05-24 01:31:43,416 - INFO - 获取到 31 条表单数据
2025-05-24 01:31:43,416 - INFO - 当前日期 2025-05-23 有 33 条MySQL数据需要处理
2025-05-24 01:31:43,416 - INFO - 开始批量插入 2 条新记录
2025-05-24 01:31:43,573 - INFO - 批量插入响应状态码: 200
2025-05-24 01:31:43,573 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 17:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '159B6361-A75C-794D-95FF-4F2270093FDB', 'x-acs-trace-id': '58622a6f5b418867f0bd8d36ce7a5848', 'etag': '1xo+rRYJvZYLUihXf9SFYmA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 01:31:43,573 - INFO - 批量插入响应体: {'result': ['FINST-WBF66B813ENV3D3E6PVXK6VAJD722WYBV21BMSO', 'FINST-WBF66B813ENV3D3E6PVXK6VAJD722WYBV21BMTO']}
2025-05-24 01:31:43,573 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-05-24 01:31:43,573 - INFO - 成功插入的数据ID: ['FINST-WBF66B813ENV3D3E6PVXK6VAJD722WYBV21BMSO', 'FINST-WBF66B813ENV3D3E6PVXK6VAJD722WYBV21BMTO']
2025-05-24 01:31:48,589 - INFO - 批量插入完成，共 2 条记录
2025-05-24 01:31:48,589 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-05-24 01:31:48,589 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-05-24 01:31:48,589 - INFO - 同步完成
2025-05-24 02:30:34,222 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 02:30:34,222 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 02:30:34,222 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 02:30:34,285 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 1 条记录
2025-05-24 02:30:34,285 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 02:30:34,285 - INFO - 开始处理日期: 2025-05-23
2025-05-24 02:30:34,285 - INFO - Request Parameters - Page 1:
2025-05-24 02:30:34,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 02:30:34,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 02:30:42,410 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 07B7614C-6276-756A-997E-A92CA1F7F711 Response: {'code': 'ServiceUnavailable', 'requestid': '07B7614C-6276-756A-997E-A92CA1F7F711', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 07B7614C-6276-756A-997E-A92CA1F7F711)
2025-05-24 02:30:42,410 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 02:31:42,429 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 02:31:42,429 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 02:31:42,429 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 02:31:42,492 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 33 条记录
2025-05-24 02:31:42,492 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 02:31:42,492 - INFO - 开始处理日期: 2025-05-23
2025-05-24 02:31:42,492 - INFO - Request Parameters - Page 1:
2025-05-24 02:31:42,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 02:31:42,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 02:31:50,601 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 52B38EE8-BA55-72C5-A9E8-D693B43F5636 Response: {'code': 'ServiceUnavailable', 'requestid': '52B38EE8-BA55-72C5-A9E8-D693B43F5636', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 52B38EE8-BA55-72C5-A9E8-D693B43F5636)
2025-05-24 02:31:50,601 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 02:31:50,601 - INFO - 同步完成
2025-05-24 03:30:33,875 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 03:30:33,875 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 03:30:33,875 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 03:30:33,953 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 1 条记录
2025-05-24 03:30:33,953 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 03:30:33,953 - INFO - 开始处理日期: 2025-05-23
2025-05-24 03:30:33,953 - INFO - Request Parameters - Page 1:
2025-05-24 03:30:33,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:30:33,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:30:42,079 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 58AA0772-59CF-7CE1-9300-DF2C9884C368 Response: {'code': 'ServiceUnavailable', 'requestid': '58AA0772-59CF-7CE1-9300-DF2C9884C368', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 58AA0772-59CF-7CE1-9300-DF2C9884C368)
2025-05-24 03:30:42,079 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 03:31:42,098 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 03:31:42,098 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 03:31:42,098 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 03:31:42,160 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 33 条记录
2025-05-24 03:31:42,160 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 03:31:42,160 - INFO - 开始处理日期: 2025-05-23
2025-05-24 03:31:42,160 - INFO - Request Parameters - Page 1:
2025-05-24 03:31:42,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 03:31:42,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 03:31:50,255 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B5F84E09-32D5-7289-8BD9-7CFE2DEE7535 Response: {'code': 'ServiceUnavailable', 'requestid': 'B5F84E09-32D5-7289-8BD9-7CFE2DEE7535', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B5F84E09-32D5-7289-8BD9-7CFE2DEE7535)
2025-05-24 03:31:50,255 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 03:31:50,255 - INFO - 同步完成
2025-05-24 04:30:34,143 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 04:30:34,143 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 04:30:34,143 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 04:30:34,206 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 3 条记录
2025-05-24 04:30:34,206 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 04:30:34,206 - INFO - 开始处理日期: 2025-05-23
2025-05-24 04:30:34,206 - INFO - Request Parameters - Page 1:
2025-05-24 04:30:34,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 04:30:34,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 04:30:42,347 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 77E22450-F22C-7553-AE39-9396E4CACAC9 Response: {'code': 'ServiceUnavailable', 'requestid': '77E22450-F22C-7553-AE39-9396E4CACAC9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 77E22450-F22C-7553-AE39-9396E4CACAC9)
2025-05-24 04:30:42,347 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 04:31:42,365 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 04:31:42,365 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 04:31:42,365 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 04:31:42,428 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 35 条记录
2025-05-24 04:31:42,428 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 04:31:42,428 - INFO - 开始处理日期: 2025-05-23
2025-05-24 04:31:42,428 - INFO - Request Parameters - Page 1:
2025-05-24 04:31:42,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 04:31:42,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 04:31:43,084 - INFO - Response - Page 1:
2025-05-24 04:31:43,084 - INFO - 第 1 页获取到 33 条记录
2025-05-24 04:31:43,287 - INFO - 查询完成，共获取到 33 条记录
2025-05-24 04:31:43,287 - INFO - 获取到 33 条表单数据
2025-05-24 04:31:43,287 - INFO - 当前日期 2025-05-23 有 35 条MySQL数据需要处理
2025-05-24 04:31:43,287 - INFO - 开始批量插入 2 条新记录
2025-05-24 04:31:43,428 - INFO - 批量插入响应状态码: 200
2025-05-24 04:31:43,428 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 20:31:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '106', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0101A322-299D-7158-988F-6E90E40CC7E7', 'x-acs-trace-id': 'e42af578e9eecbd652f569df237da3ba', 'etag': '1aDF76/wCUkUKgihPXjpvZw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 04:31:43,428 - INFO - 批量插入响应体: {'result': ['FINST-AJF66F719NOV11AEDWUBF403X2UZ10SSA91BMC', 'FINST-AJF66F719NOV11AEDWUBF403X2UZ10SSA91BMD']}
2025-05-24 04:31:43,428 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-05-24 04:31:43,428 - INFO - 成功插入的数据ID: ['FINST-AJF66F719NOV11AEDWUBF403X2UZ10SSA91BMC', 'FINST-AJF66F719NOV11AEDWUBF403X2UZ10SSA91BMD']
2025-05-24 04:31:48,443 - INFO - 批量插入完成，共 2 条记录
2025-05-24 04:31:48,443 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-05-24 04:31:48,443 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-05-24 04:31:48,443 - INFO - 同步完成
2025-05-24 05:30:34,186 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 05:30:34,186 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 05:30:34,186 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 05:30:34,248 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 3 条记录
2025-05-24 05:30:34,248 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 05:30:34,248 - INFO - 开始处理日期: 2025-05-23
2025-05-24 05:30:34,248 - INFO - Request Parameters - Page 1:
2025-05-24 05:30:34,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 05:30:34,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 05:30:42,360 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2F9412A9-B8EF-74FC-9B6C-4F03EC434DB9 Response: {'code': 'ServiceUnavailable', 'requestid': '2F9412A9-B8EF-74FC-9B6C-4F03EC434DB9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2F9412A9-B8EF-74FC-9B6C-4F03EC434DB9)
2025-05-24 05:30:42,360 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 05:31:33,714 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 05:31:33,714 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 05:31:33,714 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 05:31:33,777 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 35 条记录
2025-05-24 05:31:33,777 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 05:31:33,777 - INFO - 开始处理日期: 2025-05-23
2025-05-24 05:31:33,777 - INFO - Request Parameters - Page 1:
2025-05-24 05:31:33,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 05:31:33,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 05:31:41,871 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5305B7FF-9A35-7B28-9C82-CF7B9D501AA7 Response: {'code': 'ServiceUnavailable', 'requestid': '5305B7FF-9A35-7B28-9C82-CF7B9D501AA7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5305B7FF-9A35-7B28-9C82-CF7B9D501AA7)
2025-05-24 05:31:41,871 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 05:31:41,871 - INFO - 同步完成
2025-05-24 06:30:33,957 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 06:30:33,957 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 06:30:33,957 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 06:30:34,019 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 3 条记录
2025-05-24 06:30:34,019 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 06:30:34,019 - INFO - 开始处理日期: 2025-05-23
2025-05-24 06:30:34,035 - INFO - Request Parameters - Page 1:
2025-05-24 06:30:34,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:30:34,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:30:42,144 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8623D968-E302-794E-88D2-A90CB8B7FD36 Response: {'code': 'ServiceUnavailable', 'requestid': '8623D968-E302-794E-88D2-A90CB8B7FD36', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8623D968-E302-794E-88D2-A90CB8B7FD36)
2025-05-24 06:30:42,144 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 06:31:42,159 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 06:31:42,159 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 06:31:42,159 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 06:31:42,222 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 35 条记录
2025-05-24 06:31:42,222 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 06:31:42,222 - INFO - 开始处理日期: 2025-05-23
2025-05-24 06:31:42,222 - INFO - Request Parameters - Page 1:
2025-05-24 06:31:42,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 06:31:42,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 06:31:50,331 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 500CBB0F-4CCD-7C64-B073-E3E42A821E2D Response: {'code': 'ServiceUnavailable', 'requestid': '500CBB0F-4CCD-7C64-B073-E3E42A821E2D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 500CBB0F-4CCD-7C64-B073-E3E42A821E2D)
2025-05-24 06:31:50,331 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 06:31:50,331 - INFO - 同步完成
2025-05-24 07:30:33,933 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 07:30:33,933 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 07:30:33,933 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 07:30:33,996 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 6 条记录
2025-05-24 07:30:33,996 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 07:30:33,996 - INFO - 开始处理日期: 2025-05-23
2025-05-24 07:30:33,996 - INFO - Request Parameters - Page 1:
2025-05-24 07:30:33,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 07:30:33,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 07:30:42,121 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 19A4238B-C5E3-775C-922F-D577EECCBF88 Response: {'code': 'ServiceUnavailable', 'requestid': '19A4238B-C5E3-775C-922F-D577EECCBF88', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 19A4238B-C5E3-775C-922F-D577EECCBF88)
2025-05-24 07:30:42,121 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 07:31:42,136 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 07:31:42,136 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 07:31:42,136 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 07:31:42,198 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 67 条记录
2025-05-24 07:31:42,198 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 07:31:42,198 - INFO - 开始处理日期: 2025-05-23
2025-05-24 07:31:42,198 - INFO - Request Parameters - Page 1:
2025-05-24 07:31:42,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 07:31:42,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 07:31:42,917 - INFO - Response - Page 1:
2025-05-24 07:31:42,917 - INFO - 第 1 页获取到 35 条记录
2025-05-24 07:31:43,120 - INFO - 查询完成，共获取到 35 条记录
2025-05-24 07:31:43,120 - INFO - 获取到 35 条表单数据
2025-05-24 07:31:43,120 - INFO - 当前日期 2025-05-23 有 67 条MySQL数据需要处理
2025-05-24 07:31:43,120 - INFO - 开始批量插入 32 条新记录
2025-05-24 07:31:43,355 - INFO - 批量插入响应状态码: 200
2025-05-24 07:31:43,355 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 23:31:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1548', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '39830FAD-9731-7893-870F-9E5251A8994A', 'x-acs-trace-id': '3ed6191e0cfe32cc87cc3938a7405085', 'etag': '1znlGhLgtbDed1L9vHIYQTA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 07:31:43,355 - INFO - 批量插入响应体: {'result': ['FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BM7L', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BM8L', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BM9L', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMAL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMBL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMCL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMDL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMEL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMFL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMGL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMHL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMIL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMJL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMKL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMLL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMML', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMNL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMOL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMPL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMQL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMRL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMSL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMTL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMUL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMVL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMWL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMXL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMYL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMZL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BM0M', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BM1M', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BM2M']}
2025-05-24 07:31:43,355 - INFO - 批量插入表单数据成功，批次 1，共 32 条记录
2025-05-24 07:31:43,355 - INFO - 成功插入的数据ID: ['FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BM7L', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BM8L', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BM9L', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMAL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMBL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMCL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMDL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMEL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMFL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMGL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMHL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMIL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMJL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMKL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMLL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMML', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMNL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMOL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMPL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMQL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMRL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMSL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMTL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMUL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMVL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMWL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMXL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMYL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMZL', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BM0M', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BM1M', 'FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BM2M']
2025-05-24 07:31:48,370 - INFO - 批量插入完成，共 32 条记录
2025-05-24 07:31:48,370 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 32 条，错误: 0 条
2025-05-24 07:31:48,370 - INFO - 数据同步完成！更新: 0 条，插入: 32 条，错误: 0 条
2025-05-24 07:31:48,370 - INFO - 同步完成
2025-05-24 08:30:34,254 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 08:30:34,254 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 08:30:34,254 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 08:30:34,316 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 10 条记录
2025-05-24 08:30:34,316 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 08:30:34,316 - INFO - 开始处理日期: 2025-05-23
2025-05-24 08:30:34,316 - INFO - Request Parameters - Page 1:
2025-05-24 08:30:34,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:30:34,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:30:42,441 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7EE64093-DA69-7A30-90E6-C6C0DB0E7432 Response: {'code': 'ServiceUnavailable', 'requestid': '7EE64093-DA69-7A30-90E6-C6C0DB0E7432', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7EE64093-DA69-7A30-90E6-C6C0DB0E7432)
2025-05-24 08:30:42,441 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 08:31:42,456 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 08:31:42,456 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 08:31:42,456 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 08:31:42,519 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 78 条记录
2025-05-24 08:31:42,519 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 08:31:42,519 - INFO - 开始处理日期: 2025-05-23
2025-05-24 08:31:42,519 - INFO - Request Parameters - Page 1:
2025-05-24 08:31:42,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 08:31:42,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 08:31:50,628 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B62CD71E-6A04-7375-8DE5-050A4B96A200 Response: {'code': 'ServiceUnavailable', 'requestid': 'B62CD71E-6A04-7375-8DE5-050A4B96A200', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B62CD71E-6A04-7375-8DE5-050A4B96A200)
2025-05-24 08:31:50,628 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 08:31:50,628 - INFO - 同步完成
2025-05-24 09:30:34,246 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 09:30:34,246 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 09:30:34,246 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 09:30:34,309 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 82 条记录
2025-05-24 09:30:34,309 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 09:30:34,309 - INFO - 开始处理日期: 2025-05-23
2025-05-24 09:30:34,324 - INFO - Request Parameters - Page 1:
2025-05-24 09:30:34,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:30:34,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:30:42,434 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 204B937C-825F-7EB2-A876-A1D330D22A0A Response: {'code': 'ServiceUnavailable', 'requestid': '204B937C-825F-7EB2-A876-A1D330D22A0A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 204B937C-825F-7EB2-A876-A1D330D22A0A)
2025-05-24 09:30:42,434 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 09:31:42,449 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 09:31:42,449 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 09:31:42,449 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 09:31:42,511 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 335 条记录
2025-05-24 09:31:42,511 - INFO - 获取到 1 个日期需要处理: ['2025-05-23']
2025-05-24 09:31:42,511 - INFO - 开始处理日期: 2025-05-23
2025-05-24 09:31:42,511 - INFO - Request Parameters - Page 1:
2025-05-24 09:31:42,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 09:31:42,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 09:31:50,636 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3A2F9119-13DD-70BA-9DDC-B1832F2EE03F Response: {'code': 'ServiceUnavailable', 'requestid': '3A2F9119-13DD-70BA-9DDC-B1832F2EE03F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3A2F9119-13DD-70BA-9DDC-B1832F2EE03F)
2025-05-24 09:31:50,636 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 09:31:50,636 - INFO - 同步完成
2025-05-24 10:30:33,754 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 10:30:33,754 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 10:30:33,754 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 10:30:33,817 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 105 条记录
2025-05-24 10:30:33,817 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 10:30:33,817 - INFO - 开始处理日期: 2025-05-23
2025-05-24 10:30:33,817 - INFO - Request Parameters - Page 1:
2025-05-24 10:30:33,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 10:30:33,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 10:30:41,957 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7F0124FC-A8B6-7146-A7EC-F7C2A02D3F4B Response: {'code': 'ServiceUnavailable', 'requestid': '7F0124FC-A8B6-7146-A7EC-F7C2A02D3F4B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7F0124FC-A8B6-7146-A7EC-F7C2A02D3F4B)
2025-05-24 10:30:41,957 - INFO - 开始处理日期: 2025-05-24
2025-05-24 10:30:41,957 - INFO - Request Parameters - Page 1:
2025-05-24 10:30:41,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 10:30:41,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 10:30:42,113 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: ********-0C2E-7C00-A513-EC7E88E99D18 Response: {'requestid': '********-0C2E-7C00-A513-EC7E88E99D18', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: ********-0C2E-7C00-A513-EC7E88E99D18)
2025-05-24 10:30:42,113 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-24 10:31:42,129 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 10:31:42,129 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 10:31:42,129 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 10:31:42,191 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 395 条记录
2025-05-24 10:31:42,191 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 10:31:42,207 - INFO - 开始处理日期: 2025-05-23
2025-05-24 10:31:42,207 - INFO - Request Parameters - Page 1:
2025-05-24 10:31:42,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 10:31:42,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 10:31:50,316 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 06C6701C-EE49-7046-AF23-1D86E54FDAAC Response: {'code': 'ServiceUnavailable', 'requestid': '06C6701C-EE49-7046-AF23-1D86E54FDAAC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 06C6701C-EE49-7046-AF23-1D86E54FDAAC)
2025-05-24 10:31:50,316 - INFO - 开始处理日期: 2025-05-24
2025-05-24 10:31:50,316 - INFO - Request Parameters - Page 1:
2025-05-24 10:31:50,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 10:31:50,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 10:31:50,441 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 7A2D1355-FE42-7EA9-8CFF-EEFDE8F679D4 Response: {'requestid': '7A2D1355-FE42-7EA9-8CFF-EEFDE8F679D4', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 7A2D1355-FE42-7EA9-8CFF-EEFDE8F679D4)
2025-05-24 10:31:50,441 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-24 10:31:50,441 - INFO - 同步完成
2025-05-24 11:30:33,965 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 11:30:33,965 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 11:30:33,965 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 11:30:34,043 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 123 条记录
2025-05-24 11:30:34,043 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 11:30:34,043 - INFO - 开始处理日期: 2025-05-23
2025-05-24 11:30:34,043 - INFO - Request Parameters - Page 1:
2025-05-24 11:30:34,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 11:30:34,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 11:30:42,153 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C6A9845F-6DDF-7A90-9413-F6718C621DB0 Response: {'code': 'ServiceUnavailable', 'requestid': 'C6A9845F-6DDF-7A90-9413-F6718C621DB0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C6A9845F-6DDF-7A90-9413-F6718C621DB0)
2025-05-24 11:30:42,153 - INFO - 开始处理日期: 2025-05-24
2025-05-24 11:30:42,153 - INFO - Request Parameters - Page 1:
2025-05-24 11:30:42,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 11:30:42,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 11:30:42,309 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 15BDF4C8-9560-7A84-B974-93D6A7211960 Response: {'requestid': '15BDF4C8-9560-7A84-B974-93D6A7211960', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 15BDF4C8-9560-7A84-B974-93D6A7211960)
2025-05-24 11:30:42,309 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-24 11:31:42,324 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 11:31:42,324 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 11:31:42,324 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 11:31:42,402 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 469 条记录
2025-05-24 11:31:42,402 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 11:31:42,402 - INFO - 开始处理日期: 2025-05-23
2025-05-24 11:31:42,402 - INFO - Request Parameters - Page 1:
2025-05-24 11:31:42,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 11:31:42,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 11:31:50,511 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 638DD98F-403F-7F16-AA4B-26AF86FEA690 Response: {'code': 'ServiceUnavailable', 'requestid': '638DD98F-403F-7F16-AA4B-26AF86FEA690', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 638DD98F-403F-7F16-AA4B-26AF86FEA690)
2025-05-24 11:31:50,511 - INFO - 开始处理日期: 2025-05-24
2025-05-24 11:31:50,511 - INFO - Request Parameters - Page 1:
2025-05-24 11:31:50,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 11:31:50,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 11:31:50,652 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 290068B0-69A4-79E2-A496-A20EF979D991 Response: {'requestid': '290068B0-69A4-79E2-A496-A20EF979D991', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 290068B0-69A4-79E2-A496-A20EF979D991)
2025-05-24 11:31:50,652 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-24 11:31:50,652 - INFO - 同步完成
2025-05-24 12:30:33,754 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 12:30:33,754 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 12:30:33,754 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 12:30:33,817 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 130 条记录
2025-05-24 12:30:33,817 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 12:30:33,817 - INFO - 开始处理日期: 2025-05-23
2025-05-24 12:30:33,817 - INFO - Request Parameters - Page 1:
2025-05-24 12:30:33,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:30:33,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:30:41,926 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 415BB025-5BB0-7D42-BFA6-25D9DAECFFFF Response: {'code': 'ServiceUnavailable', 'requestid': '415BB025-5BB0-7D42-BFA6-25D9DAECFFFF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 415BB025-5BB0-7D42-BFA6-25D9DAECFFFF)
2025-05-24 12:30:41,926 - INFO - 开始处理日期: 2025-05-24
2025-05-24 12:30:41,926 - INFO - Request Parameters - Page 1:
2025-05-24 12:30:41,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:30:41,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:30:42,067 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 13C4B015-409E-76BC-B10B-7FA0F137AB65 Response: {'requestid': '13C4B015-409E-76BC-B10B-7FA0F137AB65', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 13C4B015-409E-76BC-B10B-7FA0F137AB65)
2025-05-24 12:30:42,067 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-24 12:31:42,082 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 12:31:42,082 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 12:31:42,082 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 12:31:42,160 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 475 条记录
2025-05-24 12:31:42,160 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 12:31:42,160 - INFO - 开始处理日期: 2025-05-23
2025-05-24 12:31:42,160 - INFO - Request Parameters - Page 1:
2025-05-24 12:31:42,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:31:42,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:31:42,972 - INFO - Response - Page 1:
2025-05-24 12:31:42,972 - INFO - 第 1 页获取到 67 条记录
2025-05-24 12:31:43,176 - INFO - 查询完成，共获取到 67 条记录
2025-05-24 12:31:43,176 - INFO - 获取到 67 条表单数据
2025-05-24 12:31:43,176 - INFO - 当前日期 2025-05-23 有 474 条MySQL数据需要处理
2025-05-24 12:31:43,176 - INFO - 开始更新记录 - 表单实例ID: FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMSL
2025-05-24 12:31:43,613 - INFO - 更新表单数据成功: FINST-R1A66H9169MVJBRF7EA7VDMCQUT12PMFQF1BMSL
2025-05-24 12:31:43,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13257.0, 'new_value': 13357.0}, {'field': 'total_amount', 'old_value': 13257.0, 'new_value': 13357.0}]
2025-05-24 12:31:43,613 - INFO - 开始批量插入 407 条新记录
2025-05-24 12:31:43,926 - INFO - 批量插入响应状态码: 200
2025-05-24 12:31:43,926 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 04:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CC155C2F-70B6-7330-AD54-8C9ABA25EDD3', 'x-acs-trace-id': '50abfbbf66d135f00b4f4e377aa56253', 'etag': '4z8NCBIr3VeXnNWu9J0iJ2A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 12:31:43,926 - INFO - 批量插入响应体: {'result': ['FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BML3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMM3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMN3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMO3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMP3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMQ3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMR3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMS3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMT3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMU3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMV3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMW3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMX3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMY3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMZ3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM04', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM14', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM24', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM34', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM44', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM54', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM64', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM74', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM84', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM94', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMA4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMB4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMC4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMD4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BME4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMF4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMG4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMH4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMI4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMJ4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMK4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BML4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMM4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMN4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMO4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMP4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMQ4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMR4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMS4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMT4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMU4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMV4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMW4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMX4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMY4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMZ4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM05', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM15', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM25', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM35', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM45', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM55', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM65', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM75', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM85', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM95', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMA5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMB5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMC5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMD5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BME5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMF5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMG5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMH5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMI5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMJ5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMK5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BML5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMM5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMN5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMO5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMP5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMQ5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMR5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMS5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMT5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMU5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMV5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMW5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMX5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMY5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMZ5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM06', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM16', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM26', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM36', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM46', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM56', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM66', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM76', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM86', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM96', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMA6', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMB6', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMC6']}
2025-05-24 12:31:43,926 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-24 12:31:43,926 - INFO - 成功插入的数据ID: ['FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BML3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMM3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMN3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMO3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMP3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMQ3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMR3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMS3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMT3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMU3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMV3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMW3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMX3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMY3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMZ3', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM04', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM14', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM24', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM34', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM44', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM54', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM64', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM74', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM84', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BM94', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMA4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMB4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMC4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMD4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BME4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMF4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMG4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMH4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMI4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMJ4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMK4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BML4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMM4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMN4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMO4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMP4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3L19GQ1BMQ4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMR4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMS4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMT4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMU4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMV4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMW4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMX4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMY4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMZ4', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM05', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM15', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM25', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM35', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM45', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM55', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM65', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM75', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM85', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM95', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMA5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMB5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMC5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMD5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BME5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMF5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMG5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMH5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMI5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMJ5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMK5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BML5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMM5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMN5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMO5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMP5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMQ5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMR5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMS5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMT5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMU5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMV5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMW5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMX5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMY5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMZ5', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM06', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM16', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM26', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM36', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM46', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM56', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM66', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM76', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM86', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BM96', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMA6', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMB6', 'FINST-LLF66O71KEOVVZCHAZQGEAORLQ7X3M19GQ1BMC6']
2025-05-24 12:31:49,207 - INFO - 批量插入响应状态码: 200
2025-05-24 12:31:49,207 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 04:31:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '452ABCCB-CEAB-7B6B-9659-37661DCD6661', 'x-acs-trace-id': '1811b6d6fc3f8c7403abc449a3f3075c', 'etag': '4IRJaS/kdeYrQRBHdLrc2XQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 12:31:49,207 - INFO - 批量插入响应体: {'result': ['FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMIO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMJO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMKO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMLO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMMO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMNO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMOO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMPO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMQO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMRO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMSO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMTO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMUO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMVO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMWO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMXO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMYO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMZO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM0P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM1P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM2P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM3P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM4P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM5P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM6P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM7P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM8P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM9P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMAP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMBP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMCP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMDP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMEP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMFP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMGP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMHP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMIP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMJP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMKP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMLP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMMP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMNP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMOP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMPP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMQP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMRP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMSP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMTP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMUP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMVP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMWP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMXP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMYP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMZP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM0Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM1Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM2Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM3Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM4Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM5Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM6Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM7Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM8Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM9Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMAQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMBQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMCQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMDQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMEQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMFQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMGQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMHQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMIQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMJQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMKQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMLQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMMQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMNQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMOQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMPQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMQQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMRQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMSQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMTQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMUQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMVQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMWQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMXQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMYQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMZQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM0R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM1R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM2R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM3R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM4R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM5R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM6R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM7R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM8R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM9R']}
2025-05-24 12:31:49,207 - INFO - 批量插入表单数据成功，批次 2，共 100 条记录
2025-05-24 12:31:49,207 - INFO - 成功插入的数据ID: ['FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMIO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMJO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMKO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMLO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMMO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMNO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMOO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMPO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMQO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMRO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMSO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMTO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMUO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMVO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMWO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMXO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMYO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMZO', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM0P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM1P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM2P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM3P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM4P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM5P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM6P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM7P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM8P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM9P', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMAP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMBP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMCP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMDP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMEP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMFP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMGP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMHP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMIP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMJP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMKP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMLP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMMP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMNP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMOP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMPP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMQP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMRP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMSP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMTP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMUP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMVP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMWP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMXP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMYP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMZP', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM0Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM1Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM2Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM3Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM4Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM5Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM6Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM7Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM8Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM9Q', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMAQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMBQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMCQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMDQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMEQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMFQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMGQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMHQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMIQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMJQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMKQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMLQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMMQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMNQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMOQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMPQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMQQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMRQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMSQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMTQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMUQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMVQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMWQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMXQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMYQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BMZQ', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM0R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM1R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM2R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM3R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM4R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM5R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM6R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM7R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM8R', 'FINST-LLF66J7127MVXP1L6QKW75KEUGWE3M4DGQ1BM9R']
2025-05-24 12:31:54,488 - INFO - 批量插入响应状态码: 200
2025-05-24 12:31:54,488 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 04:32:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4886', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '324EFC1F-3B27-7A14-8CBE-DAE78576EA0D', 'x-acs-trace-id': 'ea5f0bd306be314c4d06f0fee2ddd60e', 'etag': '44kaAxVMVQ3aX9JabxG41Iw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 12:31:54,488 - INFO - 批量插入响应体: {'result': ['FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMAZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMBZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMCZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMDZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMEZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMFZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMGZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMHZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMIZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMJZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMKZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMLZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMMZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMNZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMOZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMPZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMQZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMRZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMSZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMTZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMUZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMVZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMWZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMXZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMYZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMZZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM001', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM101', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM201', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM301', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM401', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM501', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM601', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM701', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM801', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM901', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMA01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMB01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMC01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMD01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BME01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMF01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMG01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMH01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMI01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMJ01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMK01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BML01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMM01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMN01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMO01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMP01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMQ01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMR01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMS01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMT01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMU01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMV01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMW01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMX01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMY01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMZ01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM011', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM111', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM211', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM311', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM411', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM511', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM611', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM711', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM811', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM911', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMA11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMB11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMC11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMD11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BME11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMF11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMG11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMH11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMI11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMJ11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMK11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BML11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMM11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMN11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMO11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMP11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMQ11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMR11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMS11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMT11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMU11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BMV11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BMW11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BMX11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BMY11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BMZ11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BM021', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BM121']}
2025-05-24 12:31:54,488 - INFO - 批量插入表单数据成功，批次 3，共 100 条记录
2025-05-24 12:31:54,488 - INFO - 成功插入的数据ID: ['FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMAZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMBZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMCZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMDZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMEZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMFZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMGZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMHZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMIZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMJZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMKZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMLZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMMZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMNZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMOZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMPZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMQZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMRZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMSZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMTZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMUZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMVZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMWZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMXZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMYZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMZZ', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM001', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM101', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM201', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM301', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM401', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM501', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM601', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM701', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM801', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM901', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMA01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMB01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMC01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMD01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BME01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMF01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMG01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMH01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMI01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMJ01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMK01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BML01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMM01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMN01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMO01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMP01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMQ01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMR01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMS01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMT01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMU01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMV01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMW01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMX01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMY01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMZ01', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM011', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM111', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM211', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM311', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM411', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM511', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM611', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM711', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM811', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BM911', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMA11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMB11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMC11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMD11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BME11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMF11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMG11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMH11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMI11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMJ11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMK11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BML11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMM11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMN11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMO11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMP11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMQ11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMR11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMS11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMT11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Y6HGQ1BMU11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BMV11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BMW11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BMX11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BMY11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BMZ11', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BM021', 'FINST-5A966081X1KVB0JYES0264K9K7KG3Z6HGQ1BM121']
2025-05-24 12:31:59,785 - INFO - 批量插入响应状态码: 200
2025-05-24 12:31:59,785 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 04:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7B72CBE9-F70D-7F43-9C56-FC702BCEA6A1', 'x-acs-trace-id': '1795b1757bd203c459c54e2e54309fd4', 'etag': '4VN/F43lZn3LKn1vOrxrMvQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 12:31:59,785 - INFO - 批量插入响应体: {'result': ['FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMKN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMLN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMMN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMNN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMON', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMPN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMQN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMRN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMSN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMTN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMUN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMVN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMWN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMXN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMYN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMZN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM0O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM1O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM2O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM3O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM4O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM5O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM6O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM7O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM8O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM9O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMAO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMBO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMCO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMDO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMEO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMFO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMGO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMHO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMIO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMJO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMKO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMLO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMMO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMNO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMOO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMPO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMQO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMRO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMSO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMTO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMUO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMVO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMWO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMXO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMYO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMZO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM0P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM1P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM2P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM3P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM4P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM5P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM6P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM7P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM8P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM9P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMAP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMBP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMCP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMDP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMEP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMFP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMGP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMHP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMIP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMJP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMKP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMLP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMMP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMNP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMOP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMPP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMQP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMRP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMSP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMTP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMUP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMVP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMWP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMXP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMYP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMZP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM0Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM1Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM2Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM3Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM4Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM5Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM6Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM7Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM8Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM9Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMAQ', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMBQ']}
2025-05-24 12:31:59,785 - INFO - 批量插入表单数据成功，批次 4，共 100 条记录
2025-05-24 12:31:59,785 - INFO - 成功插入的数据ID: ['FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMKN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMLN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMMN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMNN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMON', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMPN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMQN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMRN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMSN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMTN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMUN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMVN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMWN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMXN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMYN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMZN', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM0O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM1O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM2O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM3O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM4O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM5O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM6O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM7O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM8O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM9O', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMAO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMBO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMCO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMDO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMEO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMFO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMGO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMHO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMIO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMJO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMKO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMLO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMMO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMNO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMOO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMPO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMQO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMRO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMSO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMTO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMUO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMVO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMWO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMXO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMYO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMZO', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM0P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM1P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM2P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM3P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM4P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM5P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM6P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM7P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM8P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM9P', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMAP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMBP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMCP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMDP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMEP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMFP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMGP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMHP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMIP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMJP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMKP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMLP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMMP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMNP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMOP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMPP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMQP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMRP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMSP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMTP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMUP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMVP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMWP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMXP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMYP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMZP', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM0Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM1Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM2Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM3Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM4Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM5Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM6Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM7Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM8Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BM9Q', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMAQ', 'FINST-PGC66MB1R3NVJWKQ6NYKFCYOXNW93AALGQ1BMBQ']
2025-05-24 12:32:04,941 - INFO - 批量插入响应状态码: 200
2025-05-24 12:32:04,941 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 04:32:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '341', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3BB747C6-0FAD-7538-B932-017398FA9C6E', 'x-acs-trace-id': '5fd0760eae6a66635ddfa759cdea635a', 'etag': '3iE8naE5tUjcXjqFchnkuAw1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 12:32:04,941 - INFO - 批量插入响应体: {'result': ['FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM3', 'FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM4', 'FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM5', 'FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM6', 'FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM7', 'FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM8', 'FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM9']}
2025-05-24 12:32:04,941 - INFO - 批量插入表单数据成功，批次 5，共 7 条记录
2025-05-24 12:32:04,941 - INFO - 成功插入的数据ID: ['FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM3', 'FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM4', 'FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM5', 'FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM6', 'FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM7', 'FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM8', 'FINST-NS866I91N6PVLCXC8KE9T8TLDYZX2H9PGQ1BM9']
2025-05-24 12:32:09,957 - INFO - 批量插入完成，共 407 条记录
2025-05-24 12:32:09,957 - INFO - 日期 2025-05-23 处理完成 - 更新: 1 条，插入: 407 条，错误: 0 条
2025-05-24 12:32:09,957 - INFO - 开始处理日期: 2025-05-24
2025-05-24 12:32:09,957 - INFO - Request Parameters - Page 1:
2025-05-24 12:32:09,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 12:32:09,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 12:32:10,457 - INFO - Response - Page 1:
2025-05-24 12:32:10,457 - INFO - 查询完成，共获取到 0 条记录
2025-05-24 12:32:10,457 - INFO - 获取到 0 条表单数据
2025-05-24 12:32:10,457 - INFO - 当前日期 2025-05-24 有 1 条MySQL数据需要处理
2025-05-24 12:32:10,457 - INFO - 开始批量插入 1 条新记录
2025-05-24 12:32:10,613 - INFO - 批量插入响应状态码: 200
2025-05-24 12:32:10,613 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 04:32:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '67A9599C-6140-761B-A3CE-F089336A7DAB', 'x-acs-trace-id': '9d569e23ae88c4413af571771b784b69', 'etag': '670TZpm8dRpce8vWCK9i7YA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 12:32:10,613 - INFO - 批量插入响应体: {'result': ['FINST-L5766E71TKOVXXOSDJN8J78V0WNC3YMTGQ1BM73']}
2025-05-24 12:32:10,613 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-24 12:32:10,613 - INFO - 成功插入的数据ID: ['FINST-L5766E71TKOVXXOSDJN8J78V0WNC3YMTGQ1BM73']
2025-05-24 12:32:15,628 - INFO - 批量插入完成，共 1 条记录
2025-05-24 12:32:15,628 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-24 12:32:15,628 - INFO - 数据同步完成！更新: 1 条，插入: 408 条，错误: 0 条
2025-05-24 12:32:15,628 - INFO - 同步完成
2025-05-24 13:30:33,950 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 13:30:33,950 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 13:30:33,950 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 13:30:34,028 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 137 条记录
2025-05-24 13:30:34,028 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 13:30:34,028 - INFO - 开始处理日期: 2025-05-23
2025-05-24 13:30:34,028 - INFO - Request Parameters - Page 1:
2025-05-24 13:30:34,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 13:30:34,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 13:30:42,153 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 42D522DC-EC0E-7E1B-8C08-8C1253E66CFF Response: {'code': 'ServiceUnavailable', 'requestid': '42D522DC-EC0E-7E1B-8C08-8C1253E66CFF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 42D522DC-EC0E-7E1B-8C08-8C1253E66CFF)
2025-05-24 13:30:42,153 - INFO - 开始处理日期: 2025-05-24
2025-05-24 13:30:42,153 - INFO - Request Parameters - Page 1:
2025-05-24 13:30:42,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 13:30:42,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 13:30:50,262 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 777E2407-16C6-7BCF-9BD5-9FCB7E68BD8A Response: {'code': 'ServiceUnavailable', 'requestid': '777E2407-16C6-7BCF-9BD5-9FCB7E68BD8A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 777E2407-16C6-7BCF-9BD5-9FCB7E68BD8A)
2025-05-24 13:30:50,262 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-24 13:31:50,277 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 13:31:50,277 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 13:31:50,277 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 13:31:50,355 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 482 条记录
2025-05-24 13:31:50,355 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 13:31:50,355 - INFO - 开始处理日期: 2025-05-23
2025-05-24 13:31:50,355 - INFO - Request Parameters - Page 1:
2025-05-24 13:31:50,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 13:31:50,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 13:31:51,230 - INFO - Response - Page 1:
2025-05-24 13:31:51,230 - INFO - 第 1 页获取到 100 条记录
2025-05-24 13:31:51,433 - INFO - Request Parameters - Page 2:
2025-05-24 13:31:51,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 13:31:51,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 13:31:52,246 - INFO - Response - Page 2:
2025-05-24 13:31:52,246 - INFO - 第 2 页获取到 100 条记录
2025-05-24 13:31:52,449 - INFO - Request Parameters - Page 3:
2025-05-24 13:31:52,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 13:31:52,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 13:31:53,215 - INFO - Response - Page 3:
2025-05-24 13:31:53,215 - INFO - 第 3 页获取到 100 条记录
2025-05-24 13:31:53,418 - INFO - Request Parameters - Page 4:
2025-05-24 13:31:53,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 13:31:53,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 13:31:54,199 - INFO - Response - Page 4:
2025-05-24 13:31:54,199 - INFO - 第 4 页获取到 100 条记录
2025-05-24 13:31:54,402 - INFO - Request Parameters - Page 5:
2025-05-24 13:31:54,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 13:31:54,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 13:31:55,433 - INFO - Response - Page 5:
2025-05-24 13:31:55,433 - INFO - 第 5 页获取到 74 条记录
2025-05-24 13:31:55,637 - INFO - 查询完成，共获取到 474 条记录
2025-05-24 13:31:55,637 - INFO - 获取到 474 条表单数据
2025-05-24 13:31:55,637 - INFO - 当前日期 2025-05-23 有 481 条MySQL数据需要处理
2025-05-24 13:31:55,652 - INFO - 开始批量插入 7 条新记录
2025-05-24 13:31:55,793 - INFO - 批量插入响应状态码: 200
2025-05-24 13:31:55,793 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 05:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '348', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A122230F-836F-764C-B02C-9182451FD6A0', 'x-acs-trace-id': '5760c98a2ee0fadc0563c8f47b11b5f9', 'etag': '3o+/NmtsndKs+1qbJk/Mr+Q8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 13:31:55,793 - INFO - 批量插入响应体: {'result': ['FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BM4D', 'FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BM5D', 'FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BM6D', 'FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BM7D', 'FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BM8D', 'FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BM9D', 'FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BMAD']}
2025-05-24 13:31:55,793 - INFO - 批量插入表单数据成功，批次 1，共 7 条记录
2025-05-24 13:31:55,793 - INFO - 成功插入的数据ID: ['FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BM4D', 'FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BM5D', 'FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BM6D', 'FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BM7D', 'FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BM8D', 'FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BM9D', 'FINST-3PF66X61AGOV28LYAFG334OJTPVR330OLS1BMAD']
2025-05-24 13:32:00,808 - INFO - 批量插入完成，共 7 条记录
2025-05-24 13:32:00,808 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 7 条，错误: 0 条
2025-05-24 13:32:00,808 - INFO - 开始处理日期: 2025-05-24
2025-05-24 13:32:00,808 - INFO - Request Parameters - Page 1:
2025-05-24 13:32:00,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 13:32:00,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 13:32:01,340 - INFO - Response - Page 1:
2025-05-24 13:32:01,340 - INFO - 第 1 页获取到 1 条记录
2025-05-24 13:32:01,543 - INFO - 查询完成，共获取到 1 条记录
2025-05-24 13:32:01,543 - INFO - 获取到 1 条表单数据
2025-05-24 13:32:01,543 - INFO - 当前日期 2025-05-24 有 1 条MySQL数据需要处理
2025-05-24 13:32:01,543 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 13:32:01,543 - INFO - 数据同步完成！更新: 0 条，插入: 7 条，错误: 0 条
2025-05-24 13:32:01,543 - INFO - 同步完成
2025-05-24 14:30:33,958 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 14:30:33,958 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 14:30:33,958 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 14:30:34,036 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 152 条记录
2025-05-24 14:30:34,036 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 14:30:34,036 - INFO - 开始处理日期: 2025-05-23
2025-05-24 14:30:34,036 - INFO - Request Parameters - Page 1:
2025-05-24 14:30:34,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 14:30:34,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 14:30:42,161 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 11F89E40-DB7A-7FEF-AF37-E01A759D9F94 Response: {'code': 'ServiceUnavailable', 'requestid': '11F89E40-DB7A-7FEF-AF37-E01A759D9F94', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 11F89E40-DB7A-7FEF-AF37-E01A759D9F94)
2025-05-24 14:30:42,161 - INFO - 开始处理日期: 2025-05-24
2025-05-24 14:30:42,161 - INFO - Request Parameters - Page 1:
2025-05-24 14:30:42,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 14:30:42,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 14:30:50,286 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F852573A-7D0E-7516-8DBD-778F90AFFC04 Response: {'code': 'ServiceUnavailable', 'requestid': 'F852573A-7D0E-7516-8DBD-778F90AFFC04', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F852573A-7D0E-7516-8DBD-778F90AFFC04)
2025-05-24 14:30:50,286 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-24 14:31:50,301 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 14:31:50,301 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 14:31:50,301 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 14:31:50,379 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 497 条记录
2025-05-24 14:31:50,379 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 14:31:50,379 - INFO - 开始处理日期: 2025-05-23
2025-05-24 14:31:50,379 - INFO - Request Parameters - Page 1:
2025-05-24 14:31:50,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 14:31:50,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 14:31:51,332 - INFO - Response - Page 1:
2025-05-24 14:31:51,332 - INFO - 第 1 页获取到 100 条记录
2025-05-24 14:31:51,535 - INFO - Request Parameters - Page 2:
2025-05-24 14:31:51,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 14:31:51,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 14:31:52,379 - INFO - Response - Page 2:
2025-05-24 14:31:52,379 - INFO - 第 2 页获取到 100 条记录
2025-05-24 14:31:52,582 - INFO - Request Parameters - Page 3:
2025-05-24 14:31:52,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 14:31:52,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 14:31:53,395 - INFO - Response - Page 3:
2025-05-24 14:31:53,395 - INFO - 第 3 页获取到 100 条记录
2025-05-24 14:31:53,598 - INFO - Request Parameters - Page 4:
2025-05-24 14:31:53,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 14:31:53,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 14:31:54,426 - INFO - Response - Page 4:
2025-05-24 14:31:54,426 - INFO - 第 4 页获取到 100 条记录
2025-05-24 14:31:54,629 - INFO - Request Parameters - Page 5:
2025-05-24 14:31:54,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 14:31:54,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 14:31:55,441 - INFO - Response - Page 5:
2025-05-24 14:31:55,441 - INFO - 第 5 页获取到 81 条记录
2025-05-24 14:31:55,645 - INFO - 查询完成，共获取到 481 条记录
2025-05-24 14:31:55,645 - INFO - 获取到 481 条表单数据
2025-05-24 14:31:55,645 - INFO - 当前日期 2025-05-23 有 496 条MySQL数据需要处理
2025-05-24 14:31:55,660 - INFO - 开始批量插入 15 条新记录
2025-05-24 14:31:55,816 - INFO - 批量插入响应状态码: 200
2025-05-24 14:31:55,816 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 06:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '732', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D84338DF-FAEB-77D4-A7C8-8B80370423A1', 'x-acs-trace-id': 'e7de3f1da5b6018cacad232da9b30186', 'etag': '7gBVJuWABbXkVUBuRr9IBAw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 14:31:55,816 - INFO - 批量插入响应体: {'result': ['FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMKS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMLS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMMS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMNS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMOS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMPS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMQS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMRS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMSS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMTS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMUS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMVS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMWS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMXS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMYS']}
2025-05-24 14:31:55,816 - INFO - 批量插入表单数据成功，批次 1，共 15 条记录
2025-05-24 14:31:55,816 - INFO - 成功插入的数据ID: ['FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMKS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMLS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMMS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMNS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMOS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMPS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMQS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMRS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMSS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMTS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMUS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMVS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMWS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMXS', 'FINST-07E66I91D5NVOC6VDMUD57U3AJUJ3CTTQU1BMYS']
2025-05-24 14:32:00,832 - INFO - 批量插入完成，共 15 条记录
2025-05-24 14:32:00,832 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 15 条，错误: 0 条
2025-05-24 14:32:00,832 - INFO - 开始处理日期: 2025-05-24
2025-05-24 14:32:00,832 - INFO - Request Parameters - Page 1:
2025-05-24 14:32:00,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 14:32:00,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 14:32:01,363 - INFO - Response - Page 1:
2025-05-24 14:32:01,363 - INFO - 第 1 页获取到 1 条记录
2025-05-24 14:32:01,566 - INFO - 查询完成，共获取到 1 条记录
2025-05-24 14:32:01,566 - INFO - 获取到 1 条表单数据
2025-05-24 14:32:01,566 - INFO - 当前日期 2025-05-24 有 1 条MySQL数据需要处理
2025-05-24 14:32:01,566 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 14:32:01,566 - INFO - 数据同步完成！更新: 0 条，插入: 15 条，错误: 0 条
2025-05-24 14:32:01,566 - INFO - 同步完成
2025-05-24 15:30:34,231 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 15:30:34,231 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 15:30:34,231 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 15:30:34,309 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 170 条记录
2025-05-24 15:30:34,309 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 15:30:34,309 - INFO - 开始处理日期: 2025-05-23
2025-05-24 15:30:34,309 - INFO - Request Parameters - Page 1:
2025-05-24 15:30:34,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:30:34,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:30:42,434 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AFCA98ED-EAEB-778D-93E8-8E5E55BD27B8 Response: {'code': 'ServiceUnavailable', 'requestid': 'AFCA98ED-EAEB-778D-93E8-8E5E55BD27B8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AFCA98ED-EAEB-778D-93E8-8E5E55BD27B8)
2025-05-24 15:30:42,434 - INFO - 开始处理日期: 2025-05-24
2025-05-24 15:30:42,434 - INFO - Request Parameters - Page 1:
2025-05-24 15:30:42,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:30:42,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:30:44,747 - INFO - Response - Page 1:
2025-05-24 15:30:44,747 - INFO - 第 1 页获取到 1 条记录
2025-05-24 15:30:44,950 - INFO - 查询完成，共获取到 1 条记录
2025-05-24 15:30:44,950 - INFO - 获取到 1 条表单数据
2025-05-24 15:30:44,950 - INFO - 当前日期 2025-05-24 有 1 条MySQL数据需要处理
2025-05-24 15:30:44,950 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 15:30:44,950 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 15:31:44,965 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 15:31:44,965 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 15:31:44,965 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 15:31:45,043 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 515 条记录
2025-05-24 15:31:45,043 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 15:31:45,043 - INFO - 开始处理日期: 2025-05-23
2025-05-24 15:31:45,043 - INFO - Request Parameters - Page 1:
2025-05-24 15:31:45,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:31:45,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:31:46,137 - INFO - Response - Page 1:
2025-05-24 15:31:46,137 - INFO - 第 1 页获取到 100 条记录
2025-05-24 15:31:46,340 - INFO - Request Parameters - Page 2:
2025-05-24 15:31:46,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:31:46,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:31:47,137 - INFO - Response - Page 2:
2025-05-24 15:31:47,137 - INFO - 第 2 页获取到 100 条记录
2025-05-24 15:31:47,340 - INFO - Request Parameters - Page 3:
2025-05-24 15:31:47,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:31:47,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:31:48,262 - INFO - Response - Page 3:
2025-05-24 15:31:48,262 - INFO - 第 3 页获取到 100 条记录
2025-05-24 15:31:48,465 - INFO - Request Parameters - Page 4:
2025-05-24 15:31:48,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:31:48,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:31:49,277 - INFO - Response - Page 4:
2025-05-24 15:31:49,277 - INFO - 第 4 页获取到 100 条记录
2025-05-24 15:31:49,481 - INFO - Request Parameters - Page 5:
2025-05-24 15:31:49,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:31:49,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:31:50,309 - INFO - Response - Page 5:
2025-05-24 15:31:50,309 - INFO - 第 5 页获取到 96 条记录
2025-05-24 15:31:50,512 - INFO - 查询完成，共获取到 496 条记录
2025-05-24 15:31:50,512 - INFO - 获取到 496 条表单数据
2025-05-24 15:31:50,512 - INFO - 当前日期 2025-05-23 有 514 条MySQL数据需要处理
2025-05-24 15:31:50,527 - INFO - 开始批量插入 18 条新记录
2025-05-24 15:31:50,699 - INFO - 批量插入响应状态码: 200
2025-05-24 15:31:50,699 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 07:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '876', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '883D94E8-A3D2-7F29-BCD8-5D430CF9F4F5', 'x-acs-trace-id': '683885fc411a166134f89aa7fa00823f', 'etag': '8lBuavbFeK1QDiFHegX1jPQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 15:31:50,699 - INFO - 批量插入响应体: {'result': ['FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMJH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMKH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMLH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMMH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMNH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMOH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMPH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMQH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMRH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMSH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMTH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMUH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMVH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMWH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMXH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMYH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMZH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BM0I']}
2025-05-24 15:31:50,699 - INFO - 批量插入表单数据成功，批次 1，共 18 条记录
2025-05-24 15:31:50,699 - INFO - 成功插入的数据ID: ['FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMJH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMKH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMLH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMMH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMNH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMOH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMPH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMQH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMRH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMSH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMTH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMUH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMVH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMWH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMXH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMYH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BMZH', 'FINST-A17661C1RBLVSL03FFTCU7TIYI7F3NNVVW1BM0I']
2025-05-24 15:31:55,715 - INFO - 批量插入完成，共 18 条记录
2025-05-24 15:31:55,715 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 18 条，错误: 0 条
2025-05-24 15:31:55,715 - INFO - 开始处理日期: 2025-05-24
2025-05-24 15:31:55,715 - INFO - Request Parameters - Page 1:
2025-05-24 15:31:55,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 15:31:55,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 15:31:56,293 - INFO - Response - Page 1:
2025-05-24 15:31:56,293 - INFO - 第 1 页获取到 1 条记录
2025-05-24 15:31:56,496 - INFO - 查询完成，共获取到 1 条记录
2025-05-24 15:31:56,496 - INFO - 获取到 1 条表单数据
2025-05-24 15:31:56,496 - INFO - 当前日期 2025-05-24 有 1 条MySQL数据需要处理
2025-05-24 15:31:56,496 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 15:31:56,496 - INFO - 数据同步完成！更新: 0 条，插入: 18 条，错误: 0 条
2025-05-24 15:31:56,496 - INFO - 同步完成
2025-05-24 16:30:34,239 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 16:30:34,255 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 16:30:34,255 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 16:30:34,317 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 170 条记录
2025-05-24 16:30:34,317 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 16:30:34,317 - INFO - 开始处理日期: 2025-05-23
2025-05-24 16:30:34,317 - INFO - Request Parameters - Page 1:
2025-05-24 16:30:34,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 16:30:34,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 16:30:42,442 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 618C0086-B5A9-7296-90DC-CF3108F4522D Response: {'code': 'ServiceUnavailable', 'requestid': '618C0086-B5A9-7296-90DC-CF3108F4522D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 618C0086-B5A9-7296-90DC-CF3108F4522D)
2025-05-24 16:30:42,442 - INFO - 开始处理日期: 2025-05-24
2025-05-24 16:30:42,442 - INFO - Request Parameters - Page 1:
2025-05-24 16:30:42,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 16:30:42,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 16:30:50,536 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 17BBC6F5-433A-7E1E-BD5E-4B867EBC4879 Response: {'code': 'ServiceUnavailable', 'requestid': '17BBC6F5-433A-7E1E-BD5E-4B867EBC4879', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 17BBC6F5-433A-7E1E-BD5E-4B867EBC4879)
2025-05-24 16:30:50,536 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-24 16:31:50,551 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 16:31:50,551 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 16:31:50,551 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 16:31:50,629 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 515 条记录
2025-05-24 16:31:50,629 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 16:31:50,629 - INFO - 开始处理日期: 2025-05-23
2025-05-24 16:31:50,629 - INFO - Request Parameters - Page 1:
2025-05-24 16:31:50,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 16:31:50,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 16:31:51,598 - INFO - Response - Page 1:
2025-05-24 16:31:51,598 - INFO - 第 1 页获取到 100 条记录
2025-05-24 16:31:51,801 - INFO - Request Parameters - Page 2:
2025-05-24 16:31:51,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 16:31:51,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 16:31:52,629 - INFO - Response - Page 2:
2025-05-24 16:31:52,629 - INFO - 第 2 页获取到 100 条记录
2025-05-24 16:31:52,832 - INFO - Request Parameters - Page 3:
2025-05-24 16:31:52,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 16:31:52,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 16:31:53,660 - INFO - Response - Page 3:
2025-05-24 16:31:53,660 - INFO - 第 3 页获取到 100 条记录
2025-05-24 16:31:53,864 - INFO - Request Parameters - Page 4:
2025-05-24 16:31:53,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 16:31:53,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 16:31:55,067 - INFO - Response - Page 4:
2025-05-24 16:31:55,067 - INFO - 第 4 页获取到 100 条记录
2025-05-24 16:31:55,270 - INFO - Request Parameters - Page 5:
2025-05-24 16:31:55,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 16:31:55,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 16:31:56,114 - INFO - Response - Page 5:
2025-05-24 16:31:56,114 - INFO - 第 5 页获取到 100 条记录
2025-05-24 16:31:56,317 - INFO - Request Parameters - Page 6:
2025-05-24 16:31:56,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 16:31:56,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 16:31:56,895 - INFO - Response - Page 6:
2025-05-24 16:31:56,895 - INFO - 第 6 页获取到 14 条记录
2025-05-24 16:31:57,098 - INFO - 查询完成，共获取到 514 条记录
2025-05-24 16:31:57,098 - INFO - 获取到 514 条表单数据
2025-05-24 16:31:57,098 - INFO - 当前日期 2025-05-23 有 514 条MySQL数据需要处理
2025-05-24 16:31:57,114 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 16:31:57,114 - INFO - 开始处理日期: 2025-05-24
2025-05-24 16:31:57,114 - INFO - Request Parameters - Page 1:
2025-05-24 16:31:57,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 16:31:57,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 16:31:57,660 - INFO - Response - Page 1:
2025-05-24 16:31:57,660 - INFO - 第 1 页获取到 1 条记录
2025-05-24 16:31:57,864 - INFO - 查询完成，共获取到 1 条记录
2025-05-24 16:31:57,864 - INFO - 获取到 1 条表单数据
2025-05-24 16:31:57,864 - INFO - 当前日期 2025-05-24 有 1 条MySQL数据需要处理
2025-05-24 16:31:57,864 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 16:31:57,864 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 16:31:57,864 - INFO - 同步完成
2025-05-24 17:30:34,060 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 17:30:34,060 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 17:30:34,060 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 17:30:34,122 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 177 条记录
2025-05-24 17:30:34,122 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 17:30:34,122 - INFO - 开始处理日期: 2025-05-23
2025-05-24 17:30:34,138 - INFO - Request Parameters - Page 1:
2025-05-24 17:30:34,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 17:30:34,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 17:30:42,263 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AF190528-EE71-7B5E-B9C8-7CBC60079D27 Response: {'code': 'ServiceUnavailable', 'requestid': 'AF190528-EE71-7B5E-B9C8-7CBC60079D27', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AF190528-EE71-7B5E-B9C8-7CBC60079D27)
2025-05-24 17:30:42,263 - INFO - 开始处理日期: 2025-05-24
2025-05-24 17:30:42,263 - INFO - Request Parameters - Page 1:
2025-05-24 17:30:42,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 17:30:42,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 17:30:50,388 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B8B31455-2736-70C1-BEE4-3C05936F4CFE Response: {'code': 'ServiceUnavailable', 'requestid': 'B8B31455-2736-70C1-BEE4-3C05936F4CFE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B8B31455-2736-70C1-BEE4-3C05936F4CFE)
2025-05-24 17:30:50,388 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-24 17:31:50,403 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 17:31:50,403 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 17:31:50,403 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 17:31:50,481 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 522 条记录
2025-05-24 17:31:50,481 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 17:31:50,481 - INFO - 开始处理日期: 2025-05-23
2025-05-24 17:31:50,481 - INFO - Request Parameters - Page 1:
2025-05-24 17:31:50,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 17:31:50,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 17:31:58,590 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7A9C08B9-949B-72F4-B331-48AAD3D5B533 Response: {'code': 'ServiceUnavailable', 'requestid': '7A9C08B9-949B-72F4-B331-48AAD3D5B533', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7A9C08B9-949B-72F4-B331-48AAD3D5B533)
2025-05-24 17:31:58,590 - INFO - 开始处理日期: 2025-05-24
2025-05-24 17:31:58,590 - INFO - Request Parameters - Page 1:
2025-05-24 17:31:58,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 17:31:58,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 17:32:00,715 - INFO - Response - Page 1:
2025-05-24 17:32:00,715 - INFO - 第 1 页获取到 1 条记录
2025-05-24 17:32:00,918 - INFO - 查询完成，共获取到 1 条记录
2025-05-24 17:32:00,918 - INFO - 获取到 1 条表单数据
2025-05-24 17:32:00,918 - INFO - 当前日期 2025-05-24 有 1 条MySQL数据需要处理
2025-05-24 17:32:00,918 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 17:32:00,918 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 17:32:00,918 - INFO - 同步完成
2025-05-24 18:30:34,130 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 18:30:34,130 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 18:30:34,130 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 18:30:34,208 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 177 条记录
2025-05-24 18:30:34,208 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 18:30:34,208 - INFO - 开始处理日期: 2025-05-23
2025-05-24 18:30:34,208 - INFO - Request Parameters - Page 1:
2025-05-24 18:30:34,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:30:34,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:30:42,333 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DB96828E-A7BA-729B-AECB-BF6C40778E88 Response: {'code': 'ServiceUnavailable', 'requestid': 'DB96828E-A7BA-729B-AECB-BF6C40778E88', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DB96828E-A7BA-729B-AECB-BF6C40778E88)
2025-05-24 18:30:42,333 - INFO - 开始处理日期: 2025-05-24
2025-05-24 18:30:42,333 - INFO - Request Parameters - Page 1:
2025-05-24 18:30:42,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:30:42,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:30:44,552 - INFO - Response - Page 1:
2025-05-24 18:30:44,552 - INFO - 第 1 页获取到 1 条记录
2025-05-24 18:30:44,755 - INFO - 查询完成，共获取到 1 条记录
2025-05-24 18:30:44,755 - INFO - 获取到 1 条表单数据
2025-05-24 18:30:44,755 - INFO - 当前日期 2025-05-24 有 1 条MySQL数据需要处理
2025-05-24 18:30:44,755 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 18:30:44,755 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 18:31:44,770 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 18:31:44,770 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 18:31:44,770 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 18:31:44,848 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 522 条记录
2025-05-24 18:31:44,848 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 18:31:44,848 - INFO - 开始处理日期: 2025-05-23
2025-05-24 18:31:44,848 - INFO - Request Parameters - Page 1:
2025-05-24 18:31:44,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:31:44,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:31:52,957 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E50A945F-C6E9-7953-B5B6-076C4BF54A40 Response: {'code': 'ServiceUnavailable', 'requestid': 'E50A945F-C6E9-7953-B5B6-076C4BF54A40', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E50A945F-C6E9-7953-B5B6-076C4BF54A40)
2025-05-24 18:31:52,957 - INFO - 开始处理日期: 2025-05-24
2025-05-24 18:31:52,957 - INFO - Request Parameters - Page 1:
2025-05-24 18:31:52,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 18:31:52,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 18:32:00,207 - INFO - Response - Page 1:
2025-05-24 18:32:00,207 - INFO - 第 1 页获取到 1 条记录
2025-05-24 18:32:00,411 - INFO - 查询完成，共获取到 1 条记录
2025-05-24 18:32:00,411 - INFO - 获取到 1 条表单数据
2025-05-24 18:32:00,411 - INFO - 当前日期 2025-05-24 有 1 条MySQL数据需要处理
2025-05-24 18:32:00,411 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 18:32:00,411 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 18:32:00,411 - INFO - 同步完成
2025-05-24 19:30:35,000 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 19:30:35,000 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 19:30:35,000 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 19:30:35,063 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 177 条记录
2025-05-24 19:30:35,063 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 19:30:35,063 - INFO - 开始处理日期: 2025-05-23
2025-05-24 19:30:35,063 - INFO - Request Parameters - Page 1:
2025-05-24 19:30:35,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 19:30:35,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 19:30:43,171 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E6373FC1-B30A-78A1-9B0F-0A6FA8BADCC0 Response: {'code': 'ServiceUnavailable', 'requestid': 'E6373FC1-B30A-78A1-9B0F-0A6FA8BADCC0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E6373FC1-B30A-78A1-9B0F-0A6FA8BADCC0)
2025-05-24 19:30:43,171 - INFO - 开始处理日期: 2025-05-24
2025-05-24 19:30:43,171 - INFO - Request Parameters - Page 1:
2025-05-24 19:30:43,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 19:30:43,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 19:30:51,294 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CA8DD495-49C4-72FE-840D-19C592427BB0 Response: {'code': 'ServiceUnavailable', 'requestid': 'CA8DD495-49C4-72FE-840D-19C592427BB0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CA8DD495-49C4-72FE-840D-19C592427BB0)
2025-05-24 19:30:51,294 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-24 19:31:51,296 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 19:31:51,296 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 19:31:51,296 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 19:31:51,374 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 522 条记录
2025-05-24 19:31:51,374 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 19:31:51,374 - INFO - 开始处理日期: 2025-05-23
2025-05-24 19:31:51,374 - INFO - Request Parameters - Page 1:
2025-05-24 19:31:51,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 19:31:51,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 19:31:52,265 - INFO - Response - Page 1:
2025-05-24 19:31:52,265 - INFO - 第 1 页获取到 100 条记录
2025-05-24 19:31:52,468 - INFO - Request Parameters - Page 2:
2025-05-24 19:31:52,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 19:31:52,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 19:31:53,249 - INFO - Response - Page 2:
2025-05-24 19:31:53,249 - INFO - 第 2 页获取到 100 条记录
2025-05-24 19:31:53,452 - INFO - Request Parameters - Page 3:
2025-05-24 19:31:53,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 19:31:53,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 19:31:54,311 - INFO - Response - Page 3:
2025-05-24 19:31:54,311 - INFO - 第 3 页获取到 100 条记录
2025-05-24 19:31:54,514 - INFO - Request Parameters - Page 4:
2025-05-24 19:31:54,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 19:31:54,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 19:31:55,358 - INFO - Response - Page 4:
2025-05-24 19:31:55,358 - INFO - 第 4 页获取到 100 条记录
2025-05-24 19:31:55,561 - INFO - Request Parameters - Page 5:
2025-05-24 19:31:55,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 19:31:55,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 19:31:56,326 - INFO - Response - Page 5:
2025-05-24 19:31:56,326 - INFO - 第 5 页获取到 100 条记录
2025-05-24 19:31:56,530 - INFO - Request Parameters - Page 6:
2025-05-24 19:31:56,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 19:31:56,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 19:31:57,170 - INFO - Response - Page 6:
2025-05-24 19:31:57,170 - INFO - 第 6 页获取到 14 条记录
2025-05-24 19:31:57,373 - INFO - 查询完成，共获取到 514 条记录
2025-05-24 19:31:57,373 - INFO - 获取到 514 条表单数据
2025-05-24 19:31:57,373 - INFO - 当前日期 2025-05-23 有 521 条MySQL数据需要处理
2025-05-24 19:31:57,389 - INFO - 开始批量插入 7 条新记录
2025-05-24 19:31:57,529 - INFO - 批量插入响应状态码: 200
2025-05-24 19:31:57,529 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 11:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '341', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BA744179-554F-756E-9E6E-CD96C706CA0B', 'x-acs-trace-id': 'a3cded85b02ee58d639251389c072fb5', 'etag': '3zbaxzDjRUKswcniZbuBdEg1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 19:31:57,529 - INFO - 批量插入响应体: {'result': ['FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BM9', 'FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BMA', 'FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BMB', 'FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BMC', 'FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BMD', 'FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BME', 'FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BMF']}
2025-05-24 19:31:57,529 - INFO - 批量插入表单数据成功，批次 1，共 7 条记录
2025-05-24 19:31:57,529 - INFO - 成功插入的数据ID: ['FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BM9', 'FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BMA', 'FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BMB', 'FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BMC', 'FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BMD', 'FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BME', 'FINST-12766OC1ZCPVWX769ONAZ3VMMFSN2P7JG52BMF']
2025-05-24 19:32:02,544 - INFO - 批量插入完成，共 7 条记录
2025-05-24 19:32:02,544 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 7 条，错误: 0 条
2025-05-24 19:32:02,544 - INFO - 开始处理日期: 2025-05-24
2025-05-24 19:32:02,544 - INFO - Request Parameters - Page 1:
2025-05-24 19:32:02,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 19:32:02,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 19:32:03,028 - INFO - Response - Page 1:
2025-05-24 19:32:03,028 - INFO - 第 1 页获取到 1 条记录
2025-05-24 19:32:03,231 - INFO - 查询完成，共获取到 1 条记录
2025-05-24 19:32:03,231 - INFO - 获取到 1 条表单数据
2025-05-24 19:32:03,231 - INFO - 当前日期 2025-05-24 有 1 条MySQL数据需要处理
2025-05-24 19:32:03,231 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 19:32:03,231 - INFO - 数据同步完成！更新: 0 条，插入: 7 条，错误: 0 条
2025-05-24 19:32:03,231 - INFO - 同步完成
2025-05-24 20:30:32,889 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 20:30:32,889 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 20:30:32,889 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 20:30:32,967 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 177 条记录
2025-05-24 20:30:32,967 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 20:30:32,967 - INFO - 开始处理日期: 2025-05-23
2025-05-24 20:30:32,967 - INFO - Request Parameters - Page 1:
2025-05-24 20:30:32,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 20:30:32,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 20:30:41,074 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3E8218E8-8A5B-758E-BD84-5D11965F801B Response: {'code': 'ServiceUnavailable', 'requestid': '3E8218E8-8A5B-758E-BD84-5D11965F801B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3E8218E8-8A5B-758E-BD84-5D11965F801B)
2025-05-24 20:30:41,074 - INFO - 开始处理日期: 2025-05-24
2025-05-24 20:30:41,074 - INFO - Request Parameters - Page 1:
2025-05-24 20:30:41,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 20:30:41,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 20:30:49,182 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9F911284-89FB-79F3-93F9-92D348889AEC Response: {'code': 'ServiceUnavailable', 'requestid': '9F911284-89FB-79F3-93F9-92D348889AEC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9F911284-89FB-79F3-93F9-92D348889AEC)
2025-05-24 20:30:49,182 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-24 20:31:49,185 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 20:31:49,185 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 20:31:49,185 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 20:31:49,263 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 522 条记录
2025-05-24 20:31:49,263 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 20:31:49,263 - INFO - 开始处理日期: 2025-05-23
2025-05-24 20:31:49,263 - INFO - Request Parameters - Page 1:
2025-05-24 20:31:49,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 20:31:49,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 20:31:50,309 - INFO - Response - Page 1:
2025-05-24 20:31:50,309 - INFO - 第 1 页获取到 100 条记录
2025-05-24 20:31:50,512 - INFO - Request Parameters - Page 2:
2025-05-24 20:31:50,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 20:31:50,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 20:31:51,262 - INFO - Response - Page 2:
2025-05-24 20:31:51,262 - INFO - 第 2 页获取到 100 条记录
2025-05-24 20:31:51,465 - INFO - Request Parameters - Page 3:
2025-05-24 20:31:51,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 20:31:51,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 20:31:52,293 - INFO - Response - Page 3:
2025-05-24 20:31:52,293 - INFO - 第 3 页获取到 100 条记录
2025-05-24 20:31:52,496 - INFO - Request Parameters - Page 4:
2025-05-24 20:31:52,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 20:31:52,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 20:31:53,246 - INFO - Response - Page 4:
2025-05-24 20:31:53,246 - INFO - 第 4 页获取到 100 条记录
2025-05-24 20:31:53,449 - INFO - Request Parameters - Page 5:
2025-05-24 20:31:53,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 20:31:53,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 20:31:54,355 - INFO - Response - Page 5:
2025-05-24 20:31:54,355 - INFO - 第 5 页获取到 100 条记录
2025-05-24 20:31:54,558 - INFO - Request Parameters - Page 6:
2025-05-24 20:31:54,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 20:31:54,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 20:31:55,230 - INFO - Response - Page 6:
2025-05-24 20:31:55,230 - INFO - 第 6 页获取到 21 条记录
2025-05-24 20:31:55,433 - INFO - 查询完成，共获取到 521 条记录
2025-05-24 20:31:55,433 - INFO - 获取到 521 条表单数据
2025-05-24 20:31:55,433 - INFO - 当前日期 2025-05-23 有 521 条MySQL数据需要处理
2025-05-24 20:31:55,449 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 20:31:55,449 - INFO - 开始处理日期: 2025-05-24
2025-05-24 20:31:55,449 - INFO - Request Parameters - Page 1:
2025-05-24 20:31:55,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 20:31:55,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 20:31:55,980 - INFO - Response - Page 1:
2025-05-24 20:31:55,980 - INFO - 第 1 页获取到 1 条记录
2025-05-24 20:31:56,183 - INFO - 查询完成，共获取到 1 条记录
2025-05-24 20:31:56,183 - INFO - 获取到 1 条表单数据
2025-05-24 20:31:56,183 - INFO - 当前日期 2025-05-24 有 1 条MySQL数据需要处理
2025-05-24 20:31:56,183 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 20:31:56,183 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 20:31:56,183 - INFO - 同步完成
2025-05-24 21:30:33,089 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 21:30:33,089 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 21:30:33,089 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 21:30:33,151 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 178 条记录
2025-05-24 21:30:33,151 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 21:30:33,151 - INFO - 开始处理日期: 2025-05-23
2025-05-24 21:30:33,151 - INFO - Request Parameters - Page 1:
2025-05-24 21:30:33,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:30:33,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:30:41,275 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 865BBCCD-E506-7736-9C50-1AC31DFACE23 Response: {'code': 'ServiceUnavailable', 'requestid': '865BBCCD-E506-7736-9C50-1AC31DFACE23', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 865BBCCD-E506-7736-9C50-1AC31DFACE23)
2025-05-24 21:30:41,275 - INFO - 开始处理日期: 2025-05-24
2025-05-24 21:30:41,275 - INFO - Request Parameters - Page 1:
2025-05-24 21:30:41,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:30:41,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:30:49,398 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3C78B228-C389-74E0-8988-B46B7F3CE914 Response: {'code': 'ServiceUnavailable', 'requestid': '3C78B228-C389-74E0-8988-B46B7F3CE914', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3C78B228-C389-74E0-8988-B46B7F3CE914)
2025-05-24 21:30:49,398 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-24 21:31:49,400 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 21:31:49,400 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 21:31:49,400 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 21:31:49,479 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 523 条记录
2025-05-24 21:31:49,479 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 21:31:49,479 - INFO - 开始处理日期: 2025-05-23
2025-05-24 21:31:49,479 - INFO - Request Parameters - Page 1:
2025-05-24 21:31:49,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:31:49,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:31:50,525 - INFO - Response - Page 1:
2025-05-24 21:31:50,525 - INFO - 第 1 页获取到 100 条记录
2025-05-24 21:31:50,728 - INFO - Request Parameters - Page 2:
2025-05-24 21:31:50,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:31:50,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:31:51,494 - INFO - Response - Page 2:
2025-05-24 21:31:51,494 - INFO - 第 2 页获取到 100 条记录
2025-05-24 21:31:51,697 - INFO - Request Parameters - Page 3:
2025-05-24 21:31:51,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:31:51,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:31:52,509 - INFO - Response - Page 3:
2025-05-24 21:31:52,509 - INFO - 第 3 页获取到 100 条记录
2025-05-24 21:31:52,712 - INFO - Request Parameters - Page 4:
2025-05-24 21:31:52,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:31:52,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:31:53,853 - INFO - Response - Page 4:
2025-05-24 21:31:53,853 - INFO - 第 4 页获取到 100 条记录
2025-05-24 21:31:54,056 - INFO - Request Parameters - Page 5:
2025-05-24 21:31:54,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:31:54,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:31:54,930 - INFO - Response - Page 5:
2025-05-24 21:31:54,930 - INFO - 第 5 页获取到 100 条记录
2025-05-24 21:31:55,134 - INFO - Request Parameters - Page 6:
2025-05-24 21:31:55,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:31:55,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:31:55,790 - INFO - Response - Page 6:
2025-05-24 21:31:55,790 - INFO - 第 6 页获取到 21 条记录
2025-05-24 21:31:55,993 - INFO - 查询完成，共获取到 521 条记录
2025-05-24 21:31:55,993 - INFO - 获取到 521 条表单数据
2025-05-24 21:31:55,993 - INFO - 当前日期 2025-05-23 有 521 条MySQL数据需要处理
2025-05-24 21:31:56,008 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 21:31:56,008 - INFO - 开始处理日期: 2025-05-24
2025-05-24 21:31:56,008 - INFO - Request Parameters - Page 1:
2025-05-24 21:31:56,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 21:31:56,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 21:31:56,508 - INFO - Response - Page 1:
2025-05-24 21:31:56,508 - INFO - 第 1 页获取到 1 条记录
2025-05-24 21:31:56,711 - INFO - 查询完成，共获取到 1 条记录
2025-05-24 21:31:56,711 - INFO - 获取到 1 条表单数据
2025-05-24 21:31:56,711 - INFO - 当前日期 2025-05-24 有 2 条MySQL数据需要处理
2025-05-24 21:31:56,711 - INFO - 开始批量插入 1 条新记录
2025-05-24 21:31:56,868 - INFO - 批量插入响应状态码: 200
2025-05-24 21:31:56,868 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 13:31:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4FC4DE12-3CF8-7B9A-8CFD-D9A2477F5751', 'x-acs-trace-id': 'aa941f41ee137b591b382cde9a683b4e', 'etag': '62Nf3Dr1cKLpeCaCqNIhAxw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 21:31:56,868 - INFO - 批量插入响应体: {'result': ['FINST-1PF66VA1S2PVTQLC8MOKEDZ839QM2FGVQ92BME4']}
2025-05-24 21:31:56,868 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-24 21:31:56,868 - INFO - 成功插入的数据ID: ['FINST-1PF66VA1S2PVTQLC8MOKEDZ839QM2FGVQ92BME4']
2025-05-24 21:32:01,882 - INFO - 批量插入完成，共 1 条记录
2025-05-24 21:32:01,882 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-24 21:32:01,882 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-05-24 21:32:01,882 - INFO - 同步完成
2025-05-24 22:30:32,945 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 22:30:32,945 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 22:30:32,945 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 22:30:33,023 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 192 条记录
2025-05-24 22:30:33,023 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 22:30:33,023 - INFO - 开始处理日期: 2025-05-23
2025-05-24 22:30:33,023 - INFO - Request Parameters - Page 1:
2025-05-24 22:30:33,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 22:30:33,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 22:30:41,131 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 899298AA-DF64-7A2C-A248-B305CFA61C54 Response: {'code': 'ServiceUnavailable', 'requestid': '899298AA-DF64-7A2C-A248-B305CFA61C54', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 899298AA-DF64-7A2C-A248-B305CFA61C54)
2025-05-24 22:30:41,131 - INFO - 开始处理日期: 2025-05-24
2025-05-24 22:30:41,131 - INFO - Request Parameters - Page 1:
2025-05-24 22:30:41,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 22:30:41,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 22:30:43,365 - INFO - Response - Page 1:
2025-05-24 22:30:43,365 - INFO - 第 1 页获取到 2 条记录
2025-05-24 22:30:43,568 - INFO - 查询完成，共获取到 2 条记录
2025-05-24 22:30:43,568 - INFO - 获取到 2 条表单数据
2025-05-24 22:30:43,568 - INFO - 当前日期 2025-05-24 有 11 条MySQL数据需要处理
2025-05-24 22:30:43,568 - INFO - 开始批量插入 9 条新记录
2025-05-24 22:30:43,724 - INFO - 批量插入响应状态码: 200
2025-05-24 22:30:43,724 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 14:30:46 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '435', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1D7FC42F-4C8A-7123-9656-12283E63B1C9', 'x-acs-trace-id': 'f0f13263505b40eb721b13c5d432d5eb', 'etag': '4VBidZTTAzYjnqqia7dcyRw5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 22:30:43,724 - INFO - 批量插入响应体: {'result': ['FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMK', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BML', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMM', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMN', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMO', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMP', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMQ', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMR', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMS']}
2025-05-24 22:30:43,724 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-05-24 22:30:43,724 - INFO - 成功插入的数据ID: ['FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMK', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BML', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMM', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMN', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMO', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMP', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMQ', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMR', 'FINST-T9D66B810GOVHU0V8GZNP4KPAAK329EHUB2BMS']
2025-05-24 22:30:48,739 - INFO - 批量插入完成，共 9 条记录
2025-05-24 22:30:48,739 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 9 条，错误: 0 条
2025-05-24 22:30:48,739 - INFO - 数据同步完成！更新: 0 条，插入: 9 条，错误: 1 条
2025-05-24 22:31:48,741 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 22:31:48,741 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 22:31:48,741 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 22:31:48,819 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 549 条记录
2025-05-24 22:31:48,819 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 22:31:48,819 - INFO - 开始处理日期: 2025-05-23
2025-05-24 22:31:48,819 - INFO - Request Parameters - Page 1:
2025-05-24 22:31:48,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 22:31:48,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 22:31:56,943 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DB6A414B-6FA2-7CE1-948B-7CB8D7B679D2 Response: {'code': 'ServiceUnavailable', 'requestid': 'DB6A414B-6FA2-7CE1-948B-7CB8D7B679D2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DB6A414B-6FA2-7CE1-948B-7CB8D7B679D2)
2025-05-24 22:31:56,943 - INFO - 开始处理日期: 2025-05-24
2025-05-24 22:31:56,943 - INFO - Request Parameters - Page 1:
2025-05-24 22:31:56,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 22:31:56,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 22:32:03,722 - INFO - Response - Page 1:
2025-05-24 22:32:03,722 - INFO - 第 1 页获取到 11 条记录
2025-05-24 22:32:03,926 - INFO - 查询完成，共获取到 11 条记录
2025-05-24 22:32:03,926 - INFO - 获取到 11 条表单数据
2025-05-24 22:32:03,926 - INFO - 当前日期 2025-05-24 有 11 条MySQL数据需要处理
2025-05-24 22:32:03,926 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-24 22:32:03,926 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-24 22:32:03,926 - INFO - 同步完成
2025-05-24 23:30:33,764 - INFO - 使用默认增量同步（当天更新数据）
2025-05-24 23:30:33,764 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 23:30:33,764 - INFO - 查询参数: ('2025-05-24',)
2025-05-24 23:30:33,842 - INFO - MySQL查询成功，增量数据（日期: 2025-05-24），共获取 238 条记录
2025-05-24 23:30:33,842 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 23:30:33,842 - INFO - 开始处理日期: 2025-05-23
2025-05-24 23:30:33,842 - INFO - Request Parameters - Page 1:
2025-05-24 23:30:33,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 23:30:33,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 23:30:41,967 - ERROR - 处理日期 2025-05-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 591D76F6-F66A-7588-A355-CAD10E781ADC Response: {'code': 'ServiceUnavailable', 'requestid': '591D76F6-F66A-7588-A355-CAD10E781ADC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 591D76F6-F66A-7588-A355-CAD10E781ADC)
2025-05-24 23:30:41,967 - INFO - 开始处理日期: 2025-05-24
2025-05-24 23:30:41,967 - INFO - Request Parameters - Page 1:
2025-05-24 23:30:41,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 23:30:41,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 23:30:50,092 - ERROR - 处理日期 2025-05-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DBC873F1-B3E2-7072-814A-F0273AB8E31B Response: {'code': 'ServiceUnavailable', 'requestid': 'DBC873F1-B3E2-7072-814A-F0273AB8E31B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DBC873F1-B3E2-7072-814A-F0273AB8E31B)
2025-05-24 23:30:50,092 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-24 23:31:50,107 - INFO - 开始同步昨天与今天的销售数据: 2025-05-23 至 2025-05-24
2025-05-24 23:31:50,107 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-24 23:31:50,107 - INFO - 查询参数: ('2025-05-23', '2025-05-24')
2025-05-24 23:31:50,185 - INFO - MySQL查询成功，时间段: 2025-05-23 至 2025-05-24，共获取 595 条记录
2025-05-24 23:31:50,185 - INFO - 获取到 2 个日期需要处理: ['2025-05-23', '2025-05-24']
2025-05-24 23:31:50,185 - INFO - 开始处理日期: 2025-05-23
2025-05-24 23:31:50,185 - INFO - Request Parameters - Page 1:
2025-05-24 23:31:50,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 23:31:50,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 23:31:51,060 - INFO - Response - Page 1:
2025-05-24 23:31:51,060 - INFO - 第 1 页获取到 100 条记录
2025-05-24 23:31:51,263 - INFO - Request Parameters - Page 2:
2025-05-24 23:31:51,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 23:31:51,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 23:31:51,998 - INFO - Response - Page 2:
2025-05-24 23:31:51,998 - INFO - 第 2 页获取到 100 条记录
2025-05-24 23:31:52,201 - INFO - Request Parameters - Page 3:
2025-05-24 23:31:52,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 23:31:52,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 23:31:52,982 - INFO - Response - Page 3:
2025-05-24 23:31:52,982 - INFO - 第 3 页获取到 100 条记录
2025-05-24 23:31:53,185 - INFO - Request Parameters - Page 4:
2025-05-24 23:31:53,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 23:31:53,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 23:31:53,982 - INFO - Response - Page 4:
2025-05-24 23:31:53,982 - INFO - 第 4 页获取到 100 条记录
2025-05-24 23:31:54,185 - INFO - Request Parameters - Page 5:
2025-05-24 23:31:54,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 23:31:54,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 23:31:55,185 - INFO - Response - Page 5:
2025-05-24 23:31:55,185 - INFO - 第 5 页获取到 100 条记录
2025-05-24 23:31:55,388 - INFO - Request Parameters - Page 6:
2025-05-24 23:31:55,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 23:31:55,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 23:31:56,013 - INFO - Response - Page 6:
2025-05-24 23:31:56,013 - INFO - 第 6 页获取到 21 条记录
2025-05-24 23:31:56,216 - INFO - 查询完成，共获取到 521 条记录
2025-05-24 23:31:56,216 - INFO - 获取到 521 条表单数据
2025-05-24 23:31:56,216 - INFO - 当前日期 2025-05-23 有 538 条MySQL数据需要处理
2025-05-24 23:31:56,232 - INFO - 开始批量插入 17 条新记录
2025-05-24 23:31:56,404 - INFO - 批量插入响应状态码: 200
2025-05-24 23:31:56,404 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 15:31:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '814', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D3710A83-CBD0-7342-9889-90065093FA61', 'x-acs-trace-id': '022613b3558fe9255b2389f6f9bff9fd', 'etag': '8ore64tCdxoWszZo/YFWpBA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 23:31:56,404 - INFO - 批量插入响应体: {'result': ['FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMM', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMN', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMO', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMP', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMQ', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMR', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMS', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMT', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMU', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMV', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMW', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMX', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMY', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMZ', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BM01', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BM11', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BM21']}
2025-05-24 23:31:56,404 - INFO - 批量插入表单数据成功，批次 1，共 17 条记录
2025-05-24 23:31:56,404 - INFO - 成功插入的数据ID: ['FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMM', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMN', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMO', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMP', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMQ', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMR', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMS', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMT', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMU', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMV', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMW', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMX', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMY', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BMZ', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BM01', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BM11', 'FINST-3PF66271MKOVCA1S8PNI14I1GI3Y26D51E2BM21']
2025-05-24 23:32:01,419 - INFO - 批量插入完成，共 17 条记录
2025-05-24 23:32:01,419 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 17 条，错误: 0 条
2025-05-24 23:32:01,419 - INFO - 开始处理日期: 2025-05-24
2025-05-24 23:32:01,419 - INFO - Request Parameters - Page 1:
2025-05-24 23:32:01,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-24 23:32:01,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748016000000, 1748102399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-24 23:32:01,982 - INFO - Response - Page 1:
2025-05-24 23:32:01,982 - INFO - 第 1 页获取到 11 条记录
2025-05-24 23:32:02,185 - INFO - 查询完成，共获取到 11 条记录
2025-05-24 23:32:02,185 - INFO - 获取到 11 条表单数据
2025-05-24 23:32:02,185 - INFO - 当前日期 2025-05-24 有 57 条MySQL数据需要处理
2025-05-24 23:32:02,185 - INFO - 开始批量插入 46 条新记录
2025-05-24 23:32:02,388 - INFO - 批量插入响应状态码: 200
2025-05-24 23:32:02,388 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 24 May 2025 15:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2220', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0A9992A4-A688-7737-B8B3-6B4CB3D3C784', 'x-acs-trace-id': 'c41f27185a0fe2f6047969607abe5f58', 'etag': '2iReQi6NNBRMvagMnX6h6Qw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-24 23:32:02,388 - INFO - 批量插入响应体: {'result': ['FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMCA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMDA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMEA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMFA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMGA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMHA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMIA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMJA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMKA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMLA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMMA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMNA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMOA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMPA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMQA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMRA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMSA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMTA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMUA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMVA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMWA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMXA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMYA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMZA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM0B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM1B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM2B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM3B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM4B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM5B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM6B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM7B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM8B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM9B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMAB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMBB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMCB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMDB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMEB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMFB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMGB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMHB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMIB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMJB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMKB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMLB']}
2025-05-24 23:32:02,388 - INFO - 批量插入表单数据成功，批次 1，共 46 条记录
2025-05-24 23:32:02,388 - INFO - 成功插入的数据ID: ['FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMCA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMDA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMEA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMFA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMGA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMHA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMIA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMJA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMKA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMLA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMMA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMNA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMOA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMPA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMQA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMRA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMSA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMTA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMUA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMVA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMWA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMXA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMYA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMZA', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM0B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM1B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM2B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM3B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM4B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM5B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM6B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM7B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM8B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BM9B', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMAB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMBB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMCB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMDB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMEB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMFB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMGB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMHB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMIB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMJB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMKB', 'FINST-IQE66ZC10GOVJ9VT8ZDCE448OX8M2HZ91E2BMLB']
2025-05-24 23:32:07,404 - INFO - 批量插入完成，共 46 条记录
2025-05-24 23:32:07,404 - INFO - 日期 2025-05-24 处理完成 - 更新: 0 条，插入: 46 条，错误: 0 条
2025-05-24 23:32:07,404 - INFO - 数据同步完成！更新: 0 条，插入: 63 条，错误: 0 条
2025-05-24 23:32:07,404 - INFO - 同步完成
