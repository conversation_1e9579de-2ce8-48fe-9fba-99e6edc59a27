2025-06-16 01:30:34,836 - INFO - 使用默认增量同步（当天更新数据）
2025-06-16 01:30:34,836 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-16 01:30:34,836 - INFO - 查询参数: ('2025-06-16',)
2025-06-16 01:30:34,914 - INFO - MySQL查询成功，增量数据（日期: 2025-06-16），共获取 0 条记录
2025-06-16 01:30:34,914 - ERROR - 未获取到MySQL数据
2025-06-16 01:31:34,933 - INFO - 开始同步昨天与今天的销售数据: 2025-06-15 至 2025-06-16
2025-06-16 01:31:34,933 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-16 01:31:34,933 - INFO - 查询参数: ('2025-06-15', '2025-06-16')
2025-06-16 01:31:35,058 - INFO - MySQL查询成功，时间段: 2025-06-15 至 2025-06-16，共获取 97 条记录
2025-06-16 01:31:35,058 - INFO - 获取到 1 个日期需要处理: ['2025-06-15']
2025-06-16 01:31:35,058 - INFO - 开始处理日期: 2025-06-15
2025-06-16 01:31:35,058 - INFO - Request Parameters - Page 1:
2025-06-16 01:31:35,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 01:31:35,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 01:31:43,184 - ERROR - 处理日期 2025-06-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3EA5036C-572F-7D74-984B-233E9916A9B8 Response: {'code': 'ServiceUnavailable', 'requestid': '3EA5036C-572F-7D74-984B-233E9916A9B8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3EA5036C-572F-7D74-984B-233E9916A9B8)
2025-06-16 01:31:43,184 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-16 01:31:43,184 - INFO - 同步完成
2025-06-16 04:30:33,790 - INFO - 使用默认增量同步（当天更新数据）
2025-06-16 04:30:33,790 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-16 04:30:33,790 - INFO - 查询参数: ('2025-06-16',)
2025-06-16 04:30:33,869 - INFO - MySQL查询成功，增量数据（日期: 2025-06-16），共获取 0 条记录
2025-06-16 04:30:33,869 - ERROR - 未获取到MySQL数据
2025-06-16 04:31:33,888 - INFO - 开始同步昨天与今天的销售数据: 2025-06-15 至 2025-06-16
2025-06-16 04:31:33,888 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-16 04:31:33,888 - INFO - 查询参数: ('2025-06-15', '2025-06-16')
2025-06-16 04:31:34,029 - INFO - MySQL查询成功，时间段: 2025-06-15 至 2025-06-16，共获取 97 条记录
2025-06-16 04:31:34,029 - INFO - 获取到 1 个日期需要处理: ['2025-06-15']
2025-06-16 04:31:34,029 - INFO - 开始处理日期: 2025-06-15
2025-06-16 04:31:34,029 - INFO - Request Parameters - Page 1:
2025-06-16 04:31:34,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 04:31:34,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 04:31:42,154 - ERROR - 处理日期 2025-06-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AB7E0AEE-E622-7979-ABD3-AFC9DBB90BCE Response: {'code': 'ServiceUnavailable', 'requestid': 'AB7E0AEE-E622-7979-ABD3-AFC9DBB90BCE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AB7E0AEE-E622-7979-ABD3-AFC9DBB90BCE)
2025-06-16 04:31:42,154 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-16 04:31:42,154 - INFO - 同步完成
2025-06-16 07:30:33,755 - INFO - 使用默认增量同步（当天更新数据）
2025-06-16 07:30:33,755 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-16 07:30:33,755 - INFO - 查询参数: ('2025-06-16',)
2025-06-16 07:30:33,833 - INFO - MySQL查询成功，增量数据（日期: 2025-06-16），共获取 0 条记录
2025-06-16 07:30:33,833 - ERROR - 未获取到MySQL数据
2025-06-16 07:31:33,849 - INFO - 开始同步昨天与今天的销售数据: 2025-06-15 至 2025-06-16
2025-06-16 07:31:33,849 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-16 07:31:33,849 - INFO - 查询参数: ('2025-06-15', '2025-06-16')
2025-06-16 07:31:33,974 - INFO - MySQL查询成功，时间段: 2025-06-15 至 2025-06-16，共获取 97 条记录
2025-06-16 07:31:33,974 - INFO - 获取到 1 个日期需要处理: ['2025-06-15']
2025-06-16 07:31:33,974 - INFO - 开始处理日期: 2025-06-15
2025-06-16 07:31:33,974 - INFO - Request Parameters - Page 1:
2025-06-16 07:31:33,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 07:31:33,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 07:31:42,083 - ERROR - 处理日期 2025-06-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 556C8486-DA3D-7B98-830E-2E82060E7392 Response: {'code': 'ServiceUnavailable', 'requestid': '556C8486-DA3D-7B98-830E-2E82060E7392', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 556C8486-DA3D-7B98-830E-2E82060E7392)
2025-06-16 07:31:42,083 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-16 07:31:42,083 - INFO - 同步完成
2025-06-16 10:30:33,709 - INFO - 使用默认增量同步（当天更新数据）
2025-06-16 10:30:33,709 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-16 10:30:33,709 - INFO - 查询参数: ('2025-06-16',)
2025-06-16 10:30:33,849 - INFO - MySQL查询成功，增量数据（日期: 2025-06-16），共获取 176 条记录
2025-06-16 10:30:33,849 - INFO - 获取到 4 个日期需要处理: ['2025-06-13', '2025-06-14', '2025-06-15', '2025-06-16']
2025-06-16 10:30:33,849 - INFO - 开始处理日期: 2025-06-13
2025-06-16 10:30:33,849 - INFO - Request Parameters - Page 1:
2025-06-16 10:30:33,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:30:33,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:30:41,974 - ERROR - 处理日期 2025-06-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 80208404-019B-7123-96DA-7A835AEC7431 Response: {'code': 'ServiceUnavailable', 'requestid': '80208404-019B-7123-96DA-7A835AEC7431', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 80208404-019B-7123-96DA-7A835AEC7431)
2025-06-16 10:30:41,974 - INFO - 开始处理日期: 2025-06-14
2025-06-16 10:30:41,974 - INFO - Request Parameters - Page 1:
2025-06-16 10:30:41,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:30:41,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:30:43,021 - INFO - Response - Page 1:
2025-06-16 10:30:43,021 - INFO - 第 1 页获取到 50 条记录
2025-06-16 10:30:43,537 - INFO - Request Parameters - Page 2:
2025-06-16 10:30:43,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:30:43,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:30:49,177 - INFO - Response - Page 2:
2025-06-16 10:30:49,177 - INFO - 第 2 页获取到 50 条记录
2025-06-16 10:30:49,693 - INFO - Request Parameters - Page 3:
2025-06-16 10:30:49,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:30:49,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:30:50,349 - INFO - Response - Page 3:
2025-06-16 10:30:50,349 - INFO - 第 3 页获取到 50 条记录
2025-06-16 10:30:50,865 - INFO - Request Parameters - Page 4:
2025-06-16 10:30:50,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:30:50,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:30:51,521 - INFO - Response - Page 4:
2025-06-16 10:30:51,521 - INFO - 第 4 页获取到 50 条记录
2025-06-16 10:30:52,037 - INFO - Request Parameters - Page 5:
2025-06-16 10:30:52,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:30:52,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:30:52,724 - INFO - Response - Page 5:
2025-06-16 10:30:52,724 - INFO - 第 5 页获取到 50 条记录
2025-06-16 10:30:53,240 - INFO - Request Parameters - Page 6:
2025-06-16 10:30:53,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:30:53,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:30:53,912 - INFO - Response - Page 6:
2025-06-16 10:30:53,912 - INFO - 第 6 页获取到 50 条记录
2025-06-16 10:30:54,427 - INFO - Request Parameters - Page 7:
2025-06-16 10:30:54,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:30:54,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:30:55,177 - INFO - Response - Page 7:
2025-06-16 10:30:55,177 - INFO - 第 7 页获取到 50 条记录
2025-06-16 10:30:55,677 - INFO - Request Parameters - Page 8:
2025-06-16 10:30:55,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:30:55,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:30:56,318 - INFO - Response - Page 8:
2025-06-16 10:30:56,318 - INFO - 第 8 页获取到 50 条记录
2025-06-16 10:30:56,818 - INFO - Request Parameters - Page 9:
2025-06-16 10:30:56,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:30:56,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:30:57,396 - INFO - Response - Page 9:
2025-06-16 10:30:57,412 - INFO - 第 9 页获取到 33 条记录
2025-06-16 10:30:57,927 - INFO - 查询完成，共获取到 433 条记录
2025-06-16 10:30:57,927 - INFO - 获取到 433 条表单数据
2025-06-16 10:30:57,927 - INFO - 当前日期 2025-06-14 有 4 条MySQL数据需要处理
2025-06-16 10:30:57,927 - INFO - 开始批量插入 4 条新记录
2025-06-16 10:30:58,083 - INFO - 批量插入响应状态码: 200
2025-06-16 10:30:58,083 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 02:31:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '208', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '79FA5869-E41A-7FE3-A1EF-2125279EEBDC', 'x-acs-trace-id': '982cafdf971e0ca3f7fbad4fefe15654', 'etag': '2QsnRh2mZuKYrx2nliXAB6w8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 10:30:58,083 - INFO - 批量插入响应体: {'result': ['FINST-737662B1BT9WLOFC95TES8262A983NRG9HYBMW41', 'FINST-737662B1BT9WLOFC95TES8262A983NRG9HYBMX41', 'FINST-737662B1BT9WLOFC95TES8262A983NRG9HYBMY41', 'FINST-737662B1BT9WLOFC95TES8262A983NRG9HYBMZ41']}
2025-06-16 10:30:58,083 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-06-16 10:30:58,083 - INFO - 成功插入的数据ID: ['FINST-737662B1BT9WLOFC95TES8262A983NRG9HYBMW41', 'FINST-737662B1BT9WLOFC95TES8262A983NRG9HYBMX41', 'FINST-737662B1BT9WLOFC95TES8262A983NRG9HYBMY41', 'FINST-737662B1BT9WLOFC95TES8262A983NRG9HYBMZ41']
2025-06-16 10:31:03,099 - INFO - 批量插入完成，共 4 条记录
2025-06-16 10:31:03,099 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-06-16 10:31:03,099 - INFO - 开始处理日期: 2025-06-15
2025-06-16 10:31:03,099 - INFO - Request Parameters - Page 1:
2025-06-16 10:31:03,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:31:03,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:31:03,724 - INFO - Response - Page 1:
2025-06-16 10:31:03,724 - INFO - 第 1 页获取到 50 条记录
2025-06-16 10:31:04,240 - INFO - Request Parameters - Page 2:
2025-06-16 10:31:04,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:31:04,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:31:04,880 - INFO - Response - Page 2:
2025-06-16 10:31:04,880 - INFO - 第 2 页获取到 33 条记录
2025-06-16 10:31:05,396 - INFO - 查询完成，共获取到 83 条记录
2025-06-16 10:31:05,396 - INFO - 获取到 83 条表单数据
2025-06-16 10:31:05,396 - INFO - 当前日期 2025-06-15 有 152 条MySQL数据需要处理
2025-06-16 10:31:05,396 - INFO - 开始批量插入 152 条新记录
2025-06-16 10:31:05,615 - INFO - 批量插入响应状态码: 200
2025-06-16 10:31:05,615 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 02:31:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B14988B5-E566-7FAB-A1F3-E6B25B26A47C', 'x-acs-trace-id': 'd3b5cac7e10b1c6c6ef0b351694284f4', 'etag': '2jVX9XRQ6IVn5hHCLACPuqw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 10:31:05,615 - INFO - 批量插入响应体: {'result': ['FINST-3PF66X612T9WOAL5FSZ3NCBZPASF36LM9HYBMR2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMS2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMT2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMU2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMV2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMW2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMX2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMY2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMZ2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM03', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM13', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM23', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM33', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM43', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM53', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM63', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM73', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM83', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM93', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMA3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMB3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMC3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMD3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBME3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMF3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMG3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMH3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMI3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMJ3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMK3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBML3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMM3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMN3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMO3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMP3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMQ3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMR3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMS3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMT3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMU3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMV3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMW3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMX3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMY3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMZ3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM04', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM14', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM24', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM34', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM44']}
2025-06-16 10:31:05,615 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-16 10:31:05,615 - INFO - 成功插入的数据ID: ['FINST-3PF66X612T9WOAL5FSZ3NCBZPASF36LM9HYBMR2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMS2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMT2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMU2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMV2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMW2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMX2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMY2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMZ2', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM03', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM13', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM23', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM33', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM43', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM53', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM63', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM73', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM83', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM93', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMA3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMB3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMC3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMD3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBME3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMF3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMG3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMH3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMI3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMJ3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMK3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBML3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMM3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMN3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMO3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMP3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMQ3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMR3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMS3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMT3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMU3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMV3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMW3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMX3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMY3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMZ3', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM04', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM14', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM24', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM34', 'FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBM44']
2025-06-16 10:31:10,880 - INFO - 批量插入响应状态码: 200
2025-06-16 10:31:10,880 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 02:31:13 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E0C08725-80D2-772D-9F94-BC7529454A3C', 'x-acs-trace-id': '1b4b73a2558f226dd162ad7fa75b039f', 'etag': '2J4VJ386wh7GgI8FRGKXPjg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 10:31:10,880 - INFO - 批量插入响应体: {'result': ['FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMN1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMO1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMP1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMQ1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMR1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMS1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMT1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMU1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMV1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMW1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMX1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMY1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMZ1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM02', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM12', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM22', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM32', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM42', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM52', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM62', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM72', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM82', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM92', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMA2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMB2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMC2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMD2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBME2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMF2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMG2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMH2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMI2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMJ2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMK2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBML2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMM2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMN2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMO2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMP2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMQ2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMR2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMS2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMT2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMU2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMV2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMW2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMX2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMY2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMZ2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM03']}
2025-06-16 10:31:10,880 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-16 10:31:10,880 - INFO - 成功插入的数据ID: ['FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMN1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMO1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMP1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMQ1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMR1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMS1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMT1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMU1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMV1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMW1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMX1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMY1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMZ1', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM02', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM12', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM22', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM32', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM42', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM52', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM62', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM72', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM82', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM92', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMA2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMB2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMC2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMD2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBME2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMF2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMG2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMH2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMI2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMJ2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMK2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBML2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMM2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMN2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMO2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMP2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMQ2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMR2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMS2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMT2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMU2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMV2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMW2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMX2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMY2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBMZ2', 'FINST-K7666JC1OH9W79C68NBU2D64R0FX21NQ9HYBM03']
2025-06-16 10:31:16,146 - INFO - 批量插入响应状态码: 200
2025-06-16 10:31:16,146 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 02:31:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2386', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CC57F851-E271-78DB-AF9E-DFF22BDDE2B2', 'x-acs-trace-id': '14382dfa2fcad54f294cfdabd6bb9de3', 'etag': '2ZszxAG4mvjDS8tmu6cBMNQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 10:31:16,146 - INFO - 批量插入响应体: {'result': ['FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMA', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMB', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMC', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMD', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBME', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMF', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMG', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMH', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMI', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMJ', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMK', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBML', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMM', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMN', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMO', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMP', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMQ', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMR', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMS', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMT', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMU', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMV', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMW', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMX', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMY', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMZ', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM01', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM11', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM21', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM31', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM41', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM51', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM61', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM71', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM81', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM91', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMA1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMB1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMC1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMD1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBME1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMF1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMG1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMH1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMI1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMJ1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMK1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBML1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMM1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMN1']}
2025-06-16 10:31:16,146 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-16 10:31:16,146 - INFO - 成功插入的数据ID: ['FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMA', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMB', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMC', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMD', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBME', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMF', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMG', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMH', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMI', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMJ', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMK', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBML', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMM', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMN', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMO', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMP', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMQ', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMR', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMS', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMT', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMU', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMV', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMW', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMX', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMY', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMZ', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM01', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM11', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM21', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM31', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM41', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM51', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM61', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM71', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM81', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBM91', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMA1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMB1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMC1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMD1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBME1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMF1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMG1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMH1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMI1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMJ1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMK1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBML1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMM1', 'FINST-IOC66G71MICW5DIEE36A67U1VB0Q3GPU9HYBMN1']
2025-06-16 10:31:21,302 - INFO - 批量插入响应状态码: 200
2025-06-16 10:31:21,302 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 02:31:24 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FD407335-1571-73AC-BAC2-909F08B05B2A', 'x-acs-trace-id': '47f1943482b5416dac71f532c4cad3e2', 'etag': '1zbuYz2PwPEROmYX5x/JFbA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 10:31:21,302 - INFO - 批量插入响应体: {'result': ['FINST-IQE66ZC1YFAWKHZS7I0QACYGREF63POY9HYBMII', 'FINST-IQE66ZC1YFAWKHZS7I0QACYGREF63POY9HYBMJI']}
2025-06-16 10:31:21,302 - INFO - 批量插入表单数据成功，批次 4，共 2 条记录
2025-06-16 10:31:21,302 - INFO - 成功插入的数据ID: ['FINST-IQE66ZC1YFAWKHZS7I0QACYGREF63POY9HYBMII', 'FINST-IQE66ZC1YFAWKHZS7I0QACYGREF63POY9HYBMJI']
2025-06-16 10:31:26,318 - INFO - 批量插入完成，共 152 条记录
2025-06-16 10:31:26,318 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 152 条，错误: 0 条
2025-06-16 10:31:26,318 - INFO - 开始处理日期: 2025-06-16
2025-06-16 10:31:26,318 - INFO - Request Parameters - Page 1:
2025-06-16 10:31:26,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:31:26,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:31:26,708 - INFO - Response - Page 1:
2025-06-16 10:31:26,708 - INFO - 查询完成，共获取到 0 条记录
2025-06-16 10:31:26,708 - INFO - 获取到 0 条表单数据
2025-06-16 10:31:26,708 - INFO - 当前日期 2025-06-16 有 1 条MySQL数据需要处理
2025-06-16 10:31:26,708 - INFO - 开始批量插入 1 条新记录
2025-06-16 10:31:26,864 - INFO - 批量插入响应状态码: 200
2025-06-16 10:31:26,864 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 02:31:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FE32E80E-BB9D-7667-8B00-0E9F8B4FDB3E', 'x-acs-trace-id': '9e0f874e51f52081f672261007e929c9', 'etag': '6l/U8BFdo3gHbBxhTL+W+QA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 10:31:26,864 - INFO - 批量插入响应体: {'result': ['FINST-6PF66691UYBWLR7XE3P5Y72NRDBX2ZY2AHYBM9B']}
2025-06-16 10:31:26,864 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-16 10:31:26,864 - INFO - 成功插入的数据ID: ['FINST-6PF66691UYBWLR7XE3P5Y72NRDBX2ZY2AHYBM9B']
2025-06-16 10:31:31,880 - INFO - 批量插入完成，共 1 条记录
2025-06-16 10:31:31,880 - INFO - 日期 2025-06-16 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-16 10:31:31,880 - INFO - 数据同步完成！更新: 0 条，插入: 157 条，错误: 1 条
2025-06-16 10:32:31,895 - INFO - 开始同步昨天与今天的销售数据: 2025-06-15 至 2025-06-16
2025-06-16 10:32:31,895 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-16 10:32:31,895 - INFO - 查询参数: ('2025-06-15', '2025-06-16')
2025-06-16 10:32:32,020 - INFO - MySQL查询成功，时间段: 2025-06-15 至 2025-06-16，共获取 537 条记录
2025-06-16 10:32:32,036 - INFO - 获取到 2 个日期需要处理: ['2025-06-15', '2025-06-16']
2025-06-16 10:32:32,036 - INFO - 开始处理日期: 2025-06-15
2025-06-16 10:32:32,036 - INFO - Request Parameters - Page 1:
2025-06-16 10:32:32,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:32:32,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:32:32,677 - INFO - Response - Page 1:
2025-06-16 10:32:32,677 - INFO - 第 1 页获取到 50 条记录
2025-06-16 10:32:33,192 - INFO - Request Parameters - Page 2:
2025-06-16 10:32:33,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:32:33,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:32:33,802 - INFO - Response - Page 2:
2025-06-16 10:32:33,802 - INFO - 第 2 页获取到 50 条记录
2025-06-16 10:32:34,302 - INFO - Request Parameters - Page 3:
2025-06-16 10:32:34,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:32:34,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:32:34,864 - INFO - Response - Page 3:
2025-06-16 10:32:34,864 - INFO - 第 3 页获取到 50 条记录
2025-06-16 10:32:35,364 - INFO - Request Parameters - Page 4:
2025-06-16 10:32:35,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:32:35,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:32:35,958 - INFO - Response - Page 4:
2025-06-16 10:32:35,958 - INFO - 第 4 页获取到 50 条记录
2025-06-16 10:32:36,473 - INFO - Request Parameters - Page 5:
2025-06-16 10:32:36,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:32:36,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:32:37,083 - INFO - Response - Page 5:
2025-06-16 10:32:37,083 - INFO - 第 5 页获取到 35 条记录
2025-06-16 10:32:37,598 - INFO - 查询完成，共获取到 235 条记录
2025-06-16 10:32:37,598 - INFO - 获取到 235 条表单数据
2025-06-16 10:32:37,598 - INFO - 当前日期 2025-06-15 有 503 条MySQL数据需要处理
2025-06-16 10:32:37,598 - INFO - 开始批量插入 268 条新记录
2025-06-16 10:32:37,864 - INFO - 批量插入响应状态码: 200
2025-06-16 10:32:37,864 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 02:32:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-EAEE-7548-9215-7CEF89FB7E14', 'x-acs-trace-id': 'eb520d2d3f7176382d903285cf23e6ac', 'etag': '2b/nvWmebflkzq4iUQ7h/6w2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 10:32:37,864 - INFO - 批量插入响应体: {'result': ['FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMR6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMS6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMT6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMU6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMV6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMW6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMX6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMY6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMZ6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM07', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM17', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM27', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM37', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM47', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM57', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM67', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM77', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM87', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM97', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMA7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMB7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMC7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMD7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBME7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMF7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMG7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMH7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMI7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMJ7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMK7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBML7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMM7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMN7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMO7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMP7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMQ7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMR7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMS7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMT7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMU7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMV7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMW7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMX7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMY7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMZ7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM08', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM18', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM28', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM38', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM48']}
2025-06-16 10:32:37,864 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-16 10:32:37,864 - INFO - 成功插入的数据ID: ['FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMR6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMS6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMT6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMU6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMV6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMW6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMX6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMY6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMZ6', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM07', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM17', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM27', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM37', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM47', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM57', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM67', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM77', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM87', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM97', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMA7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMB7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMC7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMD7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBME7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMF7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMG7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMH7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMI7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMJ7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMK7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBML7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMM7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMN7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMO7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMP7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMQ7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMR7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMS7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMT7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMU7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMV7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMW7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMX7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMY7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBMZ7', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM08', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM18', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM28', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM38', 'FINST-A7C66Z71MF9WQ66O6RQLEC0RQH9B3ARLBHYBM48']
2025-06-16 10:32:43,098 - INFO - 批量插入响应状态码: 200
2025-06-16 10:32:43,098 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 02:32:45 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4ABF6549-B617-7574-8B52-488F41406A6C', 'x-acs-trace-id': 'b4f2d79cce0731ca524d765b7a36ad36', 'etag': '2ad8Yg80qpx1PZ1t0wrsSkg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 10:32:43,098 - INFO - 批量插入响应体: {'result': ['FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMNN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMON1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMPN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMQN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMRN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMSN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMTN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMUN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMVN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMWN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMXN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMYN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMZN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM0O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM1O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM2O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM3O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM4O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM5O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM6O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM7O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM8O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM9O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMAO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMBO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMCO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMDO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMEO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMFO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMGO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMHO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMIO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMJO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMKO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMLO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMMO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMNO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMOO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMPO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMQO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMRO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMSO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMTO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMUO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMVO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMWO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMXO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMYO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMZO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBM0P1']}
2025-06-16 10:32:43,098 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-16 10:32:43,098 - INFO - 成功插入的数据ID: ['FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMNN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMON1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMPN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMQN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMRN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMSN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMTN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMUN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMVN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMWN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMXN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMYN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMZN1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM0O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM1O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM2O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM3O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM4O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM5O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM6O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM7O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM8O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBM9O1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMAO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3NSPBHYBMBO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMCO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMDO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMEO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMFO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMGO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMHO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMIO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMJO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMKO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMLO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMMO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMNO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMOO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMPO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMQO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMRO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMSO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMTO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMUO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMVO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMWO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMXO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMYO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBMZO1', 'FINST-2HF66O613P6WGVMX5B4GF7PCF6IL3OSPBHYBM0P1']
2025-06-16 10:32:48,333 - INFO - 批量插入响应状态码: 200
2025-06-16 10:32:48,333 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 02:32:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BAC6C5E2-4BFF-7D97-9154-E635EB17978B', 'x-acs-trace-id': 'b5ee2ddd5dab8873871866f9bee04e56', 'etag': '2F+0LdGqID+pT/8dh0uMS3A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 10:32:48,333 - INFO - 批量插入响应体: {'result': ['FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMMF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMNF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMOF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMPF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMQF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMRF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMSF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMTF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMUF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMVF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMWF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMXF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMYF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMZF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM0G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM1G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM2G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM3G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM4G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM5G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM6G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM7G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM8G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM9G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMAG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMBG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMCG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMDG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMEG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMFG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMGG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMHG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMIG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMJG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMKG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMLG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMMG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMNG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMOG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMPG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMQG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMRG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMSG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMTG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMUG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMVG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMWG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMXG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMYG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMZG']}
2025-06-16 10:32:48,333 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-16 10:32:48,333 - INFO - 成功插入的数据ID: ['FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMMF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMNF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMOF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMPF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMQF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMRF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMSF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMTF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMUF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMVF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMWF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMXF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMYF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMZF', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM0G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM1G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM2G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM3G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM4G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM5G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM6G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM7G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM8G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBM9G', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMAG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMBG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMCG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMDG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMEG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMFG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMGG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMHG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMIG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMJG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMKG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMLG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMMG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMNG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMOG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMPG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMQG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMRG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMSG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMTG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMUG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMVG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMWG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMXG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMYG', 'FINST-LI666MB12G9WVSM86M7FX4DGJU1R3HUTBHYBMZG']
2025-06-16 10:32:53,551 - INFO - 批量插入响应状态码: 200
2025-06-16 10:32:53,551 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 02:32:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4FF0900A-BDB8-788A-BA4A-BF4282B23ECB', 'x-acs-trace-id': 'a3f559830365d799d90719c6c0ccecbf', 'etag': '29x2rMIz12hOY9DU/L/hhig2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 10:32:53,551 - INFO - 批量插入响应体: {'result': ['FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBMZ2', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM03', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM13', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM23', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM33', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM43', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM53', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM63', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM73', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM83', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM93', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMA3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMB3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMC3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMD3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBME3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMF3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMG3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMH3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMI3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMJ3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMK3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBML3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMM3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMN3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMO3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMP3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMQ3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMR3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMS3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMT3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMU3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMV3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMW3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMX3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMY3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMZ3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM04', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM14', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM24', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM34', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM44', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM54', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM64', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM74', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM84', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM94', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMA4', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMB4', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMC4']}
2025-06-16 10:32:53,551 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-06-16 10:32:53,551 - INFO - 成功插入的数据ID: ['FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBMZ2', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM03', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM13', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM23', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM33', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM43', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM53', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM63', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM73', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM83', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3CVXBHYBM93', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMA3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMB3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMC3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMD3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBME3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMF3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMG3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMH3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMI3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMJ3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMK3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBML3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMM3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMN3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMO3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMP3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMQ3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMR3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMS3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMT3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMU3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMV3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMW3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMX3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMY3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMZ3', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM04', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM14', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM24', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM34', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM44', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM54', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM64', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM74', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM84', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBM94', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMA4', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMB4', 'FINST-S0E660A16HAWG3JY7YBCLCZLQM4E3DVXBHYBMC4']
2025-06-16 10:32:58,801 - INFO - 批量插入响应状态码: 200
2025-06-16 10:32:58,801 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 02:33:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2381', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D0CCEA46-4399-7162-874C-642CB689E328', 'x-acs-trace-id': '0e6ab97579df831a3c2d6b01172faed2', 'etag': '2zX3SLb63IF1xQz2EbSgciw1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 10:32:58,801 - INFO - 批量插入响应体: {'result': ['FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM5', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM6', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM7', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM8', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM9', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMA', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMB', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMC', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMD', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBME', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMF', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMG', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMH', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMI', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMJ', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMK', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBML', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMM', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMN', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMO', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMP', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMQ', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMR', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMS', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMT', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMU', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMV', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMW', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMX', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMY', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMZ', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM01', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM11', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM21', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM31', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM41', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM51', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM61', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM71', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM81', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM91', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMA1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMB1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMC1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMD1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBME1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMF1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMG1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMH1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMI1']}
2025-06-16 10:32:58,801 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-06-16 10:32:58,801 - INFO - 成功插入的数据ID: ['FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM5', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM6', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM7', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM8', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM9', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMA', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMB', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMC', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMD', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBME', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMF', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMG', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMH', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMI', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMJ', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMK', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBML', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMM', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMN', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMO', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMP', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMQ', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMR', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMS', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMT', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMU', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMV', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMW', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMX', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMY', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMZ', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM01', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM11', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM21', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM31', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM41', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM51', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM61', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM71', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM81', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBM91', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMA1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMB1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMC1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMD1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBME1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMF1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMG1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMH1', 'FINST-QZE668D1JHCWMRBG8LZERBXIFAK630X1CHYBMI1']
2025-06-16 10:33:04,020 - INFO - 批量插入响应状态码: 200
2025-06-16 10:33:04,020 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 02:33:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '858', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6A4F7A78-FACD-758D-83E7-5E90CB8602DC', 'x-acs-trace-id': 'edc364cfd347953c6c1327232d87b0e2', 'etag': '8d2+8J5pxzXo5EguzGr7VXg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 10:33:04,020 - INFO - 批量插入响应体: {'result': ['FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMC', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMD', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBME', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMF', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMG', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMH', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMI', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMJ', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMK', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBML', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMM', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMN', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMO', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMP', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMQ', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMR', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMS', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMT']}
2025-06-16 10:33:04,020 - INFO - 批量插入表单数据成功，批次 6，共 18 条记录
2025-06-16 10:33:04,020 - INFO - 成功插入的数据ID: ['FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMC', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMD', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBME', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMF', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMG', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMH', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMI', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMJ', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMK', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBML', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMM', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMN', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMO', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMP', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMQ', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMR', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMS', 'FINST-XBF66071RICWMY61CIEDI6W9E7C43ZX5CHYBMT']
2025-06-16 10:33:09,036 - INFO - 批量插入完成，共 268 条记录
2025-06-16 10:33:09,036 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 268 条，错误: 0 条
2025-06-16 10:33:09,036 - INFO - 开始处理日期: 2025-06-16
2025-06-16 10:33:09,036 - INFO - Request Parameters - Page 1:
2025-06-16 10:33:09,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 10:33:09,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 10:33:09,520 - INFO - Response - Page 1:
2025-06-16 10:33:09,520 - INFO - 第 1 页获取到 1 条记录
2025-06-16 10:33:10,036 - INFO - 查询完成，共获取到 1 条记录
2025-06-16 10:33:10,036 - INFO - 获取到 1 条表单数据
2025-06-16 10:33:10,036 - INFO - 当前日期 2025-06-16 有 1 条MySQL数据需要处理
2025-06-16 10:33:10,036 - INFO - 日期 2025-06-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 10:33:10,036 - INFO - 数据同步完成！更新: 0 条，插入: 268 条，错误: 0 条
2025-06-16 10:33:10,036 - INFO - 同步完成
2025-06-16 13:30:33,668 - INFO - 使用默认增量同步（当天更新数据）
2025-06-16 13:30:33,668 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-16 13:30:33,668 - INFO - 查询参数: ('2025-06-16',)
2025-06-16 13:30:33,793 - INFO - MySQL查询成功，增量数据（日期: 2025-06-16），共获取 176 条记录
2025-06-16 13:30:33,793 - INFO - 获取到 4 个日期需要处理: ['2025-06-13', '2025-06-14', '2025-06-15', '2025-06-16']
2025-06-16 13:30:33,809 - INFO - 开始处理日期: 2025-06-13
2025-06-16 13:30:33,809 - INFO - Request Parameters - Page 1:
2025-06-16 13:30:33,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:30:33,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:30:39,590 - INFO - Response - Page 1:
2025-06-16 13:30:39,590 - INFO - 第 1 页获取到 50 条记录
2025-06-16 13:30:40,106 - INFO - Request Parameters - Page 2:
2025-06-16 13:30:40,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:30:40,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:30:40,762 - INFO - Response - Page 2:
2025-06-16 13:30:40,762 - INFO - 第 2 页获取到 50 条记录
2025-06-16 13:30:41,262 - INFO - Request Parameters - Page 3:
2025-06-16 13:30:41,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:30:41,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:30:49,387 - INFO - Response - Page 3:
2025-06-16 13:30:49,387 - INFO - 第 3 页获取到 50 条记录
2025-06-16 13:30:49,887 - INFO - Request Parameters - Page 4:
2025-06-16 13:30:49,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:30:49,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:30:50,543 - INFO - Response - Page 4:
2025-06-16 13:30:50,543 - INFO - 第 4 页获取到 50 条记录
2025-06-16 13:30:51,059 - INFO - Request Parameters - Page 5:
2025-06-16 13:30:51,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:30:51,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:30:51,684 - INFO - Response - Page 5:
2025-06-16 13:30:51,684 - INFO - 第 5 页获取到 50 条记录
2025-06-16 13:30:52,199 - INFO - Request Parameters - Page 6:
2025-06-16 13:30:52,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:30:52,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:30:52,856 - INFO - Response - Page 6:
2025-06-16 13:30:52,856 - INFO - 第 6 页获取到 50 条记录
2025-06-16 13:30:53,371 - INFO - Request Parameters - Page 7:
2025-06-16 13:30:53,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:30:53,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:30:54,059 - INFO - Response - Page 7:
2025-06-16 13:30:54,059 - INFO - 第 7 页获取到 50 条记录
2025-06-16 13:30:54,559 - INFO - Request Parameters - Page 8:
2025-06-16 13:30:54,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:30:54,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:30:55,246 - INFO - Response - Page 8:
2025-06-16 13:30:55,246 - INFO - 第 8 页获取到 50 条记录
2025-06-16 13:30:55,746 - INFO - Request Parameters - Page 9:
2025-06-16 13:30:55,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:30:55,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:30:56,449 - INFO - Response - Page 9:
2025-06-16 13:30:56,449 - INFO - 第 9 页获取到 50 条记录
2025-06-16 13:30:56,949 - INFO - Request Parameters - Page 10:
2025-06-16 13:30:56,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:30:56,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:30:57,528 - INFO - Response - Page 10:
2025-06-16 13:30:57,528 - INFO - 第 10 页获取到 26 条记录
2025-06-16 13:30:58,043 - INFO - 查询完成，共获取到 476 条记录
2025-06-16 13:30:58,043 - INFO - 获取到 476 条表单数据
2025-06-16 13:30:58,043 - INFO - 当前日期 2025-06-13 有 4 条MySQL数据需要处理
2025-06-16 13:30:58,043 - INFO - 开始更新记录 - 表单实例ID: FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMGD
2025-06-16 13:30:58,512 - INFO - 更新表单数据成功: FINST-FIG66R816T9W0TJY8CF8LDRMLZ382R4REMVBMGD
2025-06-16 13:30:58,512 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4.0, 'new_value': 49098.0}, {'field': 'total_amount', 'old_value': 4.0, 'new_value': 49098.0}, {'field': 'order_count', 'old_value': 49098, 'new_value': 4}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-16 13:30:58,512 - INFO - 开始批量插入 3 条新记录
2025-06-16 13:30:58,653 - INFO - 批量插入响应状态码: 200
2025-06-16 13:30:58,653 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 05:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3AA02E65-99F1-775C-B558-40D1B06D7B7A', 'x-acs-trace-id': '7f73f0479d8371d0c3124b6e18919d6f', 'etag': '10D98ce77NviS1M3F8pSRBw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 13:30:58,653 - INFO - 批量插入响应体: {'result': ['FINST-XO8662C17Q6WEER88VT126P27PXY2SLYONYBMNJ', 'FINST-XO8662C17Q6WEER88VT126P27PXY2SLYONYBMOJ', 'FINST-XO8662C17Q6WEER88VT126P27PXY2SLYONYBMPJ']}
2025-06-16 13:30:58,653 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-06-16 13:30:58,653 - INFO - 成功插入的数据ID: ['FINST-XO8662C17Q6WEER88VT126P27PXY2SLYONYBMNJ', 'FINST-XO8662C17Q6WEER88VT126P27PXY2SLYONYBMOJ', 'FINST-XO8662C17Q6WEER88VT126P27PXY2SLYONYBMPJ']
2025-06-16 13:31:03,668 - INFO - 批量插入完成，共 3 条记录
2025-06-16 13:31:03,668 - INFO - 日期 2025-06-13 处理完成 - 更新: 1 条，插入: 3 条，错误: 0 条
2025-06-16 13:31:03,668 - INFO - 开始处理日期: 2025-06-14
2025-06-16 13:31:03,668 - INFO - Request Parameters - Page 1:
2025-06-16 13:31:03,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:03,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:04,309 - INFO - Response - Page 1:
2025-06-16 13:31:04,309 - INFO - 第 1 页获取到 50 条记录
2025-06-16 13:31:04,824 - INFO - Request Parameters - Page 2:
2025-06-16 13:31:04,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:04,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:05,496 - INFO - Response - Page 2:
2025-06-16 13:31:05,496 - INFO - 第 2 页获取到 50 条记录
2025-06-16 13:31:05,996 - INFO - Request Parameters - Page 3:
2025-06-16 13:31:05,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:05,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:06,637 - INFO - Response - Page 3:
2025-06-16 13:31:06,637 - INFO - 第 3 页获取到 50 条记录
2025-06-16 13:31:07,137 - INFO - Request Parameters - Page 4:
2025-06-16 13:31:07,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:07,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:07,746 - INFO - Response - Page 4:
2025-06-16 13:31:07,746 - INFO - 第 4 页获取到 50 条记录
2025-06-16 13:31:08,246 - INFO - Request Parameters - Page 5:
2025-06-16 13:31:08,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:08,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:08,934 - INFO - Response - Page 5:
2025-06-16 13:31:08,934 - INFO - 第 5 页获取到 50 条记录
2025-06-16 13:31:09,449 - INFO - Request Parameters - Page 6:
2025-06-16 13:31:09,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:09,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:10,059 - INFO - Response - Page 6:
2025-06-16 13:31:10,059 - INFO - 第 6 页获取到 50 条记录
2025-06-16 13:31:10,559 - INFO - Request Parameters - Page 7:
2025-06-16 13:31:10,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:10,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:11,262 - INFO - Response - Page 7:
2025-06-16 13:31:11,262 - INFO - 第 7 页获取到 50 条记录
2025-06-16 13:31:11,777 - INFO - Request Parameters - Page 8:
2025-06-16 13:31:11,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:11,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:12,512 - INFO - Response - Page 8:
2025-06-16 13:31:12,512 - INFO - 第 8 页获取到 50 条记录
2025-06-16 13:31:13,012 - INFO - Request Parameters - Page 9:
2025-06-16 13:31:13,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:13,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:13,606 - INFO - Response - Page 9:
2025-06-16 13:31:13,606 - INFO - 第 9 页获取到 37 条记录
2025-06-16 13:31:14,121 - INFO - 查询完成，共获取到 437 条记录
2025-06-16 13:31:14,121 - INFO - 获取到 437 条表单数据
2025-06-16 13:31:14,121 - INFO - 当前日期 2025-06-14 有 4 条MySQL数据需要处理
2025-06-16 13:31:14,121 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 13:31:14,121 - INFO - 开始处理日期: 2025-06-15
2025-06-16 13:31:14,121 - INFO - Request Parameters - Page 1:
2025-06-16 13:31:14,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:14,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:14,699 - INFO - Response - Page 1:
2025-06-16 13:31:14,699 - INFO - 第 1 页获取到 50 条记录
2025-06-16 13:31:15,215 - INFO - Request Parameters - Page 2:
2025-06-16 13:31:15,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:15,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:15,871 - INFO - Response - Page 2:
2025-06-16 13:31:15,871 - INFO - 第 2 页获取到 50 条记录
2025-06-16 13:31:16,371 - INFO - Request Parameters - Page 3:
2025-06-16 13:31:16,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:16,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:17,027 - INFO - Response - Page 3:
2025-06-16 13:31:17,027 - INFO - 第 3 页获取到 50 条记录
2025-06-16 13:31:17,527 - INFO - Request Parameters - Page 4:
2025-06-16 13:31:17,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:17,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:18,137 - INFO - Response - Page 4:
2025-06-16 13:31:18,137 - INFO - 第 4 页获取到 50 条记录
2025-06-16 13:31:18,652 - INFO - Request Parameters - Page 5:
2025-06-16 13:31:18,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:18,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:19,434 - INFO - Response - Page 5:
2025-06-16 13:31:19,434 - INFO - 第 5 页获取到 50 条记录
2025-06-16 13:31:19,949 - INFO - Request Parameters - Page 6:
2025-06-16 13:31:19,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:19,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:20,652 - INFO - Response - Page 6:
2025-06-16 13:31:20,652 - INFO - 第 6 页获取到 50 条记录
2025-06-16 13:31:21,168 - INFO - Request Parameters - Page 7:
2025-06-16 13:31:21,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:21,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:22,355 - INFO - Response - Page 7:
2025-06-16 13:31:22,355 - INFO - 第 7 页获取到 50 条记录
2025-06-16 13:31:22,855 - INFO - Request Parameters - Page 8:
2025-06-16 13:31:22,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:22,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:23,480 - INFO - Response - Page 8:
2025-06-16 13:31:23,480 - INFO - 第 8 页获取到 50 条记录
2025-06-16 13:31:23,996 - INFO - Request Parameters - Page 9:
2025-06-16 13:31:23,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:23,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:24,590 - INFO - Response - Page 9:
2025-06-16 13:31:24,590 - INFO - 第 9 页获取到 50 条记录
2025-06-16 13:31:25,105 - INFO - Request Parameters - Page 10:
2025-06-16 13:31:25,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:25,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:25,746 - INFO - Response - Page 10:
2025-06-16 13:31:25,746 - INFO - 第 10 页获取到 50 条记录
2025-06-16 13:31:26,262 - INFO - Request Parameters - Page 11:
2025-06-16 13:31:26,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:26,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:26,746 - INFO - Response - Page 11:
2025-06-16 13:31:26,746 - INFO - 第 11 页获取到 3 条记录
2025-06-16 13:31:27,246 - INFO - 查询完成，共获取到 503 条记录
2025-06-16 13:31:27,246 - INFO - 获取到 503 条表单数据
2025-06-16 13:31:27,246 - INFO - 当前日期 2025-06-15 有 152 条MySQL数据需要处理
2025-06-16 13:31:27,246 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 13:31:27,246 - INFO - 开始处理日期: 2025-06-16
2025-06-16 13:31:27,246 - INFO - Request Parameters - Page 1:
2025-06-16 13:31:27,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:31:27,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:31:27,684 - INFO - Response - Page 1:
2025-06-16 13:31:27,684 - INFO - 第 1 页获取到 1 条记录
2025-06-16 13:31:28,199 - INFO - 查询完成，共获取到 1 条记录
2025-06-16 13:31:28,199 - INFO - 获取到 1 条表单数据
2025-06-16 13:31:28,199 - INFO - 当前日期 2025-06-16 有 1 条MySQL数据需要处理
2025-06-16 13:31:28,199 - INFO - 日期 2025-06-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 13:31:28,199 - INFO - 数据同步完成！更新: 1 条，插入: 3 条，错误: 0 条
2025-06-16 13:32:28,214 - INFO - 开始同步昨天与今天的销售数据: 2025-06-15 至 2025-06-16
2025-06-16 13:32:28,214 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-16 13:32:28,214 - INFO - 查询参数: ('2025-06-15', '2025-06-16')
2025-06-16 13:32:28,355 - INFO - MySQL查询成功，时间段: 2025-06-15 至 2025-06-16，共获取 537 条记录
2025-06-16 13:32:28,355 - INFO - 获取到 2 个日期需要处理: ['2025-06-15', '2025-06-16']
2025-06-16 13:32:28,355 - INFO - 开始处理日期: 2025-06-15
2025-06-16 13:32:28,355 - INFO - Request Parameters - Page 1:
2025-06-16 13:32:28,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:32:28,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:32:28,996 - INFO - Response - Page 1:
2025-06-16 13:32:28,996 - INFO - 第 1 页获取到 50 条记录
2025-06-16 13:32:29,496 - INFO - Request Parameters - Page 2:
2025-06-16 13:32:29,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:32:29,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:32:30,136 - INFO - Response - Page 2:
2025-06-16 13:32:30,136 - INFO - 第 2 页获取到 50 条记录
2025-06-16 13:32:30,636 - INFO - Request Parameters - Page 3:
2025-06-16 13:32:30,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:32:30,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:32:31,339 - INFO - Response - Page 3:
2025-06-16 13:32:31,339 - INFO - 第 3 页获取到 50 条记录
2025-06-16 13:32:31,855 - INFO - Request Parameters - Page 4:
2025-06-16 13:32:31,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:32:31,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:32:32,496 - INFO - Response - Page 4:
2025-06-16 13:32:32,496 - INFO - 第 4 页获取到 50 条记录
2025-06-16 13:32:33,011 - INFO - Request Parameters - Page 5:
2025-06-16 13:32:33,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:32:33,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:32:33,683 - INFO - Response - Page 5:
2025-06-16 13:32:33,683 - INFO - 第 5 页获取到 50 条记录
2025-06-16 13:32:34,183 - INFO - Request Parameters - Page 6:
2025-06-16 13:32:34,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:32:34,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:32:34,839 - INFO - Response - Page 6:
2025-06-16 13:32:34,839 - INFO - 第 6 页获取到 50 条记录
2025-06-16 13:32:35,339 - INFO - Request Parameters - Page 7:
2025-06-16 13:32:35,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:32:35,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:32:35,996 - INFO - Response - Page 7:
2025-06-16 13:32:35,996 - INFO - 第 7 页获取到 50 条记录
2025-06-16 13:32:36,511 - INFO - Request Parameters - Page 8:
2025-06-16 13:32:36,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:32:36,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:32:37,136 - INFO - Response - Page 8:
2025-06-16 13:32:37,136 - INFO - 第 8 页获取到 50 条记录
2025-06-16 13:32:37,652 - INFO - Request Parameters - Page 9:
2025-06-16 13:32:37,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:32:37,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:32:38,246 - INFO - Response - Page 9:
2025-06-16 13:32:38,246 - INFO - 第 9 页获取到 50 条记录
2025-06-16 13:32:38,761 - INFO - Request Parameters - Page 10:
2025-06-16 13:32:38,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:32:38,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:32:39,433 - INFO - Response - Page 10:
2025-06-16 13:32:39,433 - INFO - 第 10 页获取到 50 条记录
2025-06-16 13:32:39,949 - INFO - Request Parameters - Page 11:
2025-06-16 13:32:39,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:32:39,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:32:40,339 - INFO - Response - Page 11:
2025-06-16 13:32:40,339 - INFO - 第 11 页获取到 3 条记录
2025-06-16 13:32:40,839 - INFO - 查询完成，共获取到 503 条记录
2025-06-16 13:32:40,839 - INFO - 获取到 503 条表单数据
2025-06-16 13:32:40,839 - INFO - 当前日期 2025-06-15 有 503 条MySQL数据需要处理
2025-06-16 13:32:40,855 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 13:32:40,855 - INFO - 开始处理日期: 2025-06-16
2025-06-16 13:32:40,855 - INFO - Request Parameters - Page 1:
2025-06-16 13:32:40,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 13:32:40,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 13:32:41,292 - INFO - Response - Page 1:
2025-06-16 13:32:41,292 - INFO - 第 1 页获取到 1 条记录
2025-06-16 13:32:41,808 - INFO - 查询完成，共获取到 1 条记录
2025-06-16 13:32:41,808 - INFO - 获取到 1 条表单数据
2025-06-16 13:32:41,808 - INFO - 当前日期 2025-06-16 有 1 条MySQL数据需要处理
2025-06-16 13:32:41,808 - INFO - 日期 2025-06-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 13:32:41,808 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 13:32:41,808 - INFO - 同步完成
2025-06-16 16:30:33,624 - INFO - 使用默认增量同步（当天更新数据）
2025-06-16 16:30:33,624 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-16 16:30:33,624 - INFO - 查询参数: ('2025-06-16',)
2025-06-16 16:30:33,764 - INFO - MySQL查询成功，增量数据（日期: 2025-06-16），共获取 185 条记录
2025-06-16 16:30:33,764 - INFO - 获取到 4 个日期需要处理: ['2025-06-13', '2025-06-14', '2025-06-15', '2025-06-16']
2025-06-16 16:30:33,764 - INFO - 开始处理日期: 2025-06-13
2025-06-16 16:30:33,764 - INFO - Request Parameters - Page 1:
2025-06-16 16:30:33,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:33,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:41,874 - ERROR - 处理日期 2025-06-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 32DFB2B2-A3A9-7CDE-B86C-77001FEC79EC Response: {'code': 'ServiceUnavailable', 'requestid': '32DFB2B2-A3A9-7CDE-B86C-77001FEC79EC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 32DFB2B2-A3A9-7CDE-B86C-77001FEC79EC)
2025-06-16 16:30:41,874 - INFO - 开始处理日期: 2025-06-14
2025-06-16 16:30:41,874 - INFO - Request Parameters - Page 1:
2025-06-16 16:30:41,874 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:41,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:42,514 - INFO - Response - Page 1:
2025-06-16 16:30:42,514 - INFO - 第 1 页获取到 50 条记录
2025-06-16 16:30:43,030 - INFO - Request Parameters - Page 2:
2025-06-16 16:30:43,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:43,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:43,686 - INFO - Response - Page 2:
2025-06-16 16:30:43,686 - INFO - 第 2 页获取到 50 条记录
2025-06-16 16:30:44,186 - INFO - Request Parameters - Page 3:
2025-06-16 16:30:44,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:44,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:44,827 - INFO - Response - Page 3:
2025-06-16 16:30:44,827 - INFO - 第 3 页获取到 50 条记录
2025-06-16 16:30:45,343 - INFO - Request Parameters - Page 4:
2025-06-16 16:30:45,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:45,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:47,952 - INFO - Response - Page 4:
2025-06-16 16:30:47,952 - INFO - 第 4 页获取到 50 条记录
2025-06-16 16:30:48,467 - INFO - Request Parameters - Page 5:
2025-06-16 16:30:48,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:48,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:49,124 - INFO - Response - Page 5:
2025-06-16 16:30:49,124 - INFO - 第 5 页获取到 50 条记录
2025-06-16 16:30:49,624 - INFO - Request Parameters - Page 6:
2025-06-16 16:30:49,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:49,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:50,327 - INFO - Response - Page 6:
2025-06-16 16:30:50,327 - INFO - 第 6 页获取到 50 条记录
2025-06-16 16:30:50,827 - INFO - Request Parameters - Page 7:
2025-06-16 16:30:50,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:50,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:51,467 - INFO - Response - Page 7:
2025-06-16 16:30:51,467 - INFO - 第 7 页获取到 50 条记录
2025-06-16 16:30:51,967 - INFO - Request Parameters - Page 8:
2025-06-16 16:30:51,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:51,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:52,671 - INFO - Response - Page 8:
2025-06-16 16:30:52,671 - INFO - 第 8 页获取到 50 条记录
2025-06-16 16:30:53,186 - INFO - Request Parameters - Page 9:
2025-06-16 16:30:53,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:53,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:53,811 - INFO - Response - Page 9:
2025-06-16 16:30:53,811 - INFO - 第 9 页获取到 37 条记录
2025-06-16 16:30:54,327 - INFO - 查询完成，共获取到 437 条记录
2025-06-16 16:30:54,327 - INFO - 获取到 437 条表单数据
2025-06-16 16:30:54,327 - INFO - 当前日期 2025-06-14 有 4 条MySQL数据需要处理
2025-06-16 16:30:54,327 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 16:30:54,327 - INFO - 开始处理日期: 2025-06-15
2025-06-16 16:30:54,327 - INFO - Request Parameters - Page 1:
2025-06-16 16:30:54,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:54,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:55,014 - INFO - Response - Page 1:
2025-06-16 16:30:55,014 - INFO - 第 1 页获取到 50 条记录
2025-06-16 16:30:55,530 - INFO - Request Parameters - Page 2:
2025-06-16 16:30:55,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:55,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:56,186 - INFO - Response - Page 2:
2025-06-16 16:30:56,186 - INFO - 第 2 页获取到 50 条记录
2025-06-16 16:30:56,686 - INFO - Request Parameters - Page 3:
2025-06-16 16:30:56,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:56,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:57,405 - INFO - Response - Page 3:
2025-06-16 16:30:57,405 - INFO - 第 3 页获取到 50 条记录
2025-06-16 16:30:57,921 - INFO - Request Parameters - Page 4:
2025-06-16 16:30:57,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:57,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:58,546 - INFO - Response - Page 4:
2025-06-16 16:30:58,546 - INFO - 第 4 页获取到 50 条记录
2025-06-16 16:30:59,061 - INFO - Request Parameters - Page 5:
2025-06-16 16:30:59,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:30:59,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:30:59,842 - INFO - Response - Page 5:
2025-06-16 16:30:59,842 - INFO - 第 5 页获取到 50 条记录
2025-06-16 16:31:00,358 - INFO - Request Parameters - Page 6:
2025-06-16 16:31:00,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:31:00,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:31:01,077 - INFO - Response - Page 6:
2025-06-16 16:31:01,077 - INFO - 第 6 页获取到 50 条记录
2025-06-16 16:31:01,577 - INFO - Request Parameters - Page 7:
2025-06-16 16:31:01,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:31:01,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:31:02,202 - INFO - Response - Page 7:
2025-06-16 16:31:02,202 - INFO - 第 7 页获取到 50 条记录
2025-06-16 16:31:02,717 - INFO - Request Parameters - Page 8:
2025-06-16 16:31:02,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:31:02,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:31:03,327 - INFO - Response - Page 8:
2025-06-16 16:31:03,327 - INFO - 第 8 页获取到 50 条记录
2025-06-16 16:31:03,842 - INFO - Request Parameters - Page 9:
2025-06-16 16:31:03,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:31:03,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:31:04,577 - INFO - Response - Page 9:
2025-06-16 16:31:04,577 - INFO - 第 9 页获取到 50 条记录
2025-06-16 16:31:05,092 - INFO - Request Parameters - Page 10:
2025-06-16 16:31:05,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:31:05,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:31:05,889 - INFO - Response - Page 10:
2025-06-16 16:31:05,889 - INFO - 第 10 页获取到 50 条记录
2025-06-16 16:31:06,405 - INFO - Request Parameters - Page 11:
2025-06-16 16:31:06,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:31:06,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:31:06,827 - INFO - Response - Page 11:
2025-06-16 16:31:06,827 - INFO - 第 11 页获取到 3 条记录
2025-06-16 16:31:07,342 - INFO - 查询完成，共获取到 503 条记录
2025-06-16 16:31:07,342 - INFO - 获取到 503 条表单数据
2025-06-16 16:31:07,342 - INFO - 当前日期 2025-06-15 有 161 条MySQL数据需要处理
2025-06-16 16:31:07,342 - INFO - 开始批量插入 9 条新记录
2025-06-16 16:31:07,514 - INFO - 批量插入响应状态码: 200
2025-06-16 16:31:07,514 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 08:31:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '435', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FB80C62F-064E-7BDB-94CB-DC2B5E465F32', 'x-acs-trace-id': 'a54a593f6ef3f2531af0ea7ba20c612c', 'etag': '4nAtVqWOz4H+EQSaHmtc5Fw5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 16:31:07,514 - INFO - 批量插入响应体: {'result': ['FINST-AAG66KB1BSCWT7WN80GVZA6E4S652ZTM4UYBM5', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBM6', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBM7', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBM8', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBM9', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBMA', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBMB', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBMC', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBMD']}
2025-06-16 16:31:07,514 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-06-16 16:31:07,514 - INFO - 成功插入的数据ID: ['FINST-AAG66KB1BSCWT7WN80GVZA6E4S652ZTM4UYBM5', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBM6', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBM7', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBM8', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBM9', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBMA', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBMB', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBMC', 'FINST-AAG66KB1BSCWT7WN80GVZA6E4S6520UM4UYBMD']
2025-06-16 16:31:12,530 - INFO - 批量插入完成，共 9 条记录
2025-06-16 16:31:12,530 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 9 条，错误: 0 条
2025-06-16 16:31:12,530 - INFO - 开始处理日期: 2025-06-16
2025-06-16 16:31:12,530 - INFO - Request Parameters - Page 1:
2025-06-16 16:31:12,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:31:12,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:31:12,967 - INFO - Response - Page 1:
2025-06-16 16:31:12,967 - INFO - 第 1 页获取到 1 条记录
2025-06-16 16:31:13,483 - INFO - 查询完成，共获取到 1 条记录
2025-06-16 16:31:13,483 - INFO - 获取到 1 条表单数据
2025-06-16 16:31:13,483 - INFO - 当前日期 2025-06-16 有 1 条MySQL数据需要处理
2025-06-16 16:31:13,483 - INFO - 日期 2025-06-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 16:31:13,483 - INFO - 数据同步完成！更新: 0 条，插入: 9 条，错误: 1 条
2025-06-16 16:32:13,498 - INFO - 开始同步昨天与今天的销售数据: 2025-06-15 至 2025-06-16
2025-06-16 16:32:13,498 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-16 16:32:13,498 - INFO - 查询参数: ('2025-06-15', '2025-06-16')
2025-06-16 16:32:13,639 - INFO - MySQL查询成功，时间段: 2025-06-15 至 2025-06-16，共获取 553 条记录
2025-06-16 16:32:13,639 - INFO - 获取到 2 个日期需要处理: ['2025-06-15', '2025-06-16']
2025-06-16 16:32:13,639 - INFO - 开始处理日期: 2025-06-15
2025-06-16 16:32:13,639 - INFO - Request Parameters - Page 1:
2025-06-16 16:32:13,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:32:13,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:32:14,295 - INFO - Response - Page 1:
2025-06-16 16:32:14,295 - INFO - 第 1 页获取到 50 条记录
2025-06-16 16:32:14,795 - INFO - Request Parameters - Page 2:
2025-06-16 16:32:14,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:32:14,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:32:15,483 - INFO - Response - Page 2:
2025-06-16 16:32:15,483 - INFO - 第 2 页获取到 50 条记录
2025-06-16 16:32:15,983 - INFO - Request Parameters - Page 3:
2025-06-16 16:32:15,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:32:15,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:32:16,654 - INFO - Response - Page 3:
2025-06-16 16:32:16,654 - INFO - 第 3 页获取到 50 条记录
2025-06-16 16:32:17,170 - INFO - Request Parameters - Page 4:
2025-06-16 16:32:17,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:32:17,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:32:17,811 - INFO - Response - Page 4:
2025-06-16 16:32:17,811 - INFO - 第 4 页获取到 50 条记录
2025-06-16 16:32:18,311 - INFO - Request Parameters - Page 5:
2025-06-16 16:32:18,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:32:18,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:32:18,983 - INFO - Response - Page 5:
2025-06-16 16:32:18,983 - INFO - 第 5 页获取到 50 条记录
2025-06-16 16:32:19,498 - INFO - Request Parameters - Page 6:
2025-06-16 16:32:19,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:32:19,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:32:20,295 - INFO - Response - Page 6:
2025-06-16 16:32:20,295 - INFO - 第 6 页获取到 50 条记录
2025-06-16 16:32:20,811 - INFO - Request Parameters - Page 7:
2025-06-16 16:32:20,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:32:20,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:32:21,483 - INFO - Response - Page 7:
2025-06-16 16:32:21,483 - INFO - 第 7 页获取到 50 条记录
2025-06-16 16:32:21,983 - INFO - Request Parameters - Page 8:
2025-06-16 16:32:21,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:32:21,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:32:22,717 - INFO - Response - Page 8:
2025-06-16 16:32:22,717 - INFO - 第 8 页获取到 50 条记录
2025-06-16 16:32:23,217 - INFO - Request Parameters - Page 9:
2025-06-16 16:32:23,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:32:23,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:32:23,857 - INFO - Response - Page 9:
2025-06-16 16:32:23,857 - INFO - 第 9 页获取到 50 条记录
2025-06-16 16:32:24,357 - INFO - Request Parameters - Page 10:
2025-06-16 16:32:24,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:32:24,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:32:24,998 - INFO - Response - Page 10:
2025-06-16 16:32:24,998 - INFO - 第 10 页获取到 50 条记录
2025-06-16 16:32:25,498 - INFO - Request Parameters - Page 11:
2025-06-16 16:32:25,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:32:25,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:32:26,014 - INFO - Response - Page 11:
2025-06-16 16:32:26,014 - INFO - 第 11 页获取到 12 条记录
2025-06-16 16:32:26,514 - INFO - 查询完成，共获取到 512 条记录
2025-06-16 16:32:26,514 - INFO - 获取到 512 条表单数据
2025-06-16 16:32:26,514 - INFO - 当前日期 2025-06-15 有 519 条MySQL数据需要处理
2025-06-16 16:32:26,529 - INFO - 开始批量插入 7 条新记录
2025-06-16 16:32:26,717 - INFO - 批量插入响应状态码: 200
2025-06-16 16:32:26,732 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 08:32:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '341', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7B6FD5CC-754F-796B-9D1B-FCE03F2CEAEF', 'x-acs-trace-id': '6b7945c753b7644b88850cedfd7b9e19', 'etag': '3+ErOwb9+TOF9VeoxlGg7aA1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 16:32:26,732 - INFO - 批量插入响应体: {'result': ['FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBM7', 'FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBM8', 'FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBM9', 'FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBMA', 'FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBMB', 'FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBMC', 'FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBMD']}
2025-06-16 16:32:26,732 - INFO - 批量插入表单数据成功，批次 1，共 7 条记录
2025-06-16 16:32:26,732 - INFO - 成功插入的数据ID: ['FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBM7', 'FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBM8', 'FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBM9', 'FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBMA', 'FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBMB', 'FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBMC', 'FINST-BD766BC1YQCWRAFY8IYD5C969SYZ2WXB6UYBMD']
2025-06-16 16:32:31,748 - INFO - 批量插入完成，共 7 条记录
2025-06-16 16:32:31,748 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 7 条，错误: 0 条
2025-06-16 16:32:31,748 - INFO - 开始处理日期: 2025-06-16
2025-06-16 16:32:31,748 - INFO - Request Parameters - Page 1:
2025-06-16 16:32:31,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 16:32:31,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 16:32:32,170 - INFO - Response - Page 1:
2025-06-16 16:32:32,170 - INFO - 第 1 页获取到 1 条记录
2025-06-16 16:32:32,686 - INFO - 查询完成，共获取到 1 条记录
2025-06-16 16:32:32,686 - INFO - 获取到 1 条表单数据
2025-06-16 16:32:32,686 - INFO - 当前日期 2025-06-16 有 1 条MySQL数据需要处理
2025-06-16 16:32:32,686 - INFO - 日期 2025-06-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 16:32:32,686 - INFO - 数据同步完成！更新: 0 条，插入: 7 条，错误: 0 条
2025-06-16 16:32:32,686 - INFO - 同步完成
2025-06-16 19:30:33,661 - INFO - 使用默认增量同步（当天更新数据）
2025-06-16 19:30:33,661 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-16 19:30:33,661 - INFO - 查询参数: ('2025-06-16',)
2025-06-16 19:30:33,786 - INFO - MySQL查询成功，增量数据（日期: 2025-06-16），共获取 191 条记录
2025-06-16 19:30:33,786 - INFO - 获取到 4 个日期需要处理: ['2025-06-13', '2025-06-14', '2025-06-15', '2025-06-16']
2025-06-16 19:30:33,786 - INFO - 开始处理日期: 2025-06-13
2025-06-16 19:30:33,801 - INFO - Request Parameters - Page 1:
2025-06-16 19:30:33,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:30:33,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:30:39,848 - INFO - Response - Page 1:
2025-06-16 19:30:39,848 - INFO - 第 1 页获取到 50 条记录
2025-06-16 19:30:40,347 - INFO - Request Parameters - Page 2:
2025-06-16 19:30:40,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:30:40,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:30:41,004 - INFO - Response - Page 2:
2025-06-16 19:30:41,004 - INFO - 第 2 页获取到 50 条记录
2025-06-16 19:30:41,504 - INFO - Request Parameters - Page 3:
2025-06-16 19:30:41,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:30:41,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:30:42,144 - INFO - Response - Page 3:
2025-06-16 19:30:42,144 - INFO - 第 3 页获取到 50 条记录
2025-06-16 19:30:42,660 - INFO - Request Parameters - Page 4:
2025-06-16 19:30:42,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:30:42,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:30:43,300 - INFO - Response - Page 4:
2025-06-16 19:30:43,300 - INFO - 第 4 页获取到 50 条记录
2025-06-16 19:30:43,816 - INFO - Request Parameters - Page 5:
2025-06-16 19:30:43,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:30:43,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:30:44,597 - INFO - Response - Page 5:
2025-06-16 19:30:44,597 - INFO - 第 5 页获取到 50 条记录
2025-06-16 19:30:45,097 - INFO - Request Parameters - Page 6:
2025-06-16 19:30:45,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:30:45,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:30:45,753 - INFO - Response - Page 6:
2025-06-16 19:30:45,753 - INFO - 第 6 页获取到 50 条记录
2025-06-16 19:30:46,269 - INFO - Request Parameters - Page 7:
2025-06-16 19:30:46,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:30:46,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:30:54,378 - ERROR - 处理日期 2025-06-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 77E86A62-58B2-7208-9A34-40C73C7993F2 Response: {'code': 'ServiceUnavailable', 'requestid': '77E86A62-58B2-7208-9A34-40C73C7993F2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 77E86A62-58B2-7208-9A34-40C73C7993F2)
2025-06-16 19:30:54,378 - INFO - 开始处理日期: 2025-06-14
2025-06-16 19:30:54,378 - INFO - Request Parameters - Page 1:
2025-06-16 19:30:54,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:30:54,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:30:55,018 - INFO - Response - Page 1:
2025-06-16 19:30:55,018 - INFO - 第 1 页获取到 50 条记录
2025-06-16 19:30:55,534 - INFO - Request Parameters - Page 2:
2025-06-16 19:30:55,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:30:55,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:30:56,159 - INFO - Response - Page 2:
2025-06-16 19:30:56,159 - INFO - 第 2 页获取到 50 条记录
2025-06-16 19:30:56,674 - INFO - Request Parameters - Page 3:
2025-06-16 19:30:56,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:30:56,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:30:57,330 - INFO - Response - Page 3:
2025-06-16 19:30:57,330 - INFO - 第 3 页获取到 50 条记录
2025-06-16 19:30:57,846 - INFO - Request Parameters - Page 4:
2025-06-16 19:30:57,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:30:57,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:30:58,455 - INFO - Response - Page 4:
2025-06-16 19:30:58,455 - INFO - 第 4 页获取到 50 条记录
2025-06-16 19:30:58,971 - INFO - Request Parameters - Page 5:
2025-06-16 19:30:58,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:30:58,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:30:59,705 - INFO - Response - Page 5:
2025-06-16 19:30:59,705 - INFO - 第 5 页获取到 50 条记录
2025-06-16 19:31:00,221 - INFO - Request Parameters - Page 6:
2025-06-16 19:31:00,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:00,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:00,815 - INFO - Response - Page 6:
2025-06-16 19:31:00,815 - INFO - 第 6 页获取到 50 条记录
2025-06-16 19:31:01,314 - INFO - Request Parameters - Page 7:
2025-06-16 19:31:01,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:01,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:01,986 - INFO - Response - Page 7:
2025-06-16 19:31:01,986 - INFO - 第 7 页获取到 50 条记录
2025-06-16 19:31:02,486 - INFO - Request Parameters - Page 8:
2025-06-16 19:31:02,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:02,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:03,127 - INFO - Response - Page 8:
2025-06-16 19:31:03,127 - INFO - 第 8 页获取到 50 条记录
2025-06-16 19:31:03,642 - INFO - Request Parameters - Page 9:
2025-06-16 19:31:03,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:03,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:04,299 - INFO - Response - Page 9:
2025-06-16 19:31:04,299 - INFO - 第 9 页获取到 37 条记录
2025-06-16 19:31:04,814 - INFO - 查询完成，共获取到 437 条记录
2025-06-16 19:31:04,814 - INFO - 获取到 437 条表单数据
2025-06-16 19:31:04,814 - INFO - 当前日期 2025-06-14 有 4 条MySQL数据需要处理
2025-06-16 19:31:04,814 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 19:31:04,814 - INFO - 开始处理日期: 2025-06-15
2025-06-16 19:31:04,814 - INFO - Request Parameters - Page 1:
2025-06-16 19:31:04,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:04,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:05,455 - INFO - Response - Page 1:
2025-06-16 19:31:05,455 - INFO - 第 1 页获取到 50 条记录
2025-06-16 19:31:05,955 - INFO - Request Parameters - Page 2:
2025-06-16 19:31:05,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:05,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:06,611 - INFO - Response - Page 2:
2025-06-16 19:31:06,611 - INFO - 第 2 页获取到 50 条记录
2025-06-16 19:31:07,126 - INFO - Request Parameters - Page 3:
2025-06-16 19:31:07,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:07,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:07,783 - INFO - Response - Page 3:
2025-06-16 19:31:07,783 - INFO - 第 3 页获取到 50 条记录
2025-06-16 19:31:08,298 - INFO - Request Parameters - Page 4:
2025-06-16 19:31:08,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:08,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:08,939 - INFO - Response - Page 4:
2025-06-16 19:31:08,939 - INFO - 第 4 页获取到 50 条记录
2025-06-16 19:31:09,454 - INFO - Request Parameters - Page 5:
2025-06-16 19:31:09,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:09,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:10,126 - INFO - Response - Page 5:
2025-06-16 19:31:10,126 - INFO - 第 5 页获取到 50 条记录
2025-06-16 19:31:10,642 - INFO - Request Parameters - Page 6:
2025-06-16 19:31:10,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:10,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:11,267 - INFO - Response - Page 6:
2025-06-16 19:31:11,267 - INFO - 第 6 页获取到 50 条记录
2025-06-16 19:31:11,782 - INFO - Request Parameters - Page 7:
2025-06-16 19:31:11,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:11,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:12,454 - INFO - Response - Page 7:
2025-06-16 19:31:12,454 - INFO - 第 7 页获取到 50 条记录
2025-06-16 19:31:12,970 - INFO - Request Parameters - Page 8:
2025-06-16 19:31:12,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:12,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:13,595 - INFO - Response - Page 8:
2025-06-16 19:31:13,595 - INFO - 第 8 页获取到 50 条记录
2025-06-16 19:31:14,110 - INFO - Request Parameters - Page 9:
2025-06-16 19:31:14,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:14,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:14,766 - INFO - Response - Page 9:
2025-06-16 19:31:14,766 - INFO - 第 9 页获取到 50 条记录
2025-06-16 19:31:15,282 - INFO - Request Parameters - Page 10:
2025-06-16 19:31:15,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:15,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:15,923 - INFO - Response - Page 10:
2025-06-16 19:31:15,923 - INFO - 第 10 页获取到 50 条记录
2025-06-16 19:31:16,438 - INFO - Request Parameters - Page 11:
2025-06-16 19:31:16,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:16,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:17,016 - INFO - Response - Page 11:
2025-06-16 19:31:17,016 - INFO - 第 11 页获取到 19 条记录
2025-06-16 19:31:17,532 - INFO - 查询完成，共获取到 519 条记录
2025-06-16 19:31:17,532 - INFO - 获取到 519 条表单数据
2025-06-16 19:31:17,532 - INFO - 当前日期 2025-06-15 有 165 条MySQL数据需要处理
2025-06-16 19:31:17,532 - INFO - 开始批量插入 4 条新记录
2025-06-16 19:31:17,704 - INFO - 批量插入响应状态码: 200
2025-06-16 19:31:17,704 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 11:31:17 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D43DF683-1178-7AF9-AC03-71735624F5CF', 'x-acs-trace-id': 'cd8ccc708c5b266e23f9b9d883410ad2', 'etag': '2Qz1xXqYmy5v1BE0f2DY47w4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 19:31:17,704 - INFO - 批量插入响应体: {'result': ['FINST-XMC66R91AVCWMFFNARK1RA27XR3831R9K0ZBM41', 'FINST-XMC66R91AVCWMFFNARK1RA27XR3831R9K0ZBM51', 'FINST-XMC66R91AVCWMFFNARK1RA27XR3831R9K0ZBM61', 'FINST-XMC66R91AVCWMFFNARK1RA27XR3831R9K0ZBM71']}
2025-06-16 19:31:17,704 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-06-16 19:31:17,704 - INFO - 成功插入的数据ID: ['FINST-XMC66R91AVCWMFFNARK1RA27XR3831R9K0ZBM41', 'FINST-XMC66R91AVCWMFFNARK1RA27XR3831R9K0ZBM51', 'FINST-XMC66R91AVCWMFFNARK1RA27XR3831R9K0ZBM61', 'FINST-XMC66R91AVCWMFFNARK1RA27XR3831R9K0ZBM71']
2025-06-16 19:31:22,719 - INFO - 批量插入完成，共 4 条记录
2025-06-16 19:31:22,719 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-06-16 19:31:22,719 - INFO - 开始处理日期: 2025-06-16
2025-06-16 19:31:22,719 - INFO - Request Parameters - Page 1:
2025-06-16 19:31:22,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:31:22,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:31:23,219 - INFO - Response - Page 1:
2025-06-16 19:31:23,219 - INFO - 第 1 页获取到 1 条记录
2025-06-16 19:31:23,734 - INFO - 查询完成，共获取到 1 条记录
2025-06-16 19:31:23,734 - INFO - 获取到 1 条表单数据
2025-06-16 19:31:23,734 - INFO - 当前日期 2025-06-16 有 3 条MySQL数据需要处理
2025-06-16 19:31:23,734 - INFO - 开始批量插入 2 条新记录
2025-06-16 19:31:23,875 - INFO - 批量插入响应状态码: 200
2025-06-16 19:31:23,875 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 11:31:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '106', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '01AB5884-3BFB-7721-80EB-60C49A40D0F5', 'x-acs-trace-id': '41d9838105216669779d08c7a85d626c', 'etag': '1sut8glWxuzHw0/86nk9sIg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 19:31:23,875 - INFO - 批量插入响应体: {'result': ['FINST-EEC66XC1TVCWWRZGBUBEQDHVV3K03LIEK0ZBMD', 'FINST-EEC66XC1TVCWWRZGBUBEQDHVV3K03LIEK0ZBME']}
2025-06-16 19:31:23,875 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-16 19:31:23,875 - INFO - 成功插入的数据ID: ['FINST-EEC66XC1TVCWWRZGBUBEQDHVV3K03LIEK0ZBMD', 'FINST-EEC66XC1TVCWWRZGBUBEQDHVV3K03LIEK0ZBME']
2025-06-16 19:31:28,890 - INFO - 批量插入完成，共 2 条记录
2025-06-16 19:31:28,890 - INFO - 日期 2025-06-16 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-16 19:31:28,890 - INFO - 数据同步完成！更新: 0 条，插入: 6 条，错误: 1 条
2025-06-16 19:32:28,901 - INFO - 开始同步昨天与今天的销售数据: 2025-06-15 至 2025-06-16
2025-06-16 19:32:28,901 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-16 19:32:28,901 - INFO - 查询参数: ('2025-06-15', '2025-06-16')
2025-06-16 19:32:29,041 - INFO - MySQL查询成功，时间段: 2025-06-15 至 2025-06-16，共获取 563 条记录
2025-06-16 19:32:29,041 - INFO - 获取到 2 个日期需要处理: ['2025-06-15', '2025-06-16']
2025-06-16 19:32:29,041 - INFO - 开始处理日期: 2025-06-15
2025-06-16 19:32:29,041 - INFO - Request Parameters - Page 1:
2025-06-16 19:32:29,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:32:29,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:32:29,729 - INFO - Response - Page 1:
2025-06-16 19:32:29,729 - INFO - 第 1 页获取到 50 条记录
2025-06-16 19:32:30,229 - INFO - Request Parameters - Page 2:
2025-06-16 19:32:30,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:32:30,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:32:30,869 - INFO - Response - Page 2:
2025-06-16 19:32:30,869 - INFO - 第 2 页获取到 50 条记录
2025-06-16 19:32:31,385 - INFO - Request Parameters - Page 3:
2025-06-16 19:32:31,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:32:31,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:32:32,072 - INFO - Response - Page 3:
2025-06-16 19:32:32,072 - INFO - 第 3 页获取到 50 条记录
2025-06-16 19:32:32,588 - INFO - Request Parameters - Page 4:
2025-06-16 19:32:32,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:32:32,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:32:33,182 - INFO - Response - Page 4:
2025-06-16 19:32:33,182 - INFO - 第 4 页获取到 50 条记录
2025-06-16 19:32:33,697 - INFO - Request Parameters - Page 5:
2025-06-16 19:32:33,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:32:33,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:32:34,338 - INFO - Response - Page 5:
2025-06-16 19:32:34,338 - INFO - 第 5 页获取到 50 条记录
2025-06-16 19:32:34,853 - INFO - Request Parameters - Page 6:
2025-06-16 19:32:34,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:32:34,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:32:35,478 - INFO - Response - Page 6:
2025-06-16 19:32:35,478 - INFO - 第 6 页获取到 50 条记录
2025-06-16 19:32:35,978 - INFO - Request Parameters - Page 7:
2025-06-16 19:32:35,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:32:35,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:32:36,603 - INFO - Response - Page 7:
2025-06-16 19:32:36,603 - INFO - 第 7 页获取到 50 条记录
2025-06-16 19:32:37,119 - INFO - Request Parameters - Page 8:
2025-06-16 19:32:37,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:32:37,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:32:37,744 - INFO - Response - Page 8:
2025-06-16 19:32:37,744 - INFO - 第 8 页获取到 50 条记录
2025-06-16 19:32:38,244 - INFO - Request Parameters - Page 9:
2025-06-16 19:32:38,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:32:38,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:32:38,884 - INFO - Response - Page 9:
2025-06-16 19:32:38,884 - INFO - 第 9 页获取到 50 条记录
2025-06-16 19:32:39,400 - INFO - Request Parameters - Page 10:
2025-06-16 19:32:39,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:32:39,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:32:40,009 - INFO - Response - Page 10:
2025-06-16 19:32:40,025 - INFO - 第 10 页获取到 50 条记录
2025-06-16 19:32:40,540 - INFO - Request Parameters - Page 11:
2025-06-16 19:32:40,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:32:40,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:32:41,119 - INFO - Response - Page 11:
2025-06-16 19:32:41,119 - INFO - 第 11 页获取到 23 条记录
2025-06-16 19:32:41,634 - INFO - 查询完成，共获取到 523 条记录
2025-06-16 19:32:41,634 - INFO - 获取到 523 条表单数据
2025-06-16 19:32:41,634 - INFO - 当前日期 2025-06-15 有 527 条MySQL数据需要处理
2025-06-16 19:32:41,650 - INFO - 开始批量插入 4 条新记录
2025-06-16 19:32:41,822 - INFO - 批量插入响应状态码: 200
2025-06-16 19:32:41,822 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 11:32:41 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '200', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '883D9ACC-D141-7500-ACFE-1459299777A2', 'x-acs-trace-id': '173042c842e20bd67aae746a607844ee', 'etag': '2wCLi09fyOCIsSIQMw6E6SQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 19:32:41,822 - INFO - 批量插入响应体: {'result': ['FINST-SWC66P91DRCWAU1E7TRYL8Q42YVT3RN2M0ZBMK', 'FINST-SWC66P91DRCWAU1E7TRYL8Q42YVT3RN2M0ZBML', 'FINST-SWC66P91DRCWAU1E7TRYL8Q42YVT3RN2M0ZBMM', 'FINST-SWC66P91DRCWAU1E7TRYL8Q42YVT3RN2M0ZBMN']}
2025-06-16 19:32:41,822 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-06-16 19:32:41,822 - INFO - 成功插入的数据ID: ['FINST-SWC66P91DRCWAU1E7TRYL8Q42YVT3RN2M0ZBMK', 'FINST-SWC66P91DRCWAU1E7TRYL8Q42YVT3RN2M0ZBML', 'FINST-SWC66P91DRCWAU1E7TRYL8Q42YVT3RN2M0ZBMM', 'FINST-SWC66P91DRCWAU1E7TRYL8Q42YVT3RN2M0ZBMN']
2025-06-16 19:32:46,837 - INFO - 批量插入完成，共 4 条记录
2025-06-16 19:32:46,837 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-06-16 19:32:46,837 - INFO - 开始处理日期: 2025-06-16
2025-06-16 19:32:46,837 - INFO - Request Parameters - Page 1:
2025-06-16 19:32:46,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 19:32:46,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 19:32:47,259 - INFO - Response - Page 1:
2025-06-16 19:32:47,259 - INFO - 第 1 页获取到 3 条记录
2025-06-16 19:32:47,774 - INFO - 查询完成，共获取到 3 条记录
2025-06-16 19:32:47,774 - INFO - 获取到 3 条表单数据
2025-06-16 19:32:47,774 - INFO - 当前日期 2025-06-16 有 3 条MySQL数据需要处理
2025-06-16 19:32:47,774 - INFO - 日期 2025-06-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 19:32:47,774 - INFO - 数据同步完成！更新: 0 条，插入: 4 条，错误: 0 条
2025-06-16 19:32:47,774 - INFO - 同步完成
2025-06-16 22:30:33,351 - INFO - 使用默认增量同步（当天更新数据）
2025-06-16 22:30:33,351 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-16 22:30:33,351 - INFO - 查询参数: ('2025-06-16',)
2025-06-16 22:30:33,492 - INFO - MySQL查询成功，增量数据（日期: 2025-06-16），共获取 204 条记录
2025-06-16 22:30:33,492 - INFO - 获取到 4 个日期需要处理: ['2025-06-13', '2025-06-14', '2025-06-15', '2025-06-16']
2025-06-16 22:30:33,492 - INFO - 开始处理日期: 2025-06-13
2025-06-16 22:30:33,492 - INFO - Request Parameters - Page 1:
2025-06-16 22:30:33,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:30:33,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:30:39,476 - INFO - Response - Page 1:
2025-06-16 22:30:39,476 - INFO - 第 1 页获取到 50 条记录
2025-06-16 22:30:39,991 - INFO - Request Parameters - Page 2:
2025-06-16 22:30:39,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:30:39,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749744000000, 1749830399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:30:48,100 - ERROR - 处理日期 2025-06-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 87597938-C6A8-7E73-A694-776436A22797 Response: {'code': 'ServiceUnavailable', 'requestid': '87597938-C6A8-7E73-A694-776436A22797', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 87597938-C6A8-7E73-A694-776436A22797)
2025-06-16 22:30:48,100 - INFO - 开始处理日期: 2025-06-14
2025-06-16 22:30:48,100 - INFO - Request Parameters - Page 1:
2025-06-16 22:30:48,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:30:48,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:30:48,787 - INFO - Response - Page 1:
2025-06-16 22:30:48,787 - INFO - 第 1 页获取到 50 条记录
2025-06-16 22:30:49,287 - INFO - Request Parameters - Page 2:
2025-06-16 22:30:49,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:30:49,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:30:49,944 - INFO - Response - Page 2:
2025-06-16 22:30:49,944 - INFO - 第 2 页获取到 50 条记录
2025-06-16 22:30:50,459 - INFO - Request Parameters - Page 3:
2025-06-16 22:30:50,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:30:50,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:30:51,131 - INFO - Response - Page 3:
2025-06-16 22:30:51,131 - INFO - 第 3 页获取到 50 条记录
2025-06-16 22:30:51,647 - INFO - Request Parameters - Page 4:
2025-06-16 22:30:51,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:30:51,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:30:52,303 - INFO - Response - Page 4:
2025-06-16 22:30:52,303 - INFO - 第 4 页获取到 50 条记录
2025-06-16 22:30:52,803 - INFO - Request Parameters - Page 5:
2025-06-16 22:30:52,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:30:52,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:30:53,459 - INFO - Response - Page 5:
2025-06-16 22:30:53,459 - INFO - 第 5 页获取到 50 条记录
2025-06-16 22:30:53,959 - INFO - Request Parameters - Page 6:
2025-06-16 22:30:53,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:30:53,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:30:54,584 - INFO - Response - Page 6:
2025-06-16 22:30:54,584 - INFO - 第 6 页获取到 50 条记录
2025-06-16 22:30:55,099 - INFO - Request Parameters - Page 7:
2025-06-16 22:30:55,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:30:55,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:30:55,802 - INFO - Response - Page 7:
2025-06-16 22:30:55,802 - INFO - 第 7 页获取到 50 条记录
2025-06-16 22:30:56,302 - INFO - Request Parameters - Page 8:
2025-06-16 22:30:56,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:30:56,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:30:56,943 - INFO - Response - Page 8:
2025-06-16 22:30:56,943 - INFO - 第 8 页获取到 50 条记录
2025-06-16 22:30:57,443 - INFO - Request Parameters - Page 9:
2025-06-16 22:30:57,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:30:57,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:30:58,052 - INFO - Response - Page 9:
2025-06-16 22:30:58,052 - INFO - 第 9 页获取到 37 条记录
2025-06-16 22:30:58,568 - INFO - 查询完成，共获取到 437 条记录
2025-06-16 22:30:58,568 - INFO - 获取到 437 条表单数据
2025-06-16 22:30:58,568 - INFO - 当前日期 2025-06-14 有 4 条MySQL数据需要处理
2025-06-16 22:30:58,568 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 22:30:58,568 - INFO - 开始处理日期: 2025-06-15
2025-06-16 22:30:58,568 - INFO - Request Parameters - Page 1:
2025-06-16 22:30:58,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:30:58,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:30:59,240 - INFO - Response - Page 1:
2025-06-16 22:30:59,240 - INFO - 第 1 页获取到 50 条记录
2025-06-16 22:30:59,755 - INFO - Request Parameters - Page 2:
2025-06-16 22:30:59,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:30:59,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:31:00,443 - INFO - Response - Page 2:
2025-06-16 22:31:00,443 - INFO - 第 2 页获取到 50 条记录
2025-06-16 22:31:00,958 - INFO - Request Parameters - Page 3:
2025-06-16 22:31:00,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:31:00,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:31:01,583 - INFO - Response - Page 3:
2025-06-16 22:31:01,583 - INFO - 第 3 页获取到 50 条记录
2025-06-16 22:31:02,099 - INFO - Request Parameters - Page 4:
2025-06-16 22:31:02,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:31:02,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:31:02,771 - INFO - Response - Page 4:
2025-06-16 22:31:02,771 - INFO - 第 4 页获取到 50 条记录
2025-06-16 22:31:03,271 - INFO - Request Parameters - Page 5:
2025-06-16 22:31:03,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:31:03,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:31:03,927 - INFO - Response - Page 5:
2025-06-16 22:31:03,927 - INFO - 第 5 页获取到 50 条记录
2025-06-16 22:31:04,442 - INFO - Request Parameters - Page 6:
2025-06-16 22:31:04,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:31:04,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:31:05,161 - INFO - Response - Page 6:
2025-06-16 22:31:05,161 - INFO - 第 6 页获取到 50 条记录
2025-06-16 22:31:05,661 - INFO - Request Parameters - Page 7:
2025-06-16 22:31:05,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:31:05,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:31:06,333 - INFO - Response - Page 7:
2025-06-16 22:31:06,333 - INFO - 第 7 页获取到 50 条记录
2025-06-16 22:31:06,848 - INFO - Request Parameters - Page 8:
2025-06-16 22:31:06,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:31:06,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:31:07,505 - INFO - Response - Page 8:
2025-06-16 22:31:07,505 - INFO - 第 8 页获取到 50 条记录
2025-06-16 22:31:08,036 - INFO - Request Parameters - Page 9:
2025-06-16 22:31:08,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:31:08,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:31:08,645 - INFO - Response - Page 9:
2025-06-16 22:31:08,645 - INFO - 第 9 页获取到 50 条记录
2025-06-16 22:31:09,145 - INFO - Request Parameters - Page 10:
2025-06-16 22:31:09,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:31:09,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:31:10,036 - INFO - Response - Page 10:
2025-06-16 22:31:10,036 - INFO - 第 10 页获取到 50 条记录
2025-06-16 22:31:10,536 - INFO - Request Parameters - Page 11:
2025-06-16 22:31:10,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:31:10,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:31:11,192 - INFO - Response - Page 11:
2025-06-16 22:31:11,192 - INFO - 第 11 页获取到 27 条记录
2025-06-16 22:31:11,692 - INFO - 查询完成，共获取到 527 条记录
2025-06-16 22:31:11,692 - INFO - 获取到 527 条表单数据
2025-06-16 22:31:11,692 - INFO - 当前日期 2025-06-15 有 167 条MySQL数据需要处理
2025-06-16 22:31:11,692 - INFO - 开始批量插入 2 条新记录
2025-06-16 22:31:11,864 - INFO - 批量插入响应状态码: 200
2025-06-16 22:31:11,864 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 14:31:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '107', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BD1EC67C-1345-7075-9B07-20EF89E1C200', 'x-acs-trace-id': '5c5d79c1ede777182ba14e022048a47e', 'etag': '1JK6fxJ1IhvozNd/94AldeA7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 22:31:11,864 - INFO - 批量插入响应体: {'result': ['FINST-IOC66G71PZCWSU2MA6N3E8C0EPTR2Z9NZ6ZBMZ', 'FINST-IOC66G71PZCWSU2MA6N3E8C0EPTR2Z9NZ6ZBM01']}
2025-06-16 22:31:11,864 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-16 22:31:11,864 - INFO - 成功插入的数据ID: ['FINST-IOC66G71PZCWSU2MA6N3E8C0EPTR2Z9NZ6ZBMZ', 'FINST-IOC66G71PZCWSU2MA6N3E8C0EPTR2Z9NZ6ZBM01']
2025-06-16 22:31:16,879 - INFO - 批量插入完成，共 2 条记录
2025-06-16 22:31:16,879 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-16 22:31:16,879 - INFO - 开始处理日期: 2025-06-16
2025-06-16 22:31:16,879 - INFO - Request Parameters - Page 1:
2025-06-16 22:31:16,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:31:16,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:31:17,316 - INFO - Response - Page 1:
2025-06-16 22:31:17,316 - INFO - 第 1 页获取到 3 条记录
2025-06-16 22:31:17,832 - INFO - 查询完成，共获取到 3 条记录
2025-06-16 22:31:17,832 - INFO - 获取到 3 条表单数据
2025-06-16 22:31:17,832 - INFO - 当前日期 2025-06-16 有 14 条MySQL数据需要处理
2025-06-16 22:31:17,832 - INFO - 开始批量插入 11 条新记录
2025-06-16 22:31:18,004 - INFO - 批量插入响应状态码: 200
2025-06-16 22:31:18,004 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 14:31:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '540', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5D5896FE-6F17-7600-845B-823F2B3D2517', 'x-acs-trace-id': 'f8b1f91e65cdfc9caed1762d22b46e67', 'etag': '5HDCeB/DUde7FIEFq5U/zSQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 22:31:18,004 - INFO - 批量插入响应体: {'result': ['FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2K0SZ6ZBMO1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMP1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMQ1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMR1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMS1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMT1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMU1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMV1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMW1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMX1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMY1']}
2025-06-16 22:31:18,004 - INFO - 批量插入表单数据成功，批次 1，共 11 条记录
2025-06-16 22:31:18,004 - INFO - 成功插入的数据ID: ['FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2K0SZ6ZBMO1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMP1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMQ1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMR1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMS1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMT1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMU1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMV1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMW1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMX1', 'FINST-N79668C1WVCWAJVQ7H7V1AIOI8ZZ2L0SZ6ZBMY1']
2025-06-16 22:31:23,019 - INFO - 批量插入完成，共 11 条记录
2025-06-16 22:31:23,019 - INFO - 日期 2025-06-16 处理完成 - 更新: 0 条，插入: 11 条，错误: 0 条
2025-06-16 22:31:23,019 - INFO - 数据同步完成！更新: 0 条，插入: 13 条，错误: 1 条
2025-06-16 22:32:23,030 - INFO - 开始同步昨天与今天的销售数据: 2025-06-15 至 2025-06-16
2025-06-16 22:32:23,030 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-16 22:32:23,030 - INFO - 查询参数: ('2025-06-15', '2025-06-16')
2025-06-16 22:32:23,170 - INFO - MySQL查询成功，时间段: 2025-06-15 至 2025-06-16，共获取 597 条记录
2025-06-16 22:32:23,170 - INFO - 获取到 2 个日期需要处理: ['2025-06-15', '2025-06-16']
2025-06-16 22:32:23,170 - INFO - 开始处理日期: 2025-06-15
2025-06-16 22:32:23,170 - INFO - Request Parameters - Page 1:
2025-06-16 22:32:23,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:32:23,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:32:23,858 - INFO - Response - Page 1:
2025-06-16 22:32:23,858 - INFO - 第 1 页获取到 50 条记录
2025-06-16 22:32:24,358 - INFO - Request Parameters - Page 2:
2025-06-16 22:32:24,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:32:24,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:32:25,061 - INFO - Response - Page 2:
2025-06-16 22:32:25,061 - INFO - 第 2 页获取到 50 条记录
2025-06-16 22:32:25,576 - INFO - Request Parameters - Page 3:
2025-06-16 22:32:25,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:32:25,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:32:26,279 - INFO - Response - Page 3:
2025-06-16 22:32:26,279 - INFO - 第 3 页获取到 50 条记录
2025-06-16 22:32:26,795 - INFO - Request Parameters - Page 4:
2025-06-16 22:32:26,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:32:26,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:32:27,545 - INFO - Response - Page 4:
2025-06-16 22:32:27,545 - INFO - 第 4 页获取到 50 条记录
2025-06-16 22:32:28,060 - INFO - Request Parameters - Page 5:
2025-06-16 22:32:28,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:32:28,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:32:28,685 - INFO - Response - Page 5:
2025-06-16 22:32:28,685 - INFO - 第 5 页获取到 50 条记录
2025-06-16 22:32:29,185 - INFO - Request Parameters - Page 6:
2025-06-16 22:32:29,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:32:29,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:32:29,779 - INFO - Response - Page 6:
2025-06-16 22:32:29,779 - INFO - 第 6 页获取到 50 条记录
2025-06-16 22:32:30,279 - INFO - Request Parameters - Page 7:
2025-06-16 22:32:30,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:32:30,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:32:30,935 - INFO - Response - Page 7:
2025-06-16 22:32:30,935 - INFO - 第 7 页获取到 50 条记录
2025-06-16 22:32:31,435 - INFO - Request Parameters - Page 8:
2025-06-16 22:32:31,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:32:31,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:32:32,060 - INFO - Response - Page 8:
2025-06-16 22:32:32,060 - INFO - 第 8 页获取到 50 条记录
2025-06-16 22:32:32,576 - INFO - Request Parameters - Page 9:
2025-06-16 22:32:32,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:32:32,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:32:33,263 - INFO - Response - Page 9:
2025-06-16 22:32:33,263 - INFO - 第 9 页获取到 50 条记录
2025-06-16 22:32:33,763 - INFO - Request Parameters - Page 10:
2025-06-16 22:32:33,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:32:33,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:32:34,419 - INFO - Response - Page 10:
2025-06-16 22:32:34,419 - INFO - 第 10 页获取到 50 条记录
2025-06-16 22:32:34,935 - INFO - Request Parameters - Page 11:
2025-06-16 22:32:34,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:32:34,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:32:35,529 - INFO - Response - Page 11:
2025-06-16 22:32:35,529 - INFO - 第 11 页获取到 29 条记录
2025-06-16 22:32:36,044 - INFO - 查询完成，共获取到 529 条记录
2025-06-16 22:32:36,044 - INFO - 获取到 529 条表单数据
2025-06-16 22:32:36,044 - INFO - 当前日期 2025-06-15 有 550 条MySQL数据需要处理
2025-06-16 22:32:36,060 - INFO - 开始批量插入 21 条新记录
2025-06-16 22:32:36,263 - INFO - 批量插入响应状态码: 200
2025-06-16 22:32:36,263 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 16 Jun 2025 14:32:37 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1020', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '604EE70C-F8DA-7C32-9AB3-7A5BE04B5409', 'x-acs-trace-id': 'e46964eb3f292aca28267878518b8a0f', 'etag': '1eCeLxwdGOxG4aCR9B/5Zfw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-16 22:32:36,263 - INFO - 批量插入响应体: {'result': ['FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMG2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMH2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMI2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMJ2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMK2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBML2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMM2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMN2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMO2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMP2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMQ2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMR2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMS2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMT2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMU2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMV2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMW2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMX2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMY2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMZ2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBM03']}
2025-06-16 22:32:36,263 - INFO - 批量插入表单数据成功，批次 1，共 21 条记录
2025-06-16 22:32:36,263 - INFO - 成功插入的数据ID: ['FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMG2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMH2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMI2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMJ2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMK2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBML2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMM2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMN2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMO2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMP2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMQ2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2KEG17ZBMR2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMS2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMT2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMU2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMV2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMW2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMX2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMY2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBMZ2', 'FINST-6AG66W81DVCWHCW6EQHIV7GFJMTR2LEG17ZBM03']
2025-06-16 22:32:41,278 - INFO - 批量插入完成，共 21 条记录
2025-06-16 22:32:41,278 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 21 条，错误: 0 条
2025-06-16 22:32:41,278 - INFO - 开始处理日期: 2025-06-16
2025-06-16 22:32:41,278 - INFO - Request Parameters - Page 1:
2025-06-16 22:32:41,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-16 22:32:41,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750003200000, 1750089599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-16 22:32:41,778 - INFO - Response - Page 1:
2025-06-16 22:32:41,778 - INFO - 第 1 页获取到 14 条记录
2025-06-16 22:32:42,294 - INFO - 查询完成，共获取到 14 条记录
2025-06-16 22:32:42,294 - INFO - 获取到 14 条表单数据
2025-06-16 22:32:42,294 - INFO - 当前日期 2025-06-16 有 14 条MySQL数据需要处理
2025-06-16 22:32:42,294 - INFO - 日期 2025-06-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-16 22:32:42,294 - INFO - 数据同步完成！更新: 0 条，插入: 21 条，错误: 0 条
2025-06-16 22:32:42,294 - INFO - 同步完成
