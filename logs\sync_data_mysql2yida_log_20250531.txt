2025-05-31 00:30:34,155 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 00:30:34,156 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 00:30:34,157 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 00:30:34,222 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 0 条记录
2025-05-31 00:30:34,223 - ERROR - 未获取到MySQL数据
2025-05-31 00:31:34,225 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 00:31:34,225 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 00:31:34,225 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 00:31:34,291 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 59 条记录
2025-05-31 00:31:34,291 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 00:31:34,292 - INFO - 开始处理日期: 2025-05-30
2025-05-31 00:31:34,296 - INFO - Request Parameters - Page 1:
2025-05-31 00:31:34,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:31:34,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:31:42,417 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 60D40640-3BF6-7FB4-9FC6-6D57FB7AA8E7 Response: {'code': 'ServiceUnavailable', 'requestid': '60D40640-3BF6-7FB4-9FC6-6D57FB7AA8E7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 60D40640-3BF6-7FB4-9FC6-6D57FB7AA8E7)
2025-05-31 00:31:42,417 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 00:31:42,417 - INFO - 同步完成
2025-05-31 01:30:33,926 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 01:30:33,926 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 01:30:33,926 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 01:30:33,992 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 0 条记录
2025-05-31 01:30:33,992 - ERROR - 未获取到MySQL数据
2025-05-31 01:31:33,993 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 01:31:33,993 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 01:31:33,993 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 01:31:34,059 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 59 条记录
2025-05-31 01:31:34,059 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 01:31:34,060 - INFO - 开始处理日期: 2025-05-30
2025-05-31 01:31:34,064 - INFO - Request Parameters - Page 1:
2025-05-31 01:31:34,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 01:31:34,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 01:31:42,189 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 00928FF1-7FB2-7BB3-BF38-55D4B824DD28 Response: {'code': 'ServiceUnavailable', 'requestid': '00928FF1-7FB2-7BB3-BF38-55D4B824DD28', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 00928FF1-7FB2-7BB3-BF38-55D4B824DD28)
2025-05-31 01:31:42,189 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 01:31:42,189 - INFO - 同步完成
2025-05-31 02:30:34,205 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 02:30:34,206 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 02:30:34,206 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 02:30:34,272 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 3 条记录
2025-05-31 02:30:34,272 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 02:30:34,272 - INFO - 开始处理日期: 2025-05-30
2025-05-31 02:30:34,275 - INFO - Request Parameters - Page 1:
2025-05-31 02:30:34,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 02:30:34,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 02:30:42,386 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DF114237-61FD-70C9-9B0D-1CE502222B93 Response: {'code': 'ServiceUnavailable', 'requestid': 'DF114237-61FD-70C9-9B0D-1CE502222B93', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DF114237-61FD-70C9-9B0D-1CE502222B93)
2025-05-31 02:30:42,386 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 02:31:42,388 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 02:31:42,388 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 02:31:42,388 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 02:31:42,459 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 113 条记录
2025-05-31 02:31:42,460 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 02:31:42,461 - INFO - 开始处理日期: 2025-05-30
2025-05-31 02:31:42,461 - INFO - Request Parameters - Page 1:
2025-05-31 02:31:42,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 02:31:42,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 02:31:43,332 - INFO - Response - Page 1:
2025-05-31 02:31:43,333 - INFO - 第 1 页获取到 56 条记录
2025-05-31 02:31:43,533 - INFO - 查询完成，共获取到 56 条记录
2025-05-31 02:31:43,536 - INFO - 获取到 56 条表单数据
2025-05-31 02:31:43,539 - INFO - 当前日期 2025-05-30 有 113 条MySQL数据需要处理
2025-05-31 02:31:43,540 - INFO - 开始批量插入 57 条新记录
2025-05-31 02:31:43,781 - INFO - 批量插入响应状态码: 200
2025-05-31 02:31:43,781 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 30 May 2025 18:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2748', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '00A88F1F-C712-7834-AF33-7C928188E79F', 'x-acs-trace-id': 'b7de69cac33bc0475cd97f6605a5ba3c', 'etag': '2Qp200bFDGIzAzYAxxbTYkg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 02:31:43,781 - INFO - 批量插入响应体: {'result': ['FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBML6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMM6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMN6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMO6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMP6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMQ6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMR6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMS6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMT6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMU6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMV6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMW6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMX6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMY6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMZ6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM07', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM17', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM27', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM37', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM47', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM57', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM67', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM77', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM87', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM97', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMA7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMB7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMC7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMD7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBME7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMF7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMG7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMH7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMI7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMJ7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMK7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBML7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMM7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMN7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMO7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMP7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMQ7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMR7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMS7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMT7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMU7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMV7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMW7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMX7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMY7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMZ7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM08', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM18', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM28', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM38', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM48', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM58']}
2025-05-31 02:31:43,781 - INFO - 批量插入表单数据成功，批次 1，共 57 条记录
2025-05-31 02:31:43,781 - INFO - 成功插入的数据ID: ['FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBML6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMM6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMN6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMO6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMP6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMQ6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMR6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMS6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMT6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMU6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMV6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMW6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMX6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMY6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMZ6', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM07', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM17', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM27', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM37', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM47', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM57', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM67', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM77', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM87', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM97', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMA7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMB7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMC7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMD7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBME7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMF7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMG7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMH7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMI7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMJ7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMK7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBML7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMM7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMN7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMO7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMP7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMQ7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMR7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMS7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMT7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMU7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMV7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMW7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMX7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMY7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBMZ7', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM08', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM18', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM28', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM38', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM48', 'FINST-ZNE66RC1FEUVMBJUDPU9XDOZE2SV2LTG35BBM58']
2025-05-31 02:31:48,782 - INFO - 批量插入完成，共 57 条记录
2025-05-31 02:31:48,782 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 57 条，错误: 0 条
2025-05-31 02:31:48,782 - INFO - 数据同步完成！更新: 0 条，插入: 57 条，错误: 0 条
2025-05-31 02:31:48,782 - INFO - 同步完成
2025-05-31 03:30:34,293 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 03:30:34,293 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 03:30:34,293 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 03:30:34,359 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 3 条记录
2025-05-31 03:30:34,360 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 03:30:34,360 - INFO - 开始处理日期: 2025-05-30
2025-05-31 03:30:34,363 - INFO - Request Parameters - Page 1:
2025-05-31 03:30:34,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:30:34,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:30:42,499 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 069D5644-609F-7D4F-8476-9228486589C5 Response: {'code': 'ServiceUnavailable', 'requestid': '069D5644-609F-7D4F-8476-9228486589C5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 069D5644-609F-7D4F-8476-9228486589C5)
2025-05-31 03:30:42,500 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 03:31:42,502 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 03:31:42,502 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 03:31:42,502 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 03:31:42,571 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 113 条记录
2025-05-31 03:31:42,572 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 03:31:42,573 - INFO - 开始处理日期: 2025-05-30
2025-05-31 03:31:42,573 - INFO - Request Parameters - Page 1:
2025-05-31 03:31:42,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:31:42,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:31:43,501 - INFO - Response - Page 1:
2025-05-31 03:31:43,501 - INFO - 第 1 页获取到 100 条记录
2025-05-31 03:31:43,701 - INFO - Request Parameters - Page 2:
2025-05-31 03:31:43,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:31:43,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:31:47,133 - INFO - Response - Page 2:
2025-05-31 03:31:47,133 - INFO - 第 2 页获取到 13 条记录
2025-05-31 03:31:47,334 - INFO - 查询完成，共获取到 113 条记录
2025-05-31 03:31:47,334 - INFO - 获取到 113 条表单数据
2025-05-31 03:31:47,336 - INFO - 当前日期 2025-05-30 有 113 条MySQL数据需要处理
2025-05-31 03:31:47,338 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 03:31:47,338 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 03:31:47,338 - INFO - 同步完成
2025-05-31 04:30:34,197 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 04:30:34,197 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 04:30:34,197 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 04:30:34,265 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 3 条记录
2025-05-31 04:30:34,265 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 04:30:34,265 - INFO - 开始处理日期: 2025-05-30
2025-05-31 04:30:34,268 - INFO - Request Parameters - Page 1:
2025-05-31 04:30:34,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 04:30:34,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 04:30:42,400 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 52255C61-4694-7042-9B41-1479017807AF Response: {'code': 'ServiceUnavailable', 'requestid': '52255C61-4694-7042-9B41-1479017807AF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 52255C61-4694-7042-9B41-1479017807AF)
2025-05-31 04:30:42,400 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 04:31:42,402 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 04:31:42,402 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 04:31:42,402 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 04:31:42,474 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 113 条记录
2025-05-31 04:31:42,474 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 04:31:42,475 - INFO - 开始处理日期: 2025-05-30
2025-05-31 04:31:42,476 - INFO - Request Parameters - Page 1:
2025-05-31 04:31:42,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 04:31:42,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 04:31:50,583 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F7E5ED81-A159-7FDF-8DDD-7F6A48AE052B Response: {'code': 'ServiceUnavailable', 'requestid': 'F7E5ED81-A159-7FDF-8DDD-7F6A48AE052B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F7E5ED81-A159-7FDF-8DDD-7F6A48AE052B)
2025-05-31 04:31:50,583 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 04:31:50,583 - INFO - 同步完成
2025-05-31 05:30:33,173 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 05:30:33,173 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 05:30:33,173 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 05:30:33,240 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 4 条记录
2025-05-31 05:30:33,241 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 05:30:33,241 - INFO - 开始处理日期: 2025-05-30
2025-05-31 05:30:33,243 - INFO - Request Parameters - Page 1:
2025-05-31 05:30:33,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 05:30:33,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 05:30:41,378 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 32881A54-1903-7932-B5C6-69FF320C771E Response: {'code': 'ServiceUnavailable', 'requestid': '32881A54-1903-7932-B5C6-69FF320C771E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 32881A54-1903-7932-B5C6-69FF320C771E)
2025-05-31 05:30:41,378 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 05:31:44,503 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 05:31:44,503 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 05:31:44,503 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 05:31:44,572 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 114 条记录
2025-05-31 05:31:44,572 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 05:31:44,574 - INFO - 开始处理日期: 2025-05-30
2025-05-31 05:31:44,574 - INFO - Request Parameters - Page 1:
2025-05-31 05:31:44,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 05:31:44,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 05:31:45,527 - INFO - Response - Page 1:
2025-05-31 05:31:45,527 - INFO - 第 1 页获取到 100 条记录
2025-05-31 05:31:45,727 - INFO - Request Parameters - Page 2:
2025-05-31 05:31:45,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 05:31:45,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 05:31:46,424 - INFO - Response - Page 2:
2025-05-31 05:31:46,424 - INFO - 第 2 页获取到 13 条记录
2025-05-31 05:31:46,624 - INFO - 查询完成，共获取到 113 条记录
2025-05-31 05:31:46,624 - INFO - 获取到 113 条表单数据
2025-05-31 05:31:46,626 - INFO - 当前日期 2025-05-30 有 114 条MySQL数据需要处理
2025-05-31 05:31:46,628 - INFO - 开始批量插入 1 条新记录
2025-05-31 05:31:46,819 - INFO - 批量插入响应状态码: 200
2025-05-31 05:31:46,819 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 30 May 2025 21:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C1B73F20-F073-7791-96B3-EA8765FA8D7F', 'x-acs-trace-id': '85b827a5bd02f1b45263e2087d98d17f', 'etag': '66ZChi6HryGjiiZYi6KcjDg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 05:31:46,819 - INFO - 批量插入响应体: {'result': ['FINST-MLF66JA1SDTVNRNI7QHB746TE5Z52MCYIBBBM4F']}
2025-05-31 05:31:46,819 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-31 05:31:46,819 - INFO - 成功插入的数据ID: ['FINST-MLF66JA1SDTVNRNI7QHB746TE5Z52MCYIBBBM4F']
2025-05-31 05:31:51,820 - INFO - 批量插入完成，共 1 条记录
2025-05-31 05:31:51,820 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-31 05:31:51,820 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-05-31 05:31:51,820 - INFO - 同步完成
2025-05-31 06:30:34,204 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 06:30:34,205 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 06:30:34,205 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 06:30:34,271 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 4 条记录
2025-05-31 06:30:34,271 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 06:30:34,271 - INFO - 开始处理日期: 2025-05-30
2025-05-31 06:30:34,274 - INFO - Request Parameters - Page 1:
2025-05-31 06:30:34,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:30:34,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:30:42,408 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E8C715FB-3BB2-725A-A657-E7EBA836F404 Response: {'code': 'ServiceUnavailable', 'requestid': 'E8C715FB-3BB2-725A-A657-E7EBA836F404', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E8C715FB-3BB2-725A-A657-E7EBA836F404)
2025-05-31 06:30:42,409 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 06:31:42,409 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 06:31:42,409 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 06:31:42,409 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 06:31:42,479 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 114 条记录
2025-05-31 06:31:42,480 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 06:31:42,481 - INFO - 开始处理日期: 2025-05-30
2025-05-31 06:31:42,481 - INFO - Request Parameters - Page 1:
2025-05-31 06:31:42,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:31:42,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:31:43,508 - INFO - Response - Page 1:
2025-05-31 06:31:43,508 - INFO - 第 1 页获取到 100 条记录
2025-05-31 06:31:43,708 - INFO - Request Parameters - Page 2:
2025-05-31 06:31:43,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:31:43,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:31:44,352 - INFO - Response - Page 2:
2025-05-31 06:31:44,352 - INFO - 第 2 页获取到 14 条记录
2025-05-31 06:31:44,552 - INFO - 查询完成，共获取到 114 条记录
2025-05-31 06:31:44,552 - INFO - 获取到 114 条表单数据
2025-05-31 06:31:44,554 - INFO - 当前日期 2025-05-30 有 114 条MySQL数据需要处理
2025-05-31 06:31:44,556 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 06:31:44,556 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 06:31:44,556 - INFO - 同步完成
2025-05-31 07:30:33,839 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 07:30:33,840 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 07:30:33,840 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 07:30:33,905 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 4 条记录
2025-05-31 07:30:33,905 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 07:30:33,905 - INFO - 开始处理日期: 2025-05-30
2025-05-31 07:30:33,908 - INFO - Request Parameters - Page 1:
2025-05-31 07:30:33,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 07:30:33,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 07:30:42,042 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8116DC12-FF52-7B21-92E4-B6D65546242E Response: {'code': 'ServiceUnavailable', 'requestid': '8116DC12-FF52-7B21-92E4-B6D65546242E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8116DC12-FF52-7B21-92E4-B6D65546242E)
2025-05-31 07:30:42,042 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 07:31:42,042 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 07:31:42,042 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 07:31:42,042 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 07:31:42,111 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 114 条记录
2025-05-31 07:31:42,111 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 07:31:42,112 - INFO - 开始处理日期: 2025-05-30
2025-05-31 07:31:42,113 - INFO - Request Parameters - Page 1:
2025-05-31 07:31:42,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 07:31:42,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 07:31:50,227 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EE91B9E9-48BE-7520-863F-5F52194D1E6F Response: {'code': 'ServiceUnavailable', 'requestid': 'EE91B9E9-48BE-7520-863F-5F52194D1E6F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EE91B9E9-48BE-7520-863F-5F52194D1E6F)
2025-05-31 07:31:50,227 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 07:31:50,227 - INFO - 同步完成
2025-05-31 08:30:34,123 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 08:30:34,123 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 08:30:34,123 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 08:30:34,190 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 6 条记录
2025-05-31 08:30:34,190 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 08:30:34,190 - INFO - 开始处理日期: 2025-05-30
2025-05-31 08:30:34,193 - INFO - Request Parameters - Page 1:
2025-05-31 08:30:34,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 08:30:34,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 08:30:42,296 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B22A9397-F899-7DE1-8603-7FC58A8E36F5 Response: {'code': 'ServiceUnavailable', 'requestid': 'B22A9397-F899-7DE1-8603-7FC58A8E36F5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B22A9397-F899-7DE1-8603-7FC58A8E36F5)
2025-05-31 08:30:42,296 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 08:31:42,297 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 08:31:42,297 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 08:31:42,297 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 08:31:42,366 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 116 条记录
2025-05-31 08:31:42,366 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 08:31:42,367 - INFO - 开始处理日期: 2025-05-30
2025-05-31 08:31:42,368 - INFO - Request Parameters - Page 1:
2025-05-31 08:31:42,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 08:31:42,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 08:31:43,275 - INFO - Response - Page 1:
2025-05-31 08:31:43,275 - INFO - 第 1 页获取到 100 条记录
2025-05-31 08:31:43,476 - INFO - Request Parameters - Page 2:
2025-05-31 08:31:43,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 08:31:43,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 08:31:44,117 - INFO - Response - Page 2:
2025-05-31 08:31:44,117 - INFO - 第 2 页获取到 14 条记录
2025-05-31 08:31:44,317 - INFO - 查询完成，共获取到 114 条记录
2025-05-31 08:31:44,317 - INFO - 获取到 114 条表单数据
2025-05-31 08:31:44,319 - INFO - 当前日期 2025-05-30 有 116 条MySQL数据需要处理
2025-05-31 08:31:44,321 - INFO - 开始批量插入 2 条新记录
2025-05-31 08:31:44,482 - INFO - 批量插入响应状态码: 200
2025-05-31 08:31:44,482 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 31 May 2025 00:31:41 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2281906C-746A-7B12-9F83-626DF380D7D8', 'x-acs-trace-id': 'c4eb0e15be0cc4ee315e61f82eedbeb9', 'etag': '1uOStx/HH8HHNTxvrvvvLNg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 08:31:44,482 - INFO - 批量插入响应体: {'result': ['FINST-FPB66VB1S5VVNI1BF94TK4UA3KKL3PXDYHBBMB7', 'FINST-FPB66VB1S5VVNI1BF94TK4UA3KKL3PXDYHBBMC7']}
2025-05-31 08:31:44,482 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-05-31 08:31:44,482 - INFO - 成功插入的数据ID: ['FINST-FPB66VB1S5VVNI1BF94TK4UA3KKL3PXDYHBBMB7', 'FINST-FPB66VB1S5VVNI1BF94TK4UA3KKL3PXDYHBBMC7']
2025-05-31 08:31:49,483 - INFO - 批量插入完成，共 2 条记录
2025-05-31 08:31:49,483 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-05-31 08:31:49,483 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-05-31 08:31:49,483 - INFO - 同步完成
2025-05-31 09:30:34,208 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 09:30:34,208 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 09:30:34,208 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 09:30:34,277 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 71 条记录
2025-05-31 09:30:34,277 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 09:30:34,278 - INFO - 开始处理日期: 2025-05-30
2025-05-31 09:30:34,282 - INFO - Request Parameters - Page 1:
2025-05-31 09:30:34,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:30:34,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:30:42,408 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5129AB51-DC7D-794D-AF43-CE682971BBB0 Response: {'code': 'ServiceUnavailable', 'requestid': '5129AB51-DC7D-794D-AF43-CE682971BBB0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5129AB51-DC7D-794D-AF43-CE682971BBB0)
2025-05-31 09:30:42,408 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 09:31:42,409 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 09:31:42,409 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 09:31:42,409 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 09:31:42,493 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 339 条记录
2025-05-31 09:31:42,494 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 09:31:42,498 - INFO - 开始处理日期: 2025-05-30
2025-05-31 09:31:42,498 - INFO - Request Parameters - Page 1:
2025-05-31 09:31:42,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:31:42,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:31:43,377 - INFO - Response - Page 1:
2025-05-31 09:31:43,377 - INFO - 第 1 页获取到 100 条记录
2025-05-31 09:31:43,577 - INFO - Request Parameters - Page 2:
2025-05-31 09:31:43,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:31:43,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:31:44,215 - INFO - Response - Page 2:
2025-05-31 09:31:44,215 - INFO - 第 2 页获取到 16 条记录
2025-05-31 09:31:44,417 - INFO - 查询完成，共获取到 116 条记录
2025-05-31 09:31:44,417 - INFO - 获取到 116 条表单数据
2025-05-31 09:31:44,418 - INFO - 当前日期 2025-05-30 有 339 条MySQL数据需要处理
2025-05-31 09:31:44,422 - INFO - 开始批量插入 223 条新记录
2025-05-31 09:31:44,759 - INFO - 批量插入响应状态码: 200
2025-05-31 09:31:44,759 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 31 May 2025 01:31:41 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4781', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '096C3AEB-F228-7CBB-8A9C-D8736565E6FD', 'x-acs-trace-id': 'd7bf8a741bc7f4d41a6655ba4d0c572a', 'etag': '4hFIW/+jeCKVZD4T8CTC0kQ1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 09:31:44,759 - INFO - 批量插入响应体: {'result': ['FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBM5', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBM6', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBM7', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBM8', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBM9', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMA', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMB', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMC', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMD', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBME', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMF', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMG', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMH', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMI', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMJ', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMK', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBML', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMM', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMN', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMO', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMP', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMQ', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMR', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMS', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMT', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMU', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMV', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMW', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMX', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMY', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMZ', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBM01', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM11', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM21', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM31', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM41', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM51', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM61', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM71', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM81', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM91', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMA1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMB1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMC1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMD1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBME1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMF1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMG1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMH1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMI1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMJ1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMK1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBML1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMM1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMN1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMO1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMP1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMQ1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMR1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMS1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMT1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMU1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMV1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMW1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMX1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMY1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMZ1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM02', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM12', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM22', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM32', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM42', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM52', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM62', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM72', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM82', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM92', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMA2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMB2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMC2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMD2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBME2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMF2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMG2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMH2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMI2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMJ2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMK2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBML2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMM2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMN2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMO2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMP2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMQ2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMR2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMS2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMT2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMU2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMV2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMW2']}
2025-05-31 09:31:44,759 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-31 09:31:44,759 - INFO - 成功插入的数据ID: ['FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBM5', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBM6', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBM7', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBM8', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBM9', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMA', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMB', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMC', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMD', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBME', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMF', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMG', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMH', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMI', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMJ', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMK', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBML', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMM', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMN', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMO', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMP', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMQ', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMR', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMS', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMT', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMU', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMV', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMW', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMX', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMY', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBMZ', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3SXJ3KBBM01', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM11', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM21', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM31', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM41', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM51', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM61', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM71', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM81', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM91', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMA1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMB1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMC1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMD1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBME1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMF1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMG1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMH1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMI1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMJ1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMK1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBML1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMM1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMN1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMO1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMP1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMQ1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMR1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMS1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMT1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMU1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMV1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMW1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMX1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMY1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMZ1', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM02', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM12', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM22', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM32', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM42', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM52', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM62', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM72', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM82', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBM92', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMA2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMB2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMC2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMD2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBME2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMF2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMG2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMH2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMI2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMJ2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMK2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBML2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMM2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMN2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMO2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMP2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMQ2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMR2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMS2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMT2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMU2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMV2', 'FINST-KLF66WC1LLVVEJCBAFKU449NO4HD3TXJ3KBBMW2']
2025-05-31 09:31:50,056 - INFO - 批量插入响应状态码: 200
2025-05-31 09:31:50,056 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 31 May 2025 01:31:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7D4AEC11-5374-73C5-8314-C938272A86F8', 'x-acs-trace-id': 'b55d1540f5780d72293c2e2e400b12f9', 'etag': '4s676Ej3picfBll1Kzw6nOg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 09:31:50,056 - INFO - 批量插入响应体: {'result': ['FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMX8', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMY8', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMZ8', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM09', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM19', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM29', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM39', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM49', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM59', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM69', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM79', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM89', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM99', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMA9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMB9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMC9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMD9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBME9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMF9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMG9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMH9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMI9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMJ9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMK9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBML9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMM9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMN9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMO9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMP9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMQ9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMR9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMS9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMT9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMU9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMV9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMW9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMX9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMY9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMZ9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM0A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM1A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM2A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM3A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM4A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM5A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM6A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM7A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM8A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM9A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMAA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMBA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMCA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMDA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMEA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMFA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMGA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMHA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMIA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMJA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMKA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMLA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMMA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMNA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMOA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMPA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMQA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMRA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMSA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMTA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMUA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMVA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMWA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMXA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMYA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMZA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM0B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM1B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM2B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM3B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM4B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM5B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM6B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM7B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM8B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM9B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMAB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMBB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMCB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMDB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMEB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMFB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMGB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMHB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMIB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMJB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMKB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMLB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMMB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMNB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMOB']}
2025-05-31 09:31:50,056 - INFO - 批量插入表单数据成功，批次 2，共 100 条记录
2025-05-31 09:31:50,057 - INFO - 成功插入的数据ID: ['FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMX8', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMY8', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMZ8', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM09', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM19', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM29', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM39', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM49', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM59', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM69', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM79', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM89', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM99', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMA9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMB9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMC9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMD9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBME9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMF9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMG9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMH9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMI9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMJ9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMK9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBML9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMM9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMN9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMO9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMP9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMQ9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMR9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMS9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMT9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMU9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMV9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMW9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMX9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMY9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMZ9', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM0A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM1A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM2A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM3A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM4A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM5A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM6A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM7A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM8A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM9A', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMAA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMBA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMCA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMDA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMEA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMFA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMGA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMHA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMIA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMJA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMKA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMLA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMMA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMNA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMOA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMPA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMQA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMRA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMSA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMTA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMUA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMVA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMWA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMXA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMYA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMZA', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM0B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM1B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM2B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM3B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM4B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM5B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM6B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM7B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM8B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBM9B', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMAB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMBB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMCB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMDB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMEB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMFB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMGB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMHB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMIB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMJB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMKB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMLB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMMB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMNB', 'FINST-2PF662C179VV6PBQ9J566BJHIDCB251O3KBBMOB']
2025-05-31 09:31:55,203 - INFO - 批量插入响应状态码: 200
2025-05-31 09:31:55,203 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 31 May 2025 01:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1139', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '506A8B35-FE61-7D72-B72A-583DA2A061C3', 'x-acs-trace-id': 'd43ce1dfbd4d3eee72ca0a87a9cfd7f2', 'etag': '1QQjsZRRWJEfhQRoDwAX/5w9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 09:31:55,203 - INFO - 批量插入响应体: {'result': ['FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMO61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMP61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMQ61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMR61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMS61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMT61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMU61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMV61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMW61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMX61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMY61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMZ61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM071', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM171', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM271', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM371', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM471', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM571', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM671', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM771', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM871', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM971', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMA71']}
2025-05-31 09:31:55,203 - INFO - 批量插入表单数据成功，批次 3，共 23 条记录
2025-05-31 09:31:55,203 - INFO - 成功插入的数据ID: ['FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMO61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMP61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMQ61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMR61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMS61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMT61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMU61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMV61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMW61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMX61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMY61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMZ61', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM071', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM171', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM271', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM371', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM471', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM571', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM671', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM771', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM871', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBM971', 'FINST-QZE668D1FNRVKOE9AAPUVAWM7WLB380S3KBBMA71']
2025-05-31 09:32:00,204 - INFO - 批量插入完成，共 223 条记录
2025-05-31 09:32:00,204 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 223 条，错误: 0 条
2025-05-31 09:32:00,204 - INFO - 数据同步完成！更新: 0 条，插入: 223 条，错误: 0 条
2025-05-31 09:32:00,204 - INFO - 同步完成
2025-05-31 10:30:34,293 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 10:30:34,293 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 10:30:34,293 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 10:30:34,366 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 103 条记录
2025-05-31 10:30:34,366 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 10:30:34,367 - INFO - 开始处理日期: 2025-05-30
2025-05-31 10:30:34,371 - INFO - Request Parameters - Page 1:
2025-05-31 10:30:34,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 10:30:34,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 10:30:42,507 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4A20CB5B-B814-7082-82C4-1FA1793751FE Response: {'code': 'ServiceUnavailable', 'requestid': '4A20CB5B-B814-7082-82C4-1FA1793751FE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4A20CB5B-B814-7082-82C4-1FA1793751FE)
2025-05-31 10:30:42,507 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 10:31:42,508 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 10:31:42,508 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 10:31:42,508 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 10:31:42,585 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 371 条记录
2025-05-31 10:31:42,585 - INFO - 获取到 1 个日期需要处理: ['2025-05-30']
2025-05-31 10:31:42,589 - INFO - 开始处理日期: 2025-05-30
2025-05-31 10:31:42,589 - INFO - Request Parameters - Page 1:
2025-05-31 10:31:42,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 10:31:42,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 10:31:43,446 - INFO - Response - Page 1:
2025-05-31 10:31:43,446 - INFO - 第 1 页获取到 100 条记录
2025-05-31 10:31:43,647 - INFO - Request Parameters - Page 2:
2025-05-31 10:31:43,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 10:31:43,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 10:31:44,522 - INFO - Response - Page 2:
2025-05-31 10:31:44,522 - INFO - 第 2 页获取到 100 条记录
2025-05-31 10:31:44,723 - INFO - Request Parameters - Page 3:
2025-05-31 10:31:44,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 10:31:44,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 10:31:45,520 - INFO - Response - Page 3:
2025-05-31 10:31:45,520 - INFO - 第 3 页获取到 100 条记录
2025-05-31 10:31:45,720 - INFO - Request Parameters - Page 4:
2025-05-31 10:31:45,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 10:31:45,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 10:31:46,480 - INFO - Response - Page 4:
2025-05-31 10:31:46,481 - INFO - 第 4 页获取到 39 条记录
2025-05-31 10:31:46,682 - INFO - 查询完成，共获取到 339 条记录
2025-05-31 10:31:46,682 - INFO - 获取到 339 条表单数据
2025-05-31 10:31:46,688 - INFO - 当前日期 2025-05-30 有 371 条MySQL数据需要处理
2025-05-31 10:31:46,693 - INFO - 开始批量插入 32 条新记录
2025-05-31 10:31:46,914 - INFO - 批量插入响应状态码: 200
2025-05-31 10:31:46,914 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 31 May 2025 02:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1548', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '170AE560-E5B4-7273-A39C-3AB776B7CC0C', 'x-acs-trace-id': '5caded125638e8f731b4504ab7bd3554', 'etag': '1lNveEkiCfpeRNfzIvsxXsQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 10:31:46,914 - INFO - 批量插入响应体: {'result': ['FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMLC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMMC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMNC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMOC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMPC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMQC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMRC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMSC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMTC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMUC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMVC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMWC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMXC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMYC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMZC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM0D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM1D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM2D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM3D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM4D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM5D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM6D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM7D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM8D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM9D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMAD', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMBD', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMCD', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMDD', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMED', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMFD', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMGD']}
2025-05-31 10:31:46,915 - INFO - 批量插入表单数据成功，批次 1，共 32 条记录
2025-05-31 10:31:46,915 - INFO - 成功插入的数据ID: ['FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMLC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMMC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMNC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMOC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMPC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMQC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMRC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMSC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMTC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMUC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMVC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMWC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMXC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMYC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBMZC', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM0D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM1D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM2D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM3D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM4D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM5D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM6D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM7D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM8D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43FER8MBBM9D', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMAD', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMBD', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMCD', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMDD', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMED', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMFD', 'FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMGD']
2025-05-31 10:31:51,916 - INFO - 批量插入完成，共 32 条记录
2025-05-31 10:31:51,916 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 32 条，错误: 0 条
2025-05-31 10:31:51,916 - INFO - 数据同步完成！更新: 0 条，插入: 32 条，错误: 0 条
2025-05-31 10:31:51,916 - INFO - 同步完成
2025-05-31 11:30:34,064 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 11:30:34,065 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 11:30:34,065 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 11:30:34,139 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 139 条记录
2025-05-31 11:30:34,140 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 11:30:34,141 - INFO - 开始处理日期: 2025-05-30
2025-05-31 11:30:34,145 - INFO - Request Parameters - Page 1:
2025-05-31 11:30:34,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 11:30:34,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 11:30:42,264 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 98789736-9CB6-7AD3-8DA1-C6E6EC4803A6 Response: {'code': 'ServiceUnavailable', 'requestid': '98789736-9CB6-7AD3-8DA1-C6E6EC4803A6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 98789736-9CB6-7AD3-8DA1-C6E6EC4803A6)
2025-05-31 11:30:42,264 - INFO - 开始处理日期: 2025-05-31
2025-05-31 11:30:42,264 - INFO - Request Parameters - Page 1:
2025-05-31 11:30:42,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 11:30:42,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 11:30:50,379 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 99D83FDC-016B-7225-95A2-397047F88AA9 Response: {'code': 'ServiceUnavailable', 'requestid': '99D83FDC-016B-7225-95A2-397047F88AA9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 99D83FDC-016B-7225-95A2-397047F88AA9)
2025-05-31 11:30:50,379 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-31 11:31:50,379 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 11:31:50,379 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 11:31:50,379 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 11:31:50,461 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 482 条记录
2025-05-31 11:31:50,461 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 11:31:50,466 - INFO - 开始处理日期: 2025-05-30
2025-05-31 11:31:50,466 - INFO - Request Parameters - Page 1:
2025-05-31 11:31:50,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 11:31:50,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 11:31:51,397 - INFO - Response - Page 1:
2025-05-31 11:31:51,397 - INFO - 第 1 页获取到 100 条记录
2025-05-31 11:31:51,597 - INFO - Request Parameters - Page 2:
2025-05-31 11:31:51,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 11:31:51,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 11:31:52,498 - INFO - Response - Page 2:
2025-05-31 11:31:52,498 - INFO - 第 2 页获取到 100 条记录
2025-05-31 11:31:52,698 - INFO - Request Parameters - Page 3:
2025-05-31 11:31:52,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 11:31:52,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 11:31:53,531 - INFO - Response - Page 3:
2025-05-31 11:31:53,531 - INFO - 第 3 页获取到 100 条记录
2025-05-31 11:31:53,731 - INFO - Request Parameters - Page 4:
2025-05-31 11:31:53,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 11:31:53,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 11:31:54,500 - INFO - Response - Page 4:
2025-05-31 11:31:54,501 - INFO - 第 4 页获取到 71 条记录
2025-05-31 11:31:54,702 - INFO - 查询完成，共获取到 371 条记录
2025-05-31 11:31:54,702 - INFO - 获取到 371 条表单数据
2025-05-31 11:31:54,708 - INFO - 当前日期 2025-05-30 有 481 条MySQL数据需要处理
2025-05-31 11:31:54,715 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMBD
2025-05-31 11:31:55,190 - INFO - 更新表单数据成功: FINST-XL866HB1FAVVLIOBD3NHZBA63IJ43GER8MBBMBD
2025-05-31 11:31:55,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19477.0, 'new_value': 14976.11}, {'field': 'total_amount', 'old_value': 19477.0, 'new_value': 14976.11}]
2025-05-31 11:31:55,191 - INFO - 开始批量插入 110 条新记录
2025-05-31 11:31:55,533 - INFO - 批量插入响应状态码: 200
2025-05-31 11:31:55,533 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 31 May 2025 03:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4797', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '98125632-B1A5-7FA1-B806-05DAD3CD7051', 'x-acs-trace-id': '29b7609c2c625d08701415d3041c744d', 'etag': '4kYZNCn7MOuRpqtQZv9GGvA7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 11:31:55,533 - INFO - 批量插入响应体: {'result': ['FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBML', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMM', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMN', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMO', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMP', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMQ', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMR', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMS', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMT', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMU', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMV', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMW', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMX', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMY', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMZ', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM01', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM11', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM21', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM31', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM41', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM51', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM61', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM71', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM81', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM91', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMA1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMB1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMC1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMD1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBME1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMF1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMG1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMH1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMI1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMJ1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMK1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBML1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMM1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMN1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMO1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMP1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMQ1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMR1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMS1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMT1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMU1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMV1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMW1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMX1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMY1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMZ1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM02', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM12', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM22', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM32', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM42', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM52', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM62', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM72', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM82', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM92', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMA2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMB2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMC2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMD2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBME2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMF2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMG2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMH2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMI2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMJ2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMK2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBML2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMM2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMN2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMO2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMP2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMQ2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMR2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMS2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMT2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMU2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMV2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMW2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMX2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMY2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMZ2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM03', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM13', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM23', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM33', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM43', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM53', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM63', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM73', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM83', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM93', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMA3', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMB3', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMC3']}
2025-05-31 11:31:55,533 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-31 11:31:55,534 - INFO - 成功插入的数据ID: ['FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBML', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMM', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMN', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMO', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMP', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMQ', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMR', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMS', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMT', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMU', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMV', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMW', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMX', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMY', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMZ', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM01', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM11', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM21', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM31', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM41', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM51', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM61', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM71', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM81', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM91', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMA1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMB1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMC1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMD1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBME1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMF1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMG1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMH1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMI1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMJ1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMK1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBML1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMM1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMN1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMO1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMP1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMQ1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMR1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMS1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMT1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMU1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMV1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMW1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMX1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMY1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMZ1', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM02', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM12', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM22', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM32', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM42', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM52', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM62', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM72', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM82', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM92', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMA2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMB2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMC2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMD2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBME2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMF2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMG2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMH2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMI2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMJ2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMK2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBML2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMM2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMN2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMO2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMP2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMQ2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMR2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMS2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMT2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMU2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMV2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMW2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMX2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMY2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMZ2', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM03', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM13', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM23', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM33', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM43', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM53', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM63', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM73', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM83', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM93', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMA3', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMB3', 'FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMC3']
2025-05-31 11:32:00,679 - INFO - 批量插入响应状态码: 200
2025-05-31 11:32:00,679 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 31 May 2025 03:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '492', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A12701BF-29C7-7E5F-A0A5-279F8591D250', 'x-acs-trace-id': 'ba04a13238cdb3d8d4a9127e75cc3d6a', 'etag': '4Ajhr6G73XRG/fthrW8Onww2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 11:32:00,679 - INFO - 批量插入响应体: {'result': ['FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBM41', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBM51', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBM61', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBM71', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBM81', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBM91', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBMA1', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBMB1', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBMC1', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBMD1']}
2025-05-31 11:32:00,679 - INFO - 批量插入表单数据成功，批次 2，共 10 条记录
2025-05-31 11:32:00,679 - INFO - 成功插入的数据ID: ['FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBM41', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBM51', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBM61', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBM71', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBM81', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBM91', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBMA1', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBMB1', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBMC1', 'FINST-2PF66KD148VV8PNYEFXQX4RL2CUB2OT7EOBBMD1']
2025-05-31 11:32:05,680 - INFO - 批量插入完成，共 110 条记录
2025-05-31 11:32:05,680 - INFO - 日期 2025-05-30 处理完成 - 更新: 1 条，插入: 110 条，错误: 0 条
2025-05-31 11:32:05,680 - INFO - 开始处理日期: 2025-05-31
2025-05-31 11:32:05,680 - INFO - Request Parameters - Page 1:
2025-05-31 11:32:05,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 11:32:05,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 11:32:06,204 - INFO - Response - Page 1:
2025-05-31 11:32:06,204 - INFO - 查询完成，共获取到 0 条记录
2025-05-31 11:32:06,204 - INFO - 获取到 0 条表单数据
2025-05-31 11:32:06,205 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 11:32:06,205 - INFO - 开始批量插入 1 条新记录
2025-05-31 11:32:06,371 - INFO - 批量插入响应状态码: 200
2025-05-31 11:32:06,371 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 31 May 2025 03:32:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3CA9E7E0-76BF-79CB-BD97-31E037CF6A67', 'x-acs-trace-id': '73a7f8e950dcab21a8364b3aea04273c', 'etag': '5pQBcs4R91mRwdxt2CbfV9A9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 11:32:06,371 - INFO - 批量插入响应体: {'result': ['FINST-A17661C11AWVZYCT6LZLR6DEA4ZB3N7CEOBBM1']}
2025-05-31 11:32:06,371 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-31 11:32:06,371 - INFO - 成功插入的数据ID: ['FINST-A17661C11AWVZYCT6LZLR6DEA4ZB3N7CEOBBM1']
2025-05-31 11:32:11,372 - INFO - 批量插入完成，共 1 条记录
2025-05-31 11:32:11,372 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-31 11:32:11,372 - INFO - 数据同步完成！更新: 1 条，插入: 111 条，错误: 0 条
2025-05-31 11:32:11,372 - INFO - 同步完成
2025-05-31 12:30:34,241 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 12:30:34,242 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 12:30:34,242 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 12:30:34,314 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 143 条记录
2025-05-31 12:30:34,315 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 12:30:34,316 - INFO - 开始处理日期: 2025-05-30
2025-05-31 12:30:34,320 - INFO - Request Parameters - Page 1:
2025-05-31 12:30:34,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:30:34,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:30:42,452 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 38166647-338D-71E4-B036-F99BDB8197BE Response: {'code': 'ServiceUnavailable', 'requestid': '38166647-338D-71E4-B036-F99BDB8197BE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 38166647-338D-71E4-B036-F99BDB8197BE)
2025-05-31 12:30:42,452 - INFO - 开始处理日期: 2025-05-31
2025-05-31 12:30:42,452 - INFO - Request Parameters - Page 1:
2025-05-31 12:30:42,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:30:42,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:30:50,540 - INFO - Response - Page 1:
2025-05-31 12:30:50,541 - INFO - 第 1 页获取到 1 条记录
2025-05-31 12:30:50,742 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 12:30:50,742 - INFO - 获取到 1 条表单数据
2025-05-31 12:30:50,742 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 12:30:50,742 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 12:30:50,742 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 12:31:50,742 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 12:31:50,742 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 12:31:50,742 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 12:31:50,824 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 486 条记录
2025-05-31 12:31:50,824 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 12:31:50,828 - INFO - 开始处理日期: 2025-05-30
2025-05-31 12:31:50,828 - INFO - Request Parameters - Page 1:
2025-05-31 12:31:50,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:31:50,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:31:51,814 - INFO - Response - Page 1:
2025-05-31 12:31:51,815 - INFO - 第 1 页获取到 100 条记录
2025-05-31 12:31:52,016 - INFO - Request Parameters - Page 2:
2025-05-31 12:31:52,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:31:52,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:31:52,881 - INFO - Response - Page 2:
2025-05-31 12:31:52,881 - INFO - 第 2 页获取到 100 条记录
2025-05-31 12:31:53,081 - INFO - Request Parameters - Page 3:
2025-05-31 12:31:53,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:31:53,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:32:01,196 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0F48D3FE-3A6F-7ED8-9F31-F2818988156D Response: {'code': 'ServiceUnavailable', 'requestid': '0F48D3FE-3A6F-7ED8-9F31-F2818988156D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0F48D3FE-3A6F-7ED8-9F31-F2818988156D)
2025-05-31 12:32:01,197 - INFO - 开始处理日期: 2025-05-31
2025-05-31 12:32:01,197 - INFO - Request Parameters - Page 1:
2025-05-31 12:32:01,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:32:01,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:32:01,741 - INFO - Response - Page 1:
2025-05-31 12:32:01,741 - INFO - 第 1 页获取到 1 条记录
2025-05-31 12:32:01,941 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 12:32:01,941 - INFO - 获取到 1 条表单数据
2025-05-31 12:32:01,941 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 12:32:01,941 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 12:32:01,941 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 12:32:01,942 - INFO - 同步完成
2025-05-31 13:30:34,239 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 13:30:34,240 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 13:30:34,240 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 13:30:34,312 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 145 条记录
2025-05-31 13:30:34,313 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 13:30:34,314 - INFO - 开始处理日期: 2025-05-30
2025-05-31 13:30:34,318 - INFO - Request Parameters - Page 1:
2025-05-31 13:30:34,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 13:30:34,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 13:30:42,429 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5525CFEC-6189-755E-AA44-8CDE76CD1BE4 Response: {'code': 'ServiceUnavailable', 'requestid': '5525CFEC-6189-755E-AA44-8CDE76CD1BE4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5525CFEC-6189-755E-AA44-8CDE76CD1BE4)
2025-05-31 13:30:42,430 - INFO - 开始处理日期: 2025-05-31
2025-05-31 13:30:42,430 - INFO - Request Parameters - Page 1:
2025-05-31 13:30:42,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 13:30:42,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 13:30:50,448 - INFO - Response - Page 1:
2025-05-31 13:30:50,449 - INFO - 第 1 页获取到 1 条记录
2025-05-31 13:30:50,649 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 13:30:50,649 - INFO - 获取到 1 条表单数据
2025-05-31 13:30:50,649 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 13:30:50,649 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 13:30:50,649 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 13:31:50,650 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 13:31:50,650 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 13:31:50,650 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 13:31:50,732 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 488 条记录
2025-05-31 13:31:50,732 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 13:31:50,737 - INFO - 开始处理日期: 2025-05-30
2025-05-31 13:31:50,737 - INFO - Request Parameters - Page 1:
2025-05-31 13:31:50,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 13:31:50,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 13:31:51,697 - INFO - Response - Page 1:
2025-05-31 13:31:51,697 - INFO - 第 1 页获取到 100 条记录
2025-05-31 13:31:51,897 - INFO - Request Parameters - Page 2:
2025-05-31 13:31:51,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 13:31:51,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 13:32:00,004 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 253818C7-BB90-7856-B844-3C7DE509CF07 Response: {'code': 'ServiceUnavailable', 'requestid': '253818C7-BB90-7856-B844-3C7DE509CF07', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 253818C7-BB90-7856-B844-3C7DE509CF07)
2025-05-31 13:32:00,005 - INFO - 开始处理日期: 2025-05-31
2025-05-31 13:32:00,005 - INFO - Request Parameters - Page 1:
2025-05-31 13:32:00,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 13:32:00,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 13:32:00,538 - INFO - Response - Page 1:
2025-05-31 13:32:00,538 - INFO - 第 1 页获取到 1 条记录
2025-05-31 13:32:00,740 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 13:32:00,740 - INFO - 获取到 1 条表单数据
2025-05-31 13:32:00,740 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 13:32:00,740 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 13:32:00,740 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 13:32:00,741 - INFO - 同步完成
2025-05-31 14:30:34,233 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 14:30:34,233 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 14:30:34,233 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 14:30:34,309 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 146 条记录
2025-05-31 14:30:34,309 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 14:30:34,311 - INFO - 开始处理日期: 2025-05-30
2025-05-31 14:30:34,315 - INFO - Request Parameters - Page 1:
2025-05-31 14:30:34,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 14:30:34,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 14:30:42,432 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DB5A88DF-F244-73B7-AD5E-1A304F6955DA Response: {'code': 'ServiceUnavailable', 'requestid': 'DB5A88DF-F244-73B7-AD5E-1A304F6955DA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DB5A88DF-F244-73B7-AD5E-1A304F6955DA)
2025-05-31 14:30:42,433 - INFO - 开始处理日期: 2025-05-31
2025-05-31 14:30:42,433 - INFO - Request Parameters - Page 1:
2025-05-31 14:30:42,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 14:30:42,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 14:30:50,560 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BA80634F-BA6A-70FC-B6D1-3BF70A601337 Response: {'code': 'ServiceUnavailable', 'requestid': 'BA80634F-BA6A-70FC-B6D1-3BF70A601337', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BA80634F-BA6A-70FC-B6D1-3BF70A601337)
2025-05-31 14:30:50,560 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-31 14:31:50,560 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 14:31:50,560 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 14:31:50,560 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 14:31:50,642 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 489 条记录
2025-05-31 14:31:50,642 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 14:31:50,646 - INFO - 开始处理日期: 2025-05-30
2025-05-31 14:31:50,646 - INFO - Request Parameters - Page 1:
2025-05-31 14:31:50,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 14:31:50,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 14:31:51,485 - INFO - Response - Page 1:
2025-05-31 14:31:51,485 - INFO - 第 1 页获取到 100 条记录
2025-05-31 14:31:51,685 - INFO - Request Parameters - Page 2:
2025-05-31 14:31:51,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 14:31:51,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 14:31:52,506 - INFO - Response - Page 2:
2025-05-31 14:31:52,506 - INFO - 第 2 页获取到 100 条记录
2025-05-31 14:31:52,706 - INFO - Request Parameters - Page 3:
2025-05-31 14:31:52,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 14:31:52,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 14:31:53,541 - INFO - Response - Page 3:
2025-05-31 14:31:53,542 - INFO - 第 3 页获取到 100 条记录
2025-05-31 14:31:53,742 - INFO - Request Parameters - Page 4:
2025-05-31 14:31:53,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 14:31:53,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 14:31:54,538 - INFO - Response - Page 4:
2025-05-31 14:31:54,538 - INFO - 第 4 页获取到 100 条记录
2025-05-31 14:31:54,738 - INFO - Request Parameters - Page 5:
2025-05-31 14:31:54,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 14:31:54,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 14:31:55,485 - INFO - Response - Page 5:
2025-05-31 14:31:55,485 - INFO - 第 5 页获取到 81 条记录
2025-05-31 14:31:55,685 - INFO - 查询完成，共获取到 481 条记录
2025-05-31 14:31:55,685 - INFO - 获取到 481 条表单数据
2025-05-31 14:31:55,693 - INFO - 当前日期 2025-05-30 有 488 条MySQL数据需要处理
2025-05-31 14:31:55,699 - INFO - 开始更新记录 - 表单实例ID: FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMU2
2025-05-31 14:31:56,185 - INFO - 更新表单数据成功: FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBMU2
2025-05-31 14:31:56,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 367508.0, 'new_value': 31075.0}, {'field': 'total_amount', 'old_value': 367508.0, 'new_value': 31075.0}]
2025-05-31 14:31:56,187 - INFO - 开始更新记录 - 表单实例ID: FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM93
2025-05-31 14:31:56,608 - INFO - 更新表单数据成功: FINST-LR5668B17IVVS5J1EO6XF7C9T3VL3EU3EOBBM93
2025-05-31 14:31:56,608 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124400.0, 'new_value': 12400.0}, {'field': 'total_amount', 'old_value': 124400.0, 'new_value': 12400.0}]
2025-05-31 14:31:56,609 - INFO - 开始批量插入 7 条新记录
2025-05-31 14:31:56,772 - INFO - 批量插入响应状态码: 200
2025-05-31 14:31:56,773 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 31 May 2025 06:31:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '348', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1811E84C-8F4B-7475-B0C3-5C9AEAB1D43F', 'x-acs-trace-id': '94522a12a561d5d6c5bbb7c7a17d2b5b', 'etag': '33FnwbyhDW/BuFynOdAmJEA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 14:31:56,773 - INFO - 批量插入响应体: {'result': ['FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBMB7', 'FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBMC7', 'FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBMD7', 'FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBME7', 'FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBMF7', 'FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBMG7', 'FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBMH7']}
2025-05-31 14:31:56,773 - INFO - 批量插入表单数据成功，批次 1，共 7 条记录
2025-05-31 14:31:56,773 - INFO - 成功插入的数据ID: ['FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBMB7', 'FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBMC7', 'FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBMD7', 'FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBME7', 'FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBMF7', 'FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBMG7', 'FINST-XBF66071EIUVGQAHAK3BMA8UXB1G357MTUBBMH7']
2025-05-31 14:32:01,774 - INFO - 批量插入完成，共 7 条记录
2025-05-31 14:32:01,774 - INFO - 日期 2025-05-30 处理完成 - 更新: 2 条，插入: 7 条，错误: 0 条
2025-05-31 14:32:01,774 - INFO - 开始处理日期: 2025-05-31
2025-05-31 14:32:01,774 - INFO - Request Parameters - Page 1:
2025-05-31 14:32:01,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 14:32:01,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 14:32:02,368 - INFO - Response - Page 1:
2025-05-31 14:32:02,368 - INFO - 第 1 页获取到 1 条记录
2025-05-31 14:32:02,568 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 14:32:02,568 - INFO - 获取到 1 条表单数据
2025-05-31 14:32:02,569 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 14:32:02,569 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 14:32:02,569 - INFO - 数据同步完成！更新: 2 条，插入: 7 条，错误: 0 条
2025-05-31 14:32:02,569 - INFO - 同步完成
2025-05-31 15:30:34,221 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 15:30:34,222 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 15:30:34,222 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 15:30:34,295 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 147 条记录
2025-05-31 15:30:34,295 - INFO - 获取到 3 个日期需要处理: ['2025-05-29', '2025-05-30', '2025-05-31']
2025-05-31 15:30:34,297 - INFO - 开始处理日期: 2025-05-29
2025-05-31 15:30:34,301 - INFO - Request Parameters - Page 1:
2025-05-31 15:30:34,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:30:34,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:30:42,407 - ERROR - 处理日期 2025-05-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 48C6BC63-4068-72B4-AE0E-22661274FE82 Response: {'code': 'ServiceUnavailable', 'requestid': '48C6BC63-4068-72B4-AE0E-22661274FE82', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 48C6BC63-4068-72B4-AE0E-22661274FE82)
2025-05-31 15:30:42,408 - INFO - 开始处理日期: 2025-05-30
2025-05-31 15:30:42,408 - INFO - Request Parameters - Page 1:
2025-05-31 15:30:42,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:30:42,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:30:50,665 - INFO - Response - Page 1:
2025-05-31 15:30:50,665 - INFO - 第 1 页获取到 100 条记录
2025-05-31 15:30:50,865 - INFO - Request Parameters - Page 2:
2025-05-31 15:30:50,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:30:50,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:30:51,728 - INFO - Response - Page 2:
2025-05-31 15:30:51,728 - INFO - 第 2 页获取到 100 条记录
2025-05-31 15:30:51,928 - INFO - Request Parameters - Page 3:
2025-05-31 15:30:51,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:30:51,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:30:52,825 - INFO - Response - Page 3:
2025-05-31 15:30:52,826 - INFO - 第 3 页获取到 100 条记录
2025-05-31 15:30:53,026 - INFO - Request Parameters - Page 4:
2025-05-31 15:30:53,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:30:53,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:31:00,714 - INFO - Response - Page 4:
2025-05-31 15:31:00,714 - INFO - 第 4 页获取到 100 条记录
2025-05-31 15:31:00,915 - INFO - Request Parameters - Page 5:
2025-05-31 15:31:00,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:31:00,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:31:01,732 - INFO - Response - Page 5:
2025-05-31 15:31:01,733 - INFO - 第 5 页获取到 88 条记录
2025-05-31 15:31:01,933 - INFO - 查询完成，共获取到 488 条记录
2025-05-31 15:31:01,933 - INFO - 获取到 488 条表单数据
2025-05-31 15:31:01,941 - INFO - 当前日期 2025-05-30 有 145 条MySQL数据需要处理
2025-05-31 15:31:01,944 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 15:31:01,944 - INFO - 开始处理日期: 2025-05-31
2025-05-31 15:31:01,944 - INFO - Request Parameters - Page 1:
2025-05-31 15:31:01,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:31:01,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:31:02,462 - INFO - Response - Page 1:
2025-05-31 15:31:02,462 - INFO - 第 1 页获取到 1 条记录
2025-05-31 15:31:02,663 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 15:31:02,663 - INFO - 获取到 1 条表单数据
2025-05-31 15:31:02,664 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 15:31:02,664 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 15:31:02,664 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-31 15:32:02,664 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 15:32:02,664 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 15:32:02,664 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 15:32:02,750 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 489 条记录
2025-05-31 15:32:02,751 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 15:32:02,755 - INFO - 开始处理日期: 2025-05-30
2025-05-31 15:32:02,755 - INFO - Request Parameters - Page 1:
2025-05-31 15:32:02,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:32:02,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:32:03,637 - INFO - Response - Page 1:
2025-05-31 15:32:03,637 - INFO - 第 1 页获取到 100 条记录
2025-05-31 15:32:03,837 - INFO - Request Parameters - Page 2:
2025-05-31 15:32:03,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:32:03,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:32:04,661 - INFO - Response - Page 2:
2025-05-31 15:32:04,661 - INFO - 第 2 页获取到 100 条记录
2025-05-31 15:32:04,861 - INFO - Request Parameters - Page 3:
2025-05-31 15:32:04,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:32:04,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:32:05,743 - INFO - Response - Page 3:
2025-05-31 15:32:05,744 - INFO - 第 3 页获取到 100 条记录
2025-05-31 15:32:05,944 - INFO - Request Parameters - Page 4:
2025-05-31 15:32:05,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:32:05,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:32:06,837 - INFO - Response - Page 4:
2025-05-31 15:32:06,837 - INFO - 第 4 页获取到 100 条记录
2025-05-31 15:32:07,037 - INFO - Request Parameters - Page 5:
2025-05-31 15:32:07,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:32:07,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:32:07,791 - INFO - Response - Page 5:
2025-05-31 15:32:07,791 - INFO - 第 5 页获取到 88 条记录
2025-05-31 15:32:07,991 - INFO - 查询完成，共获取到 488 条记录
2025-05-31 15:32:07,991 - INFO - 获取到 488 条表单数据
2025-05-31 15:32:08,000 - INFO - 当前日期 2025-05-30 有 488 条MySQL数据需要处理
2025-05-31 15:32:08,008 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 15:32:08,008 - INFO - 开始处理日期: 2025-05-31
2025-05-31 15:32:08,008 - INFO - Request Parameters - Page 1:
2025-05-31 15:32:08,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:32:08,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:32:08,529 - INFO - Response - Page 1:
2025-05-31 15:32:08,529 - INFO - 第 1 页获取到 1 条记录
2025-05-31 15:32:08,730 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 15:32:08,730 - INFO - 获取到 1 条表单数据
2025-05-31 15:32:08,731 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 15:32:08,731 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 15:32:08,731 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 15:32:08,731 - INFO - 同步完成
2025-05-31 16:30:33,761 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 16:30:33,762 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 16:30:33,762 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 16:30:33,838 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 192 条记录
2025-05-31 16:30:33,838 - INFO - 获取到 3 个日期需要处理: ['2025-05-29', '2025-05-30', '2025-05-31']
2025-05-31 16:30:33,840 - INFO - 开始处理日期: 2025-05-29
2025-05-31 16:30:33,842 - INFO - Request Parameters - Page 1:
2025-05-31 16:30:33,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 16:30:33,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 16:30:41,964 - ERROR - 处理日期 2025-05-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 174264F2-A018-7EDB-8CA4-6A4FBCA6880E Response: {'code': 'ServiceUnavailable', 'requestid': '174264F2-A018-7EDB-8CA4-6A4FBCA6880E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 174264F2-A018-7EDB-8CA4-6A4FBCA6880E)
2025-05-31 16:30:41,964 - INFO - 开始处理日期: 2025-05-30
2025-05-31 16:30:41,964 - INFO - Request Parameters - Page 1:
2025-05-31 16:30:41,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 16:30:41,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 16:30:50,077 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 892EBC93-3049-7E44-98D4-D879BD41DD08 Response: {'code': 'ServiceUnavailable', 'requestid': '892EBC93-3049-7E44-98D4-D879BD41DD08', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 892EBC93-3049-7E44-98D4-D879BD41DD08)
2025-05-31 16:30:50,077 - INFO - 开始处理日期: 2025-05-31
2025-05-31 16:30:50,077 - INFO - Request Parameters - Page 1:
2025-05-31 16:30:50,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 16:30:50,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 16:30:50,583 - INFO - Response - Page 1:
2025-05-31 16:30:50,584 - INFO - 第 1 页获取到 1 条记录
2025-05-31 16:30:50,784 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 16:30:50,784 - INFO - 获取到 1 条表单数据
2025-05-31 16:30:50,784 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 16:30:50,784 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 16:30:50,784 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-31 16:31:50,785 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 16:31:50,785 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 16:31:50,785 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 16:31:50,872 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 534 条记录
2025-05-31 16:31:50,872 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 16:31:50,876 - INFO - 开始处理日期: 2025-05-30
2025-05-31 16:31:50,877 - INFO - Request Parameters - Page 1:
2025-05-31 16:31:50,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 16:31:50,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 16:31:51,735 - INFO - Response - Page 1:
2025-05-31 16:31:51,735 - INFO - 第 1 页获取到 100 条记录
2025-05-31 16:31:51,935 - INFO - Request Parameters - Page 2:
2025-05-31 16:31:51,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 16:31:51,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 16:31:52,806 - INFO - Response - Page 2:
2025-05-31 16:31:52,806 - INFO - 第 2 页获取到 100 条记录
2025-05-31 16:31:53,006 - INFO - Request Parameters - Page 3:
2025-05-31 16:31:53,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 16:31:53,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 16:31:53,849 - INFO - Response - Page 3:
2025-05-31 16:31:53,849 - INFO - 第 3 页获取到 100 条记录
2025-05-31 16:31:54,049 - INFO - Request Parameters - Page 4:
2025-05-31 16:31:54,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 16:31:54,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 16:31:54,871 - INFO - Response - Page 4:
2025-05-31 16:31:54,871 - INFO - 第 4 页获取到 100 条记录
2025-05-31 16:31:55,071 - INFO - Request Parameters - Page 5:
2025-05-31 16:31:55,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 16:31:55,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 16:31:55,894 - INFO - Response - Page 5:
2025-05-31 16:31:55,894 - INFO - 第 5 页获取到 88 条记录
2025-05-31 16:31:56,094 - INFO - 查询完成，共获取到 488 条记录
2025-05-31 16:31:56,094 - INFO - 获取到 488 条表单数据
2025-05-31 16:31:56,104 - INFO - 当前日期 2025-05-30 有 533 条MySQL数据需要处理
2025-05-31 16:31:56,113 - INFO - 开始批量插入 45 条新记录
2025-05-31 16:31:56,347 - INFO - 批量插入响应状态码: 200
2025-05-31 16:31:56,348 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 31 May 2025 08:31:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2172', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '15205328-1068-74EC-97FD-E08D6270FE56', 'x-acs-trace-id': '20f036525b66f50c4e42b93206791ca1', 'etag': '2flPAU1A1V3G7PQeHFsE7+Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 16:31:56,348 - INFO - 批量插入响应体: {'result': ['FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMW9', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMX9', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMY9', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMZ9', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM0A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM1A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM2A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM3A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM4A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM5A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM6A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM7A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM8A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM9A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMAA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMBA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMCA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMDA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMEA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMFA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMGA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMHA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMIA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMJA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMKA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMLA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMMA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMNA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMOA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMPA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMQA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMRA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMSA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMTA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMUA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMVA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMWA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMXA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMYA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMZA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM0B', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM1B', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM2B', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM3B', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM4B']}
2025-05-31 16:31:56,348 - INFO - 批量插入表单数据成功，批次 1，共 45 条记录
2025-05-31 16:31:56,348 - INFO - 成功插入的数据ID: ['FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMW9', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMX9', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMY9', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMZ9', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM0A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM1A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM2A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM3A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM4A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM5A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM6A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM7A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM8A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM9A', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMAA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMBA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMCA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMDA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMEA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMFA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMGA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMHA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMIA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMJA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMKA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMLA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMMA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMNA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMOA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMPA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMQA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMRA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMSA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMTA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMUA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMVA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMWA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMXA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMYA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBMZA', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM0B', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM1B', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM2B', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM3B', 'FINST-LLF66O71P9VVE9J48FFRN9S9DNA62MGX3ZBBM4B']
2025-05-31 16:32:01,349 - INFO - 批量插入完成，共 45 条记录
2025-05-31 16:32:01,349 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 45 条，错误: 0 条
2025-05-31 16:32:01,349 - INFO - 开始处理日期: 2025-05-31
2025-05-31 16:32:01,349 - INFO - Request Parameters - Page 1:
2025-05-31 16:32:01,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 16:32:01,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 16:32:01,870 - INFO - Response - Page 1:
2025-05-31 16:32:01,870 - INFO - 第 1 页获取到 1 条记录
2025-05-31 16:32:02,070 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 16:32:02,070 - INFO - 获取到 1 条表单数据
2025-05-31 16:32:02,071 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 16:32:02,071 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 16:32:02,071 - INFO - 数据同步完成！更新: 0 条，插入: 45 条，错误: 0 条
2025-05-31 16:32:02,071 - INFO - 同步完成
2025-05-31 17:30:34,261 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 17:30:34,261 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 17:30:34,261 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 17:30:34,324 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 192 条记录
2025-05-31 17:30:34,324 - INFO - 获取到 3 个日期需要处理: ['2025-05-29', '2025-05-30', '2025-05-31']
2025-05-31 17:30:34,340 - INFO - 开始处理日期: 2025-05-29
2025-05-31 17:30:34,340 - INFO - Request Parameters - Page 1:
2025-05-31 17:30:34,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 17:30:34,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 17:30:42,449 - ERROR - 处理日期 2025-05-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 92B3E150-E999-7AB1-9433-72D4AC0E44F4 Response: {'code': 'ServiceUnavailable', 'requestid': '92B3E150-E999-7AB1-9433-72D4AC0E44F4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 92B3E150-E999-7AB1-9433-72D4AC0E44F4)
2025-05-31 17:30:42,449 - INFO - 开始处理日期: 2025-05-30
2025-05-31 17:30:42,449 - INFO - Request Parameters - Page 1:
2025-05-31 17:30:42,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 17:30:42,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 17:30:50,574 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 139E2A13-696F-7CE8-906C-7B8B643E739C Response: {'code': 'ServiceUnavailable', 'requestid': '139E2A13-696F-7CE8-906C-7B8B643E739C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 139E2A13-696F-7CE8-906C-7B8B643E739C)
2025-05-31 17:30:50,574 - INFO - 开始处理日期: 2025-05-31
2025-05-31 17:30:50,574 - INFO - Request Parameters - Page 1:
2025-05-31 17:30:50,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 17:30:50,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 17:30:58,699 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 671223EA-F20C-7912-AFB6-243EE61A216D Response: {'code': 'ServiceUnavailable', 'requestid': '671223EA-F20C-7912-AFB6-243EE61A216D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 671223EA-F20C-7912-AFB6-243EE61A216D)
2025-05-31 17:30:58,699 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-31 17:31:58,714 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 17:31:58,714 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 17:31:58,714 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 17:31:58,792 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 534 条记录
2025-05-31 17:31:58,792 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 17:31:58,792 - INFO - 开始处理日期: 2025-05-30
2025-05-31 17:31:58,792 - INFO - Request Parameters - Page 1:
2025-05-31 17:31:58,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 17:31:58,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 17:31:59,730 - INFO - Response - Page 1:
2025-05-31 17:31:59,730 - INFO - 第 1 页获取到 100 条记录
2025-05-31 17:31:59,933 - INFO - Request Parameters - Page 2:
2025-05-31 17:31:59,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 17:31:59,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 17:32:00,792 - INFO - Response - Page 2:
2025-05-31 17:32:00,792 - INFO - 第 2 页获取到 100 条记录
2025-05-31 17:32:00,995 - INFO - Request Parameters - Page 3:
2025-05-31 17:32:00,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 17:32:00,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 17:32:01,792 - INFO - Response - Page 3:
2025-05-31 17:32:01,792 - INFO - 第 3 页获取到 100 条记录
2025-05-31 17:32:01,995 - INFO - Request Parameters - Page 4:
2025-05-31 17:32:01,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 17:32:01,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 17:32:02,808 - INFO - Response - Page 4:
2025-05-31 17:32:02,808 - INFO - 第 4 页获取到 100 条记录
2025-05-31 17:32:03,011 - INFO - Request Parameters - Page 5:
2025-05-31 17:32:03,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 17:32:03,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 17:32:03,891 - INFO - Response - Page 5:
2025-05-31 17:32:03,891 - INFO - 第 5 页获取到 100 条记录
2025-05-31 17:32:04,092 - INFO - Request Parameters - Page 6:
2025-05-31 17:32:04,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 17:32:04,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 17:32:04,834 - INFO - Response - Page 6:
2025-05-31 17:32:04,834 - INFO - 第 6 页获取到 33 条记录
2025-05-31 17:32:05,034 - INFO - 查询完成，共获取到 533 条记录
2025-05-31 17:32:05,034 - INFO - 获取到 533 条表单数据
2025-05-31 17:32:05,043 - INFO - 当前日期 2025-05-30 有 533 条MySQL数据需要处理
2025-05-31 17:32:05,052 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 17:32:05,052 - INFO - 开始处理日期: 2025-05-31
2025-05-31 17:32:05,052 - INFO - Request Parameters - Page 1:
2025-05-31 17:32:05,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 17:32:05,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 17:32:05,560 - INFO - Response - Page 1:
2025-05-31 17:32:05,560 - INFO - 第 1 页获取到 1 条记录
2025-05-31 17:32:05,760 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 17:32:05,760 - INFO - 获取到 1 条表单数据
2025-05-31 17:32:05,761 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 17:32:05,761 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 17:32:05,761 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 17:32:05,761 - INFO - 同步完成
2025-05-31 18:30:35,340 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 18:30:35,340 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 18:30:35,340 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 18:30:35,418 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 192 条记录
2025-05-31 18:30:35,418 - INFO - 获取到 3 个日期需要处理: ['2025-05-29', '2025-05-30', '2025-05-31']
2025-05-31 18:30:35,434 - INFO - 开始处理日期: 2025-05-29
2025-05-31 18:30:35,434 - INFO - Request Parameters - Page 1:
2025-05-31 18:30:35,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:30:35,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:30:43,543 - ERROR - 处理日期 2025-05-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F781BD52-5290-743A-A066-1A303048128B Response: {'code': 'ServiceUnavailable', 'requestid': 'F781BD52-5290-743A-A066-1A303048128B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F781BD52-5290-743A-A066-1A303048128B)
2025-05-31 18:30:43,543 - INFO - 开始处理日期: 2025-05-30
2025-05-31 18:30:43,543 - INFO - Request Parameters - Page 1:
2025-05-31 18:30:43,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:30:43,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:30:51,668 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 99CB2860-381E-7D05-95B1-************ Response: {'code': 'ServiceUnavailable', 'requestid': '99CB2860-381E-7D05-95B1-************', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 99CB2860-381E-7D05-95B1-************)
2025-05-31 18:30:51,668 - INFO - 开始处理日期: 2025-05-31
2025-05-31 18:30:51,668 - INFO - Request Parameters - Page 1:
2025-05-31 18:30:51,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:30:51,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:30:58,928 - INFO - Response - Page 1:
2025-05-31 18:30:58,928 - INFO - 第 1 页获取到 1 条记录
2025-05-31 18:30:59,131 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 18:30:59,131 - INFO - 获取到 1 条表单数据
2025-05-31 18:30:59,131 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 18:30:59,131 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 18:30:59,131 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-31 18:31:59,131 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 18:31:59,131 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 18:31:59,131 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 18:31:59,219 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 534 条记录
2025-05-31 18:31:59,220 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 18:31:59,225 - INFO - 开始处理日期: 2025-05-30
2025-05-31 18:31:59,225 - INFO - Request Parameters - Page 1:
2025-05-31 18:31:59,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:31:59,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:32:00,078 - INFO - Response - Page 1:
2025-05-31 18:32:00,078 - INFO - 第 1 页获取到 100 条记录
2025-05-31 18:32:00,278 - INFO - Request Parameters - Page 2:
2025-05-31 18:32:00,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:32:00,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:32:01,156 - INFO - Response - Page 2:
2025-05-31 18:32:01,157 - INFO - 第 2 页获取到 100 条记录
2025-05-31 18:32:01,357 - INFO - Request Parameters - Page 3:
2025-05-31 18:32:01,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:32:01,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:32:02,213 - INFO - Response - Page 3:
2025-05-31 18:32:02,213 - INFO - 第 3 页获取到 100 条记录
2025-05-31 18:32:02,414 - INFO - Request Parameters - Page 4:
2025-05-31 18:32:02,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:32:02,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:32:03,325 - INFO - Response - Page 4:
2025-05-31 18:32:03,325 - INFO - 第 4 页获取到 100 条记录
2025-05-31 18:32:03,525 - INFO - Request Parameters - Page 5:
2025-05-31 18:32:03,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:32:03,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:32:04,499 - INFO - Response - Page 5:
2025-05-31 18:32:04,499 - INFO - 第 5 页获取到 100 条记录
2025-05-31 18:32:04,700 - INFO - Request Parameters - Page 6:
2025-05-31 18:32:04,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:32:04,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:32:05,397 - INFO - Response - Page 6:
2025-05-31 18:32:05,397 - INFO - 第 6 页获取到 33 条记录
2025-05-31 18:32:05,598 - INFO - 查询完成，共获取到 533 条记录
2025-05-31 18:32:05,598 - INFO - 获取到 533 条表单数据
2025-05-31 18:32:05,607 - INFO - 当前日期 2025-05-30 有 533 条MySQL数据需要处理
2025-05-31 18:32:05,616 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 18:32:05,616 - INFO - 开始处理日期: 2025-05-31
2025-05-31 18:32:05,616 - INFO - Request Parameters - Page 1:
2025-05-31 18:32:05,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:32:05,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:32:06,136 - INFO - Response - Page 1:
2025-05-31 18:32:06,136 - INFO - 第 1 页获取到 1 条记录
2025-05-31 18:32:06,336 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 18:32:06,336 - INFO - 获取到 1 条表单数据
2025-05-31 18:32:06,337 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 18:32:06,337 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 18:32:06,337 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 18:32:06,337 - INFO - 同步完成
2025-05-31 19:30:33,909 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 19:30:33,909 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 19:30:33,909 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 19:30:33,987 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 192 条记录
2025-05-31 19:30:33,987 - INFO - 获取到 3 个日期需要处理: ['2025-05-29', '2025-05-30', '2025-05-31']
2025-05-31 19:30:33,987 - INFO - 开始处理日期: 2025-05-29
2025-05-31 19:30:33,987 - INFO - Request Parameters - Page 1:
2025-05-31 19:30:33,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 19:30:33,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 19:30:42,111 - ERROR - 处理日期 2025-05-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 373FE1D2-2B9D-7EF8-BA97-BA3C05CDFEEA Response: {'code': 'ServiceUnavailable', 'requestid': '373FE1D2-2B9D-7EF8-BA97-BA3C05CDFEEA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 373FE1D2-2B9D-7EF8-BA97-BA3C05CDFEEA)
2025-05-31 19:30:42,111 - INFO - 开始处理日期: 2025-05-30
2025-05-31 19:30:42,111 - INFO - Request Parameters - Page 1:
2025-05-31 19:30:42,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 19:30:42,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 19:30:50,253 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B8EEC922-A1A9-775A-B1F4-84E57EF85BBD Response: {'code': 'ServiceUnavailable', 'requestid': 'B8EEC922-A1A9-775A-B1F4-84E57EF85BBD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B8EEC922-A1A9-775A-B1F4-84E57EF85BBD)
2025-05-31 19:30:50,253 - INFO - 开始处理日期: 2025-05-31
2025-05-31 19:30:50,253 - INFO - Request Parameters - Page 1:
2025-05-31 19:30:50,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 19:30:50,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 19:30:52,534 - INFO - Response - Page 1:
2025-05-31 19:30:52,534 - INFO - 第 1 页获取到 1 条记录
2025-05-31 19:30:52,736 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 19:30:52,736 - INFO - 获取到 1 条表单数据
2025-05-31 19:30:52,736 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 19:30:52,736 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 19:30:52,736 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-31 19:31:52,751 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 19:31:52,751 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 19:31:52,751 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 19:31:52,829 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 534 条记录
2025-05-31 19:31:52,829 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 19:31:52,829 - INFO - 开始处理日期: 2025-05-30
2025-05-31 19:31:52,829 - INFO - Request Parameters - Page 1:
2025-05-31 19:31:52,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 19:31:52,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 19:31:53,751 - INFO - Response - Page 1:
2025-05-31 19:31:53,751 - INFO - 第 1 页获取到 100 条记录
2025-05-31 19:31:53,954 - INFO - Request Parameters - Page 2:
2025-05-31 19:31:53,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 19:31:53,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 19:31:54,798 - INFO - Response - Page 2:
2025-05-31 19:31:54,798 - INFO - 第 2 页获取到 100 条记录
2025-05-31 19:31:55,001 - INFO - Request Parameters - Page 3:
2025-05-31 19:31:55,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 19:31:55,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 19:31:55,908 - INFO - Response - Page 3:
2025-05-31 19:31:55,908 - INFO - 第 3 页获取到 100 条记录
2025-05-31 19:31:56,111 - INFO - Request Parameters - Page 4:
2025-05-31 19:31:56,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 19:31:56,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 19:31:56,939 - INFO - Response - Page 4:
2025-05-31 19:31:56,939 - INFO - 第 4 页获取到 100 条记录
2025-05-31 19:31:57,142 - INFO - Request Parameters - Page 5:
2025-05-31 19:31:57,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 19:31:57,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 19:31:57,939 - INFO - Response - Page 5:
2025-05-31 19:31:57,939 - INFO - 第 5 页获取到 100 条记录
2025-05-31 19:31:58,142 - INFO - Request Parameters - Page 6:
2025-05-31 19:31:58,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 19:31:58,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 19:31:58,845 - INFO - Response - Page 6:
2025-05-31 19:31:58,845 - INFO - 第 6 页获取到 33 条记录
2025-05-31 19:31:59,048 - INFO - 查询完成，共获取到 533 条记录
2025-05-31 19:31:59,048 - INFO - 获取到 533 条表单数据
2025-05-31 19:31:59,048 - INFO - 当前日期 2025-05-30 有 533 条MySQL数据需要处理
2025-05-31 19:31:59,064 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 19:31:59,064 - INFO - 开始处理日期: 2025-05-31
2025-05-31 19:31:59,064 - INFO - Request Parameters - Page 1:
2025-05-31 19:31:59,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 19:31:59,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 19:31:59,564 - INFO - Response - Page 1:
2025-05-31 19:31:59,564 - INFO - 第 1 页获取到 1 条记录
2025-05-31 19:31:59,767 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 19:31:59,767 - INFO - 获取到 1 条表单数据
2025-05-31 19:31:59,767 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 19:31:59,767 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 19:31:59,767 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 19:31:59,767 - INFO - 同步完成
2025-05-31 20:30:33,561 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 20:30:33,561 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 20:30:33,561 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 20:30:33,655 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 192 条记录
2025-05-31 20:30:33,655 - INFO - 获取到 3 个日期需要处理: ['2025-05-29', '2025-05-30', '2025-05-31']
2025-05-31 20:30:33,655 - INFO - 开始处理日期: 2025-05-29
2025-05-31 20:30:33,655 - INFO - Request Parameters - Page 1:
2025-05-31 20:30:33,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 20:30:33,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 20:30:41,780 - ERROR - 处理日期 2025-05-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C331F75E-43DC-7183-BB35-806285195DC2 Response: {'code': 'ServiceUnavailable', 'requestid': 'C331F75E-43DC-7183-BB35-806285195DC2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C331F75E-43DC-7183-BB35-806285195DC2)
2025-05-31 20:30:41,780 - INFO - 开始处理日期: 2025-05-30
2025-05-31 20:30:41,780 - INFO - Request Parameters - Page 1:
2025-05-31 20:30:41,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 20:30:41,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 20:30:49,905 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 86AB6B4A-4AA7-7FD2-9EEF-42C4EC681D2C Response: {'code': 'ServiceUnavailable', 'requestid': '86AB6B4A-4AA7-7FD2-9EEF-42C4EC681D2C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 86AB6B4A-4AA7-7FD2-9EEF-42C4EC681D2C)
2025-05-31 20:30:49,905 - INFO - 开始处理日期: 2025-05-31
2025-05-31 20:30:49,905 - INFO - Request Parameters - Page 1:
2025-05-31 20:30:49,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 20:30:49,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 20:30:57,108 - INFO - Response - Page 1:
2025-05-31 20:30:57,108 - INFO - 第 1 页获取到 1 条记录
2025-05-31 20:30:57,311 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 20:30:57,311 - INFO - 获取到 1 条表单数据
2025-05-31 20:30:57,311 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 20:30:57,311 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 20:30:57,311 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-31 20:31:57,326 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 20:31:57,326 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 20:31:57,326 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 20:31:57,404 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 534 条记录
2025-05-31 20:31:57,404 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 20:31:57,404 - INFO - 开始处理日期: 2025-05-30
2025-05-31 20:31:57,404 - INFO - Request Parameters - Page 1:
2025-05-31 20:31:57,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 20:31:57,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 20:31:58,248 - INFO - Response - Page 1:
2025-05-31 20:31:58,248 - INFO - 第 1 页获取到 100 条记录
2025-05-31 20:31:58,451 - INFO - Request Parameters - Page 2:
2025-05-31 20:31:58,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 20:31:58,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 20:31:59,295 - INFO - Response - Page 2:
2025-05-31 20:31:59,295 - INFO - 第 2 页获取到 100 条记录
2025-05-31 20:31:59,498 - INFO - Request Parameters - Page 3:
2025-05-31 20:31:59,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 20:31:59,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 20:32:00,326 - INFO - Response - Page 3:
2025-05-31 20:32:00,326 - INFO - 第 3 页获取到 100 条记录
2025-05-31 20:32:00,529 - INFO - Request Parameters - Page 4:
2025-05-31 20:32:00,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 20:32:00,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 20:32:01,545 - INFO - Response - Page 4:
2025-05-31 20:32:01,545 - INFO - 第 4 页获取到 100 条记录
2025-05-31 20:32:01,748 - INFO - Request Parameters - Page 5:
2025-05-31 20:32:01,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 20:32:01,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 20:32:02,654 - INFO - Response - Page 5:
2025-05-31 20:32:02,654 - INFO - 第 5 页获取到 100 条记录
2025-05-31 20:32:02,857 - INFO - Request Parameters - Page 6:
2025-05-31 20:32:02,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 20:32:02,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 20:32:03,560 - INFO - Response - Page 6:
2025-05-31 20:32:03,560 - INFO - 第 6 页获取到 33 条记录
2025-05-31 20:32:03,763 - INFO - 查询完成，共获取到 533 条记录
2025-05-31 20:32:03,763 - INFO - 获取到 533 条表单数据
2025-05-31 20:32:03,763 - INFO - 当前日期 2025-05-30 有 533 条MySQL数据需要处理
2025-05-31 20:32:03,779 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 20:32:03,779 - INFO - 开始处理日期: 2025-05-31
2025-05-31 20:32:03,779 - INFO - Request Parameters - Page 1:
2025-05-31 20:32:03,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 20:32:03,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 20:32:04,310 - INFO - Response - Page 1:
2025-05-31 20:32:04,310 - INFO - 第 1 页获取到 1 条记录
2025-05-31 20:32:04,513 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 20:32:04,513 - INFO - 获取到 1 条表单数据
2025-05-31 20:32:04,513 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 20:32:04,513 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 20:32:04,513 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 20:32:04,513 - INFO - 同步完成
2025-05-31 21:30:33,578 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 21:30:33,578 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 21:30:33,578 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 21:30:33,656 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 192 条记录
2025-05-31 21:30:33,656 - INFO - 获取到 3 个日期需要处理: ['2025-05-29', '2025-05-30', '2025-05-31']
2025-05-31 21:30:33,656 - INFO - 开始处理日期: 2025-05-29
2025-05-31 21:30:33,656 - INFO - Request Parameters - Page 1:
2025-05-31 21:30:33,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:30:33,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:30:41,780 - ERROR - 处理日期 2025-05-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E19A70FD-A579-7063-9D6F-3509A183B2F3 Response: {'code': 'ServiceUnavailable', 'requestid': 'E19A70FD-A579-7063-9D6F-3509A183B2F3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E19A70FD-A579-7063-9D6F-3509A183B2F3)
2025-05-31 21:30:41,780 - INFO - 开始处理日期: 2025-05-30
2025-05-31 21:30:41,780 - INFO - Request Parameters - Page 1:
2025-05-31 21:30:41,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:30:41,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:30:49,899 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6F464E28-91FC-71D0-9A4A-E9E2E1E3C46A Response: {'code': 'ServiceUnavailable', 'requestid': '6F464E28-91FC-71D0-9A4A-E9E2E1E3C46A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6F464E28-91FC-71D0-9A4A-E9E2E1E3C46A)
2025-05-31 21:30:49,899 - INFO - 开始处理日期: 2025-05-31
2025-05-31 21:30:49,899 - INFO - Request Parameters - Page 1:
2025-05-31 21:30:49,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:30:49,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:30:50,431 - INFO - Response - Page 1:
2025-05-31 21:30:50,431 - INFO - 第 1 页获取到 1 条记录
2025-05-31 21:30:50,640 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 21:30:50,640 - INFO - 获取到 1 条表单数据
2025-05-31 21:30:50,640 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 21:30:50,640 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 21:30:50,640 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-31 21:31:50,641 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 21:31:50,641 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 21:31:50,641 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 21:31:50,724 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 534 条记录
2025-05-31 21:31:50,725 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 21:31:50,729 - INFO - 开始处理日期: 2025-05-30
2025-05-31 21:31:50,729 - INFO - Request Parameters - Page 1:
2025-05-31 21:31:50,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:31:50,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:31:51,633 - INFO - Response - Page 1:
2025-05-31 21:31:51,633 - INFO - 第 1 页获取到 100 条记录
2025-05-31 21:31:51,833 - INFO - Request Parameters - Page 2:
2025-05-31 21:31:51,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:31:51,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:31:52,680 - INFO - Response - Page 2:
2025-05-31 21:31:52,680 - INFO - 第 2 页获取到 100 条记录
2025-05-31 21:31:52,880 - INFO - Request Parameters - Page 3:
2025-05-31 21:31:52,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:31:52,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:31:53,709 - INFO - Response - Page 3:
2025-05-31 21:31:53,709 - INFO - 第 3 页获取到 100 条记录
2025-05-31 21:31:53,921 - INFO - Request Parameters - Page 4:
2025-05-31 21:31:53,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:31:53,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:31:54,702 - INFO - Response - Page 4:
2025-05-31 21:31:54,702 - INFO - 第 4 页获取到 100 条记录
2025-05-31 21:31:54,905 - INFO - Request Parameters - Page 5:
2025-05-31 21:31:54,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:31:54,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:31:55,718 - INFO - Response - Page 5:
2025-05-31 21:31:55,718 - INFO - 第 5 页获取到 100 条记录
2025-05-31 21:31:55,918 - INFO - Request Parameters - Page 6:
2025-05-31 21:31:55,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:31:55,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:31:56,596 - INFO - Response - Page 6:
2025-05-31 21:31:56,596 - INFO - 第 6 页获取到 33 条记录
2025-05-31 21:31:56,796 - INFO - 查询完成，共获取到 533 条记录
2025-05-31 21:31:56,796 - INFO - 获取到 533 条表单数据
2025-05-31 21:31:56,806 - INFO - 当前日期 2025-05-30 有 533 条MySQL数据需要处理
2025-05-31 21:31:56,816 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 21:31:56,816 - INFO - 开始处理日期: 2025-05-31
2025-05-31 21:31:56,817 - INFO - Request Parameters - Page 1:
2025-05-31 21:31:56,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:31:56,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:31:57,306 - INFO - Response - Page 1:
2025-05-31 21:31:57,306 - INFO - 第 1 页获取到 1 条记录
2025-05-31 21:31:57,506 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 21:31:57,506 - INFO - 获取到 1 条表单数据
2025-05-31 21:31:57,507 - INFO - 当前日期 2025-05-31 有 1 条MySQL数据需要处理
2025-05-31 21:31:57,507 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 21:31:57,507 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 21:31:57,507 - INFO - 同步完成
2025-05-31 22:30:33,562 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 22:30:33,562 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 22:30:33,562 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 22:30:33,640 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 240 条记录
2025-05-31 22:30:33,640 - INFO - 获取到 3 个日期需要处理: ['2025-05-29', '2025-05-30', '2025-05-31']
2025-05-31 22:30:33,640 - INFO - 开始处理日期: 2025-05-29
2025-05-31 22:30:33,640 - INFO - Request Parameters - Page 1:
2025-05-31 22:30:33,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 22:30:33,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 22:30:41,779 - ERROR - 处理日期 2025-05-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DA722871-F421-7AD3-9C67-C1B3E85D04F2 Response: {'code': 'ServiceUnavailable', 'requestid': 'DA722871-F421-7AD3-9C67-C1B3E85D04F2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DA722871-F421-7AD3-9C67-C1B3E85D04F2)
2025-05-31 22:30:41,779 - INFO - 开始处理日期: 2025-05-30
2025-05-31 22:30:41,779 - INFO - Request Parameters - Page 1:
2025-05-31 22:30:41,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 22:30:41,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 22:30:49,202 - INFO - Response - Page 1:
2025-05-31 22:30:49,203 - INFO - 第 1 页获取到 100 条记录
2025-05-31 22:30:49,404 - INFO - Request Parameters - Page 2:
2025-05-31 22:30:49,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 22:30:49,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 22:30:50,279 - INFO - Response - Page 2:
2025-05-31 22:30:50,279 - INFO - 第 2 页获取到 100 条记录
2025-05-31 22:30:50,480 - INFO - Request Parameters - Page 3:
2025-05-31 22:30:50,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 22:30:50,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 22:30:58,579 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A0432B80-5AEB-76B4-A7DD-718E18C197F7 Response: {'code': 'ServiceUnavailable', 'requestid': 'A0432B80-5AEB-76B4-A7DD-718E18C197F7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A0432B80-5AEB-76B4-A7DD-718E18C197F7)
2025-05-31 22:30:58,579 - INFO - 开始处理日期: 2025-05-31
2025-05-31 22:30:58,579 - INFO - Request Parameters - Page 1:
2025-05-31 22:30:58,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 22:30:58,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 22:31:00,941 - INFO - Response - Page 1:
2025-05-31 22:31:00,941 - INFO - 第 1 页获取到 1 条记录
2025-05-31 22:31:01,152 - INFO - 查询完成，共获取到 1 条记录
2025-05-31 22:31:01,152 - INFO - 获取到 1 条表单数据
2025-05-31 22:31:01,152 - INFO - 当前日期 2025-05-31 有 49 条MySQL数据需要处理
2025-05-31 22:31:01,152 - INFO - 开始批量插入 48 条新记录
2025-05-31 22:31:01,368 - INFO - 批量插入响应状态码: 200
2025-05-31 22:31:01,368 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 31 May 2025 14:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2316', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0A7D0897-B02B-7155-AB7C-06EBF1DA3713', 'x-acs-trace-id': 'cf02d9781fab3bf12b8bb727ad58c518', 'etag': '2sef/xvJHGBCaRQXyMHbmYA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 22:31:01,369 - INFO - 批量插入响应体: {'result': ['FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM41', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM51', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM61', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM71', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM81', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM91', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMA1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMB1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMC1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMD1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBME1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMF1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMG1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMH1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMI1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMJ1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMK1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBML1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMM1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMN1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMO1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMP1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMQ1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMR1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMS1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMT1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMU1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMV1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMW1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMX1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMY1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMZ1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM02', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM12', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM22', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM32', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM42', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM52', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM62', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM72', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM82', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM92', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMA2', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMB2', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMC2', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMD2', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBME2', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMF2']}
2025-05-31 22:31:01,369 - INFO - 批量插入表单数据成功，批次 1，共 48 条记录
2025-05-31 22:31:01,369 - INFO - 成功插入的数据ID: ['FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM41', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM51', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM61', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM71', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM81', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM91', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMA1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMB1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMC1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMD1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBME1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMF1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMG1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMH1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMI1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMJ1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMK1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBML1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMM1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMN1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMO1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMP1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMQ1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMR1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMS1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMT1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMU1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMV1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMW1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMX1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMY1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMZ1', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM02', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM12', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM22', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM32', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM42', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM52', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM62', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM72', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM82', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBM92', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMA2', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMB2', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMC2', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMD2', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBME2', 'FINST-PPA66671EBVVQU4AB2NQ3BLITSXP3YTRXBCBMF2']
2025-05-31 22:31:06,370 - INFO - 批量插入完成，共 48 条记录
2025-05-31 22:31:06,370 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 48 条，错误: 0 条
2025-05-31 22:31:06,370 - INFO - 数据同步完成！更新: 0 条，插入: 48 条，错误: 2 条
2025-05-31 22:32:06,384 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 22:32:06,384 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 22:32:06,384 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 22:32:06,463 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 582 条记录
2025-05-31 22:32:06,463 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 22:32:06,463 - INFO - 开始处理日期: 2025-05-30
2025-05-31 22:32:06,463 - INFO - Request Parameters - Page 1:
2025-05-31 22:32:06,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 22:32:06,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 22:32:07,383 - INFO - Response - Page 1:
2025-05-31 22:32:07,383 - INFO - 第 1 页获取到 100 条记录
2025-05-31 22:32:07,587 - INFO - Request Parameters - Page 2:
2025-05-31 22:32:07,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 22:32:07,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 22:32:08,398 - INFO - Response - Page 2:
2025-05-31 22:32:08,398 - INFO - 第 2 页获取到 100 条记录
2025-05-31 22:32:08,601 - INFO - Request Parameters - Page 3:
2025-05-31 22:32:08,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 22:32:08,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 22:32:09,392 - INFO - Response - Page 3:
2025-05-31 22:32:09,392 - INFO - 第 3 页获取到 100 条记录
2025-05-31 22:32:09,592 - INFO - Request Parameters - Page 4:
2025-05-31 22:32:09,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 22:32:09,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 22:32:10,438 - INFO - Response - Page 4:
2025-05-31 22:32:10,439 - INFO - 第 4 页获取到 100 条记录
2025-05-31 22:32:10,639 - INFO - Request Parameters - Page 5:
2025-05-31 22:32:10,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 22:32:10,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 22:32:11,414 - INFO - Response - Page 5:
2025-05-31 22:32:11,414 - INFO - 第 5 页获取到 100 条记录
2025-05-31 22:32:11,614 - INFO - Request Parameters - Page 6:
2025-05-31 22:32:11,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 22:32:11,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 22:32:12,329 - INFO - Response - Page 6:
2025-05-31 22:32:12,329 - INFO - 第 6 页获取到 33 条记录
2025-05-31 22:32:12,529 - INFO - 查询完成，共获取到 533 条记录
2025-05-31 22:32:12,529 - INFO - 获取到 533 条表单数据
2025-05-31 22:32:12,539 - INFO - 当前日期 2025-05-30 有 533 条MySQL数据需要处理
2025-05-31 22:32:12,548 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 22:32:12,548 - INFO - 开始处理日期: 2025-05-31
2025-05-31 22:32:12,548 - INFO - Request Parameters - Page 1:
2025-05-31 22:32:12,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 22:32:12,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 22:32:13,278 - INFO - Response - Page 1:
2025-05-31 22:32:13,279 - INFO - 第 1 页获取到 49 条记录
2025-05-31 22:32:13,489 - INFO - 查询完成，共获取到 49 条记录
2025-05-31 22:32:13,490 - INFO - 获取到 49 条表单数据
2025-05-31 22:32:13,491 - INFO - 当前日期 2025-05-31 有 49 条MySQL数据需要处理
2025-05-31 22:32:13,492 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 22:32:13,492 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 22:32:13,493 - INFO - 同步完成
2025-05-31 23:30:33,699 - INFO - 使用默认增量同步（当天更新数据）
2025-05-31 23:30:33,700 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 23:30:33,700 - INFO - 查询参数: ('2025-05-31',)
2025-05-31 23:30:33,781 - INFO - MySQL查询成功，增量数据（日期: 2025-05-31），共获取 312 条记录
2025-05-31 23:30:33,781 - INFO - 获取到 3 个日期需要处理: ['2025-05-29', '2025-05-30', '2025-05-31']
2025-05-31 23:30:33,784 - INFO - 开始处理日期: 2025-05-29
2025-05-31 23:30:33,786 - INFO - Request Parameters - Page 1:
2025-05-31 23:30:33,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 23:30:33,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748448000000, 1748534399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 23:30:41,883 - ERROR - 处理日期 2025-05-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1D2CED4C-FA22-73E5-A662-9D677C6DE547 Response: {'code': 'ServiceUnavailable', 'requestid': '1D2CED4C-FA22-73E5-A662-9D677C6DE547', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1D2CED4C-FA22-73E5-A662-9D677C6DE547)
2025-05-31 23:30:41,883 - INFO - 开始处理日期: 2025-05-30
2025-05-31 23:30:41,883 - INFO - Request Parameters - Page 1:
2025-05-31 23:30:41,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 23:30:41,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 23:30:50,004 - ERROR - 处理日期 2025-05-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2D1BD8A0-7620-7232-89E4-AEAFBA4EDAAE Response: {'code': 'ServiceUnavailable', 'requestid': '2D1BD8A0-7620-7232-89E4-AEAFBA4EDAAE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2D1BD8A0-7620-7232-89E4-AEAFBA4EDAAE)
2025-05-31 23:30:50,004 - INFO - 开始处理日期: 2025-05-31
2025-05-31 23:30:50,004 - INFO - Request Parameters - Page 1:
2025-05-31 23:30:50,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 23:30:50,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 23:30:50,860 - INFO - Response - Page 1:
2025-05-31 23:30:50,860 - INFO - 第 1 页获取到 49 条记录
2025-05-31 23:30:51,062 - INFO - 查询完成，共获取到 49 条记录
2025-05-31 23:30:51,062 - INFO - 获取到 49 条表单数据
2025-05-31 23:30:51,062 - INFO - 当前日期 2025-05-31 有 121 条MySQL数据需要处理
2025-05-31 23:30:51,064 - INFO - 开始批量插入 72 条新记录
2025-05-31 23:30:51,285 - INFO - 批量插入响应状态码: 200
2025-05-31 23:30:51,286 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 31 May 2025 15:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3468', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8934E62A-37B1-7540-820E-ACBA07600756', 'x-acs-trace-id': '9e6ee1200c03d158e9c0879792ed8151', 'etag': '3GTTM5tciqzAfPTPJhjNdYg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 23:30:51,286 - INFO - 批量插入响应体: {'result': ['FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM87', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM97', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMA7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMB7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMC7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMD7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBME7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMF7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMG7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMH7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMI7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMJ7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMK7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBML7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMM7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMN7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMO7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMP7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMQ7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMR7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMS7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMT7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMU7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMV7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMW7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMX7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMY7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMZ7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM08', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM18', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM28', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM38', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM48', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM58', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM68', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM78', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM88', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM98', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMA8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMB8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMC8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMD8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBME8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMF8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMG8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMH8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMI8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMJ8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMK8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBML8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMM8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMN8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMO8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMP8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMQ8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMR8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMS8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMT8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMU8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMV8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMW8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMX8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMY8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMZ8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM09', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM19', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3WRP2ECBM29', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3WRP2ECBM39', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3WRP2ECBM49', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3WRP2ECBM59', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3WRP2ECBM69', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3WRP2ECBM79']}
2025-05-31 23:30:51,286 - INFO - 批量插入表单数据成功，批次 1，共 72 条记录
2025-05-31 23:30:51,286 - INFO - 成功插入的数据ID: ['FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM87', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM97', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMA7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMB7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMC7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMD7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBME7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMF7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMG7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMH7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMI7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMJ7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMK7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBML7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMM7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMN7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMO7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMP7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMQ7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMR7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMS7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMT7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMU7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMV7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMW7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMX7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMY7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMZ7', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM08', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM18', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM28', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM38', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM48', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM58', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM68', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM78', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM88', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM98', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMA8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMB8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMC8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMD8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBME8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMF8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMG8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMH8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMI8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMJ8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMK8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBML8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMM8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMN8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMO8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMP8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMQ8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMR8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMS8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMT8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMU8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMV8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMW8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMX8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMY8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBMZ8', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM09', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3VRP2ECBM19', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3WRP2ECBM29', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3WRP2ECBM39', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3WRP2ECBM49', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3WRP2ECBM59', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3WRP2ECBM69', 'FINST-5XA66LC10DUVUZRBE11WYAA97TNV3WRP2ECBM79']
2025-05-31 23:30:56,287 - INFO - 批量插入完成，共 72 条记录
2025-05-31 23:30:56,287 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 72 条，错误: 0 条
2025-05-31 23:30:56,287 - INFO - 数据同步完成！更新: 0 条，插入: 72 条，错误: 2 条
2025-05-31 23:31:56,288 - INFO - 开始同步昨天与今天的销售数据: 2025-05-30 至 2025-05-31
2025-05-31 23:31:56,288 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-31 23:31:56,288 - INFO - 查询参数: ('2025-05-30', '2025-05-31')
2025-05-31 23:31:56,381 - INFO - MySQL查询成功，时间段: 2025-05-30 至 2025-05-31，共获取 654 条记录
2025-05-31 23:31:56,381 - INFO - 获取到 2 个日期需要处理: ['2025-05-30', '2025-05-31']
2025-05-31 23:31:56,381 - INFO - 开始处理日期: 2025-05-30
2025-05-31 23:31:56,381 - INFO - Request Parameters - Page 1:
2025-05-31 23:31:56,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 23:31:56,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 23:31:57,218 - INFO - Response - Page 1:
2025-05-31 23:31:57,218 - INFO - 第 1 页获取到 100 条记录
2025-05-31 23:31:57,418 - INFO - Request Parameters - Page 2:
2025-05-31 23:31:57,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 23:31:57,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 23:31:58,218 - INFO - Response - Page 2:
2025-05-31 23:31:58,219 - INFO - 第 2 页获取到 100 条记录
2025-05-31 23:31:58,420 - INFO - Request Parameters - Page 3:
2025-05-31 23:31:58,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 23:31:58,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 23:31:59,261 - INFO - Response - Page 3:
2025-05-31 23:31:59,261 - INFO - 第 3 页获取到 100 条记录
2025-05-31 23:31:59,462 - INFO - Request Parameters - Page 4:
2025-05-31 23:31:59,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 23:31:59,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 23:32:00,335 - INFO - Response - Page 4:
2025-05-31 23:32:00,336 - INFO - 第 4 页获取到 100 条记录
2025-05-31 23:32:00,536 - INFO - Request Parameters - Page 5:
2025-05-31 23:32:00,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 23:32:00,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 23:32:01,328 - INFO - Response - Page 5:
2025-05-31 23:32:01,329 - INFO - 第 5 页获取到 100 条记录
2025-05-31 23:32:01,529 - INFO - Request Parameters - Page 6:
2025-05-31 23:32:01,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 23:32:01,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748534400000, 1748620799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 23:32:02,167 - INFO - Response - Page 6:
2025-05-31 23:32:02,168 - INFO - 第 6 页获取到 33 条记录
2025-05-31 23:32:02,371 - INFO - 查询完成，共获取到 533 条记录
2025-05-31 23:32:02,371 - INFO - 获取到 533 条表单数据
2025-05-31 23:32:02,379 - INFO - 当前日期 2025-05-30 有 533 条MySQL数据需要处理
2025-05-31 23:32:02,387 - INFO - 日期 2025-05-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 23:32:02,387 - INFO - 开始处理日期: 2025-05-31
2025-05-31 23:32:02,387 - INFO - Request Parameters - Page 1:
2025-05-31 23:32:02,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 23:32:02,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 23:32:03,338 - INFO - Response - Page 1:
2025-05-31 23:32:03,338 - INFO - 第 1 页获取到 100 条记录
2025-05-31 23:32:03,538 - INFO - Request Parameters - Page 2:
2025-05-31 23:32:03,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 23:32:03,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 23:32:04,242 - INFO - Response - Page 2:
2025-05-31 23:32:04,242 - INFO - 第 2 页获取到 21 条记录
2025-05-31 23:32:04,450 - INFO - 查询完成，共获取到 121 条记录
2025-05-31 23:32:04,450 - INFO - 获取到 121 条表单数据
2025-05-31 23:32:04,450 - INFO - 当前日期 2025-05-31 有 121 条MySQL数据需要处理
2025-05-31 23:32:04,450 - INFO - 日期 2025-05-31 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 23:32:04,450 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 23:32:04,450 - INFO - 同步完成
