2025-05-05 00:00:04,434 - INFO - =================使用默认全量同步=============
2025-05-05 00:00:05,654 - INFO - MySQL查询成功，共获取 3236 条记录
2025-05-05 00:00:05,654 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-05 00:00:05,670 - INFO - 开始处理日期: 2025-01
2025-05-05 00:00:05,686 - INFO - Request Parameters - Page 1:
2025-05-05 00:00:05,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:05,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:06,875 - INFO - Response - Page 1:
2025-05-05 00:00:07,078 - INFO - 第 1 页获取到 100 条记录
2025-05-05 00:00:07,078 - INFO - Request Parameters - Page 2:
2025-05-05 00:00:07,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:07,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:07,547 - INFO - Response - Page 2:
2025-05-05 00:00:07,751 - INFO - 第 2 页获取到 100 条记录
2025-05-05 00:00:07,751 - INFO - Request Parameters - Page 3:
2025-05-05 00:00:07,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:07,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:08,251 - INFO - Response - Page 3:
2025-05-05 00:00:08,455 - INFO - 第 3 页获取到 100 条记录
2025-05-05 00:00:08,455 - INFO - Request Parameters - Page 4:
2025-05-05 00:00:08,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:08,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:09,002 - INFO - Response - Page 4:
2025-05-05 00:00:09,206 - INFO - 第 4 页获取到 100 条记录
2025-05-05 00:00:09,206 - INFO - Request Parameters - Page 5:
2025-05-05 00:00:09,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:09,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:09,660 - INFO - Response - Page 5:
2025-05-05 00:00:09,863 - INFO - 第 5 页获取到 100 条记录
2025-05-05 00:00:09,863 - INFO - Request Parameters - Page 6:
2025-05-05 00:00:09,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:09,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:10,426 - INFO - Response - Page 6:
2025-05-05 00:00:10,629 - INFO - 第 6 页获取到 100 条记录
2025-05-05 00:00:10,629 - INFO - Request Parameters - Page 7:
2025-05-05 00:00:10,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:10,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:11,052 - INFO - Response - Page 7:
2025-05-05 00:00:11,255 - INFO - 第 7 页获取到 82 条记录
2025-05-05 00:00:11,255 - INFO - 查询完成，共获取到 682 条记录
2025-05-05 00:00:11,255 - INFO - 获取到 682 条表单数据
2025-05-05 00:00:11,255 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-05 00:00:11,271 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 00:00:11,271 - INFO - 开始处理日期: 2025-02
2025-05-05 00:00:11,271 - INFO - Request Parameters - Page 1:
2025-05-05 00:00:11,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:11,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:11,772 - INFO - Response - Page 1:
2025-05-05 00:00:11,975 - INFO - 第 1 页获取到 100 条记录
2025-05-05 00:00:11,975 - INFO - Request Parameters - Page 2:
2025-05-05 00:00:11,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:11,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:12,413 - INFO - Response - Page 2:
2025-05-05 00:00:12,616 - INFO - 第 2 页获取到 100 条记录
2025-05-05 00:00:12,616 - INFO - Request Parameters - Page 3:
2025-05-05 00:00:12,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:12,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:13,117 - INFO - Response - Page 3:
2025-05-05 00:00:13,320 - INFO - 第 3 页获取到 100 条记录
2025-05-05 00:00:13,320 - INFO - Request Parameters - Page 4:
2025-05-05 00:00:13,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:13,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:13,774 - INFO - Response - Page 4:
2025-05-05 00:00:13,978 - INFO - 第 4 页获取到 100 条记录
2025-05-05 00:00:13,978 - INFO - Request Parameters - Page 5:
2025-05-05 00:00:13,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:13,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:14,556 - INFO - Response - Page 5:
2025-05-05 00:00:14,760 - INFO - 第 5 页获取到 100 条记录
2025-05-05 00:00:14,760 - INFO - Request Parameters - Page 6:
2025-05-05 00:00:14,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:14,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:15,276 - INFO - Response - Page 6:
2025-05-05 00:00:15,479 - INFO - 第 6 页获取到 100 条记录
2025-05-05 00:00:15,479 - INFO - Request Parameters - Page 7:
2025-05-05 00:00:15,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:15,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:15,917 - INFO - Response - Page 7:
2025-05-05 00:00:16,121 - INFO - 第 7 页获取到 70 条记录
2025-05-05 00:00:16,121 - INFO - 查询完成，共获取到 670 条记录
2025-05-05 00:00:16,121 - INFO - 获取到 670 条表单数据
2025-05-05 00:00:16,121 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-05 00:00:16,137 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 00:00:16,137 - INFO - 开始处理日期: 2025-03
2025-05-05 00:00:16,137 - INFO - Request Parameters - Page 1:
2025-05-05 00:00:16,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:16,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:16,653 - INFO - Response - Page 1:
2025-05-05 00:00:16,856 - INFO - 第 1 页获取到 100 条记录
2025-05-05 00:00:16,856 - INFO - Request Parameters - Page 2:
2025-05-05 00:00:16,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:16,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:17,388 - INFO - Response - Page 2:
2025-05-05 00:00:17,591 - INFO - 第 2 页获取到 100 条记录
2025-05-05 00:00:17,591 - INFO - Request Parameters - Page 3:
2025-05-05 00:00:17,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:17,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:18,076 - INFO - Response - Page 3:
2025-05-05 00:00:18,280 - INFO - 第 3 页获取到 100 条记录
2025-05-05 00:00:18,280 - INFO - Request Parameters - Page 4:
2025-05-05 00:00:18,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:18,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:18,734 - INFO - Response - Page 4:
2025-05-05 00:00:18,937 - INFO - 第 4 页获取到 100 条记录
2025-05-05 00:00:18,937 - INFO - Request Parameters - Page 5:
2025-05-05 00:00:18,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:18,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:19,375 - INFO - Response - Page 5:
2025-05-05 00:00:19,578 - INFO - 第 5 页获取到 100 条记录
2025-05-05 00:00:19,578 - INFO - Request Parameters - Page 6:
2025-05-05 00:00:19,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:19,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:20,032 - INFO - Response - Page 6:
2025-05-05 00:00:20,235 - INFO - 第 6 页获取到 100 条记录
2025-05-05 00:00:20,235 - INFO - Request Parameters - Page 7:
2025-05-05 00:00:20,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:20,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:20,642 - INFO - Response - Page 7:
2025-05-05 00:00:20,846 - INFO - 第 7 页获取到 61 条记录
2025-05-05 00:00:20,846 - INFO - 查询完成，共获取到 661 条记录
2025-05-05 00:00:20,846 - INFO - 获取到 661 条表单数据
2025-05-05 00:00:20,846 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-05 00:00:20,861 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 00:00:20,861 - INFO - 开始处理日期: 2025-04
2025-05-05 00:00:20,861 - INFO - Request Parameters - Page 1:
2025-05-05 00:00:20,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:20,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:21,315 - INFO - Response - Page 1:
2025-05-05 00:00:21,518 - INFO - 第 1 页获取到 100 条记录
2025-05-05 00:00:21,518 - INFO - Request Parameters - Page 2:
2025-05-05 00:00:21,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:21,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:21,988 - INFO - Response - Page 2:
2025-05-05 00:00:22,191 - INFO - 第 2 页获取到 100 条记录
2025-05-05 00:00:22,191 - INFO - Request Parameters - Page 3:
2025-05-05 00:00:22,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:22,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:22,692 - INFO - Response - Page 3:
2025-05-05 00:00:22,895 - INFO - 第 3 页获取到 100 条记录
2025-05-05 00:00:22,895 - INFO - Request Parameters - Page 4:
2025-05-05 00:00:22,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:22,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:23,568 - INFO - Response - Page 4:
2025-05-05 00:00:23,771 - INFO - 第 4 页获取到 100 条记录
2025-05-05 00:00:23,771 - INFO - Request Parameters - Page 5:
2025-05-05 00:00:23,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:23,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:24,225 - INFO - Response - Page 5:
2025-05-05 00:00:24,428 - INFO - 第 5 页获取到 100 条记录
2025-05-05 00:00:24,428 - INFO - Request Parameters - Page 6:
2025-05-05 00:00:24,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:24,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:24,960 - INFO - Response - Page 6:
2025-05-05 00:00:25,164 - INFO - 第 6 页获取到 100 条记录
2025-05-05 00:00:25,164 - INFO - Request Parameters - Page 7:
2025-05-05 00:00:25,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:25,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:25,523 - INFO - Response - Page 7:
2025-05-05 00:00:25,727 - INFO - 第 7 页获取到 27 条记录
2025-05-05 00:00:25,727 - INFO - 查询完成，共获取到 627 条记录
2025-05-05 00:00:25,727 - INFO - 获取到 627 条表单数据
2025-05-05 00:00:25,727 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-05 00:00:25,727 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M24
2025-05-05 00:00:26,149 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M24
2025-05-05 00:00:26,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6489.71, 'new_value': 6622.94}, {'field': 'offline_amount', 'old_value': 80089.07, 'new_value': 84132.35}, {'field': 'total_amount', 'old_value': 86578.78, 'new_value': 90755.29}, {'field': 'order_count', 'old_value': 3400, 'new_value': 3553}]
2025-05-05 00:00:26,149 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MD4
2025-05-05 00:00:26,587 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MD4
2025-05-05 00:00:26,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91932.29, 'new_value': 95092.68}, {'field': 'total_amount', 'old_value': 91932.29, 'new_value': 95092.68}, {'field': 'order_count', 'old_value': 4211, 'new_value': 4350}]
2025-05-05 00:00:26,587 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV4
2025-05-05 00:00:27,010 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV4
2025-05-05 00:00:27,010 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33692.0, 'new_value': 36095.5}, {'field': 'offline_amount', 'old_value': 219884.5, 'new_value': 227261.6}, {'field': 'total_amount', 'old_value': 253576.5, 'new_value': 263357.1}, {'field': 'order_count', 'old_value': 2276, 'new_value': 2359}]
2025-05-05 00:00:27,010 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M15
2025-05-05 00:00:27,401 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M15
2025-05-05 00:00:27,401 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83921.7, 'new_value': 89107.77}, {'field': 'offline_amount', 'old_value': 36532.11, 'new_value': 37155.05}, {'field': 'total_amount', 'old_value': 120453.81, 'new_value': 126262.82}, {'field': 'order_count', 'old_value': 6357, 'new_value': 6686}]
2025-05-05 00:00:27,401 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M55
2025-05-05 00:00:27,823 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M55
2025-05-05 00:00:27,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47887.0, 'new_value': 49900.0}, {'field': 'total_amount', 'old_value': 47887.0, 'new_value': 49900.0}, {'field': 'order_count', 'old_value': 2539, 'new_value': 2638}]
2025-05-05 00:00:27,823 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MBA
2025-05-05 00:00:28,214 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MBA
2025-05-05 00:00:28,214 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9094.75, 'new_value': 9355.0}, {'field': 'offline_amount', 'old_value': 90414.02, 'new_value': 94836.98}, {'field': 'total_amount', 'old_value': 99508.77, 'new_value': 104191.98}, {'field': 'order_count', 'old_value': 2095, 'new_value': 2168}]
2025-05-05 00:00:28,214 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR7
2025-05-05 00:00:28,652 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR7
2025-05-05 00:00:28,652 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35994.61, 'new_value': 36724.91}, {'field': 'offline_amount', 'old_value': 200352.52, 'new_value': 214481.12}, {'field': 'total_amount', 'old_value': 236347.13, 'new_value': 251206.03}, {'field': 'order_count', 'old_value': 4424, 'new_value': 4612}]
2025-05-05 00:00:28,652 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M96
2025-05-05 00:00:29,059 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M96
2025-05-05 00:00:29,059 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94772.0, 'new_value': 98540.9}, {'field': 'total_amount', 'old_value': 94772.0, 'new_value': 98540.9}, {'field': 'order_count', 'old_value': 4978, 'new_value': 5172}]
2025-05-05 00:00:29,059 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV6
2025-05-05 00:00:29,513 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV6
2025-05-05 00:00:29,513 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92948.94, 'new_value': 96092.36}, {'field': 'total_amount', 'old_value': 92948.94, 'new_value': 96092.36}, {'field': 'order_count', 'old_value': 3816, 'new_value': 3960}]
2025-05-05 00:00:29,513 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MS7
2025-05-05 00:00:30,014 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MS7
2025-05-05 00:00:30,014 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8172.44, 'new_value': 9720.68}, {'field': 'offline_amount', 'old_value': 61883.26, 'new_value': 63401.43}, {'field': 'total_amount', 'old_value': 70055.7, 'new_value': 73122.11}, {'field': 'order_count', 'old_value': 3921, 'new_value': 4090}]
2025-05-05 00:00:30,014 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH8
2025-05-05 00:00:30,405 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH8
2025-05-05 00:00:30,405 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34216.6, 'new_value': 35250.4}, {'field': 'offline_amount', 'old_value': 37329.8, 'new_value': 39102.57}, {'field': 'total_amount', 'old_value': 71546.4, 'new_value': 74352.97}, {'field': 'order_count', 'old_value': 5593, 'new_value': 5785}]
2025-05-05 00:00:30,405 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP8
2025-05-05 00:00:30,843 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP8
2025-05-05 00:00:30,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105000.0, 'new_value': 112000.0}, {'field': 'total_amount', 'old_value': 105000.0, 'new_value': 112000.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-05 00:00:30,843 - INFO - 日期 2025-04 处理完成 - 更新: 12 条，插入: 0 条，错误: 0 条
2025-05-05 00:00:30,843 - INFO - 开始处理日期: 2025-05
2025-05-05 00:00:30,843 - INFO - Request Parameters - Page 1:
2025-05-05 00:00:30,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:30,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:31,453 - INFO - Response - Page 1:
2025-05-05 00:00:31,656 - INFO - 第 1 页获取到 100 条记录
2025-05-05 00:00:31,656 - INFO - Request Parameters - Page 2:
2025-05-05 00:00:31,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:31,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:32,047 - INFO - Response - Page 2:
2025-05-05 00:00:32,251 - INFO - 第 2 页获取到 100 条记录
2025-05-05 00:00:32,251 - INFO - Request Parameters - Page 3:
2025-05-05 00:00:32,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:32,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:32,673 - INFO - Response - Page 3:
2025-05-05 00:00:32,877 - INFO - 第 3 页获取到 100 条记录
2025-05-05 00:00:32,877 - INFO - Request Parameters - Page 4:
2025-05-05 00:00:32,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:32,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:33,299 - INFO - Response - Page 4:
2025-05-05 00:00:33,502 - INFO - 第 4 页获取到 100 条记录
2025-05-05 00:00:33,502 - INFO - Request Parameters - Page 5:
2025-05-05 00:00:33,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:33,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:33,893 - INFO - Response - Page 5:
2025-05-05 00:00:34,097 - INFO - 第 5 页获取到 100 条记录
2025-05-05 00:00:34,097 - INFO - Request Parameters - Page 6:
2025-05-05 00:00:34,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 00:00:34,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 00:00:34,504 - INFO - Response - Page 6:
2025-05-05 00:00:34,707 - INFO - 第 6 页获取到 80 条记录
2025-05-05 00:00:34,707 - INFO - 查询完成，共获取到 580 条记录
2025-05-05 00:00:34,707 - INFO - 获取到 580 条表单数据
2025-05-05 00:00:34,707 - INFO - 当前日期 2025-05 有 596 条MySQL数据需要处理
2025-05-05 00:00:34,707 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-05 00:00:35,129 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-05 00:00:35,129 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1000000.0, 'new_value': 1600000.0}, {'field': 'total_amount', 'old_value': 1100000.0, 'new_value': 1700000.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 8}]
2025-05-05 00:00:35,129 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-05 00:00:35,708 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-05 00:00:35,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7348.5, 'new_value': 11310.22}, {'field': 'total_amount', 'old_value': 7348.5, 'new_value': 11310.22}, {'field': 'order_count', 'old_value': 21, 'new_value': 26}]
2025-05-05 00:00:35,708 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDC
2025-05-05 00:00:36,131 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDC
2025-05-05 00:00:36,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10330.0, 'new_value': 16414.0}, {'field': 'total_amount', 'old_value': 10330.0, 'new_value': 16414.0}, {'field': 'order_count', 'old_value': 203, 'new_value': 318}]
2025-05-05 00:00:36,131 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-05 00:00:36,490 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-05 00:00:36,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1620.0, 'new_value': 3260.0}, {'field': 'total_amount', 'old_value': 2840.0, 'new_value': 4480.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 49}]
2025-05-05 00:00:36,490 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-05 00:00:36,929 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-05 00:00:36,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4763.8, 'new_value': 10943.8}, {'field': 'total_amount', 'old_value': 4763.8, 'new_value': 10943.8}, {'field': 'order_count', 'old_value': 60, 'new_value': 79}]
2025-05-05 00:00:36,929 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-05 00:00:37,257 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-05 00:00:37,257 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3043.4, 'new_value': 3639.25}, {'field': 'offline_amount', 'old_value': 30316.0, 'new_value': 41256.0}, {'field': 'total_amount', 'old_value': 33359.4, 'new_value': 44895.25}, {'field': 'order_count', 'old_value': 595, 'new_value': 877}]
2025-05-05 00:00:37,257 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-05 00:00:37,789 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-05 00:00:37,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10053.0, 'new_value': 13131.0}, {'field': 'offline_amount', 'old_value': 13180.0, 'new_value': 17299.0}, {'field': 'total_amount', 'old_value': 23233.0, 'new_value': 30430.0}, {'field': 'order_count', 'old_value': 555, 'new_value': 753}]
2025-05-05 00:00:37,789 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-05 00:00:38,337 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-05 00:00:38,337 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6892.4, 'new_value': 11284.4}, {'field': 'offline_amount', 'old_value': 15508.0, 'new_value': 17905.6}, {'field': 'total_amount', 'old_value': 22400.4, 'new_value': 29190.0}, {'field': 'order_count', 'old_value': 402, 'new_value': 458}]
2025-05-05 00:00:38,337 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-05 00:00:38,759 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-05 00:00:38,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1056.0, 'new_value': 1394.0}, {'field': 'offline_amount', 'old_value': 14713.46, 'new_value': 15644.36}, {'field': 'total_amount', 'old_value': 15769.46, 'new_value': 17038.36}, {'field': 'order_count', 'old_value': 37, 'new_value': 42}]
2025-05-05 00:00:38,759 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-05 00:00:39,103 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-05 00:00:39,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 552.0, 'new_value': 12194.0}, {'field': 'total_amount', 'old_value': 552.0, 'new_value': 12194.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 9}]
2025-05-05 00:00:39,103 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-05 00:00:39,447 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-05 00:00:39,447 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11018.0, 'new_value': 13840.0}, {'field': 'total_amount', 'old_value': 11018.0, 'new_value': 13840.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 110}]
2025-05-05 00:00:39,447 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-05 00:00:39,885 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-05 00:00:39,885 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 200.0, 'new_value': 350.0}, {'field': 'offline_amount', 'old_value': 2760.0, 'new_value': 4334.0}, {'field': 'total_amount', 'old_value': 2960.0, 'new_value': 4684.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 19}]
2025-05-05 00:00:39,885 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-05 00:00:40,324 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-05 00:00:40,324 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1649.2, 'new_value': 2003.84}, {'field': 'offline_amount', 'old_value': 17808.89, 'new_value': 23688.68}, {'field': 'total_amount', 'old_value': 19458.09, 'new_value': 25692.52}, {'field': 'order_count', 'old_value': 417, 'new_value': 561}]
2025-05-05 00:00:40,324 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-05 00:00:40,824 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-05 00:00:40,824 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7020.9, 'new_value': 8780.52}, {'field': 'offline_amount', 'old_value': 102978.3, 'new_value': 129667.77}, {'field': 'total_amount', 'old_value': 109999.2, 'new_value': 138448.29}, {'field': 'order_count', 'old_value': 429, 'new_value': 567}]
2025-05-05 00:00:40,824 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-05 00:00:41,231 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-05 00:00:41,231 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2638.32, 'new_value': 3116.32}, {'field': 'offline_amount', 'old_value': 11066.0, 'new_value': 16209.0}, {'field': 'total_amount', 'old_value': 13704.32, 'new_value': 19325.32}, {'field': 'order_count', 'old_value': 15, 'new_value': 22}]
2025-05-05 00:00:41,231 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-05 00:00:41,606 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-05 00:00:41,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2761.45, 'new_value': 4157.45}, {'field': 'total_amount', 'old_value': 2761.45, 'new_value': 4157.45}, {'field': 'order_count', 'old_value': 42, 'new_value': 60}]
2025-05-05 00:00:41,606 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-05 00:00:42,044 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-05 00:00:42,044 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7577.06, 'new_value': 10025.41}, {'field': 'offline_amount', 'old_value': 18459.74, 'new_value': 25754.09}, {'field': 'total_amount', 'old_value': 26036.8, 'new_value': 35779.5}, {'field': 'order_count', 'old_value': 805, 'new_value': 1094}]
2025-05-05 00:00:42,044 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-05 00:00:42,420 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-05 00:00:42,420 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75486.0, 'new_value': 107149.0}, {'field': 'total_amount', 'old_value': 75486.0, 'new_value': 107149.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 42}]
2025-05-05 00:00:42,420 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-05 00:00:42,780 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-05 00:00:42,780 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21503.25, 'new_value': 30207.85}, {'field': 'offline_amount', 'old_value': 102421.11, 'new_value': 129733.76}, {'field': 'total_amount', 'old_value': 123924.36, 'new_value': 159941.61}, {'field': 'order_count', 'old_value': 734, 'new_value': 947}]
2025-05-05 00:00:42,780 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-05 00:00:43,218 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-05 00:00:43,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3145.0, 'new_value': 4220.0}, {'field': 'total_amount', 'old_value': 3145.0, 'new_value': 4220.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 21}]
2025-05-05 00:00:43,218 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-05 00:00:43,625 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-05 00:00:43,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25727.1, 'new_value': 36220.81}, {'field': 'total_amount', 'old_value': 27092.45, 'new_value': 37586.16}, {'field': 'order_count', 'old_value': 190, 'new_value': 258}]
2025-05-05 00:00:43,625 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-05 00:00:44,125 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-05 00:00:44,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9973.08, 'new_value': 13429.35}, {'field': 'total_amount', 'old_value': 9973.08, 'new_value': 13429.35}, {'field': 'order_count', 'old_value': 91, 'new_value': 126}]
2025-05-05 00:00:44,125 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-05 00:00:44,469 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-05 00:00:44,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8408.0, 'new_value': 12829.9}, {'field': 'total_amount', 'old_value': 8408.0, 'new_value': 12829.9}, {'field': 'order_count', 'old_value': 42, 'new_value': 61}]
2025-05-05 00:00:44,469 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-05 00:00:44,845 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-05 00:00:44,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18252.4, 'new_value': 19860.4}, {'field': 'total_amount', 'old_value': 18252.4, 'new_value': 19860.4}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-05 00:00:44,845 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-05 00:00:45,173 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-05 00:00:45,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4495.77, 'new_value': 6069.88}, {'field': 'offline_amount', 'old_value': 21237.55, 'new_value': 27088.57}, {'field': 'total_amount', 'old_value': 25733.32, 'new_value': 33158.45}, {'field': 'order_count', 'old_value': 885, 'new_value': 1149}]
2025-05-05 00:00:45,173 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-05 00:00:45,658 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-05 00:00:45,658 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37565.49, 'new_value': 47625.29}, {'field': 'total_amount', 'old_value': 37565.49, 'new_value': 47625.29}, {'field': 'order_count', 'old_value': 118, 'new_value': 189}]
2025-05-05 00:00:45,658 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-05 00:00:46,050 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-05 00:00:46,050 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4202.0, 'new_value': 7981.0}, {'field': 'total_amount', 'old_value': 4331.0, 'new_value': 8110.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-05 00:00:46,050 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-05 00:00:46,503 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-05 00:00:46,503 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 234587.52, 'new_value': 301985.52}, {'field': 'offline_amount', 'old_value': 54300.91, 'new_value': 71700.91}, {'field': 'total_amount', 'old_value': 288888.43, 'new_value': 373686.43}, {'field': 'order_count', 'old_value': 3086, 'new_value': 3854}]
2025-05-05 00:00:46,503 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-05 00:00:46,973 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-05 00:00:46,973 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12362.9, 'new_value': 16835.9}, {'field': 'offline_amount', 'old_value': 25.0, 'new_value': 27.0}, {'field': 'total_amount', 'old_value': 12387.9, 'new_value': 16862.9}, {'field': 'order_count', 'old_value': 53, 'new_value': 78}]
2025-05-05 00:00:46,973 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-05 00:00:47,364 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-05 00:00:47,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10997.0, 'new_value': 21513.0}, {'field': 'total_amount', 'old_value': 10997.0, 'new_value': 21513.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 00:00:47,364 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-05 00:00:47,817 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-05 00:00:47,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1134.0, 'new_value': 2288.9}, {'field': 'total_amount', 'old_value': 5452.0, 'new_value': 6606.9}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-05-05 00:00:47,817 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-05 00:00:48,224 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-05 00:00:48,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13506.0, 'new_value': 15873.0}, {'field': 'total_amount', 'old_value': 13506.0, 'new_value': 15873.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 30}]
2025-05-05 00:00:48,224 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-05 00:00:48,631 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-05 00:00:48,631 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180259.0, 'new_value': 214347.0}, {'field': 'total_amount', 'old_value': 180259.0, 'new_value': 214347.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 28}]
2025-05-05 00:00:48,631 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-05 00:00:49,022 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-05 00:00:49,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38213.0, 'new_value': 46804.5}, {'field': 'total_amount', 'old_value': 38213.0, 'new_value': 46804.5}, {'field': 'order_count', 'old_value': 80, 'new_value': 93}]
2025-05-05 00:00:49,022 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-05 00:00:49,444 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-05 00:00:49,444 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38452.71, 'new_value': 56066.06}, {'field': 'offline_amount', 'old_value': 24064.46, 'new_value': 28047.12}, {'field': 'total_amount', 'old_value': 62517.17, 'new_value': 84113.18}, {'field': 'order_count', 'old_value': 251, 'new_value': 333}]
2025-05-05 00:00:49,444 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-05 00:00:49,851 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-05 00:00:49,851 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67144.47, 'new_value': 88633.46}, {'field': 'total_amount', 'old_value': 67144.47, 'new_value': 88633.46}, {'field': 'order_count', 'old_value': 388, 'new_value': 519}]
2025-05-05 00:00:49,851 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-05 00:00:50,274 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-05 00:00:50,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23176.63, 'new_value': 29919.72}, {'field': 'total_amount', 'old_value': 23176.63, 'new_value': 29919.72}, {'field': 'order_count', 'old_value': 831, 'new_value': 1114}]
2025-05-05 00:00:50,274 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-05 00:00:50,696 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-05 00:00:50,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2093.95, 'new_value': 3112.56}, {'field': 'offline_amount', 'old_value': 56561.03, 'new_value': 73797.74}, {'field': 'total_amount', 'old_value': 58654.98, 'new_value': 76910.3}, {'field': 'order_count', 'old_value': 283, 'new_value': 363}]
2025-05-05 00:00:50,696 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-05 00:00:51,103 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-05 00:00:51,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4214.36, 'new_value': 5976.44}, {'field': 'offline_amount', 'old_value': 106575.23, 'new_value': 139402.27}, {'field': 'total_amount', 'old_value': 110789.59, 'new_value': 145378.71}, {'field': 'order_count', 'old_value': 678, 'new_value': 882}]
2025-05-05 00:00:51,103 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-05 00:00:51,494 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-05 00:00:51,494 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8083.52, 'new_value': 11974.52}, {'field': 'offline_amount', 'old_value': 179782.55, 'new_value': 215754.9}, {'field': 'total_amount', 'old_value': 187866.07, 'new_value': 227729.42}, {'field': 'order_count', 'old_value': 1131, 'new_value': 1441}]
2025-05-05 00:00:51,494 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-05 00:00:51,885 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-05 00:00:51,885 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1002.7, 'new_value': 2097.6}, {'field': 'offline_amount', 'old_value': 3660.7, 'new_value': 3807.7}, {'field': 'total_amount', 'old_value': 4663.4, 'new_value': 5905.3}, {'field': 'order_count', 'old_value': 21, 'new_value': 28}]
2025-05-05 00:00:51,885 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-05 00:00:52,323 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-05 00:00:52,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7972.0, 'new_value': 9924.0}, {'field': 'total_amount', 'old_value': 7972.0, 'new_value': 9924.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 19}]
2025-05-05 00:00:52,323 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-05 00:00:52,699 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-05 00:00:52,699 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13907.56, 'new_value': 21269.16}, {'field': 'total_amount', 'old_value': 13907.56, 'new_value': 21269.16}, {'field': 'order_count', 'old_value': 81, 'new_value': 112}]
2025-05-05 00:00:52,699 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKU
2025-05-05 00:00:53,090 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKU
2025-05-05 00:00:53,090 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1800.0, 'new_value': 10000.0}, {'field': 'total_amount', 'old_value': 1800.0, 'new_value': 10000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 4}]
2025-05-05 00:00:53,090 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-05 00:00:53,418 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-05 00:00:53,418 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16204.0, 'new_value': 17744.0}, {'field': 'total_amount', 'old_value': 16204.0, 'new_value': 17744.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-05 00:00:53,434 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-05 00:00:53,794 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-05 00:00:53,794 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28992.0, 'new_value': 35668.1}, {'field': 'total_amount', 'old_value': 28992.0, 'new_value': 35668.1}, {'field': 'order_count', 'old_value': 80, 'new_value': 97}]
2025-05-05 00:00:53,794 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-05 00:00:54,185 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-05 00:00:54,185 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16904.79, 'new_value': 22503.39}, {'field': 'offline_amount', 'old_value': 81834.84, 'new_value': 106613.04}, {'field': 'total_amount', 'old_value': 98739.63, 'new_value': 129116.43}, {'field': 'order_count', 'old_value': 2051, 'new_value': 2729}]
2025-05-05 00:00:54,185 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-05 00:00:54,545 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-05 00:00:54,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11466.0, 'new_value': 13779.0}, {'field': 'total_amount', 'old_value': 11466.0, 'new_value': 13779.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 34}]
2025-05-05 00:00:54,545 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-05 00:00:54,967 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-05 00:00:54,967 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 488300.6, 'new_value': 658300.6}, {'field': 'total_amount', 'old_value': 488300.6, 'new_value': 658300.6}, {'field': 'order_count', 'old_value': 193, 'new_value': 238}]
2025-05-05 00:00:54,967 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1L
2025-05-05 00:00:55,390 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1L
2025-05-05 00:00:55,390 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9813.0, 'new_value': 11613.0}, {'field': 'total_amount', 'old_value': 9813.0, 'new_value': 11613.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-05 00:00:55,390 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-05 00:00:55,796 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-05 00:00:55,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9899.0, 'new_value': 12064.0}, {'field': 'total_amount', 'old_value': 9899.0, 'new_value': 12064.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 33}]
2025-05-05 00:00:55,796 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-05 00:00:56,250 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-05 00:00:56,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200000.0, 'new_value': 250000.0}, {'field': 'total_amount', 'old_value': 200000.0, 'new_value': 250000.0}, {'field': 'order_count', 'old_value': 322, 'new_value': 323}]
2025-05-05 00:00:56,250 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-05 00:00:56,641 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-05 00:00:56,641 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4801.3, 'new_value': 6710.1}, {'field': 'offline_amount', 'old_value': 4313.9, 'new_value': 5371.8}, {'field': 'total_amount', 'old_value': 9115.2, 'new_value': 12081.9}, {'field': 'order_count', 'old_value': 45, 'new_value': 61}]
2025-05-05 00:00:56,641 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-05 00:00:57,001 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-05 00:00:57,001 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16900.0, 'new_value': 20780.0}, {'field': 'total_amount', 'old_value': 16900.0, 'new_value': 20780.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-05-05 00:00:57,001 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-05 00:00:57,408 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-05 00:00:57,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4899.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4899.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-05 00:00:57,408 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-05 00:00:57,815 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-05 00:00:57,815 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8399.0, 'new_value': 13598.0}, {'field': 'total_amount', 'old_value': 8399.0, 'new_value': 13598.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-05 00:00:57,815 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-05 00:00:58,190 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-05 00:00:58,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11495.0, 'new_value': 15070.0}, {'field': 'total_amount', 'old_value': 11495.0, 'new_value': 15070.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 57}]
2025-05-05 00:00:58,190 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-05 00:00:58,612 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-05 00:00:58,612 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11948.0, 'new_value': 20406.0}, {'field': 'total_amount', 'old_value': 11948.0, 'new_value': 20406.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-05-05 00:00:58,612 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-05 00:00:59,004 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-05 00:00:59,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72239.0, 'new_value': 91041.0}, {'field': 'total_amount', 'old_value': 72239.0, 'new_value': 91041.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 73}]
2025-05-05 00:00:59,004 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-05 00:00:59,379 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-05 00:00:59,379 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1736.78, 'new_value': 2203.78}, {'field': 'offline_amount', 'old_value': 30990.6, 'new_value': 41768.2}, {'field': 'total_amount', 'old_value': 32727.38, 'new_value': 43971.98}, {'field': 'order_count', 'old_value': 1546, 'new_value': 2106}]
2025-05-05 00:00:59,379 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-05 00:00:59,864 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-05 00:00:59,864 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2290.0, 'new_value': 2488.0}, {'field': 'total_amount', 'old_value': 2290.0, 'new_value': 2488.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-05-05 00:00:59,864 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-05 00:01:00,333 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-05 00:01:00,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89982.6, 'new_value': 111740.1}, {'field': 'total_amount', 'old_value': 89982.6, 'new_value': 111740.1}, {'field': 'order_count', 'old_value': 982, 'new_value': 1250}]
2025-05-05 00:01:00,333 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-05 00:01:00,771 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-05 00:01:00,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 382715.92, 'new_value': 489899.29}, {'field': 'total_amount', 'old_value': 382715.92, 'new_value': 489899.29}, {'field': 'order_count', 'old_value': 2357, 'new_value': 3024}]
2025-05-05 00:01:00,771 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-05 00:01:01,131 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-05 00:01:01,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7210.0, 'new_value': 11010.0}, {'field': 'total_amount', 'old_value': 7210.0, 'new_value': 11010.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-05-05 00:01:01,131 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-05 00:01:01,522 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-05 00:01:01,522 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6147.72, 'new_value': 8568.67}, {'field': 'offline_amount', 'old_value': 3316.0, 'new_value': 4853.0}, {'field': 'total_amount', 'old_value': 9463.72, 'new_value': 13421.67}, {'field': 'order_count', 'old_value': 130, 'new_value': 175}]
2025-05-05 00:01:01,522 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-05 00:01:01,867 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-05 00:01:01,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7125.0, 'new_value': 14225.0}, {'field': 'total_amount', 'old_value': 7125.0, 'new_value': 14225.0}, {'field': 'order_count', 'old_value': 565, 'new_value': 893}]
2025-05-05 00:01:01,867 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-05 00:01:02,289 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-05 00:01:02,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67006.6, 'new_value': 88878.7}, {'field': 'total_amount', 'old_value': 67006.6, 'new_value': 88878.7}, {'field': 'order_count', 'old_value': 111, 'new_value': 148}]
2025-05-05 00:01:02,289 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-05 00:01:02,664 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-05 00:01:02,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119520.09, 'new_value': 158517.76}, {'field': 'total_amount', 'old_value': 119520.09, 'new_value': 158517.76}, {'field': 'order_count', 'old_value': 403, 'new_value': 535}]
2025-05-05 00:01:02,664 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-05 00:01:03,102 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-05 00:01:03,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32274.0, 'new_value': 36818.0}, {'field': 'total_amount', 'old_value': 32274.0, 'new_value': 36818.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 26}]
2025-05-05 00:01:03,102 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-05 00:01:03,509 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-05 00:01:03,509 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25935.0, 'new_value': 33500.26}, {'field': 'total_amount', 'old_value': 25935.0, 'new_value': 33500.26}, {'field': 'order_count', 'old_value': 2398, 'new_value': 3085}]
2025-05-05 00:01:03,509 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-05 00:01:03,947 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-05 00:01:03,947 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3708.2, 'new_value': 3945.7}, {'field': 'total_amount', 'old_value': 3708.2, 'new_value': 3945.7}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 00:01:03,947 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-05 00:01:04,338 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-05 00:01:04,338 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 715.11, 'new_value': 1092.69}, {'field': 'offline_amount', 'old_value': 3200.2, 'new_value': 4179.1}, {'field': 'total_amount', 'old_value': 3915.31, 'new_value': 5271.79}, {'field': 'order_count', 'old_value': 248, 'new_value': 337}]
2025-05-05 00:01:04,338 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-05 00:01:04,745 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-05 00:01:04,745 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 895928.0, 'new_value': 1183074.0}, {'field': 'total_amount', 'old_value': 895928.0, 'new_value': 1183074.0}, {'field': 'order_count', 'old_value': 13123, 'new_value': 18226}]
2025-05-05 00:01:04,745 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-05 00:01:05,168 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-05 00:01:05,168 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22174.0, 'new_value': 27910.0}, {'field': 'total_amount', 'old_value': 22174.0, 'new_value': 27910.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 34}]
2025-05-05 00:01:05,168 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-05 00:01:05,637 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-05 00:01:05,637 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3082.0, 'new_value': 11404.0}, {'field': 'total_amount', 'old_value': 3082.0, 'new_value': 11404.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-05 00:01:05,637 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-05 00:01:06,028 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-05 00:01:06,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24373.0, 'new_value': 32285.0}, {'field': 'total_amount', 'old_value': 24373.0, 'new_value': 32285.0}, {'field': 'order_count', 'old_value': 844, 'new_value': 1222}]
2025-05-05 00:01:06,028 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-05 00:01:06,435 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-05 00:01:06,435 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15614.38, 'new_value': 19912.89}, {'field': 'offline_amount', 'old_value': 10433.76, 'new_value': 14267.98}, {'field': 'total_amount', 'old_value': 26048.14, 'new_value': 34180.87}, {'field': 'order_count', 'old_value': 1788, 'new_value': 2294}]
2025-05-05 00:01:06,435 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-05 00:01:06,857 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-05 00:01:06,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81237.13, 'new_value': 109663.02}, {'field': 'total_amount', 'old_value': 81237.13, 'new_value': 109663.02}, {'field': 'order_count', 'old_value': 357, 'new_value': 479}]
2025-05-05 00:01:06,857 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-05 00:01:07,280 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-05 00:01:07,280 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4643.0, 'new_value': 6039.0}, {'field': 'total_amount', 'old_value': 4643.0, 'new_value': 6039.0}, {'field': 'order_count', 'old_value': 439, 'new_value': 581}]
2025-05-05 00:01:07,280 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-05 00:01:07,686 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-05 00:01:07,686 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9000.0, 'new_value': 15000.0}, {'field': 'total_amount', 'old_value': 9000.0, 'new_value': 15000.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 5}]
2025-05-05 00:01:07,686 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-05 00:01:08,078 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-05 00:01:08,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22227.5, 'new_value': 28954.5}, {'field': 'total_amount', 'old_value': 22227.5, 'new_value': 28954.5}, {'field': 'order_count', 'old_value': 208, 'new_value': 273}]
2025-05-05 00:01:08,078 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-05 00:01:08,422 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-05 00:01:08,422 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10594.25, 'new_value': 14529.67}, {'field': 'offline_amount', 'old_value': 51352.79, 'new_value': 67008.11}, {'field': 'total_amount', 'old_value': 61947.04, 'new_value': 81537.78}, {'field': 'order_count', 'old_value': 1877, 'new_value': 2498}]
2025-05-05 00:01:08,422 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-05 00:01:08,797 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-05 00:01:08,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17596.0, 'new_value': 27394.0}, {'field': 'total_amount', 'old_value': 17596.0, 'new_value': 27394.0}, {'field': 'order_count', 'old_value': 17596, 'new_value': 17598}]
2025-05-05 00:01:08,797 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-05 00:01:09,220 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-05 00:01:09,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9123.3, 'new_value': 11064.0}, {'field': 'total_amount', 'old_value': 9123.3, 'new_value': 11064.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 115}]
2025-05-05 00:01:09,220 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-05 00:01:09,720 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-05 00:01:09,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2562200.0, 'new_value': 3367200.0}, {'field': 'total_amount', 'old_value': 2562200.0, 'new_value': 3367200.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 00:01:09,720 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-05 00:01:10,111 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-05 00:01:10,111 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8590.0, 'new_value': 10282.0}, {'field': 'total_amount', 'old_value': 8590.0, 'new_value': 10282.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 11}]
2025-05-05 00:01:10,111 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-05 00:01:10,471 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-05 00:01:10,471 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 133.2, 'new_value': 323.7}, {'field': 'offline_amount', 'old_value': 10884.7, 'new_value': 13149.3}, {'field': 'total_amount', 'old_value': 11017.9, 'new_value': 13473.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 41}]
2025-05-05 00:01:10,471 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-05 00:01:10,988 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-05 00:01:10,988 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56056.95, 'new_value': 73535.08}, {'field': 'offline_amount', 'old_value': 3334.5, 'new_value': 4394.7}, {'field': 'total_amount', 'old_value': 59391.45, 'new_value': 77929.78}, {'field': 'order_count', 'old_value': 2170, 'new_value': 2866}]
2025-05-05 00:01:10,988 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2V
2025-05-05 00:01:11,379 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2V
2025-05-05 00:01:11,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1599900.0, 'new_value': 1949800.0}, {'field': 'total_amount', 'old_value': 1599900.0, 'new_value': 1949800.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-05 00:01:11,379 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-05 00:01:11,785 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-05 00:01:11,785 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1727.2, 'new_value': 8805.2}, {'field': 'offline_amount', 'old_value': 18204.0, 'new_value': 29311.0}, {'field': 'total_amount', 'old_value': 19931.2, 'new_value': 38116.2}, {'field': 'order_count', 'old_value': 35, 'new_value': 52}]
2025-05-05 00:01:11,785 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-05 00:01:12,192 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-05 00:01:12,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51196.0, 'new_value': 69196.0}, {'field': 'total_amount', 'old_value': 51196.0, 'new_value': 69196.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 15}]
2025-05-05 00:01:12,192 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-05 00:01:12,662 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-05 00:01:12,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18804.19, 'new_value': 25068.67}, {'field': 'offline_amount', 'old_value': 54076.52, 'new_value': 72803.36}, {'field': 'total_amount', 'old_value': 72880.71, 'new_value': 97872.03}, {'field': 'order_count', 'old_value': 816, 'new_value': 1081}]
2025-05-05 00:01:12,662 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-05 00:01:13,021 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-05 00:01:13,021 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21262.0, 'new_value': 25593.0}, {'field': 'total_amount', 'old_value': 21262.0, 'new_value': 25593.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 19}]
2025-05-05 00:01:13,021 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-05 00:01:13,522 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-05 00:01:13,522 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18839.1, 'new_value': 24254.1}, {'field': 'total_amount', 'old_value': 18839.1, 'new_value': 24254.1}, {'field': 'order_count', 'old_value': 137, 'new_value': 184}]
2025-05-05 00:01:13,522 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-05 00:01:14,054 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-05 00:01:14,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83691.59, 'new_value': 118205.01}, {'field': 'total_amount', 'old_value': 83691.59, 'new_value': 118205.01}, {'field': 'order_count', 'old_value': 151, 'new_value': 197}]
2025-05-05 00:01:14,054 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-05 00:01:14,492 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-05 00:01:14,492 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8968.58, 'new_value': 12324.25}, {'field': 'offline_amount', 'old_value': 50805.56, 'new_value': 65310.54}, {'field': 'total_amount', 'old_value': 59774.14, 'new_value': 77634.79}, {'field': 'order_count', 'old_value': 749, 'new_value': 967}]
2025-05-05 00:01:14,492 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-05 00:01:14,899 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-05 00:01:14,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19575.3, 'new_value': 23997.7}, {'field': 'total_amount', 'old_value': 19575.3, 'new_value': 23997.7}, {'field': 'order_count', 'old_value': 49, 'new_value': 62}]
2025-05-05 00:01:14,899 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-05 00:01:15,274 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-05 00:01:15,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5601.62, 'new_value': 6788.65}, {'field': 'total_amount', 'old_value': 5601.62, 'new_value': 6788.65}, {'field': 'order_count', 'old_value': 108, 'new_value': 128}]
2025-05-05 00:01:15,274 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-05 00:01:15,634 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-05 00:01:15,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7079.94, 'new_value': 8354.44}, {'field': 'total_amount', 'old_value': 7079.94, 'new_value': 8354.44}, {'field': 'order_count', 'old_value': 29, 'new_value': 34}]
2025-05-05 00:01:15,634 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-05 00:01:16,010 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-05 00:01:16,010 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1332.39, 'new_value': 3276.47}, {'field': 'offline_amount', 'old_value': 24127.0, 'new_value': 28612.0}, {'field': 'total_amount', 'old_value': 25459.39, 'new_value': 31888.47}, {'field': 'order_count', 'old_value': 24, 'new_value': 31}]
2025-05-05 00:01:16,010 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-05 00:01:16,401 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-05 00:01:16,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20463.0, 'new_value': 24562.0}, {'field': 'total_amount', 'old_value': 20463.0, 'new_value': 24562.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-05 00:01:16,401 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-05 00:01:16,807 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-05 00:01:16,807 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8639.0, 'new_value': 10747.0}, {'field': 'total_amount', 'old_value': 8639.0, 'new_value': 10747.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 102}]
2025-05-05 00:01:16,807 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-05 00:01:17,230 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-05 00:01:17,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190401.55, 'new_value': 249707.55}, {'field': 'total_amount', 'old_value': 190401.55, 'new_value': 249707.55}, {'field': 'order_count', 'old_value': 469, 'new_value': 614}]
2025-05-05 00:01:17,230 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-05 00:01:17,621 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-05 00:01:17,621 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58587.0, 'new_value': 96191.0}, {'field': 'total_amount', 'old_value': 58587.0, 'new_value': 96191.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 15}]
2025-05-05 00:01:17,637 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-05 00:01:18,059 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-05 00:01:18,059 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12517.77, 'new_value': 15948.63}, {'field': 'offline_amount', 'old_value': 32107.6, 'new_value': 42785.0}, {'field': 'total_amount', 'old_value': 44625.37, 'new_value': 58733.63}, {'field': 'order_count', 'old_value': 398, 'new_value': 521}]
2025-05-05 00:01:18,059 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-05 00:01:18,497 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-05 00:01:18,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3244.0, 'new_value': 4881.0}, {'field': 'total_amount', 'old_value': 4620.0, 'new_value': 6257.0}, {'field': 'order_count', 'old_value': 486, 'new_value': 644}]
2025-05-05 00:01:18,497 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-05 00:01:18,904 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-05 00:01:18,904 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2282.0, 'new_value': 3013.0}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 98.0}, {'field': 'total_amount', 'old_value': 2282.0, 'new_value': 3111.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 32}]
2025-05-05 00:01:18,904 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-05 00:01:19,326 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-05 00:01:19,326 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 330.0, 'new_value': 340.0}, {'field': 'offline_amount', 'old_value': 38339.0, 'new_value': 67386.0}, {'field': 'total_amount', 'old_value': 38669.0, 'new_value': 67726.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 54}]
2025-05-05 00:01:19,326 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-05 00:01:19,702 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-05 00:01:19,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7285.0, 'new_value': 8584.0}, {'field': 'total_amount', 'old_value': 7285.0, 'new_value': 8584.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 38}]
2025-05-05 00:01:19,702 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-05 00:01:20,077 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-05 00:01:20,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92730.61, 'new_value': 123024.69}, {'field': 'total_amount', 'old_value': 92730.61, 'new_value': 123024.69}, {'field': 'order_count', 'old_value': 1145, 'new_value': 1523}]
2025-05-05 00:01:20,077 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-05 00:01:20,484 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-05 00:01:20,484 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2999.9, 'new_value': 3197.9}, {'field': 'offline_amount', 'old_value': 273.0, 'new_value': 603.0}, {'field': 'total_amount', 'old_value': 3272.9, 'new_value': 3800.9}, {'field': 'order_count', 'old_value': 16, 'new_value': 19}]
2025-05-05 00:01:20,484 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-05 00:01:20,891 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-05 00:01:20,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44596.0, 'new_value': 59299.0}, {'field': 'total_amount', 'old_value': 44596.0, 'new_value': 59299.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 88}]
2025-05-05 00:01:20,906 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-05 00:01:21,266 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-05 00:01:21,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15542.23, 'new_value': 20557.07}, {'field': 'total_amount', 'old_value': 15542.23, 'new_value': 20557.07}, {'field': 'order_count', 'old_value': 489, 'new_value': 649}]
2025-05-05 00:01:21,266 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-05 00:01:21,689 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-05 00:01:21,689 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44710.0, 'new_value': 57374.0}, {'field': 'total_amount', 'old_value': 44710.0, 'new_value': 57374.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 9}]
2025-05-05 00:01:21,689 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-05 00:01:22,111 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-05 00:01:22,111 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35861.18, 'new_value': 45787.11}, {'field': 'total_amount', 'old_value': 35861.18, 'new_value': 45787.11}, {'field': 'order_count', 'old_value': 874, 'new_value': 1127}]
2025-05-05 00:01:22,111 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-05 00:01:22,596 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-05 00:01:22,596 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7338.09, 'new_value': 8537.29}, {'field': 'offline_amount', 'old_value': 74599.77, 'new_value': 93746.03}, {'field': 'total_amount', 'old_value': 81937.86, 'new_value': 102283.32}, {'field': 'order_count', 'old_value': 2001, 'new_value': 2507}]
2025-05-05 00:01:22,596 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-05 00:01:23,034 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-05 00:01:23,034 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67713.37, 'new_value': 89836.97}, {'field': 'total_amount', 'old_value': 67713.37, 'new_value': 89836.97}, {'field': 'order_count', 'old_value': 444, 'new_value': 608}]
2025-05-05 00:01:23,034 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-05 00:01:23,425 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-05 00:01:23,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26562.0, 'new_value': 36570.0}, {'field': 'total_amount', 'old_value': 26562.0, 'new_value': 36570.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 166}]
2025-05-05 00:01:23,425 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-05 00:01:23,863 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-05 00:01:23,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96898.6, 'new_value': 133982.1}, {'field': 'total_amount', 'old_value': 96898.6, 'new_value': 133982.1}, {'field': 'order_count', 'old_value': 424, 'new_value': 565}]
2025-05-05 00:01:23,863 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-05 00:01:24,254 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-05 00:01:24,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84.0, 'new_value': 252.0}, {'field': 'offline_amount', 'old_value': 7616.0, 'new_value': 9180.0}, {'field': 'total_amount', 'old_value': 7700.0, 'new_value': 9432.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 84}]
2025-05-05 00:01:24,254 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-05 00:01:24,677 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-05 00:01:24,677 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7981.5, 'new_value': 17481.5}, {'field': 'total_amount', 'old_value': 7981.5, 'new_value': 17481.5}, {'field': 'order_count', 'old_value': 307, 'new_value': 679}]
2025-05-05 00:01:24,677 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-05 00:01:25,068 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-05 00:01:25,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46976.0, 'new_value': 60760.0}, {'field': 'total_amount', 'old_value': 46976.0, 'new_value': 60760.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 49}]
2025-05-05 00:01:25,068 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-05 00:01:25,506 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-05 00:01:25,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48560.05, 'new_value': 62335.4}, {'field': 'total_amount', 'old_value': 48560.05, 'new_value': 62335.4}, {'field': 'order_count', 'old_value': 1920, 'new_value': 2512}]
2025-05-05 00:01:25,506 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-05 00:01:25,928 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-05 00:01:25,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63836.0, 'new_value': 76115.0}, {'field': 'total_amount', 'old_value': 63836.0, 'new_value': 76115.0}, {'field': 'order_count', 'old_value': 169, 'new_value': 212}]
2025-05-05 00:01:25,928 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-05 00:01:26,273 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-05 00:01:26,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6191.85, 'new_value': 8887.31}, {'field': 'offline_amount', 'old_value': 88421.23, 'new_value': 116482.28}, {'field': 'total_amount', 'old_value': 94613.08, 'new_value': 125369.59}, {'field': 'order_count', 'old_value': 380, 'new_value': 499}]
2025-05-05 00:01:26,273 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-05 00:01:26,758 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-05 00:01:26,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111509.0, 'new_value': 137540.0}, {'field': 'total_amount', 'old_value': 111509.0, 'new_value': 137540.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 41}]
2025-05-05 00:01:26,758 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-05 00:01:27,133 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-05 00:01:27,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 315.15, 'new_value': 332.87}, {'field': 'offline_amount', 'old_value': 8288.61, 'new_value': 10688.35}, {'field': 'total_amount', 'old_value': 8603.76, 'new_value': 11021.22}, {'field': 'order_count', 'old_value': 301, 'new_value': 383}]
2025-05-05 00:01:27,133 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-05 00:01:27,524 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-05 00:01:27,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153648.71, 'new_value': 192037.18}, {'field': 'total_amount', 'old_value': 153648.71, 'new_value': 192037.18}, {'field': 'order_count', 'old_value': 953, 'new_value': 1175}]
2025-05-05 00:01:27,524 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-05 00:01:27,868 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-05 00:01:27,868 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 745.58, 'new_value': 1139.81}, {'field': 'offline_amount', 'old_value': 79066.2, 'new_value': 102500.9}, {'field': 'total_amount', 'old_value': 79811.78, 'new_value': 103640.71}, {'field': 'order_count', 'old_value': 3513, 'new_value': 4642}]
2025-05-05 00:01:27,868 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-05 00:01:28,260 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-05 00:01:28,260 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21023.0, 'new_value': 22681.0}, {'field': 'total_amount', 'old_value': 21023.0, 'new_value': 22681.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 54}]
2025-05-05 00:01:28,260 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-05 00:01:28,682 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-05 00:01:28,682 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5912.3, 'new_value': 7922.6}, {'field': 'offline_amount', 'old_value': 46916.17, 'new_value': 65711.54}, {'field': 'total_amount', 'old_value': 52828.47, 'new_value': 73634.14}, {'field': 'order_count', 'old_value': 303, 'new_value': 434}]
2025-05-05 00:01:28,682 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-05 00:01:29,104 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-05 00:01:29,104 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26581.0, 'new_value': 33651.0}, {'field': 'total_amount', 'old_value': 26581.0, 'new_value': 33651.0}, {'field': 'order_count', 'old_value': 801, 'new_value': 1005}]
2025-05-05 00:01:29,104 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-05 00:01:29,511 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-05 00:01:29,511 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10050.21, 'new_value': 13350.96}, {'field': 'offline_amount', 'old_value': 19450.99, 'new_value': 25430.93}, {'field': 'total_amount', 'old_value': 29501.2, 'new_value': 38781.89}, {'field': 'order_count', 'old_value': 1314, 'new_value': 1728}]
2025-05-05 00:01:29,511 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-05 00:01:29,933 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-05 00:01:29,933 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3931.4, 'new_value': 5682.8}, {'field': 'total_amount', 'old_value': 3931.4, 'new_value': 5682.8}, {'field': 'order_count', 'old_value': 56, 'new_value': 82}]
2025-05-05 00:01:29,933 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-05 00:01:30,356 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-05 00:01:30,356 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 898.45, 'new_value': 2523.45}, {'field': 'offline_amount', 'old_value': 47808.8, 'new_value': 62510.6}, {'field': 'total_amount', 'old_value': 48707.25, 'new_value': 65034.05}, {'field': 'order_count', 'old_value': 1415, 'new_value': 1897}]
2025-05-05 00:01:30,356 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-05 00:01:30,747 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-05 00:01:30,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52655.8, 'new_value': 56197.9}, {'field': 'total_amount', 'old_value': 52655.8, 'new_value': 56197.9}, {'field': 'order_count', 'old_value': 85, 'new_value': 94}]
2025-05-05 00:01:30,747 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-05 00:01:31,123 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-05 00:01:31,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30402.0, 'new_value': 39300.0}, {'field': 'total_amount', 'old_value': 45606.0, 'new_value': 54504.0}, {'field': 'order_count', 'old_value': 1086, 'new_value': 1087}]
2025-05-05 00:01:31,123 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM9E
2025-05-05 00:01:31,545 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM9E
2025-05-05 00:01:31,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 32486.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 32486.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 45}]
2025-05-05 00:01:31,545 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-05 00:01:31,967 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-05 00:01:31,967 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27150.0, 'new_value': 33338.0}, {'field': 'total_amount', 'old_value': 27150.0, 'new_value': 33338.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 164}]
2025-05-05 00:01:31,967 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-05 00:01:32,390 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-05 00:01:32,390 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46617.0, 'new_value': 58442.0}, {'field': 'total_amount', 'old_value': 46617.0, 'new_value': 58442.0}, {'field': 'order_count', 'old_value': 1518, 'new_value': 1892}]
2025-05-05 00:01:32,390 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-05 00:01:32,734 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-05 00:01:32,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199.0, 'new_value': 1399.0}, {'field': 'total_amount', 'old_value': 199.0, 'new_value': 1399.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-05 00:01:32,734 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-05 00:01:33,125 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-05 00:01:33,125 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 156.0, 'new_value': 8629.42}, {'field': 'offline_amount', 'old_value': 101137.0, 'new_value': 123862.73}, {'field': 'total_amount', 'old_value': 101293.0, 'new_value': 132492.15}, {'field': 'order_count', 'old_value': 434, 'new_value': 710}]
2025-05-05 00:01:33,125 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-05 00:01:33,469 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-05 00:01:33,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34800.0, 'new_value': 88800.0}, {'field': 'total_amount', 'old_value': 34800.0, 'new_value': 88800.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 10}]
2025-05-05 00:01:33,469 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-05 00:01:33,845 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-05 00:01:33,845 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72528.2, 'new_value': 96938.4}, {'field': 'offline_amount', 'old_value': 3906.6, 'new_value': 4326.6}, {'field': 'total_amount', 'old_value': 76434.8, 'new_value': 101265.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 175}]
2025-05-05 00:01:33,845 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-05 00:01:34,283 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-05 00:01:34,283 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40555.62, 'new_value': 56446.82}, {'field': 'total_amount', 'old_value': 59729.05, 'new_value': 75620.25}, {'field': 'order_count', 'old_value': 1267, 'new_value': 1617}]
2025-05-05 00:01:34,283 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-05 00:01:34,643 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-05 00:01:34,643 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1387.92, 'new_value': 1736.92}, {'field': 'total_amount', 'old_value': 1387.92, 'new_value': 1736.92}, {'field': 'order_count', 'old_value': 29, 'new_value': 47}]
2025-05-05 00:01:34,643 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-05 00:01:35,034 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-05 00:01:35,034 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21869.0, 'new_value': 29530.0}, {'field': 'offline_amount', 'old_value': 21594.62, 'new_value': 28096.62}, {'field': 'total_amount', 'old_value': 43463.62, 'new_value': 57626.62}, {'field': 'order_count', 'old_value': 281, 'new_value': 377}]
2025-05-05 00:01:35,034 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-05 00:01:35,440 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-05 00:01:35,440 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 249910.8, 'new_value': 308835.6}, {'field': 'total_amount', 'old_value': 249910.8, 'new_value': 308835.6}, {'field': 'order_count', 'old_value': 303, 'new_value': 378}]
2025-05-05 00:01:35,440 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-05 00:01:35,785 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-05 00:01:35,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167724.0, 'new_value': 256390.0}, {'field': 'total_amount', 'old_value': 167724.0, 'new_value': 256390.0}, {'field': 'order_count', 'old_value': 160, 'new_value': 239}]
2025-05-05 00:01:35,785 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-05 00:01:36,176 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-05 00:01:36,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119094.0, 'new_value': 159359.0}, {'field': 'total_amount', 'old_value': 119094.0, 'new_value': 159359.0}, {'field': 'order_count', 'old_value': 765, 'new_value': 1038}]
2025-05-05 00:01:36,176 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-05 00:01:36,551 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-05 00:01:36,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2417.0, 'new_value': 3660.0}, {'field': 'total_amount', 'old_value': 2417.0, 'new_value': 3660.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 19}]
2025-05-05 00:01:36,551 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-05 00:01:36,942 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-05 00:01:36,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143380.0, 'new_value': 179376.0}, {'field': 'total_amount', 'old_value': 143380.0, 'new_value': 179376.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 20}]
2025-05-05 00:01:36,942 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-05 00:01:37,334 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-05 00:01:37,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 550000.0, 'new_value': 670000.0}, {'field': 'total_amount', 'old_value': 550000.0, 'new_value': 670000.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 00:01:37,334 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-05 00:01:37,725 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-05 00:01:37,725 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33713.7, 'new_value': 45846.7}, {'field': 'total_amount', 'old_value': 33713.7, 'new_value': 45846.7}, {'field': 'order_count', 'old_value': 390, 'new_value': 536}]
2025-05-05 00:01:37,725 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-05 00:01:38,366 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-05 00:01:38,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7729.0, 'new_value': 8870.4}, {'field': 'total_amount', 'old_value': 7729.0, 'new_value': 8870.4}, {'field': 'order_count', 'old_value': 37, 'new_value': 47}]
2025-05-05 00:01:38,366 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-05 00:01:38,773 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-05 00:01:38,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59699.5, 'new_value': 76541.5}, {'field': 'total_amount', 'old_value': 59699.5, 'new_value': 76541.5}, {'field': 'order_count', 'old_value': 314, 'new_value': 406}]
2025-05-05 00:01:38,773 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-05 00:01:39,211 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-05 00:01:39,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8400.5, 'new_value': 11146.7}, {'field': 'total_amount', 'old_value': 8400.5, 'new_value': 11146.7}, {'field': 'order_count', 'old_value': 35, 'new_value': 43}]
2025-05-05 00:01:39,211 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-05 00:01:39,649 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-05 00:01:39,649 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4629.61, 'new_value': 5929.01}, {'field': 'offline_amount', 'old_value': 198967.12, 'new_value': 265254.19}, {'field': 'total_amount', 'old_value': 203596.73, 'new_value': 271183.2}, {'field': 'order_count', 'old_value': 759, 'new_value': 1007}]
2025-05-05 00:01:39,649 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-05 00:01:40,024 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-05 00:01:40,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18874.0, 'new_value': 35369.0}, {'field': 'total_amount', 'old_value': 32649.0, 'new_value': 49144.0}, {'field': 'order_count', 'old_value': 490, 'new_value': 779}]
2025-05-05 00:01:40,024 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-05 00:01:40,541 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-05 00:01:40,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6913.15, 'new_value': 9070.1}, {'field': 'total_amount', 'old_value': 6913.15, 'new_value': 9070.1}, {'field': 'order_count', 'old_value': 30, 'new_value': 38}]
2025-05-05 00:01:40,541 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-05 00:01:40,963 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-05 00:01:40,963 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49580.0, 'new_value': 65040.0}, {'field': 'total_amount', 'old_value': 49580.0, 'new_value': 65040.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-05-05 00:01:40,963 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-05 00:01:41,339 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-05 00:01:41,339 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61372.5, 'new_value': 100829.7}, {'field': 'offline_amount', 'old_value': 151978.4, 'new_value': 175458.4}, {'field': 'total_amount', 'old_value': 213350.9, 'new_value': 276288.1}, {'field': 'order_count', 'old_value': 1436, 'new_value': 1941}]
2025-05-05 00:01:41,339 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-05 00:01:41,714 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-05 00:01:41,714 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32445.59, 'new_value': 43391.68}, {'field': 'offline_amount', 'old_value': 152796.74, 'new_value': 198312.48}, {'field': 'total_amount', 'old_value': 185242.33, 'new_value': 241704.16}, {'field': 'order_count', 'old_value': 848, 'new_value': 1124}]
2025-05-05 00:01:41,714 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-05 00:01:42,152 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-05 00:01:42,152 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4766.91, 'new_value': 5544.27}, {'field': 'offline_amount', 'old_value': 77452.42, 'new_value': 100932.32}, {'field': 'total_amount', 'old_value': 82219.33, 'new_value': 106476.59}, {'field': 'order_count', 'old_value': 470, 'new_value': 605}]
2025-05-05 00:01:42,152 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-05 00:01:42,590 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-05 00:01:42,590 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230000.0, 'new_value': 310000.0}, {'field': 'total_amount', 'old_value': 230000.0, 'new_value': 310000.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 00:01:42,590 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-05 00:01:42,981 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-05 00:01:42,981 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 250000.0, 'new_value': 310000.0}, {'field': 'total_amount', 'old_value': 250000.0, 'new_value': 310000.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 00:01:42,981 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-05 00:01:43,404 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-05 00:01:43,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1248674.0, 'new_value': 1648674.0}, {'field': 'total_amount', 'old_value': 1248674.0, 'new_value': 1648674.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 00:01:43,404 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-05 00:01:43,826 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-05 00:01:43,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6736.0, 'new_value': 9410.0}, {'field': 'total_amount', 'old_value': 6736.0, 'new_value': 9410.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 16}]
2025-05-05 00:01:43,826 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-05 00:01:44,217 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-05 00:01:44,217 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5790.41, 'new_value': 8237.91}, {'field': 'offline_amount', 'old_value': 6899.78, 'new_value': 8523.63}, {'field': 'total_amount', 'old_value': 12690.19, 'new_value': 16761.54}, {'field': 'order_count', 'old_value': 469, 'new_value': 638}]
2025-05-05 00:01:44,217 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-05 00:01:44,561 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-05 00:01:44,561 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44499.0, 'new_value': 66637.0}, {'field': 'total_amount', 'old_value': 44499.0, 'new_value': 66637.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 112}]
2025-05-05 00:01:44,561 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-05 00:01:44,984 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-05 00:01:44,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7305.0, 'new_value': 27105.0}, {'field': 'total_amount', 'old_value': 7305.0, 'new_value': 27105.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 5}]
2025-05-05 00:01:44,984 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-05 00:01:45,391 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-05 00:01:45,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40677.6, 'new_value': 52351.4}, {'field': 'total_amount', 'old_value': 63165.0, 'new_value': 74838.8}, {'field': 'order_count', 'old_value': 328, 'new_value': 409}]
2025-05-05 00:01:45,391 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-05 00:01:45,813 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-05 00:01:45,813 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43177.14, 'new_value': 60078.33}, {'field': 'offline_amount', 'old_value': 122000.0, 'new_value': 156000.0}, {'field': 'total_amount', 'old_value': 165177.14, 'new_value': 216078.33}, {'field': 'order_count', 'old_value': 257, 'new_value': 346}]
2025-05-05 00:01:45,813 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-05 00:01:46,204 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-05 00:01:46,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5459.22, 'new_value': 7226.75}, {'field': 'offline_amount', 'old_value': 5491.66, 'new_value': 7358.5}, {'field': 'total_amount', 'old_value': 10950.88, 'new_value': 14585.25}, {'field': 'order_count', 'old_value': 428, 'new_value': 591}]
2025-05-05 00:01:46,204 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-05 00:01:46,674 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-05 00:01:46,674 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69738.96, 'new_value': 91483.01}, {'field': 'total_amount', 'old_value': 69738.96, 'new_value': 91483.01}, {'field': 'order_count', 'old_value': 352, 'new_value': 468}]
2025-05-05 00:01:46,674 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-05 00:01:47,049 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-05 00:01:47,049 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5230.27, 'new_value': 6689.61}, {'field': 'offline_amount', 'old_value': 11514.11, 'new_value': 15240.7}, {'field': 'total_amount', 'old_value': 16744.38, 'new_value': 21930.31}, {'field': 'order_count', 'old_value': 1196, 'new_value': 1570}]
2025-05-05 00:01:47,049 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-05 00:01:47,471 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-05 00:01:47,471 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106875.92, 'new_value': 119112.71}, {'field': 'total_amount', 'old_value': 106875.92, 'new_value': 119112.71}]
2025-05-05 00:01:47,471 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-05 00:01:47,847 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-05 00:01:47,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48951.0, 'new_value': 85628.0}, {'field': 'total_amount', 'old_value': 48951.0, 'new_value': 85628.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 12}]
2025-05-05 00:01:47,847 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-05 00:01:48,254 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-05 00:01:48,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102209.0, 'new_value': 127114.0}, {'field': 'total_amount', 'old_value': 102209.0, 'new_value': 127114.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 91}]
2025-05-05 00:01:48,254 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-05 00:01:48,614 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-05 00:01:48,614 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52484.0, 'new_value': 129193.0}, {'field': 'total_amount', 'old_value': 52484.0, 'new_value': 129193.0}, {'field': 'order_count', 'old_value': 651, 'new_value': 817}]
2025-05-05 00:01:48,614 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-05 00:01:49,020 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-05 00:01:49,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4048.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4048.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-05-05 00:01:49,020 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-05 00:01:49,490 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-05 00:01:49,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21101.4, 'new_value': 27312.4}, {'field': 'total_amount', 'old_value': 21121.2, 'new_value': 27332.2}, {'field': 'order_count', 'old_value': 351, 'new_value': 439}]
2025-05-05 00:01:49,490 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-05-05 00:01:49,849 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-05-05 00:01:49,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50000.0, 'new_value': 53000.0}, {'field': 'total_amount', 'old_value': 50000.0, 'new_value': 53000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-05 00:01:49,849 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-05 00:01:50,225 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-05 00:01:50,225 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15820.72, 'new_value': 20194.16}, {'field': 'total_amount', 'old_value': 15820.72, 'new_value': 20194.16}, {'field': 'order_count', 'old_value': 216, 'new_value': 272}]
2025-05-05 00:01:50,225 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-05 00:01:50,632 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-05 00:01:50,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7000.0, 'new_value': 14000.0}, {'field': 'total_amount', 'old_value': 7000.0, 'new_value': 14000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-05 00:01:50,632 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-05 00:01:51,023 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-05 00:01:51,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76101.0, 'new_value': 82059.0}, {'field': 'total_amount', 'old_value': 76101.0, 'new_value': 82059.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-05-05 00:01:51,023 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-05 00:01:51,367 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-05 00:01:51,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33713.01, 'new_value': 42395.53}, {'field': 'total_amount', 'old_value': 33713.01, 'new_value': 42395.53}, {'field': 'order_count', 'old_value': 204, 'new_value': 275}]
2025-05-05 00:01:51,367 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-05 00:01:51,742 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-05 00:01:51,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4593.0, 'new_value': 21573.0}, {'field': 'total_amount', 'old_value': 4593.0, 'new_value': 21573.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 8}]
2025-05-05 00:01:51,742 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-05 00:01:52,102 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-05 00:01:52,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7730.0, 'new_value': 10807.0}, {'field': 'total_amount', 'old_value': 9741.0, 'new_value': 12818.0}, {'field': 'order_count', 'old_value': 695, 'new_value': 892}]
2025-05-05 00:01:52,118 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-05 00:01:52,462 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-05 00:01:52,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1357.0, 'new_value': 2573.0}, {'field': 'total_amount', 'old_value': 1357.0, 'new_value': 2573.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 17}]
2025-05-05 00:01:52,462 - INFO - 开始批量插入 16 条新记录
2025-05-05 00:01:52,619 - INFO - 批量插入响应状态码: 200
2025-05-05 00:01:52,619 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 04 May 2025 16:01:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '780', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '07C68B7D-CA76-7679-AE93-BAF4382F74BC', 'x-acs-trace-id': 'f244fd514eea9d52e373b60d883961a0', 'etag': '7X8UhJX8Jaz37rua+4QNjSQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-05 00:01:52,619 - INFO - 批量插入响应体: {'result': ['FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMJA']}
2025-05-05 00:01:52,619 - INFO - 批量插入表单数据成功，批次 1，共 16 条记录
2025-05-05 00:01:52,619 - INFO - 成功插入的数据ID: ['FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA', 'FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMJA']
2025-05-05 00:01:55,638 - INFO - 批量插入完成，共 16 条记录
2025-05-05 00:01:55,638 - INFO - 日期 2025-05 处理完成 - 更新: 190 条，插入: 16 条，错误: 0 条
2025-05-05 00:01:55,638 - INFO - 数据同步完成！更新: 202 条，插入: 16 条，错误: 0 条
2025-05-05 00:01:55,638 - INFO - =================同步完成====================
2025-05-05 03:00:04,423 - INFO - =================使用默认全量同步=============
2025-05-05 03:00:05,612 - INFO - MySQL查询成功，共获取 3236 条记录
2025-05-05 03:00:05,612 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-05 03:00:05,628 - INFO - 开始处理日期: 2025-01
2025-05-05 03:00:05,628 - INFO - Request Parameters - Page 1:
2025-05-05 03:00:05,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:05,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:06,660 - INFO - Response - Page 1:
2025-05-05 03:00:06,864 - INFO - 第 1 页获取到 100 条记录
2025-05-05 03:00:06,864 - INFO - Request Parameters - Page 2:
2025-05-05 03:00:06,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:06,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:07,615 - INFO - Response - Page 2:
2025-05-05 03:00:07,818 - INFO - 第 2 页获取到 100 条记录
2025-05-05 03:00:07,818 - INFO - Request Parameters - Page 3:
2025-05-05 03:00:07,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:07,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:08,319 - INFO - Response - Page 3:
2025-05-05 03:00:08,522 - INFO - 第 3 页获取到 100 条记录
2025-05-05 03:00:08,522 - INFO - Request Parameters - Page 4:
2025-05-05 03:00:08,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:08,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:08,992 - INFO - Response - Page 4:
2025-05-05 03:00:09,195 - INFO - 第 4 页获取到 100 条记录
2025-05-05 03:00:09,195 - INFO - Request Parameters - Page 5:
2025-05-05 03:00:09,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:09,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:09,727 - INFO - Response - Page 5:
2025-05-05 03:00:09,930 - INFO - 第 5 页获取到 100 条记录
2025-05-05 03:00:09,930 - INFO - Request Parameters - Page 6:
2025-05-05 03:00:09,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:09,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:10,447 - INFO - Response - Page 6:
2025-05-05 03:00:10,650 - INFO - 第 6 页获取到 100 条记录
2025-05-05 03:00:10,650 - INFO - Request Parameters - Page 7:
2025-05-05 03:00:10,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:10,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:11,151 - INFO - Response - Page 7:
2025-05-05 03:00:11,354 - INFO - 第 7 页获取到 82 条记录
2025-05-05 03:00:11,354 - INFO - 查询完成，共获取到 682 条记录
2025-05-05 03:00:11,354 - INFO - 获取到 682 条表单数据
2025-05-05 03:00:11,354 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-05 03:00:11,370 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 03:00:11,370 - INFO - 开始处理日期: 2025-02
2025-05-05 03:00:11,370 - INFO - Request Parameters - Page 1:
2025-05-05 03:00:11,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:11,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:11,839 - INFO - Response - Page 1:
2025-05-05 03:00:12,042 - INFO - 第 1 页获取到 100 条记录
2025-05-05 03:00:12,042 - INFO - Request Parameters - Page 2:
2025-05-05 03:00:12,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:12,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:12,621 - INFO - Response - Page 2:
2025-05-05 03:00:12,825 - INFO - 第 2 页获取到 100 条记录
2025-05-05 03:00:12,825 - INFO - Request Parameters - Page 3:
2025-05-05 03:00:12,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:12,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:13,278 - INFO - Response - Page 3:
2025-05-05 03:00:13,482 - INFO - 第 3 页获取到 100 条记录
2025-05-05 03:00:13,482 - INFO - Request Parameters - Page 4:
2025-05-05 03:00:13,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:13,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:13,967 - INFO - Response - Page 4:
2025-05-05 03:00:14,170 - INFO - 第 4 页获取到 100 条记录
2025-05-05 03:00:14,170 - INFO - Request Parameters - Page 5:
2025-05-05 03:00:14,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:14,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:14,624 - INFO - Response - Page 5:
2025-05-05 03:00:14,827 - INFO - 第 5 页获取到 100 条记录
2025-05-05 03:00:14,827 - INFO - Request Parameters - Page 6:
2025-05-05 03:00:14,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:14,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:15,359 - INFO - Response - Page 6:
2025-05-05 03:00:15,562 - INFO - 第 6 页获取到 100 条记录
2025-05-05 03:00:15,562 - INFO - Request Parameters - Page 7:
2025-05-05 03:00:15,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:15,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:16,016 - INFO - Response - Page 7:
2025-05-05 03:00:16,219 - INFO - 第 7 页获取到 70 条记录
2025-05-05 03:00:16,219 - INFO - 查询完成，共获取到 670 条记录
2025-05-05 03:00:16,219 - INFO - 获取到 670 条表单数据
2025-05-05 03:00:16,219 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-05 03:00:16,235 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 03:00:16,235 - INFO - 开始处理日期: 2025-03
2025-05-05 03:00:16,235 - INFO - Request Parameters - Page 1:
2025-05-05 03:00:16,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:16,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:16,673 - INFO - Response - Page 1:
2025-05-05 03:00:16,877 - INFO - 第 1 页获取到 100 条记录
2025-05-05 03:00:16,877 - INFO - Request Parameters - Page 2:
2025-05-05 03:00:16,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:16,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:17,409 - INFO - Response - Page 2:
2025-05-05 03:00:17,612 - INFO - 第 2 页获取到 100 条记录
2025-05-05 03:00:17,612 - INFO - Request Parameters - Page 3:
2025-05-05 03:00:17,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:17,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:18,159 - INFO - Response - Page 3:
2025-05-05 03:00:18,363 - INFO - 第 3 页获取到 100 条记录
2025-05-05 03:00:18,363 - INFO - Request Parameters - Page 4:
2025-05-05 03:00:18,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:18,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:18,879 - INFO - Response - Page 4:
2025-05-05 03:00:19,083 - INFO - 第 4 页获取到 100 条记录
2025-05-05 03:00:19,083 - INFO - Request Parameters - Page 5:
2025-05-05 03:00:19,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:19,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:19,536 - INFO - Response - Page 5:
2025-05-05 03:00:19,740 - INFO - 第 5 页获取到 100 条记录
2025-05-05 03:00:19,740 - INFO - Request Parameters - Page 6:
2025-05-05 03:00:19,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:19,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:20,334 - INFO - Response - Page 6:
2025-05-05 03:00:20,538 - INFO - 第 6 页获取到 100 条记录
2025-05-05 03:00:20,538 - INFO - Request Parameters - Page 7:
2025-05-05 03:00:20,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:20,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:20,976 - INFO - Response - Page 7:
2025-05-05 03:00:21,179 - INFO - 第 7 页获取到 61 条记录
2025-05-05 03:00:21,179 - INFO - 查询完成，共获取到 661 条记录
2025-05-05 03:00:21,179 - INFO - 获取到 661 条表单数据
2025-05-05 03:00:21,179 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-05 03:00:21,195 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 03:00:21,195 - INFO - 开始处理日期: 2025-04
2025-05-05 03:00:21,195 - INFO - Request Parameters - Page 1:
2025-05-05 03:00:21,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:21,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:21,680 - INFO - Response - Page 1:
2025-05-05 03:00:21,883 - INFO - 第 1 页获取到 100 条记录
2025-05-05 03:00:21,883 - INFO - Request Parameters - Page 2:
2025-05-05 03:00:21,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:21,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:22,431 - INFO - Response - Page 2:
2025-05-05 03:00:22,634 - INFO - 第 2 页获取到 100 条记录
2025-05-05 03:00:22,634 - INFO - Request Parameters - Page 3:
2025-05-05 03:00:22,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:22,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:23,072 - INFO - Response - Page 3:
2025-05-05 03:00:23,275 - INFO - 第 3 页获取到 100 条记录
2025-05-05 03:00:23,275 - INFO - Request Parameters - Page 4:
2025-05-05 03:00:23,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:23,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:23,713 - INFO - Response - Page 4:
2025-05-05 03:00:23,917 - INFO - 第 4 页获取到 100 条记录
2025-05-05 03:00:23,917 - INFO - Request Parameters - Page 5:
2025-05-05 03:00:23,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:23,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:24,402 - INFO - Response - Page 5:
2025-05-05 03:00:24,605 - INFO - 第 5 页获取到 100 条记录
2025-05-05 03:00:24,605 - INFO - Request Parameters - Page 6:
2025-05-05 03:00:24,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:24,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:25,090 - INFO - Response - Page 6:
2025-05-05 03:00:25,294 - INFO - 第 6 页获取到 100 条记录
2025-05-05 03:00:25,294 - INFO - Request Parameters - Page 7:
2025-05-05 03:00:25,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:25,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:25,622 - INFO - Response - Page 7:
2025-05-05 03:00:25,825 - INFO - 第 7 页获取到 27 条记录
2025-05-05 03:00:25,825 - INFO - 查询完成，共获取到 627 条记录
2025-05-05 03:00:25,825 - INFO - 获取到 627 条表单数据
2025-05-05 03:00:25,825 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-05 03:00:25,841 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 03:00:25,841 - INFO - 开始处理日期: 2025-05
2025-05-05 03:00:25,841 - INFO - Request Parameters - Page 1:
2025-05-05 03:00:25,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:25,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:26,389 - INFO - Response - Page 1:
2025-05-05 03:00:26,592 - INFO - 第 1 页获取到 100 条记录
2025-05-05 03:00:26,592 - INFO - Request Parameters - Page 2:
2025-05-05 03:00:26,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:26,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:27,030 - INFO - Response - Page 2:
2025-05-05 03:00:27,234 - INFO - 第 2 页获取到 100 条记录
2025-05-05 03:00:27,234 - INFO - Request Parameters - Page 3:
2025-05-05 03:00:27,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:27,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:27,765 - INFO - Response - Page 3:
2025-05-05 03:00:27,969 - INFO - 第 3 页获取到 100 条记录
2025-05-05 03:00:27,969 - INFO - Request Parameters - Page 4:
2025-05-05 03:00:27,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:27,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:28,454 - INFO - Response - Page 4:
2025-05-05 03:00:28,657 - INFO - 第 4 页获取到 100 条记录
2025-05-05 03:00:28,657 - INFO - Request Parameters - Page 5:
2025-05-05 03:00:28,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:28,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:29,330 - INFO - Response - Page 5:
2025-05-05 03:00:29,533 - INFO - 第 5 页获取到 100 条记录
2025-05-05 03:00:29,533 - INFO - Request Parameters - Page 6:
2025-05-05 03:00:29,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 03:00:29,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 03:00:29,987 - INFO - Response - Page 6:
2025-05-05 03:00:30,190 - INFO - 第 6 页获取到 96 条记录
2025-05-05 03:00:30,190 - INFO - 查询完成，共获取到 596 条记录
2025-05-05 03:00:30,190 - INFO - 获取到 596 条表单数据
2025-05-05 03:00:30,190 - INFO - 当前日期 2025-05 有 596 条MySQL数据需要处理
2025-05-05 03:00:30,206 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-05 03:00:30,628 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-05 03:00:30,628 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9171.36, 'new_value': 11271.57}, {'field': 'offline_amount', 'old_value': 90510.98, 'new_value': 122534.8}, {'field': 'total_amount', 'old_value': 99682.34, 'new_value': 133806.37}, {'field': 'order_count', 'old_value': 755, 'new_value': 1012}]
2025-05-05 03:00:30,628 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5M
2025-05-05 03:00:31,082 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5M
2025-05-05 03:00:31,082 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1178.65, 'new_value': 1572.17}, {'field': 'offline_amount', 'old_value': 8990.6, 'new_value': 12463.94}, {'field': 'total_amount', 'old_value': 10169.25, 'new_value': 14036.11}, {'field': 'order_count', 'old_value': 430, 'new_value': 577}]
2025-05-05 03:00:31,082 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-05 03:00:31,505 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-05 03:00:31,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2610.0, 'new_value': 5229.0}, {'field': 'total_amount', 'old_value': 2610.0, 'new_value': 5229.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 8}]
2025-05-05 03:00:31,505 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-05 03:00:31,974 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-05 03:00:31,974 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23576.8, 'new_value': 29816.2}, {'field': 'offline_amount', 'old_value': 36886.0, 'new_value': 45735.7}, {'field': 'total_amount', 'old_value': 60462.8, 'new_value': 75551.9}, {'field': 'order_count', 'old_value': 1209, 'new_value': 1511}]
2025-05-05 03:00:31,974 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-05 03:00:32,365 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-05 03:00:32,365 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1140.16, 'new_value': 1598.51}, {'field': 'offline_amount', 'old_value': 6362.72, 'new_value': 8403.5}, {'field': 'total_amount', 'old_value': 7502.88, 'new_value': 10002.01}, {'field': 'order_count', 'old_value': 270, 'new_value': 366}]
2025-05-05 03:00:32,365 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-05 03:00:32,772 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-05 03:00:32,772 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23187.26, 'new_value': 34644.09}, {'field': 'offline_amount', 'old_value': 12143.89, 'new_value': 15857.32}, {'field': 'total_amount', 'old_value': 35331.15, 'new_value': 50501.41}, {'field': 'order_count', 'old_value': 2052, 'new_value': 2993}]
2025-05-05 03:00:32,772 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-05 03:00:33,257 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-05 03:00:33,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173259.7, 'new_value': 225083.79}, {'field': 'total_amount', 'old_value': 173259.7, 'new_value': 225083.79}, {'field': 'order_count', 'old_value': 3724, 'new_value': 4695}]
2025-05-05 03:00:33,257 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-05 03:00:33,742 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-05 03:00:33,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73641.37, 'new_value': 96134.6}, {'field': 'total_amount', 'old_value': 73641.37, 'new_value': 96134.6}, {'field': 'order_count', 'old_value': 2868, 'new_value': 3733}]
2025-05-05 03:00:33,742 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-05 03:00:34,180 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-05 03:00:34,180 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42535.44, 'new_value': 56399.44}, {'field': 'offline_amount', 'old_value': 247108.54, 'new_value': 331154.13}, {'field': 'total_amount', 'old_value': 289643.98, 'new_value': 387553.57}, {'field': 'order_count', 'old_value': 1359, 'new_value': 1812}]
2025-05-05 03:00:34,180 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-05 03:00:34,712 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-05 03:00:34,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16608.7, 'new_value': 21699.8}, {'field': 'total_amount', 'old_value': 16608.7, 'new_value': 21699.8}, {'field': 'order_count', 'old_value': 797, 'new_value': 1073}]
2025-05-05 03:00:34,712 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-05 03:00:35,103 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-05 03:00:35,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24124.74, 'new_value': 25853.93}, {'field': 'offline_amount', 'old_value': 27971.9, 'new_value': 45489.6}, {'field': 'total_amount', 'old_value': 52096.64, 'new_value': 71343.53}, {'field': 'order_count', 'old_value': 718, 'new_value': 997}]
2025-05-05 03:00:35,103 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-05 03:00:35,525 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-05 03:00:35,525 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50602.0, 'new_value': 63461.0}, {'field': 'total_amount', 'old_value': 50602.0, 'new_value': 63461.0}, {'field': 'order_count', 'old_value': 810, 'new_value': 1038}]
2025-05-05 03:00:35,525 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-05 03:00:35,932 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-05 03:00:35,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39419.51, 'new_value': 52000.2}, {'field': 'total_amount', 'old_value': 39419.51, 'new_value': 52000.2}, {'field': 'order_count', 'old_value': 1552, 'new_value': 2054}]
2025-05-05 03:00:35,932 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-05 03:00:36,401 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-05 03:00:36,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41928.33, 'new_value': 55084.49}, {'field': 'total_amount', 'old_value': 41928.33, 'new_value': 55084.49}, {'field': 'order_count', 'old_value': 305, 'new_value': 387}]
2025-05-05 03:00:36,401 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-05 03:00:36,761 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-05 03:00:36,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10654.13, 'new_value': 21694.97}, {'field': 'total_amount', 'old_value': 29693.73, 'new_value': 40734.57}, {'field': 'order_count', 'old_value': 944, 'new_value': 1265}]
2025-05-05 03:00:36,761 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-05 03:00:37,231 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-05 03:00:37,231 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8947.27, 'new_value': 11833.22}, {'field': 'offline_amount', 'old_value': 36126.95, 'new_value': 48242.88}, {'field': 'total_amount', 'old_value': 45074.22, 'new_value': 60076.1}, {'field': 'order_count', 'old_value': 1429, 'new_value': 1777}]
2025-05-05 03:00:37,231 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-05 03:00:37,716 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-05 03:00:37,716 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19915.44, 'new_value': 26272.03}, {'field': 'offline_amount', 'old_value': 12263.51, 'new_value': 16175.76}, {'field': 'total_amount', 'old_value': 32178.95, 'new_value': 42447.79}, {'field': 'order_count', 'old_value': 1631, 'new_value': 2226}]
2025-05-05 03:00:37,716 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-05 03:00:38,122 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-05 03:00:38,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 487348.0, 'new_value': 608188.0}, {'field': 'total_amount', 'old_value': 487348.0, 'new_value': 608188.0}, {'field': 'order_count', 'old_value': 1779, 'new_value': 2270}]
2025-05-05 03:00:38,122 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-05 03:00:38,529 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-05 03:00:38,529 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2492.14, 'new_value': 3531.37}, {'field': 'offline_amount', 'old_value': 8846.1, 'new_value': 11084.1}, {'field': 'total_amount', 'old_value': 11338.24, 'new_value': 14615.47}, {'field': 'order_count', 'old_value': 445, 'new_value': 566}]
2025-05-05 03:00:38,529 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-05 03:00:38,952 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-05 03:00:38,952 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 653.0, 'new_value': 885.0}, {'field': 'offline_amount', 'old_value': 7729.8, 'new_value': 10341.4}, {'field': 'total_amount', 'old_value': 8382.8, 'new_value': 11226.4}, {'field': 'order_count', 'old_value': 309, 'new_value': 406}]
2025-05-05 03:00:38,952 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMHV
2025-05-05 03:00:39,358 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMHV
2025-05-05 03:00:39,358 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 680.0, 'new_value': 2876.0}, {'field': 'total_amount', 'old_value': 680.0, 'new_value': 2876.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-05 03:00:39,358 - INFO - 日期 2025-05 处理完成 - 更新: 21 条，插入: 0 条，错误: 0 条
2025-05-05 03:00:39,358 - INFO - 数据同步完成！更新: 21 条，插入: 0 条，错误: 0 条
2025-05-05 03:00:39,358 - INFO - =================同步完成====================
2025-05-05 06:00:04,438 - INFO - =================使用默认全量同步=============
2025-05-05 06:00:05,643 - INFO - MySQL查询成功，共获取 3236 条记录
2025-05-05 06:00:05,643 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-05 06:00:05,674 - INFO - 开始处理日期: 2025-01
2025-05-05 06:00:05,674 - INFO - Request Parameters - Page 1:
2025-05-05 06:00:05,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:05,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:06,941 - INFO - Response - Page 1:
2025-05-05 06:00:07,145 - INFO - 第 1 页获取到 100 条记录
2025-05-05 06:00:07,145 - INFO - Request Parameters - Page 2:
2025-05-05 06:00:07,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:07,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:07,614 - INFO - Response - Page 2:
2025-05-05 06:00:07,817 - INFO - 第 2 页获取到 100 条记录
2025-05-05 06:00:07,817 - INFO - Request Parameters - Page 3:
2025-05-05 06:00:07,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:07,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:08,334 - INFO - Response - Page 3:
2025-05-05 06:00:08,537 - INFO - 第 3 页获取到 100 条记录
2025-05-05 06:00:08,537 - INFO - Request Parameters - Page 4:
2025-05-05 06:00:08,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:08,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:09,116 - INFO - Response - Page 4:
2025-05-05 06:00:09,319 - INFO - 第 4 页获取到 100 条记录
2025-05-05 06:00:09,319 - INFO - Request Parameters - Page 5:
2025-05-05 06:00:09,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:09,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:09,820 - INFO - Response - Page 5:
2025-05-05 06:00:10,023 - INFO - 第 5 页获取到 100 条记录
2025-05-05 06:00:10,023 - INFO - Request Parameters - Page 6:
2025-05-05 06:00:10,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:10,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:10,540 - INFO - Response - Page 6:
2025-05-05 06:00:10,743 - INFO - 第 6 页获取到 100 条记录
2025-05-05 06:00:10,743 - INFO - Request Parameters - Page 7:
2025-05-05 06:00:10,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:10,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:11,244 - INFO - Response - Page 7:
2025-05-05 06:00:11,447 - INFO - 第 7 页获取到 82 条记录
2025-05-05 06:00:11,447 - INFO - 查询完成，共获取到 682 条记录
2025-05-05 06:00:11,447 - INFO - 获取到 682 条表单数据
2025-05-05 06:00:11,447 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-05 06:00:11,463 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 06:00:11,463 - INFO - 开始处理日期: 2025-02
2025-05-05 06:00:11,463 - INFO - Request Parameters - Page 1:
2025-05-05 06:00:11,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:11,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:11,994 - INFO - Response - Page 1:
2025-05-05 06:00:12,198 - INFO - 第 1 页获取到 100 条记录
2025-05-05 06:00:12,198 - INFO - Request Parameters - Page 2:
2025-05-05 06:00:12,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:12,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:12,636 - INFO - Response - Page 2:
2025-05-05 06:00:12,839 - INFO - 第 2 页获取到 100 条记录
2025-05-05 06:00:12,839 - INFO - Request Parameters - Page 3:
2025-05-05 06:00:12,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:12,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:13,309 - INFO - Response - Page 3:
2025-05-05 06:00:13,512 - INFO - 第 3 页获取到 100 条记录
2025-05-05 06:00:13,512 - INFO - Request Parameters - Page 4:
2025-05-05 06:00:13,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:13,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:14,044 - INFO - Response - Page 4:
2025-05-05 06:00:14,247 - INFO - 第 4 页获取到 100 条记录
2025-05-05 06:00:14,247 - INFO - Request Parameters - Page 5:
2025-05-05 06:00:14,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:14,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:14,764 - INFO - Response - Page 5:
2025-05-05 06:00:14,967 - INFO - 第 5 页获取到 100 条记录
2025-05-05 06:00:14,967 - INFO - Request Parameters - Page 6:
2025-05-05 06:00:14,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:14,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:15,499 - INFO - Response - Page 6:
2025-05-05 06:00:15,702 - INFO - 第 6 页获取到 100 条记录
2025-05-05 06:00:15,702 - INFO - Request Parameters - Page 7:
2025-05-05 06:00:15,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:15,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:16,156 - INFO - Response - Page 7:
2025-05-05 06:00:16,359 - INFO - 第 7 页获取到 70 条记录
2025-05-05 06:00:16,359 - INFO - 查询完成，共获取到 670 条记录
2025-05-05 06:00:16,359 - INFO - 获取到 670 条表单数据
2025-05-05 06:00:16,359 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-05 06:00:16,375 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 06:00:16,375 - INFO - 开始处理日期: 2025-03
2025-05-05 06:00:16,375 - INFO - Request Parameters - Page 1:
2025-05-05 06:00:16,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:16,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:16,829 - INFO - Response - Page 1:
2025-05-05 06:00:17,032 - INFO - 第 1 页获取到 100 条记录
2025-05-05 06:00:17,032 - INFO - Request Parameters - Page 2:
2025-05-05 06:00:17,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:17,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:17,455 - INFO - Response - Page 2:
2025-05-05 06:00:17,658 - INFO - 第 2 页获取到 100 条记录
2025-05-05 06:00:17,658 - INFO - Request Parameters - Page 3:
2025-05-05 06:00:17,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:17,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:18,112 - INFO - Response - Page 3:
2025-05-05 06:00:18,315 - INFO - 第 3 页获取到 100 条记录
2025-05-05 06:00:18,315 - INFO - Request Parameters - Page 4:
2025-05-05 06:00:18,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:18,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:18,784 - INFO - Response - Page 4:
2025-05-05 06:00:18,988 - INFO - 第 4 页获取到 100 条记录
2025-05-05 06:00:18,988 - INFO - Request Parameters - Page 5:
2025-05-05 06:00:18,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:18,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:19,457 - INFO - Response - Page 5:
2025-05-05 06:00:19,660 - INFO - 第 5 页获取到 100 条记录
2025-05-05 06:00:19,660 - INFO - Request Parameters - Page 6:
2025-05-05 06:00:19,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:19,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:20,239 - INFO - Response - Page 6:
2025-05-05 06:00:20,443 - INFO - 第 6 页获取到 100 条记录
2025-05-05 06:00:20,443 - INFO - Request Parameters - Page 7:
2025-05-05 06:00:20,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:20,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:20,912 - INFO - Response - Page 7:
2025-05-05 06:00:21,115 - INFO - 第 7 页获取到 61 条记录
2025-05-05 06:00:21,115 - INFO - 查询完成，共获取到 661 条记录
2025-05-05 06:00:21,115 - INFO - 获取到 661 条表单数据
2025-05-05 06:00:21,115 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-05 06:00:21,131 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 06:00:21,131 - INFO - 开始处理日期: 2025-04
2025-05-05 06:00:21,131 - INFO - Request Parameters - Page 1:
2025-05-05 06:00:21,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:21,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:21,616 - INFO - Response - Page 1:
2025-05-05 06:00:21,819 - INFO - 第 1 页获取到 100 条记录
2025-05-05 06:00:21,819 - INFO - Request Parameters - Page 2:
2025-05-05 06:00:21,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:21,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:22,320 - INFO - Response - Page 2:
2025-05-05 06:00:22,524 - INFO - 第 2 页获取到 100 条记录
2025-05-05 06:00:22,524 - INFO - Request Parameters - Page 3:
2025-05-05 06:00:22,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:22,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:23,040 - INFO - Response - Page 3:
2025-05-05 06:00:23,243 - INFO - 第 3 页获取到 100 条记录
2025-05-05 06:00:23,243 - INFO - Request Parameters - Page 4:
2025-05-05 06:00:23,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:23,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:23,681 - INFO - Response - Page 4:
2025-05-05 06:00:23,885 - INFO - 第 4 页获取到 100 条记录
2025-05-05 06:00:23,885 - INFO - Request Parameters - Page 5:
2025-05-05 06:00:23,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:23,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:24,370 - INFO - Response - Page 5:
2025-05-05 06:00:24,573 - INFO - 第 5 页获取到 100 条记录
2025-05-05 06:00:24,573 - INFO - Request Parameters - Page 6:
2025-05-05 06:00:24,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:24,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:25,105 - INFO - Response - Page 6:
2025-05-05 06:00:25,308 - INFO - 第 6 页获取到 100 条记录
2025-05-05 06:00:25,308 - INFO - Request Parameters - Page 7:
2025-05-05 06:00:25,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:25,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:25,699 - INFO - Response - Page 7:
2025-05-05 06:00:25,903 - INFO - 第 7 页获取到 27 条记录
2025-05-05 06:00:25,903 - INFO - 查询完成，共获取到 627 条记录
2025-05-05 06:00:25,903 - INFO - 获取到 627 条表单数据
2025-05-05 06:00:25,903 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-05 06:00:25,918 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 06:00:25,918 - INFO - 开始处理日期: 2025-05
2025-05-05 06:00:25,918 - INFO - Request Parameters - Page 1:
2025-05-05 06:00:25,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:25,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:26,372 - INFO - Response - Page 1:
2025-05-05 06:00:26,576 - INFO - 第 1 页获取到 100 条记录
2025-05-05 06:00:26,576 - INFO - Request Parameters - Page 2:
2025-05-05 06:00:26,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:26,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:27,107 - INFO - Response - Page 2:
2025-05-05 06:00:27,311 - INFO - 第 2 页获取到 100 条记录
2025-05-05 06:00:27,311 - INFO - Request Parameters - Page 3:
2025-05-05 06:00:27,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:27,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:27,780 - INFO - Response - Page 3:
2025-05-05 06:00:27,984 - INFO - 第 3 页获取到 100 条记录
2025-05-05 06:00:27,984 - INFO - Request Parameters - Page 4:
2025-05-05 06:00:27,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:27,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:28,453 - INFO - Response - Page 4:
2025-05-05 06:00:28,656 - INFO - 第 4 页获取到 100 条记录
2025-05-05 06:00:28,656 - INFO - Request Parameters - Page 5:
2025-05-05 06:00:28,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:28,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:29,126 - INFO - Response - Page 5:
2025-05-05 06:00:29,329 - INFO - 第 5 页获取到 100 条记录
2025-05-05 06:00:29,329 - INFO - Request Parameters - Page 6:
2025-05-05 06:00:29,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 06:00:29,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 06:00:29,845 - INFO - Response - Page 6:
2025-05-05 06:00:30,049 - INFO - 第 6 页获取到 96 条记录
2025-05-05 06:00:30,049 - INFO - 查询完成，共获取到 596 条记录
2025-05-05 06:00:30,049 - INFO - 获取到 596 条表单数据
2025-05-05 06:00:30,049 - INFO - 当前日期 2025-05 有 596 条MySQL数据需要处理
2025-05-05 06:00:30,064 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-05 06:00:30,502 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-05 06:00:30,502 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13675.0, 'new_value': 17381.0}, {'field': 'total_amount', 'old_value': 13675.0, 'new_value': 17381.0}, {'field': 'order_count', 'old_value': 570, 'new_value': 728}]
2025-05-05 06:00:30,502 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-05 06:00:30,502 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-05 06:00:30,502 - INFO - =================同步完成====================
2025-05-05 09:00:01,919 - INFO - =================使用默认全量同步=============
2025-05-05 09:00:03,107 - INFO - MySQL查询成功，共获取 3236 条记录
2025-05-05 09:00:03,107 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-05 09:00:03,138 - INFO - 开始处理日期: 2025-01
2025-05-05 09:00:03,138 - INFO - Request Parameters - Page 1:
2025-05-05 09:00:03,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:03,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:03,997 - INFO - Response - Page 1:
2025-05-05 09:00:04,200 - INFO - 第 1 页获取到 100 条记录
2025-05-05 09:00:04,200 - INFO - Request Parameters - Page 2:
2025-05-05 09:00:04,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:04,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:05,138 - INFO - Response - Page 2:
2025-05-05 09:00:05,341 - INFO - 第 2 页获取到 100 条记录
2025-05-05 09:00:05,341 - INFO - Request Parameters - Page 3:
2025-05-05 09:00:05,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:05,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:05,841 - INFO - Response - Page 3:
2025-05-05 09:00:06,044 - INFO - 第 3 页获取到 100 条记录
2025-05-05 09:00:06,044 - INFO - Request Parameters - Page 4:
2025-05-05 09:00:06,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:06,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:06,544 - INFO - Response - Page 4:
2025-05-05 09:00:06,747 - INFO - 第 4 页获取到 100 条记录
2025-05-05 09:00:06,747 - INFO - Request Parameters - Page 5:
2025-05-05 09:00:06,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:06,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:07,216 - INFO - Response - Page 5:
2025-05-05 09:00:07,419 - INFO - 第 5 页获取到 100 条记录
2025-05-05 09:00:07,419 - INFO - Request Parameters - Page 6:
2025-05-05 09:00:07,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:07,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:07,966 - INFO - Response - Page 6:
2025-05-05 09:00:08,169 - INFO - 第 6 页获取到 100 条记录
2025-05-05 09:00:08,169 - INFO - Request Parameters - Page 7:
2025-05-05 09:00:08,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:08,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:08,638 - INFO - Response - Page 7:
2025-05-05 09:00:08,841 - INFO - 第 7 页获取到 82 条记录
2025-05-05 09:00:08,841 - INFO - 查询完成，共获取到 682 条记录
2025-05-05 09:00:08,841 - INFO - 获取到 682 条表单数据
2025-05-05 09:00:08,841 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-05 09:00:08,857 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 09:00:08,857 - INFO - 开始处理日期: 2025-02
2025-05-05 09:00:08,857 - INFO - Request Parameters - Page 1:
2025-05-05 09:00:08,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:08,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:09,404 - INFO - Response - Page 1:
2025-05-05 09:00:09,607 - INFO - 第 1 页获取到 100 条记录
2025-05-05 09:00:09,607 - INFO - Request Parameters - Page 2:
2025-05-05 09:00:09,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:09,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:10,122 - INFO - Response - Page 2:
2025-05-05 09:00:10,325 - INFO - 第 2 页获取到 100 条记录
2025-05-05 09:00:10,325 - INFO - Request Parameters - Page 3:
2025-05-05 09:00:10,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:10,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:10,810 - INFO - Response - Page 3:
2025-05-05 09:00:11,013 - INFO - 第 3 页获取到 100 条记录
2025-05-05 09:00:11,013 - INFO - Request Parameters - Page 4:
2025-05-05 09:00:11,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:11,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:11,513 - INFO - Response - Page 4:
2025-05-05 09:00:11,716 - INFO - 第 4 页获取到 100 条记录
2025-05-05 09:00:11,716 - INFO - Request Parameters - Page 5:
2025-05-05 09:00:11,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:11,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:12,232 - INFO - Response - Page 5:
2025-05-05 09:00:12,435 - INFO - 第 5 页获取到 100 条记录
2025-05-05 09:00:12,435 - INFO - Request Parameters - Page 6:
2025-05-05 09:00:12,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:12,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:13,216 - INFO - Response - Page 6:
2025-05-05 09:00:13,419 - INFO - 第 6 页获取到 100 条记录
2025-05-05 09:00:13,419 - INFO - Request Parameters - Page 7:
2025-05-05 09:00:13,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:13,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:13,872 - INFO - Response - Page 7:
2025-05-05 09:00:14,075 - INFO - 第 7 页获取到 70 条记录
2025-05-05 09:00:14,075 - INFO - 查询完成，共获取到 670 条记录
2025-05-05 09:00:14,075 - INFO - 获取到 670 条表单数据
2025-05-05 09:00:14,075 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-05 09:00:14,091 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 09:00:14,091 - INFO - 开始处理日期: 2025-03
2025-05-05 09:00:14,091 - INFO - Request Parameters - Page 1:
2025-05-05 09:00:14,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:14,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:14,544 - INFO - Response - Page 1:
2025-05-05 09:00:14,747 - INFO - 第 1 页获取到 100 条记录
2025-05-05 09:00:14,747 - INFO - Request Parameters - Page 2:
2025-05-05 09:00:14,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:14,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:15,232 - INFO - Response - Page 2:
2025-05-05 09:00:15,435 - INFO - 第 2 页获取到 100 条记录
2025-05-05 09:00:15,435 - INFO - Request Parameters - Page 3:
2025-05-05 09:00:15,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:15,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:15,904 - INFO - Response - Page 3:
2025-05-05 09:00:16,107 - INFO - 第 3 页获取到 100 条记录
2025-05-05 09:00:16,107 - INFO - Request Parameters - Page 4:
2025-05-05 09:00:16,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:16,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:16,560 - INFO - Response - Page 4:
2025-05-05 09:00:16,763 - INFO - 第 4 页获取到 100 条记录
2025-05-05 09:00:16,763 - INFO - Request Parameters - Page 5:
2025-05-05 09:00:16,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:16,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:17,216 - INFO - Response - Page 5:
2025-05-05 09:00:17,419 - INFO - 第 5 页获取到 100 条记录
2025-05-05 09:00:17,419 - INFO - Request Parameters - Page 6:
2025-05-05 09:00:17,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:17,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:17,950 - INFO - Response - Page 6:
2025-05-05 09:00:18,154 - INFO - 第 6 页获取到 100 条记录
2025-05-05 09:00:18,154 - INFO - Request Parameters - Page 7:
2025-05-05 09:00:18,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:18,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:18,575 - INFO - Response - Page 7:
2025-05-05 09:00:18,779 - INFO - 第 7 页获取到 61 条记录
2025-05-05 09:00:18,779 - INFO - 查询完成，共获取到 661 条记录
2025-05-05 09:00:18,779 - INFO - 获取到 661 条表单数据
2025-05-05 09:00:18,779 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-05 09:00:18,794 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 09:00:18,794 - INFO - 开始处理日期: 2025-04
2025-05-05 09:00:18,794 - INFO - Request Parameters - Page 1:
2025-05-05 09:00:18,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:18,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:19,435 - INFO - Response - Page 1:
2025-05-05 09:00:19,638 - INFO - 第 1 页获取到 100 条记录
2025-05-05 09:00:19,638 - INFO - Request Parameters - Page 2:
2025-05-05 09:00:19,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:19,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:20,153 - INFO - Response - Page 2:
2025-05-05 09:00:20,357 - INFO - 第 2 页获取到 100 条记录
2025-05-05 09:00:20,357 - INFO - Request Parameters - Page 3:
2025-05-05 09:00:20,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:20,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:20,841 - INFO - Response - Page 3:
2025-05-05 09:00:21,044 - INFO - 第 3 页获取到 100 条记录
2025-05-05 09:00:21,044 - INFO - Request Parameters - Page 4:
2025-05-05 09:00:21,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:21,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:21,497 - INFO - Response - Page 4:
2025-05-05 09:00:21,700 - INFO - 第 4 页获取到 100 条记录
2025-05-05 09:00:21,700 - INFO - Request Parameters - Page 5:
2025-05-05 09:00:21,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:21,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:22,216 - INFO - Response - Page 5:
2025-05-05 09:00:22,419 - INFO - 第 5 页获取到 100 条记录
2025-05-05 09:00:22,419 - INFO - Request Parameters - Page 6:
2025-05-05 09:00:22,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:22,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:23,060 - INFO - Response - Page 6:
2025-05-05 09:00:23,263 - INFO - 第 6 页获取到 100 条记录
2025-05-05 09:00:23,263 - INFO - Request Parameters - Page 7:
2025-05-05 09:00:23,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:23,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:23,638 - INFO - Response - Page 7:
2025-05-05 09:00:23,841 - INFO - 第 7 页获取到 27 条记录
2025-05-05 09:00:23,841 - INFO - 查询完成，共获取到 627 条记录
2025-05-05 09:00:23,841 - INFO - 获取到 627 条表单数据
2025-05-05 09:00:23,841 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-05 09:00:23,857 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 09:00:23,857 - INFO - 开始处理日期: 2025-05
2025-05-05 09:00:23,857 - INFO - Request Parameters - Page 1:
2025-05-05 09:00:23,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:23,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:24,341 - INFO - Response - Page 1:
2025-05-05 09:00:24,544 - INFO - 第 1 页获取到 100 条记录
2025-05-05 09:00:24,544 - INFO - Request Parameters - Page 2:
2025-05-05 09:00:24,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:24,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:25,044 - INFO - Response - Page 2:
2025-05-05 09:00:25,247 - INFO - 第 2 页获取到 100 条记录
2025-05-05 09:00:25,247 - INFO - Request Parameters - Page 3:
2025-05-05 09:00:25,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:25,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:25,778 - INFO - Response - Page 3:
2025-05-05 09:00:25,982 - INFO - 第 3 页获取到 100 条记录
2025-05-05 09:00:25,982 - INFO - Request Parameters - Page 4:
2025-05-05 09:00:25,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:25,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:26,435 - INFO - Response - Page 4:
2025-05-05 09:00:26,638 - INFO - 第 4 页获取到 100 条记录
2025-05-05 09:00:26,638 - INFO - Request Parameters - Page 5:
2025-05-05 09:00:26,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:26,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:27,169 - INFO - Response - Page 5:
2025-05-05 09:00:27,372 - INFO - 第 5 页获取到 100 条记录
2025-05-05 09:00:27,372 - INFO - Request Parameters - Page 6:
2025-05-05 09:00:27,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 09:00:27,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 09:00:27,919 - INFO - Response - Page 6:
2025-05-05 09:00:28,122 - INFO - 第 6 页获取到 96 条记录
2025-05-05 09:00:28,122 - INFO - 查询完成，共获取到 596 条记录
2025-05-05 09:00:28,122 - INFO - 获取到 596 条表单数据
2025-05-05 09:00:28,122 - INFO - 当前日期 2025-05 有 596 条MySQL数据需要处理
2025-05-05 09:00:28,122 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-05 09:00:28,622 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-05 09:00:28,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 28000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 28000.0}]
2025-05-05 09:00:28,622 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-05 09:00:29,138 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-05 09:00:29,138 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140.0, 'new_value': 210.0}, {'field': 'offline_amount', 'old_value': 8920.0, 'new_value': 10535.0}, {'field': 'total_amount', 'old_value': 9060.0, 'new_value': 10745.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 125}]
2025-05-05 09:00:29,138 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-05 09:00:29,544 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-05 09:00:29,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53747.0, 'new_value': 74687.0}, {'field': 'total_amount', 'old_value': 53747.0, 'new_value': 74687.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 48}]
2025-05-05 09:00:29,544 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-05 09:00:30,013 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-05 09:00:30,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1747.0, 'new_value': 2297.37}, {'field': 'offline_amount', 'old_value': 982.0, 'new_value': 1669.2}, {'field': 'total_amount', 'old_value': 2729.0, 'new_value': 3966.57}, {'field': 'order_count', 'old_value': 129, 'new_value': 177}]
2025-05-05 09:00:30,013 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-05 09:00:30,450 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-05 09:00:30,450 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27950.0, 'new_value': 36810.0}, {'field': 'total_amount', 'old_value': 27950.0, 'new_value': 36810.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-05-05 09:00:30,450 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-05 09:00:30,935 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-05 09:00:30,935 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99.0, 'new_value': 6199.0}, {'field': 'total_amount', 'old_value': 74902.0, 'new_value': 81002.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-05-05 09:00:30,935 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-05 09:00:31,435 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-05 09:00:31,435 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39414.0, 'new_value': 73108.0}, {'field': 'offline_amount', 'old_value': 51593.0, 'new_value': 64085.0}, {'field': 'total_amount', 'old_value': 91007.0, 'new_value': 137193.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 135}]
2025-05-05 09:00:31,435 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-05 09:00:31,872 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-05 09:00:31,872 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21813.0, 'new_value': 26092.0}, {'field': 'total_amount', 'old_value': 21813.0, 'new_value': 26092.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 22}]
2025-05-05 09:00:31,872 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC
2025-05-05 09:00:32,325 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC
2025-05-05 09:00:32,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 739700.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 739700.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-05-05 09:00:32,325 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-05 09:00:32,747 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-05 09:00:32,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26971.0, 'new_value': 35451.0}, {'field': 'total_amount', 'old_value': 26971.0, 'new_value': 35451.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 16}]
2025-05-05 09:00:32,747 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-05 09:00:33,310 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-05 09:00:33,310 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8766.46, 'new_value': 10705.57}, {'field': 'offline_amount', 'old_value': 13656.0, 'new_value': 17387.0}, {'field': 'total_amount', 'old_value': 22422.46, 'new_value': 28092.57}, {'field': 'order_count', 'old_value': 240, 'new_value': 302}]
2025-05-05 09:00:33,310 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-05 09:00:33,700 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-05 09:00:33,700 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4048.0, 'new_value': 5197.0}, {'field': 'offline_amount', 'old_value': 6172.0, 'new_value': 7357.0}, {'field': 'total_amount', 'old_value': 10220.0, 'new_value': 12554.0}, {'field': 'order_count', 'old_value': 430, 'new_value': 564}]
2025-05-05 09:00:33,700 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-05 09:00:34,138 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-05 09:00:34,138 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 25499.8}, {'field': 'offline_amount', 'old_value': 70299.7, 'new_value': 95799.5}, {'field': 'total_amount', 'old_value': 70299.7, 'new_value': 121299.3}, {'field': 'order_count', 'old_value': 756, 'new_value': 1033}]
2025-05-05 09:00:34,138 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-05 09:00:34,575 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-05 09:00:34,575 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11358.0, 'new_value': 15769.0}, {'field': 'total_amount', 'old_value': 11358.0, 'new_value': 15769.0}, {'field': 'order_count', 'old_value': 580, 'new_value': 791}]
2025-05-05 09:00:34,575 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-05 09:00:35,013 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-05 09:00:35,013 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3928.0, 'new_value': 4901.0}, {'field': 'total_amount', 'old_value': 3928.0, 'new_value': 4901.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-05-05 09:00:35,013 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-05 09:00:35,482 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-05 09:00:35,482 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13973.42, 'new_value': 17849.03}, {'field': 'total_amount', 'old_value': 13973.42, 'new_value': 17849.03}, {'field': 'order_count', 'old_value': 137, 'new_value': 187}]
2025-05-05 09:00:35,482 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-05 09:00:35,888 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-05 09:00:35,903 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1257.4}, {'field': 'offline_amount', 'old_value': 14360.6, 'new_value': 14361.6}, {'field': 'total_amount', 'old_value': 14360.6, 'new_value': 15619.0}]
2025-05-05 09:00:35,903 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-05 09:00:36,357 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-05 09:00:36,357 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1486.0, 'new_value': 2243.0}, {'field': 'total_amount', 'old_value': 3862.0, 'new_value': 4619.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 20}]
2025-05-05 09:00:36,357 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-05 09:00:36,857 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-05 09:00:36,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19133.0, 'new_value': 22250.0}, {'field': 'total_amount', 'old_value': 19133.0, 'new_value': 22250.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 49}]
2025-05-05 09:00:36,857 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-05 09:00:37,310 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-05 09:00:37,310 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2452.06, 'new_value': 4574.3}, {'field': 'total_amount', 'old_value': 2452.06, 'new_value': 4574.3}, {'field': 'order_count', 'old_value': 9, 'new_value': 15}]
2025-05-05 09:00:37,310 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-05 09:00:37,794 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-05 09:00:37,794 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39623.11, 'new_value': 50582.98}, {'field': 'offline_amount', 'old_value': 7861.73, 'new_value': 9598.01}, {'field': 'total_amount', 'old_value': 47484.84, 'new_value': 60180.99}, {'field': 'order_count', 'old_value': 163, 'new_value': 212}]
2025-05-05 09:00:37,794 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-05 09:00:38,278 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-05 09:00:38,278 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42343.0, 'new_value': 53843.0}, {'field': 'offline_amount', 'old_value': 14151.0, 'new_value': 16235.8}, {'field': 'total_amount', 'old_value': 56494.0, 'new_value': 70078.8}, {'field': 'order_count', 'old_value': 330, 'new_value': 409}]
2025-05-05 09:00:38,278 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-05 09:00:38,732 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-05 09:00:38,732 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1217.0, 'new_value': 1616.0}, {'field': 'total_amount', 'old_value': 1335.0, 'new_value': 1734.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 36}]
2025-05-05 09:00:38,732 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-05 09:00:39,185 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-05 09:00:39,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10395.94, 'new_value': 13368.17}, {'field': 'total_amount', 'old_value': 10395.94, 'new_value': 13368.17}, {'field': 'order_count', 'old_value': 241, 'new_value': 318}]
2025-05-05 09:00:39,185 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-05 09:00:39,622 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-05 09:00:39,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26225.0, 'new_value': 33320.0}, {'field': 'total_amount', 'old_value': 26225.0, 'new_value': 33320.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 14}]
2025-05-05 09:00:39,622 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-05 09:00:40,107 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-05 09:00:40,107 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26437.0, 'new_value': 34613.0}, {'field': 'offline_amount', 'old_value': 10057.5, 'new_value': 13884.5}, {'field': 'total_amount', 'old_value': 36494.5, 'new_value': 48497.5}, {'field': 'order_count', 'old_value': 235, 'new_value': 321}]
2025-05-05 09:00:40,107 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-05 09:00:40,638 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-05 09:00:40,638 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1091.68, 'new_value': 1773.51}, {'field': 'offline_amount', 'old_value': 23815.05, 'new_value': 30196.66}, {'field': 'total_amount', 'old_value': 24906.73, 'new_value': 31970.17}, {'field': 'order_count', 'old_value': 251, 'new_value': 328}]
2025-05-05 09:00:40,638 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-05 09:00:41,075 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-05 09:00:41,075 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3816.0, 'new_value': 15966.0}, {'field': 'total_amount', 'old_value': 5760.0, 'new_value': 17910.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 7}]
2025-05-05 09:00:41,075 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-05 09:00:41,497 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-05 09:00:41,497 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25902.0, 'new_value': 37861.0}, {'field': 'total_amount', 'old_value': 25902.0, 'new_value': 37861.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 182}]
2025-05-05 09:00:41,497 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-05 09:00:41,935 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-05 09:00:41,935 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31485.43, 'new_value': 45721.45}, {'field': 'total_amount', 'old_value': 31485.43, 'new_value': 45721.45}, {'field': 'order_count', 'old_value': 99, 'new_value': 138}]
2025-05-05 09:00:41,935 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-05 09:00:42,388 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-05 09:00:42,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17143.0, 'new_value': 22747.0}, {'field': 'total_amount', 'old_value': 17143.0, 'new_value': 22747.0}, {'field': 'order_count', 'old_value': 391, 'new_value': 517}]
2025-05-05 09:00:42,388 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-05 09:00:42,841 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-05 09:00:42,841 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9000.38, 'new_value': 12973.8}, {'field': 'offline_amount', 'old_value': 125743.9, 'new_value': 165954.9}, {'field': 'total_amount', 'old_value': 134744.28, 'new_value': 178928.7}, {'field': 'order_count', 'old_value': 392, 'new_value': 519}]
2025-05-05 09:00:42,841 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-05 09:00:43,263 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-05 09:00:43,263 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4534.48, 'new_value': 6376.53}, {'field': 'total_amount', 'old_value': 4534.48, 'new_value': 6376.53}, {'field': 'order_count', 'old_value': 9, 'new_value': 14}]
2025-05-05 09:00:43,263 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-05 09:00:43,794 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-05 09:00:43,794 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17167.0, 'new_value': 21928.0}, {'field': 'total_amount', 'old_value': 17167.0, 'new_value': 21928.0}, {'field': 'order_count', 'old_value': 550, 'new_value': 712}]
2025-05-05 09:00:43,794 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-05 09:00:44,325 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-05 09:00:44,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107775.39, 'new_value': 140379.57}, {'field': 'total_amount', 'old_value': 107775.39, 'new_value': 140379.57}, {'field': 'order_count', 'old_value': 760, 'new_value': 989}]
2025-05-05 09:00:44,325 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-05 09:00:44,794 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-05 09:00:44,794 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8033.0, 'new_value': 9305.0}, {'field': 'offline_amount', 'old_value': 33749.0, 'new_value': 67855.0}, {'field': 'total_amount', 'old_value': 41782.0, 'new_value': 77160.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 15}]
2025-05-05 09:00:44,794 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-05 09:00:45,403 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-05 09:00:45,403 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13289.82, 'new_value': 19037.3}, {'field': 'total_amount', 'old_value': 13289.82, 'new_value': 19037.3}, {'field': 'order_count', 'old_value': 490, 'new_value': 691}]
2025-05-05 09:00:45,403 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-05 09:00:45,841 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-05 09:00:45,841 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1288.62, 'new_value': 3425.56}, {'field': 'total_amount', 'old_value': 1288.62, 'new_value': 3425.56}, {'field': 'order_count', 'old_value': 3, 'new_value': 6}]
2025-05-05 09:00:45,841 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-05 09:00:46,294 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-05 09:00:46,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22730.0, 'new_value': 32719.0}, {'field': 'total_amount', 'old_value': 22730.0, 'new_value': 32719.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 44}]
2025-05-05 09:00:46,294 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-05 09:00:46,856 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-05 09:00:46,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62541.0, 'new_value': 82057.0}, {'field': 'total_amount', 'old_value': 62541.0, 'new_value': 82057.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 129}]
2025-05-05 09:00:46,856 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-05 09:00:47,278 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-05 09:00:47,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31669.08, 'new_value': 37727.08}, {'field': 'total_amount', 'old_value': 31669.08, 'new_value': 37727.08}, {'field': 'order_count', 'old_value': 158, 'new_value': 186}]
2025-05-05 09:00:47,278 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-05 09:00:47,731 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-05 09:00:47,731 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23198.0, 'new_value': 31796.0}, {'field': 'total_amount', 'old_value': 23198.0, 'new_value': 31796.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-05 09:00:47,731 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-05 09:00:48,138 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-05 09:00:48,138 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 341885.4, 'new_value': 460582.6}, {'field': 'total_amount', 'old_value': 341885.4, 'new_value': 460582.6}, {'field': 'order_count', 'old_value': 539, 'new_value': 747}]
2025-05-05 09:00:48,138 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-05 09:00:48,591 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-05 09:00:48,591 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20467.75, 'new_value': 27139.25}, {'field': 'offline_amount', 'old_value': 12920.0, 'new_value': 16328.0}, {'field': 'total_amount', 'old_value': 33387.75, 'new_value': 43467.25}, {'field': 'order_count', 'old_value': 167, 'new_value': 220}]
2025-05-05 09:00:48,591 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-05 09:00:49,075 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-05 09:00:49,075 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7986.0, 'new_value': 10977.0}, {'field': 'total_amount', 'old_value': 12727.0, 'new_value': 15718.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-05 09:00:49,075 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-05 09:00:49,513 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-05 09:00:49,513 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6438.0, 'new_value': 8352.0}, {'field': 'total_amount', 'old_value': 6438.0, 'new_value': 8352.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 144}]
2025-05-05 09:00:49,513 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-05 09:00:49,950 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-05 09:00:49,950 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21438.0, 'new_value': 30159.0}, {'field': 'total_amount', 'old_value': 21439.0, 'new_value': 30160.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 23}]
2025-05-05 09:00:49,950 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-05 09:00:50,419 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-05 09:00:50,419 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 910.0, 'new_value': 1052.6}, {'field': 'offline_amount', 'old_value': 2303.87, 'new_value': 2682.87}, {'field': 'total_amount', 'old_value': 3213.87, 'new_value': 3735.47}, {'field': 'order_count', 'old_value': 106, 'new_value': 128}]
2025-05-05 09:00:50,419 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-05 09:00:50,810 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-05 09:00:50,810 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23628.7, 'new_value': 30605.22}, {'field': 'offline_amount', 'old_value': 18908.28, 'new_value': 22775.37}, {'field': 'total_amount', 'old_value': 42536.98, 'new_value': 53380.59}, {'field': 'order_count', 'old_value': 325, 'new_value': 425}]
2025-05-05 09:00:50,810 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-05 09:00:51,278 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-05 09:00:51,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9133.45, 'new_value': 13012.1}, {'field': 'total_amount', 'old_value': 9133.45, 'new_value': 13012.1}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-05-05 09:00:51,278 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-05 09:00:51,716 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-05 09:00:51,716 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78327.0, 'new_value': 95745.9}, {'field': 'offline_amount', 'old_value': 5242.0, 'new_value': 6583.0}, {'field': 'total_amount', 'old_value': 83569.0, 'new_value': 102328.9}, {'field': 'order_count', 'old_value': 103, 'new_value': 129}]
2025-05-05 09:00:51,716 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-05 09:00:52,185 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-05 09:00:52,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5211.0, 'new_value': 7742.0}, {'field': 'total_amount', 'old_value': 5211.0, 'new_value': 7742.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 18}]
2025-05-05 09:00:52,185 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-05 09:00:52,544 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-05 09:00:52,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27920.0, 'new_value': 41038.0}, {'field': 'total_amount', 'old_value': 30601.0, 'new_value': 43719.0}, {'field': 'order_count', 'old_value': 660, 'new_value': 687}]
2025-05-05 09:00:52,544 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-05 09:00:52,997 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-05 09:00:52,997 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1534.14, 'new_value': 2208.02}, {'field': 'offline_amount', 'old_value': 42544.2, 'new_value': 59064.3}, {'field': 'total_amount', 'old_value': 44078.34, 'new_value': 61272.32}, {'field': 'order_count', 'old_value': 278, 'new_value': 391}]
2025-05-05 09:00:52,997 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-05 09:00:53,435 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-05 09:00:53,435 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25834.29, 'new_value': 32968.14}, {'field': 'offline_amount', 'old_value': 24052.45, 'new_value': 30823.45}, {'field': 'total_amount', 'old_value': 49886.74, 'new_value': 63791.59}, {'field': 'order_count', 'old_value': 420, 'new_value': 570}]
2025-05-05 09:00:53,435 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-05 09:00:53,903 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-05 09:00:53,903 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9796.1, 'new_value': 12595.1}, {'field': 'total_amount', 'old_value': 10219.75, 'new_value': 13018.75}, {'field': 'order_count', 'old_value': 32, 'new_value': 41}]
2025-05-05 09:00:53,903 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-05 09:00:54,341 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-05 09:00:54,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12205.14, 'new_value': 16811.23}, {'field': 'total_amount', 'old_value': 12205.14, 'new_value': 16811.23}, {'field': 'order_count', 'old_value': 317, 'new_value': 441}]
2025-05-05 09:00:54,341 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-05 09:00:54,763 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-05 09:00:54,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8482.0, 'new_value': 9462.0}, {'field': 'total_amount', 'old_value': 8482.0, 'new_value': 9462.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 09:00:54,763 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-05 09:00:55,216 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-05 09:00:55,216 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11357.62, 'new_value': 15976.51}, {'field': 'offline_amount', 'old_value': 76457.8, 'new_value': 99115.78}, {'field': 'total_amount', 'old_value': 87815.42, 'new_value': 115092.29}, {'field': 'order_count', 'old_value': 533, 'new_value': 628}]
2025-05-05 09:00:55,216 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-05 09:00:55,747 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-05 09:00:55,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49066.86, 'new_value': 60533.12}, {'field': 'total_amount', 'old_value': 49066.86, 'new_value': 60533.12}, {'field': 'order_count', 'old_value': 91, 'new_value': 120}]
2025-05-05 09:00:55,747 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-05 09:00:56,185 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-05 09:00:56,185 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3034.04, 'new_value': 3489.63}, {'field': 'offline_amount', 'old_value': 77594.85, 'new_value': 107073.38}, {'field': 'total_amount', 'old_value': 80628.89, 'new_value': 110563.01}, {'field': 'order_count', 'old_value': 272, 'new_value': 382}]
2025-05-05 09:00:56,185 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-05 09:00:56,622 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-05 09:00:56,622 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21180.0, 'new_value': 24520.0}, {'field': 'offline_amount', 'old_value': 9986.0, 'new_value': 11505.0}, {'field': 'total_amount', 'old_value': 31166.0, 'new_value': 36025.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 42}]
2025-05-05 09:00:56,622 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-05 09:00:57,060 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-05 09:00:57,060 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 821.0, 'new_value': 2261.0}, {'field': 'total_amount', 'old_value': 821.0, 'new_value': 2261.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-05-05 09:00:57,060 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-05 09:00:57,497 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-05 09:00:57,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6285.0, 'new_value': 8120.0}, {'field': 'total_amount', 'old_value': 6285.0, 'new_value': 8120.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 09:00:57,497 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-05 09:00:57,919 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-05 09:00:57,919 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4345.0, 'new_value': 9578.2}, {'field': 'total_amount', 'old_value': 4345.0, 'new_value': 9578.2}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-05 09:00:57,919 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-05 09:00:58,341 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-05 09:00:58,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 778.0, 'new_value': 1578.0}, {'field': 'total_amount', 'old_value': 778.0, 'new_value': 1578.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-05 09:00:58,341 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-05 09:00:58,825 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-05 09:00:58,825 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 994.84, 'new_value': 1204.87}, {'field': 'offline_amount', 'old_value': 1296.0, 'new_value': 1713.0}, {'field': 'total_amount', 'old_value': 2290.84, 'new_value': 2917.87}, {'field': 'order_count', 'old_value': 33, 'new_value': 39}]
2025-05-05 09:00:58,825 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-05 09:00:59,247 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-05 09:00:59,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10501.26, 'new_value': 13410.22}, {'field': 'total_amount', 'old_value': 10501.26, 'new_value': 13410.22}, {'field': 'order_count', 'old_value': 80, 'new_value': 104}]
2025-05-05 09:00:59,247 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-05 09:00:59,716 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-05 09:00:59,716 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 575.0, 'new_value': 792.0}, {'field': 'offline_amount', 'old_value': 3449.1, 'new_value': 4725.4}, {'field': 'total_amount', 'old_value': 4024.1, 'new_value': 5517.4}, {'field': 'order_count', 'old_value': 157, 'new_value': 221}]
2025-05-05 09:00:59,716 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-05 09:01:00,200 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-05 09:01:00,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20762.8, 'new_value': 27500.88}, {'field': 'total_amount', 'old_value': 20762.8, 'new_value': 27500.88}, {'field': 'order_count', 'old_value': 44, 'new_value': 70}]
2025-05-05 09:01:00,200 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-05 09:01:00,638 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-05 09:01:00,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32895.0, 'new_value': 40813.0}, {'field': 'total_amount', 'old_value': 32895.0, 'new_value': 40813.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 152}]
2025-05-05 09:01:00,638 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-05 09:01:01,091 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-05 09:01:01,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32508.71, 'new_value': 42514.57}, {'field': 'total_amount', 'old_value': 32508.71, 'new_value': 42514.57}, {'field': 'order_count', 'old_value': 795, 'new_value': 1061}]
2025-05-05 09:01:01,091 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-05 09:01:01,591 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-05 09:01:01,591 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20973.78, 'new_value': 29314.41}, {'field': 'total_amount', 'old_value': 20973.78, 'new_value': 29314.41}, {'field': 'order_count', 'old_value': 97, 'new_value': 129}]
2025-05-05 09:01:01,591 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-05 09:01:02,028 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-05 09:01:02,028 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2976.43, 'new_value': 3791.61}, {'field': 'offline_amount', 'old_value': 3982.9, 'new_value': 5345.9}, {'field': 'total_amount', 'old_value': 6959.33, 'new_value': 9137.51}, {'field': 'order_count', 'old_value': 247, 'new_value': 324}]
2025-05-05 09:01:02,028 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-05 09:01:02,481 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-05 09:01:02,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3778.0, 'new_value': 7568.0}, {'field': 'total_amount', 'old_value': 6186.0, 'new_value': 9976.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 38}]
2025-05-05 09:01:02,481 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-05 09:01:02,966 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-05 09:01:02,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8193.0, 'new_value': 14063.0}, {'field': 'total_amount', 'old_value': 8193.0, 'new_value': 14063.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 6}]
2025-05-05 09:01:02,966 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-05 09:01:03,513 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-05 09:01:03,513 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7223.0, 'new_value': 8766.7}, {'field': 'offline_amount', 'old_value': 11171.77, 'new_value': 15386.97}, {'field': 'total_amount', 'old_value': 18394.77, 'new_value': 24153.67}, {'field': 'order_count', 'old_value': 185, 'new_value': 250}]
2025-05-05 09:01:03,513 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-05 09:01:04,013 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-05 09:01:04,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16347.2, 'new_value': 22120.9}, {'field': 'offline_amount', 'old_value': 14187.47, 'new_value': 21361.47}, {'field': 'total_amount', 'old_value': 30534.67, 'new_value': 43482.37}, {'field': 'order_count', 'old_value': 235, 'new_value': 334}]
2025-05-05 09:01:04,013 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-05 09:01:04,544 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-05 09:01:04,544 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1238.48, 'new_value': 1426.68}, {'field': 'offline_amount', 'old_value': 15561.0, 'new_value': 21274.53}, {'field': 'total_amount', 'old_value': 16799.48, 'new_value': 22701.21}, {'field': 'order_count', 'old_value': 578, 'new_value': 758}]
2025-05-05 09:01:04,544 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-05 09:01:05,013 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-05 09:01:05,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1298.73, 'new_value': 1848.11}, {'field': 'offline_amount', 'old_value': 3106.3, 'new_value': 4319.88}, {'field': 'total_amount', 'old_value': 4405.03, 'new_value': 6167.99}, {'field': 'order_count', 'old_value': 228, 'new_value': 319}]
2025-05-05 09:01:05,013 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-05 09:01:05,497 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-05 09:01:05,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12738.0, 'new_value': 15309.0}, {'field': 'total_amount', 'old_value': 12738.0, 'new_value': 15309.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 25}]
2025-05-05 09:01:05,497 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-05 09:01:05,981 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-05 09:01:05,981 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13690.0, 'new_value': 19660.0}, {'field': 'total_amount', 'old_value': 13690.0, 'new_value': 19660.0}, {'field': 'order_count', 'old_value': 88, 'new_value': 120}]
2025-05-05 09:01:05,981 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-05 09:01:06,388 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-05 09:01:06,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99901.0, 'new_value': 128877.0}, {'field': 'total_amount', 'old_value': 99901.0, 'new_value': 128877.0}, {'field': 'order_count', 'old_value': 765, 'new_value': 994}]
2025-05-05 09:01:06,388 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-05 09:01:06,997 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-05 09:01:06,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21958.86, 'new_value': 28510.68}, {'field': 'total_amount', 'old_value': 21958.86, 'new_value': 28510.68}, {'field': 'order_count', 'old_value': 603, 'new_value': 789}]
2025-05-05 09:01:06,997 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-05 09:01:07,466 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-05 09:01:07,466 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20215.74, 'new_value': 29878.54}, {'field': 'offline_amount', 'old_value': 32177.85, 'new_value': 41990.15}, {'field': 'total_amount', 'old_value': 52393.59, 'new_value': 71868.69}, {'field': 'order_count', 'old_value': 529, 'new_value': 685}]
2025-05-05 09:01:07,466 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-05 09:01:07,950 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-05 09:01:07,950 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23460.0, 'new_value': 31080.0}, {'field': 'total_amount', 'old_value': 23460.0, 'new_value': 31080.0}, {'field': 'order_count', 'old_value': 1955, 'new_value': 2590}]
2025-05-05 09:01:07,950 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-05 09:01:08,372 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-05 09:01:08,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11760.4, 'new_value': 13004.7}, {'field': 'total_amount', 'old_value': 11760.4, 'new_value': 13004.7}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-05-05 09:01:08,372 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-05 09:01:08,825 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-05 09:01:08,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6664.8, 'new_value': 8639.2}, {'field': 'total_amount', 'old_value': 6664.8, 'new_value': 8639.2}, {'field': 'order_count', 'old_value': 263, 'new_value': 346}]
2025-05-05 09:01:08,825 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-05 09:01:09,231 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-05 09:01:09,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5044.0, 'new_value': 6086.0}, {'field': 'total_amount', 'old_value': 5044.0, 'new_value': 6086.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 66}]
2025-05-05 09:01:09,231 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-05 09:01:09,669 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-05 09:01:09,669 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6760.22, 'new_value': 8917.02}, {'field': 'offline_amount', 'old_value': 5238.09, 'new_value': 6605.43}, {'field': 'total_amount', 'old_value': 11998.31, 'new_value': 15522.45}, {'field': 'order_count', 'old_value': 636, 'new_value': 833}]
2025-05-05 09:01:09,669 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-05 09:01:10,091 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-05 09:01:10,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8313.0, 'new_value': 9995.0}, {'field': 'total_amount', 'old_value': 8313.0, 'new_value': 9995.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-05 09:01:10,091 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-05 09:01:10,528 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-05 09:01:10,528 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2326.88, 'new_value': 2818.78}, {'field': 'total_amount', 'old_value': 2326.88, 'new_value': 2818.78}, {'field': 'order_count', 'old_value': 35, 'new_value': 42}]
2025-05-05 09:01:10,528 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-05 09:01:10,997 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-05 09:01:10,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74877.77, 'new_value': 98139.37}, {'field': 'total_amount', 'old_value': 74877.77, 'new_value': 98139.37}, {'field': 'order_count', 'old_value': 267, 'new_value': 344}]
2025-05-05 09:01:10,997 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-05 09:01:11,403 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-05 09:01:11,403 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34150.7, 'new_value': 46624.5}, {'field': 'total_amount', 'old_value': 34150.7, 'new_value': 46624.5}, {'field': 'order_count', 'old_value': 851, 'new_value': 1171}]
2025-05-05 09:01:11,403 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-05 09:01:11,856 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-05 09:01:11,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4221.72, 'new_value': 5736.45}, {'field': 'total_amount', 'old_value': 4221.72, 'new_value': 5736.45}, {'field': 'order_count', 'old_value': 537, 'new_value': 721}]
2025-05-05 09:01:11,856 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-05 09:01:12,294 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-05 09:01:12,294 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3121.74, 'new_value': 4163.15}, {'field': 'offline_amount', 'old_value': 4752.8, 'new_value': 6717.2}, {'field': 'total_amount', 'old_value': 7874.54, 'new_value': 10880.35}, {'field': 'order_count', 'old_value': 302, 'new_value': 419}]
2025-05-05 09:01:12,294 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-05 09:01:12,731 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-05 09:01:12,731 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 233237.08, 'new_value': 313139.72}, {'field': 'offline_amount', 'old_value': 16223.45, 'new_value': 18265.45}, {'field': 'total_amount', 'old_value': 249460.53, 'new_value': 331405.17}, {'field': 'order_count', 'old_value': 783, 'new_value': 1045}]
2025-05-05 09:01:12,731 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-05 09:01:13,153 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-05 09:01:13,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17721.0, 'new_value': 17861.0}, {'field': 'total_amount', 'old_value': 17721.0, 'new_value': 17861.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-05-05 09:01:13,153 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-05 09:01:13,606 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-05 09:01:13,606 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5014.3, 'new_value': 5160.0}, {'field': 'offline_amount', 'old_value': 5635.0, 'new_value': 8836.0}, {'field': 'total_amount', 'old_value': 10649.3, 'new_value': 13996.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 34}]
2025-05-05 09:01:13,606 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-05 09:01:14,059 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-05 09:01:14,059 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74820.4, 'new_value': 102246.6}, {'field': 'total_amount', 'old_value': 74820.4, 'new_value': 102246.6}, {'field': 'order_count', 'old_value': 367, 'new_value': 515}]
2025-05-05 09:01:14,059 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-05 09:01:14,466 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-05 09:01:14,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54215.0, 'new_value': 84954.0}, {'field': 'total_amount', 'old_value': 54215.0, 'new_value': 84954.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 44}]
2025-05-05 09:01:14,466 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-05 09:01:14,872 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-05 09:01:14,872 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86870.45, 'new_value': 117444.56}, {'field': 'total_amount', 'old_value': 86870.45, 'new_value': 117444.56}, {'field': 'order_count', 'old_value': 176, 'new_value': 233}]
2025-05-05 09:01:14,872 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-05 09:01:15,388 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-05 09:01:15,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18150.0, 'new_value': 24610.0}, {'field': 'total_amount', 'old_value': 18150.0, 'new_value': 24610.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 09:01:15,388 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-05 09:01:15,872 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-05 09:01:15,872 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 524175.0, 'new_value': 553572.0}, {'field': 'total_amount', 'old_value': 524175.0, 'new_value': 553572.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 64}]
2025-05-05 09:01:15,872 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-05 09:01:16,356 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-05 09:01:16,356 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3116.84, 'new_value': 7370.55}, {'field': 'offline_amount', 'old_value': 9701.94, 'new_value': 9867.39}, {'field': 'total_amount', 'old_value': 12818.78, 'new_value': 17237.94}, {'field': 'order_count', 'old_value': 49, 'new_value': 67}]
2025-05-05 09:01:16,356 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-05 09:01:16,716 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-05 09:01:16,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2400.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2400.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-05 09:01:16,716 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-05 09:01:17,169 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-05 09:01:17,184 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75618.0, 'new_value': 95343.0}, {'field': 'offline_amount', 'old_value': 3072.0, 'new_value': 3849.0}, {'field': 'total_amount', 'old_value': 78690.0, 'new_value': 99192.0}, {'field': 'order_count', 'old_value': 427, 'new_value': 587}]
2025-05-05 09:01:17,184 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-05 09:01:17,638 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-05 09:01:17,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3611.0, 'new_value': 3810.0}, {'field': 'total_amount', 'old_value': 3611.0, 'new_value': 3810.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-05 09:01:17,638 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-05 09:01:18,059 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-05 09:01:18,059 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1937.1, 'new_value': 3665.4}, {'field': 'total_amount', 'old_value': 1937.1, 'new_value': 3665.4}, {'field': 'order_count', 'old_value': 74, 'new_value': 107}]
2025-05-05 09:01:18,059 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-05 09:01:18,481 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-05 09:01:18,481 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 312.0, 'new_value': 390.0}, {'field': 'offline_amount', 'old_value': 3855.6, 'new_value': 5368.6}, {'field': 'total_amount', 'old_value': 4167.6, 'new_value': 5758.6}, {'field': 'order_count', 'old_value': 57, 'new_value': 82}]
2025-05-05 09:01:18,481 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-05 09:01:18,934 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-05 09:01:18,934 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1809.0, 'new_value': 2593.9}, {'field': 'offline_amount', 'old_value': 8012.0, 'new_value': 9948.3}, {'field': 'total_amount', 'old_value': 9821.0, 'new_value': 12542.2}, {'field': 'order_count', 'old_value': 94, 'new_value': 118}]
2025-05-05 09:01:18,934 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-05 09:01:19,388 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-05 09:01:19,388 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1728.0, 'new_value': 2397.0}, {'field': 'offline_amount', 'old_value': 3878.0, 'new_value': 4066.0}, {'field': 'total_amount', 'old_value': 5606.0, 'new_value': 6463.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 14}]
2025-05-05 09:01:19,388 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-05 09:01:19,856 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-05 09:01:19,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118474.44, 'new_value': 151785.98}, {'field': 'total_amount', 'old_value': 118474.44, 'new_value': 151785.98}, {'field': 'order_count', 'old_value': 752, 'new_value': 993}]
2025-05-05 09:01:19,856 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-05 09:01:20,294 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-05 09:01:20,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193493.0, 'new_value': 237639.0}, {'field': 'total_amount', 'old_value': 193493.0, 'new_value': 237639.0}, {'field': 'order_count', 'old_value': 904, 'new_value': 1094}]
2025-05-05 09:01:20,294 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-05 09:01:20,684 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-05 09:01:20,684 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25874.86, 'new_value': 35002.86}, {'field': 'total_amount', 'old_value': 25874.86, 'new_value': 35002.86}, {'field': 'order_count', 'old_value': 139, 'new_value': 190}]
2025-05-05 09:01:20,684 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-05 09:01:21,106 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-05 09:01:21,106 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9196.4, 'new_value': 12673.68}, {'field': 'offline_amount', 'old_value': 18557.15, 'new_value': 22580.86}, {'field': 'total_amount', 'old_value': 27753.55, 'new_value': 35254.54}, {'field': 'order_count', 'old_value': 1063, 'new_value': 1365}]
2025-05-05 09:01:21,106 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-05 09:01:21,528 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-05 09:01:21,528 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7999.0, 'new_value': 14374.0}, {'field': 'total_amount', 'old_value': 7999.0, 'new_value': 14374.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 33}]
2025-05-05 09:01:21,528 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-05 09:01:21,997 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-05 09:01:21,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2127.0, 'new_value': 6527.0}, {'field': 'total_amount', 'old_value': 2127.0, 'new_value': 6527.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 6}]
2025-05-05 09:01:21,997 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-05 09:01:22,419 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-05 09:01:22,419 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7884.0, 'new_value': 10705.0}, {'field': 'offline_amount', 'old_value': 7645.0, 'new_value': 10571.0}, {'field': 'total_amount', 'old_value': 15529.0, 'new_value': 21276.0}, {'field': 'order_count', 'old_value': 281, 'new_value': 387}]
2025-05-05 09:01:22,419 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-05 09:01:22,919 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-05 09:01:22,919 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2326.0, 'new_value': 2898.0}, {'field': 'offline_amount', 'old_value': 36427.0, 'new_value': 48058.0}, {'field': 'total_amount', 'old_value': 38753.0, 'new_value': 50956.0}, {'field': 'order_count', 'old_value': 185, 'new_value': 243}]
2025-05-05 09:01:22,919 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-05 09:01:23,372 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-05 09:01:23,372 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18522.2, 'new_value': 25400.0}, {'field': 'offline_amount', 'old_value': 15615.5, 'new_value': 20808.5}, {'field': 'total_amount', 'old_value': 34137.7, 'new_value': 46208.5}, {'field': 'order_count', 'old_value': 842, 'new_value': 1137}]
2025-05-05 09:01:23,372 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-05 09:01:23,778 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-05 09:01:23,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 670000.0, 'new_value': 730000.0}, {'field': 'total_amount', 'old_value': 670000.0, 'new_value': 730000.0}]
2025-05-05 09:01:23,778 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-05 09:01:24,231 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-05 09:01:24,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3000.0}, {'field': 'total_amount', 'old_value': 40800.0, 'new_value': 43800.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 09:01:24,231 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-05 09:01:24,622 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-05 09:01:24,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127708.53, 'new_value': 168844.73}, {'field': 'total_amount', 'old_value': 127708.53, 'new_value': 168844.73}, {'field': 'order_count', 'old_value': 1125, 'new_value': 1522}]
2025-05-05 09:01:24,622 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-05 09:01:25,091 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-05 09:01:25,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60086.0, 'new_value': 80712.0}, {'field': 'total_amount', 'old_value': 60086.0, 'new_value': 80712.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 72}]
2025-05-05 09:01:25,091 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-05 09:01:25,450 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-05 09:01:25,450 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47285.55, 'new_value': 61933.26}, {'field': 'offline_amount', 'old_value': 33287.91, 'new_value': 40965.85}, {'field': 'total_amount', 'old_value': 80573.46, 'new_value': 102899.11}, {'field': 'order_count', 'old_value': 459, 'new_value': 775}]
2025-05-05 09:01:25,450 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-05 09:01:25,934 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-05 09:01:25,934 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11664.0, 'new_value': 17484.0}, {'field': 'total_amount', 'old_value': 11664.0, 'new_value': 17484.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 44}]
2025-05-05 09:01:25,934 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-05 09:01:26,372 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-05 09:01:26,372 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32517.89, 'new_value': 41420.5}, {'field': 'offline_amount', 'old_value': 2820.0, 'new_value': 3662.0}, {'field': 'total_amount', 'old_value': 35337.89, 'new_value': 45082.5}, {'field': 'order_count', 'old_value': 1321, 'new_value': 1667}]
2025-05-05 09:01:26,372 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-05 09:01:26,809 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-05 09:01:26,809 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1648674.0, 'new_value': 1698674.0}, {'field': 'total_amount', 'old_value': 1648674.0, 'new_value': 1698674.0}]
2025-05-05 09:01:26,809 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-05 09:01:27,216 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-05 09:01:27,216 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11316.0, 'new_value': 14520.0}, {'field': 'offline_amount', 'old_value': 137184.0, 'new_value': 180280.0}, {'field': 'total_amount', 'old_value': 148500.0, 'new_value': 194800.0}, {'field': 'order_count', 'old_value': 2952, 'new_value': 3958}]
2025-05-05 09:01:27,216 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-05 09:01:27,622 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-05 09:01:27,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68921.14, 'new_value': 93557.3}, {'field': 'total_amount', 'old_value': 68921.14, 'new_value': 93557.3}, {'field': 'order_count', 'old_value': 238, 'new_value': 326}]
2025-05-05 09:01:27,622 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-05 09:01:28,044 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-05 09:01:28,044 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2682.0, 'new_value': 7946.0}, {'field': 'offline_amount', 'old_value': 27280.0, 'new_value': 38209.0}, {'field': 'total_amount', 'old_value': 29962.0, 'new_value': 46155.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 64}]
2025-05-05 09:01:28,044 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-05 09:01:28,481 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-05 09:01:28,481 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4833.4, 'new_value': 6283.3}, {'field': 'offline_amount', 'old_value': 15271.8, 'new_value': 19941.3}, {'field': 'total_amount', 'old_value': 20105.2, 'new_value': 26224.6}, {'field': 'order_count', 'old_value': 234, 'new_value': 314}]
2025-05-05 09:01:28,481 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-05 09:01:28,887 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-05 09:01:28,887 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11764.0, 'new_value': 15022.0}, {'field': 'total_amount', 'old_value': 11764.0, 'new_value': 15022.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 71}]
2025-05-05 09:01:28,887 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-05 09:01:29,356 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-05 09:01:29,356 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2147.0, 'new_value': 5181.0}, {'field': 'total_amount', 'old_value': 2147.0, 'new_value': 5181.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 18}]
2025-05-05 09:01:29,356 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-05 09:01:29,809 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-05 09:01:29,809 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33365.49, 'new_value': 42390.4}, {'field': 'total_amount', 'old_value': 33365.49, 'new_value': 42390.4}, {'field': 'order_count', 'old_value': 1535, 'new_value': 2035}]
2025-05-05 09:01:29,809 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-05 09:01:30,262 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-05 09:01:30,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27051.5, 'new_value': 34922.1}, {'field': 'total_amount', 'old_value': 27051.5, 'new_value': 34922.1}, {'field': 'order_count', 'old_value': 122, 'new_value': 159}]
2025-05-05 09:01:30,262 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-05 09:01:30,856 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-05 09:01:30,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44274.4, 'new_value': 53406.2}, {'field': 'total_amount', 'old_value': 44274.4, 'new_value': 53406.2}, {'field': 'order_count', 'old_value': 1211, 'new_value': 1457}]
2025-05-05 09:01:30,856 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-05 09:01:31,278 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-05 09:01:31,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2486.0, 'new_value': 3881.0}, {'field': 'total_amount', 'old_value': 4001.0, 'new_value': 5396.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 23}]
2025-05-05 09:01:31,278 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-05 09:01:31,747 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-05 09:01:31,747 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20071.2, 'new_value': 28182.1}, {'field': 'total_amount', 'old_value': 20071.2, 'new_value': 28182.1}, {'field': 'order_count', 'old_value': 93, 'new_value': 124}]
2025-05-05 09:01:31,747 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-05 09:01:32,184 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-05 09:01:32,184 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6694.0, 'new_value': 10372.0}, {'field': 'total_amount', 'old_value': 6694.0, 'new_value': 10372.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 09:01:32,184 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-05 09:01:32,606 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-05 09:01:32,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17834.81, 'new_value': 23686.11}, {'field': 'total_amount', 'old_value': 17834.81, 'new_value': 23686.11}, {'field': 'order_count', 'old_value': 55, 'new_value': 82}]
2025-05-05 09:01:32,606 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-05 09:01:33,028 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-05 09:01:33,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3000.0, 'new_value': 7800.0}, {'field': 'total_amount', 'old_value': 3000.0, 'new_value': 7800.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-05 09:01:33,028 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-05 09:01:33,481 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-05 09:01:33,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149779.94, 'new_value': 196764.06}, {'field': 'total_amount', 'old_value': 149779.94, 'new_value': 196764.06}, {'field': 'order_count', 'old_value': 1000, 'new_value': 1319}]
2025-05-05 09:01:33,481 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-05 09:01:33,919 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-05 09:01:33,919 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1818.0, 'new_value': 2845.0}, {'field': 'offline_amount', 'old_value': 955.0, 'new_value': 1215.0}, {'field': 'total_amount', 'old_value': 2773.0, 'new_value': 4060.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 38}]
2025-05-05 09:01:33,919 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-05 09:01:34,387 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-05 09:01:34,387 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8185.19, 'new_value': 11368.77}, {'field': 'offline_amount', 'old_value': 12525.63, 'new_value': 15900.17}, {'field': 'total_amount', 'old_value': 20710.82, 'new_value': 27268.94}, {'field': 'order_count', 'old_value': 476, 'new_value': 650}]
2025-05-05 09:01:34,387 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-05 09:01:34,825 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-05 09:01:34,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30995.0, 'new_value': 36994.0}, {'field': 'total_amount', 'old_value': 30995.0, 'new_value': 36994.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-05 09:01:34,825 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-05 09:01:35,294 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-05 09:01:35,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171668.0, 'new_value': 215790.0}, {'field': 'total_amount', 'old_value': 171668.0, 'new_value': 215790.0}, {'field': 'order_count', 'old_value': 199, 'new_value': 252}]
2025-05-05 09:01:35,294 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-05 09:01:35,716 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-05 09:01:35,716 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3725.3, 'new_value': 4259.3}, {'field': 'offline_amount', 'old_value': 54394.0, 'new_value': 67640.0}, {'field': 'total_amount', 'old_value': 58119.3, 'new_value': 71899.3}, {'field': 'order_count', 'old_value': 101, 'new_value': 138}]
2025-05-05 09:01:35,716 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-05 09:01:36,184 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-05 09:01:36,184 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6788.2, 'new_value': 9136.2}, {'field': 'offline_amount', 'old_value': 10121.0, 'new_value': 14668.0}, {'field': 'total_amount', 'old_value': 16909.2, 'new_value': 23804.2}, {'field': 'order_count', 'old_value': 243, 'new_value': 326}]
2025-05-05 09:01:36,184 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-05 09:01:36,653 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-05 09:01:36,653 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18790.0, 'new_value': 27338.0}, {'field': 'offline_amount', 'old_value': 15344.0, 'new_value': 21363.0}, {'field': 'total_amount', 'old_value': 34134.0, 'new_value': 48701.0}, {'field': 'order_count', 'old_value': 407, 'new_value': 554}]
2025-05-05 09:01:36,653 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-05 09:01:37,106 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-05 09:01:37,106 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1990.7, 'new_value': 2692.7}, {'field': 'offline_amount', 'old_value': 2691.84, 'new_value': 3305.84}, {'field': 'total_amount', 'old_value': 4682.54, 'new_value': 5998.54}, {'field': 'order_count', 'old_value': 60, 'new_value': 75}]
2025-05-05 09:01:37,106 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-05 09:01:37,606 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-05 09:01:37,606 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2639.2, 'new_value': 2897.22}, {'field': 'offline_amount', 'old_value': 15884.0, 'new_value': 18744.0}, {'field': 'total_amount', 'old_value': 18523.2, 'new_value': 21641.22}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-05-05 09:01:37,606 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-05 09:01:38,028 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-05 09:01:38,028 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7939.97, 'new_value': 10188.83}, {'field': 'total_amount', 'old_value': 7939.97, 'new_value': 10188.83}, {'field': 'order_count', 'old_value': 37, 'new_value': 48}]
2025-05-05 09:01:38,028 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-05 09:01:38,575 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-05 09:01:38,575 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2818.0, 'new_value': 3605.0}, {'field': 'offline_amount', 'old_value': 4276.0, 'new_value': 4853.0}, {'field': 'total_amount', 'old_value': 7094.0, 'new_value': 8458.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 37}]
2025-05-05 09:01:38,575 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-05 09:01:39,012 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-05 09:01:39,012 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 189.0, 'new_value': 232.0}, {'field': 'offline_amount', 'old_value': 10075.0, 'new_value': 12593.0}, {'field': 'total_amount', 'old_value': 10264.0, 'new_value': 12825.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 95}]
2025-05-05 09:01:39,012 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-05 09:01:39,450 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-05 09:01:39,450 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37552.4, 'new_value': 47071.7}, {'field': 'total_amount', 'old_value': 37552.4, 'new_value': 47071.7}, {'field': 'order_count', 'old_value': 211, 'new_value': 251}]
2025-05-05 09:01:39,450 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-05 09:01:39,887 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-05 09:01:39,887 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1010.0, 'new_value': 1398.0}, {'field': 'offline_amount', 'old_value': 2435.0, 'new_value': 3115.0}, {'field': 'total_amount', 'old_value': 3445.0, 'new_value': 4513.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 27}]
2025-05-05 09:01:39,887 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-05 09:01:40,372 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-05 09:01:40,372 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1363.0, 'new_value': 1681.0}, {'field': 'offline_amount', 'old_value': 5826.07, 'new_value': 8105.07}, {'field': 'total_amount', 'old_value': 7189.07, 'new_value': 9786.07}, {'field': 'order_count', 'old_value': 66, 'new_value': 87}]
2025-05-05 09:01:40,372 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-05 09:01:40,762 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-05 09:01:40,762 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24236.25, 'new_value': 33655.88}, {'field': 'total_amount', 'old_value': 24236.25, 'new_value': 33655.88}, {'field': 'order_count', 'old_value': 71, 'new_value': 112}]
2025-05-05 09:01:40,778 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-05 09:01:41,247 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-05 09:01:41,247 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12748.0, 'new_value': 17127.0}, {'field': 'offline_amount', 'old_value': 33569.0, 'new_value': 43802.0}, {'field': 'total_amount', 'old_value': 46317.0, 'new_value': 60929.0}, {'field': 'order_count', 'old_value': 217, 'new_value': 282}]
2025-05-05 09:01:41,247 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-05 09:01:41,669 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-05 09:01:41,669 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42088.0, 'new_value': 53670.0}, {'field': 'total_amount', 'old_value': 42088.0, 'new_value': 53670.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 115}]
2025-05-05 09:01:41,669 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-05 09:01:42,231 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-05 09:01:42,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54519.0, 'new_value': 73169.0}, {'field': 'total_amount', 'old_value': 54519.0, 'new_value': 73169.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 93}]
2025-05-05 09:01:42,231 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-05 09:01:42,747 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-05 09:01:42,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95046.0, 'new_value': 122086.6}, {'field': 'total_amount', 'old_value': 95046.0, 'new_value': 122086.6}, {'field': 'order_count', 'old_value': 2082, 'new_value': 2659}]
2025-05-05 09:01:42,747 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-05 09:01:43,200 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-05 09:01:43,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6511.0, 'new_value': 8194.0}, {'field': 'total_amount', 'old_value': 6511.0, 'new_value': 8194.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 46}]
2025-05-05 09:01:43,200 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-05 09:01:43,715 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-05 09:01:43,715 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28009.0, 'new_value': 36803.0}, {'field': 'offline_amount', 'old_value': 17541.0, 'new_value': 23510.0}, {'field': 'total_amount', 'old_value': 45550.0, 'new_value': 60313.0}, {'field': 'order_count', 'old_value': 145, 'new_value': 192}]
2025-05-05 09:01:43,715 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-05 09:01:44,137 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-05 09:01:44,137 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 247901.68, 'new_value': 280058.08}, {'field': 'total_amount', 'old_value': 247901.68, 'new_value': 280058.08}, {'field': 'order_count', 'old_value': 847, 'new_value': 1023}]
2025-05-05 09:01:44,137 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-05 09:01:44,590 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-05 09:01:44,590 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27313.54, 'new_value': 35521.93}, {'field': 'total_amount', 'old_value': 27313.54, 'new_value': 35521.93}, {'field': 'order_count', 'old_value': 1807, 'new_value': 2377}]
2025-05-05 09:01:44,590 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-05 09:01:45,012 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-05 09:01:45,012 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160345.0, 'new_value': 203642.0}, {'field': 'total_amount', 'old_value': 160345.0, 'new_value': 203642.0}, {'field': 'order_count', 'old_value': 3576, 'new_value': 4558}]
2025-05-05 09:01:45,012 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-05 09:01:45,465 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-05 09:01:45,465 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20563.0, 'new_value': 27605.0}, {'field': 'total_amount', 'old_value': 20563.0, 'new_value': 27605.0}, {'field': 'order_count', 'old_value': 1460, 'new_value': 1916}]
2025-05-05 09:01:45,465 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-05 09:01:46,090 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-05 09:01:46,090 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11200.0, 'new_value': 15224.0}, {'field': 'total_amount', 'old_value': 11200.0, 'new_value': 15224.0}, {'field': 'order_count', 'old_value': 761, 'new_value': 1040}]
2025-05-05 09:01:46,090 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-05 09:01:46,528 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-05 09:01:46,528 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2669.6, 'new_value': 3373.9}, {'field': 'offline_amount', 'old_value': 9640.6, 'new_value': 12730.5}, {'field': 'total_amount', 'old_value': 12310.2, 'new_value': 16104.4}, {'field': 'order_count', 'old_value': 430, 'new_value': 567}]
2025-05-05 09:01:46,528 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-05 09:01:47,012 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-05 09:01:47,012 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7533.92, 'new_value': 12373.69}, {'field': 'total_amount', 'old_value': 7533.92, 'new_value': 12373.69}, {'field': 'order_count', 'old_value': 65, 'new_value': 120}]
2025-05-05 09:01:47,012 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-05 09:01:47,450 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-05 09:01:47,450 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 766863.0, 'new_value': 1023943.0}, {'field': 'total_amount', 'old_value': 766863.0, 'new_value': 1023943.0}, {'field': 'order_count', 'old_value': 14509, 'new_value': 19403}]
2025-05-05 09:01:47,450 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-05 09:01:47,903 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-05 09:01:47,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2651.66, 'new_value': 3922.99}, {'field': 'total_amount', 'old_value': 2651.66, 'new_value': 3922.99}, {'field': 'order_count', 'old_value': 10, 'new_value': 17}]
2025-05-05 09:01:47,903 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-05 09:01:48,434 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-05 09:01:48,434 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113540.0, 'new_value': 145737.0}, {'field': 'total_amount', 'old_value': 113540.0, 'new_value': 145737.0}, {'field': 'order_count', 'old_value': 504, 'new_value': 666}]
2025-05-05 09:01:48,434 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-05 09:01:48,840 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-05 09:01:48,840 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34150.0, 'new_value': 44891.0}, {'field': 'total_amount', 'old_value': 34150.0, 'new_value': 44891.0}, {'field': 'order_count', 'old_value': 775, 'new_value': 1001}]
2025-05-05 09:01:48,840 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-05 09:01:49,294 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-05 09:01:49,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3449.0, 'new_value': 4929.0}, {'field': 'total_amount', 'old_value': 3449.0, 'new_value': 4929.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 5}]
2025-05-05 09:01:49,294 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-05 09:01:49,731 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-05 09:01:49,731 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32935.0, 'new_value': 56373.0}, {'field': 'total_amount', 'old_value': 32935.0, 'new_value': 56373.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 15}]
2025-05-05 09:01:49,731 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-05 09:01:50,137 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-05 09:01:50,137 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22747.7, 'new_value': 29392.3}, {'field': 'total_amount', 'old_value': 22747.7, 'new_value': 29392.3}, {'field': 'order_count', 'old_value': 513, 'new_value': 681}]
2025-05-05 09:01:50,137 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-05 09:01:50,622 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-05 09:01:50,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4358.0, 'new_value': 5210.0}, {'field': 'total_amount', 'old_value': 4358.0, 'new_value': 5210.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-05-05 09:01:50,622 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-05 09:01:51,090 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-05 09:01:51,090 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9849.27, 'new_value': 13471.77}, {'field': 'offline_amount', 'old_value': 87490.6, 'new_value': 119969.5}, {'field': 'total_amount', 'old_value': 97339.87, 'new_value': 133441.27}, {'field': 'order_count', 'old_value': 514, 'new_value': 691}]
2025-05-05 09:01:51,090 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-05 09:01:51,559 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-05 09:01:51,559 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15085.0, 'new_value': 19092.0}, {'field': 'total_amount', 'old_value': 15085.0, 'new_value': 19092.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 90}]
2025-05-05 09:01:51,559 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZF
2025-05-05 09:01:51,981 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZF
2025-05-05 09:01:51,981 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2400.0, 'new_value': 3035.0}, {'field': 'total_amount', 'old_value': 2400.0, 'new_value': 3035.0}, {'field': 'order_count', 'old_value': 323, 'new_value': 412}]
2025-05-05 09:01:51,981 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-05 09:01:52,434 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-05 09:01:52,434 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3579.98, 'new_value': 8398.55}, {'field': 'total_amount', 'old_value': 12318.15, 'new_value': 17136.72}, {'field': 'order_count', 'old_value': 723, 'new_value': 1024}]
2025-05-05 09:01:52,434 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-05 09:01:52,919 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-05 09:01:52,919 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13434.0, 'new_value': 17781.0}, {'field': 'total_amount', 'old_value': 13434.0, 'new_value': 17781.0}, {'field': 'order_count', 'old_value': 460, 'new_value': 616}]
2025-05-05 09:01:52,919 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-05 09:01:53,372 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-05 09:01:53,372 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-05-05 09:01:53,372 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-05 09:01:53,825 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-05 09:01:53,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52999.97, 'new_value': 71441.54}, {'field': 'total_amount', 'old_value': 52999.97, 'new_value': 71441.54}, {'field': 'order_count', 'old_value': 144, 'new_value': 204}]
2025-05-05 09:01:53,825 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-05 09:01:54,231 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-05 09:01:54,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25442.0, 'new_value': 28104.0}, {'field': 'total_amount', 'old_value': 25442.0, 'new_value': 28104.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 104}]
2025-05-05 09:01:54,231 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-05 09:01:54,856 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-05 09:01:54,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5640.0, 'new_value': 10240.0}, {'field': 'total_amount', 'old_value': 5640.0, 'new_value': 10240.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 23}]
2025-05-05 09:01:54,856 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-05 09:01:55,262 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-05 09:01:55,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4298.0, 'new_value': 5330.0}, {'field': 'total_amount', 'old_value': 4298.0, 'new_value': 5330.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 106}]
2025-05-05 09:01:55,262 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-05 09:01:55,747 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-05 09:01:55,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9048.0, 'new_value': 11395.0}, {'field': 'total_amount', 'old_value': 9048.0, 'new_value': 11395.0}, {'field': 'order_count', 'old_value': 820, 'new_value': 1043}]
2025-05-05 09:01:55,747 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-05 09:01:56,169 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-05 09:01:56,169 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4670.0, 'new_value': 5310.0}, {'field': 'offline_amount', 'old_value': 4346.0, 'new_value': 10262.0}, {'field': 'total_amount', 'old_value': 9016.0, 'new_value': 15572.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 29}]
2025-05-05 09:01:56,169 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-05 09:01:56,622 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-05 09:01:56,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10469.35, 'new_value': 12513.95}, {'field': 'total_amount', 'old_value': 10469.35, 'new_value': 12513.95}, {'field': 'order_count', 'old_value': 130, 'new_value': 165}]
2025-05-05 09:01:56,622 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-05 09:01:57,090 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-05 09:01:57,090 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25419.48, 'new_value': 28940.28}, {'field': 'offline_amount', 'old_value': 65412.8, 'new_value': 105038.57}, {'field': 'total_amount', 'old_value': 90832.28, 'new_value': 133978.85}, {'field': 'order_count', 'old_value': 253, 'new_value': 323}]
2025-05-05 09:01:57,090 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-05 09:01:57,528 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-05 09:01:57,528 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11540.0, 'new_value': 14185.0}, {'field': 'total_amount', 'old_value': 11540.0, 'new_value': 14185.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 46}]
2025-05-05 09:01:57,528 - INFO - 日期 2025-05 处理完成 - 更新: 196 条，插入: 0 条，错误: 0 条
2025-05-05 09:01:57,528 - INFO - 数据同步完成！更新: 196 条，插入: 0 条，错误: 0 条
2025-05-05 09:01:57,528 - INFO - =================同步完成====================
2025-05-05 12:00:01,852 - INFO - =================使用默认全量同步=============
2025-05-05 12:00:03,055 - INFO - MySQL查询成功，共获取 3238 条记录
2025-05-05 12:00:03,055 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-05 12:00:03,086 - INFO - 开始处理日期: 2025-01
2025-05-05 12:00:03,086 - INFO - Request Parameters - Page 1:
2025-05-05 12:00:03,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:03,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:04,305 - INFO - Response - Page 1:
2025-05-05 12:00:04,508 - INFO - 第 1 页获取到 100 条记录
2025-05-05 12:00:04,508 - INFO - Request Parameters - Page 2:
2025-05-05 12:00:04,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:04,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:05,039 - INFO - Response - Page 2:
2025-05-05 12:00:05,242 - INFO - 第 2 页获取到 100 条记录
2025-05-05 12:00:05,242 - INFO - Request Parameters - Page 3:
2025-05-05 12:00:05,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:05,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:05,805 - INFO - Response - Page 3:
2025-05-05 12:00:06,008 - INFO - 第 3 页获取到 100 条记录
2025-05-05 12:00:06,008 - INFO - Request Parameters - Page 4:
2025-05-05 12:00:06,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:06,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:06,508 - INFO - Response - Page 4:
2025-05-05 12:00:06,711 - INFO - 第 4 页获取到 100 条记录
2025-05-05 12:00:06,711 - INFO - Request Parameters - Page 5:
2025-05-05 12:00:06,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:06,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:07,180 - INFO - Response - Page 5:
2025-05-05 12:00:07,383 - INFO - 第 5 页获取到 100 条记录
2025-05-05 12:00:07,383 - INFO - Request Parameters - Page 6:
2025-05-05 12:00:07,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:07,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:07,883 - INFO - Response - Page 6:
2025-05-05 12:00:08,086 - INFO - 第 6 页获取到 100 条记录
2025-05-05 12:00:08,086 - INFO - Request Parameters - Page 7:
2025-05-05 12:00:08,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:08,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:08,571 - INFO - Response - Page 7:
2025-05-05 12:00:08,774 - INFO - 第 7 页获取到 82 条记录
2025-05-05 12:00:08,774 - INFO - 查询完成，共获取到 682 条记录
2025-05-05 12:00:08,774 - INFO - 获取到 682 条表单数据
2025-05-05 12:00:08,774 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-05 12:00:08,789 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 12:00:08,789 - INFO - 开始处理日期: 2025-02
2025-05-05 12:00:08,789 - INFO - Request Parameters - Page 1:
2025-05-05 12:00:08,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:08,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:09,274 - INFO - Response - Page 1:
2025-05-05 12:00:09,477 - INFO - 第 1 页获取到 100 条记录
2025-05-05 12:00:09,477 - INFO - Request Parameters - Page 2:
2025-05-05 12:00:09,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:09,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:09,930 - INFO - Response - Page 2:
2025-05-05 12:00:10,133 - INFO - 第 2 页获取到 100 条记录
2025-05-05 12:00:10,133 - INFO - Request Parameters - Page 3:
2025-05-05 12:00:10,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:10,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:10,649 - INFO - Response - Page 3:
2025-05-05 12:00:10,852 - INFO - 第 3 页获取到 100 条记录
2025-05-05 12:00:10,852 - INFO - Request Parameters - Page 4:
2025-05-05 12:00:10,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:10,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:11,383 - INFO - Response - Page 4:
2025-05-05 12:00:11,586 - INFO - 第 4 页获取到 100 条记录
2025-05-05 12:00:11,586 - INFO - Request Parameters - Page 5:
2025-05-05 12:00:11,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:11,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:12,071 - INFO - Response - Page 5:
2025-05-05 12:00:12,274 - INFO - 第 5 页获取到 100 条记录
2025-05-05 12:00:12,274 - INFO - Request Parameters - Page 6:
2025-05-05 12:00:12,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:12,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:12,711 - INFO - Response - Page 6:
2025-05-05 12:00:12,914 - INFO - 第 6 页获取到 100 条记录
2025-05-05 12:00:12,914 - INFO - Request Parameters - Page 7:
2025-05-05 12:00:12,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:12,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:13,446 - INFO - Response - Page 7:
2025-05-05 12:00:13,649 - INFO - 第 7 页获取到 70 条记录
2025-05-05 12:00:13,649 - INFO - 查询完成，共获取到 670 条记录
2025-05-05 12:00:13,649 - INFO - 获取到 670 条表单数据
2025-05-05 12:00:13,649 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-05 12:00:13,664 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 12:00:13,664 - INFO - 开始处理日期: 2025-03
2025-05-05 12:00:13,664 - INFO - Request Parameters - Page 1:
2025-05-05 12:00:13,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:13,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:14,196 - INFO - Response - Page 1:
2025-05-05 12:00:14,399 - INFO - 第 1 页获取到 100 条记录
2025-05-05 12:00:14,399 - INFO - Request Parameters - Page 2:
2025-05-05 12:00:14,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:14,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:14,977 - INFO - Response - Page 2:
2025-05-05 12:00:15,180 - INFO - 第 2 页获取到 100 条记录
2025-05-05 12:00:15,180 - INFO - Request Parameters - Page 3:
2025-05-05 12:00:15,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:15,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:15,758 - INFO - Response - Page 3:
2025-05-05 12:00:15,961 - INFO - 第 3 页获取到 100 条记录
2025-05-05 12:00:15,961 - INFO - Request Parameters - Page 4:
2025-05-05 12:00:15,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:15,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:16,477 - INFO - Response - Page 4:
2025-05-05 12:00:16,680 - INFO - 第 4 页获取到 100 条记录
2025-05-05 12:00:16,680 - INFO - Request Parameters - Page 5:
2025-05-05 12:00:16,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:16,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:17,242 - INFO - Response - Page 5:
2025-05-05 12:00:17,446 - INFO - 第 5 页获取到 100 条记录
2025-05-05 12:00:17,446 - INFO - Request Parameters - Page 6:
2025-05-05 12:00:17,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:17,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:17,899 - INFO - Response - Page 6:
2025-05-05 12:00:18,102 - INFO - 第 6 页获取到 100 条记录
2025-05-05 12:00:18,102 - INFO - Request Parameters - Page 7:
2025-05-05 12:00:18,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:18,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:18,508 - INFO - Response - Page 7:
2025-05-05 12:00:18,711 - INFO - 第 7 页获取到 61 条记录
2025-05-05 12:00:18,711 - INFO - 查询完成，共获取到 661 条记录
2025-05-05 12:00:18,711 - INFO - 获取到 661 条表单数据
2025-05-05 12:00:18,711 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-05 12:00:18,727 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 12:00:18,727 - INFO - 开始处理日期: 2025-04
2025-05-05 12:00:18,727 - INFO - Request Parameters - Page 1:
2025-05-05 12:00:18,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:18,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:19,258 - INFO - Response - Page 1:
2025-05-05 12:00:19,461 - INFO - 第 1 页获取到 100 条记录
2025-05-05 12:00:19,461 - INFO - Request Parameters - Page 2:
2025-05-05 12:00:19,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:19,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:19,914 - INFO - Response - Page 2:
2025-05-05 12:00:20,117 - INFO - 第 2 页获取到 100 条记录
2025-05-05 12:00:20,117 - INFO - Request Parameters - Page 3:
2025-05-05 12:00:20,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:20,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:20,649 - INFO - Response - Page 3:
2025-05-05 12:00:20,852 - INFO - 第 3 页获取到 100 条记录
2025-05-05 12:00:20,852 - INFO - Request Parameters - Page 4:
2025-05-05 12:00:20,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:20,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:21,383 - INFO - Response - Page 4:
2025-05-05 12:00:21,586 - INFO - 第 4 页获取到 100 条记录
2025-05-05 12:00:21,586 - INFO - Request Parameters - Page 5:
2025-05-05 12:00:21,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:21,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:22,055 - INFO - Response - Page 5:
2025-05-05 12:00:22,258 - INFO - 第 5 页获取到 100 条记录
2025-05-05 12:00:22,258 - INFO - Request Parameters - Page 6:
2025-05-05 12:00:22,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:22,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:22,758 - INFO - Response - Page 6:
2025-05-05 12:00:22,961 - INFO - 第 6 页获取到 100 条记录
2025-05-05 12:00:22,961 - INFO - Request Parameters - Page 7:
2025-05-05 12:00:22,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:22,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:23,289 - INFO - Response - Page 7:
2025-05-05 12:00:23,492 - INFO - 第 7 页获取到 27 条记录
2025-05-05 12:00:23,492 - INFO - 查询完成，共获取到 627 条记录
2025-05-05 12:00:23,492 - INFO - 获取到 627 条表单数据
2025-05-05 12:00:23,492 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-05 12:00:23,508 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 12:00:23,508 - INFO - 开始处理日期: 2025-05
2025-05-05 12:00:23,508 - INFO - Request Parameters - Page 1:
2025-05-05 12:00:23,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:23,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:24,055 - INFO - Response - Page 1:
2025-05-05 12:00:24,258 - INFO - 第 1 页获取到 100 条记录
2025-05-05 12:00:24,258 - INFO - Request Parameters - Page 2:
2025-05-05 12:00:24,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:24,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:24,711 - INFO - Response - Page 2:
2025-05-05 12:00:24,914 - INFO - 第 2 页获取到 100 条记录
2025-05-05 12:00:24,914 - INFO - Request Parameters - Page 3:
2025-05-05 12:00:24,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:24,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:25,383 - INFO - Response - Page 3:
2025-05-05 12:00:25,586 - INFO - 第 3 页获取到 100 条记录
2025-05-05 12:00:25,586 - INFO - Request Parameters - Page 4:
2025-05-05 12:00:25,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:25,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:26,039 - INFO - Response - Page 4:
2025-05-05 12:00:26,242 - INFO - 第 4 页获取到 100 条记录
2025-05-05 12:00:26,242 - INFO - Request Parameters - Page 5:
2025-05-05 12:00:26,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:26,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:26,727 - INFO - Response - Page 5:
2025-05-05 12:00:26,930 - INFO - 第 5 页获取到 100 条记录
2025-05-05 12:00:26,930 - INFO - Request Parameters - Page 6:
2025-05-05 12:00:26,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 12:00:26,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 12:00:27,445 - INFO - Response - Page 6:
2025-05-05 12:00:27,649 - INFO - 第 6 页获取到 96 条记录
2025-05-05 12:00:27,649 - INFO - 查询完成，共获取到 596 条记录
2025-05-05 12:00:27,649 - INFO - 获取到 596 条表单数据
2025-05-05 12:00:27,649 - INFO - 当前日期 2025-05 有 598 条MySQL数据需要处理
2025-05-05 12:00:27,649 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-05 12:00:28,133 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-05 12:00:28,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 68372.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 68372.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-05 12:00:28,133 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-05 12:00:28,617 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-05 12:00:28,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9943.9, 'new_value': 13342.67}, {'field': 'total_amount', 'old_value': 9943.9, 'new_value': 13342.67}, {'field': 'order_count', 'old_value': 539, 'new_value': 829}]
2025-05-05 12:00:28,617 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-05 12:00:29,133 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-05 12:00:29,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 159.0}, {'field': 'offline_amount', 'old_value': 152.9, 'new_value': 225.9}, {'field': 'total_amount', 'old_value': 152.9, 'new_value': 384.9}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-05 12:00:29,133 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-05 12:00:29,570 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-05 12:00:29,570 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78759.0, 'new_value': 187506.0}, {'field': 'offline_amount', 'old_value': 30756.0, 'new_value': 63457.0}, {'field': 'total_amount', 'old_value': 109515.0, 'new_value': 250963.0}, {'field': 'order_count', 'old_value': 137, 'new_value': 258}]
2025-05-05 12:00:29,570 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-05 12:00:30,024 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-05 12:00:30,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1599.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1599.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-05 12:00:30,024 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-05 12:00:30,570 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-05 12:00:30,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4500.0, 'new_value': 39000.0}, {'field': 'total_amount', 'old_value': 4500.0, 'new_value': 39000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 6}]
2025-05-05 12:00:30,570 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-05 12:00:31,039 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-05 12:00:31,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42230.0, 'new_value': 58535.89}, {'field': 'total_amount', 'old_value': 42230.0, 'new_value': 58535.89}, {'field': 'order_count', 'old_value': 9, 'new_value': 12}]
2025-05-05 12:00:31,039 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-05 12:00:31,586 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-05 12:00:31,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62225.13, 'new_value': 67560.13}, {'field': 'total_amount', 'old_value': 62225.13, 'new_value': 67560.13}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-05 12:00:31,586 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-05 12:00:32,149 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-05 12:00:32,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 954.91, 'new_value': 1447.31}, {'field': 'total_amount', 'old_value': 954.91, 'new_value': 1447.31}, {'field': 'order_count', 'old_value': 80, 'new_value': 129}]
2025-05-05 12:00:32,149 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-05 12:00:32,617 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-05 12:00:32,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4390.0, 'new_value': 8497.0}, {'field': 'total_amount', 'old_value': 5710.0, 'new_value': 9817.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 35}]
2025-05-05 12:00:32,617 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-05 12:00:33,055 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-05 12:00:33,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8441.34, 'new_value': 16259.76}, {'field': 'total_amount', 'old_value': 8441.34, 'new_value': 16259.76}, {'field': 'order_count', 'old_value': 13, 'new_value': 26}]
2025-05-05 12:00:33,055 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-05 12:00:33,555 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-05 12:00:33,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 188.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 188.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-05 12:00:33,555 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-05 12:00:34,024 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-05 12:00:34,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6226.8, 'new_value': 10334.4}, {'field': 'total_amount', 'old_value': 6226.8, 'new_value': 10334.4}, {'field': 'order_count', 'old_value': 31, 'new_value': 55}]
2025-05-05 12:00:34,024 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-05 12:00:34,492 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-05 12:00:34,492 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5679.06, 'new_value': 11181.88}, {'field': 'offline_amount', 'old_value': 2544.5, 'new_value': 5713.04}, {'field': 'total_amount', 'old_value': 8223.56, 'new_value': 16894.92}, {'field': 'order_count', 'old_value': 233, 'new_value': 485}]
2025-05-05 12:00:34,492 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-05 12:00:34,852 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-05 12:00:34,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5668.0, 'new_value': 9507.0}, {'field': 'total_amount', 'old_value': 5668.0, 'new_value': 9507.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 9}]
2025-05-05 12:00:34,852 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-05 12:00:35,383 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-05 12:00:35,383 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11774.0, 'new_value': 18249.0}, {'field': 'offline_amount', 'old_value': 15998.0, 'new_value': 24956.0}, {'field': 'total_amount', 'old_value': 27772.0, 'new_value': 43205.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 13}]
2025-05-05 12:00:35,383 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-05 12:00:35,852 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-05 12:00:35,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 256.16, 'new_value': 527.2}, {'field': 'offline_amount', 'old_value': 17525.74, 'new_value': 30980.3}, {'field': 'total_amount', 'old_value': 17781.9, 'new_value': 31507.5}, {'field': 'order_count', 'old_value': 90, 'new_value': 142}]
2025-05-05 12:00:35,852 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME
2025-05-05 12:00:36,274 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME
2025-05-05 12:00:36,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6999.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6999.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-05 12:00:36,274 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-05 12:00:36,711 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-05 12:00:36,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4999.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4999.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-05 12:00:36,711 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-05 12:00:37,180 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-05 12:00:37,180 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8870.79, 'new_value': 16975.79}, {'field': 'offline_amount', 'old_value': 6732.52, 'new_value': 12244.41}, {'field': 'total_amount', 'old_value': 15603.31, 'new_value': 29220.2}, {'field': 'order_count', 'old_value': 565, 'new_value': 1086}]
2025-05-05 12:00:37,180 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-05 12:00:37,633 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-05 12:00:37,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6780.0, 'new_value': 19780.0}, {'field': 'total_amount', 'old_value': 6780.0, 'new_value': 19780.0}, {'field': 'order_count', 'old_value': 157, 'new_value': 987}]
2025-05-05 12:00:37,633 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-05 12:00:38,086 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-05 12:00:38,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26413.81, 'new_value': 38539.55}, {'field': 'total_amount', 'old_value': 43602.41, 'new_value': 55728.15}, {'field': 'order_count', 'old_value': 1020, 'new_value': 1305}]
2025-05-05 12:00:38,086 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-05 12:00:38,508 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-05 12:00:38,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 715.0, 'new_value': 898.0}, {'field': 'total_amount', 'old_value': 715.0, 'new_value': 898.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-05-05 12:00:38,508 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-05 12:00:38,914 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-05 12:00:38,914 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5356.7, 'new_value': 11916.58}, {'field': 'offline_amount', 'old_value': 15930.67, 'new_value': 31115.66}, {'field': 'total_amount', 'old_value': 21287.37, 'new_value': 43032.24}, {'field': 'order_count', 'old_value': 824, 'new_value': 1691}]
2025-05-05 12:00:38,914 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-05 12:00:39,367 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-05 12:00:39,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2802.91, 'new_value': 3975.82}, {'field': 'total_amount', 'old_value': 2802.91, 'new_value': 3975.82}, {'field': 'order_count', 'old_value': 90, 'new_value': 135}]
2025-05-05 12:00:39,383 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-05 12:00:39,789 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-05 12:00:39,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44962.0, 'new_value': 74915.0}, {'field': 'total_amount', 'old_value': 45962.0, 'new_value': 75915.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 42}]
2025-05-05 12:00:39,789 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-05 12:00:40,399 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-05 12:00:40,399 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24888.7, 'new_value': 29093.8}, {'field': 'total_amount', 'old_value': 25710.5, 'new_value': 29915.6}, {'field': 'order_count', 'old_value': 149, 'new_value': 181}]
2025-05-05 12:00:40,399 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-05 12:00:40,852 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-05 12:00:40,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2889.64, 'new_value': 3585.64}, {'field': 'offline_amount', 'old_value': 4511.5, 'new_value': 5034.5}, {'field': 'total_amount', 'old_value': 7401.14, 'new_value': 8620.14}, {'field': 'order_count', 'old_value': 379, 'new_value': 444}]
2025-05-05 12:00:40,852 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-05 12:00:41,273 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-05 12:00:41,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4473.0, 'new_value': 9976.0}, {'field': 'total_amount', 'old_value': 4473.0, 'new_value': 9976.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 57}]
2025-05-05 12:00:41,273 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-05 12:00:41,695 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-05 12:00:41,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21967.52, 'new_value': 28971.82}, {'field': 'offline_amount', 'old_value': 58354.78, 'new_value': 77774.75}, {'field': 'total_amount', 'old_value': 80322.3, 'new_value': 106746.57}, {'field': 'order_count', 'old_value': 562, 'new_value': 762}]
2025-05-05 12:00:41,695 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-05 12:00:42,148 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-05 12:00:42,148 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 707.3, 'new_value': 2735.9}, {'field': 'offline_amount', 'old_value': 16190.8, 'new_value': 28531.0}, {'field': 'total_amount', 'old_value': 16898.1, 'new_value': 31266.9}, {'field': 'order_count', 'old_value': 438, 'new_value': 855}]
2025-05-05 12:00:42,148 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-05 12:00:42,633 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-05 12:00:42,633 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5380.1, 'new_value': 7911.82}, {'field': 'offline_amount', 'old_value': 3836.64, 'new_value': 4937.6}, {'field': 'total_amount', 'old_value': 9216.74, 'new_value': 12849.42}, {'field': 'order_count', 'old_value': 508, 'new_value': 727}]
2025-05-05 12:00:42,633 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-05 12:00:43,055 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-05 12:00:43,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 266544.0, 'new_value': 348062.0}, {'field': 'total_amount', 'old_value': 266544.0, 'new_value': 348062.0}, {'field': 'order_count', 'old_value': 1079, 'new_value': 1404}]
2025-05-05 12:00:43,055 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-05 12:00:43,555 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-05 12:00:43,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2159404.0, 'new_value': 3114659.0}, {'field': 'total_amount', 'old_value': 2159404.0, 'new_value': 3114659.0}, {'field': 'order_count', 'old_value': 6226, 'new_value': 8954}]
2025-05-05 12:00:43,555 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMOC
2025-05-05 12:00:43,992 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMOC
2025-05-05 12:00:43,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32174.0, 'new_value': 46006.0}, {'field': 'total_amount', 'old_value': 32174.0, 'new_value': 46006.0}, {'field': 'order_count', 'old_value': 650, 'new_value': 926}]
2025-05-05 12:00:43,992 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-05 12:00:44,445 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-05 12:00:44,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 767744.84, 'new_value': 968805.13}, {'field': 'total_amount', 'old_value': 767744.84, 'new_value': 968805.13}, {'field': 'order_count', 'old_value': 1216, 'new_value': 1596}]
2025-05-05 12:00:44,445 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-05 12:00:44,898 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-05 12:00:44,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13046.22, 'new_value': 18938.7}, {'field': 'total_amount', 'old_value': 13046.22, 'new_value': 18938.7}, {'field': 'order_count', 'old_value': 894, 'new_value': 1270}]
2025-05-05 12:00:44,898 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-05 12:00:45,305 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-05 12:00:45,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23960.0, 'new_value': 34548.0}, {'field': 'total_amount', 'old_value': 23960.0, 'new_value': 34548.0}, {'field': 'order_count', 'old_value': 529, 'new_value': 740}]
2025-05-05 12:00:45,305 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-05 12:00:45,758 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-05 12:00:45,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53670.0, 'new_value': 57670.0}, {'field': 'total_amount', 'old_value': 53670.0, 'new_value': 57670.0}]
2025-05-05 12:00:45,758 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-05 12:00:46,242 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-05 12:00:46,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73169.0, 'new_value': 72505.0}, {'field': 'total_amount', 'old_value': 73169.0, 'new_value': 72505.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 82}]
2025-05-05 12:00:46,242 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-05 12:00:46,664 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-05 12:00:46,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 14867.8}, {'field': 'total_amount', 'old_value': 48879.7, 'new_value': 63747.5}, {'field': 'order_count', 'old_value': 23, 'new_value': 48}]
2025-05-05 12:00:46,664 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-05 12:00:47,133 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-05 12:00:47,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11098.0, 'new_value': 15952.0}, {'field': 'total_amount', 'old_value': 11098.0, 'new_value': 15952.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 9}]
2025-05-05 12:00:47,133 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-05 12:00:47,664 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-05 12:00:47,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16272.83, 'new_value': 20293.51}, {'field': 'total_amount', 'old_value': 16272.83, 'new_value': 20293.51}, {'field': 'order_count', 'old_value': 401, 'new_value': 535}]
2025-05-05 12:00:47,664 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-05 12:00:48,133 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-05 12:00:48,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3440.37, 'new_value': 6270.12}, {'field': 'total_amount', 'old_value': 11750.61, 'new_value': 14580.36}, {'field': 'order_count', 'old_value': 729, 'new_value': 908}]
2025-05-05 12:00:48,133 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-05 12:00:48,602 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-05 12:00:48,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179596.69, 'new_value': 244137.05}, {'field': 'total_amount', 'old_value': 179596.69, 'new_value': 244137.05}, {'field': 'order_count', 'old_value': 548, 'new_value': 732}]
2025-05-05 12:00:48,602 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-05 12:00:49,055 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-05 12:00:49,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127688.77, 'new_value': 159564.92}, {'field': 'total_amount', 'old_value': 127688.77, 'new_value': 159564.92}, {'field': 'order_count', 'old_value': 502, 'new_value': 655}]
2025-05-05 12:00:49,055 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-05 12:00:49,492 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-05 12:00:49,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119759.7, 'new_value': 158543.3}, {'field': 'total_amount', 'old_value': 119759.7, 'new_value': 158543.3}, {'field': 'order_count', 'old_value': 412, 'new_value': 539}]
2025-05-05 12:00:49,492 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-05 12:00:49,961 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-05 12:00:49,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11361.0, 'new_value': 14001.0}, {'field': 'total_amount', 'old_value': 13987.0, 'new_value': 16627.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-05 12:00:49,961 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-05 12:00:50,461 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-05 12:00:50,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11457.0, 'new_value': 14357.0}, {'field': 'total_amount', 'old_value': 11457.0, 'new_value': 14357.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 12:00:50,461 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-05 12:00:50,883 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-05 12:00:50,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21916.0, 'new_value': 32055.0}, {'field': 'total_amount', 'old_value': 21916.0, 'new_value': 32055.0}, {'field': 'order_count', 'old_value': 161, 'new_value': 207}]
2025-05-05 12:00:50,883 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-05 12:00:51,305 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-05 12:00:51,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2729947.0, 'new_value': 3450853.0}, {'field': 'total_amount', 'old_value': 2729947.0, 'new_value': 3450853.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-05 12:00:51,305 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-05 12:00:51,820 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-05 12:00:51,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8564.78, 'new_value': 16192.58}, {'field': 'total_amount', 'old_value': 8564.78, 'new_value': 16192.58}, {'field': 'order_count', 'old_value': 812, 'new_value': 1591}]
2025-05-05 12:00:51,820 - INFO - 开始批量插入 2 条新记录
2025-05-05 12:00:52,055 - INFO - 批量插入响应状态码: 200
2025-05-05 12:00:52,055 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 05 May 2025 04:00:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '106', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3A50949C-B67B-7664-A339-9F7A1951072C', 'x-acs-trace-id': '74971337df6f22f31f6a91db63ad055e', 'etag': '1HEVihe+WMOFA+X6qiUM0lA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-05 12:00:52,055 - INFO - 批量插入响应体: {'result': ['FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH', 'FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI']}
2025-05-05 12:00:52,055 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-05-05 12:00:52,055 - INFO - 成功插入的数据ID: ['FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH', 'FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI']
2025-05-05 12:00:55,070 - INFO - 批量插入完成，共 2 条记录
2025-05-05 12:00:55,070 - INFO - 日期 2025-05 处理完成 - 更新: 52 条，插入: 2 条，错误: 0 条
2025-05-05 12:00:55,070 - INFO - 数据同步完成！更新: 52 条，插入: 2 条，错误: 0 条
2025-05-05 12:00:55,070 - INFO - =================同步完成====================
2025-05-05 15:00:01,878 - INFO - =================使用默认全量同步=============
2025-05-05 15:00:03,081 - INFO - MySQL查询成功，共获取 3238 条记录
2025-05-05 15:00:03,081 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-05 15:00:03,112 - INFO - 开始处理日期: 2025-01
2025-05-05 15:00:03,112 - INFO - Request Parameters - Page 1:
2025-05-05 15:00:03,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:03,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:04,019 - INFO - Response - Page 1:
2025-05-05 15:00:04,222 - INFO - 第 1 页获取到 100 条记录
2025-05-05 15:00:04,222 - INFO - Request Parameters - Page 2:
2025-05-05 15:00:04,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:04,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:05,019 - INFO - Response - Page 2:
2025-05-05 15:00:05,237 - INFO - 第 2 页获取到 100 条记录
2025-05-05 15:00:05,237 - INFO - Request Parameters - Page 3:
2025-05-05 15:00:05,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:05,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:05,722 - INFO - Response - Page 3:
2025-05-05 15:00:05,925 - INFO - 第 3 页获取到 100 条记录
2025-05-05 15:00:05,925 - INFO - Request Parameters - Page 4:
2025-05-05 15:00:05,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:05,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:06,487 - INFO - Response - Page 4:
2025-05-05 15:00:06,690 - INFO - 第 4 页获取到 100 条记录
2025-05-05 15:00:06,690 - INFO - Request Parameters - Page 5:
2025-05-05 15:00:06,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:06,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:07,175 - INFO - Response - Page 5:
2025-05-05 15:00:07,378 - INFO - 第 5 页获取到 100 条记录
2025-05-05 15:00:07,378 - INFO - Request Parameters - Page 6:
2025-05-05 15:00:07,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:07,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:07,862 - INFO - Response - Page 6:
2025-05-05 15:00:08,065 - INFO - 第 6 页获取到 100 条记录
2025-05-05 15:00:08,065 - INFO - Request Parameters - Page 7:
2025-05-05 15:00:08,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:08,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:08,534 - INFO - Response - Page 7:
2025-05-05 15:00:08,737 - INFO - 第 7 页获取到 82 条记录
2025-05-05 15:00:08,737 - INFO - 查询完成，共获取到 682 条记录
2025-05-05 15:00:08,737 - INFO - 获取到 682 条表单数据
2025-05-05 15:00:08,737 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-05 15:00:08,753 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 15:00:08,753 - INFO - 开始处理日期: 2025-02
2025-05-05 15:00:08,753 - INFO - Request Parameters - Page 1:
2025-05-05 15:00:08,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:08,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:09,269 - INFO - Response - Page 1:
2025-05-05 15:00:09,472 - INFO - 第 1 页获取到 100 条记录
2025-05-05 15:00:09,472 - INFO - Request Parameters - Page 2:
2025-05-05 15:00:09,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:09,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:09,940 - INFO - Response - Page 2:
2025-05-05 15:00:10,143 - INFO - 第 2 页获取到 100 条记录
2025-05-05 15:00:10,143 - INFO - Request Parameters - Page 3:
2025-05-05 15:00:10,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:10,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:10,628 - INFO - Response - Page 3:
2025-05-05 15:00:10,831 - INFO - 第 3 页获取到 100 条记录
2025-05-05 15:00:10,831 - INFO - Request Parameters - Page 4:
2025-05-05 15:00:10,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:10,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:11,315 - INFO - Response - Page 4:
2025-05-05 15:00:11,518 - INFO - 第 4 页获取到 100 条记录
2025-05-05 15:00:11,518 - INFO - Request Parameters - Page 5:
2025-05-05 15:00:11,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:11,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:11,972 - INFO - Response - Page 5:
2025-05-05 15:00:12,175 - INFO - 第 5 页获取到 100 条记录
2025-05-05 15:00:12,175 - INFO - Request Parameters - Page 6:
2025-05-05 15:00:12,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:12,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:12,722 - INFO - Response - Page 6:
2025-05-05 15:00:12,925 - INFO - 第 6 页获取到 100 条记录
2025-05-05 15:00:12,925 - INFO - Request Parameters - Page 7:
2025-05-05 15:00:12,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:12,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:13,425 - INFO - Response - Page 7:
2025-05-05 15:00:13,628 - INFO - 第 7 页获取到 70 条记录
2025-05-05 15:00:13,628 - INFO - 查询完成，共获取到 670 条记录
2025-05-05 15:00:13,628 - INFO - 获取到 670 条表单数据
2025-05-05 15:00:13,628 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-05 15:00:13,643 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 15:00:13,643 - INFO - 开始处理日期: 2025-03
2025-05-05 15:00:13,643 - INFO - Request Parameters - Page 1:
2025-05-05 15:00:13,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:13,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:14,159 - INFO - Response - Page 1:
2025-05-05 15:00:14,362 - INFO - 第 1 页获取到 100 条记录
2025-05-05 15:00:14,362 - INFO - Request Parameters - Page 2:
2025-05-05 15:00:14,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:14,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:14,847 - INFO - Response - Page 2:
2025-05-05 15:00:15,050 - INFO - 第 2 页获取到 100 条记录
2025-05-05 15:00:15,050 - INFO - Request Parameters - Page 3:
2025-05-05 15:00:15,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:15,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:15,550 - INFO - Response - Page 3:
2025-05-05 15:00:15,753 - INFO - 第 3 页获取到 100 条记录
2025-05-05 15:00:15,753 - INFO - Request Parameters - Page 4:
2025-05-05 15:00:15,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:15,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:16,206 - INFO - Response - Page 4:
2025-05-05 15:00:16,409 - INFO - 第 4 页获取到 100 条记录
2025-05-05 15:00:16,409 - INFO - Request Parameters - Page 5:
2025-05-05 15:00:16,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:16,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:16,847 - INFO - Response - Page 5:
2025-05-05 15:00:17,050 - INFO - 第 5 页获取到 100 条记录
2025-05-05 15:00:17,050 - INFO - Request Parameters - Page 6:
2025-05-05 15:00:17,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:17,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:17,518 - INFO - Response - Page 6:
2025-05-05 15:00:17,722 - INFO - 第 6 页获取到 100 条记录
2025-05-05 15:00:17,722 - INFO - Request Parameters - Page 7:
2025-05-05 15:00:17,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:17,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:18,128 - INFO - Response - Page 7:
2025-05-05 15:00:18,331 - INFO - 第 7 页获取到 61 条记录
2025-05-05 15:00:18,331 - INFO - 查询完成，共获取到 661 条记录
2025-05-05 15:00:18,331 - INFO - 获取到 661 条表单数据
2025-05-05 15:00:18,331 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-05 15:00:18,347 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 15:00:18,347 - INFO - 开始处理日期: 2025-04
2025-05-05 15:00:18,347 - INFO - Request Parameters - Page 1:
2025-05-05 15:00:18,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:18,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:18,909 - INFO - Response - Page 1:
2025-05-05 15:00:19,112 - INFO - 第 1 页获取到 100 条记录
2025-05-05 15:00:19,112 - INFO - Request Parameters - Page 2:
2025-05-05 15:00:19,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:19,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:19,550 - INFO - Response - Page 2:
2025-05-05 15:00:19,753 - INFO - 第 2 页获取到 100 条记录
2025-05-05 15:00:19,753 - INFO - Request Parameters - Page 3:
2025-05-05 15:00:19,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:19,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:20,222 - INFO - Response - Page 3:
2025-05-05 15:00:20,425 - INFO - 第 3 页获取到 100 条记录
2025-05-05 15:00:20,425 - INFO - Request Parameters - Page 4:
2025-05-05 15:00:20,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:20,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:21,097 - INFO - Response - Page 4:
2025-05-05 15:00:21,300 - INFO - 第 4 页获取到 100 条记录
2025-05-05 15:00:21,300 - INFO - Request Parameters - Page 5:
2025-05-05 15:00:21,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:21,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:21,815 - INFO - Response - Page 5:
2025-05-05 15:00:22,018 - INFO - 第 5 页获取到 100 条记录
2025-05-05 15:00:22,018 - INFO - Request Parameters - Page 6:
2025-05-05 15:00:22,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:22,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:22,487 - INFO - Response - Page 6:
2025-05-05 15:00:22,690 - INFO - 第 6 页获取到 100 条记录
2025-05-05 15:00:22,690 - INFO - Request Parameters - Page 7:
2025-05-05 15:00:22,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:22,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:23,034 - INFO - Response - Page 7:
2025-05-05 15:00:23,237 - INFO - 第 7 页获取到 27 条记录
2025-05-05 15:00:23,237 - INFO - 查询完成，共获取到 627 条记录
2025-05-05 15:00:23,237 - INFO - 获取到 627 条表单数据
2025-05-05 15:00:23,237 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-05 15:00:23,253 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 15:00:23,253 - INFO - 开始处理日期: 2025-05
2025-05-05 15:00:23,253 - INFO - Request Parameters - Page 1:
2025-05-05 15:00:23,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:23,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:23,706 - INFO - Response - Page 1:
2025-05-05 15:00:23,909 - INFO - 第 1 页获取到 100 条记录
2025-05-05 15:00:23,909 - INFO - Request Parameters - Page 2:
2025-05-05 15:00:23,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:23,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:24,393 - INFO - Response - Page 2:
2025-05-05 15:00:24,597 - INFO - 第 2 页获取到 100 条记录
2025-05-05 15:00:24,597 - INFO - Request Parameters - Page 3:
2025-05-05 15:00:24,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:24,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:25,081 - INFO - Response - Page 3:
2025-05-05 15:00:25,284 - INFO - 第 3 页获取到 100 条记录
2025-05-05 15:00:25,284 - INFO - Request Parameters - Page 4:
2025-05-05 15:00:25,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:25,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:25,753 - INFO - Response - Page 4:
2025-05-05 15:00:25,956 - INFO - 第 4 页获取到 100 条记录
2025-05-05 15:00:25,956 - INFO - Request Parameters - Page 5:
2025-05-05 15:00:25,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:25,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:26,425 - INFO - Response - Page 5:
2025-05-05 15:00:26,628 - INFO - 第 5 页获取到 100 条记录
2025-05-05 15:00:26,628 - INFO - Request Parameters - Page 6:
2025-05-05 15:00:26,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 15:00:26,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 15:00:27,065 - INFO - Response - Page 6:
2025-05-05 15:00:27,268 - INFO - 第 6 页获取到 98 条记录
2025-05-05 15:00:27,268 - INFO - 查询完成，共获取到 598 条记录
2025-05-05 15:00:27,268 - INFO - 获取到 598 条表单数据
2025-05-05 15:00:27,268 - INFO - 当前日期 2025-05 有 598 条MySQL数据需要处理
2025-05-05 15:00:27,268 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-05 15:00:27,815 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-05 15:00:27,815 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117012.18, 'new_value': 151403.76}, {'field': 'total_amount', 'old_value': 117012.18, 'new_value': 151403.76}, {'field': 'order_count', 'old_value': 385, 'new_value': 495}]
2025-05-05 15:00:27,815 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-05 15:00:28,190 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-05 15:00:28,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67250.0, 'new_value': 73725.0}, {'field': 'total_amount', 'old_value': 67250.0, 'new_value': 73725.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 73}]
2025-05-05 15:00:28,206 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-05 15:00:28,706 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-05 15:00:28,706 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37715.92, 'new_value': 47341.94}, {'field': 'offline_amount', 'old_value': 46238.35, 'new_value': 57428.08}, {'field': 'total_amount', 'old_value': 83954.27, 'new_value': 104770.02}, {'field': 'order_count', 'old_value': 2849, 'new_value': 3742}]
2025-05-05 15:00:28,706 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-05 15:00:29,237 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-05 15:00:29,237 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4269.45, 'new_value': 6804.07}, {'field': 'offline_amount', 'old_value': 50852.62, 'new_value': 84125.16}, {'field': 'total_amount', 'old_value': 53247.74, 'new_value': 89054.9}, {'field': 'order_count', 'old_value': 235, 'new_value': 370}]
2025-05-05 15:00:29,237 - INFO - 日期 2025-05 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-05-05 15:00:29,237 - INFO - 数据同步完成！更新: 4 条，插入: 0 条，错误: 0 条
2025-05-05 15:00:29,253 - INFO - =================同步完成====================
2025-05-05 18:00:01,951 - INFO - =================使用默认全量同步=============
2025-05-05 18:00:03,139 - INFO - MySQL查询成功，共获取 3238 条记录
2025-05-05 18:00:03,154 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-05 18:00:03,170 - INFO - 开始处理日期: 2025-01
2025-05-05 18:00:03,170 - INFO - Request Parameters - Page 1:
2025-05-05 18:00:03,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:03,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:04,092 - INFO - Response - Page 1:
2025-05-05 18:00:04,295 - INFO - 第 1 页获取到 100 条记录
2025-05-05 18:00:04,295 - INFO - Request Parameters - Page 2:
2025-05-05 18:00:04,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:04,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:04,842 - INFO - Response - Page 2:
2025-05-05 18:00:05,045 - INFO - 第 2 页获取到 100 条记录
2025-05-05 18:00:05,045 - INFO - Request Parameters - Page 3:
2025-05-05 18:00:05,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:05,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:05,592 - INFO - Response - Page 3:
2025-05-05 18:00:05,795 - INFO - 第 3 页获取到 100 条记录
2025-05-05 18:00:05,795 - INFO - Request Parameters - Page 4:
2025-05-05 18:00:05,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:05,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:06,451 - INFO - Response - Page 4:
2025-05-05 18:00:06,654 - INFO - 第 4 页获取到 100 条记录
2025-05-05 18:00:06,654 - INFO - Request Parameters - Page 5:
2025-05-05 18:00:06,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:06,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:07,170 - INFO - Response - Page 5:
2025-05-05 18:00:07,373 - INFO - 第 5 页获取到 100 条记录
2025-05-05 18:00:07,373 - INFO - Request Parameters - Page 6:
2025-05-05 18:00:07,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:07,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:07,857 - INFO - Response - Page 6:
2025-05-05 18:00:08,061 - INFO - 第 6 页获取到 100 条记录
2025-05-05 18:00:08,061 - INFO - Request Parameters - Page 7:
2025-05-05 18:00:08,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:08,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:08,514 - INFO - Response - Page 7:
2025-05-05 18:00:08,717 - INFO - 第 7 页获取到 82 条记录
2025-05-05 18:00:08,717 - INFO - 查询完成，共获取到 682 条记录
2025-05-05 18:00:08,717 - INFO - 获取到 682 条表单数据
2025-05-05 18:00:08,717 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-05 18:00:08,732 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 18:00:08,732 - INFO - 开始处理日期: 2025-02
2025-05-05 18:00:08,732 - INFO - Request Parameters - Page 1:
2025-05-05 18:00:08,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:08,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:09,201 - INFO - Response - Page 1:
2025-05-05 18:00:09,404 - INFO - 第 1 页获取到 100 条记录
2025-05-05 18:00:09,404 - INFO - Request Parameters - Page 2:
2025-05-05 18:00:09,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:09,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:09,842 - INFO - Response - Page 2:
2025-05-05 18:00:10,045 - INFO - 第 2 页获取到 100 条记录
2025-05-05 18:00:10,045 - INFO - Request Parameters - Page 3:
2025-05-05 18:00:10,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:10,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:10,529 - INFO - Response - Page 3:
2025-05-05 18:00:10,732 - INFO - 第 3 页获取到 100 条记录
2025-05-05 18:00:10,732 - INFO - Request Parameters - Page 4:
2025-05-05 18:00:10,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:10,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:11,279 - INFO - Response - Page 4:
2025-05-05 18:00:11,482 - INFO - 第 4 页获取到 100 条记录
2025-05-05 18:00:11,482 - INFO - Request Parameters - Page 5:
2025-05-05 18:00:11,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:11,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:12,029 - INFO - Response - Page 5:
2025-05-05 18:00:12,232 - INFO - 第 5 页获取到 100 条记录
2025-05-05 18:00:12,232 - INFO - Request Parameters - Page 6:
2025-05-05 18:00:12,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:12,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:12,779 - INFO - Response - Page 6:
2025-05-05 18:00:12,982 - INFO - 第 6 页获取到 100 条记录
2025-05-05 18:00:12,982 - INFO - Request Parameters - Page 7:
2025-05-05 18:00:12,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:12,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:13,389 - INFO - Response - Page 7:
2025-05-05 18:00:13,592 - INFO - 第 7 页获取到 70 条记录
2025-05-05 18:00:13,592 - INFO - 查询完成，共获取到 670 条记录
2025-05-05 18:00:13,592 - INFO - 获取到 670 条表单数据
2025-05-05 18:00:13,592 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-05 18:00:13,607 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 18:00:13,607 - INFO - 开始处理日期: 2025-03
2025-05-05 18:00:13,607 - INFO - Request Parameters - Page 1:
2025-05-05 18:00:13,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:13,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:14,139 - INFO - Response - Page 1:
2025-05-05 18:00:14,342 - INFO - 第 1 页获取到 100 条记录
2025-05-05 18:00:14,342 - INFO - Request Parameters - Page 2:
2025-05-05 18:00:14,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:14,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:14,842 - INFO - Response - Page 2:
2025-05-05 18:00:15,045 - INFO - 第 2 页获取到 100 条记录
2025-05-05 18:00:15,045 - INFO - Request Parameters - Page 3:
2025-05-05 18:00:15,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:15,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:15,498 - INFO - Response - Page 3:
2025-05-05 18:00:15,701 - INFO - 第 3 页获取到 100 条记录
2025-05-05 18:00:15,701 - INFO - Request Parameters - Page 4:
2025-05-05 18:00:15,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:15,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:16,264 - INFO - Response - Page 4:
2025-05-05 18:00:16,467 - INFO - 第 4 页获取到 100 条记录
2025-05-05 18:00:16,467 - INFO - Request Parameters - Page 5:
2025-05-05 18:00:16,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:16,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:16,951 - INFO - Response - Page 5:
2025-05-05 18:00:17,154 - INFO - 第 5 页获取到 100 条记录
2025-05-05 18:00:17,154 - INFO - Request Parameters - Page 6:
2025-05-05 18:00:17,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:17,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:17,654 - INFO - Response - Page 6:
2025-05-05 18:00:17,857 - INFO - 第 6 页获取到 100 条记录
2025-05-05 18:00:17,857 - INFO - Request Parameters - Page 7:
2025-05-05 18:00:17,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:17,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:18,310 - INFO - Response - Page 7:
2025-05-05 18:00:18,514 - INFO - 第 7 页获取到 61 条记录
2025-05-05 18:00:18,514 - INFO - 查询完成，共获取到 661 条记录
2025-05-05 18:00:18,514 - INFO - 获取到 661 条表单数据
2025-05-05 18:00:18,514 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-05 18:00:18,529 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 18:00:18,529 - INFO - 开始处理日期: 2025-04
2025-05-05 18:00:18,529 - INFO - Request Parameters - Page 1:
2025-05-05 18:00:18,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:18,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:19,123 - INFO - Response - Page 1:
2025-05-05 18:00:19,326 - INFO - 第 1 页获取到 100 条记录
2025-05-05 18:00:19,326 - INFO - Request Parameters - Page 2:
2025-05-05 18:00:19,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:19,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:19,779 - INFO - Response - Page 2:
2025-05-05 18:00:19,982 - INFO - 第 2 页获取到 100 条记录
2025-05-05 18:00:19,982 - INFO - Request Parameters - Page 3:
2025-05-05 18:00:19,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:19,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:20,420 - INFO - Response - Page 3:
2025-05-05 18:00:20,623 - INFO - 第 3 页获取到 100 条记录
2025-05-05 18:00:20,623 - INFO - Request Parameters - Page 4:
2025-05-05 18:00:20,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:20,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:21,123 - INFO - Response - Page 4:
2025-05-05 18:00:21,326 - INFO - 第 4 页获取到 100 条记录
2025-05-05 18:00:21,326 - INFO - Request Parameters - Page 5:
2025-05-05 18:00:21,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:21,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:21,810 - INFO - Response - Page 5:
2025-05-05 18:00:22,014 - INFO - 第 5 页获取到 100 条记录
2025-05-05 18:00:22,014 - INFO - Request Parameters - Page 6:
2025-05-05 18:00:22,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:22,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:22,529 - INFO - Response - Page 6:
2025-05-05 18:00:22,732 - INFO - 第 6 页获取到 100 条记录
2025-05-05 18:00:22,732 - INFO - Request Parameters - Page 7:
2025-05-05 18:00:22,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:22,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:23,092 - INFO - Response - Page 7:
2025-05-05 18:00:23,295 - INFO - 第 7 页获取到 27 条记录
2025-05-05 18:00:23,295 - INFO - 查询完成，共获取到 627 条记录
2025-05-05 18:00:23,295 - INFO - 获取到 627 条表单数据
2025-05-05 18:00:23,295 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-05 18:00:23,310 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT7
2025-05-05 18:00:23,779 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT7
2025-05-05 18:00:23,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40000.0, 'new_value': 45000.0}, {'field': 'total_amount', 'old_value': 40000.0, 'new_value': 45000.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-05 18:00:23,779 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-05 18:00:23,779 - INFO - 开始处理日期: 2025-05
2025-05-05 18:00:23,779 - INFO - Request Parameters - Page 1:
2025-05-05 18:00:23,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:23,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:24,264 - INFO - Response - Page 1:
2025-05-05 18:00:24,467 - INFO - 第 1 页获取到 100 条记录
2025-05-05 18:00:24,467 - INFO - Request Parameters - Page 2:
2025-05-05 18:00:24,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:24,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:24,889 - INFO - Response - Page 2:
2025-05-05 18:00:25,092 - INFO - 第 2 页获取到 100 条记录
2025-05-05 18:00:25,092 - INFO - Request Parameters - Page 3:
2025-05-05 18:00:25,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:25,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:25,545 - INFO - Response - Page 3:
2025-05-05 18:00:25,748 - INFO - 第 3 页获取到 100 条记录
2025-05-05 18:00:25,748 - INFO - Request Parameters - Page 4:
2025-05-05 18:00:25,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:25,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:26,217 - INFO - Response - Page 4:
2025-05-05 18:00:26,420 - INFO - 第 4 页获取到 100 条记录
2025-05-05 18:00:26,420 - INFO - Request Parameters - Page 5:
2025-05-05 18:00:26,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:26,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:26,889 - INFO - Response - Page 5:
2025-05-05 18:00:27,092 - INFO - 第 5 页获取到 100 条记录
2025-05-05 18:00:27,092 - INFO - Request Parameters - Page 6:
2025-05-05 18:00:27,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 18:00:27,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 18:00:27,545 - INFO - Response - Page 6:
2025-05-05 18:00:27,748 - INFO - 第 6 页获取到 98 条记录
2025-05-05 18:00:27,748 - INFO - 查询完成，共获取到 598 条记录
2025-05-05 18:00:27,748 - INFO - 获取到 598 条表单数据
2025-05-05 18:00:27,748 - INFO - 当前日期 2025-05 有 598 条MySQL数据需要处理
2025-05-05 18:00:27,748 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-05 18:00:28,263 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-05 18:00:28,263 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18029.41, 'new_value': 23609.71}, {'field': 'total_amount', 'old_value': 18029.41, 'new_value': 23609.71}, {'field': 'order_count', 'old_value': 568, 'new_value': 740}]
2025-05-05 18:00:28,263 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-05 18:00:28,685 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-05 18:00:28,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1183074.0, 'new_value': 1165844.0}, {'field': 'total_amount', 'old_value': 1183074.0, 'new_value': 1165844.0}, {'field': 'order_count', 'old_value': 18226, 'new_value': 17441}]
2025-05-05 18:00:28,685 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-05 18:00:29,217 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-05 18:00:29,217 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9393.0, 'new_value': 11341.0}, {'field': 'total_amount', 'old_value': 9393.0, 'new_value': 11341.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-05-05 18:00:29,217 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-05 18:00:29,795 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-05 18:00:29,795 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2220.91, 'new_value': 2918.81}, {'field': 'offline_amount', 'old_value': 41613.96, 'new_value': 52853.16}, {'field': 'total_amount', 'old_value': 43834.87, 'new_value': 55771.97}, {'field': 'order_count', 'old_value': 463, 'new_value': 604}]
2025-05-05 18:00:29,795 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-05 18:00:30,232 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-05 18:00:30,232 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69196.0, 'new_value': 65109.0}, {'field': 'total_amount', 'old_value': 69196.0, 'new_value': 65109.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 14}]
2025-05-05 18:00:30,232 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-05 18:00:30,779 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-05 18:00:30,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19645.9, 'new_value': 21129.8}, {'field': 'total_amount', 'old_value': 19645.9, 'new_value': 21129.8}, {'field': 'order_count', 'old_value': 906, 'new_value': 977}]
2025-05-05 18:00:30,779 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-05 18:00:31,295 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-05 18:00:31,295 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17481.5, 'new_value': 17371.89}, {'field': 'total_amount', 'old_value': 17481.5, 'new_value': 17371.89}, {'field': 'order_count', 'old_value': 679, 'new_value': 677}]
2025-05-05 18:00:31,295 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-05 18:00:31,763 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-05 18:00:31,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12525.0, 'new_value': 29338.42}, {'field': 'total_amount', 'old_value': 12525.0, 'new_value': 29338.42}, {'field': 'order_count', 'old_value': 50, 'new_value': 184}]
2025-05-05 18:00:31,763 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMAE
2025-05-05 18:00:32,185 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMAE
2025-05-05 18:00:32,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5000.0, 'new_value': 10000.0}, {'field': 'total_amount', 'old_value': 5000.0, 'new_value': 10000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-05 18:00:32,185 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-05 18:00:32,638 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-05 18:00:32,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33338.0, 'new_value': 31058.0}, {'field': 'total_amount', 'old_value': 33338.0, 'new_value': 31058.0}, {'field': 'order_count', 'old_value': 164, 'new_value': 143}]
2025-05-05 18:00:32,638 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-05 18:00:33,123 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-05 18:00:33,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 835.0, 'new_value': 3524.0}, {'field': 'total_amount', 'old_value': 835.0, 'new_value': 3524.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-05 18:00:33,123 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-05 18:00:33,592 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-05 18:00:33,592 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31392.0, 'new_value': 34192.0}, {'field': 'total_amount', 'old_value': 31392.0, 'new_value': 34192.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-05 18:00:33,592 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-05 18:00:34,029 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-05 18:00:34,029 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 5318.0}, {'field': 'offline_amount', 'old_value': 2900.0, 'new_value': 2902.0}, {'field': 'total_amount', 'old_value': 2900.0, 'new_value': 8220.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-05 18:00:34,029 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-05 18:00:34,498 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-05 18:00:34,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21573.0, 'new_value': 25850.5}, {'field': 'total_amount', 'old_value': 21573.0, 'new_value': 25850.5}, {'field': 'order_count', 'old_value': 8, 'new_value': 5}]
2025-05-05 18:00:34,498 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-05 18:00:34,873 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-05 18:00:34,873 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6722.0, 'new_value': 8610.99}, {'field': 'total_amount', 'old_value': 6722.0, 'new_value': 8610.99}, {'field': 'order_count', 'old_value': 198, 'new_value': 539}]
2025-05-05 18:00:34,873 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-05 18:00:35,310 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-05 18:00:35,326 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12522.0, 'new_value': 11449.9}, {'field': 'total_amount', 'old_value': 12522.0, 'new_value': 11449.9}, {'field': 'order_count', 'old_value': 98, 'new_value': 102}]
2025-05-05 18:00:35,326 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-05 18:00:35,763 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-05 18:00:35,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38525.0, 'new_value': 37586.0}, {'field': 'total_amount', 'old_value': 38525.0, 'new_value': 37586.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 63}]
2025-05-05 18:00:35,763 - INFO - 日期 2025-05 处理完成 - 更新: 17 条，插入: 0 条，错误: 0 条
2025-05-05 18:00:35,763 - INFO - 数据同步完成！更新: 18 条，插入: 0 条，错误: 0 条
2025-05-05 18:00:35,779 - INFO - =================同步完成====================
2025-05-05 21:00:01,991 - INFO - =================使用默认全量同步=============
2025-05-05 21:00:03,194 - INFO - MySQL查询成功，共获取 3238 条记录
2025-05-05 21:00:03,194 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-05 21:00:03,225 - INFO - 开始处理日期: 2025-01
2025-05-05 21:00:03,241 - INFO - Request Parameters - Page 1:
2025-05-05 21:00:03,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:03,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:04,256 - INFO - Response - Page 1:
2025-05-05 21:00:04,459 - INFO - 第 1 页获取到 100 条记录
2025-05-05 21:00:04,459 - INFO - Request Parameters - Page 2:
2025-05-05 21:00:04,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:04,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:05,178 - INFO - Response - Page 2:
2025-05-05 21:00:05,381 - INFO - 第 2 页获取到 100 条记录
2025-05-05 21:00:05,381 - INFO - Request Parameters - Page 3:
2025-05-05 21:00:05,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:05,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:05,881 - INFO - Response - Page 3:
2025-05-05 21:00:06,084 - INFO - 第 3 页获取到 100 条记录
2025-05-05 21:00:06,084 - INFO - Request Parameters - Page 4:
2025-05-05 21:00:06,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:06,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:06,584 - INFO - Response - Page 4:
2025-05-05 21:00:06,787 - INFO - 第 4 页获取到 100 条记录
2025-05-05 21:00:06,787 - INFO - Request Parameters - Page 5:
2025-05-05 21:00:06,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:06,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:07,272 - INFO - Response - Page 5:
2025-05-05 21:00:07,475 - INFO - 第 5 页获取到 100 条记录
2025-05-05 21:00:07,475 - INFO - Request Parameters - Page 6:
2025-05-05 21:00:07,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:07,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:07,897 - INFO - Response - Page 6:
2025-05-05 21:00:08,100 - INFO - 第 6 页获取到 100 条记录
2025-05-05 21:00:08,100 - INFO - Request Parameters - Page 7:
2025-05-05 21:00:08,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:08,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:08,600 - INFO - Response - Page 7:
2025-05-05 21:00:08,803 - INFO - 第 7 页获取到 82 条记录
2025-05-05 21:00:08,803 - INFO - 查询完成，共获取到 682 条记录
2025-05-05 21:00:08,803 - INFO - 获取到 682 条表单数据
2025-05-05 21:00:08,803 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-05 21:00:08,819 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 21:00:08,819 - INFO - 开始处理日期: 2025-02
2025-05-05 21:00:08,819 - INFO - Request Parameters - Page 1:
2025-05-05 21:00:08,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:08,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:09,397 - INFO - Response - Page 1:
2025-05-05 21:00:09,600 - INFO - 第 1 页获取到 100 条记录
2025-05-05 21:00:09,600 - INFO - Request Parameters - Page 2:
2025-05-05 21:00:09,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:09,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:10,131 - INFO - Response - Page 2:
2025-05-05 21:00:10,334 - INFO - 第 2 页获取到 100 条记录
2025-05-05 21:00:10,334 - INFO - Request Parameters - Page 3:
2025-05-05 21:00:10,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:10,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:10,819 - INFO - Response - Page 3:
2025-05-05 21:00:11,022 - INFO - 第 3 页获取到 100 条记录
2025-05-05 21:00:11,022 - INFO - Request Parameters - Page 4:
2025-05-05 21:00:11,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:11,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:11,506 - INFO - Response - Page 4:
2025-05-05 21:00:11,709 - INFO - 第 4 页获取到 100 条记录
2025-05-05 21:00:11,709 - INFO - Request Parameters - Page 5:
2025-05-05 21:00:11,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:11,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:12,209 - INFO - Response - Page 5:
2025-05-05 21:00:12,412 - INFO - 第 5 页获取到 100 条记录
2025-05-05 21:00:12,412 - INFO - Request Parameters - Page 6:
2025-05-05 21:00:12,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:12,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:12,866 - INFO - Response - Page 6:
2025-05-05 21:00:13,069 - INFO - 第 6 页获取到 100 条记录
2025-05-05 21:00:13,069 - INFO - Request Parameters - Page 7:
2025-05-05 21:00:13,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:13,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:13,537 - INFO - Response - Page 7:
2025-05-05 21:00:13,741 - INFO - 第 7 页获取到 70 条记录
2025-05-05 21:00:13,741 - INFO - 查询完成，共获取到 670 条记录
2025-05-05 21:00:13,741 - INFO - 获取到 670 条表单数据
2025-05-05 21:00:13,741 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-05 21:00:13,756 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 21:00:13,756 - INFO - 开始处理日期: 2025-03
2025-05-05 21:00:13,756 - INFO - Request Parameters - Page 1:
2025-05-05 21:00:13,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:13,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:14,303 - INFO - Response - Page 1:
2025-05-05 21:00:14,506 - INFO - 第 1 页获取到 100 条记录
2025-05-05 21:00:14,506 - INFO - Request Parameters - Page 2:
2025-05-05 21:00:14,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:14,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:15,006 - INFO - Response - Page 2:
2025-05-05 21:00:15,209 - INFO - 第 2 页获取到 100 条记录
2025-05-05 21:00:15,209 - INFO - Request Parameters - Page 3:
2025-05-05 21:00:15,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:15,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:15,678 - INFO - Response - Page 3:
2025-05-05 21:00:15,881 - INFO - 第 3 页获取到 100 条记录
2025-05-05 21:00:15,881 - INFO - Request Parameters - Page 4:
2025-05-05 21:00:15,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:15,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:16,397 - INFO - Response - Page 4:
2025-05-05 21:00:16,600 - INFO - 第 4 页获取到 100 条记录
2025-05-05 21:00:16,600 - INFO - Request Parameters - Page 5:
2025-05-05 21:00:16,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:16,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:17,131 - INFO - Response - Page 5:
2025-05-05 21:00:17,334 - INFO - 第 5 页获取到 100 条记录
2025-05-05 21:00:17,334 - INFO - Request Parameters - Page 6:
2025-05-05 21:00:17,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:17,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:17,819 - INFO - Response - Page 6:
2025-05-05 21:00:18,022 - INFO - 第 6 页获取到 100 条记录
2025-05-05 21:00:18,022 - INFO - Request Parameters - Page 7:
2025-05-05 21:00:18,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:18,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:18,459 - INFO - Response - Page 7:
2025-05-05 21:00:18,662 - INFO - 第 7 页获取到 61 条记录
2025-05-05 21:00:18,662 - INFO - 查询完成，共获取到 661 条记录
2025-05-05 21:00:18,662 - INFO - 获取到 661 条表单数据
2025-05-05 21:00:18,662 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-05 21:00:18,678 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 21:00:18,678 - INFO - 开始处理日期: 2025-04
2025-05-05 21:00:18,678 - INFO - Request Parameters - Page 1:
2025-05-05 21:00:18,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:18,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:19,209 - INFO - Response - Page 1:
2025-05-05 21:00:19,412 - INFO - 第 1 页获取到 100 条记录
2025-05-05 21:00:19,412 - INFO - Request Parameters - Page 2:
2025-05-05 21:00:19,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:19,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:19,881 - INFO - Response - Page 2:
2025-05-05 21:00:20,084 - INFO - 第 2 页获取到 100 条记录
2025-05-05 21:00:20,084 - INFO - Request Parameters - Page 3:
2025-05-05 21:00:20,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:20,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:20,553 - INFO - Response - Page 3:
2025-05-05 21:00:20,756 - INFO - 第 3 页获取到 100 条记录
2025-05-05 21:00:20,756 - INFO - Request Parameters - Page 4:
2025-05-05 21:00:20,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:20,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:21,350 - INFO - Response - Page 4:
2025-05-05 21:00:21,553 - INFO - 第 4 页获取到 100 条记录
2025-05-05 21:00:21,553 - INFO - Request Parameters - Page 5:
2025-05-05 21:00:21,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:21,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:22,022 - INFO - Response - Page 5:
2025-05-05 21:00:22,225 - INFO - 第 5 页获取到 100 条记录
2025-05-05 21:00:22,225 - INFO - Request Parameters - Page 6:
2025-05-05 21:00:22,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:22,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:22,662 - INFO - Response - Page 6:
2025-05-05 21:00:22,866 - INFO - 第 6 页获取到 100 条记录
2025-05-05 21:00:22,866 - INFO - Request Parameters - Page 7:
2025-05-05 21:00:22,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:22,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:23,194 - INFO - Response - Page 7:
2025-05-05 21:00:23,397 - INFO - 第 7 页获取到 27 条记录
2025-05-05 21:00:23,397 - INFO - 查询完成，共获取到 627 条记录
2025-05-05 21:00:23,397 - INFO - 获取到 627 条表单数据
2025-05-05 21:00:23,397 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-05 21:00:23,412 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 21:00:23,412 - INFO - 开始处理日期: 2025-05
2025-05-05 21:00:23,412 - INFO - Request Parameters - Page 1:
2025-05-05 21:00:23,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:23,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:23,928 - INFO - Response - Page 1:
2025-05-05 21:00:24,131 - INFO - 第 1 页获取到 100 条记录
2025-05-05 21:00:24,131 - INFO - Request Parameters - Page 2:
2025-05-05 21:00:24,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:24,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:24,615 - INFO - Response - Page 2:
2025-05-05 21:00:24,819 - INFO - 第 2 页获取到 100 条记录
2025-05-05 21:00:24,819 - INFO - Request Parameters - Page 3:
2025-05-05 21:00:24,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:24,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:25,350 - INFO - Response - Page 3:
2025-05-05 21:00:25,553 - INFO - 第 3 页获取到 100 条记录
2025-05-05 21:00:25,553 - INFO - Request Parameters - Page 4:
2025-05-05 21:00:25,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:25,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:26,053 - INFO - Response - Page 4:
2025-05-05 21:00:26,256 - INFO - 第 4 页获取到 100 条记录
2025-05-05 21:00:26,256 - INFO - Request Parameters - Page 5:
2025-05-05 21:00:26,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:26,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:26,725 - INFO - Response - Page 5:
2025-05-05 21:00:26,928 - INFO - 第 5 页获取到 100 条记录
2025-05-05 21:00:26,928 - INFO - Request Parameters - Page 6:
2025-05-05 21:00:26,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-05 21:00:26,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-05 21:00:27,428 - INFO - Response - Page 6:
2025-05-05 21:00:27,631 - INFO - 第 6 页获取到 98 条记录
2025-05-05 21:00:27,631 - INFO - 查询完成，共获取到 598 条记录
2025-05-05 21:00:27,631 - INFO - 获取到 598 条表单数据
2025-05-05 21:00:27,631 - INFO - 当前日期 2025-05 有 598 条MySQL数据需要处理
2025-05-05 21:00:27,647 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 21:00:27,647 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-05 21:00:27,647 - INFO - =================同步完成====================
