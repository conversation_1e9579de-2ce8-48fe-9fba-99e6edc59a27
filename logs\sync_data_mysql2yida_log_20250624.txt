2025-06-24 01:30:33,805 - INFO - 使用默认增量同步（当天更新数据）
2025-06-24 01:30:33,805 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-24 01:30:33,805 - INFO - 查询参数: ('2025-06-24',)
2025-06-24 01:30:33,883 - INFO - MySQL查询成功，增量数据（日期: 2025-06-24），共获取 0 条记录
2025-06-24 01:30:33,883 - ERROR - 未获取到MySQL数据
2025-06-24 01:31:33,901 - INFO - 开始同步昨天与今天的销售数据: 2025-06-23 至 2025-06-24
2025-06-24 01:31:33,901 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-24 01:31:33,901 - INFO - 查询参数: ('2025-06-23', '2025-06-24')
2025-06-24 01:31:34,026 - INFO - MySQL查询成功，时间段: 2025-06-23 至 2025-06-24，共获取 79 条记录
2025-06-24 01:31:34,026 - INFO - 获取到 1 个日期需要处理: ['2025-06-23']
2025-06-24 01:31:34,026 - INFO - 开始处理日期: 2025-06-23
2025-06-24 01:31:34,026 - INFO - Request Parameters - Page 1:
2025-06-24 01:31:34,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 01:31:34,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 01:31:42,136 - ERROR - 处理日期 2025-06-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BCD63C19-07CD-7593-BBD8-6EECA280590F Response: {'code': 'ServiceUnavailable', 'requestid': 'BCD63C19-07CD-7593-BBD8-6EECA280590F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BCD63C19-07CD-7593-BBD8-6EECA280590F)
2025-06-24 01:31:42,136 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-24 01:31:42,136 - INFO - 同步完成
2025-06-24 04:30:34,073 - INFO - 使用默认增量同步（当天更新数据）
2025-06-24 04:30:34,073 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-24 04:30:34,073 - INFO - 查询参数: ('2025-06-24',)
2025-06-24 04:30:34,214 - INFO - MySQL查询成功，增量数据（日期: 2025-06-24），共获取 3 条记录
2025-06-24 04:30:34,214 - INFO - 获取到 1 个日期需要处理: ['2025-06-23']
2025-06-24 04:30:34,214 - INFO - 开始处理日期: 2025-06-23
2025-06-24 04:30:34,214 - INFO - Request Parameters - Page 1:
2025-06-24 04:30:34,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 04:30:34,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 04:30:40,121 - INFO - Response - Page 1:
2025-06-24 04:30:40,121 - INFO - 第 1 页获取到 50 条记录
2025-06-24 04:30:40,636 - INFO - Request Parameters - Page 2:
2025-06-24 04:30:40,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 04:30:40,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 04:30:48,761 - ERROR - 处理日期 2025-06-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0EAAD1EA-A768-7717-BA87-4D7C08AC030D Response: {'code': 'ServiceUnavailable', 'requestid': '0EAAD1EA-A768-7717-BA87-4D7C08AC030D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0EAAD1EA-A768-7717-BA87-4D7C08AC030D)
2025-06-24 04:30:48,761 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-24 04:31:48,779 - INFO - 开始同步昨天与今天的销售数据: 2025-06-23 至 2025-06-24
2025-06-24 04:31:48,779 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-24 04:31:48,779 - INFO - 查询参数: ('2025-06-23', '2025-06-24')
2025-06-24 04:31:48,904 - INFO - MySQL查询成功，时间段: 2025-06-23 至 2025-06-24，共获取 89 条记录
2025-06-24 04:31:48,904 - INFO - 获取到 1 个日期需要处理: ['2025-06-23']
2025-06-24 04:31:48,904 - INFO - 开始处理日期: 2025-06-23
2025-06-24 04:31:48,904 - INFO - Request Parameters - Page 1:
2025-06-24 04:31:48,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 04:31:48,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 04:31:49,592 - INFO - Response - Page 1:
2025-06-24 04:31:49,608 - INFO - 第 1 页获取到 50 条记录
2025-06-24 04:31:50,123 - INFO - Request Parameters - Page 2:
2025-06-24 04:31:50,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 04:31:50,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 04:31:50,561 - INFO - Response - Page 2:
2025-06-24 04:31:50,561 - INFO - 第 2 页获取到 3 条记录
2025-06-24 04:31:51,076 - INFO - 查询完成，共获取到 53 条记录
2025-06-24 04:31:51,076 - INFO - 获取到 53 条表单数据
2025-06-24 04:31:51,076 - INFO - 当前日期 2025-06-23 有 88 条MySQL数据需要处理
2025-06-24 04:31:51,076 - INFO - 开始批量插入 35 条新记录
2025-06-24 04:31:51,295 - INFO - 批量插入响应状态码: 200
2025-06-24 04:31:51,295 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 20:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1692', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4E74A452-9428-76EE-8F6E-AB495EC8DA11', 'x-acs-trace-id': 'c1e158ec03d6ebb71261b42273ba9125', 'etag': '10G0TAlvkTveVShFNrc2Bnw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 04:31:51,295 - INFO - 批量插入响应体: {'result': ['FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMZL', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM0M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM1M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM2M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM3M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM4M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM5M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM6M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM7M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM8M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM9M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMAM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMBM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMCM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMDM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMEM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMFM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMGM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMHM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMIM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMJM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMKM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMLM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMMM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMNM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMOM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMPM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMQM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMRM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMSM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMTM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMUM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMVM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMWM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMXM']}
2025-06-24 04:31:51,295 - INFO - 批量插入表单数据成功，批次 1，共 35 条记录
2025-06-24 04:31:51,295 - INFO - 成功插入的数据ID: ['FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMZL', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM0M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM1M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM2M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM3M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM4M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM5M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM6M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM7M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM8M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM9M', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMAM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMBM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMCM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMDM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMEM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMFM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMGM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CMHM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMIM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMJM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMKM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMLM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMMM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMNM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMOM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMPM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMQM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMRM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMSM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMTM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMUM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMVM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMWM', 'FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3RIDYJ9CMXM']
2025-06-24 04:31:56,311 - INFO - 批量插入完成，共 35 条记录
2025-06-24 04:31:56,311 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 35 条，错误: 0 条
2025-06-24 04:31:56,311 - INFO - 数据同步完成！更新: 0 条，插入: 35 条，错误: 0 条
2025-06-24 04:31:56,311 - INFO - 同步完成
2025-06-24 07:30:33,790 - INFO - 使用默认增量同步（当天更新数据）
2025-06-24 07:30:33,790 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-24 07:30:33,790 - INFO - 查询参数: ('2025-06-24',)
2025-06-24 07:30:33,915 - INFO - MySQL查询成功，增量数据（日期: 2025-06-24），共获取 7 条记录
2025-06-24 07:30:33,915 - INFO - 获取到 1 个日期需要处理: ['2025-06-23']
2025-06-24 07:30:33,915 - INFO - 开始处理日期: 2025-06-23
2025-06-24 07:30:33,915 - INFO - Request Parameters - Page 1:
2025-06-24 07:30:33,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 07:30:33,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 07:30:40,149 - INFO - Response - Page 1:
2025-06-24 07:30:40,149 - INFO - 第 1 页获取到 50 条记录
2025-06-24 07:30:40,665 - INFO - Request Parameters - Page 2:
2025-06-24 07:30:40,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 07:30:40,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 07:30:41,259 - INFO - Response - Page 2:
2025-06-24 07:30:41,259 - INFO - 第 2 页获取到 38 条记录
2025-06-24 07:30:41,759 - INFO - 查询完成，共获取到 88 条记录
2025-06-24 07:30:41,759 - INFO - 获取到 88 条表单数据
2025-06-24 07:30:41,759 - INFO - 当前日期 2025-06-23 有 7 条MySQL数据需要处理
2025-06-24 07:30:41,759 - INFO - 开始批量插入 4 条新记录
2025-06-24 07:30:41,915 - INFO - 批量插入响应状态码: 200
2025-06-24 07:30:41,915 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 23:30:38 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '548E2B61-B4B7-731A-9326-858FC9EA36BB', 'x-acs-trace-id': 'ad3511d9c35b76c3d69213ac5157fa13', 'etag': '2cOS2BWoZn8E8kjhqKQzAXA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 07:30:41,915 - INFO - 批量插入响应体: {'result': ['FINST-ZX866571PZHWHDK9EBKSUDEDH55B2OEBCQ9CM4Q', 'FINST-ZX866571PZHWHDK9EBKSUDEDH55B2OEBCQ9CM5Q', 'FINST-ZX866571PZHWHDK9EBKSUDEDH55B2OEBCQ9CM6Q', 'FINST-ZX866571PZHWHDK9EBKSUDEDH55B2OEBCQ9CM7Q']}
2025-06-24 07:30:41,915 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-06-24 07:30:41,915 - INFO - 成功插入的数据ID: ['FINST-ZX866571PZHWHDK9EBKSUDEDH55B2OEBCQ9CM4Q', 'FINST-ZX866571PZHWHDK9EBKSUDEDH55B2OEBCQ9CM5Q', 'FINST-ZX866571PZHWHDK9EBKSUDEDH55B2OEBCQ9CM6Q', 'FINST-ZX866571PZHWHDK9EBKSUDEDH55B2OEBCQ9CM7Q']
2025-06-24 07:30:46,930 - INFO - 批量插入完成，共 4 条记录
2025-06-24 07:30:46,930 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-06-24 07:30:46,930 - INFO - 数据同步完成！更新: 0 条，插入: 4 条，错误: 0 条
2025-06-24 07:31:46,946 - INFO - 开始同步昨天与今天的销售数据: 2025-06-23 至 2025-06-24
2025-06-24 07:31:46,946 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-24 07:31:46,946 - INFO - 查询参数: ('2025-06-23', '2025-06-24')
2025-06-24 07:31:47,071 - INFO - MySQL查询成功，时间段: 2025-06-23 至 2025-06-24，共获取 108 条记录
2025-06-24 07:31:47,071 - INFO - 获取到 1 个日期需要处理: ['2025-06-23']
2025-06-24 07:31:47,071 - INFO - 开始处理日期: 2025-06-23
2025-06-24 07:31:47,071 - INFO - Request Parameters - Page 1:
2025-06-24 07:31:47,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 07:31:47,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 07:31:55,180 - ERROR - 处理日期 2025-06-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D1D72A99-7D2F-711B-93F9-D641654A3E61 Response: {'code': 'ServiceUnavailable', 'requestid': 'D1D72A99-7D2F-711B-93F9-D641654A3E61', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D1D72A99-7D2F-711B-93F9-D641654A3E61)
2025-06-24 07:31:55,180 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-24 07:31:55,180 - INFO - 同步完成
2025-06-24 10:30:33,631 - INFO - 使用默认增量同步（当天更新数据）
2025-06-24 10:30:33,631 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-24 10:30:33,631 - INFO - 查询参数: ('2025-06-24',)
2025-06-24 10:30:33,771 - INFO - MySQL查询成功，增量数据（日期: 2025-06-24），共获取 129 条记录
2025-06-24 10:30:33,771 - INFO - 获取到 1 个日期需要处理: ['2025-06-23']
2025-06-24 10:30:33,771 - INFO - 开始处理日期: 2025-06-23
2025-06-24 10:30:33,771 - INFO - Request Parameters - Page 1:
2025-06-24 10:30:33,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 10:30:33,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 10:30:41,896 - ERROR - 处理日期 2025-06-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 679972E0-1D9F-7738-AC73-ADADB89E8B4C Response: {'code': 'ServiceUnavailable', 'requestid': '679972E0-1D9F-7738-AC73-ADADB89E8B4C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 679972E0-1D9F-7738-AC73-ADADB89E8B4C)
2025-06-24 10:30:41,896 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-24 10:31:41,912 - INFO - 开始同步昨天与今天的销售数据: 2025-06-23 至 2025-06-24
2025-06-24 10:31:41,912 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-24 10:31:41,912 - INFO - 查询参数: ('2025-06-23', '2025-06-24')
2025-06-24 10:31:42,052 - INFO - MySQL查询成功，时间段: 2025-06-23 至 2025-06-24，共获取 509 条记录
2025-06-24 10:31:42,052 - INFO - 获取到 1 个日期需要处理: ['2025-06-23']
2025-06-24 10:31:42,052 - INFO - 开始处理日期: 2025-06-23
2025-06-24 10:31:42,052 - INFO - Request Parameters - Page 1:
2025-06-24 10:31:42,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 10:31:42,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 10:31:47,693 - INFO - Response - Page 1:
2025-06-24 10:31:47,693 - INFO - 第 1 页获取到 50 条记录
2025-06-24 10:31:48,208 - INFO - Request Parameters - Page 2:
2025-06-24 10:31:48,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 10:31:48,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 10:31:49,036 - INFO - Response - Page 2:
2025-06-24 10:31:49,036 - INFO - 第 2 页获取到 42 条记录
2025-06-24 10:31:49,536 - INFO - 查询完成，共获取到 92 条记录
2025-06-24 10:31:49,536 - INFO - 获取到 92 条表单数据
2025-06-24 10:31:49,536 - INFO - 当前日期 2025-06-23 有 498 条MySQL数据需要处理
2025-06-24 10:31:49,536 - INFO - 开始批量插入 406 条新记录
2025-06-24 10:31:49,802 - INFO - 批量插入响应状态码: 200
2025-06-24 10:31:49,802 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 02:31:46 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '19AC2DA0-B031-7E3B-AF2C-23F59901A29E', 'x-acs-trace-id': 'e1e94414fb3214350ef1e9e454e15452', 'etag': '2UmecTqEujItW0FpiTBMOnQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 10:31:49,802 - INFO - 批量插入响应体: {'result': ['FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMAO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMBO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMCO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMDO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMEO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMFO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMGO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMHO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMIO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMJO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMKO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMLO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMMO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMNO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMOO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMPO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMQO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMRO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMSO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMTO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMUO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMVO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMWO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMXO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMYO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMZO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM0P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM1P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM2P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM3P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM4P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM5P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM6P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM7P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM8P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM9P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMAP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMBP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMCP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMDP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMEP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMFP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMGP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMHP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMIP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMJP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMKP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMLP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMMP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMNP']}
2025-06-24 10:31:49,802 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-24 10:31:49,802 - INFO - 成功插入的数据ID: ['FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMAO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMBO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMCO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMDO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMEO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMFO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMGO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMHO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMIO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMJO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMKO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMLO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMMO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMNO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMOO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMPO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMQO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMRO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMSO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMTO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMUO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMVO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMWO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMXO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMYO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMZO', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM0P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM1P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM2P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM3P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM4P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM5P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM6P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM7P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM8P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CM9P', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMAP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMBP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMCP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMDP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMEP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMFP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMGP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMHP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMIP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMJP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMKP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMLP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMMP', 'FINST-LLF668811TJWP77NFLUG2BTIPIU73969TW9CMNP']
2025-06-24 10:31:55,052 - INFO - 批量插入响应状态码: 200
2025-06-24 10:31:55,052 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 02:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B2CF1B08-7E1C-7A2E-9222-2DD773CB2D7F', 'x-acs-trace-id': '637b6ebdeafc0440e594393a9188d913', 'etag': '2Xib7EzPabz6uu99MLYaf2A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 10:31:55,052 - INFO - 批量插入响应体: {'result': ['FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CMU9', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CMV9', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CMW9', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CMX9', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CMY9', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CMZ9', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CM0A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM1A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM2A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM3A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM4A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM5A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM6A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM7A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM8A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM9A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMAA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMBA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMCA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMDA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMEA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMFA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMGA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMHA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMIA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMJA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMKA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMLA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMMA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMNA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMOA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMPA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMQA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMRA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMSA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMTA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMUA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMVA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMWA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMXA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMYA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMZA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM0B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM1B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM2B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM3B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM4B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM5B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM6B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM7B']}
2025-06-24 10:31:55,052 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-24 10:31:55,052 - INFO - 成功插入的数据ID: ['FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CMU9', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CMV9', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CMW9', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CMX9', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CMY9', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CMZ9', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ278DTW9CM0A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM1A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM2A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM3A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM4A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM5A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM6A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM7A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM8A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM9A', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMAA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMBA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMCA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMDA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMEA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMFA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMGA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMHA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMIA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMJA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMKA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMLA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMMA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMNA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMOA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMPA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMQA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMRA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMSA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMTA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMUA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMVA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMWA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMXA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMYA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CMZA', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM0B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM1B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM2B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM3B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM4B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM5B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM6B', 'FINST-QZE668D1NMJWGAFL8G55V98YNUWQ288DTW9CM7B']
2025-06-24 10:32:00,286 - INFO - 批量插入响应状态码: 200
2025-06-24 10:32:00,286 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 02:31:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1EE40111-071A-7C06-B02C-810883F039E3', 'x-acs-trace-id': 'e2d906a1fc7b1af229f06af52bf67d45', 'etag': '2XeBD7kmU/mO0kR/kKWY+NQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 10:32:00,286 - INFO - 批量插入响应体: {'result': ['FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMIG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMJG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMKG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMLG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMMG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMNG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMOG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMPG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMQG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMRG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMSG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMTG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMUG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMVG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMWG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMXG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMYG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMZG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM0H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM1H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM2H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM3H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM4H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM5H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM6H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM7H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM8H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM9H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMAH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMBH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMCH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMDH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMEH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMFH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMGH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMHH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMIH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMJH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMKH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMLH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMMH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMNH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMOH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMPH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMQH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMRH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMSH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMTH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMUH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMVH']}
2025-06-24 10:32:00,286 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-24 10:32:00,286 - INFO - 成功插入的数据ID: ['FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMIG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMJG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMKG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMLG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMMG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMNG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMOG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMPG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMQG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMRG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMSG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMTG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMUG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMVG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMWG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMXG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMYG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMZG', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM0H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM1H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM2H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM3H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM4H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM5H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM6H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM7H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM8H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CM9H', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMAH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMBH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMCH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMDH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMEH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMFH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMGH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMHH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMIH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMJH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMKH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMLH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMMH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMNH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMOH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMPH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMQH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMRH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMSH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMTH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMUH', 'FINST-2PF66KD1FLJWRVF99BPDW9ZZSGKR2Q9HTW9CMVH']
2025-06-24 10:32:05,536 - INFO - 批量插入响应状态码: 200
2025-06-24 10:32:05,536 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 02:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AF8013EA-B301-7035-9D8C-F4B812E879B3', 'x-acs-trace-id': '9fa2d174781936176c9ba1dfcbda39a5', 'etag': '29CZZUB0nRQ/w86cIxfJq7g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 10:32:05,536 - INFO - 批量插入响应体: {'result': ['FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMF6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMG6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMH6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMI6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMJ6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMK6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CML6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMM6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMN6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMO6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMP6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMQ6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMR6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMS6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMT6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMU6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMV6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMW6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMX6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMY6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMZ6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM07', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM17', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM27', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM37', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM47', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM57', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM67', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM77', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM87', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM97', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMA7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMB7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMC7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMD7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CME7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMF7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMG7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMH7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMI7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMJ7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMK7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CML7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMM7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMN7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMO7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMP7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMQ7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMR7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMS7']}
2025-06-24 10:32:05,536 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-06-24 10:32:05,536 - INFO - 成功插入的数据ID: ['FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMF6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMG6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMH6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMI6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMJ6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMK6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CML6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMM6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMN6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMO6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMP6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMQ6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMR6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMS6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMT6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMU6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMV6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMW6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMX6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMY6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMZ6', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM07', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM17', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM27', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM37', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM47', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM57', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM67', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM77', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM87', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CM97', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMA7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMB7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMC7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMD7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CME7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMF7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMG7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMH7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMI7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMJ7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMK7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CML7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMM7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMN7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMO7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMP7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMQ7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMR7', 'FINST-OIF66BA104KWCALLC7H5N56TB3Y339BLTW9CMS7']
2025-06-24 10:32:10,786 - INFO - 批量插入响应状态码: 200
2025-06-24 10:32:10,786 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 02:32:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F0917B89-4DBA-7975-9FE5-1B473E23BEBB', 'x-acs-trace-id': 'a532a81c3b94cb9ab668d4b33f2ce67d', 'etag': '2Y8Mk+Fh1jxhz4rfnMedcLA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 10:32:10,786 - INFO - 批量插入响应体: {'result': ['FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMQ41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMR41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMS41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMT41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMU41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMV41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMW41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMX41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMY41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMZ41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM051', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM151', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM251', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM351', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM451', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM551', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM651', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM751', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM851', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM951', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMA51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMB51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMC51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMD51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CME51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMF51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMG51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMH51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMI51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMJ51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMK51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CML51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMM51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMN51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMO51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMP51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMQ51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMR51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMS51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMT51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMU51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMV51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMW51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMX51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMY51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMZ51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM061', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM161', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM261', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM361']}
2025-06-24 10:32:10,786 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-06-24 10:32:10,786 - INFO - 成功插入的数据ID: ['FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMQ41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMR41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMS41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMT41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMU41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMV41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMW41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMX41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMY41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMZ41', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM051', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM151', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM251', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM351', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM451', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM551', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM651', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM751', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM851', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM951', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMA51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMB51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMC51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMD51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CME51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMF51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMG51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMH51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMI51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMJ51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMK51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CML51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMM51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMN51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMO51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMP51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMQ51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMR51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMS51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMT51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMU51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMV51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMW51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMX51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMY51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMZ51', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM061', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM161', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM261', 'FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CM361']
2025-06-24 10:32:16,021 - INFO - 批量插入响应状态码: 200
2025-06-24 10:32:16,021 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 02:32:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BE5877DA-8683-787D-A0C8-A62F65B7EF9D', 'x-acs-trace-id': '4e3b7ea29a0e09502fe2645b9e16057c', 'etag': '2YzYnQXcwCxMvrcdRylFHOw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 10:32:16,021 - INFO - 批量插入响应体: {'result': ['FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMT4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMU4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMV4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMW4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMX4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMY4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMZ4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM05', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM15', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM25', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM35', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM45', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM55', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM65', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM75', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM85', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM95', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMA5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMB5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMC5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMD5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CME5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMF5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMG5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMH5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMI5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMJ5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMK5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CML5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMM5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMN5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMO5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMP5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMQ5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMR5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMS5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMT5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMU5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMV5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMW5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMX5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMY5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMZ5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM06', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM16', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM26', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM36', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM46', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM56', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM66']}
2025-06-24 10:32:16,021 - INFO - 批量插入表单数据成功，批次 6，共 50 条记录
2025-06-24 10:32:16,021 - INFO - 成功插入的数据ID: ['FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMT4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMU4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMV4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMW4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMX4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMY4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMZ4', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM05', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM15', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM25', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM35', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM45', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM55', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM65', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM75', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM85', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM95', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMA5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMB5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMC5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMD5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CME5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMF5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMG5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMH5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMI5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMJ5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMK5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CML5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMM5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMN5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMO5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMP5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMQ5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMR5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMS5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMT5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMU5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMV5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMW5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMX5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMY5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CMZ5', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM06', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM16', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM26', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM36', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM46', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM56', 'FINST-X2F66HC1ARJW5TAZC8Z33BQHD2L62VETTW9CM66']
2025-06-24 10:32:21,271 - INFO - 批量插入响应状态码: 200
2025-06-24 10:32:21,271 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 02:32:17 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2385', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '086C167E-E709-7CA3-9E2D-BFC6B27B71EB', 'x-acs-trace-id': 'cb42a2732662416b79c0752b545551e0', 'etag': '2VflCKwOB/1VF4mTULzELdg5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 10:32:21,271 - INFO - 批量插入响应体: {'result': ['FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM9', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMA', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMB', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMC', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMD', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CME', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMF', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMG', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMH', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMI', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMJ', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMK', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CML', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMM', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMN', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMO', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMP', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMQ', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMR', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMS', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMT', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMU', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMV', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMW', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMX', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMY', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMZ', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM01', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM11', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM21', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM31', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM41', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM51', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM61', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM71', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM81', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM91', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMA1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMB1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMC1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMD1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CME1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMF1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMG1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMH1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMI1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMJ1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMK1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CML1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMM1']}
2025-06-24 10:32:21,271 - INFO - 批量插入表单数据成功，批次 7，共 50 条记录
2025-06-24 10:32:21,271 - INFO - 成功插入的数据ID: ['FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM9', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMA', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMB', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMC', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMD', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CME', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMF', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMG', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMH', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMI', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMJ', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMK', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CML', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMM', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMN', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMO', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMP', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMQ', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMR', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMS', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMT', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMU', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMV', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMW', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMX', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMY', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMZ', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM01', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM11', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM21', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM31', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM41', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM51', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM61', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM71', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM81', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM91', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMA1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMB1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMC1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMD1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CME1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMF1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMG1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMH1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMI1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMJ1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMK1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CML1', 'FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CMM1']
2025-06-24 10:32:26,489 - INFO - 批量插入响应状态码: 200
2025-06-24 10:32:26,489 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 02:32:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2388', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '73D9A74A-CCC0-7D87-8238-0C38B77F9762', 'x-acs-trace-id': '17831b8fe032ebc84ce02d1598f5b52c', 'etag': '2QHGO0xTIKgzrC9DdrVH1Xw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 10:32:26,489 - INFO - 批量插入响应体: {'result': ['FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMC', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMD', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CME', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMF', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMG', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMH', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMI', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMJ', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMK', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CML', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMM', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMN', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMO', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMP', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMQ', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMR', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMS', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMT', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMU', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMV', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMW', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMX', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMY', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMZ', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM01', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM11', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM21', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM31', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM41', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM51', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM61', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM71', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM81', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM91', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMA1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMB1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMC1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMD1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CME1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMF1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMG1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMH1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMI1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMJ1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMK1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2UH1UW9CML1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2UH1UW9CMM1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2UH1UW9CMN1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2UH1UW9CMO1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2UH1UW9CMP1']}
2025-06-24 10:32:26,505 - INFO - 批量插入表单数据成功，批次 8，共 50 条记录
2025-06-24 10:32:26,505 - INFO - 成功插入的数据ID: ['FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMC', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMD', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CME', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMF', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMG', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMH', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMI', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMJ', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMK', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CML', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMM', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMN', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMO', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMP', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMQ', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMR', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMS', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMT', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMU', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMV', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMW', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMX', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMY', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMZ', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM01', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM11', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM21', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM31', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM41', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM51', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM61', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM71', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM81', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CM91', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMA1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMB1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMC1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMD1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CME1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMF1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMG1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMH1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMI1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMJ1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2TH1UW9CMK1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2UH1UW9CML1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2UH1UW9CMM1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2UH1UW9CMN1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2UH1UW9CMO1', 'FINST-OIF66RB1INKW5934BI4HJ4MLBR8E2UH1UW9CMP1']
2025-06-24 10:32:31,677 - INFO - 批量插入响应状态码: 200
2025-06-24 10:32:31,677 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 02:32:28 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '294', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DF38FDA8-0FE2-76EA-A400-20CA5094F50B', 'x-acs-trace-id': 'f8cc4443192f6f327d2afc4ac1cb1cee', 'etag': '2Hr/v5XGqblhYBNYeLmFNdg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 10:32:31,677 - INFO - 批量插入响应体: {'result': ['FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM4', 'FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM5', 'FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM6', 'FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM7', 'FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM8', 'FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM9']}
2025-06-24 10:32:31,677 - INFO - 批量插入表单数据成功，批次 9，共 6 条记录
2025-06-24 10:32:31,677 - INFO - 成功插入的数据ID: ['FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM4', 'FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM5', 'FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM6', 'FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM7', 'FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM8', 'FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM9']
2025-06-24 10:32:36,692 - INFO - 批量插入完成，共 406 条记录
2025-06-24 10:32:36,692 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 406 条，错误: 0 条
2025-06-24 10:32:36,692 - INFO - 数据同步完成！更新: 0 条，插入: 406 条，错误: 0 条
2025-06-24 10:32:36,692 - INFO - 同步完成
2025-06-24 13:30:33,588 - INFO - 使用默认增量同步（当天更新数据）
2025-06-24 13:30:33,588 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-24 13:30:33,588 - INFO - 查询参数: ('2025-06-24',)
2025-06-24 13:30:33,729 - INFO - MySQL查询成功，增量数据（日期: 2025-06-24），共获取 136 条记录
2025-06-24 13:30:33,729 - INFO - 获取到 1 个日期需要处理: ['2025-06-23']
2025-06-24 13:30:33,729 - INFO - 开始处理日期: 2025-06-23
2025-06-24 13:30:33,729 - INFO - Request Parameters - Page 1:
2025-06-24 13:30:33,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 13:30:33,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 13:30:41,870 - ERROR - 处理日期 2025-06-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B0E3EDC3-C3C8-76BF-8963-F17B1F888149 Response: {'code': 'ServiceUnavailable', 'requestid': 'B0E3EDC3-C3C8-76BF-8963-F17B1F888149', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B0E3EDC3-C3C8-76BF-8963-F17B1F888149)
2025-06-24 13:30:41,870 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-24 13:31:41,885 - INFO - 开始同步昨天与今天的销售数据: 2025-06-23 至 2025-06-24
2025-06-24 13:31:41,885 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-24 13:31:41,885 - INFO - 查询参数: ('2025-06-23', '2025-06-24')
2025-06-24 13:31:42,025 - INFO - MySQL查询成功，时间段: 2025-06-23 至 2025-06-24，共获取 513 条记录
2025-06-24 13:31:42,025 - INFO - 获取到 1 个日期需要处理: ['2025-06-23']
2025-06-24 13:31:42,041 - INFO - 开始处理日期: 2025-06-23
2025-06-24 13:31:42,041 - INFO - Request Parameters - Page 1:
2025-06-24 13:31:42,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 13:31:42,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 13:31:48,557 - INFO - Response - Page 1:
2025-06-24 13:31:48,557 - INFO - 第 1 页获取到 50 条记录
2025-06-24 13:31:49,072 - INFO - Request Parameters - Page 2:
2025-06-24 13:31:49,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 13:31:49,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 13:31:49,775 - INFO - Response - Page 2:
2025-06-24 13:31:49,775 - INFO - 第 2 页获取到 50 条记录
2025-06-24 13:31:50,275 - INFO - Request Parameters - Page 3:
2025-06-24 13:31:50,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 13:31:50,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 13:31:50,994 - INFO - Response - Page 3:
2025-06-24 13:31:50,994 - INFO - 第 3 页获取到 50 条记录
2025-06-24 13:31:51,494 - INFO - Request Parameters - Page 4:
2025-06-24 13:31:51,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 13:31:51,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 13:31:52,135 - INFO - Response - Page 4:
2025-06-24 13:31:52,135 - INFO - 第 4 页获取到 50 条记录
2025-06-24 13:31:52,635 - INFO - Request Parameters - Page 5:
2025-06-24 13:31:52,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 13:31:52,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 13:31:53,244 - INFO - Response - Page 5:
2025-06-24 13:31:53,244 - INFO - 第 5 页获取到 50 条记录
2025-06-24 13:31:53,744 - INFO - Request Parameters - Page 6:
2025-06-24 13:31:53,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 13:31:53,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 13:31:54,385 - INFO - Response - Page 6:
2025-06-24 13:31:54,385 - INFO - 第 6 页获取到 50 条记录
2025-06-24 13:31:54,900 - INFO - Request Parameters - Page 7:
2025-06-24 13:31:54,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 13:31:54,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 13:31:55,588 - INFO - Response - Page 7:
2025-06-24 13:31:55,588 - INFO - 第 7 页获取到 50 条记录
2025-06-24 13:31:56,103 - INFO - Request Parameters - Page 8:
2025-06-24 13:31:56,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 13:31:56,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 13:31:56,807 - INFO - Response - Page 8:
2025-06-24 13:31:56,807 - INFO - 第 8 页获取到 50 条记录
2025-06-24 13:31:57,322 - INFO - Request Parameters - Page 9:
2025-06-24 13:31:57,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 13:31:57,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 13:31:58,025 - INFO - Response - Page 9:
2025-06-24 13:31:58,025 - INFO - 第 9 页获取到 50 条记录
2025-06-24 13:31:58,525 - INFO - Request Parameters - Page 10:
2025-06-24 13:31:58,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 13:31:58,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 13:31:59,166 - INFO - Response - Page 10:
2025-06-24 13:31:59,166 - INFO - 第 10 页获取到 48 条记录
2025-06-24 13:31:59,666 - INFO - 查询完成，共获取到 498 条记录
2025-06-24 13:31:59,666 - INFO - 获取到 498 条表单数据
2025-06-24 13:31:59,666 - INFO - 当前日期 2025-06-23 有 502 条MySQL数据需要处理
2025-06-24 13:31:59,682 - INFO - 开始更新记录 - 表单实例ID: FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM5M
2025-06-24 13:32:00,213 - INFO - 更新表单数据成功: FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM5M
2025-06-24 13:32:00,213 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 30000.0}, {'field': 'total_amount', 'old_value': 210.0, 'new_value': 30210.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/5102eab3f98b4c579e9466565cb81780.png?Expires=2066026428&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=QbYdAsJ0%2BVf653Fzou4C%2FvyKep0%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/f9c37881472e49a1a8a42aed73dc011b.jpg?Expires=2066026428&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=wmxhtfjynNnusUx49qbJlAXStXY%3D'}]
2025-06-24 13:32:00,213 - INFO - 开始更新记录 - 表单实例ID: FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM4M
2025-06-24 13:32:00,744 - INFO - 更新表单数据成功: FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM4M
2025-06-24 13:32:00,744 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57778.0, 'new_value': 87778.0}, {'field': 'total_amount', 'old_value': 64534.0, 'new_value': 94534.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 58}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/ce900fd2ffa34b27b7561eeaf369550d.jpg?Expires=2066027201&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=h1WPm%2F04yk7SFHm9PJ3Mfae0RPU%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/4fcfee6dec13426293eb87de5d504b1a.jpg?Expires=2066026428&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=3cbn0XQwRnA%2BJVj6zowWQBeSd7s%3D'}]
2025-06-24 13:32:00,744 - INFO - 开始更新记录 - 表单实例ID: FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM2M
2025-06-24 13:32:01,150 - INFO - 更新表单数据成功: FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM2M
2025-06-24 13:32:01,150 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 7800.0, 'new_value': 57800.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 11}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/68fd909e93f3404da373651d0e879b0a.jpg?Expires=2066027201&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=KjPvT%2FzNAjGyoBIPdaz6ptzDz8k%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/01ed103510644382aae36d28be45a7cf.jpg?Expires=2066026428&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=EhYcTSsAITkDGW%2F6%2B8DnkKECZeY%3D'}]
2025-06-24 13:32:01,150 - INFO - 开始更新记录 - 表单实例ID: FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM3M
2025-06-24 13:32:01,572 - INFO - 更新表单数据成功: FINST-MUC66Q81JNJWBXZL7DLIY84H8KQX3QIDYJ9CM3M
2025-06-24 13:32:01,572 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 30000.0}, {'field': 'total_amount', 'old_value': 9800.0, 'new_value': 39800.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 9}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/529f8b9759cd4a36823e8bd7dcf82a43.jpg?Expires=2066026428&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=j%2BDIxLMtPXbWEbkGNrWwiScExyY%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/dbd957856e9e497fadad384c58652efd.jpg?Expires=2066026428&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=t7ma2J8%2Fk031G%2BSVe8u0SZQbjYQ%3D'}]
2025-06-24 13:32:01,572 - INFO - 开始批量插入 4 条新记录
2025-06-24 13:32:01,728 - INFO - 批量插入响应状态码: 200
2025-06-24 13:32:01,728 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 05:31:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '203', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EBB84905-2D3C-7B36-B9D4-3156055AF15B', 'x-acs-trace-id': '2a1699e00cc6c90bda1e59dcfb2a8771', 'etag': '2m29IO2ii31+Pk8FyO23Q/w3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 13:32:01,728 - INFO - 批量插入响应体: {'result': ['FINST-NS7669915QKW4KDF7Y2ZX9F11G4P2WRZ83ACMZ', 'FINST-NS7669915QKW4KDF7Y2ZX9F11G4P2WRZ83ACM01', 'FINST-NS7669915QKW4KDF7Y2ZX9F11G4P2WRZ83ACM11', 'FINST-NS7669915QKW4KDF7Y2ZX9F11G4P2WRZ83ACM21']}
2025-06-24 13:32:01,728 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-06-24 13:32:01,728 - INFO - 成功插入的数据ID: ['FINST-NS7669915QKW4KDF7Y2ZX9F11G4P2WRZ83ACMZ', 'FINST-NS7669915QKW4KDF7Y2ZX9F11G4P2WRZ83ACM01', 'FINST-NS7669915QKW4KDF7Y2ZX9F11G4P2WRZ83ACM11', 'FINST-NS7669915QKW4KDF7Y2ZX9F11G4P2WRZ83ACM21']
2025-06-24 13:32:06,744 - INFO - 批量插入完成，共 4 条记录
2025-06-24 13:32:06,744 - INFO - 日期 2025-06-23 处理完成 - 更新: 4 条，插入: 4 条，错误: 0 条
2025-06-24 13:32:06,744 - INFO - 数据同步完成！更新: 4 条，插入: 4 条，错误: 0 条
2025-06-24 13:32:06,744 - INFO - 同步完成
2025-06-24 16:30:33,921 - INFO - 使用默认增量同步（当天更新数据）
2025-06-24 16:30:33,921 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-24 16:30:33,921 - INFO - 查询参数: ('2025-06-24',)
2025-06-24 16:30:34,061 - INFO - MySQL查询成功，增量数据（日期: 2025-06-24），共获取 138 条记录
2025-06-24 16:30:34,061 - INFO - 获取到 1 个日期需要处理: ['2025-06-23']
2025-06-24 16:30:34,061 - INFO - 开始处理日期: 2025-06-23
2025-06-24 16:30:34,061 - INFO - Request Parameters - Page 1:
2025-06-24 16:30:34,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 16:30:34,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 16:30:42,171 - ERROR - 处理日期 2025-06-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B6A8C3E7-9537-744F-9DD2-58435B1A4A1A Response: {'code': 'ServiceUnavailable', 'requestid': 'B6A8C3E7-9537-744F-9DD2-58435B1A4A1A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B6A8C3E7-9537-744F-9DD2-58435B1A4A1A)
2025-06-24 16:30:42,171 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-24 16:31:42,186 - INFO - 开始同步昨天与今天的销售数据: 2025-06-23 至 2025-06-24
2025-06-24 16:31:42,186 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-24 16:31:42,186 - INFO - 查询参数: ('2025-06-23', '2025-06-24')
2025-06-24 16:31:42,327 - INFO - MySQL查询成功，时间段: 2025-06-23 至 2025-06-24，共获取 515 条记录
2025-06-24 16:31:42,327 - INFO - 获取到 1 个日期需要处理: ['2025-06-23']
2025-06-24 16:31:42,342 - INFO - 开始处理日期: 2025-06-23
2025-06-24 16:31:42,342 - INFO - Request Parameters - Page 1:
2025-06-24 16:31:42,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 16:31:42,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 16:31:48,014 - INFO - Response - Page 1:
2025-06-24 16:31:48,014 - INFO - 第 1 页获取到 50 条记录
2025-06-24 16:31:48,514 - INFO - Request Parameters - Page 2:
2025-06-24 16:31:48,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 16:31:48,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 16:31:49,233 - INFO - Response - Page 2:
2025-06-24 16:31:49,233 - INFO - 第 2 页获取到 50 条记录
2025-06-24 16:31:49,748 - INFO - Request Parameters - Page 3:
2025-06-24 16:31:49,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 16:31:49,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 16:31:50,436 - INFO - Response - Page 3:
2025-06-24 16:31:50,436 - INFO - 第 3 页获取到 50 条记录
2025-06-24 16:31:50,952 - INFO - Request Parameters - Page 4:
2025-06-24 16:31:50,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 16:31:50,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 16:31:51,670 - INFO - Response - Page 4:
2025-06-24 16:31:51,670 - INFO - 第 4 页获取到 50 条记录
2025-06-24 16:31:52,170 - INFO - Request Parameters - Page 5:
2025-06-24 16:31:52,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 16:31:52,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 16:31:52,795 - INFO - Response - Page 5:
2025-06-24 16:31:52,795 - INFO - 第 5 页获取到 50 条记录
2025-06-24 16:31:53,295 - INFO - Request Parameters - Page 6:
2025-06-24 16:31:53,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 16:31:53,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 16:31:53,983 - INFO - Response - Page 6:
2025-06-24 16:31:53,983 - INFO - 第 6 页获取到 50 条记录
2025-06-24 16:31:54,498 - INFO - Request Parameters - Page 7:
2025-06-24 16:31:54,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 16:31:54,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 16:31:55,170 - INFO - Response - Page 7:
2025-06-24 16:31:55,170 - INFO - 第 7 页获取到 50 条记录
2025-06-24 16:31:55,686 - INFO - Request Parameters - Page 8:
2025-06-24 16:31:55,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 16:31:55,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 16:31:56,358 - INFO - Response - Page 8:
2025-06-24 16:31:56,358 - INFO - 第 8 页获取到 50 条记录
2025-06-24 16:31:56,873 - INFO - Request Parameters - Page 9:
2025-06-24 16:31:56,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 16:31:56,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 16:31:57,561 - INFO - Response - Page 9:
2025-06-24 16:31:57,561 - INFO - 第 9 页获取到 50 条记录
2025-06-24 16:31:58,061 - INFO - Request Parameters - Page 10:
2025-06-24 16:31:58,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 16:31:58,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 16:31:58,717 - INFO - Response - Page 10:
2025-06-24 16:31:58,733 - INFO - 第 10 页获取到 50 条记录
2025-06-24 16:31:59,248 - INFO - Request Parameters - Page 11:
2025-06-24 16:31:59,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 16:31:59,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 16:31:59,686 - INFO - Response - Page 11:
2025-06-24 16:31:59,686 - INFO - 第 11 页获取到 2 条记录
2025-06-24 16:32:00,186 - INFO - 查询完成，共获取到 502 条记录
2025-06-24 16:32:00,186 - INFO - 获取到 502 条表单数据
2025-06-24 16:32:00,186 - INFO - 当前日期 2025-06-23 有 504 条MySQL数据需要处理
2025-06-24 16:32:00,202 - INFO - 开始批量插入 2 条新记录
2025-06-24 16:32:00,358 - INFO - 批量插入响应状态码: 200
2025-06-24 16:32:00,358 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 08:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1D8D40F6-F272-78FC-BBA9-4C60002AB392', 'x-acs-trace-id': '1f1cd82e30091256089d63ca358490ed', 'etag': '1jLmuR/PHdHhrrzMPZR2fTw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 16:32:00,358 - INFO - 批量插入响应体: {'result': ['FINST-B1D66U614VJWLUUE6HDM7AMP3SFJ2R3GO9ACMMA', 'FINST-B1D66U614VJWLUUE6HDM7AMP3SFJ2R3GO9ACMNA']}
2025-06-24 16:32:00,358 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-24 16:32:00,358 - INFO - 成功插入的数据ID: ['FINST-B1D66U614VJWLUUE6HDM7AMP3SFJ2R3GO9ACMMA', 'FINST-B1D66U614VJWLUUE6HDM7AMP3SFJ2R3GO9ACMNA']
2025-06-24 16:32:05,373 - INFO - 批量插入完成，共 2 条记录
2025-06-24 16:32:05,373 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-24 16:32:05,373 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-06-24 16:32:05,373 - INFO - 同步完成
2025-06-24 19:30:33,661 - INFO - 使用默认增量同步（当天更新数据）
2025-06-24 19:30:33,661 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-24 19:30:33,661 - INFO - 查询参数: ('2025-06-24',)
2025-06-24 19:30:33,786 - INFO - MySQL查询成功，增量数据（日期: 2025-06-24），共获取 139 条记录
2025-06-24 19:30:33,786 - INFO - 获取到 1 个日期需要处理: ['2025-06-23']
2025-06-24 19:30:33,786 - INFO - 开始处理日期: 2025-06-23
2025-06-24 19:30:33,802 - INFO - Request Parameters - Page 1:
2025-06-24 19:30:33,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 19:30:33,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 19:30:41,911 - ERROR - 处理日期 2025-06-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DE7E794C-FE3F-7DCC-8A72-C2F2FA818519 Response: {'code': 'ServiceUnavailable', 'requestid': 'DE7E794C-FE3F-7DCC-8A72-C2F2FA818519', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DE7E794C-FE3F-7DCC-8A72-C2F2FA818519)
2025-06-24 19:30:41,911 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-24 19:31:41,926 - INFO - 开始同步昨天与今天的销售数据: 2025-06-23 至 2025-06-24
2025-06-24 19:31:41,926 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-24 19:31:41,926 - INFO - 查询参数: ('2025-06-23', '2025-06-24')
2025-06-24 19:31:42,067 - INFO - MySQL查询成功，时间段: 2025-06-23 至 2025-06-24，共获取 516 条记录
2025-06-24 19:31:42,067 - INFO - 获取到 1 个日期需要处理: ['2025-06-23']
2025-06-24 19:31:42,067 - INFO - 开始处理日期: 2025-06-23
2025-06-24 19:31:42,067 - INFO - Request Parameters - Page 1:
2025-06-24 19:31:42,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 19:31:42,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 19:31:42,801 - INFO - Response - Page 1:
2025-06-24 19:31:42,801 - INFO - 第 1 页获取到 50 条记录
2025-06-24 19:31:43,317 - INFO - Request Parameters - Page 2:
2025-06-24 19:31:43,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 19:31:43,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 19:31:44,004 - INFO - Response - Page 2:
2025-06-24 19:31:44,004 - INFO - 第 2 页获取到 50 条记录
2025-06-24 19:31:44,520 - INFO - Request Parameters - Page 3:
2025-06-24 19:31:44,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 19:31:44,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 19:31:50,176 - INFO - Response - Page 3:
2025-06-24 19:31:50,176 - INFO - 第 3 页获取到 50 条记录
2025-06-24 19:31:50,676 - INFO - Request Parameters - Page 4:
2025-06-24 19:31:50,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 19:31:50,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 19:31:51,332 - INFO - Response - Page 4:
2025-06-24 19:31:51,332 - INFO - 第 4 页获取到 50 条记录
2025-06-24 19:31:51,848 - INFO - Request Parameters - Page 5:
2025-06-24 19:31:51,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 19:31:51,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 19:31:52,551 - INFO - Response - Page 5:
2025-06-24 19:31:52,551 - INFO - 第 5 页获取到 50 条记录
2025-06-24 19:31:53,067 - INFO - Request Parameters - Page 6:
2025-06-24 19:31:53,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 19:31:53,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 19:31:53,801 - INFO - Response - Page 6:
2025-06-24 19:31:53,801 - INFO - 第 6 页获取到 50 条记录
2025-06-24 19:31:54,301 - INFO - Request Parameters - Page 7:
2025-06-24 19:31:54,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 19:31:54,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 19:31:54,926 - INFO - Response - Page 7:
2025-06-24 19:31:54,926 - INFO - 第 7 页获取到 50 条记录
2025-06-24 19:31:55,426 - INFO - Request Parameters - Page 8:
2025-06-24 19:31:55,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 19:31:55,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 19:31:56,114 - INFO - Response - Page 8:
2025-06-24 19:31:56,114 - INFO - 第 8 页获取到 50 条记录
2025-06-24 19:31:56,629 - INFO - Request Parameters - Page 9:
2025-06-24 19:31:56,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 19:31:56,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 19:31:57,239 - INFO - Response - Page 9:
2025-06-24 19:31:57,239 - INFO - 第 9 页获取到 50 条记录
2025-06-24 19:31:57,754 - INFO - Request Parameters - Page 10:
2025-06-24 19:31:57,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 19:31:57,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 19:31:58,379 - INFO - Response - Page 10:
2025-06-24 19:31:58,395 - INFO - 第 10 页获取到 50 条记录
2025-06-24 19:31:58,911 - INFO - Request Parameters - Page 11:
2025-06-24 19:31:58,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 19:31:58,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 19:31:59,426 - INFO - Response - Page 11:
2025-06-24 19:31:59,426 - INFO - 第 11 页获取到 4 条记录
2025-06-24 19:31:59,942 - INFO - 查询完成，共获取到 504 条记录
2025-06-24 19:31:59,942 - INFO - 获取到 504 条表单数据
2025-06-24 19:31:59,942 - INFO - 当前日期 2025-06-23 有 505 条MySQL数据需要处理
2025-06-24 19:31:59,957 - INFO - 开始批量插入 1 条新记录
2025-06-24 19:32:00,114 - INFO - 批量插入响应状态码: 200
2025-06-24 19:32:00,114 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 11:32:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8292C6A1-ACCC-7EFD-AB2D-7FDF2160BA56', 'x-acs-trace-id': 'cd9fbfb0c3cceff773ace3bb20bbf33e', 'etag': '6EFGXOUcQKAZlPSAffOpX9g0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 19:32:00,114 - INFO - 批量插入响应体: {'result': ['FINST-UW966371CTJWXM8ZCDI1BCN8YTI63EVZ3GACMOG']}
2025-06-24 19:32:00,114 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-24 19:32:00,114 - INFO - 成功插入的数据ID: ['FINST-UW966371CTJWXM8ZCDI1BCN8YTI63EVZ3GACMOG']
2025-06-24 19:32:05,129 - INFO - 批量插入完成，共 1 条记录
2025-06-24 19:32:05,129 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-24 19:32:05,129 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-06-24 19:32:05,129 - INFO - 同步完成
2025-06-24 22:30:33,602 - INFO - 使用默认增量同步（当天更新数据）
2025-06-24 22:30:33,602 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-24 22:30:33,602 - INFO - 查询参数: ('2025-06-24',)
2025-06-24 22:30:33,742 - INFO - MySQL查询成功，增量数据（日期: 2025-06-24），共获取 224 条记录
2025-06-24 22:30:33,742 - INFO - 获取到 2 个日期需要处理: ['2025-06-23', '2025-06-24']
2025-06-24 22:30:33,742 - INFO - 开始处理日期: 2025-06-23
2025-06-24 22:30:33,742 - INFO - Request Parameters - Page 1:
2025-06-24 22:30:33,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:30:33,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:30:41,852 - ERROR - 处理日期 2025-06-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6516960F-9F3C-780B-8F66-71D0612AFD5E Response: {'code': 'ServiceUnavailable', 'requestid': '6516960F-9F3C-780B-8F66-71D0612AFD5E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6516960F-9F3C-780B-8F66-71D0612AFD5E)
2025-06-24 22:30:41,852 - INFO - 开始处理日期: 2025-06-24
2025-06-24 22:30:41,852 - INFO - Request Parameters - Page 1:
2025-06-24 22:30:41,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:30:41,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:30:47,570 - INFO - Response - Page 1:
2025-06-24 22:30:47,570 - INFO - 查询完成，共获取到 0 条记录
2025-06-24 22:30:47,570 - INFO - 获取到 0 条表单数据
2025-06-24 22:30:47,570 - INFO - 当前日期 2025-06-24 有 46 条MySQL数据需要处理
2025-06-24 22:30:47,570 - INFO - 开始批量插入 46 条新记录
2025-06-24 22:30:47,836 - INFO - 批量插入响应状态码: 200
2025-06-24 22:30:47,836 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 14:30:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2190', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BFEC1B48-E559-7AA3-9C90-D31F1129C17A', 'x-acs-trace-id': '3550faf999fd5b2d983bd6df896c6a1a', 'etag': '2I9nxXRxIzxNLa31+VbeQ7A0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 22:30:47,836 - INFO - 批量插入响应体: {'result': ['FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM6', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM7', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM8', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM9', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMA', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMB', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMC', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMD', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACME', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMF', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMG', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMH', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMI', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMJ', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMK', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACML', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMM', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMN', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMO', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMP', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMQ', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMR', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMS', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMT', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMU', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMV', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMW', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMX', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMY', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMZ', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM01', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM11', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM21', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM31', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM41', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM51', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM61', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM71', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM81', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM91', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMA1', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMB1', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMC1', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMD1', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACME1', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMF1']}
2025-06-24 22:30:47,836 - INFO - 批量插入表单数据成功，批次 1，共 46 条记录
2025-06-24 22:30:47,836 - INFO - 成功插入的数据ID: ['FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM6', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM7', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM8', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM9', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMA', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMB', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMC', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMD', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACME', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMF', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMG', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMH', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMI', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMJ', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMK', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACML', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMM', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMN', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMO', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMP', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMQ', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMR', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMS', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMT', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMU', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMV', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMW', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMX', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMY', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMZ', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM01', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM11', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM21', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM31', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM41', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM51', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM61', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM71', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM81', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACM91', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMA1', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMB1', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMC1', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMD1', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACME1', 'FINST-DOA66K9117LWXEQKFDBOI6HL5K3L2HHXHMACMF1']
2025-06-24 22:30:52,852 - INFO - 批量插入完成，共 46 条记录
2025-06-24 22:30:52,852 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 46 条，错误: 0 条
2025-06-24 22:30:52,852 - INFO - 数据同步完成！更新: 0 条，插入: 46 条，错误: 1 条
2025-06-24 22:31:52,867 - INFO - 开始同步昨天与今天的销售数据: 2025-06-23 至 2025-06-24
2025-06-24 22:31:52,867 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-24 22:31:52,867 - INFO - 查询参数: ('2025-06-23', '2025-06-24')
2025-06-24 22:31:53,008 - INFO - MySQL查询成功，时间段: 2025-06-23 至 2025-06-24，共获取 605 条记录
2025-06-24 22:31:53,008 - INFO - 获取到 2 个日期需要处理: ['2025-06-23', '2025-06-24']
2025-06-24 22:31:53,008 - INFO - 开始处理日期: 2025-06-23
2025-06-24 22:31:53,023 - INFO - Request Parameters - Page 1:
2025-06-24 22:31:53,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:31:53,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:31:53,789 - INFO - Response - Page 1:
2025-06-24 22:31:53,789 - INFO - 第 1 页获取到 50 条记录
2025-06-24 22:31:54,289 - INFO - Request Parameters - Page 2:
2025-06-24 22:31:54,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:31:54,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:31:54,929 - INFO - Response - Page 2:
2025-06-24 22:31:54,929 - INFO - 第 2 页获取到 50 条记录
2025-06-24 22:31:55,445 - INFO - Request Parameters - Page 3:
2025-06-24 22:31:55,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:31:55,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:31:56,132 - INFO - Response - Page 3:
2025-06-24 22:31:56,132 - INFO - 第 3 页获取到 50 条记录
2025-06-24 22:31:56,648 - INFO - Request Parameters - Page 4:
2025-06-24 22:31:56,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:31:56,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:31:57,382 - INFO - Response - Page 4:
2025-06-24 22:31:57,382 - INFO - 第 4 页获取到 50 条记录
2025-06-24 22:31:57,882 - INFO - Request Parameters - Page 5:
2025-06-24 22:31:57,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:31:57,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:31:58,539 - INFO - Response - Page 5:
2025-06-24 22:31:58,539 - INFO - 第 5 页获取到 50 条记录
2025-06-24 22:31:59,054 - INFO - Request Parameters - Page 6:
2025-06-24 22:31:59,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:31:59,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:31:59,695 - INFO - Response - Page 6:
2025-06-24 22:31:59,695 - INFO - 第 6 页获取到 50 条记录
2025-06-24 22:32:00,211 - INFO - Request Parameters - Page 7:
2025-06-24 22:32:00,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:32:00,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:32:00,820 - INFO - Response - Page 7:
2025-06-24 22:32:00,820 - INFO - 第 7 页获取到 50 条记录
2025-06-24 22:32:01,336 - INFO - Request Parameters - Page 8:
2025-06-24 22:32:01,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:32:01,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:32:01,961 - INFO - Response - Page 8:
2025-06-24 22:32:01,961 - INFO - 第 8 页获取到 50 条记录
2025-06-24 22:32:02,476 - INFO - Request Parameters - Page 9:
2025-06-24 22:32:02,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:32:02,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:32:03,179 - INFO - Response - Page 9:
2025-06-24 22:32:03,179 - INFO - 第 9 页获取到 50 条记录
2025-06-24 22:32:03,695 - INFO - Request Parameters - Page 10:
2025-06-24 22:32:03,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:32:03,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:32:04,398 - INFO - Response - Page 10:
2025-06-24 22:32:04,398 - INFO - 第 10 页获取到 50 条记录
2025-06-24 22:32:04,914 - INFO - Request Parameters - Page 11:
2025-06-24 22:32:04,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:32:04,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:32:05,492 - INFO - Response - Page 11:
2025-06-24 22:32:05,492 - INFO - 第 11 页获取到 5 条记录
2025-06-24 22:32:06,007 - INFO - 查询完成，共获取到 505 条记录
2025-06-24 22:32:06,007 - INFO - 获取到 505 条表单数据
2025-06-24 22:32:06,007 - INFO - 当前日期 2025-06-23 有 544 条MySQL数据需要处理
2025-06-24 22:32:06,023 - INFO - 开始批量插入 39 条新记录
2025-06-24 22:32:06,304 - INFO - 批量插入响应状态码: 200
2025-06-24 22:32:06,304 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 14:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1863', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4EBFEE31-84DD-77B6-89C0-16318F92D4C6', 'x-acs-trace-id': '06497bcd10feb95e56914a8a24078ce3', 'etag': '10UZ0tU07+ZbyTn59ldC1/g3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 22:32:06,304 - INFO - 批量插入响应体: {'result': ['FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32O1MJMACMF', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32O1MJMACMG', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32O1MJMACMH', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32O1MJMACMI', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32O1MJMACMJ', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32O1MJMACMK', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACML', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMM', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMN', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMO', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMP', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMQ', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMR', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMS', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMT', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMU', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMV', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMW', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMX', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMY', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMZ', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM01', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM11', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM21', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM31', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM41', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM51', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM61', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM71', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM81', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM91', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMA1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMB1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMC1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMD1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACME1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMF1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMG1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMH1']}
2025-06-24 22:32:06,304 - INFO - 批量插入表单数据成功，批次 1，共 39 条记录
2025-06-24 22:32:06,304 - INFO - 成功插入的数据ID: ['FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32O1MJMACMF', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32O1MJMACMG', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32O1MJMACMH', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32O1MJMACMI', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32O1MJMACMJ', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32O1MJMACMK', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACML', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMM', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMN', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMO', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMP', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMQ', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMR', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMS', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMT', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMU', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMV', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMW', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMX', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMY', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMZ', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM01', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM11', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM21', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM31', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM41', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM51', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM61', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM71', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM81', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACM91', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMA1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMB1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMC1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMD1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACME1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMF1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMG1', 'FINST-8SG66JA1T6LWMQC8F9A8EA9S9NN32P1MJMACMH1']
2025-06-24 22:32:11,320 - INFO - 批量插入完成，共 39 条记录
2025-06-24 22:32:11,320 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 39 条，错误: 0 条
2025-06-24 22:32:11,320 - INFO - 开始处理日期: 2025-06-24
2025-06-24 22:32:11,320 - INFO - Request Parameters - Page 1:
2025-06-24 22:32:11,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-24 22:32:11,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-24 22:32:11,929 - INFO - Response - Page 1:
2025-06-24 22:32:11,929 - INFO - 第 1 页获取到 46 条记录
2025-06-24 22:32:12,429 - INFO - 查询完成，共获取到 46 条记录
2025-06-24 22:32:12,429 - INFO - 获取到 46 条表单数据
2025-06-24 22:32:12,429 - INFO - 当前日期 2025-06-24 有 49 条MySQL数据需要处理
2025-06-24 22:32:12,429 - INFO - 开始批量插入 3 条新记录
2025-06-24 22:32:12,585 - INFO - 批量插入响应状态码: 200
2025-06-24 22:32:12,585 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 24 Jun 2025 14:32:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '153', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '932D67F3-4CF3-7847-9D38-FAC8317438AF', 'x-acs-trace-id': 'b5696880bac2a3e1597425939de45141', 'etag': '1hXF1gctSEEHjhye7iYZXRg3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-24 22:32:12,585 - INFO - 批量插入响应体: {'result': ['FINST-AEF66BC1N6LWZOE679M4DDF6WF8R21WQJMACMD', 'FINST-AEF66BC1N6LWZOE679M4DDF6WF8R21WQJMACME', 'FINST-AEF66BC1N6LWZOE679M4DDF6WF8R21WQJMACMF']}
2025-06-24 22:32:12,585 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-06-24 22:32:12,585 - INFO - 成功插入的数据ID: ['FINST-AEF66BC1N6LWZOE679M4DDF6WF8R21WQJMACMD', 'FINST-AEF66BC1N6LWZOE679M4DDF6WF8R21WQJMACME', 'FINST-AEF66BC1N6LWZOE679M4DDF6WF8R21WQJMACMF']
2025-06-24 22:32:17,601 - INFO - 批量插入完成，共 3 条记录
2025-06-24 22:32:17,601 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-06-24 22:32:17,601 - INFO - 数据同步完成！更新: 0 条，插入: 42 条，错误: 0 条
2025-06-24 22:32:17,601 - INFO - 同步完成
