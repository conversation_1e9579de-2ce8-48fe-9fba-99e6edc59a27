2025-06-28 00:00:03,131 - INFO - =================使用默认全量同步=============
2025-06-28 00:00:04,947 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-28 00:00:04,948 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-28 00:00:04,982 - INFO - 开始处理日期: 2025-01
2025-06-28 00:00:04,985 - INFO - Request Parameters - Page 1:
2025-06-28 00:00:04,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:04,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:06,053 - INFO - Response - Page 1:
2025-06-28 00:00:06,256 - INFO - 第 1 页获取到 100 条记录
2025-06-28 00:00:06,256 - INFO - Request Parameters - Page 2:
2025-06-28 00:00:06,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:06,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:07,271 - INFO - Response - Page 2:
2025-06-28 00:00:07,475 - INFO - 第 2 页获取到 100 条记录
2025-06-28 00:00:07,475 - INFO - Request Parameters - Page 3:
2025-06-28 00:00:07,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:07,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:08,053 - INFO - Response - Page 3:
2025-06-28 00:00:08,256 - INFO - 第 3 页获取到 100 条记录
2025-06-28 00:00:08,256 - INFO - Request Parameters - Page 4:
2025-06-28 00:00:08,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:08,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:08,787 - INFO - Response - Page 4:
2025-06-28 00:00:08,990 - INFO - 第 4 页获取到 100 条记录
2025-06-28 00:00:08,990 - INFO - Request Parameters - Page 5:
2025-06-28 00:00:08,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:08,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:09,600 - INFO - Response - Page 5:
2025-06-28 00:00:09,803 - INFO - 第 5 页获取到 100 条记录
2025-06-28 00:00:09,803 - INFO - Request Parameters - Page 6:
2025-06-28 00:00:09,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:09,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:10,381 - INFO - Response - Page 6:
2025-06-28 00:00:10,584 - INFO - 第 6 页获取到 100 条记录
2025-06-28 00:00:10,584 - INFO - Request Parameters - Page 7:
2025-06-28 00:00:10,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:10,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:11,365 - INFO - Response - Page 7:
2025-06-28 00:00:11,568 - INFO - 第 7 页获取到 82 条记录
2025-06-28 00:00:11,568 - INFO - 查询完成，共获取到 682 条记录
2025-06-28 00:00:11,568 - INFO - 获取到 682 条表单数据
2025-06-28 00:00:11,584 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-28 00:00:11,584 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 00:00:11,584 - INFO - 开始处理日期: 2025-02
2025-06-28 00:00:11,584 - INFO - Request Parameters - Page 1:
2025-06-28 00:00:11,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:11,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:12,100 - INFO - Response - Page 1:
2025-06-28 00:00:12,303 - INFO - 第 1 页获取到 100 条记录
2025-06-28 00:00:12,303 - INFO - Request Parameters - Page 2:
2025-06-28 00:00:12,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:12,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:12,912 - INFO - Response - Page 2:
2025-06-28 00:00:13,115 - INFO - 第 2 页获取到 100 条记录
2025-06-28 00:00:13,115 - INFO - Request Parameters - Page 3:
2025-06-28 00:00:13,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:13,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:13,615 - INFO - Response - Page 3:
2025-06-28 00:00:13,818 - INFO - 第 3 页获取到 100 条记录
2025-06-28 00:00:13,818 - INFO - Request Parameters - Page 4:
2025-06-28 00:00:13,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:13,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:14,396 - INFO - Response - Page 4:
2025-06-28 00:00:14,600 - INFO - 第 4 页获取到 100 条记录
2025-06-28 00:00:14,600 - INFO - Request Parameters - Page 5:
2025-06-28 00:00:14,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:14,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:15,068 - INFO - Response - Page 5:
2025-06-28 00:00:15,271 - INFO - 第 5 页获取到 100 条记录
2025-06-28 00:00:15,271 - INFO - Request Parameters - Page 6:
2025-06-28 00:00:15,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:15,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:15,771 - INFO - Response - Page 6:
2025-06-28 00:00:15,975 - INFO - 第 6 页获取到 100 条记录
2025-06-28 00:00:15,975 - INFO - Request Parameters - Page 7:
2025-06-28 00:00:15,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:15,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:16,475 - INFO - Response - Page 7:
2025-06-28 00:00:16,678 - INFO - 第 7 页获取到 70 条记录
2025-06-28 00:00:16,678 - INFO - 查询完成，共获取到 670 条记录
2025-06-28 00:00:16,678 - INFO - 获取到 670 条表单数据
2025-06-28 00:00:16,678 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-28 00:00:16,693 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 00:00:16,693 - INFO - 开始处理日期: 2025-03
2025-06-28 00:00:16,693 - INFO - Request Parameters - Page 1:
2025-06-28 00:00:16,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:16,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:17,334 - INFO - Response - Page 1:
2025-06-28 00:00:17,537 - INFO - 第 1 页获取到 100 条记录
2025-06-28 00:00:17,537 - INFO - Request Parameters - Page 2:
2025-06-28 00:00:17,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:17,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:18,100 - INFO - Response - Page 2:
2025-06-28 00:00:18,303 - INFO - 第 2 页获取到 100 条记录
2025-06-28 00:00:18,303 - INFO - Request Parameters - Page 3:
2025-06-28 00:00:18,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:18,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:18,881 - INFO - Response - Page 3:
2025-06-28 00:00:19,084 - INFO - 第 3 页获取到 100 条记录
2025-06-28 00:00:19,084 - INFO - Request Parameters - Page 4:
2025-06-28 00:00:19,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:19,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:19,553 - INFO - Response - Page 4:
2025-06-28 00:00:19,756 - INFO - 第 4 页获取到 100 条记录
2025-06-28 00:00:19,756 - INFO - Request Parameters - Page 5:
2025-06-28 00:00:19,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:19,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:20,287 - INFO - Response - Page 5:
2025-06-28 00:00:20,490 - INFO - 第 5 页获取到 100 条记录
2025-06-28 00:00:20,490 - INFO - Request Parameters - Page 6:
2025-06-28 00:00:20,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:20,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:21,146 - INFO - Response - Page 6:
2025-06-28 00:00:21,349 - INFO - 第 6 页获取到 100 条记录
2025-06-28 00:00:21,349 - INFO - Request Parameters - Page 7:
2025-06-28 00:00:21,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:21,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:21,740 - INFO - Response - Page 7:
2025-06-28 00:00:21,943 - INFO - 第 7 页获取到 61 条记录
2025-06-28 00:00:21,943 - INFO - 查询完成，共获取到 661 条记录
2025-06-28 00:00:21,943 - INFO - 获取到 661 条表单数据
2025-06-28 00:00:21,943 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-28 00:00:21,959 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 00:00:21,959 - INFO - 开始处理日期: 2025-04
2025-06-28 00:00:21,959 - INFO - Request Parameters - Page 1:
2025-06-28 00:00:21,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:21,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:22,568 - INFO - Response - Page 1:
2025-06-28 00:00:22,771 - INFO - 第 1 页获取到 100 条记录
2025-06-28 00:00:22,771 - INFO - Request Parameters - Page 2:
2025-06-28 00:00:22,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:22,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:23,287 - INFO - Response - Page 2:
2025-06-28 00:00:23,490 - INFO - 第 2 页获取到 100 条记录
2025-06-28 00:00:23,490 - INFO - Request Parameters - Page 3:
2025-06-28 00:00:23,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:23,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:23,928 - INFO - Response - Page 3:
2025-06-28 00:00:24,131 - INFO - 第 3 页获取到 100 条记录
2025-06-28 00:00:24,131 - INFO - Request Parameters - Page 4:
2025-06-28 00:00:24,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:24,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:24,646 - INFO - Response - Page 4:
2025-06-28 00:00:24,849 - INFO - 第 4 页获取到 100 条记录
2025-06-28 00:00:24,849 - INFO - Request Parameters - Page 5:
2025-06-28 00:00:24,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:24,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:25,396 - INFO - Response - Page 5:
2025-06-28 00:00:25,599 - INFO - 第 5 页获取到 100 条记录
2025-06-28 00:00:25,599 - INFO - Request Parameters - Page 6:
2025-06-28 00:00:25,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:25,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:26,318 - INFO - Response - Page 6:
2025-06-28 00:00:26,521 - INFO - 第 6 页获取到 100 条记录
2025-06-28 00:00:26,521 - INFO - Request Parameters - Page 7:
2025-06-28 00:00:26,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:26,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:26,928 - INFO - Response - Page 7:
2025-06-28 00:00:27,131 - INFO - 第 7 页获取到 56 条记录
2025-06-28 00:00:27,131 - INFO - 查询完成，共获取到 656 条记录
2025-06-28 00:00:27,131 - INFO - 获取到 656 条表单数据
2025-06-28 00:00:27,131 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-28 00:00:27,146 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 00:00:27,146 - INFO - 开始处理日期: 2025-05
2025-06-28 00:00:27,146 - INFO - Request Parameters - Page 1:
2025-06-28 00:00:27,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:27,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:27,678 - INFO - Response - Page 1:
2025-06-28 00:00:27,881 - INFO - 第 1 页获取到 100 条记录
2025-06-28 00:00:27,881 - INFO - Request Parameters - Page 2:
2025-06-28 00:00:27,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:27,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:28,365 - INFO - Response - Page 2:
2025-06-28 00:00:28,568 - INFO - 第 2 页获取到 100 条记录
2025-06-28 00:00:28,568 - INFO - Request Parameters - Page 3:
2025-06-28 00:00:28,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:28,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:29,021 - INFO - Response - Page 3:
2025-06-28 00:00:29,224 - INFO - 第 3 页获取到 100 条记录
2025-06-28 00:00:29,224 - INFO - Request Parameters - Page 4:
2025-06-28 00:00:29,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:29,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:29,740 - INFO - Response - Page 4:
2025-06-28 00:00:29,943 - INFO - 第 4 页获取到 100 条记录
2025-06-28 00:00:29,943 - INFO - Request Parameters - Page 5:
2025-06-28 00:00:29,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:29,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:30,428 - INFO - Response - Page 5:
2025-06-28 00:00:30,631 - INFO - 第 5 页获取到 100 条记录
2025-06-28 00:00:30,631 - INFO - Request Parameters - Page 6:
2025-06-28 00:00:30,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:30,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:31,178 - INFO - Response - Page 6:
2025-06-28 00:00:31,381 - INFO - 第 6 页获取到 100 条记录
2025-06-28 00:00:31,381 - INFO - Request Parameters - Page 7:
2025-06-28 00:00:31,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:31,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:31,803 - INFO - Response - Page 7:
2025-06-28 00:00:32,006 - INFO - 第 7 页获取到 65 条记录
2025-06-28 00:00:32,006 - INFO - 查询完成，共获取到 665 条记录
2025-06-28 00:00:32,006 - INFO - 获取到 665 条表单数据
2025-06-28 00:00:32,006 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-28 00:00:32,021 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 00:00:32,021 - INFO - 开始处理日期: 2025-06
2025-06-28 00:00:32,021 - INFO - Request Parameters - Page 1:
2025-06-28 00:00:32,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:32,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:32,537 - INFO - Response - Page 1:
2025-06-28 00:00:32,740 - INFO - 第 1 页获取到 100 条记录
2025-06-28 00:00:32,740 - INFO - Request Parameters - Page 2:
2025-06-28 00:00:32,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:32,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:33,271 - INFO - Response - Page 2:
2025-06-28 00:00:33,474 - INFO - 第 2 页获取到 100 条记录
2025-06-28 00:00:33,474 - INFO - Request Parameters - Page 3:
2025-06-28 00:00:33,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:33,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:34,037 - INFO - Response - Page 3:
2025-06-28 00:00:34,240 - INFO - 第 3 页获取到 100 条记录
2025-06-28 00:00:34,240 - INFO - Request Parameters - Page 4:
2025-06-28 00:00:34,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:34,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:34,678 - INFO - Response - Page 4:
2025-06-28 00:00:34,881 - INFO - 第 4 页获取到 100 条记录
2025-06-28 00:00:34,881 - INFO - Request Parameters - Page 5:
2025-06-28 00:00:34,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:34,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:35,318 - INFO - Response - Page 5:
2025-06-28 00:00:35,521 - INFO - 第 5 页获取到 100 条记录
2025-06-28 00:00:35,521 - INFO - Request Parameters - Page 6:
2025-06-28 00:00:35,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:35,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:35,959 - INFO - Response - Page 6:
2025-06-28 00:00:36,162 - INFO - 第 6 页获取到 100 条记录
2025-06-28 00:00:36,162 - INFO - Request Parameters - Page 7:
2025-06-28 00:00:36,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 00:00:36,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 00:00:36,553 - INFO - Response - Page 7:
2025-06-28 00:00:36,756 - INFO - 第 7 页获取到 29 条记录
2025-06-28 00:00:36,756 - INFO - 查询完成，共获取到 629 条记录
2025-06-28 00:00:36,756 - INFO - 获取到 629 条表单数据
2025-06-28 00:00:36,756 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-28 00:00:36,756 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMRX
2025-06-28 00:00:37,271 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMRX
2025-06-28 00:00:37,271 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108769.2, 'new_value': 122229.2}, {'field': 'total_amount', 'old_value': 108769.2, 'new_value': 122229.2}, {'field': 'order_count', 'old_value': 23, 'new_value': 31}]
2025-06-28 00:00:37,271 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-28 00:00:37,771 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-28 00:00:37,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26645.64, 'new_value': 26939.64}, {'field': 'total_amount', 'old_value': 26645.64, 'new_value': 26939.64}, {'field': 'order_count', 'old_value': 90, 'new_value': 95}]
2025-06-28 00:00:37,771 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-28 00:00:38,256 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-28 00:00:38,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51300.0, 'new_value': 53480.0}, {'field': 'total_amount', 'old_value': 63240.0, 'new_value': 65420.0}, {'field': 'order_count', 'old_value': 699, 'new_value': 726}]
2025-06-28 00:00:38,256 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-28 00:00:38,818 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-28 00:00:38,818 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49522.6, 'new_value': 51286.2}, {'field': 'total_amount', 'old_value': 49522.6, 'new_value': 51286.2}, {'field': 'order_count', 'old_value': 449, 'new_value': 456}]
2025-06-28 00:00:38,818 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-28 00:00:39,240 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-28 00:00:39,240 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85360.0, 'new_value': 87504.0}, {'field': 'offline_amount', 'old_value': 104133.0, 'new_value': 108320.0}, {'field': 'total_amount', 'old_value': 189493.0, 'new_value': 195824.0}, {'field': 'order_count', 'old_value': 3984, 'new_value': 4138}]
2025-06-28 00:00:39,240 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-28 00:00:39,693 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-28 00:00:39,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148292.71, 'new_value': 158930.11}, {'field': 'total_amount', 'old_value': 175067.42, 'new_value': 185704.82}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-06-28 00:00:39,693 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-28 00:00:40,115 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-28 00:00:40,115 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 876996.0, 'new_value': 894299.0}, {'field': 'total_amount', 'old_value': 876996.0, 'new_value': 894299.0}, {'field': 'order_count', 'old_value': 186, 'new_value': 189}]
2025-06-28 00:00:40,115 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-28 00:00:40,568 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-28 00:00:40,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6670.0, 'new_value': 6738.0}, {'field': 'offline_amount', 'old_value': 55878.0, 'new_value': 56693.0}, {'field': 'total_amount', 'old_value': 62548.0, 'new_value': 63431.0}, {'field': 'order_count', 'old_value': 513, 'new_value': 523}]
2025-06-28 00:00:40,568 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-28 00:00:40,927 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-28 00:00:40,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66443.97, 'new_value': 68634.44}, {'field': 'offline_amount', 'old_value': 92254.44, 'new_value': 95900.77}, {'field': 'total_amount', 'old_value': 158698.41, 'new_value': 164535.21}, {'field': 'order_count', 'old_value': 5369, 'new_value': 5560}]
2025-06-28 00:00:40,927 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-28 00:00:41,365 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-28 00:00:41,365 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121000.0, 'new_value': 123400.0}, {'field': 'total_amount', 'old_value': 121000.0, 'new_value': 123400.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-06-28 00:00:41,365 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-28 00:00:41,802 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-28 00:00:41,802 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9355.9, 'new_value': 9652.9}, {'field': 'total_amount', 'old_value': 19734.9, 'new_value': 20031.9}, {'field': 'order_count', 'old_value': 65, 'new_value': 68}]
2025-06-28 00:00:41,802 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-28 00:00:42,318 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-28 00:00:42,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 203964.49, 'new_value': 214120.75}, {'field': 'offline_amount', 'old_value': 384099.11, 'new_value': 398487.28}, {'field': 'total_amount', 'old_value': 588063.6, 'new_value': 612608.03}, {'field': 'order_count', 'old_value': 4542, 'new_value': 4739}]
2025-06-28 00:00:42,318 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-28 00:00:42,787 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-28 00:00:42,787 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24181.01, 'new_value': 24676.01}, {'field': 'offline_amount', 'old_value': 3546.0, 'new_value': 3548.0}, {'field': 'total_amount', 'old_value': 27727.01, 'new_value': 28224.01}, {'field': 'order_count', 'old_value': 118, 'new_value': 119}]
2025-06-28 00:00:42,787 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-28 00:00:43,177 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-28 00:00:43,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 597791.0, 'new_value': 601289.0}, {'field': 'total_amount', 'old_value': 645376.0, 'new_value': 648874.0}, {'field': 'order_count', 'old_value': 204, 'new_value': 207}]
2025-06-28 00:00:43,177 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-28 00:00:43,584 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-28 00:00:43,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89907.0, 'new_value': 91705.0}, {'field': 'total_amount', 'old_value': 89907.0, 'new_value': 91705.0}, {'field': 'order_count', 'old_value': 323, 'new_value': 334}]
2025-06-28 00:00:43,584 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-28 00:00:44,037 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-28 00:00:44,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44413.0, 'new_value': 44935.0}, {'field': 'total_amount', 'old_value': 44413.0, 'new_value': 44935.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 64}]
2025-06-28 00:00:44,037 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-28 00:00:44,490 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-28 00:00:44,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 481432.0, 'new_value': 508147.0}, {'field': 'total_amount', 'old_value': 485348.0, 'new_value': 512063.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 81}]
2025-06-28 00:00:44,490 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-28 00:00:44,927 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-28 00:00:44,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211164.5, 'new_value': 220917.5}, {'field': 'total_amount', 'old_value': 211164.5, 'new_value': 220917.5}, {'field': 'order_count', 'old_value': 54, 'new_value': 56}]
2025-06-28 00:00:44,927 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-28 00:00:45,412 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-28 00:00:45,412 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8205.85, 'new_value': 8325.65}, {'field': 'offline_amount', 'old_value': 60080.0, 'new_value': 60587.0}, {'field': 'total_amount', 'old_value': 68285.85, 'new_value': 68912.65}, {'field': 'order_count', 'old_value': 1585, 'new_value': 1600}]
2025-06-28 00:00:45,412 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-28 00:00:45,896 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-28 00:00:45,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23607.0, 'new_value': 24848.0}, {'field': 'total_amount', 'old_value': 23607.0, 'new_value': 24848.0}, {'field': 'order_count', 'old_value': 118, 'new_value': 121}]
2025-06-28 00:00:45,896 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-28 00:00:46,349 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-28 00:00:46,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32954.0, 'new_value': 34734.0}, {'field': 'total_amount', 'old_value': 33431.0, 'new_value': 35211.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 79}]
2025-06-28 00:00:46,349 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-28 00:00:46,818 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-28 00:00:46,818 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105465.0, 'new_value': 110715.0}, {'field': 'total_amount', 'old_value': 109963.05, 'new_value': 115213.05}, {'field': 'order_count', 'old_value': 5613, 'new_value': 5614}]
2025-06-28 00:00:46,818 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-28 00:00:47,271 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-28 00:00:47,271 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60691.55, 'new_value': 68931.55}, {'field': 'offline_amount', 'old_value': 188352.0, 'new_value': 188353.0}, {'field': 'total_amount', 'old_value': 249043.55, 'new_value': 257284.55}, {'field': 'order_count', 'old_value': 97, 'new_value': 98}]
2025-06-28 00:00:47,271 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-28 00:00:47,724 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-28 00:00:47,724 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9328.0, 'new_value': 9696.0}, {'field': 'offline_amount', 'old_value': 52079.0, 'new_value': 53145.0}, {'field': 'total_amount', 'old_value': 61407.0, 'new_value': 62841.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 64}]
2025-06-28 00:00:47,724 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-28 00:00:48,131 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-28 00:00:48,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64381.0, 'new_value': 66832.0}, {'field': 'total_amount', 'old_value': 64381.0, 'new_value': 66832.0}, {'field': 'order_count', 'old_value': 170, 'new_value': 178}]
2025-06-28 00:00:48,131 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-28 00:00:48,552 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-28 00:00:48,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 259366.53, 'new_value': 265680.53}, {'field': 'total_amount', 'old_value': 259366.53, 'new_value': 265680.53}, {'field': 'order_count', 'old_value': 154, 'new_value': 158}]
2025-06-28 00:00:48,552 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-28 00:00:48,974 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-28 00:00:48,974 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58362.0, 'new_value': 59579.0}, {'field': 'total_amount', 'old_value': 58362.0, 'new_value': 59579.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 95}]
2025-06-28 00:00:48,974 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-28 00:00:49,381 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-28 00:00:49,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77307.08, 'new_value': 79874.22}, {'field': 'total_amount', 'old_value': 77307.08, 'new_value': 79874.22}, {'field': 'order_count', 'old_value': 2505, 'new_value': 2591}]
2025-06-28 00:00:49,381 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-28 00:00:49,818 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-28 00:00:49,818 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8602.5, 'new_value': 8969.5}, {'field': 'offline_amount', 'old_value': 69905.9, 'new_value': 76895.9}, {'field': 'total_amount', 'old_value': 78508.4, 'new_value': 85865.4}, {'field': 'order_count', 'old_value': 69, 'new_value': 72}]
2025-06-28 00:00:49,818 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-28 00:00:50,256 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-28 00:00:50,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 284815.1, 'new_value': 294388.0}, {'field': 'total_amount', 'old_value': 284815.1, 'new_value': 294388.0}, {'field': 'order_count', 'old_value': 2900, 'new_value': 3028}]
2025-06-28 00:00:50,256 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-28 00:00:50,662 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-28 00:00:50,662 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62117.5, 'new_value': 63894.5}, {'field': 'total_amount', 'old_value': 68742.9, 'new_value': 70519.9}, {'field': 'order_count', 'old_value': 168, 'new_value': 173}]
2025-06-28 00:00:50,662 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8R
2025-06-28 00:00:51,162 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8R
2025-06-28 00:00:51,162 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4746000.0, 'new_value': 5095900.0}, {'field': 'total_amount', 'old_value': 4746000.0, 'new_value': 5095900.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-28 00:00:51,162 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-28 00:00:51,615 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-28 00:00:51,615 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 836772.0, 'new_value': 874815.0}, {'field': 'total_amount', 'old_value': 909364.0, 'new_value': 947407.0}, {'field': 'order_count', 'old_value': 974, 'new_value': 1014}]
2025-06-28 00:00:51,615 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJR
2025-06-28 00:00:52,006 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJR
2025-06-28 00:00:52,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73472.0, 'new_value': 84293.0}, {'field': 'total_amount', 'old_value': 73472.0, 'new_value': 84293.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 29}]
2025-06-28 00:00:52,006 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-28 00:00:52,506 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-28 00:00:52,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 356046.0, 'new_value': 373508.0}, {'field': 'total_amount', 'old_value': 356046.0, 'new_value': 373508.0}]
2025-06-28 00:00:52,506 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-28 00:00:52,927 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-28 00:00:52,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102430.28, 'new_value': 107696.86}, {'field': 'offline_amount', 'old_value': 51524.93, 'new_value': 52972.52}, {'field': 'total_amount', 'old_value': 153955.21, 'new_value': 160669.38}, {'field': 'order_count', 'old_value': 8880, 'new_value': 9269}]
2025-06-28 00:00:52,927 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-28 00:00:53,396 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-28 00:00:53,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107326.4, 'new_value': 111916.0}, {'field': 'total_amount', 'old_value': 107326.4, 'new_value': 111916.0}, {'field': 'order_count', 'old_value': 269, 'new_value': 279}]
2025-06-28 00:00:53,396 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRR
2025-06-28 00:00:53,865 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRR
2025-06-28 00:00:53,865 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137586.0, 'new_value': 146584.0}, {'field': 'total_amount', 'old_value': 137587.0, 'new_value': 146585.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 29}]
2025-06-28 00:00:53,865 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-28 00:00:54,287 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-28 00:00:54,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53071.4, 'new_value': 53271.4}, {'field': 'total_amount', 'old_value': 53071.4, 'new_value': 53271.4}, {'field': 'order_count', 'old_value': 322, 'new_value': 325}]
2025-06-28 00:00:54,287 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-28 00:00:54,740 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-28 00:00:54,740 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60378.32, 'new_value': 63123.54}, {'field': 'offline_amount', 'old_value': 78719.49, 'new_value': 80791.74}, {'field': 'total_amount', 'old_value': 139097.81, 'new_value': 143915.28}, {'field': 'order_count', 'old_value': 5496, 'new_value': 5705}]
2025-06-28 00:00:54,740 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-28 00:00:55,193 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-28 00:00:55,193 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88890.61, 'new_value': 92021.01}, {'field': 'total_amount', 'old_value': 88890.61, 'new_value': 92021.01}, {'field': 'order_count', 'old_value': 3328, 'new_value': 3455}]
2025-06-28 00:00:55,193 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-28 00:00:55,662 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-28 00:00:55,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43739.81, 'new_value': 46352.21}, {'field': 'offline_amount', 'old_value': 19576.06, 'new_value': 20144.43}, {'field': 'total_amount', 'old_value': 63315.87, 'new_value': 66496.64}, {'field': 'order_count', 'old_value': 2858, 'new_value': 2988}]
2025-06-28 00:00:55,662 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-28 00:00:56,131 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-28 00:00:56,131 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18408.09, 'new_value': 19294.13}, {'field': 'offline_amount', 'old_value': 10962.29, 'new_value': 11412.79}, {'field': 'total_amount', 'old_value': 29370.38, 'new_value': 30706.92}, {'field': 'order_count', 'old_value': 2386, 'new_value': 2500}]
2025-06-28 00:00:56,131 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-28 00:00:56,552 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-28 00:00:56,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42388.66, 'new_value': 44234.65}, {'field': 'offline_amount', 'old_value': 271839.7, 'new_value': 283834.39}, {'field': 'total_amount', 'old_value': 314228.36, 'new_value': 328069.04}, {'field': 'order_count', 'old_value': 36314, 'new_value': 36618}]
2025-06-28 00:00:56,568 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-28 00:00:56,943 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-28 00:00:56,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 430883.0, 'new_value': 433860.0}, {'field': 'total_amount', 'old_value': 440683.0, 'new_value': 443660.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 100}]
2025-06-28 00:00:56,943 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-28 00:00:57,396 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-28 00:00:57,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147412.51, 'new_value': 153796.01}, {'field': 'total_amount', 'old_value': 147412.51, 'new_value': 153796.01}, {'field': 'order_count', 'old_value': 331, 'new_value': 334}]
2025-06-28 00:00:57,396 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-28 00:00:57,865 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-28 00:00:57,865 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126096.0, 'new_value': 131305.0}, {'field': 'total_amount', 'old_value': 130663.0, 'new_value': 135872.0}, {'field': 'order_count', 'old_value': 4352, 'new_value': 4359}]
2025-06-28 00:00:57,865 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-28 00:00:58,318 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-28 00:00:58,318 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38494.0, 'new_value': 38991.0}, {'field': 'total_amount', 'old_value': 39222.0, 'new_value': 39719.0}, {'field': 'order_count', 'old_value': 146, 'new_value': 149}]
2025-06-28 00:00:58,318 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-28 00:00:58,755 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-28 00:00:58,755 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81217.32, 'new_value': 84448.17}, {'field': 'offline_amount', 'old_value': 328478.81, 'new_value': 342341.05}, {'field': 'total_amount', 'old_value': 409696.13, 'new_value': 426789.22}, {'field': 'order_count', 'old_value': 4687, 'new_value': 4855}]
2025-06-28 00:00:58,755 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-28 00:00:59,224 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-28 00:00:59,224 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10260.27, 'new_value': 10738.27}, {'field': 'offline_amount', 'old_value': 32094.03, 'new_value': 33463.83}, {'field': 'total_amount', 'old_value': 42354.3, 'new_value': 44202.1}, {'field': 'order_count', 'old_value': 1482, 'new_value': 1543}]
2025-06-28 00:00:59,224 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-28 00:00:59,677 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-28 00:00:59,677 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38837.22, 'new_value': 40795.93}, {'field': 'offline_amount', 'old_value': 34295.94, 'new_value': 35295.94}, {'field': 'total_amount', 'old_value': 73133.16, 'new_value': 76091.87}, {'field': 'order_count', 'old_value': 3503, 'new_value': 3683}]
2025-06-28 00:00:59,677 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-28 00:01:00,130 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-28 00:01:00,130 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81102.48, 'new_value': 85043.36}, {'field': 'offline_amount', 'old_value': 32278.81, 'new_value': 32910.85}, {'field': 'total_amount', 'old_value': 113381.29, 'new_value': 117954.21}, {'field': 'order_count', 'old_value': 6503, 'new_value': 6712}]
2025-06-28 00:01:00,130 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXS
2025-06-28 00:01:00,630 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXS
2025-06-28 00:01:00,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202832.0, 'new_value': 203520.0}, {'field': 'total_amount', 'old_value': 202832.0, 'new_value': 203520.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-06-28 00:01:00,646 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-28 00:01:01,099 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-28 00:01:01,099 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43858.0, 'new_value': 46958.0}, {'field': 'offline_amount', 'old_value': 149921.07, 'new_value': 156547.07}, {'field': 'total_amount', 'old_value': 193779.07, 'new_value': 203505.07}, {'field': 'order_count', 'old_value': 274, 'new_value': 286}]
2025-06-28 00:01:01,099 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-28 00:01:01,521 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-28 00:01:01,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 477903.58, 'new_value': 496495.01}, {'field': 'total_amount', 'old_value': 477903.58, 'new_value': 496495.01}, {'field': 'order_count', 'old_value': 6984, 'new_value': 7248}]
2025-06-28 00:01:01,521 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-28 00:01:02,005 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-28 00:01:02,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39373.0, 'new_value': 40627.3}, {'field': 'total_amount', 'old_value': 40780.0, 'new_value': 42034.3}, {'field': 'order_count', 'old_value': 133, 'new_value': 136}]
2025-06-28 00:01:02,005 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-28 00:01:02,459 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-28 00:01:02,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 641728.0, 'new_value': 666918.0}, {'field': 'total_amount', 'old_value': 641728.0, 'new_value': 666918.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 66}]
2025-06-28 00:01:02,459 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-28 00:01:02,849 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-28 00:01:02,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204199.27, 'new_value': 216579.35}, {'field': 'total_amount', 'old_value': 263880.18, 'new_value': 276260.26}, {'field': 'order_count', 'old_value': 11793, 'new_value': 12304}]
2025-06-28 00:01:02,849 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-28 00:01:03,302 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-28 00:01:03,302 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80972.37, 'new_value': 83894.62}, {'field': 'offline_amount', 'old_value': 64425.33, 'new_value': 66392.95}, {'field': 'total_amount', 'old_value': 145397.7, 'new_value': 150287.57}, {'field': 'order_count', 'old_value': 6796, 'new_value': 7004}]
2025-06-28 00:01:03,302 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-28 00:01:03,724 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-28 00:01:03,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46349.4, 'new_value': 48025.4}, {'field': 'total_amount', 'old_value': 46349.4, 'new_value': 48025.4}, {'field': 'order_count', 'old_value': 617, 'new_value': 634}]
2025-06-28 00:01:03,724 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-28 00:01:04,349 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-28 00:01:04,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37634.04, 'new_value': 38981.8}, {'field': 'total_amount', 'old_value': 39845.28, 'new_value': 41193.04}, {'field': 'order_count', 'old_value': 168, 'new_value': 172}]
2025-06-28 00:01:04,349 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-28 00:01:04,787 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-28 00:01:04,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15712.0, 'new_value': 15910.0}, {'field': 'total_amount', 'old_value': 15712.0, 'new_value': 15910.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 182}]
2025-06-28 00:01:04,787 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-28 00:01:05,271 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-28 00:01:05,271 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59204.31, 'new_value': 61484.82}, {'field': 'offline_amount', 'old_value': 220649.31, 'new_value': 227010.58}, {'field': 'total_amount', 'old_value': 279853.62, 'new_value': 288495.4}, {'field': 'order_count', 'old_value': 6363, 'new_value': 6602}]
2025-06-28 00:01:05,271 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-28 00:01:05,865 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-28 00:01:05,865 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 521350.0, 'new_value': 556774.0}, {'field': 'total_amount', 'old_value': 521350.0, 'new_value': 556774.0}, {'field': 'order_count', 'old_value': 123, 'new_value': 129}]
2025-06-28 00:01:05,865 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-28 00:01:06,318 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-28 00:01:06,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117665.92, 'new_value': 124056.73}, {'field': 'offline_amount', 'old_value': 238614.25, 'new_value': 247586.68}, {'field': 'total_amount', 'old_value': 356280.17, 'new_value': 371643.41}, {'field': 'order_count', 'old_value': 12866, 'new_value': 13462}]
2025-06-28 00:01:06,318 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-28 00:01:06,771 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-28 00:01:06,771 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35220.52, 'new_value': 36677.22}, {'field': 'offline_amount', 'old_value': 1043952.4, 'new_value': 1099914.36}, {'field': 'total_amount', 'old_value': 1079172.92, 'new_value': 1136591.58}, {'field': 'order_count', 'old_value': 5162, 'new_value': 5413}]
2025-06-28 00:01:06,771 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-28 00:01:07,302 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-28 00:01:07,302 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102856.6, 'new_value': 103932.4}, {'field': 'total_amount', 'old_value': 102856.6, 'new_value': 103932.4}, {'field': 'order_count', 'old_value': 218, 'new_value': 221}]
2025-06-28 00:01:07,318 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-28 00:01:07,755 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-28 00:01:07,755 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58087.7, 'new_value': 59412.6}, {'field': 'total_amount', 'old_value': 58983.6, 'new_value': 60308.5}, {'field': 'order_count', 'old_value': 426, 'new_value': 436}]
2025-06-28 00:01:07,755 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-28 00:01:08,193 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-28 00:01:08,193 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143586.9, 'new_value': 144472.9}, {'field': 'total_amount', 'old_value': 143586.9, 'new_value': 144472.9}, {'field': 'order_count', 'old_value': 4562, 'new_value': 4587}]
2025-06-28 00:01:08,193 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-28 00:01:08,646 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-28 00:01:08,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30069.0, 'new_value': 30119.0}, {'field': 'total_amount', 'old_value': 30069.0, 'new_value': 30119.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 49}]
2025-06-28 00:01:08,646 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-28 00:01:09,099 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-28 00:01:09,099 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34815.45, 'new_value': 36412.25}, {'field': 'offline_amount', 'old_value': 29474.1, 'new_value': 30500.7}, {'field': 'total_amount', 'old_value': 64289.55, 'new_value': 66912.95}, {'field': 'order_count', 'old_value': 345, 'new_value': 354}]
2025-06-28 00:01:09,099 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-28 00:01:09,552 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-28 00:01:09,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 327765.73, 'new_value': 340275.33}, {'field': 'total_amount', 'old_value': 327765.73, 'new_value': 340275.33}, {'field': 'order_count', 'old_value': 3152, 'new_value': 3290}]
2025-06-28 00:01:09,552 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-28 00:01:10,037 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-28 00:01:10,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 271162.17, 'new_value': 284547.72}, {'field': 'total_amount', 'old_value': 285158.17, 'new_value': 298543.72}, {'field': 'order_count', 'old_value': 71, 'new_value': 75}]
2025-06-28 00:01:10,037 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-28 00:01:10,459 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-28 00:01:10,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10860000.0, 'new_value': 11260000.0}, {'field': 'total_amount', 'old_value': 10860000.0, 'new_value': 11260000.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 59}]
2025-06-28 00:01:10,459 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-28 00:01:10,849 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-28 00:01:10,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37256.2, 'new_value': 38209.2}, {'field': 'total_amount', 'old_value': 37739.2, 'new_value': 38692.2}, {'field': 'order_count', 'old_value': 175, 'new_value': 177}]
2025-06-28 00:01:10,849 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-28 00:01:11,380 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-28 00:01:11,380 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101266.19, 'new_value': 113521.98}, {'field': 'total_amount', 'old_value': 304566.02, 'new_value': 316821.81}, {'field': 'order_count', 'old_value': 2458, 'new_value': 2559}]
2025-06-28 00:01:11,380 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-28 00:01:11,771 - INFO - 更新表单数据成功: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-28 00:01:11,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62074.3, 'new_value': 65751.0}, {'field': 'total_amount', 'old_value': 62074.3, 'new_value': 65751.0}, {'field': 'order_count', 'old_value': 683, 'new_value': 748}]
2025-06-28 00:01:11,771 - INFO - 开始更新记录 - 表单实例ID: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-28 00:01:12,240 - INFO - 更新表单数据成功: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-28 00:01:12,240 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59263.32, 'new_value': 66525.32}, {'field': 'total_amount', 'old_value': 59263.32, 'new_value': 66525.32}, {'field': 'order_count', 'old_value': 352, 'new_value': 393}]
2025-06-28 00:01:12,240 - INFO - 日期 2025-06 处理完成 - 更新: 78 条，插入: 0 条，错误: 0 条
2025-06-28 00:01:12,240 - INFO - 数据同步完成！更新: 78 条，插入: 0 条，错误: 0 条
2025-06-28 00:01:12,240 - INFO - =================同步完成====================
2025-06-28 03:00:02,894 - INFO - =================使用默认全量同步=============
2025-06-28 03:00:04,675 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-28 03:00:04,675 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-28 03:00:04,706 - INFO - 开始处理日期: 2025-01
2025-06-28 03:00:04,706 - INFO - Request Parameters - Page 1:
2025-06-28 03:00:04,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:04,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:05,941 - INFO - Response - Page 1:
2025-06-28 03:00:06,144 - INFO - 第 1 页获取到 100 条记录
2025-06-28 03:00:06,144 - INFO - Request Parameters - Page 2:
2025-06-28 03:00:06,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:06,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:06,800 - INFO - Response - Page 2:
2025-06-28 03:00:07,003 - INFO - 第 2 页获取到 100 条记录
2025-06-28 03:00:07,003 - INFO - Request Parameters - Page 3:
2025-06-28 03:00:07,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:07,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:07,753 - INFO - Response - Page 3:
2025-06-28 03:00:07,972 - INFO - 第 3 页获取到 100 条记录
2025-06-28 03:00:07,972 - INFO - Request Parameters - Page 4:
2025-06-28 03:00:07,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:07,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:08,550 - INFO - Response - Page 4:
2025-06-28 03:00:08,753 - INFO - 第 4 页获取到 100 条记录
2025-06-28 03:00:08,753 - INFO - Request Parameters - Page 5:
2025-06-28 03:00:08,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:08,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:09,269 - INFO - Response - Page 5:
2025-06-28 03:00:09,472 - INFO - 第 5 页获取到 100 条记录
2025-06-28 03:00:09,472 - INFO - Request Parameters - Page 6:
2025-06-28 03:00:09,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:09,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:09,972 - INFO - Response - Page 6:
2025-06-28 03:00:10,175 - INFO - 第 6 页获取到 100 条记录
2025-06-28 03:00:10,175 - INFO - Request Parameters - Page 7:
2025-06-28 03:00:10,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:10,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:10,660 - INFO - Response - Page 7:
2025-06-28 03:00:10,863 - INFO - 第 7 页获取到 82 条记录
2025-06-28 03:00:10,863 - INFO - 查询完成，共获取到 682 条记录
2025-06-28 03:00:10,863 - INFO - 获取到 682 条表单数据
2025-06-28 03:00:10,863 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-28 03:00:10,878 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 03:00:10,878 - INFO - 开始处理日期: 2025-02
2025-06-28 03:00:10,878 - INFO - Request Parameters - Page 1:
2025-06-28 03:00:10,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:10,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:11,378 - INFO - Response - Page 1:
2025-06-28 03:00:11,581 - INFO - 第 1 页获取到 100 条记录
2025-06-28 03:00:11,581 - INFO - Request Parameters - Page 2:
2025-06-28 03:00:11,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:11,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:12,113 - INFO - Response - Page 2:
2025-06-28 03:00:12,316 - INFO - 第 2 页获取到 100 条记录
2025-06-28 03:00:12,316 - INFO - Request Parameters - Page 3:
2025-06-28 03:00:12,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:12,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:12,785 - INFO - Response - Page 3:
2025-06-28 03:00:12,988 - INFO - 第 3 页获取到 100 条记录
2025-06-28 03:00:12,988 - INFO - Request Parameters - Page 4:
2025-06-28 03:00:12,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:12,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:13,488 - INFO - Response - Page 4:
2025-06-28 03:00:13,691 - INFO - 第 4 页获取到 100 条记录
2025-06-28 03:00:13,691 - INFO - Request Parameters - Page 5:
2025-06-28 03:00:13,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:13,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:14,191 - INFO - Response - Page 5:
2025-06-28 03:00:14,394 - INFO - 第 5 页获取到 100 条记录
2025-06-28 03:00:14,394 - INFO - Request Parameters - Page 6:
2025-06-28 03:00:14,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:14,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:14,863 - INFO - Response - Page 6:
2025-06-28 03:00:15,066 - INFO - 第 6 页获取到 100 条记录
2025-06-28 03:00:15,066 - INFO - Request Parameters - Page 7:
2025-06-28 03:00:15,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:15,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:15,535 - INFO - Response - Page 7:
2025-06-28 03:00:15,738 - INFO - 第 7 页获取到 70 条记录
2025-06-28 03:00:15,738 - INFO - 查询完成，共获取到 670 条记录
2025-06-28 03:00:15,738 - INFO - 获取到 670 条表单数据
2025-06-28 03:00:15,738 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-28 03:00:15,753 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 03:00:15,753 - INFO - 开始处理日期: 2025-03
2025-06-28 03:00:15,753 - INFO - Request Parameters - Page 1:
2025-06-28 03:00:15,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:15,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:16,347 - INFO - Response - Page 1:
2025-06-28 03:00:16,550 - INFO - 第 1 页获取到 100 条记录
2025-06-28 03:00:16,550 - INFO - Request Parameters - Page 2:
2025-06-28 03:00:16,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:16,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:17,238 - INFO - Response - Page 2:
2025-06-28 03:00:17,441 - INFO - 第 2 页获取到 100 条记录
2025-06-28 03:00:17,441 - INFO - Request Parameters - Page 3:
2025-06-28 03:00:17,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:17,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:17,988 - INFO - Response - Page 3:
2025-06-28 03:00:18,191 - INFO - 第 3 页获取到 100 条记录
2025-06-28 03:00:18,191 - INFO - Request Parameters - Page 4:
2025-06-28 03:00:18,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:18,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:18,706 - INFO - Response - Page 4:
2025-06-28 03:00:18,910 - INFO - 第 4 页获取到 100 条记录
2025-06-28 03:00:18,910 - INFO - Request Parameters - Page 5:
2025-06-28 03:00:18,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:18,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:19,488 - INFO - Response - Page 5:
2025-06-28 03:00:19,691 - INFO - 第 5 页获取到 100 条记录
2025-06-28 03:00:19,691 - INFO - Request Parameters - Page 6:
2025-06-28 03:00:19,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:19,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:20,222 - INFO - Response - Page 6:
2025-06-28 03:00:20,425 - INFO - 第 6 页获取到 100 条记录
2025-06-28 03:00:20,425 - INFO - Request Parameters - Page 7:
2025-06-28 03:00:20,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:20,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:20,878 - INFO - Response - Page 7:
2025-06-28 03:00:21,081 - INFO - 第 7 页获取到 61 条记录
2025-06-28 03:00:21,081 - INFO - 查询完成，共获取到 661 条记录
2025-06-28 03:00:21,081 - INFO - 获取到 661 条表单数据
2025-06-28 03:00:21,081 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-28 03:00:21,097 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 03:00:21,097 - INFO - 开始处理日期: 2025-04
2025-06-28 03:00:21,097 - INFO - Request Parameters - Page 1:
2025-06-28 03:00:21,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:21,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:21,613 - INFO - Response - Page 1:
2025-06-28 03:00:21,816 - INFO - 第 1 页获取到 100 条记录
2025-06-28 03:00:21,816 - INFO - Request Parameters - Page 2:
2025-06-28 03:00:21,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:21,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:22,347 - INFO - Response - Page 2:
2025-06-28 03:00:22,550 - INFO - 第 2 页获取到 100 条记录
2025-06-28 03:00:22,550 - INFO - Request Parameters - Page 3:
2025-06-28 03:00:22,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:22,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:23,050 - INFO - Response - Page 3:
2025-06-28 03:00:23,253 - INFO - 第 3 页获取到 100 条记录
2025-06-28 03:00:23,253 - INFO - Request Parameters - Page 4:
2025-06-28 03:00:23,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:23,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:23,800 - INFO - Response - Page 4:
2025-06-28 03:00:24,003 - INFO - 第 4 页获取到 100 条记录
2025-06-28 03:00:24,003 - INFO - Request Parameters - Page 5:
2025-06-28 03:00:24,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:24,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:24,675 - INFO - Response - Page 5:
2025-06-28 03:00:24,878 - INFO - 第 5 页获取到 100 条记录
2025-06-28 03:00:24,878 - INFO - Request Parameters - Page 6:
2025-06-28 03:00:24,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:24,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:25,347 - INFO - Response - Page 6:
2025-06-28 03:00:25,550 - INFO - 第 6 页获取到 100 条记录
2025-06-28 03:00:25,550 - INFO - Request Parameters - Page 7:
2025-06-28 03:00:25,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:25,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:26,034 - INFO - Response - Page 7:
2025-06-28 03:00:26,238 - INFO - 第 7 页获取到 56 条记录
2025-06-28 03:00:26,238 - INFO - 查询完成，共获取到 656 条记录
2025-06-28 03:00:26,238 - INFO - 获取到 656 条表单数据
2025-06-28 03:00:26,238 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-28 03:00:26,253 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 03:00:26,253 - INFO - 开始处理日期: 2025-05
2025-06-28 03:00:26,253 - INFO - Request Parameters - Page 1:
2025-06-28 03:00:26,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:26,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:26,784 - INFO - Response - Page 1:
2025-06-28 03:00:26,988 - INFO - 第 1 页获取到 100 条记录
2025-06-28 03:00:26,988 - INFO - Request Parameters - Page 2:
2025-06-28 03:00:26,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:26,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:27,519 - INFO - Response - Page 2:
2025-06-28 03:00:27,722 - INFO - 第 2 页获取到 100 条记录
2025-06-28 03:00:27,722 - INFO - Request Parameters - Page 3:
2025-06-28 03:00:27,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:27,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:28,175 - INFO - Response - Page 3:
2025-06-28 03:00:28,378 - INFO - 第 3 页获取到 100 条记录
2025-06-28 03:00:28,378 - INFO - Request Parameters - Page 4:
2025-06-28 03:00:28,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:28,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:28,972 - INFO - Response - Page 4:
2025-06-28 03:00:29,175 - INFO - 第 4 页获取到 100 条记录
2025-06-28 03:00:29,175 - INFO - Request Parameters - Page 5:
2025-06-28 03:00:29,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:29,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:29,706 - INFO - Response - Page 5:
2025-06-28 03:00:29,909 - INFO - 第 5 页获取到 100 条记录
2025-06-28 03:00:29,909 - INFO - Request Parameters - Page 6:
2025-06-28 03:00:29,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:29,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:30,441 - INFO - Response - Page 6:
2025-06-28 03:00:30,644 - INFO - 第 6 页获取到 100 条记录
2025-06-28 03:00:30,644 - INFO - Request Parameters - Page 7:
2025-06-28 03:00:30,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:30,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:31,050 - INFO - Response - Page 7:
2025-06-28 03:00:31,253 - INFO - 第 7 页获取到 65 条记录
2025-06-28 03:00:31,253 - INFO - 查询完成，共获取到 665 条记录
2025-06-28 03:00:31,253 - INFO - 获取到 665 条表单数据
2025-06-28 03:00:31,253 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-28 03:00:31,269 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 03:00:31,269 - INFO - 开始处理日期: 2025-06
2025-06-28 03:00:31,269 - INFO - Request Parameters - Page 1:
2025-06-28 03:00:31,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:31,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:31,800 - INFO - Response - Page 1:
2025-06-28 03:00:32,003 - INFO - 第 1 页获取到 100 条记录
2025-06-28 03:00:32,003 - INFO - Request Parameters - Page 2:
2025-06-28 03:00:32,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:32,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:32,566 - INFO - Response - Page 2:
2025-06-28 03:00:32,769 - INFO - 第 2 页获取到 100 条记录
2025-06-28 03:00:32,769 - INFO - Request Parameters - Page 3:
2025-06-28 03:00:32,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:32,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:33,269 - INFO - Response - Page 3:
2025-06-28 03:00:33,472 - INFO - 第 3 页获取到 100 条记录
2025-06-28 03:00:33,472 - INFO - Request Parameters - Page 4:
2025-06-28 03:00:33,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:33,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:33,972 - INFO - Response - Page 4:
2025-06-28 03:00:34,175 - INFO - 第 4 页获取到 100 条记录
2025-06-28 03:00:34,175 - INFO - Request Parameters - Page 5:
2025-06-28 03:00:34,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:34,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:34,722 - INFO - Response - Page 5:
2025-06-28 03:00:34,925 - INFO - 第 5 页获取到 100 条记录
2025-06-28 03:00:34,925 - INFO - Request Parameters - Page 6:
2025-06-28 03:00:34,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:34,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:35,456 - INFO - Response - Page 6:
2025-06-28 03:00:35,659 - INFO - 第 6 页获取到 100 条记录
2025-06-28 03:00:35,659 - INFO - Request Parameters - Page 7:
2025-06-28 03:00:35,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 03:00:35,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 03:00:36,034 - INFO - Response - Page 7:
2025-06-28 03:00:36,238 - INFO - 第 7 页获取到 29 条记录
2025-06-28 03:00:36,238 - INFO - 查询完成，共获取到 629 条记录
2025-06-28 03:00:36,238 - INFO - 获取到 629 条表单数据
2025-06-28 03:00:36,238 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-28 03:00:36,238 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-28 03:00:36,722 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-28 03:00:36,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164293.0, 'new_value': 168966.0}, {'field': 'total_amount', 'old_value': 164293.0, 'new_value': 168966.0}, {'field': 'order_count', 'old_value': 2727, 'new_value': 2811}]
2025-06-28 03:00:36,722 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-28 03:00:37,112 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-28 03:00:37,112 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138479.78, 'new_value': 143067.51}, {'field': 'total_amount', 'old_value': 138479.78, 'new_value': 143067.51}, {'field': 'order_count', 'old_value': 3633, 'new_value': 3773}]
2025-06-28 03:00:37,112 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZX
2025-06-28 03:00:37,503 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZX
2025-06-28 03:00:37,503 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108977.0, 'new_value': 120397.0}, {'field': 'total_amount', 'old_value': 108977.0, 'new_value': 120397.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 17}]
2025-06-28 03:00:37,503 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-28 03:00:37,987 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-28 03:00:37,987 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1420.67, 'new_value': 1504.98}, {'field': 'offline_amount', 'old_value': 102523.64, 'new_value': 112590.28}, {'field': 'total_amount', 'old_value': 103944.31, 'new_value': 114095.26}, {'field': 'order_count', 'old_value': 2417, 'new_value': 2645}]
2025-06-28 03:00:37,987 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-28 03:00:38,456 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-28 03:00:38,456 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113221.43, 'new_value': 117245.03}, {'field': 'offline_amount', 'old_value': 1131378.08, 'new_value': 1228248.07}, {'field': 'total_amount', 'old_value': 1244599.51, 'new_value': 1345493.1}, {'field': 'order_count', 'old_value': 10539, 'new_value': 11426}]
2025-06-28 03:00:38,456 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-28 03:00:38,862 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-28 03:00:38,862 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10110.83, 'new_value': 11246.99}, {'field': 'offline_amount', 'old_value': 197450.19, 'new_value': 210620.87}, {'field': 'total_amount', 'old_value': 207561.02, 'new_value': 221867.86}, {'field': 'order_count', 'old_value': 1182, 'new_value': 1266}]
2025-06-28 03:00:38,862 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-28 03:00:39,253 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-28 03:00:39,253 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1.0}, {'field': 'offline_amount', 'old_value': 12458.0, 'new_value': 12976.0}, {'field': 'total_amount', 'old_value': 12458.0, 'new_value': 12977.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-06-28 03:00:39,253 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-28 03:00:39,597 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-28 03:00:39,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22034.3, 'new_value': 22582.3}, {'field': 'total_amount', 'old_value': 22034.3, 'new_value': 22582.3}, {'field': 'order_count', 'old_value': 118, 'new_value': 120}]
2025-06-28 03:00:39,597 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-28 03:00:40,034 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-28 03:00:40,034 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55872.87, 'new_value': 58127.77}, {'field': 'total_amount', 'old_value': 62654.12, 'new_value': 64909.02}, {'field': 'order_count', 'old_value': 224, 'new_value': 234}]
2025-06-28 03:00:40,034 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-28 03:00:40,503 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-28 03:00:40,503 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25162.0, 'new_value': 25261.0}, {'field': 'total_amount', 'old_value': 25162.0, 'new_value': 25261.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 101}]
2025-06-28 03:00:40,503 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-28 03:00:40,941 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-28 03:00:40,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9431.5, 'new_value': 10225.0}, {'field': 'total_amount', 'old_value': 21836.47, 'new_value': 22629.97}, {'field': 'order_count', 'old_value': 103, 'new_value': 106}]
2025-06-28 03:00:40,941 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-28 03:00:41,441 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-28 03:00:41,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 357725.67, 'new_value': 368839.22}, {'field': 'total_amount', 'old_value': 357725.67, 'new_value': 368839.22}, {'field': 'order_count', 'old_value': 1818, 'new_value': 1888}]
2025-06-28 03:00:41,441 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-28 03:00:42,003 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-28 03:00:42,003 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27399.1, 'new_value': 27409.0}, {'field': 'offline_amount', 'old_value': 104257.6, 'new_value': 107952.6}, {'field': 'total_amount', 'old_value': 131656.7, 'new_value': 135361.6}, {'field': 'order_count', 'old_value': 165, 'new_value': 170}]
2025-06-28 03:00:42,003 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-28 03:00:42,456 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-28 03:00:42,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23556.0, 'new_value': 25550.0}, {'field': 'total_amount', 'old_value': 23616.0, 'new_value': 25610.0}, {'field': 'order_count', 'old_value': 101, 'new_value': 108}]
2025-06-28 03:00:42,456 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-28 03:00:42,878 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-28 03:00:42,878 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60588.91, 'new_value': 64124.15}, {'field': 'offline_amount', 'old_value': 460918.53, 'new_value': 474098.51}, {'field': 'total_amount', 'old_value': 521507.44, 'new_value': 538222.66}, {'field': 'order_count', 'old_value': 5068, 'new_value': 5368}]
2025-06-28 03:00:42,878 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-28 03:00:43,331 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-28 03:00:43,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46680.0, 'new_value': 49776.0}, {'field': 'total_amount', 'old_value': 46680.0, 'new_value': 49776.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-06-28 03:00:43,331 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-28 03:00:43,722 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-28 03:00:43,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89142.6, 'new_value': 92199.1}, {'field': 'total_amount', 'old_value': 89142.6, 'new_value': 92199.1}, {'field': 'order_count', 'old_value': 294, 'new_value': 302}]
2025-06-28 03:00:43,722 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-28 03:00:44,175 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-28 03:00:44,175 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55051.58, 'new_value': 57554.57}, {'field': 'offline_amount', 'old_value': 320498.2, 'new_value': 329990.3}, {'field': 'total_amount', 'old_value': 375549.78, 'new_value': 387544.87}, {'field': 'order_count', 'old_value': 2969, 'new_value': 3097}]
2025-06-28 03:00:44,175 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-28 03:00:44,644 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-28 03:00:44,644 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27591.39, 'new_value': 28998.09}, {'field': 'offline_amount', 'old_value': 267599.15, 'new_value': 290487.25}, {'field': 'total_amount', 'old_value': 295190.54, 'new_value': 319485.34}, {'field': 'order_count', 'old_value': 16296, 'new_value': 17810}]
2025-06-28 03:00:44,644 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-28 03:00:45,112 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-28 03:00:45,112 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46020.0, 'new_value': 46762.0}, {'field': 'total_amount', 'old_value': 46020.0, 'new_value': 46762.0}, {'field': 'order_count', 'old_value': 113, 'new_value': 115}]
2025-06-28 03:00:45,112 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-28 03:00:45,550 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-28 03:00:45,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34154.19, 'new_value': 34966.19}, {'field': 'total_amount', 'old_value': 34154.19, 'new_value': 34966.19}, {'field': 'order_count', 'old_value': 202, 'new_value': 209}]
2025-06-28 03:00:45,550 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-28 03:00:46,034 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-28 03:00:46,034 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154515.0, 'new_value': 162488.0}, {'field': 'total_amount', 'old_value': 154515.0, 'new_value': 162488.0}, {'field': 'order_count', 'old_value': 588, 'new_value': 609}]
2025-06-28 03:00:46,034 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-28 03:00:46,472 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-28 03:00:46,472 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36312.47, 'new_value': 38850.66}, {'field': 'offline_amount', 'old_value': 28247.0, 'new_value': 30873.0}, {'field': 'total_amount', 'old_value': 64559.47, 'new_value': 69723.66}, {'field': 'order_count', 'old_value': 866, 'new_value': 934}]
2025-06-28 03:00:46,472 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-28 03:00:46,987 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-28 03:00:46,987 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104689.76, 'new_value': 109575.76}, {'field': 'total_amount', 'old_value': 104689.76, 'new_value': 109575.76}, {'field': 'order_count', 'old_value': 9188, 'new_value': 9762}]
2025-06-28 03:00:46,987 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-28 03:00:47,487 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-28 03:00:47,487 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79209.09, 'new_value': 84216.34}, {'field': 'offline_amount', 'old_value': 399540.63, 'new_value': 415540.63}, {'field': 'total_amount', 'old_value': 478749.72, 'new_value': 499756.97}, {'field': 'order_count', 'old_value': 1522, 'new_value': 1584}]
2025-06-28 03:00:47,503 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-28 03:00:47,941 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-28 03:00:47,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 388787.28, 'new_value': 401856.88}, {'field': 'total_amount', 'old_value': 388787.28, 'new_value': 401856.88}, {'field': 'order_count', 'old_value': 2039, 'new_value': 2097}]
2025-06-28 03:00:47,941 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-28 03:00:48,409 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-28 03:00:48,409 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26480.19, 'new_value': 28249.19}, {'field': 'total_amount', 'old_value': 26980.19, 'new_value': 28749.19}, {'field': 'order_count', 'old_value': 120, 'new_value': 126}]
2025-06-28 03:00:48,409 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-28 03:00:48,878 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-28 03:00:48,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 290145.0, 'new_value': 300178.0}, {'field': 'total_amount', 'old_value': 325145.0, 'new_value': 335178.0}, {'field': 'order_count', 'old_value': 7332, 'new_value': 7457}]
2025-06-28 03:00:48,878 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-28 03:00:49,284 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-28 03:00:49,284 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82287.29, 'new_value': 87265.29}, {'field': 'total_amount', 'old_value': 82287.29, 'new_value': 87265.29}, {'field': 'order_count', 'old_value': 403, 'new_value': 434}]
2025-06-28 03:00:49,284 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-28 03:00:49,878 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-28 03:00:49,878 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26258.19, 'new_value': 27177.59}, {'field': 'offline_amount', 'old_value': 250436.37, 'new_value': 260467.99}, {'field': 'total_amount', 'old_value': 276694.56, 'new_value': 287645.58}, {'field': 'order_count', 'old_value': 1338, 'new_value': 1402}]
2025-06-28 03:00:49,878 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-28 03:00:50,316 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-28 03:00:50,316 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 269598.44, 'new_value': 281367.53}, {'field': 'offline_amount', 'old_value': 83222.71, 'new_value': 84509.71}, {'field': 'total_amount', 'old_value': 352821.15, 'new_value': 365877.24}, {'field': 'order_count', 'old_value': 2231, 'new_value': 2304}]
2025-06-28 03:00:50,316 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-28 03:00:50,784 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-28 03:00:50,784 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166327.2, 'new_value': 178435.4}, {'field': 'total_amount', 'old_value': 166416.2, 'new_value': 178524.4}, {'field': 'order_count', 'old_value': 2098, 'new_value': 2240}]
2025-06-28 03:00:50,784 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-28 03:00:51,237 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-28 03:00:51,237 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55925.17, 'new_value': 62842.47}, {'field': 'offline_amount', 'old_value': 342342.51, 'new_value': 371291.91}, {'field': 'total_amount', 'old_value': 398267.68, 'new_value': 434134.38}, {'field': 'order_count', 'old_value': 2483, 'new_value': 2693}]
2025-06-28 03:00:51,237 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-28 03:00:51,769 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-28 03:00:51,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29252.0, 'new_value': 31317.0}, {'field': 'total_amount', 'old_value': 29252.0, 'new_value': 31317.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 191}]
2025-06-28 03:00:51,769 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-28 03:00:52,144 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-28 03:00:52,144 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 116595.76, 'new_value': 121486.56}, {'field': 'offline_amount', 'old_value': 225024.96, 'new_value': 230617.3}, {'field': 'total_amount', 'old_value': 341620.72, 'new_value': 352103.86}, {'field': 'order_count', 'old_value': 4706, 'new_value': 5077}]
2025-06-28 03:00:52,144 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-28 03:00:52,644 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-28 03:00:52,644 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12586.44, 'new_value': 13231.87}, {'field': 'offline_amount', 'old_value': 30068.04, 'new_value': 30944.4}, {'field': 'total_amount', 'old_value': 42654.48, 'new_value': 44176.27}, {'field': 'order_count', 'old_value': 2186, 'new_value': 2281}]
2025-06-28 03:00:52,644 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-28 03:00:53,081 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-28 03:00:53,081 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112836.5, 'new_value': 129090.2}, {'field': 'offline_amount', 'old_value': 130905.9, 'new_value': 136007.4}, {'field': 'total_amount', 'old_value': 243742.4, 'new_value': 265097.6}, {'field': 'order_count', 'old_value': 4389, 'new_value': 4816}]
2025-06-28 03:00:53,081 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-28 03:00:53,472 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-28 03:00:53,472 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 221834.0, 'new_value': 225814.0}, {'field': 'total_amount', 'old_value': 221834.0, 'new_value': 225814.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 42}]
2025-06-28 03:00:53,472 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-28 03:00:53,847 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-28 03:00:53,847 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6321.55, 'new_value': 6591.64}, {'field': 'offline_amount', 'old_value': 355297.07, 'new_value': 366976.77}, {'field': 'total_amount', 'old_value': 361618.62, 'new_value': 373568.41}, {'field': 'order_count', 'old_value': 18024, 'new_value': 18752}]
2025-06-28 03:00:53,847 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-28 03:00:54,284 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-28 03:00:54,284 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101100.7, 'new_value': 103024.7}, {'field': 'offline_amount', 'old_value': 57653.7, 'new_value': 59148.2}, {'field': 'total_amount', 'old_value': 158754.4, 'new_value': 162172.9}, {'field': 'order_count', 'old_value': 1047, 'new_value': 1071}]
2025-06-28 03:00:54,284 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-28 03:00:54,800 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-28 03:00:54,800 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23820.0, 'new_value': 24062.0}, {'field': 'total_amount', 'old_value': 23820.0, 'new_value': 24062.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 104}]
2025-06-28 03:00:54,800 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-28 03:00:55,269 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-28 03:00:55,269 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30694.8, 'new_value': 31865.83}, {'field': 'offline_amount', 'old_value': 209055.7, 'new_value': 215087.3}, {'field': 'total_amount', 'old_value': 239750.5, 'new_value': 246953.13}, {'field': 'order_count', 'old_value': 7767, 'new_value': 8022}]
2025-06-28 03:00:55,269 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-28 03:00:55,737 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-28 03:00:55,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24627.63, 'new_value': 25079.23}, {'field': 'total_amount', 'old_value': 24692.23, 'new_value': 25143.83}, {'field': 'order_count', 'old_value': 155, 'new_value': 169}]
2025-06-28 03:00:55,737 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-28 03:00:56,159 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-28 03:00:56,159 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 262538.08, 'new_value': 272629.13}, {'field': 'offline_amount', 'old_value': 693373.61, 'new_value': 715502.35}, {'field': 'total_amount', 'old_value': 955911.69, 'new_value': 988131.48}, {'field': 'order_count', 'old_value': 6023, 'new_value': 6256}]
2025-06-28 03:00:56,159 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-28 03:00:56,628 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-28 03:00:56,628 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40015.84, 'new_value': 41217.69}, {'field': 'offline_amount', 'old_value': 42670.45, 'new_value': 44508.11}, {'field': 'total_amount', 'old_value': 82686.29, 'new_value': 85725.8}, {'field': 'order_count', 'old_value': 7084, 'new_value': 7336}]
2025-06-28 03:00:56,628 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-28 03:00:57,081 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-28 03:00:57,081 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36677.22, 'new_value': 37301.32}, {'field': 'offline_amount', 'old_value': 1099914.36, 'new_value': 1139745.45}, {'field': 'total_amount', 'old_value': 1136591.58, 'new_value': 1177046.77}, {'field': 'order_count', 'old_value': 5413, 'new_value': 5621}]
2025-06-28 03:00:57,081 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-28 03:00:57,440 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-28 03:00:57,440 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11663.0, 'new_value': 11862.0}, {'field': 'total_amount', 'old_value': 11663.0, 'new_value': 11862.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-06-28 03:00:57,440 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-28 03:00:57,784 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-28 03:00:57,784 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154565.83, 'new_value': 158968.66}, {'field': 'total_amount', 'old_value': 154565.83, 'new_value': 158968.66}, {'field': 'order_count', 'old_value': 6867, 'new_value': 7082}]
2025-06-28 03:00:57,784 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-28 03:00:58,269 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-28 03:00:58,269 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85384.0, 'new_value': 92203.0}, {'field': 'total_amount', 'old_value': 85384.0, 'new_value': 92203.0}, {'field': 'order_count', 'old_value': 2559, 'new_value': 2772}]
2025-06-28 03:00:58,269 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-28 03:00:58,675 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-28 03:00:58,675 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 219897.45, 'new_value': 229242.95}, {'field': 'total_amount', 'old_value': 219897.45, 'new_value': 229242.95}, {'field': 'order_count', 'old_value': 1776, 'new_value': 1864}]
2025-06-28 03:00:58,675 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-28 03:00:59,097 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-28 03:00:59,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85159.0, 'new_value': 91218.0}, {'field': 'total_amount', 'old_value': 85159.0, 'new_value': 91218.0}, {'field': 'order_count', 'old_value': 686, 'new_value': 736}]
2025-06-28 03:00:59,097 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-28 03:00:59,565 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-28 03:00:59,565 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 259186.72, 'new_value': 268327.39}, {'field': 'total_amount', 'old_value': 259186.72, 'new_value': 268327.39}, {'field': 'order_count', 'old_value': 5476, 'new_value': 5665}]
2025-06-28 03:00:59,565 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM7M
2025-06-28 03:00:59,940 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM7M
2025-06-28 03:00:59,940 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25525.0, 'new_value': 28514.0}, {'field': 'total_amount', 'old_value': 25525.0, 'new_value': 28514.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 38}]
2025-06-28 03:00:59,940 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-28 03:01:00,425 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-28 03:01:00,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 403542.16, 'new_value': 419428.45}, {'field': 'total_amount', 'old_value': 403542.16, 'new_value': 419428.45}, {'field': 'order_count', 'old_value': 16491, 'new_value': 17134}]
2025-06-28 03:01:00,425 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-28 03:01:00,940 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-28 03:01:00,940 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 282758.45, 'new_value': 292928.95}, {'field': 'offline_amount', 'old_value': 1402155.14, 'new_value': 1456068.96}, {'field': 'total_amount', 'old_value': 1684913.59, 'new_value': 1748997.91}, {'field': 'order_count', 'old_value': 8107, 'new_value': 8433}]
2025-06-28 03:01:00,940 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-28 03:01:01,378 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-28 03:01:01,378 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 566092.65, 'new_value': 585423.65}, {'field': 'total_amount', 'old_value': 566092.65, 'new_value': 585423.65}, {'field': 'order_count', 'old_value': 2202, 'new_value': 2259}]
2025-06-28 03:01:01,378 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-28 03:01:01,894 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-28 03:01:01,894 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1523173.0, 'new_value': 1998530.0}, {'field': 'total_amount', 'old_value': 1523173.0, 'new_value': 1998530.0}, {'field': 'order_count', 'old_value': 7051, 'new_value': 7274}]
2025-06-28 03:01:01,894 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-28 03:01:02,378 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-28 03:01:02,378 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54381.95, 'new_value': 56264.36}, {'field': 'offline_amount', 'old_value': 358431.91, 'new_value': 370605.81}, {'field': 'total_amount', 'old_value': 412813.86, 'new_value': 426870.17}, {'field': 'order_count', 'old_value': 3439, 'new_value': 3553}]
2025-06-28 03:01:02,378 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-28 03:01:02,831 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-28 03:01:02,831 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 292987.4, 'new_value': 304792.55}, {'field': 'offline_amount', 'old_value': 30013.39, 'new_value': 30713.89}, {'field': 'total_amount', 'old_value': 323000.79, 'new_value': 335506.44}, {'field': 'order_count', 'old_value': 12205, 'new_value': 12616}]
2025-06-28 03:01:02,831 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-28 03:01:03,300 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-28 03:01:03,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129256.0, 'new_value': 136300.0}, {'field': 'total_amount', 'old_value': 129256.0, 'new_value': 136300.0}, {'field': 'order_count', 'old_value': 232, 'new_value': 245}]
2025-06-28 03:01:03,315 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-28 03:01:03,769 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-28 03:01:03,769 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3974.0, 'new_value': 4070.0}, {'field': 'offline_amount', 'old_value': 25265.2, 'new_value': 25876.2}, {'field': 'total_amount', 'old_value': 29239.2, 'new_value': 29946.2}, {'field': 'order_count', 'old_value': 997, 'new_value': 1025}]
2025-06-28 03:01:03,769 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-28 03:01:04,300 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-28 03:01:04,300 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 154235.53, 'new_value': 161283.06}, {'field': 'offline_amount', 'old_value': 209907.96, 'new_value': 217025.07}, {'field': 'total_amount', 'old_value': 364143.49, 'new_value': 378308.13}, {'field': 'order_count', 'old_value': 12358, 'new_value': 12871}]
2025-06-28 03:01:04,300 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-28 03:01:04,722 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-28 03:01:04,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64251.28, 'new_value': 66978.64}, {'field': 'total_amount', 'old_value': 64251.28, 'new_value': 66978.64}, {'field': 'order_count', 'old_value': 1012, 'new_value': 1033}]
2025-06-28 03:01:04,722 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-28 03:01:05,206 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-28 03:01:05,206 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35797.41, 'new_value': 44006.91}, {'field': 'total_amount', 'old_value': 200995.13, 'new_value': 209204.63}, {'field': 'order_count', 'old_value': 11311, 'new_value': 11974}]
2025-06-28 03:01:05,206 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-28 03:01:05,706 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-28 03:01:05,706 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 206557.92, 'new_value': 209451.92}, {'field': 'offline_amount', 'old_value': 16470.3, 'new_value': 16536.3}, {'field': 'total_amount', 'old_value': 223028.22, 'new_value': 225988.22}, {'field': 'order_count', 'old_value': 15189, 'new_value': 15438}]
2025-06-28 03:01:05,706 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-28 03:01:06,144 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-28 03:01:06,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 228028.0, 'new_value': 228337.0}, {'field': 'total_amount', 'old_value': 240615.0, 'new_value': 240924.0}, {'field': 'order_count', 'old_value': 107, 'new_value': 110}]
2025-06-28 03:01:06,144 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-28 03:01:06,597 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-28 03:01:06,597 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17123.0, 'new_value': 17445.0}, {'field': 'offline_amount', 'old_value': 26094.0, 'new_value': 26881.0}, {'field': 'total_amount', 'old_value': 43217.0, 'new_value': 44326.0}, {'field': 'order_count', 'old_value': 351, 'new_value': 363}]
2025-06-28 03:01:06,597 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-28 03:01:07,159 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-28 03:01:07,159 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70563.45, 'new_value': 73220.34}, {'field': 'offline_amount', 'old_value': 94057.93, 'new_value': 96807.05}, {'field': 'total_amount', 'old_value': 164621.38, 'new_value': 170027.39}, {'field': 'order_count', 'old_value': 1926, 'new_value': 1994}]
2025-06-28 03:01:07,159 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-28 03:01:07,612 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-28 03:01:07,612 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74028.0, 'new_value': 77945.0}, {'field': 'total_amount', 'old_value': 74028.0, 'new_value': 77945.0}, {'field': 'order_count', 'old_value': 3288, 'new_value': 3437}]
2025-06-28 03:01:07,612 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-28 03:01:08,003 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-28 03:01:08,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44528.8, 'new_value': 46275.5}, {'field': 'total_amount', 'old_value': 44528.8, 'new_value': 46275.5}, {'field': 'order_count', 'old_value': 363, 'new_value': 372}]
2025-06-28 03:01:08,019 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-28 03:01:08,440 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-28 03:01:08,440 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109117.63, 'new_value': 114929.63}, {'field': 'total_amount', 'old_value': 175675.33, 'new_value': 181487.33}, {'field': 'order_count', 'old_value': 4225, 'new_value': 4371}]
2025-06-28 03:01:08,440 - INFO - 开始更新记录 - 表单实例ID: FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECMJ9
2025-06-28 03:01:08,909 - INFO - 更新表单数据成功: FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECMJ9
2025-06-28 03:01:08,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4654.4}, {'field': 'total_amount', 'old_value': 4355.52, 'new_value': 9009.92}, {'field': 'order_count', 'old_value': 71, 'new_value': 143}]
2025-06-28 03:01:08,909 - INFO - 开始更新记录 - 表单实例ID: FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECMK9
2025-06-28 03:01:09,331 - INFO - 更新表单数据成功: FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECMK9
2025-06-28 03:01:09,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1468.88, 'new_value': 4062.96}, {'field': 'total_amount', 'old_value': 1468.88, 'new_value': 4062.96}, {'field': 'order_count', 'old_value': 65, 'new_value': 192}]
2025-06-28 03:01:09,331 - INFO - 日期 2025-06 处理完成 - 更新: 73 条，插入: 0 条，错误: 0 条
2025-06-28 03:01:09,331 - INFO - 数据同步完成！更新: 73 条，插入: 0 条，错误: 0 条
2025-06-28 03:01:09,331 - INFO - =================同步完成====================
2025-06-28 06:00:03,238 - INFO - =================使用默认全量同步=============
2025-06-28 06:00:05,019 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-28 06:00:05,019 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-28 06:00:05,051 - INFO - 开始处理日期: 2025-01
2025-06-28 06:00:05,051 - INFO - Request Parameters - Page 1:
2025-06-28 06:00:05,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:05,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:06,129 - INFO - Response - Page 1:
2025-06-28 06:00:06,332 - INFO - 第 1 页获取到 100 条记录
2025-06-28 06:00:06,332 - INFO - Request Parameters - Page 2:
2025-06-28 06:00:06,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:06,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:07,316 - INFO - Response - Page 2:
2025-06-28 06:00:07,519 - INFO - 第 2 页获取到 100 条记录
2025-06-28 06:00:07,519 - INFO - Request Parameters - Page 3:
2025-06-28 06:00:07,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:07,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:08,004 - INFO - Response - Page 3:
2025-06-28 06:00:08,207 - INFO - 第 3 页获取到 100 条记录
2025-06-28 06:00:08,207 - INFO - Request Parameters - Page 4:
2025-06-28 06:00:08,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:08,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:09,113 - INFO - Response - Page 4:
2025-06-28 06:00:09,316 - INFO - 第 4 页获取到 100 条记录
2025-06-28 06:00:09,316 - INFO - Request Parameters - Page 5:
2025-06-28 06:00:09,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:09,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:09,785 - INFO - Response - Page 5:
2025-06-28 06:00:09,988 - INFO - 第 5 页获取到 100 条记录
2025-06-28 06:00:09,988 - INFO - Request Parameters - Page 6:
2025-06-28 06:00:09,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:09,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:10,441 - INFO - Response - Page 6:
2025-06-28 06:00:10,644 - INFO - 第 6 页获取到 100 条记录
2025-06-28 06:00:10,644 - INFO - Request Parameters - Page 7:
2025-06-28 06:00:10,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:10,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:11,144 - INFO - Response - Page 7:
2025-06-28 06:00:11,347 - INFO - 第 7 页获取到 82 条记录
2025-06-28 06:00:11,347 - INFO - 查询完成，共获取到 682 条记录
2025-06-28 06:00:11,347 - INFO - 获取到 682 条表单数据
2025-06-28 06:00:11,363 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-28 06:00:11,379 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 06:00:11,379 - INFO - 开始处理日期: 2025-02
2025-06-28 06:00:11,379 - INFO - Request Parameters - Page 1:
2025-06-28 06:00:11,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:11,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:11,863 - INFO - Response - Page 1:
2025-06-28 06:00:12,066 - INFO - 第 1 页获取到 100 条记录
2025-06-28 06:00:12,066 - INFO - Request Parameters - Page 2:
2025-06-28 06:00:12,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:12,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:12,597 - INFO - Response - Page 2:
2025-06-28 06:00:12,801 - INFO - 第 2 页获取到 100 条记录
2025-06-28 06:00:12,801 - INFO - Request Parameters - Page 3:
2025-06-28 06:00:12,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:12,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:13,316 - INFO - Response - Page 3:
2025-06-28 06:00:13,519 - INFO - 第 3 页获取到 100 条记录
2025-06-28 06:00:13,519 - INFO - Request Parameters - Page 4:
2025-06-28 06:00:13,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:13,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:14,082 - INFO - Response - Page 4:
2025-06-28 06:00:14,285 - INFO - 第 4 页获取到 100 条记录
2025-06-28 06:00:14,285 - INFO - Request Parameters - Page 5:
2025-06-28 06:00:14,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:14,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:15,207 - INFO - Response - Page 5:
2025-06-28 06:00:15,410 - INFO - 第 5 页获取到 100 条记录
2025-06-28 06:00:15,410 - INFO - Request Parameters - Page 6:
2025-06-28 06:00:15,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:15,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:15,925 - INFO - Response - Page 6:
2025-06-28 06:00:16,129 - INFO - 第 6 页获取到 100 条记录
2025-06-28 06:00:16,129 - INFO - Request Parameters - Page 7:
2025-06-28 06:00:16,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:16,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:16,582 - INFO - Response - Page 7:
2025-06-28 06:00:16,785 - INFO - 第 7 页获取到 70 条记录
2025-06-28 06:00:16,785 - INFO - 查询完成，共获取到 670 条记录
2025-06-28 06:00:16,785 - INFO - 获取到 670 条表单数据
2025-06-28 06:00:16,800 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-28 06:00:16,816 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 06:00:16,816 - INFO - 开始处理日期: 2025-03
2025-06-28 06:00:16,816 - INFO - Request Parameters - Page 1:
2025-06-28 06:00:16,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:16,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:17,347 - INFO - Response - Page 1:
2025-06-28 06:00:17,550 - INFO - 第 1 页获取到 100 条记录
2025-06-28 06:00:17,550 - INFO - Request Parameters - Page 2:
2025-06-28 06:00:17,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:17,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:18,019 - INFO - Response - Page 2:
2025-06-28 06:00:18,222 - INFO - 第 2 页获取到 100 条记录
2025-06-28 06:00:18,222 - INFO - Request Parameters - Page 3:
2025-06-28 06:00:18,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:18,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:18,660 - INFO - Response - Page 3:
2025-06-28 06:00:18,863 - INFO - 第 3 页获取到 100 条记录
2025-06-28 06:00:18,863 - INFO - Request Parameters - Page 4:
2025-06-28 06:00:18,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:18,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:19,347 - INFO - Response - Page 4:
2025-06-28 06:00:19,550 - INFO - 第 4 页获取到 100 条记录
2025-06-28 06:00:19,550 - INFO - Request Parameters - Page 5:
2025-06-28 06:00:19,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:19,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:20,019 - INFO - Response - Page 5:
2025-06-28 06:00:20,222 - INFO - 第 5 页获取到 100 条记录
2025-06-28 06:00:20,222 - INFO - Request Parameters - Page 6:
2025-06-28 06:00:20,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:20,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:20,785 - INFO - Response - Page 6:
2025-06-28 06:00:20,988 - INFO - 第 6 页获取到 100 条记录
2025-06-28 06:00:20,988 - INFO - Request Parameters - Page 7:
2025-06-28 06:00:20,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:20,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:21,597 - INFO - Response - Page 7:
2025-06-28 06:00:21,800 - INFO - 第 7 页获取到 61 条记录
2025-06-28 06:00:21,800 - INFO - 查询完成，共获取到 661 条记录
2025-06-28 06:00:21,800 - INFO - 获取到 661 条表单数据
2025-06-28 06:00:21,800 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-28 06:00:21,816 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 06:00:21,816 - INFO - 开始处理日期: 2025-04
2025-06-28 06:00:21,816 - INFO - Request Parameters - Page 1:
2025-06-28 06:00:21,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:21,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:22,332 - INFO - Response - Page 1:
2025-06-28 06:00:22,535 - INFO - 第 1 页获取到 100 条记录
2025-06-28 06:00:22,535 - INFO - Request Parameters - Page 2:
2025-06-28 06:00:22,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:22,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:23,050 - INFO - Response - Page 2:
2025-06-28 06:00:23,254 - INFO - 第 2 页获取到 100 条记录
2025-06-28 06:00:23,254 - INFO - Request Parameters - Page 3:
2025-06-28 06:00:23,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:23,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:23,847 - INFO - Response - Page 3:
2025-06-28 06:00:24,050 - INFO - 第 3 页获取到 100 条记录
2025-06-28 06:00:24,050 - INFO - Request Parameters - Page 4:
2025-06-28 06:00:24,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:24,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:24,644 - INFO - Response - Page 4:
2025-06-28 06:00:24,847 - INFO - 第 4 页获取到 100 条记录
2025-06-28 06:00:24,847 - INFO - Request Parameters - Page 5:
2025-06-28 06:00:24,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:24,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:25,332 - INFO - Response - Page 5:
2025-06-28 06:00:25,535 - INFO - 第 5 页获取到 100 条记录
2025-06-28 06:00:25,535 - INFO - Request Parameters - Page 6:
2025-06-28 06:00:25,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:25,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:26,004 - INFO - Response - Page 6:
2025-06-28 06:00:26,207 - INFO - 第 6 页获取到 100 条记录
2025-06-28 06:00:26,207 - INFO - Request Parameters - Page 7:
2025-06-28 06:00:26,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:26,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:26,707 - INFO - Response - Page 7:
2025-06-28 06:00:26,910 - INFO - 第 7 页获取到 56 条记录
2025-06-28 06:00:26,910 - INFO - 查询完成，共获取到 656 条记录
2025-06-28 06:00:26,910 - INFO - 获取到 656 条表单数据
2025-06-28 06:00:26,910 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-28 06:00:26,925 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 06:00:26,925 - INFO - 开始处理日期: 2025-05
2025-06-28 06:00:26,925 - INFO - Request Parameters - Page 1:
2025-06-28 06:00:26,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:26,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:27,379 - INFO - Response - Page 1:
2025-06-28 06:00:27,582 - INFO - 第 1 页获取到 100 条记录
2025-06-28 06:00:27,582 - INFO - Request Parameters - Page 2:
2025-06-28 06:00:27,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:27,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:28,050 - INFO - Response - Page 2:
2025-06-28 06:00:28,269 - INFO - 第 2 页获取到 100 条记录
2025-06-28 06:00:28,269 - INFO - Request Parameters - Page 3:
2025-06-28 06:00:28,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:28,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:28,785 - INFO - Response - Page 3:
2025-06-28 06:00:28,988 - INFO - 第 3 页获取到 100 条记录
2025-06-28 06:00:28,988 - INFO - Request Parameters - Page 4:
2025-06-28 06:00:28,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:28,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:29,410 - INFO - Response - Page 4:
2025-06-28 06:00:29,613 - INFO - 第 4 页获取到 100 条记录
2025-06-28 06:00:29,613 - INFO - Request Parameters - Page 5:
2025-06-28 06:00:29,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:29,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:30,160 - INFO - Response - Page 5:
2025-06-28 06:00:30,363 - INFO - 第 5 页获取到 100 条记录
2025-06-28 06:00:30,363 - INFO - Request Parameters - Page 6:
2025-06-28 06:00:30,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:30,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:30,957 - INFO - Response - Page 6:
2025-06-28 06:00:31,160 - INFO - 第 6 页获取到 100 条记录
2025-06-28 06:00:31,160 - INFO - Request Parameters - Page 7:
2025-06-28 06:00:31,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:31,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:31,613 - INFO - Response - Page 7:
2025-06-28 06:00:31,816 - INFO - 第 7 页获取到 65 条记录
2025-06-28 06:00:31,816 - INFO - 查询完成，共获取到 665 条记录
2025-06-28 06:00:31,816 - INFO - 获取到 665 条表单数据
2025-06-28 06:00:31,816 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-28 06:00:31,832 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 06:00:31,832 - INFO - 开始处理日期: 2025-06
2025-06-28 06:00:31,832 - INFO - Request Parameters - Page 1:
2025-06-28 06:00:31,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:31,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:32,347 - INFO - Response - Page 1:
2025-06-28 06:00:32,550 - INFO - 第 1 页获取到 100 条记录
2025-06-28 06:00:32,550 - INFO - Request Parameters - Page 2:
2025-06-28 06:00:32,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:32,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:33,050 - INFO - Response - Page 2:
2025-06-28 06:00:33,253 - INFO - 第 2 页获取到 100 条记录
2025-06-28 06:00:33,253 - INFO - Request Parameters - Page 3:
2025-06-28 06:00:33,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:33,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:33,785 - INFO - Response - Page 3:
2025-06-28 06:00:33,988 - INFO - 第 3 页获取到 100 条记录
2025-06-28 06:00:33,988 - INFO - Request Parameters - Page 4:
2025-06-28 06:00:33,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:33,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:34,488 - INFO - Response - Page 4:
2025-06-28 06:00:34,691 - INFO - 第 4 页获取到 100 条记录
2025-06-28 06:00:34,691 - INFO - Request Parameters - Page 5:
2025-06-28 06:00:34,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:34,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:35,238 - INFO - Response - Page 5:
2025-06-28 06:00:35,441 - INFO - 第 5 页获取到 100 条记录
2025-06-28 06:00:35,441 - INFO - Request Parameters - Page 6:
2025-06-28 06:00:35,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:35,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:35,894 - INFO - Response - Page 6:
2025-06-28 06:00:36,097 - INFO - 第 6 页获取到 100 条记录
2025-06-28 06:00:36,097 - INFO - Request Parameters - Page 7:
2025-06-28 06:00:36,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 06:00:36,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 06:00:36,582 - INFO - Response - Page 7:
2025-06-28 06:00:36,785 - INFO - 第 7 页获取到 29 条记录
2025-06-28 06:00:36,785 - INFO - 查询完成，共获取到 629 条记录
2025-06-28 06:00:36,785 - INFO - 获取到 629 条表单数据
2025-06-28 06:00:36,785 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-28 06:00:36,800 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 06:00:36,800 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 06:00:36,800 - INFO - =================同步完成====================
2025-06-28 09:00:03,143 - INFO - =================使用默认全量同步=============
2025-06-28 09:00:04,908 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-28 09:00:04,909 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-28 09:00:04,942 - INFO - 开始处理日期: 2025-01
2025-06-28 09:00:04,945 - INFO - Request Parameters - Page 1:
2025-06-28 09:00:04,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:04,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:06,005 - INFO - Response - Page 1:
2025-06-28 09:00:06,208 - INFO - 第 1 页获取到 100 条记录
2025-06-28 09:00:06,208 - INFO - Request Parameters - Page 2:
2025-06-28 09:00:06,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:06,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:06,708 - INFO - Response - Page 2:
2025-06-28 09:00:06,911 - INFO - 第 2 页获取到 100 条记录
2025-06-28 09:00:06,911 - INFO - Request Parameters - Page 3:
2025-06-28 09:00:06,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:06,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:07,489 - INFO - Response - Page 3:
2025-06-28 09:00:07,692 - INFO - 第 3 页获取到 100 条记录
2025-06-28 09:00:07,692 - INFO - Request Parameters - Page 4:
2025-06-28 09:00:07,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:07,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:08,598 - INFO - Response - Page 4:
2025-06-28 09:00:08,801 - INFO - 第 4 页获取到 100 条记录
2025-06-28 09:00:08,801 - INFO - Request Parameters - Page 5:
2025-06-28 09:00:08,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:08,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:09,364 - INFO - Response - Page 5:
2025-06-28 09:00:09,567 - INFO - 第 5 页获取到 100 条记录
2025-06-28 09:00:09,567 - INFO - Request Parameters - Page 6:
2025-06-28 09:00:09,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:09,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:10,145 - INFO - Response - Page 6:
2025-06-28 09:00:10,348 - INFO - 第 6 页获取到 100 条记录
2025-06-28 09:00:10,348 - INFO - Request Parameters - Page 7:
2025-06-28 09:00:10,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:10,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:10,864 - INFO - Response - Page 7:
2025-06-28 09:00:11,067 - INFO - 第 7 页获取到 82 条记录
2025-06-28 09:00:11,067 - INFO - 查询完成，共获取到 682 条记录
2025-06-28 09:00:11,067 - INFO - 获取到 682 条表单数据
2025-06-28 09:00:11,067 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-28 09:00:11,083 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 09:00:11,083 - INFO - 开始处理日期: 2025-02
2025-06-28 09:00:11,083 - INFO - Request Parameters - Page 1:
2025-06-28 09:00:11,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:11,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:11,645 - INFO - Response - Page 1:
2025-06-28 09:00:11,848 - INFO - 第 1 页获取到 100 条记录
2025-06-28 09:00:11,848 - INFO - Request Parameters - Page 2:
2025-06-28 09:00:11,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:11,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:12,317 - INFO - Response - Page 2:
2025-06-28 09:00:12,520 - INFO - 第 2 页获取到 100 条记录
2025-06-28 09:00:12,520 - INFO - Request Parameters - Page 3:
2025-06-28 09:00:12,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:12,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:13,005 - INFO - Response - Page 3:
2025-06-28 09:00:13,208 - INFO - 第 3 页获取到 100 条记录
2025-06-28 09:00:13,208 - INFO - Request Parameters - Page 4:
2025-06-28 09:00:13,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:13,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:13,739 - INFO - Response - Page 4:
2025-06-28 09:00:13,942 - INFO - 第 4 页获取到 100 条记录
2025-06-28 09:00:13,942 - INFO - Request Parameters - Page 5:
2025-06-28 09:00:13,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:13,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:14,458 - INFO - Response - Page 5:
2025-06-28 09:00:14,661 - INFO - 第 5 页获取到 100 条记录
2025-06-28 09:00:14,661 - INFO - Request Parameters - Page 6:
2025-06-28 09:00:14,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:14,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:15,161 - INFO - Response - Page 6:
2025-06-28 09:00:15,364 - INFO - 第 6 页获取到 100 条记录
2025-06-28 09:00:15,364 - INFO - Request Parameters - Page 7:
2025-06-28 09:00:15,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:15,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:15,817 - INFO - Response - Page 7:
2025-06-28 09:00:16,020 - INFO - 第 7 页获取到 70 条记录
2025-06-28 09:00:16,020 - INFO - 查询完成，共获取到 670 条记录
2025-06-28 09:00:16,020 - INFO - 获取到 670 条表单数据
2025-06-28 09:00:16,020 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-28 09:00:16,036 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 09:00:16,036 - INFO - 开始处理日期: 2025-03
2025-06-28 09:00:16,036 - INFO - Request Parameters - Page 1:
2025-06-28 09:00:16,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:16,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:16,567 - INFO - Response - Page 1:
2025-06-28 09:00:16,770 - INFO - 第 1 页获取到 100 条记录
2025-06-28 09:00:16,770 - INFO - Request Parameters - Page 2:
2025-06-28 09:00:16,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:16,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:17,255 - INFO - Response - Page 2:
2025-06-28 09:00:17,458 - INFO - 第 2 页获取到 100 条记录
2025-06-28 09:00:17,458 - INFO - Request Parameters - Page 3:
2025-06-28 09:00:17,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:17,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:17,973 - INFO - Response - Page 3:
2025-06-28 09:00:18,176 - INFO - 第 3 页获取到 100 条记录
2025-06-28 09:00:18,176 - INFO - Request Parameters - Page 4:
2025-06-28 09:00:18,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:18,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:18,661 - INFO - Response - Page 4:
2025-06-28 09:00:18,864 - INFO - 第 4 页获取到 100 条记录
2025-06-28 09:00:18,864 - INFO - Request Parameters - Page 5:
2025-06-28 09:00:18,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:18,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:19,395 - INFO - Response - Page 5:
2025-06-28 09:00:19,598 - INFO - 第 5 页获取到 100 条记录
2025-06-28 09:00:19,598 - INFO - Request Parameters - Page 6:
2025-06-28 09:00:19,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:19,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:20,083 - INFO - Response - Page 6:
2025-06-28 09:00:20,286 - INFO - 第 6 页获取到 100 条记录
2025-06-28 09:00:20,286 - INFO - Request Parameters - Page 7:
2025-06-28 09:00:20,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:20,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:20,817 - INFO - Response - Page 7:
2025-06-28 09:00:21,020 - INFO - 第 7 页获取到 61 条记录
2025-06-28 09:00:21,020 - INFO - 查询完成，共获取到 661 条记录
2025-06-28 09:00:21,020 - INFO - 获取到 661 条表单数据
2025-06-28 09:00:21,020 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-28 09:00:21,036 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 09:00:21,036 - INFO - 开始处理日期: 2025-04
2025-06-28 09:00:21,036 - INFO - Request Parameters - Page 1:
2025-06-28 09:00:21,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:21,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:21,551 - INFO - Response - Page 1:
2025-06-28 09:00:21,754 - INFO - 第 1 页获取到 100 条记录
2025-06-28 09:00:21,754 - INFO - Request Parameters - Page 2:
2025-06-28 09:00:21,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:21,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:22,239 - INFO - Response - Page 2:
2025-06-28 09:00:22,442 - INFO - 第 2 页获取到 100 条记录
2025-06-28 09:00:22,442 - INFO - Request Parameters - Page 3:
2025-06-28 09:00:22,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:22,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:22,973 - INFO - Response - Page 3:
2025-06-28 09:00:23,176 - INFO - 第 3 页获取到 100 条记录
2025-06-28 09:00:23,176 - INFO - Request Parameters - Page 4:
2025-06-28 09:00:23,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:23,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:23,645 - INFO - Response - Page 4:
2025-06-28 09:00:23,848 - INFO - 第 4 页获取到 100 条记录
2025-06-28 09:00:23,848 - INFO - Request Parameters - Page 5:
2025-06-28 09:00:23,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:23,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:24,395 - INFO - Response - Page 5:
2025-06-28 09:00:24,614 - INFO - 第 5 页获取到 100 条记录
2025-06-28 09:00:24,614 - INFO - Request Parameters - Page 6:
2025-06-28 09:00:24,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:24,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:25,051 - INFO - Response - Page 6:
2025-06-28 09:00:25,254 - INFO - 第 6 页获取到 100 条记录
2025-06-28 09:00:25,254 - INFO - Request Parameters - Page 7:
2025-06-28 09:00:25,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:25,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:25,676 - INFO - Response - Page 7:
2025-06-28 09:00:25,879 - INFO - 第 7 页获取到 56 条记录
2025-06-28 09:00:25,879 - INFO - 查询完成，共获取到 656 条记录
2025-06-28 09:00:25,879 - INFO - 获取到 656 条表单数据
2025-06-28 09:00:25,895 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-28 09:00:25,911 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 09:00:25,911 - INFO - 开始处理日期: 2025-05
2025-06-28 09:00:25,911 - INFO - Request Parameters - Page 1:
2025-06-28 09:00:25,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:25,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:26,426 - INFO - Response - Page 1:
2025-06-28 09:00:26,629 - INFO - 第 1 页获取到 100 条记录
2025-06-28 09:00:26,629 - INFO - Request Parameters - Page 2:
2025-06-28 09:00:26,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:26,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:27,208 - INFO - Response - Page 2:
2025-06-28 09:00:27,411 - INFO - 第 2 页获取到 100 条记录
2025-06-28 09:00:27,411 - INFO - Request Parameters - Page 3:
2025-06-28 09:00:27,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:27,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:28,004 - INFO - Response - Page 3:
2025-06-28 09:00:28,208 - INFO - 第 3 页获取到 100 条记录
2025-06-28 09:00:28,208 - INFO - Request Parameters - Page 4:
2025-06-28 09:00:28,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:28,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:28,739 - INFO - Response - Page 4:
2025-06-28 09:00:28,942 - INFO - 第 4 页获取到 100 条记录
2025-06-28 09:00:28,942 - INFO - Request Parameters - Page 5:
2025-06-28 09:00:28,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:28,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:29,583 - INFO - Response - Page 5:
2025-06-28 09:00:29,786 - INFO - 第 5 页获取到 100 条记录
2025-06-28 09:00:29,786 - INFO - Request Parameters - Page 6:
2025-06-28 09:00:29,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:29,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:30,379 - INFO - Response - Page 6:
2025-06-28 09:00:30,583 - INFO - 第 6 页获取到 100 条记录
2025-06-28 09:00:30,583 - INFO - Request Parameters - Page 7:
2025-06-28 09:00:30,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:30,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:31,022 - INFO - Response - Page 7:
2025-06-28 09:00:31,225 - INFO - 第 7 页获取到 65 条记录
2025-06-28 09:00:31,225 - INFO - 查询完成，共获取到 665 条记录
2025-06-28 09:00:31,225 - INFO - 获取到 665 条表单数据
2025-06-28 09:00:31,225 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-28 09:00:31,241 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 09:00:31,241 - INFO - 开始处理日期: 2025-06
2025-06-28 09:00:31,241 - INFO - Request Parameters - Page 1:
2025-06-28 09:00:31,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:31,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:31,725 - INFO - Response - Page 1:
2025-06-28 09:00:31,928 - INFO - 第 1 页获取到 100 条记录
2025-06-28 09:00:31,928 - INFO - Request Parameters - Page 2:
2025-06-28 09:00:31,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:31,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:32,506 - INFO - Response - Page 2:
2025-06-28 09:00:32,709 - INFO - 第 2 页获取到 100 条记录
2025-06-28 09:00:32,709 - INFO - Request Parameters - Page 3:
2025-06-28 09:00:32,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:32,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:33,303 - INFO - Response - Page 3:
2025-06-28 09:00:33,506 - INFO - 第 3 页获取到 100 条记录
2025-06-28 09:00:33,506 - INFO - Request Parameters - Page 4:
2025-06-28 09:00:33,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:33,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:34,069 - INFO - Response - Page 4:
2025-06-28 09:00:34,272 - INFO - 第 4 页获取到 100 条记录
2025-06-28 09:00:34,272 - INFO - Request Parameters - Page 5:
2025-06-28 09:00:34,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:34,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:34,788 - INFO - Response - Page 5:
2025-06-28 09:00:34,991 - INFO - 第 5 页获取到 100 条记录
2025-06-28 09:00:34,991 - INFO - Request Parameters - Page 6:
2025-06-28 09:00:34,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:34,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:35,553 - INFO - Response - Page 6:
2025-06-28 09:00:35,756 - INFO - 第 6 页获取到 100 条记录
2025-06-28 09:00:35,756 - INFO - Request Parameters - Page 7:
2025-06-28 09:00:35,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 09:00:35,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 09:00:36,147 - INFO - Response - Page 7:
2025-06-28 09:00:36,350 - INFO - 第 7 页获取到 29 条记录
2025-06-28 09:00:36,350 - INFO - 查询完成，共获取到 629 条记录
2025-06-28 09:00:36,350 - INFO - 获取到 629 条表单数据
2025-06-28 09:00:36,350 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-28 09:00:36,366 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-28 09:00:36,850 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-28 09:00:36,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1800000.0, 'new_value': 1850000.0}, {'field': 'total_amount', 'old_value': 1900000.0, 'new_value': 1950000.0}, {'field': 'order_count', 'old_value': 378, 'new_value': 379}]
2025-06-28 09:00:36,850 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-28 09:00:37,366 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-28 09:00:37,366 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 133231.8, 'new_value': 138190.8}, {'field': 'offline_amount', 'old_value': 99691.4, 'new_value': 102416.7}, {'field': 'total_amount', 'old_value': 232923.2, 'new_value': 240607.5}, {'field': 'order_count', 'old_value': 5645, 'new_value': 5839}]
2025-06-28 09:00:37,366 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-28 09:00:37,819 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-28 09:00:37,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98216.0, 'new_value': 102573.0}, {'field': 'total_amount', 'old_value': 98216.0, 'new_value': 102573.0}, {'field': 'order_count', 'old_value': 9090, 'new_value': 9345}]
2025-06-28 09:00:37,819 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-28 09:00:38,272 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-28 09:00:38,272 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2129588.0, 'new_value': 2179588.0}, {'field': 'total_amount', 'old_value': 2129588.0, 'new_value': 2179588.0}, {'field': 'order_count', 'old_value': 3087, 'new_value': 3088}]
2025-06-28 09:00:38,272 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-28 09:00:38,803 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-28 09:00:38,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190000.0, 'new_value': 195000.0}, {'field': 'total_amount', 'old_value': 190000.0, 'new_value': 195000.0}, {'field': 'order_count', 'old_value': 800, 'new_value': 801}]
2025-06-28 09:00:38,803 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-28 09:00:39,319 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-28 09:00:39,319 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190000.0, 'new_value': 195000.0}, {'field': 'total_amount', 'old_value': 200000.0, 'new_value': 205000.0}, {'field': 'order_count', 'old_value': 929, 'new_value': 930}]
2025-06-28 09:00:39,319 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-28 09:00:39,772 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-28 09:00:39,772 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 968755.36, 'new_value': 976821.96}, {'field': 'total_amount', 'old_value': 968755.36, 'new_value': 976821.96}, {'field': 'order_count', 'old_value': 4535, 'new_value': 4607}]
2025-06-28 09:00:39,772 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-28 09:00:40,209 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-28 09:00:40,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148329.16, 'new_value': 153948.24}, {'field': 'total_amount', 'old_value': 148329.16, 'new_value': 153948.24}, {'field': 'order_count', 'old_value': 10511, 'new_value': 10899}]
2025-06-28 09:00:40,209 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-28 09:00:40,788 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-28 09:00:40,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190000.0, 'new_value': 195000.0}, {'field': 'total_amount', 'old_value': 190000.0, 'new_value': 195000.0}, {'field': 'order_count', 'old_value': 595, 'new_value': 596}]
2025-06-28 09:00:40,788 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-28 09:00:41,194 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-28 09:00:41,194 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 435464.0, 'new_value': 450833.0}, {'field': 'total_amount', 'old_value': 473120.0, 'new_value': 488489.0}, {'field': 'order_count', 'old_value': 9854, 'new_value': 10171}]
2025-06-28 09:00:41,194 - INFO - 日期 2025-06 处理完成 - 更新: 10 条，插入: 0 条，错误: 0 条
2025-06-28 09:00:41,194 - INFO - 数据同步完成！更新: 10 条，插入: 0 条，错误: 0 条
2025-06-28 09:00:41,194 - INFO - =================同步完成====================
2025-06-28 12:00:03,319 - INFO - =================使用默认全量同步=============
2025-06-28 12:00:05,123 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-28 12:00:05,124 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-28 12:00:05,161 - INFO - 开始处理日期: 2025-01
2025-06-28 12:00:05,164 - INFO - Request Parameters - Page 1:
2025-06-28 12:00:05,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:05,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:06,627 - INFO - Response - Page 1:
2025-06-28 12:00:06,827 - INFO - 第 1 页获取到 100 条记录
2025-06-28 12:00:06,827 - INFO - Request Parameters - Page 2:
2025-06-28 12:00:06,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:06,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:07,377 - INFO - Response - Page 2:
2025-06-28 12:00:07,577 - INFO - 第 2 页获取到 100 条记录
2025-06-28 12:00:07,577 - INFO - Request Parameters - Page 3:
2025-06-28 12:00:07,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:07,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:08,114 - INFO - Response - Page 3:
2025-06-28 12:00:08,316 - INFO - 第 3 页获取到 100 条记录
2025-06-28 12:00:08,316 - INFO - Request Parameters - Page 4:
2025-06-28 12:00:08,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:08,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:08,925 - INFO - Response - Page 4:
2025-06-28 12:00:09,125 - INFO - 第 4 页获取到 100 条记录
2025-06-28 12:00:09,125 - INFO - Request Parameters - Page 5:
2025-06-28 12:00:09,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:09,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:09,695 - INFO - Response - Page 5:
2025-06-28 12:00:09,896 - INFO - 第 5 页获取到 100 条记录
2025-06-28 12:00:09,896 - INFO - Request Parameters - Page 6:
2025-06-28 12:00:09,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:09,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:10,404 - INFO - Response - Page 6:
2025-06-28 12:00:10,605 - INFO - 第 6 页获取到 100 条记录
2025-06-28 12:00:10,605 - INFO - Request Parameters - Page 7:
2025-06-28 12:00:10,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:10,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:11,126 - INFO - Response - Page 7:
2025-06-28 12:00:11,327 - INFO - 第 7 页获取到 82 条记录
2025-06-28 12:00:11,327 - INFO - 查询完成，共获取到 682 条记录
2025-06-28 12:00:11,327 - INFO - 获取到 682 条表单数据
2025-06-28 12:00:11,340 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-28 12:00:11,353 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 12:00:11,353 - INFO - 开始处理日期: 2025-02
2025-06-28 12:00:11,353 - INFO - Request Parameters - Page 1:
2025-06-28 12:00:11,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:11,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:11,893 - INFO - Response - Page 1:
2025-06-28 12:00:12,094 - INFO - 第 1 页获取到 100 条记录
2025-06-28 12:00:12,094 - INFO - Request Parameters - Page 2:
2025-06-28 12:00:12,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:12,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:12,625 - INFO - Response - Page 2:
2025-06-28 12:00:12,825 - INFO - 第 2 页获取到 100 条记录
2025-06-28 12:00:12,825 - INFO - Request Parameters - Page 3:
2025-06-28 12:00:12,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:12,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:13,430 - INFO - Response - Page 3:
2025-06-28 12:00:13,631 - INFO - 第 3 页获取到 100 条记录
2025-06-28 12:00:13,631 - INFO - Request Parameters - Page 4:
2025-06-28 12:00:13,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:13,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:14,208 - INFO - Response - Page 4:
2025-06-28 12:00:14,409 - INFO - 第 4 页获取到 100 条记录
2025-06-28 12:00:14,409 - INFO - Request Parameters - Page 5:
2025-06-28 12:00:14,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:14,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:14,928 - INFO - Response - Page 5:
2025-06-28 12:00:15,128 - INFO - 第 5 页获取到 100 条记录
2025-06-28 12:00:15,128 - INFO - Request Parameters - Page 6:
2025-06-28 12:00:15,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:15,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:15,629 - INFO - Response - Page 6:
2025-06-28 12:00:15,830 - INFO - 第 6 页获取到 100 条记录
2025-06-28 12:00:15,830 - INFO - Request Parameters - Page 7:
2025-06-28 12:00:15,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:15,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:16,301 - INFO - Response - Page 7:
2025-06-28 12:00:16,502 - INFO - 第 7 页获取到 70 条记录
2025-06-28 12:00:16,502 - INFO - 查询完成，共获取到 670 条记录
2025-06-28 12:00:16,502 - INFO - 获取到 670 条表单数据
2025-06-28 12:00:16,516 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-28 12:00:16,528 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 12:00:16,528 - INFO - 开始处理日期: 2025-03
2025-06-28 12:00:16,528 - INFO - Request Parameters - Page 1:
2025-06-28 12:00:16,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:16,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:17,135 - INFO - Response - Page 1:
2025-06-28 12:00:17,336 - INFO - 第 1 页获取到 100 条记录
2025-06-28 12:00:17,336 - INFO - Request Parameters - Page 2:
2025-06-28 12:00:17,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:17,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:17,922 - INFO - Response - Page 2:
2025-06-28 12:00:18,123 - INFO - 第 2 页获取到 100 条记录
2025-06-28 12:00:18,123 - INFO - Request Parameters - Page 3:
2025-06-28 12:00:18,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:18,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:18,602 - INFO - Response - Page 3:
2025-06-28 12:00:18,802 - INFO - 第 3 页获取到 100 条记录
2025-06-28 12:00:18,802 - INFO - Request Parameters - Page 4:
2025-06-28 12:00:18,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:18,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:19,464 - INFO - Response - Page 4:
2025-06-28 12:00:19,664 - INFO - 第 4 页获取到 100 条记录
2025-06-28 12:00:19,664 - INFO - Request Parameters - Page 5:
2025-06-28 12:00:19,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:19,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:20,232 - INFO - Response - Page 5:
2025-06-28 12:00:20,434 - INFO - 第 5 页获取到 100 条记录
2025-06-28 12:00:20,434 - INFO - Request Parameters - Page 6:
2025-06-28 12:00:20,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:20,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:20,985 - INFO - Response - Page 6:
2025-06-28 12:00:21,186 - INFO - 第 6 页获取到 100 条记录
2025-06-28 12:00:21,186 - INFO - Request Parameters - Page 7:
2025-06-28 12:00:21,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:21,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:21,618 - INFO - Response - Page 7:
2025-06-28 12:00:21,818 - INFO - 第 7 页获取到 61 条记录
2025-06-28 12:00:21,818 - INFO - 查询完成，共获取到 661 条记录
2025-06-28 12:00:21,818 - INFO - 获取到 661 条表单数据
2025-06-28 12:00:21,832 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-28 12:00:21,845 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 12:00:21,845 - INFO - 开始处理日期: 2025-04
2025-06-28 12:00:21,845 - INFO - Request Parameters - Page 1:
2025-06-28 12:00:21,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:21,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:22,391 - INFO - Response - Page 1:
2025-06-28 12:00:22,591 - INFO - 第 1 页获取到 100 条记录
2025-06-28 12:00:22,591 - INFO - Request Parameters - Page 2:
2025-06-28 12:00:22,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:22,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:23,161 - INFO - Response - Page 2:
2025-06-28 12:00:23,361 - INFO - 第 2 页获取到 100 条记录
2025-06-28 12:00:23,361 - INFO - Request Parameters - Page 3:
2025-06-28 12:00:23,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:23,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:24,027 - INFO - Response - Page 3:
2025-06-28 12:00:24,229 - INFO - 第 3 页获取到 100 条记录
2025-06-28 12:00:24,229 - INFO - Request Parameters - Page 4:
2025-06-28 12:00:24,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:24,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:24,788 - INFO - Response - Page 4:
2025-06-28 12:00:24,988 - INFO - 第 4 页获取到 100 条记录
2025-06-28 12:00:24,988 - INFO - Request Parameters - Page 5:
2025-06-28 12:00:24,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:24,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:25,466 - INFO - Response - Page 5:
2025-06-28 12:00:25,666 - INFO - 第 5 页获取到 100 条记录
2025-06-28 12:00:25,666 - INFO - Request Parameters - Page 6:
2025-06-28 12:00:25,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:25,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:26,193 - INFO - Response - Page 6:
2025-06-28 12:00:26,394 - INFO - 第 6 页获取到 100 条记录
2025-06-28 12:00:26,394 - INFO - Request Parameters - Page 7:
2025-06-28 12:00:26,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:26,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:26,824 - INFO - Response - Page 7:
2025-06-28 12:00:27,024 - INFO - 第 7 页获取到 56 条记录
2025-06-28 12:00:27,024 - INFO - 查询完成，共获取到 656 条记录
2025-06-28 12:00:27,024 - INFO - 获取到 656 条表单数据
2025-06-28 12:00:27,038 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-28 12:00:27,050 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 12:00:27,050 - INFO - 开始处理日期: 2025-05
2025-06-28 12:00:27,051 - INFO - Request Parameters - Page 1:
2025-06-28 12:00:27,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:27,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:27,585 - INFO - Response - Page 1:
2025-06-28 12:00:27,785 - INFO - 第 1 页获取到 100 条记录
2025-06-28 12:00:27,785 - INFO - Request Parameters - Page 2:
2025-06-28 12:00:27,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:27,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:28,335 - INFO - Response - Page 2:
2025-06-28 12:00:28,535 - INFO - 第 2 页获取到 100 条记录
2025-06-28 12:00:28,535 - INFO - Request Parameters - Page 3:
2025-06-28 12:00:28,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:28,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:29,065 - INFO - Response - Page 3:
2025-06-28 12:00:29,266 - INFO - 第 3 页获取到 100 条记录
2025-06-28 12:00:29,266 - INFO - Request Parameters - Page 4:
2025-06-28 12:00:29,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:29,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:29,869 - INFO - Response - Page 4:
2025-06-28 12:00:30,070 - INFO - 第 4 页获取到 100 条记录
2025-06-28 12:00:30,070 - INFO - Request Parameters - Page 5:
2025-06-28 12:00:30,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:30,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:30,760 - INFO - Response - Page 5:
2025-06-28 12:00:30,960 - INFO - 第 5 页获取到 100 条记录
2025-06-28 12:00:30,960 - INFO - Request Parameters - Page 6:
2025-06-28 12:00:30,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:30,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:31,468 - INFO - Response - Page 6:
2025-06-28 12:00:31,668 - INFO - 第 6 页获取到 100 条记录
2025-06-28 12:00:31,668 - INFO - Request Parameters - Page 7:
2025-06-28 12:00:31,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:31,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:32,115 - INFO - Response - Page 7:
2025-06-28 12:00:32,316 - INFO - 第 7 页获取到 65 条记录
2025-06-28 12:00:32,316 - INFO - 查询完成，共获取到 665 条记录
2025-06-28 12:00:32,316 - INFO - 获取到 665 条表单数据
2025-06-28 12:00:32,330 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-28 12:00:32,342 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 12:00:32,342 - INFO - 开始处理日期: 2025-06
2025-06-28 12:00:32,342 - INFO - Request Parameters - Page 1:
2025-06-28 12:00:32,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:32,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:32,894 - INFO - Response - Page 1:
2025-06-28 12:00:33,094 - INFO - 第 1 页获取到 100 条记录
2025-06-28 12:00:33,094 - INFO - Request Parameters - Page 2:
2025-06-28 12:00:33,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:33,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:33,662 - INFO - Response - Page 2:
2025-06-28 12:00:33,863 - INFO - 第 2 页获取到 100 条记录
2025-06-28 12:00:33,864 - INFO - Request Parameters - Page 3:
2025-06-28 12:00:33,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:33,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:34,342 - INFO - Response - Page 3:
2025-06-28 12:00:34,542 - INFO - 第 3 页获取到 100 条记录
2025-06-28 12:00:34,542 - INFO - Request Parameters - Page 4:
2025-06-28 12:00:34,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:34,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:35,042 - INFO - Response - Page 4:
2025-06-28 12:00:35,242 - INFO - 第 4 页获取到 100 条记录
2025-06-28 12:00:35,242 - INFO - Request Parameters - Page 5:
2025-06-28 12:00:35,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:35,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:35,899 - INFO - Response - Page 5:
2025-06-28 12:00:36,101 - INFO - 第 5 页获取到 100 条记录
2025-06-28 12:00:36,101 - INFO - Request Parameters - Page 6:
2025-06-28 12:00:36,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:36,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:36,626 - INFO - Response - Page 6:
2025-06-28 12:00:36,826 - INFO - 第 6 页获取到 100 条记录
2025-06-28 12:00:36,826 - INFO - Request Parameters - Page 7:
2025-06-28 12:00:36,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 12:00:36,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 12:00:37,212 - INFO - Response - Page 7:
2025-06-28 12:00:37,412 - INFO - 第 7 页获取到 29 条记录
2025-06-28 12:00:37,412 - INFO - 查询完成，共获取到 629 条记录
2025-06-28 12:00:37,412 - INFO - 获取到 629 条表单数据
2025-06-28 12:00:37,424 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-28 12:00:37,425 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-28 12:00:37,885 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-28 12:00:37,885 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 743885.0, 'new_value': 771692.0}, {'field': 'total_amount', 'old_value': 743885.0, 'new_value': 771692.0}, {'field': 'order_count', 'old_value': 5281, 'new_value': 5469}]
2025-06-28 12:00:37,885 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-28 12:00:38,401 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-28 12:00:38,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146960.0, 'new_value': 161960.0}, {'field': 'total_amount', 'old_value': 146960.0, 'new_value': 161960.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-06-28 12:00:38,402 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-28 12:00:38,885 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-28 12:00:38,885 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87901.06, 'new_value': 92270.54}, {'field': 'offline_amount', 'old_value': 956841.47, 'new_value': 1000639.91}, {'field': 'total_amount', 'old_value': 1040750.04, 'new_value': 1088917.96}, {'field': 'order_count', 'old_value': 5067, 'new_value': 5309}]
2025-06-28 12:00:38,885 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-28 12:00:39,306 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-28 12:00:39,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 229400.0, 'new_value': 241400.0}, {'field': 'total_amount', 'old_value': 229400.0, 'new_value': 241400.0}]
2025-06-28 12:00:39,306 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-28 12:00:39,758 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-28 12:00:39,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 390670.0, 'new_value': 420670.0}, {'field': 'total_amount', 'old_value': 412850.0, 'new_value': 442850.0}, {'field': 'order_count', 'old_value': 270, 'new_value': 289}]
2025-06-28 12:00:39,759 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-28 12:00:40,203 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-28 12:00:40,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28050.27, 'new_value': 29092.86}, {'field': 'offline_amount', 'old_value': 15791.57, 'new_value': 16234.62}, {'field': 'total_amount', 'old_value': 43841.84, 'new_value': 45327.48}, {'field': 'order_count', 'old_value': 1825, 'new_value': 1887}]
2025-06-28 12:00:40,203 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-28 12:00:40,615 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-28 12:00:40,615 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 393314.0, 'new_value': 401588.0}, {'field': 'offline_amount', 'old_value': 159820.0, 'new_value': 161083.0}, {'field': 'total_amount', 'old_value': 553134.0, 'new_value': 562671.0}, {'field': 'order_count', 'old_value': 616, 'new_value': 633}]
2025-06-28 12:00:40,615 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-28 12:00:41,017 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-28 12:00:41,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196880.0, 'new_value': 204240.0}, {'field': 'total_amount', 'old_value': 196880.0, 'new_value': 204240.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-06-28 12:00:41,017 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-28 12:00:41,549 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-28 12:00:41,549 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7683.0, 'new_value': 8014.0}, {'field': 'offline_amount', 'old_value': 103502.0, 'new_value': 104736.0}, {'field': 'total_amount', 'old_value': 111185.0, 'new_value': 112750.0}, {'field': 'order_count', 'old_value': 124, 'new_value': 128}]
2025-06-28 12:00:41,549 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Y
2025-06-28 12:00:41,986 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Y
2025-06-28 12:00:41,986 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29612.5, 'new_value': 35206.5}, {'field': 'total_amount', 'old_value': 29612.5, 'new_value': 35206.5}, {'field': 'order_count', 'old_value': 6143, 'new_value': 7344}]
2025-06-28 12:00:41,986 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-28 12:00:42,413 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-28 12:00:42,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188421.0, 'new_value': 188720.0}, {'field': 'total_amount', 'old_value': 188421.0, 'new_value': 188720.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-06-28 12:00:42,414 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-28 12:00:42,878 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-28 12:00:42,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 272034.8, 'new_value': 280543.3}, {'field': 'total_amount', 'old_value': 272034.8, 'new_value': 280543.3}, {'field': 'order_count', 'old_value': 4033, 'new_value': 4148}]
2025-06-28 12:00:42,879 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEY
2025-06-28 12:00:43,320 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEY
2025-06-28 12:00:43,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23118.0, 'new_value': 24803.0}, {'field': 'total_amount', 'old_value': 33718.0, 'new_value': 35403.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 16}]
2025-06-28 12:00:43,320 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFY
2025-06-28 12:00:43,798 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFY
2025-06-28 12:00:43,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121148.59, 'new_value': 137181.59}, {'field': 'total_amount', 'old_value': 121148.59, 'new_value': 137181.59}, {'field': 'order_count', 'old_value': 581, 'new_value': 656}]
2025-06-28 12:00:43,798 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-28 12:00:44,282 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-28 12:00:44,282 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6471.5, 'new_value': 6766.3}, {'field': 'offline_amount', 'old_value': 52854.67, 'new_value': 53946.47}, {'field': 'total_amount', 'old_value': 59326.17, 'new_value': 60712.77}, {'field': 'order_count', 'old_value': 522, 'new_value': 536}]
2025-06-28 12:00:44,283 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHY
2025-06-28 12:00:44,695 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHY
2025-06-28 12:00:44,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53187.0, 'new_value': 59380.0}, {'field': 'offline_amount', 'old_value': 173424.0, 'new_value': 193963.0}, {'field': 'total_amount', 'old_value': 226611.0, 'new_value': 253343.0}, {'field': 'order_count', 'old_value': 1612, 'new_value': 1820}]
2025-06-28 12:00:44,695 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-28 12:00:45,156 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-28 12:00:45,156 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54306.07, 'new_value': 55988.9}, {'field': 'offline_amount', 'old_value': 124464.77, 'new_value': 128099.77}, {'field': 'total_amount', 'old_value': 178770.84, 'new_value': 184088.67}, {'field': 'order_count', 'old_value': 1995, 'new_value': 2055}]
2025-06-28 12:00:45,156 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-28 12:00:45,586 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-28 12:00:45,586 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36144.0, 'new_value': 37152.0}, {'field': 'offline_amount', 'old_value': 57486.43, 'new_value': 60326.83}, {'field': 'total_amount', 'old_value': 93630.43, 'new_value': 97478.83}, {'field': 'order_count', 'old_value': 127, 'new_value': 133}]
2025-06-28 12:00:45,586 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-28 12:00:46,063 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-28 12:00:46,063 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64757.0, 'new_value': 67896.0}, {'field': 'total_amount', 'old_value': 64757.0, 'new_value': 67896.0}, {'field': 'order_count', 'old_value': 1233, 'new_value': 1291}]
2025-06-28 12:00:46,063 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQY
2025-06-28 12:00:46,584 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQY
2025-06-28 12:00:46,584 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34357.64, 'new_value': 38175.67}, {'field': 'offline_amount', 'old_value': 96871.33, 'new_value': 113591.54}, {'field': 'total_amount', 'old_value': 131228.97, 'new_value': 151767.21}, {'field': 'order_count', 'old_value': 1906, 'new_value': 2177}]
2025-06-28 12:00:46,584 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-28 12:00:47,059 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-28 12:00:47,059 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 148551.0, 'new_value': 155838.0}, {'field': 'offline_amount', 'old_value': 51265.97, 'new_value': 53747.15}, {'field': 'total_amount', 'old_value': 199816.97, 'new_value': 209585.15}, {'field': 'order_count', 'old_value': 1393, 'new_value': 1460}]
2025-06-28 12:00:47,059 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-28 12:00:47,546 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-28 12:00:47,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41300.0, 'new_value': 45280.0}, {'field': 'total_amount', 'old_value': 42001.2, 'new_value': 45981.2}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-06-28 12:00:47,547 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-28 12:00:48,031 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-28 12:00:48,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6272.0, 'new_value': 6705.0}, {'field': 'total_amount', 'old_value': 10464.0, 'new_value': 10897.0}, {'field': 'order_count', 'old_value': 185, 'new_value': 192}]
2025-06-28 12:00:48,031 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-28 12:00:48,513 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-28 12:00:48,513 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 233502.0, 'new_value': 241395.0}, {'field': 'total_amount', 'old_value': 233502.0, 'new_value': 241395.0}, {'field': 'order_count', 'old_value': 1255, 'new_value': 1309}]
2025-06-28 12:00:48,513 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-28 12:00:48,978 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-28 12:00:48,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161828.45, 'new_value': 169848.08}, {'field': 'total_amount', 'old_value': 161828.45, 'new_value': 169848.08}, {'field': 'order_count', 'old_value': 1056, 'new_value': 1111}]
2025-06-28 12:00:48,978 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-28 12:00:49,494 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-28 12:00:49,494 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55493.0, 'new_value': 96662.0}, {'field': 'offline_amount', 'old_value': 443195.6, 'new_value': 450412.6}, {'field': 'total_amount', 'old_value': 498688.6, 'new_value': 547074.6}, {'field': 'order_count', 'old_value': 85, 'new_value': 91}]
2025-06-28 12:00:49,494 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM1Z
2025-06-28 12:00:49,966 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM1Z
2025-06-28 12:00:49,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22911.0, 'new_value': 23425.0}, {'field': 'total_amount', 'old_value': 22911.0, 'new_value': 23425.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-06-28 12:00:49,966 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-28 12:00:50,443 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-28 12:00:50,443 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25211.85, 'new_value': 26536.95}, {'field': 'offline_amount', 'old_value': 29191.41, 'new_value': 30054.61}, {'field': 'total_amount', 'old_value': 54403.26, 'new_value': 56591.56}, {'field': 'order_count', 'old_value': 2764, 'new_value': 2888}]
2025-06-28 12:00:50,443 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-28 12:00:50,895 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-28 12:00:50,895 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 556139.05, 'new_value': 584464.36}, {'field': 'total_amount', 'old_value': 556139.05, 'new_value': 584464.36}, {'field': 'order_count', 'old_value': 4078, 'new_value': 4342}]
2025-06-28 12:00:50,895 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-28 12:00:51,387 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-28 12:00:51,387 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11077.5, 'new_value': 11454.5}, {'field': 'total_amount', 'old_value': 13427.4, 'new_value': 13804.4}, {'field': 'order_count', 'old_value': 137, 'new_value': 143}]
2025-06-28 12:00:51,387 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-28 12:00:51,870 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-28 12:00:51,870 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 920774.4, 'new_value': 966376.6}, {'field': 'total_amount', 'old_value': 1005130.7, 'new_value': 1050732.9}, {'field': 'order_count', 'old_value': 90, 'new_value': 95}]
2025-06-28 12:00:51,870 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-28 12:00:52,349 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-28 12:00:52,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125198.5, 'new_value': 128141.5}, {'field': 'total_amount', 'old_value': 125198.5, 'new_value': 128141.5}, {'field': 'order_count', 'old_value': 56, 'new_value': 59}]
2025-06-28 12:00:52,349 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Z
2025-06-28 12:00:52,795 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Z
2025-06-28 12:00:52,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22356.34, 'new_value': 23856.34}, {'field': 'total_amount', 'old_value': 42027.34, 'new_value': 43527.34}, {'field': 'order_count', 'old_value': 17877, 'new_value': 17880}]
2025-06-28 12:00:52,796 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-28 12:00:53,307 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-28 12:00:53,307 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182271.0, 'new_value': 189380.0}, {'field': 'total_amount', 'old_value': 182271.0, 'new_value': 189380.0}, {'field': 'order_count', 'old_value': 326, 'new_value': 337}]
2025-06-28 12:00:53,307 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-28 12:00:53,744 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-28 12:00:53,744 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10486.99, 'new_value': 10923.72}, {'field': 'offline_amount', 'old_value': 160578.8, 'new_value': 167996.58}, {'field': 'total_amount', 'old_value': 171065.79, 'new_value': 178920.3}, {'field': 'order_count', 'old_value': 1928, 'new_value': 2029}]
2025-06-28 12:00:53,745 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-28 12:00:54,206 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-28 12:00:54,206 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6573.4, 'new_value': 6766.4}, {'field': 'total_amount', 'old_value': 87356.4, 'new_value': 87549.4}, {'field': 'order_count', 'old_value': 78, 'new_value': 80}]
2025-06-28 12:00:54,206 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHZ
2025-06-28 12:00:54,668 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHZ
2025-06-28 12:00:54,668 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11689.08, 'new_value': 12039.63}, {'field': 'offline_amount', 'old_value': 31251.69, 'new_value': 32607.59}, {'field': 'total_amount', 'old_value': 42940.77, 'new_value': 44647.22}, {'field': 'order_count', 'old_value': 922, 'new_value': 959}]
2025-06-28 12:00:54,668 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-28 12:00:55,148 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-28 12:00:55,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147170.97, 'new_value': 154799.92}, {'field': 'total_amount', 'old_value': 147170.97, 'new_value': 154799.92}, {'field': 'order_count', 'old_value': 743, 'new_value': 785}]
2025-06-28 12:00:55,149 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-28 12:00:55,623 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-28 12:00:55,623 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29739.0, 'new_value': 30932.0}, {'field': 'total_amount', 'old_value': 29739.0, 'new_value': 30932.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 142}]
2025-06-28 12:00:55,623 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-28 12:00:56,045 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-28 12:00:56,046 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193437.0, 'new_value': 201817.0}, {'field': 'total_amount', 'old_value': 193437.0, 'new_value': 201817.0}, {'field': 'order_count', 'old_value': 7458, 'new_value': 7786}]
2025-06-28 12:00:56,046 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-28 12:00:56,493 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-28 12:00:56,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156057.24, 'new_value': 163327.14}, {'field': 'total_amount', 'old_value': 156057.24, 'new_value': 163327.14}, {'field': 'order_count', 'old_value': 621, 'new_value': 652}]
2025-06-28 12:00:56,493 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-28 12:00:57,010 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-28 12:00:57,010 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131547.5, 'new_value': 136873.5}, {'field': 'offline_amount', 'old_value': 52014.06, 'new_value': 53996.06}, {'field': 'total_amount', 'old_value': 183561.56, 'new_value': 190869.56}, {'field': 'order_count', 'old_value': 1323, 'new_value': 1372}]
2025-06-28 12:00:57,010 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-28 12:00:57,439 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-28 12:00:57,439 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8862.0, 'new_value': 12688.0}, {'field': 'total_amount', 'old_value': 263573.0, 'new_value': 267399.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 69}]
2025-06-28 12:00:57,439 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-28 12:00:57,910 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-28 12:00:57,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139617.31, 'new_value': 144151.84}, {'field': 'total_amount', 'old_value': 139617.31, 'new_value': 144151.84}, {'field': 'order_count', 'old_value': 4969, 'new_value': 5142}]
2025-06-28 12:00:57,910 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-28 12:00:58,422 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-28 12:00:58,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22410.0, 'new_value': 24818.0}, {'field': 'total_amount', 'old_value': 22926.9, 'new_value': 25334.9}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-06-28 12:00:58,423 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-28 12:00:58,824 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-28 12:00:58,824 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59558.0, 'new_value': 62290.0}, {'field': 'offline_amount', 'old_value': 184273.0, 'new_value': 196959.0}, {'field': 'total_amount', 'old_value': 243831.0, 'new_value': 259249.0}, {'field': 'order_count', 'old_value': 190, 'new_value': 200}]
2025-06-28 12:00:58,824 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-28 12:00:59,322 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-28 12:00:59,322 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18077.69, 'new_value': 18821.55}, {'field': 'offline_amount', 'old_value': 398716.87, 'new_value': 419447.13}, {'field': 'total_amount', 'old_value': 416794.56, 'new_value': 438268.68}, {'field': 'order_count', 'old_value': 2002, 'new_value': 2082}]
2025-06-28 12:00:59,322 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM201
2025-06-28 12:00:59,788 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM201
2025-06-28 12:00:59,788 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35282.66, 'new_value': 37946.84}, {'field': 'offline_amount', 'old_value': 36685.68, 'new_value': 37832.89}, {'field': 'total_amount', 'old_value': 71968.34, 'new_value': 75779.73}, {'field': 'order_count', 'old_value': 3570, 'new_value': 3776}]
2025-06-28 12:00:59,788 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAB
2025-06-28 12:01:00,268 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAB
2025-06-28 12:01:00,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77518.3, 'new_value': 88877.73}, {'field': 'total_amount', 'old_value': 77518.3, 'new_value': 88877.73}, {'field': 'order_count', 'old_value': 4065, 'new_value': 4663}]
2025-06-28 12:01:00,268 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-28 12:01:00,700 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-28 12:01:00,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161457.63, 'new_value': 168121.63}, {'field': 'total_amount', 'old_value': 161457.63, 'new_value': 168121.63}, {'field': 'order_count', 'old_value': 4235, 'new_value': 4411}]
2025-06-28 12:01:00,700 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEB
2025-06-28 12:01:01,170 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEB
2025-06-28 12:01:01,170 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 358011.93, 'new_value': 376720.61}, {'field': 'total_amount', 'old_value': 359338.93, 'new_value': 378047.61}, {'field': 'order_count', 'old_value': 5228, 'new_value': 5458}]
2025-06-28 12:01:01,170 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFB
2025-06-28 12:01:01,663 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFB
2025-06-28 12:01:01,663 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63461.99, 'new_value': 66772.43}, {'field': 'offline_amount', 'old_value': 479103.01, 'new_value': 501842.71}, {'field': 'total_amount', 'old_value': 542565.0, 'new_value': 568615.14}, {'field': 'order_count', 'old_value': 1683, 'new_value': 1771}]
2025-06-28 12:01:01,663 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-28 12:01:02,231 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-28 12:01:02,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134068.05, 'new_value': 137350.99}, {'field': 'total_amount', 'old_value': 134068.05, 'new_value': 137350.99}, {'field': 'order_count', 'old_value': 1683, 'new_value': 1724}]
2025-06-28 12:01:02,231 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMIB
2025-06-28 12:01:02,692 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMIB
2025-06-28 12:01:02,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74140.0, 'new_value': 83699.0}, {'field': 'total_amount', 'old_value': 74140.0, 'new_value': 83699.0}, {'field': 'order_count', 'old_value': 4343, 'new_value': 4950}]
2025-06-28 12:01:02,692 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-28 12:01:03,169 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-28 12:01:03,169 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69531.51, 'new_value': 70282.51}, {'field': 'total_amount', 'old_value': 69531.51, 'new_value': 70282.51}, {'field': 'order_count', 'old_value': 300, 'new_value': 306}]
2025-06-28 12:01:03,169 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-28 12:01:03,649 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-28 12:01:03,649 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13758.29, 'new_value': 13981.29}, {'field': 'offline_amount', 'old_value': 25707.83, 'new_value': 26267.83}, {'field': 'total_amount', 'old_value': 39466.12, 'new_value': 40249.12}, {'field': 'order_count', 'old_value': 1343, 'new_value': 1375}]
2025-06-28 12:01:03,650 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-28 12:01:04,099 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-28 12:01:04,100 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41372.0, 'new_value': 45352.0}, {'field': 'total_amount', 'old_value': 41372.0, 'new_value': 45352.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-06-28 12:01:04,100 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-28 12:01:04,581 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-28 12:01:04,581 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 407384.2, 'new_value': 418337.8}, {'field': 'offline_amount', 'old_value': 85014.0, 'new_value': 87001.0}, {'field': 'total_amount', 'old_value': 492398.2, 'new_value': 505338.8}, {'field': 'order_count', 'old_value': 619, 'new_value': 636}]
2025-06-28 12:01:04,581 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-28 12:01:05,035 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-28 12:01:05,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30466.16, 'new_value': 32508.16}, {'field': 'total_amount', 'old_value': 30466.16, 'new_value': 32508.16}, {'field': 'order_count', 'old_value': 1134, 'new_value': 1181}]
2025-06-28 12:01:05,035 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZB
2025-06-28 12:01:05,505 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZB
2025-06-28 12:01:05,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75387.0, 'new_value': 81085.0}, {'field': 'total_amount', 'old_value': 75387.0, 'new_value': 81085.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-06-28 12:01:05,505 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-28 12:01:06,010 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-28 12:01:06,010 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 518087.11, 'new_value': 541508.24}, {'field': 'total_amount', 'old_value': 518087.11, 'new_value': 541508.24}, {'field': 'order_count', 'old_value': 3960, 'new_value': 4152}]
2025-06-28 12:01:06,010 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-28 12:01:06,507 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-28 12:01:06,507 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-06-28 12:01:06,507 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-28 12:01:06,957 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-28 12:01:06,957 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108377.42, 'new_value': 110436.14}, {'field': 'offline_amount', 'old_value': 519008.76, 'new_value': 539479.3}, {'field': 'total_amount', 'old_value': 627386.18, 'new_value': 649915.44}, {'field': 'order_count', 'old_value': 1754, 'new_value': 1812}]
2025-06-28 12:01:06,957 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-28 12:01:07,433 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-28 12:01:07,434 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109204.5, 'new_value': 114296.3}, {'field': 'total_amount', 'old_value': 109204.5, 'new_value': 114296.3}, {'field': 'order_count', 'old_value': 3078, 'new_value': 3224}]
2025-06-28 12:01:07,434 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8C
2025-06-28 12:01:07,856 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8C
2025-06-28 12:01:07,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130382.16, 'new_value': 136607.86}, {'field': 'total_amount', 'old_value': 131386.16, 'new_value': 137611.86}, {'field': 'order_count', 'old_value': 213, 'new_value': 230}]
2025-06-28 12:01:07,856 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-28 12:01:08,371 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-28 12:01:08,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158962.71, 'new_value': 206202.71}, {'field': 'total_amount', 'old_value': 159361.71, 'new_value': 206601.71}, {'field': 'order_count', 'old_value': 59, 'new_value': 77}]
2025-06-28 12:01:08,372 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-28 12:01:08,873 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-28 12:01:08,873 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15149.2, 'new_value': 15614.2}, {'field': 'offline_amount', 'old_value': 35730.8, 'new_value': 36014.7}, {'field': 'total_amount', 'old_value': 50880.0, 'new_value': 51628.9}, {'field': 'order_count', 'old_value': 171, 'new_value': 183}]
2025-06-28 12:01:08,874 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-28 12:01:09,347 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-28 12:01:09,347 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58127.77, 'new_value': 61848.92}, {'field': 'total_amount', 'old_value': 64909.02, 'new_value': 68630.17}, {'field': 'order_count', 'old_value': 234, 'new_value': 242}]
2025-06-28 12:01:09,347 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-28 12:01:09,921 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-28 12:01:09,921 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 193349.98, 'new_value': 202230.05}, {'field': 'offline_amount', 'old_value': 85730.95, 'new_value': 88611.95}, {'field': 'total_amount', 'old_value': 279080.93, 'new_value': 290842.0}, {'field': 'order_count', 'old_value': 1186, 'new_value': 1228}]
2025-06-28 12:01:09,921 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-28 12:01:10,426 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-28 12:01:10,427 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126014.0, 'new_value': 141919.0}, {'field': 'offline_amount', 'old_value': 266094.0, 'new_value': 269801.0}, {'field': 'total_amount', 'old_value': 392108.0, 'new_value': 411720.0}, {'field': 'order_count', 'old_value': 2923, 'new_value': 3076}]
2025-06-28 12:01:10,427 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-28 12:01:10,919 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-28 12:01:10,919 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142580.0, 'new_value': 148830.0}, {'field': 'total_amount', 'old_value': 142580.0, 'new_value': 148830.0}, {'field': 'order_count', 'old_value': 186, 'new_value': 219}]
2025-06-28 12:01:10,919 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-28 12:01:11,453 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-28 12:01:11,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 380967.67, 'new_value': 394205.39}, {'field': 'total_amount', 'old_value': 380967.67, 'new_value': 394205.39}, {'field': 'order_count', 'old_value': 1214, 'new_value': 1260}]
2025-06-28 12:01:11,453 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-28 12:01:11,906 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-28 12:01:11,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46274.0, 'new_value': 47540.0}, {'field': 'total_amount', 'old_value': 46643.0, 'new_value': 47909.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 75}]
2025-06-28 12:01:11,907 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-28 12:01:12,374 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-28 12:01:12,374 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76887.0, 'new_value': 79887.0}, {'field': 'offline_amount', 'old_value': 24424.7, 'new_value': 24444.9}, {'field': 'total_amount', 'old_value': 101311.7, 'new_value': 104331.9}, {'field': 'order_count', 'old_value': 348, 'new_value': 354}]
2025-06-28 12:01:12,374 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-28 12:01:12,833 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-28 12:01:12,833 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16258.0, 'new_value': 16656.0}, {'field': 'offline_amount', 'old_value': 70213.05, 'new_value': 71382.05}, {'field': 'total_amount', 'old_value': 86471.05, 'new_value': 88038.05}, {'field': 'order_count', 'old_value': 881, 'new_value': 907}]
2025-06-28 12:01:12,833 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-28 12:01:13,290 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-28 12:01:13,291 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6581.0, 'new_value': 6969.0}, {'field': 'offline_amount', 'old_value': 31105.3, 'new_value': 32872.7}, {'field': 'total_amount', 'old_value': 37686.3, 'new_value': 39841.7}, {'field': 'order_count', 'old_value': 1316, 'new_value': 1395}]
2025-06-28 12:01:13,291 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-28 12:01:13,750 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-28 12:01:13,750 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26196.28, 'new_value': 27604.64}, {'field': 'offline_amount', 'old_value': 49565.86, 'new_value': 51672.67}, {'field': 'total_amount', 'old_value': 75762.14, 'new_value': 79277.31}, {'field': 'order_count', 'old_value': 2814, 'new_value': 2944}]
2025-06-28 12:01:13,750 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-28 12:01:14,247 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-28 12:01:14,247 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98250.16, 'new_value': 101165.96}, {'field': 'offline_amount', 'old_value': 139240.54, 'new_value': 144159.81}, {'field': 'total_amount', 'old_value': 237490.7, 'new_value': 245325.77}, {'field': 'order_count', 'old_value': 1302, 'new_value': 1323}]
2025-06-28 12:01:14,247 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-28 12:01:14,731 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-28 12:01:14,731 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77411.5, 'new_value': 81455.9}, {'field': 'total_amount', 'old_value': 77411.5, 'new_value': 81455.9}, {'field': 'order_count', 'old_value': 2109, 'new_value': 2191}]
2025-06-28 12:01:14,732 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-28 12:01:15,188 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-28 12:01:15,189 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42316.82, 'new_value': 45930.18}, {'field': 'offline_amount', 'old_value': 56440.74, 'new_value': 56665.15}, {'field': 'total_amount', 'old_value': 98757.56, 'new_value': 102595.33}, {'field': 'order_count', 'old_value': 581, 'new_value': 593}]
2025-06-28 12:01:15,189 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-28 12:01:15,697 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-28 12:01:15,697 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18055.0, 'new_value': 18894.0}, {'field': 'total_amount', 'old_value': 18055.0, 'new_value': 18894.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 59}]
2025-06-28 12:01:15,697 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHD
2025-06-28 12:01:16,129 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHD
2025-06-28 12:01:16,129 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36240.0, 'new_value': 46240.0}, {'field': 'total_amount', 'old_value': 36240.0, 'new_value': 46240.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-06-28 12:01:16,129 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-28 12:01:16,575 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-28 12:01:16,575 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14443.04, 'new_value': 15080.34}, {'field': 'offline_amount', 'old_value': 218896.22, 'new_value': 227803.22}, {'field': 'total_amount', 'old_value': 233339.26, 'new_value': 242883.56}, {'field': 'order_count', 'old_value': 1562, 'new_value': 1635}]
2025-06-28 12:01:16,575 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-28 12:01:17,044 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-28 12:01:17,044 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44087.6, 'new_value': 46345.3}, {'field': 'total_amount', 'old_value': 44087.6, 'new_value': 46345.3}, {'field': 'order_count', 'old_value': 49, 'new_value': 52}]
2025-06-28 12:01:17,045 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-28 12:01:17,509 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-28 12:01:17,509 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 324746.69, 'new_value': 339115.16}, {'field': 'total_amount', 'old_value': 324746.69, 'new_value': 339115.16}, {'field': 'order_count', 'old_value': 9573, 'new_value': 10042}]
2025-06-28 12:01:17,509 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-28 12:01:17,923 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-28 12:01:17,923 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 184696.14, 'new_value': 193316.25}, {'field': 'offline_amount', 'old_value': 157285.21, 'new_value': 162489.79}, {'field': 'total_amount', 'old_value': 341981.35, 'new_value': 355806.04}, {'field': 'order_count', 'old_value': 3224, 'new_value': 3377}]
2025-06-28 12:01:17,923 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-28 12:01:18,451 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-28 12:01:18,451 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11275.0, 'new_value': 11825.0}, {'field': 'total_amount', 'old_value': 11275.0, 'new_value': 11825.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 60}]
2025-06-28 12:01:18,451 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-28 12:01:18,982 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-28 12:01:18,982 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43268.0, 'new_value': 45228.0}, {'field': 'total_amount', 'old_value': 78474.0, 'new_value': 80434.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 101}]
2025-06-28 12:01:18,983 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTD
2025-06-28 12:01:19,571 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTD
2025-06-28 12:01:19,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16575.0, 'new_value': 42575.0}, {'field': 'total_amount', 'old_value': 16575.0, 'new_value': 42575.0}]
2025-06-28 12:01:19,571 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-28 12:01:19,993 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-28 12:01:19,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131334.0, 'new_value': 131226.0}, {'field': 'total_amount', 'old_value': 286869.0, 'new_value': 286761.0}]
2025-06-28 12:01:19,993 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-28 12:01:20,576 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-28 12:01:20,576 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32913.0, 'new_value': 33833.0}, {'field': 'total_amount', 'old_value': 32913.0, 'new_value': 33833.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-06-28 12:01:20,577 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-28 12:01:21,046 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-28 12:01:21,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49271.0, 'new_value': 53399.0}, {'field': 'total_amount', 'old_value': 49271.0, 'new_value': 53399.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 36}]
2025-06-28 12:01:21,047 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0R
2025-06-28 12:01:21,523 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0R
2025-06-28 12:01:21,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54077.0, 'new_value': 62784.0}, {'field': 'total_amount', 'old_value': 54077.0, 'new_value': 62784.0}, {'field': 'order_count', 'old_value': 167, 'new_value': 194}]
2025-06-28 12:01:21,523 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-28 12:01:21,949 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-28 12:01:21,949 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131781.45, 'new_value': 137319.22}, {'field': 'total_amount', 'old_value': 131781.45, 'new_value': 137319.22}, {'field': 'order_count', 'old_value': 3788, 'new_value': 3971}]
2025-06-28 12:01:21,949 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-28 12:01:22,470 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-28 12:01:22,470 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50805.9, 'new_value': 51766.93}, {'field': 'total_amount', 'old_value': 124046.05, 'new_value': 125007.08}, {'field': 'order_count', 'old_value': 101, 'new_value': 102}]
2025-06-28 12:01:22,470 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-28 12:01:22,886 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-28 12:01:22,886 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16404.0, 'new_value': 16868.0}, {'field': 'total_amount', 'old_value': 16404.0, 'new_value': 16868.0}, {'field': 'order_count', 'old_value': 280, 'new_value': 288}]
2025-06-28 12:01:22,887 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-28 12:01:23,340 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-28 12:01:23,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83634.16, 'new_value': 85859.06}, {'field': 'total_amount', 'old_value': 83634.16, 'new_value': 85859.06}, {'field': 'order_count', 'old_value': 47, 'new_value': 49}]
2025-06-28 12:01:23,341 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIR
2025-06-28 12:01:23,832 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIR
2025-06-28 12:01:23,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15044.0, 'new_value': 18572.0}, {'field': 'total_amount', 'old_value': 15044.0, 'new_value': 18572.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 21}]
2025-06-28 12:01:23,833 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-28 12:01:24,297 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-28 12:01:24,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227702.76, 'new_value': 237255.76}, {'field': 'total_amount', 'old_value': 227702.76, 'new_value': 237255.76}, {'field': 'order_count', 'old_value': 1268, 'new_value': 1319}]
2025-06-28 12:01:24,298 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-28 12:01:24,762 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-28 12:01:24,762 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83632.15, 'new_value': 107352.95}, {'field': 'total_amount', 'old_value': 83632.15, 'new_value': 107352.95}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-06-28 12:01:24,762 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-28 12:01:25,215 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-28 12:01:25,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26922.7, 'new_value': 27802.7}, {'field': 'total_amount', 'old_value': 26922.7, 'new_value': 27802.7}, {'field': 'order_count', 'old_value': 258, 'new_value': 264}]
2025-06-28 12:01:25,216 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-28 12:01:25,734 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-28 12:01:25,735 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48191.19, 'new_value': 50116.99}, {'field': 'total_amount', 'old_value': 48200.19, 'new_value': 50125.99}, {'field': 'order_count', 'old_value': 1986, 'new_value': 2081}]
2025-06-28 12:01:25,735 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-28 12:01:26,172 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-28 12:01:26,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 356390.96, 'new_value': 367575.96}, {'field': 'total_amount', 'old_value': 392374.96, 'new_value': 403559.96}, {'field': 'order_count', 'old_value': 2071, 'new_value': 2131}]
2025-06-28 12:01:26,172 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-28 12:01:26,568 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-28 12:01:26,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76999.0, 'new_value': 79293.0}, {'field': 'total_amount', 'old_value': 79814.0, 'new_value': 82108.0}, {'field': 'order_count', 'old_value': 310, 'new_value': 323}]
2025-06-28 12:01:26,569 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-28 12:01:27,054 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-28 12:01:27,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 636492.51, 'new_value': 662399.4}, {'field': 'total_amount', 'old_value': 636492.51, 'new_value': 662399.4}, {'field': 'order_count', 'old_value': 10921, 'new_value': 11465}]
2025-06-28 12:01:27,055 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-28 12:01:27,529 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-28 12:01:27,529 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 195663.31, 'new_value': 203767.78}, {'field': 'offline_amount', 'old_value': 43909.49, 'new_value': 44954.7}, {'field': 'total_amount', 'old_value': 239572.8, 'new_value': 248722.48}, {'field': 'order_count', 'old_value': 959, 'new_value': 997}]
2025-06-28 12:01:27,529 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAS
2025-06-28 12:01:28,027 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAS
2025-06-28 12:01:28,027 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31954.53, 'new_value': 36536.23}, {'field': 'offline_amount', 'old_value': 19936.6, 'new_value': 22191.65}, {'field': 'total_amount', 'old_value': 51891.13, 'new_value': 58727.88}, {'field': 'order_count', 'old_value': 3212, 'new_value': 3655}]
2025-06-28 12:01:28,027 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-28 12:01:28,460 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-28 12:01:28,460 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69808.0, 'new_value': 71605.0}, {'field': 'total_amount', 'old_value': 69808.0, 'new_value': 71605.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 52}]
2025-06-28 12:01:28,460 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-28 12:01:28,921 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-28 12:01:28,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 597962.3, 'new_value': 626909.02}, {'field': 'total_amount', 'old_value': 597962.3, 'new_value': 626909.02}, {'field': 'order_count', 'old_value': 1931, 'new_value': 2020}]
2025-06-28 12:01:28,922 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-28 12:01:29,531 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-28 12:01:29,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43227.1, 'new_value': 46252.1}, {'field': 'total_amount', 'old_value': 43227.1, 'new_value': 46252.1}, {'field': 'order_count', 'old_value': 105, 'new_value': 111}]
2025-06-28 12:01:29,532 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-28 12:01:29,997 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-28 12:01:29,997 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102580.0, 'new_value': 107059.0}, {'field': 'offline_amount', 'old_value': 450627.0, 'new_value': 460225.0}, {'field': 'total_amount', 'old_value': 553207.0, 'new_value': 567284.0}, {'field': 'order_count', 'old_value': 720, 'new_value': 751}]
2025-06-28 12:01:29,997 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTS
2025-06-28 12:01:30,442 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTS
2025-06-28 12:01:30,442 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4066.98, 'new_value': 4689.37}, {'field': 'offline_amount', 'old_value': 136198.75, 'new_value': 152042.35}, {'field': 'total_amount', 'old_value': 140265.73, 'new_value': 156731.72}, {'field': 'order_count', 'old_value': 2147, 'new_value': 2421}]
2025-06-28 12:01:30,442 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-28 12:01:30,872 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-28 12:01:30,872 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233361.6, 'new_value': 236841.6}, {'field': 'total_amount', 'old_value': 233361.6, 'new_value': 236841.6}, {'field': 'order_count', 'old_value': 57, 'new_value': 58}]
2025-06-28 12:01:30,872 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-28 12:01:31,383 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-28 12:01:31,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1690760.41, 'new_value': 1750532.41}, {'field': 'total_amount', 'old_value': 1749239.11, 'new_value': 1809011.11}, {'field': 'order_count', 'old_value': 3355, 'new_value': 3495}]
2025-06-28 12:01:31,384 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-28 12:01:31,902 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-28 12:01:31,902 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32221.33, 'new_value': 36406.63}, {'field': 'total_amount', 'old_value': 32221.33, 'new_value': 36406.63}, {'field': 'order_count', 'old_value': 58, 'new_value': 62}]
2025-06-28 12:01:31,903 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-28 12:01:32,381 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-28 12:01:32,382 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 152898.35, 'new_value': 159905.69}, {'field': 'offline_amount', 'old_value': 105610.0, 'new_value': 109883.0}, {'field': 'total_amount', 'old_value': 258508.35, 'new_value': 269788.69}, {'field': 'order_count', 'old_value': 2642, 'new_value': 2763}]
2025-06-28 12:01:32,382 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-28 12:01:32,810 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-28 12:01:32,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 593261.63, 'new_value': 616048.83}, {'field': 'total_amount', 'old_value': 593576.99, 'new_value': 616364.19}, {'field': 'order_count', 'old_value': 1588, 'new_value': 1655}]
2025-06-28 12:01:32,811 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-28 12:01:33,257 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-28 12:01:33,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 287616.1, 'new_value': 300412.4}, {'field': 'total_amount', 'old_value': 287616.1, 'new_value': 300412.4}, {'field': 'order_count', 'old_value': 8733, 'new_value': 9098}]
2025-06-28 12:01:33,257 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-28 12:01:33,699 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-28 12:01:33,699 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127457.0, 'new_value': 145054.0}, {'field': 'total_amount', 'old_value': 127457.0, 'new_value': 145054.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 39}]
2025-06-28 12:01:33,699 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-28 12:01:34,121 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-28 12:01:34,121 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 687039.0, 'new_value': 700787.0}, {'field': 'total_amount', 'old_value': 687039.0, 'new_value': 700787.0}, {'field': 'order_count', 'old_value': 3361, 'new_value': 3475}]
2025-06-28 12:01:34,121 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-28 12:01:34,573 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-28 12:01:34,573 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 252857.51, 'new_value': 258361.93}, {'field': 'total_amount', 'old_value': 338978.87, 'new_value': 344483.29}, {'field': 'order_count', 'old_value': 676, 'new_value': 688}]
2025-06-28 12:01:34,574 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKT
2025-06-28 12:01:35,053 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKT
2025-06-28 12:01:35,053 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47250.1, 'new_value': 52796.2}, {'field': 'total_amount', 'old_value': 47250.1, 'new_value': 52796.2}, {'field': 'order_count', 'old_value': 274, 'new_value': 315}]
2025-06-28 12:01:35,053 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-28 12:01:35,584 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-28 12:01:35,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 499476.0, 'new_value': 535735.0}, {'field': 'total_amount', 'old_value': 499476.0, 'new_value': 535735.0}, {'field': 'order_count', 'old_value': 529, 'new_value': 558}]
2025-06-28 12:01:35,584 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-28 12:01:36,015 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-28 12:01:36,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237944.0, 'new_value': 249047.0}, {'field': 'total_amount', 'old_value': 237944.0, 'new_value': 249047.0}, {'field': 'order_count', 'old_value': 243, 'new_value': 251}]
2025-06-28 12:01:36,015 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-28 12:01:36,484 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-28 12:01:36,484 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73502.0, 'new_value': 78502.0}, {'field': 'offline_amount', 'old_value': 128450.0, 'new_value': 132442.0}, {'field': 'total_amount', 'old_value': 201952.0, 'new_value': 210944.0}, {'field': 'order_count', 'old_value': 4611, 'new_value': 4827}]
2025-06-28 12:01:36,484 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-28 12:01:36,969 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-28 12:01:36,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29919.21, 'new_value': 31443.4}, {'field': 'offline_amount', 'old_value': 27592.45, 'new_value': 28282.25}, {'field': 'total_amount', 'old_value': 57511.66, 'new_value': 59725.65}, {'field': 'order_count', 'old_value': 2656, 'new_value': 2762}]
2025-06-28 12:01:36,969 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-28 12:01:37,461 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-28 12:01:37,461 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21782.14, 'new_value': 22852.19}, {'field': 'offline_amount', 'old_value': 139079.95, 'new_value': 145464.28}, {'field': 'total_amount', 'old_value': 160862.09, 'new_value': 168316.47}, {'field': 'order_count', 'old_value': 7820, 'new_value': 8182}]
2025-06-28 12:01:37,461 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-28 12:01:37,841 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-28 12:01:37,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225814.0, 'new_value': 230412.0}, {'field': 'total_amount', 'old_value': 225814.0, 'new_value': 230412.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 44}]
2025-06-28 12:01:37,842 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-28 12:01:38,332 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-28 12:01:38,332 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60240.79, 'new_value': 62184.07}, {'field': 'offline_amount', 'old_value': 38677.78, 'new_value': 40407.3}, {'field': 'total_amount', 'old_value': 98918.57, 'new_value': 102591.37}, {'field': 'order_count', 'old_value': 5862, 'new_value': 6079}]
2025-06-28 12:01:38,332 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-28 12:01:38,759 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-28 12:01:38,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23474.0, 'new_value': 25042.0}, {'field': 'total_amount', 'old_value': 23474.0, 'new_value': 25042.0}, {'field': 'order_count', 'old_value': 220, 'new_value': 235}]
2025-06-28 12:01:38,759 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-28 12:01:39,232 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-28 12:01:39,233 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93502.13, 'new_value': 97848.82}, {'field': 'offline_amount', 'old_value': 95912.61, 'new_value': 100799.73}, {'field': 'total_amount', 'old_value': 189414.74, 'new_value': 198648.55}, {'field': 'order_count', 'old_value': 7797, 'new_value': 8134}]
2025-06-28 12:01:39,233 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-28 12:01:39,726 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-28 12:01:39,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 517986.99, 'new_value': 540096.86}, {'field': 'total_amount', 'old_value': 517986.99, 'new_value': 540096.86}, {'field': 'order_count', 'old_value': 5772, 'new_value': 6034}]
2025-06-28 12:01:39,726 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-28 12:01:40,193 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-28 12:01:40,193 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111298.0, 'new_value': 116875.0}, {'field': 'total_amount', 'old_value': 111298.0, 'new_value': 116875.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 75}]
2025-06-28 12:01:40,193 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-28 12:01:40,640 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-28 12:01:40,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124772.0, 'new_value': 129380.0}, {'field': 'total_amount', 'old_value': 124772.0, 'new_value': 129380.0}, {'field': 'order_count', 'old_value': 459, 'new_value': 477}]
2025-06-28 12:01:40,640 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-28 12:01:41,136 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-28 12:01:41,136 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 298987.5, 'new_value': 306063.0}, {'field': 'offline_amount', 'old_value': 113628.3, 'new_value': 115433.1}, {'field': 'total_amount', 'old_value': 412615.8, 'new_value': 421496.1}, {'field': 'order_count', 'old_value': 1280, 'new_value': 1310}]
2025-06-28 12:01:41,136 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-28 12:01:41,643 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-28 12:01:41,643 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 259056.0, 'new_value': 270264.0}, {'field': 'total_amount', 'old_value': 259056.0, 'new_value': 270264.0}, {'field': 'order_count', 'old_value': 21588, 'new_value': 22522}]
2025-06-28 12:01:41,643 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6L
2025-06-28 12:01:42,100 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6L
2025-06-28 12:01:42,100 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188529.0, 'new_value': 196521.0}, {'field': 'total_amount', 'old_value': 188529.0, 'new_value': 196521.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 42}]
2025-06-28 12:01:42,100 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBL
2025-06-28 12:01:42,602 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBL
2025-06-28 12:01:42,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1203.52, 'new_value': 1780.82}, {'field': 'offline_amount', 'old_value': 40406.5, 'new_value': 42243.0}, {'field': 'total_amount', 'old_value': 41610.02, 'new_value': 44023.82}, {'field': 'order_count', 'old_value': 278, 'new_value': 298}]
2025-06-28 12:01:42,603 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-28 12:01:43,049 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-28 12:01:43,050 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134659.92, 'new_value': 139489.08}, {'field': 'offline_amount', 'old_value': 356604.56, 'new_value': 379998.59}, {'field': 'total_amount', 'old_value': 491264.48, 'new_value': 519487.67}, {'field': 'order_count', 'old_value': 4831, 'new_value': 5028}]
2025-06-28 12:01:43,050 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-28 12:01:43,474 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-28 12:01:43,475 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 181107.22, 'new_value': 189685.41}, {'field': 'offline_amount', 'old_value': 214308.71, 'new_value': 224308.71}, {'field': 'total_amount', 'old_value': 395415.93, 'new_value': 413994.12}, {'field': 'order_count', 'old_value': 1278, 'new_value': 1339}]
2025-06-28 12:01:43,475 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-28 12:01:43,933 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-28 12:01:43,934 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178992.0, 'new_value': 184584.0}, {'field': 'total_amount', 'old_value': 178992.0, 'new_value': 184584.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 133}]
2025-06-28 12:01:43,934 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-28 12:01:44,457 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-28 12:01:44,457 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 147350.57, 'new_value': 147363.57}, {'field': 'offline_amount', 'old_value': 305636.42, 'new_value': 320362.42}, {'field': 'total_amount', 'old_value': 452986.99, 'new_value': 467725.99}, {'field': 'order_count', 'old_value': 3898, 'new_value': 3947}]
2025-06-28 12:01:44,457 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJL
2025-06-28 12:01:44,898 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJL
2025-06-28 12:01:44,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54313.0, 'new_value': 54451.0}, {'field': 'total_amount', 'old_value': 54313.0, 'new_value': 54451.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-06-28 12:01:44,899 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKL
2025-06-28 12:01:45,349 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKL
2025-06-28 12:01:45,349 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6686.9, 'new_value': 7038.9}, {'field': 'offline_amount', 'old_value': 35157.9, 'new_value': 39750.9}, {'field': 'total_amount', 'old_value': 41844.8, 'new_value': 46789.8}, {'field': 'order_count', 'old_value': 61, 'new_value': 70}]
2025-06-28 12:01:45,349 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-28 12:01:45,796 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-28 12:01:45,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223377.92, 'new_value': 234125.72}, {'field': 'total_amount', 'old_value': 223377.92, 'new_value': 234125.72}, {'field': 'order_count', 'old_value': 806, 'new_value': 848}]
2025-06-28 12:01:45,796 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-28 12:01:46,252 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-28 12:01:46,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53736.0, 'new_value': 55180.0}, {'field': 'total_amount', 'old_value': 56348.0, 'new_value': 57792.0}, {'field': 'order_count', 'old_value': 131, 'new_value': 135}]
2025-06-28 12:01:46,253 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTL
2025-06-28 12:01:46,725 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTL
2025-06-28 12:01:46,725 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3828.6, 'new_value': 3955.8}, {'field': 'offline_amount', 'old_value': 79353.6, 'new_value': 90333.6}, {'field': 'total_amount', 'old_value': 83182.2, 'new_value': 94289.4}, {'field': 'order_count', 'old_value': 522, 'new_value': 607}]
2025-06-28 12:01:46,725 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-28 12:01:47,167 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-28 12:01:47,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 347347.58, 'new_value': 354502.17}, {'field': 'total_amount', 'old_value': 347347.58, 'new_value': 354502.17}, {'field': 'order_count', 'old_value': 1202, 'new_value': 1232}]
2025-06-28 12:01:47,168 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-28 12:01:47,633 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-28 12:01:47,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39738.1, 'new_value': 40767.1}, {'field': 'total_amount', 'old_value': 39738.1, 'new_value': 40767.1}, {'field': 'order_count', 'old_value': 272, 'new_value': 280}]
2025-06-28 12:01:47,633 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-28 12:01:48,128 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-28 12:01:48,128 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 172124.0, 'new_value': 180442.0}, {'field': 'total_amount', 'old_value': 180743.0, 'new_value': 189061.0}, {'field': 'order_count', 'old_value': 13291, 'new_value': 13930}]
2025-06-28 12:01:48,128 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-28 12:01:48,541 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-28 12:01:48,542 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84539.32, 'new_value': 88250.05}, {'field': 'offline_amount', 'old_value': 896911.67, 'new_value': 944338.16}, {'field': 'total_amount', 'old_value': 981450.99, 'new_value': 1032588.21}, {'field': 'order_count', 'old_value': 4119, 'new_value': 4309}]
2025-06-28 12:01:48,542 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-28 12:01:48,976 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-28 12:01:48,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 243221.52, 'new_value': 245943.52}, {'field': 'offline_amount', 'old_value': 225363.79, 'new_value': 228128.79}, {'field': 'total_amount', 'old_value': 468585.31, 'new_value': 474072.31}, {'field': 'order_count', 'old_value': 3108, 'new_value': 3158}]
2025-06-28 12:01:48,976 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-28 12:01:49,456 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-28 12:01:49,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28947.92, 'new_value': 34097.92}, {'field': 'total_amount', 'old_value': 29505.92, 'new_value': 34655.92}, {'field': 'order_count', 'old_value': 69, 'new_value': 77}]
2025-06-28 12:01:49,456 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-28 12:01:49,888 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-28 12:01:49,888 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10454.0, 'new_value': 12107.0}, {'field': 'total_amount', 'old_value': 12862.0, 'new_value': 14515.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 128}]
2025-06-28 12:01:49,888 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-28 12:01:50,381 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-28 12:01:50,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 249855.9, 'new_value': 258308.37}, {'field': 'total_amount', 'old_value': 249855.9, 'new_value': 258308.37}, {'field': 'order_count', 'old_value': 1580, 'new_value': 1641}]
2025-06-28 12:01:50,381 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-28 12:01:50,855 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-28 12:01:50,855 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 159225.0, 'new_value': 165058.0}, {'field': 'offline_amount', 'old_value': 106650.0, 'new_value': 109794.0}, {'field': 'total_amount', 'old_value': 265875.0, 'new_value': 274852.0}, {'field': 'order_count', 'old_value': 3817, 'new_value': 3953}]
2025-06-28 12:01:50,855 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-28 12:01:51,273 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-28 12:01:51,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1579434.46, 'new_value': 1649969.18}, {'field': 'offline_amount', 'old_value': 349443.8, 'new_value': 362060.8}, {'field': 'total_amount', 'old_value': 1928878.26, 'new_value': 2012029.98}, {'field': 'order_count', 'old_value': 7169, 'new_value': 7467}]
2025-06-28 12:01:51,273 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-28 12:01:51,829 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-28 12:01:51,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31665.46, 'new_value': 32208.46}, {'field': 'total_amount', 'old_value': 58866.4, 'new_value': 59409.4}, {'field': 'order_count', 'old_value': 6703, 'new_value': 6707}]
2025-06-28 12:01:51,830 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-28 12:01:52,354 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-28 12:01:52,354 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51203.24, 'new_value': 53155.25}, {'field': 'offline_amount', 'old_value': 470760.08, 'new_value': 489717.66}, {'field': 'total_amount', 'old_value': 521963.32, 'new_value': 542872.91}, {'field': 'order_count', 'old_value': 2319, 'new_value': 2404}]
2025-06-28 12:01:52,354 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-28 12:01:52,795 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-28 12:01:52,795 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 647925.0, 'new_value': 655219.0}, {'field': 'total_amount', 'old_value': 647925.0, 'new_value': 655219.0}, {'field': 'order_count', 'old_value': 440, 'new_value': 451}]
2025-06-28 12:01:52,795 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-28 12:01:53,258 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-28 12:01:53,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193299.2, 'new_value': 209472.4}, {'field': 'total_amount', 'old_value': 326963.7, 'new_value': 343136.9}, {'field': 'order_count', 'old_value': 8892, 'new_value': 9341}]
2025-06-28 12:01:53,259 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-28 12:01:53,748 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-28 12:01:53,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 325551.4, 'new_value': 329309.1}, {'field': 'total_amount', 'old_value': 325551.4, 'new_value': 329309.1}, {'field': 'order_count', 'old_value': 379, 'new_value': 384}]
2025-06-28 12:01:53,749 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-28 12:01:54,182 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-28 12:01:54,182 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 368998.8, 'new_value': 378777.2}, {'field': 'total_amount', 'old_value': 500616.0, 'new_value': 510394.4}, {'field': 'order_count', 'old_value': 3570, 'new_value': 3579}]
2025-06-28 12:01:54,182 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-28 12:01:54,602 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-28 12:01:54,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 177263.7, 'new_value': 184551.6}, {'field': 'offline_amount', 'old_value': 266059.12, 'new_value': 279377.68}, {'field': 'total_amount', 'old_value': 443322.82, 'new_value': 463929.28}, {'field': 'order_count', 'old_value': 4009, 'new_value': 4177}]
2025-06-28 12:01:54,602 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXM
2025-06-28 12:01:55,080 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXM
2025-06-28 12:01:55,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5555.0, 'new_value': 6723.0}, {'field': 'total_amount', 'old_value': 5555.0, 'new_value': 6723.0}, {'field': 'order_count', 'old_value': 182, 'new_value': 207}]
2025-06-28 12:01:55,080 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYM
2025-06-28 12:01:55,574 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYM
2025-06-28 12:01:55,574 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48224.31, 'new_value': 50394.97}, {'field': 'offline_amount', 'old_value': 57346.59, 'new_value': 59155.04}, {'field': 'total_amount', 'old_value': 105570.9, 'new_value': 109550.01}, {'field': 'order_count', 'old_value': 2667, 'new_value': 2765}]
2025-06-28 12:01:55,574 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-28 12:01:55,970 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-28 12:01:55,970 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112517.0, 'new_value': 116691.0}, {'field': 'total_amount', 'old_value': 112517.0, 'new_value': 116691.0}, {'field': 'order_count', 'old_value': 283, 'new_value': 296}]
2025-06-28 12:01:55,970 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0N
2025-06-28 12:01:56,462 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0N
2025-06-28 12:01:56,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25528.0, 'new_value': 49488.0}, {'field': 'total_amount', 'old_value': 25528.0, 'new_value': 49488.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 17}]
2025-06-28 12:01:56,462 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR01
2025-06-28 12:01:56,907 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR01
2025-06-28 12:01:56,907 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125551.0, 'new_value': 133305.0}, {'field': 'offline_amount', 'old_value': 118369.0, 'new_value': 122922.0}, {'field': 'total_amount', 'old_value': 243920.0, 'new_value': 256227.0}, {'field': 'order_count', 'old_value': 9626, 'new_value': 10096}]
2025-06-28 12:01:56,908 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-28 12:01:57,390 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-28 12:01:57,390 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 835253.0, 'new_value': 865178.0}, {'field': 'total_amount', 'old_value': 835253.0, 'new_value': 865178.0}, {'field': 'order_count', 'old_value': 1055, 'new_value': 1093}]
2025-06-28 12:01:57,390 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-28 12:01:57,820 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-28 12:01:57,820 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98810.3, 'new_value': 104109.7}, {'field': 'total_amount', 'old_value': 98810.3, 'new_value': 104109.7}, {'field': 'order_count', 'old_value': 455, 'new_value': 478}]
2025-06-28 12:01:57,821 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-28 12:01:58,296 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-28 12:01:58,297 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41585.99, 'new_value': 43697.85}, {'field': 'offline_amount', 'old_value': 47222.79, 'new_value': 49459.11}, {'field': 'total_amount', 'old_value': 88808.78, 'new_value': 93156.96}, {'field': 'order_count', 'old_value': 4623, 'new_value': 4850}]
2025-06-28 12:01:58,297 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-28 12:01:58,708 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-28 12:01:58,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142110.9, 'new_value': 146241.5}, {'field': 'total_amount', 'old_value': 142110.9, 'new_value': 146241.5}, {'field': 'order_count', 'old_value': 677, 'new_value': 697}]
2025-06-28 12:01:58,708 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-28 12:01:59,172 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-28 12:01:59,173 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 229360.0, 'new_value': 238480.0}, {'field': 'total_amount', 'old_value': 229360.0, 'new_value': 238480.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 48}]
2025-06-28 12:01:59,173 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-28 12:01:59,655 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-28 12:01:59,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48406.5, 'new_value': 50853.5}, {'field': 'total_amount', 'old_value': 48406.5, 'new_value': 50853.5}, {'field': 'order_count', 'old_value': 62, 'new_value': 63}]
2025-06-28 12:01:59,655 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM511
2025-06-28 12:02:00,119 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM511
2025-06-28 12:02:00,119 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149532.0, 'new_value': 152652.0}, {'field': 'total_amount', 'old_value': 149532.0, 'new_value': 152652.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-06-28 12:02:00,119 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-28 12:02:00,601 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-28 12:02:00,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16507.71, 'new_value': 16981.2}, {'field': 'offline_amount', 'old_value': 294521.1, 'new_value': 309870.7}, {'field': 'total_amount', 'old_value': 311028.81, 'new_value': 326851.9}, {'field': 'order_count', 'old_value': 1642, 'new_value': 1728}]
2025-06-28 12:02:00,610 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-28 12:02:01,058 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-28 12:02:01,058 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34531.0, 'new_value': 35610.0}, {'field': 'total_amount', 'old_value': 34531.0, 'new_value': 35610.0}, {'field': 'order_count', 'old_value': 104, 'new_value': 108}]
2025-06-28 12:02:01,059 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-28 12:02:01,524 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-28 12:02:01,524 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17553.95, 'new_value': 18219.95}, {'field': 'offline_amount', 'old_value': 142807.0, 'new_value': 152807.0}, {'field': 'total_amount', 'old_value': 160360.95, 'new_value': 171026.95}, {'field': 'order_count', 'old_value': 86, 'new_value': 88}]
2025-06-28 12:02:01,524 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-28 12:02:02,037 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-28 12:02:02,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49835.0, 'new_value': 49853.0}, {'field': 'total_amount', 'old_value': 49835.0, 'new_value': 49853.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 51}]
2025-06-28 12:02:02,037 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-28 12:02:02,493 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-28 12:02:02,493 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 257917.76, 'new_value': 266253.22}, {'field': 'offline_amount', 'old_value': 315873.64, 'new_value': 326932.74}, {'field': 'total_amount', 'old_value': 573791.4, 'new_value': 593185.96}, {'field': 'order_count', 'old_value': 18310, 'new_value': 18932}]
2025-06-28 12:02:02,493 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-28 12:02:02,953 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-28 12:02:02,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66978.64, 'new_value': 69938.56}, {'field': 'total_amount', 'old_value': 66978.64, 'new_value': 69938.56}, {'field': 'order_count', 'old_value': 1033, 'new_value': 1058}]
2025-06-28 12:02:02,954 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-28 12:02:03,404 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-28 12:02:03,405 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167892.2, 'new_value': 178327.9}, {'field': 'total_amount', 'old_value': 167892.2, 'new_value': 178327.9}, {'field': 'order_count', 'old_value': 5451, 'new_value': 5764}]
2025-06-28 12:02:03,405 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF11
2025-06-28 12:02:03,855 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF11
2025-06-28 12:02:03,855 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28778.0, 'new_value': 29077.0}, {'field': 'total_amount', 'old_value': 28778.0, 'new_value': 29077.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-28 12:02:03,855 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-28 12:02:04,299 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-28 12:02:04,299 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233443.97, 'new_value': 248057.97}, {'field': 'total_amount', 'old_value': 244070.97, 'new_value': 258684.97}, {'field': 'order_count', 'old_value': 1027, 'new_value': 1095}]
2025-06-28 12:02:04,299 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-28 12:02:04,727 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-28 12:02:04,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17179.03, 'new_value': 17630.93}, {'field': 'total_amount', 'old_value': 24129.16, 'new_value': 24581.06}, {'field': 'order_count', 'old_value': 260, 'new_value': 262}]
2025-06-28 12:02:04,728 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-28 12:02:05,240 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-28 12:02:05,240 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36654.73, 'new_value': 38698.79}, {'field': 'total_amount', 'old_value': 36654.73, 'new_value': 38698.79}, {'field': 'order_count', 'old_value': 806, 'new_value': 849}]
2025-06-28 12:02:05,240 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-28 12:02:05,761 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-28 12:02:05,761 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64105.93, 'new_value': 69636.81}, {'field': 'offline_amount', 'old_value': 308287.3, 'new_value': 313866.16}, {'field': 'total_amount', 'old_value': 372393.23, 'new_value': 383502.97}, {'field': 'order_count', 'old_value': 5128, 'new_value': 5265}]
2025-06-28 12:02:05,761 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-28 12:02:06,249 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-28 12:02:06,249 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 350544.0, 'new_value': 365544.0}, {'field': 'total_amount', 'old_value': 364102.0, 'new_value': 379102.0}, {'field': 'order_count', 'old_value': 307, 'new_value': 322}]
2025-06-28 12:02:06,249 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-28 12:02:06,700 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-28 12:02:06,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131265.0, 'new_value': 135049.0}, {'field': 'total_amount', 'old_value': 131433.0, 'new_value': 135217.0}, {'field': 'order_count', 'old_value': 386, 'new_value': 398}]
2025-06-28 12:02:06,700 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-28 12:02:07,121 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-28 12:02:07,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61678.01, 'new_value': 65019.01}, {'field': 'total_amount', 'old_value': 66354.51, 'new_value': 69695.51}, {'field': 'order_count', 'old_value': 396, 'new_value': 431}]
2025-06-28 12:02:07,122 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-28 12:02:07,590 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-28 12:02:07,590 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17445.0, 'new_value': 19269.0}, {'field': 'offline_amount', 'old_value': 26881.0, 'new_value': 27962.0}, {'field': 'total_amount', 'old_value': 44326.0, 'new_value': 47231.0}, {'field': 'order_count', 'old_value': 363, 'new_value': 378}]
2025-06-28 12:02:07,600 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-28 12:02:08,041 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-28 12:02:08,041 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 146391.75, 'new_value': 153630.09}, {'field': 'offline_amount', 'old_value': 44964.27, 'new_value': 46759.15}, {'field': 'total_amount', 'old_value': 191356.02, 'new_value': 200389.24}, {'field': 'order_count', 'old_value': 11418, 'new_value': 11896}]
2025-06-28 12:02:08,041 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-28 12:02:08,473 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-28 12:02:08,473 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 143617.5, 'new_value': 148498.26}, {'field': 'total_amount', 'old_value': 143617.5, 'new_value': 148498.26}, {'field': 'order_count', 'old_value': 690, 'new_value': 718}]
2025-06-28 12:02:08,473 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-28 12:02:08,982 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-28 12:02:08,982 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 374669.7, 'new_value': 394836.7}, {'field': 'total_amount', 'old_value': 374669.7, 'new_value': 394836.7}, {'field': 'order_count', 'old_value': 432, 'new_value': 448}]
2025-06-28 12:02:08,982 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-28 12:02:09,440 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-28 12:02:09,440 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67695.0, 'new_value': 68815.0}, {'field': 'offline_amount', 'old_value': 318408.0, 'new_value': 330633.0}, {'field': 'total_amount', 'old_value': 386103.0, 'new_value': 399448.0}, {'field': 'order_count', 'old_value': 1498, 'new_value': 1558}]
2025-06-28 12:02:09,441 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-28 12:02:09,878 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-28 12:02:09,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 246161.59, 'new_value': 248502.26}, {'field': 'total_amount', 'old_value': 246161.59, 'new_value': 248502.26}, {'field': 'order_count', 'old_value': 1106, 'new_value': 1121}]
2025-06-28 12:02:09,879 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-28 12:02:10,340 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-28 12:02:10,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 290925.0, 'new_value': 309449.0}, {'field': 'total_amount', 'old_value': 290925.0, 'new_value': 309449.0}, {'field': 'order_count', 'old_value': 373, 'new_value': 393}]
2025-06-28 12:02:10,341 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-28 12:02:10,735 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-28 12:02:10,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 414488.69, 'new_value': 443465.99}, {'field': 'total_amount', 'old_value': 414488.69, 'new_value': 443465.99}, {'field': 'order_count', 'old_value': 9720, 'new_value': 10355}]
2025-06-28 12:02:10,736 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-28 12:02:11,207 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-28 12:02:11,207 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106465.97, 'new_value': 110998.7}, {'field': 'offline_amount', 'old_value': 138543.17, 'new_value': 144879.6}, {'field': 'total_amount', 'old_value': 245009.14, 'new_value': 255878.3}, {'field': 'order_count', 'old_value': 9465, 'new_value': 9766}]
2025-06-28 12:02:11,208 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-28 12:02:11,704 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-28 12:02:11,704 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188781.71, 'new_value': 195438.31}, {'field': 'total_amount', 'old_value': 188781.71, 'new_value': 195438.31}, {'field': 'order_count', 'old_value': 802, 'new_value': 829}]
2025-06-28 12:02:11,704 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM421
2025-06-28 12:02:12,159 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM421
2025-06-28 12:02:12,159 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39882.0, 'new_value': 40881.0}, {'field': 'total_amount', 'old_value': 39882.0, 'new_value': 40881.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-28 12:02:12,160 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-28 12:02:12,669 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-28 12:02:12,669 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 212492.43, 'new_value': 228585.43}, {'field': 'total_amount', 'old_value': 294207.63, 'new_value': 310300.63}, {'field': 'order_count', 'old_value': 468, 'new_value': 489}]
2025-06-28 12:02:12,670 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-28 12:02:13,115 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-28 12:02:13,115 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89825.84, 'new_value': 93140.68}, {'field': 'total_amount', 'old_value': 89825.84, 'new_value': 93140.68}, {'field': 'order_count', 'old_value': 5858, 'new_value': 6109}]
2025-06-28 12:02:13,115 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-28 12:02:13,526 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-28 12:02:13,526 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2975.0, 'new_value': 3175.0}, {'field': 'offline_amount', 'old_value': 30785.0, 'new_value': 31585.0}, {'field': 'total_amount', 'old_value': 33760.0, 'new_value': 34760.0}, {'field': 'order_count', 'old_value': 428, 'new_value': 443}]
2025-06-28 12:02:13,526 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-28 12:02:13,953 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-28 12:02:13,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141976.0, 'new_value': 146711.0}, {'field': 'offline_amount', 'old_value': 183780.0, 'new_value': 207349.0}, {'field': 'total_amount', 'old_value': 325756.0, 'new_value': 354060.0}, {'field': 'order_count', 'old_value': 198407, 'new_value': 198466}]
2025-06-28 12:02:13,953 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-28 12:02:14,422 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-28 12:02:14,422 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74938.29, 'new_value': 78935.48}, {'field': 'offline_amount', 'old_value': 75077.81, 'new_value': 77660.21}, {'field': 'total_amount', 'old_value': 150016.1, 'new_value': 156595.69}, {'field': 'order_count', 'old_value': 7889, 'new_value': 8263}]
2025-06-28 12:02:14,422 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-28 12:02:14,871 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-28 12:02:14,871 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10380.39, 'new_value': 10816.29}, {'field': 'offline_amount', 'old_value': 26781.68, 'new_value': 29081.68}, {'field': 'total_amount', 'old_value': 37162.07, 'new_value': 39897.97}, {'field': 'order_count', 'old_value': 114, 'new_value': 118}]
2025-06-28 12:02:14,871 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB21
2025-06-28 12:02:15,307 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB21
2025-06-28 12:02:15,307 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86554.0, 'new_value': 98369.0}, {'field': 'total_amount', 'old_value': 86554.0, 'new_value': 98369.0}, {'field': 'order_count', 'old_value': 12522, 'new_value': 14319}]
2025-06-28 12:02:15,308 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC21
2025-06-28 12:02:15,767 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC21
2025-06-28 12:02:15,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55394.0, 'new_value': 63271.0}, {'field': 'total_amount', 'old_value': 57699.0, 'new_value': 65576.0}, {'field': 'order_count', 'old_value': 12522, 'new_value': 14319}]
2025-06-28 12:02:15,767 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-28 12:02:16,232 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-28 12:02:16,232 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20826.05, 'new_value': 21734.05}, {'field': 'offline_amount', 'old_value': 12191.84, 'new_value': 12789.84}, {'field': 'total_amount', 'old_value': 33017.89, 'new_value': 34523.89}, {'field': 'order_count', 'old_value': 1213, 'new_value': 1265}]
2025-06-28 12:02:16,232 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-28 12:02:16,673 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-28 12:02:16,673 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 241359.0, 'new_value': 254114.0}, {'field': 'total_amount', 'old_value': 241359.0, 'new_value': 254114.0}, {'field': 'order_count', 'old_value': 25552, 'new_value': 26871}]
2025-06-28 12:02:16,673 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG21
2025-06-28 12:02:17,086 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG21
2025-06-28 12:02:17,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 736538.0, 'new_value': 864320.0}, {'field': 'total_amount', 'old_value': 736538.0, 'new_value': 864320.0}]
2025-06-28 12:02:17,086 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH21
2025-06-28 12:02:17,546 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH21
2025-06-28 12:02:17,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16624.0, 'new_value': 16823.0}, {'field': 'total_amount', 'old_value': 16624.0, 'new_value': 16823.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-06-28 12:02:17,546 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI21
2025-06-28 12:02:17,974 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI21
2025-06-28 12:02:17,974 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13172.28, 'new_value': 14634.18}, {'field': 'offline_amount', 'old_value': 58674.84, 'new_value': 66236.58}, {'field': 'total_amount', 'old_value': 71847.12, 'new_value': 80870.76}, {'field': 'order_count', 'old_value': 1582, 'new_value': 1788}]
2025-06-28 12:02:17,974 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-28 12:02:18,481 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-28 12:02:18,482 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 169110.0, 'new_value': 172507.0}, {'field': 'offline_amount', 'old_value': 1197773.0, 'new_value': 1244156.0}, {'field': 'total_amount', 'old_value': 1366883.0, 'new_value': 1416663.0}, {'field': 'order_count', 'old_value': 46646, 'new_value': 48006}]
2025-06-28 12:02:18,482 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-28 12:02:18,932 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-28 12:02:18,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 315863.0, 'new_value': 327393.0}, {'field': 'total_amount', 'old_value': 315863.0, 'new_value': 327393.0}, {'field': 'order_count', 'old_value': 7299, 'new_value': 7575}]
2025-06-28 12:02:18,933 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM21
2025-06-28 12:02:19,397 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM21
2025-06-28 12:02:19,397 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89624.0, 'new_value': 96384.0}, {'field': 'total_amount', 'old_value': 89624.0, 'new_value': 96384.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-06-28 12:02:19,397 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-28 12:02:19,845 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-28 12:02:19,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8276.5, 'new_value': 8461.5}, {'field': 'total_amount', 'old_value': 15169.0, 'new_value': 15354.0}, {'field': 'order_count', 'old_value': 131, 'new_value': 136}]
2025-06-28 12:02:19,845 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-28 12:02:20,292 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-28 12:02:20,292 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17876.0, 'new_value': 20186.0}, {'field': 'total_amount', 'old_value': 17876.0, 'new_value': 20186.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 50}]
2025-06-28 12:02:20,292 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-28 12:02:20,737 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-28 12:02:20,738 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47180.75, 'new_value': 47976.25}, {'field': 'total_amount', 'old_value': 47180.75, 'new_value': 47976.25}, {'field': 'order_count', 'old_value': 1236, 'new_value': 1273}]
2025-06-28 12:02:20,738 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS21
2025-06-28 12:02:21,200 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS21
2025-06-28 12:02:21,200 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1051928.61, 'new_value': 1097570.21}, {'field': 'total_amount', 'old_value': 1051928.61, 'new_value': 1097570.21}, {'field': 'order_count', 'old_value': 3599, 'new_value': 3759}]
2025-06-28 12:02:21,200 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-28 12:02:21,663 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-28 12:02:21,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52580.8, 'new_value': 55121.8}, {'field': 'total_amount', 'old_value': 52580.8, 'new_value': 55121.8}, {'field': 'order_count', 'old_value': 267, 'new_value': 277}]
2025-06-28 12:02:21,663 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-28 12:02:22,111 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-28 12:02:22,111 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 700120.29, 'new_value': 729387.96}, {'field': 'total_amount', 'old_value': 700120.29, 'new_value': 729387.96}, {'field': 'order_count', 'old_value': 6059, 'new_value': 6323}]
2025-06-28 12:02:22,111 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-28 12:02:22,564 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-28 12:02:22,564 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 262907.9, 'new_value': 277422.9}, {'field': 'offline_amount', 'old_value': 23305.5, 'new_value': 24485.5}, {'field': 'total_amount', 'old_value': 286213.4, 'new_value': 301908.4}, {'field': 'order_count', 'old_value': 3529, 'new_value': 3803}]
2025-06-28 12:02:22,564 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-28 12:02:23,048 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-28 12:02:23,048 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32042.1, 'new_value': 32793.4}, {'field': 'offline_amount', 'old_value': 57872.9, 'new_value': 59873.7}, {'field': 'total_amount', 'old_value': 89915.0, 'new_value': 92667.1}, {'field': 'order_count', 'old_value': 3544, 'new_value': 3641}]
2025-06-28 12:02:23,048 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-28 12:02:23,460 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-28 12:02:23,460 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 728254.14, 'new_value': 747023.14}, {'field': 'total_amount', 'old_value': 728254.14, 'new_value': 747023.14}, {'field': 'order_count', 'old_value': 4561, 'new_value': 4628}]
2025-06-28 12:02:23,460 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-28 12:02:24,021 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-28 12:02:24,021 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34256.12, 'new_value': 35896.66}, {'field': 'total_amount', 'old_value': 35022.12, 'new_value': 36662.66}, {'field': 'order_count', 'old_value': 336, 'new_value': 352}]
2025-06-28 12:02:24,021 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-28 12:02:24,497 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-28 12:02:24,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35997.59, 'new_value': 36973.92}, {'field': 'total_amount', 'old_value': 35997.59, 'new_value': 36973.92}, {'field': 'order_count', 'old_value': 112, 'new_value': 116}]
2025-06-28 12:02:24,497 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-28 12:02:24,941 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-28 12:02:24,941 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32403.93, 'new_value': 38084.93}, {'field': 'offline_amount', 'old_value': 395271.71, 'new_value': 426271.71}, {'field': 'total_amount', 'old_value': 427675.64, 'new_value': 464356.64}, {'field': 'order_count', 'old_value': 552, 'new_value': 572}]
2025-06-28 12:02:24,941 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-28 12:02:25,449 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-28 12:02:25,449 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87109.0, 'new_value': 91110.0}, {'field': 'offline_amount', 'old_value': 171284.0, 'new_value': 178386.0}, {'field': 'total_amount', 'old_value': 258393.0, 'new_value': 269496.0}, {'field': 'order_count', 'old_value': 5143, 'new_value': 5387}]
2025-06-28 12:02:25,449 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-28 12:02:25,878 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-28 12:02:25,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5363925.49, 'new_value': 5527692.49}, {'field': 'total_amount', 'old_value': 5363925.49, 'new_value': 5527692.49}, {'field': 'order_count', 'old_value': 108194, 'new_value': 111847}]
2025-06-28 12:02:25,878 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-28 12:02:26,301 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-28 12:02:26,302 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1104339.76, 'new_value': 1150302.56}, {'field': 'total_amount', 'old_value': 1104339.76, 'new_value': 1150302.56}, {'field': 'order_count', 'old_value': 4357, 'new_value': 4492}]
2025-06-28 12:02:26,302 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-28 12:02:26,847 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-28 12:02:26,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 712823.25, 'new_value': 735007.35}, {'field': 'total_amount', 'old_value': 712823.25, 'new_value': 735007.35}, {'field': 'order_count', 'old_value': 1829, 'new_value': 1896}]
2025-06-28 12:02:26,847 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-28 12:02:27,319 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-28 12:02:27,319 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88417.0, 'new_value': 91152.0}, {'field': 'total_amount', 'old_value': 88417.0, 'new_value': 91152.0}, {'field': 'order_count', 'old_value': 452, 'new_value': 468}]
2025-06-28 12:02:27,319 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD31
2025-06-28 12:02:27,782 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD31
2025-06-28 12:02:27,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10411.1, 'new_value': 11739.5}, {'field': 'total_amount', 'old_value': 10411.1, 'new_value': 11739.5}, {'field': 'order_count', 'old_value': 893, 'new_value': 995}]
2025-06-28 12:02:27,782 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-28 12:02:28,314 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-28 12:02:28,315 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28397.12, 'new_value': 31559.13}, {'field': 'total_amount', 'old_value': 104347.94, 'new_value': 107509.95}, {'field': 'order_count', 'old_value': 6903, 'new_value': 7133}]
2025-06-28 12:02:28,315 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-28 12:02:28,756 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-28 12:02:28,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52475.83, 'new_value': 59196.0}, {'field': 'total_amount', 'old_value': 186308.54, 'new_value': 193028.71}, {'field': 'order_count', 'old_value': 12386, 'new_value': 12871}]
2025-06-28 12:02:28,757 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-28 12:02:29,165 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-28 12:02:29,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82038.0, 'new_value': 85018.0}, {'field': 'total_amount', 'old_value': 100878.0, 'new_value': 103858.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-06-28 12:02:29,165 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-28 12:02:29,615 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-28 12:02:29,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125202.0, 'new_value': 128006.0}, {'field': 'total_amount', 'old_value': 125202.0, 'new_value': 128006.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-06-28 12:02:29,616 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-28 12:02:30,050 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-28 12:02:30,050 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11591.39, 'new_value': 11827.11}, {'field': 'offline_amount', 'old_value': 81964.66, 'new_value': 86476.32}, {'field': 'total_amount', 'old_value': 93556.05, 'new_value': 98303.43}, {'field': 'order_count', 'old_value': 2887, 'new_value': 2996}]
2025-06-28 12:02:30,051 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-28 12:02:30,541 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-28 12:02:30,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 476296.86, 'new_value': 492336.86}, {'field': 'total_amount', 'old_value': 476296.86, 'new_value': 492336.86}, {'field': 'order_count', 'old_value': 3301, 'new_value': 3361}]
2025-06-28 12:02:30,542 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-28 12:02:30,961 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-28 12:02:30,962 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6731.65, 'new_value': 7077.75}, {'field': 'offline_amount', 'old_value': 40964.8, 'new_value': 42387.7}, {'field': 'total_amount', 'old_value': 47696.45, 'new_value': 49465.45}, {'field': 'order_count', 'old_value': 363, 'new_value': 378}]
2025-06-28 12:02:30,962 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-28 12:02:31,384 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-28 12:02:31,385 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285700.0, 'new_value': 299500.0}, {'field': 'total_amount', 'old_value': 285700.0, 'new_value': 299500.0}, {'field': 'order_count', 'old_value': 674, 'new_value': 706}]
2025-06-28 12:02:31,385 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-28 12:02:31,853 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-28 12:02:31,853 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 167362.97, 'new_value': 176636.0}, {'field': 'offline_amount', 'old_value': 52733.94, 'new_value': 56222.7}, {'field': 'total_amount', 'old_value': 220096.91, 'new_value': 232858.7}, {'field': 'order_count', 'old_value': 957, 'new_value': 1016}]
2025-06-28 12:02:31,853 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-28 12:02:32,403 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-28 12:02:32,403 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32108.08, 'new_value': 33485.68}, {'field': 'offline_amount', 'old_value': 123035.0, 'new_value': 139287.0}, {'field': 'total_amount', 'old_value': 155143.08, 'new_value': 172772.68}, {'field': 'order_count', 'old_value': 1893, 'new_value': 1971}]
2025-06-28 12:02:32,403 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-28 12:02:32,836 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-28 12:02:32,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 229094.77, 'new_value': 238128.36}, {'field': 'total_amount', 'old_value': 229094.77, 'new_value': 238128.36}, {'field': 'order_count', 'old_value': 3461, 'new_value': 3633}]
2025-06-28 12:02:32,836 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-28 12:02:33,341 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-28 12:02:33,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 591169.78, 'new_value': 600665.78}, {'field': 'total_amount', 'old_value': 591169.78, 'new_value': 600665.78}, {'field': 'order_count', 'old_value': 548, 'new_value': 560}]
2025-06-28 12:02:33,341 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-28 12:02:33,815 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-28 12:02:33,815 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134558.55, 'new_value': 140223.55}, {'field': 'total_amount', 'old_value': 134558.55, 'new_value': 140223.55}, {'field': 'order_count', 'old_value': 3464, 'new_value': 3608}]
2025-06-28 12:02:33,815 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-28 12:02:34,273 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-28 12:02:34,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84990.2, 'new_value': 86389.2}, {'field': 'total_amount', 'old_value': 84990.2, 'new_value': 86389.2}, {'field': 'order_count', 'old_value': 764, 'new_value': 777}]
2025-06-28 12:02:34,274 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-28 12:02:35,608 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-28 12:02:35,608 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84313.0, 'new_value': 84354.0}, {'field': 'total_amount', 'old_value': 84313.0, 'new_value': 84354.0}, {'field': 'order_count', 'old_value': 1338, 'new_value': 3813}]
2025-06-28 12:02:35,608 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-28 12:02:36,133 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-28 12:02:36,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62089.0, 'new_value': 65069.0}, {'field': 'total_amount', 'old_value': 62089.0, 'new_value': 65069.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-28 12:02:36,133 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-28 12:02:36,570 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-28 12:02:36,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128967.0, 'new_value': 132381.0}, {'field': 'total_amount', 'old_value': 128967.0, 'new_value': 132381.0}, {'field': 'order_count', 'old_value': 549, 'new_value': 567}]
2025-06-28 12:02:36,571 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-28 12:02:37,028 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-28 12:02:37,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112497.4, 'new_value': 118158.4}, {'field': 'total_amount', 'old_value': 112497.4, 'new_value': 118158.4}, {'field': 'order_count', 'old_value': 46, 'new_value': 49}]
2025-06-28 12:02:37,029 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-28 12:02:37,483 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-28 12:02:37,483 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76055.0, 'new_value': 79500.0}, {'field': 'total_amount', 'old_value': 79500.0, 'new_value': 82945.0}, {'field': 'order_count', 'old_value': 300, 'new_value': 316}]
2025-06-28 12:02:37,483 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-28 12:02:37,908 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-28 12:02:37,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49295.35, 'new_value': 51564.02}, {'field': 'total_amount', 'old_value': 49295.35, 'new_value': 51564.02}, {'field': 'order_count', 'old_value': 6225, 'new_value': 6493}]
2025-06-28 12:02:37,908 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC2
2025-06-28 12:02:38,350 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC2
2025-06-28 12:02:38,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3595189.0, 'new_value': 4186189.0}, {'field': 'total_amount', 'old_value': 4504089.0, 'new_value': 5095089.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 16}]
2025-06-28 12:02:38,350 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-28 12:02:38,814 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-28 12:02:38,814 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 327020.15, 'new_value': 347165.77}, {'field': 'total_amount', 'old_value': 327020.15, 'new_value': 347165.77}, {'field': 'order_count', 'old_value': 908, 'new_value': 959}]
2025-06-28 12:02:38,815 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-28 12:02:39,287 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-28 12:02:39,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 336203.0, 'new_value': 352095.0}, {'field': 'total_amount', 'old_value': 336203.0, 'new_value': 352095.0}, {'field': 'order_count', 'old_value': 7526, 'new_value': 7876}]
2025-06-28 12:02:39,287 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-28 12:02:39,733 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-28 12:02:39,733 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117870.0, 'new_value': 123157.0}, {'field': 'total_amount', 'old_value': 117870.0, 'new_value': 123157.0}, {'field': 'order_count', 'old_value': 6704, 'new_value': 7034}]
2025-06-28 12:02:39,733 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-28 12:02:40,221 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-28 12:02:40,221 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3042292.84, 'new_value': 3179715.43}, {'field': 'total_amount', 'old_value': 3042292.84, 'new_value': 3179715.43}, {'field': 'order_count', 'old_value': 5771, 'new_value': 6024}]
2025-06-28 12:02:40,221 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-28 12:02:40,715 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-28 12:02:40,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 835384.0, 'new_value': 862070.0}, {'field': 'total_amount', 'old_value': 835384.0, 'new_value': 862070.0}, {'field': 'order_count', 'old_value': 4542, 'new_value': 4671}]
2025-06-28 12:02:40,715 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-28 12:02:41,175 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-28 12:02:41,175 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9572501.73, 'new_value': 9866360.73}, {'field': 'total_amount', 'old_value': 9572501.73, 'new_value': 9866360.73}, {'field': 'order_count', 'old_value': 35874, 'new_value': 37024}]
2025-06-28 12:02:41,175 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-28 12:02:41,596 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-28 12:02:41,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177150.2, 'new_value': 185010.57}, {'field': 'total_amount', 'old_value': 177150.2, 'new_value': 185010.57}, {'field': 'order_count', 'old_value': 19522, 'new_value': 20407}]
2025-06-28 12:02:41,596 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-28 12:02:42,029 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-28 12:02:42,029 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 190296.36, 'new_value': 201862.61}, {'field': 'offline_amount', 'old_value': 160972.0, 'new_value': 171743.33}, {'field': 'total_amount', 'old_value': 351268.36, 'new_value': 373605.94}, {'field': 'order_count', 'old_value': 15224, 'new_value': 16093}]
2025-06-28 12:02:42,029 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN2
2025-06-28 12:02:42,548 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN2
2025-06-28 12:02:42,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123193.0, 'new_value': 133192.0}, {'field': 'total_amount', 'old_value': 123193.0, 'new_value': 133192.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-06-28 12:02:42,548 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-28 12:02:42,995 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-28 12:02:42,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 232519.9, 'new_value': 241727.9}, {'field': 'total_amount', 'old_value': 232519.9, 'new_value': 241727.9}, {'field': 'order_count', 'old_value': 7960, 'new_value': 8284}]
2025-06-28 12:02:42,995 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-28 12:02:43,444 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-28 12:02:43,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 247264.85, 'new_value': 268431.85}, {'field': 'total_amount', 'old_value': 281466.95, 'new_value': 302633.95}, {'field': 'order_count', 'old_value': 874, 'new_value': 921}]
2025-06-28 12:02:43,445 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-28 12:02:43,959 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-28 12:02:43,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51802.1, 'new_value': 51916.1}, {'field': 'total_amount', 'old_value': 51802.1, 'new_value': 51916.1}, {'field': 'order_count', 'old_value': 4268, 'new_value': 7886}]
2025-06-28 12:02:43,960 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-28 12:02:44,453 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-28 12:02:44,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124842.98, 'new_value': 124907.98}, {'field': 'total_amount', 'old_value': 124842.98, 'new_value': 124907.98}, {'field': 'order_count', 'old_value': 4503, 'new_value': 8421}]
2025-06-28 12:02:44,453 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-28 12:02:44,924 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-28 12:02:44,924 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101548.0, 'new_value': 101574.0}, {'field': 'total_amount', 'old_value': 101548.0, 'new_value': 101574.0}, {'field': 'order_count', 'old_value': 725, 'new_value': 4243}]
2025-06-28 12:02:44,924 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-28 12:02:45,320 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-28 12:02:45,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34678.0, 'new_value': 34791.0}, {'field': 'total_amount', 'old_value': 34678.0, 'new_value': 34791.0}, {'field': 'order_count', 'old_value': 3004, 'new_value': 3989}]
2025-06-28 12:02:45,321 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-28 12:02:45,818 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-28 12:02:45,818 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5233610.0, 'new_value': 5233695.0}, {'field': 'total_amount', 'old_value': 5233610.0, 'new_value': 5233695.0}, {'field': 'order_count', 'old_value': 453, 'new_value': 12253}]
2025-06-28 12:02:45,818 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-28 12:02:46,379 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-28 12:02:46,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194202.53, 'new_value': 204794.27}, {'field': 'total_amount', 'old_value': 194202.53, 'new_value': 204794.27}, {'field': 'order_count', 'old_value': 14680, 'new_value': 15485}]
2025-06-28 12:02:46,379 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-28 12:02:46,859 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-28 12:02:46,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 218657.24, 'new_value': 232175.24}, {'field': 'total_amount', 'old_value': 218657.24, 'new_value': 232175.24}, {'field': 'order_count', 'old_value': 13747, 'new_value': 13868}]
2025-06-28 12:02:46,860 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBM3Q
2025-06-28 12:02:47,305 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBM3Q
2025-06-28 12:02:47,305 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11288.0, 'new_value': 19081.3}, {'field': 'total_amount', 'old_value': 33338.7, 'new_value': 41132.0}, {'field': 'order_count', 'old_value': 3449, 'new_value': 4304}]
2025-06-28 12:02:47,305 - INFO - 开始更新记录 - 表单实例ID: FINST-RTA66X610KDWQ1WBCR65QD898OM630PY6J0CMY1
2025-06-28 12:02:47,757 - INFO - 更新表单数据成功: FINST-RTA66X610KDWQ1WBCR65QD898OM630PY6J0CMY1
2025-06-28 12:02:47,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2100.0, 'new_value': 4700.0}, {'field': 'total_amount', 'old_value': 2100.0, 'new_value': 4700.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-06-28 12:02:47,757 - INFO - 日期 2025-06 处理完成 - 更新: 277 条，插入: 0 条，错误: 0 条
2025-06-28 12:02:47,757 - INFO - 数据同步完成！更新: 277 条，插入: 0 条，错误: 0 条
2025-06-28 12:02:47,760 - INFO - =================同步完成====================
2025-06-28 15:00:03,230 - INFO - =================使用默认全量同步=============
2025-06-28 15:00:05,041 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-28 15:00:05,041 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-28 15:00:05,073 - INFO - 开始处理日期: 2025-01
2025-06-28 15:00:05,073 - INFO - Request Parameters - Page 1:
2025-06-28 15:00:05,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:05,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:06,214 - INFO - Response - Page 1:
2025-06-28 15:00:06,419 - INFO - 第 1 页获取到 100 条记录
2025-06-28 15:00:06,419 - INFO - Request Parameters - Page 2:
2025-06-28 15:00:06,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:06,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:07,173 - INFO - Response - Page 2:
2025-06-28 15:00:07,378 - INFO - 第 2 页获取到 100 条记录
2025-06-28 15:00:07,378 - INFO - Request Parameters - Page 3:
2025-06-28 15:00:07,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:07,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:07,873 - INFO - Response - Page 3:
2025-06-28 15:00:08,077 - INFO - 第 3 页获取到 100 条记录
2025-06-28 15:00:08,077 - INFO - Request Parameters - Page 4:
2025-06-28 15:00:08,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:08,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:08,633 - INFO - Response - Page 4:
2025-06-28 15:00:08,837 - INFO - 第 4 页获取到 100 条记录
2025-06-28 15:00:08,837 - INFO - Request Parameters - Page 5:
2025-06-28 15:00:08,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:08,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:09,373 - INFO - Response - Page 5:
2025-06-28 15:00:09,574 - INFO - 第 5 页获取到 100 条记录
2025-06-28 15:00:09,574 - INFO - Request Parameters - Page 6:
2025-06-28 15:00:09,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:09,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:10,042 - INFO - Response - Page 6:
2025-06-28 15:00:10,248 - INFO - 第 6 页获取到 100 条记录
2025-06-28 15:00:10,248 - INFO - Request Parameters - Page 7:
2025-06-28 15:00:10,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:10,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:10,748 - INFO - Response - Page 7:
2025-06-28 15:00:10,954 - INFO - 第 7 页获取到 82 条记录
2025-06-28 15:00:10,954 - INFO - 查询完成，共获取到 682 条记录
2025-06-28 15:00:10,954 - INFO - 获取到 682 条表单数据
2025-06-28 15:00:10,954 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-28 15:00:10,969 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 15:00:10,969 - INFO - 开始处理日期: 2025-02
2025-06-28 15:00:10,969 - INFO - Request Parameters - Page 1:
2025-06-28 15:00:10,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:10,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:11,512 - INFO - Response - Page 1:
2025-06-28 15:00:11,720 - INFO - 第 1 页获取到 100 条记录
2025-06-28 15:00:11,720 - INFO - Request Parameters - Page 2:
2025-06-28 15:00:11,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:11,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:12,351 - INFO - Response - Page 2:
2025-06-28 15:00:12,555 - INFO - 第 2 页获取到 100 条记录
2025-06-28 15:00:12,555 - INFO - Request Parameters - Page 3:
2025-06-28 15:00:12,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:12,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:13,128 - INFO - Response - Page 3:
2025-06-28 15:00:13,332 - INFO - 第 3 页获取到 100 条记录
2025-06-28 15:00:13,332 - INFO - Request Parameters - Page 4:
2025-06-28 15:00:13,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:13,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:13,873 - INFO - Response - Page 4:
2025-06-28 15:00:14,084 - INFO - 第 4 页获取到 100 条记录
2025-06-28 15:00:14,084 - INFO - Request Parameters - Page 5:
2025-06-28 15:00:14,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:14,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:14,737 - INFO - Response - Page 5:
2025-06-28 15:00:14,942 - INFO - 第 5 页获取到 100 条记录
2025-06-28 15:00:14,942 - INFO - Request Parameters - Page 6:
2025-06-28 15:00:14,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:14,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:15,478 - INFO - Response - Page 6:
2025-06-28 15:00:15,689 - INFO - 第 6 页获取到 100 条记录
2025-06-28 15:00:15,689 - INFO - Request Parameters - Page 7:
2025-06-28 15:00:15,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:15,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:16,115 - INFO - Response - Page 7:
2025-06-28 15:00:16,320 - INFO - 第 7 页获取到 70 条记录
2025-06-28 15:00:16,320 - INFO - 查询完成，共获取到 670 条记录
2025-06-28 15:00:16,320 - INFO - 获取到 670 条表单数据
2025-06-28 15:00:16,320 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-28 15:00:16,335 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 15:00:16,335 - INFO - 开始处理日期: 2025-03
2025-06-28 15:00:16,335 - INFO - Request Parameters - Page 1:
2025-06-28 15:00:16,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:16,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:16,899 - INFO - Response - Page 1:
2025-06-28 15:00:17,104 - INFO - 第 1 页获取到 100 条记录
2025-06-28 15:00:17,104 - INFO - Request Parameters - Page 2:
2025-06-28 15:00:17,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:17,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:17,663 - INFO - Response - Page 2:
2025-06-28 15:00:17,869 - INFO - 第 2 页获取到 100 条记录
2025-06-28 15:00:17,869 - INFO - Request Parameters - Page 3:
2025-06-28 15:00:17,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:17,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:18,531 - INFO - Response - Page 3:
2025-06-28 15:00:18,740 - INFO - 第 3 页获取到 100 条记录
2025-06-28 15:00:18,740 - INFO - Request Parameters - Page 4:
2025-06-28 15:00:18,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:18,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:19,330 - INFO - Response - Page 4:
2025-06-28 15:00:19,535 - INFO - 第 4 页获取到 100 条记录
2025-06-28 15:00:19,535 - INFO - Request Parameters - Page 5:
2025-06-28 15:00:19,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:19,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:20,011 - INFO - Response - Page 5:
2025-06-28 15:00:20,216 - INFO - 第 5 页获取到 100 条记录
2025-06-28 15:00:20,216 - INFO - Request Parameters - Page 6:
2025-06-28 15:00:20,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:20,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:20,764 - INFO - Response - Page 6:
2025-06-28 15:00:20,969 - INFO - 第 6 页获取到 100 条记录
2025-06-28 15:00:20,969 - INFO - Request Parameters - Page 7:
2025-06-28 15:00:20,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:20,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:21,400 - INFO - Response - Page 7:
2025-06-28 15:00:21,612 - INFO - 第 7 页获取到 61 条记录
2025-06-28 15:00:21,612 - INFO - 查询完成，共获取到 661 条记录
2025-06-28 15:00:21,612 - INFO - 获取到 661 条表单数据
2025-06-28 15:00:21,612 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-28 15:00:21,627 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 15:00:21,627 - INFO - 开始处理日期: 2025-04
2025-06-28 15:00:21,627 - INFO - Request Parameters - Page 1:
2025-06-28 15:00:21,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:21,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:22,122 - INFO - Response - Page 1:
2025-06-28 15:00:22,327 - INFO - 第 1 页获取到 100 条记录
2025-06-28 15:00:22,327 - INFO - Request Parameters - Page 2:
2025-06-28 15:00:22,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:22,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:22,885 - INFO - Response - Page 2:
2025-06-28 15:00:23,089 - INFO - 第 2 页获取到 100 条记录
2025-06-28 15:00:23,089 - INFO - Request Parameters - Page 3:
2025-06-28 15:00:23,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:23,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:23,581 - INFO - Response - Page 3:
2025-06-28 15:00:23,789 - INFO - 第 3 页获取到 100 条记录
2025-06-28 15:00:23,789 - INFO - Request Parameters - Page 4:
2025-06-28 15:00:23,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:23,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:24,246 - INFO - Response - Page 4:
2025-06-28 15:00:24,451 - INFO - 第 4 页获取到 100 条记录
2025-06-28 15:00:24,451 - INFO - Request Parameters - Page 5:
2025-06-28 15:00:24,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:24,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:24,944 - INFO - Response - Page 5:
2025-06-28 15:00:25,149 - INFO - 第 5 页获取到 100 条记录
2025-06-28 15:00:25,149 - INFO - Request Parameters - Page 6:
2025-06-28 15:00:25,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:25,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:25,644 - INFO - Response - Page 6:
2025-06-28 15:00:25,852 - INFO - 第 6 页获取到 100 条记录
2025-06-28 15:00:25,852 - INFO - Request Parameters - Page 7:
2025-06-28 15:00:25,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:25,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:26,278 - INFO - Response - Page 7:
2025-06-28 15:00:26,481 - INFO - 第 7 页获取到 56 条记录
2025-06-28 15:00:26,481 - INFO - 查询完成，共获取到 656 条记录
2025-06-28 15:00:26,481 - INFO - 获取到 656 条表单数据
2025-06-28 15:00:26,481 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-28 15:00:26,496 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 15:00:26,496 - INFO - 开始处理日期: 2025-05
2025-06-28 15:00:26,496 - INFO - Request Parameters - Page 1:
2025-06-28 15:00:26,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:26,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:27,054 - INFO - Response - Page 1:
2025-06-28 15:00:27,259 - INFO - 第 1 页获取到 100 条记录
2025-06-28 15:00:27,259 - INFO - Request Parameters - Page 2:
2025-06-28 15:00:27,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:27,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:27,754 - INFO - Response - Page 2:
2025-06-28 15:00:27,959 - INFO - 第 2 页获取到 100 条记录
2025-06-28 15:00:27,959 - INFO - Request Parameters - Page 3:
2025-06-28 15:00:27,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:27,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:28,542 - INFO - Response - Page 3:
2025-06-28 15:00:28,750 - INFO - 第 3 页获取到 100 条记录
2025-06-28 15:00:28,750 - INFO - Request Parameters - Page 4:
2025-06-28 15:00:28,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:28,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:29,245 - INFO - Response - Page 4:
2025-06-28 15:00:29,451 - INFO - 第 4 页获取到 100 条记录
2025-06-28 15:00:29,451 - INFO - Request Parameters - Page 5:
2025-06-28 15:00:29,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:29,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:29,904 - INFO - Response - Page 5:
2025-06-28 15:00:30,109 - INFO - 第 5 页获取到 100 条记录
2025-06-28 15:00:30,109 - INFO - Request Parameters - Page 6:
2025-06-28 15:00:30,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:30,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:30,724 - INFO - Response - Page 6:
2025-06-28 15:00:30,935 - INFO - 第 6 页获取到 100 条记录
2025-06-28 15:00:30,935 - INFO - Request Parameters - Page 7:
2025-06-28 15:00:30,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:30,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:31,423 - INFO - Response - Page 7:
2025-06-28 15:00:31,628 - INFO - 第 7 页获取到 65 条记录
2025-06-28 15:00:31,628 - INFO - 查询完成，共获取到 665 条记录
2025-06-28 15:00:31,628 - INFO - 获取到 665 条表单数据
2025-06-28 15:00:31,628 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-28 15:00:31,644 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 15:00:31,644 - INFO - 开始处理日期: 2025-06
2025-06-28 15:00:31,644 - INFO - Request Parameters - Page 1:
2025-06-28 15:00:31,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:31,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:32,170 - INFO - Response - Page 1:
2025-06-28 15:00:32,375 - INFO - 第 1 页获取到 100 条记录
2025-06-28 15:00:32,375 - INFO - Request Parameters - Page 2:
2025-06-28 15:00:32,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:32,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:32,915 - INFO - Response - Page 2:
2025-06-28 15:00:33,120 - INFO - 第 2 页获取到 100 条记录
2025-06-28 15:00:33,120 - INFO - Request Parameters - Page 3:
2025-06-28 15:00:33,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:33,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:33,609 - INFO - Response - Page 3:
2025-06-28 15:00:33,819 - INFO - 第 3 页获取到 100 条记录
2025-06-28 15:00:33,819 - INFO - Request Parameters - Page 4:
2025-06-28 15:00:33,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:33,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:34,486 - INFO - Response - Page 4:
2025-06-28 15:00:34,691 - INFO - 第 4 页获取到 100 条记录
2025-06-28 15:00:34,691 - INFO - Request Parameters - Page 5:
2025-06-28 15:00:34,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:34,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:35,246 - INFO - Response - Page 5:
2025-06-28 15:00:35,454 - INFO - 第 5 页获取到 100 条记录
2025-06-28 15:00:35,454 - INFO - Request Parameters - Page 6:
2025-06-28 15:00:35,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:35,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:35,947 - INFO - Response - Page 6:
2025-06-28 15:00:36,152 - INFO - 第 6 页获取到 100 条记录
2025-06-28 15:00:36,152 - INFO - Request Parameters - Page 7:
2025-06-28 15:00:36,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 15:00:36,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 15:00:36,552 - INFO - Response - Page 7:
2025-06-28 15:00:36,756 - INFO - 第 7 页获取到 29 条记录
2025-06-28 15:00:36,756 - INFO - 查询完成，共获取到 629 条记录
2025-06-28 15:00:36,756 - INFO - 获取到 629 条表单数据
2025-06-28 15:00:36,757 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-28 15:00:36,773 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-28 15:00:37,281 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-28 15:00:37,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 261641.98, 'new_value': 271705.06}, {'field': 'total_amount', 'old_value': 261641.98, 'new_value': 271705.06}, {'field': 'order_count', 'old_value': 7494, 'new_value': 7750}]
2025-06-28 15:00:37,281 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-28 15:00:37,754 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-28 15:00:37,754 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 327915.0, 'new_value': 354100.0}, {'field': 'total_amount', 'old_value': 327915.0, 'new_value': 354100.0}, {'field': 'order_count', 'old_value': 83, 'new_value': 85}]
2025-06-28 15:00:37,770 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT2
2025-06-28 15:00:38,186 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT2
2025-06-28 15:00:38,186 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51876.0, 'new_value': 57729.0}, {'field': 'total_amount', 'old_value': 57591.0, 'new_value': 63444.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 91}]
2025-06-28 15:00:38,186 - INFO - 日期 2025-06 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-28 15:00:38,186 - INFO - 数据同步完成！更新: 3 条，插入: 0 条，错误: 0 条
2025-06-28 15:00:38,186 - INFO - =================同步完成====================
2025-06-28 18:00:02,533 - INFO - =================使用默认全量同步=============
2025-06-28 18:00:04,340 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-28 18:00:04,340 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-28 18:00:04,371 - INFO - 开始处理日期: 2025-01
2025-06-28 18:00:04,387 - INFO - Request Parameters - Page 1:
2025-06-28 18:00:04,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:04,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:05,713 - INFO - Response - Page 1:
2025-06-28 18:00:05,917 - INFO - 第 1 页获取到 100 条记录
2025-06-28 18:00:05,917 - INFO - Request Parameters - Page 2:
2025-06-28 18:00:05,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:05,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:06,633 - INFO - Response - Page 2:
2025-06-28 18:00:06,838 - INFO - 第 2 页获取到 100 条记录
2025-06-28 18:00:06,838 - INFO - Request Parameters - Page 3:
2025-06-28 18:00:06,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:06,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:07,362 - INFO - Response - Page 3:
2025-06-28 18:00:07,566 - INFO - 第 3 页获取到 100 条记录
2025-06-28 18:00:07,566 - INFO - Request Parameters - Page 4:
2025-06-28 18:00:07,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:07,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:08,118 - INFO - Response - Page 4:
2025-06-28 18:00:08,323 - INFO - 第 4 页获取到 100 条记录
2025-06-28 18:00:08,323 - INFO - Request Parameters - Page 5:
2025-06-28 18:00:08,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:08,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:08,835 - INFO - Response - Page 5:
2025-06-28 18:00:09,040 - INFO - 第 5 页获取到 100 条记录
2025-06-28 18:00:09,040 - INFO - Request Parameters - Page 6:
2025-06-28 18:00:09,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:09,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:09,566 - INFO - Response - Page 6:
2025-06-28 18:00:09,778 - INFO - 第 6 页获取到 100 条记录
2025-06-28 18:00:09,778 - INFO - Request Parameters - Page 7:
2025-06-28 18:00:09,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:09,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:10,282 - INFO - Response - Page 7:
2025-06-28 18:00:10,493 - INFO - 第 7 页获取到 82 条记录
2025-06-28 18:00:10,493 - INFO - 查询完成，共获取到 682 条记录
2025-06-28 18:00:10,493 - INFO - 获取到 682 条表单数据
2025-06-28 18:00:10,493 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-28 18:00:10,509 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 18:00:10,509 - INFO - 开始处理日期: 2025-02
2025-06-28 18:00:10,509 - INFO - Request Parameters - Page 1:
2025-06-28 18:00:10,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:10,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:11,077 - INFO - Response - Page 1:
2025-06-28 18:00:11,281 - INFO - 第 1 页获取到 100 条记录
2025-06-28 18:00:11,281 - INFO - Request Parameters - Page 2:
2025-06-28 18:00:11,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:11,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:11,864 - INFO - Response - Page 2:
2025-06-28 18:00:12,068 - INFO - 第 2 页获取到 100 条记录
2025-06-28 18:00:12,068 - INFO - Request Parameters - Page 3:
2025-06-28 18:00:12,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:12,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:12,562 - INFO - Response - Page 3:
2025-06-28 18:00:12,767 - INFO - 第 3 页获取到 100 条记录
2025-06-28 18:00:12,767 - INFO - Request Parameters - Page 4:
2025-06-28 18:00:12,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:12,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:13,288 - INFO - Response - Page 4:
2025-06-28 18:00:13,496 - INFO - 第 4 页获取到 100 条记录
2025-06-28 18:00:13,496 - INFO - Request Parameters - Page 5:
2025-06-28 18:00:13,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:13,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:14,101 - INFO - Response - Page 5:
2025-06-28 18:00:14,306 - INFO - 第 5 页获取到 100 条记录
2025-06-28 18:00:14,306 - INFO - Request Parameters - Page 6:
2025-06-28 18:00:14,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:14,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:14,867 - INFO - Response - Page 6:
2025-06-28 18:00:15,072 - INFO - 第 6 页获取到 100 条记录
2025-06-28 18:00:15,072 - INFO - Request Parameters - Page 7:
2025-06-28 18:00:15,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:15,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:15,567 - INFO - Response - Page 7:
2025-06-28 18:00:15,772 - INFO - 第 7 页获取到 70 条记录
2025-06-28 18:00:15,772 - INFO - 查询完成，共获取到 670 条记录
2025-06-28 18:00:15,772 - INFO - 获取到 670 条表单数据
2025-06-28 18:00:15,788 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-28 18:00:15,788 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 18:00:15,788 - INFO - 开始处理日期: 2025-03
2025-06-28 18:00:15,788 - INFO - Request Parameters - Page 1:
2025-06-28 18:00:15,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:15,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:16,308 - INFO - Response - Page 1:
2025-06-28 18:00:16,519 - INFO - 第 1 页获取到 100 条记录
2025-06-28 18:00:16,519 - INFO - Request Parameters - Page 2:
2025-06-28 18:00:16,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:16,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:17,084 - INFO - Response - Page 2:
2025-06-28 18:00:17,289 - INFO - 第 2 页获取到 100 条记录
2025-06-28 18:00:17,289 - INFO - Request Parameters - Page 3:
2025-06-28 18:00:17,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:17,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:17,754 - INFO - Response - Page 3:
2025-06-28 18:00:17,958 - INFO - 第 3 页获取到 100 条记录
2025-06-28 18:00:17,958 - INFO - Request Parameters - Page 4:
2025-06-28 18:00:17,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:17,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:18,516 - INFO - Response - Page 4:
2025-06-28 18:00:18,720 - INFO - 第 4 页获取到 100 条记录
2025-06-28 18:00:18,720 - INFO - Request Parameters - Page 5:
2025-06-28 18:00:18,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:18,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:19,304 - INFO - Response - Page 5:
2025-06-28 18:00:19,516 - INFO - 第 5 页获取到 100 条记录
2025-06-28 18:00:19,516 - INFO - Request Parameters - Page 6:
2025-06-28 18:00:19,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:19,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:20,043 - INFO - Response - Page 6:
2025-06-28 18:00:20,247 - INFO - 第 6 页获取到 100 条记录
2025-06-28 18:00:20,247 - INFO - Request Parameters - Page 7:
2025-06-28 18:00:20,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:20,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:20,695 - INFO - Response - Page 7:
2025-06-28 18:00:20,899 - INFO - 第 7 页获取到 61 条记录
2025-06-28 18:00:20,899 - INFO - 查询完成，共获取到 661 条记录
2025-06-28 18:00:20,899 - INFO - 获取到 661 条表单数据
2025-06-28 18:00:20,899 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-28 18:00:20,916 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 18:00:20,916 - INFO - 开始处理日期: 2025-04
2025-06-28 18:00:20,916 - INFO - Request Parameters - Page 1:
2025-06-28 18:00:20,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:20,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:21,435 - INFO - Response - Page 1:
2025-06-28 18:00:21,644 - INFO - 第 1 页获取到 100 条记录
2025-06-28 18:00:21,644 - INFO - Request Parameters - Page 2:
2025-06-28 18:00:21,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:21,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:22,180 - INFO - Response - Page 2:
2025-06-28 18:00:22,390 - INFO - 第 2 页获取到 100 条记录
2025-06-28 18:00:22,390 - INFO - Request Parameters - Page 3:
2025-06-28 18:00:22,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:22,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:22,882 - INFO - Response - Page 3:
2025-06-28 18:00:23,087 - INFO - 第 3 页获取到 100 条记录
2025-06-28 18:00:23,087 - INFO - Request Parameters - Page 4:
2025-06-28 18:00:23,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:23,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:23,617 - INFO - Response - Page 4:
2025-06-28 18:00:23,822 - INFO - 第 4 页获取到 100 条记录
2025-06-28 18:00:23,822 - INFO - Request Parameters - Page 5:
2025-06-28 18:00:23,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:23,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:24,279 - INFO - Response - Page 5:
2025-06-28 18:00:24,484 - INFO - 第 5 页获取到 100 条记录
2025-06-28 18:00:24,484 - INFO - Request Parameters - Page 6:
2025-06-28 18:00:24,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:24,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:24,984 - INFO - Response - Page 6:
2025-06-28 18:00:25,189 - INFO - 第 6 页获取到 100 条记录
2025-06-28 18:00:25,189 - INFO - Request Parameters - Page 7:
2025-06-28 18:00:25,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:25,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:25,605 - INFO - Response - Page 7:
2025-06-28 18:00:25,810 - INFO - 第 7 页获取到 56 条记录
2025-06-28 18:00:25,810 - INFO - 查询完成，共获取到 656 条记录
2025-06-28 18:00:25,810 - INFO - 获取到 656 条表单数据
2025-06-28 18:00:25,810 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-28 18:00:25,826 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 18:00:25,826 - INFO - 开始处理日期: 2025-05
2025-06-28 18:00:25,826 - INFO - Request Parameters - Page 1:
2025-06-28 18:00:25,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:25,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:26,393 - INFO - Response - Page 1:
2025-06-28 18:00:26,605 - INFO - 第 1 页获取到 100 条记录
2025-06-28 18:00:26,605 - INFO - Request Parameters - Page 2:
2025-06-28 18:00:26,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:26,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:27,252 - INFO - Response - Page 2:
2025-06-28 18:00:27,457 - INFO - 第 2 页获取到 100 条记录
2025-06-28 18:00:27,457 - INFO - Request Parameters - Page 3:
2025-06-28 18:00:27,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:27,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:27,968 - INFO - Response - Page 3:
2025-06-28 18:00:28,173 - INFO - 第 3 页获取到 100 条记录
2025-06-28 18:00:28,173 - INFO - Request Parameters - Page 4:
2025-06-28 18:00:28,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:28,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:28,702 - INFO - Response - Page 4:
2025-06-28 18:00:28,910 - INFO - 第 4 页获取到 100 条记录
2025-06-28 18:00:28,910 - INFO - Request Parameters - Page 5:
2025-06-28 18:00:28,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:28,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:29,463 - INFO - Response - Page 5:
2025-06-28 18:00:29,665 - INFO - 第 5 页获取到 100 条记录
2025-06-28 18:00:29,665 - INFO - Request Parameters - Page 6:
2025-06-28 18:00:29,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:29,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:30,108 - INFO - Response - Page 6:
2025-06-28 18:00:30,313 - INFO - 第 6 页获取到 100 条记录
2025-06-28 18:00:30,313 - INFO - Request Parameters - Page 7:
2025-06-28 18:00:30,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:30,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:30,760 - INFO - Response - Page 7:
2025-06-28 18:00:30,965 - INFO - 第 7 页获取到 65 条记录
2025-06-28 18:00:30,965 - INFO - 查询完成，共获取到 665 条记录
2025-06-28 18:00:30,965 - INFO - 获取到 665 条表单数据
2025-06-28 18:00:30,965 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-28 18:00:30,981 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 18:00:30,981 - INFO - 开始处理日期: 2025-06
2025-06-28 18:00:30,981 - INFO - Request Parameters - Page 1:
2025-06-28 18:00:30,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:30,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:31,552 - INFO - Response - Page 1:
2025-06-28 18:00:31,759 - INFO - 第 1 页获取到 100 条记录
2025-06-28 18:00:31,759 - INFO - Request Parameters - Page 2:
2025-06-28 18:00:31,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:31,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:32,375 - INFO - Response - Page 2:
2025-06-28 18:00:32,585 - INFO - 第 2 页获取到 100 条记录
2025-06-28 18:00:32,585 - INFO - Request Parameters - Page 3:
2025-06-28 18:00:32,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:32,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:33,010 - INFO - Response - Page 3:
2025-06-28 18:00:33,215 - INFO - 第 3 页获取到 100 条记录
2025-06-28 18:00:33,215 - INFO - Request Parameters - Page 4:
2025-06-28 18:00:33,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:33,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:33,693 - INFO - Response - Page 4:
2025-06-28 18:00:33,898 - INFO - 第 4 页获取到 100 条记录
2025-06-28 18:00:33,898 - INFO - Request Parameters - Page 5:
2025-06-28 18:00:33,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:33,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:34,386 - INFO - Response - Page 5:
2025-06-28 18:00:34,586 - INFO - 第 5 页获取到 100 条记录
2025-06-28 18:00:34,586 - INFO - Request Parameters - Page 6:
2025-06-28 18:00:34,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:34,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:35,138 - INFO - Response - Page 6:
2025-06-28 18:00:35,343 - INFO - 第 6 页获取到 100 条记录
2025-06-28 18:00:35,343 - INFO - Request Parameters - Page 7:
2025-06-28 18:00:35,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 18:00:35,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 18:00:35,713 - INFO - Response - Page 7:
2025-06-28 18:00:35,919 - INFO - 第 7 页获取到 29 条记录
2025-06-28 18:00:35,919 - INFO - 查询完成，共获取到 629 条记录
2025-06-28 18:00:35,919 - INFO - 获取到 629 条表单数据
2025-06-28 18:00:35,919 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-28 18:00:35,919 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMTX
2025-06-28 18:00:36,533 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMTX
2025-06-28 18:00:36,533 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1.0}, {'field': 'offline_amount', 'old_value': 739739.0, 'new_value': 771881.0}, {'field': 'total_amount', 'old_value': 739739.0, 'new_value': 771882.0}, {'field': 'order_count', 'old_value': 2582, 'new_value': 2625}]
2025-06-28 18:00:36,534 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFZ
2025-06-28 18:00:36,965 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFZ
2025-06-28 18:00:36,965 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64646.23, 'new_value': 68597.76}, {'field': 'total_amount', 'old_value': 72443.77, 'new_value': 76395.3}, {'field': 'order_count', 'old_value': 1997, 'new_value': 2076}]
2025-06-28 18:00:36,981 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1R
2025-06-28 18:00:37,461 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1R
2025-06-28 18:00:37,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156447.4, 'new_value': 165712.4}, {'field': 'total_amount', 'old_value': 184105.2, 'new_value': 193370.2}, {'field': 'order_count', 'old_value': 1661, 'new_value': 1758}]
2025-06-28 18:00:37,461 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-28 18:00:37,954 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-28 18:00:37,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203272.0, 'new_value': 219497.0}, {'field': 'total_amount', 'old_value': 251256.0, 'new_value': 267481.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 49}]
2025-06-28 18:00:37,954 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-28 18:00:38,461 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-28 18:00:38,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73130.51, 'new_value': 76937.33}, {'field': 'total_amount', 'old_value': 73130.51, 'new_value': 76937.33}, {'field': 'order_count', 'old_value': 2904, 'new_value': 3059}]
2025-06-28 18:00:38,462 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-28 18:00:38,910 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-28 18:00:38,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157649.0, 'new_value': 163627.0}, {'field': 'total_amount', 'old_value': 157649.0, 'new_value': 163627.0}, {'field': 'order_count', 'old_value': 14122, 'new_value': 14689}]
2025-06-28 18:00:38,910 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-28 18:00:39,351 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-28 18:00:39,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14845.0, 'new_value': 16144.0}, {'field': 'total_amount', 'old_value': 17044.0, 'new_value': 18343.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 41}]
2025-06-28 18:00:39,351 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-28 18:00:39,832 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-28 18:00:39,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51916.1, 'new_value': 55420.1}, {'field': 'total_amount', 'old_value': 51916.1, 'new_value': 55420.1}, {'field': 'order_count', 'old_value': 7886, 'new_value': 4382}]
2025-06-28 18:00:39,832 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-28 18:00:40,257 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-28 18:00:40,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1248000.0, 'new_value': 1297000.0}, {'field': 'total_amount', 'old_value': 1248000.0, 'new_value': 1297000.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 96}]
2025-06-28 18:00:40,257 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM13
2025-06-28 18:00:40,736 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM13
2025-06-28 18:00:40,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160272.0, 'new_value': 175272.0}, {'field': 'total_amount', 'old_value': 160272.0, 'new_value': 175272.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-28 18:00:40,736 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-28 18:00:41,193 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-28 18:00:41,193 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52245.0, 'new_value': 57845.0}, {'field': 'total_amount', 'old_value': 52245.0, 'new_value': 57845.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-06-28 18:00:41,193 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-28 18:00:41,572 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-28 18:00:41,572 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131897.0, 'new_value': 140736.0}, {'field': 'total_amount', 'old_value': 131897.0, 'new_value': 140736.0}, {'field': 'order_count', 'old_value': 3630, 'new_value': 3835}]
2025-06-28 18:00:41,572 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-28 18:00:42,082 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-28 18:00:42,082 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 267485.04, 'new_value': 270713.04}, {'field': 'total_amount', 'old_value': 267485.04, 'new_value': 270713.04}, {'field': 'order_count', 'old_value': 104, 'new_value': 105}]
2025-06-28 18:00:42,082 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-28 18:00:42,523 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-28 18:00:42,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21151.0, 'new_value': 21350.0}, {'field': 'total_amount', 'old_value': 21151.0, 'new_value': 21350.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-06-28 18:00:42,523 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-28 18:00:43,001 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-28 18:00:43,001 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154000.0, 'new_value': 168000.0}, {'field': 'total_amount', 'old_value': 154000.0, 'new_value': 168000.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-06-28 18:00:43,001 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC3
2025-06-28 18:00:43,394 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC3
2025-06-28 18:00:43,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128938.0, 'new_value': 141098.0}, {'field': 'total_amount', 'old_value': 128938.0, 'new_value': 141098.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 42}]
2025-06-28 18:00:43,394 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-28 18:00:43,830 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-28 18:00:43,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124907.98, 'new_value': 128760.98}, {'field': 'total_amount', 'old_value': 124907.98, 'new_value': 128760.98}, {'field': 'order_count', 'old_value': 8421, 'new_value': 4568}]
2025-06-28 18:00:43,830 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-28 18:00:44,287 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-28 18:00:44,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101574.0, 'new_value': 105066.0}, {'field': 'total_amount', 'old_value': 101574.0, 'new_value': 105066.0}, {'field': 'order_count', 'old_value': 4243, 'new_value': 751}]
2025-06-28 18:00:44,287 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-28 18:00:44,716 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-28 18:00:44,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34791.0, 'new_value': 35663.0}, {'field': 'total_amount', 'old_value': 34791.0, 'new_value': 35663.0}, {'field': 'order_count', 'old_value': 3989, 'new_value': 3117}]
2025-06-28 18:00:44,732 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-28 18:00:45,174 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-28 18:00:45,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13142.0, 'new_value': 13395.0}, {'field': 'total_amount', 'old_value': 13142.0, 'new_value': 13395.0}, {'field': 'order_count', 'old_value': 1133, 'new_value': 1140}]
2025-06-28 18:00:45,174 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMM3
2025-06-28 18:00:45,616 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMM3
2025-06-28 18:00:45,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44970.0, 'new_value': 52570.0}, {'field': 'total_amount', 'old_value': 44970.0, 'new_value': 52570.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-28 18:00:45,616 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-28 18:00:46,047 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-28 18:00:46,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 277480.43, 'new_value': 294006.32}, {'field': 'total_amount', 'old_value': 277480.43, 'new_value': 294006.32}, {'field': 'order_count', 'old_value': 2061, 'new_value': 2109}]
2025-06-28 18:00:46,047 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMR3
2025-06-28 18:00:46,504 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMR3
2025-06-28 18:00:46,504 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41808.0, 'new_value': 46622.0}, {'field': 'total_amount', 'old_value': 41808.0, 'new_value': 46622.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 47}]
2025-06-28 18:00:46,520 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMKP
2025-06-28 18:00:46,959 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMKP
2025-06-28 18:00:46,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8430.9, 'new_value': 8440.8}, {'field': 'total_amount', 'old_value': 8430.9, 'new_value': 8440.8}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-28 18:00:46,959 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-28 18:00:47,417 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-28 18:00:47,417 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5126336.0, 'new_value': 5346395.0}, {'field': 'total_amount', 'old_value': 5126336.0, 'new_value': 5346395.0}, {'field': 'order_count', 'old_value': 94427, 'new_value': 98486}]
2025-06-28 18:00:47,417 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-28 18:00:47,852 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-28 18:00:47,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83804.0, 'new_value': 86404.0}, {'field': 'total_amount', 'old_value': 83804.0, 'new_value': 86404.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-06-28 18:00:47,852 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-28 18:00:48,357 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-28 18:00:48,357 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 222131.12, 'new_value': 254127.12}, {'field': 'total_amount', 'old_value': 222131.12, 'new_value': 254127.12}, {'field': 'order_count', 'old_value': 411, 'new_value': 442}]
2025-06-28 18:00:48,357 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-28 18:00:48,802 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-28 18:00:48,802 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9595.5, 'new_value': 9794.5}, {'field': 'total_amount', 'old_value': 9595.5, 'new_value': 9794.5}, {'field': 'order_count', 'old_value': 69, 'new_value': 70}]
2025-06-28 18:00:48,802 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-28 18:00:49,228 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-28 18:00:49,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5233695.0, 'new_value': 5245410.0}, {'field': 'total_amount', 'old_value': 5233695.0, 'new_value': 5245410.0}, {'field': 'order_count', 'old_value': 12253, 'new_value': 538}]
2025-06-28 18:00:49,228 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-28 18:00:49,710 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-28 18:00:49,710 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 420357.0, 'new_value': 436273.0}, {'field': 'total_amount', 'old_value': 420357.0, 'new_value': 436273.0}, {'field': 'order_count', 'old_value': 491, 'new_value': 509}]
2025-06-28 18:00:49,710 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-28 18:00:50,231 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-28 18:00:50,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39878.32, 'new_value': 41733.18}, {'field': 'total_amount', 'old_value': 39878.32, 'new_value': 41733.18}, {'field': 'order_count', 'old_value': 1581, 'new_value': 1650}]
2025-06-28 18:00:50,231 - INFO - 日期 2025-06 处理完成 - 更新: 31 条，插入: 0 条，错误: 0 条
2025-06-28 18:00:50,231 - INFO - 数据同步完成！更新: 31 条，插入: 0 条，错误: 0 条
2025-06-28 18:00:50,231 - INFO - =================同步完成====================
2025-06-28 21:00:03,373 - INFO - =================使用默认全量同步=============
2025-06-28 21:00:05,172 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-28 21:00:05,172 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-28 21:00:05,204 - INFO - 开始处理日期: 2025-01
2025-06-28 21:00:05,204 - INFO - Request Parameters - Page 1:
2025-06-28 21:00:05,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:05,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:06,703 - INFO - Response - Page 1:
2025-06-28 21:00:06,908 - INFO - 第 1 页获取到 100 条记录
2025-06-28 21:00:06,908 - INFO - Request Parameters - Page 2:
2025-06-28 21:00:06,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:06,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:07,460 - INFO - Response - Page 2:
2025-06-28 21:00:07,671 - INFO - 第 2 页获取到 100 条记录
2025-06-28 21:00:07,671 - INFO - Request Parameters - Page 3:
2025-06-28 21:00:07,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:07,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:08,265 - INFO - Response - Page 3:
2025-06-28 21:00:08,469 - INFO - 第 3 页获取到 100 条记录
2025-06-28 21:00:08,469 - INFO - Request Parameters - Page 4:
2025-06-28 21:00:08,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:08,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:09,041 - INFO - Response - Page 4:
2025-06-28 21:00:09,246 - INFO - 第 4 页获取到 100 条记录
2025-06-28 21:00:09,246 - INFO - Request Parameters - Page 5:
2025-06-28 21:00:09,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:09,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:09,786 - INFO - Response - Page 5:
2025-06-28 21:00:09,991 - INFO - 第 5 页获取到 100 条记录
2025-06-28 21:00:09,991 - INFO - Request Parameters - Page 6:
2025-06-28 21:00:09,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:09,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:10,543 - INFO - Response - Page 6:
2025-06-28 21:00:10,743 - INFO - 第 6 页获取到 100 条记录
2025-06-28 21:00:10,743 - INFO - Request Parameters - Page 7:
2025-06-28 21:00:10,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:10,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:11,204 - INFO - Response - Page 7:
2025-06-28 21:00:11,409 - INFO - 第 7 页获取到 82 条记录
2025-06-28 21:00:11,409 - INFO - 查询完成，共获取到 682 条记录
2025-06-28 21:00:11,409 - INFO - 获取到 682 条表单数据
2025-06-28 21:00:11,424 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-28 21:00:11,424 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 21:00:11,424 - INFO - 开始处理日期: 2025-02
2025-06-28 21:00:11,424 - INFO - Request Parameters - Page 1:
2025-06-28 21:00:11,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:11,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:11,983 - INFO - Response - Page 1:
2025-06-28 21:00:12,188 - INFO - 第 1 页获取到 100 条记录
2025-06-28 21:00:12,188 - INFO - Request Parameters - Page 2:
2025-06-28 21:00:12,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:12,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:12,732 - INFO - Response - Page 2:
2025-06-28 21:00:12,937 - INFO - 第 2 页获取到 100 条记录
2025-06-28 21:00:12,937 - INFO - Request Parameters - Page 3:
2025-06-28 21:00:12,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:12,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:13,656 - INFO - Response - Page 3:
2025-06-28 21:00:13,861 - INFO - 第 3 页获取到 100 条记录
2025-06-28 21:00:13,861 - INFO - Request Parameters - Page 4:
2025-06-28 21:00:13,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:13,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:14,388 - INFO - Response - Page 4:
2025-06-28 21:00:14,593 - INFO - 第 4 页获取到 100 条记录
2025-06-28 21:00:14,593 - INFO - Request Parameters - Page 5:
2025-06-28 21:00:14,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:14,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:15,070 - INFO - Response - Page 5:
2025-06-28 21:00:15,275 - INFO - 第 5 页获取到 100 条记录
2025-06-28 21:00:15,275 - INFO - Request Parameters - Page 6:
2025-06-28 21:00:15,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:15,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:15,798 - INFO - Response - Page 6:
2025-06-28 21:00:16,003 - INFO - 第 6 页获取到 100 条记录
2025-06-28 21:00:16,003 - INFO - Request Parameters - Page 7:
2025-06-28 21:00:16,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:16,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:16,571 - INFO - Response - Page 7:
2025-06-28 21:00:16,783 - INFO - 第 7 页获取到 70 条记录
2025-06-28 21:00:16,783 - INFO - 查询完成，共获取到 670 条记录
2025-06-28 21:00:16,783 - INFO - 获取到 670 条表单数据
2025-06-28 21:00:16,783 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-28 21:00:16,799 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 21:00:16,799 - INFO - 开始处理日期: 2025-03
2025-06-28 21:00:16,799 - INFO - Request Parameters - Page 1:
2025-06-28 21:00:16,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:16,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:17,537 - INFO - Response - Page 1:
2025-06-28 21:00:17,746 - INFO - 第 1 页获取到 100 条记录
2025-06-28 21:00:17,746 - INFO - Request Parameters - Page 2:
2025-06-28 21:00:17,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:17,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:18,251 - INFO - Response - Page 2:
2025-06-28 21:00:18,456 - INFO - 第 2 页获取到 100 条记录
2025-06-28 21:00:18,456 - INFO - Request Parameters - Page 3:
2025-06-28 21:00:18,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:18,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:18,996 - INFO - Response - Page 3:
2025-06-28 21:00:19,205 - INFO - 第 3 页获取到 100 条记录
2025-06-28 21:00:19,205 - INFO - Request Parameters - Page 4:
2025-06-28 21:00:19,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:19,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:19,814 - INFO - Response - Page 4:
2025-06-28 21:00:20,018 - INFO - 第 4 页获取到 100 条记录
2025-06-28 21:00:20,018 - INFO - Request Parameters - Page 5:
2025-06-28 21:00:20,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:20,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:20,476 - INFO - Response - Page 5:
2025-06-28 21:00:20,680 - INFO - 第 5 页获取到 100 条记录
2025-06-28 21:00:20,680 - INFO - Request Parameters - Page 6:
2025-06-28 21:00:20,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:20,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:21,397 - INFO - Response - Page 6:
2025-06-28 21:00:21,602 - INFO - 第 6 页获取到 100 条记录
2025-06-28 21:00:21,602 - INFO - Request Parameters - Page 7:
2025-06-28 21:00:21,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:21,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:22,082 - INFO - Response - Page 7:
2025-06-28 21:00:22,287 - INFO - 第 7 页获取到 61 条记录
2025-06-28 21:00:22,287 - INFO - 查询完成，共获取到 661 条记录
2025-06-28 21:00:22,287 - INFO - 获取到 661 条表单数据
2025-06-28 21:00:22,287 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-28 21:00:22,303 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 21:00:22,303 - INFO - 开始处理日期: 2025-04
2025-06-28 21:00:22,303 - INFO - Request Parameters - Page 1:
2025-06-28 21:00:22,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:22,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:22,815 - INFO - Response - Page 1:
2025-06-28 21:00:23,016 - INFO - 第 1 页获取到 100 条记录
2025-06-28 21:00:23,016 - INFO - Request Parameters - Page 2:
2025-06-28 21:00:23,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:23,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:23,599 - INFO - Response - Page 2:
2025-06-28 21:00:23,809 - INFO - 第 2 页获取到 100 条记录
2025-06-28 21:00:23,809 - INFO - Request Parameters - Page 3:
2025-06-28 21:00:23,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:23,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:24,283 - INFO - Response - Page 3:
2025-06-28 21:00:24,487 - INFO - 第 3 页获取到 100 条记录
2025-06-28 21:00:24,487 - INFO - Request Parameters - Page 4:
2025-06-28 21:00:24,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:24,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:24,983 - INFO - Response - Page 4:
2025-06-28 21:00:25,188 - INFO - 第 4 页获取到 100 条记录
2025-06-28 21:00:25,188 - INFO - Request Parameters - Page 5:
2025-06-28 21:00:25,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:25,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:25,739 - INFO - Response - Page 5:
2025-06-28 21:00:25,940 - INFO - 第 5 页获取到 100 条记录
2025-06-28 21:00:25,940 - INFO - Request Parameters - Page 6:
2025-06-28 21:00:25,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:25,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:26,461 - INFO - Response - Page 6:
2025-06-28 21:00:26,666 - INFO - 第 6 页获取到 100 条记录
2025-06-28 21:00:26,666 - INFO - Request Parameters - Page 7:
2025-06-28 21:00:26,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:26,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:27,113 - INFO - Response - Page 7:
2025-06-28 21:00:27,318 - INFO - 第 7 页获取到 56 条记录
2025-06-28 21:00:27,318 - INFO - 查询完成，共获取到 656 条记录
2025-06-28 21:00:27,318 - INFO - 获取到 656 条表单数据
2025-06-28 21:00:27,318 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-28 21:00:27,334 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 21:00:27,334 - INFO - 开始处理日期: 2025-05
2025-06-28 21:00:27,334 - INFO - Request Parameters - Page 1:
2025-06-28 21:00:27,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:27,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:27,796 - INFO - Response - Page 1:
2025-06-28 21:00:28,000 - INFO - 第 1 页获取到 100 条记录
2025-06-28 21:00:28,000 - INFO - Request Parameters - Page 2:
2025-06-28 21:00:28,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:28,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:28,607 - INFO - Response - Page 2:
2025-06-28 21:00:28,816 - INFO - 第 2 页获取到 100 条记录
2025-06-28 21:00:28,816 - INFO - Request Parameters - Page 3:
2025-06-28 21:00:28,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:28,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:29,343 - INFO - Response - Page 3:
2025-06-28 21:00:29,547 - INFO - 第 3 页获取到 100 条记录
2025-06-28 21:00:29,547 - INFO - Request Parameters - Page 4:
2025-06-28 21:00:29,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:29,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:30,040 - INFO - Response - Page 4:
2025-06-28 21:00:30,245 - INFO - 第 4 页获取到 100 条记录
2025-06-28 21:00:30,245 - INFO - Request Parameters - Page 5:
2025-06-28 21:00:30,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:30,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:30,788 - INFO - Response - Page 5:
2025-06-28 21:00:30,999 - INFO - 第 5 页获取到 100 条记录
2025-06-28 21:00:30,999 - INFO - Request Parameters - Page 6:
2025-06-28 21:00:30,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:30,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:31,503 - INFO - Response - Page 6:
2025-06-28 21:00:31,708 - INFO - 第 6 页获取到 100 条记录
2025-06-28 21:00:31,708 - INFO - Request Parameters - Page 7:
2025-06-28 21:00:31,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:31,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:32,203 - INFO - Response - Page 7:
2025-06-28 21:00:32,408 - INFO - 第 7 页获取到 65 条记录
2025-06-28 21:00:32,408 - INFO - 查询完成，共获取到 665 条记录
2025-06-28 21:00:32,408 - INFO - 获取到 665 条表单数据
2025-06-28 21:00:32,408 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-28 21:00:32,424 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-28 21:00:32,424 - INFO - 开始处理日期: 2025-06
2025-06-28 21:00:32,424 - INFO - Request Parameters - Page 1:
2025-06-28 21:00:32,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:32,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:32,901 - INFO - Response - Page 1:
2025-06-28 21:00:33,106 - INFO - 第 1 页获取到 100 条记录
2025-06-28 21:00:33,106 - INFO - Request Parameters - Page 2:
2025-06-28 21:00:33,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:33,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:33,594 - INFO - Response - Page 2:
2025-06-28 21:00:33,799 - INFO - 第 2 页获取到 100 条记录
2025-06-28 21:00:33,799 - INFO - Request Parameters - Page 3:
2025-06-28 21:00:33,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:33,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:34,309 - INFO - Response - Page 3:
2025-06-28 21:00:34,521 - INFO - 第 3 页获取到 100 条记录
2025-06-28 21:00:34,521 - INFO - Request Parameters - Page 4:
2025-06-28 21:00:34,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:34,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:35,039 - INFO - Response - Page 4:
2025-06-28 21:00:35,243 - INFO - 第 4 页获取到 100 条记录
2025-06-28 21:00:35,243 - INFO - Request Parameters - Page 5:
2025-06-28 21:00:35,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:35,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:35,896 - INFO - Response - Page 5:
2025-06-28 21:00:36,108 - INFO - 第 5 页获取到 100 条记录
2025-06-28 21:00:36,108 - INFO - Request Parameters - Page 6:
2025-06-28 21:00:36,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:36,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:36,644 - INFO - Response - Page 6:
2025-06-28 21:00:36,850 - INFO - 第 6 页获取到 100 条记录
2025-06-28 21:00:36,850 - INFO - Request Parameters - Page 7:
2025-06-28 21:00:36,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-28 21:00:36,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-28 21:00:37,267 - INFO - Response - Page 7:
2025-06-28 21:00:37,472 - INFO - 第 7 页获取到 29 条记录
2025-06-28 21:00:37,472 - INFO - 查询完成，共获取到 629 条记录
2025-06-28 21:00:37,472 - INFO - 获取到 629 条表单数据
2025-06-28 21:00:37,472 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-28 21:00:37,472 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-28 21:00:37,994 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-28 21:00:37,994 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87504.0, 'new_value': 89633.0}, {'field': 'offline_amount', 'old_value': 108320.0, 'new_value': 113203.0}, {'field': 'total_amount', 'old_value': 195824.0, 'new_value': 202836.0}, {'field': 'order_count', 'old_value': 4138, 'new_value': 4293}]
2025-06-28 21:00:38,010 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-28 21:00:38,010 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-06-28 21:00:38,010 - INFO - =================同步完成====================
