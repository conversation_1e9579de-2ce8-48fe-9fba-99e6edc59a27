2025-07-13 01:30:33,833 - INFO - 使用默认增量同步（当天更新数据）
2025-07-13 01:30:33,833 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-13 01:30:33,833 - INFO - 查询参数: ('2025-07-13',)
2025-07-13 01:30:33,911 - INFO - MySQL查询成功，增量数据（日期: 2025-07-13），共获取 0 条记录
2025-07-13 01:30:33,911 - ERROR - 未获取到MySQL数据
2025-07-13 01:31:33,926 - INFO - 开始同步昨天与今天的销售数据: 2025-07-12 至 2025-07-13
2025-07-13 01:31:33,926 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-13 01:31:33,926 - INFO - 查询参数: ('2025-07-12', '2025-07-13')
2025-07-13 01:31:34,067 - INFO - MySQL查询成功，时间段: 2025-07-12 至 2025-07-13，共获取 48 条记录
2025-07-13 01:31:34,067 - INFO - 获取到 1 个日期需要处理: ['2025-07-12']
2025-07-13 01:31:34,067 - INFO - 开始处理日期: 2025-07-12
2025-07-13 01:31:34,067 - INFO - Request Parameters - Page 1:
2025-07-13 01:31:34,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 01:31:34,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 01:31:42,207 - ERROR - 处理日期 2025-07-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8FBBD792-1478-7180-B65A-A778CEB3CCF3 Response: {'code': 'ServiceUnavailable', 'requestid': '8FBBD792-1478-7180-B65A-A778CEB3CCF3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8FBBD792-1478-7180-B65A-A778CEB3CCF3)
2025-07-13 01:31:42,207 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-13 01:31:42,207 - INFO - 同步完成
2025-07-13 04:30:33,526 - INFO - 使用默认增量同步（当天更新数据）
2025-07-13 04:30:33,526 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-13 04:30:33,526 - INFO - 查询参数: ('2025-07-13',)
2025-07-13 04:30:33,620 - INFO - MySQL查询成功，增量数据（日期: 2025-07-13），共获取 0 条记录
2025-07-13 04:30:33,620 - ERROR - 未获取到MySQL数据
2025-07-13 04:31:33,635 - INFO - 开始同步昨天与今天的销售数据: 2025-07-12 至 2025-07-13
2025-07-13 04:31:33,635 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-13 04:31:33,635 - INFO - 查询参数: ('2025-07-12', '2025-07-13')
2025-07-13 04:31:33,776 - INFO - MySQL查询成功，时间段: 2025-07-12 至 2025-07-13，共获取 48 条记录
2025-07-13 04:31:33,776 - INFO - 获取到 1 个日期需要处理: ['2025-07-12']
2025-07-13 04:31:33,776 - INFO - 开始处理日期: 2025-07-12
2025-07-13 04:31:33,776 - INFO - Request Parameters - Page 1:
2025-07-13 04:31:33,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 04:31:33,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 04:31:41,901 - ERROR - 处理日期 2025-07-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DF9F0C7D-45D7-7A4F-BBF6-93328B57101E Response: {'code': 'ServiceUnavailable', 'requestid': 'DF9F0C7D-45D7-7A4F-BBF6-93328B57101E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DF9F0C7D-45D7-7A4F-BBF6-93328B57101E)
2025-07-13 04:31:41,901 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-13 04:31:41,901 - INFO - 同步完成
2025-07-13 07:30:33,656 - INFO - 使用默认增量同步（当天更新数据）
2025-07-13 07:30:33,656 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-13 07:30:33,656 - INFO - 查询参数: ('2025-07-13',)
2025-07-13 07:30:33,812 - INFO - MySQL查询成功，增量数据（日期: 2025-07-13），共获取 2 条记录
2025-07-13 07:30:33,812 - INFO - 获取到 1 个日期需要处理: ['2025-07-12']
2025-07-13 07:30:33,812 - INFO - 开始处理日期: 2025-07-12
2025-07-13 07:30:33,812 - INFO - Request Parameters - Page 1:
2025-07-13 07:30:33,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 07:30:33,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 07:30:41,937 - ERROR - 处理日期 2025-07-12 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5DB7C08F-76B5-73E7-B0BD-5FB3D9D3F7BE Response: {'code': 'ServiceUnavailable', 'requestid': '5DB7C08F-76B5-73E7-B0BD-5FB3D9D3F7BE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5DB7C08F-76B5-73E7-B0BD-5FB3D9D3F7BE)
2025-07-13 07:30:41,937 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-13 07:31:41,953 - INFO - 开始同步昨天与今天的销售数据: 2025-07-12 至 2025-07-13
2025-07-13 07:31:41,953 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-13 07:31:41,953 - INFO - 查询参数: ('2025-07-12', '2025-07-13')
2025-07-13 07:31:42,093 - INFO - MySQL查询成功，时间段: 2025-07-12 至 2025-07-13，共获取 50 条记录
2025-07-13 07:31:42,093 - INFO - 获取到 1 个日期需要处理: ['2025-07-12']
2025-07-13 07:31:42,093 - INFO - 开始处理日期: 2025-07-12
2025-07-13 07:31:42,093 - INFO - Request Parameters - Page 1:
2025-07-13 07:31:42,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 07:31:42,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 07:31:48,968 - INFO - Response - Page 1:
2025-07-13 07:31:48,968 - INFO - 第 1 页获取到 45 条记录
2025-07-13 07:31:49,468 - INFO - 查询完成，共获取到 45 条记录
2025-07-13 07:31:49,468 - INFO - 获取到 45 条表单数据
2025-07-13 07:31:49,468 - INFO - 当前日期 2025-07-12 有 47 条MySQL数据需要处理
2025-07-13 07:31:49,468 - INFO - 开始批量插入 2 条新记录
2025-07-13 07:31:49,624 - INFO - 批量插入响应状态码: 200
2025-07-13 07:31:49,624 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 12 Jul 2025 23:31:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '110', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '39076C7D-9514-7641-ADF4-414B67042668', 'x-acs-trace-id': 'b0318320cf88f320c8d9be35d72a8be9', 'etag': '1DdSk+aiTozCGG3zgoPEo1w0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 07:31:49,624 - INFO - 批量插入响应体: {'result': ['FINST-49866E71B12X0X6Z9X4T25660JII21V0RV0DM9C1', 'FINST-49866E71B12X0X6Z9X4T25660JII22V0RV0DMAC1']}
2025-07-13 07:31:49,624 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-13 07:31:49,624 - INFO - 成功插入的数据ID: ['FINST-49866E71B12X0X6Z9X4T25660JII21V0RV0DM9C1', 'FINST-49866E71B12X0X6Z9X4T25660JII22V0RV0DMAC1']
2025-07-13 07:31:54,640 - INFO - 批量插入完成，共 2 条记录
2025-07-13 07:31:54,640 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-13 07:31:54,640 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-07-13 07:31:54,640 - INFO - 同步完成
2025-07-13 10:30:34,147 - INFO - 使用默认增量同步（当天更新数据）
2025-07-13 10:30:34,147 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-13 10:30:34,147 - INFO - 查询参数: ('2025-07-13',)
2025-07-13 10:30:34,287 - INFO - MySQL查询成功，增量数据（日期: 2025-07-13），共获取 108 条记录
2025-07-13 10:30:34,287 - INFO - 获取到 3 个日期需要处理: ['2025-07-10', '2025-07-11', '2025-07-12']
2025-07-13 10:30:34,303 - INFO - 开始处理日期: 2025-07-10
2025-07-13 10:30:34,303 - INFO - Request Parameters - Page 1:
2025-07-13 10:30:34,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 10:30:34,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 10:30:42,428 - ERROR - 处理日期 2025-07-10 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6380A3D4-7731-78BE-8194-F73E7A1353E0 Response: {'code': 'ServiceUnavailable', 'requestid': '6380A3D4-7731-78BE-8194-F73E7A1353E0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6380A3D4-7731-78BE-8194-F73E7A1353E0)
2025-07-13 10:30:42,428 - INFO - 开始处理日期: 2025-07-11
2025-07-13 10:30:42,428 - INFO - Request Parameters - Page 1:
2025-07-13 10:30:42,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 10:30:42,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 10:30:50,553 - ERROR - 处理日期 2025-07-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E227C829-0A73-74AD-91C0-A3A194A37C96 Response: {'code': 'ServiceUnavailable', 'requestid': 'E227C829-0A73-74AD-91C0-A3A194A37C96', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E227C829-0A73-74AD-91C0-A3A194A37C96)
2025-07-13 10:30:50,553 - INFO - 开始处理日期: 2025-07-12
2025-07-13 10:30:50,553 - INFO - Request Parameters - Page 1:
2025-07-13 10:30:50,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 10:30:50,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 10:30:51,568 - INFO - Response - Page 1:
2025-07-13 10:30:51,568 - INFO - 第 1 页获取到 47 条记录
2025-07-13 10:30:52,084 - INFO - 查询完成，共获取到 47 条记录
2025-07-13 10:30:52,084 - INFO - 获取到 47 条表单数据
2025-07-13 10:30:52,084 - INFO - 当前日期 2025-07-12 有 101 条MySQL数据需要处理
2025-07-13 10:30:52,084 - INFO - 开始批量插入 99 条新记录
2025-07-13 10:30:52,318 - INFO - 批量插入响应状态码: 200
2025-07-13 10:30:52,318 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 02:30:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A2D0569B-B53D-7D8D-8C0A-C5CD41AFD9C2', 'x-acs-trace-id': '11baeb0e7a613c4e39770a75cb61d302', 'etag': '2yuvAPt2iupBGJGuM4X71HQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 10:30:52,318 - INFO - 批量插入响应体: {'result': ['FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMPO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMQO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMRO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMSO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMTO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMUO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMVO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMWO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMXO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMYO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMZO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM0P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM1P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM2P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM3P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM4P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM5P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM6P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM7P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM8P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM9P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMAP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMBP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMCP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMDP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMEP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMFP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMGP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMHP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMIP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMJP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMKP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMLP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMMP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMNP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMOP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMPP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMQP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMRP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMSP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMTP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMUP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMVP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMWP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMXP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMYP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMZP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM0Q', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM1Q', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM2Q']}
2025-07-13 10:30:52,318 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-13 10:30:52,318 - INFO - 成功插入的数据ID: ['FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMPO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMQO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMRO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMSO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMTO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMUO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMVO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMWO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMXO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMYO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMZO', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM0P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM1P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM2P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM3P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM4P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM5P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM6P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM7P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM8P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM9P', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMAP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMBP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMCP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMDP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMEP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMFP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMGP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMHP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMIP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMJP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMKP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMLP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMMP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMNP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMOP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMPP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMQP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMRP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMSP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMTP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMUP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMVP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMWP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMXP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMYP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DMZP', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM0Q', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM1Q', 'FINST-3ME66E819Z2X5YUAF4DBHB6795XP3D1A521DM2Q']
2025-07-13 10:30:57,568 - INFO - 批量插入响应状态码: 200
2025-07-13 10:30:57,568 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 02:30:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2413', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7B3E8C08-2148-764B-BC9A-D6468BC77BB2', 'x-acs-trace-id': 'fd496e39fb03cdf77d4466e8e0c9e70f', 'etag': '2Q1WGoqQrRXBsWwvBU6fUxA3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 10:30:57,568 - INFO - 批量插入响应体: {'result': ['FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMBL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMCL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMDL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMEL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMFL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMGL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMHL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMIL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMJL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMKL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMLL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMML1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMNL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMOL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMPL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMQL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMRL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMSL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMTL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMUL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMVL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMWL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMXL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMYL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMZL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM0M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM1M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM2M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM3M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM4M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM5M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM6M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM7M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM8M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM9M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMAM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMBM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMCM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMDM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMEM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMFM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMGM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMHM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMIM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMJM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMKM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMLM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMMM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I373E521DMNM1']}
2025-07-13 10:30:57,568 - INFO - 批量插入表单数据成功，批次 2，共 49 条记录
2025-07-13 10:30:57,568 - INFO - 成功插入的数据ID: ['FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMBL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMCL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMDL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMEL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMFL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMGL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMHL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMIL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMJL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMKL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMLL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMML1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMNL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMOL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMPL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMQL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMRL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMSL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMTL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMUL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMVL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMWL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMXL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMYL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMZL1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM0M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM1M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM2M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM3M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM4M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM5M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM6M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM7M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM8M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DM9M1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMAM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMBM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMCM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMDM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMEM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMFM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMGM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMHM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMIM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMJM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMKM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMLM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I363E521DMMM1', 'FINST-8P666U91FB2XGEGWDXR955WHWU6I373E521DMNM1']
2025-07-13 10:31:02,584 - INFO - 批量插入完成，共 99 条记录
2025-07-13 10:31:02,584 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 99 条，错误: 0 条
2025-07-13 10:31:02,584 - INFO - 数据同步完成！更新: 0 条，插入: 99 条，错误: 2 条
2025-07-13 10:32:02,599 - INFO - 开始同步昨天与今天的销售数据: 2025-07-12 至 2025-07-13
2025-07-13 10:32:02,599 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-13 10:32:02,599 - INFO - 查询参数: ('2025-07-12', '2025-07-13')
2025-07-13 10:32:02,755 - INFO - MySQL查询成功，时间段: 2025-07-12 至 2025-07-13，共获取 389 条记录
2025-07-13 10:32:02,755 - INFO - 获取到 1 个日期需要处理: ['2025-07-12']
2025-07-13 10:32:02,755 - INFO - 开始处理日期: 2025-07-12
2025-07-13 10:32:02,755 - INFO - Request Parameters - Page 1:
2025-07-13 10:32:02,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 10:32:02,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 10:32:03,490 - INFO - Response - Page 1:
2025-07-13 10:32:03,490 - INFO - 第 1 页获取到 50 条记录
2025-07-13 10:32:03,990 - INFO - Request Parameters - Page 2:
2025-07-13 10:32:03,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 10:32:03,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 10:32:04,662 - INFO - Response - Page 2:
2025-07-13 10:32:04,677 - INFO - 第 2 页获取到 50 条记录
2025-07-13 10:32:05,177 - INFO - Request Parameters - Page 3:
2025-07-13 10:32:05,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 10:32:05,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 10:32:05,927 - INFO - Response - Page 3:
2025-07-13 10:32:05,927 - INFO - 第 3 页获取到 46 条记录
2025-07-13 10:32:06,427 - INFO - 查询完成，共获取到 146 条记录
2025-07-13 10:32:06,427 - INFO - 获取到 146 条表单数据
2025-07-13 10:32:06,427 - INFO - 当前日期 2025-07-12 有 378 条MySQL数据需要处理
2025-07-13 10:32:06,427 - INFO - 开始批量插入 232 条新记录
2025-07-13 10:32:06,724 - INFO - 批量插入响应状态码: 200
2025-07-13 10:32:06,724 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 02:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '748DE2DD-08A9-77D2-BD8F-8BABB6D7968B', 'x-acs-trace-id': '3c0294c90b7d0d3c3e1b1fd4ab790dc6', 'etag': '2MRErvWxaR3EADZ0e+cVB1Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 10:32:06,724 - INFO - 批量插入响应体: {'result': ['FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMB8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMC8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMD8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DME8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMF8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMG8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMH8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMI8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMJ8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMK8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DML8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMM8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMN8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMO8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMP8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMQ8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMR8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMS8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMT8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMU8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMV8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMW8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMX8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMY8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMZ8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM09', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM19', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM29', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM39', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM49', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM59', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM69', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM79', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM89', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM99', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMA9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMB9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMC9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMD9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DME9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMF9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMG9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMH9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMI9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMJ9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMK9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DML9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMM9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMN9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMO9']}
2025-07-13 10:32:06,724 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-13 10:32:06,724 - INFO - 成功插入的数据ID: ['FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMB8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMC8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMD8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DME8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMF8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMG8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMH8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMI8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMJ8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMK8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DML8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMM8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMN8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMO8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMP8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMQ8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMR8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMS8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMT8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMU8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMV8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMW8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMX8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMY8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMZ8', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM09', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM19', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM29', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM39', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM49', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM59', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM69', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM79', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM89', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DM99', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMA9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMB9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMC9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMD9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DME9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMF9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMG9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMH9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMI9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMJ9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMK9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DML9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMM9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMN9', 'FINST-R8666Q71T92X2WZZ587HH6FEMXVV2SFV621DMO9']
2025-07-13 10:32:11,958 - INFO - 批量插入响应状态码: 200
2025-07-13 10:32:11,958 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 02:32:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B6BF3D86-E4AA-7CCA-9AB8-05BC2F6C7B07', 'x-acs-trace-id': '5bf316279bb649b01665c02d4543df86', 'etag': '2MA01CG5JX3WdrPetikbhlw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 10:32:11,958 - INFO - 批量插入响应体: {'result': ['FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3QHZ621DMC9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3QHZ621DMD9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3QHZ621DME9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3QHZ621DMF9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3QHZ621DMG9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3QHZ621DMH9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMI9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMJ9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMK9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DML9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMM9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMN9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMO9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMP9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMQ9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMR9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMS9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMT9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMU9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMV9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMW9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMX9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMY9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMZ9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM0A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM1A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM2A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM3A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM4A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM5A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM6A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM7A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM8A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM9A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMAA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMBA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMCA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMDA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMEA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMFA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMGA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMHA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMIA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMJA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMKA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMLA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMMA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMNA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMOA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMPA']}
2025-07-13 10:32:11,958 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-13 10:32:11,958 - INFO - 成功插入的数据ID: ['FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3QHZ621DMC9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3QHZ621DMD9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3QHZ621DME9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3QHZ621DMF9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3QHZ621DMG9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3QHZ621DMH9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMI9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMJ9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMK9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DML9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMM9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMN9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMO9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMP9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMQ9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMR9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMS9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMT9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMU9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMV9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMW9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMX9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMY9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMZ9', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM0A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM1A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM2A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM3A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM4A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM5A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM6A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM7A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM8A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DM9A', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMAA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMBA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMCA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMDA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMEA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMFA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMGA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMHA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMIA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMJA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMKA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMLA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMMA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMNA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMOA', 'FINST-2PF66TC1V92XKPTAB29ARANIM1TQ3RHZ621DMPA']
2025-07-13 10:32:17,224 - INFO - 批量插入响应状态码: 200
2025-07-13 10:32:17,224 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 02:32:17 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1C759A7B-076B-7E60-A0AA-E43623CCE35D', 'x-acs-trace-id': '30f5b72bbbe271f020233f854c1f2c8c', 'etag': '2/WTlD82HuruGU3aNww7YmQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 10:32:17,224 - INFO - 批量插入响应体: {'result': ['FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMRN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMSN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMTN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMUN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMVN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMWN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMXN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMYN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMZN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM0O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM1O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM2O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM3O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM4O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM5O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM6O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM7O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM8O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM9O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMAO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMBO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMCO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMDO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMEO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMFO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMGO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMHO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMIO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMJO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMKO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMLO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMMO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMNO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMOO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMPO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMQO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMRO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMSO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMTO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMUO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMVO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMWO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMXO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMYO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMZO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM0P', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM1P', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM2P', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM3P', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM4P']}
2025-07-13 10:32:17,224 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-13 10:32:17,224 - INFO - 成功插入的数据ID: ['FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMRN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMSN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMTN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMUN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMVN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMWN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMXN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMYN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMZN', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM0O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM1O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM2O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM3O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM4O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM5O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM6O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM7O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM8O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM9O', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMAO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMBO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMCO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMDO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMEO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMFO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMGO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMHO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMIO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMJO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMKO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMLO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMMO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMNO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMOO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMPO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMQO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMRO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMSO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMTO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMUO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMVO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMWO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMXO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMYO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DMZO', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM0P', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM1P', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM2P', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM3P', 'FINST-PPA66671NZ1XDUYECRQV76VXJ0133RJ3721DM4P']
2025-07-13 10:32:22,490 - INFO - 批量插入响应状态码: 200
2025-07-13 10:32:22,490 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 02:32:22 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F462D8F2-AD23-7ED8-964D-9D04812D8E30', 'x-acs-trace-id': 'b17c39ffe40ffd111d479e47b87caf5c', 'etag': '28cJVSm0HDB09GqjrZSd0fA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 10:32:22,490 - INFO - 批量插入响应体: {'result': ['FINST-80B66291A42XZVV19VAK7CZ35S9S3XL7721DMYC1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMZC1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM0D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM1D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM2D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM3D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM4D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM5D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM6D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM7D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM8D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM9D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMAD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMBD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMCD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMDD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMED1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMFD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMGD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMHD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMID1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMJD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMKD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMLD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMMD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMND1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMOD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMPD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMQD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMRD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMSD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMTD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMUD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMVD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMWD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMXD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMYD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMZD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM0E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM1E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM2E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM3E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM4E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM5E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM6E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM7E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM8E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM9E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMAE1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMBE1']}
2025-07-13 10:32:22,490 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-13 10:32:22,490 - INFO - 成功插入的数据ID: ['FINST-80B66291A42XZVV19VAK7CZ35S9S3XL7721DMYC1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMZC1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM0D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM1D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM2D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM3D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM4D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM5D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM6D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM7D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM8D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM9D1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMAD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMBD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMCD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMDD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMED1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMFD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMGD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMHD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMID1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMJD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMKD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMLD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMMD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMND1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMOD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMPD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMQD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMRD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMSD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMTD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMUD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMVD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMWD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMXD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMYD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMZD1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM0E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM1E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM2E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM3E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM4E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM5E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM6E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM7E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM8E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DM9E1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMAE1', 'FINST-80B66291A42XZVV19VAK7CZ35S9S3YL7721DMBE1']
2025-07-13 10:32:27,740 - INFO - 批量插入响应状态码: 200
2025-07-13 10:32:27,740 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 02:32:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1580', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D98FFE07-1F04-7E37-8557-39C6EC95EFC4', 'x-acs-trace-id': '56662458813480b678f5faf874f1d02d', 'etag': '1YD3b00AfvlIonfqKW4cLFA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 10:32:27,740 - INFO - 批量插入响应体: {'result': ['FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMT81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMU81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMV81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMW81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMX81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMY81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMZ81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM091', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM191', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM291', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM391', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM491', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM591', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM691', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM791', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM891', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM991', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMA91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMB91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMC91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMD91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DME91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMF91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMG91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMH91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMI91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMJ91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMK91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DML91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMM91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMN91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMO91']}
2025-07-13 10:32:27,740 - INFO - 批量插入表单数据成功，批次 5，共 32 条记录
2025-07-13 10:32:27,740 - INFO - 成功插入的数据ID: ['FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMT81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMU81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMV81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMW81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMX81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMY81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMZ81', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM091', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM191', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM291', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM391', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM491', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM591', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM691', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM791', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM891', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DM991', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMA91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMB91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMC91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMD91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DME91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMF91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMG91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMH91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMI91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMJ91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMK91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DML91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMM91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMN91', 'FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMO91']
2025-07-13 10:32:32,755 - INFO - 批量插入完成，共 232 条记录
2025-07-13 10:32:32,755 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 232 条，错误: 0 条
2025-07-13 10:32:32,755 - INFO - 数据同步完成！更新: 0 条，插入: 232 条，错误: 0 条
2025-07-13 10:32:32,755 - INFO - 同步完成
2025-07-13 13:30:33,559 - INFO - 使用默认增量同步（当天更新数据）
2025-07-13 13:30:33,559 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-13 13:30:33,559 - INFO - 查询参数: ('2025-07-13',)
2025-07-13 13:30:33,715 - INFO - MySQL查询成功，增量数据（日期: 2025-07-13），共获取 142 条记录
2025-07-13 13:30:33,715 - INFO - 获取到 3 个日期需要处理: ['2025-07-10', '2025-07-11', '2025-07-12']
2025-07-13 13:30:33,715 - INFO - 开始处理日期: 2025-07-10
2025-07-13 13:30:33,715 - INFO - Request Parameters - Page 1:
2025-07-13 13:30:33,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:30:33,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:30:41,825 - ERROR - 处理日期 2025-07-10 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 96686EB9-91EA-7AA6-A7FC-71BBF2DFFD87 Response: {'code': 'ServiceUnavailable', 'requestid': '96686EB9-91EA-7AA6-A7FC-71BBF2DFFD87', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 96686EB9-91EA-7AA6-A7FC-71BBF2DFFD87)
2025-07-13 13:30:41,825 - INFO - 开始处理日期: 2025-07-11
2025-07-13 13:30:41,825 - INFO - Request Parameters - Page 1:
2025-07-13 13:30:41,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:30:41,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:30:45,168 - INFO - Response - Page 1:
2025-07-13 13:30:45,168 - INFO - 第 1 页获取到 50 条记录
2025-07-13 13:30:45,684 - INFO - Request Parameters - Page 2:
2025-07-13 13:30:45,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:30:45,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:30:46,387 - INFO - Response - Page 2:
2025-07-13 13:30:46,387 - INFO - 第 2 页获取到 50 条记录
2025-07-13 13:30:46,903 - INFO - Request Parameters - Page 3:
2025-07-13 13:30:46,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:30:46,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:30:53,825 - INFO - Response - Page 3:
2025-07-13 13:30:53,825 - INFO - 第 3 页获取到 50 条记录
2025-07-13 13:30:54,340 - INFO - Request Parameters - Page 4:
2025-07-13 13:30:54,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:30:54,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:30:55,012 - INFO - Response - Page 4:
2025-07-13 13:30:55,012 - INFO - 第 4 页获取到 50 条记录
2025-07-13 13:30:55,512 - INFO - Request Parameters - Page 5:
2025-07-13 13:30:55,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:30:55,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:30:56,200 - INFO - Response - Page 5:
2025-07-13 13:30:56,200 - INFO - 第 5 页获取到 50 条记录
2025-07-13 13:30:56,700 - INFO - Request Parameters - Page 6:
2025-07-13 13:30:56,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:30:56,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:30:57,418 - INFO - Response - Page 6:
2025-07-13 13:30:57,418 - INFO - 第 6 页获取到 50 条记录
2025-07-13 13:30:57,934 - INFO - Request Parameters - Page 7:
2025-07-13 13:30:57,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:30:57,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:30:58,700 - INFO - Response - Page 7:
2025-07-13 13:30:58,700 - INFO - 第 7 页获取到 50 条记录
2025-07-13 13:30:59,200 - INFO - Request Parameters - Page 8:
2025-07-13 13:30:59,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:30:59,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:30:59,965 - INFO - Response - Page 8:
2025-07-13 13:30:59,965 - INFO - 第 8 页获取到 50 条记录
2025-07-13 13:31:00,465 - INFO - Request Parameters - Page 9:
2025-07-13 13:31:00,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:31:00,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:31:01,153 - INFO - Response - Page 9:
2025-07-13 13:31:01,153 - INFO - 第 9 页获取到 50 条记录
2025-07-13 13:31:01,668 - INFO - Request Parameters - Page 10:
2025-07-13 13:31:01,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:31:01,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:31:02,184 - INFO - Response - Page 10:
2025-07-13 13:31:02,184 - INFO - 第 10 页获取到 2 条记录
2025-07-13 13:31:02,699 - INFO - 查询完成，共获取到 452 条记录
2025-07-13 13:31:02,699 - INFO - 获取到 452 条表单数据
2025-07-13 13:31:02,699 - INFO - 当前日期 2025-07-11 有 3 条MySQL数据需要处理
2025-07-13 13:31:02,699 - INFO - 开始批量插入 3 条新记录
2025-07-13 13:31:02,856 - INFO - 批量插入响应状态码: 200
2025-07-13 13:31:02,856 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 05:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '159', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3A7462FB-44D4-7E95-B5BC-431AD848C607', 'x-acs-trace-id': 'aed4e856c3cf7cb613a0aa484f269610', 'etag': '1WWQONCRFHCiHJJh3w2HpRw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 13:31:02,856 - INFO - 批量插入响应体: {'result': ['FINST-7I866981FA2XMEVCCJY1E8U8COYD31KZK81DMWD1', 'FINST-7I866981FA2XMEVCCJY1E8U8COYD31KZK81DMXD1', 'FINST-7I866981FA2XMEVCCJY1E8U8COYD31KZK81DMYD1']}
2025-07-13 13:31:02,856 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-07-13 13:31:02,856 - INFO - 成功插入的数据ID: ['FINST-7I866981FA2XMEVCCJY1E8U8COYD31KZK81DMWD1', 'FINST-7I866981FA2XMEVCCJY1E8U8COYD31KZK81DMXD1', 'FINST-7I866981FA2XMEVCCJY1E8U8COYD31KZK81DMYD1']
2025-07-13 13:31:07,871 - INFO - 批量插入完成，共 3 条记录
2025-07-13 13:31:07,871 - INFO - 日期 2025-07-11 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-07-13 13:31:07,871 - INFO - 开始处理日期: 2025-07-12
2025-07-13 13:31:07,871 - INFO - Request Parameters - Page 1:
2025-07-13 13:31:07,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:31:07,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:31:08,590 - INFO - Response - Page 1:
2025-07-13 13:31:08,590 - INFO - 第 1 页获取到 50 条记录
2025-07-13 13:31:09,090 - INFO - Request Parameters - Page 2:
2025-07-13 13:31:09,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:31:09,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:31:09,762 - INFO - Response - Page 2:
2025-07-13 13:31:09,762 - INFO - 第 2 页获取到 50 条记录
2025-07-13 13:31:10,262 - INFO - Request Parameters - Page 3:
2025-07-13 13:31:10,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:31:10,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:31:10,981 - INFO - Response - Page 3:
2025-07-13 13:31:10,981 - INFO - 第 3 页获取到 50 条记录
2025-07-13 13:31:11,496 - INFO - Request Parameters - Page 4:
2025-07-13 13:31:11,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:31:11,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:31:12,199 - INFO - Response - Page 4:
2025-07-13 13:31:12,199 - INFO - 第 4 页获取到 50 条记录
2025-07-13 13:31:12,699 - INFO - Request Parameters - Page 5:
2025-07-13 13:31:12,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:31:12,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:31:13,481 - INFO - Response - Page 5:
2025-07-13 13:31:13,481 - INFO - 第 5 页获取到 50 条记录
2025-07-13 13:31:13,981 - INFO - Request Parameters - Page 6:
2025-07-13 13:31:13,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:31:13,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:31:14,621 - INFO - Response - Page 6:
2025-07-13 13:31:14,621 - INFO - 第 6 页获取到 50 条记录
2025-07-13 13:31:15,121 - INFO - Request Parameters - Page 7:
2025-07-13 13:31:15,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:31:15,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:31:15,809 - INFO - Response - Page 7:
2025-07-13 13:31:15,809 - INFO - 第 7 页获取到 50 条记录
2025-07-13 13:31:16,309 - INFO - Request Parameters - Page 8:
2025-07-13 13:31:16,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:31:16,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:31:16,949 - INFO - Response - Page 8:
2025-07-13 13:31:16,949 - INFO - 第 8 页获取到 28 条记录
2025-07-13 13:31:17,449 - INFO - 查询完成，共获取到 378 条记录
2025-07-13 13:31:17,449 - INFO - 获取到 378 条表单数据
2025-07-13 13:31:17,449 - INFO - 当前日期 2025-07-12 有 134 条MySQL数据需要处理
2025-07-13 13:31:17,449 - INFO - 开始批量插入 33 条新记录
2025-07-13 13:31:17,684 - INFO - 批量插入响应状态码: 200
2025-07-13 13:31:17,684 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 05:31:17 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1629', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5B840B70-2B1D-70F6-B2CC-87B07D0D924D', 'x-acs-trace-id': '8a7cade0e6917a99cd5536d01e97c3ed', 'etag': '1bZQdMu9jhEN+xJDFaLNxIg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 13:31:17,684 - INFO - 批量插入响应体: {'result': ['FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMX91', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMY91', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMZ91', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM0A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM1A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM2A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM3A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM4A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM5A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM6A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM7A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM8A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM9A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMAA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMBA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMCA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMDA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMEA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMFA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMGA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMHA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMIA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMJA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMKA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMLA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMMA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMNA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMOA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMPA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMQA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMRA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMSA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMTA1']}
2025-07-13 13:31:17,684 - INFO - 批量插入表单数据成功，批次 1，共 33 条记录
2025-07-13 13:31:17,684 - INFO - 成功插入的数据ID: ['FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMX91', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMY91', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMZ91', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM0A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM1A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM2A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM3A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM4A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM5A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM6A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM7A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM8A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DM9A1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMAA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMBA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMCA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMDA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMEA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMFA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMGA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMHA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMIA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMJA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMKA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMLA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMMA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMNA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMOA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMPA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMQA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMRA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMSA1', 'FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMTA1']
2025-07-13 13:31:22,699 - INFO - 批量插入完成，共 33 条记录
2025-07-13 13:31:22,699 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 33 条，错误: 0 条
2025-07-13 13:31:22,699 - INFO - 数据同步完成！更新: 0 条，插入: 36 条，错误: 1 条
2025-07-13 13:32:22,715 - INFO - 开始同步昨天与今天的销售数据: 2025-07-12 至 2025-07-13
2025-07-13 13:32:22,715 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-13 13:32:22,715 - INFO - 查询参数: ('2025-07-12', '2025-07-13')
2025-07-13 13:32:22,871 - INFO - MySQL查询成功，时间段: 2025-07-12 至 2025-07-13，共获取 474 条记录
2025-07-13 13:32:22,871 - INFO - 获取到 1 个日期需要处理: ['2025-07-12']
2025-07-13 13:32:22,871 - INFO - 开始处理日期: 2025-07-12
2025-07-13 13:32:22,871 - INFO - Request Parameters - Page 1:
2025-07-13 13:32:22,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:32:22,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:32:23,636 - INFO - Response - Page 1:
2025-07-13 13:32:23,636 - INFO - 第 1 页获取到 50 条记录
2025-07-13 13:32:24,136 - INFO - Request Parameters - Page 2:
2025-07-13 13:32:24,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:32:24,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:32:24,840 - INFO - Response - Page 2:
2025-07-13 13:32:24,840 - INFO - 第 2 页获取到 50 条记录
2025-07-13 13:32:25,355 - INFO - Request Parameters - Page 3:
2025-07-13 13:32:25,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:32:25,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:32:26,027 - INFO - Response - Page 3:
2025-07-13 13:32:26,027 - INFO - 第 3 页获取到 50 条记录
2025-07-13 13:32:26,543 - INFO - Request Parameters - Page 4:
2025-07-13 13:32:26,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:32:26,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:32:27,277 - INFO - Response - Page 4:
2025-07-13 13:32:27,277 - INFO - 第 4 页获取到 50 条记录
2025-07-13 13:32:27,793 - INFO - Request Parameters - Page 5:
2025-07-13 13:32:27,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:32:27,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:32:28,543 - INFO - Response - Page 5:
2025-07-13 13:32:28,543 - INFO - 第 5 页获取到 50 条记录
2025-07-13 13:32:29,043 - INFO - Request Parameters - Page 6:
2025-07-13 13:32:29,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:32:29,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:32:29,730 - INFO - Response - Page 6:
2025-07-13 13:32:29,730 - INFO - 第 6 页获取到 50 条记录
2025-07-13 13:32:30,246 - INFO - Request Parameters - Page 7:
2025-07-13 13:32:30,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:32:30,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:32:30,886 - INFO - Response - Page 7:
2025-07-13 13:32:30,886 - INFO - 第 7 页获取到 50 条记录
2025-07-13 13:32:31,386 - INFO - Request Parameters - Page 8:
2025-07-13 13:32:31,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:32:31,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:32:32,105 - INFO - Response - Page 8:
2025-07-13 13:32:32,105 - INFO - 第 8 页获取到 50 条记录
2025-07-13 13:32:32,621 - INFO - Request Parameters - Page 9:
2025-07-13 13:32:32,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 13:32:32,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 13:32:33,199 - INFO - Response - Page 9:
2025-07-13 13:32:33,199 - INFO - 第 9 页获取到 11 条记录
2025-07-13 13:32:33,714 - INFO - 查询完成，共获取到 411 条记录
2025-07-13 13:32:33,714 - INFO - 获取到 411 条表单数据
2025-07-13 13:32:33,714 - INFO - 当前日期 2025-07-12 有 458 条MySQL数据需要处理
2025-07-13 13:32:33,730 - INFO - 开始批量插入 47 条新记录
2025-07-13 13:32:33,996 - INFO - 批量插入响应状态码: 200
2025-07-13 13:32:33,996 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 05:32:33 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2315', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '35D43E61-FB17-70C3-88D9-5526FC005F96', 'x-acs-trace-id': 'f2e5c66cba6bb7d5d20edef231351236', 'etag': '2y9YUXO5nJ4DgplNN+a4f8w5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 13:32:33,996 - INFO - 批量插入响应体: {'result': ['FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMF51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMG51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMH51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMI51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMJ51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMK51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DML51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMM51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMN51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMO51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMP51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMQ51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMR51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMS51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMT51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMU51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMV51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMW51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMX51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMY51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMZ51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM061', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM161', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM261', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM361', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM461', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM561', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM661', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM761', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM861', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM961', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMA61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMB61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMC61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMD61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DME61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMF61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMG61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMH61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMI61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMJ61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMK61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DML61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMM61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMN61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMO61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMP61']}
2025-07-13 13:32:33,996 - INFO - 批量插入表单数据成功，批次 1，共 47 条记录
2025-07-13 13:32:33,996 - INFO - 成功插入的数据ID: ['FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMF51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMG51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMH51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMI51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMJ51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMK51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DML51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMM51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMN51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMO51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMP51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMQ51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMR51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMS51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMT51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMU51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMV51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMW51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMX51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMY51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMZ51', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM061', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM161', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM261', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM361', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM461', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM561', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM661', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM761', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM861', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DM961', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMA61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMB61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMC61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMD61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DME61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMF61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMG61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMH61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMI61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMJ61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMK61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DML61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMM61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMN61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMO61', 'FINST-90E66JD1P70XJA6O9136G9W967L62MVXM81DMP61']
2025-07-13 13:32:39,011 - INFO - 批量插入完成，共 47 条记录
2025-07-13 13:32:39,011 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 47 条，错误: 0 条
2025-07-13 13:32:39,011 - INFO - 数据同步完成！更新: 0 条，插入: 47 条，错误: 0 条
2025-07-13 13:32:39,011 - INFO - 同步完成
2025-07-13 16:30:33,674 - INFO - 使用默认增量同步（当天更新数据）
2025-07-13 16:30:33,674 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-13 16:30:33,674 - INFO - 查询参数: ('2025-07-13',)
2025-07-13 16:30:33,830 - INFO - MySQL查询成功，增量数据（日期: 2025-07-13），共获取 146 条记录
2025-07-13 16:30:33,830 - INFO - 获取到 6 个日期需要处理: ['2025-06-18', '2025-07-02', '2025-07-06', '2025-07-10', '2025-07-11', '2025-07-12']
2025-07-13 16:30:33,830 - INFO - 开始处理日期: 2025-06-18
2025-07-13 16:30:33,830 - INFO - Request Parameters - Page 1:
2025-07-13 16:30:33,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:30:33,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:30:41,940 - ERROR - 处理日期 2025-06-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C4CFD756-CB7B-74D3-8073-F43FBD7C4425 Response: {'code': 'ServiceUnavailable', 'requestid': 'C4CFD756-CB7B-74D3-8073-F43FBD7C4425', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C4CFD756-CB7B-74D3-8073-F43FBD7C4425)
2025-07-13 16:30:41,940 - INFO - 开始处理日期: 2025-07-02
2025-07-13 16:30:41,940 - INFO - Request Parameters - Page 1:
2025-07-13 16:30:41,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:30:41,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:30:50,049 - ERROR - 处理日期 2025-07-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FD7CC311-EA45-7CB8-B79B-4A791A79F263 Response: {'code': 'ServiceUnavailable', 'requestid': 'FD7CC311-EA45-7CB8-B79B-4A791A79F263', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FD7CC311-EA45-7CB8-B79B-4A791A79F263)
2025-07-13 16:30:50,049 - INFO - 开始处理日期: 2025-07-06
2025-07-13 16:30:50,049 - INFO - Request Parameters - Page 1:
2025-07-13 16:30:50,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:30:50,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:30:51,362 - INFO - Response - Page 1:
2025-07-13 16:30:51,362 - INFO - 第 1 页获取到 50 条记录
2025-07-13 16:30:51,877 - INFO - Request Parameters - Page 2:
2025-07-13 16:30:51,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:30:51,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:30:52,533 - INFO - Response - Page 2:
2025-07-13 16:30:52,533 - INFO - 第 2 页获取到 50 条记录
2025-07-13 16:30:53,049 - INFO - Request Parameters - Page 3:
2025-07-13 16:30:53,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:30:53,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:30:53,752 - INFO - Response - Page 3:
2025-07-13 16:30:53,752 - INFO - 第 3 页获取到 50 条记录
2025-07-13 16:30:54,252 - INFO - Request Parameters - Page 4:
2025-07-13 16:30:54,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:30:54,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:30:54,971 - INFO - Response - Page 4:
2025-07-13 16:30:54,971 - INFO - 第 4 页获取到 50 条记录
2025-07-13 16:30:55,487 - INFO - Request Parameters - Page 5:
2025-07-13 16:30:55,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:30:55,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:30:56,190 - INFO - Response - Page 5:
2025-07-13 16:30:56,190 - INFO - 第 5 页获取到 50 条记录
2025-07-13 16:30:56,705 - INFO - Request Parameters - Page 6:
2025-07-13 16:30:56,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:30:56,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:30:57,393 - INFO - Response - Page 6:
2025-07-13 16:30:57,393 - INFO - 第 6 页获取到 50 条记录
2025-07-13 16:30:57,908 - INFO - Request Parameters - Page 7:
2025-07-13 16:30:57,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:30:57,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:30:58,596 - INFO - Response - Page 7:
2025-07-13 16:30:58,596 - INFO - 第 7 页获取到 50 条记录
2025-07-13 16:30:59,096 - INFO - Request Parameters - Page 8:
2025-07-13 16:30:59,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:30:59,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:30:59,846 - INFO - Response - Page 8:
2025-07-13 16:30:59,846 - INFO - 第 8 页获取到 50 条记录
2025-07-13 16:31:00,362 - INFO - Request Parameters - Page 9:
2025-07-13 16:31:00,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:00,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:01,065 - INFO - Response - Page 9:
2025-07-13 16:31:01,065 - INFO - 第 9 页获取到 50 条记录
2025-07-13 16:31:01,580 - INFO - Request Parameters - Page 10:
2025-07-13 16:31:01,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:01,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:02,299 - INFO - Response - Page 10:
2025-07-13 16:31:02,299 - INFO - 第 10 页获取到 46 条记录
2025-07-13 16:31:02,815 - INFO - 查询完成，共获取到 496 条记录
2025-07-13 16:31:02,815 - INFO - 获取到 496 条表单数据
2025-07-13 16:31:02,815 - INFO - 当前日期 2025-07-06 有 1 条MySQL数据需要处理
2025-07-13 16:31:02,815 - INFO - 开始批量插入 1 条新记录
2025-07-13 16:31:02,971 - INFO - 批量插入响应状态码: 200
2025-07-13 16:31:02,971 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 08:31:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0A5D6E27-95FD-76AB-AB67-7AEC164BD8A8', 'x-acs-trace-id': '297b861d19f10a66da9f640001f14255', 'etag': '6EV+4aLpeLZvJE/dB8yZHZA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 16:31:02,971 - INFO - 批量插入响应体: {'result': ['FINST-OIF66BA1182XSIR18M7N54QHL6FC3B1H0F1DMLZ']}
2025-07-13 16:31:02,971 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-13 16:31:02,971 - INFO - 成功插入的数据ID: ['FINST-OIF66BA1182XSIR18M7N54QHL6FC3B1H0F1DMLZ']
2025-07-13 16:31:07,986 - INFO - 批量插入完成，共 1 条记录
2025-07-13 16:31:07,986 - INFO - 日期 2025-07-06 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-13 16:31:07,986 - INFO - 开始处理日期: 2025-07-10
2025-07-13 16:31:07,986 - INFO - Request Parameters - Page 1:
2025-07-13 16:31:07,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:07,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:08,674 - INFO - Response - Page 1:
2025-07-13 16:31:08,674 - INFO - 第 1 页获取到 50 条记录
2025-07-13 16:31:09,174 - INFO - Request Parameters - Page 2:
2025-07-13 16:31:09,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:09,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:09,908 - INFO - Response - Page 2:
2025-07-13 16:31:09,908 - INFO - 第 2 页获取到 50 条记录
2025-07-13 16:31:10,408 - INFO - Request Parameters - Page 3:
2025-07-13 16:31:10,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:10,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:11,111 - INFO - Response - Page 3:
2025-07-13 16:31:11,111 - INFO - 第 3 页获取到 50 条记录
2025-07-13 16:31:11,611 - INFO - Request Parameters - Page 4:
2025-07-13 16:31:11,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:11,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:12,299 - INFO - Response - Page 4:
2025-07-13 16:31:12,299 - INFO - 第 4 页获取到 50 条记录
2025-07-13 16:31:12,799 - INFO - Request Parameters - Page 5:
2025-07-13 16:31:12,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:12,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:13,549 - INFO - Response - Page 5:
2025-07-13 16:31:13,549 - INFO - 第 5 页获取到 50 条记录
2025-07-13 16:31:14,065 - INFO - Request Parameters - Page 6:
2025-07-13 16:31:14,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:14,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:14,783 - INFO - Response - Page 6:
2025-07-13 16:31:14,783 - INFO - 第 6 页获取到 50 条记录
2025-07-13 16:31:15,283 - INFO - Request Parameters - Page 7:
2025-07-13 16:31:15,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:15,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:16,033 - INFO - Response - Page 7:
2025-07-13 16:31:16,033 - INFO - 第 7 页获取到 50 条记录
2025-07-13 16:31:16,549 - INFO - Request Parameters - Page 8:
2025-07-13 16:31:16,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:16,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:17,252 - INFO - Response - Page 8:
2025-07-13 16:31:17,252 - INFO - 第 8 页获取到 50 条记录
2025-07-13 16:31:17,768 - INFO - Request Parameters - Page 9:
2025-07-13 16:31:17,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:17,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:18,658 - INFO - Response - Page 9:
2025-07-13 16:31:18,658 - INFO - 第 9 页获取到 50 条记录
2025-07-13 16:31:19,174 - INFO - Request Parameters - Page 10:
2025-07-13 16:31:19,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:19,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:19,799 - INFO - Response - Page 10:
2025-07-13 16:31:19,799 - INFO - 第 10 页获取到 50 条记录
2025-07-13 16:31:20,299 - INFO - Request Parameters - Page 11:
2025-07-13 16:31:20,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:20,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:20,971 - INFO - Response - Page 11:
2025-07-13 16:31:20,971 - INFO - 第 11 页获取到 40 条记录
2025-07-13 16:31:21,486 - INFO - 查询完成，共获取到 540 条记录
2025-07-13 16:31:21,486 - INFO - 获取到 540 条表单数据
2025-07-13 16:31:21,486 - INFO - 当前日期 2025-07-10 有 1 条MySQL数据需要处理
2025-07-13 16:31:21,486 - INFO - 开始批量插入 1 条新记录
2025-07-13 16:31:21,627 - INFO - 批量插入响应状态码: 200
2025-07-13 16:31:21,627 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 08:31:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5ADB5428-D4DB-7315-B13E-275789B3093B', 'x-acs-trace-id': '145f118f5cb385ce6729da7608ea90f9', 'etag': '6nRhW7jV0ylR6rqW0eT3JFQ1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 16:31:21,627 - INFO - 批量插入响应体: {'result': ['FINST-2LC66IA1DF1XVUZ5C67MTBLH87722TFV0F1DMCS1']}
2025-07-13 16:31:21,627 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-13 16:31:21,627 - INFO - 成功插入的数据ID: ['FINST-2LC66IA1DF1XVUZ5C67MTBLH87722TFV0F1DMCS1']
2025-07-13 16:31:26,643 - INFO - 批量插入完成，共 1 条记录
2025-07-13 16:31:26,643 - INFO - 日期 2025-07-10 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-13 16:31:26,643 - INFO - 开始处理日期: 2025-07-11
2025-07-13 16:31:26,643 - INFO - Request Parameters - Page 1:
2025-07-13 16:31:26,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:26,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:27,361 - INFO - Response - Page 1:
2025-07-13 16:31:27,361 - INFO - 第 1 页获取到 50 条记录
2025-07-13 16:31:27,877 - INFO - Request Parameters - Page 2:
2025-07-13 16:31:27,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:27,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:28,580 - INFO - Response - Page 2:
2025-07-13 16:31:28,580 - INFO - 第 2 页获取到 50 条记录
2025-07-13 16:31:29,096 - INFO - Request Parameters - Page 3:
2025-07-13 16:31:29,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:29,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:29,893 - INFO - Response - Page 3:
2025-07-13 16:31:29,893 - INFO - 第 3 页获取到 50 条记录
2025-07-13 16:31:30,393 - INFO - Request Parameters - Page 4:
2025-07-13 16:31:30,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:30,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:31,221 - INFO - Response - Page 4:
2025-07-13 16:31:31,221 - INFO - 第 4 页获取到 50 条记录
2025-07-13 16:31:31,721 - INFO - Request Parameters - Page 5:
2025-07-13 16:31:31,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:31,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:32,439 - INFO - Response - Page 5:
2025-07-13 16:31:32,439 - INFO - 第 5 页获取到 50 条记录
2025-07-13 16:31:32,939 - INFO - Request Parameters - Page 6:
2025-07-13 16:31:32,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:32,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:33,643 - INFO - Response - Page 6:
2025-07-13 16:31:33,643 - INFO - 第 6 页获取到 50 条记录
2025-07-13 16:31:34,143 - INFO - Request Parameters - Page 7:
2025-07-13 16:31:34,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:34,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:34,814 - INFO - Response - Page 7:
2025-07-13 16:31:34,814 - INFO - 第 7 页获取到 50 条记录
2025-07-13 16:31:35,314 - INFO - Request Parameters - Page 8:
2025-07-13 16:31:35,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:35,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:36,049 - INFO - Response - Page 8:
2025-07-13 16:31:36,049 - INFO - 第 8 页获取到 50 条记录
2025-07-13 16:31:36,549 - INFO - Request Parameters - Page 9:
2025-07-13 16:31:36,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:36,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:37,283 - INFO - Response - Page 9:
2025-07-13 16:31:37,283 - INFO - 第 9 页获取到 50 条记录
2025-07-13 16:31:37,799 - INFO - Request Parameters - Page 10:
2025-07-13 16:31:37,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:37,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:38,314 - INFO - Response - Page 10:
2025-07-13 16:31:38,314 - INFO - 第 10 页获取到 5 条记录
2025-07-13 16:31:38,814 - INFO - 查询完成，共获取到 455 条记录
2025-07-13 16:31:38,814 - INFO - 获取到 455 条表单数据
2025-07-13 16:31:38,814 - INFO - 当前日期 2025-07-11 有 3 条MySQL数据需要处理
2025-07-13 16:31:38,814 - INFO - 日期 2025-07-11 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 16:31:38,814 - INFO - 开始处理日期: 2025-07-12
2025-07-13 16:31:38,814 - INFO - Request Parameters - Page 1:
2025-07-13 16:31:38,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:38,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:39,518 - INFO - Response - Page 1:
2025-07-13 16:31:39,518 - INFO - 第 1 页获取到 50 条记录
2025-07-13 16:31:40,033 - INFO - Request Parameters - Page 2:
2025-07-13 16:31:40,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:40,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:40,689 - INFO - Response - Page 2:
2025-07-13 16:31:40,689 - INFO - 第 2 页获取到 50 条记录
2025-07-13 16:31:41,189 - INFO - Request Parameters - Page 3:
2025-07-13 16:31:41,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:41,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:41,861 - INFO - Response - Page 3:
2025-07-13 16:31:41,861 - INFO - 第 3 页获取到 50 条记录
2025-07-13 16:31:42,361 - INFO - Request Parameters - Page 4:
2025-07-13 16:31:42,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:42,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:43,049 - INFO - Response - Page 4:
2025-07-13 16:31:43,049 - INFO - 第 4 页获取到 50 条记录
2025-07-13 16:31:43,549 - INFO - Request Parameters - Page 5:
2025-07-13 16:31:43,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:43,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:44,205 - INFO - Response - Page 5:
2025-07-13 16:31:44,205 - INFO - 第 5 页获取到 50 条记录
2025-07-13 16:31:44,705 - INFO - Request Parameters - Page 6:
2025-07-13 16:31:44,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:44,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:45,455 - INFO - Response - Page 6:
2025-07-13 16:31:45,455 - INFO - 第 6 页获取到 50 条记录
2025-07-13 16:31:45,955 - INFO - Request Parameters - Page 7:
2025-07-13 16:31:45,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:45,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:46,658 - INFO - Response - Page 7:
2025-07-13 16:31:46,658 - INFO - 第 7 页获取到 50 条记录
2025-07-13 16:31:47,174 - INFO - Request Parameters - Page 8:
2025-07-13 16:31:47,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:47,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:47,971 - INFO - Response - Page 8:
2025-07-13 16:31:47,971 - INFO - 第 8 页获取到 50 条记录
2025-07-13 16:31:48,486 - INFO - Request Parameters - Page 9:
2025-07-13 16:31:48,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:48,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:49,252 - INFO - Response - Page 9:
2025-07-13 16:31:49,252 - INFO - 第 9 页获取到 50 条记录
2025-07-13 16:31:49,767 - INFO - Request Parameters - Page 10:
2025-07-13 16:31:49,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:31:49,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:31:50,330 - INFO - Response - Page 10:
2025-07-13 16:31:50,330 - INFO - 第 10 页获取到 8 条记录
2025-07-13 16:31:50,846 - INFO - 查询完成，共获取到 458 条记录
2025-07-13 16:31:50,846 - INFO - 获取到 458 条表单数据
2025-07-13 16:31:50,846 - INFO - 当前日期 2025-07-12 有 135 条MySQL数据需要处理
2025-07-13 16:31:50,846 - INFO - 开始批量插入 1 条新记录
2025-07-13 16:31:51,002 - INFO - 批量插入响应状态码: 200
2025-07-13 16:31:51,002 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 08:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '313006B2-4875-7578-8231-C5695BDDFA18', 'x-acs-trace-id': 'cd10faf0fe1459891d469c03490dec4e', 'etag': '68MgTv1FDD4mriWI5DFZqqg1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 16:31:51,002 - INFO - 批量插入响应体: {'result': ['FINST-AI866781SZ2XZV677MSNZC4F0L562I3I1F1DML51']}
2025-07-13 16:31:51,002 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-13 16:31:51,002 - INFO - 成功插入的数据ID: ['FINST-AI866781SZ2XZV677MSNZC4F0L562I3I1F1DML51']
2025-07-13 16:31:56,017 - INFO - 批量插入完成，共 1 条记录
2025-07-13 16:31:56,017 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-13 16:31:56,017 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 2 条
2025-07-13 16:32:56,033 - INFO - 开始同步昨天与今天的销售数据: 2025-07-12 至 2025-07-13
2025-07-13 16:32:56,033 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-13 16:32:56,033 - INFO - 查询参数: ('2025-07-12', '2025-07-13')
2025-07-13 16:32:56,189 - INFO - MySQL查询成功，时间段: 2025-07-12 至 2025-07-13，共获取 475 条记录
2025-07-13 16:32:56,189 - INFO - 获取到 1 个日期需要处理: ['2025-07-12']
2025-07-13 16:32:56,189 - INFO - 开始处理日期: 2025-07-12
2025-07-13 16:32:56,189 - INFO - Request Parameters - Page 1:
2025-07-13 16:32:56,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:32:56,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:32:56,923 - INFO - Response - Page 1:
2025-07-13 16:32:56,923 - INFO - 第 1 页获取到 50 条记录
2025-07-13 16:32:57,439 - INFO - Request Parameters - Page 2:
2025-07-13 16:32:57,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:32:57,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:32:58,126 - INFO - Response - Page 2:
2025-07-13 16:32:58,126 - INFO - 第 2 页获取到 50 条记录
2025-07-13 16:32:58,626 - INFO - Request Parameters - Page 3:
2025-07-13 16:32:58,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:32:58,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:32:59,345 - INFO - Response - Page 3:
2025-07-13 16:32:59,345 - INFO - 第 3 页获取到 50 条记录
2025-07-13 16:32:59,861 - INFO - Request Parameters - Page 4:
2025-07-13 16:32:59,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:32:59,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:33:00,579 - INFO - Response - Page 4:
2025-07-13 16:33:00,579 - INFO - 第 4 页获取到 50 条记录
2025-07-13 16:33:01,095 - INFO - Request Parameters - Page 5:
2025-07-13 16:33:01,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:33:01,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:33:01,798 - INFO - Response - Page 5:
2025-07-13 16:33:01,798 - INFO - 第 5 页获取到 50 条记录
2025-07-13 16:33:02,298 - INFO - Request Parameters - Page 6:
2025-07-13 16:33:02,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:33:02,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:33:03,048 - INFO - Response - Page 6:
2025-07-13 16:33:03,048 - INFO - 第 6 页获取到 50 条记录
2025-07-13 16:33:03,564 - INFO - Request Parameters - Page 7:
2025-07-13 16:33:03,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:33:03,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:33:04,251 - INFO - Response - Page 7:
2025-07-13 16:33:04,251 - INFO - 第 7 页获取到 50 条记录
2025-07-13 16:33:04,767 - INFO - Request Parameters - Page 8:
2025-07-13 16:33:04,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:33:04,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:33:05,564 - INFO - Response - Page 8:
2025-07-13 16:33:05,564 - INFO - 第 8 页获取到 50 条记录
2025-07-13 16:33:06,079 - INFO - Request Parameters - Page 9:
2025-07-13 16:33:06,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:33:06,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:33:06,736 - INFO - Response - Page 9:
2025-07-13 16:33:06,736 - INFO - 第 9 页获取到 50 条记录
2025-07-13 16:33:07,236 - INFO - Request Parameters - Page 10:
2025-07-13 16:33:07,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 16:33:07,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 16:33:07,783 - INFO - Response - Page 10:
2025-07-13 16:33:07,783 - INFO - 第 10 页获取到 9 条记录
2025-07-13 16:33:08,283 - INFO - 查询完成，共获取到 459 条记录
2025-07-13 16:33:08,283 - INFO - 获取到 459 条表单数据
2025-07-13 16:33:08,283 - INFO - 当前日期 2025-07-12 有 459 条MySQL数据需要处理
2025-07-13 16:33:08,298 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 16:33:08,298 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 16:33:08,298 - INFO - 同步完成
2025-07-13 19:30:34,571 - INFO - 使用默认增量同步（当天更新数据）
2025-07-13 19:30:34,571 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-13 19:30:34,571 - INFO - 查询参数: ('2025-07-13',)
2025-07-13 19:30:34,728 - INFO - MySQL查询成功，增量数据（日期: 2025-07-13），共获取 146 条记录
2025-07-13 19:30:34,728 - INFO - 获取到 6 个日期需要处理: ['2025-06-18', '2025-07-02', '2025-07-06', '2025-07-10', '2025-07-11', '2025-07-12']
2025-07-13 19:30:34,728 - INFO - 开始处理日期: 2025-06-18
2025-07-13 19:30:34,743 - INFO - Request Parameters - Page 1:
2025-07-13 19:30:34,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:30:34,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:30:42,872 - ERROR - 处理日期 2025-06-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FE5FF8A5-6374-7424-8C5F-1228DF1A46D7 Response: {'code': 'ServiceUnavailable', 'requestid': 'FE5FF8A5-6374-7424-8C5F-1228DF1A46D7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FE5FF8A5-6374-7424-8C5F-1228DF1A46D7)
2025-07-13 19:30:42,872 - INFO - 开始处理日期: 2025-07-02
2025-07-13 19:30:42,872 - INFO - Request Parameters - Page 1:
2025-07-13 19:30:42,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:30:42,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:30:51,000 - ERROR - 处理日期 2025-07-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E29D2E91-6A57-73D1-8CD5-E0B0F7639BA1 Response: {'code': 'ServiceUnavailable', 'requestid': 'E29D2E91-6A57-73D1-8CD5-E0B0F7639BA1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E29D2E91-6A57-73D1-8CD5-E0B0F7639BA1)
2025-07-13 19:30:51,000 - INFO - 开始处理日期: 2025-07-06
2025-07-13 19:30:51,000 - INFO - Request Parameters - Page 1:
2025-07-13 19:30:51,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:30:51,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:30:51,828 - INFO - Response - Page 1:
2025-07-13 19:30:51,828 - INFO - 第 1 页获取到 50 条记录
2025-07-13 19:30:52,329 - INFO - Request Parameters - Page 2:
2025-07-13 19:30:52,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:30:52,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:30:52,985 - INFO - Response - Page 2:
2025-07-13 19:30:52,985 - INFO - 第 2 页获取到 50 条记录
2025-07-13 19:30:53,501 - INFO - Request Parameters - Page 3:
2025-07-13 19:30:53,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:30:53,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:30:54,157 - INFO - Response - Page 3:
2025-07-13 19:30:54,157 - INFO - 第 3 页获取到 50 条记录
2025-07-13 19:30:54,658 - INFO - Request Parameters - Page 4:
2025-07-13 19:30:54,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:30:54,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:30:55,377 - INFO - Response - Page 4:
2025-07-13 19:30:55,377 - INFO - 第 4 页获取到 50 条记录
2025-07-13 19:30:55,893 - INFO - Request Parameters - Page 5:
2025-07-13 19:30:55,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:30:55,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:30:56,612 - INFO - Response - Page 5:
2025-07-13 19:30:56,612 - INFO - 第 5 页获取到 50 条记录
2025-07-13 19:30:57,143 - INFO - Request Parameters - Page 6:
2025-07-13 19:30:57,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:30:57,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:30:57,815 - INFO - Response - Page 6:
2025-07-13 19:30:57,815 - INFO - 第 6 页获取到 50 条记录
2025-07-13 19:30:58,331 - INFO - Request Parameters - Page 7:
2025-07-13 19:30:58,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:30:58,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:30:59,097 - INFO - Response - Page 7:
2025-07-13 19:30:59,097 - INFO - 第 7 页获取到 50 条记录
2025-07-13 19:30:59,597 - INFO - Request Parameters - Page 8:
2025-07-13 19:30:59,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:30:59,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:00,238 - INFO - Response - Page 8:
2025-07-13 19:31:00,238 - INFO - 第 8 页获取到 50 条记录
2025-07-13 19:31:00,754 - INFO - Request Parameters - Page 9:
2025-07-13 19:31:00,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:00,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:01,457 - INFO - Response - Page 9:
2025-07-13 19:31:01,457 - INFO - 第 9 页获取到 50 条记录
2025-07-13 19:31:01,973 - INFO - Request Parameters - Page 10:
2025-07-13 19:31:01,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:01,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:02,692 - INFO - Response - Page 10:
2025-07-13 19:31:02,692 - INFO - 第 10 页获取到 47 条记录
2025-07-13 19:31:03,208 - INFO - 查询完成，共获取到 497 条记录
2025-07-13 19:31:03,208 - INFO - 获取到 497 条表单数据
2025-07-13 19:31:03,208 - INFO - 当前日期 2025-07-06 有 1 条MySQL数据需要处理
2025-07-13 19:31:03,208 - INFO - 日期 2025-07-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 19:31:03,208 - INFO - 开始处理日期: 2025-07-10
2025-07-13 19:31:03,208 - INFO - Request Parameters - Page 1:
2025-07-13 19:31:03,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:03,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:03,896 - INFO - Response - Page 1:
2025-07-13 19:31:03,896 - INFO - 第 1 页获取到 50 条记录
2025-07-13 19:31:04,396 - INFO - Request Parameters - Page 2:
2025-07-13 19:31:04,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:04,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:05,068 - INFO - Response - Page 2:
2025-07-13 19:31:05,068 - INFO - 第 2 页获取到 50 条记录
2025-07-13 19:31:05,584 - INFO - Request Parameters - Page 3:
2025-07-13 19:31:05,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:05,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:06,287 - INFO - Response - Page 3:
2025-07-13 19:31:06,287 - INFO - 第 3 页获取到 50 条记录
2025-07-13 19:31:06,803 - INFO - Request Parameters - Page 4:
2025-07-13 19:31:06,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:06,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:07,507 - INFO - Response - Page 4:
2025-07-13 19:31:07,507 - INFO - 第 4 页获取到 50 条记录
2025-07-13 19:31:08,022 - INFO - Request Parameters - Page 5:
2025-07-13 19:31:08,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:08,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:08,695 - INFO - Response - Page 5:
2025-07-13 19:31:08,695 - INFO - 第 5 页获取到 50 条记录
2025-07-13 19:31:09,195 - INFO - Request Parameters - Page 6:
2025-07-13 19:31:09,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:09,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:09,851 - INFO - Response - Page 6:
2025-07-13 19:31:09,851 - INFO - 第 6 页获取到 50 条记录
2025-07-13 19:31:10,351 - INFO - Request Parameters - Page 7:
2025-07-13 19:31:10,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:10,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:11,070 - INFO - Response - Page 7:
2025-07-13 19:31:11,070 - INFO - 第 7 页获取到 50 条记录
2025-07-13 19:31:11,586 - INFO - Request Parameters - Page 8:
2025-07-13 19:31:11,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:11,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:12,290 - INFO - Response - Page 8:
2025-07-13 19:31:12,290 - INFO - 第 8 页获取到 50 条记录
2025-07-13 19:31:12,790 - INFO - Request Parameters - Page 9:
2025-07-13 19:31:12,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:12,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:13,462 - INFO - Response - Page 9:
2025-07-13 19:31:13,462 - INFO - 第 9 页获取到 50 条记录
2025-07-13 19:31:13,978 - INFO - Request Parameters - Page 10:
2025-07-13 19:31:13,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:13,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:14,666 - INFO - Response - Page 10:
2025-07-13 19:31:14,666 - INFO - 第 10 页获取到 50 条记录
2025-07-13 19:31:15,181 - INFO - Request Parameters - Page 11:
2025-07-13 19:31:15,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:15,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:15,838 - INFO - Response - Page 11:
2025-07-13 19:31:15,838 - INFO - 第 11 页获取到 41 条记录
2025-07-13 19:31:16,354 - INFO - 查询完成，共获取到 541 条记录
2025-07-13 19:31:16,354 - INFO - 获取到 541 条表单数据
2025-07-13 19:31:16,354 - INFO - 当前日期 2025-07-10 有 1 条MySQL数据需要处理
2025-07-13 19:31:16,354 - INFO - 日期 2025-07-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 19:31:16,354 - INFO - 开始处理日期: 2025-07-11
2025-07-13 19:31:16,354 - INFO - Request Parameters - Page 1:
2025-07-13 19:31:16,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:16,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:17,042 - INFO - Response - Page 1:
2025-07-13 19:31:17,042 - INFO - 第 1 页获取到 50 条记录
2025-07-13 19:31:17,557 - INFO - Request Parameters - Page 2:
2025-07-13 19:31:17,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:17,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:18,245 - INFO - Response - Page 2:
2025-07-13 19:31:18,245 - INFO - 第 2 页获取到 50 条记录
2025-07-13 19:31:18,761 - INFO - Request Parameters - Page 3:
2025-07-13 19:31:18,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:18,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:19,402 - INFO - Response - Page 3:
2025-07-13 19:31:19,402 - INFO - 第 3 页获取到 50 条记录
2025-07-13 19:31:19,902 - INFO - Request Parameters - Page 4:
2025-07-13 19:31:19,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:19,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:20,590 - INFO - Response - Page 4:
2025-07-13 19:31:20,605 - INFO - 第 4 页获取到 50 条记录
2025-07-13 19:31:21,106 - INFO - Request Parameters - Page 5:
2025-07-13 19:31:21,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:21,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:21,825 - INFO - Response - Page 5:
2025-07-13 19:31:21,825 - INFO - 第 5 页获取到 50 条记录
2025-07-13 19:31:22,341 - INFO - Request Parameters - Page 6:
2025-07-13 19:31:22,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:22,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:23,091 - INFO - Response - Page 6:
2025-07-13 19:31:23,091 - INFO - 第 6 页获取到 50 条记录
2025-07-13 19:31:23,607 - INFO - Request Parameters - Page 7:
2025-07-13 19:31:23,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:23,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:24,373 - INFO - Response - Page 7:
2025-07-13 19:31:24,373 - INFO - 第 7 页获取到 50 条记录
2025-07-13 19:31:24,888 - INFO - Request Parameters - Page 8:
2025-07-13 19:31:24,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:24,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:25,561 - INFO - Response - Page 8:
2025-07-13 19:31:25,561 - INFO - 第 8 页获取到 50 条记录
2025-07-13 19:31:26,061 - INFO - Request Parameters - Page 9:
2025-07-13 19:31:26,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:26,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:26,749 - INFO - Response - Page 9:
2025-07-13 19:31:26,749 - INFO - 第 9 页获取到 50 条记录
2025-07-13 19:31:27,249 - INFO - Request Parameters - Page 10:
2025-07-13 19:31:27,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:27,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:27,780 - INFO - Response - Page 10:
2025-07-13 19:31:27,780 - INFO - 第 10 页获取到 5 条记录
2025-07-13 19:31:28,312 - INFO - 查询完成，共获取到 455 条记录
2025-07-13 19:31:28,312 - INFO - 获取到 455 条表单数据
2025-07-13 19:31:28,312 - INFO - 当前日期 2025-07-11 有 3 条MySQL数据需要处理
2025-07-13 19:31:28,312 - INFO - 日期 2025-07-11 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 19:31:28,312 - INFO - 开始处理日期: 2025-07-12
2025-07-13 19:31:28,312 - INFO - Request Parameters - Page 1:
2025-07-13 19:31:28,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:28,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:28,999 - INFO - Response - Page 1:
2025-07-13 19:31:28,999 - INFO - 第 1 页获取到 50 条记录
2025-07-13 19:31:29,500 - INFO - Request Parameters - Page 2:
2025-07-13 19:31:29,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:29,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:30,172 - INFO - Response - Page 2:
2025-07-13 19:31:30,172 - INFO - 第 2 页获取到 50 条记录
2025-07-13 19:31:30,672 - INFO - Request Parameters - Page 3:
2025-07-13 19:31:30,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:30,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:31,375 - INFO - Response - Page 3:
2025-07-13 19:31:31,375 - INFO - 第 3 页获取到 50 条记录
2025-07-13 19:31:31,876 - INFO - Request Parameters - Page 4:
2025-07-13 19:31:31,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:31,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:32,563 - INFO - Response - Page 4:
2025-07-13 19:31:32,563 - INFO - 第 4 页获取到 50 条记录
2025-07-13 19:31:33,079 - INFO - Request Parameters - Page 5:
2025-07-13 19:31:33,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:33,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:33,798 - INFO - Response - Page 5:
2025-07-13 19:31:33,798 - INFO - 第 5 页获取到 50 条记录
2025-07-13 19:31:34,314 - INFO - Request Parameters - Page 6:
2025-07-13 19:31:34,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:34,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:35,033 - INFO - Response - Page 6:
2025-07-13 19:31:35,033 - INFO - 第 6 页获取到 50 条记录
2025-07-13 19:31:35,533 - INFO - Request Parameters - Page 7:
2025-07-13 19:31:35,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:35,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:36,346 - INFO - Response - Page 7:
2025-07-13 19:31:36,346 - INFO - 第 7 页获取到 50 条记录
2025-07-13 19:31:36,846 - INFO - Request Parameters - Page 8:
2025-07-13 19:31:36,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:36,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:37,518 - INFO - Response - Page 8:
2025-07-13 19:31:37,518 - INFO - 第 8 页获取到 50 条记录
2025-07-13 19:31:38,034 - INFO - Request Parameters - Page 9:
2025-07-13 19:31:38,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:38,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:38,691 - INFO - Response - Page 9:
2025-07-13 19:31:38,691 - INFO - 第 9 页获取到 50 条记录
2025-07-13 19:31:39,191 - INFO - Request Parameters - Page 10:
2025-07-13 19:31:39,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:31:39,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:31:39,691 - INFO - Response - Page 10:
2025-07-13 19:31:39,691 - INFO - 第 10 页获取到 9 条记录
2025-07-13 19:31:40,207 - INFO - 查询完成，共获取到 459 条记录
2025-07-13 19:31:40,207 - INFO - 获取到 459 条表单数据
2025-07-13 19:31:40,207 - INFO - 当前日期 2025-07-12 有 135 条MySQL数据需要处理
2025-07-13 19:31:40,207 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 19:31:40,207 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-07-13 19:32:40,247 - INFO - 开始同步昨天与今天的销售数据: 2025-07-12 至 2025-07-13
2025-07-13 19:32:40,247 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-13 19:32:40,247 - INFO - 查询参数: ('2025-07-12', '2025-07-13')
2025-07-13 19:32:40,403 - INFO - MySQL查询成功，时间段: 2025-07-12 至 2025-07-13，共获取 475 条记录
2025-07-13 19:32:40,403 - INFO - 获取到 1 个日期需要处理: ['2025-07-12']
2025-07-13 19:32:40,403 - INFO - 开始处理日期: 2025-07-12
2025-07-13 19:32:40,403 - INFO - Request Parameters - Page 1:
2025-07-13 19:32:40,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:32:40,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:32:41,169 - INFO - Response - Page 1:
2025-07-13 19:32:41,169 - INFO - 第 1 页获取到 50 条记录
2025-07-13 19:32:41,685 - INFO - Request Parameters - Page 2:
2025-07-13 19:32:41,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:32:41,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:32:42,372 - INFO - Response - Page 2:
2025-07-13 19:32:42,372 - INFO - 第 2 页获取到 50 条记录
2025-07-13 19:32:42,888 - INFO - Request Parameters - Page 3:
2025-07-13 19:32:42,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:32:42,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:32:43,529 - INFO - Response - Page 3:
2025-07-13 19:32:43,529 - INFO - 第 3 页获取到 50 条记录
2025-07-13 19:32:44,029 - INFO - Request Parameters - Page 4:
2025-07-13 19:32:44,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:32:44,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:32:44,701 - INFO - Response - Page 4:
2025-07-13 19:32:44,701 - INFO - 第 4 页获取到 50 条记录
2025-07-13 19:32:45,202 - INFO - Request Parameters - Page 5:
2025-07-13 19:32:45,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:32:45,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:32:45,921 - INFO - Response - Page 5:
2025-07-13 19:32:45,921 - INFO - 第 5 页获取到 50 条记录
2025-07-13 19:32:46,437 - INFO - Request Parameters - Page 6:
2025-07-13 19:32:46,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:32:46,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:32:47,109 - INFO - Response - Page 6:
2025-07-13 19:32:47,109 - INFO - 第 6 页获取到 50 条记录
2025-07-13 19:32:47,625 - INFO - Request Parameters - Page 7:
2025-07-13 19:32:47,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:32:47,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:32:48,328 - INFO - Response - Page 7:
2025-07-13 19:32:48,328 - INFO - 第 7 页获取到 50 条记录
2025-07-13 19:32:48,828 - INFO - Request Parameters - Page 8:
2025-07-13 19:32:48,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:32:48,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:32:49,485 - INFO - Response - Page 8:
2025-07-13 19:32:49,485 - INFO - 第 8 页获取到 50 条记录
2025-07-13 19:32:49,985 - INFO - Request Parameters - Page 9:
2025-07-13 19:32:49,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:32:49,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:32:50,704 - INFO - Response - Page 9:
2025-07-13 19:32:50,704 - INFO - 第 9 页获取到 50 条记录
2025-07-13 19:32:51,204 - INFO - Request Parameters - Page 10:
2025-07-13 19:32:51,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 19:32:51,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 19:32:51,720 - INFO - Response - Page 10:
2025-07-13 19:32:51,720 - INFO - 第 10 页获取到 9 条记录
2025-07-13 19:32:52,220 - INFO - 查询完成，共获取到 459 条记录
2025-07-13 19:32:52,220 - INFO - 获取到 459 条表单数据
2025-07-13 19:32:52,220 - INFO - 当前日期 2025-07-12 有 459 条MySQL数据需要处理
2025-07-13 19:32:52,236 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 19:32:52,236 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 19:32:52,236 - INFO - 同步完成
2025-07-13 22:30:33,994 - INFO - 使用默认增量同步（当天更新数据）
2025-07-13 22:30:34,009 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-13 22:30:34,009 - INFO - 查询参数: ('2025-07-13',)
2025-07-13 22:30:34,150 - INFO - MySQL查询成功，增量数据（日期: 2025-07-13），共获取 149 条记录
2025-07-13 22:30:34,150 - INFO - 获取到 7 个日期需要处理: ['2025-06-18', '2025-07-02', '2025-07-06', '2025-07-10', '2025-07-11', '2025-07-12', '2025-07-13']
2025-07-13 22:30:34,166 - INFO - 开始处理日期: 2025-06-18
2025-07-13 22:30:34,166 - INFO - Request Parameters - Page 1:
2025-07-13 22:30:34,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:30:34,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750176000000, 1750262399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:30:42,262 - ERROR - 处理日期 2025-06-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0043C31B-5C18-7A79-AD50-70628E7EE2D4 Response: {'code': 'ServiceUnavailable', 'requestid': '0043C31B-5C18-7A79-AD50-70628E7EE2D4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0043C31B-5C18-7A79-AD50-70628E7EE2D4)
2025-07-13 22:30:42,262 - INFO - 开始处理日期: 2025-07-02
2025-07-13 22:30:42,262 - INFO - Request Parameters - Page 1:
2025-07-13 22:30:42,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:30:42,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:30:49,781 - INFO - Response - Page 1:
2025-07-13 22:30:49,781 - INFO - 第 1 页获取到 50 条记录
2025-07-13 22:30:50,297 - INFO - Request Parameters - Page 2:
2025-07-13 22:30:50,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:30:50,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:30:51,032 - INFO - Response - Page 2:
2025-07-13 22:30:51,032 - INFO - 第 2 页获取到 50 条记录
2025-07-13 22:30:51,532 - INFO - Request Parameters - Page 3:
2025-07-13 22:30:51,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:30:51,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:30:52,204 - INFO - Response - Page 3:
2025-07-13 22:30:52,204 - INFO - 第 3 页获取到 50 条记录
2025-07-13 22:30:52,704 - INFO - Request Parameters - Page 4:
2025-07-13 22:30:52,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:30:52,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:30:53,376 - INFO - Response - Page 4:
2025-07-13 22:30:53,392 - INFO - 第 4 页获取到 50 条记录
2025-07-13 22:30:53,892 - INFO - Request Parameters - Page 5:
2025-07-13 22:30:53,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:30:53,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:30:54,517 - INFO - Response - Page 5:
2025-07-13 22:30:54,517 - INFO - 第 5 页获取到 50 条记录
2025-07-13 22:30:55,033 - INFO - Request Parameters - Page 6:
2025-07-13 22:30:55,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:30:55,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:30:55,752 - INFO - Response - Page 6:
2025-07-13 22:30:55,768 - INFO - 第 6 页获取到 50 条记录
2025-07-13 22:30:56,284 - INFO - Request Parameters - Page 7:
2025-07-13 22:30:56,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:30:56,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:30:56,956 - INFO - Response - Page 7:
2025-07-13 22:30:56,956 - INFO - 第 7 页获取到 50 条记录
2025-07-13 22:30:57,472 - INFO - Request Parameters - Page 8:
2025-07-13 22:30:57,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:30:57,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:30:58,191 - INFO - Response - Page 8:
2025-07-13 22:30:58,191 - INFO - 第 8 页获取到 50 条记录
2025-07-13 22:30:58,707 - INFO - Request Parameters - Page 9:
2025-07-13 22:30:58,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:30:58,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:30:59,426 - INFO - Response - Page 9:
2025-07-13 22:30:59,426 - INFO - 第 9 页获取到 50 条记录
2025-07-13 22:30:59,926 - INFO - Request Parameters - Page 10:
2025-07-13 22:30:59,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:30:59,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:00,660 - INFO - Response - Page 10:
2025-07-13 22:31:00,660 - INFO - 第 10 页获取到 50 条记录
2025-07-13 22:31:01,176 - INFO - Request Parameters - Page 11:
2025-07-13 22:31:01,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:01,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:01,864 - INFO - Response - Page 11:
2025-07-13 22:31:01,864 - INFO - 第 11 页获取到 50 条记录
2025-07-13 22:31:02,364 - INFO - Request Parameters - Page 12:
2025-07-13 22:31:02,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:02,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:02,943 - INFO - Response - Page 12:
2025-07-13 22:31:02,943 - INFO - 第 12 页获取到 10 条记录
2025-07-13 22:31:03,443 - INFO - 查询完成，共获取到 560 条记录
2025-07-13 22:31:03,443 - INFO - 获取到 560 条表单数据
2025-07-13 22:31:03,443 - INFO - 当前日期 2025-07-02 有 1 条MySQL数据需要处理
2025-07-13 22:31:03,443 - INFO - 开始更新记录 - 表单实例ID: FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM13
2025-07-13 22:31:03,912 - INFO - 更新表单数据成功: FINST-RNA66D71I4SWFRIEBL40CCZLDMGR3UD4TRMCM13
2025-07-13 22:31:03,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/994e1c1879b846c1aa9e4e8ac72354ce.png?Expires=2066720553&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=%2Bloj2nZvGQyPBz6oS%2BSQHRmc7RE%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/6f36364ced6949b8b6bd260998462ed2.png?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=EreysBlluuXmdrB4CEuuxkS5INA%3D'}]
2025-07-13 22:31:03,912 - INFO - 日期 2025-07-02 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-13 22:31:03,912 - INFO - 开始处理日期: 2025-07-06
2025-07-13 22:31:03,912 - INFO - Request Parameters - Page 1:
2025-07-13 22:31:03,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:03,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:04,631 - INFO - Response - Page 1:
2025-07-13 22:31:04,631 - INFO - 第 1 页获取到 50 条记录
2025-07-13 22:31:05,147 - INFO - Request Parameters - Page 2:
2025-07-13 22:31:05,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:05,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:05,850 - INFO - Response - Page 2:
2025-07-13 22:31:05,850 - INFO - 第 2 页获取到 50 条记录
2025-07-13 22:31:06,366 - INFO - Request Parameters - Page 3:
2025-07-13 22:31:06,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:06,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:07,022 - INFO - Response - Page 3:
2025-07-13 22:31:07,022 - INFO - 第 3 页获取到 50 条记录
2025-07-13 22:31:07,538 - INFO - Request Parameters - Page 4:
2025-07-13 22:31:07,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:07,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:08,195 - INFO - Response - Page 4:
2025-07-13 22:31:08,195 - INFO - 第 4 页获取到 50 条记录
2025-07-13 22:31:08,710 - INFO - Request Parameters - Page 5:
2025-07-13 22:31:08,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:08,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:09,398 - INFO - Response - Page 5:
2025-07-13 22:31:09,398 - INFO - 第 5 页获取到 50 条记录
2025-07-13 22:31:09,914 - INFO - Request Parameters - Page 6:
2025-07-13 22:31:09,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:09,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:10,586 - INFO - Response - Page 6:
2025-07-13 22:31:10,586 - INFO - 第 6 页获取到 50 条记录
2025-07-13 22:31:11,086 - INFO - Request Parameters - Page 7:
2025-07-13 22:31:11,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:11,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:11,759 - INFO - Response - Page 7:
2025-07-13 22:31:11,759 - INFO - 第 7 页获取到 50 条记录
2025-07-13 22:31:12,274 - INFO - Request Parameters - Page 8:
2025-07-13 22:31:12,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:12,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:12,993 - INFO - Response - Page 8:
2025-07-13 22:31:12,993 - INFO - 第 8 页获取到 50 条记录
2025-07-13 22:31:13,494 - INFO - Request Parameters - Page 9:
2025-07-13 22:31:13,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:13,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:14,150 - INFO - Response - Page 9:
2025-07-13 22:31:14,150 - INFO - 第 9 页获取到 50 条记录
2025-07-13 22:31:14,666 - INFO - Request Parameters - Page 10:
2025-07-13 22:31:14,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:14,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:15,354 - INFO - Response - Page 10:
2025-07-13 22:31:15,354 - INFO - 第 10 页获取到 47 条记录
2025-07-13 22:31:15,870 - INFO - 查询完成，共获取到 497 条记录
2025-07-13 22:31:15,870 - INFO - 获取到 497 条表单数据
2025-07-13 22:31:15,870 - INFO - 当前日期 2025-07-06 有 1 条MySQL数据需要处理
2025-07-13 22:31:15,870 - INFO - 日期 2025-07-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 22:31:15,870 - INFO - 开始处理日期: 2025-07-10
2025-07-13 22:31:15,870 - INFO - Request Parameters - Page 1:
2025-07-13 22:31:15,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:15,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:16,604 - INFO - Response - Page 1:
2025-07-13 22:31:16,604 - INFO - 第 1 页获取到 50 条记录
2025-07-13 22:31:17,120 - INFO - Request Parameters - Page 2:
2025-07-13 22:31:17,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:17,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:17,777 - INFO - Response - Page 2:
2025-07-13 22:31:17,777 - INFO - 第 2 页获取到 50 条记录
2025-07-13 22:31:18,292 - INFO - Request Parameters - Page 3:
2025-07-13 22:31:18,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:18,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:18,980 - INFO - Response - Page 3:
2025-07-13 22:31:18,980 - INFO - 第 3 页获取到 50 条记录
2025-07-13 22:31:19,496 - INFO - Request Parameters - Page 4:
2025-07-13 22:31:19,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:19,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:20,184 - INFO - Response - Page 4:
2025-07-13 22:31:20,184 - INFO - 第 4 页获取到 50 条记录
2025-07-13 22:31:20,684 - INFO - Request Parameters - Page 5:
2025-07-13 22:31:20,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:20,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:21,372 - INFO - Response - Page 5:
2025-07-13 22:31:21,372 - INFO - 第 5 页获取到 50 条记录
2025-07-13 22:31:21,888 - INFO - Request Parameters - Page 6:
2025-07-13 22:31:21,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:21,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:22,513 - INFO - Response - Page 6:
2025-07-13 22:31:22,513 - INFO - 第 6 页获取到 50 条记录
2025-07-13 22:31:23,029 - INFO - Request Parameters - Page 7:
2025-07-13 22:31:23,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:23,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:23,732 - INFO - Response - Page 7:
2025-07-13 22:31:23,732 - INFO - 第 7 页获取到 50 条记录
2025-07-13 22:31:24,232 - INFO - Request Parameters - Page 8:
2025-07-13 22:31:24,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:24,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:24,936 - INFO - Response - Page 8:
2025-07-13 22:31:24,936 - INFO - 第 8 页获取到 50 条记录
2025-07-13 22:31:25,451 - INFO - Request Parameters - Page 9:
2025-07-13 22:31:25,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:25,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:26,186 - INFO - Response - Page 9:
2025-07-13 22:31:26,186 - INFO - 第 9 页获取到 50 条记录
2025-07-13 22:31:26,686 - INFO - Request Parameters - Page 10:
2025-07-13 22:31:26,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:26,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:27,405 - INFO - Response - Page 10:
2025-07-13 22:31:27,405 - INFO - 第 10 页获取到 50 条记录
2025-07-13 22:31:27,921 - INFO - Request Parameters - Page 11:
2025-07-13 22:31:27,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:27,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:28,515 - INFO - Response - Page 11:
2025-07-13 22:31:28,515 - INFO - 第 11 页获取到 41 条记录
2025-07-13 22:31:29,031 - INFO - 查询完成，共获取到 541 条记录
2025-07-13 22:31:29,031 - INFO - 获取到 541 条表单数据
2025-07-13 22:31:29,031 - INFO - 当前日期 2025-07-10 有 1 条MySQL数据需要处理
2025-07-13 22:31:29,031 - INFO - 日期 2025-07-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 22:31:29,031 - INFO - 开始处理日期: 2025-07-11
2025-07-13 22:31:29,031 - INFO - Request Parameters - Page 1:
2025-07-13 22:31:29,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:29,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:29,750 - INFO - Response - Page 1:
2025-07-13 22:31:29,750 - INFO - 第 1 页获取到 50 条记录
2025-07-13 22:31:30,266 - INFO - Request Parameters - Page 2:
2025-07-13 22:31:30,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:30,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:31,001 - INFO - Response - Page 2:
2025-07-13 22:31:31,001 - INFO - 第 2 页获取到 50 条记录
2025-07-13 22:31:31,501 - INFO - Request Parameters - Page 3:
2025-07-13 22:31:31,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:31,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:32,235 - INFO - Response - Page 3:
2025-07-13 22:31:32,235 - INFO - 第 3 页获取到 50 条记录
2025-07-13 22:31:32,751 - INFO - Request Parameters - Page 4:
2025-07-13 22:31:32,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:32,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:33,408 - INFO - Response - Page 4:
2025-07-13 22:31:33,408 - INFO - 第 4 页获取到 50 条记录
2025-07-13 22:31:33,908 - INFO - Request Parameters - Page 5:
2025-07-13 22:31:33,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:33,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:34,580 - INFO - Response - Page 5:
2025-07-13 22:31:34,580 - INFO - 第 5 页获取到 50 条记录
2025-07-13 22:31:35,096 - INFO - Request Parameters - Page 6:
2025-07-13 22:31:35,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:35,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:35,815 - INFO - Response - Page 6:
2025-07-13 22:31:35,815 - INFO - 第 6 页获取到 50 条记录
2025-07-13 22:31:36,331 - INFO - Request Parameters - Page 7:
2025-07-13 22:31:36,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:36,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:36,987 - INFO - Response - Page 7:
2025-07-13 22:31:36,987 - INFO - 第 7 页获取到 50 条记录
2025-07-13 22:31:37,503 - INFO - Request Parameters - Page 8:
2025-07-13 22:31:37,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:37,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:38,238 - INFO - Response - Page 8:
2025-07-13 22:31:38,238 - INFO - 第 8 页获取到 50 条记录
2025-07-13 22:31:38,738 - INFO - Request Parameters - Page 9:
2025-07-13 22:31:38,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:38,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:39,410 - INFO - Response - Page 9:
2025-07-13 22:31:39,410 - INFO - 第 9 页获取到 50 条记录
2025-07-13 22:31:39,926 - INFO - Request Parameters - Page 10:
2025-07-13 22:31:39,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:39,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:40,473 - INFO - Response - Page 10:
2025-07-13 22:31:40,473 - INFO - 第 10 页获取到 5 条记录
2025-07-13 22:31:40,989 - INFO - 查询完成，共获取到 455 条记录
2025-07-13 22:31:40,989 - INFO - 获取到 455 条表单数据
2025-07-13 22:31:40,989 - INFO - 当前日期 2025-07-11 有 3 条MySQL数据需要处理
2025-07-13 22:31:40,989 - INFO - 日期 2025-07-11 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 22:31:40,989 - INFO - 开始处理日期: 2025-07-12
2025-07-13 22:31:40,989 - INFO - Request Parameters - Page 1:
2025-07-13 22:31:40,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:40,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:41,661 - INFO - Response - Page 1:
2025-07-13 22:31:41,661 - INFO - 第 1 页获取到 50 条记录
2025-07-13 22:31:42,177 - INFO - Request Parameters - Page 2:
2025-07-13 22:31:42,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:42,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:42,865 - INFO - Response - Page 2:
2025-07-13 22:31:42,865 - INFO - 第 2 页获取到 50 条记录
2025-07-13 22:31:43,380 - INFO - Request Parameters - Page 3:
2025-07-13 22:31:43,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:43,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:44,037 - INFO - Response - Page 3:
2025-07-13 22:31:44,037 - INFO - 第 3 页获取到 50 条记录
2025-07-13 22:31:44,553 - INFO - Request Parameters - Page 4:
2025-07-13 22:31:44,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:44,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:45,209 - INFO - Response - Page 4:
2025-07-13 22:31:45,209 - INFO - 第 4 页获取到 50 条记录
2025-07-13 22:31:45,710 - INFO - Request Parameters - Page 5:
2025-07-13 22:31:45,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:45,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:46,382 - INFO - Response - Page 5:
2025-07-13 22:31:46,382 - INFO - 第 5 页获取到 50 条记录
2025-07-13 22:31:46,897 - INFO - Request Parameters - Page 6:
2025-07-13 22:31:46,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:46,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:47,570 - INFO - Response - Page 6:
2025-07-13 22:31:47,570 - INFO - 第 6 页获取到 50 条记录
2025-07-13 22:31:48,070 - INFO - Request Parameters - Page 7:
2025-07-13 22:31:48,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:48,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:48,789 - INFO - Response - Page 7:
2025-07-13 22:31:48,789 - INFO - 第 7 页获取到 50 条记录
2025-07-13 22:31:49,305 - INFO - Request Parameters - Page 8:
2025-07-13 22:31:49,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:49,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:50,008 - INFO - Response - Page 8:
2025-07-13 22:31:50,008 - INFO - 第 8 页获取到 50 条记录
2025-07-13 22:31:50,524 - INFO - Request Parameters - Page 9:
2025-07-13 22:31:50,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:50,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:51,259 - INFO - Response - Page 9:
2025-07-13 22:31:51,259 - INFO - 第 9 页获取到 50 条记录
2025-07-13 22:31:51,774 - INFO - Request Parameters - Page 10:
2025-07-13 22:31:51,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:51,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:52,322 - INFO - Response - Page 10:
2025-07-13 22:31:52,322 - INFO - 第 10 页获取到 9 条记录
2025-07-13 22:31:52,822 - INFO - 查询完成，共获取到 459 条记录
2025-07-13 22:31:52,822 - INFO - 获取到 459 条表单数据
2025-07-13 22:31:52,822 - INFO - 当前日期 2025-07-12 有 135 条MySQL数据需要处理
2025-07-13 22:31:52,822 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 22:31:52,822 - INFO - 开始处理日期: 2025-07-13
2025-07-13 22:31:52,822 - INFO - Request Parameters - Page 1:
2025-07-13 22:31:52,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:31:52,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:31:53,322 - INFO - Response - Page 1:
2025-07-13 22:31:53,322 - INFO - 查询完成，共获取到 0 条记录
2025-07-13 22:31:53,322 - INFO - 获取到 0 条表单数据
2025-07-13 22:31:53,322 - INFO - 当前日期 2025-07-13 有 3 条MySQL数据需要处理
2025-07-13 22:31:53,322 - INFO - 开始批量插入 3 条新记录
2025-07-13 22:31:53,494 - INFO - 批量插入响应状态码: 200
2025-07-13 22:31:53,494 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 14:31:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '159', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8E6724B4-11D4-7B6B-A92D-3D197AFB1DC2', 'x-acs-trace-id': '447c475ae66f6e582776de3e7dfd1c4d', 'etag': '16MbGor7N+TlciEAggkGOBw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 22:31:53,494 - INFO - 批量插入响应体: {'result': ['FINST-PPA66671670XS37E9289N6AXIV6T3W5FWR1DMCE1', 'FINST-PPA66671670XS37E9289N6AXIV6T3W5FWR1DMDE1', 'FINST-PPA66671670XS37E9289N6AXIV6T3W5FWR1DMEE1']}
2025-07-13 22:31:53,494 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-07-13 22:31:53,494 - INFO - 成功插入的数据ID: ['FINST-PPA66671670XS37E9289N6AXIV6T3W5FWR1DMCE1', 'FINST-PPA66671670XS37E9289N6AXIV6T3W5FWR1DMDE1', 'FINST-PPA66671670XS37E9289N6AXIV6T3W5FWR1DMEE1']
2025-07-13 22:31:58,511 - INFO - 批量插入完成，共 3 条记录
2025-07-13 22:31:58,511 - INFO - 日期 2025-07-13 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-07-13 22:31:58,511 - INFO - 数据同步完成！更新: 1 条，插入: 3 条，错误: 1 条
2025-07-13 22:32:58,551 - INFO - 开始同步昨天与今天的销售数据: 2025-07-12 至 2025-07-13
2025-07-13 22:32:58,551 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-13 22:32:58,551 - INFO - 查询参数: ('2025-07-12', '2025-07-13')
2025-07-13 22:32:58,707 - INFO - MySQL查询成功，时间段: 2025-07-12 至 2025-07-13，共获取 479 条记录
2025-07-13 22:32:58,707 - INFO - 获取到 2 个日期需要处理: ['2025-07-12', '2025-07-13']
2025-07-13 22:32:58,707 - INFO - 开始处理日期: 2025-07-12
2025-07-13 22:32:58,707 - INFO - Request Parameters - Page 1:
2025-07-13 22:32:58,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:32:58,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:32:59,442 - INFO - Response - Page 1:
2025-07-13 22:32:59,442 - INFO - 第 1 页获取到 50 条记录
2025-07-13 22:32:59,942 - INFO - Request Parameters - Page 2:
2025-07-13 22:32:59,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:32:59,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:33:00,630 - INFO - Response - Page 2:
2025-07-13 22:33:00,630 - INFO - 第 2 页获取到 50 条记录
2025-07-13 22:33:01,146 - INFO - Request Parameters - Page 3:
2025-07-13 22:33:01,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:33:01,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:33:01,802 - INFO - Response - Page 3:
2025-07-13 22:33:01,802 - INFO - 第 3 页获取到 50 条记录
2025-07-13 22:33:02,318 - INFO - Request Parameters - Page 4:
2025-07-13 22:33:02,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:33:02,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:33:03,037 - INFO - Response - Page 4:
2025-07-13 22:33:03,037 - INFO - 第 4 页获取到 50 条记录
2025-07-13 22:33:03,553 - INFO - Request Parameters - Page 5:
2025-07-13 22:33:03,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:33:03,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:33:04,225 - INFO - Response - Page 5:
2025-07-13 22:33:04,225 - INFO - 第 5 页获取到 50 条记录
2025-07-13 22:33:04,725 - INFO - Request Parameters - Page 6:
2025-07-13 22:33:04,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:33:04,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:33:05,444 - INFO - Response - Page 6:
2025-07-13 22:33:05,444 - INFO - 第 6 页获取到 50 条记录
2025-07-13 22:33:05,944 - INFO - Request Parameters - Page 7:
2025-07-13 22:33:05,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:33:05,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:33:06,804 - INFO - Response - Page 7:
2025-07-13 22:33:06,804 - INFO - 第 7 页获取到 50 条记录
2025-07-13 22:33:07,304 - INFO - Request Parameters - Page 8:
2025-07-13 22:33:07,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:33:07,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:33:08,008 - INFO - Response - Page 8:
2025-07-13 22:33:08,008 - INFO - 第 8 页获取到 50 条记录
2025-07-13 22:33:08,524 - INFO - Request Parameters - Page 9:
2025-07-13 22:33:08,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:33:08,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:33:09,243 - INFO - Response - Page 9:
2025-07-13 22:33:09,243 - INFO - 第 9 页获取到 50 条记录
2025-07-13 22:33:09,743 - INFO - Request Parameters - Page 10:
2025-07-13 22:33:09,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:33:09,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:33:10,274 - INFO - Response - Page 10:
2025-07-13 22:33:10,274 - INFO - 第 10 页获取到 9 条记录
2025-07-13 22:33:10,806 - INFO - 查询完成，共获取到 459 条记录
2025-07-13 22:33:10,806 - INFO - 获取到 459 条表单数据
2025-07-13 22:33:10,806 - INFO - 当前日期 2025-07-12 有 459 条MySQL数据需要处理
2025-07-13 22:33:10,821 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-13 22:33:10,821 - INFO - 开始处理日期: 2025-07-13
2025-07-13 22:33:10,821 - INFO - Request Parameters - Page 1:
2025-07-13 22:33:10,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-13 22:33:10,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752336000000, 1752422399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-13 22:33:11,306 - INFO - Response - Page 1:
2025-07-13 22:33:11,306 - INFO - 第 1 页获取到 3 条记录
2025-07-13 22:33:11,822 - INFO - 查询完成，共获取到 3 条记录
2025-07-13 22:33:11,822 - INFO - 获取到 3 条表单数据
2025-07-13 22:33:11,822 - INFO - 当前日期 2025-07-13 有 4 条MySQL数据需要处理
2025-07-13 22:33:11,822 - INFO - 开始批量插入 1 条新记录
2025-07-13 22:33:11,978 - INFO - 批量插入响应状态码: 200
2025-07-13 22:33:11,978 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 13 Jul 2025 14:33:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0BED1250-86FF-7B4D-B2DB-DBEF42603A6C', 'x-acs-trace-id': '00e6dd1efdf84f15ba0c8ab854798ea0', 'etag': '6cnRFEDcoStkaTy+CsA0RWQ1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-13 22:33:11,978 - INFO - 批量插入响应体: {'result': ['FINST-00D66K71H42XJI9CELZI2B4WEM962RO3YR1DM8I1']}
2025-07-13 22:33:11,978 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-13 22:33:11,978 - INFO - 成功插入的数据ID: ['FINST-00D66K71H42XJI9CELZI2B4WEM962RO3YR1DM8I1']
2025-07-13 22:33:16,996 - INFO - 批量插入完成，共 1 条记录
2025-07-13 22:33:16,996 - INFO - 日期 2025-07-13 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-13 22:33:16,996 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-07-13 22:33:16,996 - INFO - 同步完成
