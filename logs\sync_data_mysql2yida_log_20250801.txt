2025-08-01 01:30:33,831 - INFO - 使用默认增量同步（当天更新数据）
2025-08-01 01:30:33,831 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-01 01:30:33,831 - INFO - 查询参数: ('2025-08-01',)
2025-08-01 01:30:33,925 - INFO - MySQL查询成功，增量数据（日期: 2025-08-01），共获取 0 条记录
2025-08-01 01:30:33,925 - ERROR - 未获取到MySQL数据
2025-08-01 01:31:33,940 - INFO - 开始同步昨天与今天的销售数据: 2025-07-31 至 2025-08-01
2025-08-01 01:31:33,940 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-01 01:31:33,940 - INFO - 查询参数: ('2025-07-31', '2025-08-01')
2025-08-01 01:31:34,096 - INFO - MySQL查询成功，时间段: 2025-07-31 至 2025-08-01，共获取 78 条记录
2025-08-01 01:31:34,096 - INFO - 获取到 1 个日期需要处理: ['2025-07-31']
2025-08-01 01:31:34,096 - INFO - 开始处理日期: 2025-07-31
2025-08-01 01:31:34,096 - INFO - Request Parameters - Page 1:
2025-08-01 01:31:34,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 01:31:34,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 01:31:42,221 - ERROR - 处理日期 2025-07-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 42BDA85D-3CC7-7C80-8DB7-BECB6DB8633E Response: {'code': 'ServiceUnavailable', 'requestid': '42BDA85D-3CC7-7C80-8DB7-BECB6DB8633E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 42BDA85D-3CC7-7C80-8DB7-BECB6DB8633E)
2025-08-01 01:31:42,221 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-01 01:31:42,221 - INFO - 同步完成
2025-08-01 04:30:33,664 - INFO - 使用默认增量同步（当天更新数据）
2025-08-01 04:30:33,664 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-01 04:30:33,664 - INFO - 查询参数: ('2025-08-01',)
2025-08-01 04:30:33,758 - INFO - MySQL查询成功，增量数据（日期: 2025-08-01），共获取 0 条记录
2025-08-01 04:30:33,758 - ERROR - 未获取到MySQL数据
2025-08-01 04:31:33,773 - INFO - 开始同步昨天与今天的销售数据: 2025-07-31 至 2025-08-01
2025-08-01 04:31:33,773 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-01 04:31:33,773 - INFO - 查询参数: ('2025-07-31', '2025-08-01')
2025-08-01 04:31:33,929 - INFO - MySQL查询成功，时间段: 2025-07-31 至 2025-08-01，共获取 78 条记录
2025-08-01 04:31:33,929 - INFO - 获取到 1 个日期需要处理: ['2025-07-31']
2025-08-01 04:31:33,929 - INFO - 开始处理日期: 2025-07-31
2025-08-01 04:31:33,929 - INFO - Request Parameters - Page 1:
2025-08-01 04:31:33,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 04:31:33,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 04:31:42,054 - ERROR - 处理日期 2025-07-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 86CBF54E-FADF-7400-8AA4-A0935F90CC22 Response: {'code': 'ServiceUnavailable', 'requestid': '86CBF54E-FADF-7400-8AA4-A0935F90CC22', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 86CBF54E-FADF-7400-8AA4-A0935F90CC22)
2025-08-01 04:31:42,054 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-01 04:31:42,054 - INFO - 同步完成
2025-08-01 07:30:33,825 - INFO - 使用默认增量同步（当天更新数据）
2025-08-01 07:30:33,825 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-01 07:30:33,825 - INFO - 查询参数: ('2025-08-01',)
2025-08-01 07:30:33,981 - INFO - MySQL查询成功，增量数据（日期: 2025-08-01），共获取 1 条记录
2025-08-01 07:30:33,981 - INFO - 获取到 1 个日期需要处理: ['2025-07-31']
2025-08-01 07:30:33,981 - INFO - 开始处理日期: 2025-07-31
2025-08-01 07:30:33,996 - INFO - Request Parameters - Page 1:
2025-08-01 07:30:33,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 07:30:33,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 07:30:42,106 - ERROR - 处理日期 2025-07-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E30A197D-7A8A-77B7-AB41-98C9D9FDF920 Response: {'code': 'ServiceUnavailable', 'requestid': 'E30A197D-7A8A-77B7-AB41-98C9D9FDF920', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E30A197D-7A8A-77B7-AB41-98C9D9FDF920)
2025-08-01 07:30:42,106 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-01 07:31:42,121 - INFO - 开始同步昨天与今天的销售数据: 2025-07-31 至 2025-08-01
2025-08-01 07:31:42,121 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-01 07:31:42,121 - INFO - 查询参数: ('2025-07-31', '2025-08-01')
2025-08-01 07:31:42,277 - INFO - MySQL查询成功，时间段: 2025-07-31 至 2025-08-01，共获取 90 条记录
2025-08-01 07:31:42,277 - INFO - 获取到 1 个日期需要处理: ['2025-07-31']
2025-08-01 07:31:42,277 - INFO - 开始处理日期: 2025-07-31
2025-08-01 07:31:42,277 - INFO - Request Parameters - Page 1:
2025-08-01 07:31:42,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 07:31:42,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 07:31:49,855 - INFO - Response - Page 1:
2025-08-01 07:31:49,855 - INFO - 第 1 页获取到 50 条记录
2025-08-01 07:31:50,355 - INFO - Request Parameters - Page 2:
2025-08-01 07:31:50,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 07:31:50,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 07:31:50,886 - INFO - Response - Page 2:
2025-08-01 07:31:50,886 - INFO - 第 2 页获取到 9 条记录
2025-08-01 07:31:51,386 - INFO - 查询完成，共获取到 59 条记录
2025-08-01 07:31:51,386 - INFO - 获取到 59 条表单数据
2025-08-01 07:31:51,386 - INFO - 当前日期 2025-07-31 有 89 条MySQL数据需要处理
2025-08-01 07:31:51,386 - INFO - 开始批量插入 30 条新记录
2025-08-01 07:31:51,621 - INFO - 批量插入响应状态码: 200
2025-08-01 07:31:51,621 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 31 Jul 2025 23:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1452', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-33AD-78F3-94CE-254B34D4A2CF', 'x-acs-trace-id': 'b4a0f54bb87fd8b93e5cab9bfaf61290', 'etag': '1JDMsnRzURRvMIoJZI62v3Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-01 07:31:51,621 - INFO - 批量插入响应体: {'result': ['FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMDF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMEF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMFF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMGF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMHF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMIF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMJF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMKF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMLF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMMF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMNF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMOF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMPF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMQF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMRF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMSF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMTF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMUF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMVF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMWF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMXF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMYF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMZF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM0G', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM1G', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM2G', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM3G', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM4G', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM5G', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM6G']}
2025-08-01 07:31:51,621 - INFO - 批量插入表单数据成功，批次 1，共 30 条记录
2025-08-01 07:31:51,621 - INFO - 成功插入的数据ID: ['FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMDF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMEF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMFF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMGF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMHF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMIF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMJF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMKF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMLF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMMF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMNF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMOF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMPF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMQF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMRF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMSF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMTF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMUF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMVF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMWF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMXF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMYF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDMZF', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM0G', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM1G', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM2G', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM3G', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM4G', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM5G', 'FINST-3Z966E913AMXINK2EP4TJ5EOJ5PF2N2941SDM6G']
2025-08-01 07:31:56,636 - INFO - 批量插入完成，共 30 条记录
2025-08-01 07:31:56,636 - INFO - 日期 2025-07-31 处理完成 - 更新: 0 条，插入: 30 条，错误: 0 条
2025-08-01 07:31:56,636 - INFO - 数据同步完成！更新: 0 条，插入: 30 条，错误: 0 条
2025-08-01 07:31:56,636 - INFO - 同步完成
2025-08-01 10:30:34,058 - INFO - 使用默认增量同步（当天更新数据）
2025-08-01 10:30:34,058 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-01 10:30:34,058 - INFO - 查询参数: ('2025-08-01',)
2025-08-01 10:30:34,214 - INFO - MySQL查询成功，增量数据（日期: 2025-08-01），共获取 143 条记录
2025-08-01 10:30:34,214 - INFO - 获取到 2 个日期需要处理: ['2025-07-31', '2025-08-01']
2025-08-01 10:30:34,230 - INFO - 开始处理日期: 2025-07-31
2025-08-01 10:30:34,230 - INFO - Request Parameters - Page 1:
2025-08-01 10:30:34,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 10:30:34,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 10:30:42,355 - ERROR - 处理日期 2025-07-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4DA06900-12EF-7AD1-B556-79D97D336D13 Response: {'code': 'ServiceUnavailable', 'requestid': '4DA06900-12EF-7AD1-B556-79D97D336D13', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4DA06900-12EF-7AD1-B556-79D97D336D13)
2025-08-01 10:30:42,355 - INFO - 开始处理日期: 2025-08-01
2025-08-01 10:30:42,355 - INFO - Request Parameters - Page 1:
2025-08-01 10:30:42,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 10:30:42,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 10:30:42,839 - INFO - Response - Page 1:
2025-08-01 10:30:42,839 - INFO - 查询完成，共获取到 0 条记录
2025-08-01 10:30:42,839 - INFO - 获取到 0 条表单数据
2025-08-01 10:30:42,839 - INFO - 当前日期 2025-08-01 有 3 条MySQL数据需要处理
2025-08-01 10:30:42,839 - INFO - 开始批量插入 3 条新记录
2025-08-01 10:30:42,995 - INFO - 批量插入响应状态码: 200
2025-08-01 10:30:42,995 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 02:30:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7CC91B32-B994-7574-ACFD-8A52DEB6F688', 'x-acs-trace-id': '2b312da2131bf858513809a342ad8a80', 'etag': '1NbY0W4hfQyq4m12uVEJX+A6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-01 10:30:42,995 - INFO - 批量插入响应体: {'result': ['FINST-DKB66TA1A9KXGF096V5CV5RZOJIC3HI9I7SDM2S', 'FINST-DKB66TA1A9KXGF096V5CV5RZOJIC3HI9I7SDM3S', 'FINST-DKB66TA1A9KXGF096V5CV5RZOJIC3HI9I7SDM4S']}
2025-08-01 10:30:42,995 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-08-01 10:30:42,995 - INFO - 成功插入的数据ID: ['FINST-DKB66TA1A9KXGF096V5CV5RZOJIC3HI9I7SDM2S', 'FINST-DKB66TA1A9KXGF096V5CV5RZOJIC3HI9I7SDM3S', 'FINST-DKB66TA1A9KXGF096V5CV5RZOJIC3HI9I7SDM4S']
2025-08-01 10:30:48,011 - INFO - 批量插入完成，共 3 条记录
2025-08-01 10:30:48,011 - INFO - 日期 2025-08-01 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-08-01 10:30:48,011 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 1 条
2025-08-01 10:31:48,026 - INFO - 开始同步昨天与今天的销售数据: 2025-07-31 至 2025-08-01
2025-08-01 10:31:48,026 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-01 10:31:48,026 - INFO - 查询参数: ('2025-07-31', '2025-08-01')
2025-08-01 10:31:48,198 - INFO - MySQL查询成功，时间段: 2025-07-31 至 2025-08-01，共获取 478 条记录
2025-08-01 10:31:48,198 - INFO - 获取到 2 个日期需要处理: ['2025-07-31', '2025-08-01']
2025-08-01 10:31:48,198 - INFO - 开始处理日期: 2025-07-31
2025-08-01 10:31:48,198 - INFO - Request Parameters - Page 1:
2025-08-01 10:31:48,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 10:31:48,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 10:31:56,308 - ERROR - 处理日期 2025-07-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C6BDADC7-154E-7306-B8A8-529EB048DE90 Response: {'code': 'ServiceUnavailable', 'requestid': 'C6BDADC7-154E-7306-B8A8-529EB048DE90', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C6BDADC7-154E-7306-B8A8-529EB048DE90)
2025-08-01 10:31:56,308 - INFO - 开始处理日期: 2025-08-01
2025-08-01 10:31:56,308 - INFO - Request Parameters - Page 1:
2025-08-01 10:31:56,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 10:31:56,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 10:32:01,080 - INFO - Response - Page 1:
2025-08-01 10:32:01,081 - INFO - 第 1 页获取到 3 条记录
2025-08-01 10:32:01,581 - INFO - 查询完成，共获取到 3 条记录
2025-08-01 10:32:01,581 - INFO - 获取到 3 条表单数据
2025-08-01 10:32:01,581 - INFO - 当前日期 2025-08-01 有 3 条MySQL数据需要处理
2025-08-01 10:32:01,581 - INFO - 日期 2025-08-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 10:32:01,581 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-01 10:32:01,582 - INFO - 同步完成
2025-08-01 13:30:33,500 - INFO - 使用默认增量同步（当天更新数据）
2025-08-01 13:30:33,500 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-01 13:30:33,500 - INFO - 查询参数: ('2025-08-01',)
2025-08-01 13:30:33,666 - INFO - MySQL查询成功，增量数据（日期: 2025-08-01），共获取 164 条记录
2025-08-01 13:30:33,666 - INFO - 获取到 7 个日期需要处理: ['2025-07-04', '2025-07-06', '2025-07-07', '2025-07-23', '2025-07-27', '2025-07-31', '2025-08-01']
2025-08-01 13:30:33,668 - INFO - 开始处理日期: 2025-07-04
2025-08-01 13:30:33,672 - INFO - Request Parameters - Page 1:
2025-08-01 13:30:33,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:30:33,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:30:41,831 - ERROR - 处理日期 2025-07-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F5A3CB5B-7964-7E51-A283-F1CA2F2CD98F Response: {'code': 'ServiceUnavailable', 'requestid': 'F5A3CB5B-7964-7E51-A283-F1CA2F2CD98F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F5A3CB5B-7964-7E51-A283-F1CA2F2CD98F)
2025-08-01 13:30:41,832 - INFO - 开始处理日期: 2025-07-06
2025-08-01 13:30:41,832 - INFO - Request Parameters - Page 1:
2025-08-01 13:30:41,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:30:41,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:30:49,215 - INFO - Response - Page 1:
2025-08-01 13:30:49,215 - INFO - 第 1 页获取到 50 条记录
2025-08-01 13:30:49,717 - INFO - Request Parameters - Page 2:
2025-08-01 13:30:49,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:30:49,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:30:50,489 - INFO - Response - Page 2:
2025-08-01 13:30:50,490 - INFO - 第 2 页获取到 50 条记录
2025-08-01 13:30:50,991 - INFO - Request Parameters - Page 3:
2025-08-01 13:30:50,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:30:50,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:30:51,682 - INFO - Response - Page 3:
2025-08-01 13:30:51,682 - INFO - 第 3 页获取到 50 条记录
2025-08-01 13:30:52,183 - INFO - Request Parameters - Page 4:
2025-08-01 13:30:52,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:30:52,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:30:52,939 - INFO - Response - Page 4:
2025-08-01 13:30:52,939 - INFO - 第 4 页获取到 50 条记录
2025-08-01 13:30:53,439 - INFO - Request Parameters - Page 5:
2025-08-01 13:30:53,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:30:53,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:30:54,124 - INFO - Response - Page 5:
2025-08-01 13:30:54,124 - INFO - 第 5 页获取到 50 条记录
2025-08-01 13:30:54,625 - INFO - Request Parameters - Page 6:
2025-08-01 13:30:54,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:30:54,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:30:55,319 - INFO - Response - Page 6:
2025-08-01 13:30:55,320 - INFO - 第 6 页获取到 50 条记录
2025-08-01 13:30:55,821 - INFO - Request Parameters - Page 7:
2025-08-01 13:30:55,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:30:55,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:30:56,556 - INFO - Response - Page 7:
2025-08-01 13:30:56,556 - INFO - 第 7 页获取到 50 条记录
2025-08-01 13:30:57,057 - INFO - Request Parameters - Page 8:
2025-08-01 13:30:57,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:30:57,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:30:57,824 - INFO - Response - Page 8:
2025-08-01 13:30:57,824 - INFO - 第 8 页获取到 50 条记录
2025-08-01 13:30:58,325 - INFO - Request Parameters - Page 9:
2025-08-01 13:30:58,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:30:58,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:30:59,083 - INFO - Response - Page 9:
2025-08-01 13:30:59,083 - INFO - 第 9 页获取到 50 条记录
2025-08-01 13:30:59,584 - INFO - Request Parameters - Page 10:
2025-08-01 13:30:59,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:30:59,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:00,343 - INFO - Response - Page 10:
2025-08-01 13:31:00,343 - INFO - 第 10 页获取到 47 条记录
2025-08-01 13:31:00,844 - INFO - 查询完成，共获取到 497 条记录
2025-08-01 13:31:00,844 - INFO - 获取到 497 条表单数据
2025-08-01 13:31:00,852 - INFO - 当前日期 2025-07-06 有 1 条MySQL数据需要处理
2025-08-01 13:31:00,852 - INFO - 开始更新记录 - 表单实例ID: FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMLH
2025-08-01 13:31:01,413 - INFO - 更新表单数据成功: FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMLH
2025-08-01 13:31:01,414 - INFO - 更新记录成功，变更字段: [{'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-01 13:31:01,414 - INFO - 日期 2025-07-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-01 13:31:01,414 - INFO - 开始处理日期: 2025-07-07
2025-08-01 13:31:01,414 - INFO - Request Parameters - Page 1:
2025-08-01 13:31:01,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:01,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:02,230 - INFO - Response - Page 1:
2025-08-01 13:31:02,230 - INFO - 第 1 页获取到 50 条记录
2025-08-01 13:31:02,731 - INFO - Request Parameters - Page 2:
2025-08-01 13:31:02,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:02,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:03,501 - INFO - Response - Page 2:
2025-08-01 13:31:03,501 - INFO - 第 2 页获取到 50 条记录
2025-08-01 13:31:04,001 - INFO - Request Parameters - Page 3:
2025-08-01 13:31:04,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:04,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:04,792 - INFO - Response - Page 3:
2025-08-01 13:31:04,792 - INFO - 第 3 页获取到 50 条记录
2025-08-01 13:31:05,293 - INFO - Request Parameters - Page 4:
2025-08-01 13:31:05,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:05,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:06,075 - INFO - Response - Page 4:
2025-08-01 13:31:06,076 - INFO - 第 4 页获取到 50 条记录
2025-08-01 13:31:06,577 - INFO - Request Parameters - Page 5:
2025-08-01 13:31:06,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:06,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:07,299 - INFO - Response - Page 5:
2025-08-01 13:31:07,299 - INFO - 第 5 页获取到 50 条记录
2025-08-01 13:31:07,800 - INFO - Request Parameters - Page 6:
2025-08-01 13:31:07,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:07,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:08,578 - INFO - Response - Page 6:
2025-08-01 13:31:08,578 - INFO - 第 6 页获取到 50 条记录
2025-08-01 13:31:09,079 - INFO - Request Parameters - Page 7:
2025-08-01 13:31:09,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:09,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:09,795 - INFO - Response - Page 7:
2025-08-01 13:31:09,795 - INFO - 第 7 页获取到 50 条记录
2025-08-01 13:31:10,296 - INFO - Request Parameters - Page 8:
2025-08-01 13:31:10,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:10,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:11,052 - INFO - Response - Page 8:
2025-08-01 13:31:11,052 - INFO - 第 8 页获取到 50 条记录
2025-08-01 13:31:11,553 - INFO - Request Parameters - Page 9:
2025-08-01 13:31:11,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:11,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:12,305 - INFO - Response - Page 9:
2025-08-01 13:31:12,305 - INFO - 第 9 页获取到 50 条记录
2025-08-01 13:31:12,806 - INFO - Request Parameters - Page 10:
2025-08-01 13:31:12,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:12,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:13,534 - INFO - Response - Page 10:
2025-08-01 13:31:13,534 - INFO - 第 10 页获取到 50 条记录
2025-08-01 13:31:14,035 - INFO - Request Parameters - Page 11:
2025-08-01 13:31:14,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:14,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:14,721 - INFO - Response - Page 11:
2025-08-01 13:31:14,721 - INFO - 第 11 页获取到 30 条记录
2025-08-01 13:31:15,222 - INFO - 查询完成，共获取到 530 条记录
2025-08-01 13:31:15,222 - INFO - 获取到 530 条表单数据
2025-08-01 13:31:15,233 - INFO - 当前日期 2025-07-07 有 1 条MySQL数据需要处理
2025-08-01 13:31:15,233 - INFO - 开始更新记录 - 表单实例ID: FINST-ING66VA106YWPHH26SM4FB5P8EO12HMAM7TCMY3
2025-08-01 13:31:15,858 - INFO - 更新表单数据成功: FINST-ING66VA106YWPHH26SM4FB5P8EO12HMAM7TCMY3
2025-08-01 13:31:15,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1220.02, 'new_value': 1220.2}, {'field': 'total_amount', 'old_value': 1371.38, 'new_value': 1371.56}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/a839d0d263a44cf8b84b569380a6d99a.jpg?Expires=2066720548&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=v78K%2BvTTd4vhXxi1FNivql%2FtZ4k%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/5b919080b3e64fbb8710b9ed1aed522f.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=TG9Jz%2BoJMVGgy74PWc%2FM0vLiiUE%3D'}]
2025-08-01 13:31:15,858 - INFO - 日期 2025-07-07 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-01 13:31:15,858 - INFO - 开始处理日期: 2025-07-23
2025-08-01 13:31:15,859 - INFO - Request Parameters - Page 1:
2025-08-01 13:31:15,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:15,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:16,649 - INFO - Response - Page 1:
2025-08-01 13:31:16,649 - INFO - 第 1 页获取到 50 条记录
2025-08-01 13:31:17,150 - INFO - Request Parameters - Page 2:
2025-08-01 13:31:17,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:17,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:17,885 - INFO - Response - Page 2:
2025-08-01 13:31:17,885 - INFO - 第 2 页获取到 50 条记录
2025-08-01 13:31:18,386 - INFO - Request Parameters - Page 3:
2025-08-01 13:31:18,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:18,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:19,169 - INFO - Response - Page 3:
2025-08-01 13:31:19,169 - INFO - 第 3 页获取到 50 条记录
2025-08-01 13:31:19,670 - INFO - Request Parameters - Page 4:
2025-08-01 13:31:19,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:19,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:20,392 - INFO - Response - Page 4:
2025-08-01 13:31:20,393 - INFO - 第 4 页获取到 50 条记录
2025-08-01 13:31:20,894 - INFO - Request Parameters - Page 5:
2025-08-01 13:31:20,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:20,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:21,579 - INFO - Response - Page 5:
2025-08-01 13:31:21,579 - INFO - 第 5 页获取到 50 条记录
2025-08-01 13:31:22,080 - INFO - Request Parameters - Page 6:
2025-08-01 13:31:22,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:22,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:22,803 - INFO - Response - Page 6:
2025-08-01 13:31:22,803 - INFO - 第 6 页获取到 50 条记录
2025-08-01 13:31:23,304 - INFO - Request Parameters - Page 7:
2025-08-01 13:31:23,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:23,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:24,138 - INFO - Response - Page 7:
2025-08-01 13:31:24,139 - INFO - 第 7 页获取到 50 条记录
2025-08-01 13:31:24,639 - INFO - Request Parameters - Page 8:
2025-08-01 13:31:24,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:24,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:25,344 - INFO - Response - Page 8:
2025-08-01 13:31:25,344 - INFO - 第 8 页获取到 50 条记录
2025-08-01 13:31:25,845 - INFO - Request Parameters - Page 9:
2025-08-01 13:31:25,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:25,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:26,552 - INFO - Response - Page 9:
2025-08-01 13:31:26,553 - INFO - 第 9 页获取到 50 条记录
2025-08-01 13:31:27,054 - INFO - Request Parameters - Page 10:
2025-08-01 13:31:27,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:27,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:27,831 - INFO - Response - Page 10:
2025-08-01 13:31:27,831 - INFO - 第 10 页获取到 50 条记录
2025-08-01 13:31:28,331 - INFO - Request Parameters - Page 11:
2025-08-01 13:31:28,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:28,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:28,878 - INFO - Response - Page 11:
2025-08-01 13:31:28,878 - INFO - 第 11 页获取到 5 条记录
2025-08-01 13:31:29,379 - INFO - 查询完成，共获取到 505 条记录
2025-08-01 13:31:29,379 - INFO - 获取到 505 条表单数据
2025-08-01 13:31:29,388 - INFO - 当前日期 2025-07-23 有 1 条MySQL数据需要处理
2025-08-01 13:31:29,388 - INFO - 开始更新记录 - 表单实例ID: FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMD5
2025-08-01 13:31:30,015 - INFO - 更新表单数据成功: FINST-F3G66Q61XGEXSOYBA1WT2D1TZL7M2Q1T5FGDMD5
2025-08-01 13:31:30,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 760.09, 'new_value': 710.09}, {'field': 'total_amount', 'old_value': 1260.09, 'new_value': 1210.09}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/7bd9b6fbe90f449ab83bb11c51b324ea.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=dZIMOtFXnxXV6YDX%2FQdQPQMGYUA%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/bc533112ef684ea59f1f2c37fed76830.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=JDGYsWIX2uTTx4oUpjO0w0ZEmgA%3D'}]
2025-08-01 13:31:30,015 - INFO - 日期 2025-07-23 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-01 13:31:30,015 - INFO - 开始处理日期: 2025-07-27
2025-08-01 13:31:30,015 - INFO - Request Parameters - Page 1:
2025-08-01 13:31:30,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:30,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:30,772 - INFO - Response - Page 1:
2025-08-01 13:31:30,773 - INFO - 第 1 页获取到 50 条记录
2025-08-01 13:31:31,274 - INFO - Request Parameters - Page 2:
2025-08-01 13:31:31,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:31,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:31,934 - INFO - Response - Page 2:
2025-08-01 13:31:31,935 - INFO - 第 2 页获取到 50 条记录
2025-08-01 13:31:32,435 - INFO - Request Parameters - Page 3:
2025-08-01 13:31:32,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:32,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:33,193 - INFO - Response - Page 3:
2025-08-01 13:31:33,193 - INFO - 第 3 页获取到 50 条记录
2025-08-01 13:31:33,693 - INFO - Request Parameters - Page 4:
2025-08-01 13:31:33,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:33,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:34,430 - INFO - Response - Page 4:
2025-08-01 13:31:34,430 - INFO - 第 4 页获取到 50 条记录
2025-08-01 13:31:34,931 - INFO - Request Parameters - Page 5:
2025-08-01 13:31:34,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:34,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:35,693 - INFO - Response - Page 5:
2025-08-01 13:31:35,693 - INFO - 第 5 页获取到 50 条记录
2025-08-01 13:31:36,193 - INFO - Request Parameters - Page 6:
2025-08-01 13:31:36,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:36,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:36,930 - INFO - Response - Page 6:
2025-08-01 13:31:36,931 - INFO - 第 6 页获取到 50 条记录
2025-08-01 13:31:37,432 - INFO - Request Parameters - Page 7:
2025-08-01 13:31:37,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:37,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:38,128 - INFO - Response - Page 7:
2025-08-01 13:31:38,129 - INFO - 第 7 页获取到 50 条记录
2025-08-01 13:31:38,629 - INFO - Request Parameters - Page 8:
2025-08-01 13:31:38,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:38,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:39,384 - INFO - Response - Page 8:
2025-08-01 13:31:39,384 - INFO - 第 8 页获取到 50 条记录
2025-08-01 13:31:39,885 - INFO - Request Parameters - Page 9:
2025-08-01 13:31:39,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:39,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:40,603 - INFO - Response - Page 9:
2025-08-01 13:31:40,604 - INFO - 第 9 页获取到 50 条记录
2025-08-01 13:31:41,105 - INFO - Request Parameters - Page 10:
2025-08-01 13:31:41,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:41,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:41,856 - INFO - Response - Page 10:
2025-08-01 13:31:41,856 - INFO - 第 10 页获取到 50 条记录
2025-08-01 13:31:42,356 - INFO - Request Parameters - Page 11:
2025-08-01 13:31:42,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:42,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:43,063 - INFO - Response - Page 11:
2025-08-01 13:31:43,063 - INFO - 第 11 页获取到 43 条记录
2025-08-01 13:31:43,564 - INFO - 查询完成，共获取到 543 条记录
2025-08-01 13:31:43,564 - INFO - 获取到 543 条表单数据
2025-08-01 13:31:43,574 - INFO - 当前日期 2025-07-27 有 2 条MySQL数据需要处理
2025-08-01 13:31:43,574 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM3I1
2025-08-01 13:31:44,134 - INFO - 更新表单数据成功: FINST-00D66K71FJHXNDSLABHBZAF0V8NF282DX4MDM3I1
2025-08-01 13:31:44,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1552.11, 'new_value': 1563.65}, {'field': 'total_amount', 'old_value': 1552.11, 'new_value': 1563.65}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/a4bc2882d5ba4124975b3ae5d33beb77.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=%2Bhnvq1Yuu59MsnZ%2FF%2F47hF8DQUI%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/df48f85b067a4ec8bdbfac04099bc263.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=gHV1RS%2BTa2uoiev3d3fnYXEfb9w%3D'}]
2025-08-01 13:31:44,134 - INFO - 开始更新记录 - 表单实例ID: FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM501
2025-08-01 13:31:44,668 - INFO - 更新表单数据成功: FINST-Y7D660D1W9HXTRZ37LXBSAL3Z92R2LFMOUMDM501
2025-08-01 13:31:44,668 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7840.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2244.0, 'new_value': 10282.0}, {'field': 'total_amount', 'old_value': 10084.0, 'new_value': 10282.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-01 13:31:44,668 - INFO - 日期 2025-07-27 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-08-01 13:31:44,668 - INFO - 开始处理日期: 2025-07-31
2025-08-01 13:31:44,668 - INFO - Request Parameters - Page 1:
2025-08-01 13:31:44,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:44,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:45,358 - INFO - Response - Page 1:
2025-08-01 13:31:45,359 - INFO - 第 1 页获取到 50 条记录
2025-08-01 13:31:45,860 - INFO - Request Parameters - Page 2:
2025-08-01 13:31:45,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:31:45,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:31:46,556 - INFO - Response - Page 2:
2025-08-01 13:31:46,556 - INFO - 第 2 页获取到 39 条记录
2025-08-01 13:31:47,056 - INFO - 查询完成，共获取到 89 条记录
2025-08-01 13:31:47,056 - INFO - 获取到 89 条表单数据
2025-08-01 13:31:47,058 - INFO - 当前日期 2025-07-31 有 148 条MySQL数据需要处理
2025-08-01 13:31:47,060 - INFO - 开始批量插入 147 条新记录
2025-08-01 13:31:47,288 - INFO - 批量插入响应状态码: 200
2025-08-01 13:31:47,289 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 05:31:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DB84E038-E059-7483-9BD4-ECF4CF8CF109', 'x-acs-trace-id': '565e804e8564f0873ba769d28139f2ef', 'etag': '2LSAZiweN4g74L1DmhoQW3Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-01 13:31:47,289 - INFO - 批量插入响应体: {'result': ['FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM72', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM82', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM92', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMA2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMB2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMC2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMD2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDME2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMF2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMG2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMH2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMI2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMJ2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMK2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDML2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMM2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMN2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMO2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMP2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMQ2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMR2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMS2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMT2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMU2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMV2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMW2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMX2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMY2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMZ2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM03', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM13', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM23', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM33', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM43', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM53', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM63', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM73', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM83', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM93', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMA3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMB3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMC3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMD3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDME3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMF3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMG3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMH3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMI3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMJ3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMK3']}
2025-08-01 13:31:47,289 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-01 13:31:47,289 - INFO - 成功插入的数据ID: ['FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM72', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM82', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM92', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMA2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMB2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMC2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMD2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDME2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMF2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMG2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMH2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMI2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMJ2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMK2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDML2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMM2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMN2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMO2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMP2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMQ2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMR2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMS2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMT2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMU2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMV2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMW2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMX2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMY2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMZ2', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM03', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM13', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM23', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM33', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM43', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM53', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM63', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM73', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM83', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDM93', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMA3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMB3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMC3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMD3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDME3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMF3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMG3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMH3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMI3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMJ3', 'FINST-VEC667D1UCMX4WRKCX7825P62JWZ1DI4ZDSDMK3']
2025-08-01 13:31:52,590 - INFO - 批量插入响应状态码: 200
2025-08-01 13:31:52,590 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 05:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A22B0770-2AA4-71F7-8C5A-4375E47FAE0C', 'x-acs-trace-id': 'c83d14ff68953d850fb58e93011c1b8d', 'etag': '2RC4yDSfTiYNLEz5EiwPZrg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-01 13:31:52,590 - INFO - 批量插入响应体: {'result': ['FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM31', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM41', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM51', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM61', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM71', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM81', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM91', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMA1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMB1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMC1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMD1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDME1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMF1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMG1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMH1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMI1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMJ1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMK1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDML1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMM1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMN1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMO1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMP1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMQ1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMR1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMS1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMT1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMU1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMV1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMW1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMX1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMY1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMZ1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM02', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM12', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM22', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM32', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM42', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM52', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM62', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM72', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM82', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM92', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMA2', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMB2', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMC2', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMD2', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDME2', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMF2', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMG2']}
2025-08-01 13:31:52,590 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-01 13:31:52,590 - INFO - 成功插入的数据ID: ['FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM31', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM41', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM51', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM61', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM71', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM81', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM91', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMA1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMB1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMC1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMD1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDME1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMF1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMG1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMH1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMI1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMJ1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMK1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDML1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMM1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMN1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMO1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMP1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMQ1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMR1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMS1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMT1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMU1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMV1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMW1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMX1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMY1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMZ1', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM02', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM12', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM22', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM32', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM42', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM52', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM62', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM72', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM82', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDM92', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMA2', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMB2', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMC2', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMD2', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDME2', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMF2', 'FINST-ZP966U81OMLX82T8ACPQA8L2XRLB2OL8ZDSDMG2']
2025-08-01 13:31:57,821 - INFO - 批量插入响应状态码: 200
2025-08-01 13:31:57,821 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 05:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2268', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A96624CF-A503-7A09-A3DA-1BEAA10F464A', 'x-acs-trace-id': '1c096e044e0266a1bd0211fca2319bbe', 'etag': '2QHpj5G/n0UcoYOp+6EJ4sQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-01 13:31:57,821 - INFO - 批量插入响应体: {'result': ['FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM21', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM31', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM41', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM51', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM61', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM71', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM81', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM91', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMA1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMB1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMC1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMD1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDME1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMF1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMG1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMH1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMI1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMJ1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMK1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDML1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMM1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMN1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMO1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMP1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMQ1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMR1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMS1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMT1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMU1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMV1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMW1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMX1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMY1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMZ1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM02', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM12', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM22', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM32', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM42', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM52', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM62', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM72', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM82', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM92', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2WMCZDSDMA2', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2WMCZDSDMB2', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2WMCZDSDMC2']}
2025-08-01 13:31:57,821 - INFO - 批量插入表单数据成功，批次 3，共 47 条记录
2025-08-01 13:31:57,821 - INFO - 成功插入的数据ID: ['FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM21', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM31', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM41', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM51', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM61', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM71', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM81', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM91', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMA1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMB1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMC1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMD1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDME1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMF1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMG1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMH1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMI1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMJ1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMK1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDML1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMM1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMN1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMO1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMP1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMQ1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMR1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMS1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMT1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMU1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMV1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMW1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMX1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMY1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDMZ1', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM02', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM12', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM22', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM32', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM42', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM52', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM62', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM72', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM82', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2VMCZDSDM92', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2WMCZDSDMA2', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2WMCZDSDMB2', 'FINST-2PF66CD1U9NXJCSHAHKRN5L6VQCV2WMCZDSDMC2']
2025-08-01 13:32:02,822 - INFO - 批量插入完成，共 147 条记录
2025-08-01 13:32:02,822 - INFO - 日期 2025-07-31 处理完成 - 更新: 0 条，插入: 147 条，错误: 0 条
2025-08-01 13:32:02,822 - INFO - 开始处理日期: 2025-08-01
2025-08-01 13:32:02,822 - INFO - Request Parameters - Page 1:
2025-08-01 13:32:02,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:32:02,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:32:03,432 - INFO - Response - Page 1:
2025-08-01 13:32:03,432 - INFO - 第 1 页获取到 3 条记录
2025-08-01 13:32:03,933 - INFO - 查询完成，共获取到 3 条记录
2025-08-01 13:32:03,933 - INFO - 获取到 3 条表单数据
2025-08-01 13:32:03,933 - INFO - 当前日期 2025-08-01 有 3 条MySQL数据需要处理
2025-08-01 13:32:03,934 - INFO - 日期 2025-08-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 13:32:03,934 - INFO - 数据同步完成！更新: 5 条，插入: 147 条，错误: 1 条
2025-08-01 13:33:03,935 - INFO - 开始同步昨天与今天的销售数据: 2025-07-31 至 2025-08-01
2025-08-01 13:33:03,935 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-01 13:33:03,935 - INFO - 查询参数: ('2025-07-31', '2025-08-01')
2025-08-01 13:33:04,106 - INFO - MySQL查询成功，时间段: 2025-07-31 至 2025-08-01，共获取 497 条记录
2025-08-01 13:33:04,106 - INFO - 获取到 2 个日期需要处理: ['2025-07-31', '2025-08-01']
2025-08-01 13:33:04,111 - INFO - 开始处理日期: 2025-07-31
2025-08-01 13:33:04,112 - INFO - Request Parameters - Page 1:
2025-08-01 13:33:04,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:33:04,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:33:04,899 - INFO - Response - Page 1:
2025-08-01 13:33:04,899 - INFO - 第 1 页获取到 50 条记录
2025-08-01 13:33:05,401 - INFO - Request Parameters - Page 2:
2025-08-01 13:33:05,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:33:05,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:33:06,111 - INFO - Response - Page 2:
2025-08-01 13:33:06,111 - INFO - 第 2 页获取到 50 条记录
2025-08-01 13:33:06,611 - INFO - Request Parameters - Page 3:
2025-08-01 13:33:06,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:33:06,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:33:07,430 - INFO - Response - Page 3:
2025-08-01 13:33:07,430 - INFO - 第 3 页获取到 50 条记录
2025-08-01 13:33:07,930 - INFO - Request Parameters - Page 4:
2025-08-01 13:33:07,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:33:07,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:33:08,616 - INFO - Response - Page 4:
2025-08-01 13:33:08,616 - INFO - 第 4 页获取到 50 条记录
2025-08-01 13:33:09,117 - INFO - Request Parameters - Page 5:
2025-08-01 13:33:09,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:33:09,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753891200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:33:09,799 - INFO - Response - Page 5:
2025-08-01 13:33:09,799 - INFO - 第 5 页获取到 36 条记录
2025-08-01 13:33:10,299 - INFO - 查询完成，共获取到 236 条记录
2025-08-01 13:33:10,299 - INFO - 获取到 236 条表单数据
2025-08-01 13:33:10,304 - INFO - 当前日期 2025-07-31 有 484 条MySQL数据需要处理
2025-08-01 13:33:10,312 - INFO - 开始批量插入 248 条新记录
2025-08-01 13:33:10,537 - INFO - 批量插入响应状态码: 200
2025-08-01 13:33:10,537 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 05:33:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0EFFF45E-316F-73A1-9513-86FC78671E50', 'x-acs-trace-id': '9062e30c6d95ce22a57cce10ccfcef42', 'etag': '2M1Of4vr7O2p7LnYiJDtE7w2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-01 13:33:10,537 - INFO - 批量插入响应体: {'result': ['FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMNP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMOP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMPP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMQP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMRP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMSP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMTP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMUP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMVP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMWP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMXP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMYP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMZP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM0Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM1Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM2Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM3Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM4Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM5Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM6Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM7Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM8Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM9Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMAQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMBQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMCQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMDQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMEQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMFQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMGQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMHQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMIQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMJQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMKQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMLQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMMQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMNQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMOQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMPQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMQQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMRQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMSQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMTQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMUQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMVQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMWQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMXQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMYQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMZQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM0R']}
2025-08-01 13:33:10,537 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-01 13:33:10,537 - INFO - 成功插入的数据ID: ['FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMNP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMOP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMPP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMQP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMRP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMSP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMTP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMUP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMVP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMWP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMXP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMYP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMZP', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM0Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM1Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM2Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM3Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM4Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM5Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM6Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM7Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM8Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM9Q', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMAQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMBQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMCQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMDQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMEQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMFQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMGQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMHQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMIQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMJQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMKQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMLQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMMQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMNQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMOQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMPQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMQQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMRQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMSQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMTQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMUQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMVQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMWQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMXQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMYQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDMZQ', 'FINST-MKF66PA1PAMX8VK1BLMID6P52EOB2VQW0ESDM0R']
2025-08-01 13:33:15,788 - INFO - 批量插入响应状态码: 200
2025-08-01 13:33:15,788 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 05:33:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '24C40568-3829-7957-A597-BD66AEC3A27E', 'x-acs-trace-id': '5ee565635d3ab828f901c725cd8cbdbb', 'etag': '2pPavJwBhV2XRwXWd792qJg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-01 13:33:15,788 - INFO - 批量插入响应体: {'result': ['FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMCT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMDT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMET', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMFT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMGT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMHT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMIT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMJT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMKT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMLT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMMT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMNT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMOT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMPT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMQT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMRT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMST', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMTT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMUT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMVT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMWT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMXT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMYT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMZT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM0U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM1U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM2U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM3U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM4U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM5U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM6U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM7U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM8U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM9U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMAU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMBU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMCU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMDU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMEU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMFU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMGU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMHU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMIU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMJU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMKU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMLU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMMU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMNU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMOU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMPU']}
2025-08-01 13:33:15,788 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-01 13:33:15,788 - INFO - 成功插入的数据ID: ['FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMCT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMDT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMET', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMFT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMGT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMHT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMIT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMJT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMKT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMLT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMMT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMNT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMOT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMPT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMQT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMRT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMST', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMTT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMUT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMVT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMWT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMXT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMYT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMZT', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM0U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM1U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM2U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM3U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM4U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM5U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM6U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM7U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM8U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDM9U', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMAU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMBU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMCU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMDU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMEU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMFU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMGU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMHU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMIU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMJU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMKU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMLU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMMU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMNU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMOU', 'FINST-E3G66QA1T9KXT58WDI5ZD9AUF8SJ2MS01ESDMPU']
2025-08-01 13:33:21,009 - INFO - 批量插入响应状态码: 200
2025-08-01 13:33:21,009 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 05:33:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '07CE9884-DE6D-7019-A633-668E5476564B', 'x-acs-trace-id': '86b685c40c89f447c6015bd5688d447e', 'etag': '2g9DtkV2fWXF4fszpJuSIPg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-01 13:33:21,009 - INFO - 批量插入响应体: {'result': ['FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMPB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMQB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMRB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMSB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMTB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMUB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMVB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMWB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMXB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMYB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMZB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDM0C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDM1C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDM2C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM3C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM4C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM5C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM6C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM7C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM8C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM9C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMAC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMBC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMCC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMDC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMEC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMFC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMGC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMHC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMIC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMJC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMKC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMLC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMMC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMNC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMOC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMPC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMQC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMRC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMSC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMTC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMUC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMVC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMWC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMXC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMYC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMZC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM0D', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM1D', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM2D']}
2025-08-01 13:33:21,009 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-08-01 13:33:21,009 - INFO - 成功插入的数据ID: ['FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMPB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMQB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMRB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMSB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMTB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMUB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMVB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMWB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMXB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMYB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDMZB', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDM0C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDM1C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2QT41ESDM2C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM3C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM4C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM5C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM6C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM7C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM8C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM9C', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMAC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMBC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMCC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMDC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMEC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMFC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMGC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMHC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMIC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMJC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMKC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMLC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMMC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMNC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMOC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMPC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMQC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMRC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMSC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMTC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMUC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMVC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMWC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMXC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMYC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDMZC', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM0D', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM1D', 'FINST-00D66K71C9MXKFGP7IFAY4ARSG4B2RT41ESDM2D']
2025-08-01 13:33:26,234 - INFO - 批量插入响应状态码: 200
2025-08-01 13:33:26,234 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 05:33:26 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DE137261-F21B-7E4B-A935-F3A66BE08E70', 'x-acs-trace-id': '9d37dda4ca6994467047a3b0e585635f', 'etag': '2ADALqXnF8206c0qBRRBb6g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-01 13:33:26,234 - INFO - 批量插入响应体: {'result': ['FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMLC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMMC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMNC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMOC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMPC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMQC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMRC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMSC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMTC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMUC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMVC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMWC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMXC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMYC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMZC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM0D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM1D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM2D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM3D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM4D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM5D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM6D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM7D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM8D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM9D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMAD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMBD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMCD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMDD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMED', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMFD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMGD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMHD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMID', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMJD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMKD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMLD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMMD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMND', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMOD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMPD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMQD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMRD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMSD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMTD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMUD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMVD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMWD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMXD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMYD']}
2025-08-01 13:33:26,234 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-08-01 13:33:26,234 - INFO - 成功插入的数据ID: ['FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMLC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMMC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMNC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMOC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMPC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMQC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMRC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMSC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMTC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMUC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMVC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMWC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMXC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMYC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMZC', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM0D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM1D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM2D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM3D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM4D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM5D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM6D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM7D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM8D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDM9D', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMAD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMBD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMCD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMDD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMED', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMFD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMGD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMHD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMID', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMJD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMKD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMLD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMMD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMND', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMOD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMPD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMQD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMRD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMSD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMTD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMUD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMVD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMWD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMXD', 'FINST-Y7D660D15HMX4XL59HXP148O5FIU2XU81ESDMYD']
2025-08-01 13:33:31,522 - INFO - 批量插入响应状态码: 200
2025-08-01 13:33:31,522 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 05:33:31 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2316', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '10B5B5A3-CA52-76D9-91FB-B1CB2ABABE7B', 'x-acs-trace-id': 'cb467cbf6150699c93da9a8ca905eb3c', 'etag': '2ZhSpjtO/Ajegl7/WtwBfwg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-01 13:33:31,522 - INFO - 批量插入响应体: {'result': ['FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMDQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMEQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMFQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMGQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMHQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMIQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMJQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMKQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMLQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMMQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMNQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMOQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMPQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMQQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMRQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMSQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMTQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMUQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMVQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMWQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMXQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMYQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMZQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM0R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM1R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM2R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM3R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM4R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM5R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM6R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM7R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM8R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM9R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMAR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMBR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMCR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMDR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMER', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMFR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMGR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMHR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMIR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMJR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMKR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMLR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMMR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMNR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMOR']}
2025-08-01 13:33:31,522 - INFO - 批量插入表单数据成功，批次 5，共 48 条记录
2025-08-01 13:33:31,522 - INFO - 成功插入的数据ID: ['FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMDQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMEQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMFQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMGQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMHQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMIQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMJQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMKQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMLQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMMQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMNQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMOQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMPQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMQQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMRQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMSQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMTQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMUQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMVQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMWQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMXQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMYQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMZQ', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM0R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM1R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM2R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM3R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM4R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM5R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM6R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM7R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM8R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDM9R', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMAR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMBR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMCR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMDR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMER', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMFR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMGR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMHR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMIR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMJR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMKR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMLR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMMR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMNR', 'FINST-UNG66081EMLXFT0G6U6DW6ACMI3G3TXC1ESDMOR']
2025-08-01 13:33:36,523 - INFO - 批量插入完成，共 248 条记录
2025-08-01 13:33:36,523 - INFO - 日期 2025-07-31 处理完成 - 更新: 0 条，插入: 248 条，错误: 0 条
2025-08-01 13:33:36,523 - INFO - 开始处理日期: 2025-08-01
2025-08-01 13:33:36,523 - INFO - Request Parameters - Page 1:
2025-08-01 13:33:36,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-08-01 13:33:36,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-01 13:33:37,114 - INFO - Response - Page 1:
2025-08-01 13:33:37,115 - INFO - 第 1 页获取到 3 条记录
2025-08-01 13:33:37,615 - INFO - 查询完成，共获取到 3 条记录
2025-08-01 13:33:37,615 - INFO - 获取到 3 条表单数据
2025-08-01 13:33:37,615 - INFO - 当前日期 2025-08-01 有 3 条MySQL数据需要处理
2025-08-01 13:33:37,616 - INFO - 日期 2025-08-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-01 13:33:37,616 - INFO - 数据同步完成！更新: 0 条，插入: 248 条，错误: 0 条
2025-08-01 13:33:37,616 - INFO - 同步完成
2025-08-01 16:30:34,091 - ERROR - 程序执行出错: (2013, 'Lost connection to MySQL server during query')
2025-08-01 16:30:35,843 - ERROR - 程序执行出错: (2013, 'Lost connection to MySQL server during query')
2025-08-01 16:30:37,186 - ERROR - 程序执行出错: (2013, 'Lost connection to MySQL server during query')
