2025-06-22 00:00:03,225 - INFO - =================使用默认全量同步=============
2025-06-22 00:00:05,022 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-22 00:00:05,022 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-22 00:00:05,053 - INFO - 开始处理日期: 2025-01
2025-06-22 00:00:05,053 - INFO - Request Parameters - Page 1:
2025-06-22 00:00:05,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:05,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:06,460 - INFO - Response - Page 1:
2025-06-22 00:00:06,663 - INFO - 第 1 页获取到 100 条记录
2025-06-22 00:00:06,663 - INFO - Request Parameters - Page 2:
2025-06-22 00:00:06,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:06,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:07,335 - INFO - Response - Page 2:
2025-06-22 00:00:07,538 - INFO - 第 2 页获取到 100 条记录
2025-06-22 00:00:07,538 - INFO - Request Parameters - Page 3:
2025-06-22 00:00:07,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:07,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:08,366 - INFO - Response - Page 3:
2025-06-22 00:00:08,569 - INFO - 第 3 页获取到 100 条记录
2025-06-22 00:00:08,569 - INFO - Request Parameters - Page 4:
2025-06-22 00:00:08,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:08,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:09,085 - INFO - Response - Page 4:
2025-06-22 00:00:09,288 - INFO - 第 4 页获取到 100 条记录
2025-06-22 00:00:09,288 - INFO - Request Parameters - Page 5:
2025-06-22 00:00:09,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:09,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:09,756 - INFO - Response - Page 5:
2025-06-22 00:00:09,960 - INFO - 第 5 页获取到 100 条记录
2025-06-22 00:00:09,960 - INFO - Request Parameters - Page 6:
2025-06-22 00:00:09,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:09,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:10,491 - INFO - Response - Page 6:
2025-06-22 00:00:10,694 - INFO - 第 6 页获取到 100 条记录
2025-06-22 00:00:10,694 - INFO - Request Parameters - Page 7:
2025-06-22 00:00:10,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:10,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:11,100 - INFO - Response - Page 7:
2025-06-22 00:00:11,303 - INFO - 第 7 页获取到 82 条记录
2025-06-22 00:00:11,303 - INFO - 查询完成，共获取到 682 条记录
2025-06-22 00:00:11,303 - INFO - 获取到 682 条表单数据
2025-06-22 00:00:11,303 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-22 00:00:11,319 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 00:00:11,319 - INFO - 开始处理日期: 2025-02
2025-06-22 00:00:11,319 - INFO - Request Parameters - Page 1:
2025-06-22 00:00:11,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:11,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:11,819 - INFO - Response - Page 1:
2025-06-22 00:00:12,022 - INFO - 第 1 页获取到 100 条记录
2025-06-22 00:00:12,022 - INFO - Request Parameters - Page 2:
2025-06-22 00:00:12,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:12,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:12,522 - INFO - Response - Page 2:
2025-06-22 00:00:12,725 - INFO - 第 2 页获取到 100 条记录
2025-06-22 00:00:12,725 - INFO - Request Parameters - Page 3:
2025-06-22 00:00:12,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:12,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:13,194 - INFO - Response - Page 3:
2025-06-22 00:00:13,397 - INFO - 第 3 页获取到 100 条记录
2025-06-22 00:00:13,397 - INFO - Request Parameters - Page 4:
2025-06-22 00:00:13,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:13,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:13,866 - INFO - Response - Page 4:
2025-06-22 00:00:14,069 - INFO - 第 4 页获取到 100 条记录
2025-06-22 00:00:14,069 - INFO - Request Parameters - Page 5:
2025-06-22 00:00:14,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:14,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:14,631 - INFO - Response - Page 5:
2025-06-22 00:00:14,835 - INFO - 第 5 页获取到 100 条记录
2025-06-22 00:00:14,835 - INFO - Request Parameters - Page 6:
2025-06-22 00:00:14,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:14,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:15,600 - INFO - Response - Page 6:
2025-06-22 00:00:15,803 - INFO - 第 6 页获取到 100 条记录
2025-06-22 00:00:15,803 - INFO - Request Parameters - Page 7:
2025-06-22 00:00:15,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:15,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:16,256 - INFO - Response - Page 7:
2025-06-22 00:00:16,460 - INFO - 第 7 页获取到 70 条记录
2025-06-22 00:00:16,460 - INFO - 查询完成，共获取到 670 条记录
2025-06-22 00:00:16,460 - INFO - 获取到 670 条表单数据
2025-06-22 00:00:16,460 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-22 00:00:16,475 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 00:00:16,475 - INFO - 开始处理日期: 2025-03
2025-06-22 00:00:16,475 - INFO - Request Parameters - Page 1:
2025-06-22 00:00:16,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:16,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:16,928 - INFO - Response - Page 1:
2025-06-22 00:00:17,131 - INFO - 第 1 页获取到 100 条记录
2025-06-22 00:00:17,131 - INFO - Request Parameters - Page 2:
2025-06-22 00:00:17,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:17,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:17,975 - INFO - Response - Page 2:
2025-06-22 00:00:18,178 - INFO - 第 2 页获取到 100 条记录
2025-06-22 00:00:18,178 - INFO - Request Parameters - Page 3:
2025-06-22 00:00:18,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:18,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:18,678 - INFO - Response - Page 3:
2025-06-22 00:00:18,881 - INFO - 第 3 页获取到 100 条记录
2025-06-22 00:00:18,881 - INFO - Request Parameters - Page 4:
2025-06-22 00:00:18,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:18,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:19,366 - INFO - Response - Page 4:
2025-06-22 00:00:19,569 - INFO - 第 4 页获取到 100 条记录
2025-06-22 00:00:19,569 - INFO - Request Parameters - Page 5:
2025-06-22 00:00:19,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:19,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:20,053 - INFO - Response - Page 5:
2025-06-22 00:00:20,256 - INFO - 第 5 页获取到 100 条记录
2025-06-22 00:00:20,256 - INFO - Request Parameters - Page 6:
2025-06-22 00:00:20,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:20,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:20,819 - INFO - Response - Page 6:
2025-06-22 00:00:21,022 - INFO - 第 6 页获取到 100 条记录
2025-06-22 00:00:21,022 - INFO - Request Parameters - Page 7:
2025-06-22 00:00:21,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:21,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:21,475 - INFO - Response - Page 7:
2025-06-22 00:00:21,678 - INFO - 第 7 页获取到 61 条记录
2025-06-22 00:00:21,678 - INFO - 查询完成，共获取到 661 条记录
2025-06-22 00:00:21,678 - INFO - 获取到 661 条表单数据
2025-06-22 00:00:21,678 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-22 00:00:21,694 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 00:00:21,694 - INFO - 开始处理日期: 2025-04
2025-06-22 00:00:21,694 - INFO - Request Parameters - Page 1:
2025-06-22 00:00:21,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:21,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:22,225 - INFO - Response - Page 1:
2025-06-22 00:00:22,428 - INFO - 第 1 页获取到 100 条记录
2025-06-22 00:00:22,428 - INFO - Request Parameters - Page 2:
2025-06-22 00:00:22,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:22,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:22,975 - INFO - Response - Page 2:
2025-06-22 00:00:23,178 - INFO - 第 2 页获取到 100 条记录
2025-06-22 00:00:23,178 - INFO - Request Parameters - Page 3:
2025-06-22 00:00:23,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:23,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:23,694 - INFO - Response - Page 3:
2025-06-22 00:00:23,897 - INFO - 第 3 页获取到 100 条记录
2025-06-22 00:00:23,897 - INFO - Request Parameters - Page 4:
2025-06-22 00:00:23,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:23,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:24,381 - INFO - Response - Page 4:
2025-06-22 00:00:24,584 - INFO - 第 4 页获取到 100 条记录
2025-06-22 00:00:24,584 - INFO - Request Parameters - Page 5:
2025-06-22 00:00:24,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:24,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:25,038 - INFO - Response - Page 5:
2025-06-22 00:00:25,241 - INFO - 第 5 页获取到 100 条记录
2025-06-22 00:00:25,241 - INFO - Request Parameters - Page 6:
2025-06-22 00:00:25,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:25,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:25,694 - INFO - Response - Page 6:
2025-06-22 00:00:25,897 - INFO - 第 6 页获取到 100 条记录
2025-06-22 00:00:25,897 - INFO - Request Parameters - Page 7:
2025-06-22 00:00:25,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:25,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:26,288 - INFO - Response - Page 7:
2025-06-22 00:00:26,491 - INFO - 第 7 页获取到 56 条记录
2025-06-22 00:00:26,491 - INFO - 查询完成，共获取到 656 条记录
2025-06-22 00:00:26,491 - INFO - 获取到 656 条表单数据
2025-06-22 00:00:26,491 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-22 00:00:26,506 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 00:00:26,506 - INFO - 开始处理日期: 2025-05
2025-06-22 00:00:26,506 - INFO - Request Parameters - Page 1:
2025-06-22 00:00:26,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:26,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:27,022 - INFO - Response - Page 1:
2025-06-22 00:00:27,225 - INFO - 第 1 页获取到 100 条记录
2025-06-22 00:00:27,225 - INFO - Request Parameters - Page 2:
2025-06-22 00:00:27,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:27,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:27,678 - INFO - Response - Page 2:
2025-06-22 00:00:27,881 - INFO - 第 2 页获取到 100 条记录
2025-06-22 00:00:27,881 - INFO - Request Parameters - Page 3:
2025-06-22 00:00:27,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:27,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:28,366 - INFO - Response - Page 3:
2025-06-22 00:00:28,569 - INFO - 第 3 页获取到 100 条记录
2025-06-22 00:00:28,569 - INFO - Request Parameters - Page 4:
2025-06-22 00:00:28,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:28,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:29,038 - INFO - Response - Page 4:
2025-06-22 00:00:29,241 - INFO - 第 4 页获取到 100 条记录
2025-06-22 00:00:29,241 - INFO - Request Parameters - Page 5:
2025-06-22 00:00:29,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:29,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:29,663 - INFO - Response - Page 5:
2025-06-22 00:00:29,866 - INFO - 第 5 页获取到 100 条记录
2025-06-22 00:00:29,866 - INFO - Request Parameters - Page 6:
2025-06-22 00:00:29,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:29,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:30,413 - INFO - Response - Page 6:
2025-06-22 00:00:30,616 - INFO - 第 6 页获取到 100 条记录
2025-06-22 00:00:30,616 - INFO - Request Parameters - Page 7:
2025-06-22 00:00:30,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:30,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:31,069 - INFO - Response - Page 7:
2025-06-22 00:00:31,272 - INFO - 第 7 页获取到 65 条记录
2025-06-22 00:00:31,272 - INFO - 查询完成，共获取到 665 条记录
2025-06-22 00:00:31,272 - INFO - 获取到 665 条表单数据
2025-06-22 00:00:31,272 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-22 00:00:31,288 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 00:00:31,288 - INFO - 开始处理日期: 2025-06
2025-06-22 00:00:31,288 - INFO - Request Parameters - Page 1:
2025-06-22 00:00:31,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:31,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:31,803 - INFO - Response - Page 1:
2025-06-22 00:00:32,006 - INFO - 第 1 页获取到 100 条记录
2025-06-22 00:00:32,006 - INFO - Request Parameters - Page 2:
2025-06-22 00:00:32,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:32,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:32,491 - INFO - Response - Page 2:
2025-06-22 00:00:32,694 - INFO - 第 2 页获取到 100 条记录
2025-06-22 00:00:32,694 - INFO - Request Parameters - Page 3:
2025-06-22 00:00:32,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:32,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:33,225 - INFO - Response - Page 3:
2025-06-22 00:00:33,428 - INFO - 第 3 页获取到 100 条记录
2025-06-22 00:00:33,428 - INFO - Request Parameters - Page 4:
2025-06-22 00:00:33,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:33,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:33,881 - INFO - Response - Page 4:
2025-06-22 00:00:34,084 - INFO - 第 4 页获取到 100 条记录
2025-06-22 00:00:34,084 - INFO - Request Parameters - Page 5:
2025-06-22 00:00:34,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:34,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:34,538 - INFO - Response - Page 5:
2025-06-22 00:00:34,741 - INFO - 第 5 页获取到 100 条记录
2025-06-22 00:00:34,741 - INFO - Request Parameters - Page 6:
2025-06-22 00:00:34,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:34,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:35,209 - INFO - Response - Page 6:
2025-06-22 00:00:35,413 - INFO - 第 6 页获取到 100 条记录
2025-06-22 00:00:35,413 - INFO - Request Parameters - Page 7:
2025-06-22 00:00:35,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 00:00:35,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 00:00:35,756 - INFO - Response - Page 7:
2025-06-22 00:00:35,959 - INFO - 第 7 页获取到 23 条记录
2025-06-22 00:00:35,959 - INFO - 查询完成，共获取到 623 条记录
2025-06-22 00:00:35,959 - INFO - 获取到 623 条表单数据
2025-06-22 00:00:35,959 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-22 00:00:35,959 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-22 00:00:36,428 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-22 00:00:36,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38588.4, 'new_value': 43949.6}, {'field': 'total_amount', 'old_value': 38588.4, 'new_value': 43949.6}, {'field': 'order_count', 'old_value': 387, 'new_value': 407}]
2025-06-22 00:00:36,428 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-22 00:00:36,881 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-22 00:00:36,881 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61286.0, 'new_value': 65707.0}, {'field': 'offline_amount', 'old_value': 83195.0, 'new_value': 86318.0}, {'field': 'total_amount', 'old_value': 144481.0, 'new_value': 152025.0}, {'field': 'order_count', 'old_value': 3048, 'new_value': 3213}]
2025-06-22 00:00:36,881 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-22 00:00:37,303 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-22 00:00:37,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81351.46, 'new_value': 88361.61}, {'field': 'total_amount', 'old_value': 82682.91, 'new_value': 89693.06}, {'field': 'order_count', 'old_value': 1926, 'new_value': 2086}]
2025-06-22 00:00:37,303 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-22 00:00:37,772 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-22 00:00:37,772 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1144564.38, 'new_value': 1213968.67}, {'field': 'total_amount', 'old_value': 1144564.38, 'new_value': 1213968.67}, {'field': 'order_count', 'old_value': 12709, 'new_value': 13281}]
2025-06-22 00:00:37,772 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-22 00:00:38,209 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-22 00:00:38,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4696.0, 'new_value': 4795.0}, {'field': 'total_amount', 'old_value': 4696.0, 'new_value': 4795.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-06-22 00:00:38,209 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-22 00:00:38,647 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-22 00:00:38,647 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7459.25, 'new_value': 8181.01}, {'field': 'offline_amount', 'old_value': 153938.01, 'new_value': 160517.85}, {'field': 'total_amount', 'old_value': 161397.26, 'new_value': 168698.86}, {'field': 'order_count', 'old_value': 939, 'new_value': 983}]
2025-06-22 00:00:38,647 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-22 00:00:39,069 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-22 00:00:39,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8151.0, 'new_value': 9347.0}, {'field': 'total_amount', 'old_value': 8151.0, 'new_value': 9347.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 20}]
2025-06-22 00:00:39,069 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-22 00:00:39,459 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-22 00:00:39,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17770.3, 'new_value': 18629.3}, {'field': 'total_amount', 'old_value': 17770.3, 'new_value': 18629.3}, {'field': 'order_count', 'old_value': 95, 'new_value': 98}]
2025-06-22 00:00:39,459 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-22 00:00:39,944 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-22 00:00:39,944 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37032.07, 'new_value': 39123.53}, {'field': 'offline_amount', 'old_value': 550260.24, 'new_value': 575982.99}, {'field': 'total_amount', 'old_value': 587292.31, 'new_value': 615106.52}, {'field': 'order_count', 'old_value': 2475, 'new_value': 2594}]
2025-06-22 00:00:39,944 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-22 00:00:40,381 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-22 00:00:40,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42700.75, 'new_value': 45935.92}, {'field': 'total_amount', 'old_value': 45962.9, 'new_value': 49198.07}, {'field': 'order_count', 'old_value': 162, 'new_value': 174}]
2025-06-22 00:00:40,381 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-22 00:00:40,897 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-22 00:00:40,897 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18546.0, 'new_value': 20283.0}, {'field': 'total_amount', 'old_value': 18546.0, 'new_value': 20283.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 83}]
2025-06-22 00:00:40,897 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-22 00:00:41,459 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-22 00:00:41,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6372.0, 'new_value': 8721.9}, {'field': 'total_amount', 'old_value': 17102.67, 'new_value': 19452.57}, {'field': 'order_count', 'old_value': 80, 'new_value': 89}]
2025-06-22 00:00:41,459 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-22 00:00:41,897 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-22 00:00:41,897 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17258.1, 'new_value': 18966.5}, {'field': 'offline_amount', 'old_value': 84359.7, 'new_value': 91733.7}, {'field': 'total_amount', 'old_value': 101617.8, 'new_value': 110700.2}, {'field': 'order_count', 'old_value': 119, 'new_value': 135}]
2025-06-22 00:00:41,897 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-22 00:00:42,366 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-22 00:00:42,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19280.0, 'new_value': 20380.0}, {'field': 'total_amount', 'old_value': 19340.0, 'new_value': 20440.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 83}]
2025-06-22 00:00:42,366 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-22 00:00:42,772 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-22 00:00:42,772 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1216.37, 'new_value': 1316.37}, {'field': 'offline_amount', 'old_value': 14268.36, 'new_value': 15408.82}, {'field': 'total_amount', 'old_value': 15484.73, 'new_value': 16725.19}, {'field': 'order_count', 'old_value': 295, 'new_value': 320}]
2025-06-22 00:00:42,772 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-22 00:00:43,287 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-22 00:00:43,287 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-06-22 00:00:43,287 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-22 00:00:43,694 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-22 00:00:43,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67980.9, 'new_value': 74140.5}, {'field': 'total_amount', 'old_value': 67980.9, 'new_value': 74140.5}, {'field': 'order_count', 'old_value': 233, 'new_value': 251}]
2025-06-22 00:00:43,694 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-22 00:00:44,131 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-22 00:00:44,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60333.02, 'new_value': 65039.5}, {'field': 'total_amount', 'old_value': 60333.02, 'new_value': 65039.5}, {'field': 'order_count', 'old_value': 1908, 'new_value': 2074}]
2025-06-22 00:00:44,147 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-22 00:00:44,631 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-22 00:00:44,631 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40576.0, 'new_value': 41401.0}, {'field': 'total_amount', 'old_value': 40576.0, 'new_value': 41401.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 100}]
2025-06-22 00:00:44,631 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-22 00:00:45,022 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-22 00:00:45,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23819.7, 'new_value': 25543.7}, {'field': 'total_amount', 'old_value': 23819.7, 'new_value': 25543.7}, {'field': 'order_count', 'old_value': 146, 'new_value': 153}]
2025-06-22 00:00:45,022 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-22 00:00:45,475 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-22 00:00:45,475 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120826.0, 'new_value': 132186.0}, {'field': 'total_amount', 'old_value': 120826.0, 'new_value': 132186.0}, {'field': 'order_count', 'old_value': 468, 'new_value': 501}]
2025-06-22 00:00:45,475 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-22 00:00:45,897 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-22 00:00:45,897 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77811.92, 'new_value': 83307.91}, {'field': 'offline_amount', 'old_value': 39278.0, 'new_value': 42413.13}, {'field': 'total_amount', 'old_value': 117089.92, 'new_value': 125721.04}, {'field': 'order_count', 'old_value': 6822, 'new_value': 7301}]
2025-06-22 00:00:45,897 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-22 00:00:46,350 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-22 00:00:46,350 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45803.19, 'new_value': 48114.05}, {'field': 'offline_amount', 'old_value': 61080.44, 'new_value': 66871.2}, {'field': 'total_amount', 'old_value': 106883.63, 'new_value': 114985.25}, {'field': 'order_count', 'old_value': 4191, 'new_value': 4487}]
2025-06-22 00:00:46,350 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-22 00:00:46,787 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-22 00:00:46,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69015.48, 'new_value': 75419.84}, {'field': 'total_amount', 'old_value': 69015.48, 'new_value': 75419.84}, {'field': 'order_count', 'old_value': 2561, 'new_value': 2813}]
2025-06-22 00:00:46,787 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-22 00:00:47,241 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-22 00:00:47,241 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 306843.18, 'new_value': 331964.28}, {'field': 'total_amount', 'old_value': 306843.18, 'new_value': 331964.28}, {'field': 'order_count', 'old_value': 1686, 'new_value': 1785}]
2025-06-22 00:00:47,241 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-22 00:00:47,662 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-22 00:00:47,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17739.2, 'new_value': 19166.19}, {'field': 'total_amount', 'old_value': 18239.2, 'new_value': 19666.19}, {'field': 'order_count', 'old_value': 89, 'new_value': 97}]
2025-06-22 00:00:47,662 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-22 00:00:48,037 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-22 00:00:48,037 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13266.49, 'new_value': 14185.36}, {'field': 'offline_amount', 'old_value': 8690.79, 'new_value': 9392.99}, {'field': 'total_amount', 'old_value': 21957.28, 'new_value': 23578.35}, {'field': 'order_count', 'old_value': 1761, 'new_value': 1893}]
2025-06-22 00:00:48,037 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-22 00:00:48,412 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-22 00:00:48,412 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227259.0, 'new_value': 238153.0}, {'field': 'total_amount', 'old_value': 262259.0, 'new_value': 273153.0}, {'field': 'order_count', 'old_value': 6488, 'new_value': 6636}]
2025-06-22 00:00:48,412 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-22 00:00:48,787 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-22 00:00:48,787 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33865.77, 'new_value': 34936.86}, {'field': 'offline_amount', 'old_value': 209261.08, 'new_value': 229035.95}, {'field': 'total_amount', 'old_value': 243126.85, 'new_value': 263972.81}, {'field': 'order_count', 'old_value': 34632, 'new_value': 35164}]
2025-06-22 00:00:48,787 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-22 00:00:49,209 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-22 00:00:49,209 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62451.8, 'new_value': 64968.04}, {'field': 'offline_amount', 'old_value': 253048.87, 'new_value': 277607.17}, {'field': 'total_amount', 'old_value': 315500.67, 'new_value': 342575.21}, {'field': 'order_count', 'old_value': 3655, 'new_value': 3953}]
2025-06-22 00:00:49,209 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-22 00:00:49,584 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-22 00:00:49,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 372133.51, 'new_value': 400985.82}, {'field': 'total_amount', 'old_value': 372133.51, 'new_value': 400985.82}, {'field': 'order_count', 'old_value': 5443, 'new_value': 5807}]
2025-06-22 00:00:49,584 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-22 00:00:49,944 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-22 00:00:49,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152041.11, 'new_value': 164654.52}, {'field': 'total_amount', 'old_value': 203765.2, 'new_value': 216378.61}, {'field': 'order_count', 'old_value': 8953, 'new_value': 9546}]
2025-06-22 00:00:49,944 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-22 00:00:50,381 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-22 00:00:50,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18917.0, 'new_value': 20340.0}, {'field': 'total_amount', 'old_value': 18917.0, 'new_value': 20340.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 86}]
2025-06-22 00:00:50,381 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-22 00:00:50,850 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-22 00:00:50,850 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89016.48, 'new_value': 94932.99}, {'field': 'offline_amount', 'old_value': 184834.82, 'new_value': 199124.07}, {'field': 'total_amount', 'old_value': 273851.3, 'new_value': 294057.06}, {'field': 'order_count', 'old_value': 9853, 'new_value': 10532}]
2025-06-22 00:00:50,850 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-22 00:00:51,256 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-22 00:00:51,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 584956.13, 'new_value': 629505.08}, {'field': 'total_amount', 'old_value': 584956.13, 'new_value': 629505.08}, {'field': 'order_count', 'old_value': 3915, 'new_value': 4219}]
2025-06-22 00:00:51,256 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-22 00:00:51,584 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-22 00:00:51,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68065.0, 'new_value': 71963.0}, {'field': 'total_amount', 'old_value': 68065.0, 'new_value': 71963.0}, {'field': 'order_count', 'old_value': 549, 'new_value': 579}]
2025-06-22 00:00:51,584 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-22 00:00:52,022 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-22 00:00:52,022 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 238804.18, 'new_value': 253196.14}, {'field': 'offline_amount', 'old_value': 19188.29, 'new_value': 19924.99}, {'field': 'total_amount', 'old_value': 257992.47, 'new_value': 273121.13}, {'field': 'order_count', 'old_value': 9825, 'new_value': 10380}]
2025-06-22 00:00:52,022 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-22 00:00:52,381 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-22 00:00:52,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125607.85, 'new_value': 135453.53}, {'field': 'total_amount', 'old_value': 154802.86, 'new_value': 164648.54}, {'field': 'order_count', 'old_value': 8772, 'new_value': 9301}]
2025-06-22 00:00:52,381 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-22 00:00:52,944 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-22 00:00:52,944 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57611.93, 'new_value': 60377.27}, {'field': 'offline_amount', 'old_value': 72783.21, 'new_value': 79014.87}, {'field': 'total_amount', 'old_value': 130395.14, 'new_value': 139392.14}, {'field': 'order_count', 'old_value': 1516, 'new_value': 1622}]
2025-06-22 00:00:52,959 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-22 00:00:53,381 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-22 00:00:53,381 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8956.94, 'new_value': 9262.88}, {'field': 'offline_amount', 'old_value': 62138.95, 'new_value': 66614.34}, {'field': 'total_amount', 'old_value': 71095.89, 'new_value': 75877.22}, {'field': 'order_count', 'old_value': 2193, 'new_value': 2360}]
2025-06-22 00:00:53,381 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA1
2025-06-22 00:00:53,834 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA1
2025-06-22 00:00:53,834 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50140.0, 'new_value': 71500.0}, {'field': 'total_amount', 'old_value': 50140.0, 'new_value': 71500.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 15}]
2025-06-22 00:00:53,834 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-22 00:00:54,256 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-22 00:00:54,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33070.4, 'new_value': 36355.9}, {'field': 'total_amount', 'old_value': 33070.4, 'new_value': 36355.9}, {'field': 'order_count', 'old_value': 280, 'new_value': 302}]
2025-06-22 00:00:54,256 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-22 00:00:54,678 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-22 00:00:54,678 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35419.0, 'new_value': 37998.0}, {'field': 'offline_amount', 'old_value': 306158.0, 'new_value': 309787.2}, {'field': 'total_amount', 'old_value': 341577.0, 'new_value': 347785.2}, {'field': 'order_count', 'old_value': 94, 'new_value': 100}]
2025-06-22 00:00:54,678 - INFO - 开始更新记录 - 表单实例ID: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-22 00:00:55,131 - INFO - 更新表单数据成功: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-22 00:00:55,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29839.32, 'new_value': 35581.32}, {'field': 'total_amount', 'old_value': 29839.32, 'new_value': 35581.32}, {'field': 'order_count', 'old_value': 179, 'new_value': 214}]
2025-06-22 00:00:55,131 - INFO - 日期 2025-06 处理完成 - 更新: 44 条，插入: 0 条，错误: 0 条
2025-06-22 00:00:55,131 - INFO - 数据同步完成！更新: 44 条，插入: 0 条，错误: 0 条
2025-06-22 00:00:55,131 - INFO - =================同步完成====================
2025-06-22 03:00:02,636 - INFO - =================使用默认全量同步=============
2025-06-22 03:00:04,370 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-22 03:00:04,370 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-22 03:00:04,402 - INFO - 开始处理日期: 2025-01
2025-06-22 03:00:04,402 - INFO - Request Parameters - Page 1:
2025-06-22 03:00:04,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:04,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:05,980 - INFO - Response - Page 1:
2025-06-22 03:00:06,183 - INFO - 第 1 页获取到 100 条记录
2025-06-22 03:00:06,183 - INFO - Request Parameters - Page 2:
2025-06-22 03:00:06,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:06,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:06,933 - INFO - Response - Page 2:
2025-06-22 03:00:07,136 - INFO - 第 2 页获取到 100 条记录
2025-06-22 03:00:07,136 - INFO - Request Parameters - Page 3:
2025-06-22 03:00:07,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:07,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:07,683 - INFO - Response - Page 3:
2025-06-22 03:00:07,886 - INFO - 第 3 页获取到 100 条记录
2025-06-22 03:00:07,886 - INFO - Request Parameters - Page 4:
2025-06-22 03:00:07,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:07,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:08,417 - INFO - Response - Page 4:
2025-06-22 03:00:08,620 - INFO - 第 4 页获取到 100 条记录
2025-06-22 03:00:08,620 - INFO - Request Parameters - Page 5:
2025-06-22 03:00:08,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:08,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:09,198 - INFO - Response - Page 5:
2025-06-22 03:00:09,402 - INFO - 第 5 页获取到 100 条记录
2025-06-22 03:00:09,402 - INFO - Request Parameters - Page 6:
2025-06-22 03:00:09,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:09,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:09,855 - INFO - Response - Page 6:
2025-06-22 03:00:10,058 - INFO - 第 6 页获取到 100 条记录
2025-06-22 03:00:10,058 - INFO - Request Parameters - Page 7:
2025-06-22 03:00:10,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:10,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:10,495 - INFO - Response - Page 7:
2025-06-22 03:00:10,699 - INFO - 第 7 页获取到 82 条记录
2025-06-22 03:00:10,699 - INFO - 查询完成，共获取到 682 条记录
2025-06-22 03:00:10,699 - INFO - 获取到 682 条表单数据
2025-06-22 03:00:10,699 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-22 03:00:10,714 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 03:00:10,714 - INFO - 开始处理日期: 2025-02
2025-06-22 03:00:10,714 - INFO - Request Parameters - Page 1:
2025-06-22 03:00:10,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:10,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:11,261 - INFO - Response - Page 1:
2025-06-22 03:00:11,464 - INFO - 第 1 页获取到 100 条记录
2025-06-22 03:00:11,464 - INFO - Request Parameters - Page 2:
2025-06-22 03:00:11,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:11,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:11,964 - INFO - Response - Page 2:
2025-06-22 03:00:12,167 - INFO - 第 2 页获取到 100 条记录
2025-06-22 03:00:12,167 - INFO - Request Parameters - Page 3:
2025-06-22 03:00:12,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:12,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:12,745 - INFO - Response - Page 3:
2025-06-22 03:00:12,948 - INFO - 第 3 页获取到 100 条记录
2025-06-22 03:00:12,948 - INFO - Request Parameters - Page 4:
2025-06-22 03:00:12,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:12,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:13,448 - INFO - Response - Page 4:
2025-06-22 03:00:13,652 - INFO - 第 4 页获取到 100 条记录
2025-06-22 03:00:13,652 - INFO - Request Parameters - Page 5:
2025-06-22 03:00:13,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:13,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:14,105 - INFO - Response - Page 5:
2025-06-22 03:00:14,308 - INFO - 第 5 页获取到 100 条记录
2025-06-22 03:00:14,308 - INFO - Request Parameters - Page 6:
2025-06-22 03:00:14,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:14,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:14,808 - INFO - Response - Page 6:
2025-06-22 03:00:15,011 - INFO - 第 6 页获取到 100 条记录
2025-06-22 03:00:15,011 - INFO - Request Parameters - Page 7:
2025-06-22 03:00:15,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:15,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:15,464 - INFO - Response - Page 7:
2025-06-22 03:00:15,683 - INFO - 第 7 页获取到 70 条记录
2025-06-22 03:00:15,683 - INFO - 查询完成，共获取到 670 条记录
2025-06-22 03:00:15,683 - INFO - 获取到 670 条表单数据
2025-06-22 03:00:15,683 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-22 03:00:15,698 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 03:00:15,698 - INFO - 开始处理日期: 2025-03
2025-06-22 03:00:15,698 - INFO - Request Parameters - Page 1:
2025-06-22 03:00:15,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:15,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:16,214 - INFO - Response - Page 1:
2025-06-22 03:00:16,417 - INFO - 第 1 页获取到 100 条记录
2025-06-22 03:00:16,417 - INFO - Request Parameters - Page 2:
2025-06-22 03:00:16,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:16,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:16,902 - INFO - Response - Page 2:
2025-06-22 03:00:17,105 - INFO - 第 2 页获取到 100 条记录
2025-06-22 03:00:17,105 - INFO - Request Parameters - Page 3:
2025-06-22 03:00:17,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:17,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:17,636 - INFO - Response - Page 3:
2025-06-22 03:00:17,839 - INFO - 第 3 页获取到 100 条记录
2025-06-22 03:00:17,839 - INFO - Request Parameters - Page 4:
2025-06-22 03:00:17,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:17,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:18,323 - INFO - Response - Page 4:
2025-06-22 03:00:18,527 - INFO - 第 4 页获取到 100 条记录
2025-06-22 03:00:18,527 - INFO - Request Parameters - Page 5:
2025-06-22 03:00:18,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:18,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:19,073 - INFO - Response - Page 5:
2025-06-22 03:00:19,277 - INFO - 第 5 页获取到 100 条记录
2025-06-22 03:00:19,277 - INFO - Request Parameters - Page 6:
2025-06-22 03:00:19,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:19,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:19,730 - INFO - Response - Page 6:
2025-06-22 03:00:19,933 - INFO - 第 6 页获取到 100 条记录
2025-06-22 03:00:19,933 - INFO - Request Parameters - Page 7:
2025-06-22 03:00:19,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:19,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:20,464 - INFO - Response - Page 7:
2025-06-22 03:00:20,667 - INFO - 第 7 页获取到 61 条记录
2025-06-22 03:00:20,667 - INFO - 查询完成，共获取到 661 条记录
2025-06-22 03:00:20,667 - INFO - 获取到 661 条表单数据
2025-06-22 03:00:20,667 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-22 03:00:20,683 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 03:00:20,683 - INFO - 开始处理日期: 2025-04
2025-06-22 03:00:20,683 - INFO - Request Parameters - Page 1:
2025-06-22 03:00:20,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:20,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:21,136 - INFO - Response - Page 1:
2025-06-22 03:00:21,339 - INFO - 第 1 页获取到 100 条记录
2025-06-22 03:00:21,339 - INFO - Request Parameters - Page 2:
2025-06-22 03:00:21,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:21,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:21,917 - INFO - Response - Page 2:
2025-06-22 03:00:22,120 - INFO - 第 2 页获取到 100 条记录
2025-06-22 03:00:22,120 - INFO - Request Parameters - Page 3:
2025-06-22 03:00:22,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:22,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:22,620 - INFO - Response - Page 3:
2025-06-22 03:00:22,823 - INFO - 第 3 页获取到 100 条记录
2025-06-22 03:00:22,823 - INFO - Request Parameters - Page 4:
2025-06-22 03:00:22,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:22,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:23,402 - INFO - Response - Page 4:
2025-06-22 03:00:23,605 - INFO - 第 4 页获取到 100 条记录
2025-06-22 03:00:23,605 - INFO - Request Parameters - Page 5:
2025-06-22 03:00:23,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:23,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:24,105 - INFO - Response - Page 5:
2025-06-22 03:00:24,308 - INFO - 第 5 页获取到 100 条记录
2025-06-22 03:00:24,308 - INFO - Request Parameters - Page 6:
2025-06-22 03:00:24,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:24,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:24,761 - INFO - Response - Page 6:
2025-06-22 03:00:24,964 - INFO - 第 6 页获取到 100 条记录
2025-06-22 03:00:24,964 - INFO - Request Parameters - Page 7:
2025-06-22 03:00:24,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:24,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:25,433 - INFO - Response - Page 7:
2025-06-22 03:00:25,636 - INFO - 第 7 页获取到 56 条记录
2025-06-22 03:00:25,636 - INFO - 查询完成，共获取到 656 条记录
2025-06-22 03:00:25,636 - INFO - 获取到 656 条表单数据
2025-06-22 03:00:25,636 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-22 03:00:25,652 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 03:00:25,652 - INFO - 开始处理日期: 2025-05
2025-06-22 03:00:25,652 - INFO - Request Parameters - Page 1:
2025-06-22 03:00:25,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:25,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:26,198 - INFO - Response - Page 1:
2025-06-22 03:00:26,402 - INFO - 第 1 页获取到 100 条记录
2025-06-22 03:00:26,402 - INFO - Request Parameters - Page 2:
2025-06-22 03:00:26,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:26,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:26,855 - INFO - Response - Page 2:
2025-06-22 03:00:27,058 - INFO - 第 2 页获取到 100 条记录
2025-06-22 03:00:27,058 - INFO - Request Parameters - Page 3:
2025-06-22 03:00:27,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:27,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:27,620 - INFO - Response - Page 3:
2025-06-22 03:00:27,823 - INFO - 第 3 页获取到 100 条记录
2025-06-22 03:00:27,823 - INFO - Request Parameters - Page 4:
2025-06-22 03:00:27,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:27,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:28,323 - INFO - Response - Page 4:
2025-06-22 03:00:28,526 - INFO - 第 4 页获取到 100 条记录
2025-06-22 03:00:28,526 - INFO - Request Parameters - Page 5:
2025-06-22 03:00:28,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:28,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:29,027 - INFO - Response - Page 5:
2025-06-22 03:00:29,230 - INFO - 第 5 页获取到 100 条记录
2025-06-22 03:00:29,230 - INFO - Request Parameters - Page 6:
2025-06-22 03:00:29,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:29,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:29,714 - INFO - Response - Page 6:
2025-06-22 03:00:29,917 - INFO - 第 6 页获取到 100 条记录
2025-06-22 03:00:29,917 - INFO - Request Parameters - Page 7:
2025-06-22 03:00:29,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:29,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:30,355 - INFO - Response - Page 7:
2025-06-22 03:00:30,558 - INFO - 第 7 页获取到 65 条记录
2025-06-22 03:00:30,558 - INFO - 查询完成，共获取到 665 条记录
2025-06-22 03:00:30,558 - INFO - 获取到 665 条表单数据
2025-06-22 03:00:30,558 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-22 03:00:30,573 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 03:00:30,573 - INFO - 开始处理日期: 2025-06
2025-06-22 03:00:30,573 - INFO - Request Parameters - Page 1:
2025-06-22 03:00:30,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:30,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:31,058 - INFO - Response - Page 1:
2025-06-22 03:00:31,261 - INFO - 第 1 页获取到 100 条记录
2025-06-22 03:00:31,261 - INFO - Request Parameters - Page 2:
2025-06-22 03:00:31,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:31,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:31,776 - INFO - Response - Page 2:
2025-06-22 03:00:31,980 - INFO - 第 2 页获取到 100 条记录
2025-06-22 03:00:31,980 - INFO - Request Parameters - Page 3:
2025-06-22 03:00:31,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:31,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:32,511 - INFO - Response - Page 3:
2025-06-22 03:00:32,714 - INFO - 第 3 页获取到 100 条记录
2025-06-22 03:00:32,714 - INFO - Request Parameters - Page 4:
2025-06-22 03:00:32,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:32,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:33,151 - INFO - Response - Page 4:
2025-06-22 03:00:33,355 - INFO - 第 4 页获取到 100 条记录
2025-06-22 03:00:33,355 - INFO - Request Parameters - Page 5:
2025-06-22 03:00:33,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:33,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:33,839 - INFO - Response - Page 5:
2025-06-22 03:00:34,042 - INFO - 第 5 页获取到 100 条记录
2025-06-22 03:00:34,042 - INFO - Request Parameters - Page 6:
2025-06-22 03:00:34,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:34,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:34,573 - INFO - Response - Page 6:
2025-06-22 03:00:34,776 - INFO - 第 6 页获取到 100 条记录
2025-06-22 03:00:34,776 - INFO - Request Parameters - Page 7:
2025-06-22 03:00:34,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 03:00:34,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 03:00:35,105 - INFO - Response - Page 7:
2025-06-22 03:00:35,308 - INFO - 第 7 页获取到 23 条记录
2025-06-22 03:00:35,308 - INFO - 查询完成，共获取到 623 条记录
2025-06-22 03:00:35,308 - INFO - 获取到 623 条表单数据
2025-06-22 03:00:35,308 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-22 03:00:35,308 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-22 03:00:35,792 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-22 03:00:35,792 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137533.0, 'new_value': 148794.0}, {'field': 'total_amount', 'old_value': 137533.0, 'new_value': 148794.0}, {'field': 'order_count', 'old_value': 2208, 'new_value': 2404}]
2025-06-22 03:00:35,792 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-22 03:00:36,167 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-22 03:00:36,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120754.31, 'new_value': 128202.71}, {'field': 'total_amount', 'old_value': 146929.02, 'new_value': 154377.42}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-06-22 03:00:36,167 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-22 03:00:36,636 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-22 03:00:36,636 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 685837.0, 'new_value': 714331.0}, {'field': 'total_amount', 'old_value': 685837.0, 'new_value': 714331.0}, {'field': 'order_count', 'old_value': 146, 'new_value': 151}]
2025-06-22 03:00:36,636 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-22 03:00:37,042 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-22 03:00:37,042 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99524.83, 'new_value': 102726.03}, {'field': 'offline_amount', 'old_value': 885690.35, 'new_value': 943565.01}, {'field': 'total_amount', 'old_value': 985215.18, 'new_value': 1046291.04}, {'field': 'order_count', 'old_value': 8383, 'new_value': 8821}]
2025-06-22 03:00:37,042 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-22 03:00:37,464 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-22 03:00:37,464 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49294.63, 'new_value': 51319.76}, {'field': 'offline_amount', 'old_value': 71504.61, 'new_value': 76583.4}, {'field': 'total_amount', 'old_value': 120799.24, 'new_value': 127903.16}, {'field': 'order_count', 'old_value': 4052, 'new_value': 4285}]
2025-06-22 03:00:37,464 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPB
2025-06-22 03:00:37,886 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPB
2025-06-22 03:00:37,886 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39068.0, 'new_value': 39748.0}, {'field': 'total_amount', 'old_value': 39718.0, 'new_value': 40398.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-22 03:00:37,886 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-22 03:00:38,401 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-22 03:00:38,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 451451.0, 'new_value': 489868.0}, {'field': 'total_amount', 'old_value': 452639.0, 'new_value': 491056.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 158}]
2025-06-22 03:00:38,401 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-22 03:00:38,808 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-22 03:00:38,808 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 401019.0, 'new_value': 414230.0}, {'field': 'total_amount', 'old_value': 404935.0, 'new_value': 418146.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 59}]
2025-06-22 03:00:38,808 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-22 03:00:39,230 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-22 03:00:39,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155813.5, 'new_value': 168253.5}, {'field': 'total_amount', 'old_value': 155813.5, 'new_value': 168253.5}, {'field': 'order_count', 'old_value': 41, 'new_value': 45}]
2025-06-22 03:00:39,230 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-22 03:00:39,651 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-22 03:00:39,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25778.0, 'new_value': 26326.0}, {'field': 'total_amount', 'old_value': 26255.0, 'new_value': 26803.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 59}]
2025-06-22 03:00:39,651 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-22 03:00:40,042 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-22 03:00:40,042 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 271949.24, 'new_value': 296206.63}, {'field': 'total_amount', 'old_value': 271949.24, 'new_value': 296206.63}, {'field': 'order_count', 'old_value': 1352, 'new_value': 1473}]
2025-06-22 03:00:40,042 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-22 03:00:40,495 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-22 03:00:40,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66713.0, 'new_value': 71612.0}, {'field': 'total_amount', 'old_value': 71211.05, 'new_value': 76110.05}, {'field': 'order_count', 'old_value': 5604, 'new_value': 5605}]
2025-06-22 03:00:40,495 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-22 03:00:40,855 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-22 03:00:40,855 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135067.0, 'new_value': 142433.0}, {'field': 'total_amount', 'old_value': 195758.55, 'new_value': 203124.55}, {'field': 'order_count', 'old_value': 81, 'new_value': 85}]
2025-06-22 03:00:40,855 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-22 03:00:41,261 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-22 03:00:41,261 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7582.0, 'new_value': 8782.0}, {'field': 'offline_amount', 'old_value': 47298.0, 'new_value': 48815.0}, {'field': 'total_amount', 'old_value': 54880.0, 'new_value': 57597.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 55}]
2025-06-22 03:00:41,261 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-22 03:00:41,667 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-22 03:00:41,667 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44694.0, 'new_value': 50002.0}, {'field': 'total_amount', 'old_value': 44694.0, 'new_value': 50002.0}, {'field': 'order_count', 'old_value': 123, 'new_value': 133}]
2025-06-22 03:00:41,667 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-22 03:00:42,073 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-22 03:00:42,073 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 181212.53, 'new_value': 209548.53}, {'field': 'total_amount', 'old_value': 181212.53, 'new_value': 209548.53}, {'field': 'order_count', 'old_value': 108, 'new_value': 120}]
2025-06-22 03:00:42,073 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-22 03:00:42,464 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-22 03:00:42,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41033.0, 'new_value': 44268.0}, {'field': 'total_amount', 'old_value': 41033.0, 'new_value': 44268.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 73}]
2025-06-22 03:00:42,464 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-22 03:00:42,808 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-22 03:00:42,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41230.79, 'new_value': 43218.25}, {'field': 'offline_amount', 'old_value': 243411.71, 'new_value': 264476.61}, {'field': 'total_amount', 'old_value': 284642.5, 'new_value': 307694.86}, {'field': 'order_count', 'old_value': 2168, 'new_value': 2309}]
2025-06-22 03:00:42,808 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-22 03:00:43,261 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-22 03:00:43,261 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24770.72, 'new_value': 25614.22}, {'field': 'offline_amount', 'old_value': 209043.95, 'new_value': 222792.15}, {'field': 'total_amount', 'old_value': 233814.67, 'new_value': 248406.37}, {'field': 'order_count', 'old_value': 12979, 'new_value': 13704}]
2025-06-22 03:00:43,261 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-22 03:00:43,683 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-22 03:00:43,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234081.6, 'new_value': 251933.9}, {'field': 'total_amount', 'old_value': 234081.6, 'new_value': 251933.9}, {'field': 'order_count', 'old_value': 2345, 'new_value': 2536}]
2025-06-22 03:00:43,683 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-22 03:00:44,136 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-22 03:00:44,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49274.7, 'new_value': 53884.5}, {'field': 'total_amount', 'old_value': 55900.1, 'new_value': 60509.9}, {'field': 'order_count', 'old_value': 138, 'new_value': 148}]
2025-06-22 03:00:44,136 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8R
2025-06-22 03:00:44,526 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8R
2025-06-22 03:00:44,526 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3986200.0, 'new_value': 4346100.0}, {'field': 'total_amount', 'old_value': 3986200.0, 'new_value': 4346100.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-22 03:00:44,526 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-22 03:00:44,964 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-22 03:00:44,964 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 702589.0, 'new_value': 728560.0}, {'field': 'total_amount', 'old_value': 775181.0, 'new_value': 801152.0}, {'field': 'order_count', 'old_value': 802, 'new_value': 837}]
2025-06-22 03:00:44,964 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJR
2025-06-22 03:00:45,417 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJR
2025-06-22 03:00:45,417 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50012.0, 'new_value': 53145.0}, {'field': 'total_amount', 'old_value': 50012.0, 'new_value': 53145.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-06-22 03:00:45,417 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-22 03:00:45,808 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-22 03:00:45,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80365.68, 'new_value': 86226.63}, {'field': 'total_amount', 'old_value': 80365.68, 'new_value': 86226.63}, {'field': 'order_count', 'old_value': 6914, 'new_value': 7396}]
2025-06-22 03:00:45,808 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-22 03:00:46,276 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-22 03:00:46,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 282451.77, 'new_value': 311602.6}, {'field': 'total_amount', 'old_value': 361660.86, 'new_value': 390811.69}, {'field': 'order_count', 'old_value': 1168, 'new_value': 1264}]
2025-06-22 03:00:46,276 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-22 03:00:46,730 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-22 03:00:46,730 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32317.57, 'new_value': 33954.83}, {'field': 'offline_amount', 'old_value': 15861.85, 'new_value': 16568.85}, {'field': 'total_amount', 'old_value': 48179.42, 'new_value': 50523.68}, {'field': 'order_count', 'old_value': 2070, 'new_value': 2213}]
2025-06-22 03:00:46,730 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-22 03:00:47,120 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-22 03:00:47,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 492255.45, 'new_value': 535370.24}, {'field': 'total_amount', 'old_value': 492255.45, 'new_value': 535370.24}, {'field': 'order_count', 'old_value': 8204, 'new_value': 9096}]
2025-06-22 03:00:47,120 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-22 03:00:47,604 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-22 03:00:47,604 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64693.07, 'new_value': 68903.07}, {'field': 'total_amount', 'old_value': 64693.07, 'new_value': 68903.07}, {'field': 'order_count', 'old_value': 322, 'new_value': 346}]
2025-06-22 03:00:47,604 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-22 03:00:47,964 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-22 03:00:47,964 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280374.0, 'new_value': 323248.0}, {'field': 'total_amount', 'old_value': 280374.0, 'new_value': 323248.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 82}]
2025-06-22 03:00:47,964 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-22 03:00:48,370 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-22 03:00:48,370 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21533.34, 'new_value': 22443.96}, {'field': 'offline_amount', 'old_value': 192222.97, 'new_value': 211355.26}, {'field': 'total_amount', 'old_value': 213756.31, 'new_value': 233799.22}, {'field': 'order_count', 'old_value': 1018, 'new_value': 1117}]
2025-06-22 03:00:48,370 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-22 03:00:48,808 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-22 03:00:48,808 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110155.51, 'new_value': 120325.81}, {'field': 'total_amount', 'old_value': 110155.51, 'new_value': 120325.81}, {'field': 'order_count', 'old_value': 259, 'new_value': 277}]
2025-06-22 03:00:48,808 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-22 03:00:49,229 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-22 03:00:49,229 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 209443.29, 'new_value': 226653.44}, {'field': 'offline_amount', 'old_value': 59127.86, 'new_value': 62987.66}, {'field': 'total_amount', 'old_value': 268571.15, 'new_value': 289641.1}, {'field': 'order_count', 'old_value': 1667, 'new_value': 1805}]
2025-06-22 03:00:49,229 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-22 03:00:49,636 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-22 03:00:49,636 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100213.0, 'new_value': 106081.0}, {'field': 'total_amount', 'old_value': 104780.0, 'new_value': 110648.0}, {'field': 'order_count', 'old_value': 4303, 'new_value': 4313}]
2025-06-22 03:00:49,636 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-22 03:00:50,198 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-22 03:00:50,198 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8196.54, 'new_value': 8664.54}, {'field': 'offline_amount', 'old_value': 23661.69, 'new_value': 26440.99}, {'field': 'total_amount', 'old_value': 31858.23, 'new_value': 35105.53}, {'field': 'order_count', 'old_value': 1106, 'new_value': 1224}]
2025-06-22 03:00:50,198 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-22 03:00:50,651 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-22 03:00:50,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43976.5, 'new_value': 47026.7}, {'field': 'offline_amount', 'old_value': 270914.51, 'new_value': 292914.61}, {'field': 'total_amount', 'old_value': 314891.01, 'new_value': 339941.31}, {'field': 'order_count', 'old_value': 1964, 'new_value': 2121}]
2025-06-22 03:00:50,651 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-22 03:00:51,073 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-22 03:00:51,073 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29032.96, 'new_value': 30994.46}, {'field': 'offline_amount', 'old_value': 27295.94, 'new_value': 29295.94}, {'field': 'total_amount', 'old_value': 56328.9, 'new_value': 60290.4}, {'field': 'order_count', 'old_value': 2633, 'new_value': 2819}]
2025-06-22 03:00:51,073 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-22 03:00:51,542 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-22 03:00:51,542 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62910.64, 'new_value': 67367.91}, {'field': 'offline_amount', 'old_value': 24909.15, 'new_value': 26902.07}, {'field': 'total_amount', 'old_value': 87819.79, 'new_value': 94269.98}, {'field': 'order_count', 'old_value': 5127, 'new_value': 5470}]
2025-06-22 03:00:51,542 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-22 03:00:52,011 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-22 03:00:52,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22083.0, 'new_value': 23706.0}, {'field': 'total_amount', 'old_value': 22083.0, 'new_value': 23706.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 141}]
2025-06-22 03:00:52,011 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-22 03:00:52,464 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-22 03:00:52,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 498406.0, 'new_value': 514397.0}, {'field': 'total_amount', 'old_value': 498406.0, 'new_value': 514397.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 52}]
2025-06-22 03:00:52,464 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-22 03:00:52,854 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-22 03:00:52,854 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59966.44, 'new_value': 64852.25}, {'field': 'offline_amount', 'old_value': 51302.13, 'new_value': 55142.17}, {'field': 'total_amount', 'old_value': 111268.57, 'new_value': 119994.42}, {'field': 'order_count', 'old_value': 5239, 'new_value': 5626}]
2025-06-22 03:00:52,854 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-22 03:00:53,323 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-22 03:00:53,323 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88373.4, 'new_value': 92256.62}, {'field': 'offline_amount', 'old_value': 176717.4, 'new_value': 191816.06}, {'field': 'total_amount', 'old_value': 265090.8, 'new_value': 284072.68}, {'field': 'order_count', 'old_value': 3584, 'new_value': 3792}]
2025-06-22 03:00:53,323 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-22 03:00:53,745 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-22 03:00:53,745 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4486.41, 'new_value': 5081.51}, {'field': 'total_amount', 'old_value': 22226.41, 'new_value': 22821.51}, {'field': 'order_count', 'old_value': 71, 'new_value': 80}]
2025-06-22 03:00:53,745 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-22 03:00:54,183 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-22 03:00:54,183 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9593.06, 'new_value': 9970.14}, {'field': 'offline_amount', 'old_value': 23825.32, 'new_value': 25564.02}, {'field': 'total_amount', 'old_value': 33418.38, 'new_value': 35534.16}, {'field': 'order_count', 'old_value': 1618, 'new_value': 1733}]
2025-06-22 03:00:54,183 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-22 03:00:54,589 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-22 03:00:54,604 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 172668.0, 'new_value': 185520.0}, {'field': 'total_amount', 'old_value': 172668.0, 'new_value': 185520.0}, {'field': 'order_count', 'old_value': 182, 'new_value': 195}]
2025-06-22 03:00:54,604 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-22 03:00:55,042 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-22 03:00:55,042 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67106.9, 'new_value': 88405.7}, {'field': 'offline_amount', 'old_value': 108141.2, 'new_value': 117707.9}, {'field': 'total_amount', 'old_value': 175248.1, 'new_value': 206113.6}, {'field': 'order_count', 'old_value': 3058, 'new_value': 3675}]
2025-06-22 03:00:55,042 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-22 03:00:55,526 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-22 03:00:55,526 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47443.09, 'new_value': 49630.55}, {'field': 'offline_amount', 'old_value': 170030.08, 'new_value': 184692.39}, {'field': 'total_amount', 'old_value': 217473.17, 'new_value': 234322.94}, {'field': 'order_count', 'old_value': 4950, 'new_value': 5255}]
2025-06-22 03:00:55,526 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-22 03:00:55,917 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-22 03:00:55,917 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180894.0, 'new_value': 195034.0}, {'field': 'total_amount', 'old_value': 180894.0, 'new_value': 195034.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 36}]
2025-06-22 03:00:55,917 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-22 03:00:56,354 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-22 03:00:56,354 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77876.7, 'new_value': 86164.7}, {'field': 'offline_amount', 'old_value': 43513.1, 'new_value': 50314.6}, {'field': 'total_amount', 'old_value': 121389.8, 'new_value': 136479.3}, {'field': 'order_count', 'old_value': 799, 'new_value': 892}]
2025-06-22 03:00:56,354 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-22 03:00:56,776 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-22 03:00:56,776 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23453.4, 'new_value': 24018.01}, {'field': 'offline_amount', 'old_value': 159807.7, 'new_value': 173898.4}, {'field': 'total_amount', 'old_value': 183261.1, 'new_value': 197916.41}, {'field': 'order_count', 'old_value': 5898, 'new_value': 6384}]
2025-06-22 03:00:56,776 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-22 03:00:57,167 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-22 03:00:57,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21190.79, 'new_value': 21779.18}, {'field': 'total_amount', 'old_value': 21255.39, 'new_value': 21843.78}, {'field': 'order_count', 'old_value': 114, 'new_value': 123}]
2025-06-22 03:00:57,167 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-22 03:00:57,558 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-22 03:00:57,558 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 211093.01, 'new_value': 222689.83}, {'field': 'offline_amount', 'old_value': 534160.49, 'new_value': 579462.52}, {'field': 'total_amount', 'old_value': 745253.5, 'new_value': 802152.35}, {'field': 'order_count', 'old_value': 4685, 'new_value': 5033}]
2025-06-22 03:00:57,558 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-22 03:00:58,026 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-22 03:00:58,026 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138112.0, 'new_value': 147634.0}, {'field': 'total_amount', 'old_value': 138112.0, 'new_value': 147634.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 98}]
2025-06-22 03:00:58,026 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-22 03:00:58,448 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-22 03:00:58,448 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30199.62, 'new_value': 32022.13}, {'field': 'offline_amount', 'old_value': 33003.84, 'new_value': 35922.97}, {'field': 'total_amount', 'old_value': 63203.46, 'new_value': 67945.1}, {'field': 'order_count', 'old_value': 5409, 'new_value': 5817}]
2025-06-22 03:00:58,448 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-22 03:00:58,854 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-22 03:00:58,854 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9732.0, 'new_value': 10568.0}, {'field': 'total_amount', 'old_value': 9732.0, 'new_value': 10568.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-06-22 03:00:58,854 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-22 03:00:59,261 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-22 03:00:59,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116599.48, 'new_value': 126828.64}, {'field': 'total_amount', 'old_value': 116599.48, 'new_value': 126828.64}, {'field': 'order_count', 'old_value': 5261, 'new_value': 5687}]
2025-06-22 03:00:59,261 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-22 03:00:59,667 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-22 03:00:59,667 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72874.0, 'new_value': 81597.4}, {'field': 'total_amount', 'old_value': 72874.0, 'new_value': 81597.4}, {'field': 'order_count', 'old_value': 156, 'new_value': 173}]
2025-06-22 03:00:59,667 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-22 03:01:00,089 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-22 03:01:00,089 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64071.0, 'new_value': 70922.0}, {'field': 'total_amount', 'old_value': 64071.0, 'new_value': 70922.0}, {'field': 'order_count', 'old_value': 1920, 'new_value': 2123}]
2025-06-22 03:01:00,089 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-22 03:01:00,573 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-22 03:01:00,573 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166763.77, 'new_value': 180373.39}, {'field': 'total_amount', 'old_value': 166763.77, 'new_value': 180373.39}, {'field': 'order_count', 'old_value': 1346, 'new_value': 1430}]
2025-06-22 03:01:00,573 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-22 03:01:00,964 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-22 03:01:00,964 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203063.65, 'new_value': 222642.12}, {'field': 'total_amount', 'old_value': 203063.65, 'new_value': 222642.12}, {'field': 'order_count', 'old_value': 4249, 'new_value': 4690}]
2025-06-22 03:01:00,964 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-22 03:01:01,417 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-22 03:01:01,417 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 317885.33, 'new_value': 341132.5}, {'field': 'total_amount', 'old_value': 317885.33, 'new_value': 341132.5}, {'field': 'order_count', 'old_value': 12997, 'new_value': 13884}]
2025-06-22 03:01:01,417 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-22 03:01:01,870 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-22 03:01:01,870 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 212699.29, 'new_value': 238227.43}, {'field': 'offline_amount', 'old_value': 1078020.6, 'new_value': 1158929.81}, {'field': 'total_amount', 'old_value': 1290719.89, 'new_value': 1397157.24}, {'field': 'order_count', 'old_value': 6224, 'new_value': 6670}]
2025-06-22 03:01:01,870 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-22 03:01:02,261 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-22 03:01:02,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 592128.0, 'new_value': 596739.0}, {'field': 'total_amount', 'old_value': 592128.0, 'new_value': 596739.0}, {'field': 'order_count', 'old_value': 370, 'new_value': 390}]
2025-06-22 03:01:02,261 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-22 03:01:02,698 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-22 03:01:02,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 392935.25, 'new_value': 441804.35}, {'field': 'total_amount', 'old_value': 392935.25, 'new_value': 441804.35}, {'field': 'order_count', 'old_value': 1522, 'new_value': 1707}]
2025-06-22 03:01:02,698 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMM
2025-06-22 03:01:03,167 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMM
2025-06-22 03:01:03,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10900.0, 'new_value': 16000.0}, {'field': 'total_amount', 'old_value': 10900.0, 'new_value': 16000.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-22 03:01:03,167 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-22 03:01:03,651 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-22 03:01:03,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 266757.3, 'new_value': 288580.3}, {'field': 'total_amount', 'old_value': 266757.3, 'new_value': 288580.3}, {'field': 'order_count', 'old_value': 293, 'new_value': 321}]
2025-06-22 03:01:03,651 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-22 03:01:04,104 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-22 03:01:04,104 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1193821.0, 'new_value': 1271980.0}, {'field': 'total_amount', 'old_value': 1193821.0, 'new_value': 1271980.0}, {'field': 'order_count', 'old_value': 5531, 'new_value': 5912}]
2025-06-22 03:01:04,104 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-22 03:01:04,542 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-22 03:01:04,542 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43608.49, 'new_value': 46114.91}, {'field': 'offline_amount', 'old_value': 270621.59, 'new_value': 300290.14}, {'field': 'total_amount', 'old_value': 314230.08, 'new_value': 346405.05}, {'field': 'order_count', 'old_value': 2606, 'new_value': 2855}]
2025-06-22 03:01:04,542 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-22 03:01:04,995 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-22 03:01:04,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 222881.17, 'new_value': 231777.17}, {'field': 'total_amount', 'old_value': 236876.17, 'new_value': 245772.17}, {'field': 'order_count', 'old_value': 55, 'new_value': 58}]
2025-06-22 03:01:04,995 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-22 03:01:05,432 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-22 03:01:05,432 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102939.4, 'new_value': 109081.0}, {'field': 'offline_amount', 'old_value': 77187.9, 'new_value': 83125.3}, {'field': 'total_amount', 'old_value': 180127.3, 'new_value': 192206.3}, {'field': 'order_count', 'old_value': 4362, 'new_value': 4640}]
2025-06-22 03:01:05,432 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-22 03:01:05,886 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-22 03:01:05,886 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3107.0, 'new_value': 3323.0}, {'field': 'offline_amount', 'old_value': 20343.4, 'new_value': 22019.4}, {'field': 'total_amount', 'old_value': 23450.4, 'new_value': 25342.4}, {'field': 'order_count', 'old_value': 791, 'new_value': 862}]
2025-06-22 03:01:05,886 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-22 03:01:06,307 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-22 03:01:06,307 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111784.53, 'new_value': 118801.43}, {'field': 'offline_amount', 'old_value': 161108.81, 'new_value': 173281.57}, {'field': 'total_amount', 'old_value': 272893.34, 'new_value': 292083.0}, {'field': 'order_count', 'old_value': 9136, 'new_value': 9742}]
2025-06-22 03:01:06,307 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-22 03:01:06,792 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-22 03:01:06,792 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 193123.34, 'new_value': 205917.22}, {'field': 'offline_amount', 'old_value': 239785.55, 'new_value': 262260.45}, {'field': 'total_amount', 'old_value': 432908.89, 'new_value': 468177.67}, {'field': 'order_count', 'old_value': 13849, 'new_value': 14842}]
2025-06-22 03:01:06,792 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-22 03:01:07,526 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-22 03:01:07,526 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62812.0, 'new_value': 216912.0}, {'field': 'total_amount', 'old_value': 75399.0, 'new_value': 229499.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 100}]
2025-06-22 03:01:07,526 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-22 03:01:07,948 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-22 03:01:07,948 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54307.0, 'new_value': 58725.0}, {'field': 'total_amount', 'old_value': 54307.0, 'new_value': 58725.0}, {'field': 'order_count', 'old_value': 2455, 'new_value': 2639}]
2025-06-22 03:01:07,948 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-22 03:01:08,354 - INFO - 更新表单数据成功: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-22 03:01:08,354 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19883.1, 'new_value': 24500.4}, {'field': 'total_amount', 'old_value': 19883.1, 'new_value': 24500.4}, {'field': 'order_count', 'old_value': 348, 'new_value': 453}]
2025-06-22 03:01:08,354 - INFO - 日期 2025-06 处理完成 - 更新: 76 条，插入: 0 条，错误: 0 条
2025-06-22 03:01:08,354 - INFO - 数据同步完成！更新: 76 条，插入: 0 条，错误: 0 条
2025-06-22 03:01:08,354 - INFO - =================同步完成====================
2025-06-22 06:00:02,672 - INFO - =================使用默认全量同步=============
2025-06-22 06:00:04,422 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-22 06:00:04,422 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-22 06:00:04,453 - INFO - 开始处理日期: 2025-01
2025-06-22 06:00:04,453 - INFO - Request Parameters - Page 1:
2025-06-22 06:00:04,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:04,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:05,656 - INFO - Response - Page 1:
2025-06-22 06:00:05,860 - INFO - 第 1 页获取到 100 条记录
2025-06-22 06:00:05,860 - INFO - Request Parameters - Page 2:
2025-06-22 06:00:05,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:05,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:06,860 - INFO - Response - Page 2:
2025-06-22 06:00:07,063 - INFO - 第 2 页获取到 100 条记录
2025-06-22 06:00:07,063 - INFO - Request Parameters - Page 3:
2025-06-22 06:00:07,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:07,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:07,594 - INFO - Response - Page 3:
2025-06-22 06:00:07,797 - INFO - 第 3 页获取到 100 条记录
2025-06-22 06:00:07,797 - INFO - Request Parameters - Page 4:
2025-06-22 06:00:07,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:07,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:08,281 - INFO - Response - Page 4:
2025-06-22 06:00:08,485 - INFO - 第 4 页获取到 100 条记录
2025-06-22 06:00:08,485 - INFO - Request Parameters - Page 5:
2025-06-22 06:00:08,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:08,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:09,016 - INFO - Response - Page 5:
2025-06-22 06:00:09,219 - INFO - 第 5 页获取到 100 条记录
2025-06-22 06:00:09,219 - INFO - Request Parameters - Page 6:
2025-06-22 06:00:09,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:09,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:09,735 - INFO - Response - Page 6:
2025-06-22 06:00:09,938 - INFO - 第 6 页获取到 100 条记录
2025-06-22 06:00:09,938 - INFO - Request Parameters - Page 7:
2025-06-22 06:00:09,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:09,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:10,438 - INFO - Response - Page 7:
2025-06-22 06:00:10,641 - INFO - 第 7 页获取到 82 条记录
2025-06-22 06:00:10,641 - INFO - 查询完成，共获取到 682 条记录
2025-06-22 06:00:10,641 - INFO - 获取到 682 条表单数据
2025-06-22 06:00:10,641 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-22 06:00:10,656 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 06:00:10,656 - INFO - 开始处理日期: 2025-02
2025-06-22 06:00:10,656 - INFO - Request Parameters - Page 1:
2025-06-22 06:00:10,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:10,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:11,203 - INFO - Response - Page 1:
2025-06-22 06:00:11,406 - INFO - 第 1 页获取到 100 条记录
2025-06-22 06:00:11,406 - INFO - Request Parameters - Page 2:
2025-06-22 06:00:11,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:11,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:11,906 - INFO - Response - Page 2:
2025-06-22 06:00:12,110 - INFO - 第 2 页获取到 100 条记录
2025-06-22 06:00:12,110 - INFO - Request Parameters - Page 3:
2025-06-22 06:00:12,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:12,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:12,578 - INFO - Response - Page 3:
2025-06-22 06:00:12,781 - INFO - 第 3 页获取到 100 条记录
2025-06-22 06:00:12,781 - INFO - Request Parameters - Page 4:
2025-06-22 06:00:12,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:12,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:13,297 - INFO - Response - Page 4:
2025-06-22 06:00:13,500 - INFO - 第 4 页获取到 100 条记录
2025-06-22 06:00:13,500 - INFO - Request Parameters - Page 5:
2025-06-22 06:00:13,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:13,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:14,031 - INFO - Response - Page 5:
2025-06-22 06:00:14,234 - INFO - 第 5 页获取到 100 条记录
2025-06-22 06:00:14,234 - INFO - Request Parameters - Page 6:
2025-06-22 06:00:14,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:14,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:14,734 - INFO - Response - Page 6:
2025-06-22 06:00:14,938 - INFO - 第 6 页获取到 100 条记录
2025-06-22 06:00:14,938 - INFO - Request Parameters - Page 7:
2025-06-22 06:00:14,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:14,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:15,422 - INFO - Response - Page 7:
2025-06-22 06:00:15,625 - INFO - 第 7 页获取到 70 条记录
2025-06-22 06:00:15,625 - INFO - 查询完成，共获取到 670 条记录
2025-06-22 06:00:15,625 - INFO - 获取到 670 条表单数据
2025-06-22 06:00:15,625 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-22 06:00:15,641 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 06:00:15,641 - INFO - 开始处理日期: 2025-03
2025-06-22 06:00:15,641 - INFO - Request Parameters - Page 1:
2025-06-22 06:00:15,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:15,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:16,188 - INFO - Response - Page 1:
2025-06-22 06:00:16,391 - INFO - 第 1 页获取到 100 条记录
2025-06-22 06:00:16,391 - INFO - Request Parameters - Page 2:
2025-06-22 06:00:16,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:16,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:17,063 - INFO - Response - Page 2:
2025-06-22 06:00:17,266 - INFO - 第 2 页获取到 100 条记录
2025-06-22 06:00:17,266 - INFO - Request Parameters - Page 3:
2025-06-22 06:00:17,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:17,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:17,750 - INFO - Response - Page 3:
2025-06-22 06:00:17,953 - INFO - 第 3 页获取到 100 条记录
2025-06-22 06:00:17,953 - INFO - Request Parameters - Page 4:
2025-06-22 06:00:17,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:17,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:18,500 - INFO - Response - Page 4:
2025-06-22 06:00:18,703 - INFO - 第 4 页获取到 100 条记录
2025-06-22 06:00:18,703 - INFO - Request Parameters - Page 5:
2025-06-22 06:00:18,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:18,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:19,188 - INFO - Response - Page 5:
2025-06-22 06:00:19,391 - INFO - 第 5 页获取到 100 条记录
2025-06-22 06:00:19,391 - INFO - Request Parameters - Page 6:
2025-06-22 06:00:19,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:19,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:19,875 - INFO - Response - Page 6:
2025-06-22 06:00:20,078 - INFO - 第 6 页获取到 100 条记录
2025-06-22 06:00:20,078 - INFO - Request Parameters - Page 7:
2025-06-22 06:00:20,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:20,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:20,578 - INFO - Response - Page 7:
2025-06-22 06:00:20,781 - INFO - 第 7 页获取到 61 条记录
2025-06-22 06:00:20,781 - INFO - 查询完成，共获取到 661 条记录
2025-06-22 06:00:20,781 - INFO - 获取到 661 条表单数据
2025-06-22 06:00:20,781 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-22 06:00:20,797 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 06:00:20,797 - INFO - 开始处理日期: 2025-04
2025-06-22 06:00:20,797 - INFO - Request Parameters - Page 1:
2025-06-22 06:00:20,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:20,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:21,359 - INFO - Response - Page 1:
2025-06-22 06:00:21,563 - INFO - 第 1 页获取到 100 条记录
2025-06-22 06:00:21,563 - INFO - Request Parameters - Page 2:
2025-06-22 06:00:21,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:21,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:22,000 - INFO - Response - Page 2:
2025-06-22 06:00:22,203 - INFO - 第 2 页获取到 100 条记录
2025-06-22 06:00:22,203 - INFO - Request Parameters - Page 3:
2025-06-22 06:00:22,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:22,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:22,797 - INFO - Response - Page 3:
2025-06-22 06:00:23,000 - INFO - 第 3 页获取到 100 条记录
2025-06-22 06:00:23,000 - INFO - Request Parameters - Page 4:
2025-06-22 06:00:23,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:23,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:23,500 - INFO - Response - Page 4:
2025-06-22 06:00:23,703 - INFO - 第 4 页获取到 100 条记录
2025-06-22 06:00:23,703 - INFO - Request Parameters - Page 5:
2025-06-22 06:00:23,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:23,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:24,203 - INFO - Response - Page 5:
2025-06-22 06:00:24,406 - INFO - 第 5 页获取到 100 条记录
2025-06-22 06:00:24,406 - INFO - Request Parameters - Page 6:
2025-06-22 06:00:24,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:24,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:24,938 - INFO - Response - Page 6:
2025-06-22 06:00:25,141 - INFO - 第 6 页获取到 100 条记录
2025-06-22 06:00:25,141 - INFO - Request Parameters - Page 7:
2025-06-22 06:00:25,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:25,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:25,578 - INFO - Response - Page 7:
2025-06-22 06:00:25,781 - INFO - 第 7 页获取到 56 条记录
2025-06-22 06:00:25,781 - INFO - 查询完成，共获取到 656 条记录
2025-06-22 06:00:25,781 - INFO - 获取到 656 条表单数据
2025-06-22 06:00:25,781 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-22 06:00:25,797 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 06:00:25,797 - INFO - 开始处理日期: 2025-05
2025-06-22 06:00:25,797 - INFO - Request Parameters - Page 1:
2025-06-22 06:00:25,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:25,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:26,328 - INFO - Response - Page 1:
2025-06-22 06:00:26,531 - INFO - 第 1 页获取到 100 条记录
2025-06-22 06:00:26,531 - INFO - Request Parameters - Page 2:
2025-06-22 06:00:26,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:26,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:26,984 - INFO - Response - Page 2:
2025-06-22 06:00:27,188 - INFO - 第 2 页获取到 100 条记录
2025-06-22 06:00:27,188 - INFO - Request Parameters - Page 3:
2025-06-22 06:00:27,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:27,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:27,703 - INFO - Response - Page 3:
2025-06-22 06:00:27,906 - INFO - 第 3 页获取到 100 条记录
2025-06-22 06:00:27,906 - INFO - Request Parameters - Page 4:
2025-06-22 06:00:27,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:27,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:28,422 - INFO - Response - Page 4:
2025-06-22 06:00:28,625 - INFO - 第 4 页获取到 100 条记录
2025-06-22 06:00:28,625 - INFO - Request Parameters - Page 5:
2025-06-22 06:00:28,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:28,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:29,094 - INFO - Response - Page 5:
2025-06-22 06:00:29,297 - INFO - 第 5 页获取到 100 条记录
2025-06-22 06:00:29,297 - INFO - Request Parameters - Page 6:
2025-06-22 06:00:29,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:29,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:29,813 - INFO - Response - Page 6:
2025-06-22 06:00:30,016 - INFO - 第 6 页获取到 100 条记录
2025-06-22 06:00:30,016 - INFO - Request Parameters - Page 7:
2025-06-22 06:00:30,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:30,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:30,453 - INFO - Response - Page 7:
2025-06-22 06:00:30,656 - INFO - 第 7 页获取到 65 条记录
2025-06-22 06:00:30,656 - INFO - 查询完成，共获取到 665 条记录
2025-06-22 06:00:30,656 - INFO - 获取到 665 条表单数据
2025-06-22 06:00:30,656 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-22 06:00:30,672 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 06:00:30,672 - INFO - 开始处理日期: 2025-06
2025-06-22 06:00:30,672 - INFO - Request Parameters - Page 1:
2025-06-22 06:00:30,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:30,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:31,109 - INFO - Response - Page 1:
2025-06-22 06:00:31,313 - INFO - 第 1 页获取到 100 条记录
2025-06-22 06:00:31,313 - INFO - Request Parameters - Page 2:
2025-06-22 06:00:31,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:31,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:31,875 - INFO - Response - Page 2:
2025-06-22 06:00:32,078 - INFO - 第 2 页获取到 100 条记录
2025-06-22 06:00:32,078 - INFO - Request Parameters - Page 3:
2025-06-22 06:00:32,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:32,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:32,578 - INFO - Response - Page 3:
2025-06-22 06:00:32,781 - INFO - 第 3 页获取到 100 条记录
2025-06-22 06:00:32,781 - INFO - Request Parameters - Page 4:
2025-06-22 06:00:32,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:32,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:33,281 - INFO - Response - Page 4:
2025-06-22 06:00:33,484 - INFO - 第 4 页获取到 100 条记录
2025-06-22 06:00:33,484 - INFO - Request Parameters - Page 5:
2025-06-22 06:00:33,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:33,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:34,047 - INFO - Response - Page 5:
2025-06-22 06:00:34,250 - INFO - 第 5 页获取到 100 条记录
2025-06-22 06:00:34,250 - INFO - Request Parameters - Page 6:
2025-06-22 06:00:34,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:34,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:34,672 - INFO - Response - Page 6:
2025-06-22 06:00:34,875 - INFO - 第 6 页获取到 100 条记录
2025-06-22 06:00:34,875 - INFO - Request Parameters - Page 7:
2025-06-22 06:00:34,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 06:00:34,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 06:00:35,219 - INFO - Response - Page 7:
2025-06-22 06:00:35,422 - INFO - 第 7 页获取到 23 条记录
2025-06-22 06:00:35,422 - INFO - 查询完成，共获取到 623 条记录
2025-06-22 06:00:35,422 - INFO - 获取到 623 条表单数据
2025-06-22 06:00:35,422 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-22 06:00:35,437 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 06:00:35,437 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 06:00:35,437 - INFO - =================同步完成====================
2025-06-22 09:00:03,067 - INFO - =================使用默认全量同步=============
2025-06-22 09:00:04,802 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-22 09:00:04,802 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-22 09:00:04,833 - INFO - 开始处理日期: 2025-01
2025-06-22 09:00:04,833 - INFO - Request Parameters - Page 1:
2025-06-22 09:00:04,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:04,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:06,317 - INFO - Response - Page 1:
2025-06-22 09:00:06,520 - INFO - 第 1 页获取到 100 条记录
2025-06-22 09:00:06,520 - INFO - Request Parameters - Page 2:
2025-06-22 09:00:06,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:06,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:07,020 - INFO - Response - Page 2:
2025-06-22 09:00:07,223 - INFO - 第 2 页获取到 100 条记录
2025-06-22 09:00:07,223 - INFO - Request Parameters - Page 3:
2025-06-22 09:00:07,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:07,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:07,770 - INFO - Response - Page 3:
2025-06-22 09:00:07,973 - INFO - 第 3 页获取到 100 条记录
2025-06-22 09:00:07,973 - INFO - Request Parameters - Page 4:
2025-06-22 09:00:07,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:07,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:08,489 - INFO - Response - Page 4:
2025-06-22 09:00:08,692 - INFO - 第 4 页获取到 100 条记录
2025-06-22 09:00:08,692 - INFO - Request Parameters - Page 5:
2025-06-22 09:00:08,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:08,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:09,223 - INFO - Response - Page 5:
2025-06-22 09:00:09,427 - INFO - 第 5 页获取到 100 条记录
2025-06-22 09:00:09,427 - INFO - Request Parameters - Page 6:
2025-06-22 09:00:09,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:09,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:09,927 - INFO - Response - Page 6:
2025-06-22 09:00:10,130 - INFO - 第 6 页获取到 100 条记录
2025-06-22 09:00:10,130 - INFO - Request Parameters - Page 7:
2025-06-22 09:00:10,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:10,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:10,614 - INFO - Response - Page 7:
2025-06-22 09:00:10,817 - INFO - 第 7 页获取到 82 条记录
2025-06-22 09:00:10,817 - INFO - 查询完成，共获取到 682 条记录
2025-06-22 09:00:10,817 - INFO - 获取到 682 条表单数据
2025-06-22 09:00:10,817 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-22 09:00:10,833 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 09:00:10,833 - INFO - 开始处理日期: 2025-02
2025-06-22 09:00:10,833 - INFO - Request Parameters - Page 1:
2025-06-22 09:00:10,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:10,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:11,286 - INFO - Response - Page 1:
2025-06-22 09:00:11,489 - INFO - 第 1 页获取到 100 条记录
2025-06-22 09:00:11,489 - INFO - Request Parameters - Page 2:
2025-06-22 09:00:11,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:11,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:12,036 - INFO - Response - Page 2:
2025-06-22 09:00:12,239 - INFO - 第 2 页获取到 100 条记录
2025-06-22 09:00:12,239 - INFO - Request Parameters - Page 3:
2025-06-22 09:00:12,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:12,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:12,723 - INFO - Response - Page 3:
2025-06-22 09:00:12,927 - INFO - 第 3 页获取到 100 条记录
2025-06-22 09:00:12,927 - INFO - Request Parameters - Page 4:
2025-06-22 09:00:12,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:12,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:13,411 - INFO - Response - Page 4:
2025-06-22 09:00:13,614 - INFO - 第 4 页获取到 100 条记录
2025-06-22 09:00:13,614 - INFO - Request Parameters - Page 5:
2025-06-22 09:00:13,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:13,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:14,161 - INFO - Response - Page 5:
2025-06-22 09:00:14,364 - INFO - 第 5 页获取到 100 条记录
2025-06-22 09:00:14,364 - INFO - Request Parameters - Page 6:
2025-06-22 09:00:14,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:14,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:14,802 - INFO - Response - Page 6:
2025-06-22 09:00:15,005 - INFO - 第 6 页获取到 100 条记录
2025-06-22 09:00:15,005 - INFO - Request Parameters - Page 7:
2025-06-22 09:00:15,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:15,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:15,505 - INFO - Response - Page 7:
2025-06-22 09:00:15,708 - INFO - 第 7 页获取到 70 条记录
2025-06-22 09:00:15,708 - INFO - 查询完成，共获取到 670 条记录
2025-06-22 09:00:15,708 - INFO - 获取到 670 条表单数据
2025-06-22 09:00:15,708 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-22 09:00:15,723 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 09:00:15,723 - INFO - 开始处理日期: 2025-03
2025-06-22 09:00:15,723 - INFO - Request Parameters - Page 1:
2025-06-22 09:00:15,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:15,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:16,208 - INFO - Response - Page 1:
2025-06-22 09:00:16,411 - INFO - 第 1 页获取到 100 条记录
2025-06-22 09:00:16,411 - INFO - Request Parameters - Page 2:
2025-06-22 09:00:16,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:16,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:16,927 - INFO - Response - Page 2:
2025-06-22 09:00:17,130 - INFO - 第 2 页获取到 100 条记录
2025-06-22 09:00:17,130 - INFO - Request Parameters - Page 3:
2025-06-22 09:00:17,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:17,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:17,786 - INFO - Response - Page 3:
2025-06-22 09:00:17,989 - INFO - 第 3 页获取到 100 条记录
2025-06-22 09:00:17,989 - INFO - Request Parameters - Page 4:
2025-06-22 09:00:17,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:17,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:18,458 - INFO - Response - Page 4:
2025-06-22 09:00:18,661 - INFO - 第 4 页获取到 100 条记录
2025-06-22 09:00:18,661 - INFO - Request Parameters - Page 5:
2025-06-22 09:00:18,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:18,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:19,176 - INFO - Response - Page 5:
2025-06-22 09:00:19,380 - INFO - 第 5 页获取到 100 条记录
2025-06-22 09:00:19,380 - INFO - Request Parameters - Page 6:
2025-06-22 09:00:19,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:19,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:19,926 - INFO - Response - Page 6:
2025-06-22 09:00:20,130 - INFO - 第 6 页获取到 100 条记录
2025-06-22 09:00:20,130 - INFO - Request Parameters - Page 7:
2025-06-22 09:00:20,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:20,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:20,614 - INFO - Response - Page 7:
2025-06-22 09:00:20,817 - INFO - 第 7 页获取到 61 条记录
2025-06-22 09:00:20,817 - INFO - 查询完成，共获取到 661 条记录
2025-06-22 09:00:20,817 - INFO - 获取到 661 条表单数据
2025-06-22 09:00:20,817 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-22 09:00:20,833 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 09:00:20,833 - INFO - 开始处理日期: 2025-04
2025-06-22 09:00:20,833 - INFO - Request Parameters - Page 1:
2025-06-22 09:00:20,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:20,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:21,317 - INFO - Response - Page 1:
2025-06-22 09:00:21,520 - INFO - 第 1 页获取到 100 条记录
2025-06-22 09:00:21,520 - INFO - Request Parameters - Page 2:
2025-06-22 09:00:21,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:21,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:22,098 - INFO - Response - Page 2:
2025-06-22 09:00:22,301 - INFO - 第 2 页获取到 100 条记录
2025-06-22 09:00:22,301 - INFO - Request Parameters - Page 3:
2025-06-22 09:00:22,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:22,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:22,755 - INFO - Response - Page 3:
2025-06-22 09:00:22,958 - INFO - 第 3 页获取到 100 条记录
2025-06-22 09:00:22,958 - INFO - Request Parameters - Page 4:
2025-06-22 09:00:22,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:22,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:23,411 - INFO - Response - Page 4:
2025-06-22 09:00:23,614 - INFO - 第 4 页获取到 100 条记录
2025-06-22 09:00:23,614 - INFO - Request Parameters - Page 5:
2025-06-22 09:00:23,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:23,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:24,130 - INFO - Response - Page 5:
2025-06-22 09:00:24,333 - INFO - 第 5 页获取到 100 条记录
2025-06-22 09:00:24,333 - INFO - Request Parameters - Page 6:
2025-06-22 09:00:24,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:24,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:24,833 - INFO - Response - Page 6:
2025-06-22 09:00:25,036 - INFO - 第 6 页获取到 100 条记录
2025-06-22 09:00:25,036 - INFO - Request Parameters - Page 7:
2025-06-22 09:00:25,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:25,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:25,473 - INFO - Response - Page 7:
2025-06-22 09:00:25,676 - INFO - 第 7 页获取到 56 条记录
2025-06-22 09:00:25,676 - INFO - 查询完成，共获取到 656 条记录
2025-06-22 09:00:25,676 - INFO - 获取到 656 条表单数据
2025-06-22 09:00:25,676 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-22 09:00:25,692 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 09:00:25,692 - INFO - 开始处理日期: 2025-05
2025-06-22 09:00:25,692 - INFO - Request Parameters - Page 1:
2025-06-22 09:00:25,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:25,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:26,208 - INFO - Response - Page 1:
2025-06-22 09:00:26,411 - INFO - 第 1 页获取到 100 条记录
2025-06-22 09:00:26,411 - INFO - Request Parameters - Page 2:
2025-06-22 09:00:26,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:26,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:27,098 - INFO - Response - Page 2:
2025-06-22 09:00:27,301 - INFO - 第 2 页获取到 100 条记录
2025-06-22 09:00:27,301 - INFO - Request Parameters - Page 3:
2025-06-22 09:00:27,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:27,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:27,786 - INFO - Response - Page 3:
2025-06-22 09:00:27,989 - INFO - 第 3 页获取到 100 条记录
2025-06-22 09:00:27,989 - INFO - Request Parameters - Page 4:
2025-06-22 09:00:27,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:27,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:28,551 - INFO - Response - Page 4:
2025-06-22 09:00:28,755 - INFO - 第 4 页获取到 100 条记录
2025-06-22 09:00:28,755 - INFO - Request Parameters - Page 5:
2025-06-22 09:00:28,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:28,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:29,208 - INFO - Response - Page 5:
2025-06-22 09:00:29,411 - INFO - 第 5 页获取到 100 条记录
2025-06-22 09:00:29,411 - INFO - Request Parameters - Page 6:
2025-06-22 09:00:29,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:29,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:29,880 - INFO - Response - Page 6:
2025-06-22 09:00:30,083 - INFO - 第 6 页获取到 100 条记录
2025-06-22 09:00:30,083 - INFO - Request Parameters - Page 7:
2025-06-22 09:00:30,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:30,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:30,489 - INFO - Response - Page 7:
2025-06-22 09:00:30,692 - INFO - 第 7 页获取到 65 条记录
2025-06-22 09:00:30,692 - INFO - 查询完成，共获取到 665 条记录
2025-06-22 09:00:30,692 - INFO - 获取到 665 条表单数据
2025-06-22 09:00:30,692 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-22 09:00:30,708 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 09:00:30,708 - INFO - 开始处理日期: 2025-06
2025-06-22 09:00:30,708 - INFO - Request Parameters - Page 1:
2025-06-22 09:00:30,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:30,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:31,223 - INFO - Response - Page 1:
2025-06-22 09:00:31,426 - INFO - 第 1 页获取到 100 条记录
2025-06-22 09:00:31,426 - INFO - Request Parameters - Page 2:
2025-06-22 09:00:31,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:31,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:31,880 - INFO - Response - Page 2:
2025-06-22 09:00:32,083 - INFO - 第 2 页获取到 100 条记录
2025-06-22 09:00:32,083 - INFO - Request Parameters - Page 3:
2025-06-22 09:00:32,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:32,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:32,583 - INFO - Response - Page 3:
2025-06-22 09:00:32,786 - INFO - 第 3 页获取到 100 条记录
2025-06-22 09:00:32,786 - INFO - Request Parameters - Page 4:
2025-06-22 09:00:32,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:32,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:33,380 - INFO - Response - Page 4:
2025-06-22 09:00:33,583 - INFO - 第 4 页获取到 100 条记录
2025-06-22 09:00:33,583 - INFO - Request Parameters - Page 5:
2025-06-22 09:00:33,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:33,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:34,098 - INFO - Response - Page 5:
2025-06-22 09:00:34,301 - INFO - 第 5 页获取到 100 条记录
2025-06-22 09:00:34,301 - INFO - Request Parameters - Page 6:
2025-06-22 09:00:34,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:34,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:34,833 - INFO - Response - Page 6:
2025-06-22 09:00:35,036 - INFO - 第 6 页获取到 100 条记录
2025-06-22 09:00:35,036 - INFO - Request Parameters - Page 7:
2025-06-22 09:00:35,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 09:00:35,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 09:00:35,395 - INFO - Response - Page 7:
2025-06-22 09:00:35,598 - INFO - 第 7 页获取到 23 条记录
2025-06-22 09:00:35,598 - INFO - 查询完成，共获取到 623 条记录
2025-06-22 09:00:35,598 - INFO - 获取到 623 条表单数据
2025-06-22 09:00:35,598 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-22 09:00:35,598 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-22 09:00:36,036 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-22 09:00:36,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167800.0, 'new_value': 200400.0}, {'field': 'total_amount', 'old_value': 167800.0, 'new_value': 200400.0}]
2025-06-22 09:00:36,036 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-22 09:00:36,739 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-22 09:00:36,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 239130.0, 'new_value': 257930.0}, {'field': 'total_amount', 'old_value': 261310.0, 'new_value': 280110.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 190}]
2025-06-22 09:00:36,739 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-22 09:00:37,161 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-22 09:00:37,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155280.0, 'new_value': 161960.0}, {'field': 'total_amount', 'old_value': 155280.0, 'new_value': 161960.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-06-22 09:00:37,161 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-22 09:00:37,614 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-22 09:00:37,614 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5958.0, 'new_value': 6538.0}, {'field': 'offline_amount', 'old_value': 69665.0, 'new_value': 75195.0}, {'field': 'total_amount', 'old_value': 75623.0, 'new_value': 81733.0}, {'field': 'order_count', 'old_value': 86, 'new_value': 95}]
2025-06-22 09:00:37,614 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-22 09:00:38,020 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-22 09:00:38,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155307.0, 'new_value': 155901.0}, {'field': 'total_amount', 'old_value': 155307.0, 'new_value': 155901.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-06-22 09:00:38,020 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-22 09:00:38,395 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-22 09:00:38,395 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 208013.8, 'new_value': 232767.8}, {'field': 'total_amount', 'old_value': 208013.8, 'new_value': 232767.8}, {'field': 'order_count', 'old_value': 3125, 'new_value': 3371}]
2025-06-22 09:00:38,395 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-22 09:00:38,833 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-22 09:00:38,833 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3405.0, 'new_value': 4133.0}, {'field': 'offline_amount', 'old_value': 43778.47, 'new_value': 45764.17}, {'field': 'total_amount', 'old_value': 47183.47, 'new_value': 49897.17}, {'field': 'order_count', 'old_value': 397, 'new_value': 416}]
2025-06-22 09:00:38,833 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-22 09:00:39,317 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-22 09:00:39,317 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38930.17, 'new_value': 41871.95}, {'field': 'offline_amount', 'old_value': 90652.62, 'new_value': 93801.62}, {'field': 'total_amount', 'old_value': 129582.79, 'new_value': 135673.57}, {'field': 'order_count', 'old_value': 1489, 'new_value': 1540}]
2025-06-22 09:00:39,317 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-22 09:00:39,676 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-22 09:00:39,676 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27663.0, 'new_value': 28854.0}, {'field': 'offline_amount', 'old_value': 47915.08, 'new_value': 48479.08}, {'field': 'total_amount', 'old_value': 75578.08, 'new_value': 77333.08}, {'field': 'order_count', 'old_value': 103, 'new_value': 108}]
2025-06-22 09:00:39,676 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-22 09:00:40,114 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-22 09:00:40,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49358.0, 'new_value': 51311.0}, {'field': 'total_amount', 'old_value': 49358.0, 'new_value': 51311.0}, {'field': 'order_count', 'old_value': 935, 'new_value': 971}]
2025-06-22 09:00:40,114 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-22 09:00:40,598 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-22 09:00:40,598 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 115225.0, 'new_value': 124630.0}, {'field': 'offline_amount', 'old_value': 41127.54, 'new_value': 43253.34}, {'field': 'total_amount', 'old_value': 156352.54, 'new_value': 167883.34}, {'field': 'order_count', 'old_value': 1053, 'new_value': 1130}]
2025-06-22 09:00:40,598 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-22 09:00:41,020 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-22 09:00:41,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15972.0, 'new_value': 16952.0}, {'field': 'total_amount', 'old_value': 16540.2, 'new_value': 17520.2}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-06-22 09:00:41,020 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-22 09:00:41,458 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-22 09:00:41,458 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4034.0, 'new_value': 4133.0}, {'field': 'offline_amount', 'old_value': 4463.0, 'new_value': 4649.0}, {'field': 'total_amount', 'old_value': 8497.0, 'new_value': 8782.0}, {'field': 'order_count', 'old_value': 147, 'new_value': 155}]
2025-06-22 09:00:41,458 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-22 09:00:41,848 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-22 09:00:41,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 376219.6, 'new_value': 416838.6}, {'field': 'total_amount', 'old_value': 428134.6, 'new_value': 468753.6}, {'field': 'order_count', 'old_value': 75, 'new_value': 78}]
2025-06-22 09:00:41,848 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-22 09:00:42,286 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-22 09:00:42,286 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19993.32, 'new_value': 21001.23}, {'field': 'offline_amount', 'old_value': 22449.72, 'new_value': 24041.37}, {'field': 'total_amount', 'old_value': 42443.04, 'new_value': 45042.6}, {'field': 'order_count', 'old_value': 2073, 'new_value': 2217}]
2025-06-22 09:00:42,286 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-22 09:00:42,786 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-22 09:00:42,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85754.5, 'new_value': 113199.5}, {'field': 'total_amount', 'old_value': 85754.5, 'new_value': 113199.5}, {'field': 'order_count', 'old_value': 42, 'new_value': 49}]
2025-06-22 09:00:42,786 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-22 09:00:43,301 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-22 09:00:43,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6274.0, 'new_value': 6402.0}, {'field': 'offline_amount', 'old_value': 12861.0, 'new_value': 14496.0}, {'field': 'total_amount', 'old_value': 19135.0, 'new_value': 20898.0}, {'field': 'order_count', 'old_value': 5228, 'new_value': 5230}]
2025-06-22 09:00:43,301 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-22 09:00:43,692 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-22 09:00:43,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140842.0, 'new_value': 145023.0}, {'field': 'total_amount', 'old_value': 140842.0, 'new_value': 145023.0}, {'field': 'order_count', 'old_value': 257, 'new_value': 264}]
2025-06-22 09:00:43,692 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-22 09:00:44,129 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-22 09:00:44,129 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8438.46, 'new_value': 8829.87}, {'field': 'offline_amount', 'old_value': 127158.39, 'new_value': 130747.95}, {'field': 'total_amount', 'old_value': 135596.85, 'new_value': 139577.82}, {'field': 'order_count', 'old_value': 1529, 'new_value': 1579}]
2025-06-22 09:00:44,129 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-22 09:00:44,551 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-22 09:00:44,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58859.0, 'new_value': 62019.0}, {'field': 'total_amount', 'old_value': 65319.4, 'new_value': 68479.4}, {'field': 'order_count', 'old_value': 64, 'new_value': 65}]
2025-06-22 09:00:44,551 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-22 09:00:44,989 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-22 09:00:44,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7479.26, 'new_value': 8262.76}, {'field': 'total_amount', 'old_value': 7479.26, 'new_value': 8262.76}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-06-22 09:00:44,989 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-22 09:00:45,458 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-22 09:00:45,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107028.97, 'new_value': 115136.77}, {'field': 'total_amount', 'old_value': 107028.97, 'new_value': 115136.77}, {'field': 'order_count', 'old_value': 537, 'new_value': 582}]
2025-06-22 09:00:45,458 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-22 09:00:45,879 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-22 09:00:45,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22499.0, 'new_value': 23591.0}, {'field': 'total_amount', 'old_value': 22499.0, 'new_value': 23591.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 110}]
2025-06-22 09:00:45,879 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-22 09:00:46,270 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-22 09:00:46,270 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148448.0, 'new_value': 154823.0}, {'field': 'total_amount', 'old_value': 148448.0, 'new_value': 154823.0}, {'field': 'order_count', 'old_value': 5733, 'new_value': 5976}]
2025-06-22 09:00:46,270 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-22 09:00:46,770 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-22 09:00:46,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128332.74, 'new_value': 132804.54}, {'field': 'total_amount', 'old_value': 128332.74, 'new_value': 132804.54}, {'field': 'order_count', 'old_value': 509, 'new_value': 525}]
2025-06-22 09:00:46,770 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-22 09:00:47,223 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-22 09:00:47,223 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103047.0, 'new_value': 109376.0}, {'field': 'offline_amount', 'old_value': 41734.18, 'new_value': 44058.18}, {'field': 'total_amount', 'old_value': 144781.18, 'new_value': 153434.18}, {'field': 'order_count', 'old_value': 1013, 'new_value': 1083}]
2025-06-22 09:00:47,223 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-22 09:00:47,708 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-22 09:00:47,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109934.49, 'new_value': 114413.69}, {'field': 'total_amount', 'old_value': 109934.49, 'new_value': 114413.69}, {'field': 'order_count', 'old_value': 3879, 'new_value': 4024}]
2025-06-22 09:00:47,708 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-22 09:00:48,192 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-22 09:00:48,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3978.0, 'new_value': 4056.0}, {'field': 'total_amount', 'old_value': 4472.0, 'new_value': 4550.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-22 09:00:48,192 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-22 09:00:48,536 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-22 09:00:48,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43987.0, 'new_value': 47065.0}, {'field': 'offline_amount', 'old_value': 150368.0, 'new_value': 158503.0}, {'field': 'total_amount', 'old_value': 194355.0, 'new_value': 205568.0}, {'field': 'order_count', 'old_value': 146, 'new_value': 153}]
2025-06-22 09:00:48,551 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-22 09:00:48,942 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-22 09:00:48,942 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13753.79, 'new_value': 14696.47}, {'field': 'offline_amount', 'old_value': 292443.88, 'new_value': 315236.15}, {'field': 'total_amount', 'old_value': 306197.67, 'new_value': 329932.62}, {'field': 'order_count', 'old_value': 1536, 'new_value': 1629}]
2025-06-22 09:00:48,942 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-22 09:00:49,426 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-22 09:00:49,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124835.0, 'new_value': 130375.0}, {'field': 'total_amount', 'old_value': 124835.0, 'new_value': 130375.0}, {'field': 'order_count', 'old_value': 3259, 'new_value': 3412}]
2025-06-22 09:00:49,426 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-22 09:00:49,926 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-22 09:00:49,926 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104379.25, 'new_value': 107835.0}, {'field': 'total_amount', 'old_value': 104379.25, 'new_value': 107835.0}, {'field': 'order_count', 'old_value': 1328, 'new_value': 1369}]
2025-06-22 09:00:49,926 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-22 09:00:50,379 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-22 09:00:50,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57584.06, 'new_value': 59469.26}, {'field': 'total_amount', 'old_value': 57584.06, 'new_value': 59469.26}, {'field': 'order_count', 'old_value': 240, 'new_value': 248}]
2025-06-22 09:00:50,379 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-22 09:00:50,879 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-22 09:00:50,879 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10082.29, 'new_value': 10973.29}, {'field': 'offline_amount', 'old_value': 19416.83, 'new_value': 20607.83}, {'field': 'total_amount', 'old_value': 29499.12, 'new_value': 31581.12}, {'field': 'order_count', 'old_value': 1016, 'new_value': 1090}]
2025-06-22 09:00:50,879 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-22 09:00:51,348 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-22 09:00:51,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32936.0, 'new_value': 35916.0}, {'field': 'total_amount', 'old_value': 32936.0, 'new_value': 35916.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-06-22 09:00:51,348 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-22 09:00:51,786 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-22 09:00:51,786 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 307864.1, 'new_value': 335387.6}, {'field': 'offline_amount', 'old_value': 72065.7, 'new_value': 77785.7}, {'field': 'total_amount', 'old_value': 379929.8, 'new_value': 413173.3}, {'field': 'order_count', 'old_value': 481, 'new_value': 521}]
2025-06-22 09:00:51,786 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-22 09:00:52,239 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-22 09:00:52,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20881.32, 'new_value': 20913.32}, {'field': 'total_amount', 'old_value': 20881.32, 'new_value': 20913.32}, {'field': 'order_count', 'old_value': 862, 'new_value': 1583}]
2025-06-22 09:00:52,239 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-22 09:00:52,645 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-22 09:00:52,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81945.19, 'new_value': 86406.1}, {'field': 'total_amount', 'old_value': 81945.19, 'new_value': 86406.1}, {'field': 'order_count', 'old_value': 2343, 'new_value': 2450}]
2025-06-22 09:00:52,645 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-22 09:00:53,083 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-22 09:00:53,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 291938.79, 'new_value': 308743.61}, {'field': 'total_amount', 'old_value': 291938.79, 'new_value': 308743.61}, {'field': 'order_count', 'old_value': 933, 'new_value': 987}]
2025-06-22 09:00:53,083 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-22 09:00:53,583 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-22 09:00:53,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31338.0, 'new_value': 33232.0}, {'field': 'total_amount', 'old_value': 31338.0, 'new_value': 33232.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 53}]
2025-06-22 09:00:53,583 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-22 09:00:54,083 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-22 09:00:54,083 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54286.5, 'new_value': 56424.2}, {'field': 'offline_amount', 'old_value': 18946.3, 'new_value': 20023.9}, {'field': 'total_amount', 'old_value': 73232.8, 'new_value': 76448.1}, {'field': 'order_count', 'old_value': 262, 'new_value': 282}]
2025-06-22 09:00:54,083 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-22 09:00:54,536 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-22 09:00:54,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11586.0, 'new_value': 12493.0}, {'field': 'offline_amount', 'old_value': 57056.05, 'new_value': 60164.15}, {'field': 'total_amount', 'old_value': 68642.05, 'new_value': 72657.15}, {'field': 'order_count', 'old_value': 687, 'new_value': 713}]
2025-06-22 09:00:54,536 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-22 09:00:55,004 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-22 09:00:55,004 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4038.0, 'new_value': 4260.0}, {'field': 'offline_amount', 'old_value': 21975.5, 'new_value': 23521.9}, {'field': 'total_amount', 'old_value': 26013.5, 'new_value': 27781.9}, {'field': 'order_count', 'old_value': 948, 'new_value': 1020}]
2025-06-22 09:00:55,004 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-22 09:00:55,473 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-22 09:00:55,473 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19143.91, 'new_value': 20783.78}, {'field': 'offline_amount', 'old_value': 37715.84, 'new_value': 39744.82}, {'field': 'total_amount', 'old_value': 56859.75, 'new_value': 60528.6}, {'field': 'order_count', 'old_value': 2097, 'new_value': 2232}]
2025-06-22 09:00:55,473 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-22 09:00:55,942 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-22 09:00:55,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14876.0, 'new_value': 16415.0}, {'field': 'total_amount', 'old_value': 14876.0, 'new_value': 16415.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 92}]
2025-06-22 09:00:55,942 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-22 09:00:56,457 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-22 09:00:56,457 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68015.96, 'new_value': 74644.46}, {'field': 'offline_amount', 'old_value': 117444.03, 'new_value': 127632.89}, {'field': 'total_amount', 'old_value': 185459.99, 'new_value': 202277.35}, {'field': 'order_count', 'old_value': 1157, 'new_value': 1203}]
2025-06-22 09:00:56,457 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-22 09:00:56,879 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-22 09:00:56,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60254.76, 'new_value': 63297.66}, {'field': 'total_amount', 'old_value': 60254.76, 'new_value': 63297.66}, {'field': 'order_count', 'old_value': 1597, 'new_value': 1693}]
2025-06-22 09:00:56,879 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-22 09:00:57,301 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-22 09:00:57,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26775.17, 'new_value': 33473.61}, {'field': 'offline_amount', 'old_value': 49284.32, 'new_value': 49518.86}, {'field': 'total_amount', 'old_value': 76059.49, 'new_value': 82992.47}, {'field': 'order_count', 'old_value': 442, 'new_value': 492}]
2025-06-22 09:00:57,301 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-22 09:00:57,786 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-22 09:00:57,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12196.0, 'new_value': 13156.0}, {'field': 'total_amount', 'old_value': 12196.0, 'new_value': 13156.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 38}]
2025-06-22 09:00:57,786 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-22 09:00:58,239 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-22 09:00:58,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31192.1, 'new_value': 32059.7}, {'field': 'total_amount', 'old_value': 31192.1, 'new_value': 32059.7}, {'field': 'order_count', 'old_value': 33, 'new_value': 35}]
2025-06-22 09:00:58,239 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-22 09:00:58,707 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-22 09:00:58,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126748.68, 'new_value': 136867.12}, {'field': 'offline_amount', 'old_value': 116608.75, 'new_value': 123282.91}, {'field': 'total_amount', 'old_value': 243357.43, 'new_value': 260150.03}, {'field': 'order_count', 'old_value': 2239, 'new_value': 2407}]
2025-06-22 09:00:58,707 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-22 09:00:59,192 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-22 09:00:59,192 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28568.0, 'new_value': 32518.0}, {'field': 'total_amount', 'old_value': 63774.0, 'new_value': 67724.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 79}]
2025-06-22 09:00:59,192 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTD
2025-06-22 09:00:59,629 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTD
2025-06-22 09:00:59,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6508.0, 'new_value': 6886.0}, {'field': 'total_amount', 'old_value': 6508.0, 'new_value': 6886.0}, {'field': 'order_count', 'old_value': 1386, 'new_value': 1387}]
2025-06-22 09:00:59,629 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-22 09:01:00,067 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-22 09:01:00,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102092.63, 'new_value': 107831.93}, {'field': 'total_amount', 'old_value': 102092.63, 'new_value': 107831.93}, {'field': 'order_count', 'old_value': 2918, 'new_value': 3085}]
2025-06-22 09:01:00,067 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-22 09:01:00,504 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-22 09:01:00,504 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11783.0, 'new_value': 12885.0}, {'field': 'total_amount', 'old_value': 11783.0, 'new_value': 12885.0}, {'field': 'order_count', 'old_value': 201, 'new_value': 220}]
2025-06-22 09:01:00,504 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-22 09:01:00,879 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-22 09:01:00,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21156.3, 'new_value': 22623.3}, {'field': 'total_amount', 'old_value': 21156.3, 'new_value': 22623.3}, {'field': 'order_count', 'old_value': 197, 'new_value': 212}]
2025-06-22 09:01:00,879 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-22 09:01:01,317 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-22 09:01:01,317 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37057.44, 'new_value': 39260.84}, {'field': 'total_amount', 'old_value': 37066.44, 'new_value': 39269.84}, {'field': 'order_count', 'old_value': 1526, 'new_value': 1630}]
2025-06-22 09:01:01,317 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-22 09:01:01,879 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-22 09:01:01,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 287320.92, 'new_value': 304904.92}, {'field': 'total_amount', 'old_value': 323304.92, 'new_value': 340888.92}, {'field': 'order_count', 'old_value': 1695, 'new_value': 1780}]
2025-06-22 09:01:01,879 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-22 09:01:02,364 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-22 09:01:02,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61932.0, 'new_value': 64742.0}, {'field': 'total_amount', 'old_value': 64747.0, 'new_value': 67557.0}, {'field': 'order_count', 'old_value': 246, 'new_value': 257}]
2025-06-22 09:01:02,364 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-22 09:01:02,801 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-22 09:01:02,801 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 153695.16, 'new_value': 163567.66}, {'field': 'offline_amount', 'old_value': 31949.41, 'new_value': 33199.14}, {'field': 'total_amount', 'old_value': 185644.57, 'new_value': 196766.8}, {'field': 'order_count', 'old_value': 767, 'new_value': 811}]
2025-06-22 09:01:02,801 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-22 09:01:03,254 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-22 09:01:03,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52440.0, 'new_value': 55185.0}, {'field': 'total_amount', 'old_value': 52440.0, 'new_value': 55185.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 37}]
2025-06-22 09:01:03,254 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-22 09:01:03,739 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-22 09:01:03,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25264.1, 'new_value': 29866.1}, {'field': 'total_amount', 'old_value': 25264.1, 'new_value': 29866.1}, {'field': 'order_count', 'old_value': 72, 'new_value': 79}]
2025-06-22 09:01:03,739 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-22 09:01:04,192 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-22 09:01:04,192 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84154.0, 'new_value': 88539.0}, {'field': 'offline_amount', 'old_value': 346723.0, 'new_value': 355496.0}, {'field': 'total_amount', 'old_value': 430877.0, 'new_value': 444035.0}, {'field': 'order_count', 'old_value': 550, 'new_value': 582}]
2025-06-22 09:01:04,192 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-22 09:01:04,629 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-22 09:01:04,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1275013.61, 'new_value': 1389494.81}, {'field': 'total_amount', 'old_value': 1333492.31, 'new_value': 1447973.51}, {'field': 'order_count', 'old_value': 2565, 'new_value': 2734}]
2025-06-22 09:01:04,629 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-22 09:01:05,145 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-22 09:01:05,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24555.73, 'new_value': 27238.13}, {'field': 'total_amount', 'old_value': 24555.73, 'new_value': 27238.13}, {'field': 'order_count', 'old_value': 45, 'new_value': 48}]
2025-06-22 09:01:05,145 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-22 09:01:05,645 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-22 09:01:05,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 219321.8, 'new_value': 230228.2}, {'field': 'total_amount', 'old_value': 219321.8, 'new_value': 230228.2}, {'field': 'order_count', 'old_value': 6613, 'new_value': 6884}]
2025-06-22 09:01:05,645 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-22 09:01:06,082 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-22 09:01:06,082 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97575.0, 'new_value': 101874.0}, {'field': 'total_amount', 'old_value': 97575.0, 'new_value': 101874.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-06-22 09:01:06,082 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-22 09:01:06,504 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-22 09:01:06,504 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 562846.0, 'new_value': 590175.0}, {'field': 'total_amount', 'old_value': 562846.0, 'new_value': 590175.0}, {'field': 'order_count', 'old_value': 2651, 'new_value': 2800}]
2025-06-22 09:01:06,504 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-22 09:01:06,989 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-22 09:01:06,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 388552.0, 'new_value': 413244.0}, {'field': 'total_amount', 'old_value': 388552.0, 'new_value': 413244.0}, {'field': 'order_count', 'old_value': 396, 'new_value': 415}]
2025-06-22 09:01:06,989 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-22 09:01:07,395 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-22 09:01:07,395 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23049.67, 'new_value': 24223.21}, {'field': 'offline_amount', 'old_value': 20992.76, 'new_value': 22296.26}, {'field': 'total_amount', 'old_value': 44042.43, 'new_value': 46519.47}, {'field': 'order_count', 'old_value': 2032, 'new_value': 2149}]
2025-06-22 09:01:07,395 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-22 09:01:07,848 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-22 09:01:07,848 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7960.41, 'new_value': 9866.89}, {'field': 'offline_amount', 'old_value': 110511.62, 'new_value': 117458.41}, {'field': 'total_amount', 'old_value': 118472.03, 'new_value': 127325.3}, {'field': 'order_count', 'old_value': 5933, 'new_value': 6245}]
2025-06-22 09:01:07,848 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-22 09:01:08,270 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-22 09:01:08,270 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44497.06, 'new_value': 47289.63}, {'field': 'offline_amount', 'old_value': 29584.92, 'new_value': 31539.7}, {'field': 'total_amount', 'old_value': 74081.98, 'new_value': 78829.33}, {'field': 'order_count', 'old_value': 4372, 'new_value': 4660}]
2025-06-22 09:01:08,270 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-22 09:01:08,770 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-22 09:01:08,770 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71902.91, 'new_value': 74670.91}, {'field': 'offline_amount', 'old_value': 76273.01, 'new_value': 79733.6}, {'field': 'total_amount', 'old_value': 148175.92, 'new_value': 154404.51}, {'field': 'order_count', 'old_value': 6076, 'new_value': 6340}]
2025-06-22 09:01:08,770 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-22 09:01:09,254 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-22 09:01:09,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 238515.42, 'new_value': 253723.42}, {'field': 'offline_amount', 'old_value': 84329.15, 'new_value': 94237.77}, {'field': 'total_amount', 'old_value': 322844.57, 'new_value': 347961.19}, {'field': 'order_count', 'old_value': 1022, 'new_value': 1084}]
2025-06-22 09:01:09,254 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-22 09:01:09,739 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-22 09:01:09,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 197892.0, 'new_value': 208092.0}, {'field': 'total_amount', 'old_value': 197892.0, 'new_value': 208092.0}, {'field': 'order_count', 'old_value': 16491, 'new_value': 17341}]
2025-06-22 09:01:09,739 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-22 09:01:10,223 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-22 09:01:10,223 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 172969.15, 'new_value': 182710.5}, {'field': 'total_amount', 'old_value': 172969.15, 'new_value': 182710.5}, {'field': 'order_count', 'old_value': 626, 'new_value': 656}]
2025-06-22 09:01:10,223 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-22 09:01:10,614 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-22 09:01:10,614 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 270867.82, 'new_value': 290858.74}, {'field': 'total_amount', 'old_value': 270867.82, 'new_value': 290858.74}, {'field': 'order_count', 'old_value': 936, 'new_value': 1002}]
2025-06-22 09:01:10,614 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-22 09:01:11,176 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-22 09:01:11,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128246.0, 'new_value': 132870.0}, {'field': 'total_amount', 'old_value': 136865.0, 'new_value': 141489.0}, {'field': 'order_count', 'old_value': 9866, 'new_value': 10189}]
2025-06-22 09:01:11,176 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-22 09:01:11,707 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-22 09:01:11,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7261.0, 'new_value': 7877.0}, {'field': 'offline_amount', 'old_value': 2231.0, 'new_value': 2283.0}, {'field': 'total_amount', 'old_value': 9492.0, 'new_value': 10160.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 99}]
2025-06-22 09:01:11,707 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-22 09:01:12,254 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-22 09:01:12,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22488.94, 'new_value': 23287.94}, {'field': 'offline_amount', 'old_value': 25687.42, 'new_value': 26386.42}, {'field': 'total_amount', 'old_value': 48176.36, 'new_value': 49674.36}, {'field': 'order_count', 'old_value': 6660, 'new_value': 6665}]
2025-06-22 09:01:12,254 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-22 09:01:12,692 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-22 09:01:12,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137547.9, 'new_value': 155235.5}, {'field': 'total_amount', 'old_value': 248846.9, 'new_value': 266534.5}, {'field': 'order_count', 'old_value': 6712, 'new_value': 7139}]
2025-06-22 09:01:12,707 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-22 09:01:13,145 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-22 09:01:13,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85109.0, 'new_value': 90617.0}, {'field': 'total_amount', 'old_value': 85109.0, 'new_value': 90617.0}, {'field': 'order_count', 'old_value': 211, 'new_value': 224}]
2025-06-22 09:01:13,145 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-22 09:01:13,582 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-22 09:01:13,582 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 633712.0, 'new_value': 660557.0}, {'field': 'total_amount', 'old_value': 633712.0, 'new_value': 660557.0}, {'field': 'order_count', 'old_value': 821, 'new_value': 858}]
2025-06-22 09:01:13,582 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-22 09:01:14,020 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-22 09:01:14,020 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75201.4, 'new_value': 79342.2}, {'field': 'total_amount', 'old_value': 75201.4, 'new_value': 79342.2}, {'field': 'order_count', 'old_value': 341, 'new_value': 359}]
2025-06-22 09:01:14,020 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-22 09:01:14,426 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-22 09:01:14,426 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32194.71, 'new_value': 33600.11}, {'field': 'offline_amount', 'old_value': 36436.44, 'new_value': 38196.62}, {'field': 'total_amount', 'old_value': 68631.15, 'new_value': 71796.73}, {'field': 'order_count', 'old_value': 3566, 'new_value': 3722}]
2025-06-22 09:01:14,426 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-22 09:01:14,911 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-22 09:01:14,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107196.1, 'new_value': 116476.8}, {'field': 'total_amount', 'old_value': 107196.1, 'new_value': 116476.8}, {'field': 'order_count', 'old_value': 513, 'new_value': 558}]
2025-06-22 09:01:14,911 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-22 09:01:15,332 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-22 09:01:15,332 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15046.89, 'new_value': 15517.23}, {'field': 'offline_amount', 'old_value': 104099.0, 'new_value': 128367.0}, {'field': 'total_amount', 'old_value': 119145.89, 'new_value': 143884.23}, {'field': 'order_count', 'old_value': 66, 'new_value': 71}]
2025-06-22 09:01:15,332 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-22 09:01:15,739 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-22 09:01:15,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35820.0, 'new_value': 42220.0}, {'field': 'total_amount', 'old_value': 35820.0, 'new_value': 42220.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 41}]
2025-06-22 09:01:15,739 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-22 09:01:16,254 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-22 09:01:16,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121946.5, 'new_value': 138688.3}, {'field': 'total_amount', 'old_value': 121946.5, 'new_value': 138688.3}, {'field': 'order_count', 'old_value': 3389, 'new_value': 4249}]
2025-06-22 09:01:16,254 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF11
2025-06-22 09:01:16,801 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF11
2025-06-22 09:01:16,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18919.0, 'new_value': 21298.0}, {'field': 'total_amount', 'old_value': 18919.0, 'new_value': 21298.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-06-22 09:01:16,801 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-22 09:01:17,207 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-22 09:01:17,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174655.9, 'new_value': 181974.9}, {'field': 'total_amount', 'old_value': 185282.9, 'new_value': 192601.9}, {'field': 'order_count', 'old_value': 769, 'new_value': 803}]
2025-06-22 09:01:17,207 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-22 09:01:17,629 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-22 09:01:17,629 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5566.63, 'new_value': 5846.63}, {'field': 'offline_amount', 'old_value': 14036.65, 'new_value': 14742.95}, {'field': 'total_amount', 'old_value': 19603.28, 'new_value': 20589.58}, {'field': 'order_count', 'old_value': 198, 'new_value': 209}]
2025-06-22 09:01:17,629 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-22 09:01:18,098 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-22 09:01:18,098 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 171345.13, 'new_value': 183371.13}, {'field': 'offline_amount', 'old_value': 11540.3, 'new_value': 13876.3}, {'field': 'total_amount', 'old_value': 182885.43, 'new_value': 197247.43}, {'field': 'order_count', 'old_value': 13130, 'new_value': 13791}]
2025-06-22 09:01:18,098 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-22 09:01:18,535 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-22 09:01:18,535 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52072.06, 'new_value': 58977.69}, {'field': 'offline_amount', 'old_value': 236426.2, 'new_value': 242760.35}, {'field': 'total_amount', 'old_value': 288498.26, 'new_value': 301738.04}, {'field': 'order_count', 'old_value': 4245, 'new_value': 4397}]
2025-06-22 09:01:18,535 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-22 09:01:19,004 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-22 09:01:19,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 268450.0, 'new_value': 288330.0}, {'field': 'total_amount', 'old_value': 282008.0, 'new_value': 301888.0}, {'field': 'order_count', 'old_value': 229, 'new_value': 248}]
2025-06-22 09:01:19,004 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-22 09:01:19,426 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-22 09:01:19,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50081.01, 'new_value': 53367.01}, {'field': 'total_amount', 'old_value': 51818.01, 'new_value': 55104.01}, {'field': 'order_count', 'old_value': 321, 'new_value': 336}]
2025-06-22 09:01:19,426 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-22 09:01:19,895 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-22 09:01:19,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 275961.1, 'new_value': 294782.1}, {'field': 'total_amount', 'old_value': 275961.1, 'new_value': 294782.1}, {'field': 'order_count', 'old_value': 340, 'new_value': 355}]
2025-06-22 09:01:19,910 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-22 09:01:20,364 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-22 09:01:20,364 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58123.0, 'new_value': 60109.0}, {'field': 'offline_amount', 'old_value': 250664.0, 'new_value': 266570.0}, {'field': 'total_amount', 'old_value': 308787.0, 'new_value': 326679.0}, {'field': 'order_count', 'old_value': 1161, 'new_value': 1224}]
2025-06-22 09:01:20,364 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-22 09:01:20,785 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-22 09:01:20,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192273.13, 'new_value': 206163.62}, {'field': 'total_amount', 'old_value': 192273.13, 'new_value': 206163.62}, {'field': 'order_count', 'old_value': 905, 'new_value': 973}]
2025-06-22 09:01:20,785 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-22 09:01:21,160 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-22 09:01:21,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223804.0, 'new_value': 239673.0}, {'field': 'total_amount', 'old_value': 223804.0, 'new_value': 239673.0}, {'field': 'order_count', 'old_value': 281, 'new_value': 301}]
2025-06-22 09:01:21,160 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-22 09:01:21,660 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-22 09:01:21,660 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81659.75, 'new_value': 85549.1}, {'field': 'offline_amount', 'old_value': 104835.4, 'new_value': 110558.94}, {'field': 'total_amount', 'old_value': 186495.15, 'new_value': 196108.04}, {'field': 'order_count', 'old_value': 7916, 'new_value': 8147}]
2025-06-22 09:01:21,660 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-22 09:01:22,082 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-22 09:01:22,082 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 146291.93, 'new_value': 160070.43}, {'field': 'total_amount', 'old_value': 228007.13, 'new_value': 241785.63}, {'field': 'order_count', 'old_value': 370, 'new_value': 387}]
2025-06-22 09:01:22,082 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-22 09:01:22,535 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-22 09:01:22,535 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2075.0, 'new_value': 2175.0}, {'field': 'offline_amount', 'old_value': 26185.0, 'new_value': 26985.0}, {'field': 'total_amount', 'old_value': 28260.0, 'new_value': 29160.0}, {'field': 'order_count', 'old_value': 359, 'new_value': 367}]
2025-06-22 09:01:22,535 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-22 09:01:23,035 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-22 09:01:23,035 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 115041.0, 'new_value': 121667.0}, {'field': 'offline_amount', 'old_value': 134850.0, 'new_value': 141501.0}, {'field': 'total_amount', 'old_value': 249891.0, 'new_value': 263168.0}, {'field': 'order_count', 'old_value': 179319, 'new_value': 179376}]
2025-06-22 09:01:23,035 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG21
2025-06-22 09:01:23,504 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG21
2025-06-22 09:01:23,504 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 516747.0, 'new_value': 531737.0}, {'field': 'total_amount', 'old_value': 516747.0, 'new_value': 531737.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 69}]
2025-06-22 09:01:23,504 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH21
2025-06-22 09:01:24,020 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH21
2025-06-22 09:01:24,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13903.0, 'new_value': 15329.0}, {'field': 'total_amount', 'old_value': 13903.0, 'new_value': 15329.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 31}]
2025-06-22 09:01:24,020 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-22 09:01:24,489 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-22 09:01:24,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 143632.0, 'new_value': 148050.0}, {'field': 'offline_amount', 'old_value': 913551.0, 'new_value': 966633.0}, {'field': 'total_amount', 'old_value': 1057183.0, 'new_value': 1114683.0}, {'field': 'order_count', 'old_value': 27346, 'new_value': 28726}]
2025-06-22 09:01:24,489 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-22 09:01:24,910 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-22 09:01:24,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244722.0, 'new_value': 252691.0}, {'field': 'total_amount', 'old_value': 244722.0, 'new_value': 252691.0}, {'field': 'order_count', 'old_value': 5655, 'new_value': 5845}]
2025-06-22 09:01:24,910 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-22 09:01:25,395 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-22 09:01:25,395 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13875.0, 'new_value': 16176.0}, {'field': 'total_amount', 'old_value': 13875.0, 'new_value': 16176.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 38}]
2025-06-22 09:01:25,395 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-22 09:01:25,848 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-22 09:01:25,848 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35402.25, 'new_value': 37566.75}, {'field': 'total_amount', 'old_value': 35402.25, 'new_value': 37566.75}, {'field': 'order_count', 'old_value': 970, 'new_value': 1010}]
2025-06-22 09:01:25,848 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-22 09:01:26,301 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-22 09:01:26,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39615.1, 'new_value': 41266.1}, {'field': 'total_amount', 'old_value': 39615.1, 'new_value': 41266.1}, {'field': 'order_count', 'old_value': 207, 'new_value': 217}]
2025-06-22 09:01:26,301 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-22 09:01:26,754 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-22 09:01:26,754 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 184289.9, 'new_value': 197134.9}, {'field': 'offline_amount', 'old_value': 11001.5, 'new_value': 13494.5}, {'field': 'total_amount', 'old_value': 195291.4, 'new_value': 210629.4}, {'field': 'order_count', 'old_value': 1989, 'new_value': 2267}]
2025-06-22 09:01:26,754 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-22 09:01:27,207 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-22 09:01:27,207 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26629.5, 'new_value': 27735.8}, {'field': 'offline_amount', 'old_value': 44139.3, 'new_value': 45601.6}, {'field': 'total_amount', 'old_value': 70768.8, 'new_value': 73337.4}, {'field': 'order_count', 'old_value': 2875, 'new_value': 2965}]
2025-06-22 09:01:27,207 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-22 09:01:27,692 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-22 09:01:27,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27030.09, 'new_value': 28174.39}, {'field': 'total_amount', 'old_value': 27796.09, 'new_value': 28940.39}, {'field': 'order_count', 'old_value': 263, 'new_value': 275}]
2025-06-22 09:01:27,692 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-22 09:01:28,067 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-22 09:01:28,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29991.6, 'new_value': 32480.6}, {'field': 'total_amount', 'old_value': 29991.6, 'new_value': 32480.6}, {'field': 'order_count', 'old_value': 82, 'new_value': 91}]
2025-06-22 09:01:28,067 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-22 09:01:28,489 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-22 09:01:28,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 349017.81, 'new_value': 354840.72}, {'field': 'total_amount', 'old_value': 378421.74, 'new_value': 384244.65}, {'field': 'order_count', 'old_value': 446, 'new_value': 467}]
2025-06-22 09:01:28,489 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-22 09:01:28,926 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-22 09:01:28,926 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69235.0, 'new_value': 71905.0}, {'field': 'total_amount', 'old_value': 69235.0, 'new_value': 71905.0}, {'field': 'order_count', 'old_value': 355, 'new_value': 368}]
2025-06-22 09:01:28,926 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-22 09:01:29,426 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-22 09:01:29,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49488.0, 'new_value': 60928.0}, {'field': 'total_amount', 'old_value': 68328.0, 'new_value': 79768.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 22}]
2025-06-22 09:01:29,426 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-22 09:01:29,832 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-22 09:01:29,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 368067.86, 'new_value': 397091.86}, {'field': 'total_amount', 'old_value': 368067.86, 'new_value': 397091.86}, {'field': 'order_count', 'old_value': 2446, 'new_value': 2676}]
2025-06-22 09:01:29,832 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-22 09:01:30,270 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-22 09:01:30,270 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5387.85, 'new_value': 5990.75}, {'field': 'offline_amount', 'old_value': 33423.3, 'new_value': 36164.3}, {'field': 'total_amount', 'old_value': 38811.15, 'new_value': 42155.05}, {'field': 'order_count', 'old_value': 291, 'new_value': 322}]
2025-06-22 09:01:30,270 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-22 09:01:30,817 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-22 09:01:30,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129049.84, 'new_value': 136569.03}, {'field': 'offline_amount', 'old_value': 35428.56, 'new_value': 36999.46}, {'field': 'total_amount', 'old_value': 164478.4, 'new_value': 173568.49}, {'field': 'order_count', 'old_value': 702, 'new_value': 741}]
2025-06-22 09:01:30,817 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-22 09:01:31,317 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-22 09:01:31,317 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25099.9, 'new_value': 26523.4}, {'field': 'offline_amount', 'old_value': 95671.0, 'new_value': 101920.0}, {'field': 'total_amount', 'old_value': 120770.9, 'new_value': 128443.4}, {'field': 'order_count', 'old_value': 1506, 'new_value': 1566}]
2025-06-22 09:01:31,317 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-22 09:01:31,801 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-22 09:01:31,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69654.0, 'new_value': 71406.0}, {'field': 'total_amount', 'old_value': 69654.0, 'new_value': 71406.0}, {'field': 'order_count', 'old_value': 1056, 'new_value': 1096}]
2025-06-22 09:01:31,801 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-22 09:01:32,239 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-22 09:01:32,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45875.0, 'new_value': 46074.0}, {'field': 'total_amount', 'old_value': 45875.0, 'new_value': 46074.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-22 09:01:32,239 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-22 09:01:32,645 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-22 09:01:32,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91204.0, 'new_value': 104530.0}, {'field': 'total_amount', 'old_value': 91204.0, 'new_value': 104530.0}, {'field': 'order_count', 'old_value': 413, 'new_value': 454}]
2025-06-22 09:01:32,645 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-22 09:01:33,129 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-22 09:01:33,129 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36679.52, 'new_value': 39269.2}, {'field': 'total_amount', 'old_value': 36679.52, 'new_value': 39269.2}, {'field': 'order_count', 'old_value': 4666, 'new_value': 4986}]
2025-06-22 09:01:33,129 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-22 09:01:33,582 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-22 09:01:33,582 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 247005.0, 'new_value': 262097.21}, {'field': 'total_amount', 'old_value': 247005.0, 'new_value': 262097.21}, {'field': 'order_count', 'old_value': 685, 'new_value': 731}]
2025-06-22 09:01:33,582 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-22 09:01:34,051 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-22 09:01:34,051 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90244.0, 'new_value': 95622.0}, {'field': 'total_amount', 'old_value': 90244.0, 'new_value': 95622.0}, {'field': 'order_count', 'old_value': 5112, 'new_value': 5417}]
2025-06-22 09:01:34,051 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-22 09:01:34,520 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-22 09:01:34,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177002.9, 'new_value': 184460.9}, {'field': 'total_amount', 'old_value': 177002.9, 'new_value': 184460.9}, {'field': 'order_count', 'old_value': 6143, 'new_value': 6410}]
2025-06-22 09:01:34,520 - INFO - 日期 2025-06 处理完成 - 更新: 129 条，插入: 0 条，错误: 0 条
2025-06-22 09:01:34,520 - INFO - 数据同步完成！更新: 129 条，插入: 0 条，错误: 0 条
2025-06-22 09:01:34,520 - INFO - =================同步完成====================
2025-06-22 12:00:03,244 - INFO - =================使用默认全量同步=============
2025-06-22 12:00:05,009 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-22 12:00:05,009 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-22 12:00:05,056 - INFO - 开始处理日期: 2025-01
2025-06-22 12:00:05,056 - INFO - Request Parameters - Page 1:
2025-06-22 12:00:05,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:05,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:06,509 - INFO - Response - Page 1:
2025-06-22 12:00:06,712 - INFO - 第 1 页获取到 100 条记录
2025-06-22 12:00:06,712 - INFO - Request Parameters - Page 2:
2025-06-22 12:00:06,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:06,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:07,212 - INFO - Response - Page 2:
2025-06-22 12:00:07,415 - INFO - 第 2 页获取到 100 条记录
2025-06-22 12:00:07,415 - INFO - Request Parameters - Page 3:
2025-06-22 12:00:07,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:07,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:07,962 - INFO - Response - Page 3:
2025-06-22 12:00:08,165 - INFO - 第 3 页获取到 100 条记录
2025-06-22 12:00:08,165 - INFO - Request Parameters - Page 4:
2025-06-22 12:00:08,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:08,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:08,665 - INFO - Response - Page 4:
2025-06-22 12:00:08,868 - INFO - 第 4 页获取到 100 条记录
2025-06-22 12:00:08,868 - INFO - Request Parameters - Page 5:
2025-06-22 12:00:08,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:08,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:09,478 - INFO - Response - Page 5:
2025-06-22 12:00:09,681 - INFO - 第 5 页获取到 100 条记录
2025-06-22 12:00:09,681 - INFO - Request Parameters - Page 6:
2025-06-22 12:00:09,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:09,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:10,243 - INFO - Response - Page 6:
2025-06-22 12:00:10,447 - INFO - 第 6 页获取到 100 条记录
2025-06-22 12:00:10,447 - INFO - Request Parameters - Page 7:
2025-06-22 12:00:10,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:10,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:10,900 - INFO - Response - Page 7:
2025-06-22 12:00:11,103 - INFO - 第 7 页获取到 82 条记录
2025-06-22 12:00:11,103 - INFO - 查询完成，共获取到 682 条记录
2025-06-22 12:00:11,103 - INFO - 获取到 682 条表单数据
2025-06-22 12:00:11,103 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-22 12:00:11,118 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 12:00:11,118 - INFO - 开始处理日期: 2025-02
2025-06-22 12:00:11,118 - INFO - Request Parameters - Page 1:
2025-06-22 12:00:11,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:11,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:11,618 - INFO - Response - Page 1:
2025-06-22 12:00:11,822 - INFO - 第 1 页获取到 100 条记录
2025-06-22 12:00:11,822 - INFO - Request Parameters - Page 2:
2025-06-22 12:00:11,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:11,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:12,306 - INFO - Response - Page 2:
2025-06-22 12:00:12,509 - INFO - 第 2 页获取到 100 条记录
2025-06-22 12:00:12,509 - INFO - Request Parameters - Page 3:
2025-06-22 12:00:12,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:12,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:13,040 - INFO - Response - Page 3:
2025-06-22 12:00:13,243 - INFO - 第 3 页获取到 100 条记录
2025-06-22 12:00:13,243 - INFO - Request Parameters - Page 4:
2025-06-22 12:00:13,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:13,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:13,790 - INFO - Response - Page 4:
2025-06-22 12:00:13,993 - INFO - 第 4 页获取到 100 条记录
2025-06-22 12:00:13,993 - INFO - Request Parameters - Page 5:
2025-06-22 12:00:13,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:13,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:14,478 - INFO - Response - Page 5:
2025-06-22 12:00:14,681 - INFO - 第 5 页获取到 100 条记录
2025-06-22 12:00:14,681 - INFO - Request Parameters - Page 6:
2025-06-22 12:00:14,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:14,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:15,165 - INFO - Response - Page 6:
2025-06-22 12:00:15,368 - INFO - 第 6 页获取到 100 条记录
2025-06-22 12:00:15,368 - INFO - Request Parameters - Page 7:
2025-06-22 12:00:15,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:15,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:15,915 - INFO - Response - Page 7:
2025-06-22 12:00:16,118 - INFO - 第 7 页获取到 70 条记录
2025-06-22 12:00:16,118 - INFO - 查询完成，共获取到 670 条记录
2025-06-22 12:00:16,118 - INFO - 获取到 670 条表单数据
2025-06-22 12:00:16,118 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-22 12:00:16,134 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 12:00:16,134 - INFO - 开始处理日期: 2025-03
2025-06-22 12:00:16,134 - INFO - Request Parameters - Page 1:
2025-06-22 12:00:16,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:16,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:16,665 - INFO - Response - Page 1:
2025-06-22 12:00:16,868 - INFO - 第 1 页获取到 100 条记录
2025-06-22 12:00:16,868 - INFO - Request Parameters - Page 2:
2025-06-22 12:00:16,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:16,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:17,368 - INFO - Response - Page 2:
2025-06-22 12:00:17,572 - INFO - 第 2 页获取到 100 条记录
2025-06-22 12:00:17,572 - INFO - Request Parameters - Page 3:
2025-06-22 12:00:17,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:17,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:18,243 - INFO - Response - Page 3:
2025-06-22 12:00:18,447 - INFO - 第 3 页获取到 100 条记录
2025-06-22 12:00:18,447 - INFO - Request Parameters - Page 4:
2025-06-22 12:00:18,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:18,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:18,947 - INFO - Response - Page 4:
2025-06-22 12:00:19,150 - INFO - 第 4 页获取到 100 条记录
2025-06-22 12:00:19,150 - INFO - Request Parameters - Page 5:
2025-06-22 12:00:19,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:19,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:19,665 - INFO - Response - Page 5:
2025-06-22 12:00:19,868 - INFO - 第 5 页获取到 100 条记录
2025-06-22 12:00:19,868 - INFO - Request Parameters - Page 6:
2025-06-22 12:00:19,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:19,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:20,415 - INFO - Response - Page 6:
2025-06-22 12:00:20,618 - INFO - 第 6 页获取到 100 条记录
2025-06-22 12:00:20,618 - INFO - Request Parameters - Page 7:
2025-06-22 12:00:20,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:20,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:21,118 - INFO - Response - Page 7:
2025-06-22 12:00:21,322 - INFO - 第 7 页获取到 61 条记录
2025-06-22 12:00:21,322 - INFO - 查询完成，共获取到 661 条记录
2025-06-22 12:00:21,322 - INFO - 获取到 661 条表单数据
2025-06-22 12:00:21,322 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-22 12:00:21,337 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 12:00:21,337 - INFO - 开始处理日期: 2025-04
2025-06-22 12:00:21,337 - INFO - Request Parameters - Page 1:
2025-06-22 12:00:21,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:21,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:21,822 - INFO - Response - Page 1:
2025-06-22 12:00:22,025 - INFO - 第 1 页获取到 100 条记录
2025-06-22 12:00:22,025 - INFO - Request Parameters - Page 2:
2025-06-22 12:00:22,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:22,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:22,540 - INFO - Response - Page 2:
2025-06-22 12:00:22,743 - INFO - 第 2 页获取到 100 条记录
2025-06-22 12:00:22,743 - INFO - Request Parameters - Page 3:
2025-06-22 12:00:22,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:22,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:23,228 - INFO - Response - Page 3:
2025-06-22 12:00:23,431 - INFO - 第 3 页获取到 100 条记录
2025-06-22 12:00:23,431 - INFO - Request Parameters - Page 4:
2025-06-22 12:00:23,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:23,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:23,900 - INFO - Response - Page 4:
2025-06-22 12:00:24,103 - INFO - 第 4 页获取到 100 条记录
2025-06-22 12:00:24,103 - INFO - Request Parameters - Page 5:
2025-06-22 12:00:24,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:24,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:24,634 - INFO - Response - Page 5:
2025-06-22 12:00:24,837 - INFO - 第 5 页获取到 100 条记录
2025-06-22 12:00:24,837 - INFO - Request Parameters - Page 6:
2025-06-22 12:00:24,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:24,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:25,275 - INFO - Response - Page 6:
2025-06-22 12:00:25,478 - INFO - 第 6 页获取到 100 条记录
2025-06-22 12:00:25,478 - INFO - Request Parameters - Page 7:
2025-06-22 12:00:25,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:25,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:25,900 - INFO - Response - Page 7:
2025-06-22 12:00:26,103 - INFO - 第 7 页获取到 56 条记录
2025-06-22 12:00:26,103 - INFO - 查询完成，共获取到 656 条记录
2025-06-22 12:00:26,103 - INFO - 获取到 656 条表单数据
2025-06-22 12:00:26,103 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-22 12:00:26,118 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 12:00:26,118 - INFO - 开始处理日期: 2025-05
2025-06-22 12:00:26,118 - INFO - Request Parameters - Page 1:
2025-06-22 12:00:26,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:26,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:26,618 - INFO - Response - Page 1:
2025-06-22 12:00:26,821 - INFO - 第 1 页获取到 100 条记录
2025-06-22 12:00:26,821 - INFO - Request Parameters - Page 2:
2025-06-22 12:00:26,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:26,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:27,321 - INFO - Response - Page 2:
2025-06-22 12:00:27,525 - INFO - 第 2 页获取到 100 条记录
2025-06-22 12:00:27,525 - INFO - Request Parameters - Page 3:
2025-06-22 12:00:27,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:27,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:28,040 - INFO - Response - Page 3:
2025-06-22 12:00:28,243 - INFO - 第 3 页获取到 100 条记录
2025-06-22 12:00:28,243 - INFO - Request Parameters - Page 4:
2025-06-22 12:00:28,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:28,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:28,790 - INFO - Response - Page 4:
2025-06-22 12:00:28,993 - INFO - 第 4 页获取到 100 条记录
2025-06-22 12:00:28,993 - INFO - Request Parameters - Page 5:
2025-06-22 12:00:28,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:28,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:29,540 - INFO - Response - Page 5:
2025-06-22 12:00:29,743 - INFO - 第 5 页获取到 100 条记录
2025-06-22 12:00:29,743 - INFO - Request Parameters - Page 6:
2025-06-22 12:00:29,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:29,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:30,228 - INFO - Response - Page 6:
2025-06-22 12:00:30,431 - INFO - 第 6 页获取到 100 条记录
2025-06-22 12:00:30,431 - INFO - Request Parameters - Page 7:
2025-06-22 12:00:30,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:30,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:30,915 - INFO - Response - Page 7:
2025-06-22 12:00:31,118 - INFO - 第 7 页获取到 65 条记录
2025-06-22 12:00:31,118 - INFO - 查询完成，共获取到 665 条记录
2025-06-22 12:00:31,118 - INFO - 获取到 665 条表单数据
2025-06-22 12:00:31,118 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-22 12:00:31,134 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 12:00:31,134 - INFO - 开始处理日期: 2025-06
2025-06-22 12:00:31,134 - INFO - Request Parameters - Page 1:
2025-06-22 12:00:31,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:31,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:31,759 - INFO - Response - Page 1:
2025-06-22 12:00:31,962 - INFO - 第 1 页获取到 100 条记录
2025-06-22 12:00:31,962 - INFO - Request Parameters - Page 2:
2025-06-22 12:00:31,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:31,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:32,431 - INFO - Response - Page 2:
2025-06-22 12:00:32,634 - INFO - 第 2 页获取到 100 条记录
2025-06-22 12:00:32,634 - INFO - Request Parameters - Page 3:
2025-06-22 12:00:32,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:32,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:33,243 - INFO - Response - Page 3:
2025-06-22 12:00:33,446 - INFO - 第 3 页获取到 100 条记录
2025-06-22 12:00:33,446 - INFO - Request Parameters - Page 4:
2025-06-22 12:00:33,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:33,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:33,993 - INFO - Response - Page 4:
2025-06-22 12:00:34,196 - INFO - 第 4 页获取到 100 条记录
2025-06-22 12:00:34,196 - INFO - Request Parameters - Page 5:
2025-06-22 12:00:34,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:34,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:34,696 - INFO - Response - Page 5:
2025-06-22 12:00:34,900 - INFO - 第 5 页获取到 100 条记录
2025-06-22 12:00:34,900 - INFO - Request Parameters - Page 6:
2025-06-22 12:00:34,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:34,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:35,384 - INFO - Response - Page 6:
2025-06-22 12:00:35,587 - INFO - 第 6 页获取到 100 条记录
2025-06-22 12:00:35,587 - INFO - Request Parameters - Page 7:
2025-06-22 12:00:35,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 12:00:35,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 12:00:35,900 - INFO - Response - Page 7:
2025-06-22 12:00:36,103 - INFO - 第 7 页获取到 23 条记录
2025-06-22 12:00:36,103 - INFO - 查询完成，共获取到 623 条记录
2025-06-22 12:00:36,103 - INFO - 获取到 623 条表单数据
2025-06-22 12:00:36,103 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-22 12:00:36,103 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-22 12:00:36,603 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-22 12:00:36,603 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 572140.0, 'new_value': 610564.0}, {'field': 'total_amount', 'old_value': 572140.0, 'new_value': 610564.0}, {'field': 'order_count', 'old_value': 4006, 'new_value': 4294}]
2025-06-22 12:00:36,603 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-22 12:00:36,978 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-22 12:00:36,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106345.39, 'new_value': 117691.14}, {'field': 'total_amount', 'old_value': 106345.39, 'new_value': 117691.14}, {'field': 'order_count', 'old_value': 2810, 'new_value': 3060}]
2025-06-22 12:00:36,978 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-22 12:00:37,462 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-22 12:00:37,462 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71080.67, 'new_value': 75821.94}, {'field': 'offline_amount', 'old_value': 737207.98, 'new_value': 773361.63}, {'field': 'total_amount', 'old_value': 804296.16, 'new_value': 845191.08}, {'field': 'order_count', 'old_value': 3887, 'new_value': 4088}]
2025-06-22 12:00:37,462 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMRX
2025-06-22 12:00:37,915 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMRX
2025-06-22 12:00:37,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89445.6, 'new_value': 108769.2}, {'field': 'total_amount', 'old_value': 89445.6, 'new_value': 108769.2}, {'field': 'order_count', 'old_value': 18, 'new_value': 23}]
2025-06-22 12:00:37,915 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-22 12:00:38,415 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-22 12:00:38,415 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22000.86, 'new_value': 22838.86}, {'field': 'offline_amount', 'old_value': 12010.82, 'new_value': 12809.82}, {'field': 'total_amount', 'old_value': 34011.68, 'new_value': 35648.68}, {'field': 'order_count', 'old_value': 1424, 'new_value': 1483}]
2025-06-22 12:00:38,415 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-22 12:00:38,946 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-22 12:00:38,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 362009.0, 'new_value': 366774.0}, {'field': 'offline_amount', 'old_value': 138012.0, 'new_value': 143658.0}, {'field': 'total_amount', 'old_value': 500021.0, 'new_value': 510432.0}, {'field': 'order_count', 'old_value': 518, 'new_value': 553}]
2025-06-22 12:00:38,946 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMXX
2025-06-22 12:00:39,337 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMXX
2025-06-22 12:00:39,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12070.0, 'new_value': 12976.0}, {'field': 'total_amount', 'old_value': 12070.0, 'new_value': 12976.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-06-22 12:00:39,337 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-22 12:00:39,743 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-22 12:00:39,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21641.82, 'new_value': 23290.82}, {'field': 'total_amount', 'old_value': 21641.82, 'new_value': 23290.82}, {'field': 'order_count', 'old_value': 69, 'new_value': 74}]
2025-06-22 12:00:39,743 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Y
2025-06-22 12:00:40,212 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Y
2025-06-22 12:00:40,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65125.39, 'new_value': 76025.39}, {'field': 'total_amount', 'old_value': 65125.39, 'new_value': 76025.39}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-06-22 12:00:40,212 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM1Y
2025-06-22 12:00:40,603 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM1Y
2025-06-22 12:00:40,603 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220753.0, 'new_value': 255553.0}, {'field': 'total_amount', 'old_value': 220753.0, 'new_value': 255553.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 31}]
2025-06-22 12:00:40,603 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Y
2025-06-22 12:00:41,103 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Y
2025-06-22 12:00:41,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188965.0, 'new_value': 208565.0}, {'field': 'total_amount', 'old_value': 188965.0, 'new_value': 208565.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-06-22 12:00:41,103 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM3Y
2025-06-22 12:00:41,556 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM3Y
2025-06-22 12:00:41,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199683.0, 'new_value': 215483.0}, {'field': 'total_amount', 'old_value': 199683.0, 'new_value': 215483.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-06-22 12:00:41,556 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Y
2025-06-22 12:00:42,040 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Y
2025-06-22 12:00:42,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33844.47, 'new_value': 38378.07}, {'field': 'offline_amount', 'old_value': 29772.53, 'new_value': 36589.65}, {'field': 'total_amount', 'old_value': 63617.0, 'new_value': 74967.72}, {'field': 'order_count', 'old_value': 2220, 'new_value': 2648}]
2025-06-22 12:00:42,040 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Y
2025-06-22 12:00:42,478 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Y
2025-06-22 12:00:42,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6521.0, 'new_value': 7286.0}, {'field': 'offline_amount', 'old_value': 28765.9, 'new_value': 28831.9}, {'field': 'total_amount', 'old_value': 35286.9, 'new_value': 36117.9}, {'field': 'order_count', 'old_value': 60, 'new_value': 64}]
2025-06-22 12:00:42,478 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-22 12:00:42,962 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-22 12:00:42,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39460.0, 'new_value': 42020.0}, {'field': 'total_amount', 'old_value': 48660.0, 'new_value': 51220.0}, {'field': 'order_count', 'old_value': 519, 'new_value': 550}]
2025-06-22 12:00:42,962 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMY
2025-06-22 12:00:43,446 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMY
2025-06-22 12:00:43,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 852372.0, 'new_value': 939705.0}, {'field': 'offline_amount', 'old_value': 250986.0, 'new_value': 272783.0}, {'field': 'total_amount', 'old_value': 1103358.0, 'new_value': 1212488.0}, {'field': 'order_count', 'old_value': 1167, 'new_value': 1318}]
2025-06-22 12:00:43,446 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-22 12:00:43,868 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-22 12:00:43,868 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 186218.0, 'new_value': 199697.0}, {'field': 'total_amount', 'old_value': 186218.0, 'new_value': 199697.0}, {'field': 'order_count', 'old_value': 938, 'new_value': 1000}]
2025-06-22 12:00:43,868 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXY
2025-06-22 12:00:44,306 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXY
2025-06-22 12:00:44,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33376.46, 'new_value': 36991.16}, {'field': 'total_amount', 'old_value': 33376.46, 'new_value': 36991.16}, {'field': 'order_count', 'old_value': 83, 'new_value': 91}]
2025-06-22 12:00:44,306 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-22 12:00:44,774 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-22 12:00:44,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84576.07, 'new_value': 88206.08}, {'field': 'total_amount', 'old_value': 84576.07, 'new_value': 88206.08}, {'field': 'order_count', 'old_value': 172, 'new_value': 178}]
2025-06-22 12:00:44,774 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-22 12:00:45,243 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-22 12:00:45,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127076.72, 'new_value': 136616.81}, {'field': 'total_amount', 'old_value': 127076.72, 'new_value': 136616.81}, {'field': 'order_count', 'old_value': 797, 'new_value': 871}]
2025-06-22 12:00:45,243 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM1Z
2025-06-22 12:00:45,681 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM1Z
2025-06-22 12:00:45,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18957.0, 'new_value': 19453.0}, {'field': 'total_amount', 'old_value': 18957.0, 'new_value': 19453.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-06-22 12:00:45,681 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-22 12:00:46,071 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-22 12:00:46,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 451255.57, 'new_value': 482006.56}, {'field': 'total_amount', 'old_value': 451255.57, 'new_value': 482006.56}, {'field': 'order_count', 'old_value': 3155, 'new_value': 3396}]
2025-06-22 12:00:46,071 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-22 12:00:46,587 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-22 12:00:46,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 139.0, 'new_value': 848.0}, {'field': 'offline_amount', 'old_value': 8749.5, 'new_value': 9763.5}, {'field': 'total_amount', 'old_value': 8888.5, 'new_value': 10611.5}, {'field': 'order_count', 'old_value': 111, 'new_value': 116}]
2025-06-22 12:00:46,587 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-22 12:00:47,071 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-22 12:00:47,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 753689.7, 'new_value': 841880.0}, {'field': 'total_amount', 'old_value': 838046.0, 'new_value': 926236.3}, {'field': 'order_count', 'old_value': 69, 'new_value': 77}]
2025-06-22 12:00:47,071 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDZ
2025-06-22 12:00:47,540 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDZ
2025-06-22 12:00:47,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34797.12, 'new_value': 38878.21}, {'field': 'total_amount', 'old_value': 34797.12, 'new_value': 38878.21}, {'field': 'order_count', 'old_value': 2530, 'new_value': 2819}]
2025-06-22 12:00:47,540 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMIZ
2025-06-22 12:00:47,978 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMIZ
2025-06-22 12:00:47,978 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1365.5, 'new_value': 1764.5}, {'field': 'offline_amount', 'old_value': 37405.2, 'new_value': 38931.9}, {'field': 'total_amount', 'old_value': 38770.7, 'new_value': 40696.4}, {'field': 'order_count', 'old_value': 226, 'new_value': 240}]
2025-06-22 12:00:47,993 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMZ
2025-06-22 12:00:48,493 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMZ
2025-06-22 12:00:48,493 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110873.07, 'new_value': 133655.56}, {'field': 'offline_amount', 'old_value': 14531.95, 'new_value': 16459.95}, {'field': 'total_amount', 'old_value': 125405.02, 'new_value': 150115.51}, {'field': 'order_count', 'old_value': 3687, 'new_value': 4424}]
2025-06-22 12:00:48,493 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-22 12:00:48,868 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-22 12:00:48,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 172506.0, 'new_value': 177029.0}, {'field': 'total_amount', 'old_value': 181238.0, 'new_value': 185761.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 53}]
2025-06-22 12:00:48,868 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXZ
2025-06-22 12:00:49,462 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXZ
2025-06-22 12:00:49,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34498.5, 'new_value': 34657.5}, {'field': 'total_amount', 'old_value': 34498.5, 'new_value': 34657.5}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-06-22 12:00:49,462 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-22 12:00:49,899 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-22 12:00:49,899 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4516.0, 'new_value': 5004.0}, {'field': 'offline_amount', 'old_value': 44202.0, 'new_value': 48467.0}, {'field': 'total_amount', 'old_value': 48718.0, 'new_value': 53471.0}, {'field': 'order_count', 'old_value': 344, 'new_value': 442}]
2025-06-22 12:00:49,899 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM501
2025-06-22 12:00:50,399 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM501
2025-06-22 12:00:50,399 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29310.0, 'new_value': 33088.0}, {'field': 'total_amount', 'old_value': 31563.0, 'new_value': 35341.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 157}]
2025-06-22 12:00:50,399 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-22 12:00:50,837 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-22 12:00:50,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101016.0, 'new_value': 107560.0}, {'field': 'total_amount', 'old_value': 101016.0, 'new_value': 107560.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-06-22 12:00:50,837 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM701
2025-06-22 12:00:51,321 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM701
2025-06-22 12:00:51,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69539.0, 'new_value': 70138.0}, {'field': 'total_amount', 'old_value': 71138.0, 'new_value': 71737.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-06-22 12:00:51,321 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDB
2025-06-22 12:00:51,774 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDB
2025-06-22 12:00:51,774 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26657.0, 'new_value': 28560.0}, {'field': 'offline_amount', 'old_value': 196043.0, 'new_value': 218165.0}, {'field': 'total_amount', 'old_value': 222700.0, 'new_value': 246725.0}, {'field': 'order_count', 'old_value': 182, 'new_value': 201}]
2025-06-22 12:00:51,774 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-22 12:00:52,290 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-22 12:00:52,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7762.9, 'new_value': 8529.9}, {'field': 'offline_amount', 'old_value': 9435.0, 'new_value': 9543.0}, {'field': 'total_amount', 'old_value': 17197.9, 'new_value': 18072.9}, {'field': 'order_count', 'old_value': 52, 'new_value': 57}]
2025-06-22 12:00:52,290 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMNB
2025-06-22 12:00:52,759 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMNB
2025-06-22 12:00:52,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78960.0, 'new_value': 88560.0}, {'field': 'total_amount', 'old_value': 78960.0, 'new_value': 88560.0}, {'field': 'order_count', 'old_value': 3274, 'new_value': 3590}]
2025-06-22 12:00:52,759 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-22 12:00:53,196 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-22 12:00:53,196 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 152709.44, 'new_value': 162802.71}, {'field': 'offline_amount', 'old_value': 302957.29, 'new_value': 326251.75}, {'field': 'total_amount', 'old_value': 455666.73, 'new_value': 489054.46}, {'field': 'order_count', 'old_value': 3450, 'new_value': 3672}]
2025-06-22 12:00:53,196 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-22 12:00:53,649 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-22 12:00:53,649 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19948.75, 'new_value': 23086.01}, {'field': 'offline_amount', 'old_value': 34.0, 'new_value': 37.0}, {'field': 'total_amount', 'old_value': 19982.75, 'new_value': 23123.01}, {'field': 'order_count', 'old_value': 82, 'new_value': 90}]
2025-06-22 12:00:53,649 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZB
2025-06-22 12:00:54,165 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZB
2025-06-22 12:00:54,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62089.0, 'new_value': 66888.0}, {'field': 'total_amount', 'old_value': 62089.0, 'new_value': 66888.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-06-22 12:00:54,165 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-22 12:00:54,634 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-22 12:00:54,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66089.0, 'new_value': 77406.0}, {'field': 'total_amount', 'old_value': 66089.0, 'new_value': 77406.0}, {'field': 'order_count', 'old_value': 249, 'new_value': 288}]
2025-06-22 12:00:54,634 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-22 12:00:55,134 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-22 12:00:55,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 421343.61, 'new_value': 450034.2}, {'field': 'total_amount', 'old_value': 421343.61, 'new_value': 450034.2}, {'field': 'order_count', 'old_value': 3211, 'new_value': 3423}]
2025-06-22 12:00:55,134 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2C
2025-06-22 12:00:55,618 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2C
2025-06-22 12:00:55,618 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67737.08, 'new_value': 77005.46}, {'field': 'offline_amount', 'old_value': 149200.28, 'new_value': 164630.1}, {'field': 'total_amount', 'old_value': 216937.36, 'new_value': 241635.56}, {'field': 'order_count', 'old_value': 10142, 'new_value': 11227}]
2025-06-22 12:00:55,618 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3C
2025-06-22 12:00:56,118 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3C
2025-06-22 12:00:56,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2566.28, 'new_value': 3122.19}, {'field': 'offline_amount', 'old_value': 89646.92, 'new_value': 97166.01}, {'field': 'total_amount', 'old_value': 92213.2, 'new_value': 100288.2}, {'field': 'order_count', 'old_value': 439, 'new_value': 477}]
2025-06-22 12:00:56,118 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-22 12:00:56,571 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-22 12:00:56,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52000.0, 'new_value': 57800.0}, {'field': 'total_amount', 'old_value': 55800.0, 'new_value': 61600.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-06-22 12:00:56,571 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-22 12:00:57,024 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-22 12:00:57,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85817.58, 'new_value': 88774.28}, {'field': 'offline_amount', 'old_value': 423090.23, 'new_value': 433981.63}, {'field': 'total_amount', 'old_value': 508907.81, 'new_value': 522755.91}, {'field': 'order_count', 'old_value': 1376, 'new_value': 1437}]
2025-06-22 12:00:57,024 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-22 12:00:57,478 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-22 12:00:57,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38881.0, 'new_value': 41137.0}, {'field': 'total_amount', 'old_value': 38881.0, 'new_value': 41137.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 56}]
2025-06-22 12:00:57,478 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAC
2025-06-22 12:00:57,868 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAC
2025-06-22 12:00:57,868 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99858.97, 'new_value': 118086.3}, {'field': 'offline_amount', 'old_value': 73297.28, 'new_value': 85852.53}, {'field': 'total_amount', 'old_value': 173156.25, 'new_value': 203938.83}, {'field': 'order_count', 'old_value': 7136, 'new_value': 8434}]
2025-06-22 12:00:57,868 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-22 12:00:58,290 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-22 12:00:58,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11879.5, 'new_value': 14461.9}, {'field': 'offline_amount', 'old_value': 27881.8, 'new_value': 33281.8}, {'field': 'total_amount', 'old_value': 39761.3, 'new_value': 47743.7}, {'field': 'order_count', 'old_value': 138, 'new_value': 152}]
2025-06-22 12:00:58,290 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEC
2025-06-22 12:00:58,712 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEC
2025-06-22 12:00:58,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39946.0, 'new_value': 41240.0}, {'field': 'total_amount', 'old_value': 42626.0, 'new_value': 43920.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-06-22 12:00:58,712 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMIC
2025-06-22 12:00:59,149 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMIC
2025-06-22 12:00:59,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72759.0, 'new_value': 93255.0}, {'field': 'offline_amount', 'old_value': 122581.0, 'new_value': 140234.0}, {'field': 'total_amount', 'old_value': 195340.0, 'new_value': 233489.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 72}]
2025-06-22 12:00:59,149 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-22 12:00:59,587 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-22 12:00:59,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 154942.85, 'new_value': 168087.45}, {'field': 'offline_amount', 'old_value': 69920.15, 'new_value': 74978.15}, {'field': 'total_amount', 'old_value': 224863.0, 'new_value': 243065.6}, {'field': 'order_count', 'old_value': 954, 'new_value': 1017}]
2025-06-22 12:00:59,587 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-22 12:01:00,071 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-22 12:01:00,071 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67879.0, 'new_value': 96154.0}, {'field': 'offline_amount', 'old_value': 247031.0, 'new_value': 256344.0}, {'field': 'total_amount', 'old_value': 314910.0, 'new_value': 352498.0}, {'field': 'order_count', 'old_value': 2350, 'new_value': 2606}]
2025-06-22 12:01:00,071 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-22 12:01:00,556 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-22 12:01:00,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106837.0, 'new_value': 114500.0}, {'field': 'total_amount', 'old_value': 106837.0, 'new_value': 114500.0}, {'field': 'order_count', 'old_value': 83, 'new_value': 113}]
2025-06-22 12:01:00,556 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTC
2025-06-22 12:01:01,102 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTC
2025-06-22 12:01:01,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 369487.78, 'new_value': 448442.31}, {'field': 'total_amount', 'old_value': 369487.78, 'new_value': 448442.31}, {'field': 'order_count', 'old_value': 4325, 'new_value': 5093}]
2025-06-22 12:01:01,102 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-22 12:01:01,571 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-22 12:01:01,571 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6404.7, 'new_value': 7003.55}, {'field': 'offline_amount', 'old_value': 53201.0, 'new_value': 56279.0}, {'field': 'total_amount', 'old_value': 59605.7, 'new_value': 63282.55}, {'field': 'order_count', 'old_value': 1279, 'new_value': 1407}]
2025-06-22 12:01:01,571 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-22 12:01:01,977 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-22 12:01:01,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18209.0, 'new_value': 20464.0}, {'field': 'total_amount', 'old_value': 18209.0, 'new_value': 20464.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 97}]
2025-06-22 12:01:01,977 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-22 12:01:02,556 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-22 12:01:02,556 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42194.46, 'new_value': 45086.7}, {'field': 'offline_amount', 'old_value': 360887.75, 'new_value': 388381.28}, {'field': 'total_amount', 'old_value': 403082.21, 'new_value': 433467.98}, {'field': 'order_count', 'old_value': 3448, 'new_value': 3793}]
2025-06-22 12:01:02,556 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-22 12:01:03,009 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-22 12:01:03,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11724.71, 'new_value': 12483.41}, {'field': 'offline_amount', 'old_value': 174373.72, 'new_value': 193550.22}, {'field': 'total_amount', 'old_value': 186098.43, 'new_value': 206033.63}, {'field': 'order_count', 'old_value': 1240, 'new_value': 1366}]
2025-06-22 12:01:03,009 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-22 12:01:03,524 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-22 12:01:03,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 247759.41, 'new_value': 261247.79}, {'field': 'total_amount', 'old_value': 247759.41, 'new_value': 261247.79}, {'field': 'order_count', 'old_value': 7197, 'new_value': 7576}]
2025-06-22 12:01:03,524 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPD
2025-06-22 12:01:03,946 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPD
2025-06-22 12:01:03,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68955.75, 'new_value': 75721.99}, {'field': 'offline_amount', 'old_value': 33258.7, 'new_value': 37007.09}, {'field': 'total_amount', 'old_value': 102214.45, 'new_value': 112729.08}, {'field': 'order_count', 'old_value': 3773, 'new_value': 4171}]
2025-06-22 12:01:03,946 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-22 12:01:04,399 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-22 12:01:04,399 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30553.0, 'new_value': 31423.0}, {'field': 'total_amount', 'old_value': 30553.0, 'new_value': 31423.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-06-22 12:01:04,399 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0E
2025-06-22 12:01:04,837 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0E
2025-06-22 12:01:04,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109484.0, 'new_value': 121705.0}, {'field': 'total_amount', 'old_value': 109484.0, 'new_value': 121705.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 26}]
2025-06-22 12:01:04,837 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-22 12:01:05,290 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-22 12:01:05,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64179.6, 'new_value': 66949.6}, {'field': 'total_amount', 'old_value': 64179.6, 'new_value': 66949.6}, {'field': 'order_count', 'old_value': 877, 'new_value': 878}]
2025-06-22 12:01:05,290 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-22 12:01:05,743 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-22 12:01:05,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36780.2, 'new_value': 41862.2}, {'field': 'total_amount', 'old_value': 36780.2, 'new_value': 41862.2}, {'field': 'order_count', 'old_value': 24, 'new_value': 27}]
2025-06-22 12:01:05,743 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-22 12:01:06,290 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-22 12:01:06,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6966.5, 'new_value': 7164.5}, {'field': 'offline_amount', 'old_value': 53100.0, 'new_value': 59892.0}, {'field': 'total_amount', 'old_value': 60066.5, 'new_value': 67056.5}, {'field': 'order_count', 'old_value': 50, 'new_value': 54}]
2025-06-22 12:01:06,290 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXQ
2025-06-22 12:01:06,759 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXQ
2025-06-22 12:01:06,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64030.68, 'new_value': 71540.14}, {'field': 'offline_amount', 'old_value': 48322.57, 'new_value': 52719.14}, {'field': 'total_amount', 'old_value': 112353.25, 'new_value': 124259.28}, {'field': 'order_count', 'old_value': 4997, 'new_value': 5547}]
2025-06-22 12:01:06,759 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-22 12:01:07,259 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-22 12:01:07,259 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49851.72, 'new_value': 50805.9}, {'field': 'offline_amount', 'old_value': 66395.15, 'new_value': 69349.15}, {'field': 'total_amount', 'old_value': 116246.87, 'new_value': 120155.05}, {'field': 'order_count', 'old_value': 90, 'new_value': 96}]
2025-06-22 12:01:07,259 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-22 12:01:07,759 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-22 12:01:07,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31683.27, 'new_value': 32454.08}, {'field': 'offline_amount', 'old_value': 23837.0, 'new_value': 24323.0}, {'field': 'total_amount', 'old_value': 55520.27, 'new_value': 56777.08}, {'field': 'order_count', 'old_value': 739, 'new_value': 761}]
2025-06-22 12:01:07,759 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHR
2025-06-22 12:01:08,181 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHR
2025-06-22 12:01:08,181 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21704.77, 'new_value': 26007.92}, {'field': 'offline_amount', 'old_value': 42371.65, 'new_value': 50529.99}, {'field': 'total_amount', 'old_value': 64076.42, 'new_value': 76537.91}, {'field': 'order_count', 'old_value': 584, 'new_value': 695}]
2025-06-22 12:01:08,181 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-22 12:01:08,806 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-22 12:01:08,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174428.32, 'new_value': 185989.32}, {'field': 'total_amount', 'old_value': 174428.32, 'new_value': 185989.32}, {'field': 'order_count', 'old_value': 976, 'new_value': 1037}]
2025-06-22 12:01:08,806 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-22 12:01:09,274 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-22 12:01:09,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 262234.0, 'new_value': 277234.0}, {'field': 'total_amount', 'old_value': 262234.0, 'new_value': 277234.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 85}]
2025-06-22 12:01:09,274 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-22 12:01:09,774 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-22 12:01:09,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86960.0, 'new_value': 92447.2}, {'field': 'total_amount', 'old_value': 86960.0, 'new_value': 92447.2}, {'field': 'order_count', 'old_value': 217, 'new_value': 230}]
2025-06-22 12:01:09,774 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-22 12:01:10,196 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-22 12:01:10,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42844.95, 'new_value': 54039.65}, {'field': 'total_amount', 'old_value': 42844.95, 'new_value': 54039.65}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-22 12:01:10,196 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-22 12:01:10,634 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-22 12:01:10,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41742.2, 'new_value': 46002.0}, {'field': 'total_amount', 'old_value': 41742.2, 'new_value': 46002.0}, {'field': 'order_count', 'old_value': 244, 'new_value': 284}]
2025-06-22 12:01:10,634 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVR
2025-06-22 12:01:11,118 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVR
2025-06-22 12:01:11,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145007.87, 'new_value': 177480.11}, {'field': 'total_amount', 'old_value': 194796.18, 'new_value': 227268.42}, {'field': 'order_count', 'old_value': 4885, 'new_value': 5685}]
2025-06-22 12:01:11,118 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-22 12:01:11,509 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-22 12:01:11,509 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 481132.0, 'new_value': 516259.99}, {'field': 'total_amount', 'old_value': 481132.0, 'new_value': 516259.99}, {'field': 'order_count', 'old_value': 1542, 'new_value': 1668}]
2025-06-22 12:01:11,509 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-22 12:01:11,946 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-22 12:01:11,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27375.0, 'new_value': 31767.0}, {'field': 'total_amount', 'old_value': 28103.0, 'new_value': 32495.0}, {'field': 'order_count', 'old_value': 116, 'new_value': 126}]
2025-06-22 12:01:11,946 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOS
2025-06-22 12:01:12,384 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOS
2025-06-22 12:01:12,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29264.38, 'new_value': 35043.4}, {'field': 'offline_amount', 'old_value': 220728.57, 'new_value': 263595.06}, {'field': 'total_amount', 'old_value': 249992.95, 'new_value': 298638.46}, {'field': 'order_count', 'old_value': 19734, 'new_value': 21274}]
2025-06-22 12:01:12,384 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXS
2025-06-22 12:01:12,868 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXS
2025-06-22 12:01:12,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174194.0, 'new_value': 181254.0}, {'field': 'total_amount', 'old_value': 174194.0, 'new_value': 181254.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 31}]
2025-06-22 12:01:12,868 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-22 12:01:13,337 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-22 12:01:13,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30730.5, 'new_value': 34480.0}, {'field': 'total_amount', 'old_value': 31978.5, 'new_value': 35728.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 121}]
2025-06-22 12:01:13,337 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-22 12:01:13,806 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-22 12:01:13,806 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 114585.6, 'new_value': 128580.2}, {'field': 'offline_amount', 'old_value': 79049.0, 'new_value': 86367.0}, {'field': 'total_amount', 'old_value': 193634.6, 'new_value': 214947.2}, {'field': 'order_count', 'old_value': 2002, 'new_value': 2209}]
2025-06-22 12:01:13,806 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-22 12:01:14,243 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-22 12:01:14,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 444523.76, 'new_value': 474773.14}, {'field': 'total_amount', 'old_value': 444839.12, 'new_value': 475088.5}, {'field': 'order_count', 'old_value': 1197, 'new_value': 1264}]
2025-06-22 12:01:14,243 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-22 12:01:14,696 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-22 12:01:14,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31772.9, 'new_value': 37720.9}, {'field': 'total_amount', 'old_value': 31772.9, 'new_value': 37720.9}, {'field': 'order_count', 'old_value': 433, 'new_value': 503}]
2025-06-22 12:01:14,696 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-22 12:01:15,149 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-22 12:01:15,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 220116.29, 'new_value': 239253.37}, {'field': 'total_amount', 'old_value': 282303.47, 'new_value': 301440.55}, {'field': 'order_count', 'old_value': 563, 'new_value': 601}]
2025-06-22 12:01:15,149 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJT
2025-06-22 12:01:15,602 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJT
2025-06-22 12:01:15,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38086.0, 'new_value': 38790.0}, {'field': 'total_amount', 'old_value': 38086.0, 'new_value': 38790.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 70}]
2025-06-22 12:01:15,602 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAK
2025-06-22 12:01:16,056 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAK
2025-06-22 12:01:16,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23987.0, 'new_value': 30787.0}, {'field': 'total_amount', 'old_value': 23987.0, 'new_value': 30787.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-22 12:01:16,056 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-22 12:01:16,634 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-22 12:01:16,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28888.21, 'new_value': 30790.93}, {'field': 'total_amount', 'old_value': 28888.21, 'new_value': 30790.93}, {'field': 'order_count', 'old_value': 124, 'new_value': 132}]
2025-06-22 12:01:16,634 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-22 12:01:17,087 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-22 12:01:17,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12802.0, 'new_value': 13414.0}, {'field': 'total_amount', 'old_value': 12802.0, 'new_value': 13414.0}, {'field': 'order_count', 'old_value': 144, 'new_value': 148}]
2025-06-22 12:01:17,102 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-22 12:01:17,540 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-22 12:01:17,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58192.0, 'new_value': 60709.0}, {'field': 'offline_amount', 'old_value': 108328.0, 'new_value': 115735.0}, {'field': 'total_amount', 'old_value': 166520.0, 'new_value': 176444.0}, {'field': 'order_count', 'old_value': 3798, 'new_value': 4022}]
2025-06-22 12:01:17,540 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLK
2025-06-22 12:01:18,040 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLK
2025-06-22 12:01:18,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20077.0, 'new_value': 22824.8}, {'field': 'offline_amount', 'old_value': 143832.0, 'new_value': 162532.3}, {'field': 'total_amount', 'old_value': 163909.0, 'new_value': 185357.1}, {'field': 'order_count', 'old_value': 5289, 'new_value': 5851}]
2025-06-22 12:01:18,040 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-22 12:01:18,509 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-22 12:01:18,509 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4519.42, 'new_value': 5038.8}, {'field': 'offline_amount', 'old_value': 270517.37, 'new_value': 293518.37}, {'field': 'total_amount', 'old_value': 275036.79, 'new_value': 298557.17}, {'field': 'order_count', 'old_value': 13589, 'new_value': 14743}]
2025-06-22 12:01:18,509 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-22 12:01:18,962 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-22 12:01:18,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 360808.0, 'new_value': 380897.0}, {'field': 'total_amount', 'old_value': 360808.0, 'new_value': 380897.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 86}]
2025-06-22 12:01:18,962 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUK
2025-06-22 12:01:19,477 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUK
2025-06-22 12:01:19,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66317.47, 'new_value': 79534.4}, {'field': 'total_amount', 'old_value': 66317.47, 'new_value': 79534.4}, {'field': 'order_count', 'old_value': 1963, 'new_value': 2376}]
2025-06-22 12:01:19,477 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-22 12:01:19,931 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-22 12:01:19,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93339.0, 'new_value': 97532.0}, {'field': 'total_amount', 'old_value': 93339.0, 'new_value': 97532.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 62}]
2025-06-22 12:01:19,931 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-22 12:01:20,368 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-22 12:01:20,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97313.0, 'new_value': 103195.0}, {'field': 'total_amount', 'old_value': 97313.0, 'new_value': 103195.0}, {'field': 'order_count', 'old_value': 356, 'new_value': 376}]
2025-06-22 12:01:20,368 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2L
2025-06-22 12:01:20,821 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2L
2025-06-22 12:01:20,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190886.0, 'new_value': 214415.0}, {'field': 'total_amount', 'old_value': 190886.0, 'new_value': 214415.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-22 12:01:20,821 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4L
2025-06-22 12:01:21,243 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4L
2025-06-22 12:01:21,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23212.0, 'new_value': 25472.0}, {'field': 'total_amount', 'old_value': 23212.0, 'new_value': 25472.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 91}]
2025-06-22 12:01:21,243 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6L
2025-06-22 12:01:21,649 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6L
2025-06-22 12:01:21,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146555.0, 'new_value': 177171.0}, {'field': 'total_amount', 'old_value': 146555.0, 'new_value': 177171.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 37}]
2025-06-22 12:01:21,649 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-22 12:01:22,149 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-22 12:01:22,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 116089.96, 'new_value': 121426.06}, {'field': 'offline_amount', 'old_value': 289178.56, 'new_value': 309693.5}, {'field': 'total_amount', 'old_value': 405268.52, 'new_value': 431119.56}, {'field': 'order_count', 'old_value': 4054, 'new_value': 4276}]
2025-06-22 12:01:22,149 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-22 12:01:22,618 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-22 12:01:22,618 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 119425.89, 'new_value': 128026.11}, {'field': 'offline_amount', 'old_value': 235104.14, 'new_value': 256011.51}, {'field': 'total_amount', 'old_value': 354530.03, 'new_value': 384037.62}, {'field': 'order_count', 'old_value': 2969, 'new_value': 3263}]
2025-06-22 12:01:22,618 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJL
2025-06-22 12:01:23,071 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJL
2025-06-22 12:01:23,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43001.2, 'new_value': 49389.0}, {'field': 'total_amount', 'old_value': 43001.2, 'new_value': 49389.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-06-22 12:01:23,071 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-22 12:01:23,493 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-22 12:01:23,493 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29262.62, 'new_value': 31151.72}, {'field': 'offline_amount', 'old_value': 840580.23, 'new_value': 893955.44}, {'field': 'total_amount', 'old_value': 869842.85, 'new_value': 925107.16}, {'field': 'order_count', 'old_value': 4060, 'new_value': 4334}]
2025-06-22 12:01:23,493 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-22 12:01:24,055 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-22 12:01:24,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41184.0, 'new_value': 43992.0}, {'field': 'total_amount', 'old_value': 43796.0, 'new_value': 46604.0}, {'field': 'order_count', 'old_value': 104, 'new_value': 110}]
2025-06-22 12:01:24,055 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-22 12:01:24,587 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-22 12:01:24,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47970.7, 'new_value': 51180.1}, {'field': 'total_amount', 'old_value': 48707.6, 'new_value': 51917.0}, {'field': 'order_count', 'old_value': 344, 'new_value': 369}]
2025-06-22 12:01:24,587 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-22 12:01:25,071 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-22 12:01:25,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28842.1, 'new_value': 31715.1}, {'field': 'total_amount', 'old_value': 28842.1, 'new_value': 31715.1}, {'field': 'order_count', 'old_value': 194, 'new_value': 212}]
2025-06-22 12:01:25,071 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-22 12:01:25,524 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-22 12:01:25,524 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70144.04, 'new_value': 72665.49}, {'field': 'offline_amount', 'old_value': 673385.35, 'new_value': 716309.65}, {'field': 'total_amount', 'old_value': 743529.39, 'new_value': 788975.14}, {'field': 'order_count', 'old_value': 3170, 'new_value': 3354}]
2025-06-22 12:01:25,524 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-22 12:01:26,009 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-22 12:01:26,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 211390.52, 'new_value': 224282.52}, {'field': 'offline_amount', 'old_value': 186585.77, 'new_value': 200770.77}, {'field': 'total_amount', 'old_value': 397976.29, 'new_value': 425053.29}, {'field': 'order_count', 'old_value': 2658, 'new_value': 2826}]
2025-06-22 12:01:26,009 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-22 12:01:26,415 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-22 12:01:26,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24372.92, 'new_value': 25466.92}, {'field': 'total_amount', 'old_value': 24930.92, 'new_value': 26024.92}, {'field': 'order_count', 'old_value': 57, 'new_value': 59}]
2025-06-22 12:01:26,415 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-22 12:01:26,837 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-22 12:01:26,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1400000.0, 'new_value': 1500000.0}, {'field': 'total_amount', 'old_value': 1500000.0, 'new_value': 1600000.0}, {'field': 'order_count', 'old_value': 372, 'new_value': 373}]
2025-06-22 12:01:26,837 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-22 12:01:27,290 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-22 12:01:27,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114426.0, 'new_value': 125569.0}, {'field': 'total_amount', 'old_value': 114426.0, 'new_value': 125569.0}, {'field': 'order_count', 'old_value': 3640, 'new_value': 4002}]
2025-06-22 12:01:27,290 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-22 12:01:27,696 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-22 12:01:27,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199064.94, 'new_value': 217463.31}, {'field': 'total_amount', 'old_value': 199064.94, 'new_value': 217463.31}, {'field': 'order_count', 'old_value': 1218, 'new_value': 1329}]
2025-06-22 12:01:27,696 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-22 12:01:28,071 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-22 12:01:28,071 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121558.0, 'new_value': 128327.0}, {'field': 'offline_amount', 'old_value': 70243.0, 'new_value': 81407.0}, {'field': 'total_amount', 'old_value': 191801.0, 'new_value': 209734.0}, {'field': 'order_count', 'old_value': 2861, 'new_value': 3060}]
2025-06-22 12:01:28,071 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-22 12:01:28,540 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-22 12:01:28,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1200181.97, 'new_value': 1272639.53}, {'field': 'offline_amount', 'old_value': 276368.0, 'new_value': 293437.0}, {'field': 'total_amount', 'old_value': 1476549.97, 'new_value': 1566076.53}, {'field': 'order_count', 'old_value': 5546, 'new_value': 5839}]
2025-06-22 12:01:28,540 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-22 12:01:29,024 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-22 12:01:29,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40810.13, 'new_value': 42643.49}, {'field': 'offline_amount', 'old_value': 358508.18, 'new_value': 386689.48}, {'field': 'total_amount', 'old_value': 399318.31, 'new_value': 429332.97}, {'field': 'order_count', 'old_value': 1773, 'new_value': 1907}]
2025-06-22 12:01:29,024 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-22 12:01:29,477 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-22 12:01:29,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21880.0, 'new_value': 23567.0}, {'field': 'total_amount', 'old_value': 21880.0, 'new_value': 23567.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 38}]
2025-06-22 12:01:29,477 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-22 12:01:29,930 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-22 12:01:29,930 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96557.4, 'new_value': 116006.8}, {'field': 'offline_amount', 'old_value': 326303.9, 'new_value': 334303.9}, {'field': 'total_amount', 'old_value': 422861.3, 'new_value': 450310.7}, {'field': 'order_count', 'old_value': 2180, 'new_value': 2868}]
2025-06-22 12:01:29,930 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-22 12:01:30,384 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-22 12:01:30,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20915.15, 'new_value': 24408.05}, {'field': 'offline_amount', 'old_value': 21987.2, 'new_value': 26148.6}, {'field': 'total_amount', 'old_value': 42902.35, 'new_value': 50556.65}, {'field': 'order_count', 'old_value': 257, 'new_value': 284}]
2025-06-22 12:01:30,384 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-22 12:01:30,899 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-22 12:01:30,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257663.11, 'new_value': 280687.11}, {'field': 'total_amount', 'old_value': 257663.11, 'new_value': 280687.11}, {'field': 'order_count', 'old_value': 2433, 'new_value': 2605}]
2025-06-22 12:01:30,899 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-22 12:01:31,368 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-22 12:01:31,368 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141642.23, 'new_value': 149430.9}, {'field': 'offline_amount', 'old_value': 207403.92, 'new_value': 231840.1}, {'field': 'total_amount', 'old_value': 349046.15, 'new_value': 381271.0}, {'field': 'order_count', 'old_value': 3140, 'new_value': 3390}]
2025-06-22 12:01:31,368 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-22 12:01:31,837 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-22 12:01:31,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8600000.0, 'new_value': 9000000.0}, {'field': 'total_amount', 'old_value': 8600000.0, 'new_value': 9000000.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 46}]
2025-06-22 12:01:31,837 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV01
2025-06-22 12:01:32,274 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV01
2025-06-22 12:01:32,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150241.07, 'new_value': 175406.07}, {'field': 'total_amount', 'old_value': 150241.07, 'new_value': 175406.07}, {'field': 'order_count', 'old_value': 6755, 'new_value': 7910}]
2025-06-22 12:01:32,274 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-22 12:01:32,665 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-22 12:01:32,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102380.0, 'new_value': 112968.0}, {'field': 'total_amount', 'old_value': 102380.0, 'new_value': 112968.0}, {'field': 'order_count', 'old_value': 176, 'new_value': 192}]
2025-06-22 12:01:32,665 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-22 12:01:33,087 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-22 12:01:33,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 218040.0, 'new_value': 223480.0}, {'field': 'total_amount', 'old_value': 218040.0, 'new_value': 223480.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 44}]
2025-06-22 12:01:33,087 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-22 12:01:33,634 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-22 12:01:33,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35362.5, 'new_value': 37061.5}, {'field': 'total_amount', 'old_value': 35362.5, 'new_value': 37061.5}, {'field': 'order_count', 'old_value': 51, 'new_value': 53}]
2025-06-22 12:01:33,634 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM511
2025-06-22 12:01:34,040 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM511
2025-06-22 12:01:34,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130732.0, 'new_value': 136732.0}, {'field': 'total_amount', 'old_value': 130732.0, 'new_value': 136732.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-06-22 12:01:34,040 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-22 12:01:34,587 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-22 12:01:34,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13161.05, 'new_value': 13461.21}, {'field': 'offline_amount', 'old_value': 218650.1, 'new_value': 237150.7}, {'field': 'total_amount', 'old_value': 231811.15, 'new_value': 250611.91}, {'field': 'order_count', 'old_value': 1231, 'new_value': 1323}]
2025-06-22 12:01:34,587 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-22 12:01:34,993 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-22 12:01:34,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26524.0, 'new_value': 28446.0}, {'field': 'total_amount', 'old_value': 26524.0, 'new_value': 28446.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 88}]
2025-06-22 12:01:34,993 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM811
2025-06-22 12:01:35,399 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM811
2025-06-22 12:01:35,399 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192503.77, 'new_value': 255327.97}, {'field': 'total_amount', 'old_value': 192503.77, 'new_value': 255327.97}, {'field': 'order_count', 'old_value': 18, 'new_value': 154}]
2025-06-22 12:01:35,399 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-22 12:01:35,852 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-22 12:01:35,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51586.54, 'new_value': 58077.64}, {'field': 'total_amount', 'old_value': 51586.54, 'new_value': 58077.64}, {'field': 'order_count', 'old_value': 923, 'new_value': 961}]
2025-06-22 12:01:35,852 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-22 12:01:36,321 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-22 12:01:36,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28742.82, 'new_value': 30069.82}, {'field': 'total_amount', 'old_value': 28742.82, 'new_value': 30069.82}, {'field': 'order_count', 'old_value': 613, 'new_value': 647}]
2025-06-22 12:01:36,321 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-22 12:01:36,821 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-22 12:01:36,821 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 183371.13, 'new_value': 195397.13}, {'field': 'offline_amount', 'old_value': 13876.3, 'new_value': 16212.3}, {'field': 'total_amount', 'old_value': 197247.43, 'new_value': 211609.43}, {'field': 'order_count', 'old_value': 13791, 'new_value': 14452}]
2025-06-22 12:01:36,821 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-22 12:01:37,243 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-22 12:01:37,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98283.0, 'new_value': 102919.0}, {'field': 'total_amount', 'old_value': 98451.0, 'new_value': 103087.0}, {'field': 'order_count', 'old_value': 302, 'new_value': 316}]
2025-06-22 12:01:37,243 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-22 12:01:37,665 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-22 12:01:37,665 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13734.0, 'new_value': 15594.0}, {'field': 'offline_amount', 'old_value': 21410.0, 'new_value': 23032.0}, {'field': 'total_amount', 'old_value': 35144.0, 'new_value': 38626.0}, {'field': 'order_count', 'old_value': 280, 'new_value': 306}]
2025-06-22 12:01:37,665 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-22 12:01:38,102 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-22 12:01:38,102 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109120.37, 'new_value': 117088.16}, {'field': 'offline_amount', 'old_value': 36432.17, 'new_value': 38410.49}, {'field': 'total_amount', 'old_value': 145552.54, 'new_value': 155498.65}, {'field': 'order_count', 'old_value': 8611, 'new_value': 9226}]
2025-06-22 12:01:38,102 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-22 12:01:38,602 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-22 12:01:38,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110035.57, 'new_value': 116948.96}, {'field': 'total_amount', 'old_value': 110035.57, 'new_value': 116948.96}, {'field': 'order_count', 'old_value': 524, 'new_value': 552}]
2025-06-22 12:01:38,602 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-22 12:01:39,087 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-22 12:01:39,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 326893.19, 'new_value': 355208.49}, {'field': 'total_amount', 'old_value': 326893.19, 'new_value': 355208.49}, {'field': 'order_count', 'old_value': 7103, 'new_value': 8181}]
2025-06-22 12:01:39,087 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-22 12:01:39,571 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-22 12:01:39,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146178.54, 'new_value': 153875.92}, {'field': 'total_amount', 'old_value': 146178.54, 'new_value': 153875.92}, {'field': 'order_count', 'old_value': 629, 'new_value': 652}]
2025-06-22 12:01:39,571 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-22 12:01:40,102 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-22 12:01:40,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68223.01, 'new_value': 73322.83}, {'field': 'total_amount', 'old_value': 68223.01, 'new_value': 73322.83}, {'field': 'order_count', 'old_value': 4396, 'new_value': 4722}]
2025-06-22 12:01:40,102 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-22 12:01:40,540 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-22 12:01:40,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52027.54, 'new_value': 55427.04}, {'field': 'offline_amount', 'old_value': 57856.96, 'new_value': 59765.78}, {'field': 'total_amount', 'old_value': 109884.5, 'new_value': 115192.82}, {'field': 'order_count', 'old_value': 5697, 'new_value': 6003}]
2025-06-22 12:01:40,540 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-22 12:01:41,024 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-22 12:01:41,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6565.01, 'new_value': 6570.01}, {'field': 'total_amount', 'old_value': 26377.69, 'new_value': 26382.69}, {'field': 'order_count', 'old_value': 89, 'new_value': 90}]
2025-06-22 12:01:41,024 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-22 12:01:41,415 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-22 12:01:41,430 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16197.27, 'new_value': 17165.27}, {'field': 'offline_amount', 'old_value': 9477.84, 'new_value': 10235.84}, {'field': 'total_amount', 'old_value': 25675.11, 'new_value': 27401.11}, {'field': 'order_count', 'old_value': 956, 'new_value': 1019}]
2025-06-22 12:01:41,430 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-22 12:01:41,868 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-22 12:01:41,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184219.0, 'new_value': 189371.0}, {'field': 'total_amount', 'old_value': 184219.0, 'new_value': 189371.0}, {'field': 'order_count', 'old_value': 19496, 'new_value': 19947}]
2025-06-22 12:01:41,868 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-22 12:01:42,305 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-22 12:01:42,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75600.0, 'new_value': 82002.0}, {'field': 'total_amount', 'old_value': 75600.0, 'new_value': 82002.0}, {'field': 'order_count', 'old_value': 7402, 'new_value': 7819}]
2025-06-22 12:01:42,305 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-22 12:01:42,774 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-22 12:01:42,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5764.5, 'new_value': 8104.5}, {'field': 'total_amount', 'old_value': 12187.5, 'new_value': 14527.5}, {'field': 'order_count', 'old_value': 110, 'new_value': 122}]
2025-06-22 12:01:42,774 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ21
2025-06-22 12:01:43,243 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ21
2025-06-22 12:01:43,243 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97671.3, 'new_value': 103479.6}, {'field': 'total_amount', 'old_value': 121830.1, 'new_value': 127638.4}, {'field': 'order_count', 'old_value': 9480, 'new_value': 9889}]
2025-06-22 12:01:43,243 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR21
2025-06-22 12:01:43,680 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR21
2025-06-22 12:01:43,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36440.0, 'new_value': 38280.0}, {'field': 'total_amount', 'old_value': 36440.0, 'new_value': 38280.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-22 12:01:43,680 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-22 12:01:44,118 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-22 12:01:44,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 552965.6, 'new_value': 589535.7}, {'field': 'total_amount', 'old_value': 552965.6, 'new_value': 589535.7}, {'field': 'order_count', 'old_value': 4797, 'new_value': 5013}]
2025-06-22 12:01:44,118 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX21
2025-06-22 12:01:44,571 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX21
2025-06-22 12:01:44,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89616.0, 'new_value': 104056.0}, {'field': 'total_amount', 'old_value': 89616.0, 'new_value': 104056.0}, {'field': 'order_count', 'old_value': 2742, 'new_value': 3195}]
2025-06-22 12:01:44,571 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-22 12:01:45,040 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-22 12:01:45,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 576611.3, 'new_value': 620707.25}, {'field': 'total_amount', 'old_value': 576611.3, 'new_value': 620707.25}, {'field': 'order_count', 'old_value': 3710, 'new_value': 3930}]
2025-06-22 12:01:45,040 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM631
2025-06-22 12:01:45,508 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM631
2025-06-22 12:01:45,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85991.0, 'new_value': 114655.0}, {'field': 'total_amount', 'old_value': 94597.0, 'new_value': 123261.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 16}]
2025-06-22 12:01:45,524 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-22 12:01:45,930 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-22 12:01:45,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4259030.49, 'new_value': 4529271.49}, {'field': 'total_amount', 'old_value': 4259030.49, 'new_value': 4529271.49}, {'field': 'order_count', 'old_value': 86020, 'new_value': 91311}]
2025-06-22 12:01:45,930 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-22 12:01:46,352 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-22 12:01:46,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 836985.83, 'new_value': 899019.4}, {'field': 'total_amount', 'old_value': 836985.83, 'new_value': 899019.4}, {'field': 'order_count', 'old_value': 3611, 'new_value': 3767}]
2025-06-22 12:01:46,352 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-22 12:01:46,899 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-22 12:01:46,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 554214.54, 'new_value': 587315.0}, {'field': 'total_amount', 'old_value': 554214.54, 'new_value': 587315.0}, {'field': 'order_count', 'old_value': 1405, 'new_value': 1499}]
2025-06-22 12:01:46,899 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-22 12:01:47,352 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-22 12:01:47,352 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52868.37, 'new_value': 57098.08}, {'field': 'total_amount', 'old_value': 81265.49, 'new_value': 85495.2}, {'field': 'order_count', 'old_value': 5300, 'new_value': 5567}]
2025-06-22 12:01:47,352 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-22 12:01:47,805 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-22 12:01:47,805 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91748.95, 'new_value': 97726.79}, {'field': 'total_amount', 'old_value': 144224.78, 'new_value': 150202.62}, {'field': 'order_count', 'old_value': 9578, 'new_value': 9967}]
2025-06-22 12:01:47,805 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-22 12:01:48,290 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-22 12:01:48,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1729588.0, 'new_value': 1829588.0}, {'field': 'total_amount', 'old_value': 1729588.0, 'new_value': 1829588.0}, {'field': 'order_count', 'old_value': 3081, 'new_value': 3082}]
2025-06-22 12:01:48,290 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM41
2025-06-22 12:01:48,868 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM41
2025-06-22 12:01:48,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 699.0}, {'field': 'total_amount', 'old_value': 1398.0, 'new_value': 2097.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-22 12:01:48,868 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-22 12:01:49,321 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-22 12:01:49,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150000.0, 'new_value': 160000.0}, {'field': 'total_amount', 'old_value': 150000.0, 'new_value': 160000.0}, {'field': 'order_count', 'old_value': 794, 'new_value': 795}]
2025-06-22 12:01:49,321 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-22 12:01:49,774 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-22 12:01:49,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150000.0, 'new_value': 160000.0}, {'field': 'total_amount', 'old_value': 160000.0, 'new_value': 170000.0}, {'field': 'order_count', 'old_value': 923, 'new_value': 924}]
2025-06-22 12:01:49,774 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-22 12:01:50,243 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-22 12:01:50,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100220.0, 'new_value': 101468.0}, {'field': 'total_amount', 'old_value': 100220.0, 'new_value': 101468.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-06-22 12:01:50,243 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-22 12:01:50,712 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-22 12:01:50,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 841635.67, 'new_value': 867498.02}, {'field': 'total_amount', 'old_value': 841635.67, 'new_value': 867498.02}, {'field': 'order_count', 'old_value': 3680, 'new_value': 3895}]
2025-06-22 12:01:50,712 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-22 12:01:51,180 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-22 12:01:51,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25709.7, 'new_value': 31834.2}, {'field': 'total_amount', 'old_value': 25709.7, 'new_value': 31834.2}, {'field': 'order_count', 'old_value': 112, 'new_value': 153}]
2025-06-22 12:01:51,180 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-22 12:01:51,680 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-22 12:01:51,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188786.98, 'new_value': 200626.55}, {'field': 'total_amount', 'old_value': 188786.98, 'new_value': 200626.55}, {'field': 'order_count', 'old_value': 5826, 'new_value': 6083}]
2025-06-22 12:01:51,680 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-22 12:01:52,118 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-22 12:01:52,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112527.76, 'new_value': 120513.28}, {'field': 'total_amount', 'old_value': 112527.76, 'new_value': 120513.28}, {'field': 'order_count', 'old_value': 7977, 'new_value': 8565}]
2025-06-22 12:01:52,118 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-22 12:01:52,680 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-22 12:01:52,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210800.0, 'new_value': 220500.0}, {'field': 'total_amount', 'old_value': 210800.0, 'new_value': 220500.0}, {'field': 'order_count', 'old_value': 497, 'new_value': 520}]
2025-06-22 12:01:52,696 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-22 12:01:53,196 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-22 12:01:53,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150000.0, 'new_value': 160000.0}, {'field': 'total_amount', 'old_value': 150000.0, 'new_value': 160000.0}, {'field': 'order_count', 'old_value': 589, 'new_value': 590}]
2025-06-22 12:01:53,196 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-22 12:01:53,649 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-22 12:01:53,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166787.72, 'new_value': 174446.9}, {'field': 'total_amount', 'old_value': 166787.72, 'new_value': 174446.9}, {'field': 'order_count', 'old_value': 2535, 'new_value': 2663}]
2025-06-22 12:01:53,649 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-22 12:01:54,102 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-22 12:01:54,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 449744.9, 'new_value': 469663.9}, {'field': 'total_amount', 'old_value': 449744.9, 'new_value': 469663.9}, {'field': 'order_count', 'old_value': 415, 'new_value': 435}]
2025-06-22 12:01:54,102 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-22 12:01:54,649 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-22 12:01:54,649 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 37656.0}, {'field': 'offline_amount', 'old_value': 339920.0, 'new_value': 377576.0}, {'field': 'total_amount', 'old_value': 339920.0, 'new_value': 415232.0}, {'field': 'order_count', 'old_value': 7715, 'new_value': 8566}]
2025-06-22 12:01:54,649 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-22 12:01:55,118 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-22 12:01:55,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106958.35, 'new_value': 113179.35}, {'field': 'total_amount', 'old_value': 106958.35, 'new_value': 113179.35}, {'field': 'order_count', 'old_value': 2732, 'new_value': 2870}]
2025-06-22 12:01:55,118 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-22 12:01:55,540 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-22 12:01:55,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89866.55, 'new_value': 91761.52}, {'field': 'offline_amount', 'old_value': 141694.07, 'new_value': 161772.41}, {'field': 'total_amount', 'old_value': 231560.62, 'new_value': 253533.93}, {'field': 'order_count', 'old_value': 1827, 'new_value': 1983}]
2025-06-22 12:01:55,540 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-22 12:01:56,008 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-22 12:01:56,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67271.0, 'new_value': 72520.0}, {'field': 'total_amount', 'old_value': 67271.0, 'new_value': 72520.0}, {'field': 'order_count', 'old_value': 610, 'new_value': 658}]
2025-06-22 12:01:56,008 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-22 12:01:56,602 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-22 12:01:56,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78432.21, 'new_value': 88084.77}, {'field': 'total_amount', 'old_value': 133989.91, 'new_value': 143642.47}, {'field': 'order_count', 'old_value': 3161, 'new_value': 3414}]
2025-06-22 12:01:56,602 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-22 12:01:57,040 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-22 12:01:57,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64584.4, 'new_value': 67110.4}, {'field': 'total_amount', 'old_value': 64584.4, 'new_value': 67110.4}, {'field': 'order_count', 'old_value': 29, 'new_value': 31}]
2025-06-22 12:01:57,040 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM62
2025-06-22 12:01:57,524 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM62
2025-06-22 12:01:57,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24523.6, 'new_value': 28185.4}, {'field': 'total_amount', 'old_value': 24523.6, 'new_value': 28185.4}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-22 12:01:57,524 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-22 12:01:58,024 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-22 12:01:58,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54590.0, 'new_value': 58035.0}, {'field': 'total_amount', 'old_value': 58035.0, 'new_value': 61480.0}, {'field': 'order_count', 'old_value': 219, 'new_value': 232}]
2025-06-22 12:01:58,024 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-22 12:01:58,540 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-22 12:01:58,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 252821.0, 'new_value': 266614.0}, {'field': 'total_amount', 'old_value': 252821.0, 'new_value': 266614.0}, {'field': 'order_count', 'old_value': 5783, 'new_value': 6089}]
2025-06-22 12:01:58,540 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-22 12:01:58,993 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-22 12:01:58,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 228873.0, 'new_value': 240397.0}, {'field': 'total_amount', 'old_value': 228873.0, 'new_value': 240397.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 61}]
2025-06-22 12:01:58,993 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-22 12:01:59,415 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-22 12:01:59,415 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3816.0, 'new_value': 5177.0}, {'field': 'offline_amount', 'old_value': 17420.0, 'new_value': 18558.0}, {'field': 'total_amount', 'old_value': 21236.0, 'new_value': 23735.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 100}]
2025-06-22 12:01:59,415 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-22 12:01:59,852 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-22 12:01:59,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 625762.0, 'new_value': 679624.0}, {'field': 'total_amount', 'old_value': 625762.0, 'new_value': 679624.0}, {'field': 'order_count', 'old_value': 3416, 'new_value': 3702}]
2025-06-22 12:01:59,852 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-22 12:02:00,290 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-22 12:02:00,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7409971.73, 'new_value': 7929049.73}, {'field': 'total_amount', 'old_value': 7409971.73, 'new_value': 7929049.73}, {'field': 'order_count', 'old_value': 27585, 'new_value': 29570}]
2025-06-22 12:02:00,290 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-22 12:02:00,711 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-22 12:02:00,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132576.84, 'new_value': 141096.24}, {'field': 'total_amount', 'old_value': 132576.84, 'new_value': 141096.24}, {'field': 'order_count', 'old_value': 14389, 'new_value': 15301}]
2025-06-22 12:02:00,711 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-22 12:02:01,118 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-22 12:02:01,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 157883.48, 'new_value': 167327.4}, {'field': 'offline_amount', 'old_value': 133067.7, 'new_value': 143242.88}, {'field': 'total_amount', 'old_value': 290951.18, 'new_value': 310570.28}, {'field': 'order_count', 'old_value': 12597, 'new_value': 13420}]
2025-06-22 12:02:01,118 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-22 12:02:01,555 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-22 12:02:01,555 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 170958.85, 'new_value': 194318.85}, {'field': 'total_amount', 'old_value': 205160.95, 'new_value': 228520.95}, {'field': 'order_count', 'old_value': 636, 'new_value': 687}]
2025-06-22 12:02:01,555 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU2
2025-06-22 12:02:02,008 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU2
2025-06-22 12:02:02,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113420.0, 'new_value': 129585.0}, {'field': 'total_amount', 'old_value': 113520.0, 'new_value': 129685.0}, {'field': 'order_count', 'old_value': 11847, 'new_value': 13618}]
2025-06-22 12:02:02,008 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-22 12:02:02,446 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-22 12:02:02,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141810.26, 'new_value': 149827.15}, {'field': 'total_amount', 'old_value': 141810.26, 'new_value': 149827.15}, {'field': 'order_count', 'old_value': 10748, 'new_value': 11334}]
2025-06-22 12:02:02,446 - INFO - 日期 2025-06 处理完成 - 更新: 186 条，插入: 0 条，错误: 0 条
2025-06-22 12:02:02,446 - INFO - 数据同步完成！更新: 186 条，插入: 0 条，错误: 0 条
2025-06-22 12:02:02,461 - INFO - =================同步完成====================
2025-06-22 15:00:03,247 - INFO - =================使用默认全量同步=============
2025-06-22 15:00:05,060 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-22 15:00:05,060 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-22 15:00:05,091 - INFO - 开始处理日期: 2025-01
2025-06-22 15:00:05,107 - INFO - Request Parameters - Page 1:
2025-06-22 15:00:05,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:05,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:06,372 - INFO - Response - Page 1:
2025-06-22 15:00:06,575 - INFO - 第 1 页获取到 100 条记录
2025-06-22 15:00:06,575 - INFO - Request Parameters - Page 2:
2025-06-22 15:00:06,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:06,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:07,357 - INFO - Response - Page 2:
2025-06-22 15:00:07,560 - INFO - 第 2 页获取到 100 条记录
2025-06-22 15:00:07,560 - INFO - Request Parameters - Page 3:
2025-06-22 15:00:07,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:07,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:08,044 - INFO - Response - Page 3:
2025-06-22 15:00:08,247 - INFO - 第 3 页获取到 100 条记录
2025-06-22 15:00:08,247 - INFO - Request Parameters - Page 4:
2025-06-22 15:00:08,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:08,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:08,700 - INFO - Response - Page 4:
2025-06-22 15:00:08,903 - INFO - 第 4 页获取到 100 条记录
2025-06-22 15:00:08,903 - INFO - Request Parameters - Page 5:
2025-06-22 15:00:08,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:08,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:09,372 - INFO - Response - Page 5:
2025-06-22 15:00:09,575 - INFO - 第 5 页获取到 100 条记录
2025-06-22 15:00:09,575 - INFO - Request Parameters - Page 6:
2025-06-22 15:00:09,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:09,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:10,044 - INFO - Response - Page 6:
2025-06-22 15:00:10,247 - INFO - 第 6 页获取到 100 条记录
2025-06-22 15:00:10,247 - INFO - Request Parameters - Page 7:
2025-06-22 15:00:10,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:10,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:10,731 - INFO - Response - Page 7:
2025-06-22 15:00:10,935 - INFO - 第 7 页获取到 82 条记录
2025-06-22 15:00:10,935 - INFO - 查询完成，共获取到 682 条记录
2025-06-22 15:00:10,935 - INFO - 获取到 682 条表单数据
2025-06-22 15:00:10,935 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-22 15:00:10,950 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 15:00:10,950 - INFO - 开始处理日期: 2025-02
2025-06-22 15:00:10,950 - INFO - Request Parameters - Page 1:
2025-06-22 15:00:10,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:10,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:11,450 - INFO - Response - Page 1:
2025-06-22 15:00:11,653 - INFO - 第 1 页获取到 100 条记录
2025-06-22 15:00:11,653 - INFO - Request Parameters - Page 2:
2025-06-22 15:00:11,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:11,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:12,153 - INFO - Response - Page 2:
2025-06-22 15:00:12,356 - INFO - 第 2 页获取到 100 条记录
2025-06-22 15:00:12,356 - INFO - Request Parameters - Page 3:
2025-06-22 15:00:12,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:12,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:12,872 - INFO - Response - Page 3:
2025-06-22 15:00:13,075 - INFO - 第 3 页获取到 100 条记录
2025-06-22 15:00:13,075 - INFO - Request Parameters - Page 4:
2025-06-22 15:00:13,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:13,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:13,528 - INFO - Response - Page 4:
2025-06-22 15:00:13,731 - INFO - 第 4 页获取到 100 条记录
2025-06-22 15:00:13,731 - INFO - Request Parameters - Page 5:
2025-06-22 15:00:13,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:13,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:14,497 - INFO - Response - Page 5:
2025-06-22 15:00:14,700 - INFO - 第 5 页获取到 100 条记录
2025-06-22 15:00:14,700 - INFO - Request Parameters - Page 6:
2025-06-22 15:00:14,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:14,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:15,247 - INFO - Response - Page 6:
2025-06-22 15:00:15,450 - INFO - 第 6 页获取到 100 条记录
2025-06-22 15:00:15,450 - INFO - Request Parameters - Page 7:
2025-06-22 15:00:15,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:15,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:16,075 - INFO - Response - Page 7:
2025-06-22 15:00:16,278 - INFO - 第 7 页获取到 70 条记录
2025-06-22 15:00:16,278 - INFO - 查询完成，共获取到 670 条记录
2025-06-22 15:00:16,278 - INFO - 获取到 670 条表单数据
2025-06-22 15:00:16,278 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-22 15:00:16,294 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 15:00:16,294 - INFO - 开始处理日期: 2025-03
2025-06-22 15:00:16,294 - INFO - Request Parameters - Page 1:
2025-06-22 15:00:16,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:16,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:16,778 - INFO - Response - Page 1:
2025-06-22 15:00:16,981 - INFO - 第 1 页获取到 100 条记录
2025-06-22 15:00:16,981 - INFO - Request Parameters - Page 2:
2025-06-22 15:00:16,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:16,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:17,513 - INFO - Response - Page 2:
2025-06-22 15:00:17,716 - INFO - 第 2 页获取到 100 条记录
2025-06-22 15:00:17,716 - INFO - Request Parameters - Page 3:
2025-06-22 15:00:17,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:17,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:18,278 - INFO - Response - Page 3:
2025-06-22 15:00:18,481 - INFO - 第 3 页获取到 100 条记录
2025-06-22 15:00:18,481 - INFO - Request Parameters - Page 4:
2025-06-22 15:00:18,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:18,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:19,013 - INFO - Response - Page 4:
2025-06-22 15:00:19,216 - INFO - 第 4 页获取到 100 条记录
2025-06-22 15:00:19,216 - INFO - Request Parameters - Page 5:
2025-06-22 15:00:19,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:19,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:19,700 - INFO - Response - Page 5:
2025-06-22 15:00:19,903 - INFO - 第 5 页获取到 100 条记录
2025-06-22 15:00:19,903 - INFO - Request Parameters - Page 6:
2025-06-22 15:00:19,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:19,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:20,403 - INFO - Response - Page 6:
2025-06-22 15:00:20,606 - INFO - 第 6 页获取到 100 条记录
2025-06-22 15:00:20,606 - INFO - Request Parameters - Page 7:
2025-06-22 15:00:20,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:20,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:21,028 - INFO - Response - Page 7:
2025-06-22 15:00:21,231 - INFO - 第 7 页获取到 61 条记录
2025-06-22 15:00:21,231 - INFO - 查询完成，共获取到 661 条记录
2025-06-22 15:00:21,231 - INFO - 获取到 661 条表单数据
2025-06-22 15:00:21,231 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-22 15:00:21,247 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 15:00:21,247 - INFO - 开始处理日期: 2025-04
2025-06-22 15:00:21,247 - INFO - Request Parameters - Page 1:
2025-06-22 15:00:21,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:21,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:21,778 - INFO - Response - Page 1:
2025-06-22 15:00:21,981 - INFO - 第 1 页获取到 100 条记录
2025-06-22 15:00:21,981 - INFO - Request Parameters - Page 2:
2025-06-22 15:00:21,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:21,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:22,466 - INFO - Response - Page 2:
2025-06-22 15:00:22,669 - INFO - 第 2 页获取到 100 条记录
2025-06-22 15:00:22,669 - INFO - Request Parameters - Page 3:
2025-06-22 15:00:22,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:22,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:23,356 - INFO - Response - Page 3:
2025-06-22 15:00:23,560 - INFO - 第 3 页获取到 100 条记录
2025-06-22 15:00:23,560 - INFO - Request Parameters - Page 4:
2025-06-22 15:00:23,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:23,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:24,075 - INFO - Response - Page 4:
2025-06-22 15:00:24,278 - INFO - 第 4 页获取到 100 条记录
2025-06-22 15:00:24,278 - INFO - Request Parameters - Page 5:
2025-06-22 15:00:24,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:24,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:24,841 - INFO - Response - Page 5:
2025-06-22 15:00:25,044 - INFO - 第 5 页获取到 100 条记录
2025-06-22 15:00:25,044 - INFO - Request Parameters - Page 6:
2025-06-22 15:00:25,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:25,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:25,606 - INFO - Response - Page 6:
2025-06-22 15:00:25,810 - INFO - 第 6 页获取到 100 条记录
2025-06-22 15:00:25,810 - INFO - Request Parameters - Page 7:
2025-06-22 15:00:25,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:25,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:26,184 - INFO - Response - Page 7:
2025-06-22 15:00:26,388 - INFO - 第 7 页获取到 56 条记录
2025-06-22 15:00:26,388 - INFO - 查询完成，共获取到 656 条记录
2025-06-22 15:00:26,388 - INFO - 获取到 656 条表单数据
2025-06-22 15:00:26,388 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-22 15:00:26,403 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 15:00:26,403 - INFO - 开始处理日期: 2025-05
2025-06-22 15:00:26,403 - INFO - Request Parameters - Page 1:
2025-06-22 15:00:26,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:26,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:26,919 - INFO - Response - Page 1:
2025-06-22 15:00:27,122 - INFO - 第 1 页获取到 100 条记录
2025-06-22 15:00:27,122 - INFO - Request Parameters - Page 2:
2025-06-22 15:00:27,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:27,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:27,653 - INFO - Response - Page 2:
2025-06-22 15:00:27,856 - INFO - 第 2 页获取到 100 条记录
2025-06-22 15:00:27,856 - INFO - Request Parameters - Page 3:
2025-06-22 15:00:27,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:27,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:28,341 - INFO - Response - Page 3:
2025-06-22 15:00:28,544 - INFO - 第 3 页获取到 100 条记录
2025-06-22 15:00:28,544 - INFO - Request Parameters - Page 4:
2025-06-22 15:00:28,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:28,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:29,028 - INFO - Response - Page 4:
2025-06-22 15:00:29,231 - INFO - 第 4 页获取到 100 条记录
2025-06-22 15:00:29,231 - INFO - Request Parameters - Page 5:
2025-06-22 15:00:29,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:29,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:29,778 - INFO - Response - Page 5:
2025-06-22 15:00:29,981 - INFO - 第 5 页获取到 100 条记录
2025-06-22 15:00:29,981 - INFO - Request Parameters - Page 6:
2025-06-22 15:00:29,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:29,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:30,466 - INFO - Response - Page 6:
2025-06-22 15:00:30,669 - INFO - 第 6 页获取到 100 条记录
2025-06-22 15:00:30,669 - INFO - Request Parameters - Page 7:
2025-06-22 15:00:30,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:30,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:31,138 - INFO - Response - Page 7:
2025-06-22 15:00:31,341 - INFO - 第 7 页获取到 65 条记录
2025-06-22 15:00:31,341 - INFO - 查询完成，共获取到 665 条记录
2025-06-22 15:00:31,341 - INFO - 获取到 665 条表单数据
2025-06-22 15:00:31,341 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-22 15:00:31,356 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 15:00:31,356 - INFO - 开始处理日期: 2025-06
2025-06-22 15:00:31,356 - INFO - Request Parameters - Page 1:
2025-06-22 15:00:31,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:31,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:31,809 - INFO - Response - Page 1:
2025-06-22 15:00:32,013 - INFO - 第 1 页获取到 100 条记录
2025-06-22 15:00:32,013 - INFO - Request Parameters - Page 2:
2025-06-22 15:00:32,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:32,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:32,513 - INFO - Response - Page 2:
2025-06-22 15:00:32,716 - INFO - 第 2 页获取到 100 条记录
2025-06-22 15:00:32,716 - INFO - Request Parameters - Page 3:
2025-06-22 15:00:32,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:32,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:33,231 - INFO - Response - Page 3:
2025-06-22 15:00:33,434 - INFO - 第 3 页获取到 100 条记录
2025-06-22 15:00:33,434 - INFO - Request Parameters - Page 4:
2025-06-22 15:00:33,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:33,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:33,950 - INFO - Response - Page 4:
2025-06-22 15:00:34,153 - INFO - 第 4 页获取到 100 条记录
2025-06-22 15:00:34,153 - INFO - Request Parameters - Page 5:
2025-06-22 15:00:34,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:34,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:34,638 - INFO - Response - Page 5:
2025-06-22 15:00:34,841 - INFO - 第 5 页获取到 100 条记录
2025-06-22 15:00:34,841 - INFO - Request Parameters - Page 6:
2025-06-22 15:00:34,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:34,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:35,309 - INFO - Response - Page 6:
2025-06-22 15:00:35,513 - INFO - 第 6 页获取到 100 条记录
2025-06-22 15:00:35,513 - INFO - Request Parameters - Page 7:
2025-06-22 15:00:35,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 15:00:35,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 15:00:35,872 - INFO - Response - Page 7:
2025-06-22 15:00:36,075 - INFO - 第 7 页获取到 23 条记录
2025-06-22 15:00:36,075 - INFO - 查询完成，共获取到 623 条记录
2025-06-22 15:00:36,075 - INFO - 获取到 623 条表单数据
2025-06-22 15:00:36,075 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-22 15:00:36,091 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-22 15:00:36,653 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-22 15:00:36,653 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61378.3, 'new_value': 61506.1}, {'field': 'total_amount', 'old_value': 61378.3, 'new_value': 61506.1}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-06-22 15:00:36,653 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-22 15:00:37,122 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-22 15:00:37,122 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54101.74, 'new_value': 57200.68}, {'field': 'offline_amount', 'old_value': 68877.76, 'new_value': 71836.73}, {'field': 'total_amount', 'old_value': 122979.5, 'new_value': 129037.41}, {'field': 'order_count', 'old_value': 6180, 'new_value': 6562}]
2025-06-22 15:00:37,138 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-22 15:00:37,606 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-22 15:00:37,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 431484.29, 'new_value': 472099.29}, {'field': 'total_amount', 'old_value': 431484.29, 'new_value': 472099.29}, {'field': 'order_count', 'old_value': 4885, 'new_value': 5090}]
2025-06-22 15:00:37,606 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM03
2025-06-22 15:00:38,091 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM03
2025-06-22 15:00:38,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83294.0, 'new_value': 95784.0}, {'field': 'total_amount', 'old_value': 83294.0, 'new_value': 95784.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 29}]
2025-06-22 15:00:38,091 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-22 15:00:38,559 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-22 15:00:38,559 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18249.0, 'new_value': 18448.0}, {'field': 'total_amount', 'old_value': 18249.0, 'new_value': 18448.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-06-22 15:00:38,559 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-22 15:00:39,028 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-22 15:00:39,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133000.0, 'new_value': 140000.0}, {'field': 'total_amount', 'old_value': 133000.0, 'new_value': 140000.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-22 15:00:39,028 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC3
2025-06-22 15:00:39,466 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC3
2025-06-22 15:00:39,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117702.0, 'new_value': 123062.0}, {'field': 'total_amount', 'old_value': 117702.0, 'new_value': 123062.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 36}]
2025-06-22 15:00:39,466 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-22 15:00:39,934 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-22 15:00:39,934 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8749.0, 'new_value': 9578.0}, {'field': 'total_amount', 'old_value': 8749.0, 'new_value': 9578.0}, {'field': 'order_count', 'old_value': 1003, 'new_value': 1028}]
2025-06-22 15:00:39,934 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-22 15:00:40,356 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-22 15:00:40,356 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5364.0, 'new_value': 5963.0}, {'field': 'total_amount', 'old_value': 5364.0, 'new_value': 5963.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-06-22 15:00:40,356 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMR3
2025-06-22 15:00:40,872 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMR3
2025-06-22 15:00:40,872 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32934.0, 'new_value': 35903.0}, {'field': 'total_amount', 'old_value': 32934.0, 'new_value': 35903.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 36}]
2025-06-22 15:00:40,872 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-22 15:00:41,263 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-22 15:00:41,263 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6985.5, 'new_value': 7341.5}, {'field': 'total_amount', 'old_value': 6985.5, 'new_value': 7341.5}, {'field': 'order_count', 'old_value': 46, 'new_value': 50}]
2025-06-22 15:00:41,263 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-22 15:00:41,684 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-22 15:00:41,684 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 219203.0, 'new_value': 334810.0}, {'field': 'total_amount', 'old_value': 219203.0, 'new_value': 334810.0}, {'field': 'order_count', 'old_value': 270, 'new_value': 392}]
2025-06-22 15:00:41,684 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-22 15:00:42,138 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-22 15:00:42,138 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30336.85, 'new_value': 32262.27}, {'field': 'total_amount', 'old_value': 30336.85, 'new_value': 32262.27}, {'field': 'order_count', 'old_value': 1220, 'new_value': 1295}]
2025-06-22 15:00:42,138 - INFO - 日期 2025-06 处理完成 - 更新: 13 条，插入: 0 条，错误: 0 条
2025-06-22 15:00:42,138 - INFO - 数据同步完成！更新: 13 条，插入: 0 条，错误: 0 条
2025-06-22 15:00:42,138 - INFO - =================同步完成====================
2025-06-22 18:00:02,408 - INFO - =================使用默认全量同步=============
2025-06-22 18:00:04,220 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-22 18:00:04,220 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-22 18:00:04,267 - INFO - 开始处理日期: 2025-01
2025-06-22 18:00:04,267 - INFO - Request Parameters - Page 1:
2025-06-22 18:00:04,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:04,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:05,861 - INFO - Response - Page 1:
2025-06-22 18:00:06,064 - INFO - 第 1 页获取到 100 条记录
2025-06-22 18:00:06,064 - INFO - Request Parameters - Page 2:
2025-06-22 18:00:06,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:06,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:06,736 - INFO - Response - Page 2:
2025-06-22 18:00:06,939 - INFO - 第 2 页获取到 100 条记录
2025-06-22 18:00:06,939 - INFO - Request Parameters - Page 3:
2025-06-22 18:00:06,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:06,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:07,533 - INFO - Response - Page 3:
2025-06-22 18:00:07,736 - INFO - 第 3 页获取到 100 条记录
2025-06-22 18:00:07,736 - INFO - Request Parameters - Page 4:
2025-06-22 18:00:07,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:07,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:08,267 - INFO - Response - Page 4:
2025-06-22 18:00:08,470 - INFO - 第 4 页获取到 100 条记录
2025-06-22 18:00:08,470 - INFO - Request Parameters - Page 5:
2025-06-22 18:00:08,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:08,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:09,001 - INFO - Response - Page 5:
2025-06-22 18:00:09,204 - INFO - 第 5 页获取到 100 条记录
2025-06-22 18:00:09,204 - INFO - Request Parameters - Page 6:
2025-06-22 18:00:09,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:09,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:09,736 - INFO - Response - Page 6:
2025-06-22 18:00:09,939 - INFO - 第 6 页获取到 100 条记录
2025-06-22 18:00:09,939 - INFO - Request Parameters - Page 7:
2025-06-22 18:00:09,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:09,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:10,392 - INFO - Response - Page 7:
2025-06-22 18:00:10,595 - INFO - 第 7 页获取到 82 条记录
2025-06-22 18:00:10,595 - INFO - 查询完成，共获取到 682 条记录
2025-06-22 18:00:10,595 - INFO - 获取到 682 条表单数据
2025-06-22 18:00:10,595 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-22 18:00:10,611 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 18:00:10,611 - INFO - 开始处理日期: 2025-02
2025-06-22 18:00:10,611 - INFO - Request Parameters - Page 1:
2025-06-22 18:00:10,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:10,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:11,095 - INFO - Response - Page 1:
2025-06-22 18:00:11,298 - INFO - 第 1 页获取到 100 条记录
2025-06-22 18:00:11,298 - INFO - Request Parameters - Page 2:
2025-06-22 18:00:11,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:11,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:11,767 - INFO - Response - Page 2:
2025-06-22 18:00:11,970 - INFO - 第 2 页获取到 100 条记录
2025-06-22 18:00:11,970 - INFO - Request Parameters - Page 3:
2025-06-22 18:00:11,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:11,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:12,470 - INFO - Response - Page 3:
2025-06-22 18:00:12,673 - INFO - 第 3 页获取到 100 条记录
2025-06-22 18:00:12,673 - INFO - Request Parameters - Page 4:
2025-06-22 18:00:12,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:12,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:13,189 - INFO - Response - Page 4:
2025-06-22 18:00:13,392 - INFO - 第 4 页获取到 100 条记录
2025-06-22 18:00:13,392 - INFO - Request Parameters - Page 5:
2025-06-22 18:00:13,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:13,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:13,892 - INFO - Response - Page 5:
2025-06-22 18:00:14,095 - INFO - 第 5 页获取到 100 条记录
2025-06-22 18:00:14,095 - INFO - Request Parameters - Page 6:
2025-06-22 18:00:14,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:14,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:14,564 - INFO - Response - Page 6:
2025-06-22 18:00:14,767 - INFO - 第 6 页获取到 100 条记录
2025-06-22 18:00:14,767 - INFO - Request Parameters - Page 7:
2025-06-22 18:00:14,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:14,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:15,267 - INFO - Response - Page 7:
2025-06-22 18:00:15,470 - INFO - 第 7 页获取到 70 条记录
2025-06-22 18:00:15,470 - INFO - 查询完成，共获取到 670 条记录
2025-06-22 18:00:15,470 - INFO - 获取到 670 条表单数据
2025-06-22 18:00:15,486 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-22 18:00:15,486 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 18:00:15,486 - INFO - 开始处理日期: 2025-03
2025-06-22 18:00:15,486 - INFO - Request Parameters - Page 1:
2025-06-22 18:00:15,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:15,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:16,048 - INFO - Response - Page 1:
2025-06-22 18:00:16,251 - INFO - 第 1 页获取到 100 条记录
2025-06-22 18:00:16,251 - INFO - Request Parameters - Page 2:
2025-06-22 18:00:16,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:16,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:16,783 - INFO - Response - Page 2:
2025-06-22 18:00:16,986 - INFO - 第 2 页获取到 100 条记录
2025-06-22 18:00:16,986 - INFO - Request Parameters - Page 3:
2025-06-22 18:00:16,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:16,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:17,454 - INFO - Response - Page 3:
2025-06-22 18:00:17,658 - INFO - 第 3 页获取到 100 条记录
2025-06-22 18:00:17,658 - INFO - Request Parameters - Page 4:
2025-06-22 18:00:17,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:17,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:18,158 - INFO - Response - Page 4:
2025-06-22 18:00:18,361 - INFO - 第 4 页获取到 100 条记录
2025-06-22 18:00:18,361 - INFO - Request Parameters - Page 5:
2025-06-22 18:00:18,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:18,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:18,861 - INFO - Response - Page 5:
2025-06-22 18:00:19,064 - INFO - 第 5 页获取到 100 条记录
2025-06-22 18:00:19,064 - INFO - Request Parameters - Page 6:
2025-06-22 18:00:19,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:19,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:19,564 - INFO - Response - Page 6:
2025-06-22 18:00:19,767 - INFO - 第 6 页获取到 100 条记录
2025-06-22 18:00:19,767 - INFO - Request Parameters - Page 7:
2025-06-22 18:00:19,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:19,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:20,283 - INFO - Response - Page 7:
2025-06-22 18:00:20,486 - INFO - 第 7 页获取到 61 条记录
2025-06-22 18:00:20,486 - INFO - 查询完成，共获取到 661 条记录
2025-06-22 18:00:20,486 - INFO - 获取到 661 条表单数据
2025-06-22 18:00:20,486 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-22 18:00:20,501 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 18:00:20,501 - INFO - 开始处理日期: 2025-04
2025-06-22 18:00:20,501 - INFO - Request Parameters - Page 1:
2025-06-22 18:00:20,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:20,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:21,126 - INFO - Response - Page 1:
2025-06-22 18:00:21,329 - INFO - 第 1 页获取到 100 条记录
2025-06-22 18:00:21,329 - INFO - Request Parameters - Page 2:
2025-06-22 18:00:21,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:21,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:21,814 - INFO - Response - Page 2:
2025-06-22 18:00:22,017 - INFO - 第 2 页获取到 100 条记录
2025-06-22 18:00:22,017 - INFO - Request Parameters - Page 3:
2025-06-22 18:00:22,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:22,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:22,611 - INFO - Response - Page 3:
2025-06-22 18:00:22,814 - INFO - 第 3 页获取到 100 条记录
2025-06-22 18:00:22,814 - INFO - Request Parameters - Page 4:
2025-06-22 18:00:22,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:22,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:23,283 - INFO - Response - Page 4:
2025-06-22 18:00:23,486 - INFO - 第 4 页获取到 100 条记录
2025-06-22 18:00:23,486 - INFO - Request Parameters - Page 5:
2025-06-22 18:00:23,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:23,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:24,079 - INFO - Response - Page 5:
2025-06-22 18:00:24,283 - INFO - 第 5 页获取到 100 条记录
2025-06-22 18:00:24,283 - INFO - Request Parameters - Page 6:
2025-06-22 18:00:24,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:24,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:24,783 - INFO - Response - Page 6:
2025-06-22 18:00:24,986 - INFO - 第 6 页获取到 100 条记录
2025-06-22 18:00:24,986 - INFO - Request Parameters - Page 7:
2025-06-22 18:00:24,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:24,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:25,501 - INFO - Response - Page 7:
2025-06-22 18:00:25,704 - INFO - 第 7 页获取到 56 条记录
2025-06-22 18:00:25,704 - INFO - 查询完成，共获取到 656 条记录
2025-06-22 18:00:25,704 - INFO - 获取到 656 条表单数据
2025-06-22 18:00:25,704 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-22 18:00:25,720 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 18:00:25,720 - INFO - 开始处理日期: 2025-05
2025-06-22 18:00:25,720 - INFO - Request Parameters - Page 1:
2025-06-22 18:00:25,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:25,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:26,267 - INFO - Response - Page 1:
2025-06-22 18:00:26,470 - INFO - 第 1 页获取到 100 条记录
2025-06-22 18:00:26,470 - INFO - Request Parameters - Page 2:
2025-06-22 18:00:26,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:26,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:26,970 - INFO - Response - Page 2:
2025-06-22 18:00:27,173 - INFO - 第 2 页获取到 100 条记录
2025-06-22 18:00:27,173 - INFO - Request Parameters - Page 3:
2025-06-22 18:00:27,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:27,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:27,829 - INFO - Response - Page 3:
2025-06-22 18:00:28,032 - INFO - 第 3 页获取到 100 条记录
2025-06-22 18:00:28,032 - INFO - Request Parameters - Page 4:
2025-06-22 18:00:28,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:28,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:28,579 - INFO - Response - Page 4:
2025-06-22 18:00:28,782 - INFO - 第 4 页获取到 100 条记录
2025-06-22 18:00:28,782 - INFO - Request Parameters - Page 5:
2025-06-22 18:00:28,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:28,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:29,267 - INFO - Response - Page 5:
2025-06-22 18:00:29,470 - INFO - 第 5 页获取到 100 条记录
2025-06-22 18:00:29,470 - INFO - Request Parameters - Page 6:
2025-06-22 18:00:29,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:29,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:29,986 - INFO - Response - Page 6:
2025-06-22 18:00:30,189 - INFO - 第 6 页获取到 100 条记录
2025-06-22 18:00:30,189 - INFO - Request Parameters - Page 7:
2025-06-22 18:00:30,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:30,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:30,829 - INFO - Response - Page 7:
2025-06-22 18:00:31,032 - INFO - 第 7 页获取到 65 条记录
2025-06-22 18:00:31,032 - INFO - 查询完成，共获取到 665 条记录
2025-06-22 18:00:31,032 - INFO - 获取到 665 条表单数据
2025-06-22 18:00:31,032 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-22 18:00:31,048 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 18:00:31,048 - INFO - 开始处理日期: 2025-06
2025-06-22 18:00:31,048 - INFO - Request Parameters - Page 1:
2025-06-22 18:00:31,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:31,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:31,564 - INFO - Response - Page 1:
2025-06-22 18:00:31,767 - INFO - 第 1 页获取到 100 条记录
2025-06-22 18:00:31,767 - INFO - Request Parameters - Page 2:
2025-06-22 18:00:31,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:31,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:32,282 - INFO - Response - Page 2:
2025-06-22 18:00:32,486 - INFO - 第 2 页获取到 100 条记录
2025-06-22 18:00:32,486 - INFO - Request Parameters - Page 3:
2025-06-22 18:00:32,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:32,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:32,939 - INFO - Response - Page 3:
2025-06-22 18:00:33,142 - INFO - 第 3 页获取到 100 条记录
2025-06-22 18:00:33,142 - INFO - Request Parameters - Page 4:
2025-06-22 18:00:33,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:33,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:33,595 - INFO - Response - Page 4:
2025-06-22 18:00:33,798 - INFO - 第 4 页获取到 100 条记录
2025-06-22 18:00:33,798 - INFO - Request Parameters - Page 5:
2025-06-22 18:00:33,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:33,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:34,298 - INFO - Response - Page 5:
2025-06-22 18:00:34,501 - INFO - 第 5 页获取到 100 条记录
2025-06-22 18:00:34,501 - INFO - Request Parameters - Page 6:
2025-06-22 18:00:34,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:34,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:35,001 - INFO - Response - Page 6:
2025-06-22 18:00:35,204 - INFO - 第 6 页获取到 100 条记录
2025-06-22 18:00:35,204 - INFO - Request Parameters - Page 7:
2025-06-22 18:00:35,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 18:00:35,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 18:00:35,517 - INFO - Response - Page 7:
2025-06-22 18:00:35,720 - INFO - 第 7 页获取到 23 条记录
2025-06-22 18:00:35,720 - INFO - 查询完成，共获取到 623 条记录
2025-06-22 18:00:35,720 - INFO - 获取到 623 条表单数据
2025-06-22 18:00:35,720 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-22 18:00:35,736 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-22 18:00:36,236 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-22 18:00:36,236 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158034.0, 'new_value': 182782.0}, {'field': 'total_amount', 'old_value': 189643.0, 'new_value': 214391.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 40}]
2025-06-22 18:00:36,236 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDT
2025-06-22 18:00:36,657 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDT
2025-06-22 18:00:36,657 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 221629.4, 'new_value': 245170.4}, {'field': 'total_amount', 'old_value': 221629.4, 'new_value': 245170.4}, {'field': 'order_count', 'old_value': 47, 'new_value': 49}]
2025-06-22 18:00:36,657 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-22 18:00:37,079 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-22 18:00:37,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21891.0, 'new_value': 22866.0}, {'field': 'total_amount', 'old_value': 21891.0, 'new_value': 22866.0}, {'field': 'order_count', 'old_value': 206, 'new_value': 214}]
2025-06-22 18:00:37,079 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-22 18:00:37,564 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-22 18:00:37,564 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11032.0, 'new_value': 11790.0}, {'field': 'total_amount', 'old_value': 13231.0, 'new_value': 13989.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-06-22 18:00:37,564 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-22 18:00:38,064 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-22 18:00:38,064 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30216.0, 'new_value': 32762.0}, {'field': 'total_amount', 'old_value': 30216.0, 'new_value': 32762.0}, {'field': 'order_count', 'old_value': 2854, 'new_value': 2999}]
2025-06-22 18:00:38,064 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-22 18:00:38,548 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-22 18:00:38,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 949000.0, 'new_value': 1014000.0}, {'field': 'total_amount', 'old_value': 949000.0, 'new_value': 1014000.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 55}]
2025-06-22 18:00:38,548 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-22 18:00:39,017 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-22 18:00:39,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 270613.0, 'new_value': 288100.0}, {'field': 'total_amount', 'old_value': 270613.0, 'new_value': 288100.0}, {'field': 'order_count', 'old_value': 7987, 'new_value': 8141}]
2025-06-22 18:00:39,017 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM13
2025-06-22 18:00:39,439 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM13
2025-06-22 18:00:39,439 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115672.0, 'new_value': 120472.0}, {'field': 'total_amount', 'old_value': 115672.0, 'new_value': 120472.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-22 18:00:39,439 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM23
2025-06-22 18:00:39,861 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM23
2025-06-22 18:00:39,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24449.8, 'new_value': 28329.8}, {'field': 'total_amount', 'old_value': 24449.8, 'new_value': 28329.8}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-22 18:00:39,861 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-22 18:00:40,392 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-22 18:00:40,392 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107548.0, 'new_value': 115275.0}, {'field': 'total_amount', 'old_value': 107548.0, 'new_value': 115275.0}, {'field': 'order_count', 'old_value': 2396, 'new_value': 2900}]
2025-06-22 18:00:40,392 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-22 18:00:40,876 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-22 18:00:40,876 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216941.84, 'new_value': 220421.84}, {'field': 'total_amount', 'old_value': 216941.84, 'new_value': 220421.84}, {'field': 'order_count', 'old_value': 81, 'new_value': 82}]
2025-06-22 18:00:40,876 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB3
2025-06-22 18:00:41,314 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB3
2025-06-22 18:00:41,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8460.0, 'new_value': 10350.0}, {'field': 'total_amount', 'old_value': 8460.0, 'new_value': 10350.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-06-22 18:00:41,314 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-22 18:00:41,798 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-22 18:00:41,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100155.3, 'new_value': 104033.3}, {'field': 'total_amount', 'old_value': 100155.3, 'new_value': 104033.3}, {'field': 'order_count', 'old_value': 4083, 'new_value': 4125}]
2025-06-22 18:00:41,798 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF3
2025-06-22 18:00:42,251 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF3
2025-06-22 18:00:42,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112844.79, 'new_value': 118719.79}, {'field': 'total_amount', 'old_value': 112844.79, 'new_value': 118719.79}, {'field': 'order_count', 'old_value': 825, 'new_value': 866}]
2025-06-22 18:00:42,251 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-22 18:00:42,798 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-22 18:00:42,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77771.0, 'new_value': 82941.0}, {'field': 'total_amount', 'old_value': 77771.0, 'new_value': 82941.0}, {'field': 'order_count', 'old_value': 580, 'new_value': 610}]
2025-06-22 18:00:42,798 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-22 18:00:43,251 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-22 18:00:43,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27164.5, 'new_value': 29218.5}, {'field': 'total_amount', 'old_value': 27164.5, 'new_value': 29218.5}, {'field': 'order_count', 'old_value': 2472, 'new_value': 2617}]
2025-06-22 18:00:43,251 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMM3
2025-06-22 18:00:43,704 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMM3
2025-06-22 18:00:43,704 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36380.0, 'new_value': 44970.0}, {'field': 'total_amount', 'old_value': 36380.0, 'new_value': 44970.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 11}]
2025-06-22 18:00:43,704 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-22 18:00:44,173 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-22 18:00:44,173 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60283.0, 'new_value': 67770.0}, {'field': 'total_amount', 'old_value': 60283.0, 'new_value': 67770.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-06-22 18:00:44,189 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-22 18:00:44,610 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-22 18:00:44,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4348760.0, 'new_value': 4536610.0}, {'field': 'total_amount', 'old_value': 4348760.0, 'new_value': 4536610.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 109}]
2025-06-22 18:00:44,610 - INFO - 日期 2025-06 处理完成 - 更新: 19 条，插入: 0 条，错误: 0 条
2025-06-22 18:00:44,610 - INFO - 数据同步完成！更新: 19 条，插入: 0 条，错误: 0 条
2025-06-22 18:00:44,610 - INFO - =================同步完成====================
2025-06-22 21:00:03,243 - INFO - =================使用默认全量同步=============
2025-06-22 21:00:05,025 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-22 21:00:05,025 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-22 21:00:05,056 - INFO - 开始处理日期: 2025-01
2025-06-22 21:00:05,056 - INFO - Request Parameters - Page 1:
2025-06-22 21:00:05,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:05,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:06,463 - INFO - Response - Page 1:
2025-06-22 21:00:06,665 - INFO - 第 1 页获取到 100 条记录
2025-06-22 21:00:06,665 - INFO - Request Parameters - Page 2:
2025-06-22 21:00:06,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:06,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:07,166 - INFO - Response - Page 2:
2025-06-22 21:00:07,369 - INFO - 第 2 页获取到 100 条记录
2025-06-22 21:00:07,369 - INFO - Request Parameters - Page 3:
2025-06-22 21:00:07,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:07,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:07,884 - INFO - Response - Page 3:
2025-06-22 21:00:08,088 - INFO - 第 3 页获取到 100 条记录
2025-06-22 21:00:08,088 - INFO - Request Parameters - Page 4:
2025-06-22 21:00:08,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:08,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:08,760 - INFO - Response - Page 4:
2025-06-22 21:00:08,963 - INFO - 第 4 页获取到 100 条记录
2025-06-22 21:00:08,963 - INFO - Request Parameters - Page 5:
2025-06-22 21:00:08,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:08,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:09,494 - INFO - Response - Page 5:
2025-06-22 21:00:09,697 - INFO - 第 5 页获取到 100 条记录
2025-06-22 21:00:09,697 - INFO - Request Parameters - Page 6:
2025-06-22 21:00:09,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:09,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:10,197 - INFO - Response - Page 6:
2025-06-22 21:00:10,400 - INFO - 第 6 页获取到 100 条记录
2025-06-22 21:00:10,400 - INFO - Request Parameters - Page 7:
2025-06-22 21:00:10,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:10,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:10,869 - INFO - Response - Page 7:
2025-06-22 21:00:11,072 - INFO - 第 7 页获取到 82 条记录
2025-06-22 21:00:11,072 - INFO - 查询完成，共获取到 682 条记录
2025-06-22 21:00:11,072 - INFO - 获取到 682 条表单数据
2025-06-22 21:00:11,072 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-22 21:00:11,088 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 21:00:11,088 - INFO - 开始处理日期: 2025-02
2025-06-22 21:00:11,088 - INFO - Request Parameters - Page 1:
2025-06-22 21:00:11,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:11,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:11,619 - INFO - Response - Page 1:
2025-06-22 21:00:11,823 - INFO - 第 1 页获取到 100 条记录
2025-06-22 21:00:11,823 - INFO - Request Parameters - Page 2:
2025-06-22 21:00:11,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:11,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:12,323 - INFO - Response - Page 2:
2025-06-22 21:00:12,526 - INFO - 第 2 页获取到 100 条记录
2025-06-22 21:00:12,526 - INFO - Request Parameters - Page 3:
2025-06-22 21:00:12,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:12,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:13,042 - INFO - Response - Page 3:
2025-06-22 21:00:13,245 - INFO - 第 3 页获取到 100 条记录
2025-06-22 21:00:13,245 - INFO - Request Parameters - Page 4:
2025-06-22 21:00:13,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:13,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:13,729 - INFO - Response - Page 4:
2025-06-22 21:00:13,932 - INFO - 第 4 页获取到 100 条记录
2025-06-22 21:00:13,932 - INFO - Request Parameters - Page 5:
2025-06-22 21:00:13,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:13,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:14,448 - INFO - Response - Page 5:
2025-06-22 21:00:14,651 - INFO - 第 5 页获取到 100 条记录
2025-06-22 21:00:14,651 - INFO - Request Parameters - Page 6:
2025-06-22 21:00:14,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:14,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:15,104 - INFO - Response - Page 6:
2025-06-22 21:00:15,308 - INFO - 第 6 页获取到 100 条记录
2025-06-22 21:00:15,308 - INFO - Request Parameters - Page 7:
2025-06-22 21:00:15,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:15,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:15,730 - INFO - Response - Page 7:
2025-06-22 21:00:15,933 - INFO - 第 7 页获取到 70 条记录
2025-06-22 21:00:15,933 - INFO - 查询完成，共获取到 670 条记录
2025-06-22 21:00:15,933 - INFO - 获取到 670 条表单数据
2025-06-22 21:00:15,933 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-22 21:00:15,948 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 21:00:15,948 - INFO - 开始处理日期: 2025-03
2025-06-22 21:00:15,948 - INFO - Request Parameters - Page 1:
2025-06-22 21:00:15,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:15,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:16,464 - INFO - Response - Page 1:
2025-06-22 21:00:16,667 - INFO - 第 1 页获取到 100 条记录
2025-06-22 21:00:16,667 - INFO - Request Parameters - Page 2:
2025-06-22 21:00:16,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:16,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:17,183 - INFO - Response - Page 2:
2025-06-22 21:00:17,386 - INFO - 第 2 页获取到 100 条记录
2025-06-22 21:00:17,386 - INFO - Request Parameters - Page 3:
2025-06-22 21:00:17,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:17,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:17,917 - INFO - Response - Page 3:
2025-06-22 21:00:18,121 - INFO - 第 3 页获取到 100 条记录
2025-06-22 21:00:18,121 - INFO - Request Parameters - Page 4:
2025-06-22 21:00:18,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:18,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:18,574 - INFO - Response - Page 4:
2025-06-22 21:00:18,777 - INFO - 第 4 页获取到 100 条记录
2025-06-22 21:00:18,777 - INFO - Request Parameters - Page 5:
2025-06-22 21:00:18,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:18,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:19,808 - INFO - Response - Page 5:
2025-06-22 21:00:20,012 - INFO - 第 5 页获取到 100 条记录
2025-06-22 21:00:20,012 - INFO - Request Parameters - Page 6:
2025-06-22 21:00:20,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:20,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:20,527 - INFO - Response - Page 6:
2025-06-22 21:00:20,730 - INFO - 第 6 页获取到 100 条记录
2025-06-22 21:00:20,730 - INFO - Request Parameters - Page 7:
2025-06-22 21:00:20,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:20,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:21,277 - INFO - Response - Page 7:
2025-06-22 21:00:21,481 - INFO - 第 7 页获取到 61 条记录
2025-06-22 21:00:21,481 - INFO - 查询完成，共获取到 661 条记录
2025-06-22 21:00:21,481 - INFO - 获取到 661 条表单数据
2025-06-22 21:00:21,481 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-22 21:00:21,496 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 21:00:21,496 - INFO - 开始处理日期: 2025-04
2025-06-22 21:00:21,496 - INFO - Request Parameters - Page 1:
2025-06-22 21:00:21,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:21,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:21,996 - INFO - Response - Page 1:
2025-06-22 21:00:22,199 - INFO - 第 1 页获取到 100 条记录
2025-06-22 21:00:22,199 - INFO - Request Parameters - Page 2:
2025-06-22 21:00:22,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:22,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:22,699 - INFO - Response - Page 2:
2025-06-22 21:00:22,903 - INFO - 第 2 页获取到 100 条记录
2025-06-22 21:00:22,903 - INFO - Request Parameters - Page 3:
2025-06-22 21:00:22,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:22,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:23,512 - INFO - Response - Page 3:
2025-06-22 21:00:23,715 - INFO - 第 3 页获取到 100 条记录
2025-06-22 21:00:23,715 - INFO - Request Parameters - Page 4:
2025-06-22 21:00:23,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:23,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:24,200 - INFO - Response - Page 4:
2025-06-22 21:00:24,403 - INFO - 第 4 页获取到 100 条记录
2025-06-22 21:00:24,403 - INFO - Request Parameters - Page 5:
2025-06-22 21:00:24,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:24,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:24,872 - INFO - Response - Page 5:
2025-06-22 21:00:25,075 - INFO - 第 5 页获取到 100 条记录
2025-06-22 21:00:25,075 - INFO - Request Parameters - Page 6:
2025-06-22 21:00:25,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:25,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:25,606 - INFO - Response - Page 6:
2025-06-22 21:00:25,809 - INFO - 第 6 页获取到 100 条记录
2025-06-22 21:00:25,809 - INFO - Request Parameters - Page 7:
2025-06-22 21:00:25,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:25,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:26,263 - INFO - Response - Page 7:
2025-06-22 21:00:26,466 - INFO - 第 7 页获取到 56 条记录
2025-06-22 21:00:26,466 - INFO - 查询完成，共获取到 656 条记录
2025-06-22 21:00:26,466 - INFO - 获取到 656 条表单数据
2025-06-22 21:00:26,466 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-22 21:00:26,481 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 21:00:26,481 - INFO - 开始处理日期: 2025-05
2025-06-22 21:00:26,481 - INFO - Request Parameters - Page 1:
2025-06-22 21:00:26,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:26,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:26,997 - INFO - Response - Page 1:
2025-06-22 21:00:27,200 - INFO - 第 1 页获取到 100 条记录
2025-06-22 21:00:27,200 - INFO - Request Parameters - Page 2:
2025-06-22 21:00:27,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:27,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:27,669 - INFO - Response - Page 2:
2025-06-22 21:00:27,872 - INFO - 第 2 页获取到 100 条记录
2025-06-22 21:00:27,872 - INFO - Request Parameters - Page 3:
2025-06-22 21:00:27,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:27,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:28,450 - INFO - Response - Page 3:
2025-06-22 21:00:28,654 - INFO - 第 3 页获取到 100 条记录
2025-06-22 21:00:28,654 - INFO - Request Parameters - Page 4:
2025-06-22 21:00:28,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:28,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:29,310 - INFO - Response - Page 4:
2025-06-22 21:00:29,529 - INFO - 第 4 页获取到 100 条记录
2025-06-22 21:00:29,529 - INFO - Request Parameters - Page 5:
2025-06-22 21:00:29,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:29,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:30,029 - INFO - Response - Page 5:
2025-06-22 21:00:30,232 - INFO - 第 5 页获取到 100 条记录
2025-06-22 21:00:30,232 - INFO - Request Parameters - Page 6:
2025-06-22 21:00:30,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:30,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:30,670 - INFO - Response - Page 6:
2025-06-22 21:00:30,873 - INFO - 第 6 页获取到 100 条记录
2025-06-22 21:00:30,873 - INFO - Request Parameters - Page 7:
2025-06-22 21:00:30,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:30,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:31,373 - INFO - Response - Page 7:
2025-06-22 21:00:31,576 - INFO - 第 7 页获取到 65 条记录
2025-06-22 21:00:31,576 - INFO - 查询完成，共获取到 665 条记录
2025-06-22 21:00:31,576 - INFO - 获取到 665 条表单数据
2025-06-22 21:00:31,576 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-22 21:00:31,592 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 21:00:31,592 - INFO - 开始处理日期: 2025-06
2025-06-22 21:00:31,592 - INFO - Request Parameters - Page 1:
2025-06-22 21:00:31,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:31,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:32,076 - INFO - Response - Page 1:
2025-06-22 21:00:32,279 - INFO - 第 1 页获取到 100 条记录
2025-06-22 21:00:32,279 - INFO - Request Parameters - Page 2:
2025-06-22 21:00:32,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:32,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:32,889 - INFO - Response - Page 2:
2025-06-22 21:00:33,092 - INFO - 第 2 页获取到 100 条记录
2025-06-22 21:00:33,092 - INFO - Request Parameters - Page 3:
2025-06-22 21:00:33,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:33,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:33,576 - INFO - Response - Page 3:
2025-06-22 21:00:33,779 - INFO - 第 3 页获取到 100 条记录
2025-06-22 21:00:33,779 - INFO - Request Parameters - Page 4:
2025-06-22 21:00:33,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:33,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:34,280 - INFO - Response - Page 4:
2025-06-22 21:00:34,483 - INFO - 第 4 页获取到 100 条记录
2025-06-22 21:00:34,483 - INFO - Request Parameters - Page 5:
2025-06-22 21:00:34,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:34,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:34,967 - INFO - Response - Page 5:
2025-06-22 21:00:35,170 - INFO - 第 5 页获取到 100 条记录
2025-06-22 21:00:35,170 - INFO - Request Parameters - Page 6:
2025-06-22 21:00:35,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:35,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:35,608 - INFO - Response - Page 6:
2025-06-22 21:00:35,811 - INFO - 第 6 页获取到 100 条记录
2025-06-22 21:00:35,811 - INFO - Request Parameters - Page 7:
2025-06-22 21:00:35,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 21:00:35,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 21:00:36,186 - INFO - Response - Page 7:
2025-06-22 21:00:36,389 - INFO - 第 7 页获取到 23 条记录
2025-06-22 21:00:36,389 - INFO - 查询完成，共获取到 623 条记录
2025-06-22 21:00:36,389 - INFO - 获取到 623 条表单数据
2025-06-22 21:00:36,389 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-22 21:00:36,389 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMNX
2025-06-22 21:00:36,858 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMNX
2025-06-22 21:00:36,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113560.0, 'new_value': 131035.0}, {'field': 'total_amount', 'old_value': 113560.0, 'new_value': 131035.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-06-22 21:00:36,858 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-22 21:00:37,296 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-22 21:00:37,296 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22838.86, 'new_value': 23597.51}, {'field': 'offline_amount', 'old_value': 12809.82, 'new_value': 13608.22}, {'field': 'total_amount', 'old_value': 35648.68, 'new_value': 37205.73}, {'field': 'order_count', 'old_value': 1483, 'new_value': 1537}]
2025-06-22 21:00:37,296 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-22 21:00:37,765 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-22 21:00:37,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131257.6, 'new_value': 140153.9}, {'field': 'total_amount', 'old_value': 131346.6, 'new_value': 140242.9}, {'field': 'order_count', 'old_value': 1604, 'new_value': 1714}]
2025-06-22 21:00:37,765 - INFO - 日期 2025-06 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-22 21:00:37,765 - INFO - 数据同步完成！更新: 3 条，插入: 0 条，错误: 0 条
2025-06-22 21:00:37,765 - INFO - =================同步完成====================
